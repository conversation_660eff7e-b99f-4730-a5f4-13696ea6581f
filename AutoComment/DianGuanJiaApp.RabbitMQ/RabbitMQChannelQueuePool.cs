using CodeProject.ObjectPool;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using RabbitMQ.Client.Events;
using RabbitMQ.Client;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Text;

namespace DianGuanJiaApp.RabbitMQ
{
    public class RabbitMQChannelQueuePool
    {
        private static readonly ConcurrentDictionary<string, ChannelQueueDescrpitonModel> _dict =
            new ConcurrentDictionary<string, ChannelQueueDescrpitonModel>();
        private static RabbitMQChannelQueuePool _pool = new RabbitMQChannelQueuePool();
        private RabbitMQChannelQueuePool()
        {
            var conn = RabbitMqClientFactory.Connection;
            if (conn == null || conn.IsOpen == false)
            {
                Log.WriteError($"消息队列池初始化失败，无法链接上RabbitMQ");
                return;
            }
            // try
            // {
            //     if (_dict.ContainsKey(RabbitMQService.FxHotProductMessageDescription.QueueName))
            //     {
            //         _dict.TryAdd(RabbitMQService.FxHotProductMessageDescription.QueueName,
            //             new ChannelQueueDescrpitonModel()
            //             {
            //                 Description = RabbitMQService.FxHotProductMessageDescription,
            //                 ChannelPool =
            //                     RabbitMQChannelPool.CreateInstance(RabbitMQService.FxHotProductMessageDescription)
            //             });
            //     }
            // }
            // catch (Exception ex)
            // {
            //     Log.WriteError($"初始化RabbitMQChannelPool连接池时发生错误：{ex}");
            // }
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="message"></param>
        /// <param name="desc"></param>
        /// <returns></returns>
        [Obsolete("此方法已过期，后面请使用 DianGuanJiaApp.RabbitMqCore.Pool 组件 RabbitMqChannelQueuePool.SendMessage 方法，池化发送消息")]
        public static bool SendMessage(object message, MessageDescription desc)
        {
            try
            {
                if (_dict.ContainsKey(desc.QueueName) == false)
                {
                    _dict.TryAdd(desc.QueueName, new ChannelQueueDescrpitonModel()
                    {
                        Description = desc,
                        ChannelPool = RabbitMQChannelPool.CreateInstance(desc)
                    });
                }
                var model = _dict[desc.QueueName];
                var _pool = model.ChannelPool;
                using (var im = _pool.GetChannel())
                {
                    if (im == null)
                        return true;
                    var prs = im.CreateBasicProperties();
                    //序列化消息
                    var msg = Encoding.UTF8.GetBytes(message.ToJson(true));
                    im.BasicPublish(desc.ExchangeName, desc.RouterName, prs, msg);
                }
                return true;
            }
            catch (Exception ex)
            {
                Log.WriteError($"发送消息失败，队列名：{desc.QueueName}：消息内容：{message.ToJson()}\r\n 错误详情：{ex} \r\nInnerException：{ex?.InnerException}");
                return false;
            }
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <returns></returns>
        [Obsolete("此方法已过期，后面请使用 DianGuanJiaApp.RabbitMqCore.Pool 组件 RabbitMqChannelQueuePool.SendMessage 方法，池化发送消息")]
        public static bool SendMessages(List<object> messages, MessageDescription desc)
        {
            try
            {
                if (_dict.ContainsKey(desc.QueueName) == false)
                {
                    _dict.TryAdd(desc.QueueName, new ChannelQueueDescrpitonModel()
                    {
                        Description = desc,
                        ChannelPool = RabbitMQChannelPool.CreateInstance(desc)
                    });
                }
                var model = _dict[desc.QueueName];
                var _pool = model.ChannelPool;
                using (var im = _pool.GetChannel())
                {
                    if (im == null)
                        return true;
                    var prs = im.CreateBasicProperties();
                    //序列化消息
                    foreach (var message in messages)
                    {
                        var msg = Encoding.UTF8.GetBytes(message.ToJson(true));
                        im.BasicPublish(desc.ExchangeName, desc.RouterName, prs, msg);
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                Log.WriteError($"发送消息失败，队列名：{desc.QueueName}：消息内容：{messages.ToJson()}\r\n 错误详情：{ex} \r\nInnerException：{ex?.InnerException}");
                return true;
            }
        }

        public static string Status()
        {
            var sb = new StringBuilder();
            foreach (var kv in _dict)
            {
                sb.AppendLine($"{kv.Key},isInit:{kv.Value.ChannelPool.IsInit},MaximumPoolSize:{kv.Value.ChannelPool.MaximumPoolSize},ObjectsInPoolCount:{kv.Value.ChannelPool.ObjectsInPoolCount}<br/>");
            }
            return sb.ToString();
        }
    }

    public class RabbitMQChannelPool
    {
        private ObjectPool<PooledRabbitMQChannel> _objectPool;
        private RabbitMQChannelPool _pool;

        private RabbitMQChannelPool(MessageDescription desc)
        {
            var conn = RabbitMqClientFactory.Connection;
            if (conn == null || conn.IsOpen == false)
                return;
            try
            {
                _objectPool = new ObjectPool<PooledRabbitMQChannel>(100, () =>
                {
                    //默认：连接超时2秒，响应超时1秒，同步数据超时1秒
                    var im = new PooledRabbitMQChannel();
                    desc.InitIModel(im);
                    return im;
                });
            }
            catch (Exception ex)
            {
                Log.WriteError($"初始化RabbitMQChannelPool连接池时发生错误：{ex}");
            }
        }

        public static RabbitMQChannelPool CreateInstance(MessageDescription desc)
        {
            return new RabbitMQChannelPool(desc);
        }

        /// <summary>
        /// 是否初始化了，未初始化不能使用
        /// </summary>
        public bool IsInit
        {
            get
            {
                return _objectPool != null;
            }
        }

        public PooledRabbitMQChannel GetChannel()
        {
            if (IsInit == false)
                return null;
            return _objectPool.GetObject();
        }

        public int ObjectsInPoolCount
        {
            get { return _objectPool.ObjectsInPoolCount; }
        }

        public int MaximumPoolSize
        {
            get { return _objectPool.MaximumPoolSize; }
        }
    }

    public class ChannelQueueDescrpitonModel
    {
        public RabbitMQChannelPool ChannelPool { get; set; }
        public MessageDescription Description { get; set; }
    }

    public class PooledRabbitMQChannel : PooledObject, IModel
    {
        private readonly IModel _imodel;

        public PooledRabbitMQChannel()
        {
            this._imodel = RabbitMqClientFactory.CreateModel();
            this.OnValidateObject += context => this._imodel.IsOpen;
            this.OnReleaseResources += () => {
                this._imodel.Dispose();
            };
        }

        public bool IsConnecte => this._imodel.IsOpen;

        public int ChannelNumber => this._imodel.ChannelNumber;

        public ShutdownEventArgs CloseReason => this._imodel.CloseReason;

        public bool IsClosed => this._imodel.IsClosed;

        public bool IsOpen => this._imodel.IsOpen;

        public ulong NextPublishSeqNo => this._imodel.NextPublishSeqNo;

        IBasicConsumer IModel.DefaultConsumer { get => this._imodel.DefaultConsumer; set => this._imodel.DefaultConsumer = value; }
        TimeSpan IModel.ContinuationTimeout { get => this._imodel.ContinuationTimeout; set => this._imodel.ContinuationTimeout = value; }

        public event EventHandler<BasicAckEventArgs> BasicAcks;

        public event EventHandler<BasicNackEventArgs> BasicNacks;

        public event EventHandler<EventArgs> BasicRecoverOk;

        public event EventHandler<BasicReturnEventArgs> BasicReturn;

        public event EventHandler<CallbackExceptionEventArgs> CallbackException;

        public event EventHandler<FlowControlEventArgs> FlowControl;

        public event EventHandler<ShutdownEventArgs> ModelShutdown;

        public void Abort()
        {
            this._imodel.Abort();
        }

        public void Abort(ushort replyCode, string replyText)
        {
            this._imodel.Abort();
        }

        public void BasicAck(ulong deliveryTag, bool multiple)
        {
            this._imodel.Abort();
        }

        public void BasicCancel(string consumerTag)
        {
            this._imodel.Abort();
        }

        public string BasicConsume(string queue, bool autoAck, string consumerTag, bool noLocal, bool exclusive, IDictionary<string, object> arguments, IBasicConsumer consumer)
        {
            return this._imodel.BasicConsume(queue, autoAck, consumerTag, noLocal, exclusive, arguments, consumer);
        }

        public BasicGetResult BasicGet(string queue, bool autoAck)
        {
            return this._imodel.BasicGet(queue, autoAck);
        }

        public void BasicNack(ulong deliveryTag, bool multiple, bool requeue)
        {
            this._imodel.BasicNack(deliveryTag, multiple, requeue);
        }

        public void BasicPublish(string exchange, string routingKey, bool mandatory, IBasicProperties basicProperties, byte[] body)
        {
            this._imodel.BasicPublish(exchange, routingKey, mandatory, basicProperties, body);
        }

        public void BasicPublish(string exchange, string routingKey, IBasicProperties basicProperties, byte[] body)
        {
            this._imodel.BasicPublish(exchange, routingKey, basicProperties, body);
        }

        public void BasicQos(uint prefetchSize, ushort prefetchCount, bool global)
        {
            this._imodel.BasicQos(prefetchSize, prefetchCount, global);
        }

        public void BasicRecover(bool requeue)
        {
            this._imodel.BasicRecover(requeue);
        }

        public void BasicRecoverAsync(bool requeue)
        {
            this._imodel.BasicRecoverAsync(requeue);
        }

        public void BasicReject(ulong deliveryTag, bool requeue)
        {
            this._imodel.BasicReject(deliveryTag, requeue);
        }

        public void Close()
        {
            this._imodel.Close();
        }

        public void Close(ushort replyCode, string replyText)
        {
            this._imodel.Close(replyCode, replyText);
        }

        public void ConfirmSelect()
        {
            this._imodel.ConfirmSelect();
        }

        public uint ConsumerCount(string queue)
        {
            return this._imodel.ConsumerCount(queue);
        }

        public IBasicProperties CreateBasicProperties()
        {
            return this._imodel.CreateBasicProperties();
        }

        public IBasicPublishBatch CreateBasicPublishBatch()
        {
            return this._imodel.CreateBasicPublishBatch();
        }

        public void ExchangeBind(string destination, string source, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.ExchangeBind(destination, source, routingKey, arguments);
        }

        public void ExchangeBindNoWait(string destination, string source, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.ExchangeBindNoWait(destination, source, routingKey, arguments);
        }

        public void ExchangeDeclare(string exchange, string type, bool durable, bool autoDelete, IDictionary<string, object> arguments)
        {
            this._imodel.ExchangeDeclare(exchange, type, durable, autoDelete, arguments);
        }

        public void ExchangeDeclareNoWait(string exchange, string type, bool durable, bool autoDelete, IDictionary<string, object> arguments)
        {
            this._imodel.ExchangeDeclareNoWait(exchange, type, durable, autoDelete, arguments);
        }

        public void ExchangeDeclarePassive(string exchange)
        {
            this._imodel.ExchangeDeclarePassive(exchange);
        }

        public void ExchangeDelete(string exchange, bool ifUnused)
        {
            this._imodel.ExchangeDelete(exchange, ifUnused);
        }

        public void ExchangeDeleteNoWait(string exchange, bool ifUnused)
        {
            this._imodel.ExchangeDeleteNoWait(exchange, ifUnused);
        }

        public void ExchangeUnbind(string destination, string source, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.ExchangeUnbind(destination, source, routingKey, arguments);
        }

        public void ExchangeUnbindNoWait(string destination, string source, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.ExchangeUnbindNoWait(destination, source, routingKey, arguments);
        }

        public uint MessageCount(string queue)
        {
            return this._imodel.MessageCount(queue);
        }

        public void QueueBind(string queue, string exchange, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.QueueBind(queue, exchange, routingKey, arguments);
        }

        public void QueueBindNoWait(string queue, string exchange, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.QueueBindNoWait(queue, exchange, routingKey, arguments);
        }

        public QueueDeclareOk QueueDeclare(string queue, bool durable, bool exclusive, bool autoDelete, IDictionary<string, object> arguments)
        {
            return this._imodel.QueueDeclare(queue, durable, exclusive, autoDelete, arguments);
        }

        public void QueueDeclareNoWait(string queue, bool durable, bool exclusive, bool autoDelete, IDictionary<string, object> arguments)
        {
            this._imodel.QueueDeclareNoWait(queue, durable, exclusive, autoDelete, arguments);
        }

        public QueueDeclareOk QueueDeclarePassive(string queue)
        {
            return this._imodel.QueueDeclarePassive(queue);
        }

        public uint QueueDelete(string queue, bool ifUnused, bool ifEmpty)
        {
            return this._imodel.QueueDelete(queue, ifUnused, ifEmpty);
        }

        public void QueueDeleteNoWait(string queue, bool ifUnused, bool ifEmpty)
        {
            this._imodel.QueueDeleteNoWait(queue, ifUnused, ifEmpty);
        }

        public uint QueuePurge(string queue)
        {
            return this._imodel.QueuePurge(queue);
        }

        public void QueueUnbind(string queue, string exchange, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.QueueUnbind(queue, exchange, routingKey, arguments);
        }

        public void TxCommit()
        {
            this._imodel.TxCommit();
        }

        public void TxRollback()
        {
            this._imodel.TxRollback();
        }

        public void TxSelect()
        {
            this._imodel.TxSelect();
        }

        public bool WaitForConfirms()
        {
            return this._imodel.WaitForConfirms();
        }

        public bool WaitForConfirms(TimeSpan timeout)
        {
            return this._imodel.WaitForConfirms(timeout);
        }

        public bool WaitForConfirms(TimeSpan timeout, out bool timedOut)
        {
            return this._imodel.WaitForConfirms(timeout, out timedOut);
        }

        public void WaitForConfirmsOrDie()
        {
            this._imodel.WaitForConfirmsOrDie();
        }

        public void WaitForConfirmsOrDie(TimeSpan timeout)
        {
            this._imodel.WaitForConfirmsOrDie();
        }
    }
}