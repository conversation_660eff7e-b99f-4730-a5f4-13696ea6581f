using DianGuanJiaApp.Api.OpenPlatform.Models;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model.OpenPlatform;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services.SupplierProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace DianGuanJiaApp.Api.OpenPlatform.ApiControllers
{
    [RoutePrefix("api/base/partner")]
    public class PartnerController : BaseApiController
    {
        private readonly SupplierUserService supplierUserService;
        private readonly SupplierProductService supplierProductService;

        public PartnerController()
        {
            supplierUserService = new SupplierUserService();
            supplierProductService = new SupplierProductService();
        }

        /// <summary>
        /// 厂家列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("list")]
        public BaseOpenApiRespone<PagedRespone<PartnerListRespone>> List(BaseOpenApiRequest request)
        {
            var model = request.Param.ToObject<QueryPartnerListRequest>();
            model.CheckParam();
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            AgentBingSupplierStatus? status = null;
            if (model.Status != null)
                status = (AgentBingSupplierStatus)model.Status;
            var result = supplierUserService.GetSupplierList(fxUserId, string.Empty, status, model.Page, model.PageSize);
            var page = new PagedRespone<PartnerListRespone>();
            page.Total = result.Item1;
            if (page.Total > 0)
            {
                page.Rows = result.Item2.Select(t => new PartnerListRespone()
                {
                    Id = t.Id,
                    SupplierId = t.SupplierFxUserId,
                    NickName = t.NickName,
                    Mobile = t.Mobile.ToEncrytPhone(),
                    CreateTime = t.CreateTime,
                    Status = t.Status.ToInt()
                }).ToList();
            }
            return base.Success(page);
        }

        /// <summary>
        /// 厂家商品
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("product-list")]
        public BaseOpenApiRespone<PagedRespone<SupplierProductListRespone>> ProductList(BaseOpenApiRequest request)
        {
            var model = request.Param.ToObject<QueryPartnerProductListRequest>();
            model.CheckParam();
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            if (model.SupplierIds.IsNotNullAndAny())
            {
                //校验厂家id
                var supplierUsers = supplierUserService.GetByFxIds(new List<int> { fxUserId }, model.SupplierIds);
                if (supplierUsers.IsNullOrEmptyList() || supplierUsers.Any(t => t.Status != AgentBingSupplierStatus.Binded))
                    throw new LogicException($"您查询的厂家存在不是【绑定成功】状态，请重试，或更换其他厂家");
            }
            Data.FxModel.SupplierProduct.SupplierProductQueryModel queryModel = new Data.FxModel.SupplierProduct.SupplierProductQueryModel();
            queryModel.CategoryId = new List<int>();
            queryModel.ExpressBillId = new List<int>();
            queryModel.IsOrderDesc = true;
            queryModel.OrderByField = "PublicTime";
            queryModel.PageIndex = model.Page;
            queryModel.PageSize = model.PageSize;
            queryModel.ProductName = "";
            queryModel.SupplierFxUserId = null;
            queryModel.SupplierFxUserIds = model.SupplierIds;
            queryModel.Tag = 3;

            try
            {
                var result = supplierProductService.GetPageList(queryModel, fxUserId);
                return base.Success(result);
            }
            catch (Exception ex)
            {
                Log.WriteError($"查询厂家商品列表失败：{ex}");
                throw new LogicException("查询厂家商品列表失败");
            }
        }
    }
}