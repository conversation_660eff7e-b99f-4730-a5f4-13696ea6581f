using DianGuanJiaApp.Api.OpenPlatform.App_Start;
using System.Web.Http;

namespace DianGuanJiaApp.Api.OpenPlatform
{
    public static class WebApiConfig
    {
        public static void Register(HttpConfiguration config)
        {
            // Web API 配置和服务

            // Web API 路由
            config.MapHttpAttributeRoutes();
            config.Filters.Add(new OpenApiExceptionFilter());
            config.Filters.Add(new OpenApiLoggingAttribut());
            config.Routes.MapHttpRoute(
                name: "DefaultApi",
                routeTemplate: "{controller}/{id}",
                defaults: new { controller = "Home", action = "Get", id = RouteParameter.Optional }
            );
        }
    }
}