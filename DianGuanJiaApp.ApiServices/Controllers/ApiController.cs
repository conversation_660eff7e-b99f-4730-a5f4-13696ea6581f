using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.ServicesExtension;
using System;
using System.Threading;
using System.Web.Mvc;

namespace DianGuanJiaApp.ApiServices.Controllers
{
    public class ApiController : Controller
    {
        public ActionResult Index()
        {
            return Content($"店管家API服务 {Request.ServerVariables["Server_Name"]}_{Request.ServerVariables["Local_Addr"]}");
        }
        //[LogForOperatorFilter("跨库查询")]
        //public ActionResult Test(DbApiRequestModel model)
        //{
        //    //TODO:加密
        //    var service = new OuterDbAcessService(model);
        //    var rsp = service.Execute(model);
        //    return Json(rsp);
        //}

        #region 公共方法

        private string _body = "";

        /// <summary>
        /// 请求报文
        /// </summary>
        public string RequestBody
        {
            get
            {
                if (string.IsNullOrEmpty(_body))
                {
                    try
                    {
                        var req = Request.InputStream;
                        if (req != null && req.Length > 0)
                        {
                            req.Seek(0, System.IO.SeekOrigin.Begin);
                            var json = new System.IO.StreamReader(req).ReadToEnd();
                            _body = json;
                        }
                    }
                    catch
                    {
                    }
                }
                return _body;
            }
        }

        #endregion


        //[LogForOperatorFilter("跨库查询")]
        public ActionResult Route(string request)
        {
            var model = OuterDbAcessService.Decrypt<DbApiRequestModel>(request);
            if(model == null && string.IsNullOrEmpty(RequestBody) == false)
                model = OuterDbAcessService.Decrypt<DbApiRequestModel>(RequestBody);
            var service = new OuterDbAcessService(model);
            var rsp = service.Execute();
            var str = OuterDbAcessService.Encrypt(rsp);
            return Content(str);
        }

        public ActionResult Sleep()
        {
            var seconds = new Random().Next(5,30);
            Thread.Sleep(seconds * 1000);
            return Content(seconds.ToString());
        }
    }
}