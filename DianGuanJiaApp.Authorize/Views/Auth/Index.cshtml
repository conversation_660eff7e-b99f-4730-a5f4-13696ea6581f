@model List<DianGuanJiaApp.Utility.SuportPlatformAuthEntryModel>
@{
    ViewBag.Title = "选择平台";
}



<div class="nav-title">请选择要授权的平台</div>
    <ul class="nav-main">
    @{
        foreach (var item in Model)
        {
            <li>
                <a href="@(item.AuthEntry + (item.PlatformType == "ZhiDian" || item.PlatformType == "DouYinXiaoDian" || item.PlatformType == "TouTiaoXiaoDian" || item.PlatformType == "LuBan" ? $"?pt={item.PlatformType}" : "")) ">
                    @if (string.IsNullOrEmpty(item.Img) == false)
                    {
                        <img class="@item.PlatformType nav-main-img logo-img" src="@item.Img"/>
                    }
                    else
                    {
                        <div class="@item.PlatformType nav-main-img"></div>
                    }
                    <span class="nav-main-span" style="margin:5px 10px;">@item.Name - - 去授权</span>
                </a>
            </li>
        }

    }
</ul>




