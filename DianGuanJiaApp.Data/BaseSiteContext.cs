using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Messaging;
using DianGuanJiaApp.Data.Repository.SubAccount;

namespace DianGuanJiaApp.Data
{
    /// <summary>
    /// 站点上下文信息
    /// </summary>
    [Serializable]
    public abstract class BaseSiteContext
    {
        protected static readonly string _siteKey = "CurrentSiteContextKey";
        /// <summary>
        /// 是否在多线程中可用，若设为false，则当开启子线程时，SiteContext.Current将无法使用
        /// </summary>
        protected static bool IsAvailableToThread = true;
        public DatabaseTypeEnum DataBaseType = DatabaseTypeEnum.SQLServer;
        /// <summary>
        /// 当前登录店铺数据库配置信息，可为空
        /// </summary>
        private DbConfigModel DbConfig { get; set; }

        private List<DbConfigModel> _allDbConfigs = null;
        private SiteContextConfig _contextConfig = null;
        private string _dbName = null;

        private UserFxRepository _fxRepository = new UserFxRepository();
        private SubUserFxRepository _subFxRepository = new SubUserFxRepository();
        private ShopRepository _shopService = new ShopRepository();
        protected CommonSettingRepository _commonSettingRepository = new CommonSettingRepository();

        /// <summary>
        /// 分单系统账户权限 懒加载 权限标识(PermissionTag) 当前用户为子账号时查询赋值 
        /// </summary>
        private List<string> _permissionTags = null;
        private List<PermissionModel> _permissions = null;

        #region 重要开放属性（懒加载）
        /*************************以下采用属性懒加载方式，调用尽量避免调用属性，导致请求数据库(义真)***********************/
        /*******外部调用规则：
         * （1）采用层级化调用，优先一级属性(CurrentFxUserId,CurrentShopId)，再次二级（对象）属性(CurrentLoginShop，FxUser等),
         * （2）如此类推，调用层级浅的能避免减少深层次数据的检索，避免带来更多数据请求检索
         * （3）尽量避免属性中带属性的调用，层级越深使用复杂度越高，越容易出现Bug
         * *******/

        /*******业务实现逻辑：初始化时候，必须保证店铺ShopId，用户FxUserId，当前登录店铺LoginShop，当前分单用户信息FxUser的【4组】信息必须包含至少其一，否则初始化失败
          * (1)FxUserId=> userFx = userFxService.Get(FxUserId)=> shop=rp.GetSystemShops(userFxId)
          * (2.1)ShopID=>shop = ShopRepository.Get(shopId)
          * (2.2)ShopID=>FxUserId = new ShopService().GetFxUserIdByShopId(LoginAuthToken.ShopId)=> userFx = userFxService.Get(FxUserId) 流程未来可优化为ShopID=>userFx
          * *******/

        private int _currentFxUserId = 0;

        /// <summary>
        /// 子账号Id，若当前用户为主账号，则无需初始化
        /// </summary>
        private int _subFxUserId = 0;

        private string _userFlag = null;

        /// <summary>
        /// 当前分单系统用户ID(高效率/懒加载/必存在)
        /// </summary>
        public int CurrentFxUserId
        {
            get
            {
                if (_currentFxUserId == 0)
                {
                    loadCurrentFxUserId();
                }
                return _currentFxUserId;
            }
        }

        /// <summary>
        /// 当前分单系统子账户Id
        /// </summary>
        public int SubFxUserId
        {
            get
            {
                return _subFxUserId;
            }
        }

        /// <summary>
        /// 是否纯铺货用户
        /// </summary>
        public bool IsOnlyListingUser
        {
            get
            {
                if (_userFlag == null)
                {
                    LoadCurrentUserFlag();
                }
                return _userFlag != null && _userFlag.Contains(UserFxRepository.OnlyListingUserFlag);
            }
        }

        /// <summary>
        /// 是否白名单用户
        /// </summary>
        public bool IsWhiteUser
        {
            get
            {
                if (_userFlag == null)
                {
                    LoadCurrentUserFlag();
                }
                return _userFlag != null && _userFlag.Contains(UserFxRepository.WhiteUserFlag);
            }
        }

        /// <summary>
        /// 是否利润统计白名单用户
        /// </summary>
        public bool IsProfitStatisticsWhiteUser
        {
            get
            {
                if (_userFlag == null)
                {
                    LoadCurrentUserFlag();
                }
                return _userFlag != null && _userFlag.Contains(UserFxRepository.ProfitStatisticsUserFlag);
            }
        }

        /// <summary>
        /// 是否短视频发布白名单用户
        /// </summary>
        public bool IsShopVideoWhiteUser
        {
            get
            {
                if (_userFlag == null)
                {
                    LoadCurrentUserFlag();
                }
                return _userFlag != null && _userFlag.Contains(UserFxRepository.ShopVideoUserFlag);
            }
        }

        /// <summary>
        /// 是否站内信白名单用户
        /// </summary>
        public bool IsSiteMessageUserFlagWhiteUser
        {
            get
            {
                if (_userFlag == null)
                {
                    LoadCurrentUserFlag();
                }
                return _userFlag != null && _userFlag.Contains(UserFxRepository.SiteMessageUserFlag);
            }
        }

        /// <summary>
        /// 是否跨境白名单用户
        /// </summary>
        public bool IsShowCrossBorder
        {
            get
            {
                // 系统配置满足全局配置开启原则
                if (GlobalCrossBorderEnabled) return true;

                if (_userFlag == null)
                {
                    LoadCurrentUserFlag();
                }

                //满足用户存在跨境标识
                return _userFlag != null && _userFlag.Contains(UserFxRepository.CrossBorderUserFlag);
            }
        }

        /// <summary>
        /// 对账中心大客户标识
        /// </summary>
        public bool IsReconVip
        {
            get
            {
                if (_userFlag == null)
                {
                    LoadCurrentUserFlag();
                }

                //满足用户存在跨境标识
                return _userFlag != null && _userFlag.Contains(UserFxRepository.ReconciliationVip);
            }
        }

        /// <summary>
        /// 全局是否启用跨境配置
        /// </summary>
        private bool GlobalCrossBorderEnabled => _commonSettingRepository.Get(CacheKeys.CrossBorderGlobalEnabledKey, 0)?.Value.ToBool() ?? false;


        /// <summary>
        /// 开启懒加载--当前分单系统用户ID
        /// </summary>
        private void loadCurrentFxUserId()
        {
            //P1.有分单用户对象直接去属性
            if (_currentFxUser != null)
            {
                _currentFxUserId = _currentFxUser.Id;
            }
            else
            {
                //P2:没有分单用户对象，通过店铺ID转为分单用户ID
                if (_currentLoginShop != null)
                {
                    _currentShopId = _currentLoginShop.Id;
                }

                if (_currentShopId != 0 && (_contextConfig == null || _contextConfig.NeedLoadFxUser == true))
                {
                    _currentFxUserId = _shopService.GetFxUserIdByShopId(_currentShopId);//店铺ID转FxuserID
                }
            }

        }

        /// <summary>
        /// 获取当前用户标识，懒加载
        /// </summary>
        private void LoadCurrentUserFlag()
        {
            if (_currentFxUser != null)
            {
                _userFlag = _currentFxUser.UserFlag.ToString2();
            }
            else
            {
                _userFlag = _currentFxUserId != 0 ? _fxRepository.Get(_currentFxUserId).UserFlag.ToString2() : string.Empty;
            }
        }

        private int _currentShopId = 0;
        /// <summary>
        /// 当前店铺ID(高效率/懒加载/必存在)
        /// </summary>
        public int CurrentShopId
        {
            get
            {
                if (_currentShopId == 0)
                {
                    loadCurrentShopId();
                }
                return _currentShopId;
            }
        }

        /// <summary>
        /// 开启懒加载--当前店铺ID
        /// </summary>
        private void loadCurrentShopId()
        {
            //因为在基类中初始化过_CurrentShopId，大部分时间不会走已下流程
            //优先去店铺租信息
            if (_currentLoginShop != null)
            {
                _currentShopId = _currentLoginShop.Id;
            }
            else
            {
                //再取分单用户
                if (_currentFxUser != null)
                {
                    _currentFxUserId = _currentFxUser.Id;
                }

                //通过分单用户ID取店铺ID
                if (_currentFxUserId != 0)
                {
                    //查询当前登陆的店铺，并缓存在变量
                    //_currentLoginShop = _fxRepository.GetSystemShops(_currentFxUserId);
                    _currentLoginShop = _fxRepository.GetSystemShopByCacheSwitch(_currentFxUserId);
                    _currentShopId = _currentLoginShop?.Id ?? 0;
                }
            }

            if (_currentShopId == 0)
            {
                throw new LogicException("【当前店铺ID】初始化数据失败!");
            }

        }


        private Shop _currentLoginShop = null;
        /// <summary>
        /// 当前登陆的店铺（依赖于CurrentFxUser）
        /// </summary>
        public Shop CurrentLoginShop
        {
            get
            {
                if (_currentLoginShop == null)
                {
                    loadCurrentLoginShop();

                }
                return _currentLoginShop;

            }
        }

        /// <summary>
        /// 开启懒加载当前登陆的店铺
        /// </summary>
        private void loadCurrentLoginShop()
        {
            if (_currentLoginShop == null)
            {
                int fxUserId = this.CurrentFxUserId;//调用属性，可能会已经获取到店铺信息
                _currentLoginShop = _fxRepository.GetSystemShops(fxUserId);
            }
        }

        private UserFx _currentFxUser = null;
        /// <summary>
        /// 当前登录的分单系统用户
        /// </summary>
        public UserFx CurrentFxUser
        {
            get
            {
                if (_currentFxUser == null)
                {
                    loadCurrentFxUser();
                }
                return _currentFxUser;
            }
        }

        /// <summary>
        /// 开启懒加载--当前登录的分单系统用户
        /// </summary>
        private void loadCurrentFxUser()
        {
            if (_currentFxUser == null)
            {
                int fxUserID = this.CurrentFxUserId;//调用属性，可能会已经获取到系统用户信息
                _currentFxUser = _fxRepository.Get(fxUserID);
            }
        }

        private SubUserFx _subFxUser = null;
        /// <summary>
        /// 当前登录的分单系统子账户
        /// </summary>
        public SubUserFx SubFxUser
        {
            get
            {
                if (_subFxUser == null)
                {
                    loadSubFxUser();
                }
                return _subFxUser;
            }
        }

        /// <summary>
        /// 开启懒加载--当前登录的分单系统子账户
        /// </summary>
        private void loadSubFxUser()
        {
            if (_subFxUser == null)
            {
                int subFxUserID = this.SubFxUserId;//调用属性，可能会已经获取到系统用户信息
                if (subFxUserID > 0)
                    _subFxUser = _subFxRepository.Get(subFxUserID);
            }
        }

        /// <summary>
        /// 分单系统账户权限 懒加载 权限标识(PermissionTag) 当前用户为子账号时查询赋值 
        /// </summary>
        public List<string> PermissionTags
        {
            get
            {

                if (_permissionTags == null)
                {
                    LoadPermission();
                }
                return _permissionTags;
            }
        }

        public List<PermissionModel> Permissions
        {
            get
            {
                if (_permissions == null)
                {
                    LoadPermission();
                }
                return _permissions;
            }
        }

        /// <summary>
        /// 加载用户权限 
        /// </summary>
        /// <param name="useCache"></param>
        protected void LoadPermission(bool useCache = true)
        {
            if (SubFxUserId > 0) // 子账号权限
            {
                var permRepository = new PostPermissionFxRepository();
                if (!useCache)
                {
                    _permissions = permRepository.GetUserAllPermissionTag(this.CurrentFxUserId, this.SubFxUserId);
                }
                else
                {
                    _permissions = FxCaching.GetCache(FxCachingType.FxUserPermission, $"sub_{SubFxUserId}",
                        () => permRepository.GetUserAllPermissionTag(this.CurrentFxUserId, this.SubFxUserId));
                }
                if (IsOnlyListingUser && _permissions.Select(p=>p.PermissionTag).Contains(FxPermission.ShowSettlePrice))
                    _permissions.Add(new PermissionModel{ParentTag = null,PermissionTag = FxPermission.SetProductSettlementPrice});


                _permissionTags = _permissions.Select(p => p.PermissionTag).Distinct().ToList();
                try
                {
                    permRepository.TransferPermission(_permissions);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"主用户{CurrentFxUserId}:子账号用户{SubFxUserId}，权限转换异常：{ex}");
                }
            }
            else // 主账号获取所有权限
            {
                // 内部查询缓存
                var list = new SysPermissionFxRepository(IsOnlyListingUser).GetAllList(useCache);
                _permissionTags = list.Select(p => p.PermissionTag).Distinct().ToList();
                _permissions = new List<PermissionModel>();
                list.ForEach(pe =>
                {
                    _permissions.Add(new PermissionModel()
                    {
                        PermissionTag = pe.PermissionTag,
                        ParentTag = pe.ParentTag
                    });
                });
            }
        }


        [Obsolete("打单使用，分单已废弃")]
        public User CurrentLoginUser { get; private set; }

        /// <summary>
        /// 当前所有店铺的数据库配置信息
        /// </summary>
        public List<DbConfigModel> AllDbConfigs
        {
            get
            {
                return _allDbConfigs;
            }
        }

        public List<DbConfigModel> _allDbBusiness = null;
        public List<DbConfigModel> AllDbBusiness
        {
            get
            {
                if (_allDbBusiness == null)
                {
                    _allDbBusiness = new DbConfigRepository().GetFxBusinessDbConfigs(new List<string> { "fx" });
                }
                return _allDbBusiness;
            }
        }

        private List<Shop> _userShops = null;
        /// <summary>
        /// 当前用户下的店铺
        /// </summary>
        public List<Shop> UserShops
        {
            get
            {
                if (_userShops == null)
                {
                    int fxUserId = this.CurrentFxUserId;//调用属性，可能会已经获取到店铺信息
                    if (fxUserId != 0)
                    {
                        _userShops = _shopService.GetShopSelf(fxUserId);
                    }
                }
                return _userShops;
            }
        }


        private List<Shop> _allShops = null;

        public List<Shop> AllShops
        {
            get
            {
                if (_allShops == null)
                {
                    _allShops = new List<Shop>();
                    _allShops.Add(CurrentLoginShop);
                    if (!ChildShop.IsNullOrEmptyList())
                    {
                        _allShops.AddRange(ChildShop);
                    }
                    if (MasterShop?.Relation?.IsVisibleToEachOther == true)
                    {
                        if (!SiblingShop.IsNullOrEmptyList())
                        {
                            _allShops.AddRange(SiblingShop);
                        }
                        _allShops.Add(MasterShop);
                    }
                    if (!UserShops.IsNullOrEmptyList())
                    {
                        UserShops.Where(w=>w!=null).ToList().ForEach(x =>
                        {
                            if (!_allShops.Any(y => y.Id == x.Id))
                                _allShops.Add(x);
                        });
                    }

                    //检查店铺是否过期
                    if (_contextConfig == null || _contextConfig.NeedShopExpireTime)
                    {
                        _allShops.ForEach(s =>
                        {
                            if (s.PlatformType == this.CurrentLoginShop.PlatformType)
                            {
                                GetShopExpireTime(s);
                            }
                        });
                    }

                }
                return _allShops;
            }
        }

        private Shop _masterShop = null;
        /// <summary>
        /// 主店铺：子店铺单独登陆时才有值
        /// </summary>
        public Shop MasterShop
        {
            get
            {
                if (_masterShop == null)
                {
                    _masterShop = this.CurrentLoginShop;//默认是当前店铺
                }
                return _masterShop;
            }
        }

        public List<Shop> AllSamePlatformTypeShops
        {
            get
            {
                var shops = new List<Shop>();
                shops = AllShops.Where(s => s.PlatformType == CurrentLoginShop.PlatformType).ToList();
                return shops;
            }
        }



        public object CurrentLoginShopModel
        {
            get
            {
                var s = this.CurrentLoginShop;

                if (s.PlatformType == "YunJi")
                    return new
                    {
                        s.Id,
                        s.ShopId,
                        s.ShopName,
                        s.PlatformType,
                        s.SubPlatformType,
                        s.CreateTime,
                        s.NickName,
                        s.PlatformTypeName,
                        s.Uid,
                        s.ShareCode,
                        s.VenderId,
                        s.SystemVersion,
                        s.Version
                    };
                else
                    return new
                    {
                        s.Id,
                        s.ShopId,
                        s.ShopName,
                        s.PlatformType,
                        s.CreateTime,
                        s.NickName,
                        s.PlatformTypeName,
                        s.Uid,
                        s.ShareCode,
                        s.VenderId,
                        s.SystemVersion,
                        s.Version
                    };
            }
        }




        private int __expressTemplateShopId;
        /// <summary>
        /// 快递单模板的ShopId
        /// </summary>
        public int ExpressTemplateShopId
        {
            get
            {
                if (__expressTemplateShopId > 0)
                    return __expressTemplateShopId;
                var sc = BaseSiteContext.Current;
                if (sc.MasterShop != null && sc.MasterShop.Relation != null && sc.MasterShop.Relation.IsUseExpressTemplate)
                    __expressTemplateShopId = sc.MasterShop.Id;
                else
                    //__expressTemplateShopId = BaseSiteContext.Current.CurrentShopId;
                    __expressTemplateShopId = this.CurrentShopId;
                return __expressTemplateShopId;
            }
        }

        /// <summary>
        /// 当前店铺Api数据库参数信息：仅用于打单系统发件人信息查询
        /// </summary>
        public ApiDbConfigModel CurrentLoginShopApiRequestModel
        {
            get
            {
                return new ApiDbConfigModel(DbConfig?.DbServer?.Location, CurrentLoginShop.PlatformType, DbConfig?.DbNameConfig.Id ?? 0);
            }
        }
        /// <summary>
        /// 主店铺Api数据库参数信息：仅用于打单系统发件人信息查询
        /// </summary>
        public ApiDbConfigModel MasterShopApiRequestModel
        {
            get
            {
                return new ApiDbConfigModel(MasterShop.DbConfig?.DbServer?.Location, MasterShop.PlatformType, MasterShop.DbConfig?.DbNameConfig.Id ?? 0);
            }
        }

        /// <summary>
        /// 店铺Id列表，可作为dapper where in 的参数
        /// </summary>
        public List<int> ShopIds
        {
            get
            {
                var ids = new List<int>();
                ids.Add(this.CurrentShopId);
                if (ChildShop != null && ChildShop.Any())
                    ids.AddRange(ChildShop.Select(c => c.Id));
                if (MasterShop?.Relation?.IsVisibleToEachOther == true)
                {
                    ids.Add(MasterShop.Id);
                }
                return ids;
            }
        }


        /// <summary>
        /// 和当前登录店铺相同平台的店铺ID
        /// </summary>
        public List<int> ShopIdsOfCurrentPlatform
        {
            get
            {
                return AllShops.Where(x => x.PlatformType == CurrentLoginShop.PlatformType).Select(x => x.Id).ToList();
            }
        }

        /// <summary>
        /// 当前站点上下文信息，若未获取到抛出异常
        /// </summary>
        public static BaseSiteContext Current
        {
            get
            {
                var data = IsAvailableToThread ? CallContext.LogicalGetData(_siteKey) : CallContext.GetData(_siteKey);
                if (data != null)
                    return data as BaseSiteContext;
                else
                    throw new LogicException("无法获取站点信息，请重新登录");
            }
        }

        /// <summary>
        /// 当前站点上下文信息，若未获取到不会抛出异常
        /// </summary>
        public static BaseSiteContext CurrentNoThrow
        {
            get
            {
                var data = IsAvailableToThread ? CallContext.LogicalGetData(_siteKey) : CallContext.GetData(_siteKey);
                if (data != null)
                    return data as BaseSiteContext;
                return null;
            }
        }

        #endregion

        #region 构造函数
        /// <summary>
        /// 创建站点上下文信息，创建后，当前请求下任意地方都可以获取到。
        /// </summary>
        /// <param name="shop"></param>
        public BaseSiteContext(int shopId, SiteContextConfig config = null)
        {
            var shop = _shopService.Get(shopId);
            if (shop != null)
            {
                Init(shop, config);
            }
            SetCurrentContext();
        }

        /// <summary>
        /// 创建站点上下文信息，创建后，当前请求下任意地方都可以获取到。
        /// </summary>
        /// <param name="shop"></param>
        public BaseSiteContext(Shop shop, SiteContextConfig config = null)
        {
            Init(shop, config);
            SetCurrentContext();
        }

        /// <summary>
        /// 订单分发初始化方法
        /// </summary>
        /// <param name="userFx"></param>
        /// <param name="config"></param>
        public BaseSiteContext(UserFx userFx, SiteContextConfig config = null, SubUserFx subUserFx = null)
        {
            Init(userFx, config, subUserFx);
            SetCurrentContext();
        }

        /// <summary>
        /// 订单分发初始化方法
        /// </summary>
        /// <param name="userFx"></param>
        /// <param name="config"></param>
        public BaseSiteContext(UserFx userFx, string dbname, SiteContextConfig config = null, SubUserFx subUserFx = null)
        {
            Init(userFx, dbname, config, subUserFx);
            SetCurrentContext();
        }

        /// <summary>
        /// 订单分发初始化方法(懒加载模式)
        /// </summary>
        public BaseSiteContext(LoginAuthToken authToken, string dbname, SiteContextConfig config = null)
        {
            Init(authToken, dbname, config);
            SetCurrentContext();
        }


        #endregion

        #region 初始化数据

        /// <summary>
        /// 订单分发初始化方法(懒加载模式)
        /// </summary>
        private void Init(LoginAuthToken authToken, string dbname = "", SiteContextConfig config = null)
        {
            if (authToken == null)
                throw new LogicException("认证Token不能为空");

            _currentShopId = authToken.ShopId;
            // 240531 xieweiming：子账号功能改造，需要判断当前账号是否为子账号
            _currentFxUserId = authToken.FxUserId;
            _subFxUserId = authToken.SubUserId;

            _dbName = dbname;
            _contextConfig = config;

            //InitDbConfigsFx(dbname);//采用懒加载不需要-初始化数据库配置信息
            //this.SetCurrentContext();
        }

        /// <summary>
        /// 分库新增（懒加载模式）
        /// </summary>
        /// <param name="userFx"></param>
        /// <param name="dbname"></param>
        /// <param name="config"></param>
        private void Init(UserFx userFx, string dbname = "", SiteContextConfig config = null, SubUserFx subUserFx = null)
        {
            if (userFx == null)
                throw new LogicException("用户信息不能为空");

            _currentFxUser = userFx;
            _currentFxUserId = userFx.Id;
            _dbName = dbname;
            _contextConfig = config;

            if (subUserFx != null)
            {
                _subFxUser = subUserFx;
                _subFxUserId = subUserFx.Id;
            }

            //var rp = new UserFxRepository();
            //var shop = rp.GetSystemShops(userFx.Id);
            //_currentLoginShop = shop;
            //_masterShop = shop;

            //初始化数据库配置信息
            //InitDbConfigsFx(dbname);//采用懒加载不需要-初始化数据库配置信息
            //this.SetCurrentContext();
        }


        private void Init(UserFx userFx, SiteContextConfig config = null, SubUserFx subUserFx = null)
        {
            if (userFx == null)
                throw new LogicException("用户信息不能为空");
            var rp = new UserFxRepository();
            var shop = rp.GetSystemShopByCacheSwitch(userFx.Id);
            _currentFxUser = userFx;
            _currentLoginShop = shop;
            _contextConfig = config;
            _masterShop = shop;
            if (subUserFx != null)
            {
                _subFxUser = subUserFx;
                _subFxUserId = subUserFx.Id;
            }
            //AllShops.ForEach(s =>
            //{
            //    if (s.PlatformType == shop.PlatformType && (config == null || config.NeedShopExpireTime))
            //        GetShopExpireTime(s);
            //});
            //初始化当前用户数据库配置信息
            //InitCurrentLoginShopFxDbConfigs();
            //this.SetCurrentContext();
        }

        /// <summary>
        /// 初始化上下文（未实现懒加载）
        /// </summary>
        private void Init(Shop shop, SiteContextConfig config = null)
        {
            if (shop == null)
                throw new LogicException("店铺信息不能为空");
            _currentLoginShop = shop;
            _contextConfig = config;
            var shopID = shop.Id;
            if (config == null || config.NeedRelationShops)
            {
                var rp = new ShopRelationRepository();
                var db = rp.DbConnection;
                //var grid = db.QueryMultiple(
                //    @"SELECT s.*,sr.* FROM P_Shop s WITH(NOLOCK) INNER JOIN P_ShopRelation sr WITH(NOLOCK) ON s.Id=sr.RelatedShopId where sr.ShopId=@Id AND (sr.IsDisabled IS NULL OR sr.IsDisabled=0);--主店铺关联的子店铺
                //  SELECT s.*,sr.* FROM P_Shop s WITH(NOLOCK) INNER JOIN P_ShopRelation sr WITH(NOLOCK) ON s.Id=sr.ShopId AND sr.RelatedShopId=@Id;  --子店铺所属的主店铺
                //", new { Id = shop.Id });
                var sql_one = $@"SELECT s.*,sr.* FROM P_ShopRelation sr WITH (NOLOCK) INNER JOIN P_Shop s WITH (NOLOCK) ON s.Id =sr.RelatedShopId WHERE sr.ShopId = @Id AND (sr.IsDisabled IS NULL OR sr.IsDisabled =0) OPTION (FORCE ORDER,LOOP JOIN)";

                //                var sql_one = $@"SELECT
                //	s.id AS P_Shop_Id,
                //	sr.id AS P_ShopRelation_Id INTO #P_Shop_ShopRelation_Id 
                //FROM
                //	P_ShopRelation sr WITH ( NOLOCK, INDEX = IX_ShopId_IsDisabled, FORCESEEK )
                //	INNER JOIN P_Shop s WITH ( NOLOCK ) ON s.Id= sr.RelatedShopId 
                //WHERE
                //	sr.ShopId=@Id 
                //	AND ( sr.IsDisabled IS NULL OR sr.IsDisabled= 0 ) OPTION ( FORCE ORDER, LOOP JOIN );
                //SELECT s.*,sr.* FROM #P_Shop_ShopRelation_Id pid
                //	INNER JOIN P_ShopRelation sr WITH ( NOLOCK ) ON pid.P_ShopRelation_Id = sr.Id
                //	INNER JOIN P_Shop s WITH ( NOLOCK ) ON pid.P_Shop_Id = s.Id OPTION ( FORCE ORDER, LOOP JOIN );";
                var sql_two = $@"SELECT s.*,sr.* FROM P_Shop s WITH(NOLOCK) INNER JOIN P_ShopRelation sr WITH(NOLOCK) ON s.Id=sr.ShopId AND sr.RelatedShopId=@Id;";

                var grid = db.QueryMultiple($"{sql_one}{sql_two}", new { Id = shop.Id });
                ChildShop = grid.Read<Shop, ShopRelation, Shop>((s, sr) => { s.Relation = sr; return s; }).ToList();
                _masterShop = grid.Read<Shop, ShopRelation, Shop>((s, sr) => { s.Relation = sr; return s; }).FirstOrDefault() ?? this.CurrentLoginShop; //没有主店铺时，MasterShop为自己。

                //当前店铺不为主店铺时，需查出主店铺下的所有子店铺，用于 子店铺间相互切换
                if (MasterShop.Id != shopID)// &&MasterShop?.Relation.IsVisibleToEachOther==true
                {
                    //var sql = "SELECT s.*,sr.* FROM P_Shop s WITH(NOLOCK) INNER JOIN P_ShopRelation sr WITH(NOLOCK) ON s.Id=sr.RelatedShopId where sr.ShopId=@Id AND (sr.IsDisabled IS NULL OR sr.IsDisabled=0);--主店铺关联的子店铺";

                    var sql = $@"SELECT s.*,sr.* FROM P_ShopRelation sr WITH (NOLOCK) INNER JOIN P_Shop s WITH (NOLOCK) ON s.Id =sr.RelatedShopId WHERE sr.ShopId = @Id AND (sr.IsDisabled IS NULL OR sr.IsDisabled =0) OPTION (FORCE ORDER,LOOP JOIN)";
                    //                    var sql = $@"SELECT
                    //	s.id AS P_Shop_Id,
                    //	sr.id AS P_ShopRelation_Id INTO #P_Shop_ShopRelation_Id 
                    //FROM
                    //	P_ShopRelation sr WITH ( NOLOCK, INDEX = IX_ShopId_IsDisabled, FORCESEEK )
                    //	INNER JOIN P_Shop s WITH ( NOLOCK ) ON s.Id= sr.RelatedShopId 
                    //WHERE
                    //	sr.ShopId=@Id 
                    //	AND ( sr.IsDisabled IS NULL OR sr.IsDisabled= 0 ) OPTION ( FORCE ORDER, LOOP JOIN );
                    //SELECT s.*,sr.* FROM #P_Shop_ShopRelation_Id pid
                    //	INNER JOIN P_ShopRelation sr WITH ( NOLOCK ) ON pid.P_ShopRelation_Id = sr.Id
                    //	INNER JOIN P_Shop s WITH ( NOLOCK ) ON pid.P_Shop_Id = s.Id OPTION ( FORCE ORDER, LOOP JOIN )";

                    var relationShops = db.Query<Shop, ShopRelation, Shop>(sql, (s, sr) => { s.Relation = sr; return s; }, new { Id = MasterShop.Id }).ToList();
                    SiblingShop = relationShops.Where(m => m.Id != shopID).ToList();
                    CurrentLoginShop.Relation = relationShops.Where(m => m.Id == shopID).FirstOrDefault()?.Relation;
                }

                InitOpenPlatformAppInfo();


                //查询当前店铺及其关联店铺是否有分库分表迁移中店铺，若是迁移中，则不允许操作
                var migrateSql = "SELECT ShopId FROM dbo.P_DataMigrateTask WITH(NOLOCK) WHERE ShopId IN@shopIds AND MigrateStatus='Doing'";
                var migratingShopIds = db.Query<int>(migrateSql, new { shopIds = AllShops.Select(s => s.Id) }).ToList();
                if (migratingShopIds != null && migratingShopIds.Any())
                    MigratingShopNames = AllShops.Where(s => migratingShopIds.Contains(s.Id)).Select(s => s.NickName).ToList() ?? new List<string>();
                else
                    MigratingShopNames = new List<string>();
            }

            //AllShops.ForEach(s =>
            //{
            //    if (s.PlatformType == shop.PlatformType && (config == null || config.NeedShopExpireTime))
            //        GetShopExpireTime(s);
            //    if (CurrentLoginUser != null)
            //    {
            //        s.User = CurrentLoginUser;
            //    }
            //    else
            //    {
            //        //s.IsPayNeedRegister = true;
            //    }
            //});
            //初始化数据库配置信息
            InitDbConfigs();
            //InitAlibabaSystemVersion();
        }




        private void InitOpenPlatformAppInfo()
        {
            var opens = AllShops?.Where(s => s.PlatformType == PlatformType.OpenV1.ToString() && s.OpenPlatformAppId > 0)?.ToList();
            if (opens != null && opens.Any())
            {
                var ids = opens.Select(s => s.OpenPlatformAppId).Distinct().ToList();
                var apps = _shopService.DbConnection.Query<OpenPlatformApp>("select * from P_OpenPlatformApp where id IN @ids", new { ids }).ToList();
                if (apps != null)
                {
                    opens.ForEach(s =>
                    {
                        var app = apps.FirstOrDefault(t => t.Id == s.OpenPlatformAppId);
                        if (app != null)
                            s.OpenPlatformApp = app;
                    });
                }
            }
        }

        ///// <summary>
        ///// 分单数据库初始化方法--注释该方法，统一使用懒加载
        ///// </summary>
        //private void InitCurrentLoginShopFxDbConfigs()
        //{
        //    if (_allDbConfigs == null)
        //    {
        //        _allDbConfigs = new List<DbConfigModel>();
        //        if (DbConfig != null)
        //            _allDbConfigs.Add(DbConfig);
        //        var configs = DbPolicyExtension.GetConfigFx(new List<int> { CurrentShopId });
        //        if (configs != null && configs.Any())
        //            _allDbConfigs.AddRange(configs);
        //        var d = _allDbConfigs.Where(x => x.DbConfig != null).OrderByDescending(x => x.DbConfig.FromFxDbConfig).FirstOrDefault(x => x.DbConfig.ShopId == CurrentShopId);
        //        CurrentDbConfig = d;
        //        if (d != null && this.CurrentShopId == d?.DbConfig.ShopId)
        //            DbConfig = d;
        //    }
        //}

        /// <summary>
        /// 打单系统-数据库初始化方法
        /// </summary>
        private void InitDbConfigs()
        {
            if (_allDbConfigs == null)
            {
                _allDbConfigs = new List<DbConfigModel>();
                if (DbConfig != null)
                    _allDbConfigs.Add(DbConfig);
                var ids = AllShops.Where(s => s.DbConfig == null).Select(s => s.Id).ToList();
                if (MasterShop != null && ids.Contains(MasterShop.Id) == false)
                    ids.Add(MasterShop.Id);
                if (!ids.IsNullOrEmptyList())
                {
                    var configs = new List<DbConfigModel>();
                    configs = DbPolicyExtension.GetConfig(ids);
                    if (configs != null && configs.Any())
                        _allDbConfigs.AddRange(configs);
                }

                foreach (var shop in AllShops)
                {
                    var d = _allDbConfigs.Where(x => x.DbConfig != null).OrderByDescending(x => x.DbConfig.FromFxDbConfig).FirstOrDefault(x => x.DbConfig.ShopId == shop.Id);
                    if (d != null)
                        shop.DbConfig = d;
                    if (this.CurrentShopId == d?.DbConfig.ShopId)
                        DbConfig = d;
                }

                this._currentDbConfig = DbConfig;

                if (MasterShop != null)
                    MasterShop.DbConfig = _allDbConfigs.Where(x => x.DbConfig != null).OrderByDescending(a => a.DbConfig.FromFxDbConfig).FirstOrDefault(x => x.DbConfig?.ShopId == MasterShop.Id);
            }
        }

        #endregion

        #region 分单分库配置相关

        /// <summary>
        /// 是否使用冷库（即：订单冷数据从冷库中读取）
        /// </summary>
        public bool IsUseColdDb { get { return CurrentDbSettingConfig.IsUseColdDb; } }

        /// <summary>
        /// 是否写数据到冷库（即：订单冷数据都需要同步到冷库）
        /// </summary>
        public bool IsWriteToColdDb { get { return CurrentDbSettingConfig.IsWriteToColdDb; } }


        private DbConfigModel _currentDbSettingConfig = null;
        /// <summary>
        /// (当前登录店铺)数据库配置-用户配置库
        /// </summary>
        public DbConfigModel CurrentDbSettingConfig
        {
            get
            {
                if (this._currentDbSettingConfig == null)
                {
                    InitDbConfigsFx(this._dbName);
                }
                return this._currentDbSettingConfig;
            }
        }

        private DbConfigModel _currentDbConfig = null;
        /// <summary>
        /// (当前登录店铺)数据库配置信息-当前访问库（根据传入的DBname获得,有可能是其他平台：抖店，京东）
        /// </summary>
        public DbConfigModel CurrentDbConfig
        {
            get
            {
                if (this._currentDbConfig == null)
                {
                    if (_contextConfig != null && _contextConfig.NeedLoadFxUser == false)
                        InitDbConfigs();
                    else
                        InitDbConfigsFx(this._dbName);
                }
                return this._currentDbConfig;
            }
            set
            {
                _currentDbConfig = value; //尽量少用
            }
        }

        private List<DbConfigModel> _currentDbAreaConfig = null;
        /// <summary>
        /// (当前登录店铺) 数据库分库配置信息-所有相关库
        /// </summary>
        public List<DbConfigModel> CurrentDbAreaConfig
        {
            get
            {
                if (this._currentDbAreaConfig == null)
                {
                    InitDbConfigsFx(this._dbName);
                }
                return this._currentDbAreaConfig;
            }
        }

        /// <summary>
        /// 初始化用户的数据库分库配置
        /// 1.无业务数据，读取配置库,当前连接为配置库
        /// 2.仅有一个库有业务数据，且该库为配置库，则读取配置库，当前连接为配置库
        /// 3.仅有一个库有业务数据，且该库不为配置库，则读取业务库和配置库，当前连接为匹配输入，如果无法匹配，当前连接则为业务库
        /// 4.多个库有业务数据，且其中之一为配置库，则读取多个业务库，当前连接匹配输入，如无法匹配，当前连接为业务数据最多的库
        /// 5.多个库有业务数据，且其中不包括配置库，则读取多个业务库和配置库，当前连接匹配输入，如无法匹配，当前连接为业务数据最多的库
        /// </summary>
        /// <param name="dbname"></param>
        private void InitDbConfigsFx(string dbname)
        {
            var curShopId = this.CurrentShopId;
            var curFxUserId = this.CurrentFxUserId;

            //优先取FxDbConfig的数据
            //获取当前登录用的自己所在的库
            var dbConfigModelList = DbPolicyExtension.GetConfigFx(curFxUserId, curShopId);
            var dbSetting = dbConfigModelList.OrderByDescending(a => a.DbConfig.FromFxDbConfig).FirstOrDefault();
            //设置--数据库配置-用户配置库
            this._currentDbSettingConfig = dbSetting;
            this._currentDbConfig = dbSetting;
            //抖店完全使用新的库，不需要查询其他分区情况 || 跨境站点完全使用新的库，不需要查询其他分区情况
            if (CustomerConfig.UseFxDbConfigCloudPlatformTypes.Contains(CustomerConfig.CloudPlatformType) ||
                (CustomerConfig.IsCrossBorderSite || GetIsCrossBorder()))
            {
                this._currentDbConfig = dbSetting; //设置--数据库配置信息
                this._currentDbAreaConfig = new List<DbConfigModel> { dbSetting }; //设置-- 数据库分库配置
                return;
            }

            //Log.Debug(
            //    $"当前用户ID：{curFxUserId}|{curShopId},数据库名称：{dbname},当前云平台：{CustomerConfig.CloudPlatformType},当前数据配置库：{dbSetting},所有业务库配置信息：{AllDbBusiness.ToJson(true)}",
            //    $"InitDbConfigsFx_{DateTime.Now:yyyy-MM-dd}.log");

            var dbBusiness = DbPolicyExtension.GetConfigFx(curFxUserId, this.AllDbBusiness);

            //Log.Debug($"当前用户ID：{curFxUserId},当前云平台：{CustomerConfig.CloudPlatformType},附加分库后所有业务库配置信息：{dbBusiness.ToJson(true)}",
            //    $"InitDbConfigsFx_{DateTime.Now:yyyy-MM-dd}.log");

            if (dbBusiness == null
                || dbBusiness.Count < 1
                || (dbBusiness.Count == 1 && dbBusiness.FirstOrDefault().DbNameConfig.DbName == dbSetting.DbNameConfig.DbName))
            {
                this._currentDbConfig = dbSetting; //设置--数据库配置信息
                this._currentDbAreaConfig = new List<DbConfigModel>() { dbSetting };//设置-- 数据库分库配置
                return;
            }
            //Log.Debug($"当前用户ID：{curFxUserId},数据库名称：{dbname},当前云平台：{CustomerConfig.CloudPlatformType},数据库大于1分区信息（1）：{_currentDbAreaConfig.ToJson(true)}",
            //    $"InitDbConfigsFx_{DateTime.Now:yyyy-MM-dd}.log");

            this._currentDbAreaConfig = dbBusiness;//设置-- 数据库分库配置
            if (dbBusiness.Any() && dbBusiness.Count == 1)
            {
                if (dbBusiness.FirstOrDefault().DbNameConfig.DbName != dbSetting.DbNameConfig.DbName)
                {
                    this._currentDbAreaConfig = new List<DbConfigModel>() { dbBusiness.FirstOrDefault(), dbSetting };//设置-- 数据库分库配置
                    this._currentDbConfig = this._currentDbAreaConfig.FirstOrDefault(x => x.DbNameConfig.DbName == dbname);//设置--数据库配置信息
                    if (this._currentDbConfig == null)
                    {
                        this._currentDbConfig = dbSetting;//设置--数据库配置信息
                    }
                    return;
                }
            }
            //Log.Debug($"当前用户ID：{curFxUserId},数据库名称：{dbname},当前云平台：{CustomerConfig.CloudPlatformType},数据库大于1分区信息（2）：{_currentDbAreaConfig.ToJson(true)}，当前数据配置：{_currentDbConfig.ToJson(true)}",
            //    $"InitDbConfigsFx_{DateTime.Now:yyyy-MM-dd}.log");

            if (dbBusiness.Any() && dbBusiness.Count > 1)
            {
                if (!dbBusiness.Where(x => x.DbNameConfig.DbName == dbSetting.DbNameConfig.DbName).Any())
                {
                    dbBusiness.Add(dbSetting);
                }
                //this._currentDbAreaConfig = dbBusiness;//设置-- 数据库分库配置
                //this._currentDbConfig = dbBusiness.FirstOrDefault();//设置--数据库配置信息
                if (dbBusiness.Where(x => x.DbNameConfig.DbName == dbname).Any() && dbname != dbSetting.DbNameConfig.DbName)
                {
                    this._currentDbConfig = dbBusiness.Where(x => x.DbNameConfig.DbName == dbname).FirstOrDefault();//设置--数据库配置信息
                }
                if (this._currentDbConfig == null)
                {
                    this._currentDbConfig = dbSetting;//设置--数据库配置信息
                }
                //Log.Debug($"当前用户ID：{curFxUserId},数据库名称：{dbname},当前云平台：{CustomerConfig.CloudPlatformType},数据库大于1分区（3）当前数据配置：{_currentDbConfig.ToJson(true)}",
                //    $"InitDbConfigsFx_{DateTime.Now:yyyy-MM-dd}.log");
                return;
            }
        }

        /// <summary>
        /// 获取当前用户相关联厂家的所有相关业务库
        /// </summary>
        /// <returns></returns>
        public List<DbConfigModel> GetSupplierDbs(int fxUserId, List<int> supplierFxUserIds)
        {
            var dbBusiness = DbPolicyExtension.GetConfigFxBySupplier(supplierFxUserIds, this.AllDbBusiness, true);
            var dbAreas = CurrentDbAreaConfig.Select(x => x.DbNameConfig).ToList();
            //对比自己拥有的分区与厂家的分区，不存在的加一条路径流
            if (dbBusiness != null && dbBusiness.Any())
            {
                dbBusiness.ForEach(db =>
                {
                    if (dbAreas.Any(a => a.DbName == db.DbNameConfig.DbName) == false)
                    {
                        try
                        {
                            var model = new PathFlowNode { PathFlowCode = $"For1688Supplier-{fxUserId}", FxUserId = fxUserId, DownFxUserId = 0, UpFxUserId = 0 };
                            new PathFlowNodeRepository(db.ConnectionString).Add(model);
                        }
                        catch (Exception ex)
                        { }
                    }
                });
            }

            //清理缓存
            FxCaching.RefeshCache(FxCachingType.FxUser, fxUserId);
            FxCaching.RefeshCache(FxCachingType.DbConfigModelPathFlowNode, fxUserId);
            return dbBusiness;
        }
        /// <summary>
        /// 获取用户相关的精选业务库
        /// </summary>
        /// <returns></returns>
        public List<DbConfigModel> GetAgentAlibabaDbs(List<int> fxUserIds)
        {
            var dbs = new DbConfigRepository().GetBusinessDbConfigsByAppNamesWithLocations(
                new List<string> { "fx", "fx_cloud" },
                new List<string> { CloudPlatformType.Alibaba.ToString() });
            //Log.WriteLine($"dbs={dbs?.ToJson()}");
            //dbs = dbs.Where(a => a.DbServer.Location == CloudPlatformType.Alibaba.ToString()).ToList();
            var dbBusiness = DbPolicyExtension.GetConfigFxBySupplier(fxUserIds, dbs, true);
            //Log.WriteLine($"dbBusiness={dbBusiness?.ToJson()}");
            return dbBusiness;
        }
        #endregion

        #region 抽象类（检查过期，用户店铺列表，初始化系统版本）
        //protected abstract void InitAlibabaSystemVersion();
        /// <summary>
        /// 检查店铺是否过期
        /// </summary>
        public abstract Shop GetShopExpireTime(Shop shop);
        public abstract Tuple<User, List<Shop>> GetUserShops(int shopId);
        #endregion

        #region 公用属性方法
        /// <summary>
        /// 平台类型字符串转枚举类型
        /// </summary>
        public static PlatformType ConvertPlatform(string platform)
        {

            //转换platyform
            var platformTypeStr = CustomerConfig.ConvertPlatformType(platform);
            PlatformType platformType;
            var parsed = System.Enum.TryParse<PlatformType>(platformTypeStr, out platformType);
            if (parsed == false)
            {
                //   return null;
                throw new LogicException("平台类型转换失败！");
            }
            return platformType;
        }



        /// <summary>
        /// 设置当前上下文数据(存储key)
        /// </summary>
        private void SetCurrentContext()
        {
            if (IsAvailableToThread)
            {
                CallContext.LogicalSetData(_siteKey, this);
            }
            else
            {
                CallContext.SetData(_siteKey, this);
            }
        }


        /// <summary>
        /// 初始站点上下文是否跨境
        /// </summary>
        /// <param name="data"></param>
        public static void SetIsCrossBorder(object data)
        {
            if (IsAvailableToThread)
            {
                CallContext.LogicalSetData(CustomerConfig.ExportTaskAppContextCrossBorderKey, data);
            }
            else
            {
                CallContext.SetData(CustomerConfig.ExportTaskAppContextCrossBorderKey, data);
            }
        }

        /// <summary>
        /// 跨境非站点使用
        /// </summary>
        /// <returns></returns>
        public static bool GetIsCrossBorder()
        {
            try
            {
                object crossBorderStatus = IsAvailableToThread
                    ? CallContext.LogicalGetData(CustomerConfig.ExportTaskAppContextCrossBorderKey)
                    : CallContext.GetData(CustomerConfig.ExportTaskAppContextCrossBorderKey);

                // 检查是否为 null 或者是否可以转换为布尔值
                return crossBorderStatus != null && (bool)crossBorderStatus;
            }
            catch (Exception)
            {
                return false;
            }
        }



        /// <summary>
        /// 店铺是否正在迁移中
        /// </summary>
        public bool IsMigrating
        {
            get
            {
                return MigratingShopNames != null && MigratingShopNames.Any();
            }
        }
        #endregion

        #region "是否显示跨境相关功能"

        //private bool? _IsShowCrossBorder;

        //public bool IsShowCrossBorder
        //{
        //    get
        //    {
        //        if (_IsShowCrossBorder.HasValue)
        //            return _IsShowCrossBorder.Value;

        //        //读取跨境功能开启的配置
        //        var commonSetRepo = new CommonSettingRepository();

        //        _IsShowCrossBorder = commonSetRepo.IsShowCrossBorder(CurrentShopId);

        //        return _IsShowCrossBorder.Value;
        //    }
        //}

        #endregion

        #region 打单系统属性，忽略
        /// <summary>
        /// 正在迁移数据的店铺
        /// </summary>
        public List<string> MigratingShopNames { get; private set; }

        /// <summary>
        /// 子店铺
        /// </summary>
        public List<Shop> ChildShop { get; private set; }


        /// <summary>
        /// 兄弟店铺
        /// </summary>
        public List<Shop> SiblingShop { get; private set; }


        #region 拿货小标签模板的ShopId

        private int __naHuoTemplateShopId;
        /// <summary>
        /// 发货单模板的ShopId，根据店铺关联确认的
        /// </summary>
        public int NaHuoTemplateShopId
        {
            get
            {
                if (__naHuoTemplateShopId > 0)
                    return __naHuoTemplateShopId;
                var sc = BaseSiteContext.Current;
                if (sc.MasterShop != null && sc.MasterShop.Relation != null && sc.MasterShop.Relation.IsUseNaHuoTemplate)
                    __naHuoTemplateShopId = sc.MasterShop.Id;
                else
                    __naHuoTemplateShopId = this.CurrentShopId;
                return __naHuoTemplateShopId;
            }
        }

        #endregion

        /// <summary>
        /// 与当前登录店铺平台相同的店铺ID
        /// </summary>
        public List<int> SamePlatformShopIds
        {
            get
            {
                var ids = AllSamePlatformTypeShops.Select(x => x.Id).ToList();
                return ids;
            }
        }
        public SubUser CurrentLoginSubUser { get; private set; }
        private int __sendGoodTemplateShopId;
        /// <summary>
        /// 发货单模板的ShopId，根据店铺关联确认的
        /// </summary>
        public int SendGoodTemplateShopId
        {
            get
            {
                if (__sendGoodTemplateShopId > 0)
                    return __sendGoodTemplateShopId;
                var sc = BaseSiteContext.Current;
                if (sc.MasterShop != null && sc.MasterShop.Relation != null && sc.MasterShop.Relation.IsUseFahuoTemplate)
                    __sendGoodTemplateShopId = sc.MasterShop.Id;
                else
                    __sendGoodTemplateShopId = this.CurrentShopId;
                return __sendGoodTemplateShopId;
            }
        }
        /// <summary>
        /// 切换店铺的模型
        /// </summary>
        public SwitchShopViewModel SwitchShopViewModel
        {
            get
            {
                var shops = BaseSiteContext.Current.AllShops?.Where(s => s.Id != this.CurrentShopId).ToList();
                var model = new SwitchShopViewModel { Current = BaseSiteContext.Current.CurrentLoginShop, Relateds = shops, Master = MasterShop };

                return model;
            }
        }

        #endregion

        /// <summary>
        /// 基础商品设置键
        /// </summary>
        private const string BaseProductSettingKey = "/ErpWeb/FenDan/BaseProductSetting";

        /// <summary>
        /// 获取基础商品设置
        /// </summary>
        public BaseProductSettingsModel BaseProductSetting
        {
            get
            {
                var commonSetting = _commonSettingRepository.Get(BaseProductSettingKey, CurrentShopId);
                var model = commonSetting?.Value?.ToObject<BaseProductSettingsModel>();
                return model ?? new BaseProductSettingsModel();
            }
        }
    }
}
