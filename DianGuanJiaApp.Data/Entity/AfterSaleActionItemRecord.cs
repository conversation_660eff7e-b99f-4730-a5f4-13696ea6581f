using Dapper;

namespace DianGuanJiaApp.Data.Entity
{
    [Table("AfterSaleActionItemRecord")]
    public class AfterSaleActionItemRecord
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 唯一码
        /// </summary>
        public string RefundActionItemRecordCode { get; set; }

        /// <summary>
        /// 记录关联码
        /// </summary>
        public string RefundActionRecordCode { get; set; }

        /// <summary>
        /// 下游平台单项代码
        /// </summary>
        public string SourceOrderItemCode { get; set; }

        /// <summary>
        /// 下游平台单项编号
        /// </summary>
        public string SourceSubItemId { get; set; }

        /// <summary>
        /// 下游退款数量
        /// </summary>
        public int SourceRefundQuality { get; set; }

        /// <summary>
        /// 上游平台单项代码
        /// </summary>
        public string UpOrderItemCode { get; set; }

        /// <summary>
        /// 上游平台单项编号
        /// </summary>
        public string UpSubItemId { get; set; }
    }
}