using Dapper;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Utility.Extension;
using System;

namespace DianGuanJiaApp.Data.Entity
{
    /// <summary>
    /// 售后单地址表
    /// </summary>
    [Table("AfterSaleAddress")]
    public class AfterSaleAddress
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// 唯一编码
        /// (FxuserId + TargetFxuserId + PlatformType + AccountNumber).ToShortMd5();
        /// </summary>
        public string Code
        {
            get
            {
                return $"{FxuserId}_{PlatformType}_{TargetFxuserId}".ToShortMd5();
            }
        }
        
        /// <summary>
        /// 平台
        /// </summary>
        public string PlatformType { get; set; }
        
        /// <summary>
        /// 创建者用户Id
        /// </summary>
        public int FxuserId { get; set; }

        /// <summary>
        /// 厂家用户Id
        /// </summary>
        public int TargetFxuserId { get; set; }

        /// <summary>
        /// 厂家账号
        /// </summary>
        public string AccountNumber { get; set; }

        /// <summary>
        /// 厂家名称
        /// </summary>
        public string AccountName { get; set; }

        /// <summary>
        /// 1: 已完善 2: 未完善
        /// </summary>
        public int Isimprove { get; set; }

        /// <summary>
        /// 地址信息Json，有值的话等于是自定义
        /// <see cref="BusinessCardAfterSaleAddress"/>
        /// </summary>
        public string ContentJson { get; set; }

        /// <summary>
        /// 0: 自定义 1: 自动同步
        /// </summary>
        public int IsAutosync { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 厂家账号 -- 前端展示
        /// </summary>
        [NotMapped]
        public string SupplierMobileAndRemark
        {
            get
            {
                var name = AccountNumber + (AccountName.IsNullOrEmpty() ? (AccountName.IsNullOrEmpty() ? "" : $"({AccountName})") : $"({AccountName})");
                return name;
            }
        }

        /// <summary>
        /// 厂家售后地址-字符串 -- 前端展示
        /// </summary>
        [NotMapped]
        public string AddressStr { get; set; }

    }
}
