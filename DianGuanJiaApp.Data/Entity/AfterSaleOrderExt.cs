using Dapper;
using System;

namespace DianGuanJiaApp.Data.Entity
{
    /// <summary>
    /// 售后订单扩展信息实体类
    /// </summary>
    [Table("AfterSaleOrderExt")]
    public class AfterSaleOrderExt
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 所属平台
        /// </summary>
        public string PlatformType { get; set; }

        /// <summary>
        /// 售后ID
        /// </summary>
        public string AfterSaleId { get; set; }

        /// <summary>
        /// 平台订单ID
        /// </summary>
        public string PlatformOrderId { get; set; }

        /// <summary>
        /// 售后编号
        /// </summary>
        public string AfterSaleCode { get; set; }

        /// <summary>
        /// 店铺ID
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 换货信息
        /// <see cref="Model.ExchangeSkuInfo"/>
        /// </summary>
        public string ExchangeGoods { get; set; }

        /// <summary>
        /// 凭证信息-图片
        /// </summary>
        public string Evidence { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }
}
