using System;
using Dapper;

namespace DianGuanJiaApp.Data.Entity
{
    /// <summary>
    /// 售后订单项表
    /// </summary>
    [Table("AfterSaleOrderItem")]
    public partial class AfterSaleOrderItem
    {
        /// <summary>
        ///  
        /// </summary>
        [Key]
        public int Id { get; set; }
        /// <summary>
        /// 平台售后单Id，同主表
        /// </summary>
        public string AfterSaleId { get; set; }
        /// <summary>
        /// 售后单Code，同主表
        /// </summary>
        public string AfterSaleCode { get; set; }
        /// <summary>
        /// 平台订单Id，同主表
        /// </summary>
        public string PlatformOrderId { get; set; }
        /// <summary>
        /// 订单Code，同主表
        /// </summary>
        public string OrderCode { get; set; }
        /// <summary>
        /// 同P_OrderItem表或子订单Id
        /// </summary>
        public string SubItemId { get; set; }
        /// <summary>
        /// 所属店铺Id，同主表
        /// </summary>
        public int ShopId { get; set; }
        /// <summary>
        /// 所属FxUserId，同主表
        /// </summary>
        public int UserId { get; set; }
        /// <summary>
        /// 同P_OrderItem表
        /// </summary>
        public string ProductId { get; set; }
        /// <summary>
        /// 同P_OrderItem表
        /// </summary>
        public string SkuId { get; set; }
        /// <summary>
        /// 同P_OrderItem表或(PlatformOrderId + SubItemId + ShopId).ToShortMd5()
        /// </summary>
        public string OrderItemCode { get; set; }
        /// <summary>
        /// 同P_OrderItem表或(ProductId + ShopId + UserId).ToShortMd5()
        /// </summary>
        public string ProductCode { get; set; }
        /// <summary>
        /// 同P_OrderItem表或(ProductId + (SkuId.IsNullOrEmpty() ? ProductId : SkuId).ToString2() + ShopId + UserId).ToShortMd5()
        /// </summary>
        public string SkuCode { get; set; }
        /// <summary>
        /// 售后件数
        /// </summary>
        public int AfterSaleCount { get; set; }
        /// <summary>
        /// 售后退款金额
        /// </summary>
        public decimal RefundAmount { get; set; }
        /// <summary>
        /// 售后退运费金额
        /// </summary>
        public decimal RefundPostAmount { get; set; }
        /// <summary>
        /// 库存状态：0=未入库；1=已入库；
        /// </summary>
        public int StockState { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }
        /// <summary>
        /// 唯一项(AfterSaleCode + OrderItemCode)
        /// </summary>
        public string AfterSaleItemCode { get { return (AfterSaleCode + OrderItemCode); } }
        public string OrignalOrderId { get; set; }
        public decimal? Price { get; set; }
        public decimal? ItemAmount { get; set; }
        public string ProductSubject { get; set; }
        public string ProductCargoNumber { get; set; }
        public string CargoNumber { get; set; }
        public string ProductImgUrl { get; set; }
        public string SpecId { get; set; }
        public int? OrignalCount { get; set; }
        public DateTime? ItemSendTime { get; set; }
        public string Unit { get; set; }
        public decimal? Weight { get; set; }
        public string WeightUnit { get; set; }
        public int? LogisticsStatus { get; set; }
        public decimal? EntryDiscount { get; set; }
        public string Color { get; set; }
        public string Size { get; set; }
        /// <summary>
        /// 手工单才有，来自P_OrderItem.Status
        /// </summary>
        public string ItemStatus { get; set; }
        /// <summary>
        /// 手工单才有，来自P_OrderItem.RefundStatus
        /// </summary>
        public string ItemRefundStatus { get; set; }
        /// <summary>
        /// 手工单才有，来自LogicOrderItem.PrintState
        /// </summary>
        public int PrintState { get; set; }

        /// <summary>
        /// 订单项对应的逻辑单的pathflowcode
        /// </summary>
        public string PathFlowCode { get; set; }

        /// <summary>
        /// 订单项对应的逻辑单的LogicOrderId
        /// </summary>
        public string LogicOrderId { get; set; }

        /// <summary>
        /// 厂家的用户Id
        /// </summary>
        [NotMapped]
        public int SupplierFxUserId { get; set; }
        /// <summary>
        /// 是否要过滤掉
        /// </summary>
        [NotMapped]
        public bool IsFilter { get; set; }
        
        /// <summary>
        /// 售后类型
        /// RETURN_REFUND=0,//退货退款
        /// ONLY_REFUND=1,//仅退款
        /// EXCHANGE=2,//换货
        /// REISSUE=3,//补发
        /// CHANGE_WAYBILLCODE=4,//变更快递单号
        /// PRICE_PROTECTION=5,//价保
        /// OTHER= 99,//其他
        /// </summary>
        public int? AfterSaleType { get; set; }


        
    }
}
