using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using MongoDB.Bson.Serialization.Attributes;

namespace DianGuanJiaApp.Data.Entity
{
    
    [Table("AppOrderList")]
     public partial class AppOrderList : BaseEntity
    {
        [Key]
        [BsonIgnore]
        public int Id { get; set; }
        public string MemberId { get; set; }
        public string OrderItemNum { get; set; }
        public string BizStatus { get; set; }
        public string BizStatusExt { get; set; }
        public string ProductName { get; set; }
        [BsonIgnore]
        public string ProductCode { get; set; }
        public string PaymentAmount { get; set; }
        public string ExecutePrice { get; set; }
        public string RefundFee { get; set; }
        public DateTime GmtCreate { get; set; }
        public DateTime GmtServiceEnd { get; set; }
        public DateTime GmtServiceBegin { get; set; }
        public DateTime GmtConfirm { get; set; }
        public string PlatformType { get; set; }
        public string LoginId { get; set; }
        public string AppKey { get; set; }
        //�û�ID
        public int UserId { get; set; }
        /// <summary>
        /// ��ע��Ϣ
        /// </summary>
        public string Remark { get; set; }
        public string SkuVersion { get; set; }
        public string SkuName { get; set; }
    }
}
