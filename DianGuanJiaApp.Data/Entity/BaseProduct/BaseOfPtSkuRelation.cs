using System;
using Dapper;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json;

namespace DianGuanJiaApp.Data.Entity.BaseProduct
{
    /// <summary>
    /// 基础商品规格与平台商品规格关系表
    /// </summary>
    [Table("BaseOfPtSkuRelation")]
    public class BaseOfPtSkuRelation : BaseEntity
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// 数据行唯一标识
        /// </summary>
        [NotMapped]
        public string UniqueKey { get { return $"{this.ProductSkuCode}{this.BaseProductSkuUid}".ToShortMd5(); } }

        /// <summary>
        /// 基础商品SKU唯一码，纯数字，来自BaseProductSku.UId
        /// </summary>
        public long BaseProductSkuUid { get; set; }

        /// <summary>
        /// 基础商品唯一码，纯数字，来自BaseProduct.Uid
        /// </summary>
        public long BaseProductUid { get; set; }

        /// <summary>
        /// 默认0，不使用仓库，不管理库存，值为1时需创建WarehouseSkuBindRelation
        /// </summary>
        public bool IsUseWarehouse { get; set; }

        /// <summary>
        /// 店铺商品SKU平台ID，来自ProductSKU.SkuId
        /// </summary>
        public string ProductSkuPtId { get; set; }

        /// <summary>
        /// 店铺商品平台ID，来自ProductSKU.PlatformId
        /// </summary>
        public string ProductPtId { get; set; }

        /// <summary>
        /// 店铺商品SKU唯一码，来自ProductSKU.SkuCode
        /// </summary>
        public string ProductSkuCode { get; set; }

        /// <summary>
        /// 店铺商品唯一码，来自Product.ProductCode
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// 店铺商品的店铺ID，来自Product.ShopId
        /// </summary>
        public int ProductShopId { get; set; }

        /// <summary>
        /// 店铺商品的所属用户ID，来自Product.SourceUserId
        /// </summary>
        public int ProductFxUserId { get; set; }

        /// <summary>
        /// 店铺商品的平台类型，来自Product.PlatformType
        /// </summary>
        public string ProductPlatformType { get; set; }

        /// <summary>
        /// 分单用户ID
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 状态，默认1正常，0已删除
        /// </summary>
        public int Status { get; set; } = 1;

        /// <summary>
        /// 默认0手工关联，1自动关联
        /// </summary>
        public int RelationType { get; set; } = 0;
        
        /// <summary>
        /// 所属云平台
        /// </summary>
        public string CloudPlatform { get; set; }

        /// <summary>
        /// 基础商品标题内容
        /// </summary>
        public string BaseProductSubject { get; set; }

        /// <summary>
        /// 基础商品Sku标题内容（基础商品规格属性值）
        /// </summary>
        public string BaseProductSkuSubject { get; set; }

        /// <summary>
        /// 基础商品商家编码，由用户填写，用户维度唯一   
        /// </summary>
        public string BaseProductSpuCode { get; set; }

        /// <summary>
        /// 基础商品Sku商家编码，由用户填写，用户维度唯一
        /// </summary>
        public string BaseProductSkuCode { get; set; }

        /// <summary>
        /// 基础商品简称
        /// </summary>
        public string BaseProductShortTitle { get; set; }

        /// <summary>
        /// 基础商品Sku简称
        /// </summary>
        public string BaseProductSkuShortTitle { get; set; }

        /// <summary>
        /// 分库数据库连接串
        /// </summary>
        [NotMapped]
        [JsonIgnore]
        public string DbConn { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [NotMapped]
        public DateTime? Temp { get; set; }

        /// <summary>
        /// 分库数据库配置
        /// </summary>
        [NotMapped]
        // [JsonIgnore]
        public DbConfigModel DbConfig { get; set; }

        /// <summary>
        /// 消息队列使用，默认0 删除， 1新增， 2 更新
        /// </summary>
        [NotMapped]
        public int Type { get; set; } = 0;

        /// <summary>
        /// 数据库名称
        /// </summary>
        [NotMapped]
        // [JsonIgnore]
        public string DbName { get; set; }
        
        /// <summary>
        /// 厂家分销价即当前用户的采购价
        /// </summary>
        [NotMapped]
        public decimal SettlePrice { get; set; }

        /// <summary>
        /// 结算价类型
        /// 0或null关联基础商品默认分销价，1手工设置结算价，2等级分销价换算结算价 
        /// </summary>
        [NotMapped]
        public int? DistributePriceChangeType { get; set; } = null;



        /// <summary>
        /// 所属云平台
        /// </summary>
        [NotMapped]
        public string CalculateCloudPlatform {
        
            get
            {
                if (string.IsNullOrEmpty(CloudPlatform))
                {
                    if(CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(ProductPlatformType))
                    {
                        return CloudPlatformType.Pinduoduo.ToString();
                    }
                    else if (CustomerConfig.FxJingDongCloudPlatformTypes.Contains(ProductPlatformType))
                    {
                        return CloudPlatformType.Jingdong.ToString();
                    }
                    else if (CustomerConfig.FxDouDianCloudPlatformTypes.Contains(ProductPlatformType))
                    {
                        return CloudPlatformType.TouTiao.ToString();
                    }
                    else
                    {
                        return CloudPlatformType.Alibaba.ToString();
                    }
                }
                else
                {
                    return CloudPlatform;
                }
            }
        }
    }
}
