using System;
using Dapper;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Data.Entity.BaseProduct
{
    /// <summary>
    /// 从店铺商品生成基础商品的记录
    /// </summary>
    [Table("GenerateBaseProductRecord")]
    public class GenerateBaseProductRecord
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 店铺商品的商品Code
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// 基础商品的Uid
        /// </summary>
        public long BaseProductUid { get; set; }

        /// <summary>
        /// 系统用户Id
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 系统店铺Id
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 商品所属平台
        /// </summary>
        public string PlatformType { get; set; }

        /// <summary>
        /// 记录状态 0：正常，-1：删除
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 唯一键 $"{ShopId}{BaseProductUid}{ProductCode}".ToShortMd5();
        /// </summary>
        public string UniqueKey => $"{ShopId}{BaseProductUid}{ProductCode}".ToShortMd5();
    }
}
