using Dapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Entity
{
    [OnlyInAliyunConfigDb]
    [Table("P_BranchShareExportExcelTask")]
    public partial class BranchShareExportExcelTask
    {
        [Key]
        public int Id { get; set; }
        /// <summary>
        /// 待执行SQL语句
        /// </summary>
        public string ExecSql { get; set; }
        /// <summary>
        /// 下载状态：0：待生成EXCEL，1：生成EXCEL中，2：已生成，3：上传服务器中，4：已上传，5：下载完成，-1：失败，-10：手动取消
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// 操作店铺ID
        /// </summary>
        public int ShopId { get; set; }
        /// <summary>
        /// 分享单号Id集
        /// </summary>
        public string ShareIds { get; set; }
        /// <summary>
        /// 下载文件地址
        /// </summary>
        public string FilePath { get; set; }
        /// <summary>
        /// 下载文件名称
        /// </summary>
        public string FileName { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public System.DateTime CreateTime { get; set; }
        public System.DateTime? UploadToServerTime { get; set; }
        /// <summary>
        /// 导出消息
        /// </summary>
        public string ExportMessage { get; set; }
        /// <summary>
        /// 花费时间(s)
        /// </summary>
        public double SpendTime { get; set; }
        /// <summary>
        /// Excel导出来源模块
        /// </summary>
        public string FromModule { get; set; }
        /// <summary>
        /// 是否已下载
        /// </summary>
        public bool IsDownload { get; set; }
        /// <summary>
        /// 文件是否已生成
        /// </summary>
        public bool IsCreate { get; set; }
        /// <summary>
        /// 分页页数
        /// </summary>
        public int PageIndex { get; set; }
        /// <summary>
        /// 分页页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalCount { get; set; }
        /// <summary>
        /// 错误重试次数
        /// </summary>
        public int RetryTimes { get; set; }     
    }

}
