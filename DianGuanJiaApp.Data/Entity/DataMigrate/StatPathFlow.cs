using System;
using System.Collections.Generic;
using Dapper;

namespace DianGuanJiaApp.Data.Entity
{

    [Table("StatPathFlow")]
    public class StatPathFlow : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 分销用户编号
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 店铺ID
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 路径流Code
        /// </summary>
        public string PathFlowCode { get; set; }

        /// <summary>
        /// 业务库DbName
        /// </summary>
        public string DbName { get; set; }

        /// <summary>
        /// 节点数
        /// </summary>
        public int NodeNum { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
    }
}