using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using Jayrock.Json.Conversion;

namespace DianGuanJiaApp.Data.Entity
{
    public partial class ExportTaskExt : ExportTask
    {
        /// <summary>
        /// 文件有效期是+7天
        /// </summary>
        public const int FileExpiredDays = 7;

        /// <summary>
        /// 文件有效期开始
        /// </summary>
        [NotMapped]
        public string ExpiredDateFrom
        {
            get
            {
                if (UploadToServerTime != null)
                {
                    return UploadToServerTime.Value.ToString("yyyy-MM-dd HH:mm");
                }
                return null;
            }
        }

        /// <summary>
        /// 文件有效期是+7天
        /// </summary>
        [NotMapped]
        public string ExpiredDateTo
        {
            get
            {
                if (UploadToServerTime != null)
                {
                    return UploadToServerTime.Value.AddDays(FileExpiredDays).ToString("yyyy-MM-dd HH:mm");
                }
                return null;
            }
        }

        /// <summary>
        /// 文件是否有效
        /// </summary>
        [NotMapped]
        public bool FileIsValidation
        {
            get
            {
                bool IsValid = false;
                if (Status == 4 || Status == 5)
                {
                    if (this.UploadToServerTime.HasValue && this.UploadToServerTime.Value.AddDays(FileExpiredDays) >= DateTime.Now)
                    {
                        IsValid = true;
                    }
                }
                return IsValid;
            }
        }

        [NotMapped]
        public string StatusDescription
        {
            get
            {
                string statusDescription = "";
                if (Status == 0 || Status == 1)
                {
                    if(Status == 0 && HopeExecuteTime != null)
                    {
                        statusDescription = "预约出账，查看";
                    }
                    else statusDescription = "任务排队中";
                }
                else if (Status == 2 || Status == 3)
                {
                    statusDescription = "任务生成中";
                }
                else if (Status == 4 || Status == 5)
                {
                    if (this.UploadToServerTime.HasValue && this.UploadToServerTime.Value.AddDays(FileExpiredDays) >= DateTime.Now)
                    {
                        statusDescription = "文件已生成";
                    }
                    else
                    {
                        statusDescription = "文件已失效";
                    }
                }
                else if (Status == -1)
                {
                    statusDescription = "任务生成失败";
                }
                else if (Status == -10)
                {
                    statusDescription = "任务已取消";
                }
                else if (Status == -99)
                {
                    statusDescription = "任务已删除";
                }
                return statusDescription;
            }
        }

        //private string _taskName = string.Empty;
        ///// <summary>
        ///// 用于前端
        ///// </summary>
        //[NotMapped]
        //public string TaskNameExt
        //{
        //    get
        //    {
        //        _taskName = this.TaskName;
        //        if (string.IsNullOrWhiteSpace(_taskName))
        //        {
        //            ////为空时候，用文件名填充
        //            //if (!string.IsNullOrWhiteSpace(this.FileName))
        //            //{
        //            //    _taskName = this.FileName;
        //            //    int lastDotIndex = _taskName.LastIndexOf(".");
        //            //    if (lastDotIndex > -1)
        //            //    {
        //            //        _taskName = _taskName.Substring(0, lastDotIndex);
        //            //    }
        //            //}
        //            //else if (!string.IsNullOrWhiteSpace(this.FromModule))
        //            //{
        //            //    //模块名填充
        //            //    _taskName = this.FromModule + this.CreateTime.ToString("yyyyMMddHHmmss");
        //            //}

        //            // 统一用模块名 + 任务创建时间
        //            if (!string.IsNullOrWhiteSpace(this.FromModule))
        //            {
        //                _taskName = this.FromModule + this.CreateTime.ToString("yyyyMMddHHmmss");
        //            }
        //            else
        //            {
        //                if (!string.IsNullOrWhiteSpace(this.FileName))
        //                {
        //                    _taskName = this.FileName;
        //                    int lastDotIndex = _taskName.LastIndexOf(".");
        //                    if (lastDotIndex > -1)
        //                    {
        //                        _taskName = _taskName.Substring(0, lastDotIndex);
        //                    }
        //                }
        //            }
        //        }

        //        return _taskName;
        //    }

        //}
        /// <summary>
        /// 是否渲染查看明细
        /// </summary>
        [NotMapped]
        public bool hideDetail
        {
            //默认不渲染，是否渲染根据前端的tab点击判断
            get
            {
                return true;
            }
        }
    }
}
