using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;


namespace DianGuanJiaApp.Data.Entity
{
    
    [Table("P_ExpressCpCodeMapping")]
     public partial class ExpressCpCodeMapping:BaseEntity
    {
        [Key]
    	public int Id { get; set; }
        /// <summary>
        /// 快递公司编码：关联使用
        /// </summary>
        public string ExpressCompanyCode { get; set; }
        /// <summary>
        /// 电子面单平台快递公司编码：取单号时使用
        /// </summary>
        public string CpCode { get; set; }
        /// <summary>
        /// 快递公司平台名称：发货时使用
        /// </summary>
        public string PlatformExpressName { get; set; }
        /// <summary>
        /// 平台类型
        /// </summary>
        public string PlatformType { get; set; }
    }
}
