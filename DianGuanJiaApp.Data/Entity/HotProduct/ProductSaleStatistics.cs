using Dapper;
using System;

namespace DianGuanJiaApp.Data.Entity.HotProduct
{
    /// <summary>
    /// 爆品商品销量汇总表（按统计周期）
    /// </summary>
    [Table("ProductSaleStatistics")]
    public class ProductSaleStatistics : BaseEntity
    {
        /// <summary>
        /// 自增ID
        /// </summary>
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// 平台商品id
        /// 来源LogicOrderItem.ItemProductId
        /// </summary>
        public string PlatformId { get; set; }

        /// <summary>
        /// 商品code
        /// 来源LogicOrderItem.ProductCode
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// 平台规格id
        ///  来源LogicOrderItem.ProductSkuId
        /// </summary>
        public string ProductSkuId { get; set; }

        /// <summary>
        /// 规格code
        /// 来源LogicOrderItem.SkuCode
        /// </summary>
        public string ProductSkuCode { get; set; }

        /// <summary>
        /// 最后的类目ID优先取CategoryId4，若为空然后3、2、1；最后取
        /// </summary>
        public string CategoryId { get; set; }

        /// <summary>
        /// 系统店铺Id
        /// 来源LogicOrder.ShopId
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 源用户ID
        /// LogicOrder.FxUserId
        /// </summary>
        public int SourceFxUserId { get; set; }

        /// <summary>
        /// 平台类型
        /// </summary>
        public string PlatformType { get; set; }

        /// <summary>
        /// 统计周期时间按天，时间精确到天，后面补0按月，时间精确到月，后面补0按年，时间精确到年，后面补0
        /// </summary>
        public DateTime StatisticsDayTime { get; set; }

        /// <summary>
        /// 统计类型：按天、按月、按季度、按年Day\Month\Season\Year
        /// </summary>
        public string StatisticsType { get; set; }

        /// <summary>
        /// 统计到的数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 状态，默认0待处理，1已汇总
        /// </summary>
        public int SumStatus { get; set; } = 0;
    }
}