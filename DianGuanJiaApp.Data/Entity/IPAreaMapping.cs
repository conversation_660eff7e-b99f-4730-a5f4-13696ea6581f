using Dapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Entity
{
    /// <summary>
    /// IP归属地
    /// </summary>
    [Table("IPAreaMapping")]
    public partial class IPAreaMapping : BaseEntity
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// 登录IP
        /// </summary>
        public string IP { get; set; }

        /// <summary>
        /// IPRegion.ToShortMd5()
        /// </summary>
        public string IPRegionCode { get; set; }

        /// <summary>
        /// IP归属国家
        /// </summary>
        public string IPContry { get; set; }

        /// <summary>
        /// IP归属省
        /// </summary>
        public string IPProvince { get; set; }

        /// <summary>
        /// IP归属市
        /// </summary>
        public string IPCity { get; set; }

        /// <summary>
        /// IP归属区
        /// </summary>
        public string IPCounty { get; set; }

        /// <summary>
        /// 国家省市区
        /// </summary>
        public string IPRegion { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// IP接口返回的原始报文
        /// </summary>
        public string Json { get; set; }
    }
}
