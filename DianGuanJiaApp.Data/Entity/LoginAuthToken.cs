using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Data.Entity
{
    /// <summary>
    /// 登陆授权Token
    /// </summary>
    [SyncToConfigDb]

    [Table("P_LoginAuthToken")]
    public partial class LoginAuthToken : BaseEntity
    {

        [Key]
        public int Id { get; set; }
        public string Sign { get; set; }
        public System.DateTime? CreateTime { get; set; }
        public DateTime? ExpiredTime { get; set; }
        public int ShopId { get; set; }
        public bool IsExpired { get; set; }
        /// <summary>
        /// 是否是快捷方式，快捷方式过期时间比链接方式的过期时间长，默认一个月
        /// </summary>
        public bool IsQuickLink { get; set; }
        /// <summary>
        /// 是否是从主店铺切换过来的，过期时间和主店铺一样
        /// </summary>
        public bool IsFromParent { get; set; }

        [NotMapped]
        public string ClientSign { get; set; }

        public void RefreshExpiredTime()
        {
            IsExpired = false;
            if (IsQuickLink)
                ExpiredTime = DateTime.Now.AddMonths(1);
            else
                ExpiredTime = DateTime.Now.AddDays(15);
            CreateTime = DateTime.Now;
        }

        /// <summary>
        /// 计算列：根据Sign和ID得到的Token信息
        /// </summary>
        [NotMapped]
        public string Token
        {
            get
            {
                return DES.EncryptUrl(Id.ToString(), CustomerConfig.LoginCookieEncryptKey);
            }
        }

        /// <summary>
        /// 子账号ID
        /// </summary>
        public int SubUserId { get; set; }
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        public string PlatformType { get; set; }
        public int FromId { get; set; }
        public string IP { get; set; }
        /// <summary>
        /// 分销系统用户ID
        /// </summary>
        public int FxUserId { get; set; }
    }
}
