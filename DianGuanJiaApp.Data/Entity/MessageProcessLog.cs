using Dapper;
using DianGuanJiaApp.Data.MongoRepository;
using DianGuanJiaApp.Utility.Extension;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Entity
{
    /// <summary>
    /// 消息消费日志
    /// </summary>
    [Table("MessageProcessLog")]
    [CollectionName("MessageProcessLog")]
    public class MessageProcessLog : BaseEntity
    {
        [BsonIgnore]
        [Key]
        public int? Id { get; set; }
        public string AppKey { get; set; }
        public int MessageTag { get; set; }
        public int ShopId { get; set; }
        public string OrderId { get; set; }
        public string ChildOrderId { get; set; }
        public DateTime CreateTime { get; set; } = DateTime.Now;
        public long? OrderQueryElapsed { get; set; }
        public long? OrderSyncElapsed { get; set; }
        public long? OrderUpdateElapsed { get; set; }
        public long? UpdateMergeOIElapsed { get; set; }
        public string UpdateFlag { get; set; }
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 订单对象序列化耗时
        /// </summary>
        [NotMapped]
        public long? JsSeriElapse { get; set; }

        /// <summary>
        /// 消息处理耗时
        /// </summary>
        [NotMapped]
        public long? Elapse { get; set; }

        /// <summary>
        /// 订单从哪里同步
        /// 1：推送库
        /// 2：api
        /// </summary>
        public int? OrderFrom { get; set; }

        /// <summary>
        /// 合单id
        /// </summary>
        [NotMapped]
        public string MegerOrderId { get; set; }

        /// <summary>
        /// 消息id
        /// </summary>
        [NotMapped]
        public string MsgId { get; set; }

        /// <summary>
        /// 消息是否跳过处理
        /// 0：处理
        /// 1：跳过
        /// </summary>
        [NotMapped]
        public int? IsIgnore { get; set; }

        [NotMapped]
        public string OrderJSON { get; set; }

        public OrderStatusModel OldOrder { get; set; }
        public OrderStatusModel NewOrder { get; set; }

        public OrderAfterSaleInfo AlterSaleInfo { get; set; }

        public Tuple<int, string, string> UpdateOS { get; set; } //更新订单状态：店铺id，订单编号，订单状态
        public Tuple<int, string, List<string>, string> UpdateOIS { get; set; } //更新订单项状态：店铺id，订单编号，订单项Id，订单状态
        public Tuple<int, string, string> UpdateORS { get; set; }//更新订单退款状态：店铺id，订单编号，订单退款状态
        public Tuple<int, string, List<string>, string> UpdateOIRS { get; set; } //更新订单项退款状态：订单id，订单编号，订单项Id，订单项退款状态

        public OrderStatusModel GetOrderStatusModel(Order order)
        {
            if (order == null) return null;
            return new OrderStatusModel()
            {
                pid = order.PlatformOrderId,
                sid = order.ShopId,
                Status = order.PlatformStatus,
                RefundStatus = order.RefundStatus.ToString2(),
                Ois = order.OrderItems.Select(f => new OIStatusModel()
                {
                    SubItemId = f.SubItemID,
                    Status = f.Status,
                    RefundStatus = f.RefundStatus.ToString2(),
                }).ToList()
            };
        }
    }

    public class OrderStatusModel
    {
        public string pid { get; set; }
        public int sid { get; set; }

        public string Status { get; set; }
        public string RefundStatus { get; set; }

        public List<OIStatusModel> Ois { get; set; }
    }

    public class OIStatusModel
    {
        public string SubItemId { get; set; }
        public string Status { get; set; }
        public string RefundStatus { get; set; }
    }

    public class OrderAfterSaleInfo
    {
        public string after_sale_type { get; set; }
        public string after_sale_status { get; set; }
        public string refund_status { get; set; }

        public List<string> SubItemIds { get; set; }

        public string ToRefundStatus { get; set; }
    }
}
