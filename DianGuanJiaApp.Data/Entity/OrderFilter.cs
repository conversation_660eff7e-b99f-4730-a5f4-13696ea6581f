using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;


namespace DianGuanJiaApp.Data.Entity
{
    
    [Table("P_OrderFilter")]
     public partial class OrderFilter:BaseEntity
    {
        [Key]
    	public int Id { get; set; }
        public string Config { get; set; }
        public string CustomerConfig { get; set; }
        public int? Priority { get; set; }
        public bool? IsHide { get; set; }
        public bool? IsDeleted { get; set; }
        public System.DateTime? CreateTime { get; set; }
        public int ShopId { get; set; }
    }
}
