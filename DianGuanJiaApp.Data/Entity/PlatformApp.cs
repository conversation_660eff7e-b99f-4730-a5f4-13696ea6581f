using Dapper;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Entity
{
    [Table("PlatformApp")]
    public class PlatformApp : BaseEntity
    {
        [Key]
        [BsonIgnore]
        public int Id { get; set; }
        /// <summary>
        /// 所属平台
        /// </summary>
        public string PlatformType { get; set; }
        /// <summary>
        /// 应用名称，平台上的应用名称
        /// </summary>
        public string AppName { get; set; }
        /// <summary>
        /// 应用名称，我们系统内部显示的应用名称
        /// </summary>
        public string ShowAppName { get; set; }
        /// <summary>
        /// 应用Key
        /// </summary>
        public string AppKey { get; set; }
        /// <summary>
        /// 应用Secret
        /// </summary>
        public string AppSecret { get; set; }
        /// <summary>
        /// 服务市场Id
        /// </summary>
        public string ServiceId { get; set; }
        /// <summary>
        /// 所属系统：分单系统=FenDanSystem,打单系统 = PrintSystem,主客铺货 = ZhuKeSystem,跨境系统 = CrossBorderSystem
        /// </summary>
        public string Source { get; set; }
        /// <summary>
        /// 授权地址
        /// </summary>
        public string AuthUrl { get; set; }
        /// <summary>
        /// 订购地址
        /// </summary>
        public string PayUrl { get; set; }
        /// <summary>
        /// 接口文档地址
        /// </summary>
        public string DocUrl { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 所属公司主体名：深圳店管家软件有限公司,深圳市中恒网络有限公司,深圳店管家科技有限公司
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

    }
}
