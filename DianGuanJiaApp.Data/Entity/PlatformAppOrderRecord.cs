using Dapper;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Entity
{
    /// <summary>
    /// 订购记录-仅用于统计，不参与业务逻辑
    /// </summary>
    [Table("PlatformAppOrderRecord")]
    public class PlatformAppOrderRecord
    {
        [Key]
        [BsonIgnore]
        public int Id { get; set; }
        /// <summary>
        /// 第三方交易Id
        /// </summary>
        public string ThirdPayTradeId { get; set; }
        /// <summary>
        /// 套餐名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 应用AppKey
        /// </summary>
        public string ServiceAppId { get; set; }
        /// <summary>
        /// 套餐Id
        /// </summary>
        public string SkuId { get; set; }
        /// <summary>
        /// 套餐Code，内部自定义，如:Print_TouTiao
        /// </summary>
        public string ProductCode { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        public string OrderNo { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }
        /// <summary>
        /// 付款金额
        /// </summary>
        public decimal PayAmount { get; set; }
        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal RefundAmount { get; set; }
        /// <summary>
        /// 服务费金额
        /// </summary>
        public decimal ServiceAmount { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string PayStatus { get; set; }
        /// <summary>
        /// 交易时间
        /// </summary>
        public DateTime TradeTime { get; set; }
        /// <summary>
        /// 付款时间
        /// </summary>
        public DateTime? PayTime { get; set; }
        /// <summary>
        /// 退款时间
        /// </summary>
        public DateTime? RefundTime { get; set; }
        /// <summary>
        /// 服务开始时间
        /// </summary>
        public DateTime? ServiceBegin { get; set; }
        /// <summary>
        /// 服务结束时间
        /// </summary>
        public DateTime? ServiceEnd { get; set; }
        /// <summary>
        /// 用户Id
        /// </summary>
        public int UserId { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 套餐天数
        /// </summary>
        public int Days { get; set; }
        /// <summary>
        /// 店铺Id，对应P_Shop.Id
        /// </summary>
        public int ShopId { get; set; }
        /// <summary>
        /// 平台店铺Id，对应P_Shop.ShopId
        /// </summary>
        public string PlatformShopId { get; set; }
        /// <summary>
        /// 平台店铺名称，快手订购记录中的
        /// </summary>
        public string PlatformShopName { get; set; }
        /// <summary>
        /// 平台类型
        /// </summary>
        public string PlatformType { get; set; }
        /// <summary>
        /// 落库时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
    }
}
