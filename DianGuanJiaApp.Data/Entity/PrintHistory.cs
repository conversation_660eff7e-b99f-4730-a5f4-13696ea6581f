using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;


namespace DianGuanJiaApp.Data.Entity
{

    [Table("P_PrintHistory")]
    public partial class PrintHistory : BaseEntity
    {
        [Key]
        public long ID { get; set; }
        /// <summary>
        /// 用户的系统店铺Id
        /// </summary>
        public int ShopId { get; set; }
        public int WaybillCodeOrderId { get; set; }
        public string ExpressWaybillCode { get; set; }
        public string ExpressWaybillCodeChild { get; set; }
        public string BuyerMemberId { get; set; }
        public string BuyerMemberName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Reciver { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ReciverPhone { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ReciverAddress { get; set; }
        public string ToProvince { get; set; }
        public string ToCity { get; set; }
        public string ToDistrict { get; set; }
        public string SenderCompany { get; set; }
        public string Sender { get; set; }
        public string SenderPhone { get; set; }
        public string SenderAddress { get; set; }
        public string FromProvince { get; set; }
        public string FromCity { get; set; }
        public string FromDistrict { get; set; }
        public int ProductCount { get; set; }
        public decimal? TotalWeight { get; set; }
        public decimal? TotalPayAomount { get; set; }
        public int ExpressId { get; set; }
        public string ExpressName { get; set; }
        public int TemplateId { get; set; }
        public int TemplateType { get; set; }
        public string TemplateName { get; set; }
        public string SendContent { get; set; }
        public string PrinterName { get; set; }
        /// <summary>
        /// printtype 1：快递单，2：发货单
        /// </summary>
        public int PrintType { get; set; }
        /// <summary>
        /// 1：平台订单，2：自由订单
        /// </summary>
        public int PrintDataType { get; set; }
        public System.DateTime PrintDate { get; set; }
        public string PrinterJsonData { get; set; }
        public string PlatformOrderId { get; set; }
        public string PlatformOrderJoin { get; set; }
        /// <summary>
        /// 自由打印客户订单id
        /// </summary>
        public string CustomerOrderId { get; set; }

        public int CaiNiaoBranchId { get; set; }
        /// <summary>
        /// 打印记录是否被确认
        /// </summary>
        public bool IsConfirmed { get; set; }

        /// <summary>
        /// 打印方式
        /// </summary>
        public string PrintMethod { get; set; }

        public int UserId { get; set; }
        /// <summary>
        /// 打印批次详情(详细记录到并发批次和序号格式  当前第几个并发请求/并发请求总数/批次号（为前端时间+随机数）)
        /// </summary>
        public string PrintBatchNumber { get; set; }

        /// <summary>
        /// 打印批次号（前端勾选一次打印的批次号，为前端时间+随机数）
        /// </summary>
        public string PrintBatch { get; set; }

        /// <summary>
        /// 打印序号
        /// </summary>
        public int BatchIndex { get; set; }
        /// <summary>
        ///  分销用户Id
        /// </summary>
        public int? FxUserId { get; set; }

        public string PathFlowCode { get; set; }
        /// <summary>
        /// 发货类型：0=正常；1=补发；2=换货；3=变更单号
        /// </summary>
        public int SendType { get; set; }
        /// <summary>
        /// 是否隐藏
        /// </summary>
        public bool? IsHide { get; set; }

        /// <summary>
        /// 发货状态  查询二次发货打印记录时用到，二次发货的值为 2
        /// </summary>
        public int SendStatus { get; set; }
        /// <summary>
        /// 源店铺Id
        /// </summary>
        [NotMapped]
        public int SourceShopId { get; set; }

        /// <summary>
        /// 检查快递模板结果
        /// </summary>
        [NotMapped]
        public object CheckResult { get; set; }

        #region 跨境
        /// <summary>
        /// 收件人的国家
        /// 当前仅Tiktok平台打印记录必填，可选值为：ID/TH/SG/MY/VN/PH/UK，来源于店铺的SubPlatformType
        /// </summary>
        public string ToCountry { get; set; }

        /// <summary>
        /// 发件人的国家
        /// 发件人的国家，暂无用途，可选值为：China
        /// </summary>
        public string FromCountry { get; set; }

        /// <summary>
        /// 包裹ID,目前仅TK平台有值
        /// </summary>
        public string PackageId { get; set; }

        /// <summary>
        /// 状态，仅TK平台有值，原定存包裹状态，但是没用上，先存LogisticStatus。
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 模板类型：SHIPPING_LABEL 、SHIPPING_LABEL_AND_PACKING_SLIP
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 模板大小：A5、A6
        /// </summary>
        public string Size { get; set; }
        #endregion
    }
}
