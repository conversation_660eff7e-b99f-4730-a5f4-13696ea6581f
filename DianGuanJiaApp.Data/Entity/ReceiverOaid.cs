using Dapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Entity
{
    /// <summary>
    /// 收件人Oaid表
    /// </summary>
    [Table("P_ReceiverOaid")]
    public class ReceiverOaid
    {
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// 店铺id
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 订单接口返回的Oaid1和Oaid2
        /// </summary>
        public string Oaid { get; set; }

        /// <summary>
        /// 订单用于生成的BuyerHashCode的Oaid（也就是我们内部应用记录的内部平台Oaid）
        /// </summary>
        public string OrderOaid { get; set; }

        /// <summary>
        /// 创建时间（此创建时间暂标记存储为订单的平台创建时间，方便后续合单相关）
        /// </summary>
        public DateTime CreateTime { get; set; }
        [NotMapped]
        public int FxUserId {  get; set; }  
    }

    /// <summary>
    /// 收件人Oaid表更新数据模型（此表只涉及对应Id关联后对应更新OrderOaid的可能性，所以这里模型暂仅保留这两个字段）
    /// </summary>
    [Table("P_ReceiverOaid")]
    public class ReceiverOaidUpdateModel
    {
        [Key]
        public long Id { get; set; }

        ///// <summary>
        ///// 店铺id
        ///// </summary>
        //public int ShopId { get; set; }

        ///// <summary>
        ///// 订单接口返回的Oaid1和Oaid2
        ///// </summary>
        //public string Oaid { get; set; }

        /// <summary>
        /// 订单用于生成的BuyerHashCode的Oaid（也就是我们内部应用记录的内部平台Oaid）
        /// </summary>
        public string OrderOaid { get; set; }

        ///// <summary>
        ///// 创建时间（此创建时间暂标记存储为订单的平台创建时间，方便后续合单相关）
        ///// </summary>
        //public DateTime CreateTime { get; set; }
    }
    /// <summary>
    /// 对应订单收件人Oaid匹配P_ReceiverOaid对应OrderOaid入参查询模型 
    /// </summary>
    public class ReceiverOaidModel
    {
        /// <summary>
        /// 订单对应FxUserId
        /// </summary>
        public int FxUserId { get; set; }
        /// <summary>
        /// 店铺Id
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 平台订单id
        /// </summary>
        public string PlatformOrderId { get; set; }


        /// <summary>
        /// 订单接口返回的Oaid1
        /// </summary>
        public string Oaid1 { get; set; }

        /// <summary>
        /// 订单接口返回的Oaid2
        /// </summary>
        public string Oaid2 { get; set; }

        /// <summary>
        /// 订单匹配P_ReceiverOaid后的匹配结果Oaid
        /// </summary>
        public string OrderOaid { get; set; }

        /// <summary>
        /// 匹配到的订单的创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 订单对应Oaid信息模型
    /// </summary>
    public class OrderOrderOaidMapModel
    {
        /// <summary>
        /// 订单匹配P_ReceiverOaid后的匹配结果Oaid
        /// </summary>
        public string OrderOaid { get; set; }

        /// <summary>
        /// 对应映射的Oaid
        /// </summary>
        public string Oaid { get; set; }
    }
}
