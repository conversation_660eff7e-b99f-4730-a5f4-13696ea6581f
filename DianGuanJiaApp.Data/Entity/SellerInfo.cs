using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;


namespace DianGuanJiaApp.Data.Entity
{

    [Table("P_SellerInfo")]
    public partial class SellerInfo : BaseEntity
    {
        [Key]
        public int Id { get; set; }
        public string CompanyName { get; set; }     
        public string SellerName { get; set; }
        public string SellerMobile { get; set; }
        public string SellerPhone { get; set; }
        public string SellerAddress { get; set; }
        public System.DateTime? AddDate { get; set; }
        public string SellerProvince { get; set; }
        public string SellerCity { get; set; }
        public string SellerArea { get; set; }
        public string SellerDetailsAddress { get; set; }
        //public string ProvinceCode { get; set; }
        //public string CityCode { get; set; }
        //public string AreaCode { get; set; }
        public bool IsDefault { get; set; }
        /// <summary>
        /// 1.平台订单发件人，2.自由订单发件人
        /// </summary>
        public int Type { get; set; } = 1;       
        public int ShopId { get; set; }
    }
}
