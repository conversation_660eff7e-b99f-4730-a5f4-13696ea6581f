using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;


namespace DianGuanJiaApp.Data.Entity
{
    [SyncToConfigDb]
    [Table("P_SendGoodTemplate")]
    public partial class SendGoodTemplate : BaseEntity
    {
        [Key]
        public int Id { get; set; }
        public string TemplateName { get; set; }
        public string Config { get; set; }
        public bool IsDefault { get; set; }
        public bool IsDeleted { get; set; }

        public string Snapshot { get; set; }
        public int ShopId { get; set; }

        /// <summary>
        /// ����ģ��id
        /// </summary>
        public int? RefTemplateId { get; set; }
    }
}
