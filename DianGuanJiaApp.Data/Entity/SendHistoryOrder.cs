using System;
using Dapper;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Data.Entity
{
    /// <summary>
    /// 发货记录订单关联表
    /// </summary>
    [Table("P_SendHistoryOrder")]
     public partial class SendHistoryOrder : BaseEntity
    {
        [Key]
    	public int ID { get; set; }
        public int SendHistoryId { get; set; }
        public string OrderId { get; set; }

        /// <summary>
        /// 唯一键(SendHistoryCode + OrderId + SendType).ToShortMd5()
        /// </summary>
        public string SendHistoryOrderCode
        {
            get
            {
                if (string.IsNullOrEmpty(SendHistoryCode))
                    return (SendHistoryId + OrderId + SendType).ToShortMd5();
                else
                    return (SendHistoryCode + OrderId + SendType).ToShortMd5();
            }
        }

        /// <summary>
        /// 发货类型：0=正常；1=补发；2=换货；3=变更单号
        /// </summary>
        public int SendType { get; set; }
        public string PlatformOrderId { get; set; }
        // 是否部分发货
        public bool? IsPartSend { get; set; }
        // 发货商品总数
        public int? ProductItemCount { get; set; }
        // 发货商品Sku种数
        public int? ProductKindCount { get; set; }
        // 发货商品种数
        public int? ProductIdCount { get; set; }

        [NotMapped]
        public string SendOrderCode { get; set; }
        /// <summary>
        /// 订单付款时间
        /// </summary>
        public DateTime? PayTime { get; set; }
        /// <summary>
        /// 订单创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        ///  P_SendHistory表唯一键
        /// </summary>
        public string SendHistoryCode { get; set; }
        /// <summary>
        /// 对账时针对某个订单排除(合单发货情况)
        /// 订单类型：OfflineNoSku(线下单无商品不对账) 发货订单类型，暂时OfflineNoSku：无商品不对账
        /// AliFxPurchase：非1688平台的预付单
        /// AlibabaSupplierOrder：1688平台采购单
        /// </summary>
        public string OrderType { get; set; }

        /// <summary>
        /// 采购单运费
        /// </summary>
        public decimal? PurchaseShippingFee { get; set; }
    }
}
