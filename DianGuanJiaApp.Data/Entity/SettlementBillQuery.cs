using Dapper;
using System;

namespace DianGuanJiaApp.Data.Entity
{
    [Table("SettlementBillQuery")]
    public class SettlementBillQuery
    {
        /// <summary>
        /// 自增ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 对账代码
        /// </summary>
        public string BillCode { get; set; }

        /// <summary>
        /// 对账类型
        /// </summary>
        public int SettlementType { get; set; }

        /// <summary>
        /// 所属分销用户
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 对账时间
        /// </summary>
        public DateTime? BillTime { get; set; }

        /// <summary>
        /// 对账状态
        /// </summary>
        public int BillStatus { get; set; }

        /// <summary>
        /// 是否子任务
        /// </summary>
        public bool? IsSubTask { get; set; }

        /// <summary>
        /// 是否隐藏
        /// </summary>
        public bool? IsHide { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        public int CreateFxUserId { get; set; }
    }
}