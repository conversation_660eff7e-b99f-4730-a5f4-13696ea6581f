using System;
using Dapper;

namespace DianGuanJiaApp.Data.Entity.SupplierProduct
{
    /// <summary>
    /// 供应商地址表
    /// </summary>
    [Table("SupplierAddress")]
    public class SupplierAddress : BaseEntity
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// 唯一Code
        /// </summary>
        public string AddressCode { get; set; }

        /// <summary>
        /// 所属用户Id
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 关联的商品ID，名片地址时为空
        /// </summary>
        public long? ProductUid { get; set; }

        /// <summary>
        /// 地址类型 (0:商品发货地址, 1:名片发货地址)
        /// </summary>
        public int AddressType { get; set; } = 0;

        /// <summary>
        /// 省份名称
        /// </summary>
        public string ProvinceName { get; set; }

        /// <summary>
        /// 城市名称
        /// </summary>
        public string CityName { get; set; }

        /// <summary>
        /// 区/县名称
        /// </summary>
        public string DistrictName { get; set; }

        /// <summary>
        /// 街道/乡镇名称
        /// </summary>
        public string StreetName { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        public string DetailAddress { get; set; }

        /// <summary>
        /// 联系人姓名
        /// </summary>
        public string ContactName { get; set; }

        /// <summary>
        /// 联系人手机号
        /// </summary>
        public string ContactMobile { get; set; }

        /// <summary>
        /// 联系人固定电话
        /// </summary>
        public string ContactPhone { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }
}
