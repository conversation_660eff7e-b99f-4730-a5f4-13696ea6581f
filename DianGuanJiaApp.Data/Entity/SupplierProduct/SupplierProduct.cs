using System;
using System.Collections.Generic;
using Dapper;

namespace DianGuanJiaApp.Data.Entity.SupplierProduct
{
    /// <summary>
    /// 货盘商品表
    /// </summary>
    [Table("SupplierProduct")]
    public class SupplierProductEntity : BaseEntity
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Key]
        public long Id { get; set; }

        /// <summary>
        /// 唯一ID，总长度19位，FxUserId占7位，不够补零固定前缀3+FxUserId +11位自增值（不够补零），自增值取自BaseUniqueIdCode.Id
        /// </summary>
        public long Uid { get; set; }

        /// <summary>
        /// 商家编码，来源基础商品SpuCode或随机生成，用户维度唯一
        /// </summary>
        public string SpuCode { get; set; }

        /// <summary>
        /// 分单用户ID
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// SPU商品标题
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// 主图图片列表，用JSON格式，[{ "ObjectId": "oss.id", "FullUrl": url, "IsMain": true }]
        /// </summary>
        public string MainImageJson { get; set; }

        /// <summary>
        /// 商品详情，目前仅仅是图片，来源BaseProduct.Description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 默认空字符，简称
        /// </summary>
        public string ShortTitle { get; set; } = string.Empty;

        /// <summary>
        /// 属性名，多个英文逗号隔开（有顺序区分），例如：颜色,尺寸最多3个值该字段为冗余字段
        /// </summary>
        public string AttributeNames { get; set; }

        /// <summary>
        /// 来源基础商品对象BaseProduct.Uid
        /// </summary>
        public long FromProductUid { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 创建该条数据的用户ID
        /// </summary>
        public int CreateFxUserId { get; set; }

        /// <summary>
        /// 状态，默认1正常，0已删除
        /// </summary>
        public int Status { get; set; } = 1;

        /// <summary>
        /// 是否上架默认0，不公开，公开后货盘可见
        /// </summary>
        public bool IsPublic { get; set; } = false;

        /// <summary>
        /// 上架时间，用于排序
        /// </summary>
        public DateTime? PublicTime { get; set; }

        /// <summary>
        /// 发货相关内容，有以下内容：发货地址、发货快递、代发运费、发货时效，JSON格式
        /// </summary>
        public string ShipmentsInfo { get; set; }

        /// <summary>
        /// 售后相关内容，有以下内容：售后地址、售后处理时效、售后承诺、其他承诺，JSON格式
        /// </summary>
        public string AfterSalesInfo { get; set; }

        /// <summary>
        /// 支持面单，用逗号隔开
        /// </summary>
        public string ExpressBill { get; set; }
        
        /// <summary>
        /// 类目的属性，JSON格式
        /// </summary>
        public string CategoryAttributes { get; set; }

        /// <summary>
        /// 状态：false 无需同步、true 待同步
        /// </summary>
        public bool IsWaitSyncBaseProduct { get; set; } = false;

        /// <summary>
        /// 冗余字段，商品的最高价，用于列表查询、排序
        /// </summary>
        public decimal? MaxPrice { get; set; }

        /// <summary>
        /// 冗余字段，商品的最低价，用于列表查询、排序
        /// </summary>
        public decimal? MinPrice { get; set; }

        /// <summary>
        /// 来源SharePath.PathFlowCode，创建时生成或继承于BaseProduct.SharePathCode
        /// </summary>
        public string SharePathCode { get; set; }

        /// <summary>
        /// 来源BaseProduct.PathNodeDeep
        /// </summary>
        public int PathNodeDeep { get; set; }

        /// <summary>
        /// 根路径节点的用户ID，来源BaseProduct.RootNodeFxUserId
        /// </summary>
        public int RootNodeFxUserId { get; set; }

        /// <summary>
        /// 商品Sku
        /// </summary>
        [NotMapped]
        public List<SupplierProductSku> Skus { get; set; }

        /// <summary>
        /// 平台规格模式=0（默认）、自定义规格模式=1
        /// </summary>
        public int SkuModeType { get; set; }

        /// <summary>
        /// 商品地址关联Code，来源SupplierAddress.AddressCode
        /// </summary>
        public string AddressCode { get; set; }
        
        /// <summary>
        /// 商品类型，默认空，空为小站商品，按平台类型记录
        /// </summary>
        public string ProductType { get; set; }
        
        /// <summary>
        /// 是否引用名片地址，默认false，true则引用了名片地址信息
        /// </summary>
        public bool? IsUseCardInfo { get; set; }
    }
}
