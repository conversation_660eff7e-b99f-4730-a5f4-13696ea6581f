using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;


namespace DianGuanJiaApp.Data.Entity
{

    /// <summary>
    /// 用户店铺关联信息
    /// </summary>
    [SyncToConfigDb]
    [Table("P_UserShopRelation")]
     public partial class UserShopRelation : BaseEntity
    {
        [Key]
    	public int Id { get; set; }
        /// <summary>
        /// 店铺ID
        /// </summary>
        public int ShopId { get; set; }
        public int UserId { get; set; }
        public bool IsDeleted { get; set; }
        public int CreateBy { get; set; }
        public System.DateTime? CreateTime { get; set; }

        public bool IsDefault { get; set; }
    }
}
