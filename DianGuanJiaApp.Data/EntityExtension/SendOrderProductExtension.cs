using Dapper;

namespace DianGuanJiaApp.Data.Entity
{
    public partial class SendOrderProduct
    {
        /// <summary>
        /// 发货记录商品数量
        /// </summary>
        [NotMapped]
        public int? ProductItemCount { get; set; }

        /// <summary>
        /// 是否部分发货
        /// </summary>
        [NotMapped]
        public bool? IsPartSend { get; set; }

        /// <summary>
        /// 订单数量
        /// </summary>
        [NotMapped]
        public int? OrderCount { get; set; }
    }
}