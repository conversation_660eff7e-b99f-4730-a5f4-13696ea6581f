using Dapper;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.Model;
using System.Collections.Generic;

namespace DianGuanJiaApp.Data.Entity.BaseProduct
{
    /// <summary>
    /// 平台资料
    /// </summary>
    public partial class PtProductInfo
    {
        /// <summary>
        /// 扩展数据
        /// </summary>
        [NotMapped]
        public PtProductInfoExt Ext { get; set; }

        /// <summary>
        /// 本次铺货的店铺列表
        /// </summary>
        [NotMapped]
        public List<int> ListingShopIds { get; set; }

        /// <summary>
        /// 铺货设置-前端赋值
        /// </summary>
        [NotMapped]
        public ListingSetting UserListingSetting { get; set; }

        /// <summary>
        /// 铺货任务-铺货设置-后端处理
        /// </summary>
        [NotMapped]
        public ShopListingConfigSaveRequest ListingConfig { get; set; }


        /// <summary>
        /// 查询来源
        /// </summary>
        [NotMapped]
        public int FromType { get; set; }

        /// <summary>
        /// 查询来源编码
        /// </summary>
        [NotMapped]
        public string FromCode { get; set; }

        /// <summary>
        /// 是否需要合并小站商品信息，用于复制商品时，当SkuCode不一致时需要合并
        /// </summary>
        [NotMapped]
        public bool IsNeedCombine { get; set; } = false;
        
        /// <summary>
        /// 对应的货盘商品Uid
        /// </summary>
        [NotMapped]
        public string SupplierProductUid { get; set; }
    }
}
