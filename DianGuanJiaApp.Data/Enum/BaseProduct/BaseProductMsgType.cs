using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace DianGuanJiaApp.Data.Enum
{
    /// <summary>
    /// 基础商品-消息类型
    /// </summary>
    public struct BaseProductMsgType
    {
        /// <summary>
        /// 基础商品规格与平台商品规格关系表：业务库-->基础商品库
        /// </summary>
        public const string BaseOfPtSkuRelation = "BaseOfPtSkuRelation";
        /// <summary>
        /// 
        /// </summary>
        public const string BaseOfPtSkuRelationAdd = "BaseOfPtSkuRelationAdd";
        /// <summary>
        /// 自动关联时添加关联关系
        /// </summary>
        public const string BaseOfPtSkuRelationAddByAuto = "BaseOfPtSkuRelationAddByAuto";
        /// <summary>
        /// 
        /// </summary>
        public const string BaseOfPtSkuRelationDel = "BaseOfPtSkuRelationDel";        
        /// <summary>
        /// 库存商品：基础商品库-->库存系统
        /// </summary>
        public const string WareHouseProduct = "WareHouseProduct";
        /// <summary>
        /// 基础商品：库存系统-->基础商品库
        /// </summary>
        public const string BaseProduct = "BaseProduct";
        /// <summary>
        /// 库存是否扣减（库存商品绑定店铺商品）：库存系统-->基础商品
        /// </summary>
        public const string WareHouseSkuBindRelation = "WareHouseSkuBindRelation";
        /// <summary>
        /// 规格简称：基础商品库-->业务库
        /// </summary>
        public const string SkuShortTitle = "SkuShortTitle";
        /// <summary>
        /// 结算价：基础商品库-->业务库
        /// </summary>
        public const string ProductSettlementPrice = "ProductSettlementPrice";
        /// <summary>
        /// 规格绑定关系：基础商品库-->业务库
        /// </summary>
        public const string BindSupplier = "BindSupplier";
        /// <summary>
        /// 
        /// </summary>
        public const string WareHouseRelationToBaseProduct = "WareHouseRelationToBaseProduct";
        /// <summary>
        /// 新版同步关联关系到基础库
        /// </summary>
        public const string WareHouseRelationToBaseProductNew = "WareHouseRelationToBaseProductNew";
        
        /// <summary>
        /// 
        /// </summary>
        public const string BindSupplierWithModify = "BindSupplierWithModify";

        /// <summary>
        /// 关联同款-同步数据到平台商品
        /// </summary>
        public const string RelationBindSyncPtSku = "RelationBindSyncPtSku";
        /// <summary>
        /// 
        /// </summary>
        public const string SyncPtSku = "SyncPtSku";
        /// <summary>
        /// 
        /// </summary>
        public const string WarehouseStockIn = "WarehouseStockIn";
        /// <summary>
        /// 
        /// </summary>
        public const string WarehouseStockOut = "WarehouseStockOut";

        /// <summary>
        /// 同步更新平台商品关联关系
        /// </summary>
        public const string SyncPtRelationStatus = "SyncPtRelationStatus";

        /// <summary>
        /// 自动关联
        /// </summary>
        public const string AutoRelationBind = "AutoRelationBind";

        /// <summary>
        /// 自动关联-同步库存数据到平台商品
        /// </summary>
        public const string WarehouseSkuBind = "WarehouseSkuBind";
        
        /// <summary>
        /// 添加基础商品异常数据
        /// </summary>
        public const string AddBaseProductAbnormal = "AddBaseProductAbnormal";

        /// <summary>
        /// 复制关联关系副本
        /// </summary>
        public const string BaseProductRelationModify = "BaseProductRelationModify";

        /// <summary>
        /// 自动关联-新版
        /// </summary>
        public const string AutoRelationBindNew = "AutoRelationBindNew";
        
        /// <summary>
        /// 自动关联-完成
        /// </summary>
        public const string AutoRelationBindComplete = "AutoRelationBindComplete";

        /// <summary>
        /// 编辑更新时，同步关联关系到各业务库
        /// </summary>
        public const string SyncRelationFromEdit = "SyncRelationFromEdit";

        /// <summary>
        /// 变更等级更新历史结算价
        /// </summary>
        public const string MemberSettlementPriceChange = "MemberSettlementPriceChange";


        /// <summary>
        /// 批量解绑
        /// </summary>
        public const string BatchRelationUnBind = "BatchRelationUnBind";
    }
}
