using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Enum
{
    public class ContrastTaskStatus
    {
        /// <summary>
        /// 等待执行
        /// </summary>
        public const string Pending = "Pending";
        /// <summary>
        /// 执行中
        /// </summary>
        public const string Doing = "Doing";
        /// <summary>
        /// 执行完成
        /// </summary>
        public const string Finished = "Finished";
        /// <summary>
        /// 发生异常
        /// </summary>
        public const string Error = "Error";
    }
    public class ContrastTaskType
    {
        /// <summary>
        /// 有原始单，无原始单项
        /// </summary>
        public const string OrderNotHasItem = "OrderNotHasItem";
        /// <summary>
        /// 有逻辑单，无逻辑单项
        /// </summary>
        public const string LogicOrderNotHasItem = "LogicOrderNotHasItem";
        /// <summary>
        /// 有逻辑单项，无原始单项
        /// </summary>
        public const string LogicOrderItemNotHasOrderItem = "LogicOrderItemNotHasOrderItem";
        /// <summary>
        /// 子单关联的主单不存在
        /// </summary>
        public const string LogicOrderNotHasMainOrder = "LogicOrderNotHasMainOrder";
        /// <summary>
        /// 逻辑单退款状态和原始单不一致
        /// </summary>
        public const string RefundStatusNotSame = "RefundStatusNotSame";
        /// <summary>
        /// 发货记录商品项，无关联原始单项
        /// </summary>
        public const string SendOrderProductNotHasOrderItem = "SendOrderProductNotHasOrderItem";
        /// <summary>
        /// 发货记录重复（同订单同运单号）
        /// </summary>
        public const string SendHistoryRepeat = "SendHistoryRepeat";
        /// <summary>
        /// 底单记录已发货状态，但无发货记录
        /// </summary>
        public const string WaybillCodeNotHasSendHistory = "WaybillCodeNotHasSendHistory";
        /// <summary>
        /// 有商品，无Sku
        /// </summary>
        public const string ProductNotHasSku = "ProductNotHasSku";
        /// <summary>
        /// 有商品，无PathFlowReference
        /// </summary>
        public const string ProductNotHasPathFlowReference = "ProductNotHasPathFlowReference";
        /// <summary>
        /// PathFlowReference，无条件参数超过1条
        /// </summary>
        public const string PathFlowReferenceMoreThenOne = "PathFlowReferenceMoreThenOne";
        /// <summary>
        /// PathFlowReference路径流上第一节点非商家自己
        /// </summary>
        public const string PathFlowReferenceFirstNodeNotSelf = "PathFlowReferenceFirstNodeNotSelf";
    }

}
