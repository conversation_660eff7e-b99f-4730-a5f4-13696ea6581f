namespace DianGuanJiaApp.Data.Enum
{
    public enum Stat1688FxUserIdFlag
    {
        /// <summary>
        /// 已开启预付+已绑定买家账号+未推送商品给厂家的商家
        /// </summary>
        BinedBuyerButNotPostProductAgent = 0,
        /// <summary>
        /// 已开启预付+已绑定买家账号+未推送商品给厂家的相关厂家
        /// </summary>
        BinedBuyerButNotPostProductSupplier = 1,
        /// <summary>
        /// 已开启预付+已绑定买家账号+已推送商品+未关联商品的相关商家
        /// </summary>
        PostedProductButNotMappingProductAgent = 2,
        /// <summary>
        /// 已开启预付+已绑定买家账号+已推送商品+未关联商品的相关厂家
        /// </summary>
        PostedProductButNotMappingProductSupplier = 3,
        /// <summary>
        /// 下采购单失败明细
        /// </summary>
        PurchaseOrderFailDetail = 4,
        /// <summary>
        /// 回流失败明细
        /// </summary>
        ReturnFailDetail = 5,
    }
}