using System.Collections.Generic;

namespace DianGuanJiaApp.Data.Enum
{
    public struct UpdateFieldName
    {
        public static List<string> LogicOrderStatus = new List<string> { "ExceptionStatus", "ErpRefundState", "ErpState" };

        public static List<string> LogicOrderRemark = new List<string>
            { "SellerRemarkFlag", "SellerRemark", "SystemRemark", "PrintRemark" };

        public static List<string> LogicOrderTotalWeight = new List<string> { "TotalWeight" };
        public static List<string> LogicOrderPathFlowCode = new List<string> { "PathFlowCode" };

        public static List<string> LogicOrderBuyerHashCode = new List<string>
            { "ApprovalStatus", "BuyerHashCodeV2", "BuyerHashCode", "PayTime" };

        public static List<string> LogicOrderSendState = new List<string>
            { "ErpState", "SendState", "BuyerHashCode", "OnlineSendTime", "IsMultiPack" };

        public static List<string> LogicOrderIsPreviewed = new List<string> { "IsMultiPack" };
        public static List<string> LogicOrderStockState = new List<string> { "StockState" };
    }
}