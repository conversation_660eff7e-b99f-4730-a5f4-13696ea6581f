using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Helpers;
using DianGuanJiaApp.Data.Model;

namespace DianGuanJiaApp.Data.Extension
{
    
    /// <summary>
    /// 数据变更仓储类接口
    /// </summary>
    public abstract class BaseDataChangeLogRepository : ExceptionHandler
    {
        private readonly CommonSettingRepository _commonSettingRepository;
        public BaseDataChangeLogRepository()
        {
            //配置
            _commonSettingRepository = new CommonSettingRepository();
        }
        /// <summary>
        /// 添加一条变更记录
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        public abstract bool Add(DataChangeLog log);
        /// <summary>
        /// 添加多条变更记录
        /// </summary>
        /// <param name="logs"></param>
        /// <returns></returns>
        public abstract bool Add(List<DataChangeLog> logs);

        /// <summary>
        /// 获取备库连接字符串
        /// </summary>
        /// <returns></returns>
        public string GetBackupDbConnectionString()
        {
            var connectionString =
                _commonSettingRepository.GetValue(SystemSettingKeys.DataChangeLogBackupDbKey, 0);
            return connectionString;
        }

        /// <summary>
        /// 添加到备库数据库
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        public bool AddToBackupDb(DataChangeLog log)
        {
            //获取数据库连接
            var db = GetDb(GetBackupDbConnectionString());
            //插入到数据库
            Insert(db, log);
            return true;
        }

        /// <summary>
        /// 添加到备库数据库
        /// </summary>
        /// <param name="logs"></param>
        /// <returns></returns>
        public bool AddToBackupDb(List<DataChangeLog> logs)
        {
            //获取数据库连接
            var db = GetDb(GetBackupDbConnectionString());
            using (db)
            {
                if (db.State != ConnectionState.Open)
                    db.Open();
                foreach (var log in logs)
                {
                    Insert(db, log);
                }
            }
            return true;
        }

        /// <summary>
        /// 添加到备用库，错误重试
        /// </summary>
        /// <param name="log"></param>
        /// <returns></returns>
        public bool AddToBackupDbExceptionRetry(DataChangeLog log)
        {
            try
            {
                var result = ExceptionRetryHandler(() => AddToBackupDb(log), true);
                return result;
            }
            catch
            {
                //保存到备库，重试后还是异常，则保存到阿里云，并抛出异常
                DataChangeRecordLogDataEventTracking.Instance.WriteLog(log);
                return false;
            }
        }

        /// <summary>
        /// 添加到备用库，错误重试
        /// </summary>
        /// <param name="logs"></param>
        /// <returns></returns>
        public bool AddToBackupDbExceptionRetry(List<DataChangeLog> logs)
        {
            try
            {
                var result = ExceptionRetryHandler(() =>
                {
                    AddToBackupDb(logs);
                    return true;
                }, true);
                return result;
            }
            catch
            {
                //保存到备库，重试后还是异常，则保存到阿里云，并抛出异常
                DataChangeRecordLogDataEventTracking.Instance.WriteLog(logs);
                return false;
            }
        }

        /// <summary>
        /// 获取数据库连接
        /// </summary>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        public IDbConnection GetDb(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new LogicException("链接字符串不能为空");
            }
            if (connectionString?.StartsWith("server") == true)
                return DbUtility.GetConnection(connectionString);
            else
                return DbUtility.GetMySqlConnection(connectionString);
        }
        /// <summary>
        /// 查询变更记录
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="shopId">店铺ID</param>
        /// <param name="startTime"></param>
        /// <param name="endTime">小于等于结束时间</param>
        /// <param name="fetchCount">获取条数</param>
        /// <param name="minId">最小ID，即游标ID，当传递了游标ID后，起始时间结束时间失效，仅使用店铺ID和最小ID作为条件查询，返回的数据ID大于minId</param>
        /// <returns>返回的数据，ID从小到大排列</returns>
        public abstract List<DataChangeLog> Query(int fxUserId, int shopId, DateTime startTime, DateTime endTime, int fetchCount = 100, long minId = 0);

        /// <summary>
        /// 查询变更记录
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="shopId">店铺ID</param>
        /// <param name="tableTypeName">表类型名</param>
        /// <param name="startTime"></param>
        /// <param name="endTime">小于等于结束时间</param>
        /// <param name="fetchCount">获取条数</param>
        /// <param name="minId">最小ID，即游标ID，当传递了游标ID后，起始时间结束时间失效，仅使用店铺ID和最小ID作为条件查询，返回的数据ID大于minId</param>
        /// <returns>返回的数据，ID从小到大排列</returns>
        public abstract List<DataChangeLog> Query(int fxUserId, int shopId, DataChangeTableTypeName tableTypeName, DateTime startTime, DateTime endTime, int fetchCount = 100, long minId = 0);

        /// <summary>
        /// 查询变更记录
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="shopId">店铺ID</param>
        /// <param name="tableTypeName">表类型名</param>
        /// <param name="startTime"></param>
        /// <param name="endTime">小于等于结束时间</param>
        /// <returns>返回的数据，该结果集仅返回RelationKey、DataChangeType，目前仅应用到LogicOrder的副本同步</returns>
        public abstract List<DataChangeLog> DistinctQuery(int fxUserId, int shopId, DataChangeTableTypeName tableTypeName, DateTime startTime, DateTime endTime);


        /// <summary>
        /// 查询变更记录
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="tableTypeName">表类型名</param>
        /// <param name="startTime"></param>
        /// <param name="endTime">小于等于结束时间</param>
        /// <param name="fetchCount">获取条数</param>
        /// <param name="minId">最小ID，即游标ID，当传递了游标ID后，起始时间结束时间失效，仅使用店铺ID和最小ID作为条件查询，返回的数据ID大于minId</param>
        /// <returns>返回的数据，ID从小到大排列</returns>
        public abstract List<DataChangeLog> Query(int fxUserId, DataChangeTableTypeName tableTypeName, DateTime startTime, DateTime endTime, int fetchCount = 100, long minId = 0);

        public abstract List<DataChangeLog> QueryForPushMessage(int fetchCount = 100, long minId = 0);

        /// <summary>
        /// 清理历史数据（内部循环，直至完成）
        /// </summary>
        /// <param name="dateTime">截止时间</param>
        /// <param name="pageSize">每次数量</param>
        /// <param name="sleepMs">ms</param>
        /// <returns></returns>
        public abstract int ClearHistoryData(DateTime dateTime, int pageSize = 500, int sleepMs = 50);

        public abstract DateTime GetNowTime(int sourceShopId);
        public List<DataChangeLog> ToDataChangeLog(List<LogicOrder> logicOrders)
        {
            return new List<DataChangeLog>();
        }
        /// <summary>
        /// 插入变更日志
        /// </summary>
        /// <param name="dbConnection"></param>
        /// <param name="log"></param>
        protected void Insert(IDbConnection dbConnection, DataChangeLog log)
        {
            //判空处理
            if (dbConnection == null || log == null)
            {
                return;
            }
            //插入
            if (dbConnection is MySqlConnection)
                dbConnection.InsertMysqlWithNullId(log);
            else
                dbConnection.Insert(log);
        }

        protected void PreCheck(DataChangeLog log)
        {
            if (log == null)
                return;
            var message = $"{log.DataChangeType},{log.TableTypeName},{log.TempKey}";
            if (log.SourceFxUserId <= 0)
                throw new LogicException($"SourceFxUserId必须大于0，{message}");
            if (log.SourceShopId <= 0)
                throw new LogicException($"SourceShopId必须大于0，{message}");
            if (string.IsNullOrEmpty(log.RelationKey))
                throw new LogicException($"RelationKey不能为空，{message}");
        }

        protected void PreCheck(List<DataChangeLog> logs)
        {
            foreach (var log in logs)
            {
                PreCheck(log);
            }
        }
        public abstract void WriteDataChangeLogLog(List<DataChangeLog> logs, bool isException = false,
            string exceptionMessage = null);
    }

    /// <summary>
    /// 默认的数据变更日志的实现类，该类使用数据库直连的方式
    /// </summary>
    public class DefaultDataChangeLogRepository : BaseDataChangeLogRepository
    {
        //private static LogHelper _logHelper;
        private string _cpt;
        private string _connectionString;
        private CommonSettingRepository _csRepository;
        public DefaultDataChangeLogRepository(string cpt)
        {
            _cpt = cpt;
            _csRepository = new CommonSettingRepository();
        }

        public DefaultDataChangeLogRepository(int dbNameId, string connectionString)
        {
            _connectionString = connectionString;
            _csRepository = new CommonSettingRepository();
        }
        private DataChangeDbConfigModel InitDbConfigs(int sourceShopId)
        {
            return InitDbConfigs(new List<int> { sourceShopId }).FirstOrDefault().Value;
        }

        private Dictionary<int, DataChangeDbConfigModel> InitDbConfigs(List<int> sourceShopIds)
        {
            var dict = new Dictionary<int, DataChangeDbConfigModel>();
            //获取用户变更日志数据库配置
            var logDbConfigRepository = new DataChangeLogDbConfigRepository(_cpt);
            var dbConfigModels = logDbConfigRepository.GetConfigByShopId(sourceShopIds, _cpt);
            foreach (var sourceShopId in sourceShopIds)
            {
                if (dict.ContainsKey(sourceShopId))
                    continue;
                var dbConfigModel = dbConfigModels.FirstOrDefault(x => x.ShopId == sourceShopId);
                if (dbConfigModel == null)
                {
                    var created = logDbConfigRepository.CreateDataChangeDbConfig(sourceShopId, _cpt);
                    if (created == false)
                        throw new Exception("初始化用户的数据变更配置时发生错误");
                    dbConfigModel = logDbConfigRepository.GetConfigByShopId(sourceShopId, _cpt);
                }
                if (dbConfigModel == null)
                    throw new Exception("初始化用户的数据变更配置时发生错误");
                dict.Add(sourceShopId, dbConfigModel);
            }
            return dict;
        }

        private void DisposeDb(Dictionary<int, IDbConnection> dbs)
        {
            //释放所有的数据库链接
            foreach (var kv in dbs)
            {
                var temp = kv.Value;
                temp.Dispose();
            }
        }
        /// <summary>
        /// 写入AliLog
        /// </summary>
        /// <param name="logs"></param>
        /// <param name="isException"></param>
        /// <param name="exceptionMessage"></param>
        public override void WriteDataChangeLogLog(List<DataChangeLog> logs, bool isException = false,
            string exceptionMessage = null)
        {
            //判空处理
            if (logs == null || !logs.Any())
            {
                return;
            }

            try
            {
                ExceptionRetryHandler(() =>
                {
                    //开关，默认为关
                    const string key = "/System/Fendan/WriteDataChangeLogLog";
                    var isOpenValue = _csRepository.Get(key, 0)?.Value ?? "";
                    if (isOpenValue == "" || isOpenValue == "0" || isOpenValue == "false")
                    {
                        return true;
                    }

                    var currentFxUserId = BaseSiteContext.CurrentNoThrow?.CurrentFxUserId ?? 0;

                    var batchNo = DateTime.Now.ToString("yyyyMMddHHmmss");
                    const int batchSize = 500;
                    var count = Math.Ceiling(logs.Count * 1.0 / batchSize);
                    //记录日志
                    var models = new List<DataChangeLogModel>();
                    for (var i = 0; i < count; i++)
                    {
                        var batchLogs = logs.Skip(i * batchSize).Take(batchSize).ToList();
                        var model = new DataChangeLogModel
                        {
                            BatchNo = $"{batchNo}-{i}",
                            Logs = batchLogs.Select(a =>
                                    $"{a.TableTypeName.ToInt()},{a.DataChangeType.ToInt()},{a.SourceShopId},{a.SourceFxUserId},{a.RelationKey},{a.ExtField1},{a.ExtField2}")
                                .ToJson(),
                            SourceFxUserId = batchLogs.First().SourceFxUserId,
                            SourceShopId = batchLogs.First().SourceShopId,
                            HostIp = HostHelper.IpAddress(),
                            HostName = Environment.MachineName,
                            CurFxUserId = currentFxUserId,
                            CloudPlatformType = CustomerConfig.CloudPlatformType,
                            ExceptionMessage = exceptionMessage,
                            IsException = isException
                        };
                        models.Add(model);
                    }
                    var chunks = models.ChunkList(10);
                    chunks.ForEach(chunk =>
                    {
                        DataChangeLogOfLogDataEventTracking.Instance.WriteLog(chunk);
                    });
                    return true;
                }, true);
            }
            catch (Exception e)
            {
                Log.WriteError($"写变更日志阿里云日志异常，异常原因：{e.Message}，堆栈信息：{e.StackTrace}",
                    $"DataChangeLogSaveSls_{DateTime.Now:yyyy-MM-dd}.log");
            }
        }

        /// <summary>
        /// 获取当前时间
        /// </summary>
        /// <param name="sourceShopId"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        public override DateTime GetNowTime(int sourceShopId)
        {
            //判空处理
            if (sourceShopId <= 0)
            {
                throw new LogicException("店铺ID必须大于0");
            }
            IDbConnection db;
            if (CustomerConfig.DataDuplicationEnvironment == DataDuplicationEnvironment.Backup)
            {
                //备用环境
                db = GetDb(GetBackupDbConnectionString());
            }
            else
            {
                //正式环境
                var configModel = InitDbConfigs(sourceShopId);
                db = GetDb(configModel.ConnectionString);
            }
            using (db)
            {
                //异常处理里面
                if (db.State != ConnectionState.Open)
                {
                    db.Open(); 
                }
                //数据库类型判断
                if (db.ConnectionString?.StartsWith("server") == true)
                {
                    return db.ExecuteScalar<DateTime>("SELECT GETDATE()");
                }
                return db.ExecuteScalar<DateTime>("SELECT NOW()");
            }
        }

        public override bool Add(DataChangeLog log)
        {
            PreCheck(log);
            //获取用户变更日志数据库配置
            var configModel = TryOneTime(() => { return InitDbConfigs(log.SourceShopId); }, isThrowEx: false);
            //配置初始化失败，将日志写入到备库中
            if (configModel == null)
            {
                return AddToBackupDbExceptionRetry(log);
            }
            //保存到数据库

            var conn = configModel.ConnectionString;
            var db = GetDb(conn);
            Insert(db, log);
            return true;
        }

        public override bool Add(List<DataChangeLog> logs)
        {
            //判空处理
            if (logs == null || !logs.Any())
            {
                return true;
            }
            //检查变更日志
            PreCheck(logs);

            var sourceShopIds = logs.Select(x => x.SourceShopId).Distinct().ToList();
            var fxDbConfigDict = TryOneTime(() =>
            {
                return InitDbConfigs(sourceShopIds);
            }, isThrowEx: false);
            //日志数据库配置不符合预期，则认为失败，将日志写入备库
            if (fxDbConfigDict == null || fxDbConfigDict.Any() == false ||
                fxDbConfigDict.Count < sourceShopIds.Count())
            {
                return AddToBackupDbExceptionRetry(logs);
            }

            //保持写入的顺序
            var dbs = new Dictionary<int, IDbConnection>();
            foreach (var kv in fxDbConfigDict)
            {
                var config = kv.Value;
                if (dbs.ContainsKey(config.DbNameConfigId))
                    continue;
                var db = GetDb(config.ConnectionString);
                dbs.Add(config.DbNameConfigId, db);
            }
            //异常日志
            var exceptionLogs = new List<DataChangeLog>();
            var errorMessages = new List<string>();
            //保存
            foreach (var log in logs)
            {
                var config = fxDbConfigDict[log.SourceShopId];
                var db = dbs[config.DbNameConfigId];
                try
                {
                    //异常处理里面
                    if (db.State != ConnectionState.Open)
                        db.Open();

                    TryInsertLog(log, db);
                }
                catch (Exception ex)
                {
                    //异常补偿
                    AddToBackupDbExceptionRetry(log);
                    //本地日志
                    Log.WriteError($"保存数据变更日志时发生错误：{ex} ==> {log.ToJson(true)}", "AddDataChangeLogError.log");
                    //写错误日志
                    exceptionLogs.Add(log);
                    //异常日志
                    errorMessages.Add(ex.Message);
                    //释放所有的数据库链接
                    db.Dispose();
                }
            }
            //异常日志批量提交到阿里云
            if (errorMessages.Any())
            {
                WriteDataChangeLogLog(exceptionLogs, true, string.Join(",", errorMessages.Distinct().ToList()));
            }
            //释放所有的数据库链接
            DisposeDb(dbs);
            return true;
        }

        private void TryInsertLog(DataChangeLog log, IDbConnection db)
        {
            try
            {
                ExceptionRetryHandler(() =>
                {
                    //添加日志
                    Insert(db, log);
                    return true;
                }, true);
            }
            catch
            {
                //重试后还是异常，则保存致备用库，备库保存后还是异常，则抛出异常
                AddToBackupDbExceptionRetry(log);
            }
        }

        private List<DataChangeLog> _query(int fxUserId, int? shopId, DateTime startTime, DateTime endTime,
            int fetchCount = 100, int tableTypeName = -1, long minId = 0)
        {
            // 240328 复制副本时，当startTime小于15天时，重置为15天（DateTime.Now.AddDays(-15)），因为副本日志仅保留15天，超过15天无意义
            if (startTime < DateTime.Now.AddDays(-15))
            {
                var oldStartTime = startTime;
                startTime = DateTime.Now.AddDays(-15);
                Log.Debug(() => new
                        {
                            fxUserId,
                            shopId,
                            startTime,
                            endTime,
                            tableTypeName,
                            minId,
                            currentTime = DateTime.Now
                        }.ToJson() + $"\nstartTime小于15天，重置为15天，原时间为{oldStartTime.ToJson()}",
                    Log.LogDirectory + DateTime.Now.ToString("yyyyMMdd") + "_QueryDataChange.log");
            }
            else
            {
                //日志
                Log.Debug(() => new
                {
                    fxUserId,
                    shopId,
                    startTime,
                    endTime,
                    tableTypeName,
                    minId,
                    currentTime = DateTime.Now
                }.ToJson(), Log.LogDirectory + DateTime.Now.ToString("yyyyMMdd") + "_QueryDataChange.log");
            }
            
            if (fxUserId <= 0)
                throw new LogicException("fxUserId必须大于0");
            //if (shopId == null || shopId <= 0)
            //    throw new ArgumentException("shopId必须大于0");
            if (startTime > endTime)
                throw new LogicException($"startTime={startTime}必须小于endTime={endTime}");
            if (startTime < DateTime.Now.AddMonths(-3))
                throw new LogicException($"startTime={startTime}过小");


            var tableTypeNameFilter = string.Empty;
            if (tableTypeName != -1)
                tableTypeNameFilter = $" AND TableTypeName = {tableTypeName}";

            var shopIdFilter = string.Empty;
            if (shopId.HasValue && shopId.Value > 0)
            {
                shopIdFilter = $" AND SourceShopId = {shopId.Value}";
            }
            else
            {
                //shopId = new ShopRepository().GetFxSystemShopByFxId(fxUserId)?.Id ?? 0;
                shopId = new UserFxRepository().GetSystemShopByFxUserIdWithCache(fxUserId)?.Id ?? 0;
            }

            //复制副本环境处理
            IDbConnection db;
            if (CustomerConfig.DataDuplicationEnvironment == DataDuplicationEnvironment.Backup)
            {
                //备用环境
                db = GetDb(GetBackupDbConnectionString());
            }
            else
            {
                //正式环境
                var configModel = InitDbConfigs(shopId.Value);
                db = GetDb(configModel.ConnectionString);
            }

            string sql;
            //var sqlParam = new { fxUserId, shopId, startTime, endTime, minId };
            if (minId <= 0)
            {
                if (db is MySqlConnection)
                    sql =
                        $"select * from datachangelog where SourceFxUserId = {fxUserId} {shopIdFilter} AND CreateTime>='{startTime.ToString("yyyy-MM-dd HH:mm:ss")}' AND CreateTime<='{endTime.ToString("yyyy-MM-dd HH:mm:ss")}' {tableTypeNameFilter} ORDER BY CreateTime,Id ASC limit {fetchCount}";
                else
                    sql =
                        $"select top({fetchCount}) * from DataChangeLog WITH(NOLOCK) where SourceFxUserId = {fxUserId} {shopIdFilter} AND SourceShopId={shopId} AND CreateTime>='{startTime.ToString("yyyy-MM-dd HH:mm:ss")}' AND CreateTime<='{endTime.ToString("yyyy-MM-dd HH:mm:ss")}' {tableTypeNameFilter} ORDER BY CreateTime,Id ASC";
            }
            else
            {
                if (db is MySqlConnection)
                    sql =
                        $"select * from datachangelog where SourceFxUserId = {fxUserId} {shopIdFilter} AND CreateTime>='{startTime.ToString("yyyy-MM-dd HH:mm:ss")}' AND CreateTime<='{endTime.ToString("yyyy-MM-dd HH:mm:ss")}'  {tableTypeNameFilter} AND Id>{minId} ORDER BY CreateTime,Id ASC limit {fetchCount}";
                else
                    sql =
                        $"select top({fetchCount}) * from DataChangeLog WITH(NOLOCK) where SourceFxUserId = {fxUserId} {shopIdFilter} AND CreateTime>='{startTime.ToString("yyyy-MM-dd HH:mm:ss")}' AND CreateTime<='{endTime.ToString("yyyy-MM-dd HH:mm:ss")}'  {tableTypeNameFilter} AND Id>{minId} ORDER BY CreateTime,Id ASC";
            }

            var result = db.Query<DataChangeLog>(sql).ToList();
            //记录日志
            Log.Debug(() => new
            {
                SQL = sql,
                DataChangeLogIds = result?.Select(m => m.Id).ToList()
            }.ToJson(), Log.LogDirectory + DateTime.Now.ToString("yyyyMMdd") + "_QueryDataChange.log");
            return result;
        }

        public override List<DataChangeLog> Query(int fxUserId, int shopId, DateTime startTime, DateTime endTime, int fetchCount = 100, long minId = 0)
        {
            return _query(fxUserId, shopId, startTime, endTime, fetchCount, -1, minId);
        }


        public override List<DataChangeLog> Query(int fxUserId, int shopId, DataChangeTableTypeName tableTypeName, DateTime startTime, DateTime endTime, int fetchCount = 100, long minId = 0)
        {
            return _query(fxUserId, shopId, startTime, endTime, fetchCount, (int)tableTypeName, minId);
        }

        public override List<DataChangeLog> Query(int fxUserId, DataChangeTableTypeName tableTypeName, DateTime startTime, DateTime endTime,
            int fetchCount = 100, long minId = 0)
        {
            return _query(fxUserId, null, startTime, endTime, fetchCount, (int)tableTypeName, minId);
        }

        public override List<DataChangeLog> QueryForPushMessage(int fetchCount = 100, long minId = 0)
        {
            var db = GetDb(_connectionString);
            var sql = string.Empty;
            if (db is MySqlConnection)
                sql = $"SELECT Id,SourceFxUserId,SourceShopId,TableTypeName FROM datachangelog WHERE Id>{minId} AND TableTypeName IN(2, 4, 6, 20) ORDER BY Id ASC limit {fetchCount}";
            else
                sql = $"SELECT TOP {fetchCount} Id,SourceFxUserId,SourceShopId,TableTypeName FROM DataChangeLog WITH(NOLOCK) WHERE Id>{minId}  AND TableTypeName IN(2, 4, 6, 20) ORDER BY Id ASC";

            var result = db.Query<DataChangeLog>(sql).ToList();

            var dbName = db.Database;
            long firstId = 0;
            long lastId = 0;
            if (result != null && result.Count > 0)
            {
                firstId = result.Min(x => x.Id);
                lastId = result.Max(x => x.Id);
            }

            //日志
            Log.WriteWarning(new
            {
                dbName,
                fetchCount,
                minId,
                firstId,
                lastId,
                currentTime = DateTime.Now,
                SQL = sql
            }.ToJson(), Log.LogDirectory + DateTime.Now.ToString("yyyyMMdd") + "_QueryForPushMessage.log");

            return result;
        }

        /// <summary>
        /// 清理历史数据（内部循环，直至完成）
        /// </summary>
        /// <param name="dateTime">截止时间</param>
        /// <param name="pageSize">每次数量</param>
        /// <param name="sleepMs">ms</param>
        /// <returns></returns>
        public override int ClearHistoryData(DateTime dateTime, int pageSize = 500, int sleepMs = 50)
        {
            var totalCount = 0;
            var db = GetDb(_connectionString);
            //var sql = string.Empty;
            //var strTime = dateTime.ToString("yyyy-MM-dd HH:mm:ss");
            //if (db is MySqlConnection)
            //{
            //    if (CloudPlatformType.TouTiao.ToString() == CustomerConfig.CloudPlatformType)
            //    {
            //        sql =
            //            $"DELETE FROM datachangelog WHERE Id IN (SELECT t2.* FROM(SELECT Id FROM datachangelog WHERE CreateTime<'{strTime}' limit {pageSize}) AS t2) ;";
            //    }
            //    else
            //    {
            //        sql = $"DELETE FROM datachangelog WHERE CreateTime<'{strTime}' limit {pageSize};";
            //    }
            //}
            //else
            //    sql = $"DELETE FROM DataChangeLog WHERE Id IN(SELECT TOP ({pageSize}) Id FROM DataChangeLog WITH(NOLOCK) WHERE CreateTime<'{strTime}')";
            
            //公共对象
            var endTime = Convert.ToDateTime(dateTime.ToString("yyyy-MM-dd 23:59:59"));
            var startTime = endTime.AddHours(-4);
            var dbName = db.Database.ToUpper();
            //获取最大ID
            var sqlByMaxId = "SELECT MAX(Id) FROM datachangelog WHERE CreateTime<=@EndTime AND CreateTime>@StartTime";
            if (db is SqlConnection)
            {
                sqlByMaxId = "SELECT MAX(Id) FROM DataChangeLog WITH(NOLOCK) WHERE CreateTime<=@EndTime AND CreateTime>@StartTime";
            }

            var maxId = db.QueryFirstOrDefault<long?>(sqlByMaxId, new { StartTime = startTime, EndTime = endTime }) ??
                        0;
            //获取Redis MaxId
            const string key = CacheKeys.ClearDataChangeLogKey;
            var lastMinIdValue = RedisHelper.HGet(key, dbName);
            long minId = 0;
            if (!string.IsNullOrWhiteSpace(lastMinIdValue))
            {
                minId = lastMinIdValue.Split('|').First().ToLong(0);
            }
            //未获取到MaxId
            if (minId == 0)
            {
                //最小
                var sqlByMinId = "SELECT Id FROM datachangelog ORDER BY Id ASC LIMIT 1";
                if (db is SqlConnection)
                {
                    sqlByMinId = "SELECT TOP 1 Id FROM DataChangeLog WITH(NOLOCK) ORDER BY Id ASC";
                }

                minId = db.QueryFirstOrDefault<long?>(sqlByMinId) ?? 0;
                RedisHelper.HSet(key, dbName, $"{minId}|{DateTime.Now:yyyy-MM-dd}|{maxId}");
            }
            //没有需要清理数据
            if (minId >= maxId)
            {
                //日志
                Log.WriteLine(
                    $"当前最小ID大于最大ID，没有需要清理的数据",
                    $"ClearHistoryData_{DateTime.Now:yyyy-MM-dd}.log");
                return 0;
            }
            //获取删除ID
            const string sqlByDelete = "DELETE FROM datachangelog WHERE Id<=@EndId AND Id>=@BeginId";
            
            if (db.State == ConnectionState.Closed)
                db.Open();

            using (db)
            {
                //连续为0的次数
                while (true)
                {
                    //是否执行清理日志
                    if (CustomerConfig.IsExecuteClearDataChangeLog() == false)
                    {
                        break;
                    }
                    //保底
                    var compareTime = DateTime.Now.Date.AddDays(-1);
                    if (endTime >= compareTime)
                    {
                        //日志
                        Log.WriteLine(
                            $"最大只能清理最近1天的数据",
                            $"ClearHistoryData_{DateTime.Now:yyyy-MM-dd}.log");
                        break;
                    }
                    //最小ID
                    var param = new { BeginId = minId, EndId = minId + pageSize };
                    var curCount = db.Execute(sqlByDelete, param);
                    //清理总笔数
                    totalCount += curCount;
                    //日志
                    Log.WriteLine(
                        $"{dbName} ==> {endTime:yyyy-MM-dd} ==> {param.BeginId}-{param.EndId} ==> {curCount} 条",
                        $"ClearHistoryData_{DateTime.Now:yyyy-MM-dd}.log");
                    //赋值最大ID
                    minId += pageSize;

                    //连续5次为0才退出
                    if (minId >= maxId)
                    {
                        RedisHelper.HDel(key, dbName);
                        break;
                    }

                    //保存最大ID
                    RedisHelper.HSet(key, dbName, $"{minId}|{DateTime.Now:yyyy-MM-dd}|{maxId}");
                    //睡眠
                    Thread.Sleep(sleepMs);
                }
            }
            return totalCount;
        }

        public override List<DataChangeLog> DistinctQuery(int fxUserId, int shopId, DataChangeTableTypeName tableTypeName, DateTime startTime, DateTime endTime)
        {
            //日志
            Log.Debug(() => new
            {
                fxUserId,
                shopId,
                startTime,
                endTime,
                tableTypeName,
                currentTime = DateTime.Now
            }.ToJson(), Log.LogDirectory + DateTime.Now.ToString("yyyyMMdd") + "_QueryDataChange.log");

            if (fxUserId <= 0)
                throw new LogicException("fxUserId必须大于0");
            if (shopId <= 0)
                throw new LogicException("shopId必须大于0");
            if (startTime > endTime)
                throw new LogicException($"startTime={startTime}必须小于endTime={endTime}");
            if (startTime < DateTime.Now.AddMonths(-3))
                throw new LogicException($"startTime={startTime}过小");


            var tableTypeNameFilter = string.Empty;
            var intTableTypeName = (int)tableTypeName;
            if (intTableTypeName != -1)
                tableTypeNameFilter = $" AND TableTypeName = {intTableTypeName}";

            var shopIdFilter = $" AND SourceShopId = {shopId}";


            IDbConnection db;
            if (CustomerConfig.DataDuplicationEnvironment == DataDuplicationEnvironment.Backup)
            {
                //备用环境
                db = GetDb(GetBackupDbConnectionString());
            }
            else
            {
                //正式环境
                var configModel = InitDbConfigs(shopId);
                db = GetDb(configModel.ConnectionString);
            }

            string sql;
            if (db is MySqlConnection)
                sql =
                    $"select distinct RelationKey,DataChangeType,ColdHotType from datachangelog where SourceFxUserId = {fxUserId} {shopIdFilter} AND CreateTime>='{startTime.ToString("yyyy-MM-dd HH:mm:ss")}' AND CreateTime<='{endTime.ToString("yyyy-MM-dd HH:mm:ss")}'  {tableTypeNameFilter}";
            else
                sql =
                    $"select distinct RelationKey,DataChangeType,ColdHotType from DataChangeLog WITH(NOLOCK) where SourceFxUserId = {fxUserId} {shopIdFilter} AND CreateTime>='{startTime.ToString("yyyy-MM-dd HH:mm:ss")}' AND CreateTime<='{endTime.ToString("yyyy-MM-dd HH:mm:ss")}'  {tableTypeNameFilter}";
            var query = db.Query<DataChangeLog>(sql);
            var result = query.ToList();
            return result;
        }
    }

    /// <summary>
    /// 日志变更仓储工厂类
    /// </summary>
    public static class DataChangeLogRepositoryFactory
    {
        public static BaseDataChangeLogRepository GetDataChangeLogRepository(string cpt = "")
        {
            return new DefaultDataChangeLogRepository(cpt);
        }

        public static BaseDataChangeLogRepository GetDataChangeLogRepository(int dbNameId, string connectionString)
        {
            return new DefaultDataChangeLogRepository(dbNameId, connectionString);
        }
    }
    
    /// <summary>
    /// 阿里云SLS 变更日志记录
    /// </summary>
    public class DataChangeRecordLogHelper
    {
        private static readonly DataChangeRecordLogHelper instance = new DataChangeRecordLogHelper();

        private static LogHelper _logHelper;
        private DataChangeRecordLogHelper()
        {
            _logHelper = new LogHelper(Config.SlsProject, LogStoreNames.DataChangeRecord);
        }

        public static LogHelper Instance
        {
            get
            {
                return _logHelper;
            }
        }
    }
    /// <summary>
    /// 火山云VLS 变更日志记录
    /// </summary>
    public class DataChangeRecordLogVolcanoHelper
    {
        private static readonly DataChangeRecordLogVolcanoHelper instance = new DataChangeRecordLogVolcanoHelper();

        private static VolcanoLogHelper _logHelper;
        private DataChangeRecordLogVolcanoHelper()
        {
            _logHelper = new VolcanoLogHelper(VolcanoLogProject.FenDanProjectId, VolcanoLogTopic.DataChangeRecordLogTopicId);
        }

        public static VolcanoLogHelper Instance
        {
            get
            {
                return _logHelper;
            }
        }
    }

    /// <summary>
    /// 数据变更日志
    /// </summary>
    public class DataChangeRecordLogDataEventTracking
    {
        /// <summary>
        /// 单例实列
        /// </summary>
        private static readonly DataChangeRecordLogDataEventTracking instance = new DataChangeRecordLogDataEventTracking();

        /// <summary>
        /// 单例
        /// </summary>
        public static DataChangeRecordLogDataEventTracking Instance
        {
            get
            {
                return instance;
            }
        }

        /// <summary>
        /// 写日志
        /// </summary>
        /// <param name="model"></param>
        public void WriteLog(DataChangeLog model)
        {
            WriteLog(new List<DataChangeLog> { model });
        }

        /// <summary>
        /// 批量写日志
        /// </summary>
        /// <param name="models"></param>
        public void WriteLog(List<DataChangeLog> models)
        {
            try
            {
                if (models == null || !models.Any())
                {
                    return;
                }
                models.ForEach(model =>
                {
                    model.HostName = Environment.MachineName;
                    model.ProcessId = Process.GetCurrentProcess().Id;
                    model.AppFilePath = AppDomain.CurrentDomain.BaseDirectory;
                });
                if (CustomerConfig.CloudPlatformType != CloudPlatformType.Pinduoduo.ToString())
                {
                    ThreadPool.QueueUserWorkItem(state =>
                    {
                        if (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString())
                        {
                            DataChangeRecordLogVolcanoHelper.Instance.WriteLog(models);
                        }
                        else
                        {
                            DataChangeRecordLogHelper.Instance.WriteLog(models);
                        }
                        
                    });
                    return;
                }
                DataChangeRecordLogHelper.Instance.WriteLog(models);
            }
            catch
            {
                //ignore
            }
        }
    }

    /// <summary>
    /// 阿里云SLS 变更日志记录
    /// </summary>
    public class DataChangeLogOfLogHelper
    {
        private static readonly DataChangeLogOfLogHelper instance = new DataChangeLogOfLogHelper();

        private static LogHelper _logHelper;
        private DataChangeLogOfLogHelper()
        {
            _logHelper = new LogHelper(Config.SlsProject, LogStoreNames.DataChangeLog);
        }

        public static LogHelper Instance
        {
            get
            {
                return _logHelper;
            }
        }
    }
    /// <summary>
    /// 火山云VLS 变更日志记录
    /// </summary>
    public class DataChangeLogOfLogVolcanoHelper
    {
        private static readonly DataChangeLogOfLogVolcanoHelper instance = new DataChangeLogOfLogVolcanoHelper();

        private static VolcanoLogHelper _logHelper;
        private DataChangeLogOfLogVolcanoHelper()
        {
            _logHelper = new VolcanoLogHelper(VolcanoLogProject.FenDanProjectId, VolcanoLogTopic.DataChangeLogTopicId);
        }

        public static VolcanoLogHelper Instance
        {
            get
            {
                return _logHelper;
            }
        }
    }

    /// <summary>
    /// 数据变更日志
    /// </summary>
    public class DataChangeLogOfLogDataEventTracking
    {
        /// <summary>
        /// 单例实列
        /// </summary>
        private static readonly DataChangeLogOfLogDataEventTracking instance = new DataChangeLogOfLogDataEventTracking();

        /// <summary>
        /// 单例
        /// </summary>
        public static DataChangeLogOfLogDataEventTracking Instance
        {
            get
            {
                return instance;
            }
        }

        /// <summary>
        /// 写日志
        /// </summary>
        /// <param name="model"></param>
        public void WriteLog(DataChangeLogModel model)
        {
            WriteLog(new List<DataChangeLogModel> { model });
        }

        /// <summary>
        /// 批量写日志
        /// </summary>
        /// <param name="models"></param>
        public void WriteLog(List<DataChangeLogModel> models)
        {
            try
            {
                if (models == null || !models.Any())
                {
                    return;
                }
                models.ForEach(model =>
                {
                    model.HostName = Environment.MachineName;
                    model.ProcessId = Process.GetCurrentProcess().Id;
                    model.AppFilePath = AppDomain.CurrentDomain.BaseDirectory;
                });
                if (CustomerConfig.CloudPlatformType != CloudPlatformType.Pinduoduo.ToString())
                {
                    ThreadPool.QueueUserWorkItem(state =>
                    {
                        if (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString())
                        {
                            DataChangeLogOfLogVolcanoHelper.Instance.WriteLog(models);
                        }
                        else
                        {
                            DataChangeLogOfLogHelper.Instance.WriteLog(models);
                        }
                        
                    });
                    return;
                }
                DataChangeLogOfLogHelper.Instance.WriteLog(models);
            }
            catch
            {
                //ignore
            }
        }
    }
}
