using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.FxModel.FxColdHotMigrate
{
    /// <summary>
    /// 冷热迁移配置
    /// </summary>
    public class ColdHotMigrateConfig
    {
        /// <summary>
        /// 是否进行重新迁移
        /// </summary>
        public bool IsReMigrate { get; set; } = false;
        /// <summary>
        /// 重迁数据的开始时间
        /// </summary>
        public DateTime? ReMigrateDataBegin { get; set; }
        /// <summary>
        /// 重迁数据的结束时间
        /// </summary>
        public DateTime? ReMigrateDataEnd { get; set; }

        /// <summary>
        /// 冷库限制写入数据行，超出这个行数将不再写入冷库
        /// </summary>
        public int LimitRows { get; set; } =300*10000; // 每次迁移的行数限制，默认300万行
    }
}
