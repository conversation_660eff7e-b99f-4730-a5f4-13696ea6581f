using DianGuanJiaApp.Data.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.FxModel
{
    public class LogicOrderChangeModel
    {
        public List<string> Fields { get; set; }
        public LogicOrder Order { get; set; }
        public LogicOrder OldOrder { get; set; }
    }

    public class LogicOrderPathChangeModel
    {
        public string LogicOrderId { get; set; }
        public string PlatformOrderId { get; set; }
        public LogicOrderPathChangeLogModel New { get; set; }
        public LogicOrderPathChangeLogModel Old { get; set; }
    }

    public class LogicOrderPathChangeLogModel
    {
        public string Code { get; set; }
        public string Province { get; set; }
        public string City { get; set; }
    }

    public class OrderItemChangeModel
    {
        public List<string> Fields { get; set; }
        public OrderItem Item { get; set; }
        public OrderItem OldItem { get; set; }
    }

}
