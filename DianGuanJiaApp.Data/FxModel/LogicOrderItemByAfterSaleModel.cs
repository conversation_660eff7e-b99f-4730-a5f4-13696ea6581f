namespace DianGuanJiaApp.Data.FxModel
{
    public class LogicOrderItemByAfterSaleModel
    {
        /// <summary>
        /// 系统单号
        /// </summary>
        public string LogicOrderId { get; set; }
        /// <summary>
        /// 平台订单号
        /// </summary>
        public string PlatformOrderId { get; set; }
        /// <summary>
        /// 店铺ID
        /// </summary>
        public int ShopId { get; set; }
        /// <summary>
        /// 订单项代码
        /// </summary>
        public string OrderItemCode { get; set; }
        /// <summary>
        /// 路径代码
        /// </summary>
        public string PathFlowCode { get; set; }
    }
}