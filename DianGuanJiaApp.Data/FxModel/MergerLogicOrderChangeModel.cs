using Dapper;
using DianGuanJiaApp.Data.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Entity
{

    /// <summary>
    /// 逻辑订单合并修改模型
    /// </summary>
    public partial class MergerLogicOrderChangeModel
    {
        public string PlatformType { get; set; }
        public LogicOrder InsertOrder { get; set; }
        public List<LogicOrderItem> InsertLogicOrderItems { get; set; }
        public int MergeredType { get; set; }
        public List<string> UpdateMergeredTypeLogicOrderIds { get; set; }

        //public MergerLogicOrderChangeMergeredTypeModel UpdateMergeredTypeModel { get; set; }

        public MergerLogicOrderChangeUpdateCacluteFieldModel UpdateCaculateFiledOrder { get; set; }
        public string MainLogicOrderId 
        { 
            get 
            {
                if (InsertOrder != null)
                    return InsertOrder.LogicOrderId;
                if(!string.IsNullOrEmpty(_InsertOrderLogicOrderId))
                    return _InsertOrderLogicOrderId;
                if(UpdateCaculateFiledOrder != null)
                    return UpdateCaculateFiledOrder.LogicOrderId;
                return "";
            }
        }
        /// <summary>
        /// 实时同步时：InsertOrder.LogicOrderId
        /// </summary>
        public string _InsertOrderLogicOrderId { get; set; }
        public OrderTags NeedNewAddOrderTag { get; set; }
    }

    public class MergerLogicOrderChangeUpdateCacluteFieldModel
    {
        public string LogicOrderId { get; set; }
        public int MergeredType { get; set; }
        /// <summary>
        ///  
        /// </summary>
        public decimal? TotalAmount { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public decimal? TotalWeight { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public int? ProductPlatformIdCount { get; set; }
        /// <summary>
        ///  
        /// </summary>
        public int? ProductCount { get; set; }
        /// <summary>
        ///  
        /// </summary>
        public int? ProductKindCount { get; set; }

        /// <summary>
        ///  
        /// </summary>
        public int? ProductItemCount { get; set; }

        /// <summary>
        /// 源店铺Id（变更日志）
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 源用户Id（变更日志）
        /// </summary>
        public int FxUserId { get; set; }

    }

}