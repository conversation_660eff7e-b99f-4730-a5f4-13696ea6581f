using DianGuanJiaApp.Data.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DianGuanJiaApp.Data.Model
{
    public class SaveShortTitleOrWeightModel
    {
        /// <summary>
        /// 商品唯一编码
        /// </summary>
        public string ProductCode { get; set; }
        /// <summary>
        /// 规格唯一编码
        /// </summary>
        public string SkuCode { get; set; }
        /// <summary>
        /// 商品简称
        /// </summary>
        public string ShortTitle { get; set; }
        /// <summary>
        /// 修改前商品简称
        /// </summary>
        public string RowShortTitle { get; set; }
        /// <summary>
        /// 商品重量
        /// </summary>
        public decimal Weight { get; set; }
        /// <summary>
        /// 修改前商品重量
        /// </summary>
        public decimal RawWeight { get; set; }
        /// <summary>
        /// 商品规格简称
        /// </summary>
        public string SkuShortTitle { get; set; }
        /// <summary>
        /// 修改前商品规格简称
        /// </summary>
        public string RawSkuShortTitle { get; set; }
        /// <summary>
        /// 商品规格重量
        /// </summary>
        public decimal SkuWeight { get; set; }
        /// <summary>
        /// 修改前商品规格重量
        /// </summary>
        public decimal RawSkuWeight { get; set; }
        /// <summary>
        /// 重量是否修改
        /// </summary>
        public bool IsWeightChange { get; set; }
        /// <summary>
        /// 简称是否修改
        /// </summary>
        public bool IsShortTitleChange { get; set; }

        /// <summary>
        /// 同步对象用户Id
        /// </summary>
        public int SyncFxUserId { get; set; }
    }

    /// <summary>
    /// 更新采购结算价模型
    /// </summary>
    public class UpdatePurchaseSettlementPriceModel
    {
        /// <summary>
        /// 所属用户Id
        /// </summary>
        public int UserId { get; set; }
        /// <summary>
        /// 所属店铺Id
        /// </summary>
        public int ShopId { get; set; }
        /// <summary>
        /// 商品平台Id
        /// </summary>
        public string PlatformId { get; set; }

        /// <summary>
        /// 各Sku
        /// </summary>
        public List<PurchaseSettlementPriceSku> Skus { get; set; }
    }
    public class PurchaseSettlementPriceSku
    {
        /// <summary>
        /// 
        /// </summary>
        public string SkuId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PriceJson { get; set; }

    }
}