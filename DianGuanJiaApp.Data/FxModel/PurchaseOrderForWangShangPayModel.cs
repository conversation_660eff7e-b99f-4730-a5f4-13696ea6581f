using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Data.FxModel
{
    public class PurchaseOrderForWangShangPayModel
    {
        /// <summary>
        /// 厂家用户Id
        /// </summary>
        public int SupplierFxUserId { get; set; }
        /// <summary>
        /// 厂家名称
        /// </summary>
        public string SupplierNickName { get; set; }
        /// <summary>
        /// 厂家是否开通了采购金支付
        /// </summary>
        public bool IsOpenWangShangPay { get; set; }
        /// <summary>
        /// 开通链接
        /// </summary>
        public string OpenWangShangPayUrl { get; set; }
        /// <summary>
        /// 当前用户：支付方式配置
        /// </summary>
        public string PasswordFreePaySet { get; set; }
        /// <summary>
        /// 当前用户：是否开通了免密支付
        /// </summary>
        public bool IsOpenedPasswordFreePay { get; set; }
        /// <summary>
        /// 申请批次号
        /// </summary>
        public string ApplyNo { get; set; }
        public List<PurchaseOrderCreateResultModel> PurchaseOrders { get; set; }
        public List<PurchaseOrderCreateResultModel> SuccessPurchaseOrders
        {
            get
            {
                if (PurchaseOrders == null || PurchaseOrders.Any() == false)
                    return new List<PurchaseOrderCreateResultModel>();

                return PurchaseOrders.Where(a => a.Status == 1).ToList();
            }

        }
        public List<PurchaseOrderCreateResultModel> ErrorPurchaseOrders
        {
            get
            {
                if (PurchaseOrders == null || PurchaseOrders.Any() == false)
                    return new List<PurchaseOrderCreateResultModel>();

                return PurchaseOrders.Where(a => a.Status != 1).ToList();
            }
        }
        public List<string> CouldPayLogicOrderIds
        {
            get
            {
                if (PurchaseOrders == null || PurchaseOrders.Any() == false)
                    return new List<string>();

                return PurchaseOrders.Where(a => a.Status == 1).Select(a => a.SourceLogicOrderId).ToList();
            }
        }
        public List<string> CouldPayPlatformOrderIds
        {
            get
            {
                if (PurchaseOrders == null || PurchaseOrders.Any() == false)
                    return new List<string>();

                return PurchaseOrders.Where(a => a.Status == 1).Select(a => a.PurchasePlatformOrderId).ToList();
            }
        }
        public List<string> ErrorLogicOrderIds
        {
            get
            {
                if (PurchaseOrders == null || PurchaseOrders.Any() == false)
                    return new List<string>();

                return PurchaseOrders.Where(a => a.Status != 1).Select(a => a.SourceLogicOrderId).ToList();
            }
        }
        public int SuccessPurchaseCount
        {
            get
            {
                if (PurchaseOrders == null || PurchaseOrders.Any() == false)
                    return 0;

                return PurchaseOrders.Where(a => a.Status == 1).Count();
            }
        }
        public string SuccessLogicOrderString
        {
            get
            {
                if (CouldPayLogicOrderIds == null || CouldPayLogicOrderIds.Any() == false)
                    return "";

                return string.Join(",", CouldPayLogicOrderIds);
            }
        }
        public string ErrorLogicOrderString
        {
            get
            {
                if (ErrorLogicOrderIds == null || ErrorLogicOrderIds.Any() == false)
                    return "";

                return string.Join(",", ErrorLogicOrderIds);
            }
        }
    }

    /// <summary>
    /// 检查采购金是否支付成功-结果模型
    /// </summary>
    public class CheckLogicOrderIsPayResutForWangShangPay
    {
        public List<PaymentStatementOrder> Orders { get; set; }

        /// <summary>
        /// 需要继续支付订单
        /// </summary>
        public string NeedPayLogicOrderIdString { get; set; }
        /// <summary>
        /// 需要继续支付订单的总金额
        /// </summary>
        public decimal NeedPayAmount { get; set; }
        /// <summary>
        /// 待支付订单数
        /// </summary>
        public int WaitPayCount
        {
            get
            {
                if (Orders == null)
                    return 0;
                return Orders.Count(x => x.PayStatus == 0);
            }
        }
        /// <summary>
        /// 支付失败数
        /// </summary>
        public int FailPayCount
        {
            get
            {
                if (Orders == null)
                    return 0;
                return Orders.Count(x => x.PayStatus < 0);
            }
        }
        /// <summary>
        /// 支付成功数
        /// </summary>
        public int SuccessPayCount
        {
            get
            {
                if (Orders == null)
                    return 0;
                return Orders.Count(x => x.PayStatus == 1);
            }
        }

    }
}