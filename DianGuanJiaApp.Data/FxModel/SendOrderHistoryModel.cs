using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Extension;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.FxModel
{
    public class SendHistoryModel
    {
        public SendHistoryModel()
        {
            SendOrders = new List<SendHistoryOrderModel>();
            OrderTags = new List<OrderTags>();
        }
        public int Id { get; set; }
        public string SendOrderId { get; set; }
        public bool IsMergerOrder
        {
            get
            {
                if (string.IsNullOrEmpty(SendOrderId))
                    return false;
                return SendOrderId.StartsWith("C");
            }
        }
        public string OrderJoin { get; set; }
        public string LogistiscBillNo { get; set; }
        public string ExpressName { get; set; }
        public string Reciver { get; set; }
        public string ReciverPhone { get; set; }
        public string ReciverAddress { get; set; }
        public string ToProvince { get; set; }
        public string ToCity { get; set; }
        public string ToDistrict { get; set; }
        public string PlatformType { get; set; }
        public decimal? Weight { get; set; }
        public int SendType { get; set; }
        public string SendDate { get; set; }

        public int Status { get; set; }
        public int ShopId { get; set; }
        public int FxUserId { get; set; }
        public int UpFxUserId { get; set; }
        public int DownFxUserId { get; set; }
        /// <summary>
        /// 商家（上游商家或自身）
        /// </summary>
        public string AgentName { get;set;}
        /// <summary>
        /// 厂家（下游厂家或自身）
        /// </summary>
        public string SupplierName{get;set; }
        public string SelfShopName { get; set; }
        public bool IsShowSalePrice { get; set; }
        /// <summary>
        /// 是否显示商品标题
        /// </summary>
        public bool IsShowProductTitle { get; set; } = true;

        /// <summary>
        /// 是否显示商品图片
        /// </summary>
        public bool IsShowProductImg { get; set; } = true;

        /// <summary>
        /// 商家店铺名
        /// </summary>
        public string AgentShopName { get; set; }

        public string PathFlowCode { get; set; }
        public bool IsPartSend { get; set; }
        public int Index { get; set; }
        /// <summary>
        /// 发货的快递单号
        /// </summary>
        public List<ExpressNameWybCodeModel> ExpressNameWybCodes { get; set; }
        public List<SendHistoryOrderModel> SendOrders {get;set; }

        public string SendHistoryCode { get; set; }

        /// <summary>
        /// 订单标签
        /// </summary>
        public List<OrderTags> OrderTags { get; set; }

        /// <summary>
        /// 是否虚拟手机号-分机号
        /// </summary>
        public bool IsVirtualPhone {
            get {
                if(PlatformType == DianGuanJiaApp.Data.Enum.PlatformType.WxVideo.ToString())
                {
                    if(ReciverPhone != null && ReciverPhone.Contains("-"))
                        return true;
                }
                return false;
            }
        }
        public int SendHistoryCount { get; set; }
        public int SumItemCount { get; set; }
        public int RowsCount { get; set; }
        /// <summary>
        /// 是否线下单
        /// </summary>
        public bool IsOfflineOrder { get; set; }

        /// <summary>
        /// 揽收方式DOOR_TO_DOOR/SELF_DELIVERY
        /// </summary>
        public string Collectiontype { get; set; }
        /// <summary>
        /// 国家
        /// </summary>
        public string ToCountry { get; set; }

        /// <summary>
        /// 包裹ID
        /// </summary>
        public string PackageId { get; set; }

        /// <summary>
        /// 发件人的国家
        /// </summary>
        public string FromCountry { get; set; }

        public string PackageStatus { get; set; }

        /// <summary>
        /// 自行寄送信息,当自寄时不为空
        /// </summary>
        public string OrderSelfDeliveryBatchNo { get; set; }

        /// <summary>
        /// 是否手工发货
        /// </summary>
        public bool IsManual => SendType == 44;

        public bool IsOfflineNoSku
        {
            get
            {
                if (this.OrderTags != null && this.OrderTags.Any())
                {
                    return this.OrderTags.Any(t => t.Tag == OrderTag.OfflineNoSku.ToString());
                }
                else
                {
                    return false;
                }
            }
        }
    }

    public class SendHistoryOrderModel
    {
        public SendHistoryOrderModel()
        {
            SendProducts = new List<SendHistoryOrderProductModel>();
        }
        public int ID { get; set; }
        public int Index { get; set; }
        public int SendHistoryId { get; set; }
        public string OrderId { get; set; }
        public int SendType { get; set; }
        public string PlatformOrderId { get; set; }
        // 是否部分发货
        public bool IsPartSend { get; set; }
        public int TotalWeight { get; set; }
        /// <summary>
        /// 实收款
        /// </summary>
        public decimal TotalAmount { get; set; }
        /// <summary>
        /// 平台补贴
        /// </summary>
        public decimal PlatformSubsidy { get; set; }
        /// <summary>
        /// 运费
        /// </summary>
        public decimal ShippingFee { get; set; }
        /// <summary>
        /// 实收款 = 平台补贴+实收款
        /// </summary>
        private decimal _payTotalAmount;
        public decimal PayTotalAmount
        {
            get {
                return TotalAmount + PlatformSubsidy;
            }

            set
            {
                _payTotalAmount = value;
            }
        }
        /// <summary>
        /// 订单付款时间
        /// </summary>
        public DateTime? PayTime { get; set; }
        /// <summary>
        /// 订单创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }
        public List<SendHistoryOrderProductModel> SendProducts { get; set; }

        public string SendHistoryCode { get; set; }
        /// <summary>
        /// 自动生成 (SendHistoryCode + OrderId + SendType).ToShortMd5()
        /// </summary>
        public string SendHistoryOrderCode
        {
            get
            {
                if (string.IsNullOrEmpty(SendHistoryCode))
                    return (SendHistoryId + OrderId + SendType).ToShortMd5();
                else
                    return (SendHistoryCode + OrderId + SendType).ToShortMd5();
            }
        }

        /// <summary>
        /// 采购单是否已付款
        /// </summary>
        public bool IsPurchasePayed
        {
            get; set;
        }

        /// <summary>
        /// 采购单情况：0不需要，1已下单，-1待下单
        /// </summary>
        public int NeedPurchase
        {
            get; set;
        }

        /// <summary>
        /// 采购单状态;waitcreate待创建，waitpay待支付，waitsellersend待发货，waitbuyerreceive待收货，close交易关闭，success交易完成，error
        /// </summary>
        public string PurchaseStatus
        {
            get; set;
        }

        /// <summary>
        /// 自行寄送信息,当自寄时不为空
        /// </summary>
        public string OrderSelfDeliveryBatchNo { get; set; }
    }

    public class SendHistoryOrderProductModel
    {
        public int Id { get; set; }
        /// <summary>
        /// SendHistoryOrder.Id
        /// </summary>
        public int MasterId { get; set; }
        public int OrderItemId { get; set; }
        public int Quantity { get; set; }
        public string OrderItemCode { get; set; }
        public int SendType { get; set; }
        public string ProductId { get; set; }
        public string SkuId { get; set; }
        public string ProductCode { get; set; }
        public string SkuCode { get; set; }

        public int Count { get; set; }
        public decimal? Price { get; set; }
        public decimal? ItemAmount { get; set; }
        public string ProductSubject { get; set; }
        public string ProductCargoNumber { get; set; }
        public string CargoNumber { get; set; }
        public string ProductImgUrl { get; set; }
        public string RefundStatus { get; set; }
        public string Status { get; set; }
        public string Color { get; set; }
        public string Size { get; set; }

        public string ShortTitle { get; set; }
        public int Weight { get; set; }
        public string SkuShortTitle { get; set; }
        public int SkuWeight { get; set; }
        public int Index { get; set; }
        /// <summary>
        /// 达人Id
        /// </summary>
        public string AuthorId { get; set; }
        /// <summary>
        /// 达人名称
        /// </summary>
        public string AuthorName { get; set; }
        public string SendHistoryOrderCode { get; set; }

        /// <summary>
        /// 订单项标签
        /// </summary>
        public List<OrderTags> OrderItemTags { get; set; }

        /// <summary>
        /// 我对上游厂家设置的结算价
        /// </summary>
        public decimal AuthorUpSettlementPrice { get; set; }
        /// <summary>
        /// 上游厂家结算价
        /// </summary>
        public decimal UpFxUserSettlementPrice { get; set; }
        /// <summary>
        /// 下游商家结算价
        /// </summary>
        public decimal DownFxUserSettlementPrice { get; set; }
        /// <summary>
        /// 我对下游商家设置的结算价
        /// </summary>
        public decimal AuthorDownSettlementPrice { get; set; }

        /// <summary>
        /// 基础资料规格表的SkuUid 
        /// </summary>
        public long BaseProductSkuUid { get; set; }

        /// <summary>
        /// 是否有关联基础商品
        /// </summary>
        public bool IsRelationBaseProduct { get; set; }

        /// <summary>
        /// 卡片->基础商品数据【json】
        /// </summary>
        public PrductSkuSimple BaseProductInfo { get; set; } = new PrductSkuSimple();

        /// <summary>
        /// 卡片->平台商品数据【json】
        /// </summary>
        public PrductSkuSimple PtProductInfo { get; set; } = new PrductSkuSimple();
    }

    public class ExpressNameWybCodeModel
    {
        public string ExpressName { get; set; }
        public List<WaybillCodeModel> WaybillCodes { get; set; } 
    }

    public class WaybillCodeModel
    {
        public string LogistiscBillNo { get; set; }
        public int Status { get; set; }

        public List<WaybillCodeChild> ChildWaybillCodes { get; set; }

        /// <summary>
        /// TikTok 跨境专用（物流信息列显示 自信寄送/上门揽收/平台物流等信息）
        /// 物流类型: 0.无需处理，1.国内段物流，2.国际段物流
        /// </summary>
        public int LogisticType { get; set; }

        /// <summary>
        /// 自行寄送物流公司编码
        /// </summary>
        public string ExpressCpCode { get; set;}

        /// <summary>
        /// 包裹id
        /// </summary>
        public string PackageId { get; set; }
    }
}
