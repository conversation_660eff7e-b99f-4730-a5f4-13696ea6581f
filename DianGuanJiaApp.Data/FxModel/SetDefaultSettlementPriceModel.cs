using System;
using DianGuanJiaApp.Utility.Model.AliLog;

namespace DianGuanJiaApp.Data.FxModel
{
    public class SetDefaultSettlementPriceModel : IAliPutLogs
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        public string BatchId { get; set; }
        /// <summary>
        /// ID
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        ///  商品编码
        /// </summary>
        public string ProductCode { get; set; }
        /// <summary>
        /// 规格SKU代码
        /// </summary>
        public string SkuCode { get; set; }
        /// <summary>
        /// 旧的默认结算价
        /// </summary>
        public decimal? OldDefaultSettlementPrice { get; set; }
        /// <summary>
        /// 默认结算价
        /// </summary>
        public decimal? DefaultSettlementPrice { get; set; }
        /// <summary>
        /// 设置分销用户ID
        /// </summary>
        public int CreateFxUserId { get; set; }
        /// <summary>
        /// 创建用户昵称
        /// </summary>
        public string CreateFxUserNickName { get; set; }
        /// <summary>
        /// 创建用户手机
        /// </summary>
        public string CreateFxUserMobile { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        public int? SubFxUserId { get; set; }
        /// <summary>
        /// 显示操作账号,若是主账号：手机号（账号名）；子账号：账号名称（手机号）；
        /// </summary>
        public string ShowOperateUserFx { get; set; }

    }
}