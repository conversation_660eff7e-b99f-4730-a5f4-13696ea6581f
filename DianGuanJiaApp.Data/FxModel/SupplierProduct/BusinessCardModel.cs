using System;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Data.FxModel
{
    /// <summary>
    /// 我的小站（原名片资料）
    /// </summary>
    public class BusinessCardModel
    {
        /// <summary>
        /// 头像Url
        /// </summary>
        public string AvatarUrl { get; set; }

        /// <summary>
        /// 小站名称
        /// </summary>
        public string StationName { get; set; }

        /// <summary>
        /// 公司信息
        /// </summary>
        public List<BusinessCardCompanyInfo> CompanyInfoList { get; set; }

        /// <summary>
        /// 代发发货地址
        /// </summary>
        public List<BusinessCardSendaddress> SendaddressList { get; set; }

        /// <summary>
        /// 业务联系人
        /// </summary>
        public List<BusinessCardContact> BusinesscontactList { get; set; }

        /// <summary>
        /// 代发售后地址
        /// </summary>
        public List<BusinessCardAfterSaleAddress> AftersaleaddressList { get; set; }

        /// <summary>
        /// 支持分销平台
        /// </summary>
        public List<BusinessCardPlatformType> PlatformtypeList { get; set; }

        /// <summary>
        /// 收款信息
        /// </summary>
        public List<BusinessCardPaymentInfo> PaymentinfoList { get; set; }

        /// <summary>
        /// 自营店铺
        /// </summary>
        public List<BusinessCardShop> ShopList { get; set; }

        /// <summary>
        /// 售后服务
        /// </summary>
        public BusinessCardAftesales Aftesales { get; set; }

        /// <summary>
        /// 发货快递，用逗号隔开
        /// </summary>
        public string SendCourier { get; set; }

        /// <summary>
        /// 运费模板类型
        /// </summary>
        public string TemplateType { get; set; }

        /// <summary>
        /// 发货时间
        /// </summary>
        public string DeliveryTime { get; set; }

        /// <summary>
        /// 订单截止时间
        /// </summary>
        public OrderCutoffTime OrderCutoffTime { get; set; }

        /// <summary>
        /// 面单信息
        /// </summary>
        public List<ExpressBill> ExpressBill { get; set; }

        /// <summary>
        /// 售后承诺
        /// </summary>
        public int? AfterSalePromise { get; set; }

        /// <summary>
        /// 售后处理时效
        /// </summary>
        public string AfterSaleTime { get; set; }

        /// <summary>
        /// 结算周期
        /// </summary>
        public string Period { get; set; }

        /// <summary>
        /// 名片备注
        /// </summary>
        public string Remark { get; set; }
    }

    /// <summary>
    /// 发货信息
    /// </summary>
    public class ShipmentsInfo
    {
        /// <summary>
        /// 代发发货地址
        /// </summary>
        public List<BusinessCardSendaddress> SendaddressList { get; set; }
        
        /// <summary>
        /// 发货快递，用逗号隔开
        /// </summary>
        public string SendCourier { get; set; } 
        
        /// <summary>
        /// 运费模板类型
        /// </summary>
        public string TemplateType { get; set; }
        
        /// <summary>
        /// 发货时间
        /// </summary>
        public string DeliveryTime { get; set; }
        
        /// <summary>
        /// 面单信息
        /// </summary>
        public List<ExpressBill> ExpressBill { get; set; }
    }

    /// <summary>
    /// 售后信息
    /// </summary>
    public class AfterSalesInfo
    {
        /// <summary>
        /// 代发售后地址
        /// </summary>
        public List<BusinessCardAfterSaleAddress> AftersaleaddressList { get; set; }
        
        /// <summary>
        /// 售后承诺
        /// </summary>
        public int? AfterSalePromise { get; set; }

        /// <summary>
        /// 售后处理时效
        /// </summary>
        public string AfterSaleTime { get; set; } 
        
        /// <summary>
        /// 其他承诺
        /// </summary>
        public List<BusinessCardAftesales> Aftersales { get; set; }
    }

    /// <summary>
    /// 订单截止时间
    /// </summary>
    public class OrderCutoffTime
    {
        /// <summary>
        /// 小时
        /// </summary>
        public string Hour { get; set; }
        
        /// <summary>
        /// 分钟
        /// </summary>
        public string Minute { get; set; }
    }

    /// <summary>
    /// 快递面单
    /// </summary>
    public class ExpressBill
    {
        /// <summary>
        /// Id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 快递面单类型
        /// </summary>
        public string ExpressBillType { get; set; }
        
        /// <summary>
        /// 快递面单名称
        /// </summary>
        public string ExpressBillName { get; set; }

        /// <summary>
        /// 快递面单名称str
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public static string ConvertNameToStr(List<ExpressBill> models)
        {
            if (models == null || models.Any() == false) return string.Empty;

            var list = models.Select(x => x.ExpressBillName).ToList();
            // 用逗号隔开的字符串
            return string.Join(",", list);
        }
    }

    /// <summary>
    /// 公司信息
    /// </summary>
    public class BusinessCardCompanyInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 公司名称 
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 营业期限 
        /// </summary>
        public string BusinessTerm { get; set; }

        /// <summary>
        /// 统一社会信用代码 
        /// </summary>
        public string USCC { get; set; }

        /// <summary>
        /// 省 
        /// </summary>
        public string Province { get; set; }

        /// <summary>
        /// 市 
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 区/县 
        /// </summary>
        public string County { get; set; }

        /// <summary>
        /// 详细地址 
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// 营业执照图片 
        /// </summary>
        public string LicensePic { get; set; }

        /// <summary>
        /// 主体类型 
        /// </summary>
        public string SubjectType { get; set; }

        /// <summary>
        /// 是否长期
        /// </summary>
        public bool IsLongTerm { get; set; }
    }

    /// <summary>
    /// 代发发货地址
    /// </summary>
    public class BusinessCardSendaddress
    {
        /// <summary>
        /// 省 
        /// </summary>
        public string Province
        {
            get { return _province == "0" ? string.Empty : _province; }
            set { _province = value; }
        }
        private string _province;

        /// <summary>
        /// 市 
        /// </summary>
        public string City
        {
            get { return _city == "0" ? string.Empty : _city; }
            set { _city = value; }
        }
        private string _city;

        /// <summary>
        /// 区/县 
        /// </summary>
        public string County
        {
            get { return _county == "0" ? string.Empty : _county; }
            set { _county = value; }
        }
        private string _county;
        
        /// <summary>
        /// 街道
        /// </summary>
        public string Street
        {
            get { return _street == "0" ? string.Empty : _street; }
            set { _street = value; }
        }
        private string _street;

        /// <summary>
        /// Id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 详细地址 
        /// </summary>
        public string Address
        {
            get { return _address ?? string.Empty; }
            set { _address = value; }
        }
        private string _address;

        /// <summary>
        /// 收货人姓名
        /// </summary>
        public string ReceiverName
        {
            get { return _receiverName ?? string.Empty; }
            set { _receiverName = value; }
        }
        private string _receiverName;

        /// <summary>
        /// 收货人联系方式
        /// </summary>
        public string ReceiverContract
        {
            get { return _receiverContract ?? string.Empty; }
            set { _receiverContract = value; }
        }
        private string _receiverContract;

        /// <summary>
        /// 收货人固话
        /// </summary>
        public string ReceiverTel
        {
            get { return _receiverTel ?? string.Empty; }
            set { _receiverTel = value; }
        }
        private string _receiverTel;
    }

    /// <summary>
    /// 业务联系人
    /// </summary>
    public class BusinessCardContact
    {
        /// <summary>
        /// 主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        public string JobTitle { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string Tel { get; set; }

        /// <summary>
        /// 联系人二维码
        /// </summary>
        public string ImageUrl { get; set; }
    }

    /// <summary>
    /// 代发售后地址
    /// </summary>
    public class BusinessCardAfterSaleAddress
    {
        /// <summary>
        /// 省 
        /// </summary>
        public string Province
        {
            get { return _province == "0" ? string.Empty : _province; }
            set { _province = value; }
        }
        private string _province;

        /// <summary>
        /// 市 
        /// </summary>
        public string City
        {
            get { return _city == "0" ? string.Empty : _city; }
            set { _city = value; }
        }
        private string _city;

        /// <summary>
        /// 区/县 
        /// </summary>
        public string County
        {
            get { return _county == "0" ? string.Empty : _county; }
            set { _county = value; }
        }
        private string _county;

        /// <summary>
        /// 街道
        /// </summary>
        public string Street
        {
            get { return _street == "0" ? string.Empty : _street; }
            set { _street = value; }
        }
        private string _street;

        /// <summary>
        /// Id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 详细地址 
        /// </summary>
        public string Address
        {
            get { return _address ?? string.Empty; }
            set { _address = value; }
        }
        private string _address;

        /// <summary>
        /// 收货人姓名
        /// </summary>
        public string ReceiverName
        {
            get { return _receiverName ?? string.Empty; }
            set { _receiverName = value; }
        }
        private string _receiverName;

        /// <summary>
        /// 收货人联系方式
        /// </summary>
        public string ReceiverContract
        {
            get { return _receiverContract ?? string.Empty; }
            set { _receiverContract = value; }
        }
        private string _receiverContract;

        /// <summary>
        /// 收货人固定电话
        /// </summary>
        public string ReceiverTel
        {
            get { return _receiverTel ?? string.Empty; }
            set { _receiverTel = value; }
        }
        private string _receiverTel;
    }

    /// <summary> 
    /// 此模型是由 抖店平台售后地址转换而来(不干扰系统地址模型)，比系统地址模型多了 退货默认
    /// </summary>
    public class BusinessCardAfterSaleAddressV2 : BusinessCardAfterSaleAddress
    {
        /// <summary>
        /// 是否为退货默认,1-是；0-否
        /// 2025-06-24 新加
        /// </summary>
        public int IsDefault { get; set; }

        /// <summary>
        /// 平台售后地址Id （退货或者换货，选择厂家地址时，AfterSaleAddressType = 2时，必传）
        /// </summary>
        public string ReceiverAddressId { get; set; }
    }

    /// <summary>
    /// 支持分销平台
    /// </summary>
    public class BusinessCardPlatformType
    {
        /// <summary>
        /// 主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 平台名称
        /// </summary>
        public string PlatformName { get; set; }
    }

    /// <summary>
    /// 收款信息
    /// </summary>
    public class BusinessCardPaymentInfo
    {
        /// <summary>
        /// 主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 收款类型
        /// </summary>
        public string PaymentType { get; set; }

        /// <summary>
        /// 收款账号
        /// </summary>
        public string PaymentAccount { get; set; }

        /// <summary>
        /// 户名
        /// </summary>
        public string AccountName { get; set; }

        /// <summary>
        /// 开户行
        /// </summary>
        public string AccountBank { get; set; }

        /// <summary>
        /// 收款码
        /// </summary>
        public string PayPicObj { get; set; }
    }

    /// <summary>
    /// 自营店铺
    /// </summary>
    public class BusinessCardShop
    {
        /// <summary>
        /// 主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 店铺ID
        /// </summary>
        public string ShopId { get; set; }

        /// <summary>
        /// 销售平台
        /// </summary>
        public string Platform { get; set; }

        /// <summary>
        /// 店铺名称
        /// </summary>
        public string ShopName { get; set; }

        /// <summary>
        /// 店铺链接
        /// </summary>
        public string Url { get; set; }
    }

    /// <summary>
    /// 售后服务
    /// </summary>
    public class BusinessCardAftesales
    {
        /// <summary>
        /// 主键
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 售后内容
        /// </summary>
        public string Content { get; set; }
    }

    /// <summary>
    /// 合作反馈模型
    /// </summary>
    public class CooperateEvaluateModel
    {
        /// <summary>
        /// 主键自增
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 标签，多个逗号隔开
        /// </summary>
        public string Tags { get; set; }

        /// <summary>
        /// 评价内容
        /// </summary>
        public string Remark { get; set; }
        
        /// <summary>
        /// 留言账号
        /// </summary>
        public string Account { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public string UpdateTime { get; set; }
    }
}