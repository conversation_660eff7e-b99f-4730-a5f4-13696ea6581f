using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model
{

    /// <summary>
    /// 添加预发货请求模型
    /// </summary>
    public class AddPreordainRequestModel
    {
        /// <summary>
        /// 要添加到 Predain表中的数据
        /// </summary>
        public List<OrderPreordainModel> AddPreordainOrder { get; set; }

        /// <summary>
        /// 要修改 IsPreordain 状态的订单
        /// </summary>
        public List<OrderPreordainModel> UpdateIsPreordainOrder { get; set; }

        /// <summary>
        /// 快递id
        /// </summary>
        public int ExpressId { get; set; }

    }

    public class OrderPreordainModel : OrderSelectKeyModel
    {
        public string WaybillCode { get; set; }
    }


}
