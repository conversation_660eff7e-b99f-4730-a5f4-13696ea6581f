using DianGuanJiaApp.Data.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model
{
    public class AppendLogPageResultModel : PagedResultModel<BranchShareRelationLogDetail>
    {                                      
        public int AppendTotalQuantity { get; set; }                       
    }

    public class UsedLogPageResultModel : PagedResultModel<BranchShareRelationLogDetail>
    {
        public int UsedTotalQuantity { get; set; }
        public List<int> ShareIds { get; set; }
    }
}
