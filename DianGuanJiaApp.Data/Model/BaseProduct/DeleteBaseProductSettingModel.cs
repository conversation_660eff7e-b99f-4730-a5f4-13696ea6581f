using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model.BaseProduct
{
    /// <summary>
    /// 基础商品删除设置模型
    /// </summary>
    public class DeleteBaseProductSettingModel
    {
        /// <summary>
        /// 重置简称(删除使用)
        /// </summary>
        public bool IsRestoreShortTitle { get; set; }
        /// <summary>
        /// 重置成本价(删除使用)
        /// </summary>
        public bool IsRestoreCostPrice { get; set; }

        /// <summary>
        /// 重置结算价(删除使用)
        /// </summary>
        public bool IsRestoreSettlePrice { get; set; }
        /// <summary>
        /// 重置分销价(删除使用)
        /// </summary>
        public bool IsRestoreDistributePrice { get; set; }

        /// <summary>
        /// 供货方式(删除使用)
        /// </summary>
        public bool IsRestoreSupplier { get; set; }
    }


    public class SettingModel
    {
        public string Key { get; set; }

        public bool Value { get; set; }
    }
}
