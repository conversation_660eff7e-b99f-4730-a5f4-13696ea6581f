using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model.BaseProduct
{
    /// <summary>
    /// 用于平台规格转换
    /// </summary>
    public class TranSourceSkuModel
    {
        /// <summary>
        /// 唯一ID，总长度19位，FxUserId占7位，不够补零
        /// 固定前缀2+FxUserId +11位自增值（不够补零）
        /// 自增值取自BaseUniqueIdCode.Id
        /// </summary>
        public long Uid { get; set; }
        /// <summary>
        /// 商品UID
        /// </summary>
        public long BaseProductUid { get; set; }

        /// <summary>
        /// 规格编码，由用户填写，用户维度唯一
        /// </summary>
        public string SkuCode { get; set; }

        /// <summary>
        /// SPU商品标题
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// 默认0，主图的对象ID，来源OssObject.Id
        /// 该字段为冗余字段，可避免关联表查询
        /// </summary>
        public long ImageObjectId { get; set; }

        /// <summary>
        /// 主图的URL，来源OssObject.Url
        /// 该字段为冗余字段，可避免关联表查询
        /// </summary>
        public string ImageUrl { get; set; }

        /// <summary>
        /// 分单用户ID
        /// </summary>
        public int FxUserId { get; set; }
        /// <summary>
        /// 供货类型
        /// 默认0，表示未设置
        /// -1表示自营
        /// 大于0，表示绑定了厂家
        /// 该字段待定，可能会使用表BaseProductSkuSupplierConfig代替
        /// </summary>
        public int SupplierFxUserId { get; set; }

        /// <summary>
        /// 成本价（采购价）
        /// 成本价在供应链中可能是指的上游厂家对该用户
        /// </summary>
        public decimal? CostPrice { get; set; }

        /// <summary>
        /// 默认结算价，对上游厂家设置生效
        /// </summary>
        public decimal? SettlePrice { get; set; }

        /// <summary>
        /// 默认分销价，对下游商家设置生效
        /// </summary>
        public decimal? DistributePrice { get; set; }

        /// <summary>
        /// 是否公开，默认0，不公开，公开后货盘可见
        /// </summary>
        public bool IsPublic { get; set; }

        /// <summary>
        /// 默认空字符串，JSON格式，格式如下
        /// 该字段为冗余字段-可避免关联表查询
        /// [{"k":"颜色","v":"白色"},{"k":"尺寸","v":"XL"},{ "k":"材质","v":"棉质"}]
        /// k：对应BaseProductSkuAttribute.AttributeName1/2/3
        /// v：对应BaseProductSkuAttribute.AttributeValue1/2/3
        /// </summary>
        public string Attributes { get; set; }
        
        /// <summary>
        /// 
        /// </summary>
        public SkuAttributeModel Attribute { get; set; }
        /// <summary>
        /// 根路径节点的用户ID
        /// </summary>
        public int? RootNodeFxUserId { get; set; }

        /// <summary>
        /// 来源SupplierProductSku.PathNodeDeep+ 1，默认0
        /// </summary>
        public int PathNodeDeep { get; set; }

        /// <summary>
        /// 来源SharePath.PathFlowCode，仅从厂家货盘复制过来会有数据
        /// </summary>
        public string SharePathCode { get; set; }
    }
    /// <summary>
    /// sku具体的值
    /// </summary>
    public class SkuAttributeModel
    {
        /// <summary>
        /// 图片的对应属性序号，可选值：0，1、2、3，0表示没有图片，1对应AttributeValue1，以此类推
        /// </summary>
        public int ImgAttributeValueNo { get; set; }

        /// <summary>
        /// 属性名称1
        /// </summary>
        public string AttributeName1 { get; set; }

        /// <summary>
        /// 属性名称2，属性1不为空值时，属性2才可能有值
        /// </summary>
        public string AttributeName2 { get; set; }

        /// <summary>
        /// 属性名称3，属性2不为空值时，属性3才可能有值
        /// </summary>
        public string AttributeName3 { get; set; }

        /// <summary>
        /// 属性值1
        /// </summary>
        public string AttributeValue1 { get; set; }

        /// <summary>
        /// 属性值2，属性1不为空值时，属性2才可能有值
        /// </summary>
        public string AttributeValue2 { get; set; }

        /// <summary>
        /// 属性值3，属性2不为空值时，属性3才可能有值
        /// </summary>
        public string AttributeValue3 { get; set; }

        /// <summary>
        /// 转换方法，将JSON转为SkuAttributeModel类型
        /// </summary>
        /// <param name="json"></param>
        /// <returns></returns>
        public static SkuAttributeModel FromJson(string json)
        {
            var attributeModel = new SkuAttributeModel();

            try
            {
                var attributes = JsonConvert.DeserializeObject<List<Dictionary<string, string>>>(json);

                if (attributes != null && attributes.Count > 0)
                {
                    var attribute1 = attributes.FirstOrDefault(a => a.ContainsKey("k") && a.ContainsKey("v"));
                    if (attribute1 != null)
                    {
                        attributeModel.AttributeName1 = attribute1["k"];
                        attributeModel.AttributeValue1 = attribute1["v"];
                    }

                    var attribute2 = attributes.Skip(1).FirstOrDefault(a => a.ContainsKey("k") && a.ContainsKey("v"));
                    if (attribute2 != null)
                    {
                        attributeModel.AttributeName2 = attribute2["k"];
                        attributeModel.AttributeValue2 = attribute2["v"];
                    }

                    var attribute3 = attributes.Skip(2).FirstOrDefault(a => a.ContainsKey("k") && a.ContainsKey("v"));
                    if (attribute3 != null)
                    {
                        attributeModel.AttributeName3 = attribute3["k"];
                        attributeModel.AttributeValue3 = attribute3["v"];
                    }
                }
            }
            catch (JsonException ex)
            {
                Utility.Log.WriteError($"平台商品Sku属性转换出错{ex.Message}");
            }
            return attributeModel;
        }

        /// <summary>
        /// 将类型转为Json字符串
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static string FromClass(SkuAttributeModel model)
        {
            var attributes = new List<Dictionary<string, string>>();
            if (model == null) return JsonConvert.SerializeObject(attributes);

            var attribute = new Dictionary<string, string>
            {
                { "k", model.AttributeName1 },
                { "v", model.AttributeValue1 }
            };
            attributes.Add(attribute);

            if (!string.IsNullOrEmpty(model.AttributeName2))
            {
                attribute = new Dictionary<string, string>
                {
                    { "k", model.AttributeName2 },
                    { "v", model.AttributeValue2 }
                };
                attributes.Add(attribute);
            }

            if (!string.IsNullOrEmpty(model.AttributeName3))
            {
                attribute = new Dictionary<string, string>
                {
                    { "k", model.AttributeName3 },
                    { "v", model.AttributeValue3 }
                };
                attributes.Add(attribute);
            }

            return JsonConvert.SerializeObject(attributes);
        }
    }
}
