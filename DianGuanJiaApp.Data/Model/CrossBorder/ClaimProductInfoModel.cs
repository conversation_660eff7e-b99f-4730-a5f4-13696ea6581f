using DianGuanJiaApp.Data.FxModel.SupplierProduct;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Utility;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model.CrossBorder
{
    /// <summary>
    /// 认领商品模型
    /// </summary>
    public class ClaimProductInfoModel
    {
        /// <summary>
        /// 明细ID
        /// </summary>
        public long ItemId { get; set; }

        /// <summary>
        /// 采集明细ID
        /// </summary>
        public long CollectDetailId { get; set; }

        /// <summary>
        /// 认领UID
        /// </summary>
        public string ClaimUId { get; set; }

        /// <summary>
        /// 店铺ID
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 店铺名称
        /// </summary>
        public string ShopName { get; set; }

        /// <summary>
        /// 认领平台
        /// </summary>
        public string PlatfromType { get; set; }


        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 类目ID
        /// </summary>
        public string CategoryId { get; set; }

        /// <summary>
        /// 品牌Id
        /// </summary>
        public string BrandId { get; set; }

        /// <summary>
        /// 货源ID
        /// </summary>
        public string SourceID { get; set; }


        /// <summary>
        /// 来源
        /// </summary>
        public string Source { get; set; }


        /// <summary>
        /// 来源站点
        /// </summary>
        public string SourceSite { get; set; }
        /// <summary>
        /// 来源币种
        /// </summary>
        public string SourceCurrency { get; set; }

        /// <summary>
        /// 来源语种
        /// </summary>
        public string SourceLanguage { get; set; }

        #region 扩展


        /// <summary>
        /// 商品详细说明
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 平台属性信息
        /// </summary>
        public string CategoryAttribute { get; set; }


        /// <summary>
        /// 产品属性
        /// </summary>
        public List<CrossProductAttribute> ProductAttributes { get; set; }


        /// <summary>
        /// 类目集合
        /// </summary>
        public List<CategoryInfo> CategoryInfoList { get; set; }

        /// <summary>
        /// 图片集合
        /// </summary>
        public List<string> ProductImages { get; set; }


        /// <summary>
        /// 尺寸图表
        /// </summary>
        public string SizeImageUrl { get; set; }

        /// <summary>
        /// 商品资质图
        /// </summary>
        public List<string> CertificationUrls { get; set; }


        /// <summary>
        /// 商品视频链接
        /// </summary>
        public ClaimProductVideo Video { get; set; }

        /// <summary>
        /// 平台规格属性类型（Sku规格的说明）
        /// </summary>
        public List<CollectedAttributeModel> AttributeTypes { get; set; }


        /// <summary>
        /// Sku
        /// </summary>
        public List<CollectProductInfoSkuModel> ProductInfoSkus { get; set; }

        /// <summary>
        /// 重量
        /// </summary>
        public decimal? Weight { get; set; }

        /// <summary>
        /// 重量单位
        /// </summary>
        public string WeightUnit { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PackageLength { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PackageWidth { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PackageHeight { get; set; }

        #endregion

        /// <summary>
        /// 分销用户ID
        /// </summary>

        public int FxUserId { get; set; }

        /// <summary>
        /// 产品来源属性
        /// </summary>
        public string SourceAttrs { get; set; }

        /// <summary>
        /// 0:查询 1：保存 2：保存并更新其他店铺 3：保存并发布
        /// </summary>
        public int Operationtype { get; set; }

        /// <summary>
        /// 目标语言
        /// </summary>
        public string TargetLanguage { get; set; }

    }





    /// <summary>
    /// 重量
    /// </summary>
    public class PackageWeight
    {
        ///<summary>
        /// 订单号
        ///</summary>

        public decimal? Weight;

        ///<summary>
        /// 包裹重量(单位:kg)
        ///</summary>

        public string WeightUnit;
    }

    /// <summary>
    /// 
    /// </summary>
    public class ClaimProductVideo
    {
        /// <summary>
        /// TK 视频ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// TK 返回的URL
        /// </summary>
        public string Url { get; set; }
    }

    /// <summary>
    /// 物流尺寸
    /// </summary>
    public class PackageDimensions
    {
        /// <summary>
        /// 
        /// </summary>
        public string PackageLength { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PackageWidth { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string PackageHeight { get; set; }

    }
}
