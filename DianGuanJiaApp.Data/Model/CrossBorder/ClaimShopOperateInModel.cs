using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model.CrossBorder
{
    /// <summary>
    /// 商品箱操作入参
    /// </summary>
    public class ClaimShopOperateInModel
    {

        /// <summary>
        /// 单个认领关系ID
        /// </summary>
        public string ClaimRelationUid { get; set; }
        /// <summary>
        /// 多个认领关系ID
        /// </summary>
        public List<string> ClaimRelationUids { get; set; }

        /// <summary>
        ///是否标记 
        /// </summary>
        public int IsMarkers { get; set; }

        /// <summary>
        /// 商品标题
        /// </summary>
        public string Title { get; set; }
    }
}
