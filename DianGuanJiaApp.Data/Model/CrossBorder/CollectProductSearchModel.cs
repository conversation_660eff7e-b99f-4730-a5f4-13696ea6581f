using System;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model
{
    /// <summary>
    /// 采集商品查询模型
    /// </summary>
    public class CollectProductSearchModel
    {

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public CollectProductSearchModel()
        {
            PageIndex = 1;
            PageSize = 10;
        }

        /// <summary>
        /// 商品Uid
        /// </summary>
        public string ProductUids { get; set; }

        /// <summary>
        /// 查询类型
        /// </summary>
        public int SearchType { get; set; }


        /// <summary>
        /// 查询内容
        /// </summary>
        public string SearchContent { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        public string Source { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public string StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public string EndTiem { get; set; }


        /// <summary>
        /// 用户ID
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 查询字段
        /// </summary>
        public List<string> Fields { get; set; }

        /// <summary>
        /// 认领状态
        /// </summary>
        public int ClaimStatus {  get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; }
    }



    /// <summary>
    /// 商品认领模型
    /// </summary>
    public class CollectClaimInModel
    {
        public List<string> productUids { get; set; }

        /// <summary>
        /// 认领平台
        /// </summary>
        public string claimPlatfrom { get; set; }

    }

    /// <summary>
    /// 商品采集模型
    /// </summary>
    public class CollectInModel
    {

        /// <summary>
        /// 0：链接：1：base64
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Link {  get; set; }

        /// <summary>
        /// 采集内容
        /// </summary>
        public string Coent { get; set; }

    }

}
