using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model.CrossBorder
{
    public class GlobalProductSkuModel
    {
        // 主表的UniqueCode
        public string GpUniqueCode { get; set; }

        // skuID
        public string SkuId { get; set; }

        // 平台产品ID
        public string PlatformId { get; set; }

        //sku的全部库存数量
        public int GlobalQuantity { get; set; }

        // 图片URL
        public string ImagesUrl { get; set; }

        // 用于管理sku的内部代码/名称，对买家不可见。最大长度：50个字符
        public string SellerSku { get; set; }

        // SKU单位数量
        public string SkuUnitCount { get; set; }

        // 标识代码相关 identifier_code": { "code": "10000000000010", "type": "GTIN" }
        public string Identifiercode { get; set; }

        // 价格金额
        public decimal PriceAmount { get; set; }

        // 价格货币
        public string PriceCurrency { get; set; }

        // 库存信息
        public string Inventory { get; set; }

        // 销售属性
        public string SalesAttributes { get; set; }

        // 记录创建时间
        public DateTime CreateTime { get; set; }

        // 记录更新时间
        public DateTime? UpdateTime { get; set; }
    }
}
