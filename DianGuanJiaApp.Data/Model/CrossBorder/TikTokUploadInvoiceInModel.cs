using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model
{

    /// <summary>
    /// 巴西
    /// </summary>
    public class TikTokUploadInvoiceInModel
    {
        /// <summary>
        /// 包裹ID
        /// </summary>
        public string PackageId { get; set; }

        /// <summary>
        /// 平台订单ID
        /// </summary>

        public List<string> LogicOrderIds { get; set; }

        /// <summary>
        /// 平台类型
        /// </summary>

        public string FileType { get; set; }

        /// <summary>
        /// 文件内容base64字符
        /// </summary>
        public string FileContent { get; set; }
    }
}
