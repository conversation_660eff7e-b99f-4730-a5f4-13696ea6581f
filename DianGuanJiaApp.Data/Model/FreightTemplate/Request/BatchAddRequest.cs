using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Data.Model.FreightTemplate
{
    public class AddRequest : BaseFreightTemplateRequest<AddOrEditResponse>
    {
        public List<ExpShippingFeeTemplate> Templates { get; set; }

        public override void Check()
        {
            base.Check();
            //请求数据检查
            if (Templates.IsNullOrEmptyList())
            {
                throw new ArgumentException("数据不能为空");
            }
            if (Templates.Any(t => string.IsNullOrEmpty(t.TemplateType) || string.IsNullOrEmpty(t.ExpressCompanyName) || string.IsNullOrEmpty(t.TemplateRule)))
            {
                throw new ArgumentException("数据格式异常");
            }
            if (Templates.Any(t => t.ExpressCompanyId == 0 || t.CreateBy == 0 || t.FxUserId == 0))
            {
                throw new ArgumentException("数据格式异常");
            }
        }

        /// <summary>
        /// 接口名称
        /// </summary>
        /// <returns></returns>
        public override string GetApiName()
        {
            return "template.batch.add";
        }
    }

    public class EditRequest : BaseFreightTemplateRequest<AddOrEditResponse>
    {
        public List<ExpShippingFeeTemplate> Templates { get; set; }

        public override void Check()
        {
            base.Check();
            //请求数据检查
            if (Templates.IsNullOrEmptyList())
            {
                throw new ArgumentException("数据不能为空");
            }
            if (Templates.Any(t => string.IsNullOrEmpty(t.TemplateType) || string.IsNullOrEmpty(t.TemplateRule)))
            {
                throw new ArgumentException("数据格式异常");
            }
        }

        /// <summary>
        /// 接口名称
        /// </summary>
        /// <returns></returns>
        public override string GetApiName()
        {
            return "template.batch.edit";
        }
    }
}