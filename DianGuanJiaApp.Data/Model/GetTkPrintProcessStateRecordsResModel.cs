using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model
{
    public class GetTkPrintProcessStateRecordsResModel
    {

        //唯一识别码
        public string UniqueCode { get; set; }

        //打印批次号
        public string BatchNo { get; set; }

        //店铺名称
        public string ShopName { get; set; }

        //店铺站点
        public string SubPlatformType { get; set; }

        //逻辑订单Id
        public string LogicOrderId { get; set; }

        //平台订单Id
        public string PlatformOrderId { get; set; }

        //物流公司名称
        public string ExpressName { get; set; }

        //快递单号
        public string ExpressNo { get; set; }

        //发货状态
        public string SendState { get; set; }

        //平台类型
        public string PlatformType { get; set; }

        //打印执行状态
        public string State { get; set; }

        //打印错误原因
        public string ErrorMessage { get; set; }

        //创建时间
        public DateTime? CreateTime { get; set; }

        //修改时间
        public DateTime? UpdateTime { get; set; }
    }
}
