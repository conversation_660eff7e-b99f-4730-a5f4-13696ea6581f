using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model
{    
    /// <summary>
    /// 京东 消息推送模型
    /// </summary>
    public class JingDongMessageModel
    {
        /// <summary>
        /// 订单号
        /// </summary>
        public string orderId { get; set; }
        /// <summary>
        /// 商家编号
        /// </summary>
        public string venderId { get; set; }
        /// <summary>
        /// 订单状态
        /// </summary>
        public string orderStatus { get; set; }
        /// <summary>
        /// 订单创建时间
        /// </summary>
        public string orderCreateTime { get; set; }
        /// <summary>
        /// 订单类型
        /// </summary>
        public string orderType { get; set; }
        /// <summary>
        /// 订单支付方式
        /// </summary>
        public string orderPaymentType { get; set; }
    }

    /// <summary>
    /// 拼多多代打 消息推送模型
    /// </summary>
    public class PddFDSMessageModel
    {
        /// <summary>
        /// 厂家店铺id
        /// </summary>
        public string mall_id { get; set; }
        /// <summary>
        /// 代打订单号
        /// </summary>
        public string order_mask_sn { get; set; }
        /// <summary>
        /// 代打店铺id
        /// </summary>
        public string mall_mask_id { get; set; }
        /// <summary>
        /// 回传状态 0:取消回传 1:回传【pdd_fds_OrderStatusReturn】
        /// </summary>
        public string return_status { get; set; }
        /// <summary>
        /// 卖家备注【pdd_fds_Remark】
        /// </summary>
        public string remark { get; set; }
    }

}
