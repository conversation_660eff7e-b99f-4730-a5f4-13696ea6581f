using DianGuanJiaApp.Data.Model.OpenTelemetry;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model
{
	/// <summary>
	/// p_CommonSetting.OpenTelemetryConfig
	/// </summary>
	public class OpenTelemetryConfigModel
	{
		/// <summary>
		/// 应用名
		/// </summary>
		public string ServiceName { get; set; }

		/// <summary>
		/// 版本号
		/// </summary>
		public string ServiceVersion { get; set; }

		/// <summary>
		/// 授权链接
		/// </summary>
		public string AuthUrl { get; set; }

		/// <summary>
		/// 监听http api 请求指定耗时（毫秒） 0是不指定
		/// </summary>
		public string AspNetDuration { get; set; } = "0";

		/// <summary>
		/// 监听SqlServer请求指定耗时（毫秒）  0是不指定 ,建议耗时小于等于AspNetDuration，否则链路数据有可能不完整
		/// </summary>
		public string SqlServerDuration { get; set; } = "0";

		/// <summary>
		/// 监听http Client 请求指定耗时（毫秒） 0是不指定,建议耗时小于等于AspNetDuration，否则链路数据有可能不完整
		/// </summary>
		public string HttpClientDuration { get; set; } = "0";

		/// <summary>
		/// 是否上报因耗时过滤掉的链路数据
		/// 背景：AspNetDuration=5000，SqlServerDuration=2000。api链路耗时超过5000毫秒会上报，但是SqlServerDuration的耗时没超过2000毫秒不会上报。导致上报的api链路会缺少Sql Server的链路。此配置为true会上报缺失的Sql Server链路数据。
		/// </summary>
		public bool IsOtlpExporterFilterData { get; set; }

		/// <summary>
		/// 是否开启手动埋点
		/// </summary>
		public bool IsOpenHand { get; set; } = true;

		/// <summary>
		/// clickhouse连接字符串，例：Host=localhost;Port=8123;Database=otel;User=default;Password=*****;Connect Timeout=30;Command Timeout=60。与es数据库共用，多个以逗号隔开
		/// </summary>
		public string ClickHouseConnentionString = "";

		/// <summary>
		/// es数据库账号,没有就不填
		/// </summary>
		public string EsConnentionUserName { get; set; }

		/// <summary>
		/// es数据库密码,没有就不填
		/// </summary>
		public string EsConnentionPassword { get; set; }

		/// <summary>
		/// 是否开启监听aspnet api
		/// </summary>
		public bool IsOpenAspNet { get; set; }

		/// <summary>
		/// 是否开启监听SqlServer
		/// </summary>
		public bool IsOpenSqlServer { get; set; }

		/// <summary>
		/// 是否开启监听http
		/// </summary>
		public bool IsOpenHttp { get; set; }

		/// <summary>
		/// 是否开启Signoz,过渡字段，全平台发布Signoz后去掉
		/// </summary>
		public bool IsOpenSignoz { get; set; }

		/// <summary>
		/// 链路日志配置
		/// </summary>
		public OpenTelemetryLogConfigModel LogConfig { get; set; }

		/// <summary>
		/// 前端配置
		/// </summary>
		public OpenTelemetryWebConfigModel WebConfig { get; set; }
	}
}
