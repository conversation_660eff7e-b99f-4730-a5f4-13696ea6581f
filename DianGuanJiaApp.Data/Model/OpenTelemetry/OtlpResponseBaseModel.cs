using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Helpers;
using DianGuanJiaApp.Utility.Helpers.Enum;
using Nest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model.OpenTelemetry
{
	public class OtlpResponseBaseModel: OtlpLogView
	{
		/// <summary>
		/// 应用名
		/// </summary>
		public string ServiceName { get; set; }

		/// <summary>
		/// 环境版本
		/// </summary>
		public string Version { get; set; }

		/// <summary>
		/// 日志类型
		/// </summary>
		public OpenTelemetryLogTypeEnum LogTypeEnum { get; set; }
	}
}
