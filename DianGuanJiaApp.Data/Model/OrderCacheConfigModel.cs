using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model
{
    public class OrderCacheConfigModel
    {
        /// <summary>
        /// 要缓存的平台状态
        /// </summary>
        public List<string> PlatformStatus { get; set; }
        /// <summary>
        /// 订单数据起始时间,为空时表示查询所有的数据
        /// </summary>
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// 订单数据结束时间,为空时不生效
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// 筛选的时间类型：PayTime、CreateTime
        /// </summary>
        public string TimeType { get; set; }
    }
}
