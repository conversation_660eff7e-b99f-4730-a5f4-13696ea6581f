namespace DianGuanJiaApp.Data.Model
{
    public class OrderRequestSimpleModel
    {
        public string LogicOrderId { get; set; }
        public int ShopId { get; set; }
        public int FxUserId { get; set; }
        public string PlatformType { get; set; }
        public string PlatformOrderId { get; set; }
        public string CustomerOrderId { get; set; }
        public string OrderCode { get; set; }
        public string PathFlowCode { get; set; }
        /// <summary>
        /// 快递单号：当模板类型为1（传统模板）时必填
        /// 发货时：此单号必填
        /// </summary>
        public string WaybillCode { get; set; }

        public int DataFlag { get; set; }
    }
}