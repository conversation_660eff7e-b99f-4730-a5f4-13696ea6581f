using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.FxModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model
{
    /// <summary>
    /// 分页数据
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class PagedResultModel<T> where T : new()
    {
        public PagedResultModel()
        {
            Rows = new List<T>();
            PageSize = 10;
            Setting = new SystemSetting();
        }
        public int PageIndex
        {
            get; set;
        }
        public int PageSize
        {
            get; set;
        }
        /// <summary>
        /// 排序字段名称
        /// </summary>
        public string OrderByField
        {
            get; set;
        }
        /// <summary>
        /// 是否降序排列
        /// </summary>
        public bool IsOrderDesc
        {
            get; set;
        }
        public int PageCount
        {
            get; set;
        }
        public int Total
        {
            get; set;
        }
        public int RealOrderTotal
        {
            get; set;
        }
        public int PtOrderTotal
        {
            get; set;
        }

        /// <summary>
        /// 退款数量
        /// </summary>
        public int RefundTotal
        {
            get; set;
        }

        /// <summary>
        /// 头条退货退款数量
        /// </summary>
        public int OtherRefundTotal
        {
            get; set;
        }

        public List<T> Rows
        {
            get; set;
        }

        public List<SimpleProduct> ProductAtrs
        {
            get; set;
        }
        public SystemSetting Setting { get; set; }

        public string QueryCondition { get; set; }

        public string LogId { get; set; }
        /// <summary>
        /// 是否在同步中
        /// </summary>
        public bool IsSyncing { get; set; }

        /// <summary>
        /// 是否设置自动合并
        /// </summary>
        public bool IsFxShopAutoMergerOpened { get; set; }

        /// <summary>
        /// 厂家商品数据，仅用于1688分销：换绑关联
        /// </summary>
        public List<ProductFx> SupplierProducts
        {
            get; set;
        }

        /// <summary>
        /// 冷数据为：2
        /// </summary>
        public int dataFlag { get; set; }

        /// <summary>
        /// 全局搜索的云平台
        /// </summary>
        public string GlobalSearchCloudPlatform { get; set; }

        /// <summary>
        /// 全局搜索的库
        /// </summary>
        public string GlobalSearchDbName { get; set; }
        
        /// <summary>
        /// 订单显示设置 ，json
        /// </summary>
        public string OrderDisplaySettingStr { get; set; }
        
        /// <summary>
        /// 是否使用了新查询逻辑
        /// </summary>
        public bool UseNewQuery { get; set; }

        /// <summary>
        /// 备注设置
        /// </summary>
        public RemarkSettingModel RemarkSetting { get; set; }
        
        /// <summary>
        /// 店铺信息，商家选择器有用到
        /// </summary>
        public List<SimpleShopInfo> ShopAtrs
        {
            get; set;
        }

        public string QuerySecond { get; set; }
    }

    public class PagedResultModel2<T, T2>
    {
        public PagedResultModel2()
        {
            Rows = new List<T>();
            PageSize = 10;
        }

        public int PageIndex
        {
            get; set;
        }
        public int PageSize
        {
            get; set;
        }
        public int Total
        {
            get; set;
        }

        public List<T> Rows
        {
            get; set;
        }
        public T2 TotalData { get; set; }
    }
}
