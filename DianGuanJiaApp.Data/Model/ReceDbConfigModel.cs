using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Data.Model
{
    public class ReceDbConfigModel
    {
        public ReceDbServerConfig ReceDbServer { get; set; }
        public ReceDbNameConfig ReceDbNameConfig { get; set; }
        public ReceDbConfig ReceDbConfig { get; set; }

        public string ConnectionString
        {
            get
            {
                var conn = ReceDbServer?.ConnectionString;
                if (CustomerConfig.IsLocalDbDebug && !string.IsNullOrEmpty(ReceDbServer?.LocalConnectionString))
                    conn = ReceDbServer?.LocalConnectionString;
                if (!string.IsNullOrEmpty(conn) && !string.IsNullOrEmpty(ReceDbNameConfig?.DbName))
                    conn = conn.Replace("$database$", ReceDbNameConfig.DbName);
                else
                    return "";
                //Log.Debug($"Rece数据库连接：{conn}");
                return conn;
            }
        }

        /// <summary>
        /// 是否使用MySQL
        /// </summary>
        public bool IsMySQL
        {
            get
            {
                var isMySQL = ReceDbServer?.IsMySQL ?? false;
                return isMySQL;
            }
        }

        /// <summary>
        /// 标识，若是同一数据库、同一表该标识一样
        /// </summary>
        public string Identity
        {
            get
            {
                if (ReceDbNameConfig == null)
                    return "";
                return ReceDbNameConfig.DbServerConfigId + ReceDbNameConfig.DbName;
            }
        }

        /// <summary>
        /// 是否新库
        /// </summary>
        public bool IsNewDb { get; set; }
    }

    public class DbNameConfigIdShopId
    {
        public int DbNameConfigId { get; set; }
        public int ShopId { get; set; }
    }
}
