using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model
{
    public class SaveOfferModule
    {
        public bool isCheck { get; set; }

        public List<SaveOfferProduct> ProductList { get; set; }

        public int ShopId { get; set; }

        public bool SyncWeightToOrder { get; set; }
    }


    public class SaveOfferProduct {
        public string productId { get; set; }
        
        public string names { get; set; }

        public string weight { get; set; }
        /// <summary>
        /// 重量是否已经修改
        /// </summary>
        public bool isWeightChanged { get; set; }


        public string shiChang { get; set; }

        public string dangKou { get; set; }

        public string chengBenJia { get; set; }
        public int ShopId { get; set; }
        public string PlatformId { get; set; }
        public List<SaveOfferProductSkuAttribute> SkuAttributeList { get; set; }

    }



    public class SaveOfferProductSkuAttribute
    {
        public string attributeId { get; set; }

        public string ShortTitle { get; set; }
        public string weight { get; set; }
        public string Bazaar { get; set; }
        public string Stall { get; set; }
        public string CostPrice { get; set; }
        /// <summary>
        /// 重量是否已经修改
        /// </summary>
        public bool isWeightChanged { get; set; }
        public string PlatformId { get; set; }
        public string SkuId { get; set; }
    }



}
