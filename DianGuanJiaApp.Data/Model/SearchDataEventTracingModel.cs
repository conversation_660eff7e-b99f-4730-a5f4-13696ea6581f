using DianGuanJiaApp.Utility.Model.AliLog;

namespace DianGuanJiaApp.Data.Model
{
    public class SearchDataEventTracingModel : IAliPutLogs
    {
        /// <summary>
        /// 当前用户ID
        /// </summary>
        public int CurrentFxUserId { get; set; }

        /// <summary>
        /// 主机名称
        /// </summary>
        public string HostName { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public string TrackingType { get; set; }

        /// <summary>
        /// 云平台类型
        /// </summary>
        public string CloudPlatformType { get; set; }
        /// <summary>
        /// 耗时
        /// </summary>
        public double Durations { get; set; }
        /// <summary>
        /// 查询条件
        /// </summary>
        public string SearchCondition { get; set; }

        public int RealOrderTotal { get; set; }
        public int PlatformOrderTotal { get; set; }
        public int Total { get; set; }
        public int Pages { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public string Sql { get; set; }
        public string RequestIp { get; set; }
        public string TraceId { get; set; }
        public string Token { get; set; }
        public string DbName { get; set; }
    }
}