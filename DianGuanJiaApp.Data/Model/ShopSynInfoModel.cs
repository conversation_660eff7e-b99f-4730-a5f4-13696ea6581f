using DianGuanJiaApp.Data.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DianGuanJiaApp.Data.Model
{
    public class ShopSynInfoModel
    {
        public ShopSynInfoModel()
        {
            Steps = new List<ShopSynInfoStepModel>();
        }
        public bool IsFinished { get; set; }
        public int TotalStep { get; set; }
        public int CurStep { get { return Steps.Count(); } }

        public int Percent
        {
            get
            {
                if (IsFinished)
                    return 100;
                if (TotalStep == 0 || TotalStep == 0)
                    return 0;
                else
                {
                    var percent = CurStep / (TotalStep * 1.0);
                    var p = (int)Math.Ceiling(percent * 100);
                    if (p > 100)
                        return 100;
                    return p;
                }
            }
        }

        public List<ShopSynInfoStepModel> Steps { get; set; }
        public int ShopId { get; set; }
    }

    public class ShopSynInfoStepModel
    {
        public int Index { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string Status { get; set; }
    }
    /// <summary>
    /// 检查店铺P_SyncStatus,P_SyncTask是否存在
    /// </summary>
    public class CheckShopSyncInfoModel
    {
        /// <summary>
        /// 
        /// </summary>
        public int FxUserId { get; set; }
        /// <summary>
        /// 店铺Id列表
        /// </summary>
        public List<int> ShopIds { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<int> AgentIds { get; set; }
        /// <summary>
        /// 平台类型
        /// </summary>
        public List<string> InPlatfromTypes { get; set; }
        /// <summary>
        /// 任务类型
        /// </summary>
        public int TaskType { get; set; }
    }

    public class CheckShopSyncInfoResult
    {
        public int FxUserId { get; set; }
        public int ShopId { get; set; }
        public string SyncType { get; set; }
    }

    public class ShopSyncStatus
    {
        public SyncStatus SyncStatus { get; set; }


        public Shop Shop { get; set; }
    }
}