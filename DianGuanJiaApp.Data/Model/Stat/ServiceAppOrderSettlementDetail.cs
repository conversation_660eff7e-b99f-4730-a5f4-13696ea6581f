using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace DianGuanJiaApp.Data.Model
{
    /// <summary>
    /// 处理订购数据的条件
    /// </summary>
    public class QueryServiceAppOrderSettlementDetailModel
    {
        /// <summary>
        /// 任务Code
        /// </summary>
        public string TaskCode { get; set; }
        /// <summary>
        /// 来源：ServiceAppOrder、AppOrderList、PlatformAppOrderRecord
        /// </summary>
        public string FromType { get; set; }
        /// <summary>
        /// 开始Id（不含此值）
        /// </summary>
        public int StartId { get; set; }

        /// <summary>
        /// 结束Id（含此值）
        /// </summary>
        public int EndId { get; set; }

        /// <summary>
        /// 每批次数量
        /// </summary>
        public int BatchSize { get; set; }
    }

    /// <summary>
    /// 平台结算规则
    /// </summary>
    public class PtSettlementRule
    {
        /// <summary>
        /// 平台类型
        /// </summary>
        public string PlatformType { get; set; }
        /// <summary>
        /// 平台手续费费率
        /// </summary>
        public decimal ServiceRate { get; set; }
        /// <summary>
        /// 分账模式：0:分月结算；1:一次性结算
        /// </summary>
        public int SettlementMode { get; set; }
    }

    /// <summary>
    /// 统计账单结果模型
    /// </summary>
    public class StatSettlementResult
    {
        /// <summary>
        /// 【计算列】唯一Code=(StatFlag + "X" +StatValue + "X" +SettlementCycle).ToShortMd5()
        /// </summary>
        public string UniqueCode
        {
            get
            {
                return (StatFlag + "X" + StatValue + "X" + SettlementCycle.ToString("yyyyMMddHHmmss")).ToShortMd5();
            }
        }
        /// <summary>
        /// 账期
        /// </summary>
        public DateTime SettlementCycle { get; set; }

        /// <summary>
        /// 统计类型：所有:all、分平台:platform、分应用:app
        /// </summary>
        public string StatFlag { get; set; }
        /// <summary>
        /// 统计值：所属平台\所属应用AppKey\all
        /// </summary>
        public string StatValue { get; set; }
        /// <summary>
        /// 数量统计结果1
        /// </summary>
        public int Count1 { get; set; }
        /// <summary>
        /// 数量统计结果2
        /// </summary>
        public int Count2 { get; set; }
        /// <summary>
        /// 数量统计结果3
        /// </summary>
        public int Count3 { get; set; }
        /// <summary>
        /// 金额统计结果1
        /// </summary>
        public int SumAmout1 { get; set; }
        /// <summary>
        /// 金额统计结果2
        /// </summary>
        public int SumAmout2 { get; set; }
        /// <summary>
        /// 金额统计结果3
        /// </summary>
        public int SumAmout3 { get; set; }
    }
}