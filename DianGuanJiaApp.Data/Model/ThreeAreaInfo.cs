using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Model
{
    public class ThreeAreaInfo
    {
        private string _province = "";
        /// <summary>
        /// 省
        /// </summary>
        public string Province
        {
            set { _province = value; }
            get { return _province; }
        }

        private string _city = "";
        /// <summary>
        /// 市
        /// </summary>
        public string City
        {
            set { _city = value; }
            get { return _city; }
        }

        private string _area = "";
        /// <summary>
        /// 区
        /// </summary>
        public string Area
        {
            set { _area = value; }
            get { return _area; }
        }

        private string _subdistrict = "";
        /// <summary>
        /// 详细地址
        /// </summary>
        public string Subdistrict
        {
            set { _subdistrict = value; }
            get { return _subdistrict; }
        }
    }


    public class HighSearchThreeAreaInfo
    {
        public SimpleAreaCodeModel Province { get; set; }
        public SimpleAreaCodeModel City { get; set; }
        public List<SimpleAreaCodeModel> Areas { get; set; }
    }

    public class SimpleAreaCodeModel
    {
        public int Id { get; set; }
        public int ParentId { get; set; }
        public string Name { get; set; }
    }


    public class ThreeAreaFisrtModel 
    {
        public AreaBaseModel ProvinceInfo { get; set; }
        public List<ThreeAreaSecondModel> CityLst { get; set; }
    }

    public class ThreeAreaSecondModel 
    {
        public AreaBaseModel CityInfo { get; set; }
        public List<AreaBaseModel> AreaLst { get; set; }
    }

    public class AreaBaseModel
    {
        public int Id { get; set; }
        public int ParentId { get; set; }
        public string Name { get; set; }
        public bool Checked { get; set; }
        public bool isCheckAll { get; set; }
        public bool Disabled { get; set; }
        public int Num { get; set; }
    }


}
