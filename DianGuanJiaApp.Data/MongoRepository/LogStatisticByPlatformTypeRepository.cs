using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.MongoRepository
{
    public class LogStatisticByPlatformTypeRepository : BaseMongoRepository<LogForStatisticByPlatformType>
    {
        LogOperatorStatisticRepository _logStatisticRepository = new LogOperatorStatisticRepository();
        public LogStatisticByPlatformTypeRepository() : base("Log")
        {

        }


        public List<LogForStatisticByPlatformType> GetLastStatisticList(LogStatisticSearchModel model)
        {
            Expression<Func<LogForStatisticByPlatformType, bool>> whereFunc = null;
            if (!model.PlatformType.IsNullOrEmpty())
                whereFunc = x => x.PlatformType == model.PlatformType;
            if (!model.StartTime.IsNullOrEmpty() && !model.EndTime.IsNullOrEmpty())
                whereFunc = whereFunc != null ? whereFunc.And(x => x.StartTime >= BsonUtils.ToUniversalTime(model.StartTime.toDateTime()) && x.StartTime <= BsonUtils.ToUniversalTime(model.EndTime.toDateTime())) : x => x.StartTime >= BsonUtils.ToUniversalTime(model.StartTime.toDateTime()) && x.StartTime <= BsonUtils.ToUniversalTime(model.EndTime.toDateTime());
            if (!model.Operator.IsNullOrEmpty())
                whereFunc = whereFunc != null ? whereFunc.And(x => x.OperatorType == model.Operator) : x => x.OperatorType == model.Operator;

            var statisticCollection = this.Collection;
            var sort = Builders<LogForStatisticByPlatformType>.Sort.Descending(x => x.StartTime);
            // 无查询条件时默认查7天的统计数据
            var filter = whereFunc == null ? Builders<LogForStatisticByPlatformType>.Filter.Where(x => x.StartTime >= BsonUtils.ToUniversalTime(DateTime.Now.AddDays(-7))) : Builders<LogForStatisticByPlatformType>.Filter.Where(whereFunc);
            var bsons = statisticCollection.Find(filter)
                        .Sort(sort)
                        .As<BsonDocument>().ToList();

            var logList = bsons?.Select(s => new LogForStatisticByPlatformType
            {
                _Id = s["_id"].ToString2(),
                ObjId = s["_id"].ToString2(),
                PlatformType = s["PlatformType"] == BsonNull.Value ? "" : s["PlatformType"].ToString2(),
                OperatorType = s["OperatorType"] == BsonNull.Value ? "" : s["OperatorType"].ToString2(),
                PartFalidCount = s["PartFalidCount"] == BsonNull.Value ? 0 : s["PartFalidCount"].ToInt(),
                AllFalidCount = s["AllFalidCount"] == BsonNull.Value ? 0 : s["AllFalidCount"].ToInt(),
                ShopCount = s["ShopCount"] == BsonNull.Value ? 0 : s["ShopCount"].ToInt(),
                TotalShopCount = s["TotalShopCount"] == BsonNull.Value ? 0 : s["TotalShopCount"].ToInt(),
                SuccessCount = s["SuccessCount"] == BsonNull.Value ? 0 : s["SuccessCount"].ToInt(),
                TotalCount = s["TotalCount"] == BsonNull.Value ? 0 : s["TotalCount"].ToInt(),

                TotalOperatorCount = s["TotalOperatorCount"] == BsonNull.Value ? 0 : s["TotalOperatorCount"].ToInt(),
                SuccessOperatorCount = s["SuccessOperatorCount"] == BsonNull.Value ? 0 : s["SuccessOperatorCount"].ToInt(),
                PartFalidOperatorCount = s["PartFalidOperatorCount"] == BsonNull.Value ? 0 : s["PartFalidOperatorCount"].ToInt(),
                AllFalidOperatorCount = s["AllFalidOperatorCount"] == BsonNull.Value ? 0 : s["AllFalidOperatorCount"].ToInt(),

                CreateTime = s["CreateTime"] == BsonNull.Value ? DateTime.Now : s["CreateTime"].toDateTime().ToLocalTime(),
                StartTime = s["StartTime"] == BsonNull.Value ? DateTime.Now : s["StartTime"].toDateTime().ToLocalTime(),
                EndTime = s["EndTime"] == BsonNull.Value ? DateTime.Now : s["EndTime"].toDateTime().ToLocalTime(),
                UpdateTime = s["UpdateTime"] == BsonNull.Value ? DateTime.Now : s["UpdateTime"].toDateTime().ToLocalTime(),
                IsCompleted = s["IsCompleted"] == BsonNull.Value ? false : s["IsCompleted"].ToBool(),
            }).ToList();

            var pts = logList.Select(x => x.PlatformType).Distinct().ToList();
            var opts = logList.Select(x => x.OperatorType).Distinct().ToList();
            //var sfilter = Builders<LogOperatorStatistic>.Filter.Where(x => pts.Contains(x.PlatformType) && opts.Contains(x.OperatorType) && x.StartTime == BsonUtils.ToUniversalTime(model.StartTime.toDateTime()));
            //var sLogList = _logStatisticRepository.Find(sfilter)?.ToList();
            var sLogList = _logStatisticRepository.GetPlatformTypeOptStatisticList(pts,opts,true, model.StartTime.toDateTime())?.ToList();
            logList.ForEach(m =>
            {
                var sLog = sLogList.Where(x => x.PlatformType == m.PlatformType && x.OperatorType == m.OperatorType).FirstOrDefault();
                m.PartFalidRate = decimal.Round((m.PartFalidCount * 100.0 / m.TotalCount).ToDecimal(), 2);
                m.AllFalidRate = decimal.Round((m.AllFalidCount * 100.0 / m.TotalCount).ToDecimal(), 2);
                m.ShopRate = decimal.Round((m.ShopCount * 100.0 / m.TotalShopCount).ToDecimal(), 2);
                if (sLog!=null)
                {
                    m.TimeOutCount = sLog.TimeOutCount;
                    m.TimeOutCountRate = sLog.TimeOutCountRate;
                    m.SecondCount_1 = sLog.SecondCount_1;
                    m.SecondCount_1_Rate = sLog.SecondCount_1_Rate;
                    m.SecondCount_2 = sLog.SecondCount_2;
                    m.SecondCount_2_Rate = sLog.SecondCount_2_Rate;
                    m.SecondCount_3 = sLog.SecondCount_3;
                    m.SecondCount_3_Rate = sLog.SecondCount_3_Rate;
                    m.SecondCount_4 = sLog.SecondCount_4;
                    m.SecondCount_4_Rate = sLog.SecondCount_4_Rate;
                    m.SecondCount_5 = sLog.SecondCount_5;
                    m.SecondCount_5_Rate = sLog.SecondCount_5_Rate;
                    //m.SecondCount_5_Up = sLog.SecondCount_5_Up;
                    //m.SecondCount_5_Up_Rate = sLog.SecondCount_5_Up_Rate;
                }
            });
            return logList;
        }

        public List<LogForStatisticByPlatformType> GetStatisticList(LogStatisticSearchModel model)
        {
            if (model == null)
                model = new LogStatisticSearchModel();
            //if (string.IsNullOrEmpty(model.PlatformType))
            //    model.PlatformType = "Alibaba";
            Expression<Func<LogForStatisticByPlatformType, bool>> whereFunc = null;
            if (!model.PlatformType.IsNullOrEmpty())
                whereFunc = x => x.PlatformType == model.PlatformType;
            if (!model.StartTime.IsNullOrEmpty() && !model.EndTime.IsNullOrEmpty())
                whereFunc = whereFunc != null ? whereFunc.And(x => x.StartTime >= BsonUtils.ToUniversalTime(model.StartTime.toDateTime()) && x.StartTime <= BsonUtils.ToUniversalTime(model.EndTime.toDateTime())) : x => x.StartTime >= BsonUtils.ToUniversalTime(model.StartTime.toDateTime()) && x.StartTime <= BsonUtils.ToUniversalTime(model.EndTime.toDateTime());
            if(model.SubOperatorType.IsNullOrEmpty() == false)
                whereFunc = whereFunc != null ? whereFunc.And(x => x.ParentOperatorType == model.SubOperatorType) : x => x.ParentOperatorType == model.SubOperatorType;
            else
            {
                if (!model.Operator.IsNullOrEmpty())
                    whereFunc = whereFunc != null ? whereFunc.And(x => x.OperatorType == model.Operator) : x => x.OperatorType == model.Operator;
                whereFunc = whereFunc != null ? whereFunc.And(x => x.ParentOperatorType == null) : x => x.ParentOperatorType == null;
            }
            if (model.TimeType == "Day")
            {
                whereFunc = whereFunc.And(x => x.Interval == 60 * 24);
            }
            else
            {
                whereFunc = whereFunc.And(x => x.Interval == 1);
            }
            var opts = CustomerConfig.AlibabaAppKey;
            var statisticCollection = this.Collection;
            var sort = Builders<LogForStatisticByPlatformType>.Sort.Ascending(x => x.StartTime);
            // 无查询条件时默认查7天的统计数据
            var filter = whereFunc == null ? Builders<LogForStatisticByPlatformType>.Filter.Where(x => x.StartTime >= BsonUtils.ToUniversalTime(DateTime.Now.AddDays(-7))) : Builders<LogForStatisticByPlatformType>.Filter.Where(whereFunc);

            
            var bsons = statisticCollection.Find(filter)
                        .Sort(sort)
                        .As<BsonDocument>().ToList();

            var logList = bsons?.Select(s => new LogForStatisticByPlatformType
            {
                _Id = s["_id"].ToString2(),
                ObjId = s["_id"].ToString2(),
                PlatformType = s["PlatformType"] == BsonNull.Value ? "" : s["PlatformType"].ToString2(),
                OperatorType = s["OperatorType"] == BsonNull.Value ? "" : s["OperatorType"].ToString2(),
                PartFalidCount = s["PartFalidCount"] == BsonNull.Value ? 0 : s["PartFalidCount"].ToInt(),
                AllFalidCount = s["AllFalidCount"] == BsonNull.Value ? 0 : s["AllFalidCount"].ToInt(),
                ShopCount = s["ShopCount"] == BsonNull.Value ? 0 : s["ShopCount"].ToInt(),
                TotalShopCount = s["TotalShopCount"] == BsonNull.Value ? 0 : s["TotalShopCount"].ToInt(),
                SuccessCount = s["SuccessCount"] == BsonNull.Value ? 0 : s["SuccessCount"].ToInt(),
                TotalCount = s["TotalCount"] == BsonNull.Value ? 0 : s["TotalCount"].ToInt(),

                TotalOperatorCount = s["TotalOperatorCount"] == BsonNull.Value ? 0 : s["TotalOperatorCount"].ToInt(),
                SuccessOperatorCount = s["SuccessOperatorCount"] == BsonNull.Value ? 0 : s["SuccessOperatorCount"].ToInt(),
                PartFalidOperatorCount = s["PartFalidOperatorCount"] == BsonNull.Value ? 0 : s["PartFalidOperatorCount"].ToInt(),
                AllFalidOperatorCount = s["AllFalidOperatorCount"] == BsonNull.Value ? 0 : s["AllFalidOperatorCount"].ToInt(),

                CreateTime = s["CreateTime"] == BsonNull.Value ? DateTime.Now : s["CreateTime"].toDateTime().ToLocalTime(),
                StartTime = s["StartTime"] == BsonNull.Value ? DateTime.Now : s["StartTime"].toDateTime().ToLocalTime(),
                EndTime = s["EndTime"] == BsonNull.Value ? DateTime.Now : s["EndTime"].toDateTime().ToLocalTime(),
                UpdateTime = s["UpdateTime"] == BsonNull.Value ? DateTime.Now : s["UpdateTime"].toDateTime().ToLocalTime(),
                IsCompleted = s["IsCompleted"] == BsonNull.Value ? false : s["IsCompleted"].ToBool(),
                DbErrorCount = s.Contains("DbErrorCount") ? s["DbErrorCount"].ToInt() : 0,
                DbTimeoutCount = s.Contains("DbTimeoutCount") ? s["DbTimeoutCount"].ToInt() : 0,
                SecondCount_1 = s.Contains("SecondCount_1") ? s["SecondCount_1"].ToInt() : 0,
                SecondCount_2 = s.Contains("SecondCount_2") ? s["SecondCount_2"].ToInt() : 0,
                SecondCount_3 = s.Contains("SecondCount_3") ? s["SecondCount_3"].ToInt() : 0,
                SecondCount_4 = s.Contains("SecondCount_4") ? s["SecondCount_4"].ToInt() : 0,
                SecondCount_5 = s.Contains("SecondCount_5") ? s["SecondCount_5"].ToInt() : 0,
                SecondCount_10 = s.Contains("SecondCount_10") ? s["SecondCount_10"].ToInt() : 0,
                SecondCount_20 = s.Contains("SecondCount_20") ? s["SecondCount_20"].ToInt() : 0,
                SecondCount_30 = s.Contains("SecondCount_30") ? s["SecondCount_30"].ToInt() : 0,
                SecondCount_60 = s.Contains("SecondCount_60") ? s["SecondCount_60"].ToInt() : 0,
                SecondCount_120 = s.Contains("SecondCount_120") ? s["SecondCount_120"].ToInt() : 0,
                SecondCount_120_Up = s.Contains("SecondCount_120_Up") ? s["SecondCount_120_Up"].ToInt() : 0,
            }).ToList();

            //var pts = logList.Select(x => x.PlatformType).Distinct().ToList();
            //var opts = logList.Select(x => x.OperatorType).Distinct().ToList();
            //var sfilter = Builders<LogOperatorStatistic>.Filter.Where(x => pts.Contains(x.PlatformType) && opts.Contains(x.OperatorType) && x.StartTime == BsonUtils.ToUniversalTime(model.StartTime.toDateTime()));
            //var sLogList = _logStatisticRepository.Find(sfilter)?.ToList();
            //var sLogList = _logStatisticRepository.GetPlatformTypeOptStatisticList(pts, opts, true, model.StartTime.toDateTime())?.ToList();
            //logList.ForEach(m =>
            //{
            //    var sLog = sLogList.Where(x => x.PlatformType == m.PlatformType && x.OperatorType == m.OperatorType).FirstOrDefault();
            //    m.PartFalidRate = decimal.Round((m.PartFalidCount * 100.0 / m.TotalCount).ToDecimal(), 2);
            //    m.AllFalidRate = decimal.Round((m.AllFalidCount * 100.0 / m.TotalCount).ToDecimal(), 2);
            //    m.ShopRate = decimal.Round((m.ShopCount * 100.0 / m.TotalShopCount).ToDecimal(), 2);
            //    if (sLog != null)
            //    {
            //        m.TimeOutCount = sLog.TimeOutCount;
            //        m.TimeOutCountRate = sLog.TimeOutCountRate;
            //        m.SecondCount_1 = sLog.SecondCount_1;
            //        m.SecondCount_1_Rate = sLog.SecondCount_1_Rate;
            //        m.SecondCount_2 = sLog.SecondCount_2;
            //        m.SecondCount_2_Rate = sLog.SecondCount_2_Rate;
            //        m.SecondCount_3 = sLog.SecondCount_3;
            //        m.SecondCount_3_Rate = sLog.SecondCount_3_Rate;
            //        m.SecondCount_4 = sLog.SecondCount_4;
            //        m.SecondCount_4_Rate = sLog.SecondCount_4_Rate;
            //        m.SecondCount_5 = sLog.SecondCount_5;
            //        m.SecondCount_5_Rate = sLog.SecondCount_5_Rate;
            //        //m.SecondCount_5_Up = sLog.SecondCount_5_Up;
            //        //m.SecondCount_5_Up_Rate = sLog.SecondCount_5_Up_Rate;
            //    }
            //});
            return logList;
        }

    }
}
