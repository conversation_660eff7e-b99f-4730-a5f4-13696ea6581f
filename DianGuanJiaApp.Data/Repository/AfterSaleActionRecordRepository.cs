using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Data.Repository
{
    public class AfterSaleActionRecordRepository : BaseRepository<AfterSaleActionRecord>
    {
        public AfterSaleActionRecordRepository()
        {

        }

        public AfterSaleActionRecordRepository(string connectionString) : base(connectionString)
        {

        }

        /// <summary>
        /// 获取信息为复制副本
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public List<AfterSaleActionRecord> GetListForDuplication(List<string> codes, string selectFields = null)
        {
            //SQL脚本
            //var sql =
            //    $"SELECT {(string.IsNullOrWhiteSpace(selectFields) ? "*" : selectFields)} FROM AfterSaleActionRecord(NOLOCK) WHERE RefundActionRecordCode IN @RefundActionRecordCodes";
            var sql =
                $"SELECT {(string.IsNullOrWhiteSpace(selectFields) ? "*" : selectFields)} FROM AfterSaleActionRecord(NOLOCK) INNER JOIN dbo.FunStringToTable(@RefundActionRecordCodes,',') fstt ON RefundActionRecordCode = fstt.item";
            //查询
            return DbConnection.Query<AfterSaleActionRecord>(sql, new { RefundActionRecordCodes = string.Join(",", codes.Distinct()) }).ToList();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pids">UpPlatformOrderId</param>
        /// <param name="status"></param>
        /// <param name="fields"></param>
        /// <returns></returns>
        public List<AfterSaleActionRecord> GetByPids(List<string> pids, int? status = null, List<string> fields = null)
        {
            //SQL脚本
            var fieldString = "*";
            if (fields != null && fields.Any())
                fieldString = string.Join(",", fields);
            DynamicParameters parameters = new DynamicParameters();
            string sql = $"SELECT {fieldString} FROM AfterSaleActionRecord(NOLOCK) WHERE UpPlatformOrderId IN @pids";
            parameters.Add("pids", pids);
            if (status != null && status.HasValue)
            {
                sql += " AND Status=@status ";
                parameters.Add("status", status);
            }

            //查询
            return DbConnection.Query<AfterSaleActionRecord>(sql, parameters).ToList();
        }
        /// <summary>
        /// 返回结果带Items
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="fields"></param>
        /// <param name="status"></param>
        /// <param name="whereFieldName">默认查询字段：o.RefundActionRecordCode</param>
        /// <returns></returns>
        public List<AfterSaleActionRecord> GetListAndItems(List<string> codes, List<string> fields = null,
            int? status = null, string whereFieldName = "o.RefundActionRecordCode")
        {
            if (codes == null || !codes.Any())
            {
                return new List<AfterSaleActionRecord>();
            }

            var filedString = "*";
            if (fields != null && fields.Any())
            {
                filedString = string.Join(",", fields);
            }
            var whereString = "";
            if (status != null && status.HasValue)
            {
                whereString += $" AND o.Status={status.Value}";
            }

            var db = DbConnection;
            var sql = $@"SELECT {filedString} FROM dbo.AfterSaleActionRecord o WITH(NOLOCK)
                            INNER JOIN dbo.AfterSaleActionItemRecord oi WITH(NOLOCK) ON oi.RefundActionRecordCode=o.RefundActionRecordCode
                            WHERE {whereFieldName} IN@Codes {whereString}";

            var dicOrder = new Dictionary<string, AfterSaleActionRecord>();

            var result = db.Query<AfterSaleActionRecord, AfterSaleActionItemRecord, AfterSaleActionRecord>(sql,
                (o, oi) =>
                {
                    AfterSaleActionRecord order = null;
                    if (!dicOrder.TryGetValue(o.RefundActionRecordCode, out order))
                    {
                        order = o;
                        dicOrder.Add(o.RefundActionRecordCode, order);
                    }

                    //处理Item
                    if (oi != null &&
                        !order.Items.Any(x => x.RefundActionItemRecordCode == oi.RefundActionItemRecordCode))
                    {
                        order.Items.Add(oi);
                    }

                    return order;
                }, new { Codes = codes });

            return dicOrder.Values.ToList();
        }

        /// <summary>
        /// 更新执行结果
        /// </summary>
        /// <param name="models"></param>
        public void UpdateExeResult(List<AfterSaleActionRecord> models)
        {
            var lastSql = string.Empty;
            //拼更新SQL，单条单条拼
            var count = 0;
            var batchSize = 200;
            var db = DbConnection;

            if (db.State == System.Data.ConnectionState.Closed)
                db.Open();
            using (db)
            {
                var parameters = new DynamicParameters();
                models.ForEach(m =>
                {
                    count++;
                    if (m.Status == -1)
                    {
                        lastSql += $"UPDATE AfterSaleActionRecord SET Status={m.Status},ErrorMessage=@p1_{count},UpRefundType=@p4_{count},UpRefundReason=@p5_{count},UpdateTime=GETDATE() WHERE RefundActionRecordCode='{m.RefundActionRecordCode}'; ";
                        parameters.Add($"p1_{count}", m.ErrorMessage);
                        parameters.Add($"p4_{count}", m.UpRefundType);
                        parameters.Add($"p5_{count}", m.UpRefundReason);
                    }
                    else
                    {
                        lastSql += $"UPDATE AfterSaleActionRecord SET Status={m.Status},ErrorMessage=@p1_{count},UpAfterSaleId=@p2_{count},UpAfterSaleCode=@p3_{count},UpRefundType=@p4_{count},UpRefundReason=@p5_{count},UpdateTime=GETDATE() WHERE RefundActionRecordCode='{m.RefundActionRecordCode}'; ";
                        parameters.Add($"p1_{count}", m.ErrorMessage);
                        parameters.Add($"p2_{count}", m.UpAfterSaleId);
                        parameters.Add($"p3_{count}", m.UpAfterSaleCode);
                        parameters.Add($"p4_{count}", m.UpRefundType);
                        parameters.Add($"p5_{count}", m.UpRefundReason);

                        //更新关联表采购单退款状态
                        if(m.Relation != null)
                        {
                            var refundStatus = "AUTO_REFUND_PART";
                            if (m.Relation.IsPartRunfund)
                            {
                                refundStatus = "AUTO_REFUND";
                            }
                            lastSql += $"UPDATE PurchaseOrderRelation SET PurchaseOrderRefundStatus='{refundStatus}',UpdateTime=GETDATE() WHERE PurchaseRelationCode='{m.Relation.PurchaseRelationCode}'; ";
                            parameters.Add($"p6_{count}", m.Relation.PurchaseRelationCode);
                        }

                    }
                    if (count >= batchSize)
                    {
                        db.Execute(lastSql, parameters);
                        lastSql = string.Empty;
                        count = 0;
                        parameters = new DynamicParameters();
                    }
                });
                if (string.IsNullOrEmpty(lastSql) == false)
                    db.Execute(lastSql, parameters);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logicOrderIds"></param>
        /// <returns></returns>
        public List<AfterSaleActionRecord> GetByLogicOrderIds(List<string> logicOrderIds)
        {
            //SQL脚本
            const string sql = "SELECT * FROM AfterSaleActionRecord(NOLOCK) WHERE SourceLogicOrderId IN @logicOrderIds";
            //查询
            return DbConnection.Query<AfterSaleActionRecord>(sql, new { logicOrderIds }).ToList();
        }

        /// <summary>
        /// 获取已存在列表
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        public List<IdAndCodeModel> GetExistIdAndCodes(List<string> codes)
        {
            //SQL脚本
            const string sql = "SELECT Id,RefundActionRecordCode AS Code FROM AfterSaleActionRecord(NOLOCK) WHERE RefundActionRecordCode IN @Codes";
            //查询
            return DbConnection.Query<IdAndCodeModel>(sql, new { Codes = codes }).ToList();
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<AfterSaleActionRecord>> GetPageList(AfterSaleActionRecordQuery query)
        {
            var strFields = "asar.*";
            if (query.Fields != null && query.Fields.Any())
                strFields = string.Join(",", query.Fields);

            //商家
            string whereStr = $" pfn.FxUserId={query.SourceFxUserId} AND pfn.UpFxUserId=0";
            var parameters = new DynamicParameters();

            if (query.UpPlatformOrderIds != null && query.UpPlatformOrderIds.Any())
            {
                whereStr += " AND asar.UpPlatformOrderId IN @UpPlatformOrderIds";
                parameters.Add("UpPlatformOrderIds", query.UpPlatformOrderIds);
            }
            if (query.SourceLogicOrderIds != null && query.SourceLogicOrderIds.Any())
            {
                whereStr += " AND asar.SourceLogicOrderId IN @SourceLogicOrderIds";
                parameters.Add("SourceLogicOrderIds", query.SourceLogicOrderIds);
            }
            if (query.Status.HasValue)
            {
                whereStr += " AND asar.Status=@Status";
                parameters.Add("Status", query.Status.Value);
            }
            if (query.StartUpdateTime.HasValue)
            {
                whereStr += " AND asar.UpdateTime>=@StartUpdateTime";
                parameters.Add("StartUpdateTime", query.StartUpdateTime.Value);
            }
            if (query.StartUpdateTime.HasValue)
            {
                whereStr += " AND asar.UpdateTime<@EndUpdateTime";
                parameters.Add("EndUpdateTime", query.EndUpdateTime.Value);
            }


            #region 拼SQL

            var sql = $@" SELECT COUNT(1) FROM AfterSaleActionRecord asar WITH(NOLOCK)
INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode=asar.SourcePathFlowCode
WHERE {whereStr} ; 
SELECT {strFields} FROM AfterSaleActionRecord asar WITH(NOLOCK)
INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode=asar.SourcePathFlowCode
WHERE {whereStr} ORDER BY asar.Id DESC
OFFSET {(query.PageIndex - 1) * query.PageSize} ROWS FETCH NEXT {query.PageSize} ROWS ONLY
";
            #endregion

            #region 记录执行的SQL语句
            if (CustomerConfig.IsDebug)
            {
                WriteSqlToLog(sql, parameters, "GetPageListSql.txt", "AfterSaleActionRecord", "GetPageList");
            }
            #endregion

            var db = this.DbConnection;
            var grid = db.QueryMultiple(sql, param: parameters);
            var totalCount = grid.Read<int>().FirstOrDefault();
            var result = grid.Read<AfterSaleActionRecord>().ToList();

            return Tuple.Create(totalCount, result);
        }

    }
}