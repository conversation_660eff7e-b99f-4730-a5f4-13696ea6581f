using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using Z.Dapper.Plus;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility;
using System.Data;
using DianGuanJiaApp.Data.Model;
using Nest;
using DianGuanJiaApp.Data.Model.Tools;

namespace DianGuanJiaApp.Data.Repository.BaseProduct
{
    /// <summary>
    /// 基础商品表 相关数据层操作
    /// </summary>
    public partial class BaseProductRepository : BaseProductBaseRepository<BaseProductEntity>
    {
        private string _connectionString = string.Empty;

        /// <summary>
        /// 通过BaseSiteContext.CurrentNoThrow.CurrentFxUserId获取使用的数据库
        /// </summary>
        public BaseProductRepository()
        {
        }

        /// <summary>
        /// 指定fxUserId
        /// </summary>
        /// <param name="fxUserId"></param>
        public BaseProductRepository(int fxUserId) : base(fxUserId)
        {
        }

        /// <summary>
        /// 指定连接字符串
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="isUseMySQL"></param>
        public BaseProductRepository(string connectionString, bool isUseMySQL = true) : base(connectionString, isUseMySQL)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 批量添加
        /// </summary>
        /// <param name="models"></param>
        public void BatchAdd(List<BaseProductEntity> models)
        {
            if (models == null || models.Any() == false)
                return;

            var db = DbConnection;
            db.BulkInsert(models);
        }

        /// <summary>
        /// 根据uid获取信息
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public BaseProductEntity GetByUid(long uid)
        {
            var sqlTemplate = "SELECT * FROM BaseProduct WITH(NOLOCK) WHERE Uid=@uid";
            var sql = TranSql(sqlTemplate);
            var model = DbConnection.QueryFirstOrDefault<BaseProductEntity>(sql, new { uid });
            return model;
        }

        /// <summary>
        /// 根据uid获取信息
        /// </summary>
        /// <param name="uid"></param>
        /// <returns></returns>
        public BaseProductEntity GetByUid(long uid,int fxUserId)
        {
            var sqlTemplate = "SELECT * FROM BaseProduct WITH(NOLOCK) WHERE Uid=@uid AND FxUserId = @fxUserId";
            var sql = TranSql(sqlTemplate);
            var model = DbConnection.QueryFirstOrDefault<BaseProductEntity>(sql, new { uid,fxUserId });
            return model;
        }

        /// <summary>
        /// 获取信息为复制副本
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<BaseProductEntity> GetList(List<string> codes, string selectFields = "*",
            string whereFieldName = "SpuCode")
        {
            var sqlTemplate = $"SELECT {(string.IsNullOrWhiteSpace(selectFields) ? "*" : selectFields)} FROM BaseProduct WITH(NOLOCK) WHERE {whereFieldName} IN @Codes";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Query<BaseProductEntity>(sql, new { Codes = codes }).ToList();
        }

        /// <summary>
        /// 获取已存在列表
        /// </summary>
        /// <param name="codes">SpuCode</param>
        /// <returns></returns>
        public List<IdAndCodeModel> GetExistIdAndCodes(List<string> codes, int fxUserId)
        {
            var sqlTemplate = "SELECT Id,SpuCode AS Code,FxUserId FROM BaseProduct WITH(NOLOCK) WHERE SpuCode IN @codes AND FxUserId=@fxUserId AND Status=1";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Query<IdAndCodeModel>(sql, new { codes, fxUserId }).ToList();
        }



        /// <summary>
        /// 获取已存在列表
        /// </summary>
        /// <param name="codes">SpuCode</param>
        /// <returns></returns>
        public List<BaseProductEntity> GetBaseProductByCode(List<string> codes, int fxUserId)
        {
            var data = new List<BaseProductEntity>();
            var pageSize = 200;
            var total = Math.Ceiling(codes.Count() * 1.0 / pageSize);
            for (int i = 0; i < total; i++)
            {
                var tempCodes = codes.Skip(i * pageSize).Take(pageSize).ToList(); 
                var sqlTemplate = "SELECT * FROM BaseProduct WITH(NOLOCK) WHERE SpuCode IN @tempCodes AND FxUserId=@fxUserId AND Status=1";
                var sql = TranSql(sqlTemplate);
                var res = DbConnection.Query<BaseProductEntity>(sql, new { tempCodes, fxUserId }).ToList();
                data.AddRange(res);
            }
            return data;
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<BaseProductEntity>> GetPageList(BaseProductQuery query)
        {
            var strFields = "*";
            if (query.Fields != null && query.Fields.Any())
                strFields = string.Join(",", query.Fields);

            string whereStr = $" FxUserId={query.FxUserId} ";
            var parameters = new DynamicParameters();

            if (query.Uid.HasValue)
            {
                whereStr += " AND Uid=@Uid";
                parameters.Add("Uid", query.Uid.Value);
            }
            if (!string.IsNullOrEmpty(query.ShortTitle))
            {
                whereStr += " AND ShortTitle LIKE @ShortTitle";
                parameters.Add("@ShortTitle", $"%{query.ShortTitle}%");
            }
            if (!string.IsNullOrEmpty(query.Subject))
            {
                whereStr += " AND Subject LIKE @Subject";
                parameters.Add("@Subject", $"%{query.Subject}%");
            }
            if (!string.IsNullOrEmpty(query.SpuCode))
            {
                whereStr += " AND SpuCode IN (@SpuCode)";
                parameters.Add("@SpuCode", query.SpuCode.Split(',').ToList());
            }
            whereStr += " AND Status = 1";
            #region 拼SQL

            var sql = $@"SELECT COUNT(1) FROM BaseProduct WITH(NOLOCK) WHERE {whereStr} ; 
SELECT {strFields} FROM BaseProduct WITH(NOLOCK) WHERE {whereStr} ORDER BY Id DESC
OFFSET {(query.PageIndex - 1) * query.PageSize} ROWS FETCH NEXT {query.PageSize} ROWS ONLY
";
            if (IsUseMySQL)
            {
                sql = $@"SELECT COUNT(1) FROM BaseProduct WHERE {whereStr} ; 
SELECT {strFields} FROM BaseProduct WHERE {whereStr} ORDER BY Id DESC
limit {(query.PageIndex - 1) * query.PageSize},{query.PageSize}
";
            }

            #endregion

            #region 记录执行的SQL语句
            if (CustomerConfig.IsDebug)
            {
                WriteSqlToLog(sql, parameters, "GetPageListSql.txt", "BaseProduct", "GetPageList");
            }
            #endregion
            var db = this.DbConnection;
            var grid = db.QueryMultiple(sql, param: parameters);
            var totalCount = grid.Read<int>().FirstOrDefault();
            var result = grid.Read<BaseProductEntity>().ToList();

            return Tuple.Create(totalCount, result);
        }

        /// <summary>
        /// 批量更新
        /// </summary>
        /// <param name="models"></param>
        public new void BulkUpdate(List<BaseProductEntity> models)
        {
            if (models == null || models.Any() == false)
                return;

            const int count = 500;
            var total = models.Count;
            var times = total / count;
            if (total % count > 0)
                times++;

            var db = DbConnection;
            for (var i = 0; i < times; i++)
            {
                var list = models.Skip(i * count).Take(count).ToList();
                db.BulkUpdate(list);
            }
        }

        /// <summary>
        /// 获取已存在的FormProductUid
        /// </summary>
        /// <param name="fromUids"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<string> GetByFromUids(List<long> fromUids, int fxUserId)
        {
            const string sqlTemplate = "SELECT FromProductUid FROM BaseProduct WITH(NOLOCK) WHERE FromProductUid IN @FromUids AND FxUserId=@FxUserId AND Status = 1";
            var sql = TranSql(sqlTemplate);
            var uidString = fromUids.Select(x => x.ToString()).ToList();
            return DbConnection.Query<string>(sql, new { FromUids = uidString, FxUserId = fxUserId }).AsList();
        }

        /// <summary>
        /// 根据uid进行逻辑删除
        /// </summary>
        /// <param name="uid"></param>
        public void DeleteByUid(long uid)
        {
            const string sqlTemplate = "UPDATE BaseProduct SET Status=0 WHERE Uid=@uid";
            var sql = TranSql(sqlTemplate);
            DbConnection.Execute(sql, new { uid });
        }

        /// <summary>
        /// 根据uid进行批量逻辑删除
        /// </summary>
        /// <param name="uids"></param>
        /// <returns></returns>
        public int BatchDelete(List<long> uids)
        {
            //if (uids.IsNullOrEmptyList())
            //    return 0;
            //const string sqlTemplate = "UPDATE BaseProduct SET Status=0 WHERE Uid IN @uids";
            //var sql = TranSql(sqlTemplate);
            //return DbConnection.Execute(sql, new { uids });


            if (uids == null || uids.Any() == false)
                return 0;

            const int count = 500;
            var total = uids.Count;
            var times = total / count;
            if (total % count > 0)
                times++;

            var sql = "UPDATE BaseProduct SET Status=0 WHERE Uid IN @uids";
            var res = 0;

            for (var i = 0; i < times; i++)
            {
                var list = uids.Skip(i * count).Take(count).ToList();
                res = DbConnection.Execute(sql, new { uids = list });
            }
            return res;
        } 

        /// <summary>
        /// 根据uid和用户获取数据
        /// </summary>
        /// <param name="uids"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BaseProductEntity> GetByUidsAndFxUserId(List<long> uids, int fxUserId)
        {
            const string sqlTemplate = "SELECT * FROM BaseProduct WITH(NOLOCK) WHERE Uid IN @uids AND FxUserId = @fxUserId";
            var sql = TranSql(sqlTemplate);
            var entList = DbConnection.Query<BaseProductEntity>(sql, new { uids, fxUserId }).ToList();
            return entList;
        }

        /// <summary>
        /// 获取已存在指定来源Uid的数据
        /// </summary>
        /// <param name="fromProductUid"></param>
        /// <param name="fxUserId"></param>
        /// <returns>Uid</returns>
        public BaseProductEntity GetExistByFromProductUid(string fromProductUid, int fxUserId)
        {
            var sql = "SELECT TOP 1 * FROM BaseProduct WITH(NOLOCK) WHERE FromProductUid = @fromProductUid AND FxUserId=@fxUserId AND Status=1";
            if (IsUseMySQL)
                sql = "SELECT * FROM BaseProduct WHERE FromProductUid = @fromProductUid AND FxUserId=@fxUserId AND Status=1 LIMIT 1";

            var result = DbConnection.QueryFirstOrDefault<BaseProductEntity>(sql, new { fromProductUid, fxUserId });
            return result;
        }

        /// <summary>
        /// 获取指定Uid的基础商品
        /// </summary>
        /// <param name="baseProUids"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BaseProductEntity> GetListByUids(List<long> baseProUids, int fxUserId)
        {
            if (baseProUids == null || baseProUids.Any() == false) return new List<BaseProductEntity>();
            
            const int count = 500;
            var total = baseProUids.Count;
            var times = total / count;
            if (total % count > 0)
                times++;
            
            var result = new List<BaseProductEntity>();
            
            using (var db = DbConnection) 
            {
                if (db.State != ConnectionState.Open) db.Open();
                
                for (var i = 0; i < times; i++)
                {
                    var list = baseProUids.Skip(i * count).Take(count).ToList();
                    const string sqlTemplate = "SELECT * FROM BaseProduct WITH(NOLOCK) WHERE Uid IN @uids AND FxUserId = @fxUserId AND Status = 1";
                    var sql = TranSql(sqlTemplate);
                    var model = db.Query<BaseProductEntity>(sql, new { uids = list, fxUserId }).ToList();
                    result.AddRange(model);
                }
            }
            
            return result;
        }

        /// <summary>
        /// 基础商品查询
        /// </summary>
        /// <param name="baseProductUids"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BaseProductEntity> GetBaseProductByUids(List<long> baseProductUids, int fxUserId)
        {
            if (baseProductUids == null || baseProductUids.Any() == false) return new List<BaseProductEntity>();

            const int count = 500;
            var total = baseProductUids.Count;
            var times = total / count;
            if (total % count > 0)
                times++;

            var result = new List<BaseProductEntity>();

            using (var db = DbConnection)
            {
                if (db.State != ConnectionState.Open) db.Open();

                for (var i = 0; i < times; i++)
                {
                    var tempBaseProductUids = baseProductUids.Skip(i * count).Take(count).ToList();
                    const string sqlTemplate = "SELECT * FROM BaseProduct WITH(NOLOCK) p LEFT JOIN baseproductsku WITH(NOLOCK) s ON p.Uid = s.BaseProductUid WHERE p.Uid IN @uids AND p.FxUserId = @fxUserId AND p.Status = 1 AND s.Status=1";
                    var sql = TranSql(sqlTemplate);

                    var lookUp = new Dictionary<long, BaseProductEntity>();
                    var dictSku = new Dictionary<long, Dictionary<long, BaseProductSku>>();

                    var model = db.Query<BaseProductEntity , BaseProductSku, BaseProductEntity>(sql, (p, ps) =>
                    {
                        BaseProductEntity product = null;
                        if (!lookUp.TryGetValue(p.Id, out product))
                        {
                            product = p;
                            product.Skus = new List<BaseProductSku>();
                            lookUp.Add(product.Id, product);
                            dictSku.Add(product.Id, new Dictionary<long, BaseProductSku>());
                        }
                        //添加sku的数据
                        if (ps != null && dictSku[product.Id].ContainsKey(ps.Id) == false)
                        {
                            product.Skus.Add(ps);
                            dictSku[product.Id].Add(ps.Id, null);
                        }
                        return product;
                    }, new { uids= tempBaseProductUids, fxUserId = fxUserId}, splitOn: "Id,Id,Id").ToList();

                    var data = lookUp.Select(k => k.Value ?? new BaseProductEntity()).ToList();
                    if (lookUp.Count != 0) {
                        result.AddRange(data);
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 基础商品查询
        /// </summary>
        /// <param name="baseProductCodes"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<BaseProductEntity> GetBaseProductByCodes(List<string> baseProductCodes, int fxUserId)
        {
            if (baseProductCodes == null || baseProductCodes.Any() == false) return new List<BaseProductEntity>();

            const int count = 500;
            var total = baseProductCodes.Count;
            var times = total / count;
            if (total % count > 0)
                times++;

            var result = new List<BaseProductEntity>();

            using (var db = DbConnection)
            {
                if (db.State != ConnectionState.Open) db.Open();

                for (var i = 0; i < times; i++)
                {
                    var tempBaseProductCodes = baseProductCodes.Skip(i * count).Take(count).ToList();
                    const string sqlTemplate = "SELECT * FROM BaseProduct WITH(NOLOCK) p LEFT JOIN baseproductsku WITH(NOLOCK) s ON p.Uid = s.BaseProductUid WHERE p.SpuCode IN @SpuCode AND p.FxUserId = @fxUserId AND p.Status = 1 AND s.Status=1";
                    var sql = TranSql(sqlTemplate);

                    var lookUp = new Dictionary<long, BaseProductEntity>();
                    var dictSku = new Dictionary<long, Dictionary<long, BaseProductSku>>();

                    var model = db.Query<BaseProductEntity, BaseProductSku, BaseProductEntity>(sql, (p, ps) =>
                    {
                        BaseProductEntity product = null;
                        if (!lookUp.TryGetValue(p.Id, out product))
                        {
                            product = p;
                            product.Skus = new List<BaseProductSku>();
                            lookUp.Add(product.Id, product);
                            dictSku.Add(product.Id, new Dictionary<long, BaseProductSku>());
                        }
                        //添加sku的数据
                        if (ps != null && dictSku[product.Id].ContainsKey(ps.Id) == false)
                        {
                            product.Skus.Add(ps);
                            dictSku[product.Id].Add(ps.Id, null);
                        }
                        return product;
                    }, new { SpuCode = tempBaseProductCodes, fxUserId= fxUserId }, splitOn: "Id,Id,Id").ToList();

                    var data = lookUp.Select(k => k.Value ?? new BaseProductEntity()).ToList();
                    if (lookUp.Count != 0)
                    {
                        result.AddRange(data);
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 获取指定Uid的基础商品，不区分用户和状态
        /// </summary>
        /// <param name="baseProUids"></param>
        /// <returns></returns>
        public List<BaseProductEntity> GetListByUids(List<long> baseProUids)
        {
            if (baseProUids == null || baseProUids.Any() == false) return new List<BaseProductEntity>();
            
            const int count = 500;
            var total = baseProUids.Count;
            var times = total / count;
            if (total % count > 0)
                times++;
            
            var result = new List<BaseProductEntity>();

            using (var db = DbConnection) 
            {
                if (db.State != ConnectionState.Open) db.Open();
                
                for (var i = 0; i < times; i++)
                {
                    var list = baseProUids.Skip(i * count).Take(count).ToList();
                    const string sqlTemplate = "SELECT * FROM BaseProduct WITH(NOLOCK) WHERE Uid IN @uids";
                    var sql = TranSql(sqlTemplate);
                    var model = db.Query<BaseProductEntity>(sql, new { uids = list }).ToList();
                    result.AddRange(model);
                }
            } 
            return result;
        }

       /// <summary>
       /// 获取基础商品分页信息，循环分页以获取全部结果，仅用于工具
       /// </summary>
       /// <param name="fxUserId"></param>
       /// <param name="isQueryCombine">是否查询组合商品</param>
       /// <returns></returns>
       public List<BaseProductEntity> GetAll(int fxUserId, bool isQueryCombine = false)
       {
           var pageNumber = 1;
           const int pageSize = 100;
           var productDictionary = new Dictionary<long, BaseProductEntity>();
           bool hasMoreResults;
           
           var combineStr = isQueryCombine ? string.Empty : " AND t2.IsCombineSku = 0";
           var joinSql = isQueryCombine ? "LEFT JOIN " : "INNER JOIN ";

           do
           {
               var offset = (pageNumber - 1) * pageSize;

               var sql = $@"SELECT *
                FROM BaseProduct t1
                {joinSql} BaseProductSku t2 ON t1.Uid = t2.BaseProductUid AND t2.Status = 1 {combineStr}
                LEFT JOIN BaseProductImage t3 ON t1.Uid = t3.ProductUid AND t3.ImageObjectId = t1.MainImageObjectId AND t3.Status = 1
                LEFT JOIN BaseOfPtSkuRelation t4 ON t2.Uid = t4.BaseProductSkuUid AND t4.Status = 1
                WHERE t1.Status = 1 AND (@FxUserId <= 0 OR t1.FxUserId = @FxUserId)
                ORDER BY t1.Uid
                LIMIT @Offset, @PageSize;";

               var parameters = new
               {
                   FxUserId = fxUserId,
                   IsQueryCombine = isQueryCombine,
                   Offset = offset,
                   PageSize = pageSize
               };
               
               var grid = DbConnection.QueryMultiple(sql, parameters);

               // 读取查询结果并直接处理关联
               var currentBatchCount = grid
                   .Read<BaseProductEntity, BaseProductSku, BaseProductImage, BaseOfPtSkuRelation, BaseProductEntity>(
                       (product, sku, image, relation) =>
                       {
                           BaseProductEntity entity;
                           // 查找或创建商品实体
                           if (!productDictionary.TryGetValue(product.Uid, out entity))
                           {
                               entity = product;
                               productDictionary.Add(product.Uid, entity);
                           }

                           // 处理 SKU 信息
                           if (sku != null && sku.BaseProductUid == entity.Uid && sku.SkuCode.IsNotNullOrEmpty())
                           {
                               // 检查并初始化 SKU 列表
                               if (entity.SkuDic == null) entity.SkuDic = new Dictionary<long, BaseProductSku>();
                               if (entity.Skus == null) entity.Skus = new List<BaseProductSku>();

                               BaseProductSku tempSku;
                               if (!entity.SkuDic.TryGetValue(sku.Uid, out tempSku))
                               {
                                   tempSku = sku;
                                   entity.SkuDic.Add(tempSku.Uid, tempSku);
                                   entity.Skus.Add(tempSku);
                               }

                               // 处理 SKU 的关联关系
                               if (tempSku.RelationList == null) tempSku.RelationList = new List<BaseOfPtSkuRelation>();

                               if (relation != null && tempSku.Uid == relation.BaseProductSkuUid) tempSku.RelationList.Add(relation);
                           }

                           // 处理图片信息
                           if (image != null && entity.MainImageObjectId == image.ImageObjectId)
                           {
                               if (entity.Images == null) entity.Images = new List<BaseProductImage>();
                        
                               entity.Images.Add(image);
                               entity.MainImageUrl = image.FullUrl; // 设置主图URL 
                           }

                           return null;
                       }).Count();

               hasMoreResults = currentBatchCount == pageSize;
               pageNumber++;
           } while (hasMoreResults);

           // 返回商品实体列表
           return productDictionary.Values.ToList();
       }
       
       /// <summary>
       /// 检查已存在的SkuUid
       /// </summary>
       /// <param name="skuUids"></param>
       /// <param name="fxUserId"></param>
       /// <returns></returns>
       public List<long> CheckExistSkuUids(List<long> skuUids, int fxUserId)
       {
           if (skuUids == null || skuUids.Count == 0) return new List<long>();

           const string sqlTemplate = @"SELECT sku.Uid FROM BaseProduct pro 
INNER JOIN BaseProductSku sku ON pro.Uid = sku.BaseProductUid 
WHERE sku.Uid IN @skuUids AND pro.FxUserId = @fxUserId AND pro.Status=1";
           var result = DbConnection.Query<long>(sqlTemplate, new { skuUids, fxUserId })?.ToList();
           if (result == null || result.Count == 0) return new List<long>();
           result = result.Distinct().ToList();
           return result;
       }

       /// <summary>
       /// 游标方式查询基础商品以初始化GenerateBaseProductRecord 仅工具使用
       /// </summary>
       /// <param name="preLastId">查询起始Id</param>
       /// <param name="endId"></param>
       /// <param name="pageSize"></param>
       /// <returns></returns>
       public List<BaseProductEntity> GetPageForInitGenerateRecord(long preLastId,long? endId, long pageSize)
       {
            var whereStr = " Id>@preLastId AND CreateFrom = 'Product'";
            var pageStr = string.Empty;
            var param = new DynamicParameters();
           
            param.Add("preLastId",preLastId);

            if (endId != null)
            {
                whereStr = " Id>@preLastId AND Id<=@endId AND CreateFrom = 'Product'";
                param.Add("endId", endId);
            }
            else
            {
                pageStr = " LIMIT @pageSize";
                param.Add("pageSize", pageSize);
            }

            whereStr += " AND FromFxUserId >0 AND FxUserId >0";

            var sql = $@"
SELECT Id,Uid,FromShopPt,FromShopId,FromFxUserId,FromProductUid,FxUserId FROM BaseProduct WHERE {whereStr} {pageStr}";

            return DbConnection.Query<BaseProductEntity>(sql, param).ToList();
       }
    }

}
