using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Data.Repository
{

    public partial class CommonSettingRecordRepository : BaseRepository<Entity.CommonSettingRecord>
    {
        public CommonSettingRecordRepository() : base(CustomerConfig.ConfigureDbConnectionString)
        {
        }

        /// <summary>
        /// 配置内容变更日志
        /// </summary>
        /// <param name="createFxUserId">创建人用户Id</param>
        /// <param name="keyNames">Key名称</param>
        /// <returns></returns>
        public List<CommonSettingRecord> GetList(int createFxUserId, List<string> keyNames)
        {
            var sql = $"SELECT TOP 50 * FROM dbo.CommonSettingRecord WITH(NOLOCK) WHERE CreateFxUserId=@createFxUserId AND KeyName IN @keyNames ORDER BY Id DESC";
            return DbConnection.Query<CommonSettingRecord>(sql, new { createFxUserId, keyNames }).ToList();
        }
    }
}
