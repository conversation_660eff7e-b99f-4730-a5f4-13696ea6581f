using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using System.Data;
using DianGuanJiaApp.Data.Entity;
using Z.Dapper.Plus;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Data.Repository
{

    public partial class CustomerStaffRepository : BaseRepository<CustomerStaff>
    {
        public CustomerStaffRepository() : base(CustomerConfig.ConfigureDbConnectionString)
        {
        }

        public CustomerStaff GetModel(string loginName, string pwd)
        {
            var sql = "select * from P_customerStaff WITH(NOLOCK) where LoginName=@loginName AND pwd=@pwd";
            var model = DbConnection.QueryFirstOrDefault<CustomerStaff>(sql, new
            {
                loginName = loginName,
                pwd = pwd
            });

            if (model != null)
            {
                model.Online = CommUtls.MD5(loginName + pwd + DateTime.Now.ToString("yyyyMMddHHmmssfff"));
                model.LastOpreatTime = DateTime.Now;
                Update(model);
            }

            return model;
        }

        public Tuple<bool, string> AddUserOrUpdatePwd(string loginName, string nickName, string pwd, string isAdd)
        {
            var sql = "select * from P_customerStaff WITH(NOLOCK) where LoginName=@loginName";
            var model = DbConnection.QueryFirstOrDefault<CustomerStaff>(sql, new
            {
                loginName = loginName
            });
            if (isAdd == "1")
            {
                if (model != null)
                {
                    return new Tuple<bool, string>(false, "�˺��Ѵ���");
                }
                else
                {
                    Add(new CustomerStaff()
                    {
                        LoginName = loginName,
                        NickName = nickName,
                        Pwd = pwd,
                        UserType = 2, //Ĭ�Ϲ���Ա�ɣ���Щ�û��½����͸�������
                        CreateTime = DateTime.Now,
                    });
                }

            }
            else
            {
                if (model == null)
                {
                    return new Tuple<bool, string>(false, "�˺Ų�����");
                }
                else
                {
                    model.Pwd = pwd;
                    Update(model);
                }
            }

            return new Tuple<bool, string>(true, string.Empty);
        }
    }
}
