using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Entity.DataMigrate;
using DianGuanJiaApp.Data.Repository.Settings;

namespace DianGuanJiaApp.Data.Repository
{

    public class DataChangeLogRepository : ExceptionHandler
    {
        private FxDbConfigRepository _fxDbConfigRepository;
        private CommonSettingRepository _csRepository;
        public DataChangeLogRepository()
        {
            _fxDbConfigRepository = new FxDbConfigRepository();
            _csRepository = new CommonSettingRepository();
        }

        /// <summary>
        /// 是否开启新增
        /// </summary>
        /// <returns></returns>
        public bool IsEnabledAdd(int fxUserId)
        {
            //按云平台是否开启收集变更日志控制（Alibaba，Pinduoduo，TouTiao）
            if (DuplicationColdStorageSwitchRepository.Instance.IsEnabledDuplication())
            {
                return true;
            }
            //var curCloudPlatformType = CustomerConfig.CloudPlatformType.ToLower();
            ////2023-10-12
            ////1、精选云：只有指定白名单的用户才要添加
            ////2、抖店云：现有逻辑不变，根据迁移状态判断
            //if (curCloudPlatformType == CloudPlatformType.TouTiao.ToString().ToLower())
            //    return true;

            //if (curCloudPlatformType == CloudPlatformType.Alibaba.ToString().ToLower())
            //{
            //    //精选需要写日志的用户白名单
            //    var key = "/System/Fendan/NeedWriteDataChangeLog/FxUserIds";
            //    var strNeedWriteLogFxUserIds = _csRepository.Get(key, 0)?.Value ?? "";
            //    if (string.IsNullOrEmpty(strNeedWriteLogFxUserIds))
            //        return false;
            //    strNeedWriteLogFxUserIds = strNeedWriteLogFxUserIds.Replace("，", ",");
            //    strNeedWriteLogFxUserIds = strNeedWriteLogFxUserIds.Replace(" ", ",");
            //    return strNeedWriteLogFxUserIds.Split(',').Contains(fxUserId.ToString());
            //}
            return false;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="log"></param>
        /// <param name="flag">1=针对迁移中，0=针对常规</param>
        /// <returns></returns>
        public bool Add(DataChangeLog log, int flag = 0)
        {
            //分销用户ID为空
            if (log.SourceFxUserId == 0)
            {
                return true;
            }

            //暂时非阿里云及抖店云平台不开启
            if (!IsEnabledAdd(log.SourceFxUserId))
            {
                return true;
            }
            try
            {
                //是否异常
                Exception exception = null;
                try
                {
                    //抖店云 && flag == 0，不需要判断迁移任务，直接添加
                    var isAdd = false;
                    if (flag == 0)
                        //if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString() && flag == 0)
                    {
                        if (BaseSiteContext.CurrentNoThrow?.CurrentDbConfig?.DbConfig?.FromFxDbConfig == 0)
                        {
                            if (IsWriteDataChangeLogByNotMigrateUser() == false)
                            {
                                Log.Debug(
                                    $"当前用户({BaseSiteContext.CurrentNoThrow?.CurrentFxUserId})数据库配置是旧库，则不记录数据变更日志，相关信息：{log.SourceFxUserId}|{log.SourceShopId}|{log.TableTypeName}|{log.RelationKey}",
                                    $"AddDataChangeLog_Old_{DateTime.Now:yyyy-MM-dd}.log");
                            }
                            else
                            {
                                isAdd = true;
                            }
                        }
                        else
                        {
                            isAdd = true;
                        }
                    }
                    else
                    {
                        //查询迁移状态
                        isAdd = IsAdd(flag, log.SourceFxUserId);
                    }

                    if (!isAdd)
                        return true;
                    return ExceptionRetryHandler(
                        () => DataChangeLogRepositoryFactory.GetDataChangeLogRepository().Add(log), true);
                }
                catch (Exception ex)
                {
                    exception = ex;
                    Log.WriteError($"写入变更日志时异常：{ex} ==> {log.ToJson()}", "AddDataChangeLogError.log");
                    //重试后还是异常，则保存至备用库
                    return DataChangeLogRepositoryFactory.GetDataChangeLogRepository().AddToBackupDbExceptionRetry(log);
                }
                finally
                {
                    DataChangeLogRepositoryFactory.GetDataChangeLogRepository().WriteDataChangeLogLog(
                        new List<DataChangeLog> { log },
                        isException: exception != null,
                        exceptionMessage: exception?.Message);
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"写入变更日志时异常：{e} ==> {log.ToJson()}", "AddDataChangeLogError.log");
            }
            return true;
        }

        /// <summary>
        /// 是否写变更日志
        /// </summary>
        /// <returns></returns>
        public bool IsWriteDataChangeLogByNotMigrateUser()
        {
            //配置键，程序类型：Site（站点），Service（服务）
            var key = $"/DianGuanJia/FenDan/IsWriteDataChangeLog/{CustomerConfig.ApplicationType}";
            var defaultValue = "1";
            if (CustomerConfig.ApplicationType.Equals("Service", StringComparison.OrdinalIgnoreCase))
            {
                defaultValue = "0";
            }
            //获取配置
            var isEnabledWriteDataChangeLog = _csRepository.Get(key, 0)?.Value ?? defaultValue;
            //为空则按照默认值处理
            if (string.IsNullOrWhiteSpace(isEnabledWriteDataChangeLog))
            {
                return defaultValue == "1";
            }
            //返回
            return isEnabledWriteDataChangeLog == "1";
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logs"></param>
        /// <param name="flag">1=针对迁移中，0=针对常规</param>
        /// <param name="exMsg">异常信息</param>
        /// <returns></returns>
        public bool Add(List<DataChangeLog> logs, int flag = 0, string exMsg = "")
        {
            if (logs == null || !logs.Any())
                return true;
            //非阿里云及抖店云平台不开启
            if (!IsEnabledAdd(logs.First().SourceFxUserId))
            {
                return true;
            }

            //是否异常
            Exception exception = null;
            try
            {
                if (flag == 0)
                //if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString() && flag == 0)
                {
                    //当前数据库配置是旧库，则不需要记录变更日志
                    if (BaseSiteContext.CurrentNoThrow?.CurrentDbConfig?.DbConfig?.FromFxDbConfig == 0)
                    {
                        if (IsWriteDataChangeLogByNotMigrateUser() == false)
                        {
                            var infos = logs.Select(m =>
                                $"{m.SourceFxUserId}|{m.SourceShopId}|{m.TableTypeName}|{m.RelationKey}").ToList();
                            Log.Debug(
                                () =>
                                    $"当前用户({BaseSiteContext.CurrentNoThrow?.CurrentFxUserId})数据库配置是旧库，则不记录数据变更日志，相关信息：{string.Join(",", infos)}",
                                $"AddDataChangeLog_Old_{DateTime.Now:yyyy-MM-dd}.log");
                            return true;
                        }
                    }
                }
                else
                {
                    try
                    {
                        //异常处理，当配置库异常时会导致日志丢失，此处异常不抛出，继续后续流程
                        var tempLogs = TryOneTime(() => { return GetNeedAddLogs(logs, flag); }, isThrowEx: true);
                        if (tempLogs == null || !tempLogs.Any())
                            return true;
                        logs = tempLogs;
                    }
                    catch (Exception ex)
                    {
                        //发生异常，需继续往下处理，不能跳过
                    }
                }

                logs = logs.Where(a => a.SourceFxUserId > 0).ToList();

                if (!logs.Any())
                {
                    return true;
                }
                //内部已经有异常处理
                return DataChangeLogRepositoryFactory.GetDataChangeLogRepository().Add(logs);
            }
            catch (Exception ex)
            {
                exception = ex;
                //重试后还是异常，则保存至备用库
                DataChangeLogRepositoryFactory.GetDataChangeLogRepository().AddToBackupDbExceptionRetry(logs);
                //本地日志
                Log.WriteError($"批量写入变更日志时异常：{ex} ==> {logs?.ToJson()}", "AddDataChangeLogError.log");
            }
            finally
            {
                DataChangeLogRepositoryFactory.GetDataChangeLogRepository()
                    .WriteDataChangeLogLog(logs, exception != null, exception?.Message);
            }

            return true;
        }

        /// <summary>
        /// 根据迁移状态判断是否需要添加
        /// </summary>
        /// <param name="flag"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool IsAdd(int flag, int fxUserId)
        {
            var migrateTask = _fxDbConfigRepository.GetFxDataMigrateTaskByFxUserId(fxUserId, new List<string> { "FxUserId", "MigrateStatus" }, true);
            if (migrateTask == null)
            {
                if(flag == 0)
                //if (flag == 0 || CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                    return false;
                else
                    migrateTask = new FxDataMigrateTask() { MigrateStatus = MigrateStatus.Pending };
            }
            if (flag == 0)
            {
                if (migrateTask.MigrateStatus == MigrateStatus.Pending)
                    return false;

                //精选平台，已完成的也不需要添加日志
                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString() && migrateTask.MigrateStatus == MigrateStatus.Finished)
                    return false;
            }
            else
            {
                if (migrateTask.MigrateStatus == MigrateStatus.Pending || migrateTask.MigrateStatus == MigrateStatus.Finished)
                {
                    //校验关联的厂家，只要有一家迁移中就返回true，京东暂时不支持分库
                    if (CustomerConfig.CloudPlatformType != CloudPlatformType.Jingdong.ToString())
                    //if (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString())
                    {
                        try
                        {
                            var dict = CheckSupplierIsMigrateing(new List<int> { fxUserId });
                            if (dict.ContainsKey(fxUserId) == false)
                                return false;

                            return dict[fxUserId];
                        }
                        catch (Exception)
                        {
                            //发生异常时，返回true，需继续将日志写到日志库，防止日志丢失
                            return true;
                        }

                    }
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 根据迁移状态获取需要添加的Logs
        /// </summary>
        /// <param name="logs"></param>
        /// <param name="flag"></param>
        /// <returns></returns>
        public List<DataChangeLog> GetNeedAddLogs(List<DataChangeLog> logs, int flag)
        {
            //其他平台
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
            {
                return new List<DataChangeLog>();
            }
            //精选平台，过滤店铺类型，只取TouTiao类型的店铺
            //if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            //{
            //    var sourceShopIds = logs.Select(a => a.SourceShopId).Distinct().ToList();
            //    var shops = new ShopRepository().GetListByShopIds(sourceShopIds);
            //    var needShopIds = shops.Where(a => a.PlatformType == PlatformType.TouTiao.ToString()).Select(a => a.Id).ToList();
            //    if (needShopIds == null || needShopIds.Any() == false)
            //    {
            //        return new List<DataChangeLog>();
            //    }
            //    logs = logs.Where(a => needShopIds.Contains(a.SourceShopId)).ToList();
            //}

            //其他平台
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
            {
                return new List<DataChangeLog>();
            }

            var migrateingFxUserIds = new List<int>();
            var sourceFxUserIds = logs.Select(a => a.SourceFxUserId).Distinct().ToList();
            var migrateTasks = _fxDbConfigRepository.GetFxDataMigrateTaskByFxUserId(sourceFxUserIds, new List<string> { "FxUserId", "MigrateStatus" }, true);

            if (flag == 0)
            {
                //精选平台 && 状态已完成 不需要添加日志
                if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
                    migrateingFxUserIds = migrateTasks.Where(a => a.MigrateStatus != MigrateStatus.Pending && a.MigrateStatus != MigrateStatus.Finished).Select(a => a.FxUserId).ToList();
                else
                    migrateingFxUserIds = migrateTasks.Where(a => a.MigrateStatus != MigrateStatus.Pending).Select(a => a.FxUserId).ToList();

            }
            else
            {
                migrateingFxUserIds = migrateTasks.Where(a => a.MigrateStatus != MigrateStatus.Pending && a.MigrateStatus != MigrateStatus.Finished).Select(a => a.FxUserId).ToList();

                //追加校验关联的厂家，只要有一家迁移中就返回true，京东暂时不支持分库
                if (CustomerConfig.CloudPlatformType != CloudPlatformType.Jingdong.ToString())
                //if (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString())
                {
                    var needCheckFxUserIds = sourceFxUserIds.Where(a => !migrateingFxUserIds.Contains(a)).ToList();
                    if (needCheckFxUserIds.Any())
                    {
                        var dict = CheckSupplierIsMigrateing(needCheckFxUserIds, migrateingFxUserIds);
                        var needAddFxUserIds = dict.ToList().Where(a => a.Value == true).Select(a => a.Key).ToList();
                        if (needAddFxUserIds != null && needAddFxUserIds.Any())
                            migrateingFxUserIds.AddRange(needAddFxUserIds);
                    }
                }
            }

            if (migrateingFxUserIds != null && migrateingFxUserIds.Any())
            {
                Log.Debug(() => $"保存数据变更日志检测迁移用户，相关信息：{migrateingFxUserIds.ToJson()}",
                    $"GetNeedAddLogs_{DateTime.Now:yyyy-MM-dd}.log");
                logs = logs.Where(a => migrateingFxUserIds.Contains(a.SourceFxUserId)).ToList();
            }
            else
                logs = new List<DataChangeLog>();

            return logs;
        }

        /// <summary>
        /// 用户关联的厂家是否有迁移中的任务
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="migrateingFxUserIds">已确定是迁移中的用户</param>
        /// <returns></returns>
        private Dictionary<int, bool> CheckSupplierIsMigrateing(List<int> fxUserIds, List<int> migrateingFxUserIds = null)
        {
            Dictionary<int, bool> dicMigrateStatus = new Dictionary<int, bool>();
            if (fxUserIds == null || fxUserIds.Count == 0)
                return dicMigrateStatus;

            //所有关联的厂家Id
            var pathFlowWithFxUserIds = new PathFlowRepository().GetPathFlowWithFxUserId(fxUserIds);
            if (pathFlowWithFxUserIds == null || pathFlowWithFxUserIds.Count == 0)
                return dicMigrateStatus;
            var supplierFxUserIds = pathFlowWithFxUserIds.Select(a => a.SourceFxUserId).Distinct().ToList();
            var migrateTasks = _fxDbConfigRepository.GetFxDataMigrateTaskByFxUserId(supplierFxUserIds, new List<string> { "FxUserId", "MigrateStatus" }, true);

            fxUserIds.ForEach(fxUserId =>
            {
                var curSupplierFxUserIds = pathFlowWithFxUserIds.Where(a => a.NodeFxUserId == fxUserId).Select(a => a.SourceFxUserId).Distinct().ToList();
                var isMigrateing = migrateTasks.Where(a => curSupplierFxUserIds.Contains(a.FxUserId)).Any(a => a.MigrateStatus != MigrateStatus.Pending && a.MigrateStatus != MigrateStatus.Finished);

                if (isMigrateing == false && migrateingFxUserIds != null)
                {
                    isMigrateing = curSupplierFxUserIds.Any(a => migrateingFxUserIds.Contains(a));
                }

                if (dicMigrateStatus.ContainsKey(fxUserId) == false)
                {
                    dicMigrateStatus.Add(fxUserId, isMigrateing);
                }
            });
            return dicMigrateStatus;
        }
    }
}

