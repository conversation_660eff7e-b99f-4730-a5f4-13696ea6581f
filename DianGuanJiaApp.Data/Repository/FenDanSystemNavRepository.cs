using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Data.Repository
{
    public partial class FenDanSystemNavRepository : BaseRepository<FenDanSystemNav>
    {
        public FenDanSystemNavRepository() : base(Utility.CustomerConfig.ConfigureDbConnectionString) { }

        /// <summary>
        /// 导航菜单
        /// </summary>
        private static readonly string _systemNavKey = $"/System/Config/Fendan/NewFenDanSystemNav";
        private static readonly string _systemOldNavKey = $"/System/Config/Fendan/OldFenDanSystemNav";
        private static readonly string _enableCacheKey = $"/System/Config/Fendan/FenDanSystemNav/EnableCache";


        #region 配置是否使用Redis缓存
        /// <summary>
        /// 有效时间
        /// </summary>
        private int _secondTimeout = TimeSpan.FromDays(1).TotalSeconds.ToInt();

        /// <summary>
        /// Redis状态是否初始化完成
        /// </summary>
        private bool IsUseRedisCache
        {
            get
            {
                return RedisPool.IsInit;
            }
        }
        #endregion


        /// <summary>
        /// 缓存开关 0-不启用缓存 1-启用缓存 2-清空所有缓存，并启用缓存，3-清空所有缓存，且不启用缓存
        /// </summary>
        public int EnableCacheValue
        {
            get
            {
                if (IsUseRedisCache)
                    return 1;// RedisHelper.Get(_enableCacheKey).ToInt();
                else
                    return 0;
            }
        }

        /// <summary>
        /// 0-不启用缓存 1-启用缓存 2-清空所有缓存，并启用缓存，3-清空所有缓存，且不启用缓存
        /// </summary>
        private bool IsEnableCache(string navKey)
        {
            switch (EnableCacheValue)
            {
                case 3:
                    MemoryCacheHelper.RemoveCacheItem(navKey);
                    RedisHelper.Del(navKey);
                    RedisHelper.Set(_enableCacheKey, 0);
                    return false;
                case 2:
                    MemoryCacheHelper.RemoveCacheItem(navKey);
                    RedisHelper.Del(navKey);
                    RedisHelper.Set(_enableCacheKey, 1);
                    return true;
                case 1:
                    return true;
                case 0:
                default:
                    return false;
            }
        }


        /// <summary>
        /// 获取菜单 - 新版
        /// </summary>
        /// <returns></returns>
        public List<FenDanSystemNav> GetList(string version)
        {
            var navKey = _systemNavKey + $"/{version}";
            var cacheNavs = new List<FenDanSystemNav>();
            if (IsEnableCache(navKey))
                cacheNavs = MemoryCacheHelper.GetOrAddCacheItem(navKey, () => DbGetList(version, navKey, true), null, TimeSpan.FromMinutes(15));
            else
                cacheNavs = DbGetList(version, null, true);
            // 深拷贝一份 ，否则后续对菜单项列表的处理会应用到缓存中
            return CommUtls.DeepClone(cacheNavs);
        }

        public List<FenDanSystemNav> GetDbOldList(List<int> ids)
        {
            var db = this.DbConnection;
            var sql = "SELECT Id,Title,ClassName,ParentId AS ParentNavCode FROM dbo.FenDanSystemNav (NOLOCK) WHERE Id IN @ids AND IsDelete IS NULL";
            var result = db.Query<FenDanSystemNav>(sql, new { ids }).ToList();
            return result;
        }
        /// <summary>
        /// 从配置库读取
        /// </summary>
        /// <returns></returns>
        private List<FenDanSystemNav> DbGetList(string version = "V1", string redisKey = null, bool isNew = false)
        {
            var navJson = string.Empty;
            if (redisKey.IsNotNullOrEmpty() && IsUseRedisCache && CustomerConfig.IsLocalDbDebug == false)
            {
                navJson = RedisHelper.Get(redisKey);
                if (!string.IsNullOrWhiteSpace(navJson))
                {
                    var newNavs = navJson.ToObject<List<FenDanSystemNav>>();
                    return newNavs;
                }
            }

            // FenDanSystemNavNew 还存在Version字段时 过渡时期先指定Version，之后如果不需要FenDanSystemNavNew.Version，就去掉这里的筛选
            var isNewWhere = " AND r.IsNew = 1";
            var param = new DynamicParameters();
            var navVersion = "Basic_new";
            if (BaseSiteContext.Current.IsOnlyListingUser)
            {
                isNewWhere = " AND r.IsNew IS NULL";
                navVersion = version;
            }

            param.Add("@version", version);
            param.Add("@navVersion", navVersion);


            var sql = $@"SELECT n.*,r.PermissionTag FROM dbo.FenDanSystemNavNew n (NOLOCK)
                         INNER JOIN dbo.SystemNavVersionRelation r (NOLOCK) ON n.NavCode = r.NavCode
                         WHERE n.Version = @navVersion AND r.Version = @version {isNewWhere} AND (r.IsDelete = 0 OR r.IsDelete IS NULL) AND (n.IsDelete = 0 OR n.IsDelete IS NULL)";
            var result = DbConnection.Query<FenDanSystemNav>(sql,param).ToList();

            if (redisKey.IsNotNullOrEmpty() && IsUseRedisCache && CustomerConfig.IsLocalDbDebug == false)
            {
                RedisHelper.Set(redisKey, result.ToJson(), 60 * 60 * 12);
            }

            return result;
        }
    }
}
