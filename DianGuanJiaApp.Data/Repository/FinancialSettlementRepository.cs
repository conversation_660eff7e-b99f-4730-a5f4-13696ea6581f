using DianGuanJiaApp.Data.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Dapper;
using System.Data;
using DianGuanJiaApp.Utility.Extension;
using System.Diagnostics;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Model;

namespace DianGuanJiaApp.Data.Repository
{
    public class FinancialSettlementRepository : BaseRepository<ProductSettlementPrice>
    {
        private readonly SubProductSettlementPriceRepository subProductSettlementPriceRepository;
        public FinancialSettlementRepository() {
            subProductSettlementPriceRepository = new SubProductSettlementPriceRepository();
        }
        public FinancialSettlementRepository(string connectionString) : base(connectionString) { 
            subProductSettlementPriceRepository = new SubProductSettlementPriceRepository(connectionString);
        }

        public int SetProductSettlementPrice(List<ProductSettlementPrice> priceModels,int changeType = (int)ChangeType.Default)
        {
            return SetProductSettlementPriceV2(priceModels, changeType);
            /*
            if (priceModels.IsNullOrEmptyList())
                return 0;
            Stopwatch sw1 = new Stopwatch();
            sw1.Start();
            var batchSql = new StringBuilder();
            var updateModels = priceModels.Where(x => x.Id > 0).GroupBy(t => t.UniqueKey).Select(t => t.First()).ToList();
            var addModels = priceModels.Where(x => x.Id < 1).GroupBy(t => t.UniqueKey).Select(t => t.First()).ToList();
            batchSql.AppendLine("DECLARE @currentPrice DECIMAL(14,2)");
            var items = string.Join(",", updateModels.Select(it => it.UniqueKey).ToList());
            if (updateModels != null && updateModels.Count > 0)
            {
                foreach (var item in updateModels)
                {
                    batchSql.AppendLine($"SELECT @currentPrice=Price FROM P_ProductSettlementPrice WITH(NOLOCK) WHERE UniqueKey='{item.UniqueKey}'");
                    batchSql.AppendLine($"UPDATE P_ProductSettlementPrice SET Price={item.Price},UpdateTime=GETDATE() WHERE UniqueKey='{item.UniqueKey}'");
                    batchSql.AppendLine($@"IF NOT EXISTS(SELECT Id FROM P_ProductSettlementRecord WHERE Status=0 AND ParentUniqueKey='{item.UniqueKey}' AND Price={item.Price})
                                            BEGIN
                                            UPDATE P_ProductSettlementRecord SET Status=-1 WHERE PriceId= {item.Id}
                                            INSERT INTO dbo.P_ProductSettlementRecord
                                            (
                                                PriceId,
                                                CreateTime,
                                                UpdateTime,
                                                Status,
                                                Price,
                                                Price_Old,
                                                FxUserId,
                                                ParentUniqueKey,
                                                UniqueKey,
                                                ChangeType,
                                                SkuName,
                                                SubFxUserId
                                            )
                                            VALUES
                                            (   {item.Id},         -- PriceId - int
                                                GETDATE(), -- CreateTime - datetime
                                                GETDATE(), -- UpdateTime - datetime
                                                0,         -- Status - int
                                                {item.Price},      -- Price - decimal(14, 2)
                                                @currentPrice,      -- Price_Old - decimal(14, 2)
                                                {item.CreateUser},          -- FxUserId - int
                                                '{item.UniqueKey}',    -- ParentUniqueKey 
                                                '{Guid.NewGuid().ToString().ToShortMd5()}',    -- UniqueKey 
                                                '{changeType}',  -- ChangeType
                                                '{item.SkuName}',  -- SkuName
                                                 {BaseSiteContext.CurrentNoThrow.SubFxUserId}
                                                )
                                            END");
                }
            }
            if (addModels != null && addModels.Count > 0)
            {
                addModels.GroupBy(t => new { t.ProductSkuCode ,t.SettlementType }).ToList().ForEach(t =>{
                    var item = t.First();
                    batchSql.AppendLine($@"INSERT INTO dbo.P_ProductSettlementPrice
                                            (
                                                CreateTime,
                                                UpdateTime,
                                                ProductCode,
                                                ProductSkuCode,
                                                SettlementType,
                                                PlatformType,
                                                CreateUser,
                                                FxUserId,
                                                Price,
                                                UniqueKey
                                            )
                                            VALUES
                                            (   GETDATE(), -- CreateTime - datetime
                                                GETDATE(), -- UpdateTime - datetime
                                                '{item.ProductCode}',       -- ProductCode - nvarchar(32)
                                                '{item.ProductSkuCode}',       -- ProductSkuCode - nvarchar(32)
                                                {item.SettlementType},         -- SettlementType - int
                                                '{item.PlatformType ?? string.Empty}',       -- PlatformType - nvarchar(32)
                                                {item.CreateUser},         -- CreateUser - int
                                                {item.FxUserId},         -- FxUserId - int
                                                {item.Price},       -- Price - decimal(10, 2)
                                                '{item.UniqueKey}'    -- UniqueKey 
                                                )");
                    batchSql.AppendLine($@"INSERT INTO dbo.P_ProductSettlementRecord
                                            (
                                                PriceId,
                                                CreateTime,
                                                UpdateTime,
                                                Status,
                                                Price,
                                                Price_Old,
                                                FxUserId,
                                                ParentUniqueKey,
                                                UniqueKey,
                                                ChangeType,
                                                SkuName,
                                                SubFxUserId
                                            )
                                            VALUES
                                            (   IDENT_CURRENT('P_ProductSettlementPrice'),         -- PriceId - int
                                                GETDATE(), -- CreateTime - datetime
                                                GETDATE(), -- UpdateTime - datetime
                                                0,         -- Status - int
                                                {item.Price},      -- Price - decimal(14, 2)
                                                0,      -- Price_Old - decimal(14, 2)
                                                {item.CreateUser},          -- FxUserId - int
                                                '{item.UniqueKey}',    -- ParentUniqueKey 
                                                '{Guid.NewGuid().ToString().ToShortMd5()}',    -- UniqueKey 
                                                '{changeType}',  -- ChangeType
                                                '{item.SkuName}',  -- SkuName
                                                {BaseSiteContext.CurrentNoThrow.SubFxUserId}
                                                )");
                });
            }
            var db = this.DbConnection;
            if (db.State != ConnectionState.Open)
                db.Open();
            var tran = db.BeginTransaction();
            var result = 0;
            try
            {
                Log.Debug($"batchSql=》{batchSql.ToString()}", "SetProductSettlementPrice.txt");
                result = db.Execute(batchSql.ToString(), null, tran);
                //设置sku结算价
                Stopwatch sw = new Stopwatch();
                sw.Start();
                subProductSettlementPriceRepository.SetSubProductSettlementPrice(priceModels);
                sw.Stop();
                Log.Debug(() => $"设置子结算价耗时：{sw.ElapsedMilliseconds}毫秒");
                tran.Commit();
            }
            catch (Exception ex)
            {
                Utility.Log.WriteError($"设置结算价异常：sql语句{batchSql.ToString()}、异常信息{ex}");
                tran.Rollback();
            }
            sw1.Stop();
            Log.Debug(() => $"设置结算价耗时：{sw1.ElapsedMilliseconds}毫秒");
            return result;
            */
        }

        public int SetProductSettlementPriceV2(List<ProductSettlementPrice> priceModels, int changeType = (int)ChangeType.Default)
        {
            const string logName = "SetProductSettlementPriceV2.txt";
            if (priceModels.IsNullOrEmptyList()) return 0;
            Stopwatch stopwatch = Stopwatch.StartNew();
            var updateModels = priceModels.Where(x => x.Id > 0).GroupBy(t => t.UniqueKey).Select(t => t.First()).ToList();
            var addModels = priceModels.Where(x => x.Id < 1).GroupBy(t => t.UniqueKey).Select(t => t.First()).ToList();
            var result = 0;
            try
            {
                using (var sdb = DbConnection)
                {
                    if (updateModels?.Count > 0)
                    {
                        List<string> uniqueKeyList = updateModels.Select(a => a.UniqueKey).ToList();
                        var updateBatch = new BatchUpdater("P_ProductSettlementPrice");
                        foreach (var item in updateModels)
                        {
                            var keyDict = new Dictionary<string, string>();
                            keyDict.Add("UniqueKey", item.UniqueKey);
                            updateBatch.TryAdd(keyDict, "Price", item.Price.ToString());
                            updateBatch.TryAdd(keyDict, "UpdateTime", DateTime.Now.ToString());
                        }

                        // 1、更新结算表
                        BatchUpdateBySql(updateBatch);
                        
                        var uniqueKeys = string.Join(",", uniqueKeyList);
                        const string currentProductSelectSql = @"SELECT UniqueKey, Price FROM P_ProductSettlementRecord WITH(NOLOCK)
INNER JOIN FunStringToTable(@uniqueKeys,',') t on t.item = ParentUniqueKey WHERE Status=0;";
                        const string currentPricesSelectSql = @"SELECT CreateUser,ProductSkuCode,SettlementType,FxUserId, Price FROM P_ProductSettlementPrice WITH(NOLOCK)
INNER JOIN FunStringToTable(@uniqueKeys,',') t on t.item = UniqueKey;";

                        var currentProductSettlementRecord = sdb.Query<ProductSettlementRecord>(currentProductSelectSql,
                            new { uniqueKeys }).ToList();
                        var currentPrices = sdb.Query<ProductSettlementPrice>(currentPricesSelectSql,
                            new { uniqueKeys }).ToDictionary(x => x.UniqueKey, x => x.Price);

                        var notExistsRecord = new List<ProductSettlementRecord>();
                        var installExistsRecord = new List<ProductSettlementRecord>();
                        foreach (var updateitem in updateModels)
                        {
                            var findResult = currentProductSettlementRecord.Find(x => x.UniqueKey == updateitem.UniqueKey && x.Price == updateitem.Price);
                            if (findResult == null && !notExistsRecord.Any(it => it.Price == updateitem.Id))
                            {
                                findResult = new ProductSettlementRecord()
                                {
                                    Status = -1,
                                    PriceId = updateitem.Id // 更新时，要根据 PriceId字段，更新 P_ProductSettlementPrice 表
                                };
                                notExistsRecord.Add(findResult);

                                var addSettlementRecord = new ProductSettlementRecord();
                                addSettlementRecord.PriceId = updateitem.Id;
                                addSettlementRecord.CreateTime = DateTime.Now;
                                addSettlementRecord.UpdateTime = DateTime.Now;
                                addSettlementRecord.Status = 0;
                                addSettlementRecord.Price = updateitem.Price;
                                addSettlementRecord.Price_Old = currentPrices.ContainsKey(updateitem.UniqueKey) ? currentPrices[updateitem.UniqueKey] : 0;
                                addSettlementRecord.FxUserId = updateitem.CreateUser;
                                addSettlementRecord.ParentUniqueKey = updateitem.UniqueKey;
                                addSettlementRecord.UniqueKey = Guid.NewGuid().ToString().ToShortMd5();
                                addSettlementRecord.ChangeType = changeType;
                                addSettlementRecord.SkuName = updateitem.SkuName;
                                addSettlementRecord.SubFxUserId = BaseSiteContext.CurrentNoThrow.SubFxUserId;
                                installExistsRecord.Add(addSettlementRecord);
                            }
                        }

                        var updateModel = new BatchUpdater("P_ProductSettlementRecord");
                        notExistsRecord.ForEach(t =>
                        {
                            var whereCondition = new Dictionary<string, string>();
                            whereCondition.Add("PriceId", t.PriceId.ToString());
                            updateModel.TryAdd(whereCondition, "Status", t.Status.ToString());
                        });

                        // var usql = updateModel.GetUpdateSql();
                        // 2、更新结算记录表,  更新存在的数据的结算价
                        BatchUpdateBySql(updateModel);

                        // 3、插入新的历史记录
                        // sdb.BulkInsert(installExistsRecord);
                        BulkWrite(installExistsRecord, "P_ProductSettlementRecord");
                    }
                    if (addModels?.Count > 0)
                    {
                        var groupedAddModels = addModels
                            .Where(x => string.IsNullOrEmpty(x.ProductCode) == false)
                            .GroupBy(t => new { t.ProductSkuCode, t.SettlementType })
                            .Select(t => t.First())
                            .ToList();

                        // 先插入主表数据
                        var now = DateTime.Now;
                        foreach (var model in groupedAddModels)
                        {
                            model.PlatformType = model.PlatformType ?? string.Empty;
                            model.CreateTime = now;
                            model.UpdateTime = now;
                        }

                        // sdb.BulkInsert(groupedAddModels);
                        BulkWrite(groupedAddModels, "P_ProductSettlementPrice");

                        // 获取新插入数据的ID
                        var uniqueKeys = groupedAddModels.Select(x => x.UniqueKey).ToList();
                        var insertedRecords = sdb.Query<ProductSettlementPrice>(
                            "SELECT Id, CreateUser,ProductSkuCode,SettlementType,FxUserId FROM P_ProductSettlementPrice WHERE UniqueKey IN @uniqueKeys",
                            new { uniqueKeys }).ToDictionary(x => x.UniqueKey, x => x.Id);

                        // 6. 添加新数据的历史记录
                        var newRecords = groupedAddModels
                            .Where(x => insertedRecords.ContainsKey(x.UniqueKey))
                            .Select(item => new ProductSettlementRecord
                            {
                                PriceId = insertedRecords[item.UniqueKey],
                                CreateTime = now,
                                UpdateTime = now,
                                Status = 0,
                                Price = item.Price,
                                Price_Old = 0,
                                FxUserId = item.CreateUser,
                                ParentUniqueKey = item.UniqueKey,
                                UniqueKey = Guid.NewGuid().ToString().ToShortMd5(),
                                ChangeType = changeType,
                                SkuName = item.SkuName,
                                SubFxUserId = BaseSiteContext.CurrentNoThrow.SubFxUserId
                            }).ToList();

                        if (newRecords.Any())
                            BulkWrite(newRecords, "P_ProductSettlementRecord"); // sdb.BulkInsert(newRecords);
                    }

                    // 设置sku结算价
                    Stopwatch sw = Stopwatch.StartNew();
                    subProductSettlementPriceRepository.SetSubProductSettlementPrice(priceModels);
                    sw.Stop();
                    Log.Debug(() => $"设置子结算价耗时：{sw.ElapsedMilliseconds}毫秒", logName);
                }
                result = 1;
            }
            catch (Exception ex)
            {
                result = 0;
                Log.WriteError($"设置结算价异常：异常信息{ex}", logName);
            }

            stopwatch.Stop();
            Log.Debug(() => $"设置结算价耗时：{stopwatch.ElapsedMilliseconds}毫秒", logName);
            return result;
        }

        /// <summary>
        /// 批量设置结算价
        /// </summary>
        /// <param name="priceModels"></param>
        /// <param name="fxUserId"></param>
        /// <param name="changeType">结算价历史记录变更类型</param>
        /// <returns></returns>
        public int SetProductSettlementPrice(List<ProductSettlementPrice> priceModels, int fxUserId,int changeType)
        {
            //设置结算价
            return SetProductSettlementPrice(priceModels,changeType);
        }

        /// <summary>
        /// 获取结算价格信息（按唯一键列表）
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetListByUniqueKeys(List<string> keys)
        {
            //SQL脚本
            const string sql =
                @"SELECT Id, CreateUser, ProductCode, ProductSkuCode, SettlementType, FxUserId, Price 
FROM P_ProductSettlementPrice(NOLOCK) 
INNER JOIN dbo.FunStringToTable (@keys,',') ON item =UniqueKey";
            //查询
            return DbConnection.Query<ProductSettlementPrice>(sql, new { keys = string.Join(",", keys) }).ToList();
        }

        /// <summary>
        /// 获取结算价格信息（按Id）
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetListById(List<int> ids,int fxUserId)
        {
            if (ids.IsNullOrEmpty())
                return new List<ProductSettlementPrice>();
            //SQL脚本
            const string sql =
                "SELECT Id,ProductCode,CreateUser,FxUserId, ProductSkuCode,SettlementType, PlatformType,Price FROM P_ProductSettlementPrice(NOLOCK) WHERE Id IN @ids AND (CreateUser = @fxUserId OR FxUserId = @fxUserId)";
            //查询
            return DbConnection.Query<ProductSettlementPrice>(sql, new { ids ,fxUserId}).ToList();
        }

        /// <summary>
        /// 使用UniqueKey批量删除
        /// </summary>
        /// <param name="uniqueKeys"></param>
        public int BatchDeleteByKeys(List<string> uniqueKeys)
        {
            var sql = @"WHERE UniqueKey IN @keys";
            int batchSize = 500;
            for (int i = 0; i < uniqueKeys.Count; i += batchSize)
            {
                var keys = uniqueKeys.Skip(i).Take(batchSize).ToList();
                base.Delete(sql, new { keys});
            }
            return uniqueKeys.Count;
        }

        /// <summary>
        /// 获取自己的成本价
        /// </summary>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetCostPriceListBySkuCodes(int CreateUserId, List<string> skuCodes)
        {
            var result = new List<ProductSettlementPrice>();
            int batchSize = 500; // 每个批次的大小
            for (int i = 0; i < skuCodes.Count; i += batchSize)
            {
                var batchCodes = skuCodes.Skip(i).Take(batchSize).ToList();
                var sql = @"
            SELECT Id, CreateUser, ProductSkuCode, ProductCode, SettlementType, PlatformType, FxUserId, Price
            FROM P_ProductSettlementPrice(NOLOCK) 
            INNER JOIN FunStringToTable(@batchCodes,',') t on t.item = ProductSkuCode
            WHERE SettlementType = 3 AND CreateUser = @CreateUserId AND FxUserId = 0";

                var batchResult = DbConnection.Query<ProductSettlementPrice>(sql, new { CreateUserId, batchCodes = string.Join(",", batchCodes) }).ToList();
                result.AddRange(batchResult);
            }

            return result;
        }

        /// <summary>
        /// 查询结算价列表（用当前用户、结算价类型、SkuCode）
        /// </summary>
        /// <param name="CreateUserId"></param>
        /// <param name=""></param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetSettlementPriceListBySkuCodes(int CreateUserId,int SettlementType,List<string> skuCodes)
        {
            var result = new List<ProductSettlementPrice>();
            int batchSize = 500; // 每个批次的大小
            for (int i = 0; i < skuCodes.Count; i += batchSize)
            {
                var batchCodes = skuCodes.Skip(i).Take(batchSize).ToList();
                var sql = @"
            SELECT Id, CreateUser, ProductSkuCode, ProductCode, SettlementType, PlatformType, FxUserId, Price
            FROM P_ProductSettlementPrice(NOLOCK) 
            INNER JOIN FunStringToTable(@batchCodes,',') t on t.item = ProductSkuCode
            WHERE SettlementType = @SettlementType AND CreateUser = @CreateUserId";

                var batchResult = DbConnection.Query<ProductSettlementPrice>(sql, new { CreateUserId, SettlementType, batchCodes = string.Join(",", batchCodes) }).ToList();
                result.AddRange(batchResult);
            }
            return result;
        }

        /// <summary>
        /// 查询对方的结算价列表（用当前用户、SkuCode）
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="skuCodes"></param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetSettlementPriceListBySkuCodes(int fxUserId,List<string> skuCodes)
        {
            var result = new List<ProductSettlementPrice>();
            int batchSize = 500; // 每个批次的大小
            for (int i = 0; i < skuCodes.Count; i += batchSize)
            {
                var batchCodes = skuCodes.Skip(i).Take(batchSize).ToList();
                var sql = @"
            SELECT Id, CreateUser, ProductSkuCode, ProductCode, SettlementType, PlatformType, FxUserId, Price
            FROM P_ProductSettlementPrice(NOLOCK) 
            INNER JOIN FunStringToTable(@batchCodes,',') t on t.item = ProductSkuCode
            WHERE FxUserId = @fxUserId";

                var batchResult = DbConnection.Query<ProductSettlementPrice>(sql, new { fxUserId, batchCodes = string.Join(",", batchCodes) }).ToList();
                result.AddRange(batchResult);
            }
            return result;
        }

        /// <summary>
        /// 查询我的结算价列表（用当前用户、SkuCode）包含对商家、对厂家、成本价
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="skuCodes"></param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetAllPriceListBySkuCodes(int fxUserId, List<string> skuCodes,List<string> fields=null)
        {
            var fieldsSqlWhere = " Id, CreateUser, ProductSkuCode, ProductCode, SettlementType, PlatformType, FxUserId, Price ";
            if (!fields.IsNullOrEmptyList())
            {
                fieldsSqlWhere = string.Join(",", fields);
            }
            var result = new List<ProductSettlementPrice>();
            int batchSize = 500; // 每个批次的大小
            for (int i = 0; i < skuCodes.Count; i += batchSize)
            {
                var batchCodes = skuCodes.Skip(i).Take(batchSize).ToList();
                var sql = $@"
            SELECT {fieldsSqlWhere}
            FROM P_ProductSettlementPrice(NOLOCK) 
            INNER JOIN FunStringToTable(@batchCodes,',') t on t.item = ProductSkuCode
            WHERE CreateUser = @fxUserId";

                var batchResult = DbConnection.Query<ProductSettlementPrice>(sql, new { fxUserId, batchCodes = string.Join(",", batchCodes) }).ToList();
                result.AddRange(batchResult);
            }
            return result;
        }


        /// <summary>
        /// 获取结算价格信息（按批量用户和对象、skuCodes）
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="createUserIds"></param>
        /// <param name="skuCodes"></param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetSettlementPriceListBySkuCodes(List<int> fxUserIds, List<int> createUserIds, List<string> skuCodes)
        {
            if (fxUserIds.IsNullOrEmpty() && createUserIds.IsNullOrEmpty())
                return new List<ProductSettlementPrice>();

            var result = new List<ProductSettlementPrice>();
            int batchSize = 500; // 每个批次的大小
            for (int i = 0; i < skuCodes.Count; i += batchSize)
            {
                var batchCodes = skuCodes.Skip(i).Take(batchSize).ToList();
                var parameters = new DynamicParameters();
                parameters.Add("@fxUserIds", fxUserIds);
                parameters.Add("@createUserIds", createUserIds);
                parameters.Add("@batchCodes", string.Join(",",batchCodes));

                var sql = @"
            SELECT Id, CreateUser, ProductSkuCode, ProductCode, SettlementType, PlatformType, FxUserId, Price, UniqueKey DbUniqueKey
            FROM P_ProductSettlementPrice(NOLOCK) 
            INNER JOIN FunStringToTable(@batchCodes,',') t on t.item = ProductSkuCode
            WHERE FxUserId IN @fxUserIds AND CreateUser IN @createUserIds";

                var batchResult = DbConnection.Query<ProductSettlementPrice>(sql, parameters).ToList();
                result.AddRange(batchResult);
            }

            return result;
        }

        /// <summary>
        /// 结算价类型
        /// </summary>
        public enum SettlementType
        {
            /// <summary>
            /// 商家
            /// </summary>
            Merchant = 1,
            /// <summary>
            /// 厂家
            /// </summary>
            Manufacturer = 2,
            // <summary>
            /// 成本价
            /// </summary>
            CostPrice = 3,
            // <summary>
            /// 默认采购价
            /// </summary>
            DefaultMerchant = 4
        }


        /// <summary>
        /// 结算价历史记录变更类型
        /// </summary>
        public enum ChangeType
        {
            /// <summary>
            /// 默认 手动设置
            /// </summary>
            Default = 1,
            /// <summary>
            /// 同步对方
            /// </summary>
            Sync = 2,
            /// <summary>
            /// 同步基础商品
            /// </summary>
            SyncBaseProduct = 3,
            /// <summary>
            /// 基础商品解绑关联平台商品，重置结算价
            /// </summary>
            UnBindBaseSku = 4,
            /// <summary>
            /// 等级分销价换算结算价
            /// </summary>
            MemberCalculate = 5,
            /// <summary>
            /// 分销等级变更
            /// </summary>
            MemberChange = 6

        }

        /// <summary>
        /// 获取结算价格信
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="createUserIds"></param>
        /// <param name="skuCodes"></param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetSettlementPriceList(List<int> fxUserIds, List<int> createUserIds, List<string> skuCodes)
        {
            if (fxUserIds.IsNullOrEmpty() && createUserIds.IsNullOrEmpty())
                return new List<ProductSettlementPrice>();

            var result = new List<ProductSettlementPrice>();
            int batchSize = 500; // 每个批次的大小
            for (int i = 0; i < skuCodes.Count; i += batchSize)
            {
                var batchCodes = skuCodes.Skip(i).Take(batchSize).ToList();
                var parameters = new DynamicParameters();
                parameters.Add("@fxUserIds", fxUserIds);
                parameters.Add("@createUserIds", createUserIds);
                parameters.Add("@batchCodes", string.Join(",", batchCodes));

                var sql = @"
            SELECT Id, CreateUser, ProductSkuCode, ProductCode, SettlementType, PlatformType, FxUserId, Price
            FROM P_ProductSettlementPrice(NOLOCK) 
            INNER JOIN FunStringToTable(@batchCodes,',') t on t.item = ProductSkuCode
            WHERE FxUserId IN @fxUserIds AND CreateUser IN @createUserIds";

                var batchResult = DbConnection.Query<ProductSettlementPrice>(sql, parameters).ToList();
                result.AddRange(batchResult);
            }

            return result;
        }
    }
}
