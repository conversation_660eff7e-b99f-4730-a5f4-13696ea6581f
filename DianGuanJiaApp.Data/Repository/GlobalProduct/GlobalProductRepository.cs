using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model.CrossBorder;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Z.Dapper.Plus;
using static Dapper.SqlMapper;

namespace DianGuanJiaApp.Data.Repository
{
    /// <summary>
    /// 全球商品仓储层
    /// </summary>
    public class GlobalProductRepository : BaseRepository<GlobalProduct>
    {

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public GridReader GetPageList(GlobalProductSearchModel query)
        {
            var strFields = "gp.UniqueCode AS UniqueCode,gp.PlatformId,gp.Title,gp.ShopId,gp.ProductSource,gp.ProductSourceUrl,gp.ProductSourceUid,gp.FxUserId, gp.CreateFrom, gp.Description,gp.PlatformCreatetime,gp.PlatformUpdatetime, gp.BrandID,gp.CategoryId,gp.GlobalSellerId,gp.ImageUrl,gp.PublishedSite, gp.CreateTime,gp.UpdateTime,gp.PlatformType,gp.ProductStatus,ext.Id AS ExtId,ext.Video,ext.PackageDimensions,ext.PackageWeight,ext.ImageJson,ext.Certification,ext.SizeChart,ext.Manufacturer,ext.Region,ext.CategoryJson,ext.AttributeTypesJson";
            var parameters = new DynamicParameters();
            var whereStr = "1=1";
            string otherWhereStr = "1=1";
            //分销用户,这个必须要
            if (query.FxUserId >= 0)
            {
                whereStr += "AND gp.FxUserId=@FxUserId";
                otherWhereStr += "AND gp.FxUserId=@FxUserId";
                parameters.Add("FxUserId", query.FxUserId);
            }
            if (!string.IsNullOrEmpty(query.ShopIds))
            {
                whereStr += " AND gp.ShopId IN @ShopIds";
                parameters.Add("ShopIds", query.ShopIds.Split(','));
            }
            if (!string.IsNullOrEmpty(query.GlobalProductId))
            {
                whereStr += " AND gp.PlatformId=@PlatformId";
                parameters.Add("PlatformId", query.GlobalProductId);
            }
            if (!string.IsNullOrEmpty(query.CategoryId))
            {
                whereStr += " AND gp.CategoryId=@CategoryId";
                parameters.Add("CategoryId", query.CategoryId);
            }
            if (!string.IsNullOrEmpty(query.Title))
            {
                whereStr += " AND gp.Title LIKE @Title";
                parameters.Add("Title", $"%{query.Title}%");
            }
            if (!string.IsNullOrEmpty(query.CreateStartTime))
            {
                whereStr += " AND gp.PlatformCreatetime>=@CreateStartTime";
                parameters.Add("CreateStartTime", query.CreateStartTime);
            }
            if (!string.IsNullOrEmpty(query.CreateEndTime))
            {
                whereStr += " AND gp.PlatformCreatetime<=@CreateEndTime";
                parameters.Add("CreateEndTime", query.CreateEndTime);
            }
            if (!string.IsNullOrEmpty(query.UpdateStartTime))
            {
                whereStr += " AND gp.UpdateTime>=@UpdateStartTime";
                parameters.Add("UpdateStartTime", query.UpdateStartTime);
            }
            if (!string.IsNullOrEmpty(query.UpdateEndTime))
            {
                whereStr += " AND gp.UpdateTime<=@UpdateEndTime";
                parameters.Add("UpdateEndTime", query.UpdateEndTime);
            }

            if (!string.IsNullOrEmpty(query.ProductStatus))
            {
                whereStr += " AND gp.ProductStatus=@ProductStatus";
                parameters.Add("ProductStatus", query.ProductStatus);
                if (!query.ProductStatus.Equals("DELETED"))
                {
                    whereStr += " AND gp.Del =0";
                }
                else if (query.ProductStatus.Equals("DELETED"))
                {
                    whereStr += " AND gp.Del =1";
                }
            }


            #region 拼SQL
            var sql = $@"SELECT COUNT(1) FROM GlobalProduct gp WITH(NOLOCK) INNER JOIN GlobalProductExt ext WITH(NOLOCK) ON gp.UniqueCode= ext.GpUniqueCode WHERE {whereStr} ; 
                         -- 统计产品状态为PUBLISHED数量
                        SELECT COUNT(1) AS PublishedCount
                        FROM GlobalProduct gp WITH(NOLOCK)
                        INNER JOIN GlobalProductExt ext WITH(NOLOCK) ON gp.UniqueCode = ext.GpUniqueCode
                        WHERE {otherWhereStr} AND gp.ProductStatus = 'PUBLISHED';

                        -- 统计产品状态为'UNPUBLISHED'的记录数量
                        SELECT COUNT(1) AS UnPublishedCount
                        FROM GlobalProduct gp WITH(NOLOCK)
                        INNER JOIN GlobalProductExt ext WITH(NOLOCK) ON gp.UniqueCode = ext.GpUniqueCode
                        WHERE {otherWhereStr} AND gp.ProductStatus = 'UNPUBLISHED';

                        -- 统计产品状态为'DRAFT'的记录数量
                        SELECT COUNT(1) AS DraftCount
                        FROM GlobalProduct gp WITH(NOLOCK)
                        INNER JOIN GlobalProductExt ext WITH(NOLOCK) ON gp.UniqueCode = ext.GpUniqueCode
                        WHERE {otherWhereStr} AND gp.ProductStatus = 'DRAFT';

                        -- 统计产品状态为'DELETED'的记录数量
                        SELECT COUNT(1) AS DeletedCount
                        FROM GlobalProduct gp WITH(NOLOCK)
                        INNER JOIN GlobalProductExt ext WITH(NOLOCK) ON gp.UniqueCode = ext.GpUniqueCode
                        WHERE {otherWhereStr} AND gp.ProductStatus = 'DELETED';
                         SELECT {strFields} FROM GlobalProduct gp WITH(NOLOCK) INNER JOIN GlobalProductExt ext WITH(NOLOCK) ON gp.UniqueCode=ext.GpUniqueCode WHERE {whereStr} ORDER BY gp.Id DESC 
                         OFFSET {(query.PageIndex - 1) * query.PageSize} ROWS 
                         FETCH NEXT {query.PageSize} ROWS ONLY;";
            #endregion

            #region 记录执行的SQL语句
            if (CustomerConfig.IsDebug)
            {
                var sqllog = WriteSqlToLog(sql, parameters, "GlobalProduct.txt", "GlobalProductSql", "GetGlobalProductPageList");
            }
            #endregion

            var db = DbConnection;
            var grid = db.QueryMultiple(sql, param: parameters);
            return grid;
        }

        /// <summary>
        /// 根据UniqueCode查出所有符合条件的全球商品信息
        /// </summary>
        /// <param name="UniqueCodeList"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public GridReader GetGlobalProductTotalInfoList(List<string> UniqueCodeList)
        {
            var strFields = "gp.PlatformId,gp.UniqueCode,gp.Title,gp.ShopId,gp.PlatformType,gp.ProductSource,gp.ProductSourceUrl,gp.ProductSourceUid,gp.FxUserId, gp.CreateFrom, gp.Description,gp.PlatformCreatetime,gp.PlatformUpdatetime, gp.BrandID,gp.CategoryId,gp.GlobalSellerId,gp.ImageUrl,gp.PublishedSite, gp.CreateTime,gp.UpdateTime,gp.ProductStatus,ext.Id AS extId,ext.Video,ext.PackageDimensions,ext.PackageWeight,ext.ImageJson,ext.Certification,ext.SizeChart,ext.Manufacturer,ext.Region,ext.CategoryJson,ext.AttributeTypesJson";
            var parameters = new DynamicParameters();
            var whereStr = "gp.UniqueCode IN @UniqueCode";
            parameters.Add("UniqueCode", UniqueCodeList);

            #region 拼SQL
            var sql = $@"SELECT COUNT(1) FROM GlobalProduct gp WITH(NOLOCK) INNER JOIN GlobalProductExt ext WITH(NOLOCK) ON ext.GpUniqueCode=gp.UniqueCode WHERE {whereStr} ; 
                         SELECT {strFields} FROM GlobalProduct gp WITH(NOLOCK) INNER JOIN GlobalProductExt ext WITH(NOLOCK) ON ext.GpUniqueCode=gp.UniqueCode WHERE {whereStr};";
            #endregion

            #region 记录执行的SQL语句
            if (CustomerConfig.IsDebug)
            {
                var sqllog = WriteSqlToLog(sql, parameters, "GlobalProduct.txt", "GlobalProductSql", "GetGlobalProductPageList");
            }
            #endregion

            var db = DbConnection;
            var grid = db.QueryMultiple(sql, param: parameters);
            return grid;
        }

        /// <summary>
        /// 删除单条全球商品
        /// </summary>
        /// <param name="UniqueCode"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public int DeleteGlobalProduct(string UniqueCode, int curFxUserId)
        {
            int totalAffectedRows = 0;

            string sql1 = "UPDATE GlobalProductSku SET Del=1 WHERE GpUniqueCode=@UniqueCode";
            string sql2 = "UPDATE GlobalProductExt SET Del=1 WHERE GpUniqueCode=@UniqueCode";
            string sql3 = "UPDATE GlobalProduct SET Del=1,ProductStatus = 'DELETED' WHERE UniqueCode=@UniqueCode AND FxUserId = @FxUserId";

            try
            {
                int flag1 = DbConnection.Execute(sql1, new { UniqueCode = UniqueCode });
                totalAffectedRows += flag1;
            }
            catch (Exception ex)
            {
                Log.WriteError($"删除GlobalProductSku表的GpUniqueCode为{UniqueCode}的数据失败，错误原因:{ex}");
            }
            try
            {
                int flag2 = DbConnection.Execute(sql2, new { UniqueCode = UniqueCode });
                totalAffectedRows += flag2;
            }
            catch (Exception ex)
            {
                Log.WriteError($"删除GlobalProductExt表的GpUniqueCode为{UniqueCode}的数据失败，错误原因:{ex}");
            }
            try
            {
                int flag3 = DbConnection.Execute(sql3, new { UniqueCode = UniqueCode, FxUserId = curFxUserId });
                totalAffectedRows += flag3;
            }
            catch (Exception ex)
            {
                Log.WriteError($"删除GlobalProduct表的UniqueCode为{UniqueCode}的数据失败，错误原因:{ex}");
            }
            return totalAffectedRows;
        }

        /// <summary>
        /// 获取数据库的单条全球商品主表数据
        /// </summary>
        /// <param name="UniqueCode"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public GlobalProduct GetGlobalProductByUniqueCode(string UniqueCode, int curFxUserId)
        {
            string sql = "SELECT * FROM GlobalProduct WITH(NOLOCK) WHERE UniqueCode=@UniqueCode AND FxUserId = @FxUserId";
            var model = DbConnection.QueryFirstOrDefault<GlobalProduct>(sql, new { UniqueCode = UniqueCode, FxUserId = curFxUserId });
            return model;
        }

        /// <summary>
        /// 获取数据库的单条全球商品主表数据
        /// </summary>
        /// <param name="GlobalProductId"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public GlobalProduct GetGlobalProductByGlobalProductId(string GlobalProductId, int curFxUserId)
        {
            string sql = "SELECT * FROM GlobalProduct WITH(NOLOCK) WHERE PlatformId=@PlatformId AND FxUserId = @FxUserId";
            var model = DbConnection.QueryFirstOrDefault<GlobalProduct>(sql, new { PlatformId = GlobalProductId, FxUserId = curFxUserId });
            return model;
        }

        /// <summary>
        /// 获取数据库的多条全球商品主表数据
        /// </summary>
        /// <param name="GlobalProductIds"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public List<GlobalProduct> GetGlobalProductsByGlobalProductIds(List<string> GlobalProductIds, int curFxUserId)
        {
            string sql = "SELECT * FROM GlobalProduct WITH(NOLOCK) WHERE PlatformId IN @PlatformIds AND FxUserId = @FxUserId";
            var model = DbConnection.Query<GlobalProduct>(sql, new { PlatformIds = string.Join(",", GlobalProductIds).Split(','), FxUserId = curFxUserId }).ToList();
            return model;
        }


        /// <summary>
        /// 获取数据库的单条全球商品主表数据
        /// </summary>
        /// <param name="UniqueCode"></param>
        /// <param name="GlobalProductId"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public GlobalProduct GetGlobalProductByUniqueCodeAndGlobalProductId(string UniqueCode, string GlobalProductId, int FxUserId)
        {
            string sql = "SELECT * FROM GlobalProduct WITH(NOLOCK) WHERE UniqueCode=@UniqueCode AND FxUserId = @FxUserId AND PlatformId=@PlatformId";
            var model = DbConnection.QueryFirstOrDefault<GlobalProduct>(sql, new { UniqueCode = UniqueCode, PlatformId = GlobalProductId, FxUserId = FxUserId });
            return model;
        }


        /// <summary>
        /// 根据用户平台获取全球商品
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public List<GlobalProduct> GetGlobalProductsByFxUserId(int fxUserId, string platformType)
        {
            string sql = "SELECT * FROM GlobalProduct WITH(NOLOCK) WHERE PlatformType = @PlatformType AND FxUserId = @FxUserId";
            var model = DbConnection.Query<GlobalProduct>(sql, new { PlatformType = platformType, FxUserId = fxUserId }).ToList();
            return model;
        }

        /// <summary>
        /// 插入操作
        /// </summary>
        /// <param name="globalProduct"></param>
        /// <returns></returns>
        public object BulkInsert(GlobalProduct globalProduct)
        {
            var result = DbConnection.BulkInsert(globalProduct);
            return result;
        }

        /// <summary>
        /// 修改操作
        /// </summary>
        /// <param name="globalProduct"></param>
        public int Update(GlobalProduct globalProduct)
        {
            var sql = @"
    UPDATE GlobalProduct
    SET
        Title = @Title,
        Description = @Description,
        PlatformCreatetime = @PlatformCreatetime,
        PlatformUpdatetime = @PlatformUpdatetime,
        ProductStatus=@ProductStatus,
        BrandID = @BrandID,
        CategoryId = @CategoryId,
        GlobalSellerId = @GlobalSellerId,
        ImageUrl = @ImageUrl,
        UpdateTime = @UpdateTime,
        Del=@Del
    WHERE
        UniqueCode = @UniqueCode
        AND FxUserId = @FxUserId";
            return DbConnection.Execute(sql, globalProduct);
        }

    }
}
