using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Repository;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System.IO;
using System.Diagnostics;
using System.Data;
using DianGuanJiaApp.Data.EntityExtension;
using Newtonsoft.Json.Linq;
using DianGuanJiaApp.Data.Enum;
using Z.Dapper.Plus;
using System.Data.SqlClient;
using MongoDB.Driver.Core.Configuration;

namespace DianGuanJiaApp.Data.Repository
{
    public class ImportOrderBatchRepository : BaseRepository<ImportOrderBatch>
    {
        public ImportOrderBatchRepository() { }
        public ImportOrderBatchRepository(string connection) : base(connection) { }

        public void BulkInsert(List<ImportOrderBatch> list)
        {
            DbConnection.BulkInsert(list);
        }

        public List<ImportOrderBatch> GetListByPlatformOrderId(List<string> platformOrderIds)
        {
            //SQL脚本
            var sql = $"SELECT * FROM ImportOrderBatch(NOLOCK) WHERE PlatformOrderId IN @Codes";
            //查询
            return DbConnection.Query<ImportOrderBatch>(sql, new { Codes = platformOrderIds }).ToList();
        }

        /// <summary>
        /// 获取已存在列表
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        public List<IdAndCodeModel> GetExistIdAndCodes(List<string> codes)
        {
            //SQL脚本
            string sql = $@"SELECT id,PlatformOrderId AS Code FROM ImportOrderBatch(NOLOCK) WHERE PlatformOrderId IN @codes";
            //查询
            return DbConnection.Query<IdAndCodeModel>(sql, new { codes = codes }).ToList();
        }

    }
}
