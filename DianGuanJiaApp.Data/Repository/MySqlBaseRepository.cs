using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using MySql.Data.MySqlClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Repository
{
    /// <summary>
    /// mysql 仓储基类
    /// </summary>
    /// <typeparam name="T">分库泛型</typeparam>
    public class MySqlBaseRepository<T> : BaseRepository<T>  where T:class
    {
        private IDbConnection _connection;
        private string _connectionString;
        private const bool _isUseMySQL = true;

        /// <summary>
        /// 无参构造，之后需要指定ConnectionString
        /// </summary>
        public MySqlBaseRepository()
        {
            
        }
        /// <summary>
        /// 指定连接串
        /// </summary>
        /// <param name="connectionString"></param>
        public MySqlBaseRepository(string connectionString)
        {
            _connectionString = connectionString;
        }

        protected string ConnectionString
        {
            get
            {
                return _connectionString;
            }
            set
            {
                _connectionString = value;
            }
        
        }

        public static bool IsUseMySQL
        {
            get { return _isUseMySQL; }
        }

        /// <summary>
        /// 转换SQL，针对MySQL去除部分不支持的关键字
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public static string TranSql(string sql)
        {
            if (!IsUseMySQL)
                return sql;
            sql = sql.Replace("WITH(NOLOCK)", string.Empty);
            sql = sql.Replace("with(nolock)", string.Empty);
            sql = sql.Replace("(NOLOCK)", string.Empty);
            sql = sql.Replace("(nolock)", string.Empty);
            return sql;
        }

        public new IDbConnection DbConnection
        {
            get
            {
                IDbConnection db = null;

                if (!string.IsNullOrEmpty(_connectionString))
                {
                    db = Dapper.DbUtility.GetMySqlConnection(_connectionString);
                }
                else
                {
                    throw new ArgumentNullException("MySql仓储基类连接字符串为空");
                }
                return db;
            }
        }

        #region 基础操作，重写，兼容MySQL
        public new long Add(T model)
        {
            if (IsUseMySQL)
            {
                long? result;
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    result = _connection.InsertMysqlWithLongId<T>(model);
                }
                return result ?? 0;
            }
            else
            {
                return base.Add(model).ToLong();
            }
        }

        public new bool Update(T model)
        {
            if (IsUseMySQL)
            {
                int result;
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    result = _connection.UpdateMysql<T>(model);
                }
                if (result > 0)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return base.Update(model);
            }
        }

        public new void BulkInsert(List<T> models)
        {
            if (IsUseMySQL)
            {
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    if (_connection.State == ConnectionState.Closed)
                        _connection.Open();

                    models.ForEach(model =>
                    {
                        try
                        {
                            _connection.InsertMysqlWithLongId<T>(model);
                        }
                        catch (MySqlException ex)
                        {
                            // 如果异常属于唯一键冲突，则忽略
                            if (ex.Number == 1062 || ex.Message.Contains("Duplicate entry"))
                            {
                                Log.WriteError($"批量插入数据时，发生唯一键冲突，忽略此条数据，数据：{model.ToJson()}", "MySqlBulkInsert.txt");
                            }
                            else throw;
                        }
                    });
                }
            }
            else
            {
                base.BulkInsert(models);
            }
        }

        /// <summary>
        /// 批量更新
        /// </summary>
        /// <param name="models"></param>
        public new int BulkUpdate(List<T> models)
        {
            var result = 0;
            if (IsUseMySQL)
            {
                using (_connection = DbUtility.GetMySqlConnection(_connectionString))
                {
                    if (_connection.State == ConnectionState.Closed)
                        _connection.Open();

                    models.ForEach(model =>
                    {
                        try
                        {
                            result+= _connection.UpdateMysql<T>(model);
                        }
                        catch (MySqlException ex)
                        {
                            // 如果异常属于唯一键冲突，则忽略
                            if (ex.Number == 1062 || ex.Message.Contains("Duplicate entry"))
                            {
                                Log.WriteError($"批量插入数据时，发生唯一键冲突，忽略此条数据，数据：{model.ToJson()}", "MySqlBulkInsert.txt");
                            }
                            else throw;
                        }
                    });
                }
            }
            else
            {
                base.BulkUpdate(models);
                result = models.Count();
            }
            return result;
        }
        #endregion

        /// <summary>
        /// 分批查询V2 指定映射模型
        /// </summary>
        /// <typeparam name="T2"></typeparam>
        /// <param name="allCodes"></param>
        /// <param name="result"></param>
        /// <param name="tableName"></param>
        /// <param name="whereKey">查询匹配的字段名</param>
        /// <param name="selectStr"></param>
        /// <param name="batchSize"></param>
        public void BatchQueryV2<T2>(List<string> allCodes,out List<T2> result, string tableName,string whereKey, string selectStr = "*",int batchSize = 500)
        {
            if (tableName.IsNullOrEmpty())
                throw new ArgumentNullException("需要传入tableName");
            if (selectStr.IsNullOrEmpty())
                throw new ArgumentNullException("需要传入selectStr");

            var sql = $@"SELECT {selectStr} FROM {tableName} Where {whereKey} IN @codes";
            result = new List<T2>(allCodes.Count);
            using (var db = DbConnection)
            {
                if (db.State != System.Data.ConnectionState.Open)
                    db.Open();
                for (int i = 0; i < allCodes.Count; i += batchSize)
                {
                    var batchCodes = allCodes.Skip(i).Take(batchSize);
                    result.AddRange(db.Query<T2>(sql, new { codes = batchCodes}));
                }
            }
        }

        /// <summary>
        /// 分批查询
        /// </summary>
        /// <typeparam name="T2"></typeparam>
        /// <param name="allCodes"></param>
        /// <param name="tableName"></param>
        /// <param name="whereKey">查询匹配的字段名</param>
        /// <param name="selectStr"></param>
        /// <param name="batchSize"></param>
        public List<T> BatchQuery(List<string> allCodes, string whereKey, string selectStr = "*", int batchSize = 500)
        {
            var tableName = typeof(T).GetCustomAttribute<TableAttribute>()?.Name;

            if (tableName.IsNullOrEmpty())
                throw new ArgumentNullException($"{typeof(T).Name}未指定[Table]标签");
            if (selectStr.IsNullOrEmpty())
                throw new ArgumentNullException("需要传入selectStr");  

            var sql = $@"SELECT {selectStr} FROM {tableName} WHERE {whereKey} IN @codes";
            var result = new List<T>(allCodes.Count);
            using (var db = DbConnection)
            {
                if (db.State != System.Data.ConnectionState.Open)
                    db.Open();
                for (int i = 0; i < allCodes.Count; i += batchSize)
                {
                    var batchCodes = allCodes.Skip(i).Take(batchSize);
                    result.AddRange(db.Query<T>(sql, new { codes = batchCodes }));
                }
            }
            return result;
        }
    }
}
