using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Entity;

namespace DianGuanJiaApp.Data.Repository
{
    
    public partial class NaHuoLabelTemplateRepository : BaseRepository<Entity.NaHuoLabelTemplate>
    {
        public NaHuoLabelTemplateRepository() { }
        public NaHuoLabelTemplateRepository(string connectionString) : base(connectionString) { }

        public NaHuoLabelTemplate GetNaHuoTemplate(int id, int shopId)
        {
            var sql = "SELECT * FROM P_NaHuoLabelTemplate WITH(NOLOCK) WHERE id=@id AND shopId=@shopId";
            var db = this.DbConnection;
            var temp = db.Query<NaHuoLabelTemplate>(sql, new { id, shopId }).FirstOrDefault();
            return temp;
        }

        public NaHuoLabelTemplate CheckExistSameName(int id, string templateName, int shopId)
        {
            var db = DbConnection;
             NaHuoLabelTemplate t = null;
            if (id == 0) 
               t = db.GetList<NaHuoLabelTemplate>(" where TemplateName=@name AND ShopId=@sid AND IsDeleted !=1", new { name = templateName, sid = shopId })?.FirstOrDefault();
            else
               t = db.GetList<NaHuoLabelTemplate>(" where TemplateName=@name AND ShopId=@sid AND Id!=@id  AND IsDeleted !=1 ", new { name = templateName, sid = shopId, id })?.FirstOrDefault();
            return t;
        }

        public List<NaHuoLabelTemplate> GetTemplates(int shopId)
        {
            var db = DbConnection;
            var list = db.Query< NaHuoLabelTemplate>("SELECT * FROM P_NaHuoLabelTemplate WITH(NOLOCK) WHERE ShopId=@sid AND (IsDeleted=0 OR IsDeleted IS NULL) Order By Id desc ", new
            {
                sid = shopId,
            }).ToList();      
            return list?.OrderByDescending(l => l.IsOldTemplate).ThenByDescending(l => l.Id)?.ToList();
        }
    }
}
