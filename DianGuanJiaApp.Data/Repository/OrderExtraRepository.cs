using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Data.Repository
{
    public class OrderExtraRepository : BaseRepository<Entity.OrderExtra>
    {
        /// <summary>
        /// 订单扩展表
        /// </summary>

        public OrderExtraRepository() { }
        public OrderExtraRepository(string connectionString) : base(connectionString) { }
        /// <summary>
        /// 获取订单扩展表数据
        /// </summary>
        /// <param name="shopIds">店铺id</param>
        /// <param name="platformOrderIds">订单号</param>
        /// <returns></returns>
        public List<OrderExtra> GetList(List<int> shopIds, List<string> platformOrderIds)
        {
            if (shopIds == null || shopIds.Any() == false || platformOrderIds == null || platformOrderIds.Any() == false)
                return new List<OrderExtra>();
            var db = DbConnection;
            var list = new List<OrderExtra>(); ;
            var groupSize = 1000;
            var count = Math.Ceiling(platformOrderIds.Count * 1.0 / groupSize);
            for (int i = 0; i < count; i++)
            {
                var oPids = platformOrderIds.Skip(i * groupSize).Take(groupSize).ToList();
                var str = string.Join(",", oPids.Select(x => $"'{x}'"));
                var sids = string.Join(",", shopIds.Select(x => $"'{x}'"));
                var sql = $"select * from P_OrderExtra with(NOLOCK) where ShopId IN({sids}) and PlatformOrderId IN({str})";
                var datas = db.Query<OrderExtra>(sql).ToList() ?? new List<OrderExtra>();
                if (datas != null && datas.Any())
                    list.AddRange(datas);
            }
            return list;
        }
        public void BulkUpdate(List<OrderExtra> orderExtras)
        {

            if (orderExtras == null || orderExtras.Any() == false)
                return;
            try
            {
                var sql = $"Update P_OrderExtra SET [StartShipTime]=@StartShipTime,EndShipTime=@EndShipTime WHERE ShopId=@ShopId AND PlatformOrderId=@PlatformOrderId";
                using (var db = this.DbConnection)
                {
                    if (db.State != ConnectionState.Open)
                        db.Open();
                    foreach (var item in orderExtras)
                    {
                        db.Execute(sql, new
                        {
                            StartShipTime = item.StartShipTime,
                            EndShipTime = item.EndShipTime,
                            ShopId = item.ShopId,
                            PlatformOrderId = item.PlatformOrderId,
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"订单扩展项批量更新时发生错误：{ex}");
            }

        }

        public List<OrderExtra> GetListForDuplication(List<string> codes, string selectFields = "*",string whereFieldName = "PlatformOrderId")
        {
            var result = new List<OrderExtra>();
            if (codes.IsNullOrEmptyList())
                return result;
            //SQL脚本
            var sqlByIn = $@"SELECT {selectFields} FROM P_OrderExtra oc(NOLOCK) WHERE oc.{whereFieldName} IN @Codes";
            var sqlByTableFun =
                $@"SELECT {selectFields} FROM P_OrderExtra oc(NOLOCK) INNER JOIN dbo.FunStringToTableV2(@Codes,',') t ON oc.{whereFieldName}=t.item";
            var joinCodes = string.Join(",", codes);
            //查询
            //return DbConnection.Query<OrderCheckModel>(sql,new { joinCodes }).ToList();
            return SqlOptimizationHandler.QueryEntities(codes, sqlByIn, sqlByTableFun,
                (sql, param) => DbConnection.Query<OrderExtra>(sql, param).ToList());
        }

        public List<IdAndCodeModel> GetExistIdAndCodes(List<string> codes)
        {
            //SQL脚本
            const string sqlByIn =
                "SELECT Id, PlatformOrderId AS Code FROM P_OrderExtra (NOLOCK) WHERE PlatformOrderId IN @PlatformOrderId";
            const string sqlByTableFun =
                "SELECT Id, PlatformOrderId AS Code FROM P_OrderExtra(NOLOCK) INNER JOIN dbo.FunStringToTable(@PlatformOrderId,',') fstt ON PlatformOrderId = fstt.item";
            //查询
            //return DbConnection.Query<IdAndCodeModel>(sql, new { UniqueKeys = codes }).ToList();
            return SqlOptimizationHandler.QueryEntities(codes, sqlByIn, sqlByTableFun,
                (sql, param) => DbConnection.Query<IdAndCodeModel>(sql, param).ToList(), "PlatformOrderId");
        }

        public void BulkUpdateV2(List<OrderExtra> orderExtras)
        {

            if (orderExtras == null || orderExtras.Any() == false)
                return;
            try
            {
                using (var db = this.DbConnection)
                {
                    if (db.State != ConnectionState.Open)
                        db.Open();
                    foreach (var item in orderExtras)
                    {
                        var upfieldStr = string.Empty;
                        var parameters = new DynamicParameters();
                        parameters.Add("ShopId", item.ShopId);
                        parameters.Add("PlatformOrderId", item.PlatformOrderId);

                        if (item.PlatformType == PlatformType.TouTiao.ToString())
                        {
                            upfieldStr = "[StartShipTime]=@StartShipTime,EndShipTime=@EndShipTime";
                            parameters.Add("StartShipTime", item.StartShipTime);
                            parameters.Add("EndShipTime", item.EndShipTime);
                        }

                        if (string.IsNullOrWhiteSpace(upfieldStr) == false)
                        {
                            db.Execute($"Update P_OrderExtra SET {upfieldStr} WHERE ShopId=@ShopId AND PlatformOrderId=@PlatformOrderId", parameters);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"订单扩展项批量更新时发生错误：{ex}");
            }

        }

        /// <summary>
        /// 根据平台订单号获取订单扩展信息
        /// </summary>
        /// <param name="platformOrderId"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public OrderExtra GetOrderExtra(string platformOrderId,string selectFields = "*")
        {
            var sql = $"SELECT {selectFields}  FROM P_OrderExtra(NOLOCK) WHERE PlatformOrderId=@PlatformOrderId";
            return DbConnection.QueryFirstOrDefault<OrderExtra>(sql, new { PlatformOrderId = platformOrderId });
        }
    }
}
