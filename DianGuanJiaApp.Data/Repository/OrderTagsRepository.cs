using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using System.Data;
using DianGuanJiaApp.Data.Entity;
using Z.Dapper.Plus;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Data.Enum;
using MongoDB.Driver;

namespace DianGuanJiaApp.Data.Repository
{

    /// <summary>
    /// 订单标签
    /// </summary>
    public partial class OrderTagsRepository : BaseRepository<OrderTags>
    {
        public OrderTagsRepository() { }
        public OrderTagsRepository(string connectionString) : base(connectionString) { }


        public int Insert(OrderTags tag)
        {
            var db = this.DbConnection;
            var sql = @"INSERT INTO dbo.P_OrderTags( OiCode ,Sid ,Platform ,Tag ,TagType ,Status ,CreateTime ,Sort)
VALUES(@OiCode, @Sid, @Platform, @Tag, @TagType, @Status, GETDATE(), @Sort)";
            return db.Execute(sql, tag);
        }

        public OrderTags GetModel(string oiCode, string tagName)
        {
            var db = this.DbConnection;
            var sql = $@"Select * from P_OrderTags WITH(NOLOCK) where oicode=@oiCode AND Tag='{tagName}'";
            return db.QueryFirstOrDefault<OrderTags>(sql, new { oiCode });
        }

        public List<OrderTags> GetList(List<string> oiCodes, int? status)
        {
            var result = new List<OrderTags>();
            if (oiCodes == null || oiCodes.Any() == false)
                return result;
            oiCodes = oiCodes.Distinct().ToList();
            var pageSize = 2000;
            var pageCount = (int)decimal.Ceiling(oiCodes.Count / (decimal)pageSize);
            //SQL脚本
            var sqlByIn =
                $"SELECT * FROM P_OrderTags WITH(NOLOCK) WHERE OiCode IN @Codes {(status.HasValue ? $" AND Status={status}" : "")}";
            var sqlByTableFun = $@"SELECT ot.* FROM P_OrderTags ot WITH(NOLOCK)
                            INNER JOIN dbo.FunStringToTable(@Codes,',') fstt ON ot.OiCode = fstt.item  
                            WHERE 1=1 {(status.HasValue ? $" AND Status={status}" : "")}";
            using (var db = this.DbConnection)
            {
                if (db.State != ConnectionState.Open)
                    db.Open();
            
                for (var i = 0; i < pageCount; i++)
                {
                    var codes = oiCodes.Skip(i * pageSize).Take(pageSize).ToList();
                    //codes.Add("a");
                    //var qr = db.Query<OrderTags>(sql, new { codeStr = string.Join(",", codes) });
                    var qr = SqlOptimizationHandler.QueryEntities(codes, sqlByIn, sqlByTableFun,
                        (sql, param) => DbConnection.Query<OrderTags>(sql, param).ToList());
            
                    if (qr.Any())
                        result.AddRange(qr);
                }
            }
            return result;
        }

        public void BulkUpdate(List<string> oiCodes, string tag, int status)
        {
            if (oiCodes == null || oiCodes.Any() == false)
                return;
            oiCodes = oiCodes.Distinct().ToList();
            int pageSize = 1000;
            var pageCount = (int)Decimal.Ceiling(oiCodes.Count / (decimal)pageSize);
            var sql = $"Update P_OrderTags SET [Status]={status} WHERE Tag='{tag}' AND OiCode in @codes";
            using (var db = this.DbConnection)
            {
                if (db.State != ConnectionState.Open)
                    db.Open();

                for (int i = 0; i < pageCount; i++)
                {
                    var codes = oiCodes.Skip(i * pageSize).Take(pageSize);
                    var er = db.Execute(sql, new { codes = codes });
                }
            }
        }

        public void BulkUpdate(List<string> oiCodes, string tag, string tagValue, int status)
        {
            if (oiCodes == null || oiCodes.Any() == false)
                return;
            oiCodes = oiCodes.Distinct().ToList();
            int pageSize = 1000;
            var pageCount = (int)Decimal.Ceiling(oiCodes.Count / (decimal)pageSize);
            var sql = $"Update P_OrderTags SET [Status]=@status,TagValue=@tagValue WHERE Tag=@tag AND OiCode in @codes";
            using (var db = this.DbConnection)
            {
                if (db.State != ConnectionState.Open)
                    db.Open();

                for (int i = 0; i < pageCount; i++)
                {
                    var codes = oiCodes.Skip(i * pageSize).Take(pageSize);
                    var er = db.Execute(sql, new { tag = tag, codes = codes, status = status, tagValue = tagValue });
                }
            }
        }

        /// <summary>
        /// 不存在插入，存在更新
        /// </summary>
        /// <param name="orderTags"></param>
        public new void BulkInsert(List<OrderTags> orderTags)
        {
            //BulkWrite(orderTags, "P_OrderTags");
            if (orderTags == null || orderTags.Any() == false)
                return;

            try
            {
                //1.查询数据库存在的标签
                var oiCodes = orderTags.Select(f => f.OiCode).ToList();
                var dbTags = GetList(oiCodes, null);

                //1.1标签去重
                var dbTagDict = new Dictionary<string, OrderTags>();
                dbTags?.ForEach(m =>
                {
                    var key = m.OiCode + m.Tag;
                    if (dbTagDict.ContainsKey(key) == false)
                    {
                        dbTagDict.Add(key, m);
                    }
                });
                
                //2.找出存在的tag
                var waitUpdateTags = new List<OrderTags>();
                var waitInsertTags = new List<OrderTags>();
                foreach (var item in orderTags)
                {
                    var key = item.OiCode + item.Tag;
                    OrderTags dbModel;
                    if (dbTagDict.TryGetValue(key, out dbModel))
                    {
                        if (dbModel != null && (dbModel.Status != item.Status || dbModel.TagValue != item.TagValue))
                        {
                            item.Id = dbModel.Id;
                            waitUpdateTags.Add(item); //状态或值不一致
                        }
                    }
                    else if (item.Status == 0)
                    {
                        if (waitInsertTags.Any(w => w.HashCode == item.HashCode) == false)
                            waitInsertTags.Add(item); //新增的(只有打标的才新增，不存在的去标数据不插入)
                    }
                }
                //执行修改或者插入
                if (waitUpdateTags.Any())
                {
                    //foreach (var group in waitUpdateTags.GroupBy(f => new { f.Tag, f.Status }))
                    //{
                    //    var status = group.Key.Status;
                    //    var tagName = group.Key.Tag;
                    //    var oiCodeList = group.ToList().Select(f => f.OiCode).Distinct().ToList();
                    //    BulkUpdate(oiCodeList, tagName, status);
                    //}
                    //改为单个，不仅更新Status，其他字段也可能更新
                    using (var db = DbConnection)
                    {
                        if (db.State != ConnectionState.Open)
                            db.Open();
                        foreach (var item in waitUpdateTags)
                        {
                            db.Update(item);
                        }
                    }
                }
                if (waitInsertTags.Any())
                {
                    using (var db = DbConnection)
                    {
                        if (db.State != ConnectionState.Open)
                            db.Open();
                        foreach (var item in waitInsertTags)
                        {
                            db.Insert(item);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"订单标签保存失败：{ex.Message + ex.StackTrace}");
            }
        }

        /// <summary>
        /// 获取标签信息，为复制副本
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="sids"></param>
        /// <param name="tagType"></param>
        /// <param name="whereFieldName"></param>
        /// <param name="tag"></param>
        /// <returns></returns>
        public List<OrderTags> GetListForDuplication(List<string> codes, List<int> sids, string tagType,
            string whereFieldName = "OiCode", string tag = null,string selectFields = "*")
        {
            //SQL脚本
            var sqlByIn = $"SELECT {selectFields} FROM P_OrderTags(NOLOCK) WHERE {whereFieldName} IN @Codes AND Sid IN @sids AND TagType = @tagType";
            var sqlByTableFun = $"SELECT {selectFields} FROM P_OrderTags(NOLOCK) INNER JOIN dbo.FunStringToTable(@Codes,',') fstt ON {whereFieldName} = fstt.item WHERE Sid IN @sids AND TagType = @tagType";
            if (!string.IsNullOrEmpty(tag))
            {
                sqlByIn += " AND Tag = @Tag";
                sqlByTableFun += " AND Tag = @Tag";
            }
            //查询
            //return DbConnection.Query<OrderTags>(sql, new { Codes = string.Join(",", codes.Distinct()), sids = sids, tagType = tagType, Tag = tag })
            //    .ToList();
            return SqlOptimizationHandler.QueryEntities(codes, sqlByIn, sqlByTableFun,
                (sql, param) =>
                {
                    param.Add("sids", sids);
                    param.Add("tagType", tagType);
                    param.Add("Tag", tag);
                    return DbConnection.Query<OrderTags>(sql, param).ToList();
                });
        }

        /// <summary>
        /// 获取标签信息，为复制副本
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="sids"></param>
        /// <param name="tagType"></param>
        /// <param name="whereFieldName"></param>
        /// <param name="tag"></param>
        /// <returns></returns>
        public List<OrderTags> GetListForDuplicationNew(List<string> codes, List<int> sids, string tagType,
            string whereFieldName = "OiCode", string tag = null, string selectFields = "*")
        {
            //SQL脚本
            var sqlByIn = $"SELECT {selectFields} FROM P_OrderTags(NOLOCK) WHERE {whereFieldName} IN @Codes AND Sid IN @sids AND TagType = @tagType";
            var sqlByTableFun = $"SELECT pot.{selectFields} FROM dbo.FunStringToTable(@Codes,',') fstt INNER JOIN P_OrderTags pot (NOLOCK) ON fstt.item=pot.{whereFieldName} WHERE pot.Sid IN @sids AND pot.TagType = @tagType";
            if (!string.IsNullOrEmpty(tag))
            {
                sqlByIn += " AND Tag = @Tag";
                sqlByTableFun += " AND pot.Tag = @Tag";
            }
            sqlByTableFun += " OPTION(FORCE ORDER,LOOP JOIN)";
            return SqlOptimizationHandler.QueryEntities(codes, sqlByIn, sqlByTableFun,
                (sql, param) =>
                {
                    param.Add("sids", sids);
                    param.Add("tagType", tagType);
                    param.Add("Tag", tag);
                    return DbConnection.Query<OrderTags>(sql, param).ToList();
                });
        }

        public List<OrderTags> GetListForDuplication(List<string> codes,string whereFieldName = "UniqueKey", string tag = null, string selectFields = "*")
        {
            //SQL脚本
            //var sql = $"SELECT * FROM P_OrderTags(NOLOCK) WHERE {whereFieldName} IN @Codes AND Sid IN @sids AND TagType = @tagType";
            var sql = $"SELECT {selectFields} FROM P_OrderTags(NOLOCK) INNER JOIN dbo.FunStringToTable(@Codes,',') fstt ON {whereFieldName} = fstt.item";

            //查询
            return DbConnection.Query<OrderTags>(sql, new { Codes = string.Join(",", codes.Distinct())}).ToList();
        }

        /// <summary>
        /// 获取已存在列表
        /// </summary>
        /// <param name="codes"></param>
        /// <returns></returns>
        public List<IdAndCodeModel> GetExistIdAndCodes(List<string> codes)
        {
            //SQL脚本
            const string sqlByIn =
                "SELECT Id, UniqueKey AS Code FROM P_OrderTags(NOLOCK) WHERE UniqueKey IN @UniqueKeys";
            const string sqlByTableFun =
                "SELECT Id, UniqueKey AS Code FROM P_OrderTags(NOLOCK) INNER JOIN dbo.FunStringToTable(@UniqueKeys,',') fstt ON UniqueKey = fstt.item";
            //查询
            //return DbConnection.Query<IdAndCodeModel>(sql, new { UniqueKeys = codes }).ToList();
            return SqlOptimizationHandler.QueryEntities(codes, sqlByIn, sqlByTableFun,
                (sql, param) => DbConnection.Query<IdAndCodeModel>(sql, param).ToList(), "UniqueKeys");
        }

        public List<OrderTags> GetList(List<string> oiCodes, List<string> tagNames, int? status, string tagValue = null)
        {
            var result = new List<OrderTags>();
            if (oiCodes == null || oiCodes.Any() == false)
                return result;
            oiCodes = oiCodes.Distinct().ToList();
            int pageSize = 1000;
            var pageCount = (int)Decimal.Ceiling(oiCodes.Count / (decimal)pageSize);
            //SQL脚本
            var sqlByIn =
                $"select * from P_OrderTags WITH(NOLOCK) WHERE OiCode in @Codes AND Tag in @names {(status.HasValue ? $"AND Status={status}" : "")}";
                 sqlByIn += string.IsNullOrWhiteSpace(tagValue) ? "" : $" AND TagValue={tagValue}";


            var sqlByTableFun = $@"SELECT ot.* FROM P_OrderTags ot WITH(NOLOCK)
                            INNER JOIN dbo.FunStringToTable(@Codes,',') fstt ON ot.OiCode = fstt.item  
                            WHERE ot.Tag IN @names {(status.HasValue ? $"AND ot.Status={status}" : "")}";
            sqlByTableFun += string.IsNullOrWhiteSpace(tagValue) ? "" : $" AND TagValue={tagValue}";

            using (var db = this.DbConnection)
            {
                if (db.State != ConnectionState.Open)
                    db.Open();

                for (int i = 0; i < pageCount; i++)
                {
                    var codes = oiCodes.Skip(i * pageSize).Take(pageSize).ToList();
                    codes.Add("a");
                    //查询
                    //var qr =  db.Query<OrderTags>(sql, new { Codes = string.Join(",", codes), names = tagNames });
                    var qr = SqlOptimizationHandler.QueryEntities(codes, sqlByIn, sqlByTableFun,
                        (sql, param) =>
                        {
                            param.Add("names", tagNames);
                            return DbConnection.Query<OrderTags>(sql, param).ToList();
                        });
                    if (qr.Any())
                        result.AddRange(qr);
                }
            }
            return result;
        }

        /// <summary>
        /// 逻辑删除数据
        /// </summary>
        /// <param name="oiCodes"></param>
        /// <param name="tagName"></param>
        public void SetDeleted(List<string> oiCodes, string tagName, string tagValue = null)
        {
            try
            {
                var sql = @"
UPDATE o
SET o.OiCode = '-' + o.UniqueKey,o.Status=-1
FROM P_OrderTags AS o
INNER JOIN dbo.FunStringToTable (@codes,',')  ON item = OiCode
WHERE o.Tag= @tagName ";
                sql += string.IsNullOrWhiteSpace(tagValue) ? "" : $" AND o.TagValue={tagValue}";
                DbConnection.Execute(sql, new { codes = string.Join(",",oiCodes), tagName });
            }
            catch (Exception ex)
            {
                Log.WriteError($"批量逻辑删除数据失败，下面进行单条删除数据：{ex.Message + ex.StackTrace}");
                try
                {
                    foreach (var item in oiCodes)
                    {
                        string sql = "UPDATE P_OrderTags SET OiCode = '-' + UniqueKey,Status=-1 WHERE OiCode = @oiCode AND Tag=@tagName";
                        sql += string.IsNullOrWhiteSpace(tagValue) ? "" : $" AND TagValue={tagValue}";
                        DbConnection.Execute(sql, new { oiCode = item, tagName = tagName });
                    }

                }
                catch (Exception ex2)
                {
                    Log.WriteError($"单条逻辑删除数据也失败：{ex2.Message + ex2.StackTrace}");
                }
            }


        }

        /// <summary>
        /// 批量插入和更新标签
        /// </summary>
        /// <param name="orderTags"></param>
        /// <param name="tagNames"></param>
        /// <param name="tagValue"></param>
        public void BulkInsertOrUpdateTags(List<OrderTags> orderTags, List<string> tagNames, string tagValue = null)
        {
            if (orderTags == null || orderTags.Any() == false)
                return;

            try
            {
                // 1. 构建查询条件
                var oiCodes = orderTags.Select(t => t.OiCode).Distinct().ToList();
                var tagConditions = tagNames.Distinct().ToList();

                // 2. 一次性查询数据库中已存在的标签
                var dbTags = GetList(oiCodes, tagConditions, 0, tagValue);

                // 3. 构建字典以快速查找
                var dbTagDict = dbTags.ToDictionary(t => t.OiCode + t.Tag, t => t);

                //4.找出存在的tag
                var waitUpdateTags = new List<OrderTags>();
                var waitInsertTags = new List<OrderTags>();
                foreach (var item in orderTags)
                {
                    var key = item.OiCode + item.Tag;
                    OrderTags dbModel;
                    if (dbTagDict.TryGetValue(key, out dbModel))
                    {
                        if (dbModel != null && (dbModel.Status != item.Status || dbModel.TagValue != item.TagValue))
                        {
                            item.Id = dbModel.Id;
                            waitUpdateTags.Add(item); //状态或值不一致
                        }
                    }
                    else if (item.Status == 0)
                    {
                        if (waitInsertTags.Any(w => w.HashCode == item.HashCode) == false)
                            waitInsertTags.Add(item); //新增的(只有打标的才新增，不存在的去标数据不插入)
                    }
                }
                //执行修改或者插入
                if (waitUpdateTags.Any())
                {
                    using (var db = DbConnection)
                    {
                        if (db.State != ConnectionState.Open)
                            db.Open();
                        foreach (var item in waitUpdateTags)
                        {
                            db.Update(item);
                        }
                    }
                }
                if (waitInsertTags.Any())
                {
                    using (var db = DbConnection)
                    {
                        if (db.State != ConnectionState.Open)
                            db.Open();
                        foreach (var item in waitInsertTags)
                        {
                            db.Insert(item);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"订单标签保存失败：{ex.Message + ex.StackTrace}");
            }
        }

        /// <summary>
        /// 检查订单是否有标签（receiver_change），如果有则需要去标
        /// </summary>
        /// <param name="orderIds">逻辑单编号</param>
        public void CheckTagChangeOfAddress(List<string> orderIds)
        {
            try
            {
                if (orderIds.IsNullOrEmptyList()) return;
                const string sql = "select OiCode from P_OrderTags WITH(NOLOCK) where OiCode in @orderIds and tag = 'receiver_change' ";
                var oiCodes = DbConnection.Query<string>(sql, new { orderIds }).ToList();
                if (oiCodes.Any())
                {
                    BulkUpdate(oiCodes, "receiver_change", -1);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"地址变更的订单去标失败：{ex.Message + ex.StackTrace}");
            }
        }

        /// <summary>
        /// 检查逻辑单是否有标签（receiver_change）
        /// </summary>
        /// <param name="logicOrderIdList"></param>
        /// <returns>返回OiCode</returns>
        public List<string> CheckLogicOrderReceiverChange(List<string> logicOrderIdList)
        {
            const string sql = "select OiCode from P_OrderTags WITH(NOLOCK) where OiCode in @logicOrderIdList and Tag = 'receiver_change' and status = 0 ";
            return DbConnection.Query<string>(sql, new { logicOrderIdList }).ToList();
        }

        /// <summary>
        /// 打上换款中的标签
        /// </summary>
        public void AddChangeingSkuOrder(int shopId, string originalOrderItemCode, string changeingSkuTime)
        {
            try
            {
                if (originalOrderItemCode.IsNullOrEmptyList()) return;
                const string sql = "select OiCode from P_OrderTags WITH(NOLOCK) where OiCode = @orderId and tag = 'ChangeingSku_Order' ";
                var oiCodes = DbConnection.Query<string>(sql, new { originalOrderItemCode }).ToList();
                if (!oiCodes.Any())
                {
                    var orderTag = new OrderTags()
                    {
                        OiCode = originalOrderItemCode,
                        Sid = shopId,
                        TagType = TagType.OrderItem.ToString(),
                        Platform = PlatformType.WxVideo.ToString(),
                        Tag = OrderTag.ChangeingSku_Order.ToString(),
                        Status = 0,
                        TagValue = changeingSkuTime,
                        CreateTime = DateTime.Now
                    };

                    List<OrderTags> orderTagList = new List<OrderTags>() { orderTag };
                    BulkInsert(orderTagList);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"微信小店订单订单换款打标失败：{ex.Message + ex.StackTrace}");
            }
        }

        /// <summary>
        /// 获取商品变更的标签（ProductChange）
        /// </summary>
        /// <param name="logicOrderIdList">逻辑订单Id</param>
        /// <returns>返回OiCode</returns>
        public List<string> GetProductChangeTag(List<string> logicOrderIdList)
        {
            if (!logicOrderIdList.Any()) return new List<string>();
            const string sql = "select OiCode from P_OrderTags WITH(NOLOCK) where OiCode in @logicOrderIdList and Tag = 'ProductChange' and status = 0 ";
            return DbConnection.Query<string>(sql, new { logicOrderIdList }).ToList();
        }

        /// <summary>
        /// 作废订单标签-商品变更
        /// </summary>
        /// <param name="logicOrderIdLsit">逻辑单维度</param>
        public void InvalidateOrderTags(List<string> logicOrderIdLsit)
        {
            if (!logicOrderIdLsit.Any()) return;

            // const string sql = "update P_OrderTags set status = -1 where OiCode in @originalOrderItemCodeLsit";
            // DbConnection.Execute(sql, new { originalOrderItemCodeLsit });
            const string sql2 = "update P_OrderTags set status = -1 where OiCode in @logicOrderIdLsit and Tag = 'ProductChange' ";
            DbConnection.Execute(sql2, new { logicOrderIdLsit });
        }
    }
}
