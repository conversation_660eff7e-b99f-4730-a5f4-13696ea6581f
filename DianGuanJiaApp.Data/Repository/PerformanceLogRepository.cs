using Dapper;
using DianGuanJiaApp.Data.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Repository
{
    public class PerformanceLogRepository : BaseRepository<PerformanceLog>
    {
        public PerformanceLogRepository()
        { }
        public PerformanceLogRepository(string connection) : base(connection)
        {
        }

        public void Insert(PerformanceLog log)
        {
            string sql = @" INSERT INTO PerformanceLog([Route],Method,DbType,FxUserId,Db,Exception,times,Args,DT)
                            VALUES(@route, @method,@dbtype, @fxuserid, @db, @exception,@times, @args, @dt) ";
            DbConnection.Execute(sql, new { route = log.Route, method = log.Method,dbtype=log.DbType, fxuserid = log.FxUserId, db = log.Db, exception = log.Exception,times = log.times, args = log.Args, dt = log.DT });
        }


    }
}
