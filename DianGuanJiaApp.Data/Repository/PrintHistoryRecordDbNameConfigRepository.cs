using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Repository
{
    /// <summary>
    /// 
    /// </summary>
    public class PrintHistoryRecordDbNameConfigRepository:BaseRepository<PrintHistoryRecordDbNameConfig>
    {
        /// <summary>
        /// 
        /// </summary>
        public PrintHistoryRecordDbNameConfigRepository() : base(CustomerConfig.ConfigureDbConnectionString)
        {

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="connectionString"></param>
        public PrintHistoryRecordDbNameConfigRepository(string connectionString) : base(connectionString)
        {

        }


        /// <summary>
        /// 
        /// </summary>
        private bool IsEnabledPrinHistoryRecordDbNameConfigCache
        {
            get
            {
                return new CommonSettingRepository().IsEnabledPrinHistoryRecordDbNameConfigCache();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        private bool IsEnabledPrinHistoryRecordDbServerConfigCache
        {
            get
            {
                return new CommonSettingRepository().IsEnabledPrinHistoryRecordDbServerConfigCache();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public List<PrintHistoryRecordDbServerConfig> GetPrintHistoryRecordDbServers()
        {
            var sqlresults = new List<PrintHistoryRecordDbServerConfig>();
            var sql = $@"SELECT * FROM P_PrintHistoryRecordDbServerConfig WITH(NOLOCK)";
            var sqldb = DbConnection;

            if (IsEnabledPrinHistoryRecordDbServerConfigCache)
            {
                // 开启缓存
                sqlresults =
                    sqldb.Query<PrintHistoryRecordDbServerConfig>(sql).ToList();
            }
            else
            {
                // 关闭缓存
                sqlresults =
                    sqldb.QueryWithCache<PrintHistoryRecordDbServerConfig>(sql);
            }
            return sqlresults;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public List<PrintHistoryRecordDbNameConfig> GetPrintHistoryRecordDbNmaes()
        {
            var sqlresults = new List<PrintHistoryRecordDbNameConfig>();
            var sql = $@"SELECT * FROM P_PrintHistoryRecordDbNameConfig WITH(NOLOCK)";
            var sqldb = DbConnection;

            if (IsEnabledPrinHistoryRecordDbNameConfigCache)
            {
                // 开启缓存
                sqlresults = 
                    sqldb.Query<PrintHistoryRecordDbNameConfig>(sql).ToList();
            }
            else
            {
                // 关闭缓存
                sqlresults = 
                    sqldb.QueryWithCache<PrintHistoryRecordDbNameConfig>(sql);
            }
            return sqlresults;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dbNameId"></param>
        /// <returns></returns>
        public PrintHistoryDbRecordConfig GetDbConfigModel(int dbNameId)
        {
            var db = DbConnection;
            var dbNames = GetPrintHistoryRecordDbNmaes();
            var dbServers = GetPrintHistoryRecordDbServers();
            if (dbServers != null && dbServers.Any())
            {
                var res = from s in dbServers
                          join n in dbNames on s.Id equals n.DbServerConfigId into dbAll
                          from n in dbAll.DefaultIfEmpty()
                          where n.Id == dbNameId
                          select new PrintHistoryDbRecordConfig
                          {
                              DbServer = s,
                              DbNameConfig = n
                          };
                return res.FirstOrDefault();
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<PrintHistoryDbRecordConfig> GetDbConfigModels(List<int> fxUserIds)
        {
            if(fxUserIds == null || !fxUserIds.Any())
                return null;
            var dbNameIds = fxUserIds.Select(p => DataBasePolicy(p)).Distinct().ToList();
            var db = DbConnection;
            var dbNames = GetPrintHistoryRecordDbNmaes();
            var dbServers = GetPrintHistoryRecordDbServers();
            if (dbServers != null && dbServers.Any())
            {
                var res = from s in dbServers
                          join n in dbNames on s.Id equals n.DbServerConfigId into dbAll
                          from n in dbAll.DefaultIfEmpty()
                          where dbNameIds.Contains(n.Id)
                          select new PrintHistoryDbRecordConfig
                          {
                              DbServer = s,
                              DbNameConfig = n,
                          };
                return res.ToList();
            }
            else
            {
                return null;
            }
        }

        /// <summary>
        /// 分库策略
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public int DataBasePolicy(int fxUserId)
        {
            var dbid = fxUserId % 100;
            return dbid + 1;

            //if (CustomerConfig.IsDebug)
            //{
            //    if (fxUserId == 5)
            //        return 1;
            //    if (fxUserId == 45)
            //        return 2;

            //    if (fxUserId == 28)
            //        return 3;
            //    return 4;
            //}
            //else
            //{
            //    var dbid = fxUserId % 100;
            //    return dbid + 1;
            //}
        }
    }
}
