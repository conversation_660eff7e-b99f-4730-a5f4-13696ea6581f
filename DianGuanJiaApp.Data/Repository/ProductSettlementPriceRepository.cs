using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility.Extension;
using StackExchange.Redis;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Data.Repository
{
    public class ProductSettlementPriceRepository : BaseRepository<ProductSettlementPrice>
    {
        public ProductSettlementPriceRepository() { }
        public ProductSettlementPriceRepository(string connectionString) : base(connectionString) { }

        /// <summary>
        /// 获取信息列表，为迁移数据
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetListForDuplication(DuplicationConditionModel condition,
            int pageSize)
        {
            //SQL脚本
            var sql =
                $@"SELECT TOP {pageSize} * FROM P_ProductSettlementPrice(NOLOCK) WHERE  CreateUser = @FxUserId";
            //类型
            if (!string.IsNullOrWhiteSpace(condition.PlatformType))
            {
                switch (condition.PlatformType.ToLower())
                {
                    case "alibaba":
                        sql +=
                            " AND PlatformType NOT IN('System','Pinduoduo','KuaiTuanTuan','Jingdong','TouTiao')";
                        break;
                    case "pinduoduo":
                        sql +=
                            " AND PlatformType IN('Pinduoduo','KuaiTuanTuan')";
                        break;
                    case "jingdong":
                        sql +=
                            " AND PlatformType = 'Jingdong'";
                        break;
                    case "toutiao":
                        sql +=
                            " AND PlatformType = 'TouTiao'";
                        break;
                }
            }

            //游标分页
            if (condition.MaxId.HasValue && condition.MaxId.Value > 0)
            {
                sql += " AND Id > @MaxId";
            }

            //时间区间
            if (condition.BeginTime.HasValue)
            {
                sql += " AND UpdateTime > @BeginTime";
            }

            if (condition.EndTime.HasValue)
            {
                sql += " AND UpdateTime <= @EndTime";
            }

            //排序
            sql += " ORDER BY Id";
            //查询
            return DbConnection.Query<ProductSettlementPrice>(sql, condition).ToList();
        }

        /// <summary>
        /// 获取信息为复制副本，按SendHistoryCode
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="platformType"></param>
        /// <param name="selectFieldNames"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetListForDuplication(List<string> codes, string platformType,
            string selectFieldNames = "*",
            string whereFieldName = "UniqueKey")
        {
            //类型
            var platformTypeWhere = string.Empty;
            if (!string.IsNullOrWhiteSpace(platformType))
            {
                platformTypeWhere = $" AND PlatformType = '{platformType}' ";
            }
            //SQL脚本
            //var sql = $"SELECT {selectFieldNames} FROM P_ProductSettlementPrice(NOLOCK) WHERE {whereFieldName} IN @Codes{platformTypeWhere}";
            var sql = $"SELECT {selectFieldNames} FROM P_ProductSettlementPrice(NOLOCK) INNER JOIN dbo.FunStringToTable(@Codes,',') fstt ON {whereFieldName} = fstt.item WHERE 1=1 {platformTypeWhere}";
            //查询
            return DbConnection.Query<ProductSettlementPrice>(sql, new { Codes = string.Join(",", codes.Distinct()) }).ToList();
        }

        /// <summary>
        /// 获取已存在列表
        /// </summary>
        /// <returns></returns>
        public List<IdAndCodeModel> GetExistIdAndCodes(List<string> codes)
        {
            //SQL脚本
            const string sql = "SELECT Id,UniqueKey AS Code FROM P_ProductSettlementPrice(NOLOCK) WHERE UniqueKey IN @Codes";
            //查询
            return DbConnection.Query<IdAndCodeModel>(sql, new { Codes = codes }).ToList();
        }
        /// <summary>
        /// 获取信息列表，为抖店云内迁移数据
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="pageSize"></param>
        /// <param name="maxId"></param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetListForSameCloudDuplication(int fxUserId, int pageSize, long maxId = 0)
        {
            //SQL脚本
            var sql =
                $@"SELECT TOP {pageSize} * FROM P_ProductSettlementPrice(NOLOCK) WHERE (CreateUser = @FxUserId OR FxUserId = @FxUserId) ";

            if (maxId > 0)
            {
                sql += " AND Id > @MaxId";
            }
            //排序
            sql += " ORDER BY Id";
            //查询
            return DbConnection.Query<ProductSettlementPrice>(sql, new { FxUserId= fxUserId, MaxId = maxId }).ToList();
        }
        /// <summary>
        /// 获取上游厂家设置的结算价
        /// </summary>
        /// <param name="skuCodes"></param>
        /// <param name="fxUserId">当前用户ID</param>
        /// <param name="upFxUserId">上游厂家ID</param>
        /// <param name="fields"></param>
        /// <returns></returns>
        public List<ProductSettlementPrice> Get(List<string> skuCodes, int fxUserId, int upFxUserId,List<string> fields = null)
        {
            var strFields = "*";
            if (fields != null && fields.Any())
                strFields = string.Join(",", fields);
            var sql = $"select {strFields} from P_ProductSettlementPrice (NOLOCK) where ProductSkuCode IN@skuCodes and FxUserId=@fxUserId AND CreateUser=@upFxUserId";
            return DbConnection.Query<ProductSettlementPrice>(sql, new { skuCodes, fxUserId, upFxUserId }).ToList();
        }

        /// <summary>
        /// 查询结算价
        /// </summary>
        /// <param name="productSkuCodes">商品skuCode</param>
        /// <param name="createUsers">创建人</param>
        /// <param name="fxUserIds">对方</param>
        /// <param name="settlementType">对方类型：1厂家，2商家</param>
        /// <param name="fields">查询字段：p.*</param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetProductSettlementPrice(List<string> productSkuCodes,
            List<int> createUsers, List<int> fxUserIds, int settlementType, string fields = "*")
        {
            if (createUsers == null || !createUsers.Any())
                return new List<ProductSettlementPrice>();
            var sql = $@"SELECT {fields},p.UniqueKey AS DbUniqueKey 
                        FROM dbo.P_ProductSettlementPrice p WITH(NOLOCK) INNER JOIN dbo.FunStringToTable(@Codes,',') AS t1 ON t1.item = p.ProductSkuCode
                        WHERE p.FxUserId IN@fxUserIds AND p.SettlementType=@settlementType AND p.CreateUser IN@createUsers";

            return DbConnection.Query<ProductSettlementPrice>(sql,
                new { Codes = string.Join(",", productSkuCodes), fxUserIds, settlementType, createUsers }).ToList();

        }

        /// <summary>
        /// 查询结算价
        /// </summary>
        /// <param name="productSkuCodes">商品skuCode</param>
        /// <param name="createUsers">创建人</param>
        /// <param name="fxUserIds">对方</param>
        /// <param name="fields">查询字段：p.*</param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetProductSettlementPrice(List<string> productSkuCodes, List<int> createUsers, List<int> fxUserIds, string fields = "*")
        {
            if (createUsers.IsNullOrEmpty()&&fxUserIds.IsNullOrEmpty())
                return new List<ProductSettlementPrice>();

            var batchSize = 500; // 每个批次的大小
            var result = new List<ProductSettlementPrice>();

            for (int i = 0; i < productSkuCodes.Count; i += batchSize)
            {
                var batchCodes = productSkuCodes.Skip(i).Take(batchSize).ToList();

                var sql = $@"SELECT {fields}, p.UniqueKey AS DbUniqueKey 
                     FROM dbo.P_ProductSettlementPrice p WITH(NOLOCK) 
                     INNER JOIN dbo.FunStringToTable(@batchCodes, ',') AS t1 ON t1.item = p.ProductSkuCode
                     WHERE p.FxUserId IN @fxUserIds AND p.CreateUser IN @createUsers";

                fxUserIds = fxUserIds?.Distinct().ToList();
                createUsers = createUsers?.Distinct().ToList();

                var batchResult = DbConnection.Query<ProductSettlementPrice>(sql, new { fxUserIds, createUsers,batchCodes = string.Join(",", batchCodes) }).ToList();
                result.AddRange(batchResult);
            }

            return result;
        }

        /// <summary>
        /// 获取新的结算价
        /// </summary>
        /// <returns></returns>
        public List<SubProductSettlementPrice> GetSubProductSettlementPrice(List<string> uniqueCodes)
        {
            var result = new List<SubProductSettlementPrice>();
            //分批
            uniqueCodes.ChunkList(500).ForEach(codes =>
            {
                var codesWhere = $" INNER JOIN dbo.FunStringToTable(@codes,',') AS t ON t.item = ssp.ProductSettlementPriceUniqueCode";
                var sql = $@"SELECT Id,ProductSettlementPriceUniqueCode,ProductCode,SkuCode,SkuName,Price,CreateTime FROM SubProductSettlementPrice ssp WITH(NOLOCK) {codesWhere} WHERE IsDelete = 0";
                var batchResult = DbConnection.Query<SubProductSettlementPrice>(sql, new { codes = string.Join(",", codes) }).ToList();
                if (batchResult.IsNotNullAndAny())
                    result.AddRange(batchResult);
            });
            return result;
        }

        /// <summary>
        /// 查询结算价
        /// </summary>
        /// <param name="productSkuCodes">商品skuCode</param>
        /// <param name="createUsers">创建人</param>
        /// <param name="settlementType">结算类型</param>
        /// <param name="fields">查询字段：p.*</param>
        /// <returns></returns>
        public List<ProductSettlementPrice> GetProductSettlementPriceV2(List<string> productSkuCodes,List<int> createUsers, List<int> settlementType ,string fields = "*")
        {
            if (createUsers.IsNullOrEmpty() || settlementType.IsNullOrEmpty())
                return new List<ProductSettlementPrice>();

            var batchSize = 500; // 每个批次的大小
            var result = new List<ProductSettlementPrice>();

            for (int i = 0; i < productSkuCodes.Count; i += batchSize)
            {
                var batchCodes = productSkuCodes.Skip(i).Take(batchSize).ToList();

                var sql = $@"SELECT {fields}, p.UniqueKey AS DbUniqueKey 
                     FROM dbo.P_ProductSettlementPrice p WITH(NOLOCK) 
                     INNER JOIN dbo.FunStringToTable(@batchCodes, ',') AS t1 ON t1.item = p.ProductSkuCode
                     WHERE p.CreateUser IN @createUsers AND p.SettlementType IN@settlementType";

                createUsers = createUsers?.Distinct().ToList();

                var batchResult = DbConnection.Query<ProductSettlementPrice>(sql, new { createUsers,settlementType, batchCodes = string.Join(",", batchCodes) }).ToList();
                result.AddRange(batchResult);
            }

            return result;
        }

        /// <summary>
        /// 结算价类型
        /// </summary>
        public enum SettlementType
        {
            /// <summary>
            /// 我是商家 对方是厂家
            /// </summary>
            Merchant = 1,
            /// <summary>
            /// 我是厂家 对方是商家
            /// </summary>
            Manufacturer = 2,
            // <summary>
            /// 成本价
            /// </summary>
            CostPrice = 3,

            /// <summary>
            /// 默认采购价
            /// </summary>
            DefaultPurchasePrice = 4,

        }

    }
}