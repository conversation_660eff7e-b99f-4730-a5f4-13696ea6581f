using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Repository.DataMigrate;
using DianGuanJiaApp.Utility.Extension;
using MySql.Data.MySqlClient;

namespace DianGuanJiaApp.Data.Repository
{
    public class ReceDbConfigRepository : ConfigDbBaseRepository<ReceDbConfig>
    {
        public ReceDbConfigRepository() : base(GetConnectionString())
        {
        }
        public ReceDbConfigRepository(string connectionString, bool isUseMySql = false) : base(connectionString, isUseMySql)
        {
        }
        
        /// <summary>
        /// 获取数据库连接串
        /// </summary>
        /// <returns></returns>
        public static string GetConnectionString(bool fromTool = false)
        {
            if (fromTool) return GetNewMySqlConn("ReceDbConfig");
            
            // 如果开启使用新MySQL数据库，则使用新的数据库连接字符串
            return IsUseMySqlDb("ReceIsUseMySqlDb")
                ? GetNewMySqlConn("ReceDbConfig")
                : CustomerConfig.ConfigureDbConnectionString;
        }
        
        /// <summary>
        /// 获取新库开关配置
        /// </summary>
        /// <param name="fxUserId">用户Id</param>
        /// <returns></returns>
        public static bool GetSwitchNewDbConfigId(int? fxUserId)
        {
            var result = IsEnableNewDbConfigId("ReceDbConfig", fxUserId);
            return result;
        }

        /// <summary>
        /// 创建【收件人数据库配置信息】
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="dbNameConfigId"></param>
        /// <param name="cloudPlatformType"></param>
        /// <param name="newDbNameId">新库Id</param>
        public void TryToCreateCloudReceDbConfig(int fxUserId, int dbNameConfigId, string cloudPlatformType, int newDbNameId = 0)
        {
            var sql = "SELECT * FROM ReceDbConfig WITH(NOLOCK) WHERE FxUserId=@fxUserId AND DbCloudPlatform=@cloudPlatformType";
            if (IsUseMySql) sql = TranSql(sql);
            var db = DbConnection;
            var oldDbConfig = db.QueryFirstOrDefault<ReceDbConfig>(sql , new { fxUserId, cloudPlatformType });
            if (oldDbConfig == null)
            {
                var ent = new ReceDbConfig
                {
                    FxUserId = fxUserId,
                    DbNameConfigId = dbNameConfigId,
                    NewDbNameConfigId = newDbNameId > 0 ? newDbNameId : dbNameConfigId,
                    DbCloudPlatform = cloudPlatformType,
                    CreateTime = DateTime.Now,
                };
                if (IsUseMySql) db.InsertMysql(ent);
                else db.Insert(ent);
                Log.Debug(() => $"创建收件人数据库配置信息：{ent.ToJson()}");
            }
        }
        
        /// <summary>
        /// 是否开启收件人数据库配置缓存
        /// 只有使用新MySQL数据库时才开启，避免切换时数据不一致
        /// </summary>
        private bool IsEnabledReceDbConfigCache => IsUseMySql && new CommonSettingRepository().IsEnabledReceDbConfigCache();

        /// <summary>
        /// 批量获取当前云平台指定FxUserIds的收件人数据库配置
        /// 20250115，无调用处，参数化处理，新增NewDbNameConfigId，优先使用这个
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<ReceDbConfigModel> GetDbConfigModels(List<int> fxUserIds)
        {
            var cpt = CustomerConfig.CloudPlatformType;
            if (fxUserIds == null || !fxUserIds.Any())
                return null;
            //是否开启缓存
            if (IsEnabledReceDbConfigCache == false)
            {
                var db = DbConnection;
                var sql = @"
    SELECT *
    FROM ReceDbServerConfig s WITH(NOLOCK)
        INNER JOIN ReceDbNameConfig n WITH(NOLOCK)
            ON s.Id = n.DbServerConfigId
        INNER JOIN ReceDbConfig d WITH(NOLOCK)
            ON n.Id = d.NewDbNameConfigId
                WHERE d.FxUserId IN @fxUserIds AND d.DbCloudPlatform = @cpt;";
                if (IsUseMySql) sql = TranSql(sql);
                var models = db.Query<ReceDbServerConfig, ReceDbNameConfig, ReceDbConfig, ReceDbConfigModel>(sql, 
                    (server, dbname, config) =>
                {
                    var temp = new ReceDbConfigModel { ReceDbServer = server, ReceDbNameConfig = dbname, ReceDbConfig = config };
                    return temp;
                    }, 
                    new { fxUserIds, cpt }, 
                    splitOn: "Id,Id,Id,Id").ToList();
                return models;
            }
            //缓存
            var dbConfigModels = new List<ReceDbConfigModel>();
            fxUserIds.ForEach(m =>
            {
                var dbConfigModel = GetDbConfigModel(m, cpt);
                if (dbConfigModel == null)
                {
                    return;
                }
                dbConfigModels.Add(dbConfigModel);
            });
            return dbConfigModels;
        }
        
        /// <summary>
        /// 根据配置获取收件人数据库配置
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="cpt"></param>
        /// <returns></returns>
        public ReceDbConfigModel GetNewDbConfigModel(int fxUserId, string cpt = "")
        {
            // 如果cpt为空，则使用当前云平台
            if (cpt == "") cpt = CustomerConfig.CloudPlatformType;
            // 先获取新库，如果没有则获取旧库
            var model = GetDbConfigModel(fxUserId, cpt) ?? GetDbConfigModel(fxUserId, cpt, false);

            // 如果还是为空，则考虑是不是迁移问题，从配置库获取
            if (model == null && IsUseMySql)
            {
                var oldRepository = new ReceDbConfigRepository(CustomerConfig.ConfigureDbConnectionString, false);
                // 获取配置库的配置
                model = oldRepository.GetNewDbConfigModel(fxUserId, cpt);
                
                // 如果不为空，则插入到新库
                if (model != null) TryToCreateCloudReceDbConfig(fxUserId, model.ReceDbNameConfig.Id, cpt);
            }
            
            // 还是为空，创建对应的记录
            if (model == null) model = GetDefaultDbConfigModel(fxUserId, cpt);
            
            // 如果还是为空，则抛出异常
            if (model == null) throw new LogicException("收件人配置创建失败");
            
            if (model.IsNewDb == false)
            {
                // 如果获取到旧库，配置新库Id，更新并重新获取
                UpdateNewDbNameConfigIds(new List<ReceDbConfig> { model.ReceDbConfig });
                if (model.ReceDbConfig.NewDbNameConfigId > 0)
                {
                    model.IsNewDb = true;
                } else Log.Debug(() => $"获取新库Id失败，FxUserId：{fxUserId}");
            } 
            var isUseNewDbId = GetSwitchNewDbConfigId(fxUserId);
            if (isUseNewDbId == false)
            {
                // 如果关闭新库开关，则获取旧库
                model = GetDbConfigModel(fxUserId, cpt, false);
            }

            return model;
        }

        /// <summary>
        /// 获取当前云平台指定FxUserId的收件人数据库配置。无记录使用默认的并创建当前云平台对应记录。
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="cpt"></param>
        /// <param name="isNew">是否查询新库</param>
        /// <returns></returns>
        public ReceDbConfigModel GetDbConfigModel(int fxUserId, string cpt = "", bool isNew = true)
        {
            if (fxUserId < 0) return null;

            //是否开启缓存
            if (IsEnabledReceDbConfigCache == false)
            {
                if (string.IsNullOrEmpty(cpt))
                    cpt = CustomerConfig.CloudPlatformType;

                var onStr = isNew ? "d.NewDbNameConfigId" : "d.DbNameConfigId";
                var db = DbConnection;

                var sql = $@"
    SELECT *
    FROM ReceDbServerConfig s WITH(NOLOCK)
        INNER JOIN ReceDbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
        INNER JOIN ReceDbConfig d WITH(NOLOCK) ON n.Id = {onStr}
    WHERE d.FxUserId = @fxUserId AND d.DbCloudPlatform = @cpt;";

                if (IsUseMySql) sql = TranSql(sql);

                var model = db.Query<ReceDbServerConfig, ReceDbNameConfig, ReceDbConfig, ReceDbConfigModel>(sql, (server, dbname, config) =>
                {
                    var temp = new ReceDbConfigModel { ReceDbServer = server, ReceDbNameConfig = dbname, ReceDbConfig = config };
                    return temp;
                }, new { fxUserId, cpt }, splitOn: "Id,Id,Id").FirstOrDefault();
                if (isNew && model != null)  model.IsNewDb = true;

                return model;
            }
            //获取配置
            var dbConfig = GetDbConfigWithCache(fxUserId, cpt);
            if (dbConfig == null)
            {
                return null;
            }
            //获取服务器配置
            var dbNameId = isNew ? dbConfig.NewDbNameConfigId : dbConfig.DbNameConfigId;
            var dbConfigModel = GetDbConfigModelByDbNameId(dbNameId);
            if (dbConfigModel == null)
            {
                return null;
            }
            dbConfigModel.ReceDbConfig = dbConfig;
            if (isNew) dbConfigModel.IsNewDb = true;
            return dbConfigModel;
        }
        
        public ReceDbConfig GetDbConfigWithCache(int fxUserId, string dbCloudPlatform = "")
        {
            dbCloudPlatform = string.IsNullOrEmpty(dbCloudPlatform) 
                ? CustomerConfig.CloudPlatformType 
                : dbCloudPlatform;
        
            var sql = "SELECT * FROM ReceDbConfig WITH(NOLOCK) WHERE FxUserId = @FxUserId AND DbCloudPlatform = @DbCloudPlatform;";
            if (IsUseMySql) sql = TranSql(sql);
    
            var parameters = new { FxUserId = fxUserId, DbCloudPlatform = dbCloudPlatform };
            var models = DbConnection.QueryWithCache<ReceDbConfig>(sql, parameters, fxUserId.ToString());
    
            if (models?.Any() != true || !models.Any(x => x.NewDbNameConfigId <= 0)) 
                return models?.FirstOrDefault();
            
            // 分配新库Id
            UpdateNewDbNameConfigIds(models);
            return models.FirstOrDefault();
        }

        /// <summary>
        /// 获取指定dbNameId的收件人数据库配置
        /// </summary>
        /// <param name="dbNameId"></param>
        /// <returns></returns>
        public ReceDbConfigModel GetDbConfigModelByDbNameId(int dbNameId)
        {
            return GetDbConfigModelByDbNameId(new List<int> { dbNameId })?.FirstOrDefault();
        }

        /// <summary>
        /// 批量获取指定dbNameId的收件人数据库配置
        /// </summary>
        /// <param name="dbNameIds"></param>
        /// <returns></returns>
        public List<ReceDbConfigModel> GetDbConfigModelByDbNameId(List<int> dbNameIds)
        {
            if (dbNameIds == null || !dbNameIds.Any())
                return null;
            //是否开启缓存
            if (IsEnabledReceDbConfigCache == false)
            {
                var db = DbConnection;

                // 使用参数化查询
                var sql = @"
    SELECT *
    FROM ReceDbServerConfig s WITH(NOLOCK)
    INNER JOIN ReceDbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
    WHERE n.Id IN @dbNameIds;";

                if (IsUseMySql) sql = TranSql(sql);

                var model = db.Query<ReceDbServerConfig, ReceDbNameConfig, ReceDbConfigModel>(sql, (server, dbname) =>
                {
                    var temp = new ReceDbConfigModel { ReceDbServer = server, ReceDbNameConfig = dbname };
                    return temp;
                }, new { dbNameIds }, splitOn: "Id,Id").ToList();

                return model;
            }
            //获取所有配置
            var models = GetReceDbNameConfigsWithCache();
            //过滤
            var dbConfigs = models.Where(m => dbNameIds.Contains(m.Id))
                .Select(m => new ReceDbConfigModel
                {
                    ReceDbServer = m.DbServer, ReceDbNameConfig = m
                }).ToList();
            //返回
            return dbConfigs;
        }

        /// <summary>
        /// 获取默认的收件人数据库配置
        /// </summary>
        /// <param name="fxUserId">大于0，同时创建DbConfig记录</param>
        /// <param name="cpt"></param>
        /// <returns></returns>
        public ReceDbConfigModel GetDefaultDbConfigModel(int fxUserId = 0, string cpt = "")
        {
            if (string.IsNullOrEmpty(cpt))
                cpt = CustomerConfig.CloudPlatformType;
            var newDbNameId = GetHashDefaultDbId(fxUserId, cpt);
            var key = $"/SystemSetting/ReceDbNameConfigId/{cpt}Cloud";
            var dbNameConfigId = newDbNameId == 0
                ? new CommonSettingRepository().Get(key, 0)?.Value?.ToInt() ?? 0
                : newDbNameId;
            if (dbNameConfigId > 0)
            {
                if (fxUserId > 0)
                {
                    //为fxUserId创建DbConfig记录
                    TryToCreateCloudReceDbConfig(fxUserId, dbNameConfigId, cpt, dbNameConfigId);
                }
                return GetDbConfigModelByDbNameId(dbNameConfigId);
            }
            return null;
        }

        /// <summary>
        /// 获取新库的默认连接串列表
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="cpt"></param>
        /// <returns></returns>
        public int GetHashDefaultDbId(int fxUserId, string cpt = null)
        {
            cpt = string.IsNullOrEmpty(cpt) 
                ? CustomerConfig.CloudPlatformType 
                : cpt;
            
            var key = $"/System/Fendan/Rece/DefaultDbNameConfigIds/{cpt}Cloud";
            return GetHashDefaultDbIds(fxUserId, key);
        } 

        /// <summary>
        /// 获取所有收件人数据库配置
        /// </summary>
        /// <returns></returns>
        public List<ReceDbConfigModel> GetAllDbConfigModel()
        {
            if (IsEnabledReceDbConfigCache == false)
            {
                var db = DbConnection;
                
                var sql = $@"
            SELECT *
            FROM ReceDbServerConfig s WITH(NOLOCK)
                INNER JOIN ReceDbNameConfig n WITH(NOLOCK)
                    ON s.Id = n.DbServerConfigId;";
                if (IsUseMySql) sql = TranSql(sql);
                var model = db.Query<ReceDbServerConfig, ReceDbNameConfig, ReceDbConfigModel>(sql, (server, dbname) =>
                {
                    var temp = new ReceDbConfigModel { ReceDbServer = server, ReceDbNameConfig = dbname };
                    return temp;
                }, splitOn: "Id,Id,Id").ToList();
                return model;
            }

            var models = GetReceDbNameConfigsWithCache();
            return models.Select(m => new ReceDbConfigModel { ReceDbServer = m.DbServer, ReceDbNameConfig = m })
                .ToList();
        }

        /// <summary>
        /// 获取所有收件人数据库名
        /// </summary>
        /// <param name="cloudPlatform">云平台名称</param>
        /// <returns></returns>
        public List<ReceDbNameConfig> GetAllDbConfigName(string cloudPlatform)
        {
            if (string.IsNullOrEmpty(cloudPlatform))
                cloudPlatform = CustomerConfig.CloudPlatformType;
            //是否开启缓存
            if (IsEnabledReceDbConfigCache == false)
            {
                var db = DbConnection;
                // 使用参数化查询
                var sql = @"
    SELECT n.* 
    FROM ReceDbNameConfig n WITH(NOLOCK) 
    INNER JOIN ReceDbServerConfig sc WITH(NOLOCK) ON sc.Id = n.DbServerConfigId AND sc.Location = @cloudPlatform;";
                if (IsUseMySql) sql = TranSql(sql);
                var list = db.Query<ReceDbNameConfig>(sql, new { cloudPlatform }).ToList();


                return list;
            }     

            //关联
            var models = GetReceDbNameConfigsWithCache()
                .Where(ns => ns.DbServer.Location == cloudPlatform)
                .ToList();

            return models;
        }

        /// <summary>
        ///  批量获取指定FxUserId的收件人数据库配置
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="cloudPlatform">当前云平台</param>
        /// <param name="strFields"></param>
        /// <returns></returns>
        public List<ReceDbConfig> GetDbConfigByFxUserId(List<int> fxUserIds, string cloudPlatform, string strFields = "*")
        {
            if (fxUserIds?.Any() != true) return new List<ReceDbConfig>();

            var result = IsEnabledReceDbConfigCache
                ? GetDbConfigsFromCache(fxUserIds, cloudPlatform)
                : GetDbConfigsFromDatabase(fxUserIds, cloudPlatform, strFields);
            
            result.ForEach(config =>
            {
                config.IsUserNewDbConfigId = GetSwitchNewDbConfigId(config.FxUserId);
            });
            return result;
        }

        /// <summary>
        /// 从缓存中获取配置，如果没有则创建
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="cloudPlatform"></param>
        /// <returns></returns>
        private List<ReceDbConfig> GetDbConfigsFromCache(List<int> fxUserIds, string cloudPlatform)
        {
            var results = new List<ReceDbConfig>();
            var missingUserIds = new List<int>();

            // 从缓存中获取配置
            foreach (var userId in fxUserIds)
            {
                var config = GetDbConfigWithCache(userId, cloudPlatform);
                if (config != null) results.Add(config);
                else missingUserIds.Add(userId);
            }

            if (!missingUserIds.Any()) return results;
            
            // 如果使用MySql，还需要查看配置库是否有数据
            if (IsUseMySql)
            {
                var oldRepository = new ReceDbConfigRepository(CustomerConfig.ConfigureDbConnectionString);
                var oldModels = oldRepository.GetDbConfigByFxUserId(missingUserIds, cloudPlatform);
                if (oldModels?.Any() == true)
                {
                    Log.Debug(() => $"收件人配置插入到新库：{oldModels.ToJson()}");
                    BulkInsert(oldModels);
                    
                    // 获取userId
                    var userIds = oldModels.Select(m => m.FxUserId).ToList();
                    missingUserIds = missingUserIds.Except(userIds).ToList();
                    
                    // 重新获取配置
                    foreach (var userId in userIds)
                    {
                        var newConfig = GetDbConfigWithCache(userId, cloudPlatform);
                        if (newConfig != null) results.Add(newConfig);
                        else Log.WriteError($"收件人配置信息创建后获取失败: {userId}");
                    }
                }
            }
            
            if (!missingUserIds.Any()) return results;
            
            // 处理sqlserver和mysql都为空的配置
            CreateNewRecord(results, cloudPlatform, missingUserIds);

            return results;
        }

        /// <summary>
        /// 从数据库中获取配置，如果没有则创建
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="cloudPlatform"></param>
        /// <param name="strFields"></param>
        /// <returns></returns>
        private List<ReceDbConfig> GetDbConfigsFromDatabase(List<int> fxUserIds, string cloudPlatform, string strFields)
        {
            // 查询字段必含NewDbNameConfigId，否则无法判断是否新库
            string fields;
            if (strFields == "*") fields = strFields;
            else fields = !strFields.Contains("NewDbNameConfigId")
                ? $"{strFields},NewDbNameConfigId"
                : strFields;

            var sql = $"SELECT {fields} FROM ReceDbConfig WITH(NOLOCK) WHERE FxUserId IN @fxUserIds AND DbCloudPlatform = @cloudPlatform;";
            if (IsUseMySql) sql = TranSql(sql);
            var models = DbConnection.Query<ReceDbConfig>(sql, new { fxUserIds, cloudPlatform }).ToList();
            
            if (IsUseMySql) models = HandleMissingConfigs(models, fxUserIds, cloudPlatform, fields);

            UpdateNewDbNameConfigIds(models);

            return models;
        }

        /// <summary>
        /// 处理为空的配置
        /// </summary>
        /// <param name="models"></param>
        /// <param name="fxUserIds"></param>
        /// <param name="cloudPlatform"></param>
        /// <param name="fields"></param>
        /// <returns></returns>
        private List<ReceDbConfig> HandleMissingConfigs(List<ReceDbConfig> models, List<int> fxUserIds, string cloudPlatform, string fields)
        {
            if (models.Count == fxUserIds.Count) return models;

            var missingUserIds = fxUserIds.Except(models.Select(m => m.FxUserId)).ToList();
            if (!missingUserIds.Any()) return models;

            var oldRepository = new ReceDbConfigRepository(CustomerConfig.ConfigureDbConnectionString);
            var oldModels = oldRepository.GetDbConfigByFxUserId(missingUserIds, cloudPlatform, fields) ?? new List<ReceDbConfig>();

            Log.Debug(() => $"收件人配置插入到新库：{oldModels.ToJson()}");

            // 清理缓存
            if (IsEnabledReceDbConfigCache)
                missingUserIds.ForEach(id => DbConnection.DeleteQueryCache<ReceDbConfig>(id.ToString()));

            BulkInsert(oldModels);
            models.AddRange(oldModels);
            
            // 获取userId
            var userIds = oldModels.Select(m => m.FxUserId).ToList();
            missingUserIds = missingUserIds.Except(userIds).ToList();
            
            if (!missingUserIds.Any()) return models;
            CreateNewRecord(models, cloudPlatform, missingUserIds);

            return models;
        }

        /// <summary>
        /// 创建新的配置
        /// </summary>
        /// <param name="models"></param>
        /// <param name="cloudPlatform"></param>
        /// <param name="missingUserIds"></param>
        private void CreateNewRecord(List<ReceDbConfig> models, string cloudPlatform, List<int> missingUserIds)
        {
            foreach (var userId in missingUserIds)
            {
                var defaultDbNameId = GetHashDefaultDbId(userId, cloudPlatform);

                if (defaultDbNameId > 0)
                {
                    // 清理缓存
                    if (IsEnabledReceDbConfigCache) DbConnection.DeleteQueryCache<ReceDbConfig>(userId.ToString());
                
                    // 创建新的配置
                    TryToCreateCloudReceDbConfig(userId, defaultDbNameId, cloudPlatform, defaultDbNameId);
        
                    // 重新获取配置
                    var newConfig = GetDbConfigWithCache(userId, cloudPlatform);
                    if (newConfig != null) models.Add(newConfig);
                    else Log.WriteError($"收件人配置信息创建后获取失败: {userId}");
                } else Log.WriteError($"收件人配置信息获取新库失败: {userId}");
            }
        }

        /// <summary>
        /// 更新新库Id
        /// </summary>
        /// <param name="models"></param>
        private void UpdateNewDbNameConfigIds(List<ReceDbConfig> models)
        {
            var updateList = models
                .Where(m => m.NewDbNameConfigId <= 0)
                .Select(m =>
                {
                    var newDbNameId = GetHashDefaultDbId(m.FxUserId);
                    if (newDbNameId <= 0) return null;

                    m.NewDbNameConfigId = newDbNameId;
                    return m;
                })
                .Where(m => m != null)
                .ToList();

            if (!updateList.Any()) return;

            BulkUpdateNewDbNameId(updateList);

            if (IsEnabledReceDbConfigCache)
                updateList.ForEach(m => DbConnection.DeleteQueryCache<ReceDbConfig>(m.FxUserId.ToString()));
        }
        
        public List<ReceDbNameConfig> GetAllReceDbNameConfigs()
        {
            //是否开启缓存
            if (IsEnabledReceDbConfigCache == false)
            {
                var db = DbConnection;
                var sql = $@"SELECT * FROM ReceDbNameConfig AS n WITH(NOLOCK)
INNER JOIN ReceDbServerConfig AS s WITH(NOLOCK) ON n.DbServerConfigId=s.Id
WHERE n.RunningStatus='Running'	AND s.Location=@cpt";
            if (IsUseMySql) sql = TranSql(sql);
            var cpt = CustomerConfig.CloudPlatformType;
            
                var dbNameConfigs = db.Query<ReceDbNameConfig, ReceDbServerConfig, ReceDbNameConfig>(sql, (n, s) =>
                {
                    if (n != null && s != null)
                        n.DbServer = s;
                    return n;
            }, new { cpt }).ToList();
                return dbNameConfigs;
            }
            //关联
            var models = GetReceDbNameConfigsWithCache()
                .Where(ns =>
                    ns.RunningStatus == "Running" && ns.DbServer.Location == CustomerConfig.CloudPlatformType)
                .ToList();

            return models;
        }

        /// <summary>
        /// 获取收件人所有配置，并缓存
        /// </summary>
        /// <returns></returns>
        public List<ReceDbNameConfig> GetReceDbNameConfigsWithCache()
        {
            //数据库名称配置
            var dbNameConfigs = GetDbNameConfigsWithCache();
            if (dbNameConfigs == null || dbNameConfigs.Any() == false)
            {
                return new List<ReceDbNameConfig>();
            }

            //服务器配置
            var dbServerConfigs = GetDbServerConfigsWithCache();
            if (dbServerConfigs == null || dbServerConfigs.Any() == false)
            {
                return new List<ReceDbNameConfig>();
            }

            //关联
            var models = dbNameConfigs.Join(dbServerConfigs, nc => nc.DbServerConfigId, sc => sc.Id, (nc, sc) =>
                {
                    nc.DbServer = sc;
                    return nc;
                }).ToList();

            return models;
        }

        /// <summary>
        /// 查询服务器信息，并缓存
        /// </summary>
        /// <returns></returns>
        public List<ReceDbServerConfig> GetDbServerConfigsWithCache()
        {
            var sql = "SELECT * FROM ReceDbServerConfig WITH(NOLOCK)";
            if (IsUseMySql) sql = TranSql(sql);
            return DbConnection.QueryWithCacheAndSecondCache<ReceDbServerConfig>(sql);
        }

        /// <summary>
        /// 查询名称信息，并缓存
        /// </summary>
        /// <returns></returns>
        public List<ReceDbNameConfig> GetDbNameConfigsWithCache()
        {
            var sql = "SELECT * FROM ReceDbNameConfig WITH(NOLOCK)";
            if (IsUseMySql) sql = TranSql(sql);
            return DbConnection.QueryWithCacheAndSecondCache<ReceDbNameConfig>(sql, secondCacheExpireSeconds: 60);
        }

        public void GetAllReceiverDataMaxIds()
        {
            var dbNameConfigs = GetAllReceDbNameConfigs();
            var dic = new Dictionary<string, int>();
            var maxIdModels = new List<MaxIdModel>();
            foreach (var dbNameConfig in dbNameConfigs)
            {
                try
                {
                    //4.实例化数据库连接
                    var conn = dbNameConfig.DbServer.ConnectionString.Replace("$database$", dbNameConfig.DbName);
                    var db = Data.Dapper.DbUtility.GetMySqlConnection(conn);
                    var sql = $"SELECT Id FROM `receiver` ORDER BY `Id` desc limit 1";
                    var id = db.Query<int>(sql).FirstOrDefault();
                    //if (dic.ContainsKey(dbNameConfig.DbName) == false)
                    //    dic.Add(dbNameConfig.DbName, id);
                    var model = new MaxIdModel
                    {
                        MaxId = id,
                        DbKey = dbNameConfig.DbName,
                        TableName = "receiver"
                    };
                    maxIdModels.Add(model);
                }
                catch (Exception ex)
                {
                    Log.Debug($"收件人数据库：{dbNameConfig.ToJson()}，查询Id最大值异常：{ex}");
                }
            }

            //var json = dic.ToList().OrderByDescending(x => x.Value).ToJson();
            //Log.Debug($"收件人数据表最大id值：{json}");
            Log.WriteLine($"收件人数据表最大id值：{maxIdModels.OrderByDescending(x => x.MaxId).ToJson()}", "ReceiverMaxId.txt");
        }
        
        /// <summary>
        /// 根据唯一索引获取实体，MySql
        /// </summary>
        /// <returns></returns>
        public List<ReceDbConfig> GetListByUniqueIndex(List<ReceDbConfig> list)
        {
            if (list == null || !list.Any()) return new List<ReceDbConfig>();
            if (DbConnection.IsMySqlDb() == false) return new List<ReceDbConfig>();

            var result = new List<ReceDbConfig>();
            var db = DbConnection;
            const string sql = "SELECT * FROM ReceDbConfig WHERE FxUserId=@fxUserId AND DbCloudPlatform=@dbCloudPlatform";
            const string sqlNullPlatform = "SELECT * FROM ReceDbConfig WHERE FxUserId=@fxUserId AND DbCloudPlatform IS NULL";

            foreach (var x in list)
            {
                var query = x.DbCloudPlatform == null ? sqlNullPlatform : sql;
                var parameters = new DynamicParameters();
                if (x.DbCloudPlatform == null)
                {
                    parameters.Add("fxUserId", x.FxUserId);
                }
                else
                {
                    parameters.Add("fxUserId", x.FxUserId);
                    parameters.Add("dbCloudPlatform", x.DbCloudPlatform);
                }
                var model = db.QueryFirstOrDefault<ReceDbConfig>(query, parameters);
                if (model != null) result.Add(model);
            }

            return result;
        }

        /// <summary>
        /// 根据唯一索引fxUserId和dbCloudPlatform批量更新实体NewDbNameConfigId
        /// </summary>
        /// <param name="list"></param>
        /// <param name="batchSize"></param>
        public void BulkUpdateNewDbNameId(List<ReceDbConfig> list, int batchSize = 1000)
        {
            if (list == null || !list.Any()) return;

            var db = DbConnection;
            const string sql = "UPDATE ReceDbConfig SET NewDbNameConfigId=@NewDbNameConfigId WHERE FxUserId=@FxUserId AND DbCloudPlatform=@DbCloudPlatform";

            // 分批处理
            for (var i = 0; i < list.Count; i += batchSize)
            {
                var batch = list.Skip(i).Take(batchSize).ToList();
            
                try
                {
                    db.ExecuteWithNoSyncToOtherConfigDb(sql, batch);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"批量更新收件人数据库新库配置失败：{ex.Message}");
                }
            }
        }
    }
}
