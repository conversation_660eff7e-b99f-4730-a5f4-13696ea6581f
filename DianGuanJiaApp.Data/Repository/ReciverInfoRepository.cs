using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Entity;

namespace DianGuanJiaApp.Data.Repository
{
    
    public partial class ReciverInfoRepository : BaseRepository<ReciverInfo>
    {
        public ReciverInfoRepository() { }
        public ReciverInfoRepository(string connectionString) : base(connectionString) { }

        public bool BulkWrite(List<ReciverInfo> recivers)
        {
            if (recivers == null || !recivers.Any())
                return false;
                                      
            try
            {
                BulkWrite(recivers, "P_ReciverInfo");
            }
            catch (Exception ex)
            {
                return false;
            }
            return true;
        }
    }
}
