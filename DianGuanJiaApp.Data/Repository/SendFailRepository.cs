using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using Z.Dapper.Plus;
using System.Linq.Expressions;
using DianGuanJiaApp.Data.Model;
using System.Data;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.EntityExtension;
using DianGuanJiaApp.Data.Enum;
using System.IO;
using Newtonsoft.Json.Linq;

namespace DianGuanJiaApp.Data.Repository
{

    public partial class SendFailRepository : BaseRepository<SendFail>
    {
        public SendFailRepository()
        {
        }
        public SendFailRepository(string connectionString) : base(connectionString)
        {
        }

        /// <summary>
        /// 根据SendFailCode查询
        /// </summary>
        /// <param name="sendFailCode"></param>
        /// <param name="fxUserId">创建者Id</param>
        /// <param name="fields">字段集</param>
        /// <returns></returns>
        public SendFail GetBySendFailCode(string sendFailCode, int fxUserId, List<string> fields)
        {
            var strField = "Id,SendFailCode,PlatformOrderId,LogicOrderId,ShopId,PlatformType,LogistiscBillNo,CreateTime";
            if (fields != null && fields.Any())
                strField = string.Join(",", fields);
            var sql = $"SELECT TOP 1 {strField} FROM SendFail WITH(NOLOCK) WHERE SendFailCode=@SendFailCode AND IsDeleted=0 AND CreateBy=@CreateBy ";
            return this.DbConnection.Query<SendFail>(sql, new { SendFailCode = sendFailCode, CreateBy = fxUserId }).FirstOrDefault();
        }

        /// <summary>
        /// 根据SendFailCode列表查询
        /// </summary>
        /// <param name="sendFailCodes"></param>
        /// <param name="fxUserId">创建者Id</param>
        /// <param name="fields">字段集</param>
        /// <returns></returns>
        public List<SendFail> GetListBySendFailCode(List<string> sendFailCodes, int fxUserId, List<string> fields)
        {
            var strField = "s.Id,SendFailCode,PlatformOrderId,LogicOrderId,ShopId,PlatformType,LogistiscBillNo,s.CreateTime";
            if (fields != null && fields.Any())
                strField = string.Join(",", fields);
            var sql = $"SELECT {strField} FROM SendFail s WHERE SendFailCode WITH(NOLOCK) IN @SendFailCodes AND IsDeleted=0 AND s.CreateBy=@CreateBy ";
            if (strField.ToLower().IndexOf("inputparam") > 0)//关联SendFailRequest表
                sql = $"SELECT {strField} FROM SendFail s WITH(NOLOCK) INNER JOIN SendFailRequest r WITH(NOLOCK) ON s.RequestCode=r.RequestCode WHERE s.SendFailCode IN @SendFailCodes AND s.IsDeleted=0 AND s.CreateBy=@CreateBy ";

            return this.DbConnection.Query<SendFail>(sql, new { SendFailCodes = sendFailCodes, CreateBy = fxUserId }).ToList();
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<SendFailResult>> GetSendFailList(SendFailQuery query)
        {
            var strField = "s.Id,s.SendFailCode,s.PlatformOrderId,s.LogicOrderId,s.ShopId,s.PlatformType,s.ExpressId,s.LogistiscBillNo,s.Reason,s.SendType,s.Method,s.CreateTime,so.LogicOrderId OLogicOrderId,p.Id,p.SendFailCode,p.OrderItemCode,p.Quantity";
            if (query.Fields != null && query.Fields.Any())
                strField = string.Join(",", query.Fields);

            var sql_count = $@"SELECT COUNT(1) AS TCount FROM(
SELECT DISTINCT o.[SendFailCode]  FROM SendFail o WITH(NOLOCK)
INNER JOIN SendFailOrderProduct p WITH(NOLOCK) ON o.SendFailCode=p.SendFailCode 
INNER JOIN SendFailOrder so ON so.SendFailCode=o.SendFailCode 
WHERE o.IsDeleted=0 AND o.CreateBy=@CreateBy @whereStr@ ) AS t ";
            var sql_where = $@"SELECT SendFailCode FROM SendFail o WITH(NOLOCK) WHERE o.IsDeleted=0 AND o.CreateBy=@CreateBy ";
            var sql = $@"SELECT {strField}
FROM SendFail s WITH(NOLOCK)
INNER JOIN SendFailOrderProduct p WITH(NOLOCK) ON s.SendFailCode=p.SendFailCode 
INNER JOIN SendFailOrder so ON so.SendFailCode=s.SendFailCode";
            if (query.IsNeedInputParam)
                sql += $@" LEFT JOIN SendFailRequest r WITH(NOLOCK) ON s.RequestCode=r.RequestCode";

            string whereStr = "";
            var parameters = new DynamicParameters();
            parameters.Add("CreateBy", query.FxUserId);

            if (!string.IsNullOrEmpty(query.PlatformType))
            {
                whereStr += " AND o.PlatformType=@PlatformType";
                parameters.Add("PlatformType", query.PlatformType);
            }
            if (!string.IsNullOrEmpty(query.PlatformOrderId))
            {
                whereStr += " AND o.PlatformOrderId LIKE @PlatformOrderId";
                parameters.Add("PlatformOrderId", $"%{query.PlatformOrderId}%");
            }
            if (query.SourceShopIds != null && query.SourceShopIds.Any())
            {
                whereStr += $" AND o.ShopId IN ({string.Join(",", query.SourceShopIds.Distinct().ToList())})";
            }
            if (query.StartTime != null && query.EndTime != null)
            {
                whereStr += $" AND o.CreateTime BETWEEN @StartTime AND @EndTime ";
                parameters.Add("StartTime", query.StartTime.Value);
                parameters.Add("EndTime", query.EndTime.Value);
            }
            if (!string.IsNullOrWhiteSpace(query.PlatformOrderIds))
            {
                whereStr += " AND o.PlatformOrderId IN @PlatformOrderIds";
                parameters.Add("PlatformOrderIds", query.PlatformOrderIds.SplitToList(",").Distinct().ToList());
            }
            if (!string.IsNullOrEmpty(query.LogicOrderId))
            {
                whereStr += $" AND o.LogicOrderId LIKE @LogicOrderId";
                parameters.Add("LogicOrderId", $"%{query.LogicOrderId}%");
            }
            if (!string.IsNullOrWhiteSpace(query.LogicOrderIds))
            {
                whereStr += $" AND o.LogicOrderId IN @LogicOrderIds";
                parameters.Add("LogicOrderIds", query.LogicOrderIds.SplitToList(",").Distinct().ToList());
            }
            if (query.IsHandle != null)
            {
                whereStr += " AND o.IsHandle=@IsHandle";
                parameters.Add("IsHandle", query.IsHandle.Value);
            }
            if(query.Ids != null && query.Ids.Any())
            {
                whereStr += " AND o.Id IN @Ids";
                parameters.Add("Ids", query.Ids);
            }

            sql_count = sql_count.Replace("@whereStr@", whereStr);

            sql_where += whereStr;
            sql_where += " ORDER BY Id DESC";
            sql_where += $" OFFSET {(query.PageIndex - 1) * query.PageSize} ROWS FETCH NEXT {query.PageSize} ROWS ONLY ";

            sql += $" WHERE s.SendFailCode IN ({sql_where}) ORDER BY p.Id DESC";
            if (query.IsExport)
                sql_count = "SELECT 0 AS TCount";

            var db = this.DbConnection;
            var grid = db.QueryMultiple(sql_count + " ; " + sql, param: parameters);
            var totalCount = grid.Read<int>().FirstOrDefault();

            Log.Debug(GetRealSql(sql_count + " ; " + sql, parameters));

            var productList = new List<SendFailOrderProductResult>();
            var productDicts = new Dictionary<string, SendFailOrderProductResult>();
            var orderDicts = new Dictionary<string, SendFailOrder>();
            var lookUp = new Dictionary<string, SendFailResult>();
            grid.Read<SendFailResult, SendFailOrderProductResult, SendFailResult>((o, p) =>
            {
                //订单级
                SendFailResult porder = null;
                var key = o.SendFailCode;
                if (!lookUp.TryGetValue(key, out porder))
                {
                    porder = o;
                    lookUp.Add(key, porder);
                }

                //商品项
                SendFailOrderProductResult product = null;
                var pkey = p.Id.ToString();
                if (!productDicts.TryGetValue(pkey, out product))
                {
                    product = p;
                    productDicts.Add(pkey, product);
                }

                if (!string.IsNullOrEmpty(o.OLogicOrderId))
                {
                    //订单子项
                    SendFailOrder so = null;
                    var okey = o.SendFailCode + o.OLogicOrderId;
                    if (!orderDicts.TryGetValue(okey, out so))
                    {
                        so = new SendFailOrder
                        {
                            LogicOrderId = o.OLogicOrderId,
                            SendFailCode = o.SendFailCode
                        };
                        orderDicts.Add(okey, so);
                    }
                }

                return porder;
            }).ToList();

            var result = lookUp.Values.ToList();

            //更新结果集的商品项和子订单项
            if (result != null && result.Any())
            {
                result.ForEach(order =>
                {
                    order.ProductResults = productDicts.Values.Where(b => b.SendFailCode == order.SendFailCode).ToList();
                    order.OLogicOrderIds = orderDicts.Values.Where(b => b.SendFailCode == order.SendFailCode)?.Select(a => a.LogicOrderId).ToList(); ;
                });
            }

            return Tuple.Create(totalCount, result);
        }

        /// <summary>
        /// 更新IsHandle
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int UpdateIsHandle(SendFailBatchUpdate model)
        {
            var sql = "UPDATE SendFail SET IsHandle=@UpdateStatus,UpdateBy=@UpdateBy,UpdateTime=GETDATE() WHERE SendFailCode IN @SendFailCodes AND IsDeleted=0 AND CreateBy=@UpdateBy";

            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("UpdateStatus", model.UpdateStatus);
            parameters.Add("UpdateBy", model.UpdateBy);
            parameters.Add("SendFailCodes", model.SendFailCodes);

            return DbConnection.Execute(sql, parameters);
        }

        /// <summary>
        /// 逻辑删除
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int SetDeleted(SendFailBatchUpdate model)
        {
            var sql = @"UPDATE s
SET s.IsDeleted=1,s.UpdateBy=@UpdateBy,s.UpdateTime=GETDATE() 
FROM SendFail AS s
INNER JOIN dbo.FunStringToTable (@codes,',') ON item = s.SendFailCode
WHERE IsDeleted=0 AND CreateBy=@UpdateBy";

            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("UpdateBy", model.UpdateBy);
            parameters.Add("codes", string.Join(",", model.SendFailCodes));

            return DbConnection.Execute(sql, parameters);
        }

        /// <summary>
        /// 逻辑删除（指定LogistiscBillNo）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public int SetDeletedByBillNo(string logistiscBillNo, int fxUserId)
        {
            var sql = "UPDATE SendFail SET IsDeleted=1,UpdateBy=@UpdateBy,UpdateTime=GETDATE() WHERE LogistiscBillNo=@logistiscBillNo AND IsDeleted=0 AND CreateBy=@UpdateBy";

            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("UpdateBy", fxUserId);
            parameters.Add("logistiscBillNo", logistiscBillNo);

            return DbConnection.Execute(sql, parameters);
        }

        /// <summary>
        /// 逻辑删除（指定LogicOrderIds）
        /// </summary>
        /// <param name="logicOrderIds"></param>
        /// <param name="fxUserId"></param>
        /// <param name="isAll">为0时，只针对首次发货</param>
        /// <returns></returns>
        public int SetDeleted(List<string> logicOrderIds, int fxUserId, int isAll = 1)
        {
            var sql = "UPDATE SendFail SET IsDeleted=1,UpdateBy=@UpdateBy,UpdateTime=GETDATE() WHERE LogicOrderId IN @LogicOrderIds AND IsDeleted=0 AND CreateBy=@UpdateBy";
            if (isAll == 0)
                sql += " AND SendType=0";

            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("UpdateBy", fxUserId);
            parameters.Add("LogicOrderIds", logicOrderIds);

            return DbConnection.Execute(sql, parameters);
        }

        /// <summary>
        /// 统计记录数
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public int GetTotalCount(int fxUserId)
        {
            var sql = "SELECT COUNT(1) FROM SendFail WITH(NOLOCK) WHERE IsDeleted=0 AND CreateBy=@UpdateBy";
            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("UpdateBy", fxUserId);
            return DbConnection.ExecuteScalar<int>(sql, parameters);
        }
    }
}
