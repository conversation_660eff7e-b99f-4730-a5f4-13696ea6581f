using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Data.Repository
{
    public partial class ShareWaybillCodeRecordRepository : BaseRepository<ShareWaybillCodeRecord>
    {
        public ShareWaybillCodeRecordRepository() : base(Utility.CustomerConfig.ConfigureDbConnectionString)
        {
        }

        public void InsertList(List<ShareWaybillCodeRecord> list)
        {
            list.ForEach(sw => this.DbConnection.Insert(sw));
        }

        public new ShareWaybillCodeRecord Get(int id)
        {
            var sql = "SELECT * FROM P_ShareWaybillCodeRecord WITH(NOLOCK) WHERE id = @id";
            return ExcuteFuncByCloundPlatform<ShareWaybillCodeRecord>(sql, new { id })?.FirstOrDefault();
        }


        public List<ShareWaybillCodeRecord> GetModelListByIds(List<int> branchIds, string fields)
        {
            if (string.IsNullOrWhiteSpace(fields)) fields = "*";
            var sql = $"SELECT {fields} FROM dbo.P_ShareWaybillCodeRecord WITH(NOLOCK) WHERE  ShareRelationId IN @ids";
            //return _repository.DbConnection.Query<ShareWaybillCodeRecord>(sql, new { ids = branchIds })?.ToList();
            return ExcuteFuncByCloundPlatform<ShareWaybillCodeRecord>(sql, new { ids = branchIds })?.ToList();
        }


        public int GetSumCountByTemplateIds(List<int> tpids)
        {
            var sql = "SELECT SUM(COUNT) FROM dbo.P_ShareWaybillCodeRecord WITH(NOLOCK) WHERE Templateid IN @tpids ";
            //return _repository.DbConnection.ExecuteScalar<int>(sql, new { tpids });
            return ExcuteFuncByCloundPlatform<int>(sql, new { tpids }).FirstOrDefault();
        }


        public List<int> GetToIds(List<int> templateIds)
        {
            if (templateIds == null || templateIds.Any() == false) return new List<int>();
            var sql = @"SELECT DISTINCT t1.ToId  FROM dbo.P_BranchShareRelation AS t1 WITH(NOLOCK) 
INNER JOIN dbo.P_ShareWaybillCodeRecord AS t2 WITH(NOLOCK) ON t1.Id = t2.ShareRelationId
WHERE t2.TemplateId IN @templateIds";
            //return _repository.DbConnection.Query<int>(sql, new { templateIds = templateIds })?.ToList();
            return ExcuteFuncByCloundPlatform<int>(sql, new { templateIds = templateIds }).ToList();
        }


    }
}
