using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Data.Repository
{
    public class StatSkuSettlementRepository : BaseRepository<StatSkuSettlement>
    {
        public StatSkuSettlementRepository() : base(CustomerConfig.ConfigureDbConnectionString)
        {

        }
        
        public StatSkuSettlementRepository(string connectionString) : base(connectionString)
        {

        }

    }
}