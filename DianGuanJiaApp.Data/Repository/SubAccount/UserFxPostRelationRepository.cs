using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Data.Repository
{

    public class UserFxPostRelationRepository : BaseRepository<Entity.UserFxPostRelation>
    {
        private string _connectionString = string.Empty;
        public UserFxPostRelationRepository() : base(Utility.CustomerConfig.ConfigureDbConnectionString)
        {
        }

        public UserFxPostRelationRepository(string connectionString) : base(connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// 获取用户职位列表
        /// </summary>
        /// <param name="createUserId">创建人</param>
        /// <param name="fxUserId">子账号Id</param>
        /// <param name="status"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public List<UserFxPostRelation> GetUserPostList(int createUserId, int fxUserId, int status = 1,
            string selectFields = "*")
        {
            //查询字段为空
            if (string.IsNullOrWhiteSpace(selectFields))
            {
                selectFields = "*";
            }

            var sql = $@"SELECT {selectFields} FROM UserFxPostRelation WITH(NOLOCK) 
                         WHERE CreateUserId = @createUserId AND FxUserId = @fxUserId AND Status = @status;";

            return DbConnection.Query<UserFxPostRelation>(sql, new { createUserId, fxUserId, status }).ToList();
        }

        /// <summary>
        /// 获取用户职位列表
        /// </summary>
        /// <param name="createUserId"></param>
        /// <param name="postCode"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        public List<UserFxPostRelation> GetUserPostListByPostCode(int createUserId, string postCode, int status = 1)
        {
            var sql = @"SELECT * FROM UserFxPostRelation WITH(NOLOCK) 
                        WHERE CreateUserId = @createUserId AND PostCode = @postCode AND Status = @status;";
            return DbConnection.Query<UserFxPostRelation>(sql, new { createUserId, postCode, status }).ToList();
        }

        /// <summary>
        /// 批量获取用户职位列表
        /// </summary>
        /// <param name="createUserIds"></param>
        /// <param name="postCodes"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        public List<UserFxPostRelation> GetUserPostListByPostCode(List<int> createUserIds, List<string> postCodes, int status = 1, List<string> selectFields = null)
        {
            var result = new List<UserFxPostRelation>();
            if (postCodes.IsNullOrEmptyList() || createUserIds.IsNullOrEmptyList())
                return result;
            var fieldStr = "*";
            if (selectFields.IsNotNullAndAny())
            {
                fieldStr = string.Join(",", selectFields); 
            }
            var sql = $@"SELECT {fieldStr} FROM UserFxPostRelation WITH(NOLOCK)
                         INNER JOIN FunStringToTable(@postCodes,',') t ON t.Item = PostCode
                         WHERE CreateUserId IN @createUserIds AND Status = @status;";

            return DbConnection.Query<UserFxPostRelation>(sql, new { createUserIds, postCodes, status }).ToList();

        }

        /// <summary>
        /// 新增用户职位
        /// </summary>
        /// <param name="postPermissions"></param>
        /// <returns></returns>
        public void BatchAdd(List<UserFxPostRelation> relation)
        {
            if (relation.IsNullOrEmptyList())
                return ;
            using (var db = DbConnection)
            {
                if (db.State != System.Data.ConnectionState.Open)
                    db.Open();
                relation.ForEach(r =>
                {
                    db.Insert(r);
                });
            }
        }

        /// <summary>
        /// 通过Id删除用户职位 软删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public int BatchDelete(List<int> ids,int createUserId)
        {
            if (ids.IsNullOrEmptyList())
                return 0;
            var sql = $"UPDATE UserFxPostRelation SET Status = -1 WHERE Id IN@ids AND CreateUserId = @createUserId";
            return DbConnection.Execute(sql, new { ids , createUserId });
        }

        /// <summary>
        /// 更新职位
        /// </summary>
        /// <param name="id"></param>
        /// <param name="newPostCode"></param>
        /// <returns></returns>
        public bool UpdatePostCode(int id, string newPostCode,bool isRefreshCache = true,int subFxUserId = 0)
        {
            using (var db = DbConnection)
            {
                if (db.State != System.Data.ConnectionState.Open)
                    db.Open();
                var result = db.Execute($"UPDATE UserFxPostRelation SET PostCode = @newPostCode,UpdateTime=GETDATE() WHERE Id=@id", new { id, newPostCode }) > 0;
                
                if (isRefreshCache)
                {
                    if (subFxUserId <= 0)
                        subFxUserId = db.QueryFirst<int>($"SELECT FxUserId FROM UserFxPostRelation WHERE Id=@id", new { id });
                    FxCaching.RefeshRemoteCache(FxCachingType.FxUserPermission, $"sub_{subFxUserId}");
                }
                return result;
            };
        }
        /// <summary>
        /// 删除子账号与职位码的关联关系（通过子账号ID，一对一关系）
        /// </summary>
        /// <param name="fxUserid"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public bool DeleteByFxUserId(int fxUserid,bool isRefreshCache = true)
        {
            var result = DbConnection.Execute($"update UserFxPostRelation set Status=-1,UpdateTime=GETDATE() where FxUserId=@fxUserid and Status=1", new { fxUserid }) > 0;
            if(isRefreshCache)
                FxCaching.RefeshRemoteCache(FxCachingType.FxUserPermission, $"sub_{fxUserid}");
            return result;
        }

        /// <summary>
        /// 删除子账号与职位码的关联关系（通过创建人ID）
        /// </summary>
        /// <param name="createUserId"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public bool DeleteByCreateUserId(int createUserId)
        {
            var result = DbConnection.Execute($"UPDATE UserFxPostRelation SET Status=-1,UpdateTime=GETDATE() WHERE CreateUserId=@createUserId AND Status=1", new { createUserId }) > 0;
            
            return result;
        }


    }   
}
