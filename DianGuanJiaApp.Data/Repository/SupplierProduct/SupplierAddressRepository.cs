using System;
using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity.SupplierProduct;
using DianGuanJiaApp.Utility.Extension;
using MySql.Data.MySqlClient;
using Z.Dapper.Plus;

namespace DianGuanJiaApp.Data.Repository.SupplierProduct
{
    /// <summary>
    /// 供应商地址仓储层
    /// </summary>
    public class SupplierAddressRepository : SupplierProductBaseRepository<SupplierAddress>
    {
        /// <summary>
        /// 默认获取的数据库连接
        /// </summary>
        public SupplierAddressRepository()
        {
        }

        /// <summary>
        /// 根据连接字符串和数据库类型获取数据库连接
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="isUseMySql"></param>
        public SupplierAddressRepository(string connectionString, bool isUseMySql) : base(connectionString, isUseMySql)
        {
        }

        /// <summary>
        /// 添加地址
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public new long Add(SupplierAddress model)
        {
            if (model == null)
                return 0;

            model.UpdateTime = DateTime.Now;
            var db = DbConnection;
            if (db is MySqlConnection)
                return db.InsertMysqlWithLongId(model) ?? 0;
            else
                return db.Insert(model).ToLong();
        }

        /// <summary>
        /// 更新地址
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public new bool Update(SupplierAddress model)
        {
            if (model == null)
                return false;

            model.UpdateTime = DateTime.Now;
            var db = DbConnection;
            if (db is MySqlConnection)
                return db.UpdateMysql(model) > 0;
            else
                return db.Update(model);
        }

        /// <summary>
        /// 根据AddressCode获取地址信息
        /// </summary>
        /// <param name="addressCode"></param>
        /// <returns></returns>
        public SupplierAddress GetByAddressCode(string addressCode)
        {
            if (string.IsNullOrEmpty(addressCode))
                return null;

            var sqlTemplate = "SELECT * FROM SupplierAddress WHERE AddressCode=@AddressCode";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Query<SupplierAddress>(sql, new { AddressCode = addressCode }).FirstOrDefault();
        }

        /// <summary>
        /// 根据用户ID和地址类型获取地址列表
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="addressType">地址类型 (0:商品发货地址, 1:名片地址)</param>
        /// <returns></returns>
        public List<SupplierAddress> GetByFxUserIdAndType(int fxUserId, int? addressType = null)
        {
            var sqlTemplate = "SELECT * FROM SupplierAddress WHERE FxUserId=@FxUserId";
            if (addressType.HasValue)
                sqlTemplate += " AND AddressType=@AddressType";
            sqlTemplate += " ORDER BY CreateTime DESC";

            var sql = TranSql(sqlTemplate);
            return DbConnection.Query<SupplierAddress>(sql, new { FxUserId = fxUserId, AddressType = addressType }).ToList();
        }

        /// <summary>
        /// 根据商品ID获取发货地址
        /// </summary>
        /// <param name="productUid"></param>
        /// <returns></returns>
        public List<SupplierAddress> GetByProductUid(long productUid)
        {
            var sqlTemplate = "SELECT * FROM SupplierAddress WHERE ProductUid=@ProductUid AND AddressType=0 ORDER BY CreateTime DESC";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Query<SupplierAddress>(sql, new { ProductUid = productUid }).ToList();
        }

        /// <summary>
        /// 根据ID获取地址信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public SupplierAddress GetById(long id)
        {
            var sqlTemplate = "SELECT * FROM SupplierAddress WHERE Id=@Id";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Query<SupplierAddress>(sql, new { Id = id }).FirstOrDefault();
        }

        /// <summary>
        /// 删除地址（物理删除）
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public bool Delete(long id)
        {
            var sqlTemplate = "DELETE FROM SupplierAddress WHERE Id=@Id";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Execute(sql, new { Id = id }) > 0;
        }

        /// <summary>
        /// 根据AddressCode删除地址
        /// </summary>
        /// <param name="addressCode"></param>
        /// <returns></returns>
        public bool DeleteByAddressCode(string addressCode)
        {
            if (string.IsNullOrEmpty(addressCode))
                return false;

            var sqlTemplate = "DELETE FROM SupplierAddress WHERE AddressCode=@AddressCode";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Execute(sql, new { AddressCode = addressCode }) > 0;
        }

        /// <summary>
        /// 检查AddressCode是否存在
        /// </summary>
        /// <param name="addressCode"></param>
        /// <returns></returns>
        public bool IsExistAddressCode(string addressCode)
        {
            if (string.IsNullOrEmpty(addressCode))
                return false;

            var sqlTemplate = "SELECT COUNT(1) FROM SupplierAddress WHERE AddressCode=@AddressCode";
            var sql = TranSql(sqlTemplate);
            return DbConnection.Query<int>(sql, new { AddressCode = addressCode }).FirstOrDefault() > 0;
        }

        /// <summary>
        /// 批量添加地址
        /// </summary>
        /// <param name="models"></param>
        public void BatchAdd(List<SupplierAddress> models)
        {
            if (models == null || !models.Any())
                return;

            var now = DateTime.Now;
            models.ForEach(m => m.UpdateTime = now);

            var db = DbConnection;
            db.BulkInsert(models);
        }
    }
}
