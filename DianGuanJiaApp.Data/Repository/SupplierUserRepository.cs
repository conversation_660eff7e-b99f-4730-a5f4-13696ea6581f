using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Repository;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility.Extension;
using System.Web.UI.WebControls;
using Nest;
using System.Text.RegularExpressions;
using System.Data;
using DianGuanJiaApp.Utility.SubAccount;
using StackExchange.Redis;
namespace DianGuanJiaApp.Data.Repository
{
    public class SupplierUserRepository : BaseRepository<Entity.SupplierUser>
    {
        public SupplierUserRepository() : base(Utility.CustomerConfig.ConfigureDbConnectionString)
        {

        }

        public Tuple<int, List<SupplierUser>> GetSupplierList(int fxUserId, string key, AgentBingSupplierStatus? status, List<AgentBingSupplierStatus> notStatus, int pageIndex, int pageSize, bool needEncryptAccount = false)
        {
            var hideCancelUser = new CommonSettingRepository().IsShowCancelUser(BaseSiteContext.Current.CurrentShopId);
            if (status != null && status != AgentBingSupplierStatus.Binded && hideCancelUser)
            {
                return Tuple.Create(0, new List<SupplierUser>());
            }
            var isMatch = false;
            if (key.IsNotNullOrEmpty())
            {
                key = key.Trim();
                var regex = new Regex(@"^1\d{10}$");
                isMatch = regex.IsMatch(key);
            }
            if (needEncryptAccount && key.IsNotNullOrEmpty())
            {
                if (key.Contains("*"))
                {
                    key = $"{key.Replace(key.Substring(key.IndexOf("*"), key.LastIndexOf("*") - key.IndexOf("*") + 1), "%")}";
                }
                //如果是手机号（key.length==11）且是纯数字，用精确匹配，否则用%模糊匹配
                else if (key.Length == 11 && !isMatch)
                {
                    key = $"{key.Substring(0, 3)}%{key.Substring(7, 4)}";
                }
            }

            var parameters = new DynamicParameters();
            var sql_option = " option(loop join, force order);";
            var sql_count =
                $"SELECT COUNT(*) FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.SupplierFxUserId = t2.Id WHERE t1.FxUserId = {fxUserId}";
            var sql =
                $@"SELECT {GetFieldsSqlWithTopStatus(false)},
t2.Id,t2.NickName,t2.NickName AS UserName,t2.Mobile 
FROM dbo.P_SupplierUser t1 WITH(NOLOCK) 
INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.SupplierFxUserId = t2.Id 
WHERE t1.FxUserId = {fxUserId}";
            if (string.IsNullOrWhiteSpace(key) == false)
            {
               
                // 非完整手机号
                if (isMatch)
                {
                    //注意，由创建虚拟厂家生成的P_UserFx用户的手机号是一个new的guid,所以这儿要加上Or条件的nickName
                    sql_count += " AND (t2.NickName = @key OR t2.Mobile = @key)";
                    sql += " AND (t2.NickName = @key OR t2.Mobile = @key)";
                    parameters.Add("@key", $"{key}");
                }
                else
                {
                    sql_count += " AND (t2.NickName LIKE @key OR t2.Mobile LIKE @key)";
                    sql += " AND (t2.NickName LIKE @key OR t2.Mobile LIKE @key)";
                    parameters.Add("@key", $"%{key}%");
                }
            }
            if (notStatus.Any())
            {
                var temps = notStatus.Select(s => s.ToInt()).ToArray();
                sql_count += $" AND t1.Status not in @temps";
                sql += $" AND t1.Status not in @temps";
                parameters.Add("@temps", temps);
            }

            if (status.HasValue)
            {
                if (notStatus.Count == 1 && notStatus[0]== AgentBingSupplierStatus.UnBind && status.Value == AgentBingSupplierStatus.UnBind)
                    return Tuple.Create(0, new List<SupplierUser>());

                var temps = notStatus.Select(s => s.ToInt()).ToArray();
                sql_count += $" AND t1.Status = @status";
                sql += $" AND t1.Status = @status";
                parameters.Add("@status", status.Value);
            }
            else if (hideCancelUser)
            {
                sql_count += " AND t1.Status IN(1,2,5,6)";
                sql += " AND t1.Status IN(1,2,5,6)";
            }
            sql += " ORDER BY TopTime Desc, CreateTime DESC";
            sql += $" OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";

            sql_count += sql_option;
            sql += sql_option;
            if (CustomerConfig.IsDebug)
                WriteSqlToLog(sql, parameters, "MySupplier.txt", "MySupplier");

            var multiple = this.DbConnection.QueryMultiple(sql + sql_count, parameters);
            var result = multiple.Read<SupplierUser, UserFx, SupplierUser>((s, u) =>
            {
                if (u != null)
                {
                    s.UserFx = u;
                    s.NickName = u.NickName;
                    s.UserName = u.NickName;
                    s.Mobile = u.Mobile;
                }
                return s;
            }).ToList();

            SetSupplierUserName(result, false, needEncryptAccount: needEncryptAccount);
            var total = multiple.Read<int>().FirstOrDefault();
            return Tuple.Create(total, result);
        }

        public List<SupplierUser> GetSupplierList(int fxUserId, AgentBingSupplierStatus? status = null, bool isIgnoreHideCancelUserSetting = false, bool needEncryptAccount = false)
        {
            var hideCancelUser = !isIgnoreHideCancelUserSetting && new CommonSettingRepository().IsShowCancelUser(BaseSiteContext.Current.CurrentShopId);
            if (status != null && status != AgentBingSupplierStatus.Binded && hideCancelUser)
            {
                return new List<SupplierUser>();
            }
            // 1. 查询P_SupplierUser表
            var sql = $@"SELECT {GetFieldsSqlWithTopStatus(false)} FROM dbo.P_SupplierUser t1 WITH(NOLOCK) WHERE t1.FxUserId = {fxUserId}";
            if (status != null)
            {
                sql += $" AND t1.Status = {status.ToInt()}";
            }
            else if (hideCancelUser)
            {
                sql += " AND t1.Status IN(1,2,5,6)";
            }
            sql += " ORDER BY TopTime Desc, CreateTime DESC";
            var supplierResult = DbConnection.Query<SupplierUser>(sql).ToList();
            
            // 2. 使用公共方法补充用户信息
            var result = GetSupplierUsersWithUserInfo(supplierResult, s => s.SupplierFxUserId, true);

            SetSupplierUserName(result, false, needEncryptAccount: needEncryptAccount);
            return result;
        }

        public Tuple<int, List<SupplierUser>> GetSupplierListByFinancialSettlement(int fxUserId, string key, AgentBingSupplierStatus? status, int pageIndex, int pageSize)
        {
            var sql_option = " option(loop join, force order);";
            var sql_count = "SELECT COUNT(*) FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.SupplierFxUserId = t2.Id WHERE t1.FxUserId = @userId";
            var sql = "SELECT t1.*,t2.NickName,t2.NickName AS UserName,t2.Mobile FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.SupplierFxUserId = t2.Id WHERE t1.FxUserId = @userId";
            if (string.IsNullOrWhiteSpace(key) == false)
            {

                key = key.Trim();
                Regex regex = new Regex(@"^1\d{10}$");
                bool isMatch = regex.IsMatch(key);
                // 是完整手机号
                // 非完整手机号
                if (isMatch)
                {
                    //sql_count += " AND (t2.NickName LIKE @key OR t2.Mobile=@key1)";
                    //sql += " AND (t2.NickName LIKE @key OR t2.Mobile=@key1)";
                    sql_count += " AND (t2.Mobile=@key)";
                    sql += " AND (t2.Mobile=@key)";
                }
                else
                {
                    sql_count += " AND (t2.NickName LIKE @key OR t2.Mobile LIKE @key)";
                    sql += " AND (t2.NickName LIKE @key OR t2.Mobile LIKE @key)";
                    key = $"%{key}%";
                }
            }
            if (status != null)
            {
                sql_count += " AND t1.Status = @status";
                sql += " AND t1.Status = @status";
            }
            sql += " ORDER BY CreateTime DESC";
            sql += $" OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";

            sql += sql_option;
            sql_count += sql_option;
            var multiple = this.DbConnection.QueryMultiple(sql + sql_count, new
            {
                userId = fxUserId,
                key = key,
                status = status
            });
            var result = multiple.Read<SupplierUser>().ToList();
            SetSupplierUserName(result, false);
            var total = multiple.Read<int>().FirstOrDefault();
            return Tuple.Create(total, result);
        }

        public SupplierUser GetSupplierBindInfo(int id)
        {
            var sql = @"SELECT t1.*,t2.NickName,t2.Mobile FROM dbo.P_SupplierUser t1 WITH(NOLOCK) 
INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.SupplierFxUserId = t2.Id WHERE t1.Id = @id option(loop join, force order)";
            var result = this.DbConnection.QueryFirstOrDefault<SupplierUser>(sql, new { id });
            return result;
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="id"></param>
        /// <param name="status"></param>
        /// <param name="isUpdateMemberLevelCode">是否更新MemberLevelCode</param>
        public void Update(int id, AgentBingSupplierStatus status, bool isUpdateMemberLevelCode = false)
        {
            var updateMemberLevelCodeSql = isUpdateMemberLevelCode ? ",MemberLevelCode = null" : string.Empty;
            var sql = status == AgentBingSupplierStatus.Binded
                ? $"UPDATE dbo.P_SupplierUser SET Status = @status,UpdateTime=GETDATE(),LastOpenPrepayTime=GETDATE() {updateMemberLevelCodeSql} WHERE Id = @id"
                : $"UPDATE dbo.P_SupplierUser SET Status = @status,UpdateTime=GETDATE() {updateMemberLevelCodeSql} WHERE Id = @id";
            DeleteCacheById(id);
            DbConnection.Execute(sql, new
            {
                id,
                status
            });
        }

        /// <summary>
        /// 根据当前登录店铺获取厂家或商家数据
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="isSupplier"></param>
        /// <returns></returns>
        public List<SupplierUser> GetByFxUserId(int fxUserId, bool isSupplier, bool needEncryptAccount = false)
        {
            // 1. 查询P_SupplierUser表获取基础数据
            var supplierSql = isSupplier ?
                $"SELECT * FROM P_SupplierUser WITH(NOLOCK) WHERE FxUserId = {fxUserId}" :
                $"SELECT * FROM P_SupplierUser WITH(NOLOCK) WHERE SupplierFxUserId = {fxUserId}";
            
            var supplierUsers = DbConnection.Query<SupplierUser>(supplierSql).ToList();
            
            if (!supplierUsers.Any())
                return new List<SupplierUser>();

            // 2. 使用公共方法补充用户信息
            var userIdSelector = isSupplier ? 
                (Func<SupplierUser, int>)(s => s.SupplierFxUserId) : 
                (Func<SupplierUser, int>)(s => s.FxUserId);
            
            var result = GetSupplierUsersWithUserInfo(supplierUsers, userIdSelector);

            SetSupplierUserName(result, !isSupplier, needEncryptAccount: needEncryptAccount);
            return result;
        }

        private static string EncryptStr(string s)
        {
            var halfIndex = s.Length / 3;
            var code = new StringBuilder();
            for (var i = 0; i < halfIndex; i++)
            {
                code.Append("*");
            }

            return $"{s.Substring(0, halfIndex)}{code}{s.Substring(2 * halfIndex, s.Length - 2 * halfIndex)}";
        }
		
		 /// <summary>
        /// 获取合作关系
        /// </summary>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="selectFields">查询字段</param>
        /// <param name="isSupplier">是否厂家</param>
        /// <returns></returns>
        public List<SupplierUser> GetsByFxUserId(int fxUserId, string selectFields = "f.*, u.*", bool isSupplier = true)
        {
            //查询字段未指定
            if (string.IsNullOrWhiteSpace(selectFields))
            {
                selectFields = "f.*, u.*";
            }

            //查询SQL脚本
            var sql = isSupplier
                ? $"SELECT {selectFields} FROM P_UserFx u WITH(NOLOCK) INNER JOIN P_SupplierUser f WITH(NOLOCK) ON u.Id = f.SupplierFxUserId WHERE f.FxUserId = @FxUserId"
                : $"SELECT {selectFields} FROM P_UserFx u WITH(NOLOCK) INNER JOIN P_SupplierUser f WITH(NOLOCK) ON u.Id = f.FxUserId WHERE f.SupplierFxUserId = @FxUserId";
            var lookUp = new Dictionary<int, SupplierUser>();

            //执行查询
            DbConnection.Query<SupplierUser, UserFx, SupplierUser>(sql, (su, u) =>
            {
                SupplierUser s = null;
                if (lookUp.TryGetValue(su.Id, out s))
                {
                    return null;
                }
                su.UserFx = u;
                su.NickName = u.NickName;
                su.Mobile = u.Mobile;
                lookUp.Add(su.Id, su);
                return null;
            }, param: new { FxUserId = fxUserId });
            var list = lookUp.Values.ToList();
            return list;
        }
		
        /// <summary>
        /// 获取用户信息（包含P_UserFx和Bkunbind_P_UserFx）
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="fields">查询字段，默认为"*"</param>
        /// <returns>用户信息字典</returns>
        private Dictionary<int, UserFx> GetUserInfoByIds(List<int> userIds, string fields = "*")
        {
            if (!userIds.Any())
                return new Dictionary<int, UserFx>();

            var param = new DynamicParameters();
            param.Add("@userIds", userIds);
            
            // 1. 查询P_UserFx表
            var userSql = $"SELECT {fields} FROM P_UserFx WITH(NOLOCK) WHERE Id IN @userIds";
            if (userIds.Count > 5)
            {
                userSql = $"SELECT {fields} FROM P_UserFx WITH(NOLOCK) INNER JOIN dbo.FunStringToIntTable(@userIds, ',') t ON Id = t.item";
                param = new DynamicParameters(new { userIds = string.Join(",", userIds) });
            }
            var userResult = DbConnection.Query<UserFx>(userSql, param).ToDictionary(u => u.Id);

            // 2. 查询Bkunbind_P_UserFx表补充数据
            if (userResult.Count != userIds.Count)
            {
                var unbindUserIds = userIds.Where(id => !userResult.ContainsKey(id)).ToList();
                var unbindUserSql = $"SELECT {fields} FROM Bkunbind_P_UserFx WITH(NOLOCK) WHERE Id IN @unbindUserIds";
                var param2 = new DynamicParameters(new { unbindUserIds });
                if (unbindUserIds.Count > 5)
                {
                    unbindUserSql = $"SELECT {fields} FROM Bkunbind_P_UserFx u WITH(NOLOCK) INNER JOIN dbo.FunStringToIntTable(@Codes, ',') t ON u.Id = t.item";
                    param2 = new DynamicParameters(new { Codes = string.Join(",", unbindUserIds) });
                }

                var unbindUserFxDic = DbConnection.Query<UserFx>(unbindUserSql, param2)
                    .GroupBy(u => u.Id)
                    .Select(g => g.First())
                    .ToDictionary(u => u.Id);
                
                // 补充到字典中
                foreach (var unbindUserFx in unbindUserFxDic.Where(unbindUserFx => !userResult.ContainsKey(unbindUserFx.Key)))
                {
                    unbindUserFx.Value.IsDelete = true;
                    userResult[unbindUserFx.Key] = unbindUserFx.Value;
                }
            }

            return userResult;
        }

        /// <summary>
        /// 为供应商用户补充用户信息
        /// </summary>
        /// <param name="supplierUsers">供应商用户列表</param>
        /// <param name="userIdSelector">用户ID选择器</param>
        /// <param name="needUserName">是否补充UserName</param>
        /// <returns>补充了用户信息的供应商用户列表</returns>
        private List<SupplierUser> GetSupplierUsersWithUserInfo(
            List<SupplierUser> supplierUsers, 
            Func<SupplierUser, int> userIdSelector,
            bool needUserName = false)
        {
            if (!supplierUsers.Any())
                return new List<SupplierUser>();

            // 提取用户ID
            var userIds = supplierUsers.Select(userIdSelector).Distinct().ToList();
            
            // 获取用户信息
            var userInfoDic = GetUserInfoByIds(userIds);

            // 合并数据
            var result = supplierUsers.Select(s =>
            {
                var userId = userIdSelector(s);
                UserFx user;
                if (userInfoDic.TryGetValue(userId, out user))
                {
                    s.UserFx = user;
                    s.NickName = user.NickName;
                    if (needUserName) s.UserName = user.NickName;
                    s.Mobile = user.Mobile;
                }
                return s;
            }).ToList();

            return result;
        }

        /// <summary>
        /// 构建批量查询SQL
        /// </summary>
        /// <param name="baseSql">基础SQL</param>
        /// <param name="ids">ID列表</param>
        /// <param name="paramName">参数名</param>
        /// <returns>批量查询SQL</returns>
        private string BuildBatchQuerySql(string baseSql, List<int> ids, string paramName)
        {
            if (ids.Count <= 5)
            {
                return baseSql.Replace($"@{paramName}", $"@{paramName}");
            }
            else
            {
                return baseSql.Replace($"@{paramName}", $"@{paramName}")
                    .Replace($"IN @{paramName}", $"INNER JOIN dbo.FunStringToIntTable(@{paramName}, ',') t ON Id = t.item");
            }
        }

        private void SetSupplierUserName(List<SupplierUser> result, bool isAgent, bool needEncryptAccount = false)
        {

            if (result.IsNullOrEmptyList())
                return;

            var isEncryptAccount = false;
            if (needEncryptAccount)
            {
                // 子账号校验权限 是否要给厂家账号加密
                var subFxUser = BaseSiteContext.CurrentNoThrow.SubFxUserId;
                if (subFxUser > 0)
                {
                    var userPermission = BaseSiteContext.CurrentNoThrow.PermissionTags;
                    if (!userPermission.IsNullOrEmptyList())
                    {
                        var needPermission = isAgent ? FxPermission.AgentAccount : FxPermission.SupplierAccount;
                        isEncryptAccount = !userPermission?.Contains(needPermission) ?? false;
                    }
                    else
                    {
                        isEncryptAccount = true;
                    }
                }
            }

            result?.ForEach(x =>
            {
                if (isEncryptAccount)
                {
                    if (x.Mobile.IsNotNullOrEmpty())
                    {
                        x.Mobile = EncryptUtil.EncryptAccount(x.Mobile);
                    }
                    if (x.UserFx != null && x.UserFx.Mobile.IsNotNullOrEmpty())
                    {
                        x.UserFx.Mobile = EncryptUtil.EncryptAccount(x.Mobile);
                    }
                }

                if (x.SupplierType == "Virtual")
                {
                    x.UserName = x.NickName + "(虚拟)";
                    x.Mobile = x.NickName + "(虚拟)";//商品列表销售供货类型展示
                }
                else
                {
                    if (string.IsNullOrEmpty(x.NickName))
                    {
                        x.NickName = x.Mobile;
                        if (isAgent)
                            x.UserName = string.IsNullOrWhiteSpace(x.Remark) ? x.Mobile : $"{x.Mobile}({x.Remark})";
                        else
                            x.UserName = string.IsNullOrWhiteSpace(x.RemarkName) ? x.Mobile : $"{x.Mobile}({x.RemarkName})";
                    }
                    else
                    {
                        if (isAgent)
                            x.UserName = string.IsNullOrWhiteSpace(x.Remark) ? $"{x.Mobile}({x.NickName})" : $"{x.Mobile}({x.NickName})({x.Remark})";
                        else
                            x.UserName = string.IsNullOrWhiteSpace(x.RemarkName) ? $"{x.Mobile}({x.NickName})" : $"{x.Mobile}({x.NickName})({x.RemarkName})";
                    }
                }
            });
        }

        public List<SupplierUser> GetByUserFxIds(List<int> fxUserIds, bool isSupplier)
        {
            var sql = isSupplier ?
                $"SELECT f.*,u.* FROM P_UserFx u WITH(NOLOCK) INNER JOIN P_SupplierUser f WITH(NOLOCK) ON u.Id = f.FxUserId WHERE u.Id IN({string.Join(",", fxUserIds)})" :
                $"SELECT f.*,u.* FROM P_UserFx u WITH(NOLOCK) INNER JOIN P_SupplierUser f WITH(NOLOCK) ON u.Id = f.SupplierFxUserId WHERE u.Id IN({string.Join(",", fxUserIds)})";
            var lookUp = new Dictionary<int, SupplierUser>();
            DbConnection.Query<SupplierUser, UserFx, SupplierUser>(sql, (su, u) =>
            {
                SupplierUser s = null;
                if (!lookUp.TryGetValue(su.Id, out s))
                {
                    su.UserFx = u;
                    lookUp.Add(su.Id, su);
                }
                return null;
            }).ToList();
            var list = lookUp.Values.ToList();
            SetSupplierUserName(list, !isSupplier);
            return list;
        }


        public int EditAgentRemark(int id, string remark)
        {
            var sql = "UPDATE dbo.P_SupplierUser SET Remark=@remark WHERE Id=@id";
            var res = DbConnection.Execute(sql, new { remark = (remark ?? ""), id = id });
            DeleteCacheById(id);
            return res;
        }
        public int EditSupplierRemark(int id, string remark)
        {
            var sql = "UPDATE dbo.P_SupplierUser SET RemarkName=@remark WHERE Id=@id";
            var res = DbConnection.Execute(sql, new { remark = (remark ?? ""), id = id });
            DeleteCacheById(id);
            return res;
        }



        /// <summary>
        /// 根据厂家和商家ID查询关联关系
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="supplierUserIds"></param>
        /// <returns></returns>
        public List<SupplierUser> GetByFxIds(List<int> fxUserIds, List<int> supplierUserIds)
        {
            var sql = $"SELECT * FROM dbo.P_SupplierUser us WITH(NOLOCK) WHERE us.FxUserId IN@fxUserIds AND us.SupplierFxUserId IN@supplierUserIds";
            return DbConnection.Query<SupplierUser>(sql, new { fxUserIds, supplierUserIds }).ToList();
        }
        /// <summary>
        /// 获取供应商关系
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="selectFields"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<SupplierUser> GetListByFxUserIds(List<int> fxUserIds, string selectFields = "*",
            string whereFieldName = "SupplierFxUserId")
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return new List<SupplierUser>();
            }

            var sql =
                $"SELECT {selectFields} FROM dbo.P_SupplierUser us WITH(NOLOCK) WHERE us.{whereFieldName} IN@FxUserIds";
            return DbConnection.Query<SupplierUser>(sql, new { FxUserIds = fxUserIds }).ToList();
        }
        /// <summary>
        /// 根据厂家和商家ID查询关联关系
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="supplierUserIds"></param>
        /// <returns></returns>
        public List<SupplierUser> GetSupplierUserAndFxUserByFxIds(List<int> fxUserIds, List<int> supplierUserIds)
        {
            var sql = $@"SELECT t1.*,t2.Id,t2.NickName,t2.NickName AS UserName,t2.Mobile FROM dbo.P_SupplierUser t1 WITH(NOLOCK) 
INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id 
WHERE t1.FxUserId IN @fxUserIds AND t1.SupplierFxUserId IN @supplierUserIds option(loop join, force order)";
            var result = DbConnection.Query<SupplierUser, UserFx, SupplierUser>(sql, (s, u) =>
            {
                if (u != null)
                {
                    s.UserFx = u;
                    s.NickName = u.NickName;
                    s.UserName = u.NickName;
                    s.Mobile = u.Mobile;
                }
                return s;
            }, new { fxUserIds, supplierUserIds }).ToList();
            return result;
        }

        public bool UpdateSendInfo(SupplierUser _newData)
        {
            var sql = "UPDATE dbo.P_SupplierUser SET SenderName=@name,SenderMobile=@mobile,Province=@province,City=@city,District=@district,[Address]=@address,RemarkName=@remarkName,SenderTelePhone=@SenderTelePhone,MemberLevelCode = null,EnableAddress=@EnableAddress";
            if (true)
            {
                sql += ",[Status]=@status";
            }
            if (_newData.CreateBy > 0)
            {
                sql += ",[CreateBy]=@createby";
            }
            if (_newData.Status == AgentBingSupplierStatus.Binded)
            {
                sql += ",[LastOpenPrepayTime]=@lastOpenPrepayTime";
            }
            sql += ",[OpenPrePayTime]=@OpenPrePayTime";

            sql += " WHERE Id = @id";
            var result = DbConnection.Execute(sql, new
            {
                id = _newData.Id,
                status = _newData.Status,
                name = _newData.SenderName,
                mobile = _newData.SenderMobile,
                province = _newData.Province,
                city = _newData.City,
                district = _newData.District,
                address = _newData.Address,
                createby = _newData.CreateBy,
                remarkName = _newData.RemarkName,
                lastOpenPrepayTime = DateTime.Now.ToString(),
                _newData.SenderTelePhone,
                _newData.OpenPrePayTime,
                EnableAddress = _newData.EnableAddress
            });
            DeleteCacheById(_newData.Id);
            return result > 0 ? true : false;
        }

        public Tuple<int, List<SupplierUser>> GetAgentList(int fxUserId, string key, AgentBingSupplierStatus? status, List<AgentBingSupplierStatus> notStatus, int pageIndex, int pageSize, int? isPrePay = null, bool needEncryptAccount = false)
        {
            var hideCancelUser = new CommonSettingRepository().IsShowCancelUser(BaseSiteContext.Current.CurrentShopId);
            if (status != null && status != AgentBingSupplierStatus.Binded && hideCancelUser)
            {
                return Tuple.Create(0, new List<SupplierUser>());
            }

            var whereStr = string.Empty;
            if (isPrePay.HasValue)
                whereStr += $" AND IsPrePay={isPrePay.Value}";
            var isMatch = false;
            if (key.IsNotNullOrEmpty())
            {
                key = key.Trim();
                var regex = new Regex(@"^1\d{10}$");
                isMatch = regex.IsMatch(key);
            }
            if (needEncryptAccount && key.IsNotNullOrEmpty())
            {
                if (key.Contains("*"))
                {
                    key = $"{key.Replace(key.Substring(key.IndexOf("*"), key.LastIndexOf("*") - key.IndexOf("*") + 1), "%")}";
                }
                //如果是手机号（key.length==11）且是纯数字，用精确匹配，否则用%模糊匹配
                else if (key.Length == 11&&!isMatch)
                {
                    key = $"{key.Substring(0, 3)}%{key.Substring(7, 4)}";
                }
            }

            var parameters = new DynamicParameters();
            var sql_option = " option(loop join, force order);";
            var sql_count = $"SELECT COUNT(*) FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id WHERE t1.SupplierFxUserId = {fxUserId}";
            var sql = $@"SELECT {GetFieldsSqlWithTopStatus(true)},
t2.Id,t2.NickName,t2.NickName AS UserName,t2.Mobile 
FROM dbo.P_SupplierUser t1 WITH(NOLOCK) 
INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id 
WHERE t1.SupplierFxUserId = {fxUserId} {whereStr}";
            if (string.IsNullOrWhiteSpace(key) == false)
            {
                // 是完整手机号
                if (isMatch)
                {
                    /*
                     *注意，由创建虚拟厂家生成的P_UserFx用户的手机号是一个new的guid,所以这儿按道理要加上Or条件的nickName
                     *但是根据绑定商家的逻辑，目前是不支持绑定由创建虚拟厂家生成的P_UserFx用户作为商家的,所以不必考虑nickName条件
                     * 为了保险也可以加上无妨，和GetSupplierList保持一样的处理逻辑
                     */
                    sql_count += " AND (t2.NickName = @key OR t2.Mobile = @key)";
                    sql += " AND (t2.NickName = @key OR t2.Mobile = @key)";
                    parameters.Add("@key", $"{key}");
                }
                else
                {
                    sql_count += " AND (t2.NickName LIKE @key OR t2.Mobile LIKE @key)";
                    sql += " AND (t2.NickName LIKE @key OR t2.Mobile LIKE @key)";
                    parameters.Add("@key", $"%{key}%");
                }
            }

            if (notStatus.Any())
            {
                var temps = notStatus.Select(s => s.ToInt()).ToArray();
                sql_count += $" AND t1.Status not in @temps ";
                sql += $" AND t1.Status not in @temps ";
                parameters.Add("@temps", temps);
            }

            if (status != null)
            {
                if (notStatus.Count == 1 && notStatus[0] == AgentBingSupplierStatus.UnBind && status.Value == AgentBingSupplierStatus.UnBind)
                    return Tuple.Create(0, new List<SupplierUser>());

                sql_count += " AND t1.Status = @Status";
                sql += " AND t1.Status = @Status";
                parameters.Add("@Status", status.ToInt());
            }
            else if (hideCancelUser)
            {
                sql_count += " AND t1.Status IN(1,2,5,6)";
                sql += " AND t1.Status IN(1,2,5,6)";
            }
            sql += " ORDER BY TopTime Desc, CreateTime DESC";
            sql += $" OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";

            sql += sql_option;
            sql_count += sql_option;

            if (CustomerConfig.IsDebug)
                WriteSqlToLog(sql, parameters, "MySupplier.txt", "MySupplier");

            var multiple = this.DbConnection.QueryMultiple(sql + sql_count, parameters);
            var result = multiple.Read<SupplierUser, UserFx, SupplierUser>((s, u) =>
            {
                if (u != null)
                {
                    s.UserFx = u;
                    s.NickName = u.NickName;
                    s.UserName = u.NickName;
                    s.Mobile = u.Mobile;
                }
                return s;
            }).ToList();
            var total = multiple.Read<int>().FirstOrDefault();
            SetSupplierUserName(result, true, needEncryptAccount: needEncryptAccount);
            return Tuple.Create(total, result);
        }

        private string GetFieldsSqlWithTopStatus(bool isSupplier)
        {
            var fields = @"
t1.Id,t1.RemarkName,t1.SupplierFxUserId,t1.FxUserId,t1.SenderName,t1.SenderMobile,t1.Province,t1.City,t1.District,t1.Address,
t1.CreateBy,t1.Status,t1.[From],t1.Remark,t1.InviteTime,t1.AcceptTime,t1.CreateTime,t1.SupplierType,t1.SenderTelePhone,t1.UpdateTime,t1.IsPrePay,t1.OpenPrePayTime,
t1.DeliveryMode,t1.IsOpenWangShangPay,t1.ApplyPrepayStatus,t1.ApplyPrepayTime,t1.LastOpenPrepayTime,t1.IsHasSended,t1.MemberLevelCode,t1.EnableAddress";
            return isSupplier
                ? $@"{fields},t1.SupplierSetTopTime AS TopTime,t1.SupplierIsTop AS IsTop"
                : $@"{fields},t1.TopTime,t1.IsTop";
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="statusList"></param>
        /// <param name="getAll">true时和配置是否显示合作关系无关</param>
        /// <returns></returns>
        public List<SupplierUser> GetAgentListV2(List<int> fxUserIds, List<int> statusList = null, bool getAll = false,
            bool ignoreCanelSetting = false,
            string fields = "", int? isPrePay = null, bool isIgnoreHideCancelUserSetting = false,
            bool needEncryptAccount = false)
        {
            return GetAgentListV2New(fxUserIds, statusList, getAll, ignoreCanelSetting, fields, isPrePay, isIgnoreHideCancelUserSetting, needEncryptAccount);
            if (fxUserIds == null || !fxUserIds.Any())
                return new List<SupplierUser>();

            var hideCancelUser = isIgnoreHideCancelUserSetting
                ? false
                : new CommonSettingRepository().IsShowCancelUser(BaseSiteContext.Current.CurrentShopId);
            //if (status != null && status != AgentBingSupplierStatus.Binded && hideCancelUser && !getAll)
            if (statusList != null && statusList.Any() && !(statusList.Count == 1 && statusList.First() == 1) &&
                hideCancelUser && !getAll)
            {
                return new List<SupplierUser>();
            }

            //var fieldStr = $"{GetFieldsSqlWithTopStatus(true)},t2.Id,t2.NickName,t2.NickName AS UserName,t2.Mobile";
            var fieldStr = GetFieldsSqlWithTopStatus(true);

            if (fields.IsNotNullOrEmpty())
            {
                fieldStr = fields;
                if (fieldStr.Contains("IsTop") || fieldStr.Contains("TopTime"))
                {
                    if (!fieldStr.Contains("IsTop"))
                    {
                        fieldStr += ",t1.SupplierIsTop AS IsTop";
                    }
                    else if (!fieldStr.Contains("SupplierIsTop"))
                    {
                        fieldStr = fieldStr.Replace("t1.IsTop", "t1.SupplierIsTop AS IsTop");
                    }

                    if (!fieldStr.Contains("TopTime"))
                    {
                        fieldStr += ",t1.SupplierSetTopTime AS TopTime";
                    }
                    else if (!fieldStr.Contains("SupplierSetTopTime"))
                    {
                        fieldStr = fieldStr.Replace("t1.TopTime", "t1.SupplierSetTopTime AS TopTime");
                    }
                }
                
            }


            var whereSql = string.Empty;
            if (isPrePay.HasValue)
                whereSql += $" AND t1.IsPrePay = {isPrePay.Value}";
            if (statusList != null && statusList.Any())
            {
                whereSql += $" AND t1.Status IN ({string.Join(",", statusList)})";
            }
            else if (!ignoreCanelSetting && hideCancelUser)
            {
                whereSql += " AND t1.Status IN(1,2,5,6)";
            }

            var fxUserIdJoinSql = string.Empty;
            object param = null;
            if (fxUserIds != null && fxUserIds.Count > 5)
            {
                fxUserIdJoinSql = " INNER JOIN dbo.FunStringToIntTable(@Codes, ',')  t ON t1.SupplierFxUserId = t.item";
                param = new
                {
                    Codes = string.Join(",", fxUserIds)
                };
            }
            else
            {
                whereSql += $" AND t1.SupplierFxUserId IN ({string.Join(",", fxUserIds)})";
            }

            //var sql = $@"SELECT {fieldStr} FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id {fxUserIdJoinSql} WHERE 1=1 {whereSql}
            //ORDER BY t1.TopTime Desc, t1.CreateTime DESC option(loop join)";

            var sql =
                $@"SELECT {fieldStr} FROM dbo.P_SupplierUser t1 WITH(NOLOCK) {fxUserIdJoinSql} WHERE 1=1 {whereSql}
            ORDER BY TopTime DESC,t1.CreateTime DESC option(loop join)";

            // var result = this.DbConnection.Query<SupplierUser, UserFx, SupplierUser>(sql, (s, u) =>
            // {
            //     if (u != null)
            //     {
            //         s.UserFx = u;
            //         s.NickName = u.NickName;
            //         s.UserName = u.NickName;
            //         s.Mobile = u.Mobile;
            //     }
            //     return s;
            // }, param: param).ToList();

            //拆分查询，先查询合作关系
            var supplierUsers = DbConnection.Query<SupplierUser>(sql, param: param).ToList();
            if (supplierUsers == null || !supplierUsers.Any())
            {
                return new List<SupplierUser>();
            }

            //查询用户
            fxUserIds = supplierUsers.Select(m => m.FxUserId).Distinct().ToList();
            //var fxUsers = new UserFxRepository().GetListByIdsWithCache(fxUserIds);
            var fxUsers = new UserFxRepository().GetsByIds(fxUserIds);
            if (fxUsers == null || !fxUsers.Any())
            {
                return new List<SupplierUser>();
            }

            //关联查询
            var result = supplierUsers.Join(fxUsers, su => su.FxUserId, u => u.Id, (su, u) =>
            {
                su.UserFx = u;
                su.NickName = u.NickName;
                su.UserName = u.NickName;
                su.Mobile = u.Mobile;
                return su;
            }).ToList();

            SetSupplierUserName(result, true, needEncryptAccount: needEncryptAccount);

            return result;
        }

        /// <summary>
        ///     获取代理商列表
        /// </summary>
        /// <param name="fxUserIds">厂家ID列表</param>
        /// <param name="statusList">状态列表</param>
        /// <param name="getAll">true时和配置是否显示合作关系无关</param>
        /// <param name="ignoreCanelSetting"></param>
        /// <param name="fields"></param>
        /// <param name="isPrePay"></param>
        /// <param name="isIgnoreHideCancelUserSetting"></param>
        /// <param name="needEncryptAccount"></param>
        /// <returns>代理商列表</returns>
        public List<SupplierUser> GetAgentListV2New(List<int> fxUserIds, List<int> statusList = null,
            bool getAll = false, bool ignoreCanelSetting = false,
            string fields = "", int? isPrePay = null, bool isIgnoreHideCancelUserSetting = false,
            bool needEncryptAccount = false)
        {
            // 1. 基础验证
            if (fxUserIds == null || !fxUserIds.Any())
                return new List<SupplierUser>();

            // 2. 检查是否显示已取消的用户
            var hideCancelUser = !isIgnoreHideCancelUserSetting &&
                                 new CommonSettingRepository().IsShowCancelUser(BaseSiteContext.Current.CurrentShopId);
            if (statusList != null && statusList.Any() && !(statusList.Count == 1 && statusList.First() == 1) &&
                hideCancelUser && !getAll) return new List<SupplierUser>();

            // 3. 构建查询条件
            var whereSql = new StringBuilder();
            if (isPrePay.HasValue)
                whereSql.Append($" AND IsPrePay={isPrePay.Value}");

            if (statusList != null && statusList.Any())
                whereSql.Append($" AND Status IN ({string.Join(",", statusList)})");
            else if (!ignoreCanelSetting && hideCancelUser)
                whereSql.Append(" AND Status IN(1,2,5,6)");

            // 4. 构建ID查询参数
            object param;
            string fxUserIdCondition;

            if (fxUserIds.Count > 5)
            {
                fxUserIdCondition = "INNER JOIN dbo.FunStringToIntTable(@Codes, ',') t ON SupplierFxUserId = t.item";
                param = new { Codes = string.Join(",", fxUserIds) };
            }
            else
            {
                fxUserIdCondition = "";
                whereSql.Append(" AND SupplierFxUserId IN @fxUserIds");
                param = new { fxUserIds };
            }

            var fieldStr = "Id,RemarkName,SupplierFxUserId,FxUserId,SenderName,SenderMobile,Province,City,District,Address,CreateBy,Status,[From],Remark,InviteTime,AcceptTime,CreateTime,SupplierType,SenderTelePhone,UpdateTime,IsPrePay,OpenPrePayTime,DeliveryMode,IsOpenWangShangPay,ApplyPrepayStatus,ApplyPrepayTime,LastOpenPrepayTime,IsHasSended,MemberLevelCode,EnableAddress,SupplierIsTop AS IsTop,SupplierSetTopTime AS TopTime";
            // 5. 查询P_SupplierUser
            var supplierSql = $@"SELECT {fieldStr} FROM dbo.P_SupplierUser WITH(NOLOCK) 
                  {fxUserIdCondition}
                  WHERE 1=1 {whereSql}
                  ORDER BY TopTime DESC, CreateTime DESC";

            var supplierUsers = DbConnection.Query<SupplierUser>(supplierSql, param).ToList();

            if (!supplierUsers.Any())
                return new List<SupplierUser>();

            // 6. 提取FxUserIds并解析字段列表
            var fxUserIdsToQuery = supplierUsers.Select(s => s.FxUserId).Distinct().ToList();

            // 7. 解析字段列表
            var userFields = "Id, NickName, Mobile";
            if (!string.IsNullOrEmpty(fields))
            {
                var fieldParts = fields.Split(',')
                    .Where(f => f.Trim().StartsWith("t2."))
                    .Select(f => f.Trim().Substring(3).Trim())
                    .ToList();

                if (fieldParts.Any())
                    userFields = string.Join(", ", fieldParts);
            }

            // 8. 使用公共方法查询用户信息
            var userFxDic = GetUserInfoByIds(fxUserIdsToQuery, userFields);

            // 9. 过滤无效用户并使用公共方法合并数据
            var validUsers = supplierUsers.Where(s => userFxDic.ContainsKey(s.FxUserId)).ToList();
            
            // 10. 合并数据
            foreach (var supplierUser in validUsers)
            {
                var user = userFxDic[supplierUser.FxUserId];

                supplierUser.UserFx = user;
                supplierUser.NickName = user.NickName;
                supplierUser.UserName = user.NickName;
                supplierUser.Mobile = user.Mobile;
            }

            // 11. 设置供应商用户名
            SetSupplierUserName(validUsers, true, needEncryptAccount);

            return validUsers;
        }
        

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="status"></param>
        /// <param name="fields">只可以指定 t1 字段及SupplierUser字段</param>
        /// <param name="isPrePay"></param>
        /// <param name="isIgnoreHideCancelUserSetting"></param>
        /// <param name="needEncryptAccount"></param>
        /// <returns></returns>
        public List<SupplierUser> GetAgentList(int fxUserId, List<AgentBingSupplierStatus> status = null, string fields = "", int? isPrePay = null
            , bool isIgnoreHideCancelUserSetting = false, bool needEncryptAccount = false)
        {
            var statusList = new List<int>();
            if (status != null) statusList = status.Select(x => x.ToInt()).ToList();
            return GetAgentListV2(new List<int> { fxUserId }, statusList, fields: fields, isPrePay: isPrePay, isIgnoreHideCancelUserSetting: isIgnoreHideCancelUserSetting, needEncryptAccount: needEncryptAccount);
        }

        public Tuple<int, List<SupplierUser>> GetAgentListByFinancialSettlement(int fxUserId, string key, AgentBingSupplierStatus? status, int pageIndex, int pageSize)
        {
            var sql_option = " option(loop join, force order);";
            var sql_count = "SELECT COUNT(*) FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id WHERE t1.SupplierFxUserId = @userId";
            var sql = "SELECT t1.*,t2.NickName,t2.NickName AS UserName,t2.Mobile FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id WHERE t1.SupplierFxUserId = @userId";
            if (string.IsNullOrWhiteSpace(key) == false)
            {
                //sql_count += " AND (t2.NickName LIKE @key OR t2.Mobile LIKE @key)";
                //sql += " AND (t2.NickName LIKE @key OR t2.Mobile LIKE @key)";
                key = key.Trim();
                Regex regex = new Regex(@"^1\d{10}$");
                bool isMatch = regex.IsMatch(key);
                // 是完整手机号
                // 非完整手机号
                if (isMatch)
                {
                    sql_count += " AND (t2.Mobile=@key)";
                    sql += " AND (t2.Mobile=@key)";
                }
                else
                {
                    sql_count += " AND (t2.NickName LIKE @key OR t2.Mobile LIKE @key)";
                    sql += " AND (t2.NickName LIKE @key OR t2.Mobile LIKE @key)";
                    key = $"%{key}%";
                }
            }
            if (status != null)
            {
                sql_count += " AND t1.Status = @status";
                sql += " AND t1.Status = @status";
            }
            sql += " ORDER BY CreateTime DESC";
            sql += $" OFFSET {(pageIndex - 1) * pageSize} ROWS FETCH NEXT {pageSize} ROWS ONLY";

            sql += sql_option;
            sql_count += sql_option;

            var multiple = this.DbConnection.QueryMultiple(sql + sql_count, new
            {
                userId = fxUserId,
                key = key,
                status = status
            });
            var result = multiple.Read<SupplierUser>().ToList();
            var total = multiple.Read<int>().FirstOrDefault();

            SetSupplierUserName(result, true);
            return Tuple.Create(total, result);
        }

        /// <summary>
        /// 查询我的厂家的名称信息，可用于下拉框显示
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="isStatusOk">是否是状态正常的，默认true，仅筛选出状态正常的厂家名称信息</param>
        /// <returns></returns>
        public Dictionary<int, string> GetSupplierUserNames(int fxUserId, bool isStatusOk = true)
        {
            var statusExt = " AND t1.Status =1";
            var sqlOption = " option(loop join, force order);";
            if (isStatusOk == false)
                statusExt = "";
            var users = DbConnection.Query<UserFx>($"SELECT DISTINCT t2.Id,t2.NickName,t2.Name,t2.Mobile FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.SupplierFxUserId = t2.Id WHERE t1.FxUserId = {fxUserId} {statusExt} {sqlOption}").ToList();
            return users?.ToDictionary(x => x.Id, x =>
            {
                var temp = x.Mobile;
                if (string.IsNullOrEmpty(x.NickName) == false)
                    temp = x.NickName;
                else if (string.IsNullOrEmpty(x.Name) == false)
                    temp = x.Name;
                return temp;
            });
        }

        public Dictionary<int, string> GetAgentUserNames(int fxUserId, bool isStatusOk = true)
        {
            var statusExt = " AND t1.Status = 1";
            var sqlOption = " option(loop join, force order);";
            if (isStatusOk == false)
                statusExt = "";
            var users = DbConnection.Query<UserFx>($"SELECT distinct t2.Id,t2.NickName,t2.Name,t2.Mobile FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id WHERE t1.SupplierFxUserId = {fxUserId} {statusExt} {sqlOption}").ToList();

            return users?.ToDictionary(x => x.Id, x =>
            {
                var temp = x.Mobile;
                if (string.IsNullOrEmpty(x.NickName) == false)
                    temp = x.NickName;
                else if (string.IsNullOrEmpty(x.Name) == false)
                    temp = x.Name;
                return temp;
            });
        }

        /// <summary>
        /// 获取对应的厂家或商家信息
        /// </summary>
        /// <param name="fxUserId">厂家或商家Id</param>
        /// <param name="curUserId">当前用户Id</param>
        /// <param name="isSupplier">是否为厂家</param>
        /// <returns></returns>
        public SupplierUser GetUserInfoByUserId(int fxUserId, int curUserId, bool isSupplier = false)
        {
            var sql = "SELECT t1.*,t2.Id,t2.NickName,t2.NickName AS UserName,t2.Mobile FROM dbo.P_SupplierUser t1 WITH(NOLOCK) ";
            var joinSql = isSupplier
                ? "INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.SupplierFxUserId = t2.Id WHERE t1.SupplierFxUserId = @fxUserId AND t1.FxUserId = @curUserId"
                : "INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id WHERE t1.FxUserId = @fxUserId AND t1.SupplierFxUserId = @curUserId";
            var optionSql = " option(loop join, force order);";

            var multiple = DbConnection.QueryMultiple($"{sql} {joinSql} {optionSql}", new { curUserId, fxUserId });
            var result = multiple.Read<SupplierUser, UserFx, SupplierUser>((s, u) =>
            {
                if (u != null)
                {
                    s.UserFx = u;
                    s.NickName = u.NickName;
                    s.UserName = u.NickName;
                    s.Mobile = u.Mobile;
                }
                return s;
            }).FirstOrDefault();

            if (result != null) SetSupplierUserName(new List<SupplierUser> { result }, !isSupplier);
            return result;
        }


        public List<int> GetAgentUserId(int fxUserId, bool isStatusOk = true)
        {
            var statusExt = " AND t1.Status = 1";
            var sqlOption = " option(loop join, force order);";
            if (isStatusOk == false)
                statusExt = "";
            var users = DbConnection.Query<int>($"SELECT distinct t2.Id FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id WHERE t1.SupplierFxUserId = {fxUserId} {statusExt} {sqlOption}").ToList();
            return users;
        }

        public List<SupplierUser> GetSupplierUserByIds(List<int> supplierFxUserIds, int fxUserId, int userType)
        {
            var sql = $"SELECT * FROM dbo.P_SupplierUser WITH(NOLOCK) WHERE 1=1  "; //--AND [Status] IN (1,2)
            if (userType == 1)
                sql += $"AND SupplierFxUserId IN@supplierFxUserIds AND FxUserId=@fxUserId";
            else if (userType == 2)
                sql += $"AND FxUserId IN@supplierFxUserIds AND SupplierFxUserId=@fxUserId";

            var param = new DynamicParameters();
            param.Add("fxUserId",fxUserId);

            return BatchQuery(supplierFxUserIds, "supplierFxUserIds", sql, param);
            //return DbConnection.Query<SupplierUser>(sql, new { supplierFxUserIds, fxUserId }).ToList();
        }

        public SupplierUser GetSupplierUserById(int supplierFxUserId, int fxUserId)
        {
            var sql = $"SELECT * FROM dbo.P_SupplierUser WITH(NOLOCK) WHERE SupplierFxUserId =@supplierFxUserId AND FxUserId=@fxUserId ";
            return DbConnection.Query<SupplierUser>(sql, new { supplierFxUserId, fxUserId }).FirstOrDefault();
        }

        public List<int> GetAvaliableSupplierId(int fxUserId, List<int> supplierIds)
        {
            var successStatus = CustomerConfig.AgentBingSupplierSuccessStatus;

            var param = new DynamicParameters();
            param.Add("fxUserId", fxUserId);
            param.Add("status", successStatus);

            return BatchQuery(supplierIds, "supplierIds",
                $"SELECT Id FROM dbo.P_SupplierUser WITH(NOLOCK) WHERE FxUserId=@fxUserId AND SupplierFxUserId IN @supplierIds AND Status IN@status",
                param)
                .Select(t=>t.Id)
                .ToList();
            //return DbConnection.Query<int>($"SELECT Id FROM dbo.P_SupplierUser WITH(NOLOCK) WHERE FxUserId=@fxUserId AND SupplierFxUserId IN @supplierIds AND Status IN(@status)", new { supplierIds }).ToList();
        }

        /// <summary>
        /// 递归获取用户绑定关系
        /// </summary>
        /// <param name="fxUserId">指定用户Id</param>
        /// <param name="isAgent">商家或厂家</param>
        /// <param name="limitLv">递归层数</param>
        public List<SupplierRelationModule> GetUserRelations(int fxUserId, int rootFxUserId = 0, bool isAgent = true, int limitLv = 10)
        {
            var list = new List<SupplierRelationModule>();
            if (limitLv <= 0 || fxUserId <= 0)
                return list;

            var sql = $@"
DECLARE @node INT = {fxUserId}; --表示初始的节点ID,即从指定的哪个节点开始查找
DECLARE @num INT = {limitLv}; --控制递归层数
WITH    temp_cte
          AS ( SELECT   FxUserId ,
                        SupplierFxUserId ,
                        0 lv  -- 查询出“根节点”，即指定的起始节点
               FROM     dbo.P_SupplierUser
               WHERE   {(isAgent ? "SupplierFxUserId=@node" : "FxUserId = @node")} 
               UNION ALL
               SELECT   b.FxUserId ,
                        b.SupplierFxUserId ,
                        a.lv + 1
               FROM     temp_cte a
                        INNER JOIN P_SupplierUser b ON {(isAgent ? "b.SupplierFxUserId = a.FxUserId" : "a.SupplierFxUserId = b.FxUserId")}
               WHERE    a.lv < @num {(rootFxUserId > 0 ? (isAgent ? $"b.FxUserId={rootFxUserId}" : $"b.SupplierFxUserId={rootFxUserId}") : "")}
             )
    SELECT  *
    FROM    temp_cte
    ORDER BY lv";

            list = DbConnection.Query<SupplierRelationModule>(sql).ToList();
            return list;
        }

        public List<FxUserShopSupplierUserModel> GetUnbindSupplierUserByVersion(int version, bool isAgent = false)
        {
            var sql = $@"SELECT DISTINCT fx.ShopId,su.FxUserId,su.SupplierFxUserId FROM dbo.P_Shop s WITH(NOLOCK)
INNER JOIN dbo.P_FxUserShop fx WITH(NOLOCK) ON s.Id=fx.ShopId AND fx.PlatformType='System'
INNER JOIN dbo.P_SupplierUser su WITH(NOLOCK) ON {(isAgent ? "fx.FxUserId = su.SupplierFxUserId" : "fx.FxUserId = su.FxUserId")}
WHERE s.Version='{version}' AND s.PlatformType='System' AND su.Status IN(3,4)";

            var results = DbConnection.Query<FxUserShopSupplierUserModel>(sql).ToList();
            return results;
        }

        /// <summary>
        /// 置顶/取消置顶
        /// </summary>
        /// <param name="id">主键id</param>
        /// <param name="topStatus"></param>
        /// <param name="isSupplier"></param>
        /// <param name="isTop"></param>
        /// <returns></returns>
        public int UpdateTopStatus(int id,bool isSupplier,bool isTop)
        {
            var setTimeSql = string.Empty;
            if (isSupplier)
            {
                setTimeSql = isTop ? "SupplierIsTop = @isTop,SupplierSetTopTime = GETDATE()" : "SupplierIsTop = @isTop,SupplierSetTopTime = null";
            }
            else
            {
                setTimeSql = isTop ? "IsTop = @isTop ,TopTime = GETDATE()" : "IsTop = @isTop  ,TopTime = null";
            }
            var sql = $"UPDATE P_SupplierUser SET {setTimeSql} WHERE Id = @id";

            return DbConnection.Execute(sql, new { id ,isTop});
        }

        /// <summary>
        /// 获取虚拟厂家及对应商家
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        [Obsolete("此方法已经有最新版本，进行拆分查询，今后不能再用此版本，新版方法：GetVirtualSupplierFxUserIdsByUserIds")]
        public List<SupplierFxUserIdModel> GetVirtualSupplierFxUserIds(List<int> fxUserIds)
        {
            return GetVirtualSupplierFxUserIdsNew(fxUserIds);
            const string sql = @"SELECT su.SupplierFxUserId,su.FxUserId FROM P_UserFx uf(NOLOCK) 
                            INNER JOIN P_SupplierUser su(NOLOCK) ON uf.Id = su.SupplierFxUserId 
                            WHERE su.SupplierType = 'Virtual' AND uf.Id IN @Ids;";

            return BatchQuery<SupplierFxUserIdModel>(fxUserIds, "Ids", sql, null);
            //return DbConnection.Query<SupplierFxUserIdModel>(sql, param: new { Ids = fxUserIds }).ToList();
        }

        /// <summary>
        /// 获取虚拟厂家及对应商家（拆分查询）
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<SupplierFxUserIdModel> GetVirtualSupplierFxUserIdsByUserIds(List<int> fxUserIds)
        {
            //判空处理
            if (fxUserIds == null || !fxUserIds.Any())
            {
                return new List<SupplierFxUserIdModel>();
            }
            //SQL脚本
            const string sqlByIn = @"SELECT su.SupplierFxUserId,su.FxUserId FROM P_SupplierUser su WITH(NOLOCK) 
                WHERE su.SupplierType = 'Virtual' AND su.SupplierFxUserId IN @Ids;";
            const string sqlByTableFun = @"SELECT su.SupplierFxUserId,su.FxUserId FROM P_SupplierUser su WITH(NOLOCK) 
                  INNER JOIN dbo.FunStringToIntTable(@Ids,',') t ON t.item = su.SupplierFxUserId 
                  WHERE su.SupplierType = 'Virtual'";
            //查询
            var supplierFxUserIds = SqlOptimizationHandler.QueryEntities(fxUserIds, sqlByIn, sqlByTableFun,
                (sql, param) => DbConnection.Query<SupplierFxUserIdModel>(sql, param).ToList(), "Ids");
            //查询用户
            const string sqlUserByIn = "SELECT Id FROM P_UserFx uf WITH(NOLOCK) WHERE Id IN @Ids;";
            const string sqlUserByTableFun = @"SELECT Id FROM P_UserFx uf WITH(NOLOCK) 
                                      INNER JOIN dbo.FunStringToIntTable(@Ids,',') t ON t.item = uf.Id";
            var fxUsers = SqlOptimizationHandler.QueryEntities(fxUserIds, sqlUserByIn, sqlUserByTableFun,
                (sql, param) => DbConnection.Query<UserFx>(sql, param).ToList(), "Ids");
            //关联
            var models = supplierFxUserIds.Join(fxUsers, sf => sf.SupplierFxUserId, u => u.Id, (sf, u) => sf).ToList();
            //返回
            return models;
        }
        
        /// <summary>
        /// 获取虚拟厂家及对应商家
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<SupplierFxUserIdModel> GetVirtualSupplierFxUserIdsNew(List<int> fxUserIds)
        {
            const string sql = @"SELECT su.SupplierFxUserId, su.FxUserId 
                        FROM P_SupplierUser su(NOLOCK) 
                        INNER JOIN FunStringToIntTable(@Ids, ',') t ON su.SupplierFxUserId = t.item
                        WHERE su.SupplierType = 'Virtual' ";

            const string fxUserSql = "SELECT Id FROM P_UserFx INNER JOIN FunStringToIntTable(@Ids, ',') t ON Id = t.item";
            var ids = string.Join(",", fxUserIds);

            var supplierResult = DbConnection.Query<SupplierFxUserIdModel>(sql, new { Ids = ids }).ToList();
            var fxUserIdsResult = DbConnection.Query<int>(fxUserSql, new { Ids = ids }).ToList();

            var result = supplierResult.Where(x => fxUserIdsResult.Contains(x.SupplierFxUserId)).ToList();

            return result;
        }

        /// <summary>
        /// 获取关联的厂家（带SystemShopId）
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<SupplierUser> GetSuppliers(List<int> fxUserIds)
        {
            var sql = $@"SELECT t1.Id,t1.FxUserId,t1.SupplierFxUserId,t1.RemarkName,t2.ShopId AS SystemShopId
FROM dbo.P_SupplierUser t1 WITH(NOLOCK) 
INNER JOIN dbo.P_FxUserShop t2 WITH(NOLOCK) ON t1.SupplierFxUserId = t2.FxUserId AND t2.PlatformType='System'
WHERE t1.FxUserId IN @fxUserIds ";

            return BatchQuery(fxUserIds, "fxUserIds", sql, null);
            //var results = DbConnection.Query<SupplierUser>(sql, new { fxUserIds = fxUserIds }).ToList();
            //return results;
        }

        /// <summary>
        /// 统计解绑中的合作关系数量
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns>我的厂家，我的商家</returns>
        public Tuple<int, int> GetSupplierUserStatInfo(int fxUserId)
        {
            return GetSupplierUserStatInfoNew(fxUserId);
            var sql = $@"SELECT COUNT(0) FROM dbo.P_SupplierUser t1 WITH(NOLOCK) 
INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.SupplierFxUserId = t2.Id WHERE t1.FxUserId = @fxUserId AND t1.Status =2 ;
SELECT COUNT(0) FROM dbo.P_SupplierUser t1 WITH(NOLOCK) 
INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id WHERE t1.SupplierFxUserId = @fxUserId AND t1.Status =2 ;";
            var multiple = this.DbConnection.QueryMultiple(sql, new { fxUserId = fxUserId });
            var supplierCount = multiple.Read<int>().FirstOrDefault();
            var agentCount = multiple.Read<int>().FirstOrDefault();
            return Tuple.Create(supplierCount, agentCount);
        }

        /// <summary>
        /// 统计解绑中的合作关系数量
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns>我的厂家，我的商家</returns>
        public Tuple<int, int> GetSupplierUserStatInfoNew(int fxUserId)
        {
            // 先处理P_SupplierUser
            const string supplierSql = "SELECT SupplierFxUserId FROM dbo.P_SupplierUser WITH(NOLOCK) WHERE FxUserId = @fxUserId AND Status = 2";
            const string agentSql = "SELECT FxUserId FROM dbo.P_SupplierUser WITH(NOLOCK) WHERE SupplierFxUserId = @fxUserId AND Status = 2";

            var supplierIds = DbConnection.Query<int>(supplierSql, new { fxUserId }).ToList();
            var agentIds = DbConnection.Query<int>(agentSql, new { fxUserId }).ToList();

            // 验证商家和厂家是否存在
            var supplierCount = 0;
            var agentCount = 0;

            if (supplierIds.Any())
            {
                const string validateSupplierSql = "SELECT COUNT(*) FROM dbo.P_UserFx WITH(NOLOCK) INNER JOIN FunStringToIntTable(@ids, ',') t ON Id = t.item";
                supplierCount = DbConnection.QueryFirstOrDefault<int>(validateSupplierSql, new { ids = string.Join(",", supplierIds) });
            }

            if (agentIds.Any())
            {
                const string validateAgentSql = "SELECT COUNT(*) FROM dbo.P_UserFx WITH(NOLOCK) INNER JOIN FunStringToIntTable(@ids, ',') t ON Id = t.item";
                agentCount = DbConnection.QueryFirstOrDefault<int>(validateAgentSql, new { ids = string.Join(",", agentIds) });
            }

            return Tuple.Create(supplierCount, agentCount);
        }

        /// <summary>
        /// 获取用户关联的商家和厂家用户Id
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<int> GetAgentOrSuplierIds(List<int> fxUserIds)
        {
            var sql = $@"SELECT FxUserId FROM P_SupplierUser WITH(NOLOCK) WHERE SupplierFxUserId IN @fxUserIds ;
SELECT SupplierFxUserId FROM P_SupplierUser WITH(NOLOCK) WHERE FxUserId IN @fxUserIds";

            var result = new List<int>();
            using (var db = DbConnection)
            {
                db.Open();
                const int batchSize = 500;
                for (int i = 0; i < fxUserIds.Count; i+=batchSize)
                {
                    var batchIds = fxUserIds.Skip(i).Take(batchSize).ToList();
                    var grid = db.QueryMultiple(sql, param: new { fxUserIds = batchIds});

                    result.AddRange(grid.Read<int>());
                    result.AddRange(grid.Read<int>());
                }
            }
            
            result = result.Distinct().ToList();
            return result;
        }

        /// <summary>
        /// 获取用户关联的商家和厂家用户Id
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns>Item1：商家Id，Item2：厂家Id</returns>
        public List<int> GetAgentOrSupplierIds(int fxUserId, bool getAgent = false)
        {
            var sql = "SELECT SupplierFxUserId FROM P_SupplierUser WITH(NOLOCK) WHERE FxUserId = @fxUserId";

            if (getAgent)
            {
                sql = "SELECT FxUserId FROM P_SupplierUser WITH(NOLOCK) WHERE SupplierFxUserId = @fxUserId ;";
            }
            return DbConnection.Query<int>(sql, param: new { fxUserId }).ToList();
        }

        public List<FxUserShop> GetAgentFxUserShopList(List<int> shopIds)
        {
            var sql = $@"SELECT fx2.* FROM dbo.P_SupplierUser su(NOLOCK)
INNER JOIN dbo.P_FxUserShop fx(NOLOCK) ON su.SupplierFxUserId=fx.FxUserId AND fx.PlatformType='System'
INNER JOIN dbo.P_FxUserShop fx2(NOLOCK) ON su.FxUserId = fx2.FxUserId AND fx2.PlatformType='System'
WHERE fx.ShopId IN({string.Join(",", shopIds)})";
            var fxShops = DbConnection.Query<FxUserShop>(sql).ToList();
            return fxShops;
        }

        /// <summary>
        /// 设置预付状态
        /// </summary>
        /// <param name="supplierFxUserId">厂家FxUserId</param>
        /// <param name="fxUserId">商家FxUserId</param>
        /// <param name="isPrePay">1:开启、其它值:关闭</param>
        /// <param name="deliveryMode">0发下游商家订单；1发1688订单</param>
        /// <returns></returns>
        public int SetPrePay(int supplierFxUserId, int fxUserId, int isPrePay, int? deliveryMode = null)
        {
            var setStr = string.Empty;
            //var nowStr = DateTime.Now.ToString();
            if (deliveryMode.HasValue)
                setStr = $",DeliveryMode={deliveryMode.Value}";

            var sql = $@"UPDATE P_SupplierUser SET IsPrePay=0{setStr} WHERE SupplierFxUserId=@supplierFxUserId AND FxUserId=@fxUserId";

            if (isPrePay == 1)
                sql = $@"UPDATE P_SupplierUser SET IsPrePay=1{setStr},LastOpenPrepayTime=GETDATE() WHERE SupplierFxUserId=@supplierFxUserId AND FxUserId=@fxUserId ;UPDATE P_SupplierUser SET OpenPrePayTime=GETDATE() WHERE SupplierFxUserId=@supplierFxUserId AND FxUserId=@fxUserId AND OpenPrePayTime IS NULL AND IsPrePay=1";
            //删除缓存
            DeleteCacheByFxUserId(fxUserId);
            return DbConnection.Execute(sql, new { supplierFxUserId, fxUserId });
        }

        /// <summary>
        /// 审批设置担保交易状态
        /// </summary>
        /// <param name="supplierFxUserId"></param>
        /// <param name="fxUserId"></param>
        /// <param name="isPrePay"></param>
        /// <returns></returns>
        public int SetApprovePrePay(int supplierFxUserId, int fxUserId, int? isPrePay, bool isKeep = false)
        {
            string sql = string.Empty;
            //string nowStr = DateTime.Now.ToString();
            if (isKeep)
                return 0;
            if (isPrePay == 1)
            {
                if (isKeep)
                {
                    sql = $@"UPDATE P_SupplierUser SET IsPrePay=0 WHERE SupplierFxUserId=@supplierFxUserId AND FxUserId=@fxUserId ";
                }
                sql = $@"UPDATE P_SupplierUser SET ApplyPrepayStatus=1,LastOpenPrepayTime=GETDATE() WHERE SupplierFxUserId=@supplierFxUserId AND FxUserId=@fxUserId ";
            }
            else
            {
                sql = $@"UPDATE P_SupplierUser SET ApplyPrepayStatus=3,LastOpenPrepayTime=GETDATE() WHERE SupplierFxUserId=@supplierFxUserId AND FxUserId=@fxUserId ";
            }
            //删除缓存
            DeleteCacheByFxUserId(fxUserId);
            return DbConnection.Execute(sql, new { supplierFxUserId, fxUserId });
        }

        /// <summary>
        /// 申请设置担保交易状态
        /// </summary>
        /// <param name="supplierFxUserId"></param>
        /// <param name="fxUserId"></param>
        /// <param name="applyPrepayStatus"></param>
        /// <returns></returns>
        public int SetApplyPrepay(int supplierFxUserId, int fxUserId, int applyPrepayStatus)
        {
            //var nowStr = DateTime.Now.ToString();
            var sql =
                $@"UPDATE P_SupplierUser SET ApplyPrepayStatus ={applyPrepayStatus},ApplyPrepayTime=GETDATE() WHERE SupplierFxUserId=@supplierFxUserId AND FxUserId=@fxUserId";
            //删除缓存
            DeleteCacheByFxUserId(fxUserId);
            return DbConnection.Execute(sql, new { supplierFxUserId, fxUserId });
        }

        /// <summary>
        /// 设置发货方式
        /// </summary>
        /// <param name="supplierFxUserId">厂家FxUserId</param>
        /// <param name="fxUserId">商家FxUserId</param>
        /// <param name="deliveryMode">0发下游商家订单；1发1688订单</param>
        /// <returns></returns>
        public int SetDeliveryMode(int supplierFxUserId, int fxUserId, int deliveryMode)
        {
            var sql = $@"UPDATE P_SupplierUser SET DeliveryMode={deliveryMode} WHERE SupplierFxUserId=@supplierFxUserId AND FxUserId=@fxUserId";
            //删除缓存
            DeleteCacheByFxUserId(fxUserId);
            return DbConnection.Execute(sql, new { supplierFxUserId, fxUserId });
        }

        /// <summary>
        /// 设置采购金状态
        /// </summary>
        /// <param name="supplierFxUserId">厂家FxUserId</param>
        /// <param name="isOpen"></param>
        /// <returns></returns>
        public int SetOpenWangshangPay(int supplierFxUserId, bool isOpen)
        {
            var sql = $@"UPDATE P_SupplierUser SET IsOpenWangshangPay={(isOpen ? 1 : 0)} WHERE SupplierFxUserId=@supplierFxUserId";
            //删除缓存
            DeleteCacheBySupplierFxUserId(supplierFxUserId);
            return DbConnection.Execute(sql, new { supplierFxUserId });
        }

        /// <summary>
        /// 预付状态统计
        /// </summary>
        /// <param name="supplierFxUserId">厂家FxUserId</param>
        /// <returns></returns>
        public List<IsPrePayCountModel> GetPrePayCount(int supplierFxUserId)
        {
            var successStatus = CustomerConfig.AgentBingSupplierSuccessStatus;
            var sql = $@"SELECT IsPrePay,COUNT(*) AS AgentCount FROM P_SupplierUser su WITH(NOLOCK)
INNER JOIN P_UserFx uf WITH(NOLOCK) ON uf.Id=su.FxUserId WHERE SupplierFxUserId=@supplierFxUserId AND su.Status IN ({string.Join(",", successStatus)}) GROUP BY IsPrePay";
            var list = DbConnection.Query<IsPrePayCountModel>(sql, new { supplierFxUserId }).ToList();
            return list;
        }

        /// <summary>
        /// 获取已开启预付的相关厂家
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<SupplierUser> GetOpenedPrePaySupplierFxUserIds(List<int> fxUserIds)
        {
            var sql = $@"SELECT FxUserId,SupplierFxUserId FROM dbo.P_SupplierUser WITH(NOLOCK)
WHERE IsPrePay=1 AND FxUserId IN@fxUserIds AND OpenPrePayTime IS NOT NULL";

            return BatchQuery(fxUserIds, "fxUserIds", sql, null);
            //var list = DbConnection.Query<SupplierUser>(sql).ToList();
            //return list;
        }

        /// <summary>
        /// 获取已开启预付的相关商家
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<int> GetOpenedPrePayAgentFxUserIds(int supplierFxUserId)
        {
            var successStatus = CustomerConfig.AgentBingSupplierSuccessStatus;
            var sql = $@"SELECT FxUserId FROM dbo.P_SupplierUser WITH(NOLOCK)
WHERE IsPrePay=1 AND SupplierFxUserId={supplierFxUserId} AND Status IN ({string.Join(",", successStatus)}) AND OpenPrePayTime IS NOT NULL";
            var list = DbConnection.Query<int>(sql).ToList();
            return list;
        }

        public List<SupplierUser> GetSupplierUsers(int fxUserId, List<int> supplierFxUserIds, List<string> fields = null)
        {
            var fieldString = "";
            if (fields != null && fields.Any())
                fieldString = string.Join(",", fields);
            var sql = "SELECT t1.*,t2.NickName,t2.NickName AS UserName,t2.Mobile FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.SupplierFxUserId = t2.Id WHERE t1.FxUserId = @fxUserId AND t1.SupplierFxUserId IN@supplierFxUserIds";
            sql += " option(loop join, force order)";

            var param = new DynamicParameters();
            param.Add("fxUserId",fxUserId);
            var result = BatchQuery(supplierFxUserIds, "supplierFxUserIds", sql, param);
            //var result = DbConnection.Query<SupplierUser>(sql, new { fxUserId, supplierFxUserIds }).ToList();
            SetSupplierUserName(result, false);
            return result;
        }

        /// <summary>
        /// 是否被开启过预付模式
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool IsOpenedPrePay(int fxUserId)
        {
            var sql = "SELECT TOP 1 1 FROM dbo.P_SupplierUser WITH(NOLOCK) WHERE FxUserId=@fxUserId AND OpenPrePayTime IS NOT NULL";
            var result = DbConnection.QueryFirstOrDefault<bool>(sql, new { fxUserId });
            return result;
        }

        /// <summary>
        /// 获取所有被开启预付的商家Id
        /// </summary>
        /// <param name="agentFxUserIds"></param>
        /// <returns></returns>
        public List<int> GetAllOpenedPrePayAgentFxUserId()
        {
            var sql = "SELECT DISTINCT FxUserId FROM dbo.P_SupplierUser WITH(NOLOCK) WHERE IsPrePay=1 AND OpenPrePayTime IS NOT NULL";
            var agentFxUserIds = DbConnection.Query<int>(sql).ToList();
            return agentFxUserIds;
        }

        ///// <summary>
        ///// [厂家或商家]是否开启过预付模式
        ///// </summary>
        ///// <param name="fxUserId"></param>
        ///// <returns></returns>
        //public bool SupplierOrAgentIsOpenedPrePay(int fxUserId)
        //{
        //    //var sql = "SELECT TOP 1 1 FROM dbo.P_SupplierUser WITH(NOLOCK) WHERE (SupplierFxUserId=@fxUserId OR FxUserId=@fxUserId) AND OpenPrePayTime IS NOT NULL";
        //    //var result = DbConnection.QueryFirstOrDefault<bool>(sql, new { fxUserId });
        //    bool result;

        //    if (!IsEnableCache)
        //    {
        //        var sqlParam = new DynamicParameters();
        //        sqlParam.Add("@fxUserId", fxUserId);
        //        if (CustomerConfig.IsDebug)
        //            Log.WriteLine($"[厂家或商家]是否开启过预付模式SupplierOrAgentIsOpenedPrePay: 开始 ", "SupplierOrAgentIsOpenedPrePay.txt");
        //        result = DbConnection.QueryFirstOrDefault<bool>("dbo.[Fx_IsOpenedPrePay]", sqlParam, commandType: CommandType.StoredProcedure);
        //        if (CustomerConfig.IsDebug)
        //            Log.WriteLine($"[厂家或商家]是否开启过预付模式SupplierOrAgentIsOpenedPrePay: {result}", "SupplierOrAgentIsOpenedPrePay.txt"); 
        //        return result;
        //    }
            
        //    var supplierUsers = GetFxUserSuppliersWithCache(fxUserId);
        //    result = supplierUsers.Any(s => s.IsPrePay.HasValue);
        //    return result;
        //}

        /// <summary>
        /// [厂家或商家]是否开启过预付模式
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool SupplierOrAgentIsOpenedPrePay(int fxUserId)
        {
            //var sql = "SELECT TOP 1 1 FROM dbo.P_SupplierUser WITH(NOLOCK) WHERE (SupplierFxUserId=@fxUserId OR FxUserId=@fxUserId) AND OpenPrePayTime IS NOT NULL";
            //var result = DbConnection.QueryFirstOrDefault<bool>(sql, new { fxUserId });
            var sqlParam = new DynamicParameters();
            sqlParam.Add("@fxUserId", fxUserId);
            if (CustomerConfig.IsDebug)
                Log.WriteLine($"[厂家或商家]是否开启过预付模式SupplierOrAgentIsOpenedPrePay: 开始 ", "SupplierOrAgentIsOpenedPrePay.txt");
            var result = DbConnection.QueryFirstOrDefault<bool>("dbo.[Fx_IsOpenedPrePay]", sqlParam, commandType: CommandType.StoredProcedure);
            if (CustomerConfig.IsDebug)
                Log.WriteLine($"[厂家或商家]是否开启过预付模式SupplierOrAgentIsOpenedPrePay: {result}", "SupplierOrAgentIsOpenedPrePay.txt");
            return result;
        }

        ///// <summary>
        ///// [厂家或商家]是否开启过预付模式
        ///// </summary>
        ///// <param name="fxUserId"></param>
        ///// <returns></returns>
        //public List<SupplierUser> GetSupplierWhitelist(int fxUserId)
        //{
        //    var sql = new StringBuilder();
        //    sql.Append($"DECLARE @str VARCHAR(MAX) = 0");
        //    sql.Append($"SELECT @str=[Value] FROM P_CommonSetting WITH(NOLOCK) WHERE [Key]='/ErpWeb/1688Supply/CanApplyPrepay/Supplier/FxUserIds' AND ShopId=0");
        //    sql.Append($"DECLARE @xml XML = '<X>'+REPLACE(@str,',','</X><X>')+'</X>'");
        //    sql.Append($"SELECT SupplierFxUserId,FxUserId FROM P_SupplierUser WITH(NOLOCK) WHERE FxUserId=@fxUserId AND SupplierFxUserId IN (SELECT T.c.value('.','int') AS [Value] FROM @xml.nodes('X') AS T(c))");
        //    var result = DbConnection.Query<SupplierUser>(sql.ToString(), new { fxUserId }).ToList();
        //    return result;
        //}

        /// <summary>
        /// [厂家或商家]是否开启过预付模式
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<int> GetSupplierFxUserId(int fxUserId)
        {
            if (!IsEnabledSupplierUserCache)
            {
                var sql = $"SELECT SupplierFxUserId FROM P_SupplierUser WITH(NOLOCK) WHERE FxUserId = @fxUserId ";
                var result = DbConnection.Query<int>(sql, new { fxUserId }).ToList();
                return result;
            }
            var supplierUsers = GetFxUserSuppliersWithCache(fxUserId);
            return supplierUsers.Select(s => s.SupplierFxUserId).ToList();
        }


        /// <summary>
        /// 是否是1688new
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool Is1688new(int fxUserId)
        {
            var sql = "SELECT TOP 1 1 FROM dbo.P_UserFx WITH(NOLOCK) WHERE Id=@fxUserId AND UserFlag LIKE '%1688new%'";
            var result = DbConnection.QueryFirstOrDefault<bool>(sql, new { fxUserId });
            return result;
        }

        /// <summary>
        /// 获取对指定商家已开启过预付的厂家
        /// </summary>
        /// <param name="agentFxUserId"></param>
        /// <returns></returns>
        public List<int> GetOpenedPrePaySupplierFxUserIds(int agentFxUserId)
        {
            var sql = $@"SELECT SupplierFxUserId FROM dbo.P_SupplierUser WITH(NOLOCK)
WHERE OpenPrePayTime IS NOT NULL AND FxUserId={agentFxUserId} ";
            var list = DbConnection.Query<int>(sql).ToList();
            return list;
        }

        /// <summary>
        /// 是否存在绑定厂商家信息
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool IsAnySupplierUsers(int fxUserId)
        {
            const string sql = @"SELECT TOP 1 1 FROM dbo.P_SupplierUser (NOLOCK) WHERE FxUserId=@fxUserId OR SupplierFxUserId=@fxUserId";
            return DbConnection.QueryFirstOrDefault<int>(sql, new { fxUserId }) == 1;
        }
        /// <summary>
        /// 用户是否绑定指定的商家
        /// </summary>
        /// <returns></returns>
        public bool IsBindSupplierUser(int fxUserId, int supplierFxUserId, IEnumerable<int> statusList = null)
        {
            //商家绑定厂家的状态：1 = 绑定成功，2 = 申请绑定中， 3 = 绑定失败，4 = 已取消， 5 = 解绑处理中，6 = 解绑失败
            if (statusList == null)
            {
                statusList = new List<int>() { 1, 5, 6 };
            }
            const string sql = @"SELECT TOP 1 1 FROM dbo.P_SupplierUser (NOLOCK) WHERE FxUserId=@fxUserId AND SupplierFxUserId=@supplierFxUserId AND Status IN @statusList;";
            return DbConnection.QueryFirstOrDefault<int>(sql, new { fxUserId, supplierFxUserId, statusList }) == 1;
        }
        /// <summary>
        /// 获取用户关联的商家和厂家
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="supplierFxUserId"></param>
        /// <returns></returns>
        public SupplierUser GetByUser(int fxUserId, int supplierFxUserId)
        {
            const string sql = @"SELECT * FROM dbo.P_SupplierUser (NOLOCK) WHERE FxUserId=@fxUserId AND SupplierFxUserId=@supplierFxUserId";
            return DbConnection.QueryFirstOrDefault<SupplierUser>(sql, new { fxUserId, supplierFxUserId });
        }

        public List<int> GetSupplierListId(int fxUserId, string key, AgentBingSupplierStatus status)
        {
            var parameters = new DynamicParameters();
            var sql = $"SELECT t2.Id FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.SupplierFxUserId = t2.Id WHERE t1.FxUserId = {fxUserId}";
            if (string.IsNullOrWhiteSpace(key) == false)
            {
                sql += " AND (t2.NickName LIKE @key OR t2.Mobile LIKE @key)";
                parameters.Add("@key", $"%{key}%");
            }
            sql += " AND t1.Status = @status";
            parameters.Add("@status", (int)status); var multiple = this.DbConnection.QueryMultiple(sql, parameters);
            var supplierFxUserIds = multiple.Read<int>().ToList();
            return supplierFxUserIds;
        }

        public List<int> GetAgentListId(int fxUserId, string key, AgentBingSupplierStatus status)
        {
            var parameters = new DynamicParameters();
            var sql = $"SELECT t2.Id FROM dbo.P_SupplierUser t1 WITH(NOLOCK) INNER JOIN dbo.P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id WHERE t1.SupplierFxUserId = {fxUserId}";
            if (string.IsNullOrWhiteSpace(key) == false)
            {
                sql += " AND (t2.NickName LIKE @key OR t2.Mobile LIKE @key)";
                parameters.Add("@key", $"%{key}%");
            }
            sql += " AND t1.Status = @status";
            sql += " option(loop join, force order)";
            parameters.Add("@status", (int)status);
            var multiple = this.DbConnection.QueryMultiple(sql, parameters);
            var supplierFxUserIds = multiple.Read<int>().ToList();
            return supplierFxUserIds;
        }

        /// <summary>
        /// 更新网商支付开通状态
        /// </summary>
        /// <param name="isOpen"></param>
        /// <param name="supplierFxUserId"></param>
        /// <returns></returns>
        public int UpdateWangShangPay(bool isOpen, List<int> supplierFxUserId)
        {
            var sql = $@"UPDATE P_SupplierUser SET IsOpenWangShangPay=0 WHERE SupplierFxUserId in @supplierFxUserId";
            if (isOpen == true)
                sql = $@"UPDATE P_SupplierUser SET IsOpenWangShangPay=1 WHERE SupplierFxUserId in @supplierFxUserId";

            foreach (var id in supplierFxUserId)
            {
                DeleteCacheBySupplierFxUserId(id);
            }
            return DbConnection.Execute(sql, new { supplierFxUserId });
        }

        /// <summary>
        /// 更新网商支付开通状态
        /// </summary>
        /// <param name="isOpen"></param>
        /// <param name="supplierFxUserId"></param>
        /// <returns></returns>
        public int UpdateWangShangPay(bool isOpen, int supplierFxUserId)
        {
            var sql = $@"UPDATE P_SupplierUser SET IsOpenWangShangPay=0 WHERE SupplierFxUserId = @supplierFxUserId and IsOpenWangShangPay != 0";
            if (isOpen == true)
                sql = $@"UPDATE P_SupplierUser SET IsOpenWangShangPay=1 WHERE SupplierFxUserId = @supplierFxUserId and IsOpenWangShangPay != 1";

            DeleteCacheBySupplierFxUserId(supplierFxUserId);
            return DbConnection.Execute(sql, new { supplierFxUserId });
        }

        /// <summary>
        /// 获取当前用户所有厂家    
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<int> GetSupplierByFxUserId(int fxUserId)
        {
            var sql = "SELECT t1.SupplierFxUserId FROM P_SupplierUser t1 WITH(NOLOCK) INNER JOIN P_UserFx t2 WITH(NOLOCK) ON t1.SupplierFxUserId = t2.Id WHERE t1.FxUserId=@FxUserId";
            sql += " option(loop join, force order)";
            return DbConnection.Query<int>(sql, new { FxUserId = fxUserId }).ToList();
        }

        /// <summary>
        /// 获取当前用户所有商家
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<int> GetAgentByFxUserId(int fxUserId)
        {
            var sql = "SELECT t1.FxUserId FROM P_SupplierUser t1 WITH(NOLOCK) INNER JOIN P_UserFx t2 WITH(NOLOCK) ON t1.FxUserId = t2.Id WHERE t1.SupplierFxUserId=@FxUserId";
            sql += " option(loop join, force order)";
            return DbConnection.Query<int>(sql, new { FxUserId = fxUserId }).ToList();
        }

        /// <summary>
        /// 检查是否有预付关系
        /// </summary>
        /// <param name="currentFxUserId"></param>
        /// <param name="role">供应商：Supplier 分销商：Agent</param>
        /// <returns></returns>
        public int CheckPrePayStatus(int currentFxUserId, string role)
        {
            var sql = "SELECT TOP 1 1 FROM P_SupplierUser WITH(NOLOCK) WHERE IsPrePay=1 AND Status=1 AND OpenPrePayTime IS NOT NULL ";

            switch (role)
            {
                case "Supplier":
                    sql += $"AND SupplierFxUserId=@currentFxUserId";
                    return DbConnection.QueryFirstOrDefault<int>(sql, new { currentFxUserId });
                case "Agent":
                    sql += $"AND FxUserId=@currentFxUserId";
                    return DbConnection.QueryFirstOrDefault<int>(sql, new { currentFxUserId });
                default:
                    return 0;
            }
        }

        #region 预付弹窗数据
        public List<SupplierUser> GetSupplierConfirm(int fxUserId, DateTime? lastTipDateTime, bool isCurrentDay)
        {
            var sql = $@"SELECT
	                        t1.*,
	                        t2.Id,
	                        t2.NickName,
	                        t2.NickName AS UserName,
	                        t2.Mobile 
                        FROM
	                        P_SupplierUser t1 WITH ( NOLOCK )
	                        INNER JOIN P_UserFx t2 WITH ( NOLOCK ) ON t1.SupplierFxUserId = t2.Id  WHERE t1.FxUserId ={fxUserId} 
                        AND t1.IsPrePay = 1 
                        AND (t1.Status = 1 OR t1.Status = 5 OR t1.Status = 6)
                        AND t1.ApplyPrepayStatus = 1";
            if (isCurrentDay)
            {
                sql += $@"AND t1.LastOpenPrepayTime > '{lastTipDateTime}'";
            }
            sql += " option(loop join, force order)";
            var list = DbConnection.Query<SupplierUser>(sql).ToList();
            return list;
        }

        public List<SupplierUser> GetSupplierReject(int fxUserId, DateTime? lastTipDateTime, bool isCurrentDay)
        {
            var sql = $@"SELECT
	                        t1.*,
	                        t2.Id,
	                        t2.NickName,
	                        t2.NickName AS UserName,
	                        t2.Mobile 
                        FROM
	                        P_SupplierUser t1 WITH ( NOLOCK )
	                        INNER JOIN P_UserFx t2 WITH ( NOLOCK ) ON t1.SupplierFxUserId = t2.Id  WHERE t1.FxUserId ={fxUserId} 
                        AND (t1.IsPrePay IS NULL OR t1.IsPrePay=0)
                        AND (t1.Status = 1 OR t1.Status = 5 OR t1.Status = 6)
                        AND t1.ApplyPrepayStatus = 3";
            if (isCurrentDay)
            {
                sql += $@"AND t1.LastOpenPrepayTime > '{lastTipDateTime}'";
            }
            sql += " option(loop join, force order)";
            var list = DbConnection.Query<SupplierUser>(sql).ToList();
            return list;
        }

        public List<SupplierUser> GetSupplierStartB(int fxUserId, DateTime? lastTipDateTime, bool isCurrentDay)
        {
            var sql = $@"SELECT
	                        t1.*,
	                        t2.Id,
	                        t2.NickName,
	                        t2.NickName AS UserName,
	                        t2.Mobile 
                        FROM
	                        P_SupplierUser t1 WITH ( NOLOCK )
	                        INNER JOIN P_UserFx t2 WITH ( NOLOCK ) ON t1.SupplierFxUserId = t2.Id 
                        WHERE
	                        t1.FxUserId ={fxUserId} 
	                        AND t1.IsPrePay = 1 
	                        AND (t1.Status = 1 OR t1.Status = 5 OR t1.Status = 6)";
            var sql2 = $@"SELECT TOP 1 1 FROM dbo.FxAlibabaBuyerShopRelation WITH(NOLOCK) WHERE FxUserId={fxUserId}";
            var isBindBuer = DbConnection.QueryFirstOrDefault<bool>(sql2);
            // var isBindBuer = new FxAlibabaBuyerShopRelationRepository().GetByFxUserIdWithCache(fxUserId).Any();

            if (!isCurrentDay && !isBindBuer)
            {

            }
            else
            {
                sql += $@"AND t1.LastOpenPrepayTime > '{lastTipDateTime}'";
            }
            sql += " option(loop join, force order)";
            var list = DbConnection.Query<SupplierUser>(sql).ToList();
            return list;
        }

        public List<SupplierUser> GetBusineApply(int fxUserId, DateTime? lastTipDateTime, bool isCurrentDay)
        {
            return GetBusineApplyNew(fxUserId, lastTipDateTime, isCurrentDay);
            var sql = $@"SELECT
	                    t1.*,
	                    t2.Id,
	                    t2.NickName,
	                    t2.NickName AS UserName,
	                    t2.Mobile 
                    FROM
	                    P_SupplierUser t1 WITH ( NOLOCK )
	                    INNER JOIN P_UserFx t2 WITH ( NOLOCK ) ON t1.FxUserId = t2.Id 
                    WHERE
	                    t1.SupplierFxUserId ={fxUserId} 
	                    AND t1.ApplyPrepayStatus = 2 ";
            if (isCurrentDay)
            {
                sql += $@"AND ApplyPrepayTime > '{lastTipDateTime}'";
            }
            sql += " option(loop join, force order)";
            var list = DbConnection.Query<SupplierUser>(sql).ToList();
            return list;
        }
        
        /// <summary>
        /// GetBusineApply进行SQL拆分
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="lastTipDateTime"></param>
        /// <param name="isCurrentDay"></param>
        /// <returns></returns>
        public List<SupplierUser> GetBusineApplyNew(int fxUserId, DateTime? lastTipDateTime, bool isCurrentDay)
        {
            // 1. 构建查询条件和参数
            var parameters = new DynamicParameters();
            parameters.Add("@fxUserId", fxUserId);
    
            // 2. 查询符合条件的P_SupplierUser记录
            var supplierSql = @"SELECT * FROM P_SupplierUser WITH(NOLOCK) 
                      WHERE SupplierFxUserId = @fxUserId 
                      AND ApplyPrepayStatus = 2";
    
            if (isCurrentDay && lastTipDateTime.HasValue)
            {
                supplierSql += " AND ApplyPrepayTime > @lastTipDateTime";
                parameters.Add("@lastTipDateTime", lastTipDateTime);
            }
    
            var supplierUsers = DbConnection.Query<SupplierUser>(supplierSql, parameters).ToList();
    
            if (!supplierUsers.Any())
                return new List<SupplierUser>();
    
            // 3. 提取所有关联的用户ID
            var fxUserIds = supplierUsers.Select(s => s.FxUserId).Distinct().ToList();
    
            // 4. 查询相关的用户信息
            var userSql = "SELECT Id, NickName, Mobile FROM P_UserFx WITH(NOLOCK) WHERE Id IN @fxUserIds";
            var userDict = DbConnection.Query<UserFx>(userSql, new { fxUserIds })
                .ToDictionary(u => u.Id);
    
            // 5. 合并数据
            foreach (var supplier in supplierUsers)
            {
                UserFx user;
                if (!userDict.TryGetValue(supplier.FxUserId, out user)) continue;
                
                supplier.NickName = user.NickName;
                supplier.UserName = user.NickName;
                supplier.Mobile = user.Mobile;
            }
    
            return supplierUsers;
        }

        /// <summary>
        /// 获取供应商合作关系信息（简单）
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<SimpleSupplierUser> GetFxUserSuppliersWithCache(int fxUserId)
        {
            const string sql =
                "SELECT SupplierFxUserId, IsPrePay FROM P_SupplierUser WITH(NOLOCK) WHERE FxUserId = @fxUserId";
            var supplierUsers = DbConnection.QueryWithCache<SimpleSupplierUser>(sql, new { fxUserId },
                fxUserId.ToString());
            return supplierUsers ?? new List<SimpleSupplierUser>();
        }

        public void DeleteCacheById(int id)
        {
            var supplierUser = DbConnection.QueryFirst<SupplierUser>("SELECT FxUserId FROM dbo.P_SupplierUser WITH(NOLOCK) WHERE id=@id", new { id });

            if (supplierUser != null)
            {
                DeleteCacheByFxUserId(supplierUser.FxUserId);
            }
        }

        public void DeleteCacheBySupplierFxUserId(int supplierFxUserId)
        {
            var supplierUser = DbConnection.QueryFirst<SupplierUser>("SELECT FxUserId FROM dbo.P_SupplierUser WITH(NOLOCK) WHERE SupplierFxUserId=@supplierFxUserId", new { supplierFxUserId });

            if (supplierUser != null)
            {
                DeleteCacheByFxUserId(supplierUser.FxUserId);
            }
        }


        public void DeleteCacheByFxUserId(int fxUserId)
        {

            DbConnection.DeleteQueryCache<SupplierUser>(fxUserId.ToString());

        }

        /// <summary>
        /// 是否开启合作关系缓存（简单）
        /// </summary>
        public bool IsEnabledSupplierUserCache
        {
            get
            {
                return new CommonSettingRepository().IsEnabledSupplierUserCache;
            }
        }

        public List<SupplierUser> GetSupplierStartS(int fxUserId, DateTime? lastTipDateTime, bool isCurrentDay)
        {
            return GetSupplierStartSNew(fxUserId, lastTipDateTime, isCurrentDay);
            var sql = $@"SELECT
	                        t1.*,
	                        t2.Id,
	                        t2.NickName,
	                        t2.NickName AS UserName,
	                        t2.Mobile 
                        FROM
	                        P_SupplierUser t1 WITH ( NOLOCK )
	                        INNER JOIN P_UserFx t2 WITH ( NOLOCK ) ON t1.FxUserId = t2.Id  WHERE t1.SupplierFxUserId ={fxUserId} 
                        AND t1.IsPrePay = 1 
                        AND (t1.Status = 1 OR t1.Status = 5 OR t1.Status = 6) 
                        AND ( t1.ApplyPrepayStatus = -10 OR t1.ApplyPrepayStatus = -11 )";
            if (isCurrentDay)
            {
                sql += $@"AND t1.LastOpenPrepayTime > '{lastTipDateTime}'";
            }
            sql += " option(loop join, force order)";
            var list = DbConnection.Query<SupplierUser>(sql).ToList();
            return list;
        }
        
        /// <summary>
        /// GetSupplierStartS进行SQL拆分
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="lastTipDateTime"></param>
        /// <param name="isCurrentDay"></param>
        /// <returns></returns>
        public List<SupplierUser> GetSupplierStartSNew(int fxUserId, DateTime? lastTipDateTime, bool isCurrentDay)
        {
            // 1. 构建查询条件和参数
            var parameters = new DynamicParameters();
            parameters.Add("@fxUserId", fxUserId);
    
            // 2. 先查询P_SupplierUser表
            var supplierSql = @"SELECT * FROM P_SupplierUser WITH(NOLOCK) 
                      WHERE SupplierFxUserId = @fxUserId 
                      AND IsPrePay = 1 
                      AND (Status = 1 OR Status = 5 OR Status = 6) 
                      AND (ApplyPrepayStatus = -10 OR ApplyPrepayStatus = -11)";
                      
            if (isCurrentDay && lastTipDateTime.HasValue)
            {
                supplierSql += " AND LastOpenPrepayTime > @lastTipDateTime";
                parameters.Add("@lastTipDateTime", lastTipDateTime);
            }
    
            var supplierUsers = DbConnection.Query<SupplierUser>(supplierSql, parameters).ToList();
    
            if (!supplierUsers.Any())
                return new List<SupplierUser>();
    
            // 3. 获取相关的用户IDs
            var fxUserIds = supplierUsers.Select(s => s.FxUserId).Distinct().ToList();
    
            // 4. 查询P_UserFx
            const string userSql = "SELECT Id, NickName, Mobile FROM P_UserFx WITH(NOLOCK) WHERE Id IN @fxUserIds";
            var userDict = DbConnection.Query<UserFx>(userSql, new { fxUserIds })
                .ToDictionary(u => u.Id);
    
            // 5. 合并数据
            foreach (var supplier in supplierUsers)
            {
                UserFx user;
                if (!userDict.TryGetValue(supplier.FxUserId, out user)) continue;
                
                supplier.NickName = user.NickName;
                supplier.UserName = user.NickName;
                supplier.Mobile = user.Mobile;
            }
    
            return supplierUsers;
        }
        #endregion

        /// <summary>
        /// 获取绑定数据
        /// </summary>
        /// <returns></returns>
        public int GetAllFxUsersCount()
        {
            const string sql = @"SELECT COUNT(1) FROM P_SupplierUser WITH(NOLOCK)";
            return DbConnection.QueryFirstOrDefault<int>(sql);
        }

        /// <summary>
        /// 获取绑定数据
        /// </summary>
        /// <returns></returns>
        public List<SupplierUser> GetAllFxUsers(string sql)
        {
            return DbConnection.Query<SupplierUser>(sql).ToList(); ;
        }

        /// <summary>
        /// 获取绑定数据
        /// </summary>
        /// <returns></returns>
        public List<SupplierUser> GetAllFxUsers(List<int> ids)
        {
            var sql = $@"SELECT
                    Id,
                    RemarkName,
                    SupplierFxUserId,
                    FxUserId,
                    Status,
                    Remark,
                    CreateTime
                    FROM P_SupplierUser WITH(NOLOCK) WHERE Id IN @ids ORDER BY Id ASC";

            return BatchQuery(ids, "ids", sql, null);
            //return DbConnection.Query<SupplierUser>(sql, new { ids }).ToList();
        }

        public List<int> GetFxUserRelationUsers(List<int> fxUserIds)
        {
            var sql = @" SELECT SupplierFxUserId,FxUserId FROM P_SupplierUser WITH(NOLOCK) WHERE FxUserId IN @fxUserIds OR SupplierFxUserId IN@fxUserIds";

            var supplierUsers = BatchQuery(fxUserIds, "fxUserIds", sql, null);
            //var supplierUsers = DbConnection.Query<SupplierUser>(sql, new { fxUserIds }).ToList();
            var allUserIds = supplierUsers
                            .SelectMany(x => new[] { x.SupplierFxUserId, x.FxUserId })
                            .Distinct()
                            .ToList();
            return allUserIds;
        }

        /// <summary>
        /// 获取已合作的商家Id或者厂家Id
        /// </summary>
        /// <param name="type">1获取商家，2获取厂家</param>
        /// <param name="curFxUserId"></param>
        /// <param name="supplierFxUserIds"></param>
        /// <returns></returns>
        public List<int> GetIdsByType(int type, int curFxUserId, List<int> supplierFxUserIds)
        {
            var checkSql = type == 1
                ? "SELECT FxUserId FROM P_SupplierUser WITH(NOLOCK) WHERE SupplierFxUserId=@curFxUserId AND Status IN(1,5,6) AND FxUserId IN @supplierFxUserIds"
                : "SELECT SupplierFxUserId FROM P_SupplierUser WITH(NOLOCK) WHERE FxUserId=@curFxUserId AND Status IN(1,5,6) AND SupplierFxUserId IN @supplierFxUserIds";

            return DbConnection.Query<int>(checkSql, new { curFxUserId, supplierFxUserIds }).Distinct().ToList();
        }

        /// <summary>
        /// 通过用户Id获取商家或厂家的Id
        /// </summary>
        /// <param name="type">1 获取商家 2 获取厂家</param>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<int> GetIdsByCurFxUserIds(int type, List<int> fxUserIds)
        {
            const string getAgentSql =
                "SELECT FxUserId FROM P_SupplierUser WITH(NOLOCK) INNER JOIN FunStringToIntTable(@curFxUserIds, ',') it ON SupplierFxUserId = it.item WHERE Status IN(1,5,6)";
            const string getSupplierSql =
                "SELECT SupplierFxUserId FROM P_SupplierUser WITH(NOLOCK) INNER JOIN FunStringToIntTable(@curFxUserIds, ',') it ON FxUserId = it.item WHERE Status IN(1,5,6)";

            var sql = type == 1 ? getAgentSql : getSupplierSql;
            return DbConnection.Query<int>(sql, new { curFxUserIds = string.Join(",", fxUserIds) }).Distinct().ToList();
        }

        /// <summary>
        /// 更新会员等级
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="memberLevelCode"></param>
        /// <param name="agentIds"></param>
        /// <param name="isCancel"></param>
        public int UpdateMemberLevel(int fxUserId, string memberLevelCode, List<int> agentIds, bool isCancel = false)
        {
            var sql = "UPDATE P_SupplierUser SET MemberLevelCode=@memberLevelCode, UpdateTime=GETDATE() WHERE SupplierFxUserId=@fxUserId AND FxUserId IN @agentIds";
            if (isCancel) sql = "UPDATE P_SupplierUser SET MemberLevelCode=NULL, UpdateTime=GETDATE() WHERE SupplierFxUserId=@fxUserId AND FxUserId IN @agentIds";
            return DbConnection.Execute(sql, new { fxUserId, memberLevelCode, agentIds });
        }

        /// <summary>
        /// 根据会员等级Code获取商家Id
        /// </summary>
        /// <param name="supplierFxUserId"></param>
        /// <param name="memberLevelCodes"></param>
        /// <returns></returns>
        public List<int> GetAgentIdByMemberCodes(int supplierFxUserId, List<string> memberLevelCodes)
        {
            const string sql = "SELECT FxUserId FROM P_SupplierUser WITH(NOLOCK) WHERE SupplierFxUserId=@supplierFxUserId AND MemberLevelCode IN @memberLevelCodes";
            return DbConnection.Query<int>(sql, new { supplierFxUserId, memberLevelCodes }).ToList();
        }

        /// <summary>
        /// 获取商家等级
        /// </summary>
        /// <param name="agentIds"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public List<SupplierUser> GetAgentLevelByIds(List<int> agentIds, int fxUserId)
        {
            const string sql = @"SELECT t1.FxUserId, t2.Level AS MemberLevel
                FROM P_SupplierUser t1 WITH (NOLOCK)
                         LEFT JOIN MemberLevel t2 WITH (NOLOCK) ON t1.MemberLevelCode = t2.MemberLevelCode
                WHERE t1.SupplierFxUserId = @fxUserId
                  AND t1.FxUserId IN @agentIds
                  AND t2.Status = 1";
            return DbConnection.Query<SupplierUser>(sql, new { agentIds, fxUserId }).ToList();
        }

        /// <summary>
        /// 获取当前时间
        /// </summary>
        /// <returns></returns>
        public DateTime GetNowTime()
        {
            var db = DbConnection;
            return db.ExecuteScalar<DateTime>("SELECT GETDATE()");
        }

        public List<SupplierUser> GetSuppliersByFxUserId(int fxUxerId, string selectFields = "f.*", bool isSupplier=true) {
            if (string.IsNullOrWhiteSpace(selectFields))
            {
                selectFields = "f.*";
            }
            var status = 1;
            var sql = isSupplier
                ? $"SELECT {selectFields} FROM P_SupplierUser f WITH(NOLOCK) WHERE f.FxUserId = @fxUxerId and Status=@status"
                : $"SELECT {selectFields} FROM P_SupplierUser f WITH(NOLOCK) WHERE f.SupplierFxUserId = @fxUxerId and Status=@status";
            var list = DbConnection.Query<SupplierUser>(sql, new { fxUxerId, status }).ToList();
            return list;
        }
    }
}
