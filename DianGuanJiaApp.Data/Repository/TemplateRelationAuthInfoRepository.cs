using System.Collections.Generic;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Data.Repository
{

    public partial class TemplateRelationAuthInfoRepository : BaseRepository<TemplateRelationAuthInfo>
    {

        public TemplateRelationAuthInfoRepository() : base(CustomerConfig.ConfigureDbConnectionString)
        {

        }


        /// <summary>
        /// 是否开启历史打印数据数据库配置缓存
        /// </summary>
        private bool IsEnableTemplateRelationAuthInfoCache
        {
            get
            {
                return new CommonSettingRepository().IsEnableTemplateRelationAuthInfoCache();
            }
        }


        public List<PrintTemplateSimpleModel> GetByBranchShareRelationId(int id)
        {
            var sql = $@" 
                 SELECT DISTINCT pt.Id,pt.ShopId,s.PlatformType FROM dbo.P_PrintTemplate pt WITH(NOLOCK) 
				 INNER JOIN dbo.P_TemplateRelationAuthInfo tr WITH(NOLOCK) ON pt.Id=tr.TemplateId
				 LEFT JOIN dbo.P_Shop s WITH(NOLOCK) ON s.Id = pt.ShopId
				 WHERE tr.BranchShareRelationId={id}";
            var models = DbConnection.Query<PrintTemplateSimpleModel>(sql).ToList();
            if (models == null || !models.Any())
            {
                Log.WriteWarning($"BranchShareRelationId为{id}对应的打印模板未找到");
            }
            return models;
        }

        public List<TemplateRelationAuthInfo> GetByShareRelationId(int id)
        {
            var sql = $@" SELECT * FROM dbo.P_TemplateRelationAuthInfo WITH(NOLOCK) 
                        WHERE BranchShareRelationId = {id}";
            var models = DbConnection.Query<TemplateRelationAuthInfo>(sql).ToList();
            if (models == null || !models.Any())
            {
                Log.WriteWarning($"BranchShareRelationId为{id}对应的打印模板未找到");
            }
            return models;
        }


        public List<TemplateRelationAuthInfo> GetByShareRelationIds(List<int> ids)
        {
            if (ids == null || !ids.Any())
                return new List<TemplateRelationAuthInfo>();

            //var idStr = string.Join(",", ids.Distinct());
            //var sql = $@" SELECT * FROM dbo.P_TemplateRelationAuthInfo WITH(NOLOCK) 
            //            WHERE BranchShareRelationId IN ({string.Join(",", ids)})";
            //var models = ExcuteFuncByCloundPlatform<TemplateRelationAuthInfo>(sql)?.ToList();

            var idStr = string.Join(",", ids.Distinct());
            var sqlParameters = new DynamicParameters();
            sqlParameters.Add("@ids", ids.Distinct());
            var sql = $@" SELECT * FROM dbo.P_TemplateRelationAuthInfo WITH(NOLOCK) 
                        WHERE BranchShareRelationId IN @ids";
            var models = ExcuteFuncByCloundPlatform<TemplateRelationAuthInfo>(sql,sqlParameters)?.ToList();

            if (models == null || !models.Any())
            {
                Log.WriteWarning($"BranchShareRelationId为{idStr}对应的打印模板未找到");
            }
            return models;
        }


        public List<dynamic> GetUsedShopAndTemplate(int id)
        {
            var sql = $@"SELECT DISTINCT t2.TemplateId,t3.Id AS ShopId,t3.PlatformType FROM dbo.P_TemplateRelationAuthInfo AS t1 WITH(NOLOCK) 
INNER JOIN dbo.P_ShareWaybillCodeRecord AS t2 WITH(NOLOCK) ON t1.BranchShareRelationId = t2.ShareRelationId
INNER JOIN dbo.P_Shop AS t3 WITH(NOLOCK) ON t2.ShopId = t3.Id
WHERE t1.BranchShareRelationId={id}";
            //var models = DbConnection.Query(sql).ToList();
            var models = ExcuteFuncByCloundPlatform<dynamic>(sql)?.ToList();
            return models;
        }

        /// <summary>
        /// 查询模板对应的分享店铺
        /// </summary>
        /// <param name="templateIds"></param>
        /// <returns></returns>
        public List<TemplateRelationAuthInfo> GetToShopNickNamesByTemplateId(List<int> templateIds)
        {
            var sql = @"SELECT t3.Id, t3.NickName AS BranchName,t2.Remark as BranchAddress,t1.TemplateId FROM dbo.P_TemplateRelationAuthInfo AS t1 WITH(NOLOCK) 
INNER JOIN dbo.P_BranchShareRelation AS t2 WITH(NOLOCK) ON t1.BranchShareRelationId = t2.Id
INNER JOIN dbo.P_Shop AS t3 WITH(NOLOCK) ON t2.ToId = t3.Id WHERE t1.TemplateId IN @tmplateIds ";
            //return DbConnection.Query<TemplateRelationAuthInfo>(sql, new { tmplateIds = templateIds }).ToList();
            return ExcuteFuncByCloundPlatform<TemplateRelationAuthInfo>(sql, new { tmplateIds = templateIds })?.ToList();
        }

        /// <summary>
        /// 查询模板对应的店铺
        /// </summary>
        /// <param name="templateIds"></param>
        /// <returns></returns>
        public List<PrintTemplate> GetShopNickNamesByTemplateId(List<int> templateIds)
        {
            var sql = @"SELECT t1.Id,t2.ShopName as TemplateName  FROM dbo.P_PrintTemplate AS t1 WITH(NOLOCK)
INNER JOIN dbo.P_Shop AS t2 WITH(NOLOCK) ON t1.ShopId = t2.Id
WHERE t1.Id IN @tmplateIds";
            return DbConnection.Query<PrintTemplate>(sql, new { tmplateIds = templateIds }).ToList();
        }

        /// <summary>
        /// 根据模板id加载模板授权账号信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public TemplateRelationAuthInfo GetModelByTemplId(int tid)
        {
            var sql = $@"SELECT * FROM dbo.P_TemplateRelationAuthInfo WITH(NOLOCK) 
                        WHERE TemplateId ={tid}";
            var models = DbConnection.Query<TemplateRelationAuthInfo>(sql).ToList();

            return models?.FirstOrDefault();
        }
        /// <summary>
        /// 查询模板对应的店铺
        /// </summary>
        /// <param name="templateIds"></param>
        /// <returns></returns>
        public List<TemplateRelationAuthInfo> GetByTemplateId(List<int> templateIds)
        {
            var sql = $@"SELECT * FROM dbo.P_TemplateRelationAuthInfo WITH(NOLOCK) WHERE TemplateId IN ({string.Join(",", templateIds)})";
            return DbConnection.Query<TemplateRelationAuthInfo>(sql).ToList();
        }
    }
}
