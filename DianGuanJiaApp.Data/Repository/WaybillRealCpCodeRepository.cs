using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Data.Repository
{
    public class WaybillRealCpCodeRepository : BaseRepository<WaybillRealCpCode>
    {
        public WaybillRealCpCodeRepository() { }
        public WaybillRealCpCodeRepository(string connectionString) : base(connectionString) { }

        /// <summary>
        /// 批量保存数据
        /// </summary>
        /// <param name="list"></param>
        public void AddList(List<WaybillRealCpCode> list)
        {
            if (list == null || list.Any() == false)
                return;
            //BulkWrite(list, "WaybillRealCpCode");

            //1.查询是否存在
            var fromDb = GetWaybillRealCpCode(list, "WaybillCode");
            var dict = fromDb.ToLookup(f => f.WaybillCode).ToDictionary(f => f.Key, f => f.FirstOrDefault());

            //2.找出新增的数据
            var waitInsertData = new List<WaybillRealCpCode>();
            foreach (var item in list)
            {
                if (dict.ContainsKey(item.WaybillCode))
                    continue;
                waitInsertData.Add(item);
            }

            if (waitInsertData.Any())
            {
                //using (var db = DbConnection)
                //{
                //    if (db.State != ConnectionState.Open)
                //        db.Open();
                //    foreach (var item in waitInsertData)
                //    {
                //        db.Insert(item);
                //    }
                //}
                BulkWrite(list, "WaybillRealCpCode");
            }
        }

        /// <summary>
        /// 查询运单号的真实承运信息
        /// </summary>
        /// <param name="queryModels"></param>
        /// <returns></returns>
        public List<WaybillRealCpCode> GetWaybillRealCpCode(List<WaybillRealCpCode> queryModels, string field = "*")
        {
            var rst = new List<WaybillRealCpCode>();
            if (queryModels == null || queryModels.Count <= 0)
                return rst;

            var sql = $"SELECT {field} FROM dbo.WaybillRealCpCode WITH(NOLOCK) WHERE FxUserId = @fxUserId AND ExpressId IN @exIds AND WaybillCode IN @wblCodes";

            var fxUserId = queryModels.Where(q => q.FxUserId.IsNotNullOrEmpty()).FirstOrDefault().FxUserId;
            var pageSize = 1000;
            if (queryModels.Count > pageSize)
            {
                //大于1k需要分页查询
                var pageCount = queryModels.Count / pageSize + 1;
                for (int i = 0; i < pageCount; i++)
                {
                    var list = queryModels.Skip(i * pageSize).Take(pageSize).ToList();
                    if (list.Count <= 0)
                        break;

                    //查询条件整理
                    var exIds = list.Select(f => f.ExpressId).Distinct().ToList();
                    var wblCodes = list.Select(f => f.WaybillCode).Distinct().ToList();
                    var datas = DbConnection.Query<WaybillRealCpCode>(sql, new { fxUserId, exIds, wblCodes });
                    if (!datas.Any())
                        rst.AddRange(datas);
                }
            }
            else
            {
                //查询条件整理
                var exIds = queryModels.Select(f => f.ExpressId).Distinct().ToList();
                var wblCodes = queryModels.Select(f => f.WaybillCode).Distinct().ToList();
                rst = DbConnection.Query<WaybillRealCpCode>(sql, new { fxUserId, exIds, wblCodes }).ToList();
            }

            return rst;
        }
    }
}
