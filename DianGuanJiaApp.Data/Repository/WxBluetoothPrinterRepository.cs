using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using Z.Dapper.Plus;
using DianGuanJiaApp.Utility;
using System.Collections.Concurrent;

namespace DianGuanJiaApp.Data.Repository
{

    public partial class WxBluetoothPrinterRepository : BaseRepository<Entity.WxBluetoothPrinter>
    {
        public WxBluetoothPrinterRepository():base(CustomerConfig.ConfigureDbConnectionString)
        {

        }

        public bool IsExist(int WxUserId, int DeviceType)
        {

            WxBluetoothPrinter bl = base.Get("where WxUserId=@WxUserId AND DeviceType=@DeviceType", new { WxUserId, DeviceType }).FirstOrDefault();

            if (bl != null)
                return true;
            else
                return false;
        }
        public bool WsIsExist(int ShopId)
        {

            WxBluetoothPrinter bl = base.Get("where ShopId=@ShopId", new { ShopId }).FirstOrDefault();

            if (bl != null)
                return true;
            else
                return false;
        }

        public bool WsIsExist(int ShopId, string DeviceId)
        {

            WxBluetoothPrinter bl = base.Get("where ShopId=@ShopId AND DeviceId=@DeviceId", new { ShopId, DeviceId }).FirstOrDefault();

            if (bl != null)
                return true;
            else
                return false;
        }
        public bool IsExist(int WxUserId, string DeviceId,int DeviceType)
        {

            WxBluetoothPrinter bl = base.Get("where WxUserId=@WxUserId AND DeviceId=@DeviceId AND DeviceType=@DeviceType ", new { WxUserId, DeviceId, DeviceType }).FirstOrDefault();

            if (bl != null)
                return true;
            else
                return false;
        }


        public List<WxBluetoothPrinter> GetList(int WxUserId, int DeviceType)
        {
            return base.Get("where WxUserId=@WxUserId AND DeviceType=@DeviceType ", new { WxUserId, DeviceType }).ToList();
        }

        public List<WxBluetoothPrinter> GetListByTemplate(int WxUserId)
        {
            var sql = @"SELECT * FROM P_WxBluetoothPrinter blp WITH(NOLOCK)
                        LEFT JOIN P_PrintTemplate pt WITH(NOLOCK) ON blp.TemplateId=pt.Id
                        LEFT JOIN P_ExpressCompany ec WITH(NOLOCK) ON pt.ExpressCompanyId=ec.Id WHERE blp.DeviceType=1 AND blp.WxUserId=@UserId ";
            var lookUp = new Dictionary<int, WxBluetoothPrinter>();

            var db = this.DbConnection;
            var current = db.Query<WxBluetoothPrinter, PrintTemplate, ExpressCompany, WxBluetoothPrinter>(sql, (blp, pt, ec) =>
            {
                WxBluetoothPrinter bl = null;
                if (!lookUp.TryGetValue(blp.Id, out bl))
                {
                    bl = blp;
                    lookUp.Add(blp.Id, bl);
                }
                
                if (pt != null)
                    bl.PrintTemplate = pt;

                if (ec != null)
                    bl.ExpressCompany = ec;
                
                return bl;
            }, new
            {
                UserId = WxUserId,
            });
            
            List<WxBluetoothPrinter> result = lookUp.Select(k => k.Value).ToList();
            
            return result;
        }


        public List<WxBluetoothPrinter> GetWsList(int ShopId)
        {
            return base.Get("where ShopId=@ShopId ", new { ShopId }).ToList();
        }

        public bool SetDefPrinter(int WxUserId, string DeviceId, int DeviceType)
        {
            WxBluetoothPrinter bl = base.Get("where WxUserId=@WxUserId AND DeviceId=@DeviceId AND DeviceType=@DeviceType", new { WxUserId, DeviceId, DeviceType }).FirstOrDefault();
            bool IsOk = false;

            if (bl != null)
            {
                List<WxBluetoothPrinter> blList = base.Get("where WxUserId=@WxUserId AND DeviceType=@DeviceType ", new { WxUserId, DeviceType }).ToList();
                blList.ForEach(t =>
                {
                    if (t.DeviceId == DeviceId) 
                        t.IsDefault = true;
                    else
                        t.IsDefault = false;

                    base.Update(t);
                });
                IsOk = true;
            }
            
            return IsOk;
        }
        public bool SetWsDefPrinter(int ShopId, string DeviceId)
        {
            WxBluetoothPrinter bl = base.Get("where ShopId=@ShopId AND DeviceId=@DeviceId", new { ShopId, DeviceId }).FirstOrDefault();
            bool IsOk = false;

            if (bl != null)
            {
                List<WxBluetoothPrinter> blList = base.Get("where ShopId=@ShopId ", new { ShopId }).ToList();
                blList.ForEach(t =>
                {
                    if (t.DeviceId == DeviceId)
                        t.IsDefault = true;
                    else
                        t.IsDefault = false;

                    base.Update(t);
                });
                IsOk = true;
            }

            return IsOk;
        }
        public bool SetDefaulOnetPrinter(int WxUserId, int printerId, int DeviceType)
        {
            WxBluetoothPrinter bl = base.Get("where WxUserId=@WxUserId AND Id=@printerId AND DeviceType=@DeviceType", new { WxUserId, printerId, DeviceType }).FirstOrDefault();
            bool IsOk = false;

            if (bl != null)
            {
                List<WxBluetoothPrinter> blList = base.Get("where WxUserId=@WxUserId AND DeviceType=@DeviceType ", new { WxUserId, DeviceType }).ToList();
                blList.ForEach(t =>
                {
                    if (t.Id == printerId)
                        t.IsDefault = true;
                    else
                        t.IsDefault = false;

                    base.Update(t);
                });
                IsOk = true;
            }

            return IsOk;

        }






        //目前默认的也可以删除，删除后如果还有一个并非多个的情况下，就给这一个赋默认值
        public bool DeleteWsPrinter(int ShopId, string DeviceId, int DeviceType)
        {
            WxBluetoothPrinter bl = base.Get("where ShopId=@ShopId AND DeviceId=@DeviceId AND DeviceType=@DeviceType", new { ShopId, DeviceId, DeviceType }).FirstOrDefault();
            bool IsOk = false;
            if (bl != null)
            {
                base.Delete(bl.Id);
                IsOk = true;
                List<WxBluetoothPrinter> blList = base.Get("where ShopId=@ShopId AND DeviceType=@DeviceType ", new { ShopId, DeviceType }).ToList();
                if (blList.Count == 1)
                {
                    WxBluetoothPrinter blOne = blList.FirstOrDefault();
                    if (blOne != null)
                    {
                        blOne.IsDefault = true;
                        base.Update(blOne);
                    }
                }
            }

            return IsOk;
        }



        //目前默认的也可以删除，删除后如果还有一个并非多个的情况下，就给这一个赋默认值
        public bool DeletePrinter(int WxUserId, string DeviceId, int DeviceType) {
            WxBluetoothPrinter bl = base.Get("where WxUserId=@WxUserId AND DeviceId=@DeviceId AND DeviceType=@DeviceType", new { WxUserId, DeviceId, DeviceType }).FirstOrDefault();
            bool IsOk = false;
            if (bl != null) {
                base.Delete(bl.Id);
                IsOk = true;
                List<WxBluetoothPrinter> blList = base.Get("where WxUserId=@WxUserId AND DeviceType=@DeviceType ", new { WxUserId, DeviceType }).ToList();
                if (blList.Count == 1)
                {
                    WxBluetoothPrinter blOne=  blList.FirstOrDefault();
                    if (blOne != null) {
                        blOne.IsDefault = true;
                        base.Update(blOne);
                    }
                }
            }

            return IsOk;
        }

        public WxBluetoothPrinter GetDefaultPrinter(int WxUserId, int DeviceType)
        {
            WxBluetoothPrinter bl = base.Get("where WxUserId=@WxUserId AND DeviceType=@DeviceType AND IsDefault= 1 ", new { WxUserId, DeviceType }).FirstOrDefault();

            return bl;
        }

        public bool UpdateWsBluetoothTemplate(int ShopId, string DeviceId, int TemId, string temName)
        {
            WxBluetoothPrinter bl = base.Get("where ShopId=@ShopId AND DeviceId=@DeviceId", new { ShopId, DeviceId }).FirstOrDefault();
            bool IsOk = false;

            if (bl != null)
            {
                bl.TemplateId = TemId;
                bl.TemplateName = temName;

                base.Update(bl);
                IsOk = true;
            }

            return IsOk;
        }

        public bool UpdateBluetoothTemplate(int UserId, string DeviceId, int TemId, string temName)
        {
            WxBluetoothPrinter bl = base.Get("where WxUserId=@UserId AND DeviceId=@DeviceId AND DeviceType=1 ", new { UserId, DeviceId }).FirstOrDefault();
            bool IsOk = false;

            if (bl != null)
            {
                bl.TemplateId = TemId;
                bl.TemplateName = temName;

                base.Update(bl);
                IsOk = true;
            }

            return IsOk;
        }

        public WxBluetoothPrinter GetDefaultPrinter(int ShopId)
        {
            WxBluetoothPrinter bl = base.Get("where ShopId=@ShopId AND IsDefault= 1 ", new { ShopId }).FirstOrDefault();

            return bl;
        }




    }
}
