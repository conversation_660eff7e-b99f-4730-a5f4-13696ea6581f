using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.OpenTelemetry;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.OpenTelemetry;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.ErpWeb.Api
{
	public class OtlpApiController : ApiBaseController
	{

		[LogForOperatorFilter("查询后端概要数据")]
		public ActionResult GetSummary()
		{
			var requestApiModel = RequestModel.GetParamModel<QueryOtlpRequestForApiModel>();
			string errormsg = "";
			try
			{
				var data = new OtlpClickHouseService().GetBackSummaryExcelData(requestApiModel.Param);
				string result = "";
				if (data != null)
				{
					result = DES.EncryptDES(data.ToJson(), CustomerConfig.LoginCookieEncryptKey);
				}

				return SuccessResult(result);
			}
			catch (Exception e)
			{
				Log.WriteError($"链路日志API查询后端概要数据出现异常,参数：{requestApiModel.ToJson()},ex:{e}");
				errormsg = e.Message;
			}
			return FalidResult($"服务器出现异常，{errormsg}");
		}

		[LogForOperatorFilter("查询后端详细数据")]
		public ActionResult GetDetailList()
		{
			var requestApiModel = RequestModel.GetParamModel<QueryOtlpRequestForApiModel>();
			string errormsg = "";
			try
			{
				var list = new OtlpClickHouseService().GetBackDetailExcelDataList(requestApiModel.Param);
				string result = "";
				if (list != null && list.Count > 0)
				{
					result = DES.EncryptDES(list.ToJson(), CustomerConfig.LoginCookieEncryptKey);
				}
				return SuccessResult(result);
			}
			catch (Exception e)
			{
				Log.WriteError($"链路日志API查询后端详细数据出现异常,参数：{requestApiModel.ToJson()},ex:{e}");
				errormsg = e.Message;
			}
			return FalidResult($"服务器出现异常，{errormsg}");
		}

		[LogForOperatorFilter("查询api基础数据")]
		public ActionResult GetApiBasicDataList()
		{
			var requestApiModel = RequestModel.GetParamModel<QueryOtlpRequestForApiModel>();
			string errormsg = "";
			try
			{
				List<ApiBasicDataResponseModel> list = new List<ApiBasicDataResponseModel>();
				if (CustomerConfig.CloudPlatformType == "Pinduoduo")
				{
					//es时间是国际时区
					requestApiModel.Param.QueryDate = requestApiModel.Param.QueryDate.ToDateTime().Value.ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");
					list = new Services.Services.OpenTelemetry.OtlpEsService().GetApiBasicDataList(requestApiModel.Param);
				}
				else
				{
					var commonSetting = new Services.CommonSettingService().Get($"/ErpWeb/OpenTelemetryConfig/{CustomerConfig.CloudPlatformType}/{CustomerConfig.SystemEnvironmentVersion}", 0);
					if (commonSetting != null && !string.IsNullOrWhiteSpace(commonSetting.Value))
					{
						var openTelemetryConfig = Newtonsoft.Json.JsonConvert.DeserializeObject<OpenTelemetryConfigModel>(commonSetting.Value);
						if (openTelemetryConfig.IsOpenSignoz)
						{
							//Signoz的Timestamp是国际时区
							requestApiModel.Param.QueryDate = requestApiModel.Param.QueryDate.ToDateTime().Value.ToUniversalTime().ToString("yyyy-MM-dd HH:mm:ss");
							list = new OtlpSignozService().GetApiBasicDataList(requestApiModel.Param);
						}
						else
						{
							list = new OtlpClickHouseService().GetApiBasicDataList(requestApiModel.Param);
						}
					}
						
				}

				string result = "";
				if (list != null && list.Count > 0)
				{
					result = DES.EncryptDES(list.ToJson(), CustomerConfig.LoginCookieEncryptKey);
				}
				return SuccessResult(result);
			}
			catch (Exception e)
			{
				Log.WriteError($"链路日志API查询api基础数据出现异常,参数：{requestApiModel.ToJson()},ex:{e}");
				errormsg = e.Message;
			}
			return FalidResult($"服务器出现异常，{errormsg}");
		}
	}
}