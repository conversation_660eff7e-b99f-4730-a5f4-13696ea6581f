using System;
using System.Collections.Generic;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json.Linq;
using System.Linq;
using DianGuanJiaApp.Utility.Web;
using System.Web.Http;
using DianGuanJiaApp.Services.CrossBorder;
using DianGuanJiaApp.Data.Model.CrossBorder;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Enum;
using System.Web.UI.WebControls;
using NPOI.SS.Formula.Functions;
using DianGuanJiaApp.Data.Entity.Collect;
using DianGuanJiaApp.Services.Services.CrossBorder;

namespace DianGuanJiaApp.ErpWeb.ApiControllers.CollectBox
{
    /// <summary>
    /// 采集接口
    /// </summary>
    public class CollectApiController : BaseApiController
    {


        /// <summary>
        /// 商品认领关系服务
        /// </summary>
        private CollectClaimRelationService _relationService = new CollectClaimRelationService();

        /// <summary>
        /// 公共采集箱服务
        /// </summary>
        private CommCollecBoxService _commBoxService = new CommCollecBoxService();



        /// <summary>
        /// 同步东南亚汇率
        /// </summary>
        /// <returns></returns>
        public AjaxResult SyncSoutheastAsiaRate()
        {
            var tuple = new CurrencyConvertRateServer().SyncCurrencyRate();
            return new AjaxResult { Data = null, Success = tuple.Item1, Message = tuple.Item2 };
        }



        /// <summary>
        /// 货币转换汇率
        /// </summary>
        /// <param name="sourceCurrency"></param>
        /// <param name="targetCurrenc"></param>
        /// <returns></returns>
        public AjaxResult GetCurrencyExchangeRates(string sourceCurrency = null, string targetCurrenc = null)
        {
            var currencyConvertRates = new CurrencyConvertRateServer().GetCurrencyConvertExchangeRates(sourceCurrency, targetCurrenc);
            return new AjaxResult { Data = currencyConvertRates, Success = true, Message = "" };
        }

        /// <summary>
        /// 采集商品-采集
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult CollectProduct(CollectInModel model)
        {
            //登录校验 校验Token 由前端重定向
            if (model == null)
                return new AjaxResult { Data = null, Success = false, Message = "请输入内容" };
            var jToken = JToken.Parse(model.Coent);
            var fxUserId = SiteContext.Current.CurrentFxUserId;//登录用户ID
            var mobile = SiteContext.Current.CurrentFxUser.Mobile;
            var tuple = _commBoxService.GenerateCollectBoxProduct(model.Coent, fxUserId);
            return new AjaxResult { Data = mobile, Success = tuple.Item1, Message = tuple.Item2 };
        }



        /// <summary>
        /// 认领平台
        /// </summary>
        /// <param name="inModel"></param>
        /// <returns></returns>
        public AjaxResult ClaimPlatform(CollectClaimInModel inModel)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //1.获取用户是否跨境店主账号 不存在 返回
            FxUserShopQueryModel _reqModel = new FxUserShopQueryModel()
            {
                FxUserId = fxUserId,
                QueryPageType = 2,
                IsShowAccount = true,
            };
            var tuple = new FxUserForeignShopService().GetList(_reqModel);
            //过滤本土店
            var crossBorderAccountShops = tuple.Item2.Where(f => f.SellerType != TikTokShopType.LOCAL.ToString()).ToList();
            if (!crossBorderAccountShops.Any())
                return new AjaxResult { Success = false, Message = "请先授权相关跨境店铺", Data = null };
            //2.通过 uids 获取 采集数据
            var boxProductSearchRes = _commBoxService.GetBoxProductByUids(inModel.productUids, fxUserId);
            if (!boxProductSearchRes.Any())
                return new AjaxResult { Success = false, Message = "未找到相关数据", Data = null };
            //3.认领商品
            var tupleCliam = _commBoxService.ClaimPlatform(crossBorderAccountShops, boxProductSearchRes, fxUserId, inModel.claimPlatfrom);
            return new AjaxResult { Success = true, Message = "", Data = null };
        }


        /// <summary>
        /// 采集商品-删除
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult DeleteCollectProduct(List<long> productUids)
        {
            if (productUids == null || !productUids.Any())
                return new AjaxResult { Success = false, Message = "参数不可为空，请重新选择" };

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _commBoxService.DeleteCommCollectBoxProduct(productUids, fxUserId);
            return new AjaxResult { Success = result, Message = "" };
        }


        /// <summary>
        /// 是否存在授权店铺
        /// </summary>
        /// <returns></returns>
        public AjaxResult IsExistAuthShop()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //1.获取用户是否跨境店主账号 不存在 返回
            FxUserShopQueryModel _reqModel = new FxUserShopQueryModel()
            {
                FxUserId = fxUserId,
                QueryPageType = 2,
                IsShowAccount = true,
            };
            var tuple = new FxUserForeignShopService().GetList(_reqModel);
            //过滤本土店
            var list = tuple.Item2.Where(f => f.SellerType != TikTokShopType.LOCAL.ToString()).ToList();
            if (!tuple.Item2.Any())
                return new AjaxResult { Success = false, Message = "请先授权相关跨境店铺", Data = null };
            return new AjaxResult { Success = true, Message = "" };
        }


        /// <summary>
        ///  采集商品-列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("采集列表")]
        [HttpPost]
        public AjaxResult<PageResultApiModel<CollectBoxProductSearchRes>> GetComBoxProducts(CollectProductSearchModel model)
        {

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            model.FxUserId = fxUserId;
            var result = _commBoxService.GetPageResult(model);
            return new AjaxResult<PageResultApiModel<CollectBoxProductSearchRes>>()
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 同步TK全球类目
        /// </summary>
        /// <param name="ShopId">全球店主账号ID</param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult SyncTikTokGlobalCategories(int ShopId)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            new PlatformCategoryCrossBorderService().SyncTikTokFnDdCateGory(ShopId, fxUserId);
            return new AjaxResult { Data = null, Success = true, Message = "" };
        }

        /// <summary>
        /// 同步类目仅同步类目
        /// </summary>
        /// <param name="ShopId"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult ManualRsyncCategoryList(int ShopId)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var tuple = new PlatformCategoryCrossBorderService().ManualRsyncCategoryList(ShopId, fxUserId);
            return new AjaxResult { Success = true, };
        }

        /// <summary>
        /// 1688图片搜搜同款
        /// 注：目前此接口不需要用户授权 指定已授权店铺供用户使用
        /// </summary>
        /// <param name="inModel"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult FindPictureID(CollectInModel inModel)
        {
            if (inModel == null)
                return new AjaxResult { Success = false, Message = "参数为空，请刷新重试" };
            if (inModel.Type == 1 && string.IsNullOrEmpty(inModel.Coent))
                return new AjaxResult { Success = false, Message = "请上传图片" };
            //1.查询配置 P_CommonSetting返回配置 店铺信息 ShopExtension.AppKey == CustomerConfig.AlibabaCrossAppKey
            var key = BusinessSettingKeys.SupplyBy1688.CrossBroder1688SearchImageSetting;
            var shopId = new CommonSettingService().Get(key, 0)?.Value ?? "";
            if (string.IsNullOrEmpty(shopId))
                return new AjaxResult { Success = false, Message = "功能暂不可用,请联系平台" };
            //2.根据类型是下载还是直接上传
            var tuple = _commBoxService.Find1688PictureID(inModel, shopId.ToInt());
            return new AjaxResult { Success = tuple.Item1, Data = tuple.Item2 };
        }

    }
}