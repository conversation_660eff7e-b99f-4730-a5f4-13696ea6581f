using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model.FreightTemplate;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services.FreightTemplate;
using System.Collections.Generic;
using System.Web.Http;

namespace DianGuanJiaApp.ErpWeb.ApiControllers
{
    public class FreightTemplateController : BaseApiController
    {
        private readonly FreightTemplateService _service;

        /// <summary>
        /// 运费模板
        /// </summary>
        public FreightTemplateController()
        {
            _service = new FreightTemplateService();
        }

        /// <summary>
        /// 获取运费模板列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogForOperatorFilter("获取运费模板列表")]
        public AjaxResult<List<ExpShippingFeeTemplate>> GetList()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var rows = _service.GetList(fxUserId);
            return new AjaxResult<List<ExpShippingFeeTemplate>>()
            {
                Success = true,
                Data = rows,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 获取快递公司列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [LogForOperatorFilter("获取快递公司列表")]
        public AjaxResult<List<ExpressCompany>> GetExpressCompanyList()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var rows = _service.GetExpressCompanyList(fxUserId);
            return new AjaxResult<List<ExpressCompany>>()
            {
                Success = true,
                Data = rows,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 获取运费模板变更记录(该接口暂未使用)
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        [HttpGet]
        [LogForOperatorFilter("获取运费模板变更记录")]
        public AjaxResult<PagingResultModel<ExpShippingFeeTemplateChangeRecord>> GetChangeRecordList(long templateId, int pageIndex, int pageSize)
        {
            //var page = _service.GetChangeRecordList(templateId, pageIndex, pageSize);
            return new AjaxResult<PagingResultModel<ExpShippingFeeTemplateChangeRecord>>
            {
                Success = true,
                Data = null,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 批量编辑运费模板
        /// </summary>
        /// <param name="templates"></param>
        /// <returns></returns>
        [HttpPost]
        [LogForOperatorFilter("编辑运费模板")]
        public AjaxResult<AddOrEditResponse> BatchEdit(List<ExpShippingFeeTemplate> templates)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _service.BatchEdit(templates, fxUserId);
            return new AjaxResult<AddOrEditResponse>()
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 批量新增运费模板
        /// </summary>
        /// <param name="templates"></param>
        /// <returns></returns>
        [HttpPost]
        [LogForOperatorFilter("新增运费模板")]
        public AjaxResult<AddOrEditResponse> BatchAdd(List<ExpShippingFeeTemplate> templates)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _service.BatchAdd(templates, fxUserId);
            return new AjaxResult<AddOrEditResponse>()
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 批量删除运费模板
        /// </summary>
        /// <param name="IdAndCodeKv"></param>
        /// <returns></returns>
        [HttpPost]
        [LogForOperatorFilter("删除运费模板")]
        public AjaxResult<string> BatchDelete(Dictionary<long, string> IdAndCodeKv)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _service.BatchDelete(IdAndCodeKv, fxUserId);
            return new AjaxResult<string>()
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }
    }
}