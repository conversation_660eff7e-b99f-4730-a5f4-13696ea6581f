using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.ProductSkuHistory;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace DianGuanJiaApp.ErpWeb.ApiControllers
{
    /// <summary>
    /// 商品规格变更历史
    /// </summary>
    public class ProductSkuHistoryController : BaseApiController
    {
        private readonly ProductSkuHistoryService _service;

        /// <summary>
        /// 商品规格变更历史
        /// </summary>
        public ProductSkuHistoryController()
        {
            _service = new ProductSkuHistoryService();
        }

        /// <summary>
        /// 商品规格变更历史列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [LogForOperatorFilter("商品规格变更历史列表")]
        [HttpPost]
        public AjaxResult<PageResultApiModel<ProductSkuHistory>> GetList(ProductSkuHistoryQuery model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _service.GetList(model, fxUserId);
            return new AjaxResult<PageResultApiModel<ProductSkuHistory>>()
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 批量设置商品简称或结算价
        /// </summary>
        /// <param name="models">批量设置商品简称或结算价</param>
        /// <param name="isConfirmed">是否确认变更记录(忽略提醒)</param>
        /// <returns></returns>
        [LogForOperatorFilter("批量设置商品简称或结算价")]
        [HttpPost]
        public AjaxResult SetShortTitleOrPrice(List<SetShortTitleOrPriceModel> models, bool isConfirmed = false)
        {
            var result = new AjaxResult();
            try
            {
                result.Success = true;
                result.Data = true;
                result.Message = "成功！";
                if (models.IsNullOrEmptyList())
                {
                    return result;
                }
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                _service.SetShortTitleOrPrice(models, fxUserId, isConfirmed);
            }
            catch (Exception ex)
            {
                Log.WriteError($"批量设置商品简称或结算价失败！失败原因：{ex.ToJson()}");
                result.Message = $"批量设置商品简称或结算价失败！失败原因：{ex.ToJson()}";
                result.Success = false;
                result.Data = false;
            }
            return result;
        }

        /// <summary>
        /// 确认变更记录(忽略提醒)
        /// </summary>
        /// <param name="kv">id - MessagesCode</param>
        /// <returns></returns>
        [LogForOperatorFilter("确认变更记录")]
        [HttpPost]
        public AjaxResult Confirmed(Dictionary<int, string> kv)
        {
            var result = new AjaxResult();
            try
            {
                result.Success = true;
                result.Data = true;
                result.Message = "成功！";
                if (kv.IsNullOrEmptyList())
                {
                    return result;
                }
                //内部方法已经对越权进行了判断
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                _service.Confirmed(kv, fxUserId);
            }
            catch (Exception ex)
            {
                Log.WriteError($"确认变更记录失败！失败原因：{ex.ToJson()}");
                result.Message = $"确认变更记录失败！失败原因：{ex.ToJson()}";
                result.Success = false;
                result.Data = false;
            }
            return result;
        }

        /// <summary>
        /// 获取
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public AjaxResult<ProductSkuHistory> GetById(int id)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _service.GetById(id, fxUserId);
            return new AjaxResult<ProductSkuHistory>()
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }

        /// <summary>
        /// 获取商品变更消息未读数量
        /// </summary>
        /// <returns></returns>
        public AjaxResult<ProductSkuHistoryMessageModel> GetUnreadCount()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _service.GetUnreadCount(fxUserId);
            return new AjaxResult<ProductSkuHistoryMessageModel>()
            {
                Success = true,
                Data = result,
                Message = "成功！"
            };
        }
    }
}