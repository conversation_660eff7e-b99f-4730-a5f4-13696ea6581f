using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.FxModel.SupplierProduct;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.SupplierProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.ErpWeb.ApiControllers
{
    /// <summary>
    /// 货盘商品控制器
    /// </summary>
    [FxWhiteUserFilterApi]
    public class SupplierProductController : BaseApiController
    {
        private readonly SupplierProductService _supplierProductService = new SupplierProductService();
        private readonly BusinessCardService _businessCardService = new BusinessCardService();
        private readonly UserSupplierStatusService _userSupplierStatusService = new UserSupplierStatusService();
        private readonly WareHouseService _wareHouseService = new WareHouseService();

        /// <summary>
        /// 货盘商品模块 - 创建货盘商品 - 手工创建货盘商品
        /// [肖茂翔]
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<string> Add(SupplierProductAddModel model)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            if (model.Subject.IsNullOrEmpty()) return FailedResult("商品标题不能为空，请输入商品标题!");
            if (model.ProductSkus == null || model.ProductSkus.Count == 0) return FailedResult("请添加商品SKU");
            if (model.ProductSkus.Any()) model.ProductSkus.ForEach(a =>
            {
                if (a.SkuCode.IsNullOrEmpty()) throw new LogicException("SkuCode不能为空，请输入SkuCode");
            });
            try
            {
                //_wareHouseService.GenerateDefaultStore();后置处理
                var result = _supplierProductService.AddSupplierProduct(model, curFxUserId);
                return result > 0 ? SuccessResult("保存成功") : FailedResult("保存失败");
            }
            catch (LogicException lo)
            {
                return FailedResult(lo.Message);
            }
            catch (Exception e)
            {
                Log.WriteError($"手工创建货盘商品失败: {e.Message}");
                return FailedResult("服务器繁忙，请稍会儿再试");
            }
        }
        
        /// <summary>
        /// 货盘商品模块 - 编辑 - 编辑更新货盘商品
        /// [肖茂翔]
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<string> Update(SupplierProductUpdateModel model)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            if (model.Subject.IsNullOrEmpty()) return FailedResult("商品标题不能为空，请输入商品标题!");
            if (model.ProductSkus == null || model.ProductSkus.Count == 0) return FailedResult("请添加商品SKU");
            if (model.ProductSkus.Any()) model.ProductSkus.ForEach(a =>
            {
                if (a.SkuCode.IsNullOrEmpty()) throw new LogicException("SkuCode不能为空，请输入SkuCode");
            });

            try
            {
                var result = _supplierProductService.UpdateSupplierProduct(model, curFxUserId);
                return result ? SuccessResult("保存成功") : FailedResult("保存失败");
            }
            catch (LogicException lo)
            {
                return FailedResult(lo.Message);
            }
            catch (Exception e)
            {
                Log.WriteError($"更新货盘商品失败: {e.Message}");
                return FailedResult("服务器繁忙，请稍会儿再试");
            }
        }

        /// <summary>
        /// 货盘商品模块 - 创建货盘商品 - 基础商品上架货盘商品
        /// [肖茂翔]
        /// </summary>
        /// <param name="model">基础商品UidList</param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<string> PutOn(PutOnProductModel model)
        {
            if (model?.BaseProductUidList == null) return FailedResult("无效的输入");
            var baseProductUidList = model.BaseProductUidList;
            if (baseProductUidList == null || baseProductUidList.Count == 0) return FailedResult("请选择要上架的商品");
            if (baseProductUidList.Count > 500) return FailedResult("数量不能超过500条，请重新选择！");
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            try
            {
                var result = _supplierProductService.GenerateSupplierProduct(baseProductUidList, curFxUserId);
                return result > 0 ? SuccessResult($"成功{result}条，失败{baseProductUidList.Count - result}条") : FailedResult("发布失败");
            }
            catch (LogicException lo)
            {
                return FailedResult(lo.Message);
            }
            catch (Exception e)
            {
                Log.WriteError($"基础商品上架货盘商品失败: {e.Message}");
                return FailedResult("服务器繁忙，请稍会儿再试");
            }
        }
        
        /// <summary>
        /// 货盘商品模块 - 创建基础商品 -复制货盘商品到商品库
        /// [肖茂翔]
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<string> Copy(List<CopySupplierProductModel> models)
        {
            if (models == null || models.Any() == false) return FailedResult("请选择要复制的商品");
            if (models.Count > 500) return FailedResult("数量不能超过500条，请重新选择！");

            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            try
            {
                var result = _supplierProductService.CopySupplierProduct(models, curFxUserId);
                var model = new
                {
                    SuccessCount = result,
                    FailedCount = models.Count - result
                };
                return result > 0 ? SuccessResult(model.ToJson()) : FailedResult(model.ToJson(), "复制失败");
            }
            catch (LogicException lo)
            {
                return FailedResult(lo.Message);
            }
            catch (Exception e)
            {
                Log.WriteError($"复制货盘商品到商品库失败: {e.Message}, {e.StackTrace}");
                return FailedResult("服务器繁忙，请稍会儿再试");
            }
        }
        
        /// <summary>
        /// 货盘商品模块 - 编辑货盘商品 - 获取我的小站的发货信息
        /// [肖茂翔]
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<ShipmentsInfo> GetShipmentsInfo()
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _businessCardService.GetShipmentsInfo(curFxUserId);

            return SuccessResult(result);
        }
        
        /// <summary>
        /// 货盘商品模块 - 编辑货盘商品 - 获取我的小站的售后信息
        /// [肖茂翔]
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<AfterSalesInfo> GetAfterSalesInfo()
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _businessCardService.GetAfterSalesInfo(curFxUserId);

            return SuccessResult(result);
        }

        /// <summary>
        /// 货盘商品模块 - 获取详情 - 获取货盘商品详情
        /// [肖茂翔]
        /// </summary>
        /// <param name="uid">SupplierProduct.Uid</param>
        /// <param name="isBaseProUid">是否基础商品的Uid</param>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<SupplierProductDetailModel> GetByUid(long uid, bool isBaseProUid = false)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            try
            {
                var result = _supplierProductService.GetByUid(uid, curFxUserId, isBaseProUid);

                return SuccessResult(result);
            }
            catch (LogicException lo)
            {
                return FailedResult(new SupplierProductDetailModel(),lo.Message);
            }
            catch (Exception e)
            {
                Log.WriteError($"获取货盘商品详情失败: {e.Message}");
                return FailedResult(new SupplierProductDetailModel(),"服务器繁忙，请稍会儿再试");            
            }
        }
        
        /// <summary>
        /// 货盘商品模块 - 获取列表 - 获取货盘商品列表
        /// [肖茂翔]
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<SupplierProductListModel> GetList(SupplierProductQueryModel model)
        {
            if (!(model.Tag == 1 || model.Tag == 2 || model.Tag == 3))
                return FailedResult(new SupplierProductListModel(), "请指定页面类型！");

            if (model.Tag == 2 && model.SupplierFxUserId == null)
                return FailedResult(new SupplierProductListModel(), "厂家小站，需要指定厂家信息！");

            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            try
            {
                var result = _supplierProductService.GetSupplierProductPage(model, curFxUserId);

                return SuccessResult(result);
            }
            catch (LogicException lo)
            {
                return FailedResult(new SupplierProductListModel(), lo.Message);
            }
            catch (Exception e)
            {
                Log.WriteError($"获取货盘商品列表失败: {e.Message}");
                return FailedResult(new SupplierProductListModel(), "服务器繁忙，请稍会儿再试");
            }
        }
        
        /// <summary>
        /// 货盘商品模块 - 公开私密 - 上架或下架货盘商品
        /// [肖茂翔]
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<string> PublicOrPrivate(PublicOrPrivateModel model)
        {
            if (model.UidList.Count > 500) return FailedResult("数量不能超过500条，请重新选择！");
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            try
            {
                var result = _supplierProductService.PublicOrPrivate(model.UidList, model.IsPublic, curFxUserId);
                return result > 0 ? SuccessResult($"成功{result}条，失败{model.UidList.Count - result}条") : FailedResult("复制失败");
            }
            catch (LogicException lo)
            {
                return FailedResult(lo.Message);
            }
            catch (Exception e)
            {
                Log.WriteError($"上架或下架货盘商品失败: {e.Message}");
                return FailedResult("服务器繁忙，请稍会儿再试");
            }
        }

        /// <summary>
        /// 货盘商品模块 - 获取列表 - 获取合作厂家列表
        /// [肖茂翔]
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<List<SuppliersModel>> GetSuppliers()
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            try
            {
                var result = _userSupplierStatusService.GetSuppliers(curFxUserId);
                var exist = _supplierProductService.ExistProductByFxUserId(result.Select(x => x.SupplierUserId));
                // 过滤不存在的厂家
                result = result.Where(x => exist.Contains(x.SupplierUserId)).ToList();

                return SuccessResult(result);
            }
            catch (LogicException lo)
            {
                return FailedResult(new List<SuppliersModel>(), lo.Message);
            }
            catch (Exception e)
            {
                Log.WriteError($"获取合作厂家列表失败: {e.Message}");
                return FailedResult(new List<SuppliersModel>(), "服务器繁忙，请稍会儿再试");
            }
        }

        /// <summary>
        /// 货盘商品模块 - 获取品牌列表
        /// [张立聪]
        /// <param name="brandname">品牌名称（选填）</param>
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public AjaxResult<List<BrandModel>> GetBrand(string brandname)
        {
            List<BrandModel> pairs = _supplierProductService.GetBrand(SiteContext.Current.CurrentFxUserId, brandname);
            return new AjaxResult<List<BrandModel>>
            {
                Success = true,
                Data = pairs,
                Message = "操作成功"
            };
        }

        /// <summary>
        /// 货盘商品模块 - 保存品牌
        /// [张立聪]
        /// </summary>
        /// <param name="req">入参</param>
        /// <returns></returns>
        [HttpPost]
        public AjaxResult<string> SaveBrand(SaveBrandModel req )
        {
            if (string.IsNullOrWhiteSpace(req.BrandName))
                return FailedResult("品牌名称不能为空");

            var okmsg = _supplierProductService.SaveBrand(SiteContext.Current.CurrentFxUserId, req.BrandName);
            if (okmsg == null)
                return SuccessResult("添加成功");

            return FailedResult("添加失败");
        }

        /// <summary>
        /// TODO 仅供测试使用，逻辑删除，上线要去掉，不做越权校验
        /// </summary>
        /// <param name="uids"></param>
        /// <returns></returns>
        public AjaxResult<string> Delete(List<long> uids)
        {
            if (CustomerConfig.IsDebug)
            {
                if (uids == null || uids.Count == 0) return FailedResult("请选择要删除的商品");
                if (uids.Count > 500) return FailedResult("数量不能超过500条，请重新选择！");
                try
                {
                    var result = _supplierProductService.DeleteSupplierProduct(uids);
                    return result > 0 ? SuccessResult($"成功{result}条，失败{uids.Count - result}条") : FailedResult("删除失败");
                }
                catch (LogicException lo)
                {
                    return FailedResult(lo.Message);
                }
                catch (Exception e)
                {
                    Log.WriteError($"删除货盘商品失败: {e.Message}");
                    return FailedResult("服务器繁忙，请稍会儿再试");
                }
  
            }

            return FailedResult(string.Empty);
        }
    }
}