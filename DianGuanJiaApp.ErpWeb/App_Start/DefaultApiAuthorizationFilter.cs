using System;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Services;
using System.Web.Http.Filters;
using System.Threading.Tasks;
using System.Threading;
using System.Web.Http.Controllers;
using System.Web;
using System.Net.Http;
using System.Net;
using DianGuanJiaApp.Utility.Extension;
using ErpWebBaseController = DianGuanJiaApp.Controllers.BaseController;
namespace DianGuanJiaApp.ErpWeb
{
    /// <summary>
    ///默认接口权限验证
    /// </summary>
    public class DefaultApiAuthorizationFilterAttribute : AuthorizationFilterAttribute
    {
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public DefaultApiAuthorizationFilterAttribute()
        {
        }

        /// <summary>
        /// 鉴权逻辑
        /// </summary>
        /// <param name="actionContext"></param>
        public override void OnAuthorization(HttpActionContext actionContext)
        {
            var curRequest = HttpContext.Current.Request;
            var xRequestedWith = HttpContext.Current.Request.Headers["X-Requested-With"];
            //维护期间不允许操作
            var tuple = ErpWebBaseController.IsMaintenanceTime(xRequestedWith);
            if(tuple.Item1)
            {
                if (ErpWebBaseController.IsAjaxRequest(xRequestedWith))
                    actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.Unauthorized, new AjaxResultModel { Message = tuple.Item2, Success = false });
                else
                    actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.Unauthorized, tuple.Item2);
                return;
            }
            //测试环境仅指定白名单可以使用
            if (!ErpWebBaseController.CheckWhiteIP(
                curRequest?.Url.Host,
                curRequest?.UserHostAddress,
                curRequest?.ServerVariables["REMOTE_ADDR"],
                curRequest?.Headers["X-Forwarded-For"]))
            {
                actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.Unauthorized, new AjaxResultModel { Message = "IP白名单限制", Success = false });
                return;
            }
            //优先获取Headers中的token，获取不到再获取链接上的
            var token = curRequest.Headers["token"];
            var userAgent = curRequest.Headers["User-Agent"];
            if (string.IsNullOrEmpty(token))
                token = curRequest["token"];
            if (string.IsNullOrEmpty(token))
            {
                actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.Unauthorized, new AjaxResultModel { Message = "token不能为空", Success = false });
                return;
            }
            //验证token的合法性
            var authToken = GetLoginToken(curRequest, token, userAgent);
            if(authToken == null)
                actionContext.Response = actionContext.Request.CreateResponse(HttpStatusCode.Unauthorized, new AjaxResultModel { Message = "token is invalid", Success = false });
            else
            {
                var dbname = ErpWebBaseController.GetCurrentDBName(curRequest["dbname"]);
                new SiteContext(authToken,dbname, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
            }
        }

        /// <summary>
        /// 异步鉴权逻辑
        /// </summary>
        /// <param name="actionContext"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public override Task OnAuthorizationAsync(HttpActionContext actionContext, CancellationToken cancellationToken)
        {
            try
            {
                OnAuthorization(actionContext);
            }
            catch (Exception exception)
            {
                return Task.FromException(exception);
            }
            return Task.CompletedTask ;
        }

        private static readonly CommonSettingService _commonSettingService = new CommonSettingService();
        private static readonly ShopService _shopService = new ShopService();
        /// <summary>
        /// 获取登录token
        /// </summary>
        /// <returns></returns>
        private LoginAuthToken GetLoginToken(HttpRequest request,string token,string userAgent)
        {
            var json = DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey);
            if (string.IsNullOrEmpty(json))
                throw new LogicException("登录信息不合法，请重新登录", "401");

            var tokenID = json.ToInt();
            var model = new LoginAuthCookieModel { Id = tokenID };
            var isWechatBrowser = userAgent.Contains("MicroMessenger");
            var isFromWeixin = ErpWebBaseController.IsFromWeixin(token,userAgent, request.Headers["Fx-WxToken"], request.Headers["Fx-WxOpenId"]);
            if (ErpWebBaseController.IsFromQingWk(token, request.Headers["X-UserId"]))
                isFromWeixin = true;//阿里轻应用，和微信一样，都是前后端分离，api的请求方式
            var hashCode = userAgent.GetHashCode();
            var sign = hashCode.ToString();
            var md5sign = userAgent.ToShortMd5();
            var md5signForWechat = md5sign;
            if (isWechatBrowser)
            {
                //微信浏览器，首次进入是UserAgent后缀包含了：" Flue"，后面iframe中没有这个
                var extUserAgent = " Flue";
                if (userAgent.EndsWith(extUserAgent) == false)
                {
                    md5signForWechat = (userAgent + extUserAgent).ToShortMd5();
                }
                else
                {
                    md5signForWechat = (userAgent.TrimEnd(extUserAgent)).ToShortMd5();
                }
            }
            //双重验证
            var key = CustomerConfig.FxSystemIsOpenDoubleAuthKey;
            var isOpenDoubleAuth = 0;
            var _isIgnoreDoubleAuth = false;
            if (_isIgnoreDoubleAuth == false)
                isOpenDoubleAuth = _commonSettingService.Get(key, 0)?.Value.ToInt() ?? 0;

            //签名必须一致，且数据库中这个token存在、未过期
            var currentIP = ErpWebBaseController.GetCurrentIP(request?.Headers["X-Forwarded-For"], request?.UserHostAddress); 
            var authToken = _shopService.IsTokenValid(model, sign, md5sign, isFromWeixin, currentIP, md5signForWechat, isOpenDoubleAuth: isOpenDoubleAuth, _isIgnoreDoubleAuth);
            if (authToken == null)
            {
                Log.WriteWarning($"登录信息无法识别，token验证失效，token:{token} userAgent:{userAgent} ");
                return null;
            }

            //不存在分销店铺ID，加载UserID
            if (authToken.FxUserId <= 0)
            {
                authToken.FxUserId = _shopService.GetFxUserIdByShopId(authToken.ShopId);
            }
            if (authToken.FxUserId <= 0)
            {
                Log.WriteWarning($"登录信息无法识别，通过ShopId获取FxUserId依然无数据。请求tokenId： {model.Id}，LoginAuthToken.ShopId：{authToken.ShopId} ");
                return null;
            }

            //本地连接&微信不校验cookie
            var isLocalhost = string.Equals(request.Url.Host, "localhost");
            if (!CustomerConfig.IsLocalDbDebug && !isFromWeixin && !CustomerConfig.IsDebug && !isLocalhost)
            {
                //校验Cookie
                var cookieKey = CustomerConfig.FxSystemLoginCookieKey.Replace("$id", authToken.Id.ToString());
                var loginCookie = request.Cookies[cookieKey];
                var checkResult = ErpWebBaseController.CheckLoginCookie(_commonSettingService, loginCookie?.Value, isOpenDoubleAuth, authToken, key);
                if (checkResult == false)
                    return null;
            }
            return authToken;
        }

    }
}