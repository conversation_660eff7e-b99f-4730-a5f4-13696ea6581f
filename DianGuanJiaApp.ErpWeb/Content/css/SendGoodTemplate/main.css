
.gray
{
	color: #999;
	font-size: 12px;
	font-weight: normal;
}
.more
{
	float: right;
	font-size: 12px;
	font-weight: 100;
	margin-right: 10px;
}
.hide
{
	display: none;
}
.timer
{
	float: right;
	color: #999;
	font-size: 12px;
}
.red
{
	color: #f00;
}
.overline
{
	background: #ebf8ff;
}
UL.normal LI
{
	border-bottom: #cfcfcf 1px dotted;
	padding-bottom: 0px;
	padding-left: 15px;
	padding-right: 0px;
	background: url(../images/bg_li_normal.gif) no-repeat left center;
	height: 20px;
	padding-top: 5px;
}
UL.black LI
{
	border-bottom: #cfcfcf 1px solid;
	padding-bottom: 0px;
	padding-left: 15px;
	padding-right: 0px;
	background: url(../images/bg_li_black.gif) no-repeat left center;
	height: 20px;
	padding-top: 5px;
}
SPAN.sp
{
	margin: 0px 10px;
}
SPAN.spright
{
	margin-right: 10px;
}
SPAN.spleft
{
	margin-left: 10px;
}
INPUT.btn_intd
{
	cursor: pointer;
	margin-right: 10px;
}
INPUT.btn_scpanel
{
	cursor: pointer;
}
INPUT.ni
{
	border-bottom: #93b2c2 1px solid;
	border-left: #93b2c2 1px solid;
	background: #f3fbff;
	border-top: #93b2c2 1px solid;
	border-right: #93b2c2 1px solid;
}
INPUT.txt
{
	border-bottom: #d5d5d5 1px solid;
	border-left: #d5d5d5 1px solid;
	height: 16px;
	border-top: #d5d5d5 1px solid;
	border-right: #d5d5d5 1px solid;
}
.viewer_cont
{
	padding-bottom: 10px;
	padding-left: 10px;
	padding-right: 10px;
	padding-top: 10px;
}
INPUT.btn_1
{
	border-bottom: #c6c6c6 1px solid;
	border-left: #c6c6c6 1px solid;
	padding-bottom: 0px;
	padding-left: 10px;
	padding-right: 10px;
	background: #ddd;
	height: 24px;
	color: #333;
	font-size: 12px;
	border-top: #c6c6c6 1px solid;
	cursor: pointer;
	border-right: #c6c6c6 1px solid;
	padding-top: 0px;
}
INPUT.btn_2
{
	border-bottom: #c6c6c6 1px solid;
	border-left: #c6c6c6 1px solid;
	padding-bottom: 0px;
	padding-left: 10px;
	padding-right: 10px;
	background: #b9e3f9;
	height: 24px;
	color: #004884;
	font-size: 12px;
	border-top: #c6c6c6 1px solid;
	cursor: pointer;
	border-right: #c6c6c6 1px solid;
	padding-top: 0px;
}
INPUT.btn_3
{
	border-bottom: #c6c6c6 1px solid;
	border-left: #c6c6c6 1px solid;
	padding-bottom: 0px;
	padding-left: 10px;
	padding-right: 10px;
	background: #f7c667;
	height: 24px;
	color: #514600;
	font-size: 12px;
	border-top: #c6c6c6 1px solid;
	cursor: pointer;
	border-right: #c6c6c6 1px solid;
	padding-top: 0px;
}
A.link_bar
{
	border-bottom: #ffb243 1px solid;
	border-left: #ffe4be 1px solid;
	padding-bottom: 2px;
	padding-left: 8px;
	padding-right: 8px;
	background: #fffaf1;
	border-top: #ffe4be 1px solid;
	margin-right: 8px;
	border-right: #ffb243 1px solid;
	padding-top: 2px;
}
A.btn_img
{
	margin-right: 10px;
}
A:link
{
	color: #0071a5;
	text-decoration: none;
}
A:visited
{
	color: #0071a5;
	text-decoration: none;
}
A:active
{
	color: #0071a5;
	text-decoration: none;
}
A.red:link
{
	color: #f00;
	text-decoration: none;
}
A.red:visited
{
	color: #f00;
	text-decoration: none;
}
A.red:hover
{
	color: #0071ff;
	text-decoration: underline;
}
A.red:active
{
	color: #f00;
	text-decoration: none;
}
A.white:link
{
	color: #dff5ff;
	text-decoration: none;
}
A.white:visited
{
	color: #dff5ff;
	text-decoration: none;
}
A.white:hover
{
	color: #fd0;
	text-decoration: underline;
}
A.white:active
{
	color: #dff5ff;
	text-decoration: none;
}
A.org:link
{
	color: #f60;
	text-decoration: none;
}
A.org:visited
{
	color: #f60;
	text-decoration: none;
}
A.org:hover
{
	color: #f90;
}
A.org:active
{
	color: #f60;
	text-decoration: none;
}
.container_index
{
	margin: 0px auto;
	width: 990px;
}
.block_normal
{
	margin-top: 20px;
	width: 100%;
	float: left;
}
.block_normal H2
{
	border-bottom: #d8d8d8 1px solid;
	text-indent: 1em;
	width: 100%;
	font-family: Microsoft Yahei;
	background: url(../images/bg_h2.gif) repeat-x;
	float: left;
	height: 24px;
	color: #333;
	font-size: 15px;
	font-weight: normal;
	padding-top: 8px;
}
.smsset
{
	border-bottom: #d8d8d8 1px solid;
	border-left: #d8d8d8 1px solid;
	width: 254px;
	float: left;
	height: 183px;
	border-top: #d8d8d8 1px solid;
	margin-right: 10px;
	border-right: #d8d8d8 1px solid;
}
.smsset UL
{
	padding-bottom: 0px;
	padding-left: 25px;
	width: 204px;
	padding-right: 25px;
	float: left;
	padding-top: 15px;
}
.smsset UL LI
{
	width: 100%;
	float: left;
	padding-top: 7px;
}
.smsset UL LI IMG
{
	margin-top: -3px;
	float: right;
}
.syc
{
	border-bottom: #d8d8d8 1px solid;
	border-left: #d8d8d8 1px solid;
	width: 462px;
	float: left;
	height: 183px;
	border-top: #d8d8d8 1px solid;
	margin-right: 10px;
	border-right: #d8d8d8 1px solid;
}
.syc UL.loading
{
	text-align: center;
	padding-bottom: 10px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	font-family: Microsoft Yahei;
	float: left;
	color: #999;
	font-size: 24px;
	padding-top: 10px;
}
.syc UL.loading IMG
{
	margin-right: 20px;
}
.syc UL.autosys
{
	padding-bottom: 8px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	float: left;
	padding-top: 8px;
}
.syc UL.autosys DL.pending
{
	width: 200px;
	padding-right: 20px;
	float: left;
	color: #333;
	font-size: 14px;
}
.syc UL.autosys DL.pending DD
{
	padding-bottom: 0px;
	text-indent: 4em;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	float: left;
	height: 16px;
	padding-top: 5px;
}
.syc UL.autosys DL.pending DD A
{
	font-weight: bold;
}
.syc UL.autosys .donow
{
	width: 200px;
	float: left;
	padding-top: 20px;
}
.syc .sysad
{
	margin-top: 10px;
	text-indent: 2em;
	width: 100%;
	float: left;
	border-top: #d8d8d8 1px dashed;
	padding-top: 15px;
}
.syc .sysad IMG
{
	margin: -5px 20px 0px 0px;
	float: right;
}
.syc1
{
	border-bottom: #d8d8d8 0px solid;
	border-left: #d8d8d8 0px solid;
	width: 462px;
	float: left;
	height: 110px;
	border-top: #d8d8d8 0px solid;
	margin-right: 10px;
	border-right: #d8d8d8 0px solid;
}
.syc1 UL.loading
{
	text-align: center;
	padding-bottom: 10px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	font-family: Microsoft Yahei;
	float: left;
	color: #999;
	font-size: 24px;
	padding-top: 10px;
}
.syc1 UL.loading IMG
{
	margin-right: 20px;
}
.syc1 UL.autosys
{
	padding-bottom: 8px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	float: left;
	padding-top: 8px;
}
.syc1 UL.autosys DL.pending
{
	width: 200px;
	padding-right: 20px;
	float: left;
	color: #333;
	font-size: 14px;
}
.syc1 UL.autosys DL.pending DD
{
	padding-bottom: 0px;
	text-indent: 4em;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	float: left;
	height: 16px;
	padding-top: 5px;
}
.syc1 UL.autosys DL.pending DD A
{
	font-weight: bold;
}
.syc1 UL.autosys .donow
{
	width: 200px;
	float: left;
	padding-top: 20px;
}
.syc1 .sysad
{
	margin-top: 10px;
	text-indent: 2em;
	width: 100%;
	float: left;
	border-top: #d8d8d8 1px dashed;
	padding-top: 15px;
}
.syc1 .sysad IMG
{
	margin: -5px 20px 0px 0px;
	float: right;
}
.templateset
{
	border-bottom: #d8d8d8 1px solid;
	border-left: #d8d8d8 1px solid;
	width: 245px;
	float: left;
	height: 183px;
	border-top: #d8d8d8 1px solid;
	border-right: #d8d8d8 1px solid;
}
.templateset UL
{
	margin-top: 15px;
	width: 100%;
	float: left;
}
.templateset UL LI
{
	text-align: center;
	padding-bottom: 7px;
	line-height: 2;
	width: 100%;
	float: left;
}
.deliveryname
{
	border-bottom: #d2a725 1px solid;
	border-left: #d2a725 1px solid;
	padding-bottom: 4px;
	padding-left: 20px;
	padding-right: 20px;
	font-family: Microsoft Yahei;
	background: url(../images/bg_deliveryname.gif) repeat-x;
	color: #333;
	font-size: 12px;
	border-top: #d2a725 1px solid;
	border-right: #d2a725 1px solid;
	padding-top: 4px;
}
INPUT.save
{
	border-bottom-style: none;
	border-right-style: none;
	margin-top: -2px;
	width: 40px;
	border-top-style: none;
	background: #f0831e;
	height: 17px;
	color: #fff;
	font-size: 10px;
	border-left-style: none;
	cursor: pointer;
}
.sblock
{
	border-bottom: #d8d8d8 1px solid;
	border-left: #d8d8d8 1px solid;
	width: 492px;
	float: left;
	height: 215px;
	overflow: hidden;
	border-top: #d8d8d8 1px;
	border-right: #d8d8d8 1px solid;
}
.bleft
{
	border-right-style: none;
}
.sblock H2
{
	text-indent: 3em;
	width: 100%;
	font-family: Microsoft Yahei;
	background: url(../images/bg_h2_sblock.gif) repeat-x;
	float: left;
	height: 28px;
	color: #333;
	font-size: 15px;
	font-weight: normal;
	padding-top: 10px;
}
.operation
{
	text-indent: 1em;
	margin: 0px 15px 0px 0px;
	width: 53px;
	font-family: Tahoma,Verdana;
	background: url(../images/btn_operation.png) no-repeat;
	float: right;
	height: 17px;
	font-size: 12px;
	padding-top: 3px;
}
UL.list_news
{
	padding-bottom: 10px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	float: left;
	padding-top: 20px;
}
UL.list_news LI
{
	padding-left: 15px;
	width: 450px;
	background: url(../images/bg_li_star.gif) no-repeat left center;
	float: left;
	height: 20px;
	margin-left: 15px;
	padding-top: 5px;
}
UL.list_news LI SPAN.timer_news
{
	padding-right: 20px;
	float: right;
	color: #999;
}
UL.list_s1
{
	padding-bottom: 10px;
	padding-left: 30px;
	width: 400px;
	padding-right: 0px;
	float: left;
	padding-top: 20px;
}
UL.list_s1 LI
{
	width: 100%;
	float: left;
	height: 17px;
	padding-top: 5px;
}
UL.list_s1 LI SPAN.title_gray
{
	text-align: left;
	width: 100px;
	display: inline-block;
}
.footer
{
	margin-top: 20px;
	width: 100%;
	background: url(../images/bg_footer.gif) no-repeat center bottom;
	float: left;
	height: 167px;
}
#wrap
{
	position: relative;
	padding-bottom: 7px;
	color: #717171;
}
.box
{
	margin-top: 10px;
	width: 100%;
	float: left;
}
.root
{
	border-bottom: #e4e4e4 1px solid;
	line-height: 1;
	text-indent: 2em;
	width: 1100px;
	background: url(../images/bg_root.gif) repeat-x;
	float: left;
	height: 27px;
	padding-top: 7px;
}
.root .roottitle
{
	margin-top: 5px;
	float: left;
}
* + HTML .root
{
	position: relative;
	width: 99%;
	float: none;
}
.root .manualsyc
{
	margin: -2px 10px 0px 0px;
	float: right;
}
UL.labelbox
{
	border-bottom: #ccc 1px solid;
	border-left: #ccc 1px solid;
	margin-top: 10px;
	width: 100%;
	background: #efefef;
	float: left;
	border-top: #ccc 1px solid;
	border-right: #ccc 1px solid;
}
* + HTML .labelbox
{
	position: relative;
	width: 99%;
	float: none;
}
UL.labelbox LI
{
	float: left;
	margin-left: 5px;
}
UL.labelbox LI A
{
	padding-bottom: 7px;
	padding-left: 20px;
	padding-right: 20px;
	display: block;
	float: left;
	color: #f90;
	padding-top: 7px;
}
UL.labelbox LI A:hover
{
	color: #555;
}
UL.labelbox LI.current A:link
{
	color: #555;
	font-weight: bold;
}
UL.labelbox LI.current A:visited
{
	color: #555;
	font-weight: bold;
}
UL.labelbox LI.current A:active
{
	color: #555;
	font-weight: bold;
}
.searchbox
{
	border-bottom: #e4e4e4 1px solid;
	text-align: center;
	padding-bottom: 10px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	float: left;
	padding-top: 10px;
}
* + HTML .searchbox
{
	position: relative;
	width: 99%;
	float: none;
}
TABLE.search
{
	text-align: left;
	width: 100%;
	border-collapse: collapse;
}
TABLE.search TD
{
	height: 25px;
}
TABLE.search LABEL
{
	width: 60px;
	display: inline-block;
	color: #333;
}
INPUT.btn_getorder
{
	border-bottom-style: none;
	border-right-style: none;
	width: 86px;
	font-family: Microsoft Yahei;
	border-top-style: none;
	background: url(../images/bg_btn_getorder.gif) no-repeat;
	height: 38px;
	font-size: 14px;
	border-left-style: none;
	cursor: pointer;
}
.count_tabs
{
	margin-top: 10px;
	width: 100%;
	background: url(../images/bg_sprite_count_tab.gif) repeat-x 0px -108px;
	float: left;
	height: 27px;
}
* + HTML .count_tabs
{
	position: relative;
	width: 99%;
	float: none;
}
.count_tabs LI
{
	text-align: center;
	line-height: 27px;
	background: url(../images/bg_sprite_count_tab.gif) no-repeat 0px 0px;
	float: left;
	height: 27px;
	margin-left: 20px;
	cursor: pointer;
}
.count_tabs LI A
{
	text-align: center;
	padding-bottom: 5px;
	padding-left: 19px;
	padding-right: 19px;
	display: block;
	background: url(../images/bg_sprite_count_tab.gif) no-repeat right -23px;
	height: 22px;
	padding-top: 0px;
}
.count_tabs LI A SPAN
{
	display: inline-block;
}
.count_tabs LI A
{
	color: #333;
}
.count_tabs LI A:hover
{
	color: #333;
}
.count_tabs LI A:visited
{
	color: #333;
}
.count_tabs LI.current
{
	margin-top: -2px;
	background: url(../images/bg_sprite_count_tab.gif) no-repeat 0px -51px;
}
.count_tabs LI.current A
{
	background: url(../images/bg_sprite_count_tab.gif) no-repeat right -78px;
	color: #f60;
	font-weight: bold;
}
.operation_buttons
{
	border-bottom: #e4e4e4 1px solid;
	text-align: center;
	width: 100%;
	background: url(../images/bg_operation_buttons.gif) repeat-x center 50%;
	float: left;
	height: 44px;
	padding-top: 20px;
}
.operation_buttons SPAN
{
	display: inline;
}
.operation_buttons INPUT
{
	margin-right: 20px;
}
* + HTML .operation_buttons
{
	position: relative;
	width: 99%;
	float: none;
}
* + HTML .operation_buttons INPUT
{
	padding-left: 0px;
	padding-right: 0px;
	display: inline;
	margin-right: 10px;
}
TABLE.datalist
{
	border-bottom: #cdcdcd 1px solid;
	border-left: #cdcdcd 1px solid;
	margin-top: 10px;
	width: 100%;
	border-collapse: collapse;
	border-top: #cdcdcd 1px solid;
	border-right: #cdcdcd 1px solid;
}
TABLE.datalist TD
{
	border-bottom: #cdcdcd 1px solid;
	border-left: #cdcdcd 0px solid;
	padding-bottom: 5px;
	line-height: 1.5em;
	padding-left: 5px;
	padding-right: 5px;
	font-size: 12px;
	border-top: #cdcdcd 0px solid;
	border-right: #cdcdcd 1px solid;
	padding-top: 5px;
}
TABLE.datalist TH
{
	border-bottom: #cdcdcd 1px solid;
	border-left: #cdcdcd 0px solid;
	padding-bottom: 5px;
	line-height: 1.5em;
	padding-left: 5px;
	padding-right: 5px;
	font-size: 12px;
	border-top: #cdcdcd 0px solid;
	border-right: #cdcdcd 1px solid;
	padding-top: 5px;
}
TABLE.datalist TR TD
{
	color: #333;
}
TABLE.datalist TH
{
	text-align: center;
	background: #fce9c8;
	color: #333;
	font-weight: normal;
}
TABLE.datalist CAPTION
{
	text-align: left;
	line-height: 3em;
}
TABLE.datalist .colorline TD
{
	background: #e0f2fc;
}
TABLE.datalist TD.expand
{
	background: #f2f2f2;
}
.subdata
{
	margin: 30px auto;
	width: 80%;
	color: #717171;
}
TABLE.child
{
	border-bottom: #cdcdcd 1px solid;
	border-left: #cdcdcd 1px solid;
	margin-top: 10px;
	width: 100%;
	border-collapse: collapse;
	margin-bottom: 15px;
	background: #fff;
	border-top: #cdcdcd 1px solid;
	border-right: #cdcdcd 1px solid;
}
TABLE.child THEAD TR TD
{
	background: #e0f2fc;
}
TABLE.child TBODY TR TD
{
	border-bottom: #cdcdcd 1px solid;
}
TABLE.child TR TD
{
	color: #717171;
}
.tfoot_total
{
	padding-right: 5px;
	float: right;
}
.tfoot_beizhu
{
	padding-left: 5px;
	float: left;
}
TABLE.sublist
{
	border-right-width: 0px;
	width: 100%;
	border-collapse: collapse;
	border-top-width: 0px;
	border-bottom-width: 0px;
	border-left-width: 0px;
}
TABLE.sublist TR TD
{
	border-bottom: #e4e4e4 0px dotted;
	border-right-style: none;
}
.impnotice
{
	padding-bottom: 15px;
	text-indent: 2em;
	margin: 10px auto;
	padding-left: 15px;
	width: 92%;
	padding-right: 15px;
	font-family: Microsoft Yahei;
	background: #ffc257;
	height: 25px;
	color: #fff;
	font-size: 16px;
	padding-top: 15px;
}
.noticeintro
{
	border-bottom: #e4e4e4 1px dotted;
	padding-bottom: 10px;
	line-height: 2;
	text-indent: 4em;
	margin: 0px auto;
	width: 100%;
}
.pager
{
	line-height: 18px;
	float: right;
	margin-right: 30px;
}
.pager SPAN
{
	padding-bottom: 0px;
	padding-left: 5px;
	padding-right: 5px;
	cursor: pointer;
	padding-top: 0px;
}
.line
{
	border-left: #e9f1f8 1px solid;
	border-right-width: 0px;
	width: 0px;
	float: left;
	border-top-width: 0px;
	border-bottom-width: 0px;
	height: 24px;
}
.nowN
{
	border-bottom: #d6e6f1 1px solid;
	filter: progid:dximagetransform.microsoft.gradient(gradienttype=0, startcolorstr=#ffffff, endcolorstr=#D6E6F1);
	border-left: #d6e6f1 0px solid;
	padding-bottom: 1px;
	background-color: #d6e6f1;
	padding-left: 6px;
	padding-right: 6px;
	float: left;
	height: 18px;
	color: #313131;
	font-size: 12px;
	border-top: #d6e6f1 1px solid;
	border-right: #d6e6f1 1px solid;
	padding-top: 3px;
}
.offN
{
	padding-bottom: 1px;
	background-color: #fff;
	padding-left: 6px;
	padding-right: 6px;
	float: left;
	height: 18px;
	color: #313131;
	font-size: 12px;
	padding-top: 3px;
}
.offN A:visited
{
	display: block;
	color: #313131;
	text-decoration: none;
	padding-top: 2px;
}
.offN A:link
{
	display: block;
	color: #313131;
	text-decoration: none;
	padding-top: 2px;
}
.offN A:active
{
	color: #005590;
	text-decoration: underline;
}
.offN A:hover
{
	color: #005590;
	text-decoration: underline;
}
.tabLine
{
	width: 100%;
}
.tabLine TD
{
	border-bottom: #d6e6f1 1px solid;
	padding-bottom: 2px;
	line-height: 2.5em;
	padding-left: 2px;
	padding-right: 2px;
	padding-top: 2px;
}
.tabLine CAPTION
{
	text-align: left;
	line-height: 3em;
	background: #f6f9fc;
}
.configbox
{
	border-bottom: #e5ddd0 1px solid;
	border-left: #e5ddd0 1px solid;
	border-top: #e5ddd0 1px solid;
	border-right: #e5ddd0 1px solid;
	line-height: 2.5;
	margin-top: 10px;
	width: 1084px;
	background: #fdeac9;
	float: left;
}
.setbox
{
	position: relative;
	margin-top: 10px;
	width: 1185px;
	float: left;
	border-top: #e4e4e4 1px solid;
	padding-top: 10px;
}
.setbox_main
{
	border-bottom: #e4e4e4 1px solid;
	position: relative;
	border-left: #e4e4e4 1px solid;
	padding-bottom: 5px;
	padding-left: 5px;
	width: 880px;
	padding-right: 5px;
	float: left;
	border-top: #e4e4e4 1px solid;
	border-right: #e4e4e4 1px solid;
	padding-top: 5px;
}
.deliverypaperset
{
	padding-bottom: 7px;
	text-indent: 1em;
	padding-left: 0px;
	width: 100%;
	height: 45px;
	padding-right: 0px;
	background: #e0f2fc;
	float: left;
	padding-top: 7px;
}
.printbox
{
	position: relative;
	margin-top: 5px;
	width: 827px;
	float: left;
	height: auto;
}
.sidecheck
{
	float: left;
}
#pb
{
	color: #333;
}
.divset
{
	border-bottom: #ff8c3f 1px solid;
	position: absolute;
	border-left: #ff8c3f 1px solid;
	background: #fff;
	border-top: #ff8c3f 1px solid;
	border-right: #ff8c3f 1px solid;
}
.title
{
	z-index: 2;
	position: absolute;
	line-height: 1.6em;
	padding-left: 5px;
	width: 95%;
	height: 95%;
	color: #333;
	top: 0px;
	cursor: move;
	left: 0px;
}
.resize
{
	z-index: 5;
	position: absolute;
	width: 7px;
	bottom: 1px;
	background: url(../images/btn_resize.gif) no-repeat;
	height: 7px;
	cursor: nw-resize;
	right: 1px;
}
.printbox A.btn_close
{
	z-index: 4;
	position: absolute;
	width: 10px;
	display: block;
	background: url(../images/btn_close_s.gif) no-repeat;
	height: 10px;
	top: 1px;
	right: 1px;
}
.searchTab
{
	float: left;
}
H2.deliverset_h2
{
	text-align: center;
	padding-bottom: 5px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	background: #fff;
	float: left;
	font-size: 14pt;
	padding-top: 5px;
}
H3.deliverset_h3
{
	text-align: center;
	padding-bottom: 0px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	background: #fff;
	float: left;
	font-size: 14px;
	padding-top: 5px;
}
.deliverset
{
	position: relative;
	margin-top: 5px;
	width: 827px;
	float: left;
	height: auto;
	color: #333;
}
.deliverset UL.d3
{
	width: 100%;
	background: #fff;
	float: left;
}
.deliverset UL.d3 LI
{
	padding-bottom: 10px;
	padding-left: 0px;
	width: 33%;
	padding-right: 0px;
	float: left;
	padding-top: 0px;
}
.deliverset UL.d1
{
	width: 100%;
	background: #fff;
	float: left;
}
.deliverset UL.d1 LI
{
	padding-bottom: 10px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	float: left;
	padding-top: 0px;
}
.deliverset TABLE.child
{
	margin-top: 10px;
	float: left;
}
.floattpset
{
	z-index: 100;
	border-bottom: #ff9900 1px solid;
	position: absolute;
	filter: alpha(opacity=90);
	border-left: #ff9900 1px solid;
	background: #fff;
	color: #333;
	border-top: #ff9900 1px solid;
	border-right: #ff9900 1px solid;
	opacity: 0.9;
	-moz-opacity: 0.9;
}
.pbset
{
	padding-bottom: 10px;
	line-height: 3;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	color: #333;
	padding-top: 10px;
}
.pbset_left
{
	padding-left: 1%;
	width: 100%;
	float: left;
}
.pbset_left LI
{
	padding-bottom: 5px;
	padding-left: 10px;
	padding-right: 0px;
	padding-top: 0px;
}
.pbset_left LI INPUT
{
	margin-right: 10px;
}
.pbset_right
{
	padding-left: 1%;
	width: 44%;
	float: left;
}
.pbset_right H3
{
	width: 80px;
	float: left;
	height: 100%;
	font-size: 12px;
}
.pbset_right .setcontent
{
	width: 250px;
	float: left;
	height: 100%;
}
.sidecheck
{
	border-bottom: #e4e4e4 1px solid;
	position: relative;
	border-left: #e4e4e4 1px solid;
	padding-bottom: 30px;
	padding-left: 5px;
	width: 180px;
	padding-right: 5px;
	float: left;
	margin-left: 10px;
	border-top: #e4e4e4 1px solid;
	border-right: #e4e4e4 1px solid;
	padding-top: 5px;
}
.sidecheck H2
{
	padding-bottom: 9px;
	text-indent: 2em;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	font-family: Microsoft Yahei;
	margin-bottom: 5px;
	background: #e0f2fc;
	float: left;
	font-size: 13px;
	cursor: pointer;
	padding-top: 9px;
}
.sidecheck H2 IMG
{
	margin: -4px 20px 0px 0px;
	float: right;
}
.sidecheck UL
{
	width: 180px;
	float: left;
}
.sidecheck UL LI
{
	padding-bottom: 3px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	float: left;
	padding-top: 3px;
}
.tempsave
{
	text-align: center;
	padding-bottom: 0px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	float: left;
	padding-top: 20px;
}
.tempsave INPUT
{
	border-bottom-style: none;
	border-right-style: none;
	width: 87px;
	border-top-style: none;
	background: url(../print_files/button.gif) no-repeat;
	height: 28px;
	color: #333;
	border-left-style: none;
}
TABLE.ntable
{
	border-bottom-style: none;
	border-right-style: none;
	margin-top: 5px;
	width: 100%;
	border-top-style: none;
	color: #000;
	border-left-style: none;
}
TABLE.sborder
{
	border-bottom: #e4e4e4 1px solid;
	border-left: #e4e4e4 1px solid;
	margin-top: 5px;
	width: 100%;
	border-collapse: collapse;
	background: #fff;
	color: #000;
	border-top: #e4e4e4 1px solid;
	border-right: #e4e4e4 1px solid;
}
TABLE.sborder THEAD TR TH
{
	border-bottom: #e4e4e4 1px solid;
	text-align: center;
	padding-bottom: 5px;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 5px;
}
TABLE.sborder THEAD TR TD
{
	border-bottom: #e4e4e4 1px solid;
	text-align: center;
	padding-bottom: 5px;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 5px;
}
TABLE.sborder TBODY TR TH
{
	border-bottom: #e4e4e4 1px solid;
	padding-bottom: 5px;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 5px;
}
TABLE.sborder TBODY TR TD
{
	border-bottom: #e4e4e4 1px solid;
	padding-bottom: 5px;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 5px;
}
TABLE.sborder TFOOT TR TD
{
	padding-bottom: 5px;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 5px;
}
TABLE.dborder
{
	border-bottom: #000 1px dotted;
	border-left: #000 1px dotted;
	margin-top: 5px;
	width: 100%;
	border-collapse: collapse;
	background: #fff;
	color: #000;
	border-top: #000 1px dotted;
	border-right: #000 1px dotted;
}
TABLE.dborder THEAD TR TH
{
	border-bottom: #000 1px dotted;
	text-align: center;
	padding-bottom: 5px;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 5px;
}
TABLE.dborder THEAD TR TD
{
	border-bottom: #000 1px dotted;
	text-align: center;
	padding-bottom: 5px;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 5px;
}
TABLE.dborder TBODY TR TH
{
	border-bottom: #000 1px dotted;
	padding-bottom: 5px;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 5px;
}
TABLE.dborder TBODY TR TD
{
	border-bottom: #000 1px dotted;
	padding-bottom: 5px;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 5px;
}
TABLE.dborder TFOOT TR TD
{
	padding-bottom: 5px;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 5px;
}
TABLE.noborder
{
	border-bottom-style: none;
	border-right-style: none;
	margin-top: 5px;
	width: 100%;
	border-collapse: collapse;
	border-top-style: none;
	background: #fff;
	color: #000;
	border-left-style: none;
}
TABLE.noborder THEAD TR TH
{
	border-bottom-style: none;
	text-align: center;
	padding-bottom: 5px;
	border-right-style: none;
	padding-left: 0px;
	padding-right: 0px;
	border-top-style: none;
	border-left-style: none;
	padding-top: 5px;
}
TABLE.noborder THEAD TR TD
{
	border-bottom-style: none;
	text-align: center;
	padding-bottom: 5px;
	border-right-style: none;
	padding-left: 0px;
	padding-right: 0px;
	border-top-style: none;
	border-left-style: none;
	padding-top: 5px;
}
TABLE.noborder TBODY TR TH
{
	border-bottom-style: none;
	padding-bottom: 5px;
	border-right-style: none;
	padding-left: 0px;
	padding-right: 0px;
	border-top-style: none;
	border-left-style: none;
	padding-top: 5px;
}
TABLE.noborder TBODY TR TD
{
	border-bottom-style: none;
	padding-bottom: 5px;
	border-right-style: none;
	padding-left: 0px;
	padding-right: 0px;
	border-top-style: none;
	border-left-style: none;
	padding-top: 5px;
}
TABLE.noborder TFOOT TR TD
{
	padding-bottom: 5px;
	padding-left: 0px;
	padding-right: 0px;
	padding-top: 5px;
}
#main
{
	overflow-x: hidden;
	width: auto;
	padding-right: 10px;
	float: left;
	height: 100%;
	overflow: auto;
	padding-top: 0px;
}
UNKNOWN
{
	padding-bottom: 0px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	padding-top: 0px;
}
HTML > BODY #main
{
	padding-bottom: 0px;
	padding-left: 0px;
	width: 100%;
	padding-right: 0px;
	padding-top: 0px;
}
#notice
{
	padding-bottom: 10px;
	padding-left: 10px;
	padding-right: 10px;
	float: left;
	padding-top: 10px;
}
#tabs
{
	margin-top: 5px;
}
TABLE.scpanel
{
	border-bottom: #bda692 1px dotted;
	border-left: #bda692 1px dotted;
	margin: 5px 0px 0px;
	width: 100%;
	background: #f3f7fa;
	float: left;
	font-size: 12px;
	border-top: #bda692 1px dotted;
	border-right: #bda692 1px dotted;
}
TABLE.scpanel TR TD
{
	padding-bottom: 5px;
	padding-left: 10px;
	padding-top: 5px;
}
TABLE.list
{
	border-bottom: #bda692 1px solid;
	border-left: #bda692 1px solid;
	margin-top: 5px;
	width: 100%;
	border-collapse: collapse;
	background: #fff;
	float: left;
	font-size: 12px;
	border-top: #bda692 1px solid;
	border-right: #bda692 1px solid;
}
TABLE.list THEAD TR TD
{
	border-bottom: #bda692 1px solid;
	border-left: #bda692 1px solid;
	padding-bottom: 0px;
	padding-left: 5px;
	padding-right: 5px;
	background: url(../images/bg_thead_org.png) #ecf3f6 repeat-x;
	height: 30px;
	border-top: #bda692 1px solid;
	font-weight: bold;
	border-right: #bda692 1px solid;
	padding-top: 0px;
}
TABLE.list TBODY TR TH
{
	text-align: left;
	padding-bottom: 0px;
	padding-left: 5px;
	padding-right: 5px;
	background: #ecedef;
	height: 26px;
	padding-top: 0px;
}
TABLE.list TBODY TR TD
{
	padding-bottom: 0px;
	padding-left: 5px;
	padding-right: 5px;
	height: 26px;
	padding-top: 0px;
}
TABLE.list TFOOT TR TD
{
	padding-bottom: 0px;
	padding-left: 5px;
	padding-right: 5px;
	height: 36px;
	padding-top: 0px;
}
TABLE.list TBODY TR.dl
{
	background: #e7f7ff;
}
TABLE.list TD.maintain
{
	padding-bottom: 5px;
	padding-top: 5px;
}
TABLE.list TD.conText
{
	padding-bottom: 5px;
	padding-top: 5px;
}
TABLE.linkPanel
{
	line-height: 2;
	margin-top: 5px;
	width: 100%;
	border-collapse: collapse;
	float: left;
	font-size: 12px;
}
TABLE.formTable
{
	border-bottom: #bda692 1px solid;
	border-left: #bda692 1px solid;
	line-height: 2;
	margin-top: 5px;
	width: 100%;
	border-collapse: collapse;
	background: #fff;
	float: left;
	font-size: 12px;
	border-top: #bda692 1px solid;
	border-right: #bda692 1px solid;
}
TABLE.formTable THEAD TR TD
{
	border-bottom: #bda692 1px solid;
	border-left: #bda692 1px solid;
	text-indent: 1em;
	background: url(../images/bg_thead_org.png) #ecf3f6 repeat-x;
	height: 30px;
	font-size: 12px;
	border-top: #bda692 1px solid;
	font-weight: bold;
	border-right: #bda692 1px solid;
}
TABLE.formTable TBODY TR TH
{
	text-align: right;
	padding-right: 10px;
	height: 30px;
	font-weight: bold;
}
TABLE.formTable TBODY TR TD
{
	height: 26px;
}
.x-tab-panel-body .x-panel-body
{
	padding-bottom: 10px;
	padding-left: 10px;
	padding-right: 10px;
	padding-top: 10px;
}
.module
{
	border-bottom: #8cb0bf 1px solid;
	border-left: #8cb0bf 1px solid;
	width: 100%;
	margin-bottom: 10px;
	background: #fff;
	float: left;
	border-top: #8cb0bf 1px solid;
	border-right: #8cb0bf 1px solid;
}
.module H3
{
	border-bottom: #b8c9d7 1px solid;
	text-indent: 1em;
	width: 100%;
	background: url(../images/bg_thead_org.png) #ecf3f6 repeat-x;
	float: left;
	height: 30px;
	font-size: 12px;
	padding-top: 8px;
}
.module UL
{
	padding-bottom: 10px;
	padding-left: 10px;
	padding-right: 10px;
	float: left;
	padding-top: 10px;
}
.module UL LI
{
	padding-bottom: 3px;
	padding-left: 12px;
	width: 100%;
	padding-right: 0px;
	background: url(../images/bg_li_square.png) no-repeat left 50%;
	float: left;
	padding-top: 3px;
}
DL.linklist
{
	padding-bottom: 10px;
	padding-left: 10px;
	width: 100%;
	padding-right: 10px;
	float: left;
	padding-top: 10px;
}
DL.linklist DT
{
	width: 100%;
	float: left;
	height: 30px;
	font-weight: bold;
}
DL.linklist DD
{
	width: 150px;
	float: left;
	height: 30px;
}
.goodsImg
{
	padding-bottom: 5px;
	padding-left: 5px;
	padding-right: 5px;
	display: block;
	float: left;
	height: 100%;
	padding-top: 5px;
}
.tabs1
{
	list-style-type: none;
	width: 660px;
	height: 23px;
}
.tabs1 A
{
	padding-bottom: 2px;
	padding-left: 20px;
	padding-right: 20px;
	display: block;
	background: #eff7ff;
	float: left;
	margin-right: 2px;
	text-decoration: none;
	padding-top: 2px;
}
.tabs1 A.current
{
	background: #a1c6de;
	color: #000;
}
.tab1
{
	border-bottom: #ccc 0px solid;
	border-left: #ccc 0px solid;
	width: 660px;
	display: block;
	height: 280px;
	border-top: #ccc 0px solid;
	border-right: #ccc 0px solid;
}
.operation_buttons1
{
	border-bottom: #e4e4e4 0px solid;
	text-align: left;
	width: 100%;
	float: left;
	height: 34px;
	padding-top: 10px;
}
.operation_buttons1 SPAN
{
	display: inline;
}
.operation_buttons1 INPUT
{
	margin-right: 10px;
}
.operation_buttons2
{
	border-bottom: #e4e4e4 0px solid;
	text-align: left;
	width: 100%;
	float: left;
	height: 10px;
	padding-top: 10px;
}
.operation_buttons2 SPAN
{
	display: inline;
}
.operation_buttons2 INPUT
{
	margin-right: 10px;
}
