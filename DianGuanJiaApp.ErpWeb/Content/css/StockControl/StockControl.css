body {
    background-color:#fff;
}
.bindWrap {
    padding:15px;
    padding-bottom:0;
    box-sizing:border-box;
    padding-top:10px;
}
        .layui-mywrap {
            padding: 15px;
        }

        .icon-tianjia {
            margin-right: 3px;
        }

        .layui-mysearch {
            margin: 15px 0 0 0;
            padding: 15px;
            background-color: #f8f8f8;
        }

        .mysearch-partOne {
            margin-right: 0;
        }

            .mysearch-partOne .layui-input-inline {
                margin-right: 10px;
            }

        .mysearch-partOne {
            margin-bottom: 0;
        }

            .mysearch-partOne .layui-input-inline {
                width: 142px;
            }

        .layui-mytable {
            margin-top: 15px;
        }
        .bindProductDailogWrap {
            position:fixed;
            background-color:rgba(0,0,0,0.4);
            width:100%;
            height:100%;
            bottom:0;
            left:0;
            right:0;
            top:40px;
            display:flex;
            justify-content:center;
            align-items:center;

        }
        .bindProductDailog {
            padding: 15px;
            width: 970px;
            height: 560px;
            box-sizing: border-box;
            background-color: #fff;
            box-sizing: border-box;
        }

        .unbindProductDailog {
            padding: 15px;
            width: 870px;
            background-color: #fff;
            box-sizing: border-box;
            height: 550px;
            overflow-y: auto;
            display: none;
        }

        .bindProduct-content {
            display: flex;
            padding: 15px 0;
        }
.bindWrap.bindWrap-chooseProduct .bindProduct-content {
    padding-top: 10px;
}

            .bindProduct-content > div {
                flex: 1;
            }

            .bindProduct-content .bindProduct-content-title {
                font-size: 14px;
                color: #04385d;
                padding: 5px 0;
                display: flex;
                justify-content: space-between;
            }

                .bindProduct-content .bindProduct-content-title > span > i {
                    color: #3aadff;
                    font-weight: 700;
                    padding: 0 5px;
                }

        .bindProduct-content-left {
            margin-right: 15px;
        }

            .bindProduct-content-left .productShow,
            .bindProduct-content-right .productShow {
                display: flex;
            }

                .bindProduct-content-left .productShow img,
                .bindProduct-content-right .productShow img {
                    width: 50px;
                    height: 50px;
                    margin-right: 3px;
                }

                .bindProduct-content-left .productShow > ul,
                .bindProduct-content-right .productShow > ul {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-around;
                    height: auto;
                }

                    .bindProduct-content-left .productShow > ul > li,
                    .bindProduct-content-right .productShow > ul > li {
                        /* overflow: hidden; */
                        /* text-overflow: ellipsis; */
                        /* white-space: nowrap; */
                        /* width: 190px; */
                        word-break: break-all;
                    }

        .unbindProductDailog .bindProduct-content-left .productShow > ul > li {
            width: 100%;
        }

        .stockup_table_content tbody tr td {
            padding: 5px 10px;
        }

        .shopName > .icon-yun {
            position: relative;
            top: 2px;
        }

        /* 		.page2{
            padding-top: 15px;
        }
        .page2 .tcdNumber,.page2 .prevPage{
            margin-right:-1px;
        }
        .page2 .current{
            background-color: #3aadff;
            color:#fff!important;
        }
        .mCustomScrollbar{
            height: 400px;
            overflow: auto;
        } */

        .page2 {
            padding-top: 15px;
            text-align: right;
        }

            .page2 .tcdNumber,
            .page2 .prevPage {
                margin-right: -1px;
            }

            .page2 .current {
                background-color: #3aadff;
                color: #fff !important;
                border: 1px solid #3aadff;
                position: relative;
                top: -1px;
            }

            .page2 > div > a,
            .page2 > div > span {
                height: 30px !important;
                display: inline-block;
                width: 35px !important;
            }

            .page2 > div .disabled,
            .page2 > div .nextPage,
            .page2 > div .prevPage {
                width: 50px !important;
            }

        .stockup_table_content {
            border-left: none;
        }

            .stockup_table_content thead tr th,
            .stockup_table_content tbody tr td {
                border-right: none;
                border-top: 1px solid #e5e9f2;
                border-bottom: 1px solid #e5e9f2;
            }
        /* 		.layui-mytable{
            max-width: 1350px;
        } */

        .layui-mywrap .productShow {
            display: flex;
            align-items: center;
        }

        .productShow > img {
            width: 60px;
            height: 60px;
            margin: 0 5px;
        }

        .productShow > ul {
            height: 60px;
            justify-content: space-around;
            display: flex;
            flex-direction: column;
        }

        .productSkuTr {
            background-color: #f8f8f8;
        }

        .skuProductShow {
            display: flex;
            padding-left: 65px;
        }

            .skuProductShow > img {
                margin: 0 5px;
                width: 50px;
                height: 50px;
            }

            .skuProductShow > ul {
                height: 50px;
                display: flex;
                flex-direction: column;
            }

        .productSkuTr .hideSkuSpan {
            cursor: pointer;
            color: #3aadff;
            margin-left: 135px;
        }

            .productSkuTr .hideSkuSpan > i {
                display: inline-block;
                transform: rotate(180deg);
                font-size: 12px;
                margin-left: 5px;
            }

        .hideSkuTr {
            display: none;
        }

        .stockup_table_content tbody tr.productSkuTr td {
            border-top: none !important;
            border-bottom: none !important;
        }

            .stockup_table_content tbody tr.productSkuTr td.hideTd {
                border-top: 1px solid #e5e9f2 !important;
                border-bottom: 1px solid #e5e9f2 !important;
            }

        .stockup_table_content tbody tr td.tableOparete {
            text-align: right;
        }

            .stockup_table_content tbody tr td.tableOparete > a {
                border-right: 1px solid #ddd;
                padding-right: 5px;
                display: inline-block;
            }

                .stockup_table_content tbody tr td.tableOparete > a:last-child {
                    border-right: none;
                    padding-right: 0;
                }

        .bindProductDailog .layui-tab-title, .unbindProductDailog .layui-tab-title {
            margin-bottom: 15px;
            height: 35px;
        }

            .bindProductDailog .layui-tab-title .layui-this, .unbindProductDailog .layui-tab-title .layui-this {
                background-color: #f59c1a;
                color: #fff;
            }

            .bindProductDailog .layui-tab-title li, .unbindProductDailog .layui-tab-title li {
                line-height: 35px;
                height: 35px;
            }

            .bindProductDailog .layui-tab-title .layui-this:after, .unbindProductDailog .layui-tab-title .layui-this:after {
                height: 36px;
            }

        .attributes-litem {
            padding-right: 5px;
            color: #04385d;
        }

        .layui-mytable-operate {
            display: flex;
            justify-content: flex-end;
            padding-bottom: 10px;
        }

        .wareHouseNum {
            display: flex;
            align-items: center;
        }

            .wareHouseNum.hide {
                display: none;
            }

            .wareHouseNum > input {
                width: 80px;
                height: 28px;
                border: 1px solid #e2e2e2;
                padding-left: 3px;
                box-sizing: border-box;
                color: #666;
                font-size: 12px;
                display: none;
            }

                .wareHouseNum > input.active {
                    display: block;
                }

            .wareHouseNum > i {
                width: 20px;
                height: 12px;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #e2e2e2;
                cursor: pointer;
                line-height: 12px;
                font-size: 14px;
                color: #888;
                margin: 0 3px;
            }

        .showOrHideSku {
            display: flex;
            align-items: center;
            margin: 0 15px;
            color: #3aadff;
        }

        .layui-mytable-operate .iconfont {
            font-size: 14px !important;
            margin-right: 3px;
        }

        .wareHouseNum > .wareHouseNumShow {
            min-width: 35px;
            text-align: right;
            display: inline-block;
            color: #3aadff;
        }

        .wareHouseTitle {
            margin-left: 5px;
        }

        .setWareHouseItem {
            position: relative;
        }

            .setWareHouseItem .icon-xiangxia {
                display: inline-block;
                transform: rotate(180deg);
                font-size: 12px;
                margin-left: 3px;
            }

        .setWareHouseItem-ul {
            position: absolute;
            /* border: 1px solid #e2e2e2; */
            top: -53px;
            left: 0;
            width: 80px;
            padding: 0 5px;
            background-color: #fff;
            border-radius: 2px;
            box-shadow: 0 0 5px 0px #e2e2e2;
            text-align: left;
            color: #888;
            display: none;
        }

        .setWareHouseItem:hover .setWareHouseItem-ul {
            display: block;
        }

        .setWareHouseItem-ul > li {
            padding: 3px;
            cursor: pointer;
        }

            .setWareHouseItem-ul > li:hover {
                color: #3aadff;
            }

        .bindProduct-content-tableWrap {
            width: 450px;
            max-height: 400px;
            box-sizing: border-box;
            overflow: auto;
        }
.bindProduct-content-tableWrap.unBindProduct-content-tableWrap{
    width:950px;
}


        .unbindProductDailog .productShow > ul {
            height: unset;
        }

        .layui-laypage {
            margin-bottom: 0;
        }


        /*.newCreateWareHouse-dailog {
            width: 500px;
            height: 300px;
            display: none;
            padding: 15px;
            background-color: #fcebd1;
            box-sizing: border-box;
        }

            .newCreateWareHouse-dailog > .newCreateWareHouse-main {
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
            }

            .newCreateWareHouse-dailog .icon-cangku1 {
                margin-right: 3px;
            }

            .newCreateWareHouse-dailog .layui-btn-normal {
                margin-top: 20px;
            }*/

        .wareHouseNumWrap {
            display: flex;
            align-items: center;
        }

        .stockup_table_content .skuItem:hover {
            background-color: #eaeaea;
        }

        .unbindProductDailog .bindProduct-content-tableWrap {
            width: 100%;
        }

        .productSkuTr.skuItem {
            border-bottom: 1px solid #f0f0f0;
        }



        .newCreateWareHouse-dailog {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            justify-content: center;
            align-items: center;
            background-color: rgba(0,0,0,0.4);
            display: none;
        }

        .newCreateWareHouse-content {
            width: 500px;
            height: 300px;
            padding: 15px;
            background-color: #fcebd1;
            box-sizing: border-box;
        }

            .newCreateWareHouse-content > .newCreateWareHouse-main {
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
            }

            .newCreateWareHouse-content .icon-cangku1 {
                margin-right: 3px;
            }

            .newCreateWareHouse-content .layui-btn-normal {
                margin-top: 20px;
            }

        .setWareHouseNumBtn > i {
            margin-right: 3px;
            font-size: 14px !important;
        }

        .layui-btn-sm {
            height: 25px;
            line-height: 25px;
        }
.footer {
    position:fixed;
    display:flex;
    bottom:0;
    right:0;
    padding:15px;
    width:30%;
    justify-content:flex-end;
    box-sizing:border-box;
}
.footer > .btn {
    height: 28px;
    line-height: 28px;
    margin: 5px 5px 0;
    padding: 0 15px;
    border: 1px solid #dedede;
    background-color: #fff;
    color: #333;
    border-radius: 2px;
    font-weight: 400;
    cursor: pointer;
    text-decoration: none;
    margin-left:10px;
}
.footer > .btn:hover {
    opacity:0.8;
}
.bindWrap-chooseProduct.bindWrap .layui-form {
    display: flex;
}
.bindWrap-chooseProduct.bindWrap input[type=text], .bindWrap select {
    height: 30px;
    box-sizing: border-box;
}

.bindWrap-chooseProduct.bindWrap .selectWrap input {
    display: inline-block;
}

.bindWrap-chooseProduct.bindWrap .selectMore {
    height: 30px;
    top:0;
    line-height:28px;
}

.bindWrap-chooseProduct.bindWrap .selectWrap {
    margin-right:10px;
}

.bindWrap-chooseProduct.bindWrap .layui-form-left {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
}

.bindWrap-chooseProduct.bindWrap .layui-form-right {
    display: flex;
    flex-direction: row;
    padding-right: 28px;
}
.bindWrap.bindWrap-chooseProduct .checkproductLists-ul > li {
    display:flex;
}
.bindWrap.bindWrap-chooseProduct .checkproductLists-ul > li .spgg {
    flex:1;
}

.bindWrap.bindWrap-chooseProduct .checkproductLists-ul li .setprice {
    width:50px;
    text-align:center;
    flex: 1;
}

.bindWrap.bindWrap-chooseProduct .clooseSkuWrap {
    display:flex;
}
.bindWrap.bindWrap-chooseProduct .clooseSkuWrap-showSku {
    width: 250px;
    border: 1px solid #e5e9f2;
    overflow-y: auto;
    margin-left:5px;
    box-sizing:border-box;
    padding:15px;
    height:380px;
}
.clooseSkuWrap-showSku-warn {
    display:flex;
    flex-direction:row;
    font-size:14px;
    color:#999;
    align-items:center;
}
.clooseSkuWrap-showSku-warn .icon-xiayi {
    display:inline-block;
    transform:rotate(90deg);
    font-weight:100;
    margin-right:3px;
}
.clooseSkuWrap-showSku-main {
    display: flex;
    flex-direction: column;
    flex: 1;
    padding-top: 30px;
    position: relative;
}
.clooseSkuWrap-showSku-main>li {
    display:flex;
    padding:5px 0;
}
.clooseSkuWrap-showSku-main > li>label {
    display: flex;
    align-items:center;
}
.clooseSkuWrap-showSku-main > li > label > span:first-child {
    margin-right:3px;
}
#table_tbody_productList tr {
   cursor:pointer;
}
#table_tbody_productList tr.active {
    background-color: rgba(8, 136, 255, 0.08) !important;
}
.checkLeftProductList-sku-title {
    position: absolute;
    top: -14px;
    left: -14px;
    width: 113%;
    height: 30px;
    background-color: #f6f9fd;
    box-sizing: border-box;
    padding-left: 5px!important;
    border-bottom: 1px solid #e5e9f2;
}
