.calender-wrap {
    /*-webkit-animation:clafade .3s ease;*/
    /*-moz-animation:clafade .3s ease;*/
    /*animation:clafade .3s ease;*/
    padding:5px;
    background:#fff;
    width:220px;
    border-radius:4px;
    position:relative;
    font-family:"Microsoft yahei";
    position:absolute;
    z-index:1000
}
.calender-wrap:nth-child(1):after {
    content:'';
    display:inline-block;
    border-left:7px solid transparent;
    border-right:7px solid transparent;
    border-bottom:7px solid #eee;
    border-top:0;
    border-bottom-color:#d7d7d7;
    position:absolute;
    left:9px;
    top:-7px
}
.calender-wrap:nth-child(1):before {
    content:'';
    display:inline-block;
    border-left:6px solid transparent;
    border-right:6px solid transparent;
    border-bottom:6px solid #ffffff;
    border-top:0;
    position:absolute;
    left:10px;
    top:-6px;
    z-index:10
}
.calender-caption {
    height:35px;
    border-bottom:1px solid #ddd;
    z-index:2;
    background:#eee
}
.calender-content,calender-content2 {
    position:relative;
    overflow:hidden
}
.calender-content:after,.calender-content2:after {
    content:'';
    display:block;
    clear:both
}
.calender-cell {
    cursor:pointer;
    float:left;
    width:14.28571428%;
    height:30px;
    text-align:center;
    line-height:30px;
    font-size:12px;
    color:#000;
    z-index:1;
    border-bottom:1px solid #eee
}
.calender-cell:hover {
    background:#eee
}
.calender-caption .calender-cell:hover {
    background:none
}
.calender-cell-dark {
    cursor:no-drop;
    color:#b9b9b9
}
.calender-caption .calender-cell {
    height:35px;
    line-height:35px;
    font-size:13px;
    color:#111;
    font-weight:bold
}
.calender-header {
    text-align:center;
    line-height:35px;
    text-align:center;
    color:#888;
    padding-bottom:4px;
    margin-bottom:1px;
    background:#fff;
    position:relative;
    border-bottom:1px solid #e6e6e6;
    font-size:14px
}
#calender-prev,#calender-next,#calender-prev2,#calender-next2 {
    text-decoration:none;
    display:block;
    width:14.2857%;
    height:35px;
    background:#fff;
    position:absolute;
    left:0%;
    top:0px;
    font-family:'宋体';
    font-size:14px;
    color:#555
}
#calender-prev,#calender-next,#calender-prev2,#calender-next2 {
    color:#999;
    font-size:16px
}
#calender-prev:hover,#calender-next:hover,#calender-prev2:hover,#calender-next2:hover {
    background:#eee;
    border-radius:5px;
    color:#222
}
#calender-next,#calender-next2 {
    left:auto;
    right:0%
}
#calender-year,#calender-mon,#calender-year2,#calender-mon2 {
    cursor:pointer;
    padding:2px 4px;
    border-radius: 3px;
    margin:0 3px;
}
#calender-year:hover,#calender-mon:hover {
    background:#eee
}
#calender-year2:hover,#calender-mon2:hover {
    background:#eee
}
.calender-list {
    overflow:hidden
}
.calender-list2,.calender-list3 {
    display:none
}
.calender-year-cell,.calender-mon-cell {
    width:32.41%;
    float:left;
    border-radius:4px;
    text-align:center;
    font-size:12px;
    padding:15px 0;
    border:1px solid #fff
}
.calender-year-cell:hover,.calender-mon-cell:hover {
    background:#eee;
    cursor:pointer
}
.calender-cell.active,.calender-year-cell.active,.calender-mon-cell.active {
    background:#23acf1;
    color:#fff
}
.calender-cell.active:hover,.calender-year-cell.active:hover,.calender-mon-cell.active:hover {
    background:#23acf1;
    color:#fff
}
.calender-button {
    border-top:1px solid #eee;
    width:100%;
    margin-top:-1px;
    padding:7px 0px 2px 0;
    overflow:hidden
}
.calender-button a {
    display:block;
    text-align:center;
    padding:0px 15px;
    height: 25px;
    line-height: 25px;
    float:right;
    background:#23acf1;
    color:#fff;
    margin-right:5px;
    cursor:pointer;
    margin-left:5px;
    font-size:12px;
    text-decoration:none
}
.calender-button a:hover {
    background:#0084c9
}
.calender-wrap.year .calender-list,.calender-wrap.month .calender-list {
    display:none
}
.calender-wrap.year .calender-list2 {
    display:block
}
.calender-wrap.month .calender-list3 {
    display:block
}
/*@keyframes clafade {*/
/*0% {*/
/*transform:scale(0.95);*/
/*opacity:0*/
/*}*/
/*100% {*/
/*transform:scale(1);*/
/*opacity:1*/
/*}*/
/*}@-webkit-keyframes clafade {*/
/*0% {*/
/*-webkit-transform:scale(0.95);*/
/*opacity:0*/
/*}*/
/*100% {*/
/*-webkit-transform:scale(1);*/
/*opacity:1*/
/*}*/
/*}   */
.calend {
    display: block;
    width: 180px;
    line-height: 28px;
    background: #222;
    color: #fff;
    padding: 5px 12px;
    margin:20px 20px 20px 0;
    font-size: 14px;
}
.calender-footer>div{
    padding: 5px;
}
.calender-footer>div>select{
    border:1px solid #ddd;
    width: 50px!important;
}
.wrapperTime{
    border:1px solid #ddd;
    width: 460px;
    box-shadow:0 5px 10px rgba(0,0,0,0.15);
    height: 340px;
    border-radius: 6px;
    display: none;
    position: absolute;
    top:24px;
    left:0;
    background-color:#fff;
    z-index:100;
}
.otheOne{
    margin-left: 230px;
}
.wrapperTime-footer{
    width: 100%;
    height: 30px;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1100;

}
.selectActiveTime{
    float: left;
    margin-left: 10px;
}
.selectActiveTime>span{
    margin-right: 15px;
    padding: 5px 10px;
    background-color: #f0f0f0;
    font-size: 12px;
    color:#23acf1;
    border-radius: 4px;
    cursor: pointer;
}
.calender-footer-button{
    padding: 2px 10px;
    border:1px solid #ddd;
    font-size: 14px;
    cursor: pointer;
    float: right;
    margin-right: 10px;
    border-radius: 4px;

}
.selectActiveTime>.active{
    background-color: #23acf1;
    color:#fff;
}

.inputSelectTime{
    width: 210px;
    height: 22px;
    border:1px solid #e2e2e2;
    position: relative;
    box-sizing: border-box;
    font-size: 12px;
    display: inline-block;
    position: relative;
    top: 4px;
}
.wrapperTime_input{
    width: 212px;
    height: 22px;
    box-sizing: border-box;
    overflow: hidden;
    cursor: pointer;
    line-height: 22px;
    white-space:nowrap;
    padding-left:5px;
}

/*@media screen and (-webkit-min-device-pixel-ratio:0) {
    .wrapperTime_input{
        transform: scale(0.85);
        width: 250px;
        height:25px;
        box-sizing: border-box;
        overflow: hidden;
        cursor: pointer;
        line-height: 22px;
        position: absolute;
        left:-20px;
        top:0;
    }
}*/

/*@-moz-document url-prefix() {
    .wrapperTime_input{
        transform: scale(1);
        width: 212px;
        height:22px;
        left:0;
        top:-2px;
    }
}*/
.wrapperTimeWarn{
    padding: 1px 5px;
    position: absolute;
    z-index: 1200;
    background-color: #fff;
    border: 1px solid red;
    color: red;
    top: -1px;
    left: 215px;
    box-shadow: 1px 1px 2px #ddd;
    display: none;
    width: 150px;
}
