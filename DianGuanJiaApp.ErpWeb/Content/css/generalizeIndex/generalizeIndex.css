.index-wrap {
    max-width: 1420px;
}

    .index-wrap > .index-wrap-top {
        display: flex;
    }

        .index-wrap > .index-wrap-top > .picFull {
            width: 1000px;
            min-width: unset;
        }

        .index-wrap > .index-wrap-top > .notice {
            width: 410px;
            min-width: unset;
            margin-left: 0;
            min-width: 200px;
        }

.layui-mywrap-title {
    padding-bottom: 15px;
    border-bottom: 1px dotted #e2e2e2;
    font-size: 14px;
    color: #04385d;
    display: flex;
    justify-content: space-between;
}

.notice-content {
    padding: 15px 0;
    box-sizing: border-box;
    max-height: 245px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 25px;
}

.index-wrap-main .index-wrap-main-title {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.index-wrap-main-title-wrap {
    width: 220px;
    height: 35px;
    background-color: #fff;
    color: #fff;
    margin: 0 15px 0 15px;
    color: #f59c1b;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.orderSync {
    color: #888;
}

.orderSync-btn {
    display: inline-block;
    background-color: #f5f5f5;
    border-radius: 13px;
    color: #3aadff;
    cursor: pointer;
    margin-left: 10px;
    width: 50px;
    height: 25px;
    line-height: 25px;
    text-align: center;
}

.index-wrap-main-title-wrap .index-wrap-main-title-text {
    position: relative;
    padding-left: 25px;
    animation: shakes 1s linear infinite forwards;
    background: -webkit-gradient(linear,left top,right top,from(#ffae12),to(#fb0555));
    background: linear-gradient(left,#ffae12,#fb0555);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 14px;
    font-weight: 700;
}

@keyframes shakes {
    0% {
        background-position: 0 0
    }

    25% {
        background-position: 0 0
    }

    26% {
        background-position: 50px -50px
    }

    50% {
        background-position: 50px -50px
    }

    51% {
        background-position: 100px -100px
    }

    75% {
        background-position: 100px -100px
    }

    76% {
        background-position: 150px -150px
    }

    99% {
        background-position: 150px -150px
    }

    to {
        background-position: 0 0
    }
}

.index-wrap-main-title-wrap .index-wrap-main-title-text::before {
    position: absolute;
    display: block;
    content: "";
    width: 0;
    height: 0;
    border-top: 35px solid #f59c1b;
    border-right: 18px solid transparent;
    top: -8px;
    left: 0;
}

.layui-mywrap-main-content {
    margin-top: 0;
}

.index-wrap-main .index-wrap-main-title .index-wrap-main-title-wrap .icon-shuju {
    width: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f59c1b;
    height: 35px;
    color: #fff;
    font-size: 20px;
    padding-left: 5px;
    box-sizing: border-box;
    border-bottom-left-radius: 2px;
    border-top-left-radius: 2px;
}

.layui-mywrap-main-content {
    box-sizing: border-box;
    padding: 0;
}

.statusWrap {
    width: 100%;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.statusWrap-noData {
    text-align: center;
    padding-bottom: 15px;
    font-size: 14px;
    color: #f7941f;
}

.statusWrap > .statusItem {
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: center;
    align-items: center;
    border-right: 1px solid #e2e2e2;
}

    .statusWrap > .statusItem > a {
        display: flex;
        flex-direction: column;
        flex: 1;
        justify-content: center;
        align-items: center;
    }

    .statusWrap > .statusItem:last-child {
        border-right: unset;
    }

    .statusWrap > .statusItem > a .orderStatusMun {
        font-size: 14px;
        color: #04385d;
        padding-bottom: 10px;
    }

    .statusWrap > .statusItem > a .orderStatusTitle {
        color: #3aadff;
        font-size: 17px;
        font-weight: 700;
    }

.layui-mywrap-noData {
    width: 100%;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

    .layui-mywrap-noData > .layui-mywrap-noDataWrap {
        display: inline-block;
        position: relative;
    }

        .layui-mywrap-noData > .layui-mywrap-noDataWrap > .layui-btn {
            width: 130px;
            margin-right: 15px;
        }

        .layui-mywrap-noData > .layui-mywrap-noDataWrap > .noData-help {
            position: absolute;
            left: 145px;
            width: 300px;
            line-height: 35px;
        }

.indexNewContact {
    position: fixed;
    right: 5px;
    bottom: 15px;
}

    .indexNewContact > li {
        width: 60px;
        height: 60px;
        margin-top: 15px;
        border-radius: 4px;
        background: #3aadff;
        background: linear-gradient(345deg, #3a72ff, #3aadff);
        display: flex;
        justify-content: center;
        align-items: center;
        color: #e2f2ff;
        flex-direction: column;
        cursor: pointer;
        position: relative;
    }

        .indexNewContact > li > a {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .indexNewContact > li i {
            display: inline-block;
            width: 33px;
            height: 25px;
            background: url(/Content/images/indexicons.png)no-repeat 0 -166px;
            margin-bottom: 5px;
        }

        .indexNewContact > li .indexNewContact-content {
            position: absolute;
            width: 185px;
            background-color: #fff;
            /* border: 1px solid #e2e2e2; */
            top: 0;
            right: 65px;
            box-shadow: 0 0 10px 0px #ccc;
            border-radius: 10px;
            padding: 15px 0;
            display: none;
        }

        .indexNewContact > li#showMoreContent:hover .indexNewContact-content {
            display: block;
        }

        .indexNewContact > li .indexNewContact-content > li {
            display: flex;
            flex-direction: column;
            color: #666;
            padding: 5px 15px;
        }

.userFeedback-dialog {
    width: 650px;
    padding: 15px 30px;
    box-sizing: border-box;
    display: none;
}

.userFeedback-dialog-two {
    margin-bottom: 10px;
}

    .userFeedback-dialog-two > textarea {
        width: 100%;
        height: 150px;
        border: 1px solid #e2e2e2;
    }

.userFeedback-dialog-three > input {
    width: 280px;
    border: 1px solid #e2e2e2;
    height: 30px;
}

.indexNewContact > li a {
    color: #e2f2ff;
}

#showMoreContent > .indexNewContact-content a > .icon-dianhua {
    color: #3aadff;
    font-size: 18px;
}

#showMoreContent > .indexNewContact-content .icon-tubiaozhizuo- {
    color: #3aadff;
    font-size: 18px;
}

.hasData-item-pintaiIcon {
    display: inline-block;
    width: 18px;
    height: 18px;
    background-image: url(/Content/images/pingtai-icon.png);
    background-position: 0 -482px;
}

.hasData-item-gfIcon {
    display: inline-block;
    border-radius: 50%;
    width: 21px;
    height: 21px;
    background-image: url(/Content/images/indexNew-icons02.png);
    background-position: -79px -26px;
}

.fenxiao-icon {
    background-position: -100px -26px;
}

.layui-mywrap-hasDataWrap {
    display: flex;
    width: 100%;
    justify-content: flex-start;
    padding: 10px 0;
    flex-wrap: wrap;
}

    .layui-mywrap-hasDataWrap > .hasData-item {
        width: 295px;
        height: 90px;
        background-color: #f8f8f8;
        margin-right: 30px;
        padding: 10px 15px;
        box-sizing: border-box;
        position: relative;
        margin-bottom: 10px;
    }

        .layui-mywrap-hasDataWrap > .hasData-item > .hasData-item-backgroundImg {
            display: inline-block;
            width: 75px;
            height: 50px;
            background-image: url(/Content/images/indexNew-icons02.png);
            background-position: 0 0;
            position: absolute;
            bottom: 0;
            right: 0;
        }

        .layui-mywrap-hasDataWrap > .hasData-item > .hasData-item-title {
            display: flex;
            border-bottom: 1px dotted #e2e2e2;
            padding-bottom: 10px;
            color: #04385d;
            margin-bottom: 3px;
            align-items: center;
        }

            .layui-mywrap-hasDataWrap > .hasData-item > .hasData-item-title > span {
                margin-left: 5px;
                flex: 1;
            }

        .layui-mywrap-hasDataWrap > .hasData-item > .hasData-item-content > div {
            padding: 3px 0;
            color: #888;
            margin-top: 12px;
        }

            .layui-mywrap-hasDataWrap > .hasData-item > .hasData-item-content > div .hasData-item-munColor {
                color: #f59c1b;
                font-weight: 700;
                padding-left: 5px;
            }

            .layui-mywrap-hasDataWrap > .hasData-item > .hasData-item-content > div .statusItem {
                width: 85px;
                display: inline-block;
            }

.hasData-item-pintaiIcon.pintaiIcon {
    top: 0;
}

#sp_sync {
    padding-left: 10px;
}

.icon-tongbu {
    position: relative;
    top: 2px;
}

#lblSyncPercent {
    padding-left: 3px;
}

.invitesupplier {
    width: 400px;
    border: 1px solid #e2e2e2;
    position: absolute;
    bottom: 42px;
    left: 144px;
    background-color: #fff;
    z-index: 100;
    border-radius: 2px;
    box-shadow: 0 0 9px 3px #e2e2e2;
    padding: 15px;
    display: none;
}

    .invitesupplier::after {
        display: block;
        content: "";
        position: absolute;
        bottom: -7px;
        left: 5px;
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-top: 10px solid #fff;
    }

    .invitesupplier .invitesupplier-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

        .invitesupplier .invitesupplier-content > img {
            width: 180px;
            height: 180px;
        }

    .invitesupplier .invitesupplier-title {
        padding: 15px;
        text-align: left;
        margin-bottom: 15px;
    }

.invitesupplier-content-oparte {
    display: flex;
    width: 100%;
}

    .invitesupplier-content-oparte > input {
        margin-right: 15px;
    }

.mysearch-partOne {
    margin-bottom: 0;
}

.invitesupplier .icon-jia-copy1 {
    position: absolute;
    top: 5px;
    right: 10px;
    display: inline-block;
    transform: rotate(45deg);
    font-size: 20px;
    cursor: pointer;
}

.warn-wrap {
    background-color: #fce6e1;
    height: 35px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    margin-left: 25px;
    font-size: 12px;
    color: #ff3c00;
    flex: 1;
    margin-right: 15px;
}

    .warn-wrap .icon {
        display: inline-block;
        height: 17px;
        width: 17px;
        border-radius: 10px;
        text-align: center;
        line-height: 17px;
        background-color: #ff5b28;
        color: #fff;
        font-size: 12px;
        margin-right: 3px;
    }

.layui-mywrap-title-right {
    padding-left: 10px;
    border-left: 1px solid #e2e2e2;
    margin-left: 10px;
}

.newUserFeedback-dialog {
    width: 400px;
    text-align: center;
}

.newRightContact {
    position: fixed;
    right: 0;
    bottom: 15px;
    display: flex;
    flex-direction: column;
    z-index: 9999;
}

    .newRightContact .newRightContact-top {
        display: flex;
        justify-content: center;
        align-items: center;
    }

        .newRightContact .newRightContact-top .newRightContact-img {
            display: inline-block;
            width: 48px;
            height: 44px;
            background: url(/Content/images/2022-contact-right.png)no-repeat 0 0;
        }

    .newRightContact .newRightContact-content {
        display: flex;
        flex-direction: column;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        background-color: #3a95ff;
    }

    .newRightContact .newRightContact-item {
        height: 42px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 15px;
        border-bottom: 1px solid #358cf1;
        position: relative;
    }

        .newRightContact .newRightContact-item:last-child {
            border-bottom: unset;
        }

        .newRightContact .newRightContact-item:hover {
            background-color: #0076ff;
        }

            .newRightContact .newRightContact-item:hover .newRightContact-item-left-title {
                word-break: break-all;
                width: 25px;
                display: block !important;
            }

            .newRightContact .newRightContact-item:hover .newRightContact-item-left-icon {
                display: none !important;
            }

        .newRightContact .newRightContact-item .newRightContact-item-left {
            display: flex;
            height: 42px;
            flex-direction: row;
            align-items: center;
            color: #fff;
            position: relative;
        }

            .newRightContact .newRightContact-item .newRightContact-item-left .newRightContact-item-left-icon {
                display: inline-block;
                width: 25px;
                height: 20px;
                background: url(/Content/images/2022-contact-right.png)no-repeat 0 -44px;
            }

            .newRightContact .newRightContact-item .newRightContact-item-left .newRightContact-item-left-title {
                display: none;
            }

        .newRightContact .newRightContact-item .newRightContact-item-right {
            display: flex;
            position: absolute;
            right: 0;
            flex-direction: column;
            padding: 15px;
            border-radius: 8px;
            background-color: #fff;
            box-shadow: 0 0 11px 1px #e2e2e2;
            bottom: -130px;
            display: none;
        }

        .newRightContact .newRightContact-item:hover .newRightContact-item-right.active {
            opacity: 0;
            right: -108px;
            animation: showContact 0.6s ease-in-out 0.8s 1 alternate forwards;
            display: flex;
        }

@keyframes showContact {
    from {
        opacity: 0;
        right: -108px;
    }

    to {
        opacity: 1;
        right: 56px;
    }
}

.newRightContact .newRightContact-content.active .newRightContact-item-left-title {
    margin-left: 5px;
    width: unset;
    display: block !important;
}

.newRightContact .newRightContact-content.active .newRightContact-item-left-icon {
    display: inline-block !important;
}

.newRightContact .newRightContact-content.active .newRightContact-item:hover .newRightContact-item-right.active {
    animation: showContact02 0.6s ease-in-out 0s 1 alternate forwards;
}

@keyframes showContact02 {
    from {
        opacity: 0;
        right: -108px;
    }

    to {
        opacity: 1;
        right: 108px;
    }
}


.aialog-newGreenhandWrap {
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 1000000;
    position: fixed;
    left: 170px;
    bottom: 0;
    top: 0;
    right: 0;
    display: none;
}

    .aialog-newGreenhandWrap.hide {
        background-color: unset;
        animation: hideNewGreenhandWrap 1.5s linear alternate forwards;
    }

        .aialog-newGreenhandWrap.hide.hide02 {
            background-color: unset;
            animation: hideNewGreenhandWrap 0s linear alternate forwards;
        }

@keyframes hideNewGreenhandWrap {
    0% {
        left: 0;
    }

    100% {
        left: 3500px;
    }
}

.aialog-newGreenhandWrap.show {
    background-color: unset;
    animation: showNewGreenhandWrap 0.5s linear alternate forwards;
}

@keyframes showNewGreenhandWrap {
    0% {
        background-color: unset;
        left: 3500px;
    }

    100% {
        background-color: rgba(0, 0, 0, 0.4);
        left: 170px;
    }
}

.aialog-newGreenhandWrap .newGreenhandWrap-content {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    box-shadow: 0px -3px 9px 0px #555555;
    background: rgba(253, 253, 253, 0.9);
}

.aialog-newGreenhandWrap.hide .newGreenhandWrap-content {
    animation: hideNewGreenhandContent 1.5s linear alternate forwards;
}

@keyframes hideNewGreenhandContent {
    0% {
        bottom: 0;
    }

    100% {
        bottom: 420px;
    }
}

.aialog-newGreenhandWrap .newGreenhandWrap-content .newGreenhandWrap-content-title {
    font-size: 18px;
    font-weight: 700;
    font-style: italic;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    color: #f59c1a;
    letter-spacing: 2px;
    background-color: #fff;
    position: relative;
}

    .aialog-newGreenhandWrap .newGreenhandWrap-content .newGreenhandWrap-content-title .newGreenhandWrap-main-title {
        position: absolute;
        left: 15px;
    }

    .aialog-newGreenhandWrap .newGreenhandWrap-content .newGreenhandWrap-content-title .newGreenhandWrap-main-Nav {
        display: flex;
        font-style: normal;
        font-size: 14px;
        font-weight: 400;
        padding: 0 15px;
        color: #666;
    }

        .aialog-newGreenhandWrap .newGreenhandWrap-content .newGreenhandWrap-content-title .newGreenhandWrap-main-Nav li {
            padding: 10px 50px;
            cursor: pointer;
            font-size: 20px;
            margin-right: 2px;
            font-weight: 700;
        }

            .aialog-newGreenhandWrap .newGreenhandWrap-content .newGreenhandWrap-content-title .newGreenhandWrap-main-Nav li:hover {
                color: #3aadff;
                position: relative;
            }

                .aialog-newGreenhandWrap .newGreenhandWrap-content .newGreenhandWrap-content-title .newGreenhandWrap-main-Nav li:hover::before {
                    width: 0;
                    height: 5px;
                    display: block;
                    content: "";
                    background-color: #3aadff;
                    position: absolute;
                    left: 0;
                    bottom: -8px;
                    animation: navShowHover 0.3s ease alternate forwards;
                }

@keyframes navShowHover {
    0% {
        width: 0;
    }

    100% {
        width: 100%;
    }
}

.aialog-newGreenhandWrap .newGreenhandWrap-content .newGreenhandWrap-content-title .newGreenhandWrap-main-Nav li.active {
    color: #3aadff;
    position: relative;
}

    .aialog-newGreenhandWrap .newGreenhandWrap-content .newGreenhandWrap-content-title .newGreenhandWrap-main-Nav li.active::before {
        width: 100%;
        height: 5px;
        display: block;
        content: "";
        background-color: #3aadff;
        position: absolute;
        left: 0;
        bottom: -8px;
        animation: unset;
    }

.aialog-newGreenhandWrap .newGreenhandWrap-content .newGreenhandWrap-content-title .icon-kuaisu {
    font-size: 24px;
    font-weight: 400;
    position: relative;
    top: 2px;
}

.newGreenhandWrap-setBtns {
    position: fixed;
    bottom: 0;
    right: 0;
    display: none;
    flex-direction: column;
    z-index: 10000000;
}

    .newGreenhandWrap-setBtns .newGreenhandWrap-hideOrShow {
        position: absolute;
        bottom: 105px;
        right: 0;
        width: 23px;
        height: 105px;
        z-index: 1000;
        display: flex;
        background-image: url(/Content/images/newGreenhand-icons.png);
        color: #fff;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        cursor: pointer;
        background-position: -23px 0;
    }




        .newGreenhandWrap-setBtns .newGreenhandWrap-hideOrShow .hideOrShow-title {
            position: relative;
            left: 6px;
        }

        .newGreenhandWrap-setBtns .newGreenhandWrap-hideOrShow .icon-xiangxia {
            transform: rotate(270deg);
            display: inline-block;
            font-size: 14px;
            position: relative;
            top: 3px;
            left: 0;
            animation: showMoreIcon 0.6s linear infinite;
        }

        .newGreenhandWrap-setBtns .newGreenhandWrap-hideOrShow.showBtn .icon-xiangxia {
            transform: rotate(90deg);
        }


@keyframes showMoreIcon {
    0% {
        left: 0;
    }

    50% {
        left: 2px;
    }

    100% {
        left: 0;
    }
}

.aialog-newGreenhandWrap .newGreenhandWrap-steps {
    display: flex;
    padding: 30px 20px;
    justify-content: space-between;
}

    .aialog-newGreenhandWrap .newGreenhandWrap-steps .newGreenhandWrap-steps-item {
        width: 390px;
        height: 210px;
        background-color: #fff;
        padding: 0 10px;
        display: flex;
        flex-direction: column;
        box-shadow: 0 0 10px 2px #e2e2e2;
        overflow: hidden;
    }

.aialog-newGreenhandWrap.hide .newGreenhandWrap-steps .newGreenhandWrap-steps-item {
    animation: hideNewGreenhandItem 1.2s linear alternate forwards;
}

@keyframes hideNewGreenhandItem {
    0% {
        width: 390px;
        height: 240px;
    }

    100% {
        width: 0;
        height: 0;
    }
}

/* 		.aialog-newGreenhandWrap.show .newGreenhandWrap-steps .newGreenhandWrap-steps-item{
            animation: showNewGreenhandItem 1.5s linear alternate forwards;
        }
        @@keyframes showNewGreenhandItem {
            0% {
                width: 0;
                height: 0;
            }

            100% {
                width: 390;
                height: 240;
            }
        } */
.aialog-newGreenhandWrap .newGreenhandWrap-steps .newGreenhandWrap-steps-item .newGreenhandWrap-steps-itemTitle {
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    font-weight: 700;
    color: #999;
    border-bottom: 1px solid #f0f0f0;
}

.aialog-newGreenhandWrap .newGreenhandWrap-steps .newGreenhandWrap-steps-item .newGreenhandWrap-steps-itemContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    color: #04385d;
    font-size: 14px;
}

    .aialog-newGreenhandWrap .newGreenhandWrap-steps .newGreenhandWrap-steps-item .newGreenhandWrap-steps-itemContent > span > i {
        cursor: pointer;
        color: #3aadff;
    }

.aialog-newGreenhandWrap .newGreenhandWrap-steps .newGreenhandWrap-steps-direction {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
}

    .aialog-newGreenhandWrap .newGreenhandWrap-steps .newGreenhandWrap-steps-direction .icon-buzhou {
        color: #3cc3a2;
        font-size: 24px;
        animation: showMoreIcon 0.6s linear infinite;
        position: relative;
    }

.newGreenhandWrap-setBtns .newGreenhandWrap-hideOrShow.showBtn {
    position: fixed;
    bottom: 320px;
    right: -23px;
    width: 23px;
    height: 105px;
    z-index: 1000;
    display: flex;
    background-image: url(/Content/images/newGreenhand-icons.png);
    color: #fff;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    cursor: pointer;
    background-position: 0 0;
}

.newGreenhandWrap-setBtns.hide .hideBtn {
    animation: hideNewGreenhandHideBtn 0.5s linear alternate forwards;
}

.newGreenhandWrap-setBtns.hide.hide02 .hideBtn {
    animation: hideNewGreenhandHideBtn 0s linear alternate forwards;
}

.newGreenhandWrap-setBtns.hide .newGreenhandWrap-hideOrShow.showBtn {
    animation: hideNewGreenhandshowBtnn 0.5s 0.8s linear alternate forwards;
}

.newGreenhandWrap-setBtns.hide.hide02 .newGreenhandWrap-hideOrShow.showBtn {
    animation: hideNewGreenhandshowBtnn 0s 0s linear alternate forwards;
}

@keyframes hideNewGreenhandHideBtn {
    0% {
        right: 0;
    }

    100% {
        right: -23px;
    }
}

.newGreenhandWrap-setBtns.hide .showBtn {
    animation: hideNewGreenhandshowBtn 10s 2s linear alternate forwards;
}

@keyframes hideNewGreenhandshowBtnn {
    0% {
        right: -23px;
    }

    100% {
        right: 0;
    }
}

.aialog-newGreenhandWrap .newGreenhandWrap-steps .newGreenhandWrap-steps-item .newGreenhandWrap-steps-itemTitle .itemTitleNum {
    display: inline-block;
    width: 30px;
    height: 20px;
    background-image: url(/Content/images/newGreenhand-02.png);
    background-position: 0 -40px;
    position: relative;
    top: 2px;
    left: -5px;
    opacity: 0.7;
}

.itemTitleNum.two {
    background-position: -30px -40px !important;
}

.itemTitleNum.three {
    background-position: -60px -40px !important;
}


.adialog-Shops > .stockup_table_content.kuajing > tbody > tr > td {
    background-image: url(/Content/images/kuanjin-icons.png);
    background-position: 0 0;
    background-color:unset;
}

        .adialog-Shops > .stockup_table_content > tbody > tr > td.active {
            position: relative;
            color: #f59c1a;
            text-align: center;
        }

            .adialog-Shops > .stockup_table_content > tbody > tr > td.active::before {
                display: block;
                position: absolute;
                width: 100%;
                height: 100%;
                content: "";
                border: 1px solid #f59c1a;
                top: 0;
                left: 0;
                box-shadow: 0 0 7px 0px #fcbc5f;
            }

.mysearch-partOne {
    margin-bottom: 0;
}

.stockup_table_content {
    width: auto;
}

.support-platform-help {
    /* margin-left: 10px; */
    /* color: #3aadff; */
    cursor: pointer;
}

.support-platform-content {
    padding: 15px;
    width: 300px;
    box-sizing: border-box;
}

    .support-platform-content > div {
        margin-bottom: 5px;
    }

    .support-platform-content > input {
        height: 35px;
        width: 270px;
    }

.support-platform-into {
    width: 350px;
    box-sizing: border-box;
    background-color: #fff;
    display: none;
}

    .support-platform-into .support-platform-title {
        background-color: #F8F8F8;
        padding: 15px;
        position: relative;
    }

    .support-platform-into .icon-jia-copy1 {
        position: absolute;
        right: 15px;
        color: #2d2c3b;
        font-size: 18px;
        font-weight: 700;
    }

.support-platform-wxhelpShow {
    display: none;
    width: 1200px;
    height: 700px;
    border: 2px solid #fff;
}

.layui-warnin {
    display: flex;
    border: 1px solid #f59c1a;
    margin-bottom: 15px;
    padding: 10px 15px;
}

    .layui-warnin > ul > li {
        padding: 2px 0;
    }

.iconfontGan {
    width: 20px;
    height: 20px;
    border: 2px solid #f7941f;
    color: #f7941f;
    display: inline-block;
    border-radius: 50%;
    font-weight: 700;
    text-align: center;
    font-size: 17px;
    margin-right: 10px;
    margin-top: 2px;
    line-height: 20px;
}

.layui-warnin-title {
    font-size: 17px;
    font-weight: 700;
    line-height: 20px;
    color: #04385d;
    margin-bottom: 5px;
}

    .layui-warnin-title i {
        color: #ff511c;
        font-size: 20px;
        padding: 0 3px;
    }

.span-warn {
    color: #ff511c;
    margin-left: 10px;
    font-size: 14px;
}

.adialog-Shops-warn {
    font-size: 18px;
    font-weight: 700;
    color: #ff6161;
    /* border: 1px solid #e2e2e2; */
    padding: 10px;
    background-color: #fff5f5;
    display: none;
    margin: 15px;
    line-height: 50px;
}

.dot-i {
    color: #ff0000;
    font-size: 14px;
    font-weight: 700;
    margin-right: 8px;
}

/*.adialog-Shops .stockup_table_content thead tr th, .adialog-Shops .stockup_table_content tbody tr td {
    border-right: solid 7px #fff;
    border-top: unset;
    border-bottom: 7px solid #fff;
    background-color: #f8f8f8;
}*/


.adialog-Shops > .stockup_table_content > tbody > tr > td:hover {
    background-color: #f5fbff;
    position: relative;
}

    .adialog-Shops > .stockup_table_content > tbody > tr > td:hover:before {
        display: block;
        position: absolute;
        width: 100%;
        height: 100%;
        content: "";
        border: 1px solid #78c6ff;
        top: 0;
        left: 0;
    }

.aialog-newGreenhandWrap .newGreenhandWrap-close {
    position: absolute;
    right: 15px;
    font-style: normal;
    font-size: 14px;
    cursor: pointer;
    color: #999;
    font-weight: 400;
}

.newVersionsLeftNav.hasTopBanner .index-wrap {
    padding-top: 0px !important;
}

.newVersionsLeftNav .aialog-newGreenhandWrap {
    left: 115px;
}

.newContactWrap {
    display: none !important;
}

.fjWarn {
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box
}

    .fjWarn .fjWarn-content {
        background-color: #fff;
        width: 100%;
        height: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
.newIndexWrap {
    padding:15px;
    box-sizing:border-box;
    min-width:1250px;
}
.newIndexWrap .newIndexWrap-up {
    display:flex;
    flex-direction:row;
}
.newIndexWrap .newIndexWrap-up .newIndexWrap-up-left {
    flex: 3;
    border-radius: 2px;
    display:flex;
    flex-direction:column;
}
    .newIndexWrap .newIndexWrap-up .newIndexWrap-up-right {
        display: flex;
        flex-direction: column;
        flex: 1;
        margin-left: 15px;
        border-radius: 2px;
        min-width: 350px;
        /* height: 745px; */
    }
.newPicFull {
    width:100%;
    padding:15px;
    background-color:#fff;
    box-sizing:border-box;
    height:322px;
}
.newPicFull .slider-main-img img {
    width:100%;
}
.newPicFull .slider {
    width: 100%;
}
.newIndexWrap .guidanceWrap {
    display:flex;
    flex-direction:row;
    margin-top:15px; 
    /* height:195px; */
    height: 220px;
    flex:1;
}
    .newIndexWrap .guidanceWrap .guidanceWrap-left {
        flex: 5;
        background-color: #fff;
        box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%);
        height: 100%;
        margin-right: 15px;
        display: flex;
        flex-direction: column;
        min-width: 700px;
        /* height: 220px; */
        
    }
    .newIndexWrap .guidanceWrap .guidanceWrap-right {
        flex: 2;
        background-color: #fff;
        box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%);
        height: 100%;
        /* height: 220px; */
        min-width: 260px;
        display:flex;
        flex-direction:column;

    }
.newIndexWrap-useInfo {
    display: flex;
    flex-direction: row;
    padding: 30px;
    box-sizing: border-box;
    /* background: linear-gradient(26deg,#fbf9e4 30%,#97d4ff ); */
    background: linear-gradient(180deg, #D2E9FF -36%, #FFFFFF 100%);
    /* width: 320px; */
    /* height: 221px; */
    /* border-radius: 2px; */
}
.newIndexWrap-article {
    display:flex;
    flex-direction:column;
    /* width: 320px; */
    /* height: 184px; */
}
.newIndexWrap-article .newIndexWrap-article-title {
    display:flex;
    padding:15px 15px 10px 15px;
    justify-content:space-between;
    /* height:45px; */
    height: 56px;
    align-items:center;
    box-sizing:border-box;
}
.newIndexWrap-article .newIndexWrap-article-title span {
    /* font-size: 14px;
    color: #192a45; */
}
.newIndexWrap-article .newIndexWrap-article-title a {
    font-size: 14px;
    /* color: #888; */
    cursor:pointer;
}
.newIndexWrap-article-main {
    display: flex;
    flex: 3;
    flex-direction: column;
    height: 160px;
    justify-content:space-between;
    padding-bottom: 12px;
}
.newIndexWrap-article-main > li {
    /* height:48px; */
    /* border-radius:6px; */
    min-height: 38px;
}
.newIndexWrap-article-main > li:hover {
    background: rgba(0, 0, 0, 0.04);
}
.newIndexWrap-notice {
    flex: 1;
    margin-top: 15px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%);
    background-color:#fff;
    /* height:325px; */
    /* width: 320px; */
    height:322px;
    box-sizing:border-box;
    overflow:auto;
}
.newIndexWrap-notice .newIndexWrap-title {
    border-bottom: none;
}
.newIndexWrap-mid {
    margin-top: 15px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%);
    background-color: #fff;
    height:320px;
    display:flex;
    flex-direction:column;
}
.newIndexWrap-footer {
    margin-top: 15px;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%);
    background-color: #fff;
    /* min-height: 220px; */
    display:flex;
    flex-direction:column;
    height:auto;
}
#carouselIndex .layui-icon {
    font-family: 微软雅黑 !important;
    font-size: 10px!important;
}

#carouselIndex .layui-carousel-arrow::before {
    content: "";
    display: block;
    position: absolute;
    top: 6px;
    left: 10px;
    width: 0;
    height: 0;
    border-top: 12px solid transparent;
    border-right: 12px solid #fff;
    border-bottom: 12px solid transparent;
}
#carouselIndex .layui-carousel-arrow[lay-type=add]::before {
    transform: rotate(180deg);
    left: 14px;
}
#carouselIndex .slider-new-img {
    border:1px solid #e2e2e2;
    box-sizing:border-box;
}
#carouselIndex .slider-new-img, #carouselIndex .slider-new-img a, #carouselIndex .slider-new-img a img {
    width: 100%;
    height: 100%;
}
.newIndexWrap-useInfo {
    display:flex;
    flex-direction:column;
    justify-content:center;
}
.newIndexWrap-useInfo .newIndexWrap-useInfo-up {
    display: flex;
    flex-direction: row;
}
.newIndexWrap-useInfo .newIndexWrap-useInfo-up .icon-yonghu {
    padding:15px;
    /* font-size:50px; */
    font-size:56px;
    /* color:#3aadff; */
    color: rgba(8, 136, 255, 0.3);
}
.newIndexWrap-useInfo-up-right .newIndexWrap-useInfo-name {
    font-size: 17px;
    color: #333;
    font-weight: 700;
    /* margin-bottom: 10px; */
    margin-bottom: 12px;
    margin-top: 6px;
    display: flex;
    align-items: center;
    line-height: 24px;
}
.newIndexWrap-useInfo-up-right .newIndexWrap-useInfo-name>i {
    font-weight: normal;
}
    .newIndexWrap-useInfo-up-right .newIndexWrap-useInfo-name .icon-fuzhi1 {
        font-size:14px;
        color:#888;
        cursor:pointer;
        margin-left:5px;
        font-weight:400;
    }
.newIndexWrap-useInfo-up-right .newIndexWrap-useInfo-content {
    display:flex;
    flex-direction:column;
    color:#888;
    /* line-height:28px; */
    line-height:16px;
}
.newIndexWrap-useInfo-down {
    display:flex;
    justify-content:center;
    align-items:center;
    margin-top:10px;
    flex-direction:row;
    position:relative;
    z-index:10;
}
.newIndexWrap-useInfo-down .newIndexWrap-useInfo-content {
    display: flex;
    flex-direction:row;
    align-items:center;
    justify-content:center;
}
.newIndexWrap-useInfo-down .newIndexWrap-useInfo-content .sColor {
    margin-left:5px;
}
.newIndexWrap-useInfo-down .newIndexWrap-useInfo-down-select {
    border: 1px solid #ffc5c5;
    padding: 6px 5px;
    box-sizing: border-box;
    /* border-radius: 2px; */
    color: #f56c6c;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    min-width: 330px;
}
.newIndexWrap-useInfo-down .newIndexWrap-useInfo-down-select #AccessExpireShopCount {
    width: 17px;
    height: 17px;
    display: flex;
    align-items: center;
    background-color: #ff0000;
    border-radius: 50%;
    font-size: 12px;
    margin-right: 3px;
    justify-content: center;
    opacity: 0.8;
    margin: 0 5px;
    color: #fff;
    line-height: 15px;
}
.newIndexWrap-useInfo-down .newIndexWrap-useInfo-down-select .icon-down {
    font-size:12px;
    margin-left:5px;
    color:#999;
}
.newIndexWrap-article .newIndexWrap-article-main-item {
    display: flex;
    align-items: center;
    /* padding: 0 12px; */
    box-sizing: border-box;
    color: #566a7d;
    cursor: pointer;
    /* border-radius: 6px; */
}

.newIndexWrap-article .newIndexWrap-article-main-item .iconfont {
    display: flex;
    width: 35px;
    height: 35px;
    /* margin-right: 10px; */
    border-radius: 8px;
    /* background-color: #e5f8f9;
    color: #50c9da; */
    font-size:18px;
    justify-content:center;
    align-items:center;
}
.newIndexWrap-article .newIndexWrap-article-main-item .icon-jiaocheng1 {
    color: #bd66fa;
    background-color: #f6e6fe;
    font-size: 19px;
}
.newIndexWrap-article .newIndexWrap-article-main-item .icon-zhinan1 {
    color: #e3bc00;
    background-color: #fef9df;
    font-size: 20px;
}



/* animation domination */

.three-d {
    perspective: 200px;
    transition: all .07s linear;
    position: relative;
    cursor: pointer;
    text-transform: uppercase;
    overflow: visible;
}
    .three-d:hover {
        background-color:transparent;
    }
    /* complete the animation! */
    .three-d:hover .three-d-box,
    .three-d:focus .three-d-box {
        transform: translateZ(-25px) rotateX(90deg);
    }

.three-d-box {
    transition: all .3s ease-out;
    transform: translatez(-25px);
    transform-style: preserve-3d;
    pointer-events: none;
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 6px;
}

/* 
	put the "front" and "back" elements into place with CSS transforms, 
	specifically translation and translatez
*/
.front {
    transform: rotatex(0deg) translatez(25px);
}

.back {
    transform: rotatex(-90deg) translatez(25px);
    color: #ffe7c4;
}

.front, .back {
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: #fff;
    padding: 15px 10px;
    color: white;
    pointer-events: none;
    box-sizing: border-box;
}
.newIndexWrap-title {
    padding:12px 0;
    /* border-bottom:1px dotted #ddd; */
    border-bottom:1px solid rgba(0, 0, 0, 0.09);
    margin:0 15px;
    display:flex;
    flex-direction:row;
    align-items:center;
    position:relative;
}
/* .newIndexWrap-title .newIndexWrap-title-fText {
    font-size: 14px;
    color: #385067;
} */
.newIndexWrap-title .iconfont {
    font-size: 18px;
    display:inline-block;
    margin-right:3px;
}
/* .newIndexWrap-title .newIndexWrap-title-dText {
    margin-left:5px;
    font-size:12px;
    color:#3aadff;
    cursor:pointer;
} */
.helpSteps {
    display:flex;
    flex-direction:row;
    justify-content:space-around;
    flex:1;
    align-items:center;

}
.helpSteps .helpSteps-item {
    display:flex;
    flex-direction:column;
    align-items:center;
    flex:1;
}
.rightborder {
    border-right: 1px solid #e2e2e2;
}
.helpSteps .helpSteps-item .helpSteps-item-title {
    color: #f7941f;
    font-size: 14px;
    margin-bottom: 15px;
}
.helpSteps .helpSteps-item .helpSteps-item-title02 {
    /* color: #888;
    font-size: 14px; */
    margin-bottom: 15px;
    /* font-family: 'Source Han Sans'; */
}
.helpSteps .helpSteps-item .helpSteps-item-title03 {
    /* color: #333;
    font-size: 16px;
    font-weight:700; */
    /* font-family: 'barlow'; */
}

.helpSteps .helpSteps-item .helpSteps-item-title03:hover {
    color: #0888FF;
}
.copy-account-icon {
    color: rgba(0,0,0,0.4);
    cursor: pointer;
}
.helpSteps .helpSteps-item .layui-btn {
    width:150px;
    cursor:pointer;
}
.wait-confirm-bill {
    height: 72px;
}
.newIndexWrap-notice-content {
    padding:15px;
    box-sizing:border-box;
    line-height:28px;
}

@keyframes living {

    0% {
        -webkit-transform: scaleY(1);
        -ms-transform: scaleY(1);
        transform: scaleY(1);
        -webkit-transform-origin: 0 40px;
        -ms-transform-origin: 0 40px;
        transform-origin: 0 40px
    }

    50% {
        -webkit-transform: scaleY(.3);
        -ms-transform: scaleY(.3);
        transform: scaleY(.3);
        -webkit-transform-origin: 0 40px;
        -ms-transform-origin: 0 40px;
        transform-origin: 0 40px
    }

    100% {
        -webkit-transform: scaleY(1);
        -ms-transform: scaleY(1);
        transform: scaleY(1);
        -webkit-transform-origin: 0 40px;
        -ms-transform-origin: 0 40px;
        transform-origin: 0 40px
    }
}
.lvdonanWrap {
    width: 22px;
    height: 15px;
    border-left: 2px solid #f59c1b;
    border-bottom: 2px solid #f59c1b;
    display:flex;
    justify-content:space-around;
    padding:0 1px;
    box-sizing:border-box;
    margin-right:5px;
    border-bottom-left-radius:2px;
}
.lvdonanWrap > i {
    width: 3px;
    height: 12px;
    background-color: #f59c1b;
    display:block;
}
.lvdonanWrap > i.m1 {
    animation: 1s .1s living linear infinite backwards normal;
}
.lvdonanWrap > i.m2 {
    animation: 1s .3s living linear infinite backwards normal;
}
.lvdonanWrap > i.m3 {
    animation: 1s .6s living linear infinite backwards normal;
}
@keyframes living {
    0% {
        -webkit-transform: scaleY(1);
        -ms-transform: scaleY(1);
        transform: scaleY(1);
        -webkit-transform-origin: 0 14px;
        -ms-transform-origin: 0 14px;
        transform-origin: 0 14px;
    }

    50% {
        -webkit-transform: scaleY(.3);
        -ms-transform: scaleY(.3);
        transform: scaleY(.3);
        -webkit-transform-origin: 0 14px;
        -ms-transform-origin: 0 14px;
        transform-origin: 0 14px;
    }

    100% {
        -webkit-transform: scaleY(1);
        -ms-transform: scaleY(1);
        transform: scaleY(1);
        -webkit-transform-origin: 0 14px;
        -ms-transform-origin: 0 14px;
        transform-origin: 0 14px;
    }
}
.newIndexWrap-persion {
    display:flex;
    flex-direction:row;
    justify-content:space-around;
    flex:1;
    align-items:center;
}
.newIndexWrap-persion .newIndexWrap-persion-item {
    display: flex;
    flex-direction: column;
    flex: 1;
    padding: 0 60px;
}

.newIndexWrap-persion .newIndexWrap-persion-item .newIndexWrap-persion-title {
    display:flex;
    flex-direction:row;
    align-items:center;
    margin-bottom:25px;

}
.newIndexWrap-persion-title .persion-icon {
   width:20px;
   height:20px;
   background-image:url(/Content/images/svg/perIcon.png);
   background-position:0 0;
   display:inline-block;
   margin-right:3px;
}
.newIndexWrap-persion-title .persion-title {
    color: #5ccddd;
    font-size: 14px;
}
.newIndexWrap-persion-content {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    border-right: 1px solid #e2e2e2;
}
.newIndexWrap-persion-content .persion-content-item {
    width:50%;
    display:flex;
    flex-direction:column;
    margin-bottom:20px;
}
.newIndexWrap-persion-content .persion-content-item > span:nth-child(1) {
    color:#666;
    margin-bottom:8px;
}
.newIndexWrap-persion-content .persion-content-item > span:nth-child(2) {
    color: #333;
    font-size:17px;
    font-weight:700;
}
.newIndexWrap-persion .newIndexWrap-persion-item:last-child .newIndexWrap-persion-content {
    border: unset;
}
.newIndexWrap-footer-content {
    flex: 1;
    display: flex;
    align-items: center;
    /* justify-content: flex-start; */
    justify-content: space-evenly;
    max-width: 100%;
    flex-wrap:wrap;
}
    .newIndexWrap-footer-content .newIndexWrap-footer-content-item {
        display: none;
        padding: 15px 70px;
        max-width: 100%;
    }
.newIndexWrap-footer-content a {
    display: flex;
    flex-direction: column;
    align-items: center;
}
.newIndexWrap-footer-content a .iconfont {
    /* width: 70px;
    height: 70px; */
    width: 60px;
    height: 60px;
    background-color: #3aadff;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 45px;
    border-radius: 8px;
    margin-bottom: 10px;
    position:relative;
}
.puhuo-ad-back {
    position: absolute;
    top: 0;
    left: -100%;
    width: 50px;
    height: 100%;
    transform: skewX(-20deg);
    background-image: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5), transparent);
    pointer-events: none;
}
.newIndexWrap-footer-content a:hover .puhuo-ad-back {
    animation: shine 1s ease-in-out 0s 1 alternate forwards;
}
@keyframes shine {
    100% {
        left: 100%;
    }
}


.newIndexWrap-footer-content a .newIndexWrap-footer-title {
    color:#333;
}
.newIndexWrap-sync {
    display:flex;
    flex-direction:row;
    margin-left:10px;
    align-items:center;
}
.newIndexWrap-sync .dColor {
    margin-left:3px;
}
.newinputSelectTime {
    margin-left:5px;
}
.syncActive {
    margin-left: 15px;
    border:1px solid #e2e2e2;
    padding:0 5px;
    border-radius:4px;
    width:232px;
    height:32px;
    box-sizing:border-box;
    display:flex;
    flex-direction:row;
    align-items:center;
}
.syncActive .newinputSelectTime {
    border: unset;
    margin-left: 0;
}
.syncActive .newinputSelectTime .newwrapperTime_input {

}
.newIndexWrap-sync-btn {
    cursor: pointer;
    min-width: 50px;
    position:relative;
    top:-1px;
    margin-left:5px;
}
.setlayui-btn {
    /* height: 25px; */
    line-height: 25px;
    /* font-size: 12px; */
    padding: 0 10px;
    /* background-color: #f7941f; */
    /* position: absolute; */
    right: 0;
    color: #fff;
    cursor: pointer;
    border: 1px solid #3aadff;
    /* border-radius: 3px; */
    color: #3aadff;
}
.setlayui-btn:hover {
    color: #3aadff;
    opacity: 0.8;
}

.newhelpSteps {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 15px;
    box-sizing: border-box;
    align-items: center;
}
.newhelpSteps .newhelpSteps-item {
    display:flex;
    flex-direction:column;
    align-items:center;
    position:relative;
}
.newhelpSteps .newhelpSteps-item .newhelpSteps-item-up {
    display:flex;
    align-items:center;
}
    .newhelpSteps .newhelpSteps-item .newhelpSteps-item-title {
        font-size: 14px;
        color: #d1d5da;
        margin-right:5px;
    }
.newhelpSteps .newhelpSteps-item .newhelpSteps-item-img {
    display: inline-block;
    width: 38px;
    height: 38px;
    background-image: url(/Content/images/generalizeIndex-icons.png);
    background-position: 0 0;
}
.newhelpSteps .newhelpSteps-item .newhelpSteps-item-mid-btn {
    /* min-width:105px; */
    box-sizing:border-box;
    /* height:25px; */
    /* background-color:#3aadff; */
    /* cursor:pointer; */
    /* color:#fff; */
    display:flex;
    justify-content:center;
    align-items:center;
    /* padding:0 8px; */
    margin:8px 0;

    /* background-color:#fff;
    border:1px solid #3aadff;
    border-radius:3px;
    color:#3aadff;
    height:26px; */
    min-width:80px;


}
.newhelpSteps .newhelpSteps-item .newhelpSteps-item-mid-btn:hover {
    opacity:0.8;
}
.newhelpSteps .newhelpSteps-item .newhelpSteps-item-down {
    display: flex;
    flex-direction: column;
    /* color: #888; */
}
.newhelpSteps .newhelpSteps-active {
    width: 120px;
    height: 17px;
    background-image: url(/Content/images/generalizeIndex-icons.png);
    background-position: 0px -38px;
}
.newhelpSteps.wu-steps .wu-step-inProgress{  
        width: unset;
        height: unset;   
}

.newhelpSteps.wu-steps .wu-step-inProgress .go-to-operate {
    white-space: nowrap;
}
.wu-steps .wu-step-inProgress {
    flex: 1;
}
.wu-steps .wu-step-inProgress .step-right .step-title::after {
    /* flex: 1; */
    display: none;
}
.wu-steps .wu-last-step .step-right .step-title::after {
    display: none;
}
.wu-steps .wu-step-inProgress .step-right .step-content .text-height {
    width: 166px;
    height: 50px;
}
.productHelpDailog {
    padding:25px;
    width:1000px;
    background-color:#fff;
    display:none;
}
.productHelpDailog-title {
    display:flex;
    flex-direction:column;
    line-height:30px;
    font-size:18px;
    color:#888;
    padding:15px;
}
    .productHelpDailog-title .fColor {
        font-weight:700;
        margin-left:10px;
    }
    .productHelpDailog-content {
        width: 1000px;
        height: 468px;
        display: inline-block;
    }
.productHelpDailog-down {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 25px;
    padding-bottom:0;
}
.productHelpDailog-down .productHelpDailog-btn {
    display: inline-block;
    height: 38px;
    line-height: 38px;
    padding: 0 25px;
    background-color: #009688;
    color: #fff;
    white-space: nowrap;
    text-align: center;
    font-size: 16px;
    border: none;
    /* border-radius: 2px; */
    cursor: pointer;
    background-color: #f59c1a;  
}
.newhelpSteps .newhelpSteps-item .newhelpSteps-item-down a {
    /* color:#888; */
}
.bindCooperationWarnWrap {
    /* width:380px; */
    height:150px;
    display:none;
}
.bindCooperationWarnWrap .bindCooperationWarnWrap-content {
    width:100%;
    height:100%;
    display:flex;
    align-items:center;
    justify-content:space-around;
}
.bindCooperationWarnWrap .layui-btn {
    /* color: #409eff; */
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
}
.bindCooperationWarnWrap .yiBtn {
    /* color: #666; */
    background: #f5f5f5;
    border: 1px solid #e2e2e2;
}


@media screen and (max-width: 1440px) {
    .newIndexWrap-title .newIndexWrap-title-dText {
        display:none;
    }
}

.helpVedioWrap {
    /* width: 420px; */
    height: 180px;
    padding:25px 15px;
    box-sizing:border-box;
    display:none;
}
.helpVedioWrap .helpVedioWrap-title {
    padding:15px 0;
    display:flex;
    justify-content:center;
    font-size:17px;
    /* color:#888; */
    font-weight:700;
}
.helpVedioWrap .helpVedioWrap-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-top:30px;
}
.helpVedioWrap .helpVedioWrap-content .layui-btn {
    width: 165px;
    color: #409eff;
    background: #ecf5ff;
    border: 1px solid #b3d8ff;
}
    .helpVedioWrap .helpVedioWrap-content .layui-btn.active {
        color: #f29a1a;
        background: #ecf5ff;
        border: 1px solid #b3d8ff;
    }
.helpVedioWrap#setAccountDailog {
    height: 200px;
}
.helpVedioWrap#setAccountDailog .setAccountDailog-title {
    display: flex;
    flex-direction: column;
    line-height: 25px;
    font-size: 16px;
    /* color: #888; */
    font-weight:400;
}
.helpVedioWrap#setAccountDailog {
    width:450px;
}
.helpVedioWrap#setAccountDailog .helpVedioWrap-content {
    margin-top:20px;
}
.helpVedioWrap#setAccountDailog .helpVedioWrap-content .layui-btn {
    color: #409eff;
    background: #ecf5ff;
    border:1px solid #b3d8ff;
}
.inviteMerchantsCopyWrap {
    width:450px;
    padding:25px;
    box-sizing:border-box;
}
.inviteMerchantsCopyWrap-title {
    display:flex;
    flex-direction:column;
    line-height:25px;
    font-size:16px;
    /* color:#888; */
    justify-content:center;
    align-items:center;
}
.inviteMerchantsCopyWrap-btns {
    margin-top:30px;
    display:flex;
    justify-content:center;
}
.inviteQrcodeDailog {
    width:550px;
    padding:25px;
    box-sizing:border-box;
    display:none;

}
.inviteQrcodeDailog .inviteQrcodeDailog-up {
    display: block;
    height: 32px;
    padding: 0 10px;
    line-height: 30px;
    font-size: 12px;
    /* color: #409eff; */
    border: 1px solid #d9ecff;
    border-radius: 4px;
    box-sizing: border-box;
    white-space: nowrap;
    background-color: #fdf6ec;
    border-color: #faecd8;
    /* color: #e6a23c; */
    margin-top:10px;

}
.inviteQrcodeDailog .inviteQrcodeDailog-mid {
    display:flex;
    flex-direction:column;
    width:70%;
    margin:25px auto;
}
.inviteQrcodeDailog .inviteQrcodeDailog-mid .inviteQrcodeDailog-mid-title {
    display:flex;
    flex-direction:column;
}
.inviteQrcodeDailog .inviteQrcodeDailog-mid .inviteQrcodeDailog-mid-img {
    margin:10px 0;
    display:flex;
    flex-direction:row;
    align-items:center;
}
.inviteQrcodeDailog .inviteQrcodeDailog-mid .inviteQrcodeDailog-mid-img .mid-img-right {
    margin-left:10px;
    color:#3aadff;
}
.inviteQrcodeDailog .inviteQrcodeDailog-mid .inviteQrcodeDailog-mid-img .mid-img-right .btn {
    padding:5px;
    background-color:#3aadff;
    color:#fff;
    cursor:pointer;
}
.inviteQrcodeDailog .inviteQrcodeDailog-mid .inviteQrcodeDailog-mid-img img {
    width: 180px;
    height: 180px;
    border: 1px solid #e2e2e2;
}
.inviteQrcodeDailog .inviteQrcodeDailog-mid .inviteQrcodeDailog-mid-warn {
    display:flex;
    flex-direction:column;
}
.newIndexWrap-useInfo-content {
    position:relative;
}
.newIndexWrap-useInfo-selectWrap {
    position: absolute;
    top: 40px;
    width: 410px;
    background-color: #fff;
    box-shadow: 0 0 8px 0 #cbcbcb;
    /* border-radius: 4px; */
    right: 0;
    box-sizing: border-box;
    padding: 15px;
    flex-direction: column;
    display: none;
    opacity: 0;
}
.newIndexWrap-useInfo-down:hover .newIndexWrap-useInfo-selectWrap {
    display: flex;
    top: 30px;
    opacity: 1;
    animation: anShopPastWrap_main 0.2s ease-in-out 0s 1 alternate forwards;
}
.newIndexWrap-useInfo-selectWrap::after {
    position: absolute;
    display: block;
    content: "";
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #fff;
    top: -7px;
    right: 20px;
    z-index:1000;
}
.newIndexWrap-useInfo-down-select-main {
    display:flex;
    flex-wrap:nowrap;
}
.newIndexWrap-useInfo-selectWrap .shopPastWrapShow .layui-form {
    display: flex;
    align-items: center;
}
.newIndexWrap-useInfo-selectWrap  .shopPastWrap-main {
    max-height: 250px;
    overflow: auto;
    padding:15px;
}
.newIndexWrap-useInfo-selectWrap .shopPastWrap-main .shopPastWrap-main-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    font-size: 12px;
    border-bottom: 1px dotted #e2e2e2;
    cursor: auto;
    line-height:50px;
}
.newIndexWrap-useInfo-selectWrap .shopPastWrap-main .shopPastWrap-main-item .shopPastWrap-main-item-left {
    display: flex;
    flex-direction: row;
    width: 170px;
    align-items: center;
}
.newIndexWrap-useInfo-selectWrap .shopPastWrap-main .shopPastWrap-main-item .shopPastWrap-main-item-right {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.newIndexWrap-useInfo-selectWrap .shopPastWrap-main .shopPastWrap-main-item .shopPastWrap-main-item-left > .shopName {
    color: #565960;
    font-size: 12px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left:3px;
}
.newIndexWrap-useInfo-selectWrap .pintaiIcon {
    top: 0;
}
.newIndexWrap-useInfo-down {
    cursor:pointer;
}
.newIndexWrap-useInfo-down .adialog-Shops > .adialog-Shops-title {
    font-size: 14px;
    color: #000;
}

.newIndexWrap-useInfo-down .warn-tar .warn-tar-title {
    width: 200px;
    display: inline-block;
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    box-shadow: 0 0 15px 3px #e2e2e2;
    line-height: 18px;
    background-color: #fff;
    padding: 10px;
    color: #04385d;
    display: none;
    z-index: 10000;
    position: absolute;
    top: -58px;
    left: -5px;
    font-weight: 400;
    font-size: 12px;
    color: #888;
}

.newIndexWrap-useInfo-down .warn-tar:hover .warn-tar-title {
    display: block;
}

.shopPastWrap-main-item-right .status {
    margin-right:5px;
}
.moreHelp {
    display: flex;
    flex-direction: row;
    /* font-size: 12px !important; */
    font-size: 14px !important;
    /* color: #3aadff !important; */
}
.moreHelp .icon-zhankai1 {
    display:inline-block;
    transform:rotate(-90deg);
    font-size:12px;
    margin-left:3px;

}
.adialog-addSupplier {
    /* width: 600px; */
    width: 435px;
    background-color: #fff;
    display: none;
}

    .adialog-addSupplier ul {
        padding: 25px;
    }

        .adialog-addSupplier ul > li {
            padding: 8px 15px;
            display: flex;
            align-items: center;
        }

            .adialog-addSupplier ul > li#virtual_li {
                justify-content: flex-start;
                align-items: flex-start;
                border: 1px solid #e2e2e2;
                margin-bottom: 5px;
                background-color: #fefce8;
                border: 1px solid #f5a623;
            }


                .adialog-addSupplier ul > li#virtual_li > div:nth-child(1) a {
                    color: #3aadff;
                    margin-left: 15px;
                }

            .adialog-addSupplier ul > li > label {
                display: flex;
                align-items: center;
                flex-grow: 1;
            }

                .adialog-addSupplier ul > li > label > span {
                    /* width: 110px; */
                    width: 80px;
                    text-align: right;
                    display: inline-block;
                    /* color: #04385d; */
                }

    .adialog-addSupplier > ul > li > label select,
    .adialog-addSupplier > ul > li > label input[type=text] {
        width: 108px;
        border: 1px solid #e2e2e2;
        box-sizing: border-box;
        height: 35px;
        flex-grow: 1;
    }

.adialog-addSupplier-textarea {
    margin-top: 15px;
}

    .adialog-addSupplier-textarea > textarea {
        width: 345px;
        height: 40px;
        border: 1px solid #e2e2e2;
        border-radius: 2px;
        padding-left: 5px;
    }

.adialog-addSupplier > ul > .adialog-addSupplier-select {
    display: inline-block;
    position: relative;
    left: 110px;
}

.adialog-addSupplier ul > li.adialog-addSupplier-select > label {
    display: inline-block;
    margin-right: 10px;
}




.adialog-addDistributor {
    /* width: 400px; */
    width: 435px;
    height: 190px;
    background-color: #fff;
    padding: 20px 15px 0 15px;
    box-sizing: border-box;
    display: none;
}

    .adialog-addDistributor > .addDistributor-title {
        padding: 5px 0 20px 0;
        font-size: 14px;
        color: #000;
    }

    .adialog-addDistributor > .addDistributor-content {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }

        .adialog-addDistributor > .addDistributor-content > input {
            width: 280px;
        }

    .adialog-addDistributor > .addDistributor-warn {
        /*padding: 15px 0 0 72px;*/
        margin-left: 15px;
    }

.mysearch-partOne {
    margin-bottom: 0;
}

.invitesupplier .icon-jia-copy1 {
    position: absolute;
    top: 5px;
    right: 10px;
    display: inline-block;
    transform: rotate(45deg);
    font-size: 20px;
    cursor: pointer;
}

.adialog-addDistributor ul {
    padding: 10px;
    width: 100%;
}

    .adialog-addDistributor ul > li {
        padding: 8px 15px;
        display: flex;
        align-items: center;
    }

        .adialog-addDistributor ul > li > label {
            display: flex;
            align-items: center;
            width: 100%;
        }

            .adialog-addDistributor ul > li > label > span {
                /* width: 110px; */
                min-width: 110px;
                text-align: right;
                display: inline-block;
            }

.adialog-addDistributor > ul > li > label input[type=text] {
    width: 108px;
    border: 1px solid #e2e2e2;
    box-sizing: border-box;
    height: 35px;
}

.pointer {
    cursor: pointer
}
.edmitDemandDailogSkin {
    width:420px!important;
}
.newIndexWrap-mid-helps {
    display:flex;
    flex-direction:row;
    padding:15px;
    justify-content:space-around;
    flex-wrap:wrap;
    padding-bottom:0;
}
    .newIndexWrap-mid-helps .newIndexWrap-mid-helps-li {
        display: flex;
        flex-direction: row;
        justify-content:center;
        /* margin-bottom:15px; */
    }
    .newIndexWrap-mid-helps .newIndexWrap-mid-helps-li a {
        /* border: 1px solid #e2e2e2; */
        /* margin-right: 15px;
        border-radius: 2px; */
        color: #566a7d;
        display: flex;
        align-items: center;
        cursor: pointer;
        justify-content: center;
        /* padding:8px 0; */
        /* width:180px; */
        box-sizing:border-box;
    }
    .newIndexWrap-mid-helps .newIndexWrap-mid-helps-li a:hover {
        color: #3aadff;
    }
    .newIndexWrap-mid-helps .newIndexWrap-mid-helps-li .pintaiIcon {
        margin-right: 4px;
        top: 0;
    }
    .newIndexWrap-mid-helps .newIndexWrap-mid-helps-li .pintaiIcon.duoIcon {
        background-color: #50c9da;
        background-image: unset;
        border-radius: 6px;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 20px;
        height: 20px;
    }


.commonDailog {
    width: 580px;
    padding: 20px;
    background-color: #fff;
    box-sizing: border-box;
    min-height: 150px;
    display: none;
}

    .commonDailog .commonDailog-title {
        display: flex;
        position: relative;
    }

        .commonDailog .commonDailog-title .noPrompt {
            position: absolute;
            top: 0;
            right: 0;
            color: #999;
            font-size: 12px;
        }

    .commonDailog .commonDailog-title02 {
        padding: 10px;
        border: 1px solid #ffe3b9;
        color: #f59c1a;
        background-color: #fef7ed;
        font-size: 14px;
        margin-top: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .commonDailog .commonDailog-title03 {
        color: #888888;
        font-size: 18px;
        margin-top: 10px;
    }

    .commonDailog .commonDailog-title .commonDailog-title-log {
        width: 210px;
        height: 52px;
        background-image: url(/Content/Images/fd-2023-5-4-8.png);
        background-position: 0 -50px;
        display: inline-block;
    }

    .commonDailog .commonDailog-main {
        padding: 25px 20px 0 20px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        position: relative;
    }

        .commonDailog .commonDailog-main .commonDailog-main-text {
            font-size: 16px;
            color: #888;
            margin-bottom: 15px;
        }

.commonDailog-main-btnsWrap {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 20px;
}
.commonDailog-main-item {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    color: #888;
    flex:1;
}

.commonDailog-main-item .commonDailog-main-item-img {
    min-width:52px;
    width: 52px;
    height: 50px;
    background-image: url(/Content/Images/fd-2023-5-4-8.png);
    background-position: 0 -150px;
    margin-right: 15px;
}
.commonDailog-main-btnsWrap {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 20px;
}

.commonBtn01 {
    width: 175px;
    height: 40px;
    border-radius: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #3aadff;
    color: #fff;
    cursor: pointer;
    font-size: 14px;
}

    .commonBtn01:hover {
        opacity: 0.8;
    }
    .commonBtn01:hover{
        color:#fff;
    }

.againBindQrCodeDailogskin .layui-layer-setwin .layui-layer-close2 {
    top:-13px;
    right:-13px;
    opacity:0.3;
}

@media screen and (max-width: 1770px) {
    .newIndexWrap-mid-helps {
        justify-content:flex-start;
    }
}
.newIndexWrap-useInfo {
    position:relative;
}
    .newIndexWrap-useInfo .layui-btn-sm.layui-btn-sm-top {
        position: absolute;
        top: 0;
        left: auto;
        height: 24px;
        line-height: 24px;
        border-top-left-radius: 15px;
        border-bottom-right-radius: 15px;
        background: linear-gradient(to right, #3aadff 30%, #3aadff);
        padding: 0 8px;
    }
.newIndexWrap-useInfo .icon-shezhi1 {
    font-size: 14px!important;
    margin-right:3px;
}
.newIndexWrap-useInfo-createUrl-wrap {
    display:flex;
    justify-content:center;
}
    .newIndexWrap-useInfo-createUrl-wrap .iconfont {
        margin-right:3px;
    }

.adialog-Shops-skin.layui-layer-page .layui-layer-content {
    overflow: hidden;
    padding: 0px;
    border-radius: 10px;
    max-height: unset;
}
.adialog-Shops-skin .dailog-shops-title {
    padding:20px 20px 10px 20px;
    color:#999;
    font-size:14px;
}

.adialog-Shops .stockup_table_content thead tr th, .adialog-Shops .stockup_table_content tbody tr td {
    border:unset;
}
.layui-layer-page .layui-layer-content {
    max-height: unset;
    
}

.add-support-platform-dailog>.layui-layer-content {
    padding: 0px;
    border-radius: 10px 10px 0 0 ;
}
