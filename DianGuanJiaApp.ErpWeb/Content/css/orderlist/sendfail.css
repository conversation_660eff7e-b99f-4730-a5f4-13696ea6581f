.layui-mywrap {
    border-right: unset;
    min-height: unset;
    margin-bottom: 70px;
}

.layui-laypage {
    margin-top: 0;
}

.orderList_search_one select, .orderList_search_one input[type=text] {
    height: 32px;
}

.layui-bodys {
    min-width: 1440px;
}

.layui-mysearch {
    width: 1200px;
    display: flex;
}

    .layui-mysearch .layui-form {
        width: 920px;
    }

.mysearch-partOne {
    margin-right: 10px;
    margin-bottom: 15px;
}

.mysearch-partTwo {
    display: flex;
    flex-direction: column;
    padding-left: 15px;
    margin-bottom: 15px;
    border-left: 1px solid #e2e2e2;
    height: 100%;
}

    .mysearch-partTwo button {
        width: 80px;
    }

        .mysearch-partTwo button.layui-btn-normal {
            margin-bottom: 15px;
        }

.layui-btn + .layui-btn {
    margin-left: 0;
}

.layui-mynav {
    display: flex;
    margin-bottom: 5px;
}

    .layui-mynav > li {
        margin-right: 45px;
        height: 25px;
    }

        .layui-mynav > li.active {
            color: #3aadff;
            position: relative;
        }

        .layui-mynav > li a {
            color: #666;
        }

        .layui-mynav > li.active a {
            color: #3aadff;
        }

        .layui-mynav > li.active::after {
            width: 100%;
            position: absolute;
            height: 2px;
            background-color: #3aadff;
            content: "";
            left: 0;
            bottom: 0;
            display: block;
        }

        .layui-mynav > li a:hover {
            color: #3aadff;
        }

.layui-mytable {
    margin-bottom: 0;
}

.layui-myPage {
    border-top: 1px solid #e5e9f2;
}

.layui-mytable .layui-mytable-header {
    /* border-top: 1px solid #e5e9f2; */
    height: 40px;
    display: flex;
    align-items: center;
    /* background-color: #f6f9fd; */
    background: rgba(0, 0, 0, 0.05);
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.9);
}

    .layui-mytable .layui-mytable-header > div {
        text-align: center;
        /* color: #04385d; */
        color: rgba(0, 0, 0, 0.9);
        position: relative;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
    }

.layui-mytable .allcheckorder, .layui-mytable .order-chx {
    position: absolute;
    left: 15px;
}
.layui-mytable .order-chx {
    top: 0px;
}
.expressTemplate {
    display: flex;
    padding: 15px;
    min-height: unset;
    align-items: center;
}

    .expressTemplate .orderList_expressTemplate {
        display: flex;
        align-items: flex-start;
    }

    .expressTemplate .expressTemplate-title {
        font-size: 14px;
        color: #04385d;
    }

.expressTemplate-content {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
}

    .expressTemplate-content > li {
        display: flex;
        align-items: center;
        margin-right: 15px;
        border-bottom: 2px;
    }

.expressTemplate-content-operate {
    position: relative;
    top: 1px;
}

    .expressTemplate-content-operate > span {
        margin-right: 5px;
        color: #57beea;
        font-weight: 700;
        font-size: 14px;
        cursor: pointer;
    }

.expressTemplate-content > li > label {
    cursor: pointer;
    display: flex;
    align-items: center;
}

    .expressTemplate-content > li > label > input[type=radio] {
        margin-right: 3px;
    }

.layui-mytable .layui-mytable-tr {
    padding: 15px 0;
    /* color: #666; */
    color: rgba(0, 0, 0, 0.9);
    display: flex;
    /* align-items: center; */
    align-items: flex-start;
}

    .layui-mytable .layui-mytable-tr.layui-row-item {
        border-top: 1px solid #e5e9f2;
    }

        .layui-mytable .layui-mytable-tr.layui-row-item.active {
            background-color: #f4fcf4;
            color: #04385d;
        }

    .layui-mytable .layui-mytable-tr > div {
        display: flex;
        align-items: center;
    }

.layui-mytable .layui-mytable-tr-showItems {
    display: none;
}

.layui-mytable .layui-mytable-tr.layui-mytable-tr-show {
    background-color: #f8f8f8;
    align-items: center;
    border-bottom: 1px solid #fff;
}

.layui-mytable-tr-showItems .layui-mytable-tr.layui-mytable-tr-show:last-child {
    border-bottom: none;
}

.layui-mytable .layui-mytable-tr .mytable-tr-oparete {
    display: flex;
    align-items: center;
    padding-left: 35px;
}

.layui-mytable .layui-mytable-tr .mytable-tr-oparete02 {
    /* justify-content: center; */
    justify-content: flex-start;
}

    .layui-mytable .layui-mytable-tr .mytable-tr-oparete02 .icon-zhankai1 {
        /* color: #3aadff; */
        /* color: #0888ff; */
        font-size: 12px;
        cursor: pointer;
        display: inline-block;
    }

        .layui-mytable .layui-mytable-tr .mytable-tr-oparete02 .icon-zhankai1.zk {
            transform: rotate(180deg);
        }

.layui-mytable .layui-mytable-tr .mytable-tr-time {
    display: flex;
    flex-direction: column;
}

.layui-mytable .layui-mytable-tr > div.typeOne {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: flex-start;
}

.layui-mytable .layui-mytable-tr-show-lists {
    background-color: #fff9f0;
    display: flex;
    padding: 3px 5px;
    width: 100%;
    border: 1px solid #f5f5f5;
    box-sizing: border-box;
}

.layui-mytable .layui-mytable-tr-showItem {
    min-width: 240px;
    display: flex;
    flex-direction: column;
}

    .layui-mytable .layui-mytable-tr-showItem .icon-flag-warning {
        margin-right: 3px;
        position: relative;
        top: 5px;
    }

.layui-mytable-tr-show-listsNum > li {
    padding: 1px 0;
    word-break: break-all;
}

.layui-mytable .layui-mytable-tr-show-remark {
    border: 1px solid #ffe1af;
    padding: 5px 10px;
    border-radius: 2px;
    background-color: #fffefc;
    margin-top: 3px;
    width: 100%;
    box-sizing: border-box;
    color: #3aadff;
    word-break: break-all;
}

.layui-mytable-tr-show .productShow {
    display: flex;
    padding: 10px;
}

    .layui-mytable-tr-show .productShow .productShow-itemInput {
        margin-right: 3px;
    }

    .layui-mytable-tr-show .productShow img {
        width: 55px;
        height: 55px;
        margin-right: 3px;
    }

    .layui-mytable-tr-show .productShow > ul {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        word-break: break-all;
        flex: 1;
    }

.productShow > .productShow-imgWrap {
    position: relative;
    width: 55px;
    height: 55px;
    margin-left: 20px;
}

    .productShow > .productShow-imgWrap > .product-status {
        position: absolute;
        bottom: 0;
        left: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        opacity: 0.8;
        filter: alpha(opacity=80);
        background-color: #027cff;
        width: 100%;
        height: 14px;
    }

.layui-mytable-header .showMoreOrHide {
    display: flex;
    align-items: center;
}

.layui-footer .btnMoreOparte .butonsWrap > li {
    padding: 0 10px;
}

@@media (max-width: 1440px) {
    .layui-mysearch .mysearch-partTwo > div {
        width: 100px;
    }
}

.newSplitItemOrder {
    cursor: pointer;
    background-color: #77bf04;
    color: #fff;
    border-radius: 2px;
    padding: 0 2px;
}

.table_content_dagou .heWrap {
    display: flex;
    flex-direction: row;
    position: absolute;
    bottom: -17px;
    width: 150px;
}

.heWrap {
    display: flex;
    flex-direction: row;
}

    .heWrap .combinationOrder-wrap {
        background-color: #f7941f;
        border-radius: 2px;
        color: #fff;
        padding: 0 2px;
        margin-right: 5px;
    }

.hedailogPrintState {
    width: 100px;
    position: relative
}

    .hedailogPrintState > .heWrap .newSplitItemOrder {
        display: none;
    }

.order-column-PrintState {
    min-height: 25px;
}

.product_table_tbody span.ToName {
    display: block;
}

.failOrderWrap-title {
    padding: 2px;
    margin-bottom: 3px;
    color: #04385d;
    font-size: 14px;
    display: flex;
    align-items: center;
    position:relative;
    z-index:1000;
}

.layui-mytable .layui-mytable-tr .mytable-tr-oparete02 {
    /* justify-content: center; */
    justify-content: flex-start;
}

    .layui-mytable .layui-mytable-tr .mytable-tr-oparete02 .icon-zhankai1.zk {
        transform: rotate(180deg);
    }

    .layui-mytable .layui-mytable-tr .mytable-tr-oparete02 .icon-zhankai1 {
        /* color: #3aadff; */
        /* color: #0888ff; */
        font-size: 12px;
        cursor: pointer;
        display: inline-block;
    }

.layui-myPage {
    padding-top: 10px;
}


.sureDailog {
    width: 350px;
    height: 80px;
}

    .sureDailog .sureDailog-content {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 30px 20px 10px 20px;
        box-sizing: border-box;
        font-size: 16px;
    }

    .sureDailog .icon-tips {
        font-size: 32px;
        color: #f7941f;
        margin-right: 15px;
    }

.layui-footer > span, .layui-footer > div span {
    display: inline-block;
    cursor: pointer;
    margin: 0 10px;
    width: 130px;
    /* box-sizing: border-box; */
    /* height: 35px; */
    /* line-height: 35px; */
    /* color: #fff;
    font-size: 14px;
    border-radius: 2px; */
    line-height: 23px;
}

    .layui-footer > span:hover {
        cursor: pointer;
        opacity: 0.8;
    }

.huiBtn {
    background-color: #3aadff;
    width: 50px;
    height: 22px;
    line-height: 22px;
}
.copyPlatformOrderIdWrap {
    display:flex;
    flex-direction:column;
    align-items:flex-start;
}
.copyPlatformOrderIdWrap .icon-fuzhi1{
    cursor:pointer;
    font-size:14px;
    color:#888;   
}
.mySelectWrap:hover .mySelectWrap-options {
    display:block!important;
}
.task-progressWrap #task-progress-cancel {
    width: 30px;
}

.send-fail {
    margin: 16px;
    padding: 16px;
}

.send-fail .exportProgress-btn {
    border-radius: 6px;
    height: 32px;
    line-height: 32px;
}
.send-fail .exportProgress-btn .mySelectWrap-exportText {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
}
.send-fail .exportProgress-btn:hover {
    background-color: #FFF;
    border: 1px solid #0888ff;
    color: #0888ff;
}
.send-fail .exportProgress-btn:hover .mySelectWrap-exportText s {
    color: #0888ff;
}
.send-fail .exportProgress-btn:hover .mySelectWrap-exportText .icon-down {
    transform: rotate(180deg);
    color: #0888ff;
}

.send-fail .exportProgress-btn .mySelectWrap-options {
    padding: 8px;
    border-radius: 6px;
    border: unset;
    box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 8px 16px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
    font-size: 14px;
}
.send-fail .exportProgress-btn .mySelectWrap-options>li {
    border-bottom: unset;
    padding: 6px 8px;
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.6);
    border-radius: 6px;
    line-height: initial;
    margin: 4px 0;
}
.send-fail .exportProgress-btn .mySelectWrap-options>li:hover {
    background-color: rgba(0, 0, 0, 0.04) !important;
    color: rgba(0, 0, 0, 0.9) !important;
}

.send-fail .mySelectWrap:hover {
    background-color: unset;
    color: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(8, 136, 255, 0.8);
}

.send-fail .mySelectWrap:hover .mySelectWrap-options {
    display:block;
    animation: newMySelectWrapAnimation 0.1s ease-in-out 0s 1 alternate forwards;
}

@keyframes newMySelectWrapAnimation {
    0% {
    opacity: 0;
    top: 40px;       
}

    100% {
        opacity: 1;
        top: 34px;
    }
}

.send-fail .failOrderWrap {
    margin-bottom: 22px;
}

