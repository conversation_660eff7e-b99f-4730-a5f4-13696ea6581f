.flex {
  display: flex;
}
.flex_center {
  display: flex;
  align-items: center;
}
.ProductCollectionBox_content {
  margin: 24px 24px 0px 24px;
  min-width: 1393px;
}
.ProductCollectionBox_content h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
}
.ProductCollectionBox_content_box {
  height: 100%;
  width: 100%;
  border-radius: 8px;
  background-color: #fff;
  padding-bottom: 16px;
}
.ProductCollectionBox_content_box .content_box {
  margin: 16px 16px 12px 16px;
}
.ProductCollectionBox_content_box .tab_header {
  width: 100%;
  height: 44px;
  opacity: 1;
  display: flex;
  flex-direction: row;
  padding: 0px 8px;
  box-sizing: border-box;
  border-width: 0px 0px 1px 0px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.09);
}
.ProductCollectionBox_content_box .tab_header li {
  width: 118px;
  line-height: 44px;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
}
.ProductCollectionBox_content_box .tab_header li.active {
  color: #0888ff;
  box-sizing: border-box;
  border-width: 0px 0px 2px 0px;
  border-style: solid;
  border-color: #0888ff;
}
.ProductCollectionBox_content_box .query_form .layui-row > div {
  padding-right: 10px;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
}
.ProductCollectionBox_content_box .query_form .flex_center span {
  flex-shrink: 0;
}
.ProductCollectionBox_content_box .query_form .flex_center .time_select {
  margin-left: 8px;
  flex: 1;
}
.ProductCollectionBox_content_box .query_form .layui-col-md2,
.layui-col-md3,
.layui-col-md4 {
  display: flex;
  align-items: center;
}
.ProductCollectionBox_content_box .query_form .label {
  flex-shrink: 0;
  padding-left: 8px;
}
.ProductCollectionBox_content_box .query_form > div:hover {
  border: 1px solid #0888ff !important;
}
.ProductCollectionBox_content_box .query_form .flex_center input,
select {
  width: 100%;
  border: none !important;
}
.ProductCollectionBox_content_box .query_form .flex_center select option {
  padding: 14px 16px;
  font-size: 14px;
}
.first_select {
  width: 92px;
}
.specification-box .prepare_publish_category {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 260px;
  display: flex;
  align-items: center;
}
.specification-box .prepare_publish_category .prepare_publish_category_name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
.specification-box .prepare_publish_category .matchingText {
  padding: 0px 4px;
  background: #EFF9FC;
  color: #81CFE6;
  border-radius: 4px;
  margin-right: 4px;
  font-size: 12px;
}
.specification-box .prepare_publish_category .master_category {
  padding: 0px 4px;
  background: rgba(141, 58, 189, 0.1);
  color: #772ef4;
  border-radius: 4px;
  margin-right: 4px;
  font-size: 12px;
}
.specification-box .prepare_publish_category .iconfont {
  color: rgba(0, 0, 0, 0.4);
  display: none;
  cursor: pointer;
}
.specification-box .prepare_publish_category:hover .iconfont {
  display: inline-block;
}
.specification-box .prepare_publish_shop {
  width: 180px;
  display: flex;
  align-items: center;
}
.specification-box .prepare_publish_shop .circle_box,
.showShoptip .circle_box {
  border-radius: 50%;
  display: inline-block;
  width: 8px;
  height: 8px;
  margin-right: 4px;
}
.showShoptip {
  color: #3d3d3d;
}
.specification-box .prepare_publish_shop .not_ublish_shop,
.showShoptip .not_ublish_shop {
  background: rgba(66, 74, 87, 0.2);
}
.specification-box .prepare_publish_shop .publishing_shop,
.showShoptip .publishing_shop {
  background: #dc8715;
}
.specification-box .prepare_publish_shop .publish_shop,
.showShoptip .publish_shop {
  background: #73ac1f;
}
.specification-box .prepare_publish_shop .shop_name {
  max-width: 140px;
  display: inline-block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.specification-box .prepare_publish_shop .iconfont {
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
  cursor: pointer;
  display: none;
}
.specification-box .check_shop {
  color: #0888ff;
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
}
.specification-box .check_shop .iconfont {
  rotate: 135deg;
}
.mark_box .iconfont {
  display: inline-block;
  width: 12px;
  height: 14px;
}
.mark_box .iconfont.isMark {
  color: #73ac1f;
  cursor: pointer;
}
.mark_box .iconfont.isNotMark {
  color: rgba(0, 0, 0, 0.3);
  cursor: pointer;
}
.ant-popover-content .ant-popover-inner {
  max-width: 200px;
  border: 0.5px solid rgba(0, 0, 0, 0.09);
  border-radius: 8px;
}
.table_box_btn span {
  margin-right: 12px;
}
.table_box_btn span:last-child {
  margin-right: 0px;
}
.ProductCollectionBox_content_box .productWrap .productShow .product-title li {
  display: flex;
  font-size: 12px;
  width: 224px;
  position: relative;
}
.ProductCollectionBox_content_box
  .productWrap
  .productShow
  .product-title
  li
  .iconfont {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.4);
  cursor: pointer;
}
.ProductCollectionBox_content_box
  .productWrap
  .productShow
  .product-title
  .title_txt
  span {
  width: 224px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 显示2行 */
  -webkit-box-orient: vertical;
}

.ProductCollectionBox_content_box
  .productWrap
  .productShow
  .product-title
  li
  .title_txt {
  width: 204px;
  height: auto;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.ProductCollectionBox_content_box
  .productWrap
  .productShow
  .product-title
  li
  .title_txt::before {
  content: '';
  float: right;
  width: 0px;
}

.ProductCollectionBox_content_box
  .productWrap
  .productShow
  .product-title
  li
  .title_txt
  .iconfont {
  font-size: 16px;
  position: absolute;
  right: 0;
  top: 1px;
}
.ProductCollectionBox_content_box
  .productWrap
  .productShow
  .product-title
  li
  .title_txt
  .iconfont:hover {
  color: #1890ff;
}

.product-title li .title_btnBox {
  font-size: 12px;
  display: flex;
  justify-content: right;
  cursor: pointer;
}
.product-title li .title_btnBox .title_cancel_edit_btn {
  color: rgba(0, 0, 0, 0.4);
  margin-right: 8px;
}
.product-title li .title_btnBox .title_save_edit_btn {
  color: #0888ff;
}
.ProductCollectionBox_content_box
  .productWrap
  .productShow
  .product-title
  .textarea-style {
  border: 1px solid rgba(0, 0, 0, 0.14);
  border-radius: 4px;
  height: 60px;
  font-size: 12px;
  width: 100%;
  color: rgba(0, 0, 0, 0.9);
  overflow-x: hidden;
}
#handledMsg:hover,
#handledMsg:focus {
  border: 1px solid #0888ff !important;
}
.showEdit .iconfont {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.4);
  cursor: pointer;
  margin-left: 2px;
  display: none;
}

/* 价格批量编辑css start*/
.checkShopTip {
  background: rgba(230, 158, 62, 0.1);
  border: 0.5px solid rgba(230, 158, 62, 0.2);
  padding: 8px;
  display: flex;
  margin-bottom: 16px;
  border-radius: 4px;
  font-size: 14px;
}
.checkShopTip .iconfont {
  margin: 0 6px;
  color: #dc8715;
  font-size: 14px;
}
.editProductPiceTable ul {
  width: 100%;
}
.editProductPiceTable li {
  width: 100%;
  display: flex;
}
.editProductPiceTable li.header_box > div {
  width: 50%;
  padding: 6px 8px;
  background: rgba(0, 0, 0, 0.04);
  border-width: 1px 0px 1px 0px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.09);
}
.editProductPiceTable .edit_box {
  display: flex;
  width: 100%;
  border-bottom: 1px solid rgba(0, 0, 0, 0.14);
  max-height: 248px;
}
.editProductPiceTable .edit_box > div {
  width: 50%;
  display: flex;
  justify-content: space-between;
}
.editPriceTip_box {
  display: flex;
}
.editPriceTip_box_tip {
  position: relative;
}
.editPriceTip_box_tip .editPriceTip_box_content {
  padding: 8px 12px;
  position: absolute;
  top: -86px;
  left: -10px;
  width: 164px;
  height: 80px;
  border-radius: 8px;
  background: #ffffff;
  color: rgba(0, 0, 0, 0.9);
  font-size: 12px;
  border: 0.5px solid rgba(0, 0, 0, 0.09);
  box-sizing: border-box;
  z-index: 100;
}
.editPriceTip_box_content::after {
  position: absolute;
  content: '';
  width: 10px;
  height: 10px;
  opacity: 1;
  background: #ffffff;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.09);
  box-sizing: border-box;
  transform: rotate(-45deg);
  border-width: 0 0 0.5px 0px;
  top: 73px;
}
.editProductPiceTable .input_price {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.14);
  border-radius: 4px;
  padding: 4px;
  font-size: 14px;
  margin-right: 4px;
}
.editProductPiceTable .input_price:hover {
  border: 1px solid #0888ff;
}
.editProductPiceTable .input_price .unit {
  padding: 0px 10px 0px 8px;
  display: inline-block;
}
.editProductPiceTable .input_price:focus-within {
  box-shadow: none;
  border: 1px solid #0888ff;
}
.editProductPiceTable .input_price input {
  border: none;
  border-radius: 8px;
  width: 100%;
}
.editProductPiceTable .input_price .ant-input {
  width: 66px;
}
.editProductPiceTable .stockAll .ant-input {
  width: 100px;
}
.editProductPiceTable .input_price .ant-input-affix-wrapper {
  border: none;
  display: flex;
  padding: 0px 4px;
}
.editProductPiceTable .input_price .ant-input-affix-wrapper:hover {
  border: none;
}
.editProductPiceTable
  .input_price
  .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
  border: none;
}
.editProductPiceTable .input_price .ant-input-affix-wrapper-focused,
.editProductPiceTable .input_price .ant-input-affix-wrapper:focus {
  border: none;
  box-shadow: none;
}
/* 价格批量编辑css end*/
.n-tabNav .n-tabNav-item {
  padding: 10px 16px;
}

.categoryMeu-wrap .categoryMeu-title {
  border: none;
}

.categoryMeu-wrap.active .categoryMeu-title {
  border: none !important;
  box-shadow: none !important;
}

.categoryMeu-wrap .categoryMeu-title:hover {
  border: none !important;
}

.createProductIframe {
  width: 100%;
  height: 100%;
}

.createProductIframe.activeScreen {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
}

.createProductIframe {
  display: none;
}

.createProductIframe.active {
  display: block;
}

#createFullMaskName .full-mask-header {
  float: right;
}

#createProductIframeWrap {
  border-top: none;
}
.ProductCollectionBox_content .query_form .inputSelectTime-box {
  border: none !important;
  padding-left: 0px;
  position: relative;
}
.ProductCollectionBox_content .query_form .inputSelectTime-box:hover {
  border: none !important;
}
#inputSelectCreateTime {
  position: absolute;
  width: 100% !important;
  height: 34px !important;
  z-index: 10;
}
/* #inputSelectCreateTime:hover {
  border: 1px solid #0888ff !important;
} */
#inputSelectCreateTimeDiv {
  width: 240px !important;
  height: 34px !important;
  border-radius: 4px;
  margin-left: 6px;
}
#inputSelectCreateTimeDiv:hover {
  border: 1px solid #0888ff !important;
}


#inputSelectUpdateTimeDiv {
  width: 240px !important;
  height: 34px !important;
  border-radius: 4px;
  margin-left: 6px;
  cursor: pointer;
}
#inputSelectUpdateTimeDiv:hover {
  border: 1px solid #0888ff !important;
}
#inputSelectUpdateTime {
  position: absolute;
  width: 100% !important;
  height: 34px !important;
  z-index: 10;
}
.closeShop2 .layui-layer-content {
  padding: 16px;
}
.closeShop .layui-layer-content {
  padding: 16px;
}
.closeShop {
  left: 57% !important;
}
.inputSelectTime-box .iconfont {
  position: absolute;
  right: 17px;
  top: 6px;
  color: rgba(0, 0, 0, 0.4);
  font-size: 14px;
}

/* 表格样式 */
.productWrap .ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
  max-height: calc(1.5em * 2);
  white-space: normal;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.9);
  cursor: pointer;
}
.specification-box {
  display: flex;
  flex-direction: column;
}
.specification-box .specification-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(0, 0, 0, 0.14);
  padding: 12px 8px;
}

.specification-box .specification-item:last-child {
  border-bottom: none;
}

.linkContent:hover {
  color: #0888ff !important;
}

/* 修改全球商品库存弹框样式 */
.editProductCountTable .goodsInfo-box {
  padding: 12px 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.14);
}
.editProductCountTable .goodsInfo-box .goodsInfo-img {
  width: 56px;
  height: 56px;
  border-radius: 3px;
  box-sizing: border-box;
  /* 中性色/Gray3 */
  border: 0.5px solid rgba(0, 0, 0, 0.14);
}
.editProductCountTable .goodsInfo-box .goodsInfo-img img {
  width: 100%;
  height: 100%;
  border-radius: 3px;
}
.editProductCountTable .goodsInfo-box .goodsInfo-title {
  width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.editProductCountTable .goodsInfo-box .goodsCount {
  padding-left: 26px;
}
.ProductCollectionBox_content_box .new_product_table .m_b2{
  margin-bottom: 2px;
}

.conmmonHover:hover .hovericon {
  /* color: #0888FF !important; */
  cursor: pointer;
}
