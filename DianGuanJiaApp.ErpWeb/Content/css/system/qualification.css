.layui-tab {
    margin-bottom: 15px;
}

.layui-mysearch .layui-form {
    width: 1120px;
}

.setWrapMain {
    background-color: #fff;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%);
    height: 100%;
    margin-right: 15px;
    display: flex;
    flex-direction: column;
    /* margin: 15px; */
    margin: 16px 24px;
    min-width: 1300px;
}

.setWrapContent-title {
    padding: 12px 0;
    border-bottom: 1px dotted #ddd;
    margin: 0 15px;
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
}

.setWrapContent-main {
    padding: 20px;
    display: flex;
    flex-direction: row;
}

.setWrapContent-title .setWrapContent-title-fText {
    font-size: 14px;
    color: #385067;
}

.setWrapContent-main-ul {
    display: flex;
    flex-direction: column;
    width: 1200px;
    box-sizing: border-box;
    min-width: 1000px;
}

    .setWrapContent-main-ul .setWrapContent-main-li {
        display: flex;
        box-sizing: border-box;
        width: 100%;
        flex-wrap: wrap;
        margin-bottom: 25px;
    }

        .setWrapContent-main-ul .setWrapContent-main-li:last-child {
            margin-bottom: 0;
        }

        .setWrapContent-main-ul .setWrapContent-main-li .setWrapContent-main-item {
            width: 50%;
            display: flex;
        }

.setWrapContent-main-item-title {
    width: 120px;
    min-width: 120px;
    display: inline-block;
    text-align: right;
    color: #888;
    margin-right: 15px;
    font-size: 14px;
}

.setWrapContent-main-item-content {
    color: #333;
}

.setWrapContent-main-item-img {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
}

    .setWrapContent-main-item-img img {
        width: 60px;
        height: 60px;
        display: inline-block;
        margin-right: 10px;
        margin-left: 10px;
    }

.setWrapContent-main-item-content .pintaiIcon {
}

.setWrapContent-main-item .icon-fuzhi1 {
    font-size: 13px;
    margin-left: 5px;
    cursor: pointer;
}

.setWrapContent-main-item-content-item {
    margin-bottom: 10px;
    display:flex;
}

.layui-mytable {
    width: 1000px;
}

.layui-btn .icon-shezhi1 {
    font-size: 12px !important;
    margin-right: 3px;
}

.supperWrap {
    display: flex;
    flex-direction: row;
}

.supperWrap-left {
    width: 300px;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: row;
    padding: 25px;
    box-sizing: border-box;
    justify-content: center;
    align-items: center;
}

    .supperWrap-left .supperWrap-left-img {
    }

        .supperWrap-left .supperWrap-left-img img {
            width: 60px;
            height: 60px;
            display: inline-block;
            border-radius: 50%;
        }

.supperWrap-right {
    width: 800px;
    display: flex;
    padding: 15px;
    box-sizing: border-box;
}

.supperWrap-left-text {
    display: flex;
    flex-direction: column;
    margin-left: 10px;
}

    .supperWrap-left-text .supperWrap-left-text-up {
        color: #333;
        font-size: 14px;
        margin-bottom: 8px;
    }

.supperWrap-right .supperWrap-right-title {
    font-size: 14px;
    color: #385067;
    margin-right: 15px;
    padding: 10px 0;
}

.supperWrap-right .supperWrap-right-main {
    width: 600px;
}

.supperWrap-right-main-up {
    display: flex;
    height: 50px;
    padding: 0 10px;
    align-items: center;
}

    .supperWrap-right-main-up li {
        margin-right: 20px;
        width: 120px;
        height: 35px;
        background-color: #ebebeb;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        position: relative;
    }

        .supperWrap-right-main-up li.active {
            color: #f29a1a;
            background-color: #fff0d9;
            overflow: hidden;
        }

            .supperWrap-right-main-up li.active::before {
                border-bottom: 20px solid #f29a1a;
                border-left: 20px solid transparent;
                position: absolute;
                bottom: 0;
                right: 0;
                display: block;
                content: "";
            }

            .supperWrap-right-main-up li.active::after {
                display: block;
                content: "";
                width: 8px;
                height: 5px;
                border-left: 1px solid #fff;
                border-bottom: 1px solid #fff;
                position: absolute;
                bottom: 6px;
                right: 1px;
                transform: rotate(313deg);
            }

.supperWrap-right-main-down {
    flex: 1;
    display: flex;
}

    .supperWrap-right-main-down textarea {
        flex: 1;
        height: 120px;
        border: 2px solid #f5f5f5;
        padding: 5px;
        box-sizing: border-box;
    }

.submitButton {
    font-size: 14px;
    margin-top: 10px;
    display: inline-block;
}

.upFileWrap {
    width: 100%;
    padding: 15px;
    box-sizing: border-box;
    background-color: #f5f5f5;
    flex: 1
}

.hide {
    display: none;
}

.upFileWrap .upFileWrap-content {
    display: flex;
    flex-direction: row;
}

    .upFileWrap .upFileWrap-content .upFileWrap-content-left {
        width: 120px;
        height: 120px;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #e2e2e2;
        border-radius: 8px;
        background-color: #fff;
        cursor: pointer;
    }

        .upFileWrap .upFileWrap-content .upFileWrap-content-left .icon-tubiaolunkuo- {
            font-size: 60px;
            color: #a1d8ff;
        }

.upFileWrap-content-right {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    margin-left: 10px;
}

    .upFileWrap-content-right .upFileWrap-content-right-item {
        color: #666;
    }

.setWrapContent-centeritem {
    align-items: center;
}

.edmitAddressWrap select {
    width: 145px;
    margin-right: 15px;
    height: 32px;
}

.setWrapContent-main-item-up input[type=text],
.setWrapContent-main-item-mid select {
    width: 144px;
    margin-right: 15px;
    height: 32px;
    box-sizing: border-box;
    padding: 5px;
}

    .setWrapContent-main-item-up input[type=text]:last-child,
    .setWrapContent-main-item-mid select:last-child {
        margin-right: 0;
    }

.setWrapContent-main-item-up,
.setWrapContent-main-item-mid {
    margin-bottom: 15px;
}

.setWrapContent-main-item-down textarea {
    width: 470px;
    height: 50px;
    padding: 5px;
    box-sizing: border-box;
}

.textarea01 {
    width: 470px;
    height: 50px;
    padding: 5px;
    box-sizing: border-box;
}

.creditPayWrap {
    width: 1070px;
    border: 1px solid #e2e2e2;
    padding: 15px;
    box-sizing: border-box;
    margin-top: 10px;
    display: flex;
    flex-direction: column;
}

    .creditPayWrap .creditPayWrap-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 15px;
    }

        .creditPayWrap .creditPayWrap-item input[type=text] {
            width: 320px;
            height: 32px;
        }

.creditPayWrap-item-btn {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    border: 1px dashed #e2e2e2;
    border-radius: 8px;
    width: 50px;
    height: 50px;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 5px;
    box-sizing: border-box;
    color: #3aadff;
    cursor: pointer;
}

.creditPayWrap-item-title {
    width: 50px;
    display: inline-block;
    text-align: right;
}

.creditPayWrap-item-main {
    display: flex;
    flex-wrap: wrap;
    padding-left: 15px;
}

    .creditPayWrap-item-main .creditPayWrap-item-main-item {
        display: flex;
        margin-right: 15px;
        flex-direction: column;
    }

        .creditPayWrap-item-main .creditPayWrap-item-main-item span {
            color: #333;
            padding: 0 15px 10px 15px;
        }

        .creditPayWrap-item-main .creditPayWrap-item-main-item input[type=text] {
            width: 200px;
        }

.platformWrap-td {
    width: 110px;
    height: 52px;
    box-sizing: border-box;
    cursor: pointer;
    background-image: url(../../images/pingtai-icon.png);
    background-position: 0 0;
}

.platformWrap {
    display: flex;
    flex-direction: column;
}

.platformWrap-tr {
    display: flex;
}

.platformWrap-tdWrap {
    display: flex;
    flex-direction: row;
    align-items: center;
    border: 1px solid #e2e2e2;
    cursor: pointer;
    justify-content: center;
    margin-top: -1px;
    margin-left: -1px;
    padding: 0 15px;
    box-sizing: border-box;
}

.addmoreShop {
    border: 1px dashed #e2e2e2;
    padding: 5px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    box-sizing: border-box;
    display: flex;
    cursor: pointer;
}

.fileCompanyInfoPicShowWrap {
    width: 120px;
    height: 120px;
    position: relative;
    border-radius: 8px;
    display: none;
}

#fileCompanyInfoPic_show {
    width: 120px;
    height: 120px;
    border-radius: 8px;
}

.fileCompanyInfoPic-targetText {
    width: 120px;
    background: rgba(0, 0, 0, .3);
    height: 25px;
    justify-content: center;
    align-items: center;
    color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    cursor: pointer;
    display: none;
}

.fileCompanyInfoPicShowWrap:hover .fileCompanyInfoPic-targetText {
    display: flex;
}

.creditPayWrap-imgWrap {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    position: relative;
}

    .creditPayWrap-imgWrap .icon-laji {
        width: 50px;
        background: rgba(0, 0, 0, .3);
        height: 20px;
        justify-content: center;
        align-items: center;
        color: #fff;
        position: absolute;
        bottom: 0;
        left: 0;
        cursor: pointer;
        display: none;
    }

    .creditPayWrap-imgWrap:hover .icon-laji {
        display: flex;
    }

.creditPayWrap-imgWrap,
.creditPayWrap_img {
    width: 50px;
    height: 50px;
    display: flex;
    border-radius: 8px;
}
.setWrapContentinput {
    
}
.setWrapContent-main-item-contentPayWrap {
    display:flex;
    flex-direction:column;
}
.setWrapContent-main-item-contentPayWrap .payItem {
    display: inline-block;
    margin-bottom: 5px;
}
.setWrapContent-main-item-contentPayWrap .payItem:last-child {
    margin-bottom: 0;
}
.setWrapContent-main-item-contentPayWrap .icon-fuzhi1 {
    font-size: 13px;
    color:#666;
}
.layui-layout-admin .layui-footer {
    z-index:10;
}
.authorizationNav > li {
    padding:0;
}
.authorizationNav > li>span {
    display:inline-block;
    padding:0 15px;

}