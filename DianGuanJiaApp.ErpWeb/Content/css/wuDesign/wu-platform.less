@import "./unit.less"; //变量：包括颜色 字体大小  圆角
.wu-pintaiIcon {
	display: inline-block;
	background-image: url(/Content/images/platform-icons-2025-2-14.png);
	background-size: 540px 400px;

	&.wu-big {
		border-radius: @6radius;
		background-position: -90px -160px;
		width: 90px;
		height: 40px;

		&.<PERSON> { //菜鸟
			background-position: -450px -120px !important;
		}

		&.TouTiao { //抖店
			background-position: 0 0 !important;
		}

		&.Taobao { //淘宝
			background-position: -360px 0 !important;
		}

		&.TikTok { //TikTok
			background-position: -270px -160px !important;
		}

		&.Alibaba { //阿里巴巴
			background-position: -270px 0 !important;
		}

		&.<PERSON><PERSON> {
			background-position: 0 0 !important;
		}

		&.TaobaoMaiCai, &.TaoBaoMaiCai { //淘宝卖菜 小兔子
			background-position: -270px -40px !important;
		}

		&.<PERSON><PERSON><PERSON><PERSON><PERSON> { //拼多多
			background-position: -90px 0 !important;
		}

		&.<PERSON><PERSON> { //京东
			background-position: 0 -40px !important;
		}

		&.JingdongPurchase { //京东供销平台  黄牛
			background-position: -90px -40px !important;
		}

		&.KuaiShou { //快手
			background-position: -180px 0 !important;
		}

		&.OwnShop { //自有商城
			background-position: 0 -160px !important;
		}

		&.WxXiaoShangDian { //微信小商店
			background-position: -450px -40px !important;
		}

		&.WxXiaoDian { //微信小店
			background-position: -360px -40px !important;
		}

		&.WxVideo { //微信小店
			background-position: -450px -40px !important;
		}

		&.YouZan { //有赞
			background-position: -180px -80px !important;
		}

		&.Other_Heliang,
		&.Other, &.OtherPlatforms { //其它平台
			background-position: -90px -160px !important;
		}

		&.WeiMeng { //微盟
			background-position: -270px -80px !important;
		}

		&.DuXiaoDian { //度小店
			background-position: -90px -80px !important;
		}

		&.Virtual { //虚拟店铺
			background-position: -360px -160px !important;
		}

		&.AlibabaC2M { //淘工厂 红厂图标
			background-position: -450px 0 !important;
		}

		&.MeiLiShuo { //美丽说
			background-position: -450px -80px !important;
		}

		&.MoGuJie { //蘑菇街
			background-position: -360px -80px !important;
		}

		&.MengTui { //萌推
			background-position: -450px -160px !important;
		}

		&.MoKuai { //魔块星选
			background-position: 0 -120px !important;
		}

		&.Suning, &.SuNing { //苏宁易购
			background-position: -180px -120px !important;
		}

		&.WeiDian { //微店
			background-position: -90px -120px !important;
		}

		&.TuanHaoHuo { //团好货
			background-position: -270px -120px !important;
		}

		&.XiaoHongShu { //小红书
			background-position: -180px -40px !important;
		}

		&.VipShop { //唯品会
			background-position: -360px -120px !important;
		}

		&.KuaiTuanTuan { //快团团
			background-position: 0 -80px !important;
		}

		&.BiliBili { //快团团
			background-position: -360px -200px !important;
		}
	}

	&.wu-mid {
		border-radius: @6radius;
		background-position: 0 -348px;
		width: 20px;
		height: 20px;
		background-position: -500px -348px !important;

		&.Link { //菜鸟
			background-position: -460px -348px !important;
		}

		&.TouTiao { //抖店
			background-position: 0px -328px !important;
		}

		&.Taobao { //淘宝
			background-position: -80px -348px !important;
		}

		&.TikTok { //TikTok
			background-position: -20px -328px !important;
		}

		&.Alibaba, &.System { //阿里巴巴
			background-position: -60px -348px !important;
		}

		&.JingXuan {
			background-position: -140px -328px !important;
		}

		&.TaobaoMaiCai, &.TaoBaoMaiCai { //淘宝卖菜 小兔子
			background-position: -180px -348px !important;
		}

		&.Pinduoduo { //拼多多
			background-position: -20px -348px !important;
		}

		&.Jingdong { //京东
			background-position: -120px -348px !important;
		}

		&.JingdongPurchase { //京东供销平台  黄牛
			background-position: -140px -348px !important;
		}

		&.KuaiShou { //快手
			background-position: -40px -348px !important;
		}

		&.OwnShop { //自有商城
			background-position: -480px -348px !important;
		}

		&.WxXiaoShangDian { //微信小商店
			background-position: -220px -348px !important;
		}

		&.WxXiaoDian { //微信小店
			background-position: -200px -348px !important;
		}

		&.WxVideo { //微信小店
			background-position: -220px -348px !important;
		}

		&.YouZan { //有赞
			background-position: -280px -348px !important;
		}

		&.Other_Heliang,
		&.Other,
		&.OtherPlatforms { //其它平台
			background-position: -500px -348px !important;
		}

		&.WeiMeng { //微盟
			background-position: -300px -348px !important;
		}

		&.DuXiaoDian { //度小店
			background-position: -260px -348px !important;
		}

		&.Virtual { //虚拟店铺
			background-position: -40px -328px !important;
		}

		&.AlibabaC2M { //淘工厂 红厂图标
			background-position: -100px -348px !important;
		}

		&.MeiLiShuo { //美丽说
			background-position: -340px -348px !important;
		}

		&.MoGuJie { //蘑菇街
			background-position: -320px -348px !important;
		}

		&.MengTui { //萌推
			background-position: -60px -328px !important;
		}

		&.MoKuai { //魔块星选
			background-position: -360px -348px !important;
		}

		&.Suning, &.SuNing { //苏宁易购
			background-position: -400px -348px !important;
		}

		&.WeiDian { //微店
			background-position: -380px -348px !important;
		}

		&.TuanHaoHuo { //团好货
			background-position: -420px -348px !important;
		}

		&.XiaoHongShu { //小红书
			background-position: -160px -348px !important;
		}

		&.VipShop { //唯品会
			background-position: -440px -348px !important;
		}

		&.KuaiTuanTuan { //快团团
			background-position: -240px -348px !important;
		}

		&.BiliBili { //快团团
			background-position: -180px -328px !important;
		}

		&.Dgj { //店管家
			background-position: -520px -348px !important;
		}

		&.Cainiaolink {
			background-position: -460px -348px !important;
		}
	}
	&.wu-small {
		background-position: -400px -384px !important;
		width: 16px;
		height: 16px;
		border-radius: @4radius;

		&.Link { //菜鸟
			background-position: -368px -384px !important;
		}
		&.TouTiao { //抖店
			background-position: 0px -368px !important;
		}

		&.Taobao { //淘宝
			background-position: -64px -384px !important;
		}

		&.TikTok { //TikTok
			background-position: -16px -368px !important;
		}

		&.Alibaba, &.System { //阿里巴巴
			background-position: -48px -384px !important;
		}

		&.JingXuan {
			background-position: -112px -368px !important;
		}

		&.TaobaoMaiCai, &.TaoBaoMaiCai { //淘宝卖菜 小兔子
			background-position: -144px -384px !important;
		}

		&.Pinduoduo { //拼多多
			background-position: -16px -384px !important;
		}

		&.Jingdong { //京东
			background-position: -96px -384px !important;
		}

		&.JingdongPurchase { //京东供销平台  红和蓝
			background-position: -112px -384px !important;
		}

		&.KuaiShou { //快手
			background-position: -32px -384px !important;
		}

		&.OwnShop { //自有商城
			background-position: -384px -384px !important;
		}

		&.WxXiaoShangDian { //微信小商店
			background-position: -176px -384px !important;
		}

		&.WxXiaoDian { //微信小店
			background-position: -160px -384px !important;
		}

		&.WxVideo { //微信小店
			background-position: -176px -384px !important;
		}

		&.YouZan { //有赞
			background-position: -224px -384px !important;
		}

		&.Other_Heliang,
		&.Other,
		&.OtherPlatforms { //其它平台
			background-position: -400px -384px !important;
		}

		&.WeiMeng { //微盟
			background-position: -240px -384px !important;
		}

		&.DuXiaoDian { //度小店
			background-position: -208px -384px !important;
		}

		&.Virtual { //虚拟店铺
			background-position: -32px -368px !important;
		}

		&.AlibabaC2M { //淘工厂 红厂图标
			background-position: -80px -384px !important;
		}

		&.MeiLiShuo { //美丽说
			background-position: -272px -384px !important;
		}

		&.MoGuJie { //蘑菇街
			background-position: -256px -384px !important;
		}

		&.MengTui { //萌推
			background-position: -48px -368px !important;
		}

		&.MoKuai { //魔块星选
			background-position: -288px -384px !important;
		}

		&.Suning, &.SuNing { //苏宁易购
			background-position: -320px -384px !important;
		}

		&.WeiDian { //微店
			background-position: -304px -384px !important;
		}

		&.TuanHaoHuo { //团好货
			background-position: -336px -384px !important;
		}

		&.XiaoHongShu { //小红书
			background-position: -128px -384px !important;
		}

		&.VipShop { //唯品会
			background-position: -352px -384px !important;
		}

		&.KuaiTuanTuan { //快团团
			background-position: -192px -384px !important;
		}

		&.BiliBili { //快团团
			background-position: -144px -368px !important;
		}

		&.Dgj { //店管家
			background-position: -416px -384px !important;
		}

		&.Cainiaolink {
			background-position: -368px -384px !important;
		}
	}
}
