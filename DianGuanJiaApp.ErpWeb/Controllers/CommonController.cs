using DianGuanJiaApp.Controllers;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.EntityExtension;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.StorageService;
using DianGuanJiaApp.Services.Services.Tools;
using DianGuanJiaApp.Services.Services.MessageQueue;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Net;
using DianGuanJiaApp.Utility.NPOI;
using DianGuanJiaApp.Utility.Other;
using DianGuanJiaApp.Utility.Web;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Security.Policy;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Web;
using System.Web.Mvc;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model.Trace;
using DianGuanJiaApp.Services.Services.BaseProduct;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Portal.Controllers;

namespace DianGuanJiaApp.ErpWeb.Controllers
{

    /// <summary>
    /// 公共控制器
    /// 获取一些公共数据的方法
    /// </summary>
    public class CommonController : BaseController
    {
        private ExpressCompanyService expressCompanyService = new ExpressCompanyService();
        private AreaCodeInfoService areaCodeInfoService = new AreaCodeInfoService();
        private CommonSettingService commonSettingService = new CommonSettingService();
        private PrinterBindService printerBindService = new PrinterBindService();
        private ShopService ShopBindService = new ShopService();
        private OrderCategoryService _orderCategory = new OrderCategoryService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private DeliveryModeChangeLogService deliveryModeChangeLogService = new DeliveryModeChangeLogService();
        /// <summary>
        /// 获取系统快递公司
        /// </summary>
        /// <returns></returns>
        //[IgnoreToken]
        public ActionResult LoadExpressCompany()
        {
            //this.ValidLoginToken();
            var expressCompanyList = expressCompanyService.GetExpressCompay();

            return SuccessResult(expressCompanyList);
        }

        /// <summary>
        /// 获取区域信息
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        //[IgnoreToken]
        public ActionResult LoadAreaCodeInfo(int parentId)
        {
            //this.ValidLoginToken();
            var areaCodeInfoList = areaCodeInfoService.GetAreaInfoList(parentId)?.OrderBy(a => a.Name)?.ToList();

            return SuccessResult(areaCodeInfoList);
        }

        /// <summary>
        /// 获取所有的省市区信息
        /// </summary>
        /// <returns></returns>
        //[IgnoreToken]
        public ActionResult LoadAreas()
        {
            //this.ValidLoginToken();
            var provinceKey = new string[] { "特别行政区", "古自治区", "维吾尔自治区", "壮族自治区", "回族自治区", "自治区", "省省直辖", "省", "市" };
            var cityKey = new string[] { "布依族苗族自治州", "苗族侗族自治州", "自治州", "州", "市", "县" };
            var areas = areaCodeInfoService.GetAreaInfoList();
            var provinces = areas.Where(m => m.ParentId == 1);
            var result = provinces.Select(p => new
            {
                name = p.Name.TrimEnd(provinceKey),
                label = p.Name,
                p.Id,
                city = areas.Where(c => c.ParentId == p.Id).Select(c => new
                {
                    name = c.Name.TrimEnd(cityKey),
                    label = c.Name,
                    c.Id,
                    area = areas.Where(a => a.ParentId == c.Id).Select(a => a.Name)
                })
            });
            return SuccessResult(result);
        }

        /// <summary>
        /// 获取所有的省市区街道信息（四级地址）
        /// 不建议直接全部查出来，可以使用下面的通过三级查街道
        /// </summary>
        /// <param name="needSubfix">是否需要保留地址后缀，默认false（去除后缀）</param>
        /// <returns></returns>
        //[IgnoreToken]
        public ActionResult LoadFourLevelAreas(bool needSubfix = false)
        {
            //this.ValidLoginToken();
            var fourLevelAreas = areaCodeInfoService.GetFourLevelAreaInfoList(needSubfix);
            var result = fourLevelAreas.Select(p => new
            {
                name = p.ProvinceInfo.Name,
                label = p.ProvinceInfo.Name,
                id = p.ProvinceInfo.Id,
                city = p.CityLst.Select(c => new
                {
                    name = c.CityInfo.Name,
                    label = c.CityInfo.Name,
                    id = c.CityInfo.Id,
                    area = c.AreaLst.Select(a => new
                    {
                        name = a.Name,
                        label = a.Name,
                        id = a.Id,
                        street = c.StreetLst.Where(s => s.AreaInfo.Id == a.Id)
                                           .SelectMany(s => s.StreetLst)
                                           .Select(st => new
                                           {
                                               name = st.Name,
                                               label = st.Name,
                                               id = st.Id
                                           })
                    })
                })
            });
            return SuccessResult(result);
        }

        /// <summary>
        /// 根据区域ID获取街道信息
        /// </summary>
        /// <param name="areaId">区域ID</param>
        /// <param name="needSubfix">是否需要保留地址后缀，默认false</param>
        /// <returns></returns>
        //[IgnoreToken]
        public ActionResult LoadStreetsByAreaId(int areaId, bool needSubfix = false)
        {
            //this.ValidLoginToken();
            var streets = areaCodeInfoService.GetStreetsByAreaId(areaId, needSubfix);
            var result = streets.Select(s => new
            {
                name = s.Name,
                label = s.Name,
                id = s.Id,
                parentId = s.ParentId
            });
            return SuccessResult(result);
        }

        /// <summary>
        /// 获取指定平台所有的省市区信息
        /// </summary>
        /// <param name="pt">KuaiShou\Pinduoduo</param>
        /// <returns></returns>
        public ActionResult LoadPlatformThreeAreas(string pt)
        {
            var _ptAreaService = new PlatformAreaCodeInfoService();
            var areaCodeInfos = _ptAreaService.GetTreeAreaInfoList(pt);
            return SuccessResult(areaCodeInfos);
        }

        public ActionResult LoadCommonSetting(string settingKey)
        {
            if (string.IsNullOrEmpty(settingKey))
                return SuccessResult();

            var shopId = SiteContext.Current.CurrentShopId;
            var setting = commonSettingService.GetString(settingKey, shopId);
            if (settingKey == "PrintContent" && setting == null)
            {
                //var val = "{\"PrintContentSet\":[{\"Name\":\"序号\",\"Value\":\"SerialNumber\",\"Checked\":true,\"Sort\":0},{\"Name\":\"简称标题\",\"Value\":\"Title\",\"Checked\":true,\"Sort\":1},{\"Name\":\"商品编码\",\"Value\":\"UPC\",\"Checked\":true,\"Sort\":2},{\"Name\":\"规格颜色\",\"Value\":\"Color\",\"Checked\":true,\"Sort\":3},{\"Name\":\"规格尺码\",\"Value\":\"Size\",\"Checked\":true,\"Sort\":4},{\"Name\":\"单价\",\"Value\":\"Price\",\"Checked\":false,\"Sort\":5},{\"Name\":\"数量\",\"Value\":\"Qty\",\"Checked\":true,\"Sort\":6}],\"TitleSet\":\"NoShortPrintTitle\",\"UpcSet\":\"NoProductUpcPirntGoodsUpc\",\"MergeGoodsSet\":false,\"LinePrintNum\":1,\"PrintPaging\":\"Paging\",\"PagingProductNum\":5,\"QtyStryle\":\"[{0}]\",\"QtyUnit\":\"无单位\"}";
                //if (SiteContext.Current.CurrentLoginShop.PlatformType == "YouZan")
                //    val = "{\"PrintContentSet\":[{\"Name\":\"序号\",\"Value\":\"SerialNumber\",\"Checked\":true,\"Sort\":0},{\"Name\":\"简称标题\",\"Value\":\"Title\",\"Checked\":true,\"Sort\":1},{\"Name\":\"商品编码\",\"Value\":\"UPC\",\"Checked\":true,\"Sort\":2},{\"Name\":\"商品规格\",\"Value\":\"ProductAttr\",\"Checked\":true,\"Sort\":3},{\"Name\":\"单价\",\"Value\":\"Price\",\"Checked\":false,\"Sort\":4},{\"Name\":\"数量\",\"Value\":\"Qty\",\"Checked\":true,\"Sort\":5}],\"TitleSet\":\"NoShortPrintTitle\",\"UpcSet\":\"NoProductUpcPirntGoodsUpc\",\"MergeGoodsSet\":false,\"LinePrintNum\":1,\"PrintPaging\":\"Paging\",\"PagingProductNum\":5,\"QtyStryle\":\"[{0}]\",\"QtyUnit\":\"无单位\"}";
                //else if (SiteContext.Current.CurrentLoginShop.PlatformType == "KuaiShou")
                //    val = "{\"PrintContentSet\":[{\"Name\":\"序号\",\"Value\":\"SerialNumber\",\"Checked\":true,\"Sort\":0},{\"Name\":\"简称标题\",\"Value\":\"Title\",\"Checked\":true,\"Sort\":1},{\"Name\":\"SKU编码\",\"Value\":\"UPC\",\"Checked\":true,\"Sort\":2},{\"Name\":\"产品ID\",\"Value\":\"ProductID\",\"Checked\":true,\"Sort\":3},{\"Name\":\"规格颜色\",\"Value\":\"Color\",\"Checked\":true,\"Sort\":4},{\"Name\":\"规格尺码\",\"Value\":\"Size\",\"Checked\":true,\"Sort\":5},{\"Name\":\"单价\",\"Value\":\"Price\",\"Checked\":false,\"Sort\":6},{\"Name\":\"数量\",\"Value\":\"Qty\",\"Checked\":true,\"Sort\":7}],\"TitleSet\":\"NoShortPrintTitle\",\"UpcSet\":\"NoProductUpcPirntGoodsUpc\",\"MergeGoodsSet\":false,\"LinePrintNum\":1,\"PrintPaging\":\"Paging\",\"PagingProductNum\":5,\"QtyStryle\":\"[{0}]\",\"QtyUnit\":\"无单位\"}";

                //20240122 新增1688定制服务
                //var val = "{\"PrintContentSet\":[{\"Name\":\"序号\",\"Value\":\"SerialNumber\",\"Checked\":true,\"Sort\":0},{\"Name\":\"简称标题\",\"Value\":\"Title\",\"Checked\":true,\"Sort\":1},{\"Name\":\"商品编码\",\"Value\":\"UPC\",\"Checked\":true,\"Sort\":2},{\"Name\":\"规格颜色\",\"Value\":\"Color\",\"Checked\":true,\"Sort\":3},{\"Name\":\"规格尺码\",\"Value\":\"Size\",\"Checked\":true,\"Sort\":4},{\"Name\":\"定制服务\",\"Value\":\"ExtAttr4\",\"Checked\":true,\"Sort\":5},{\"Name\":\"单价\",\"Value\":\"Price\",\"Checked\":false,\"Sort\":6},{\"Name\":\"数量\",\"Value\":\"Qty\",\"Checked\":true,\"Sort\":7}],\"TitleSet\":\"NoShortPrintTitle\",\"UpcSet\":\"NoProductUpcPirntGoodsUpc\",\"MergeGoodsSet\":false,\"LinePrintNum\":1,\"PrintPaging\":\"Paging\",\"PagingProductNum\":5,\"QtyStryle\":\"[{0}]\",\"QtyUnit\":\"无单位\"}";

                //20241031 新增配置项
                //var val = "{\"PrintContentSet\":[{\"Name\":\"序号\",\"Value\":\"SerialNumber\",\"Checked\":true,\"Sort\":0},{\"Name\":\"简称标题\",\"Value\":\"Title\",\"Checked\":true,\"Sort\":1},{\"Name\":\"货号\",\"Value\":\"UPC\",\"Checked\":true,\"Sort\":2},{\"Name\":\"规格颜色\",\"Value\":\"Color\",\"Checked\":true,\"Sort\":3},{\"Name\":\"规格尺码\",\"Value\":\"Size\",\"Checked\":true,\"Sort\":4},{\"Name\":\"单价\",\"Value\":\"Price\",\"Checked\":false,\"Sort\":5},{\"Name\":\"定制服务\",\"Value\":\"ExtAttr4\",\"Checked\":true,\"Sort\":6},{\"Name\":\"数量\",\"Value\":\"Qty\",\"Checked\":true,\"Sort\":7},{\"Name\":\"规格简称\",\"Value\":\"SkuShortTilte\",\"Checked\":false,\"Sort\":8}],\"TitleSet\":\"NoShortPrintTitle\",\"UpcSet\":\"NoProductUpcPirntGoodsUpc\",\"MergeGoodsSet\":false,\"LinePrintNum\":1,\"PrintPaging\":\"NoPaging\",\"PagingProductNum\":0,\"QtyStryle\":\"[{0}]\",\"QtyUnit\":\"无单位\",\"Separator\":\"space\",\"IsPaging\":false,\"ProductSkuTitleSet\":true,\"ReceiverSet\":{\"EncryptReceiverName\":false,\"EncryptReceiverPhone\":false,\"EncryptReceiverAddress\":false,\"NotEncryptReceiverTemplateId\":[]},\"SkuTitleSet\":\"PrintShort\"}";

                //20241218 新增配置项
                var val = "{\"PrintContentSet\":[{\"Name\":\"序号\",\"Value\":\"SerialNumber\",\"Checked\":true,\"Sort\":0},{\"Name\":\"商品简称\",\"Value\":\"ProductShortTitle\",\"Checked\":true,\"Sort\":1},{\"Name\":\"货号\",\"Value\":\"UPC\",\"Checked\":true,\"Sort\":2},{\"Name\":\"规格颜色\",\"Value\":\"Color\",\"Checked\":true,\"Sort\":3},{\"Name\":\"规格尺码\",\"Value\":\"Size\",\"Checked\":true,\"Sort\":4},{\"Name\":\"单价\",\"Value\":\"Price\",\"Checked\":false,\"Sort\":5},{\"Name\":\"定制服务\",\"Value\":\"ExtAttr4\",\"Checked\":true,\"Sort\":6},{\"Name\":\"数量\",\"Value\":\"Qty\",\"Checked\":true,\"Sort\":7},{\"Name\":\"规格简称\",\"Value\":\"SkuShortTilte\",\"Checked\":false,\"Sort\":8},{\"Name\":\"简称标题\",\"Value\":\"Title\",\"Checked\":false,\"Sort\":9}],\"TitleSet\":\"NoShortPrintTitle\",\"UpcSet\":\"NoProductUpcPirntGoodsUpc\",\"MergeGoodsSet\":false,\"LinePrintNum\":1,\"PrintPaging\":\"NoPaging\",\"PagingProductNum\":0,\"QtyStryle\":\"[{0}]\",\"QtyUnit\":\"无单位\",\"Separator\":\"space\",\"IsPaging\":false,\"ProductSkuTitleSet\":true,\"ReceiverSet\":{\"EncryptReceiverName\":false,\"EncryptReceiverPhone\":false,\"EncryptReceiverAddress\":false,\"NotEncryptReceiverTemplateId\":[]},\"ProductShortTitleSet\":\"NoShortPrintTitle\",\"SkuTitleSet\":\"PrintShort\",\"isNewSet\":true}";

                var result = commonSettingService.Set(settingKey, val, shopId);
                if (result > 0)
                    setting = val;
            }
            else if (settingKey == "ScanSendSelectContentSet" && setting == null)
            {
                var val = "{\"AutoSend\":false,\"WeightOpen\":false,\"UnitIndex\":false}";
                var result = commonSettingService.Set(settingKey, val, shopId);
                if (result > 0)
                    setting = val;
            }
            else if (settingKey == "/ErpWeb/SetInfo/ManyCodeSendConfig" && setting == null)
            {
                //多单号发货平台的默认值
                var val = new ManyCodeSendConfigModel().ToJson();
                var result = commonSettingService.Set(settingKey, val, shopId);
                if (result > 0)
                    setting = val;
            }

            return SuccessResult(setting);
        }

        public ActionResult SaveMessageSetting(string settingValue)
        {
            if (SiteContext.IsSubAccount())
            {
                return FalidResult("子账号无权进行此操作");
            }
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var settingKey = $"FxMessage:User:Config:{fxUserId}";
            var result = SaveCommonSetting(settingKey, settingValue);

            // 清除未读缓存
            var rsp = new SiteMessageService().ClearCache(new SiteMessage.Model.Request.MessageClearCecheRequest() { UserId = fxUserId, Source = 0 });
            if (!rsp.IsSucc)
            {
                Log.WriteError($"站内信清除缓存失败：{rsp.Message}");
            }
            return result;
        }
        public ActionResult LoadMessageSetting()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var shopId = SiteContext.Current.CurrentShopId;
            var settingKey = $"FxMessage:User:Config:{fxUserId}";
            try
            {
                var settingValue = commonSettingService.GetOrNewMessageSetting(settingKey, shopId, out bool isNew);
                if (isNew)
                {
                    SaveCommonSetting(settingKey, settingValue);
                    return SuccessResult(settingValue);
                }
                return SuccessResult(settingValue);
            }
            catch (Exception ex)
            {
                return FalidResult(ex.Message);
            }
        }

        /// <summary>
        /// 获取备注设置 
        /// </summary>
        /// <param name="type">type=1：所有订单，type=2：打单发货</param>
        /// <returns></returns>
        public ActionResult LoadRemarkSetting(int type)
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var settingKey = $"DianGuanJiaApp:FenXiao:AllOrder:RemarkSetting";
            if (type == 2)
            {
                settingKey = $"DianGuanJiaApp:FenXiao:WaitOrder:RemarkSetting";
            }
            try
            {
                var settingValue = commonSettingService.GetOrNewRemarkSetting(settingKey, shopId, out bool isNew, out var remarkSettingModel);
                if (isNew)
                {
                    SaveCommonSetting(settingKey, settingValue);
                    return SuccessResult(remarkSettingModel);
                }
                return SuccessResult(remarkSettingModel);
            }
            catch (Exception ex)
            {
                return FalidResult("服务器繁忙，请稍后再试");
            }
        }

        /// <summary>
        /// 保存备注设置
        /// </summary>
        /// <param name="type"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult SaveRemarkSetting(int type, RemarkSettingModel model)
        {
            if (model == null)
            {
                return FalidResult("请选择备注设置");
            }
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var settingKey = $"DianGuanJiaApp:FenXiao:AllOrder:RemarkSetting";
            if (type == 2)
            {
                settingKey = $"DianGuanJiaApp:FenXiao:WaitOrder:RemarkSetting";
            }
            return SaveCommonSetting(settingKey, model.ToJson());
        }

        public ActionResult SaveCommonSetting(string settingKey, string settingValue)
        {
            if (string.IsNullOrEmpty(settingKey))
            {
                return SuccessResult();
            }
            //20250121简易处理 settingValue 跨境模板含特殊字符匹配
            if (!string.IsNullOrWhiteSpace(settingValue) && !CustomerConfig.IsCrossBorderSite)
            {
                settingValue = Server.UrlDecode(settingValue);
            }
            
            //检测配置键是否存在非法字符
            if (ValidCommonSettingKeyContainsIllegalCharacters(settingKey))
            {
                return FalidResult("保存配置失败");
            }

            //TODO:ShopId 待 授权做好之后,再修改为授权店铺
            var shopId = SiteContext.Current.CurrentShopId;
            var date = DateTime.Now;
            // 查询字段为时间格式异常日志记录
            if (settingKey.ToString2() == "CustomConditionSet" && DateTime.TryParse(settingValue.ToString2(), out date))
            {
                try
                {
                    var ip = Request?.Headers["X-Forwarded-For"];
                    if (string.IsNullOrEmpty(ip))
                    {
                        ip = Request?.UserHostAddress;
                    }
                    var log = new LogForOperator()
                    {
                        OperatorType = "查询条件异常",
                        TraceId = Request?["traceId"],
                        DBType = DatabaseTypeEnum.SQLServer.ToString(),
                        ShopId = SiteContext.GetCurrentShopId(),
                        UserId = SiteContext.GetCurrentFxUserId(),
                        IP = ip,
                        ServerIP = DianGuanJiaApp.Utility.Net.HttpUtility.GetServerIP(),
                        Description = new OperationDescription
                        {
                            Route = $"Common/SaveCommonSetting?settingKey={settingKey.ToString2()}&settingValue={settingValue.ToString2()}",
                            Referrer = Request?.UrlReferrer?.ToString(),
                            UserAgent = Request?.UserAgent,
                            Url = Request?.Url?.ToString(),
                            Name = "查询条件异常"
                        },

                    };
                    LogForOperatorContext.Current.Begin(log);
                    LogForOperatorContext.Current.End();
                }
                catch (Exception ex)
                {
                    Log.WriteError($"查询条件设置异常：{ex}");
                }
            }

            var result = commonSettingService.Set(settingKey, settingValue, shopId);

            //需要转发到拼多多或京东、抖店云
            var TransferKeys = commonSettingService.Get("/FxSystem/TransferToOtherPlatformKeys", 0)?.Value.SplitToList(",") ?? new List<string>();
            if (result > 0 && TransferKeys != null && TransferKeys.Contains(settingKey) && CustomerConfig.CloudPlatformType != "Pinduoduo")
            {
                //Log.Debug($"配置【{settingKey}】转发更新其他平台配置");
                //发往京东和拼多多平台添加发件人
                var apiUrl = "/CommonApi/SaveCommonSetting";
                var aliCloudHost = CustomerConfig.AlibabaFenFaSystemUrl.TrimEnd("/") + apiUrl;
                var pddCloudHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/") + apiUrl;
                var ttCloudHost = CustomerConfig.ToutiaoFenFaSystemUrl.TrimEnd("/") + apiUrl;
                var jdCloudHost = CustomerConfig.JingdongFenFaSystemUrl.TrimEnd("/") + apiUrl;

                var setting = new CommonSetting { Key = settingKey, Value = settingValue, ShopId = shopId };
                //分单系统主站点在阿里云，所有分发到朵朵云和京东云、抖店云
                if (CustomerConfig.CloudPlatformType != PlatformType.Alibaba.ToString() && !string.IsNullOrEmpty(CustomerConfig.AlibabaFenFaSystemUrl))
                {
                    SendToOtherCloudPlatform(aliCloudHost, setting);
                }
                if (CustomerConfig.CloudPlatformType != PlatformType.Pinduoduo.ToString() && !string.IsNullOrEmpty(CustomerConfig.PinduoduoFenFaSystemUrl))
                {
                    SendToOtherCloudPlatform(pddCloudHost, setting);
                }
                if (CustomerConfig.CloudPlatformType != PlatformType.TouTiao.ToString() && !string.IsNullOrEmpty(CustomerConfig.ToutiaoFenFaSystemUrl))
                {
                    SendToOtherCloudPlatform(ttCloudHost, setting);
                }
                if (CustomerConfig.CloudPlatformType != PlatformType.Jingdong.ToString() && !string.IsNullOrEmpty(CustomerConfig.JingdongFenFaSystemUrl))
                {
                    SendToOtherCloudPlatform(jdCloudHost, setting);
                }

                //var jdApiResult = SendToOtherCloudPt(jdCloudHost, setting);
            }

            if (settingKey == "/JingdongPurchase/DeliveryMode")
            {
                //京东供销平台的发货回传模式变更，需要记录日志
                int fxUserId = SiteContext.CurrentNoThrow.CurrentFxUserId;
                var subFxUserId = SiteContext.GetSubFxUserId();
                var log = new DeliveryModeChangeLog()
                {
                    FxUserId = fxUserId,
                    SubFxUserId = subFxUserId,
                    Value = settingValue,
                };
                deliveryModeChangeLogService.Add(log);
            }
            if (result > 0)
            {
                return SuccessResult();
            }
            else
            {
                return FalidResult("保存配置失败");
            }
        }

        /// <summary>
        /// 获取发货回传模式变更日志
        /// </summary>
        /// <returns></returns>
        public ActionResult GetDeliveryModeChangeLog()
        {
            int fxUserId = SiteContext.CurrentNoThrow.CurrentFxUserId;
            var res = deliveryModeChangeLogService.GetList(fxUserId);
            return SuccessResult(res);
        }

        /// <summary>
        /// 设置是否导出订单明细
        /// </summary>
        /// <returns></returns>
        public ActionResult SetIsOutOrderDetailSetting(string value)
        {
            if (value.IsNullOrEmpty() || (!value.Equals("0") && !value.Equals("1")))
            {
                return FalidResult("设置是否导出订单明细失败", value);
            }
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var key = $"/FxSystem/OutAccountExtSettings/IsOutOrderDetail/{fxUserId}";
            try
            {
                return SaveCommonSetting(key, value);
            }
            catch (Exception ex)
            {
                Utility.Log.WriteError($"设置是否导出账单明细配置异常，{ex}");
                return FalidResult("设置是否导出账单明细配置异常", ex.ToJson());
            }
        }

        public ActionResult SaveNowTimeCommonSetting(string settingKey, string settingValue)
        {
            //判空处理
            if (string.IsNullOrWhiteSpace(settingKey))
            {
                return FalidResult("保存配置失败"); 
            }
            //检测配置键是否存在非法字符
            if (ValidCommonSettingKeyContainsIllegalCharacters(settingKey))
            {
                return FalidResult("保存配置失败");
            }

            //TODO:ShopId 待 授权做好之后,再修改为授权店铺
            var shopId = SiteContext.Current.CurrentShopId;
            var date = DateTime.Now;
            if (settingValue.IsNotNullOrEmpty() && !DateTime.TryParse(settingValue, out date))
                throw new LogicException("时间格式无法识别");

            var dateStr = date.Format();
            var result = commonSettingService.Set(settingKey, dateStr, shopId);
            if (result > 0)
            {
                // 精选云-京东云，清理缓存
                if (CustomerConfig.CloudPlatformType == PlatformType.Alibaba.ToString() || CustomerConfig.CloudPlatformType == PlatformType.Jingdong.ToString())
                {
                    FxCaching.RemoveRemoteChaching(settingKey);
                }

                return SuccessResult(dateStr);
            }
            else
                return FalidResult("保存配置失败");
        }

        /// <summary>
        /// 加载打印机绑定数据
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadPrinterBind()
        {
            var printerBindList = printerBindService.Get(" WHERE ShopId IN @ShopIds", new { ShopIds = SiteContext.Current.ShopIds });
            return Json(printerBindList);
        }

        /// <summary>
        /// 绑定打印机
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult BindPrinter(PrinterBind model)
        {
            if (model == null || model.TemplateId == 0 || model.TemplateType == 0 || string.IsNullOrWhiteSpace(model.PrinterName))
            {
                return FalidResult("请求参数错误.");
            }
            else
            {
                model.ShopId = SiteContext.Current.CurrentShopId;
                printerBindService.Delete("WHERE ShopId=@ShopId AND TemplateId=@TemplateId AND TemplateType=@TemplateType", new
                {
                    ShopId = model.ShopId,
                    TemplateId = model.TemplateId,
                    TemplateType = model.TemplateType
                });
                try
                {
                    int id = printerBindService.Add(model);
                    model.Id = id;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"绑定打印机异常：{ex}");
                }
            }
            return SuccessResult(model);
        }

        public ActionResult moreFunLeftNav()
        {
            return PartialView();
        }

        public ActionResult LoadShopBind()
        {
            var shops = SiteContext.Current.AllShops;
            return SuccessResult(shops);
        }

        /// <summary>
        /// 查询店铺信息作为手工同步选择
        /// </summary>
        /// <returns></returns>
        //public ActionResult GetShopsForManualSync()
        //{
        //    // 过滤线下单店铺
        //    var filterPlats = new List<string>() { "Virtual", "System"}; // , "OwnShop" 
        //    // 查询已绑定的店铺
        //    var reqModel = new FxUserShopQueryModel { FxUserId = SiteContext.GetCurrentFxUserId(),Status = FxUserShopStatus.Binded};
        //    var bindShops = new FxUserShopService().GetList(reqModel)?.Item2 ?? new List<FxUserShop>();
        //    // 过滤线下单店铺、授权过期店铺
        //    bindShops = bindShops.Where(s => !filterPlats.Contains(s.PlatformType) && !s.IsAuthExpired).ToList();
        //    if (bindShops.IsNullOrEmptyList())
        //        return SuccessResult(new List<Shop>());

        //    Log.Debug(() => $"GetShopsForManualSync，bindShops:{bindShops.Select(s=>s.ShopId).ToJson()}", ModuleFileName.ManualSyncOrder);

        //    // 获取店铺最新服务到期状态
        //    var shopIds = bindShops.Select(s => s.ShopId).Distinct().ToList();
            
        //    var fxShops = new ShopService("all").GetShopsAndShopExtensionFunc(shopIds, false, false);
        //    // 过滤授权过期、服务到期店铺
        //    fxShops = fxShops.Where(s => s.PayUrl.IsNotNullOrEmpty() &&(s.ShopExtension?.ExpireTime > DateTime.Now || s.ExpireTime > DateTime.Now) )
        //        .ToList();

        //    Log.Debug(() => $"GetShopsForManualSync，fxShops:{fxShops.Select(s=>s.Id).ToJson()}", ModuleFileName.ManualSyncOrder);

        //    return SuccessResult(fxShops.Select(s => new {s.Id, s.NickName, s.PlatformType, s.IsAuthExpired, IsServiceEnd = false, s.CreateTime })
        //        .OrderByDescending(x => x.CreateTime).ToList());
        //}

        public ActionResult LoadShopBindEqualType()
        {
            Shop _shops = SiteContext.Current.CurrentLoginShop;
            var shops = SiteContext.Current.AllShops.Where(m => m.PlatformType == _shops.PlatformType).ToList();
            return SuccessResult(shops);
        }

        public ActionResult LoadOrderCategoryBind()
        {
            var categorys = _orderCategory.Get().ToList();
            return SuccessResult(categorys);
        }

        public ActionResult SaveFile(string fileDirectory)
        {
            //Thread.Sleep(TimeSpan.FromSeconds(1));

            if (string.IsNullOrWhiteSpace(fileDirectory))
            {
                //return FalidResult("没有指定文件保存路劲");
                fileDirectory = "Files/Temp";
            }
            var path = $"/{fileDirectory.TrimStart('/').TrimEnd('/')}/";
            var directory = Server.MapPath("~" + path);
            if (System.IO.Directory.Exists(directory) == false)
            {
                System.IO.Directory.CreateDirectory(directory);
            }

            if (Request.Files.Count > 0)
            {
                var file = Request.Files[0];
                if (file.FileName.EndsWith(".exe") == true)
                {
                    //return FalidResult("不能上传.exe文件");

                    return Content((new AjaxResult()
                    {
                        Success = false,
                        Message = "不能上传.exe文件"
                    }).ToJson());
                }
                var fileName = (DateTime.Now.ToString("yyyyMMddHHmmssfff") + file.FileName);
                file.SaveAs(directory + fileName);
                //return SuccessResult("文件上传成功");                
                //return SuccessResult(path + fileName);
                return Content((new AjaxResult()
                {
                    Success = true,
                    Data = path + fileName
                }).ToJson());
            }
            else
            {
                //return FalidResult("未读取到文件");

                return Content((new AjaxResult()
                {
                    Success = false,
                    Message = "未读取到文件"
                }).ToJson());
            }
        }

        /// <summary>
        /// 图片上传
        /// </summary>
        /// <param name="fileName">文件名称，必填，需包含后缀</param>
        /// <param name="fileContent">文件内容（base64编码）</param>
        /// <returns>Z</returns>
        [ValidateInput(false)]
        public ActionResult UploadImage(string fileName, string fileContent)
        {
            var rspJson = string.Empty;
            var imageServer = CustomerConfig.ImageServer;
            var imageServerOut = CustomerConfig.ImageServerOut;
            if (CustomerConfig.CloudPlatformType == PlatformType.TouTiao.ToString())
            {
                //抖店直接使用阿里的图片服务器
                imageServer = CustomerConfig.AlibabaImageServer;
                imageServerOut = CustomerConfig.AlibabaImageServer;
            }
            try
            {
                var para = new Dictionary<string, string> { { "fileName", fileName }, { "fileContent", fileContent }, { "memberId", SiteContext.Current.CurrentLoginShop.ShopId }, { "key", DES.EncryptDES(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "sda98yp3") } };
                rspJson = WebRequestHelper.PostRequest(imageServer + "/Image.ashx?FuncName=Upload&key=debug", para, "UTF-8");
            }
            catch (Exception ex)
            {
                Log.WriteError("上传图片时发生错误：" + ex.ToString());
                return OriginalJson(new { IsOk = false, Message = "图片服务器暂不可用，请使用图片链接" });
            }

            var result = rspJson.ToObject<dynamic>();
            if (result.IsOk == true)
            {
                return OriginalJson(new { IsOk = result.IsOk, Message = result.Message, Data = imageServerOut + result.Data });
            }
            else
            {
                return OriginalJson(new { IsOk = result.IsOk, Message = result.Message });
            }
        }


        /// <summary>
        /// 文件上传 到 文件服务器
        /// </summary>
        /// <param name="fileName">文件名称，必填，需包含后缀</param>
        /// <param name="fileContent">文件内容（base64编码）</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult UploadFile()
        {
            if (Request.Files.Count == 0 || Request.Files["upfile"] == null)
                return FalidResult("没有选择文件上传");
            try
            {
                var postedFile = Request.Files["upfile"];//获取上传的文件
                string fileName = postedFile.FileName;

                //文件格式校验
                var notSupportFileExt = new List<string> { ".exe", ".js", ".vbs" };
                foreach (var ext in notSupportFileExt)
                {
                    if (fileName.EndsWith(ext))
                    {
                        return FalidResult($"不允许上传{ext}的文件");
                    }
                }

                //文件大小校验
                if (postedFile.ContentLength == 0)
                {
                    return FalidResult("请勿导入空文件");
                }
                var maxSize = Request.Params["MaxSize"].ToInt();
                if (maxSize > 0)
                {
                    var size = postedFile.ContentLength * 1.0 / 1024 / 1024;
                    if (size > maxSize)
                    {
                        return FalidResult($"导入文件请不要超过{maxSize}M");
                    }
                }

                byte[] bs = new byte[postedFile.InputStream.Length];
                var length = Convert.ToInt32(postedFile.InputStream.Length);
                postedFile.InputStream.Position = 0;
                //var pageSize = 100000;
                //var count = (int)Math.Ceiling(postedFile.InputStream.Length / (double)pageSize);
                postedFile.InputStream.Read(bs, 0, length);

                var fileContentBase64 = Convert.ToBase64String(bs);

                var para = new Dictionary<string, string> { { "fileName", fileName }, { "fileContent", fileContentBase64 }, { "memberId", SiteContext.Current.CurrentLoginShop.ShopId }, { "key", DES.EncryptDES(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "sda98yp3") } };

                //上传到文件服务器
                var rspJson = WebRequestHelper.PostRequest(CustomerConfig.ImageServer + "/Image.ashx?FuncName=Export&key=debug", para, "UTF-8");

                var result = rspJson.ToObject<dynamic>();
                if (result.IsOk == false)
                    return FalidResult(result.Message);
                else
                    return SuccessResult(result.Data);

            }
            catch (Exception ex)
            {
                var errorMsg = "文件上传到文件服务器失败：" + ex.Message;
                Log.WriteError(errorMsg);
                return FalidResult(errorMsg);
            }
        }

        /// <summary>
        ///  文件服务器上的文件下载
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        [LogForOperatorFilter("文件下载")]
        public string Download(string filePath, string fileName)
        {
            var logInfo = LogForOperatorContext.Current?.logInfo;
            if (logInfo != null)
            {
                logInfo.Request = fileName;
            }
            System.IO.Stream iStream = null;
            try
            {
                var taskId = Request["taskId"].ToInt();
                if (taskId == 0)
                    return "数据下载异常(0)";
                var task = new ShareWaybillAccountCheckingRecordService().Get(taskId);
                if (task == null)
                    return "数据下载异常(1)";
                else if (task.ShopId != SiteContext.Current.CurrentShopId)
                    return "数据下载异常(2)";
                if (task.MatchedDoneFile?.Trim() != filePath?.Trim())
                    return "数据下载异常(3)";

                System.Net.WebClient client = new System.Net.WebClient();
                var httpPath = CustomerConfig.ImageServer + filePath;
                byte[] data = client.DownloadData(httpPath);

                var downloadToken = Request.Form["downloadToken"].ToString2();
                Response.Cookies.Add(new HttpCookie("downloadToken", downloadToken));
                Response.ContentType = "application/octet-stream";
                Response.AddHeader("Content-Disposition", "attachment; filename=" + System.Web.HttpUtility.UrlEncode(fileName));//System.Text.UTF8Encoding.UTF8.GetBytes(FileName)
                Response.OutputStream.Write(data, 0, data.Length);
                Response.Flush();
                //return Content(downloadToken);                                                             
            }
            catch (Exception ex)
            {
                if (logInfo != null)
                {
                    logInfo.Response = ex.Message;
                }
                Response.Cookies.Add(new HttpCookie("downloadToken", "error"));
                string message = ex.Message;
                //return Content("error");
                return "<script>alert('Error : " + message + "');</script>";
            }
            finally
            {
                if (iStream != null)
                {
                    iStream.Close();
                }
            }
            if (logInfo != null)
            {
                logInfo.Response = "下载成功";
            }
            //return Content("");
            return string.Empty;
        }

        [FxAuthorize(AuthorizeType.DownloadFile)]
        [LogForOperatorFilter("文件下载方式2")]
        public ActionResult Download2()
        {
            var fileName = Request["fileName"].ToString2();
            var filePath = Request["filePath"].ToString2();
            var taskId = Request["taskId"].ToInt();
            var fileFrom = Request["fileFrom"].ToString2();
            var httpPath = CustomerConfig.ImageServer + filePath;
            //var httpPath = "http://localhost:80" + filePath;
            byte[] data = new byte[] { };
            var fileContent = string.Empty;

            //文件名过滤特殊字符
            fileName.Replace("*", "X");
            fileName = ExcelHelper.RemoveChar(fileName);
            if (string.IsNullOrEmpty(fileName))
            {
                int lastDotIndex = filePath.LastIndexOf(".");
                var fileExition = "";
                //获取文件后缀名
                if (lastDotIndex > -1)
                {
                    fileExition = filePath.Substring(lastDotIndex, filePath.Length - lastDotIndex);
                }
                fileName = $"file-{DateTime.Now.ToString("yyyyMMddHHmmss")}{fileExition}";
            }

            var _taskService = new ExportTaskService();
            ExportTask task = null;

            var logInfo = LogForOperatorContext.Current?.logInfo;
            try
            {
                if (taskId == 0)
                    return Content("数据下载异常(0)");

                //下载分单对账账单文件
                if (fileFrom?.ToLower() == "settlement")
                {
                    var settlementBillModel = new SettlementBillService().Get(taskId);
                    if (settlementBillModel == null
                        || (settlementBillModel.FxUserId != SiteContext.Current.CurrentFxUserId && settlementBillModel.CreateUser != SiteContext.Current.CurrentFxUserId)
                        || (settlementBillModel.FilePath?.Trim() != filePath?.Trim() && settlementBillModel.FilePath2?.Trim() != filePath?.Trim()))
                        return Content($"账单不存在");
                }
                else
                {
                    task = _taskService.Get(taskId);

                    if (task == null)
                        return Content("数据下载异常(1)");
                    else if (task.ShopId != SiteContext.Current.CurrentShopId)
                        return Content("数据下载异常(2)");
                    if (task.FilePath?.Trim() != filePath?.Trim())
                        return Content("数据下载异常(3)");
                }

                System.Net.WebClient client = new System.Net.WebClient();
                data = client.DownloadData(httpPath);

                if (filePath.EndsWith(".json") && data.Length > 0)
                {
                    //忽略BOM头将其转string 
                    fileContent = data.ToUTF8String();
                    if (!fileContent.IsNullOrEmpty() && task != null && (task.Type == ExportType.ExpressBill.ToInt() || task.Type == ExportType.ErpExpressBill.ToInt()))
                    {
                        var showType = Request["showType"].ToString2();
                        var returnModel = new ExpressBillReturnModel();
                        var expressBillViewModel = fileContent.ToObject<ExpressBillViewModel>();

                        switch (showType)
                        {
                            case "use_list":
                                returnModel.List = expressBillViewModel?.UseList ?? new List<ExpressWaybillCodeModel>();
                                break;
                            case "check_list":
                                returnModel.List = expressBillViewModel?.CheckList ?? new List<ExpressWaybillCodeModel>();
                                break;
                            case "found_list":
                                returnModel.List = expressBillViewModel?.FoundList ?? new List<ExpressWaybillCodeModel>();
                                break;
                            case "not_found_list":
                                returnModel.List = expressBillViewModel?.NotFoundList ?? new List<ExpressWaybillCodeModel>();
                                break;
                            case "recycle_list":
                                returnModel.List = expressBillViewModel?.RecycleList ?? new List<ExpressWaybillCodeModel>();
                                break;
                            case "repeat_list":
                                returnModel.List = expressBillViewModel?.RepeatList ?? new List<ExpressWaybillCodeModel>();
                                break;
                            default:
                                returnModel.List = expressBillViewModel?.CheckList ?? new List<ExpressWaybillCodeModel>();
                                break;
                        }
                        #region 收件人信息脱敏

                        //EncryptionService DataMaskservice = new EncryptionService();
                        //var noVirtualOrders = DataMaskservice.getPlatformType(returnModel.List);
                        //EncryptionService.DataMaskingExpression(returnModel.List.Where(w => noVirtualOrders.Any(a => a.LogicOrderId == w.OrderId))?.ToList());
                        var notEncryptOrders = returnModel.List.Where(x => x.PlatformType.IsNullOrEmpty() || x.PlatformType == PlatformType.WeiDian.ToString() || x.PlatformType == PlatformType.WxVideo.ToString() || (FxPlatformEncryptService.encryptPlatformTypes.Contains(x.PlatformType.ToString2()) == false && x.PlatformType != PlatformType.Virtual.ToString())).ToList();
                        notEncryptOrders = notEncryptOrders.Where(f => f.PlatformType != PlatformType.Virtual.ToString()).ToList(); //排除线下单
                        if (notEncryptOrders.Any())
                            EncryptionService.DataMaskingExpression(notEncryptOrders);//EncryptionService.DataMaskingReflection(pageModel.Rows);

                        #endregion

                        returnModel.CurrentShowType = showType.IsNullOrEmpty() ? "check_list" : showType;
                        returnModel.UseCount = expressBillViewModel?.UseList?.Count ?? 0;
                        returnModel.CheckCount = expressBillViewModel?.CheckList?.Count ?? 0;
                        returnModel.FoundCount = expressBillViewModel?.FoundList?.Count ?? 0;
                        returnModel.NotFoundCount = expressBillViewModel?.NotFoundList?.Count ?? 0;
                        returnModel.RecycleCount = expressBillViewModel?.RecycleList?.Count ?? 0;
                        returnModel.RepeatCount = expressBillViewModel?.RepeatList?.Count ?? 0;
                        fileContent = returnModel.ToJson();
                    }
                }
                else
                    fileName = ExcelHelper.GetFileName(fileName, Request);
            }
            catch (Exception ex)
            {
                if (logInfo != null)
                    logInfo.Response = ex.Message;
                return FalidResult($"账单获取失败：{ex.Message}");
            }

            if (logInfo != null)
                logInfo.Response = "下载成功";
            if (task != null)
            {
                task.Status = 5;
                _taskService.UpdateStatus(task, false);
            }

            if (filePath.IndexOf(".xlsx") >= 0)
                return File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            else if (filePath.IndexOf(".xls") >= 0)
                return File(data, "application/ms-excel", fileName);
            else if (filePath.IndexOf(".json") >= 0)
                return SuccessResult(fileContent);
            else
                return FalidResult("未知文件类型");
        }

        public ActionResult LoadPrintSerialNumber()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var setting = commonSettingService.Get(CommonSettingService.PrintSerialSet, shopId);
            return SuccessResult(setting);
        }

        public ActionResult ClearSerialNumber()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            PrintSerialSetting printSerialSetting = _commonSettingService.GetPrintSerialSetting(shopId);
            if (printSerialSetting != null)
            {
                var clearFormat = printSerialSetting.ClearSet.ToString2();
                if (!clearFormat.IsNullOrEmpty())
                {
                    var key = DateTime.Now.ToString(clearFormat);
                    var value = printSerialSetting.PrintSerialValues.FirstOrDefault(m => m.DateTime == key);
                    if (value != null)
                    {
                        value.SerialNum = 0;
                    }
                    var printSerialSettingJson = JsonExtension.ToJson(printSerialSetting);
                    _commonSettingService.UpdatePrintSerialNumber(printSerialSettingJson, shopId);
                }
            }
            return SuccessResult();
        }

        /// <summary>
        /// 地址识别日志
        /// </summary>
        /// <param name="addr"></param>
        /// <param name="discernResult"></param>
        /// <returns></returns>
        [LogForOperatorFilter("地址识别")]
        public ActionResult AddressDiscernLog(string addr, bool isDiscernName, string discernResult)
        {
            var result = discernResult.ToObject<dynamic>();

            var loginfo = LogForOperatorContext.Current.logInfo;
            try
            {
                loginfo.TotalCount = 1;
                loginfo.Remark = addr; //识别的地址
                loginfo.Detail = discernResult; //识别结果
                if (result.isSuccess == false)
                {
                    loginfo.IsError = true;
                    //loginfo.Exception = discernResult;
                }
                else if (result.needConfirm == true)
                {
                    loginfo.SuccessCount = 1; //查询 SuccessCount==1为成功识别的
                    loginfo.Detail = "识别成功需要确认：" + loginfo.Detail;
                }
                else
                {
                    loginfo.SuccessCount = 1;//查询 SuccessCount==1为成功识别的
                }
            }
            catch (Exception ex)
            {
                loginfo.Exception = ex.ToString();
            }

            return SuccessResult();
        }

        /// <summary>
        /// 订单打印，前端错误日志
        /// </summary>
        /// <param name="addr"></param>
        /// <param name="isDiscernName"></param>
        /// <param name="discernResult"></param>
        /// <returns></returns>
        [LogForOperatorFilter]
        //[IgnoreToken]
        public ActionResult JavasScriptExcptionLog(string operatorType, string exception)
        {
            var loginfo = LogForOperatorContext.Current.logInfo;
            loginfo.OperatorType = operatorType;
            loginfo.Exception = exception;

            return SuccessResult();
        }

        /// <summary>
        /// 前端日志
        /// </summary>
        /// <param name="addr"></param>
        /// <param name="isDiscernName"></param>
        /// <param name="discernResult"></param>
        /// <returns></returns>
        [LogForOperatorFilter]
        //[IgnoreToken]
        public ActionResult JavasScriptLog(string operatorType, string logJson)
        {
            var loginfo = LogForOperatorContext.Current.logInfo;
            loginfo.OperatorType = operatorType;
            //loginfo.Remark = logJson;

            return SuccessResult();
        }


        public ActionResult TestPage()
        {
            return View();
        }

        public ActionResult TestBatchAjax(int requestNumber, List<int> datas)
        {

            if ((new int[] { 25, 18, 38 }).Contains(requestNumber))
            {
                throw new Exception("后台发生错误...");
            }

            if (requestNumber < 10)
            {
                Thread.Sleep(TimeSpan.FromMinutes(3));
                return SuccessResult(new { Sleep = 0, RequestNumber = requestNumber, Data = datas });
            }

            if (datas.Last() > 50)
            {
                int x = (new Random().Next(1, 6)) * 1000;

                Thread.Sleep(x);
                return SuccessResult(new { Sleep = x, RequestNumber = requestNumber, Data = datas });
            }

            int s = (new Random().Next(1, 5)) * 1000;
            Thread.Sleep(s);
            return SuccessResult(new { Sleep = s, RequestNumber = requestNumber, Data = datas });


        }

        [HttpPost]
        public ActionResult ReAuthorization(int shopId, string appKey, string appSecret)
        {
            var shop = SiteContext.Current.CurrentLoginShop;
            if (shop == null)
                return FalidResult("未登陆店铺");

            if (shopId != 0)
            {
                shop = SiteContext.Current.AllShops.FirstOrDefault(m => m.Id == shopId);
                if (shop == null)
                    return FalidResult($"店铺【{shopId}】不存在");
            }

            if (appKey.IsNullOrEmpty() || appSecret.IsNullOrEmpty())
                return FalidResult("AppKey和AppSecret都为必填项");

            var oldAppKey = shop.AccessToken;
            var oldAppSecret = shop.RefreshToken;

            ShopService _shopService = new ShopService();
            shop.AccessToken = appKey;
            shop.RefreshToken = appSecret;
            shop.ShopId = shop.ShopId.IsNullOrEmpty() ? appKey : shop.ShopId;
            shop.LastSyncMessage = $"已重新授权，AppKey【{oldAppKey}->{appKey}】,AppSecret【{oldAppSecret}->{appSecret}】";
            var result = _shopService.Update(shop);
            return SuccessResult(result);
        }

        #region 通用iframe页面

        public ActionResult Page(string id)
        {
            //从外部POST过来的pids，附加到目标url后
            var pids = Request.Form["pids"] + "";
            var loids = Request.Form["loids"] + "";
            var orderfrom = Request.Form["orderfrom"] + "";
            var url = id?.Replace('-', '/');
            if (string.IsNullOrEmpty(url))
                url = "/NewOrder/AllOrder";
            else
                url = "/" + url.Trim('/', '?');
            //ViewBag.IFrameSrc = url + "?" + Request.QueryString;
            //TempData["pids"] = (string.IsNullOrWhiteSpace(pids) ? "" : pids);
            //TempData["loids"] = (string.IsNullOrWhiteSpace(loids) ? "" : loids);
            //TempData["orderfrom"] = (string.IsNullOrWhiteSpace(orderfrom) ? "" : orderfrom);
            ViewBag.IFrameSrc = url + "?" + Request.QueryString + (string.IsNullOrWhiteSpace(pids) ? "" : "&pids=" + pids) + (string.IsNullOrWhiteSpace(loids) ? "" : "&loids=" + loids) + (string.IsNullOrWhiteSpace(orderfrom) ? "" : "&orderfrom=" + orderfrom);
            var menuId = url?.EndsWith("Index", StringComparison.OrdinalIgnoreCase) == true ? url?.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries).FirstOrDefault() : url?.Split(new[] { '/' }, StringSplitOptions.RemoveEmptyEntries).LastOrDefault();
            var type = Request["type"]?.ToString();

            var menuActive = "OrderManagement";
            if (menuId.Contains("Product"))
                menuActive = "ProductManagement";
            if (menuId.Contains("WaybillCodeList") && type == "cj")
                menuActive = "SettlementAccount";
            if (menuId.Contains("SendOrder"))
                menuActive = "SettlementAccount";
            if (menuId == "AfterSale" && (Request["fromUrl"] + "").ToLower() == "aliincludepayorder")
            {
                menuId = "AliIncludePayOrder";
                menuActive = "AliIncludePayOrder";
            }
            else if (menuId == "AfterSale" && (Request["fromUrl"] + "").ToLower() == "aliincludeorder")
            {
                menuId = "AliIncludeOrder";
                menuActive = "AliIncludeOrder";
            }

            if (menuId == "AfterSale" || menuId == "ManualAfterSale")
                menuActive = "AfterSaleManagement";
            if (menuId == "AliIncludePayOrder")
                menuActive = "SupplySet1688";


            if (string.IsNullOrEmpty(type) == false)
                menuId += "_" + type;
            ViewBag.MenuId = menuId;
            ViewBag.MenuActive = menuActive;

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //已全部迁移，不需要再兼容判断 2023-10-12
            //var fxDbConfig = new FxDbConfigService().GetByFxUserId(fxUserId, CloudPlatformType.TouTiao.ToString());
            //ViewBag.ShowTouTiaoCloud = fxDbConfig == null ? "0" : "1";

            //获取商家的分库情况
            var agentDbs = DbPolicyExtension.GetConfigFxSupplierNames(fxUserId);
            var agentDbStr = agentDbs.Item1.Select(x => new { DbName = DES.EncryptDES(x.Key, CustomerConfig.LoginCookieEncryptKey), Agents = x.Value.Select(a => a.ShowName) }).ToJson();
            ViewBag.AgentDb = RemoveDbNameSpecialChar(agentDbStr);
            ViewBag.AgentDbCloud = agentDbs.Item2.Select(x => new { DbCloudName = x.Key, Agents = x.Value.Select(a => a.ShowName) }).ToJson();
            ViewBag.IsOrderLifeCycleRigth = new OrderLifeCycleDetectionService().IsOrderLifeCycleRight();

            var curDbName = string.Empty;
            try
            {
                var tutles = new FxUserShopService().GetList(new FxUserShopQueryModel()
                {
                    FxUserId = SiteContext.Current.CurrentFxUserId,
                    PlatformType = PlatformType.TaobaoMaiCai.ToString()
                });
                var tbmcShops = tutles?.Item2?.ToDictionary(d => d.ShopId, d => d.NickName);
                //tbmcShops.Add(16132, "Test");
                ViewBag.TbmcShops = tbmcShops.ToJson();

                if (!tbmcShops.IsNullOrEmptyList())
                {

                    curDbName = DbPolicyExtension.GetConfigFx(new List<int>() { SiteContext.GetCurrentShopId() })
                        .OrderByDescending(a => a.DbConfig.FromFxDbConfig)
                        .FirstOrDefault()
                        .DbNameConfig.DbName;

                    curDbName = DES.EncryptDES(curDbName, CustomerConfig.LoginCookieEncryptKey);

                }

            }
            catch { };

            ViewBag.SysShopDbName = curDbName;
            //显示跨境功能
            var notDisplayCroosBorderUrls = new List<string>() {
                "NewOrder-AliIncludePayOrder",  //待付款列表
                //"AfterSale-ManualAfterSale",    //手工售后单
                "AfterSale-Index"               //平台售后单
            };
            if (string.IsNullOrWhiteSpace(id) || notDisplayCroosBorderUrls.Contains(id))
            {
                ViewBag.IsShowCrossBorder = false; //不显示跨境功能
            }

            //if (SiteContext.Current.IsShowCrossBorder
            //    && !string.IsNullOrWhiteSpace(id)
            //    && !notDisplayCroosBorderUrls.Contains(id))
            //{
            //    //初始化tiktok业务库
            //    var fxDbConfig = new FxDbConfig
            //    {
            //        FxUserId = SiteContext.Current.CurrentFxUserId,
            //        SystemShopId = SiteContext.Current.CurrentShopId,
            //        DbCloudPlatform = CloudPlatformType.ChinaAliyun.ToString(),
            //        FromFxDbConfig = 1,
            //        Status = "authshop"
            //    };
            //    new FxDbConfigService().TryToCreateCloudFxDbConfig(new List<FxDbConfig> { fxDbConfig },
            //        new List<string> { CloudPlatformType.ChinaAliyun.ToString() });
            //}

            return View();
        }

        #endregion

        /// <summary>
        /// 获取分区
        /// </summary>
        /// <param name="cloudPlatformType"></param>
        /// <returns></returns>
        [LogForOperatorFilter("获取分区")]
        public ActionResult GetDbAreas(string cloudPlatformType)
        {
            //同云
            var siteCloudPlatformType = CustomerConfig.CloudPlatformType;
            if (cloudPlatformType.ToLower() == siteCloudPlatformType.ToLower())
            {
                return GetDbAreasApi(cloudPlatformType);
            }
            //将请求发送到相应站点触发同步
            var wu = new WebUtils(15000);
            if (CustomerConfig.IsDebug)
            {
                wu = new WebUtils(120000);
            }
            var headers = new Dictionary<string, string>
            {
                { "User-Agent", Request.Headers["User-Agent"] },
                { "X-Requested-With", "XMLHttpRequest" }
            };
            //分销云平台地址
            var url = GetFenDanCloudPlatformUrl(cloudPlatformType);
            var apiUrl = url.TrimEnd("/") +
                         $"/Common/GetDbAreasApi?token={Request["token"]}&cloudPlatformType={cloudPlatformType}";
            var result = wu.DoPost(apiUrl,
                new Dictionary<string, string> { { "timestamp", DateTime.Now.ToTicks().ToString() } },
                headers: headers);
            return new CustomJsonResult(result.ToObject<AjaxResult>());
        }
        /// <summary>
        /// 获取数据分区API
        /// </summary>
        /// <param name="cloudPlatformType"></param>
        /// <returns></returns>
        [IgnoreDoubleAuth]
        public ActionResult GetDbAreasApi(string cloudPlatformType)
        {
            if (cloudPlatformType == null)
                cloudPlatformType = Request["cloudPlatformType"];
            //站点云平台
            var siteCloudPlatformType = CustomerConfig.CloudPlatformType;
            if (cloudPlatformType?.ToLower() == siteCloudPlatformType.ToLower())
            {
                //分区
                var dbAreas = SiteContext.Current.CurrentDbAreaConfig.Select(x => x.DbNameConfig).OrderBy(x => x.Id)
                    .Select(y => new
                    {
                        DbName = DES.EncryptDES(y.DbName, CustomerConfig.LoginCookieEncryptKey),
                        y.NickName,
                        y.ApplicationName
                    }).ToList();
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                //获取商家的分库情况
                var agentDbs = DbPolicyExtension.GetConfigFxSupplierNames(fxUserId);
                var agentDbStr = agentDbs.Item1.Select(x => new
                {
                    DbName = DES.EncryptDES(x.Key, CustomerConfig.LoginCookieEncryptKey),
                    Agents = x.Value.Select(a => a.ShowName)
                }).ToJson();
                return Json(new { DbAreas = dbAreas.ToJson(), DbAgents = RemoveDbNameSpecialChar(agentDbStr) });
            }

            return SuccessResult($"获取数据分区信息时，平台不一致：{cloudPlatformType}!={siteCloudPlatformType}。");
        }

        public ActionResult ChangeVersion(bool isAuto)
        {
            var val = isAuto ? "0" : "1";
            var shopId = SiteContext.Current.CurrentShopId;
            int result = _commonSettingService.Set("/System/Config/Fendan/OldNavStyle", val, shopId);
            if (result > 0)
            {
                return SuccessResult("切换主题成功");
            }
            return FalidResult("切换主题失败");
        }

        public ActionResult SeveQuickSort(List<string> navIds)
        {
            var val = "";
            if (navIds != null && navIds.Any())
                val = string.Join(",", navIds);
            var shopId = SiteContext.Current.CurrentShopId;
            int result = _commonSettingService.Set("/System/Config/Fendan/NavQuickEntrySortNew", val, shopId);
            // 设为空时，删除旧排序数据
            if (val.IsNullOrEmpty())
            {
                _commonSettingService.Set("/System/Config/Fendan/NavQuickEntrySort", val, shopId);
            }
            if (result > 0)
            {
                return SuccessResult("保存成功");
            }
            return FalidResult("保存失败");
        }

        public ActionResult SeveFilterInvalidShopId(string idStr, bool isCheck)
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var val = _commonSettingService.Get("/System/Config/Fendan/FilterInvalidShopId", shopId)?.Value;
            if (string.IsNullOrWhiteSpace(idStr))
                val = "";
            if (!string.IsNullOrWhiteSpace(val))
            {
                var newSplit = idStr.Split(',');
                var oldSplit = val.Split(',');
                var union = newSplit.Union(oldSplit);
                idStr = string.Join(",", union);
            }
            int result = _commonSettingService.Set("/System/Config/Fendan/FilterInvalidShopId", idStr, shopId);
            if (result > 0)
            {
                return SuccessResult("操作成功");
            }
            return FalidResult("操作失败");
        }

        public ActionResult SetHelpAxis(string x, string y)
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var result = _commonSettingService.Set("DGJ/CustomerHelp/Point", $"{{\"x\":{x},\"y\":{y}}}", shopId);
            if (result > 0)
                return SuccessResult("保存成功");
            return FalidResult("保存失败");
        }

        public ActionResult GetHelpAxis()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var result = _commonSettingService.Get("DGJ/CustomerHelp/Point", shopId);
            if (result == null || result.Value == null)
                result = _commonSettingService.Get("DGJ/CustomerHelp/Point", 0);
            if (result == null || result.Value == null)
                result = new CommonSetting() { Value = "{\"x\":-100,\"y\":-50}" };
            return Content(result.Value);
        }

        public ActionResult PddPageCode()
        {
            var code = GetPddPageCode();
            return SuccessResult(new { PddPageCode = code });
        }

        public ActionResult IsWhiteFxUser()
        {
            try
            {
                var isWhiteUser = SiteContext.Current.IsWhiteUser;
                return isWhiteUser ? SuccessResult(true) : SuccessResult(false); // 白名单用户才可以导出这三个金额字段
            }
            catch (Exception ex)
            {
                return FalidResult($"获取白名单配置失败，{ex.Message}");
            }
        }

        public ActionResult IsReconVipUser()
        {
            try
            {
                var isWhiteUser = SiteContext.Current.IsReconVip;
                return isWhiteUser ? SuccessResult(true) : SuccessResult(false);
            }
            catch (Exception ex)
            {
                return FalidResult($"获取[对账中心大客版]白名单配置失败，{ex.Message}");
            }
        }

        /// <summary>
        /// 检测迁移情况
        /// </summary>
        /// <returns></returns>
        //[IgnoreToken]
        public ActionResult CheckMigrate()
        {
            //精选平台才处理
            return SuccessResult(new { IsTip = false });
            //if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
            //    return SuccessResult(new { IsTip = false });

            //this.ValidLoginToken();

            //var fxUserId = SiteContext.Current.CurrentFxUserId;
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            bool isUseRedis = !string.IsNullOrEmpty(CustomerConfig.ConfigRedisConnectionString);
            bool isTip = false;
            bool isSelf = false;
            var strAgentList = string.Empty;
            if (!isUseRedis)
            {
                return SuccessResult(new { IsTip = false });
            }

            var key = $"/FxSystem/MigratedAgents/{fxUserId}";
            if (RedisHelper.Exists(key))
            {
                var migratedFxUserIds = RedisHelper.Get(key).ToObject<List<int>>();
                isSelf = migratedFxUserIds.Any(a => a == fxUserId);
                isTip = true;

                //拿商家的名称
                if (!isSelf)
                {
                    var agents = new SupplierUserService().GetSupplierUserAndFxUserByFxIds(migratedFxUserIds, new List<int> { fxUserId });
                    strAgentList = string.Join(",", agents.Select(a => a.AgentMobileAndRemark).ToList());
                }
            }

            return SuccessResult(new { IsTip = isTip, IsSelf = isSelf, AgentList = strAgentList });
        }

        /// <summary>
        /// 移除迁移提示
        /// </summary>
        /// <returns></returns>
        public ActionResult RemoveMigrateTip()
        {
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                return SuccessResult();

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            bool isUseRedis = !string.IsNullOrEmpty(CustomerConfig.ConfigRedisConnectionString);
            if (!isUseRedis)
                return SuccessResult();

            var key = $"/FxSystem/MigratedAgents/{fxUserId}";
            if (RedisHelper.Exists(key))
                RedisHelper.Del(key);

            return SuccessResult();
        }
        /// <summary>
        /// 对象存储上传
        /// </summary>
        /// <param name="fileName">文件名称</param>
        /// <param name="fileContent">base64字符</param>
        /// <param name="bucketName">存储桶名称 不同的业务不同的存储桶</param>
        /// <param name="typeFlag">平台资料：1为主图  2为详情图  3为规格图</param>
        /// <returns></returns>
        public ActionResult UploadImageFile(string fileName, string fileContent, string businessType, int typeFlag = 0)
        {
            try
            {
                //注：各模块对应的存储桶
                //"AfterSale": "fx-aftersale",
                //"BaseProduct": "fx-baseproduct",
                //"Order": "fxali-order"
                var platformType = CustomerConfig.CloudPlatformType;
                if (string.IsNullOrWhiteSpace(businessType))
                    return OriginalJson(new { IsOk = false, Message = "上传图片时发生错误，未匹配业务类型" });
                //图片Base64字符
                byte[] imageBytes = Convert.FromBase64String(fileContent.Split(',')[1]);//去除图片前缀
                #region 这段是因为平台资料需要对相关的图片进行裁剪
                if (typeFlag > 0)
                {
                    imageBytes = new PtImgService().GetCutImgBytes(imageBytes, typeFlag);
                }
                #endregion
                var result = CloudStorageUploaderFactory.Uploader(platformType, businessType, SiteContext.Current.CurrentFxUserId.ToString(), fileName, imageBytes);
                if (result.Success)
                {
                    return OriginalJson(new
                    {
                        IsOk = result.Success,
                        Message = "上传成功",
                        Data = DES.EncryptUrl(result.Data, CustomerConfig.LoginCookieEncryptKey),
                        TransitUrl = ImgHelper.GetImgeTransitUrl(result.Data)
                    });
                }
                else
                    return OriginalJson(new { IsOk = result.Success, Message = "图片上传失败,请联系客服工作人员" });
            }
            catch (Exception ex)
            {
                Log.WriteError("上传图片时发生错误：" + ex.ToString(), "OssStorageLog.txt");
                return OriginalJson(new { IsOk = false, Message = "图片上传失败,请联系客服工作人员" });
            }
        }

        /// <summary>
        /// 获取当前线程Id
        /// </summary>
        public void GetProcessId()
        {
            if (CustomerConfig.IsDebug)
            {
                var processId = System.Diagnostics.Process.GetCurrentProcess().Id;
                Response.Write(processId);
            }
        }

        /// <summary>
        /// 对象存储获取图片文件
        /// </summary>
        /// <param name="objectKey">加密后的KEY</param>
        /// <param name="platform">请求平台</param>
        /// <param name="token"></param>
        /// <param name="businessType">业务类型</param>
        /// <returns></returns>
        [ValidateInput(false)]
        //[IgnoreToken]
        public ActionResult GetImageFile(string objectKey, string platform, string token, string businessType = null)
        {
            // 根据业务类型二次处理平台类型
            platform = CommUtls.ChangePlatformType(platform, businessType);
            //抖音：https://fx-aftersale.tos-cn-beijing.volces.com/3223efbc24c7d258/2a305a9dcfdec1e9.jpg
            //阿里：https://fx-aftersale.oss-cn-zhangjiakou.aliyuncs.com/3223efbc24c7d258/c2c5a77b6fac7b02.jpg
            //PDD： /fx-aftersale/3223efbc24c7d258/dee4d9e8eb66f0fb.jpg
            //注：历史兼容待确定
            var decryptObjectKey = string.Empty;
            // 如果当前站点平台为拼多多，且platform不为拼多多，则转发请求
            //非精选都走以下逻辑 2024.11.4
            if (CustomerConfig.CloudPlatformType != platform && businessType != "cloudplatform")
            {
                return GetImageByPdd(new ImageModel { ObjectKey = objectKey, Platform = platform, Token = token });
            }

            try
            {
                if (string.IsNullOrWhiteSpace(token)) return Content("Token验证失败！");
                var json = DES.DecryptUrl(token, CustomerConfig.LoginCookieEncryptKey);
                if (string.IsNullOrEmpty(json))
                    return Content("Token验证失败！");
                var authToken = new ShopService().GetToken(json.ToInt());
                if (authToken == null || authToken.FxUserId <= 0)
                    return Content("Token验证失败！");
                if (string.IsNullOrEmpty(platform)) { return null; }

                if (!string.IsNullOrWhiteSpace(objectKey))
                {
                    //解密
                    decryptObjectKey = DES.DecryptUrl(objectKey, CustomerConfig.LoginCookieEncryptKey);
                    var bytes = CloudStorageUploaderFactory.GetObjectStorage(platform, decryptObjectKey);
                    if (bytes == null)
                        return Content("未获取到相关资源");
                    string contentType = $"image/{decryptObjectKey.Substring(decryptObjectKey.LastIndexOf(".") + 1)}";
                    return File(bytes, contentType);
                }

                return Content("请求KEY不存在");
            }
            catch (Exception ex)
            {
                Log.WriteError("获取图片服务器异常：" + ex.Message, "OssStorageLog.txt");
                return Content(ex.Message);
            }
        }

        /// <summary>
        /// 由pdd发起获取图片
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult GetImageByPdd(ImageModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            // 转发请求
            try
            {
                const string apiUrl = "/BaseProductApi/GetImageByPdd";
                var targetSiteUrl = CommUtls.GetTargetSiteUrl(apiUrl);
                //Log.Debug($"跨云查询图片信息，targetSiteUrl={targetSiteUrl}，lastModel={model.ToJson()}");

                var result = Common.PostFxSiteApi<ImageModel, ImageResultModel>(targetSiteUrl,
                    fxUserId, model, "跨云查询图片信息", isEncrypt: true);

                if (result == null) return Content("未获取到相关资源");

                // 逗号前为图片类型，逗号后为图片Base64
                return File(result.Bytes, result.Type);
            }
            catch (Exception ex)
            {
                Log.WriteError($"跨云获取图片信息失败：{ex.Message}");
                return Content(ex.Message);
            }
        }

        /// <summary>
        /// 获取物流轨迹
        /// </summary>
        /// <param name="logisticsName">物流中文名</param>
        /// <param name="logisticsCode">物流编码</param>
        /// <param name="logisticsNo">物流单号</param>
        /// <returns>url</returns>
        public ActionResult GetLogisticsTrace()
        {
            try
            {
                string cacheKey = "LogisticsTraceRule";

                //读取物流配置,先查内存缓存
                var cache = HttpRuntime.Cache[cacheKey] as List<LogisticsTraceConfigModel>;
                if (cache != null)
                {
                    return SuccessResult(cache);
                }

                //内存缓存没有数据，查redis
                var commonSetting = commonSettingService.Get(cacheKey, 0);
                if (commonSetting == null)
                {
                    return FalidResult("配置为空");
                }

                //解析
                List<LogisticsTraceConfigModel> configList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<LogisticsTraceConfigModel>>(commonSetting.Value);
                if (configList == null || configList.Count == 0)
                {
                    return FalidResult("配置为空");
                }

                HttpRuntime.Cache.Insert(cacheKey, configList, null, DateTime.Now.AddMinutes(10), System.Web.Caching.Cache.NoSlidingExpiration);

                return SuccessResult(configList);
            }
            catch (Exception e)
            {
                Log.WriteError($"查询物流轨迹出现异常，错误信息：{e}", "GetLogisticsTrace.txt");
            }
            return FalidResult("查询失败");
        }

        /// <summary>
        /// 验证阿里云验证码
        /// </summary>
        /// <param name="captchaVerifyParam">验证参数</param>
        /// <returns></returns>
        public ActionResult VerifyCaptcha(string captchaVerifyParam)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            return new CommonPortalController().VerifyCaptcha(captchaVerifyParam, fxUserId);
        }
    }
}
