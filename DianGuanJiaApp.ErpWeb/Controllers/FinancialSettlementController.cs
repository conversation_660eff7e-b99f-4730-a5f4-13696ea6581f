using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.FxModel;
using NPOI.SS.Formula.Functions;
using DianGuanJiaApp.Utility.Web;
using System.Security.Policy;
using System.Drawing.Drawing2D;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Utility.NPOI;
using System.Data;
using NPOI.Util.Collections;
using System.Collections;
using DianGuanJiaApp.Models.FreePrintExpress;
using Newtonsoft.Json.Linq;
using System.Text;
using DianGuanJiaApp.Services.Services.MessageQueue;
using Jayrock.Json;
using System.Text.RegularExpressions;
using System.Web.Services.Description;
using System.Diagnostics.Metrics;
using Newtonsoft.Json;
using NPOI.SS.Formula.PTG;
using System.Web;
using DianGuanJiaApp.Services.Services.SubAccount;
using System.Diagnostics;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    public class FinancialSettlementController : BaseController
    {
        private ProductFxService _productFxService = new ProductFxService();
        private FinancialSettlementService _financialSettlementService = new FinancialSettlementService();
        private SupplierUserService _supplierUserService = new SupplierUserService();
        private SettlementBillService _settlementBillService = new SettlementBillService();
        private SettlementProductSkuService _settlementProductSkuService = new SettlementProductSkuService();
        private CommonSettingService _commonSettingService = new CommonSettingService();
        private FxUserShopService _fxUserShopService = new FxUserShopService();
        private ShopService _shopService = new ShopService();

        #region 对账设置
        /// <summary>
        /// 对账设置
        /// </summary>
        /// <returns></returns>
        public ActionResult PriceSetting()
        {
            //是否是白名单用户
            var isWhiteUser = SiteContext.Current.IsWhiteUser;
            ViewBag.IsWhiteUser = isWhiteUser;
            // 页面按钮展示权限
            ViewBag.ShowPermDict = new Dictionary<string, bool>
            {
                {$"#{nameof(FxPermission.QuickOutAccountFxUser)}",SiteContext.HasPermission(FxPermission.QuickOutAccountFxUser)},
            }.ToJson();
            return View();
        }

        /// <summary>
        /// 对账设置，我的厂家
        /// </summary>
        /// <returns></returns>
        public ActionResult PriceSetting_Supplier()
        {
            Dictionary<int, string> newStatusDic = new Dictionary<int, string>();
            foreach (var item in Enum.GetValues(typeof(AgentBingSupplierStatus)))
            {
                newStatusDic.Add((int)item, ((AgentBingSupplierStatus)item).GetEnumDescription());
            }
            var useLastTime = _commonSettingService.GetString("/ErpWeb/Settlement/UseLastTime", SiteContext.Current.CurrentShopId) ?? "false";
            var isWhite = SiteContext.Current.IsWhiteUser;
            ViewBag.IsWhiteUser = isWhite;
            ViewBag.Status = newStatusDic;
            ViewBag.UseLastTime = useLastTime;
            ViewBag.Shops = GetFxUserShops(isAllPlats: true).Select(x => new { x.NickName, x.ShopId, x.PlatformType }).ToJson();
            //当前用户是否存在跨境发货记录（自发或者代发)
            ViewBag.HasCrossBorderShop = _settlementBillService.IsExistCrossBorderOrderDelivered();   //new FxUserForeignShopService().GetShopsByFxUserId(SiteContext.GetCurrentFxUserId())?.IsNotNullAndAny();
            ViewBag.BatchCount = _settlementBillService.GetBatchCount();

            // 页面按钮展示权限
            ViewBag.ShowPermDict = new Dictionary<string, bool>
            {
                {$".{nameof(FxPermission.OutAccountFxUser)}",SiteContext.HasPermission(FxPermission.OutAccountFxUser)},
                {$".{nameof(FxPermission.SetProductSettlementPrice)}",SiteContext.HasPermission(FxPermission.SetProductSettlementPrice)},
            }.ToJson();
            return View();
        }

        /// <summary>
        /// 对账设置，我的商家
        /// </summary>
        /// <returns></returns>
        public ActionResult PriceSetting_Agent()
        {
            Dictionary<int, string> newStatusDic = new Dictionary<int, string>();
            foreach (var item in Enum.GetValues(typeof(AgentBingSupplierStatus)))
            {
                newStatusDic.Add((int)item, ((AgentBingSupplierStatus)item).GetEnumDescription());
            }
            var isWhite = SiteContext.Current.IsWhiteUser;
            ViewBag.IsWhiteUser = isWhite;
            var useLastTime = _commonSettingService.GetString("/ErpWeb/Settlement/UseLastTime", SiteContext.Current.CurrentShopId) ?? "false";
            ViewBag.UseLastTime = useLastTime;
            ViewBag.Status = newStatusDic;
            ViewBag.BatchCount = _settlementBillService.GetBatchCount();
            // 页面按钮展示权限
            ViewBag.ShowPermDict = new Dictionary<string, bool>
            {
                {$".{nameof(FxPermission.OutAccountFxUser)}",SiteContext.HasPermission(FxPermission.OutAccountFxUser)},
                {$".{nameof(FxPermission.SetProductSettlementPrice)}",SiteContext.HasPermission(FxPermission.SetProductSettlementPrice)},
            }.ToJson();
            return View();
        }

        /// <summary>
        /// 对账设置，我的店铺
        /// </summary>
        /// <returns></returns>
        public ActionResult MyShops()
        {
            Dictionary<int, string> newStatusDic = new Dictionary<int, string>();
            foreach (var item in Enum.GetValues(typeof(AgentBingSupplierStatus)))
            {
                newStatusDic.Add((int)item, ((AgentBingSupplierStatus)item).GetEnumDescription());
            }
            var useLastTime = _commonSettingService.GetString("/ErpWeb/Settlement/UseLastTime", SiteContext.Current.CurrentShopId) ?? "false";
            ViewBag.UseLastTime = useLastTime;
            ViewBag.Status = newStatusDic;
            ViewBag.Shops = GetFxUserShops(isAllPlats: true).Select(x => new { x.NickName, x.ShopId, x.PlatformType }).ToJson();
            ViewBag.BatchCount = _settlementBillService.GetBatchCount();
            // 页面按钮展示权限
            ViewBag.ShowPermDict = new Dictionary<string, bool>
            {
                {$".{nameof(FxPermission.OutAccountFxUser)}",SiteContext.HasPermission(FxPermission.OutAccountFxUser)},
                {$".{nameof(FxPermission.SetProductSettlementPrice)}",SiteContext.HasPermission(FxPermission.SetProductSettlementPrice)},
            }.ToJson();
            return View();
        }
        /// <summary>
        /// 设置商品结算价格
        /// </summary>
        /// <param name="fxUserID">对应厂家或商家 用户ID</param>
        /// <param name="type">1=厂家 2=商家</param>
        /// <returns></returns>
        [FxAuthorize(FxPermission.SetProductSettlementPrice)]
        public ActionResult PriceSettingProduct(string fxUserID, int type, string name)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //已全部迁移，不需要再兼容判断 2023-10-12
            //var fxDbConfig = new FxDbConfigService().GetByFxUserId(fxUserId, CloudPlatformType.TouTiao.ToString());
            //ViewBag.ShowTouTiaoCloud = fxDbConfig == null ? "0" : "1";
            //ViewBag.PriceSettingParam = (new { FxUserId = fxUserID, Type = type, Name = name }).ToJson();

            //获取商家的分库情况
            var agentDbs = DbPolicyExtension.GetConfigFxSupplierNames(fxUserId);
            var agentDbStr = agentDbs.Item1.Select(x => new { DbName = DES.EncryptDES(x.Key, CustomerConfig.LoginCookieEncryptKey), Agents = x.Value.Select(a => a.ShowName) }).ToJson();
            ViewBag.AgentDb = RemoveDbNameSpecialChar(agentDbStr);
            ViewBag.AgentDbCloud = agentDbs.Item2.Select(x => new { DbCloudName = x.Key, Agents = x.Value.Select(a => a.ShowName) }).ToJson();

            return View();
        }

        public ActionResult CostPriceSettingProductWrap(int shopid)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //获取商家的分库情况

            //var db = DbPolicyExtension.GetConfigFxUserId(fxUserId);
            var db = DbPolicyExtension.GetAllDbNameSimpleConfigs(shopIds: new List<int>() { shopid }, fxUserId: fxUserId).FirstOrDefault();
            if (db != null)
            {
                ViewBag.ShopDbName = RemoveDbNameSpecialChar(DES.EncryptDES(db.DbName, CustomerConfig.LoginCookieEncryptKey));
            }
            //var dbname = db.DbNameConfig.DbName;

            /*var agentDbs = DbPolicyExtension.GetConfigFxSupplierNames(fxUserId);
            var agentDbStr = agentDbs.Item1.Select(x => new { DbName = DES.EncryptDES(x.Key, CustomerConfig.LoginCookieEncryptKey), Agents = x.Value.Select(a => a.ShowName) }).ToJson();
            ViewBag.AgentDb = RemoveDbNameSpecialChar(agentDbStr);
            ViewBag.AgentDb = RemoveDbNameSpecialChar(agentDbStr);
            ViewBag.AgentDbCloud = agentDbs.Item2.Select(x => new { DbCloudName = x.Key, Agents = x.Value.Select(a => a.ShowName) }).ToJson();*/
            return View();
        }

        /// <summary>
        /// 设置不同平台商品结算价格
        /// </summary>
        /// <param name="pt">平台</param>
        /// <param name="fxUserID">对应厂家或商家 用户ID</param>
        /// <param name="type">1=厂家 2=商家</param>
        /// <returns></returns>
        public ActionResult PriceSettingClound(string pt, string fxUserID, int type, string name)
        {
            var permissionService = new SysPermissionFxService();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var agents = _supplierUserService.GetAgentList(fxUserId, needEncryptAccount: true);
            //permissionService.EncryptAccount(agents, isAgent: true);
            var agentJson = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop, x.NickName, x.Remark }).Distinct().ToJson();
            ViewBag.Agents = agentJson.IsNullOrEmpty() ? null : agentJson;
            //ViewBag.AgentList = agents;
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, needEncryptAccount: true);
            //permissionService.EncryptAccount(suppliers, isAgent: false);
            var supplierJson = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, SupplierFxUserId = x.SupplierFxUserId, Status = x.Status, IsTop = x.IsTop, x.NickName, x.RemarkName, x.IsFilter }).Distinct().ToJson();
            ViewBag.Suppliers = supplierJson.IsNullOrEmpty() ? null : supplierJson;
            //ViewBag.SupplierList = suppliers;
            return View();
        }

        public ActionResult OutAccountProduct()
        {
            return View();
        }
        /// <summary>
        /// SKU回收站-外层
        /// </summary>
        /// <returns></returns>
        public ActionResult SkuRecycle()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //获取商家的分库情况
            var agentDbs = DbPolicyExtension.GetConfigFxSupplierNames(fxUserId);
            var agentDbStr = agentDbs.Item1.Select(x => new { DbName = DES.EncryptDES(x.Key, CustomerConfig.LoginCookieEncryptKey), Agents = x.Value.Select(a => a.ShowName) }).ToJson();
            ViewBag.AgentDb = RemoveDbNameSpecialChar(agentDbStr);
            ViewBag.AgentDbCloud = agentDbs.Item2.Select(x => new { DbCloudName = x.Key, Agents = x.Value.Select(a => a.ShowName) }).ToJson();
            return View();
        }
        /// <summary>
        /// 我的厂家-SKU回收站
        /// </summary>
        /// <returns></returns>
        public ActionResult SkuRecycleSupplier()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, needEncryptAccount: true);
            //new SysPermissionFxService().EncryptAccount(suppliers, isAgent: false);
            var supplierJson = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, SupplierFxUserId = x.SupplierFxUserId, Status = x.Status, IsTop = x.IsTop, x.NickName, x.RemarkName, x.IsFilter }).Distinct().ToJson();
            ViewBag.Suppliers = supplierJson.IsNullOrEmpty() ? null : supplierJson;
            ViewBag.SupplierList = suppliers;
            return View();
        }
        /// <summary>
        /// 我的商家-SKU回收站
        /// </summary>
        /// <returns></returns>
        public ActionResult SkuRecycleAgent()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var agents = _supplierUserService.GetAgentList(fxUserId, needEncryptAccount: true);
            //new SysPermissionFxService().EncryptAccount(agents, isAgent: true);
            var agentJson = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, FxUserId = x.FxUserId, SupplierFxUserId = x.FxUserId, Status = x.Status, IsTop = x.IsTop, x.NickName, x.RemarkName }).Distinct().ToJson();
            ViewBag.Agents = agentJson.IsNullOrEmpty() ? null : agentJson;
            ViewBag.AgentList = agents;
            return View();
        }

        /// <summary>
        /// 根据厂家或商家 查询对应的商品信息
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadProductByFxUserId(int pageIndex, int pageSize, string productName, string shotName, string nameGui, string productSkuName, string platformType, int productStatus, string fxUserId, int type)
        {
            var fxUserIds = fxUserId.SplitToList(",").ConvertAll(x => x.ToInt()).Where(x => x > 0).ToList();
            if (fxUserIds == null || fxUserIds.Any())
                return FalidResult($"{(type == 1 ? "厂家" : "商家")}不存在");

            string agentID = string.Empty, supplierId = string.Empty;
            if (type == 1) supplierId = string.Join(",", fxUserIds);
            else if (type == 2) agentID = string.Join(",", fxUserIds);

            var data = _productFxService.LoadProductByFxUserId(SiteContext.Current.CurrentFxUserId, new Data.Model.ProductFxRequertModule()
            {
                PageIndex = pageIndex,
                PageSize = pageSize,
                SelectPlatformType = platformType,
                InputProductName = productName,
                ProductShortTitle = shotName,
                ProductSkuShortTitle = nameGui,
                ProductSkuName = productSkuName,
                ProductStatus = productStatus,
                SelectAgentId = agentID.ToString(),
                SelectSupplierId = supplierId.ToString()
            }, type, fxUserIds);

            return Json(data);
        }

        /// <summary>
        /// 设置结算金额
        /// </summary>
        /// <param name="pricelist"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult SetProductSettlementPrice(List<ProductSettlementPrice> pricelist, int? changeType)
        {
            if (pricelist == null || !pricelist.Any())
                return SuccessResult();
            var queryIdFromDb = pricelist.Any(p => p.Id <= 0);

            foreach (var item in pricelist)
            {
                item.SkuName = HttpUtility.UrlDecode(item.SkuName);
            }

            int count = _financialSettlementService.SetProductSettlementPrice(pricelist, SiteContext.Current.CurrentFxUserId, needSetIdFromDb: queryIdFromDb, changeType: changeType.ToInt(1));
            if (count < 1) return FalidResult("设置结算价失败");
            return SuccessResult();
        }
        /// <summary>
        /// 同步结算金额 
        /// </summary>
        /// <param name="settlementIds"></param>
        /// <param name="type">用户类型，1-商家 2-厂家</param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult SyncProductSettlementPrice(List<int> settlementIds, int type)
        {
            int count = _financialSettlementService.SyncProductSettlementPrice(settlementIds, SiteContext.Current.CurrentFxUserId, type);
            if (count < 1) return FalidResult("同步结算价失败");

            return SuccessResult();

        }

        /// <summary>
        /// 设置成本价
        /// </summary>
        /// <param name="pricelist"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult SetProductCostPrice(List<ProductSettlementPrice> pricelist)
        {
            if (pricelist.IsNullOrEmpty())
                return SuccessResult("操作成功");
            var queryIdFromDb = pricelist.Any(p => p.Id <= 0);
            int count = _financialSettlementService.SetProductCostPrice(pricelist, SiteContext.Current.CurrentFxUserId, needSetIdFromDb: queryIdFromDb);
            if (count < 1) return FalidResult("操作异常，请稍后再试");

            return SuccessResult("操作成功");
        }

        /// <summary>
        /// 设置默认采购价格
        /// </summary>
        /// <param name="pricelist"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult SetProductDefaultSettlementPrice(List<ProductSettlementPrice> pricelist)
        {
            if (pricelist.IsNullOrEmpty())
                return SuccessResult("操作成功");

            _financialSettlementService.SetCommonSettlementPrice(pricelist, SiteContext.Current.CurrentFxUserId, 4);
            return SuccessResult("操作成功");
        }

        /// <summary>
        /// 同步成本价，源价格来自对方的结算价或自己的结算价
        /// </summary>
        /// <param name="settlementIds">价格来源记录Id</param>
        /// <returns></returns>
        public ActionResult SyncProductCostPrice(List<int> settlementIds)
        {
            if (settlementIds.IsNullOrEmpty())
                return SuccessResult("操作成功");

            int count = _financialSettlementService.SyncProducCostPriceFromSettlementPrice(settlementIds, SiteContext.Current.CurrentFxUserId);
            if (count < 1) return FalidResult("操作异常，请稍后再试");
            return SuccessResult("操作成功");
        }

        public ActionResult LoadMySupplierList(string key, AgentBingSupplierStatus? status, int pageIndex, int pageSize,int formtype = 0)
        {
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _supplierUserService.GetSupplierList(fxUserId, key, status, pageIndex, pageSize, needEncryptAccount: true, formtype: formtype);
            return SuccessResult(new { Total = result.Item1, List = result.Item2 });
        }

        public ActionResult LoadMyAgentList(string key, AgentBingSupplierStatus? status, int pageIndex, int pageSize,int formtype = 0)
        {
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            var result = _supplierUserService.GetAgentList(fxUserId, key, status, pageIndex, pageSize, needEncryptAccount: true, formtype: formtype);
            // 查询商家是否有跨境店铺
            var agentIds = result.Item2.Select(a => a.FxUserId).ToList();
            //当前用户是否存在跨境发货记录（自发或者代发)
            var isExistCrossBorderOrderDelivered = _settlementBillService.IsExistCrossBorderOrderDelivered();//new FxUserForeignShopService().GetShopsByFxUserIds(agentIds,null,new List<string>() { "FxUserId"}).Select(s=>s.FxUserId).ToList();
            result.Item2.ForEach(a =>
            {
                a.HasCrossBorderShop = isExistCrossBorderOrderDelivered;
            });
            return SuccessResult(new { Total = result.Item1, List = result.Item2 });
        }

        public ActionResult LoadMyShopList(int pageIndex, int pageSize, string nickName, int status)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            //获取当前用户绑定店铺

            var _reqModel = new FxUserShopQueryModel { FxUserId = fxUserId };
            _reqModel.PageIndex = pageIndex;
            _reqModel.PageSize = pageSize;
            _reqModel.Status = (FxUserShopStatus)status;//只查询绑定状态正常的数据
            _reqModel.NickName = nickName;
            var result = _fxUserShopService.GetListOfFinancialSettlement(_reqModel);
            var date = result.Item2.Select(x => new { x.ShopId, x.NickName, x.PlatformTypeName, x.PlatformType });


            return SuccessResult(new { Total = result.Item1, List = date });
        }
        #endregion

        [FxMigrateLockFilter()]
        public ActionResult BillManagement()
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var agents = _supplierUserService.GetAgentList(fxUserId, needEncryptAccount: true, isIgnoreHideCancelUserSetting: true);//_supplierUserService.GetAgentList(fxUserId, null, null, 1, 10000);
            var agentJson = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop, x.NickName, x.Remark }).Distinct().ToJson();
            ViewBag.Agents = agentJson.IsNullOrEmpty() ? null : agentJson;
            //ViewBag.Agents = agents?.Item2.ToJson();
            ViewBag.AgentList = agents;//agents?.Item2;
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, needEncryptAccount: true, isIgnoreHideCancelUserSetting: true);//_supplierUserService.GetSupplierList(fxUserId, null, null, 1, 10000);
            var supplierJson = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, SupplierFxUserId = x.SupplierFxUserId, Status = x.Status, IsTop = x.IsTop, x.NickName, x.RemarkName, x.IsFilter }).Distinct().ToJson();
            //是否是白名单用户
            var isWhite = SiteContext.Current.IsWhiteUser;
            ViewBag.IsWhiteUser = isWhite;
            ViewBag.Suppliers = supplierJson.IsNullOrEmpty() ? null : supplierJson;
            //ViewBag.Suppliers = suppliers?.Item2.ToJson();
            ViewBag.SupplierList = suppliers;
            ViewBag.FxUserId = fxUserId;
            ViewBag.FxUserNickName = SiteContext.Current.CurrentFxUser.NickName;
            ViewBag.FxUserId = fxUserId;
            ViewBag.BillExportWaittingRemark = _commonSettingService.Get("/ErpWeb/FinancialSettlement/BillExportWaittingRemark", 0)?.Value ?? "";
            #region 调整分库下各参数的范围 -对账这里是多库合并的,不需要这个
            //如果只有一个分库,跳过此段
            //如果有多个分库,按照分库分组商家(商家可在多个分库中出现)
            //然后重新赋值新的商家下拉框信息
            //ViewBag.Agents = _commonSettingService.RecoverSupplierViewBag(agents?.Item2
            //    , SiteContext.Current.CurrentDbAreaConfig);

            //ViewBag.AgentList = _commonSettingService.RecoverSupplierObject(agents?.Item2
            //    , SiteContext.Current.CurrentDbAreaConfig) ?? new List<SupplierUser>();

            #endregion

            // 页面按钮展示权限
            ViewBag.ShowPermDict = new Dictionary<string, bool>
            {
                {$".{nameof(FxPermission.ExportSettlementBill)}",SiteContext.HasPermission(FxPermission.ExportSettlementBill)},
                {$".{nameof(FxPermission.AccountDetail)}",SiteContext.HasPermission(FxPermission.AccountDetail)},
                {$".{nameof(FxPermission.ConfirmSettlementBill)}",SiteContext.HasPermission(FxPermission.ConfirmSettlementBill)},
                {$".{nameof(FxPermission.DeleteSettlementBill)}",SiteContext.HasPermission(FxPermission.DeleteSettlementBill)},
                {$".{nameof(FxPermission.UpdateBillRemark)}",SiteContext.HasPermission(FxPermission.UpdateBillRemark)},
                {$".{nameof(FxPermission.UpdateBillComment)}",SiteContext.HasPermission(FxPermission.UpdateBillComment)},
            }.ToJson();
            return View();
        }


        public ActionResult BillCenter()
        {
            if (!SiteContext.Current.IsReconVip)
            {
                return Redirect($"/FinancialSettlement/BillManagement?{Request.QueryString.ToString()}");
            }

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var agents = _supplierUserService.GetAgentList(fxUserId, needEncryptAccount: true, isIgnoreHideCancelUserSetting: true);//_supplierUserService.GetAgentList(fxUserId, null, null, 1, 10000);
            var agentJson = agents?.Select(x => new { UserName = x.AgentMobileAndRemark, x.FxUserId, x.Status, x.IsTop, x.NickName, x.Remark }).Distinct().ToJson();
            ViewBag.Agents = agentJson.IsNullOrEmpty() ? null : agentJson;
            //ViewBag.Agents = agents?.Item2.ToJson();
            ViewBag.AgentList = agents;//agents?.Item2;
            var suppliers = _supplierUserService.GetSupplierList(fxUserId, needEncryptAccount: true, isIgnoreHideCancelUserSetting: true);//_supplierUserService.GetSupplierList(fxUserId, null, null, 1, 10000);
            var supplierJson = suppliers?.Select(x => new { UserName = x.SupplierMobileAndRemark, FxUserId = x.SupplierFxUserId, SupplierFxUserId = x.SupplierFxUserId, Status = x.Status, IsTop = x.IsTop, x.NickName, x.RemarkName, x.IsFilter }).Distinct().ToJson();
            //是否是白名单用户
            var isWhite = SiteContext.Current.IsWhiteUser;
            ViewBag.IsWhiteUser = isWhite;
            ViewBag.Suppliers = supplierJson.IsNullOrEmpty() ? null : supplierJson;
            //ViewBag.Suppliers = suppliers?.Item2.ToJson();
            ViewBag.SupplierList = suppliers;
            ViewBag.FxUserId = fxUserId;
            ViewBag.FxUserNickName = SiteContext.Current.CurrentFxUser.NickName;
            ViewBag.FxUserId = fxUserId;
            ViewBag.BillExportWaittingRemark = _commonSettingService.Get("/ErpWeb/FinancialSettlement/BillExportWaittingRemark", 0)?.Value ?? "";
            #region 调整分库下各参数的范围 -对账这里是多库合并的,不需要这个
            //如果只有一个分库,跳过此段
            //如果有多个分库,按照分库分组商家(商家可在多个分库中出现)
            //然后重新赋值新的商家下拉框信息
            //ViewBag.Agents = _commonSettingService.RecoverSupplierViewBag(agents?.Item2
            //    , SiteContext.Current.CurrentDbAreaConfig);

            //ViewBag.AgentList = _commonSettingService.RecoverSupplierObject(agents?.Item2
            //    , SiteContext.Current.CurrentDbAreaConfig) ?? new List<SupplierUser>();

            #endregion

            // 页面按钮展示权限
            ViewBag.ShowPermDict = new Dictionary<string, bool>
            {
                {$".{nameof(FxPermission.ExportSettlementBill)}",SiteContext.HasPermission(FxPermission.ExportSettlementBill)},
                {$".{nameof(FxPermission.AccountDetail)}",SiteContext.HasPermission(FxPermission.AccountDetail)},
                {$".{nameof(FxPermission.ConfirmSettlementBill)}",SiteContext.HasPermission(FxPermission.ConfirmSettlementBill)},
                {$".{nameof(FxPermission.DeleteSettlementBill)}",SiteContext.HasPermission(FxPermission.DeleteSettlementBill)},
                {$".{nameof(FxPermission.UpdateBillRemark)}",SiteContext.HasPermission(FxPermission.UpdateBillRemark)},
                {$".{nameof(FxPermission.UpdateBillComment)}",SiteContext.HasPermission(FxPermission.UpdateBillComment)},
            }.ToJson();
            return View();
        }

        public ActionResult Index()
        {
            return View();
        }

        public ActionResult quickOutAccount()
        {
            //当前用户是否存在跨境发货记录（自发或者代发)
            ViewBag.IsExistCrossBorderOrderDelivered = _settlementBillService.IsExistCrossBorderOrderDelivered();
            return View();
        }
        public ActionResult CostPriceSettingProduct()
        {

            return View();
        }

        /// <summary>
        /// 判断是否有对账任务积压
        /// </summary>
        /// <param name="supplierFxUserIds"></param>
        /// <returns></returns>
        public ActionResult CheckIsOverstock()
        {
            //var syncTaskCount = new ExportTaskService().GetExportTasksWithDay(types, day: 1)?.Count() ?? 0;
            var type = ExportType.ErpSettlementV2.ToInt();
            var key = "/ErpWeb/System/SettlementWarningCount";
            //if (CustomerConfig.IsDebug)
            //{
            //    key = "/ErpWeb/System/SettlementWarningCountTest";
            //    type = ExportType.ErpSettlementV124.ToInt();
            //}
            var warnigCount = _commonSettingService.Get(key, 0)?.Value.ToInt() ?? 50;
            warnigCount = warnigCount > 0 ? warnigCount : 50;

            // 判断是否启用消息队列
            const string queueKey = ExportTaskService.ExportTaskQueueKey;
            var value = new CommonSettingService().Get(queueKey, 0)?.Value.ToInt() ?? 1;
            int syncTaskCount;
            if (value == 1)
            {
                // 从Redis获取任务数量
                var version = SiteContext.Current.CurrentLoginShop.Version;
                if (CustomerConfig.IsDebug) version = "t";
                switch (version)
                {
                    case "1": version = "v1"; break;
                    case "3": version = "v3"; break;
                    case "6": version = "v6"; break;
                    case "t": version = "test"; break;
                    default: version = "release"; break;
                }
                var redisKey = CacheKeys.SettlementTaskQueueCountKey.Replace("{version}", version);
                syncTaskCount = RedisHelper.Get<int>(redisKey);
            }
            else
            {
                var queueUrl = $"{CustomerConfig.SystemQueueUrl}/SyncFxExportTaskQueue/Count?type={type}";
                var dic = new Dictionary<string, string>();
                dic.Add("type", type.ToString());
                var wu = new WebUtils();
                syncTaskCount = wu.DoGet(queueUrl, dic).ToInt();
            }

            Utility.Log.Debug($"待处理对账任务数量：{syncTaskCount}，积压阈值：{warnigCount}");
            var isOverStock = syncTaskCount > warnigCount ? "1" : "0";
            return SuccessResult(isOverStock);
        }

        [FxMigrateLockFilter()]
        [FxAuthorize(AuthorizeType.OutAccount)]
        public ActionResult CreateOutAccount(string supplierFxUserIds, bool? isBatch, int usertype, string billCode, DateTime starttime, DateTime endtime, string appointTime, OrderSearchModel options, CommonSettingService commonSettingService)
        {
            var oldTaskFlag = options.Filters.FirstOrDefault(o => o.Name == "TaskFlag")?.Value;//店铺信息 
            List<string> newTaskFlag = new List<string>();
            if (!oldTaskFlag.IsNullOrEmpty())
            {
                newTaskFlag = oldTaskFlag.Split(',').ToList();
            }

            int currentFxUserId = SiteContext.Current.CurrentFxUserId, currentLoginShop = SiteContext.Current.CurrentShopId;
            if (string.IsNullOrWhiteSpace(supplierFxUserIds) && newTaskFlag.IsNullOrEmptyList())
            {
                return FalidResult("未读取到出账对象，出账失败");
            }

            if ((starttime - endtime).TotalSeconds > 0)
            {
                return FalidResult("出账周期开始时间不能大于结束时间");
            }
            //目前数据仅支持近45天内对账
            var nowDate = DateTime.Now.Date;
            var startTimeRight = nowDate.AddDays(-44);
            var endTimeRight = nowDate.AddDays(1).AddSeconds(-1);
            //结束时间大于当天，则取当天。
            var newEndTime = endtime;
            if (endtime > endTimeRight)
            {
                newEndTime = endTimeRight;
            }
            //是否在45天
            var isRight = (starttime >= startTimeRight && starttime <= endTimeRight) && (newEndTime >= startTimeRight &&
                newEndTime <= endTimeRight);
            if (!isRight)
            {
                return FalidResult(
                    $"目前数据仅支持近45天内对账，支持时间段:{startTimeRight:yyyy-MM-dd HH:mm:ss}~{endTimeRight:yyyy-MM-dd HH:mm:ss}.");
            }

            var diffDays = (endtime - starttime).TotalDays;
            if (diffDays > 45)
            {
                return FalidResult("目前数据仅支持45天范围内的对账");
            }

            var fxUserIdList = supplierFxUserIds?.SplitWithString(",")?.ToList().ConvertAll(id => id.ToInt()) ?? new List<int>();
            if (!newTaskFlag.Exists(t => t == "3"))//排除店铺ID进入次验证
            {
                var supplierUsers = _supplierUserService.GetSupplierUserByIds(fxUserIdList, currentFxUserId, usertype);
                if (supplierUsers.Count != fxUserIdList.Count)
                {
                    return FalidResult("存在绑定状态异常的出账对象，出账失败");
                }
            }

            var shopIds = new List<int>();
            var shop = options.Filters.FirstOrDefault(o => o.Name == "Shop")?.Value;//店铺信息 
            if (!string.IsNullOrWhiteSpace(shop))
            {
                var shopIdList = shop.Split(',');
                foreach (var strId in shopIdList)
                {
                    int id = 0;
                    if (int.TryParse(strId, out id))
                    {
                        if (id > 0)
                            shopIds.Add(id);
                    }
                }
            }

            if (newTaskFlag.Exists(t => t == "3"))
            {
                //对账表结构优化生产 店铺出账ShopID条件
                var shopIdOption = options.Filters.FirstOrDefault(m => m.Name == "Shop");
                if (shopIdOption != null)
                {
                    options.Filters.Remove(shopIdOption);
                }
            }



            //对账表结构优化生产 用户ID条件
            var fxUserIdOption = options.Filters.FirstOrDefault(m => m.Name == "FxUserId");
            if (fxUserIdOption != null)
            {
                options.Filters.Remove(fxUserIdOption);
            }

            var dbname = Request["dbname"].ToString2();

            try
            {
                var isWhite = SiteContext.Current.IsWhiteUser;
                var outOrderDetailSetting = commonSettingService.Get($"/FxSystem/OutAccountExtSettings/IsOutOrderDetail/{currentFxUserId}", currentLoginShop)?.Value ?? null;
                //var TransferKeys = commonSettingService.Get("/FxSystem/TransferToOtherPlatformKeys", 0)?.Value.SplitToList(",") ?? new List<string>();
                //if (result > 0 && TransferKeys != null && TransferKeys.Contains(settingKey) && CustomerConfig.CloudPlatformType != "Pinduoduo")

                //判断是否调用批量用户打印(下载)与白名单
                //if (!isBatch.IsNullOrEmptyList() && cache.Contains(SiteContext.Current.CurrentFxUserId.ToString()))
                if (!newTaskFlag.IsNullOrEmptyList() && isBatch == true && isWhite)
                {
                    // 是否导出账单明细数据 白名单用户默认导出明细
                    if (outOrderDetailSetting.IsNullOrEmpty())
                    {
                        options.IsOutOrderDetail = true;
                        commonSettingService.Set($"/FxSystem/OutAccountExtSettings/IsOutOrderDetail/{currentFxUserId}", "1", currentLoginShop);
                    }
                    else
                        options.IsOutOrderDetail = outOrderDetailSetting.Equals("0") ? false : true;
                    _settlementBillService.BatchBillByUser(fxUserIdList, usertype, billCode, currentLoginShop, dbname, starttime, endtime, appointTime, options, newTaskFlag, currentFxUserId);
                }
                else
                {
                    // 是否导出账单明细数据 非白名单用户默认不导出明细
                    if (outOrderDetailSetting.IsNullOrEmpty())
                    {
                        options.IsOutOrderDetail = false;
                        commonSettingService.Set($"/FxSystem/OutAccountExtSettings/IsOutOrderDetail/{currentFxUserId}", "0", currentLoginShop);
                    }
                    else
                        options.IsOutOrderDetail = outOrderDetailSetting.Equals("1") ? true : false;

                    var paramjson = options.ToJson();

                    _settlementBillService.BatchInsert(fxUserIdList, usertype, starttime, endtime, paramjson, currentFxUserId, currentLoginShop, dbname, newTaskFlag, shopIds);
                }
                return SuccessResult("出账成功");
            }
            catch (Exception e)
            {
                Utility.Log.WriteError($"出账失败,错误详情：{e}");
                return FalidResult("程序出错，出账失败");
            }
        }

        /// <summary>
        /// 对账中心--批量下载账单
        /// </summary>
        /// <param name="billCode"></param>
        /// <param name="starttime"></param>
        /// <param name="endtime"></param>
        /// <param name="options"></param>
        /// <param name="commonSettingService"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        [FxAuthorize(FxPermission.ExportSettlementBill)]
        public ActionResult BatchDownLoadBills(string[] billCode, bool isBatch, int usertype, DateTime starttime, DateTime endtime)
        {
            if (billCode.IsNullOrEmpty())
            {
                return FalidResult("未读取到下载数据");
            }

            if ((starttime - endtime).TotalSeconds > 0)
            {
                return FalidResult("下载周期开始时间不能大于结束时间");
            }

            var diffDays = (endtime - starttime).TotalDays;
            if (diffDays > 45)
            {
                return FalidResult("目前下载数据仅支持45天范围内的账单");
            }
            var dbname = Request["dbname"].ToString2();
            try
            {
                int currentFxUserId = SiteContext.Current.CurrentFxUserId, currentLoginShop = SiteContext.Current.CurrentShopId;

                _settlementBillService.BatchDownLoadBills(billCode, dbname, currentLoginShop, currentFxUserId, starttime, endtime);

                return SuccessResult("生成任务成功");
            }
            catch (Exception e)
            {
                Utility.Log.WriteError($"出账失败,错误详情：{e}");
                return FalidResult("服务器繁忙，请稍会儿再试");
            }
        }

        /// <summary>
        /// 获取导出任务中心数据
        /// </summary>
        /// <param name="query"></param>
        /// <param name="SupplierFxUserId"></param>
        /// <param name="AgentFxUserId"></param>
        /// <param name="BillStatus"></param>
        /// <returns></returns>
        //public ActionResult GetSettlementBatchList(ExportTaskSearchModel query, string SupplierFxUserId, string AgentFxUserId, int BillStatus = 999)
        //{
        //    var FxUserId = SiteContext.Current.CurrentFxUserId;
        //    List<int> SupplierFxUserIdlist = new List<int>();
        //    List<int> AgentFxUserIdlist = new List<int>();
        //    if (!string.IsNullOrEmpty(SupplierFxUserId))
        //    {
        //        var sfu = SupplierFxUserId.Split(',');
        //        for (int i = 0; i < sfu.Length; i++)
        //        {
        //            if (!string.IsNullOrEmpty(sfu[i]))
        //            {
        //                SupplierFxUserIdlist.Add(int.Parse(sfu[i]));
        //            }
        //        }
        //    }
        //    if (!string.IsNullOrEmpty(AgentFxUserId))
        //    {
        //        var afu = AgentFxUserId.Split(',');
        //        for (int i = 0; i < afu.Length; i++)
        //        {
        //            if (!string.IsNullOrEmpty(afu[i]))
        //            {
        //                AgentFxUserIdlist.Add(int.Parse(afu[i]));
        //            }
        //        }
        //    }
        //    if (query == null)
        //        query = new ExportTaskSearchModel();

        //    query.FxUserId = FxUserId;
        //    query.SupplierFxUserId = SupplierFxUserIdlist;
        //    query.AgentFxUserId = AgentFxUserIdlist;
        //    query.BillStatus = BillStatus;
        //    if (query.PageSize <= 0)
        //        query.PageSize = 10;
        //    if (query.PageIndex <= 0)
        //        query.PageIndex = 1;

        //    var list = _settlementBillService.GetSettlementBillList(query);

        //    // 从队列站点获取当前对账任务是显示在积压排队中，还是显示进度条
        //    var settlements = list.Rows.Where(x => x.BillStatus == 0).ToList();
        //    if (settlements.Any())
        //    {
        //        var billCodes = settlements.Select(x => x.BillCode).Distinct().ToList();
        //        var queueUrl = $"{CustomerConfig.SystemQueueUrl}/SyncFxExportTaskQueue/Percent2";
        //        var dic = new Dictionary<string, string>();
        //        dic.Add("BillCodes", string.Join(",", billCodes));
        //        var wu = new WebUtils();
        //        var result = wu.DoPost(queueUrl, dic);
        //        var resultDic = result.ToObject<Dictionary<string, double>>();
        //        foreach (var item in settlements)
        //        {
        //            item.Percent = resultDic != null && resultDic.ContainsKey(item.BillCode) ? resultDic[item.BillCode] : -1;
        //        }
        //    }

        //    //处理厂家/商家名称
        //    if (query.IsMobileApi == 1)
        //    {
        //        var selfNickName = SiteContext.Current.CurrentFxUser.NickName + " (本人)";
        //        var agents = _supplierUserService.GetAgentList(FxUserId);
        //        var suppliers = _supplierUserService.GetSupplierList(FxUserId);

        //        list.Rows?.ForEach(o =>
        //        {
        //            o.CurLoginFxUserId = FxUserId;
        //            if (o.CreateUser == FxUserId)
        //            {
        //                o.CreateUserNickName = selfNickName;
        //            }
        //            else
        //            {
        //                if (o.SettlementType == 2)
        //                    o.CreateUserNickName = "厂家：" + suppliers.FirstOrDefault(a => a.SupplierFxUserId == o.CreateUser)?.SupplierMobileAndRemark;
        //                else
        //                    o.CreateUserNickName = "商家：" + agents.FirstOrDefault(a => a.FxUserId == o.CreateUser)?.AgentMobileAndRemark;
        //            }

        //            if (o.FxUserId == FxUserId)
        //            {
        //                o.UserFxNickName = selfNickName;
        //            }
        //            else
        //            {
        //                if (o.SettlementType == 1)
        //                    o.UserFxNickName = "厂家：" + suppliers.FirstOrDefault(a => a.SupplierFxUserId == o.FxUserId)?.SupplierMobileAndRemark;
        //                else
        //                    o.UserFxNickName = "商家：" + agents.FirstOrDefault(a => a.FxUserId == o.FxUserId)?.AgentMobileAndRemark;
        //            }
        //        });
        //    }

        //    return Json(list);
        //}

        public ActionResult GetSettlementBillList(SettlementBillQueryModel query, string SupplierFxUserId, string AgentFxUserId, int BillStatus = 999)
        {
            var FxUserId = SiteContext.Current.CurrentFxUserId;
            List<int> SupplierFxUserIdlist = new List<int>();
            List<int> AgentFxUserIdlist = new List<int>();
            if (!string.IsNullOrEmpty(SupplierFxUserId))
            {
                var sfu = SupplierFxUserId.Split(',');
                for (int i = 0; i < sfu.Length; i++)
                {
                    if (!string.IsNullOrEmpty(sfu[i]))
                    {
                        SupplierFxUserIdlist.Add(int.Parse(sfu[i]));
                    }
                }
            }
            if (!string.IsNullOrEmpty(AgentFxUserId))
            {
                var afu = AgentFxUserId.Split(',');
                for (int i = 0; i < afu.Length; i++)
                {
                    if (!string.IsNullOrEmpty(afu[i]))
                    {
                        AgentFxUserIdlist.Add(int.Parse(afu[i]));
                    }
                }
            }
            if (query == null)
                query = new SettlementBillQueryModel();

            query.FxUserId = FxUserId;
            query.SupplierFxUserId = SupplierFxUserIdlist;
            query.AgentFxUserId = AgentFxUserIdlist;
            query.BillStatus = BillStatus;
            if (query.PageSize <= 0)
                query.PageSize = 10;
            if (query.PageIndex <= 0)
                query.PageIndex = 1;

            var pagedResultModel = _settlementBillService.GetSettlementBillListAssemble(query);

            //根据结算价权限进行打码
            /*if (SiteContext.Current.SubFxUserId >= 0 && !SiteContext.Current.PermissionTags.Contains(FxPermission.ShowSettlePrice))
            {
                list.Rows.ForEach(r => { r.TotalPrice = -99; });// 前端判断-99后改为**
            }*/

            return Json(pagedResultModel);
        }

        /// <summary>
        /// 获取导出设置
        /// </summary>
        /// <returns></returns>
        public ActionResult GetExportSet()
        {
            var currShop = SiteContext.Current.CurrentLoginShop;
            var exportSettingVal = _commonSettingService.GetFxDefaultSetting(currShop.PlatformType, "SettlementBillExportSet");

            var key = "/ErpWeb/SettlementBill/SettlementBillExportSet";
            // 获取用户级的配置，用户没有配置，则默认勾上全部
            var settlementBillExportSet = _commonSettingService.Get(key, currShop.Id);
            if (settlementBillExportSet == null || settlementBillExportSet.Value.IsNullOrEmpty())
            {
                _commonSettingService.Set(key, exportSettingVal, currShop.Id);
            }
            return SuccessResult(exportSettingVal);
        }

        /// <summary>
        /// 导出对账中心
        /// </summary>
        /// <returns></returns>
        [FxAuthorize(FxPermission.ExportSettlementBillCenter)]
        public ActionResult ExportExcel()
        {
            try
            {
                var shop = SiteContext.Current.CurrentLoginShop;
                var fxUserId = SiteContext.Current.CurrentFxUserId;

                var options = Request.Form["options"].ToString2();
                options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
                options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果
                SettlementBillQueryModelV2 billQueryModelV2 = JsonExtension.ToObject<SettlementBillQueryModelV2>(options) ?? new SettlementBillQueryModelV2();
                SettlementBillQueryModel query = billQueryModelV2.BuildQueryModel();
                query.FxUserId = fxUserId;
                query.IsReconVip = SiteContext.Current.IsReconVip;
                query.PageIndex = 1;
                var timeOutStatus = false;
                var pageSize = 500;

                // 查询第一页数据
                query.PageIndex = 1;
                query.PageSize = pageSize;
                var pagedResultModel = _settlementBillService.GetSettlementBillListAssemble(query); // 导出第一页

                var model = query.ToJson().ToObject<SettlementBillQueryModel>();

                var returnCount = pagedResultModel.Total;
                var limitCount = _commonSettingService.Get("/ErpWeb/ReconciliationVip/ExportExcel", 0)?.Value.ToInt();
                var dbname = Request["dbname"].ToString2();

                // 任务方式
                if (returnCount > limitCount || timeOutStatus)
                {
                    #region 导出任务验证、查询超时转换后台任务
                    var timeOutSecond = 25;
                    var typeStr = "";
                    var timeOutSet = Request["time_out_set"].ToBoolean();// 模拟任务校验超时
                    var isVerification = query.DisabledTaskVerification == true ? false : true; // 是否任务校验
                    if (isVerification)
                    {
                        try
                        {
                            var task = new ExportTaskService().GetErpWebExportTaskSharding(shop.Id
                                , CustomerConfig.CloudPlatformType
                                , ExportType.SettlementBill.ToInt()
                                , Request["dbname"] ?? ""
                                , timeOutSecond);

                            if (task != null && task.Status >= 0 && task.Status < 4)
                                return FalidResult("已存在订单导出任务，如需重新导出，请先取消再创建新导出任务", GetExportTaskToWeb(task));
                        }
                        catch (Exception ex)
                        {
                            if (ex.Message.IndexOf("执行超时已过期") != -1)
                            {
                                timeOutStatus = true;
                                typeStr = "导出对账中心";
                                Utility.Log.WriteError($"导出任务查询超时，转换为后台导出任务，超时原因：{ex.ToJsonExt()},超时任务类型：{typeStr}");
                            }
                        }
                    }
                    timeOutStatus = timeOutSet ? true : timeOutStatus;
                    #endregion

                    var newTask = new ExportTask
                    {
                        IP = Request.UserHostAddress,
                        CreateTime = DateTime.Now,
                        PlatformType = CustomerConfig.CloudPlatformType,
                        ShopId = shop.Id,
                        UserId = SiteContext.Current.CurrentFxUserId.ToString(),
                        Status = 0,
                        Type = ExportType.SettlementBill.ToInt(),
                        PageIndex = 1,
                        PageSize = pageSize,
                        TotalCount = returnCount,
                        ParamJson = model.ToJson(),
                        FromModule = "对账中心-导出Excel",
                        ExtField2 = shop.PlatformType,
                        ExtField5 = dbname,
                        ExtField4 = CustomerConfig.IsCrossBorderSite ? "3" : null,//跨境环境指定灰度3导出程序
                        ExtField6 = CustomerConfig.IsCrossBorderSite ? PlatformType.TikTok.ToString() : CustomerConfig.CloudPlatformType, //跨境
                        SubFxUserId = SiteContext.GetSubFxUserId().ToString()
                    };
                    newTask.HopeExecuteTime = new CommService().ExportExecuteCheck(newTask.UserId.ToInt(), newTask.Type);
                    newTask.Id = new ExportTaskService().Add(newTask);
                    return SuccessResult("导出任务创建成功", GetExportTaskToWeb(newTask));
                }

                // 直接导出方式进行导出            
                var list = new List<SettlementBill>();

                // 第一页
                if (pagedResultModel.Rows.Count > 0)
                    list.AddRange(pagedResultModel.Rows);

                // 后面的页
                model.PageIndex++;
                model.PageSize = pageSize;
                while (returnCount > 0)
                {
                    var pagedResultModel2 = _settlementBillService.GetSettlementBillListAssemble(model); // 导出后续其他页
                    var pgList = pagedResultModel2.Rows;
                    list.AddRange(pgList);
                    returnCount = pgList.Count;
                    model.PageIndex++;
                }

                if ((list?.Count ?? 0) == 0)
                    return FalidResult("无满足条件的数据导出");

                var fileName = $"对账中心导出_{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx";

                NPOI.SS.UserModel.IWorkbook workbook = FxBuildExccelService.SettlementBillListBuildExcel(list, fileName);

                Response.Cookies.Add(new HttpCookie("downloadToken", Request.Form["downloadToken"].ToString2()));
                using (var ms = new System.IO.MemoryStream())
                {
                    workbook.Write(ms);
                    var buffer = ms.GetBuffer();
                    return File(buffer, "application/ms-excel", ExcelHelper.GetFileName(fileName, Request));
                }
            }
            catch (Exception ex)
            {
                Utility.Log.WriteError($"对账中心Excel导出失败：{ex}");
                return FalidResult("当前操作人数较多，导出失败，请重试导出");
            }
        }

        public ActionResult GetSettlementBillPercent(string billCode)
        {
            if (billCode.IsNullOrEmpty())
                return Content("0");

            var queueUrl = $"{CustomerConfig.SystemQueueUrl}/SyncFxExportTaskQueue/Percent2";
            var dic = new Dictionary<string, string>();
            dic.Add("BillCodes", billCode);
            var wu = new WebUtils();
            var result = wu.DoPost(queueUrl, dic);
            var resultDic = result.ToObject<Dictionary<string, double>>();
            var percent = resultDic != null && resultDic.ContainsKey(billCode) ? resultDic[billCode] : -1;

            return Content(percent.ToString());
        }

        public ActionResult ResetSettlementBillTask(string billCode)
        {
            if (billCode.IsNullOrEmpty())
                return FalidResult("BillCode不能为空");

            var result = new ExportTaskService().ResetByBillCode(new List<string> { billCode });
            var queueUrl = $"{CustomerConfig.SystemQueueUrl}/SyncFxExportTaskQueue/Push?billCode={billCode}";
            var dic = new Dictionary<string, string>();
            dic.Add("billCode", billCode);
            var wu = new WebUtils();
            var response = wu.DoGet(queueUrl, dic);
            return GetSettlementBillPercent(billCode);
        }

        [FxMigrateLockFilter()]
        [FxAuthorize()]
        public ActionResult ConfirmSettlementBill(string BillCode)
        {
            var FxUserId = SiteContext.Current.CurrentFxUserId;
            var Model = _settlementBillService.GetModelByBillCode(new List<string> { BillCode }, FxUserId).FirstOrDefault();
            if (Model == null || Model.BillStatus != 1 || Model.FxUserId != FxUserId)
            {
                return FalidResult("没有查询到待确认的此账单，请检查");
            }

            try
            {
                _settlementBillService.ConfirmSettlementBill(BillCode);
                return SuccessResult(DateTime.Now.ToString("yyyy-MM-dd HH:mm"));
            }
            catch (Exception e)
            {
                return FalidResult("程序出错，确认失败");
            }
        }

        [FxMigrateLockFilter()]
        [FxAuthorize()]
        public ActionResult ConfirmSettlementBillBatch(string billCode)
        {
            if (!SiteContext.Current.IsReconVip)
                return FalidResult("抱歉，您没有权限操作");
            
            // 需要校验功能白名单,白名单才有这个批量操作
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            string[] billCodeArray = billCode.Split(',');
            var model = _settlementBillService.GetModelByBillCode(billCodeArray.ToList(), fxUserId).FirstOrDefault();
            if (model == null || model.BillStatus != 1 || model.FxUserId != fxUserId)
            {
                return FalidResult("没有查询到待确认的此账单，请检查");
            }

            try
            {
                _settlementBillService.ConfirmSettlementBill(billCodeArray);
                return SuccessResult(DateTime.Now.ToString("yyyy-MM-dd HH:mm"));
            }
            catch (Exception e)
            {
                return FalidResult("程序出错，确认失败");
            }
        }

        [FxMigrateLockFilter()]
        [FxAuthorize()]
        public ActionResult DeleteSettlementBill(string BillCodes)
        {
            var FxUserId = SiteContext.Current.CurrentFxUserId;
            var Codes = BillCodes.Split(',').ToList();
            var Model = _settlementBillService.GetModelByBillCode(Codes, FxUserId).FirstOrDefault();
            if (Model == null || (Model.BillStatus != 0 && Model.BillStatus != 1 && Model.BillStatus != -11) || Model.CreateUser != FxUserId)
            {
                return FalidResult("没有查询到可删除的账单，请检查");
            }

            try
            {
                _settlementBillService.DeleteSettlementBill(Codes);
                return SuccessResult("删除成功");
            }
            catch (Exception e)
            {
                return FalidResult("程序出错，删除失败");
            }
        }

        [FxMigrateLockFilter()]
        [FxAuthorize()]
        public ActionResult UpdateRemark(List<string> BillCodes, string Remark)
        {
            var FxUserId = SiteContext.Current.CurrentFxUserId;
            var Models = _settlementBillService.GetModelByBillCode(BillCodes, FxUserId);
            if (Models.IsNullOrEmptyList())
            {
                return FalidResult("没有查询到此记录，请检查");
            }

            if (Models.Count != BillCodes.Count)
            {
                return FalidResult("账单编号与查询记录数不匹配，请检查");
            }

            try
            {
                _settlementBillService.UpdateRemark(BillCodes, Remark);
                return SuccessResult("保存成功");
            }
            catch (Exception)
            {
                return FalidResult("程序出错，保存失败");
            }
        }

        /// <summary>
        /// 更新多条或单条账单的备注，仅当前用户可见
        /// </summary>
        /// <param name="billCodes">账单编号</param>
        /// <param name="remark">备注</param>
        /// <param name="flag">旗帜：0：无旗帜，1：红旗，2：蓝旗，3：绿旗，4：黄旗，5：紫旗 </param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        [FxAuthorize()]
        public async Task<ActionResult> UpdateBillRemark(List<string> billCodes, string remark, int? flag)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            if (billCodes.IsNullOrEmptyList())
            {
                return FalidResult("账单选择为空！");
            }

            var sum = 0;

            foreach (var billCode in billCodes)
            {
                var models = await _settlementBillService.GetBillRemarkByFxUserId(billCode, fxUserId);

                // 0 更新 1 插入
                if (models.IsNotNullOrEmpty())
                {
                    sum += await _settlementBillService.InsertOrUpdateBillRemark(billCode, remark, flag, fxUserId, 0) ? 1 : 0;
                }
                else
                {
                    sum += await _settlementBillService.InsertOrUpdateBillRemark(billCode, remark, flag, fxUserId, 1) ? 1 : 0;
                }

            }

            return sum > 0 ? SuccessResult("保存成功") : FalidResult("保存失败");
        }

        /// <summary>
        /// 获取不同平台所有店铺
        /// </summary>
        /// <returns></returns>
        private string GetFxUserShops()
        {
            try
            {
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                FxUserShopService _fxUserShopService = new FxUserShopService();

                //店铺
                var _reqModel = new FxUserShopQueryModel { FxUserId = fxUserId };
                var fxUserShops = _fxUserShopService.GetList(_reqModel)?.Item2 ?? new List<FxUserShop>();
                ///用户开启了显示跨境相关功能
                //if (SiteContext.Current.IsShowCrossBorder)
                if (_commonSettingService.IsShowCrossBorder())
                {

                    var foreignShops = new FxUserForeignShopService().GetShopsByFxUserId(fxUserId);
                    fxUserShops.AddRange(foreignShops.Select(item => new FxUserShop
                    {
                        Id = item.Id,
                        Status = item.Status,
                        NickName = item.NickName,
                        PlatformType = item.PlatformType,
                        ShopId = item.ShopId,
                    }));
                }
                fxUserShops = fxUserShops.Where(x => x.Status != FxUserShopStatus.UnBind).ToList();
                return fxUserShops.Select(x => new { x.NickName, x.ShopId, x.PlatformType }).ToJson(); ;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return "";
            }
        }



        /// <summary>
        /// 对账结算 出账结算商品列表
        /// </summary>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult GetSettlementProductList(SettlementProductQuery query, string shotName, string nameGui, string productSkuName, string fxUserId, int? type)
        {
            if (type == null)
            {
                return FalidResult("用户类型不能为空");
            }
            var fxUserIds = fxUserId.SplitToList(",").ConvertAll(x => x.ToInt()).Where(x => x > 0).ToList();
            //查已删除的数据，fxUserIds允许为空
            if ((fxUserIds == null || fxUserIds.Any() == false) && query.IsDeleted == 0)
                return FalidResult("用户不存在");
            if (query == null)
                query = new SettlementProductQuery();
            query.ProductShortTitle = shotName;
            query.SkuShortTitle = nameGui;
            query.SkuName = productSkuName;
            query.UserType = (int)type;
            query.ToFxUserIds = fxUserIds;
            query.LoginFxUserId = SiteContext.Current.CurrentFxUserId;

            query.IsBaseProcuctCombine = SiteContext.Current.BaseProductSetting.OrderCombine;
            query.FxUserId = SiteContext.Current.CurrentFxUserId; ;

            var data = _settlementProductSkuService.GetSettlementProductList(query);
            #region 替换规格名中的单引号
            //规格名中的单引号，会导致前端异常，无法设置结算价，也会引起后端设置结算价拼接的sql异常
            if (data.Total > 0)
            {
                Parallel.ForEach(data.Rows, product =>
                {
                    if (product.Skus.Any(t => t.Name != null && (t.Name.Contains("'") || t.Name.Contains("\""))))
                    {
                        //将存在的英文单引号替换成中文单引号
                        product.Skus.ForEach(sku =>
                        {
                            if (sku.Name != null)
                            {
                                sku.Name = sku.Name.Replace("'", "’").Replace("\"", "”");
                            }
                        });
                    }
                });
            }
            #endregion
            return Json(data);
        }

        /// <summary>
        /// 对账结算 成本价商品列表 
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult GetCostPriceProductList(ProductFxRequertModule query, int isSetCost)
        {
            if (query == null)
                query = new ProductFxRequertModule();
            int fxUserId = SiteContext.Current.CurrentFxUserId;

            var data = _settlementProductSkuService.GetCostPriceProductList(fxUserId, query, isSetCost);
            return Json(data);
        }

        [FxMigrateLockFilter()]
        public ActionResult DelSettlementProductSku(string settlementSkuIdS)
        {
            var settlementSkuIdList = settlementSkuIdS.Split(',').Select(x => Convert.ToInt32(x)).ToList();
            var res = _settlementProductSkuService.DelSettlementProductSku(settlementSkuIdList, SiteContext.Current.CurrentFxUserId);
            return res == true ? SuccessResult("删除成功") : FalidResult("程序出错，删除失败");
        }

        [FxMigrateLockFilter()]
        public ActionResult RecoverySettlementProductSku(string settlementSkuIdS)
        {
            var settlementSkuIdList = settlementSkuIdS.Split(',').Select(x => Convert.ToInt32(x)).ToList();
            var result = _settlementProductSkuService.RecoveryStatus(settlementSkuIdList);
            return result == true ? SuccessResult("恢复成功") : FalidResult("程序出错，恢复失败");
        }
        /// <summary>
        /// 使用UniqueKey
        /// </summary>
        /// <param name="settlementSkuIdS"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult DelSettlementProductSkuV2(string keys,List<string> delPriceKeyList)
        {
            var keyList = keys?.Split(',').ToList();
            //if (delPriceKeyList.IsNullOrEmptyList())
            //{
            //    return keyList.IsNullOrEmptyList()? SuccessResult("删除成功") : FalidResult("系统繁忙，请稍后重试");
            //}
            var res = _settlementProductSkuService.DelSettlementProductSku(keyList, SiteContext.Current.CurrentFxUserId);
            //删除结算价
            delPriceKeyList = delPriceKeyList.Where(k => k.IsNotNullOrEmpty()).ToList();
            var priceList = _financialSettlementService.GetListByUniqueKeys(delPriceKeyList);
            priceList = priceList.Where(p => p.CreateUser == SiteContext.GetCurrentFxUserId()).ToList();
            if (priceList.IsNotNullAndAny())
            {
                _financialSettlementService.BatchDelete(priceList, SiteContext.GetCurrentFxUserId());
            }

            return res == true ? SuccessResult("删除成功") : FalidResult("程序出错，删除失败");
        }
        /// <summary>
        /// 使用UniqueKey
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        [FxMigrateLockFilter()]
        public ActionResult RecoverySettlementProductSkuV2(string keys)
        {
            var keyList = keys.Split(',').ToList();
            var result = _settlementProductSkuService.RecoveryStatus(keyList);
            return result == true ? SuccessResult("恢复成功") : FalidResult("程序出错，恢复失败");
        }

        public ActionResult GetSettlementLastTime(int fxUserId)
        {
            var bill = _settlementBillService.GetModelByFxUserId(SiteContext.Current.CurrentFxUserId, fxUserId);
            if (bill != null)
            {
                return Json(bill.EndTime);
            }
            return Json("");
        }

        public ActionResult GetSettlementPriceRecord(int priceId)
        {
            var list = _settlementProductSkuService.GetSettlementPriceRecord(priceId);
            if (list != null)
            {
                return Json(list);
            }
            return Json("");
        }

        public ActionResult GetSettlementPriceRecordV2(string key)
        {
            var list = _settlementProductSkuService.GetSettlementPriceRecord(key);
            if (list != null)
            {
                return Json(list);
            }
            return Json("");
        }

        public ActionResult GetSettlementPriceRecordV3(List<int> priceIds)
        {
            if (priceIds == null || !priceIds.Any())
                return Json("");
            var list = _settlementProductSkuService.GetSettlementPriceRecord(priceIds, SiteContext.Current.CurrentFxUserId);
            if (list != null)
            {
                return Json(list);
            }
            return Json("");
        }

        public ActionResult CheckIsSetPrice(int userType)
        {
            var toFxUserIds = Request["toFxUserId"].ToString2().SplitToList(",").ConvertAll(x => x.ToInt());
            var count = _settlementProductSkuService.GetUnSetSkuPriceCount(userType, toFxUserIds, SiteContext.Current.CurrentFxUserId);
            return Json(count);
        }

        /// <summary>
        /// 获取是否导出订单明细设置
        /// </summary>
        /// <returns></returns>
        public ActionResult GetIsOutOrderDetailSetting()
        {
            int fxUserId = SiteContext.Current.CurrentFxUserId, shopId = SiteContext.Current.CurrentShopId;
            try
            {
                var outOrderDetailSetting = _commonSettingService.Get($"/FxSystem/OutAccountExtSettings/IsOutOrderDetail/{fxUserId}", shopId)?.Value ?? null;
                if (outOrderDetailSetting.IsNotNullOrEmpty())
                {
                    return SuccessResult(outOrderDetailSetting.ToBoolean());
                }

                var isWhite = SiteContext.Current.IsWhiteUser;
                return isWhite ? SuccessResult(true) : SuccessResult(false);
            }
            catch (Exception ex)
            {
                Utility.Log.WriteError($"获取是否导出账单明细配置异常，{ex}");
                return FalidResult("获取是否导出账单明细配置异常", ex.ToJson());
            }
        }


        /// <summary>
        /// 出账任务
        /// </summary>
        /// <param name="BillCode"></param>
        /// <returns></returns>
        public ActionResult GetTask(string billCode)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var model = new ExportTaskService().GetExportTaskByBillCode(billCode, fxUserId);
            if (model == null || (model.Type != ExportType.ErpSettlement.ToInt() && model.Type != ExportType.ErpSettlementV2.ToInt()))
            {
                return FalidResult("没有查询到此账单的任务执行情况，请检查");
            }
            model.FilePath = "";
            model.FileName = "";
            return SuccessResult(model);
        }

        #region 批量导入
        [LogForOperatorFilter("设置结算价-批量导入(解析Excel)")]
        [FxAuthorize(FxPermission.SetProductSettlementPrice)]
        public ActionResult ExcelUpload(ImportSettlementQueryModel query)
        {
            query.LoginFxUserId = SiteContext.Current.CurrentFxUserId;
            var nickName = SiteContext.Current.CurrentDbConfig.DbNameConfig.NickName;
            var isWhite = SiteContext.Current.IsWhiteUser;
            if (!isWhite)//判断当前用户是否是白名单
                return FalidResult($"当前用户不可使用，请联系工作人员");
            try
            {
                ////1.导入的文件需要判断表头本地文件默认返回
                string errorMessage = string.Empty;
                var postedFile = Request.Files["upfile"];//获取上传的文件
                bool validateResult = ValidateAndReadExcelFile(postedFile, out DataTable dt, out errorMessage, query);

                var model = DataTableToCustomOrder(dt);
                if (!validateResult)
                    return FalidResult(errorMessage, JsonExtension.ToJson(model));

                var importSetSettlements = MatchAndValidateData(dt, query);
                if (query.PlatformIdAndCodes.Count == 0)
                    return FalidResult($"匹配出错啦~请你检查文档【{query.CustomSkuOrSkuColumnName}】列内容，!使用系统模版匹配更准确喔!");
                var excelDatasModel = _settlementProductSkuService.GetImportSettlementtList(query, importSetSettlements);
                var json = JsonExtension.ToJson(excelDatasModel);
                return SuccessResult(new { dbName = nickName, Rows = json });
            }
            catch (Exception ex)
            {
                Utility.Log.WriteError($"批量导入结算价匹配出错:{ex}");
                return FalidResult($"匹配出错啦~请你手动选择对应表头信息，使用系统模版匹配更准确喔!");
            }

        }
        private ExcelDatasModel DataTableToCustomOrder(DataTable dt)
        {
            ExcelDatasModel model = new ExcelDatasModel();
            model.ColumnNames = ExcelHelper.GetColumnsByDataTable(dt).ToList();

            var rows = dt.Rows;
            for (var i = 0; i < rows.Count; i++)
            {
                List<string> row = new List<string>();
                foreach (var col in model.ColumnNames)
                {
                    row.Add(rows[i][col].ToString2());
                }
                model.Rows.Add(row);
            }
            return model;
        }

        /// <summary>
        /// 批量导入结算价
        /// </summary>
        /// <returns></returns>
        [LogForOperatorFilter("批量导入结算价格")]
        public ActionResult BulkImportOfflineSettlement()
        {
            Stopwatch stopwatch = Stopwatch.StartNew();
            Utility.Log.Debug(() => "批量导入结算价格 进来", "BulkImportOfflineSettlement.txt");
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var list = new List<ImportSetSettlementPriceDetail>();
            var now = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            var fileName = Request["FileName"].ToString2();
            var rowsJson = Request["Rows"].ToString2();
            var userType = Request["UserType"].ToInt();
            var importType = Request["ImportType"].ToString2();
            #region 前置检查
            // 验证用户类型非空
            if (string.IsNullOrWhiteSpace(userType.ToString()))
                return FalidResult("参数有误，请刷新重试");
            // 生成批次号
            var batchNo = $"{fileName}_{now}";
            var log = LogForOperatorContext.Current.logInfo;
            if (rowsJson.IsNullOrEmpty())
            {
                log.Exception = fileName.IsNullOrEmpty() ? "无导入信息" : "您当前的导入文件没有满足导入条件的行，请检查导入文件后重新导入.";
                return FalidResult(log.Exception);
            }
            try
            {
                var jarray = rowsJson.ToList<JToken>();
                var rowCount = jarray?.Count() ?? 0;
                if (rowCount == 0)
                {
                    log.Exception = fileName.IsNullOrEmpty() ? "无导入信息" : "您当前的导入文件没有满足导入条件的行，请检查导入文件后重新导入.";
                    return FalidResult(log.Exception);
                }
                for (int i = 0; i < rowCount; i++)
                {
                    try
                    {
                        var jmodel = jarray[i].ToJson().ToObject<ImportSetSettlementPriceDetail>();
                        if (jmodel == null)
                        {
                            log.Exception = $"请检查数据类型是否填写有误，如：数值类型字段";
                            return FalidResult(log.Exception);
                        }
                        list.Add(jmodel);
                    }
                    catch (Exception)
                    {
                        log.Exception = $"请检查数据类型是否填写有误";
                        return FalidResult(log.Exception);
                    }
                }
                log.Request = new LogForImportDetailModel() { BatchNo = batchNo, ImportRowCount = list.Count };
            }
            catch (Exception ex)
            {
                log.Exception = $"【批量导入结算价】导入数据解析失败：" + ex.Message;
                Utility.Log.WriteError(log.Exception);
                return FalidResult("无法解析数据，请检查数据是否符合要求.");
            }
            var errMsg = new StringBuilder();
            var index = 1;

            list.ForEach(m =>
            {
                var line = new StringBuilder();
                var tips = importType == "SkuCode" ? $"SKU编码:{m.SkuCode}" : $"SKUID:{m.SkuId}";
                if (m.ImpSetSettlementPrice < 0)
                    line.Append($"，商品ID{m.PlatformId},{tips}导入结算价格式有误,请修正");
                if (line.IsNotNullOrEmpty() && !errMsg.ToString().Contains(m.PlatformId))
                    errMsg.AppendLine($"出现错误：{line.ToString().TrimStart("，")}");
                index++;
            });
            if (errMsg.IsNotNullOrEmpty())
            {
                log.Exception = errMsg.ToString();
                return FalidResult(log.Exception);
            }
            if (list.Count == 0)
                return FalidResult("数据导入失败，请检查输入的结算价");
            #endregion

            Utility.Log.Debug(() => "批量导入结算价格 前置检查完成", "BulkImportOfflineSettlement.txt");

            #region 处理
            try
            {
                ///商家结算设置
                List<ProductSettlementPrice> productSettlementPriceList = new List<ProductSettlementPrice>();
                ///商品规格关联表
                List<SettlementProductSku> settlementProductSkuList = new List<SettlementProductSku>();
                foreach (var item in list)
                {
                    ///结算价相关信息
                    productSettlementPriceList.Add(new ProductSettlementPrice
                    {
                        CreateUser = fxUserId,///当前登录用户
                        FxUserId = item.ToFxUserId,//对应的厂家/商家
                        SettlementType = userType,//1厂家，2商家
                        ProductCode = item.ProductCode,
                        ProductSkuCode = item.ImportSkuCode,
                        PlatformType = item.PlatformType,
                        Price = item.ImpSetSettlementPrice,
                        CreateTime = DateTime.Now,
                        ShopId = item.ShopId,
                        SkuName = item.Name
                    });
                    ///构建商品规格关联
                    if (!settlementProductSkuList.Any(x => x.SkuCode == item.ImportSkuCode && x.PathFlowCode == item.PathFlowCode))
                        settlementProductSkuList.Add(new SettlementProductSku
                        {
                            ProductCode = item.ProductCode,
                            SkuCode = item.ImportSkuCode,
                            PathFlowCode = item.PathFlowCode,
                            SourceShopId = item.ShopId,
                            SourceUserId = item.SourceUserId,
                            FxUserId = item.ToFxUserId,//发货人
                            LastFxUserId = item.UpFxUserId,//最后一级商人
                            CreateTime = DateTime.Now,
                            Status = 0,
                            DataSource = 1,///用于区分非导入数据
                            OrderItemCode = null
                        });
                }
                ///根据配置判断处理方式数量少于配置直接导入处理
                if (productSettlementPriceList.Count <= CustomerConfig.ImporSettlementPriceCountOfTask)
                {
                    Utility.Log.Debug(() => "根据配置判断处理方式数量少于配置直接导入处理", "BulkImportOfflineSettlement.txt");

                    ///绑定商品关系
                    _settlementProductSkuService.AddSettlementProductSku(settlementProductSkuList, true);
                    ////保存结算价
                    var count = _financialSettlementService.SetProductSettlementPrice(productSettlementPriceList, fxUserId, needSetIdFromDb: true);
                    if (count < 1) return FalidResult("设置结算价失败");
                    return SuccessResult();
                }
                else
                {
                    Utility.Log.Debug(() => "根据配置判断处理方式数量少于配置直接导入处理（反）", "BulkImportOfflineSettlement.txt");

                    ///1.使用阿里云的对象存储服务,将处理的数据转以JSON文本的格式存入
                    var jsonContent = JsonConvert.SerializeObject(list);
                    byte[] bytes = Encoding.UTF8.GetBytes(jsonContent);
                    fileName = Guid.NewGuid().ToString() + ".json";

                    Utility.Log.Debug(() => "准备进行文件上传", "BulkImportOfflineSettlement.txt");

                    var result = CloudStorageUploaderFactory.Uploader("Alibaba", "AfterSale", SiteContext.Current.CurrentFxUserId.ToString(), fileName, bytes);

                    Utility.Log.Debug(() => $"文件上传完成 result.Success={result.Success}", "BulkImportOfflineSettlement.txt");

                    if (result.Success)
                    {
                        string objectKey = result.Data.Substring(result.Data.LastIndexOf(".com/") + ".com/".Length);
                        ImportSettlementPriceHandleMessageService.SendMessage(new ImportSettlementPriceHandleMessageModel
                        {
                            Url = result.Data,
                            SettlementType = userType,
                            BucketName = "fx-aftersale",
                            ObjectName = objectKey,
                            FxUserId = fxUserId,
                            Time = DateTime.Now
                        });

                        stopwatch.Stop();
                        Utility.Log.Debug(() => $"发消息结束={stopwatch.ElapsedMilliseconds} 毫秒", "BulkImportOfflineSettlement.txt");

                        return SuccessResult("后台任务执行中，请稍后查看");
                    }
                    else
                        return FalidResult("上传处理失败，请联系客服人员");
                }
            }
            catch (Exception ex)
            {
                Utility.Log.WriteError("【批量导入结算价】导入数据处理异常：" + ex.Message);
                return FalidResult("导入结算价处理失败，请稍后再试！");
            }
            #endregion
        }

        private bool ValidateAndReadExcelFile(HttpPostedFileBase fileBase, out DataTable dt, out string errorMessage, ImportSettlementQueryModel query)
        {
            var skuOrSkuColumnName = query.ImportType == "SkuCode" ? "SKU编码" : "SKUID";
            query.CustomPriceColumnName = string.IsNullOrWhiteSpace(query.CustomPriceColumnName) ? "我设置的结算价" : query.CustomPriceColumnName;
            query.CustomProductIdColumnName = string.IsNullOrWhiteSpace(query.CustomProductIdColumnName) ? "商品ID" : query.CustomProductIdColumnName;
            query.CustomSkuOrSkuColumnName = string.IsNullOrWhiteSpace(query.CustomSkuOrSkuColumnName) ? skuOrSkuColumnName : query.CustomSkuOrSkuColumnName;
            dt = null;
            errorMessage = string.Empty;
            string exMsg = string.Empty;
            if (fileBase == null || fileBase.ContentLength == 0)
            {
                errorMessage = "没有选择文件上传";
                return false;
            }

            if (fileBase.ContentLength > 10 * 1024 * 1024)
            {
                errorMessage = "上传文件不能超过10M";
                return false;
            }

            if (!fileBase.FileName.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase) &&
                !fileBase.FileName.EndsWith(".xls", StringComparison.OrdinalIgnoreCase))
            {
                errorMessage = "匹配出错啦~只能识别xls和xlsx格式的Excel文件";
                return false;
            }
            try
            {
                if (fileBase.FileName.IndexOf(".xlsx") > -1) // 2007版本以上  
                    dt = ExcelHelper.GetExcelDataTable(out errorMessage, out exMsg, stream: fileBase.InputStream, fileExt: ".xlsx");
                else if (fileBase.FileName.IndexOf(".xls") > -1) // 2003版本  
                    dt = ExcelHelper.GetExcelDataTable(out errorMessage, out exMsg, stream: fileBase.InputStream, fileExt: ".xls");

                if (!errorMessage.IsNullOrEmpty() || !exMsg.IsNullOrEmpty())
                {
                    errorMessage = errorMessage + $"，异常信息：{exMsg}";
                    return false;
                }
                ////这里需要判断返回的DT是否包含（商品ID、我设置的结算价、SKU编码、SKUID）
                if (!dt.Columns.Contains(query.CustomProductIdColumnName) || !dt.Columns.Contains(query.CustomPriceColumnName) || !dt.Columns.Contains(query.CustomSkuOrSkuColumnName))
                {
                    errorMessage = "匹配出错啦~请你手动选择对应表头信息，使用系统模版匹配更准确喔!";
                    return false;
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"读取Excel文件失败: {ex.Message}";
                return false;
            }
            if (dt == null || dt.Rows.Count == 0)
            {
                errorMessage = "匹配出错啦~Excel文件为空或格式不正确";
                return false;
            }
            return true;
        }

        private List<ImportSetSettlementPriceModel> MatchAndValidateData(DataTable dt, ImportSettlementQueryModel query)
        {
            List<ImportSetSettlementPriceModel> impList = new List<ImportSetSettlementPriceModel>();
            query.PlatformIdAndCodes = new List<ImpotPlatformIdAndCode>();
            #region 匹配数据
            try
            {
                var isUsingSkuCode = query.ImportType == "SkuCode";
                // 使用HashSet来避免重复添加
                var uniqueHashSetCodes = new System.Collections.Generic.HashSet<string>();
                var uniqueCode = string.Empty;///商品ID+SKU编码或SKUID
                foreach (DataRow row in dt.Rows)
                {
                    var platformId = row.Field<string>(query.CustomProductIdColumnName);
                    var importingSettlementPrice = row.Field<string>(query.CustomPriceColumnName);
                    var code = row.Field<string>(query.CustomSkuOrSkuColumnName).Trim(); //isUsingSkuCode ? row.Field<string>("SKU编码") : row.Field<string>("SKUID");
                    decimal settlementPrice = 0;//导入的结算价

                    #region 导入价格校验
                    try
                    {
                        settlementPrice = Convert.ToDecimal(importingSettlementPrice);
                    }
                    catch
                    {
                        settlementPrice = 0;
                    }
                    #endregion
                    var model = new ImportSetSettlementPriceModel
                    {
                        PlatformId = platformId,
                        ImpSetSettlementPrice = settlementPrice,
                        SkuCode = isUsingSkuCode ? code : null,
                        SkuId = isUsingSkuCode ? null : code,
                        IsException = true,
                        ExceptionMessage = string.Empty
                    };
                    uniqueCode = platformId + "_" + code;
                    // 避免重复处理
                    if (!uniqueHashSetCodes.Add(uniqueCode))
                    {
                        model.IsException = false;
                        model.ExceptionMessage = "当前规格有重复导入，请检查导入信息，避免重复设置";
                        impList.Add(model);
                        continue;
                    }


                    // 检查是否符合导入类型要求
                    if (string.IsNullOrWhiteSpace(code))
                    {
                        model.IsException = false;
                        model.ExceptionMessage = $"当前数据有误，未能参与匹配数据";
                        impList.Add(model);
                        continue;
                    }
                    // 添加到PlatformIdAndCodes集合
                    query.PlatformIdAndCodes.Add(new ImpotPlatformIdAndCode
                    {
                        Code = code,
                        PlatformId = platformId,
                        CodeType = query.ImportType
                    });
                    // 避免重复规格导入
                    if (impList.Any(p => p.PlatformId == platformId && (isUsingSkuCode ? p.SkuCode == code : p.SkuId == code)))
                    {
                        model.IsException = false;
                        model.ExceptionMessage = "当前规格有重复导入，请检查导入信息，避免重复设置";
                        impList.Add(model);
                        continue;
                    }
                    impList.Add(model);
                }
            }
            catch (Exception ex)
            {
                Utility.Log.WriteError($"批量导入结算价匹配】匹配出错啦：{ex.Message}");
            }
            return impList;
            #endregion

        }
        #endregion

        /// <summary>
        /// 财务结算批量弹窗设置
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost]
        public ActionResult BatchSettlementPopup(BatchSettlementPopupReq req)
        {
            if (!req.Dic.Any()) return FalidResult("参数有误");

            var commonSettingService = new CommonSettingService();
            var shopId = SiteContext.Current.CurrentShopId;
            int errCount = _financialSettlementService.BatchSettlementPopup(shopId, req.Parms);
            if (errCount > 0) return FalidResult("保存配置失败") ;
            return SuccessResult();
        }

        /// <summary>
        /// 获取-财务结算批量弹窗设置
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public ActionResult GetBatchSettlementPopup()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            int fxUserId = SiteContext.Current.CurrentFxUserId;
            List<BatchSettlementPopupModel> resultData = _financialSettlementService.GetBatchSettlementPopup(fxUserId, shopId);
            return SuccessResult(resultData) ;
        }
    }
}