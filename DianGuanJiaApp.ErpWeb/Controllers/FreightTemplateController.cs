using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.SettingsService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Web.Mvc;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Entity;
using System;

namespace DianGuanJiaApp.ErpWeb.Controllers
{
    [SessionState(System.Web.SessionState.SessionStateBehavior.Disabled)]
    public class FreightTemplateController : BaseController
    {
        public readonly ShippingFeeTemplateService _shippingFeeTemplateService = new ShippingFeeTemplateService();
        public readonly BusinessSettingsService BusinessSettingsService = new BusinessSettingsService();
        public readonly FxAlibabaBuyerShopRelationService _fxAlibabaBuyerShopRelationService = new FxAlibabaBuyerShopRelationService();
        private readonly SupplierUserService _supplierUserService = new SupplierUserService();
        private readonly FxUserShopService _fxUserShopService = new FxUserShopService();
        private readonly ShopService _shopService = new ShopService();
        private readonly CommonSettingService _commonSettingService = new CommonSettingService();
        
        [FxAuthorize(FxPermission.NewShippingFeeSet)]
        public ActionResult NewShippingFeeSet()
        {
            //获取相关配置值
            var settings = BusinessSettingsService.GetsByCurrentUser(new List<string>
            {
                BusinessSettingKeys.SupplyBy1688.ShippingFeeRule
            });
            //运费规则
            ViewBag.ShippingFeeRule = settings
                .FirstOrDefault(m => m.Key == BusinessSettingKeys.SupplyBy1688.ShippingFeeRule)?.Value;
            return View();
        }

    }
}