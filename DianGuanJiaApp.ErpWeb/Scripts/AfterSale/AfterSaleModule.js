
///售后单记录
var handleSingleAgreeRefund = null;
var handleSingleRefuseRefund = null;
var handleBatchAgreeAfterSalesType = null;
var handleSingleExchangeGoods = null;
var handleSingleExtendDeliveryPeriod = null;
var handleSingleDirectRefund = null;
var triggerUploadAfterSalesImage = null;
var removeUploadAfterSalesImage = null;
var changeAfterSalesImageFile = null;
var selectAgreeRefundAddressRadio = null;
var showPlatformAfterSalesAddress = null;
var confirmAgreeRefundAddress = null;
var cancelAgreeRefundAddress = null;
var showAgreeRefundAddressSelectDailog = null;
var openSupplierAfterSaleDrawer = null;
var closeFullMaskSupplierAddress = null;
var confirmEnableAutoSync = null;
var saveSupplierAfterSalesAddress = null;
var editSupplierAfterSalesAddress = null;
var openAfterSalesAddressLog = null;
var searchAfterSalesAddressData = null;
var resetAfterSalesAddressData = null;
var switchAddressSetting = null;
var commonFocusInputAction = null;
var commonChangeSelectAction = null;
var setSupplierAfterSale = null;
var AfterSaleModule = (function (module, common, $, layer) {
    var _pageIndex = 1, _pageSize = PageSize || 10;
    var afterSaleDatas = [];

    var reqSupplierAddressModel = {
        PageIndex: 1,
        PageSize: 50,
        AccountNumber: '', // 厂家账号
        IsAutosync: '', // 地址来源 0: 自定义 1: 自动同步
        Isimprove: '', // 是否需要改善 1：已完善 2：未完善
    }

    var SupplierAddressTableData = []; // 厂家地址列表数据

    var saveEditSupplierAddressData = [];

    var afterSalesRejectReasonData = [];

    var afterSalesRejectCertificateImage = ''; // 拒绝退款-上传售后凭证

    var selectedAddressType = 'supplier';

    var afterSalesAddrLogModel = {
        PageIndex: 1,
        PageSize: 50
    }

    var isEnableAutoSync = false;

    var OrderAgreeReturnAndRefundParams = {
        Ctype: null,
        AfterSaleCodeList: [],
        TargetFxuserId: null,
        Address: null
    }

    var ReceiverAddressId = ''; // 平台售后地址Id
    var ReceiverAddressInfo = ''; // 平台售后地址

    var SupplierSupportArea = []; // 厂家售后地址

    var totalCheckeEffectAfterSaleCodeList = []; // 所有生效的售后单
    var checkeEffectSupplierAfterSaleCodeList = []; // 生效的厂家售后单

    var isShowSupplierAddressSelect = false; // 默认不展示厂家售后地址选择

    var isPlatformAfterSalesOperationAuth = false; // 平台售后操作权限

    module.navTarUrl = function (url) {
        var newUrl = commonModule.rewriteUrlToMainDomain(url);//token
        common.OpenNewTab(newUrl);
    }
    module.navTarUrlV2 = function (url) {
        var newUrl = commonModule.rewriteUrl(url);//token
        window.location.href = newUrl;
    }

    // 底部栏 标记包裹已拦截
    $(document).ready(function () {
        var currentPlatformType = commonModule.getQueryVariable('pt');
        if (currentPlatformType && currentPlatformType === 'toutiao') {
            $("#SupplierAddress").show();
        } else {
            $("#SupplierAddress").hide();
        }
        wuFormModule.initSelect('#searchWrap_Select_key');
        wuFormModule.initMySelect("#searchWrap_Select_key");
        wuFormModule.initblurInput('#searchWrap_Select_key');
        wuFormModule.initLayuiSelect('active-select-filter');
        wuFormModule.initblurInput('#supplier_aftersale_address_form');
        // 监听下拉框的选中值变化事件
        $(document).on('change', '.interceptSelect', function (e) {
            var selectedValue = $(this).val();
            var btn = $('.setInterceptStatus');
            if (selectedValue == 1) {
                btn.text('标记包裹已拦截');
                btn.attr("data-status", "1");
                btn.css("background-color", "orange");
            } else if (selectedValue == 0) {
                btn.text('批量取消标记');
                btn.attr("data-status", "0");
                btn.css("background-color", "grey");
            }
        });
        var type = commonModule.CloudPlatformType.toLowerCase();
        if (type !== 'pinduoduo' && type !== 'jingdong') {
            $('<option>', { 
                value: 'afterSaleStatusToFinalTime',
                text : '售后完结时间' 
            }).appendTo('#QueryDateType');
        }else{
            $('#QueryDateType option[value="afterSaleStatusToFinalTime"]').remove();
        }
        var tarHref = window.location.href;         
        if (tarHref.indexOf("/AfterSale/Index") != -1) {
            commonModule.monitoringWithoutOperation(); //监控用户操作
        }
        window.addEventListener('message', function (e) {
            if (e.data.operateType && e.data.operateType == "skuRelation") {   //ifarme关联商品
                $("#PrepareDistributionIframeWrap").removeClass('active');
                if (!window.tarSkuRelationListWrap) {
                    $('body').css({ overflow: 'auto' });
                }
                if (e.data.type == 'refresh') {
                    module.LoadList();
                }
            }
        });
        var currentPt = commonModule.getQueryVariable('pt');
        if (currentPt === 'toutiao') {
            getPlatformAfterSalesOperationAuth();
        }
    });

    var OrderCombine = null;
    var supportPlatformtTypes = ["TouTiao", "Pinduoduo", "Taobao", "KuaiShou", "XiaoHongShu", "WxVideo", "Jingdong", "DuXiaoDian", "Alibaba", "AlibabaC2M","BiliBili"];
    module.LoadList = function (isPaging) {
        
        var reqModel = _getAfterSaleRequertModel(isPaging) || "";
        //console.log(reqModel);
        if (reqModel != null && reqModel != "") {
            common.Ajax({
                url: '/AfterSale/LoadAfterSaleList',
                loadingMessage: "查询中...",
                showMasker: false,
                data: reqModel,
                success: function (rsp) {
                    if (rsp.Success == false && rsp.ErrorCode == 'FX_PAGEDEPTHCONTROL') {
                        common.PageDepthControlDailg('当前操作频繁，请使用导出功能！');
                    } else {
                        afterSaleDatas = rsp.Data ? rsp.Data.List : [];
                        for (var x = 0; x < afterSaleDatas.length; x++) {
                            var item = afterSaleDatas[x];
                            if (item.Orders == null || item.Orders == undefined) {
                                item.Orders = [];
                            }
                            ///1688采购单
                            if (item.PurchaseAfterSaleOrder != null) {
                                if (item.PurchaseAfterSaleOrder.Orders[0] == null) {
                                    item.PurchaseAfterSaleOrder.Orders = []
                                }
                            }
                            if (item.Orders && item.Orders.length) {
                                for (var j = 0; j < item.Orders.length; j++) {
                                    var jData = item.Orders[j];
                                    if (jData.Items && jData.Items.length) {
                                        jData.Items = commonModule.listTransformImgSrc(jData.Items)
                                        //for (var k = 0; k < jData.Items.length; k++) {
                                        //    var kData = jData.Items[k];
                                        //    if (kData.IsRelationBaseProduct) {
                                        //        if (kData.BaseProductInfo && kData.BaseProductInfo.ProductImgUrl) {
                                        //            kData.BaseProductInfo.ProductImgUrl = commonModule.newTransformImgSrc(kData.BaseProductInfo.ProductImgUrl, '&businessType=baseproduct');
                                        //        }
                                        //        if (kData.PtProductInfo && kData.PtProductInfo.ProductImgUrl) {
                                        //            kData.PtProductInfo.ProductImgUrl = commonModule.newTransformImgSrc(kData.PtProductInfo.ProductImgUrl, '&businessType=baseproduct');
                                        //        }
                                        //        kData.ProductImgUrl = commonModule.newTransformImgSrc(kData.ProductImgUrl, '&businessType=baseproduct');
                                        //    }
                                        //}
                                    }
                                }
                            }
                           item.Index = x;
                        }
                        afterSaleDatasList(afterSaleDatas);
                        $.views.converters({
                            upperImgSrc: function (src) {
                                return newTransformImgSrc(src);//转大写
                            }
                        })
                        var sourceFlag = $("#sourceflag .active").attr("data-sourceflag") || "0";
                        commonModule.getBaseProductSetting(function (rspData) {
                            OrderCombine = rspData.OrderCombine;
                            //2.渲染
                            var tplt = $.templates("#aftersale-detail-tmpl");
                            var html = tplt.render({
                                afterSaleDatas: afterSaleDatas,
                                OrderCombine: OrderCombine,
                                supportPlatformtTypes: supportPlatformtTypes,
                                sourceFlag: sourceFlag,
                                currentUserId: _fxUserId,
                                isPlatformAfterSalesOperationAuth: isPlatformAfterSalesOperationAuth
                            });
                            $("#ShareAfterSaleList_body").html(html);
                        })
                        if (isPaging == true) {
                            return;
                        }

                        layui.laypage.render({
                            elem: 'paging',
                            theme: ' wu-page wu-one',
                            count: rsp.Data && rsp.Data.Total || 0,
                            limit: reqModel.PageSize,
                            curr: reqModel.PageIndex,
                            limits: [50, 100, 150, 200, 300, 500],
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                            jump: function (obj, first) {
                                if (!first) {
                                    _pageIndex = obj.curr;
                                    var isPageSizeChange = false;
                                    if (_pageSize != obj.limit) {
                                        _pageSize = obj.limit;
                                        commonModule.SaveCommonSetting("/ErpWeb/AfterSale/PageSize", obj.limit, function (rsp) {
                                            if (rsp.Success) {
                                                _pageSize = obj.limit;
                                                isPageSizeChange = true;
                                            }
                                        });
                                    }
                                    _pageSize = obj.limit;
                                    module.LoadList(true);
                                }
                            }
                        });
                    }
                }
            });

            $("#orderNum").text("[0]");
            $("#allcheckorder").prop("checked", false);

        }
    }

    function agreeRefundAfterSalesResult(data) {
        var tplt = $.templates("#agree_refund_after_sales_result");
        var html = tplt.render(data);
        layer.open({
            type: 1,
            title: '系统提示',
            content: html,
            area: '560px', //宽高
            skin: 'wu-dailog refundAfterSalesResultDailog',
            success: function () { },
            btn: ['关闭'],
            btn1: function (index) {
                layer.close(index);
            },
            end: function () {
                module.LoadList(false);
            }
        });
    }

    triggerUploadAfterSalesImage = function () {
        $("#AfterSalesImageFile").click();
    }

    removeUploadAfterSalesImage = function () {
        console.log('删除图片');
        $("#after_sales_image_empty").show();
        $("#after_sales_image_content").hide();
        $("#after_sales_image_certificate").attr("src", '');
        afterSalesRejectCertificateImage = '';
    }

    changeAfterSalesImageFile = function (_this) {
        console.log(_this.files)
        var selectedPicFile = _this.files[0];
        //if (!$$.checkFileExt(selectedPicFile.name, ["jpg", "png", "jpeg"])) {
        //    wuFormModule.wu_toast({ type: 3, content: '请上传jpg/png/jpeg格式的文件' });
        //    return;
        //}
        if (selectedPicFile.size > 5000000) { // 大于5m不上传
            wuFormModule.wu_toast({ type: 3, content: '文件大小不能大于5M' });
            return;
        }
        // 新建一个文件对象 读取文件信息  主要作显示用的 把flies图片转为base64位
        var fileRead = new FileReader();
        fileRead.readAsDataURL(_this.files[0]);
        fileRead.addEventListener("load", function () {
            var param = {};
            var imgBase64 = this.result;
            // 开始上传图片
            var param = {
                fileName: _this.files[0].name.toLowerCase(),
                fileContent: imgBase64,
                businessType: "AfterSale"
            };
            commonModule.Ajax({
                url: '/Common/UploadImageFile',
                type: 'POST',
                data: param,
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return;
                    }
                    if (rsp.IsOk) {
                        var imgSrc = newTransformImgSrc(rsp.TransitUrl);
                        $("#after_sales_image_empty").hide();
                        $("#after_sales_image_content").show();
                        $("#after_sales_image_certificate").attr("src", imgSrc);
                        afterSalesRejectCertificateImage = imgSrc;
                        // 重置文件输入框的 value，避免缓存
                        _this.value = "";
                    }
                }
            });
        })
    }

    // 同意仅退款
    function agreeRefundOperation(operate, status, ctype, afterSaleCodeList) {
        var html = '';
        html += '<div class="wu-f14 wu-c09">';
        if (status == '1') { // 已发货仅退款
            html += '<div>1. 退款成功会同步到平台，直接将资金退给买家；</div>';
            html += '<div class="wu-mT8">2. 已发货的售后订单，需要线下联系快递追回。</div>';
        } else { // 未发货仅退款
            html += '退款成功会同步到平台，直接将资金退给买家。';
        }
        html += '</div>';
        layer.open({
            type: 1,
            title: operate == 'single' ? '同意退款' : '批量同意退款',
            content: html,
            area: '560px', //宽高
            skin: 'wu-dailog',
            success: function () { },
            btn: ['确定', '取消'],
            btn1: function (index) {
                commonModule.Ajax({
                    url: '/AfterSale/AgreeToRefund',
                    type: "POST",
                    data: {
                        Ctype: ctype,
                        AfterSaleCodeList: afterSaleCodeList,
                    },
                    success: function (rsp) {
                        layer.close(index);
                        if (rsp.Success) {
                            var resData = rsp.Data;
                            var returnDataList = [];
                            resData.OperateResult.forEach(function (item) {
                                if (!item.Success) {
                                    returnDataList.push(item.ReturnData);
                                }
                            });
                            var Results = {
                                refundFailData: returnDataList,
                                successCount: resData.SuccessCount,
                                failCount: resData.ErrorCount,
                                title: '同意退款',
                                refundType: 'agreeRefund'
                            }
                            agreeRefundAfterSalesResult(Results);
                        } else {
                            wuFormModule.wu_toast({ type: 2, content: rsp.Message });
                        }
                    }
                });
            },
            btn2: function (index) {
                layer.close(index);
            },
        });
    }
    // 同意退款
    handleSingleAgreeRefund = function (operate, status, afterSaleType, afterSaleCode, downFxUserId) {
        console.log("同意退款", operate, downFxUserId);
        console.log('发货状态', typeof status);
        SupplierSupportArea = [];
        var title = '同意退货退款';
        var ctype = '';
        var list = [afterSaleCode];
        // 判断是否包含了代发订单
        // DownFxUserId等于0是自营的，DownFxUserId大于0是代销
        // 选择的订单包含了代发订单
        if (downFxUserId > 0) {
            isShowSupplierAddressSelect = true;
        } else {
            isShowSupplierAddressSelect = false;
        }
        var currentRowOrder = [
            {
                afterSaleCode: afterSaleCode,
                afterSaleType: Number(afterSaleType),
                downFxUserId: Number(downFxUserId)
            }
        ]
        // 退货退款
        if (afterSaleType == 0) {
            ctype = 3;
            title = '同意退货退款';
            agreeReturnGoodsRefundAddressSelect(operate, title, ctype, list, currentRowOrder);
        }
        // 仅退款
        if (afterSaleType == 1) {
            if (status == '0') {
                ctype = 1; // 未发货仅退款
            } else {
                ctype = 2; // 已发货仅退款
            }
            agreeRefundOperation(operate, status, ctype, list);
        }
        // 换货
        if (afterSaleType == 2) {
            ctype = 4;
            title = '同意换货';
            agreeReturnGoodsRefundAddressSelect(operate, title, ctype, list, currentRowOrder);
        }
    }

    function getRejectReasonData(ctype, afterSaleCode) {
        commonModule.Ajax({
            url: '/AfterSale/GetRejectReason?ctype=' + ctype + '&afterSaleCode=' + afterSaleCode,
            type: "GET",
            data: {},
            success: function (rsp) {
                if (commonModule.IsError(rsp)) {
                    return;
                }
                if (rsp.Success) {
                    afterSalesRejectReasonData = rsp.Data || [];
                    if (afterSalesRejectReasonData && afterSalesRejectReasonData.length > 0) {
                        var select = $('#reject_reason_select');
                        select.empty(); // 清空现有选项
                        // select.append('<option value="">请选择原因</option>'); // 先添加默认选项
                        $(afterSalesRejectReasonData).each(function (i, s) {
                            select.append('<option value="' + s.reject_reason_code + '">' + s.reason + '</option>');
                        });
                        layui.form.render('select');
                    }
                }
            }
        });
    }
    // 拒绝退款-已发货仅退款
    handleSingleRefuseRefund = function (afterSaleType, afterSaleCode) {
        var title = '拒绝退款';
        //if (afterSaleType === 1) { // 未发货仅退款
        //    var tplt = $.templates("#no_ship_refuse_refund_after_sales_tpl");
        //    var html = tplt.render({
        //        refundType: 'refuseRefund'
        //    });
        //    var selectInit = {
        //        eles: '#logistics_company_after_sales',
        //        emptyTitle: '全部物流公司',
        //        data: [{
        //            Value: '1',
        //            Text: '顺丰快递'
        //        }],
        //        searchType: 1,
        //        isRadio: true,
        //        allSelect: false,
        //        selectData: [],
        //        skin: 'wu-select-skin',
        //    };
        //}
        var ctype = '';
        // 退货退款
        if (afterSaleType == 0) {
            title = '拒绝退货退款';
            ctype = 3;
        }
        // 仅退款
        if (afterSaleType == 1) {
            title = '拒绝退款';
            ctype = 2;
        }
        // 换货
        if (afterSaleType == 2) {
            title = '拒绝换货';
            ctype = 4;
        }
        var tplt = $.templates("#ship_refuse_refund_after_sales_tpl");
        var html = tplt.render({});
        layer.open({
            type: 1,
            title: title,
            content: html,
            area: '560px', //宽高
            skin: 'wu-dailog',
            success: function () {
                //if (status === 1) {
                //    var selectBox = new selectBoxModule2();
                //    selectBox.initData(selectInit);
                //}
                layui.use(['form'], function () {
                    var form = layui.form;
                    // 显示弹框后渲染 select 组件
                    form.render('select');
                });
                getRejectReasonData(ctype, afterSaleCode);
            },
            btn: ['确定', '取消'],
            btn1: function (index) {
                var reasonValue = $("#reject_reason_select").val();
                var reasonText = afterSalesRejectReasonData.find(function (item) {
                    return item.reject_reason_code == reasonValue;
                });
                console.log("reasonText", reasonText);
                if (reasonValue == '') {
                    wuFormModule.wu_toast({ type: 3, content: '请选择拒绝原因' });
                    return false;
                }
                if (afterSalesRejectCertificateImage == '') {
                    wuFormModule.wu_toast({ type: 3, content: '请上传图片' });
                    return false;
                }
                commonModule.Ajax({
                    url: '/AfterSale/RefuseRefund',
                    type: "POST",
                    data: {
                        Ctype: ctype,
                        AfterSaleCode: afterSaleCode,
                        RejectReasonCode: reasonValue,
                        Reason: reasonText.reason,
                        ImgUrl: afterSalesRejectCertificateImage,
                        Remark: $("#after_sales_remark_textarea").val()
                    },
                    success: function (rsp) {
                        layer.close(index);
                        var resData = rsp.Data;
                        if (resData.ErrorCount === 0) {
                            var Results = {
                                rejectFailReason: '',
                                title: title + '成功',
                                refundType: 'refuseRefund'
                            }
                            agreeRefundAfterSalesResult(Results);
                        } else {
                            var Results = {
                                rejectFailReason: resData.OperateResult[0].Message,
                                title: title + '失败',
                                refundType: 'refuseRefund'
                            }
                            agreeRefundAfterSalesResult(Results);
                        }
                    }
                });
            },
        });
    }
    // 立即换货
    handleSingleExchangeGoods = function () {
        var tplt = $.templates("#no_ship_refuse_refund_after_sales_tpl");
        var html = tplt.render({
            refundType: 'exchangeGoods'
        });
        var selectInit = {
            eles: '#logistics_company_after_sales',
            emptyTitle: '全部物流公司',
            data: [{
                Value: '1',
                Text: '顺丰快递'
            }, {
                Value: '2',
                Text: '圆通快递'
            }, {
                Value: '3',
                Text: '邮政快递'
            }, {
                Value: '4',
                Text: '邮政快递'
            }, {
                Value: '5',
                Text: '邮政快递'
            }],
            searchType: 1,
            isRadio: true,
            allSelect: false,
            selectData: [],
            skin: 'wu-select-skin',
        };

        layer.open({
            type: 1,
            title: '立即换货',
            content: html,
            area: '560px', //宽高
            skin: 'wu-dailog',
            success: function () {
                var selectBox = new selectBoxModule2();
                selectBox.initData(selectInit);
            },
            btn: ['确定', '取消'],
            btn1: function (index) {
                layer.close(index);
                var Results = {
                    rejectFailReason: "",
                    title: '换货成功',
                    refundType: 'exchangeGoods'
                }
                agreeRefundAfterSalesResult(Results);
            },
        });
    }
    // 延长收货
    handleSingleExtendDeliveryPeriod = function () {
        var html = '';
        html += '<div class="wu-f14 wu-c09">您可以发起一次收货延长，支持延长7天</div>';
        html += '<div class="wu-f14 wu-c09">请在延长后的时间内完成售后处理，否则系统将自动为用户退款</div>';
        html += '<div class="wu-f14 wu-c06 wu-mT16">用户发货5天后可延长收货</div>';
        layer.open({
            type: 1,
            title: '延长收货',
            content: html,
            area: '560px', //宽高
            skin: 'wu-dailog',
            success: function () {
                
            },
            btn: ['确定', '取消'],
            btn1: function (index) {
                layer.close(index);
                var Results = {
                    rejectFailReason: "",
                    title: '延长收货成功',
                    refundType: 'extendDeliveryPeriod'
                }
                agreeRefundAfterSalesResult(Results);
            },
        });
    }
    // 直接退款
    handleSingleDirectRefund = function () {
        layer.open({
            type: 1,
            title: '直接退款',
            content: '<div class="wu-f14 wu-c09">请确认是否直接退款，无需换货？确认后不可撤销，将在1-3个工作日内完成退款</div>',
            area: '560px', //宽高
            skin: 'wu-dailog',
            success: function () {

            },
            btn: ['确定', '取消'],
            btn1: function (index) {
                layer.close(index);
                var Results = {
                    rejectFailReason: "",
                    title: '操作成功',
                    refundType: 'directRefund'
                }
                agreeRefundAfterSalesResult(Results);
            },
        });
    }
    // 将勾选的店铺id去重
    function uniqueShopIds(shopIds) {
        var list = shopIds.filter(function (value, index, self) {
            return self.indexOf(value) === index;  // 判断当前值的索引是否等于它第一次出现的索引
        });
        return list;
    }

    // 批量同意换货、退货、退款
    handleBatchAgreeAfterSalesType = function (type) {
        SupplierSupportArea = [];
        var batchTypeMapping = {
            exchangeGoods: '换货',
            returnGoods: '退货',
            refund: '仅退款'
        }
        $(this).addClass("wu-active").siblings('li').removeClass('wu-active');
        var $checkedOrderData = $(".order-chx:checked");
        if (!$checkedOrderData.length) {
            wuFormModule.wu_toast({ type: 3, content: '请先勾选订单' });
            return;
        }
        var shopIds = []; // 店铺id
        var afterSaleTypes = []; // 退货退款：0, 仅退款：1, 换货：2
        var afterSaleCodeList = []; // 售后单编码
        var checkedAfterSaleOrder = [];
        var downFxUserIds = []; // 0代表自营
        var ctype = '';
        var title = type === 'returnGoods' ? '批量同意退货退款' : '批量同意换货';
        var tip = batchTypeMapping[type];
        var afterSaleStatusList = [];
        var sendState = $("#layui_mynav_childrenNav .active").attr("data-sendstate");
        $checkedOrderData.each(function () {
            var shopId = $(this).attr("data-shopid"); // 获取每个节点的 data-shopid 属性值
            var afterSaleType = $(this).attr("data-aftersaletype");
            var afterSaleCode = $(this).attr("data-ascode");
            var downFxUserId = $(this).attr("data-downfxuserid");
            var afterSaleStatus = $(this).attr("data-aftersalestatus");
            shopIds.push(shopId);
            afterSaleTypes.push(Number(afterSaleType));
            downFxUserIds.push(Number(downFxUserId));
            afterSaleStatusList.push(Number(afterSaleStatus));
            checkedAfterSaleOrder.push({
                afterSaleCode: afterSaleCode,
                afterSaleType: Number(afterSaleType),
                downFxUserId: Number(downFxUserId),
                afterSaleStatus: Number(afterSaleStatus)
            });
        });
        console.log("checkedAfterSaleOrder", checkedAfterSaleOrder);
        // 判断是否包含了代发订单
        // DownFxUserId等于0是自营的，DownFxUserId大于0是代销
        var hasConsignmentOrder = downFxUserIds.some(function (count) {
            return count > 0;
        });
        // 选择的订单包含了代发订单
        if (hasConsignmentOrder) {
            isShowSupplierAddressSelect = true;
        } else {
            isShowSupplierAddressSelect = false;
        }
        var selectedShopIds = uniqueShopIds(shopIds);
        if (selectedShopIds.length > 1) {
            wuFormModule.wu_toast({ type: 3, content: '【同意' + batchTypeMapping[type] + '】' + '不能同时处理多个店铺的订单，请重新选择' });
            return;
        }
        // 退货退款
        if (type === 'returnGoods') {
            ctype = 3;
            // 售后单状态-判断当前勾选的数据是否包含【待商家处理】
            if (!afterSaleTypes.includes(0) || !afterSaleStatusList.includes(1)) {
                wuFormModule.wu_toast({ type: 3, content: '你所选择的售后单状态或类型不符合要求' });
                return;
            }
            var hasReturnGoodsOrderList = checkedAfterSaleOrder.filter(function (item) {
                return item.afterSaleType === 0 && item.afterSaleStatus === 1;
            });
            if (!hasReturnGoodsOrderList.length) {
                wuFormModule.wu_toast({ type: 3, content: '你所选择的售后单状态或类型不符合要求' });
                return;
            }
            // 如果批量同意退货退款，包含了仅退款和换货的订单
            if (afterSaleTypes.includes(1) || afterSaleTypes.includes(2)) {
                showAgreeRefundSecondConfirm(type, tip, title, ctype, checkedAfterSaleOrder, 0);
                return;
            }
        }
        // 仅退款
        if (type === 'refund') {
            if (sendState == '0') {
                ctype = 1; // 未发货仅退款
            } else {
                ctype = 2; // 已发货仅退款
            }
            if (!afterSaleTypes.includes(1) || !afterSaleStatusList.includes(1)) {
                wuFormModule.wu_toast({ type: 3, content: '你所选择的售后单状态或类型不符合要求' });
                return;
            }
            var hasRefundOrderList = checkedAfterSaleOrder.filter(function (item) {
                return item.afterSaleType === 1 && item.afterSaleStatus === 1;
            });
            if (!hasRefundOrderList.length) {
                wuFormModule.wu_toast({ type: 3, content: '你所选择的售后单状态或类型不符合要求' });
                return;
            }
            // 如果批量同意仅退款，包含了退货退款和换货的订单
            if (afterSaleTypes.includes(0) || afterSaleTypes.includes(2)) {
                showAgreeRefundSecondConfirm(type, tip, title, ctype, checkedAfterSaleOrder, 1);
                return;
            }
        }
        // 换货
        if (type === 'exchangeGoods') {
            ctype = 4;
            if (!afterSaleTypes.includes(2) || !afterSaleStatusList.includes(1)) {
                wuFormModule.wu_toast({ type: 3, content: '你所选择的售后单状态或类型不符合要求' });
                return;
            }
            var hasExchangeGoodsOrderList = checkedAfterSaleOrder.filter(function (item) {
                return item.afterSaleType === 2 && item.afterSaleStatus === 1;
            });
            if (!hasExchangeGoodsOrderList.length) {
                wuFormModule.wu_toast({ type: 3, content: '你所选择的售后单状态或类型不符合要求' });
                return;
            }
            // 如果批量同意换货，包含了退货退款和仅退款的订单
            if (afterSaleTypes.includes(0) || afterSaleTypes.includes(1)) {
                showAgreeRefundSecondConfirm(type, tip, title, ctype, checkedAfterSaleOrder, 2);
                return;
            }
        }
      
        afterSaleCodeList = [];
        checkedAfterSaleOrder.forEach(function (item) {
            if (item.afterSaleStatus === 1) {
                afterSaleCodeList.push(item.afterSaleCode);
            }
        });
        console.log("====afterSaleCodeList====", afterSaleCodeList);

        if (type === 'refund') {
            agreeRefundOperation('batch', sendState, ctype, afterSaleCodeList);
        } else {
            agreeReturnGoodsRefundAddressSelect('batch', title, ctype, afterSaleCodeList, checkedAfterSaleOrder);
        }
    }
    function showAgreeRefundSecondConfirm(type, tip, title, ctype, checkedAfterSaleOrder, afterSaleType) {
        var html = '';
        html += '<div class="wu-f14 wu-c09">你选择的售后单中包含非';
        html += tip;
        html += '的售后单，是否忽略，继续同意';
        html += tip;
        html += '的售后单</div>';
        layer.open({
            type: 1,
            title: '系统提示',
            content: html,
            area: '560px', //宽高
            skin: 'wu-dailog',
            success: function () {},
            btn: ['忽略并继续', '取消'],
            btn1: function (index) {
                layer.close(index);
                var afterSaleCodeData = [];
                var checkedOrder = checkedAfterSaleOrder;
                checkedOrder.forEach(function (item) {
                    if (item.afterSaleType == afterSaleType && item.afterSaleStatus === 1) {
                        afterSaleCodeData.push(item.afterSaleCode);
                    }
                });
                var filterOrder = checkedOrder.filter(function (item) {
                    return item.afterSaleType == afterSaleType && item.afterSaleStatus === 1;
                });
                if (type === 'refund') {
                    var sendState = $("#layui_mynav_childrenNav .active").attr("data-sendstate");
                    agreeRefundOperation('batch', sendState, ctype, afterSaleCodeData);
                } else {
                    agreeReturnGoodsRefundAddressSelect('batch', title, ctype, afterSaleCodeData, filterOrder);
                }
            },
        });
    }
    // 获取厂家售后地址
    function getSupplierAddressData(afterSaleCode) {
        commonModule.Ajax({
            url: "/AfterSale/GetAllAfterSaleAddress?afterSaleCode=" + afterSaleCode,
            data: {},
            type: "GET",
            success: function (rsp) {
                if (rsp.Success) {
                    var PlatformAfterSalesArea = rsp.Data.PlatformAfterSalesArea;
                    if (rsp.Data.SupplierName) {
                        $("#current_supplier_name").text(rsp.Data.SupplierName);
                    }
                    SupplierSupportArea = rsp.Data.SupplierSupportArea;
                    if (SupplierSupportArea.length > 0) {
                        var addressData = SupplierSupportArea[0].Address;
                        if (addressData) {
                            $("#setting_supplier_address_info").show();
                            $("#not_supplier_address_info").hide();
                            var htmlContent = '';
                            htmlContent += '<span>' + addressData.ReceiverName + '</span>';
                            htmlContent += '<span>' + addressData.ReceiverContract + '</span>';
                            htmlContent += '<span>' + addressData.Province + addressData.City + addressData.County + addressData.Street + addressData.Address + '</span>';
                            $("#setting_supplier_address_info").html(htmlContent);
                        } else {
                            $("#setting_supplier_address_info").hide();
                            $("#not_supplier_address_info").show();
                        }
                    }
                    renderPlatformAddressSelect(PlatformAfterSalesArea);
                } else {
                    wuFormModule.wu_toast({ type: 2, content: rsp.Message });
                    $("#not_supplier_address_info").show();
                }
            }
        });
    }
    // 渲染平台售后地址下拉选
    function renderPlatformAddressSelect(platformAddressList) {
        $("#platform_after_sales_address_select").empty(); // 清空现有选项
        $(platformAddressList).each(function (i, s) {
            var liContent = '<li class="platform-after-sales-address-li" data-default="' + s.IsDefault + '" data-id="' + s.ReceiverAddressId + '" data-index="' + i + '">';
            // 只有当 i 等于 1 时，才添加默认标识
            if (s.IsDefault === 1) {
                liContent += '<span class="wu-tag wu-processing wu-mR4 defaultTag">默认</span>';
            }
            // 添加地址信息
            liContent += '<span class="platform-after-sales-address-text">' + s.ReceiverName + ' ' + s.ReceiverTel + ' ' + s.Province + s.City + s.County + s.Street + s.Address + '</span>';
            liContent += '</li>';
            // 将构建的内容添加到指定元素
            $("#platform_after_sales_address_select").append(liContent);
        });
        $("#platform_after_sales_address_select").append('<li class="wu-flex wu-center" style="padding:6px 8px;" id="agent_setting_li"><a class="wu-f14 wu-color-a wu-operate wu-flex wu-yCenter" href="https://fxg.jinritemai.com/ffa/morder/logistics/address-list?btm_ppre=a2427.b76571.c902327.d871297&btm_pre=a2427.b687601.c67024&btm_show_id=ff9a5ab8-4525-4d2d-bade-f4536a91063b" target="_blank"><i class="iconfont icon-a-setting1x1 wu-mR4"></i>前往商家后台设置</a></li>');
        // 显示默认地址
        var defaultAddressInfo = platformAddressList.find(function (item) {
            return item.IsDefault === 1;
        });
        if (defaultAddressInfo) {
            $("#show_default_tag").show();
            var allAddr = defaultAddressInfo.ReceiverName + ' ' + defaultAddressInfo.ReceiverTel + ' ' + defaultAddressInfo.Province + defaultAddressInfo.City + defaultAddressInfo.County + defaultAddressInfo.Street + defaultAddressInfo.Address;
            ReceiverAddressInfo = allAddr;
            $(".platform-after-sales-address-wrap .selected-platform-after-sales-address").text(ReceiverAddressInfo);
            ReceiverAddressId = defaultAddressInfo.ReceiverAddressId;
            OrderAgreeReturnAndRefundParams.Address = defaultAddressInfo;
        }
        $("#platform_after_sales_address_select .platform-after-sales-address-li").on("click", function (event) {
            event.stopPropagation();
            $(this).closest("#platform_after_sales_address_select").hide();
            ReceiverAddressInfo = $(this).find(".platform-after-sales-address-text").text();
            $(".platform-after-sales-address-wrap .selected-platform-after-sales-address").text(ReceiverAddressInfo);
            $(this).addClass("address-li-active");
            $(this).siblings().removeClass("address-li-active");
            var isDefault = $(this).attr("data-default");
            ReceiverAddressId = $(this).attr("data-id");
            var index = $(this).attr("data-index");
            OrderAgreeReturnAndRefundParams.Address = platformAddressList[index];
            if (isDefault == 1) {
                $("#show_default_tag").show();
            } else {
                $("#show_default_tag").hide();
            }
        });
        $("#agent_setting_li").click(function (event) {
            event.stopPropagation();
        });
    }
    // 获取平台售后地址
    function getAllPlatformAddressData(afterSaleCodeList) {
        commonModule.Ajax({
            url: "/AfterSale/GetAllAfterSaleAddressBatch",
            data: {
                AfterSaleCodeList: afterSaleCodeList
            },
            type: "POST",
            success: function (rsp) {
                if (rsp.Success) {
                    var PlatformAfterSalesArea = rsp.Data.PlatformAfterSalesArea;
                    SupplierSupportArea = rsp.Data.SupplierSupportArea;
                    // 如果是代发订单
                    if (isShowSupplierAddressSelect) {
                        $("#supplier_effect_order_count").text(SupplierSupportArea.length);
                        $("#total_effect_order_count").text(SupplierSupportArea.length);
                    } else {
                        $("#supplier_effect_order_count").text(0);
                        $("#total_effect_order_count").text(afterSaleCodeList.length);
                    }
                    renderPlatformAddressSelect(PlatformAfterSalesArea);
                } else {
                    wuFormModule.wu_toast({ type: 2, content: rsp.Message });
                }
            }
        });
    }
    // 同意退货退款-地址选择
    function agreeReturnGoodsRefundAddressSelect(operate, title, ctype, afterSaleCodeList, list) {
        console.log("订单数据", list);
        totalCheckeEffectAfterSaleCodeList = afterSaleCodeList;
        checkeEffectSupplierAfterSaleCodeList = [];
        // 选择厂家售后地址要过滤【自营订单】的afterSaleCode
        list.forEach(function (item) {
            if (item.downFxUserId > 0) {
                checkeEffectSupplierAfterSaleCodeList.push(item.afterSaleCode);
            }
        });
        OrderAgreeReturnAndRefundParams = {
            Ctype: ctype,
            AfterSaleCodeList: afterSaleCodeList,
        }
        showAgreeRefundAddressSelectDailog = layer.open({
            type: 1,
            title: title,
            content: $("#agree_return_goods_and_refund_tpl"),
            area: '640px', //宽高
            skin: 'wu-dailog refundAddressSelectDailog',
            success: function () {
                if (operate == 'single') {
                    $("#supplier_address_single_info").show();
                    $("#supplier_address_batch_info").hide();
                    $("#show_after_sales_order_effect").hide();
                    getSupplierAddressData(afterSaleCodeList[0]);
                } else {
                    $("#supplier_address_batch_info").show();
                    $("#supplier_address_single_info").hide();
                    $("#show_after_sales_order_effect").show();
                    if (isShowSupplierAddressSelect) {
                        getAllPlatformAddressData(checkeEffectSupplierAfterSaleCodeList);
                    } else {
                        getAllPlatformAddressData(afterSaleCodeList);
                    }
                }
                commonModule.LoadAreaInfoToControl('custom_address_province_select', 1, function () {}, commonSelectCallBack, "name");
                $("#agree_refund_custom_address_wrap").hide();
                $("#show_default_tag").hide();
                $("#agree_refund_custom_address_wrap .wu-inputWrap .wu-input").val('');
                $('#agree_refund_custom_address_wrap .wu-selectWrap .wu-select').val('0');
                $("#custom_address_textarea").val('');
                $("#return_remark").val('');
                $("#agree_refund_custom_address_wrap").find(".wu-inputWrap").removeClass("wu-warn").find(".wu-warn-title").hide().text("");
                $("#agree_refund_custom_address_wrap").find(".wu-selectWrap").removeClass("wu-warn");
                $("#agree_refund_custom_address_wrap .address-warn-title").hide().text("");
                $("#agree_refund_address_content").find(".agree-refund-address-radio .wu-my-radioWrap").removeClass("checked");
                $(".platform-after-sales-address-wrap .down-icon").removeClass("icon-a-chevron-up1x").addClass("icon-a-chevron-down1x");
                $(".custom-address-select").css("color", "rgba(0, 0, 0, 0.5)");
                if (isShowSupplierAddressSelect) {
                    $("#show_supplier_address_select").show();
                    $("#agree_refund_address_content").find(".supplier-address-card").addClass("checked-address-radio").siblings(".agree-refund-address-radio").removeClass("checked-address-radio");
                    $("#agree_refund_address_content").find(".supplier-address-card").find(".wu-my-radioWrap").addClass("checked");
                    selectedAddressType = 'supplier';
                } else {
                    $("#show_supplier_address_select").hide();
                    $("#agree_refund_address_content").find(".platform-address-card").addClass("checked-address-radio").siblings(".agree-refund-address-radio").removeClass("checked-address-radio");
                    $("#agree_refund_address_content").find(".platform-address-card").find(".wu-my-radioWrap").addClass("checked");
                    selectedAddressType = 'platform';
                }
            },
            end: function () {
                $("#platform_after_sales_address_select .platform-after-sales-address-li").off("click");
            },
            btn: false,
        });
    }

    // 售后地址选择
    selectAgreeRefundAddressRadio = function (type) {
        console.log("地址类型", $(this).attr("data-addressType"));
        selectedAddressType = type;
        $(this).addClass("checked-address-radio").siblings(".agree-refund-address-radio").removeClass("checked-address-radio");
        $(this).find(".wu-my-radioWrap").addClass("checked");
        $(this).siblings(".agree-refund-address-radio").find(".wu-my-radioWrap").removeClass("checked");
        
        if (type === 'supplier') {
            $("#supplier_effect_order_count").text(SupplierSupportArea.length);
            $("#total_effect_order_count").text(SupplierSupportArea.length);
        } else {
            $("#supplier_effect_order_count").text(0);
            $("#total_effect_order_count").text(totalCheckeEffectAfterSaleCodeList.length);
        }
        if (type === 'custom') {
            $("#agree_refund_custom_address_wrap").show();
        } else {
            $("#agree_refund_custom_address_wrap").hide();
        }
    }
    // 展示平台地址下拉选择
    showPlatformAfterSalesAddress = function (event) {
        event.stopPropagation();
        $("#platform_after_sales_address_select").toggle();
        $(this).find('i').toggleClass('icon-a-chevron-down1x icon-a-chevron-up1x');
    }
    // 同意-退货退款-换货-选择厂家
    function submitSupplierAgreeRefund() {
        if (!SupplierSupportArea.length) {
            wuFormModule.wu_toast({ type: 3, content: '厂家售后地址不能为空' });
            return false;
        }
        var data = {
            Ctype: OrderAgreeReturnAndRefundParams.Ctype,
            Remark: $("#return_remark").val(),
            AfterSaleAddress: SupplierSupportArea
        }
        commonModule.Ajax({
            url: "/AfterSale/AgreeToReturnRefundMill",
            data: data,
            type: "POST",
            loading: true,
            success: function (rsp) {
                layer.close(showAgreeRefundAddressSelectDailog);
                if (rsp.Success) {
                    var resData = rsp.Data;
                    var returnDataList = [];
                    resData.OperateResult.forEach(function (item) {
                        if (!item.Success) {
                            returnDataList.push(item.ReturnData);
                        }
                    });
                    var Results = {
                        refundFailData: returnDataList,
                        successCount: resData.SuccessCount,
                        failCount: resData.ErrorCount,
                        title: OrderAgreeReturnAndRefundParams.Ctype == 3 ? '同意退货退款' :'同意换货',
                        refundType: 'agreeRefund'
                    }
                    agreeRefundAfterSalesResult(Results);
                } else {
                    wuFormModule.wu_toast({ type: 2, content: rsp.Message });
                }
            }
        });
    }

    // 同意-退货退款-换货
    function submitPlatformAgreeRefund() {
        var data = {
            Ctype: OrderAgreeReturnAndRefundParams.Ctype,
            Remark: $("#return_remark").val(),
            AfterSaleCodeList: OrderAgreeReturnAndRefundParams.AfterSaleCodeList,
            AfterSaleAddress: OrderAgreeReturnAndRefundParams.Address,
            AfterSaleAddressType: selectedAddressType == 'platform' ? 2 : 3,
            ReceiverAddressId: ReceiverAddressId
        }
        commonModule.Ajax({
            url: "/AfterSale/AgreeToReturnAndRefund",
            data: data,
            type: "POST",
            loading: true,
            success: function (rsp) {
                layer.close(showAgreeRefundAddressSelectDailog);
                if (rsp.Success) {
                    var resData = rsp.Data;
                    var returnDataList = [];
                    resData.OperateResult.forEach(function (item) {
                        if (!item.Success) {
                            returnDataList.push(item.ReturnData);
                        }
                    });
                    var Results = {
                        refundFailData: returnDataList,
                        successCount: resData.SuccessCount,
                        failCount: resData.ErrorCount,
                        title: OrderAgreeReturnAndRefundParams.Ctype == 3 ? '同意退货退款' : '同意换货',
                        refundType: 'agreeRefund'
                    }
                    agreeRefundAfterSalesResult(Results);
                } else {
                    wuFormModule.wu_toast({ type: 2, content: rsp.Message });
                }
            }
        });
    }

    // 同意退货退款/换货
    confirmAgreeRefundAddress = function () {
        if (selectedAddressType == 'supplier') {
            submitSupplierAgreeRefund();
        } else {
            if (selectedAddressType == 'platform') {
                if (!ReceiverAddressInfo) {
                    wuFormModule.wu_toast({ type: 3, content: '请选择平台售后地址' });
                    return false;
                }
            }
            if (selectedAddressType == 'custom') {
                var mobileReg = /^(86-[1][0-9]{10})|(86[1][0-9]{10})|([1][0-9]{10})$/; // 手机正则
                var Name = $('#custom_address_account_name').val().trim();
                var Mobile = $('#custom_address_account_mobile').val().trim();
                var Province = $('#custom_address_province_select').val();
                var City = $('#custom_address_city_select').val();
                var County = $('#custom_address_county_select').val();
                var Street = $('#custom_address_street_select').val();
                var DetailAddr = $('#custom_address_textarea').val();
                if (Name == '') {
                    $("#custom_address_account_name").closest(".wu-inputWrap").addClass("wu-warn").find(".wu-warn-title").show().text("请输入收货人姓名");
                    return false;
                }
                if (Mobile == '') {
                    $("#custom_address_account_mobile").closest(".wu-inputWrap").addClass("wu-warn").find(".wu-warn-title").show().text("请输入手机号");
                    return false;
                }
                if (Mobile != '' && !mobileReg.test(Mobile)) {
                    $("#custom_address_account_mobile").closest(".wu-inputWrap").addClass("wu-warn").find(".wu-warn-title").show().text("手机号格式不对");
                    return false;
                }

                if ((Province && Province == "0") && (City && City == "0") && (County && County == "0") && (Street && Street == "0")) {
                    $(".custom-address-select").closest(".wu-selectWrap").addClass("wu-warn");
                    $(".address-warn-title").show().text("请选择省市区街道");
                    return false;
                }

                if (Province && Province == "0") {
                    $("#custom_address_province_select").closest(".wu-selectWrap").addClass("wu-warn");
                    $(".address-warn-title").show().text("请选择省市区街道");
                    return false;
                }
                if (City && City == "0") {
                    $("#custom_address_city_select").closest(".wu-selectWrap").addClass("wu-warn");
                    $(".address-warn-title").show().text("请选择省市区街道");
                    return false;
                }
                if (County && County == "0") {
                    $("#custom_address_county_select").closest(".wu-selectWrap").addClass("wu-warn");
                    $(".address-warn-title").show().text("请选择省市区街道");
                    return false;
                }
                if (Street && Street == "0") {
                    $("#custom_address_street_select").closest(".wu-selectWrap").addClass("wu-warn");
                    $(".address-warn-title").show().text("请选择省市区街道");
                    return false;
                }
                if (DetailAddr == '') {
                    $("#custom_address_textarea").closest(".wu-inputWrap").addClass("wu-warn").find(".wu-warn-title").show().text("请输入详细地址");
                    return false;
                }
                var addressJson = {
                    "Province": Province,
                    "City": City,
                    "County": County,
                    "Street": Street,
                    "Address": DetailAddr,
                    "ReceiverName": Name,
                    "ReceiverContract": Mobile,
                    "ReceiverTel": ""
                }
                OrderAgreeReturnAndRefundParams.Address = addressJson;
            }
            submitPlatformAgreeRefund();
        }
    }

    cancelAgreeRefundAddress = function () {
        layer.close(showAgreeRefundAddressSelectDailog);
    }

    // 点击document
    $(document).on("click", function () {
        $("#platform_after_sales_address_select").hide();
        
    });
    // 检测售后管理-平台售后单-权限校验
    function getPlatformAfterSalesOperationAuth() {
        // 厂家地址管理权限校验
        commonModule.FxPermission(function (p) {
            commonModule.Ajax({
                type: "POST",
                url: "/SubAccount/CheckPermission",
                data: { permission: p.SupplierAddress },
                success: function (rsp) {
                    if (rsp.Success) {
                        if (rsp.Data == true) {
                            $("#SupplierAddress").show();
                        } else {
                            $("#SupplierAddress").hide();
                        }
                    }
                }
            });
        });
        // 平台售后操作权限校验
        commonModule.FxPermission(function (p) {
            commonModule.Ajax({
                type: "POST",
                url: "/SubAccount/CheckPermission",
                data: { permission: p.PlatformOperation },
                success: function (rsp) {
                    if (rsp.Success) {
                        if (rsp.Data == true) {
                            isPlatformAfterSalesOperationAuth = true;
                            $("#platform_batch_operation").show();
                        } else {
                            isPlatformAfterSalesOperationAuth = false;
                            $("#platform_batch_operation").hide();
                        }
                    }
                }
            });
        });
    }
    // 前往设置
    setSupplierAfterSale = function () {
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    openSupplierAfterSaleDrawer();
                }
                else return;
            }, p.SupplierAddress);
        });
    }
    // 厂家售后地址抽屉弹窗
    openSupplierAfterSaleDrawer = function () {
        layer.closeAll();
        $("#createFullMaskSupplierAddress").addClass("active");
        $(".layui-layout-body").css("overflow", "hidden");
        commonModule.Ajax({
            url: "/AfterSale/GetAfterSaleAddressConfig",
            data: {},
            type: "GET",
            loading: false,
            success: function (rsp) {
                if (rsp.Success) {
                    isEnableAutoSync = rsp.Data == '1' ? true : false; // 1代表已开启，0代表未开启
                    if (isEnableAutoSync) {
                        resetAfterSalesAddressData();
                    } else {
                        resetFormData();
                        renderAddressTableTpl([]);
                    }
                }
            }
        });
        saveEditSupplierAddressData = [];
    }
    // 关闭-厂家售后地址抽屉弹窗
    closeFullMaskSupplierAddress = function () {
        $("#createFullMaskSupplierAddress").removeClass("active");
        $(".layui-layout-body").css("overflow", "auto");
    }
    // 开启自动同步
    confirmEnableAutoSync = function () {
        commonModule.Ajax({
            url: "/AfterSale/EnableAllFsAsAutoSync",
            data: {},
            type: "POST",
            loading: true,
            success: function (rsp) {
                if (rsp.Success) {
                    isEnableAutoSync = true;
                    wuFormModule.wu_toast({ type: 4, content: '开启成功' });
                    resetAfterSalesAddressData();
                } else {
                    isEnableAutoSync = false;
                    wuFormModule.wu_toast({ type: 2, content: rsp.Message });
                }
            }
        });
    }

    saveSupplierAfterSalesAddress = function () {
        if (!saveEditSupplierAddressData.length) {
            wuFormModule.wu_toast({ type: 4, content: '保存成功' });
            closeFullMaskSupplierAddress();
            return false;
        }
        commonModule.Ajax({
            url: "/AfterSale/AfterSaleAddressSave",
            data: {
                Data: saveEditSupplierAddressData
            },
            type: "POST",
            loading: true,
            success: function (rsp) {
                if (rsp.Success) {
                    wuFormModule.wu_toast({ type: 4, content: '保存成功' });
                    closeFullMaskSupplierAddress();
                    module.LoadList(false);
                } else {
                    wuFormModule.wu_toast({ type: 2, content: rsp.Message });
                }
            }
        });
    }

    function commonSelectCallBack(control) {
        var deep = control.attr('deep');
        if (deep > 1) {
            var dataValue = control.attr("data-value");
            var isExistsVal = control.find("option[value='" + dataValue + "']").length;
            if (isExistsVal > 0)
                control.val(dataValue).trigger('change');
        }
    };
    commonFocusInputAction = function () {
        $(this).closest(".wu-inputWrap").removeClass("wu-warn").find(".wu-warn-title").hide().text("");
    }

    commonChangeSelectAction = function () {
        var value = $(this).val();
        console.log("value", value);
        if (value != '0') {
            $(this).closest(".wu-selectWrap").removeClass("wu-warn");
            $(".address-warn-title").hide().text("");
            $(this).css("color", "rgba(0, 0, 0, 0.9)");
        } else {
            $(this).css("color", "rgba(0, 0, 0, 0.5)");
        }
    }

    // 编辑-厂家售后地址
    editSupplierAfterSalesAddress = function (accountNumber, id) {
        var tplt = $.templates("#edit_after_sales_address_tpl");
        var html = tplt.render({
            AccountNumber: accountNumber
        });
        // 编辑回显地址数据
        var selectedAddrData = SupplierAddressTableData.find(function (item) {
            return item.Id == id;
        });
        console.log("selectedAddrData", selectedAddrData);
        var addressDataToJson = null;
        if (selectedAddrData.ContentJson) {
            addressDataToJson = JSON.parse(selectedAddrData.ContentJson);
        }
        layer.open({
            type: 1,
            title: '编辑厂家地址',
            content: html,
            area: '560px',
            skin: 'wu-dailog editAfterSalesAddressDailog',
            success: function () {
                //layui.use(['form'], function () {
                //    var form = layui.form;
                //    // 显示弹框后渲染 select 组件
                //    form.render('select');
                //});
                $(".supplier-address-select").css("color", "rgba(0, 0, 0, 0.5)");
                commonModule.LoadAreaInfoToControl('address_supplier_province_select', 1, function () {}, commonSelectCallBack, "name");
                if (addressDataToJson) {
                    $("#address_supplier_account_name").val(addressDataToJson.ReceiverName ? addressDataToJson.ReceiverName : '');
                    $("#address_supplier_account_mobile").val(addressDataToJson.ReceiverContract ? addressDataToJson.ReceiverContract : '');
                    $("#supplier_address_textarea").val(addressDataToJson.Address);
                    $("#address_supplier_province_select").attr("data-value", addressDataToJson.Province).val(addressDataToJson.Province).change();
                    $("#address_supplier_city_select").attr("data-value", addressDataToJson.City).val(addressDataToJson.City).change();
                    $("#address_supplier_county_select").attr("data-value", addressDataToJson.County).val(addressDataToJson.County).change();
                    $("#address_supplier_province_select").attr("data-value", addressDataToJson.Province).val(addressDataToJson.Province);
                    $("#address_supplier_city_select").attr("data-value", addressDataToJson.City).val(addressDataToJson.City);
                    $("#address_supplier_county_select").attr("data-value", addressDataToJson.County).val(addressDataToJson.County);
                    $("#address_supplier_street_select").attr("data-value", addressDataToJson.Street).val(addressDataToJson.Street? addressDataToJson.Street: '0');
                }
            },
            btn: ['保存', '取消'],
            btn1: function (index) {
                var mobileReg = /^(86-[1][0-9]{10})|(86[1][0-9]{10})|([1][0-9]{10})$/; // 手机正则
                var Name = $('#address_supplier_account_name').val().trim();
                var Mobile = $('#address_supplier_account_mobile').val().trim();
                var Province = $('#address_supplier_province_select').val();
                var City = $('#address_supplier_city_select').val();
                var County = $('#address_supplier_county_select').val();
                var Street = $('#address_supplier_street_select').val();
                var DetailAddr = $('#supplier_address_textarea').val();
                if (Name == '') {
                    $("#address_supplier_account_name").closest(".wu-inputWrap").addClass("wu-warn").find(".wu-warn-title").show().text("请输入姓名");
                    return false;
                }
                if (Mobile == '') {
                    $("#address_supplier_account_mobile").closest(".wu-inputWrap").addClass("wu-warn").find(".wu-warn-title").show().text("请输入手机号");
                    return false;
                }
                if (Mobile != '' && !mobileReg.test(Mobile)) {
                    $("#address_supplier_account_mobile").closest(".wu-inputWrap").addClass("wu-warn").find(".wu-warn-title").show().text("手机号格式不对");
                    return false;
                }
                var Addr = '';
                if ((Province && Province == "0") && (City && City == "0") && (County && County == "0") && (Street && Street == "0")) {
                    $(".supplier-address-select").closest(".wu-selectWrap").addClass("wu-warn");
                    $(".address-warn-title").show().text("请选择省市区街道");
                    return false;
                }
                if (Province && Province == "0") {
                    $("#address_supplier_province_select").closest(".wu-selectWrap").addClass("wu-warn");
                    $(".address-warn-title").show().text("请选择省市区街道");
                    return false;
                }
                if (City && City == "0") {
                    $("#address_supplier_city_select").closest(".wu-selectWrap").addClass("wu-warn");
                    $(".address-warn-title").show().text("请选择省市区街道");
                    return false;
                }
                if (County && County == "0") {
                    $("#address_supplier_county_select").closest(".wu-selectWrap").addClass("wu-warn");
                    $(".address-warn-title").show().text("请选择省市区街道");
                    return false;
                }
                if (Street && Street == "0") {
                    $("#address_supplier_street_select").closest(".wu-selectWrap").addClass("wu-warn");
                    $(".address-warn-title").show().text("请选择省市区街道");
                    return false;
                }
                if (DetailAddr == '') {
                    $("#supplier_address_textarea").closest(".wu-inputWrap").addClass("wu-warn").find(".wu-warn-title").show().text("请输入详细地址");
                    return false;
                }
                Addr += Province;
                Addr += City;
                Addr += County;
                Addr += Street;
                Addr += DetailAddr;
                var completeAddress = '';
                completeAddress = Name + ',' + Mobile + "," + Addr;
                var addressJson = {
                    "Province": Province,
                    "City": City,
                    "County": County,
                    "Street": Street,
                    "Address": DetailAddr,
                    "ReceiverName": Name,
                    "ReceiverContract": Mobile,
                    "ReceiverTel": ""
                }
                SupplierAddressTableData.forEach(function (item) {
                    if (item.Id == id) {
                        item.AddressStr = completeAddress;
                        item.ContentJson = JSON.stringify(addressJson);
                    }
                });
                renderAddressTableTpl(SupplierAddressTableData);
                var hasEditRowAddress = saveEditSupplierAddressData.find(function (item) {
                    return item.Id == selectedAddrData.Id;
                });
                if (hasEditRowAddress) {

                    saveEditSupplierAddressData.forEach(function (item) {
                        if (item.Id == selectedAddrData.Id) {
                            item.Id = selectedAddrData.Id;
                            item.TargetFxuserId = selectedAddrData.TargetFxuserId;
                            item.ContentJson = JSON.stringify(addressJson);
                            item.IsAutosync = selectedAddrData.IsAutosync;
                        }
                    });
                } else {
                    saveEditSupplierAddressData.push({
                        Id: selectedAddrData.Id,
                        TargetFxuserId: selectedAddrData.TargetFxuserId,
                        ContentJson: JSON.stringify(addressJson),
                        IsAutosync: selectedAddrData.IsAutosync,
                    });
                }
                wuFormModule.wu_toast({ type: 4, content: '已编辑' });
                layer.close(index);
                // console.log("saveEditSupplierAddressData", saveEditSupplierAddressData);
            },
            btn2: function () {
                layer.confirm('将不会保存当前已填写的内容，确认是否取消？', {
                    title: '提示',
                    btn: ['确定', '取消'],
                    area: '340px',
                    skin: 'wu-dailog',
                }, function () {
                    layer.closeAll();
                }, function (index1) {
                    layer.close(index1);
                });
                return false;
            }
        });
    }

    openAfterSalesAddressLog = function () {
        layer.open({
            type: 1,
            title: '厂家售后地址修改日志',
            content: $("#show_address_log_table"),
            area: '640px',
            skin: 'wu-dailog',
            offset: '80px',
            success: function () {
                initAfterSalesLogList(false);
            },
            btn: false,
        });
    }
    // 查询
    searchAfterSalesAddressData = function () {
        if (!isEnableAutoSync) {
            wuFormModule.wu_toast({ type: 3, content: '请先开启自动同步' });
            return false;
        }
        reqSupplierAddressModel.AccountNumber = $("#address_account_number_input").val();
        reqSupplierAddressModel.IsAutosync = $("#address_auto_sync_select").val();
        initSupplierAddressList(false);
    }
    // 重置
    resetAfterSalesAddressData = function () {
        resetFormData();
        if (!isEnableAutoSync) {
            wuFormModule.wu_toast({ type: 3, content: '请先开启自动同步' });
            return false;
        }
        initSupplierAddressList(false);
    }
    function resetFormData() {
        wuFormModule.resetConditionsStyle('#supplier_aftersale_address_form');
        reqSupplierAddressModel.PageIndex = 1;
        reqSupplierAddressModel.PageSize = 50;
        reqSupplierAddressModel.Isimprove = '';
        reqSupplierAddressModel.AccountNumber = '';
        reqSupplierAddressModel.IsAutosync = '';
        $("#address_account_number_input").val('');
        $("#address_auto_sync_select").val('');
        $("#address_tab_type li:first").addClass("layui-this").siblings("li").removeClass("layui-this");
        layui.form.render('select'); // 重新渲染select组件
    }

    switchAddressSetting = function (selectedValue, id, userId) {
        console.log("选中的值是：" + selectedValue, id, typeof selectedValue);
        // 自定义
        if (selectedValue == 0) {
            SupplierAddressTableData.forEach(function (item) {
                if (item.Id == id) {
                    item.AddressStr = null;
                    item.IsAutosync = Number(selectedValue);
                    saveEditSupplierAddressData.push({
                        Id: item.Id,
                        TargetFxuserId: item.TargetFxuserId,
                        ContentJson: '',
                        IsAutosync: item.IsAutosync
                    });
                }
            });
            renderAddressTableTpl(SupplierAddressTableData);
        }
        // 自动同步
        if (selectedValue == 1) {
            commonModule.Ajax({
                url: "/AfterSale/GetAddressByManufacturer",
                data: {
                    TargetFxuserId: userId
                },
                type: "POST",
                loading: true,
                success: function (rsp) {
                    if (rsp.Success) {
                        wuFormModule.wu_toast({ type: 4, content: '自动同步成功' });
                        var data = rsp.Data;
                        saveEditSupplierAddressData = [];
                        SupplierAddressTableData.forEach(function (item) {
                            if (item.Id == id) {
                                item.AddressStr = data;
                                item.IsAutosync = Number(selectedValue);
                                saveEditSupplierAddressData.push({
                                    Id: item.Id,
                                    TargetFxuserId: item.TargetFxuserId,
                                    ContentJson: '',
                                    IsAutosync: item.IsAutosync
                                });
                            }
                        });
                        renderAddressTableTpl(SupplierAddressTableData);
                    } else {
                        wuFormModule.wu_toast({ type: 2, content: rsp.Message });
                    }
                }
            });
        }
    }
    function renderAddressTableTpl(list) {
        var tplt = $.templates("#supplier_after_sales_address_tpl");
        var html = tplt.render({
            data: list,
            isEnableAutoSync: isEnableAutoSync
        });
        $("#supplier_after_sales_address_tbody_data").html(html);
    }
    // 初始化售后地址日志列表
    function initAfterSalesLogList(isPaging) {
        commonModule.Ajax({
            url: '/AfterSale/GetAfterSaleAddressLogList',
            data: afterSalesAddrLogModel,
            loading: true,
            type: 'POST',
            success: function (rsp) {
                if (commonModule.IsError(rsp)) {
                    return;
                }
                var list = rsp.Data.List || [];
                var tplt = $.templates("#after_sales_address_log_table_body");
                var html = tplt.render({
                    data: list,
                });
                $("#address_log_tbody_data").html(html);
                if (isPaging == true) {
                    return;
                }
                layui.laypage.render({
                    elem: 'afterSalesAddressLogPaging',
                    theme: ' wu-page',
                    count: rsp.Data.Total,
                    limit: afterSalesAddrLogModel.PageSize,
                    curr: afterSalesAddrLogModel.PageIndex,
                    limits: [50, 100, 150, 200, 300, 500],
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            afterSalesAddrLogModel.PageSize = obj.limit;
                            afterSalesAddrLogModel.PageIndex = obj.curr;
                            initAfterSalesLogList(true);
                        }
                    }
                });
            }
        });
    }
    // 地址tab栏切换
    $('#address_tab_type li').on('click', function () {
        if (!isEnableAutoSync) {
            wuFormModule.wu_toast({ type: 3, content: '请先开启自动同步' });
            return false;
        }
        // 获取当前点击的 li 元素的 data-type 属性值
        var dataType = $(this).data('type');
        reqSupplierAddressModel.Isimprove = dataType;
        initSupplierAddressList(false);
    });

    function initSupplierAddressList(isPaging) {
        commonModule.Ajax({
            url: '/AfterSale/GetAfterSalesAddressesList',
            data: reqSupplierAddressModel,
            loading: true,
            type: 'POST',
            success: function (rsp) {
                if (commonModule.IsError(rsp)) {
                    return;
                }
                SupplierAddressTableData = rsp.Data.List || [];
                renderAddressTableTpl(SupplierAddressTableData);

                if (isPaging == true) {
                    return;
                }
                layui.laypage.render({
                    elem: 'SupplierAddressPaging',
                    theme: ' wu-page',
                    count: rsp.Data.Total,
                    limit: reqSupplierAddressModel.PageSize,
                    curr: reqSupplierAddressModel.PageIndex,
                    limits: [50, 100, 150, 200, 300, 500],
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function (obj, first) {
                        if (!first) {
                            reqSupplierAddressModel.PageSize = obj.limit;
                            reqSupplierAddressModel.PageIndex = obj.curr;
                            initSupplierAddressList(true);
                        }
                    }
                });
            }
        });
    }

    function _getAfterSaleRequertModel(isPaging) {
        var $container = $("#SearchContainer");
        if (isPaging != true) {
            //如果是通过搜索按钮，则恢复到第一页
            _pageIndex = 1;
            //_pageSize = PageSize || 10;
        }

        //售后单号、订单编号、系统单号
        var $elmts = $container.find(".MultipleQuery");
        $elmts.each(function (i, elmt) {
            var val = $(elmt).val() || "";
            var key = $(elmt).attr("name");
            val = val.trim().replace(/ +/g, ',');
            if (val == "")
                return null;
            var len = val.split(",").length;
            if (len > 100) {
                layer.confirm("查询数量不能超过100个",{skin: 'wu-dailog'});
                isReturn = true;
                return null;
            }
        });

        var startTime = $("#inputSelectTime .QueryDateVal").attr("start-date") || "";
        var endTime = $("#inputSelectTime .QueryDateVal").attr("end-date") || "";

        //转换成毫秒进行比较
        var oDate1 = new Date(startTime);
        var oDate2 = new Date(endTime);
        if (oDate1.getTime() > oDate2.getTime()) {
            // layer.msg("开始日期不能大于结束日期");
            wuFormModule.wu_alert({content: "开始日期不能大于结束日期", type: 2 })
            return null;
        }

        //店铺
        var shopIds = $("#selectShops").attr("data-values") || "";
        var supplierIds = $("#Supplier").attr("data-values") || "";
        var agentIds = $("#Agent").attr("data-values") || "";
        var sourceFlag = $("#sourceflag .active").attr("data-sourceflag") || "0";
        var sendState = $("#layui_mynav_childrenNav .active").attr("data-sendstate") || "";

        var fromUrl = commonModule.getQueryVariable("fromUrl");
        if (fromUrl == "AliIncludeOrder") sendState = $("#aliIncludeOrderstatus .active").attr("data-sendstate") || "";  // 从收单列表进入的售后单

        var data = {
            PageIndex: _pageIndex,
            PageSize: _pageSize,
            SelectShopIds: shopIds,
            SupplierIds: supplierIds,
            AgentIds: agentIds,
            StartTime: startTime,
            EndTime: endTime,
            QueryDateType: $("#QueryDateType").val(),
            LogicOrderIds: getVal("LogicOrderIds", 1),
            PlatformOrderIds: getVal("PlatformOrderIds", 1),
            AfterSaleIds: getVal("AfterSaleIds", 1),
            ReturnTrackingNo: getVal("ReturnTrackingNo", 0),
            ResendTrackingNo: getVal("ResendTrackingNo", 0),
            AfterSaleRemark: getVal("AfterSaleRemark", 0),
            AfterSaleType: $("#AfterSaleType").val(),
            AfterSaleStatus: $("#AfterSaleStatus").val(),
            InterceptStatus: $("#InterceptStatus").val(),
            ApplyAfterSaleStatus: $("#ApplyAfterSaleStatus").val(),
            OrderPrintStatus: $("#OrderPrintStatus").val(),
            SourceFlag: sourceFlag,
            SendState: sendState,
            PlatformType: $("#PlatformType").val() || "",
            PlatAfterSaleRemark: $("#PlatAfterSaleRemark").val(),
            SellerRemarkFlag: $("#SellerRemarkFlag").val(),
            SellerRemark: $("#SellerRemark").val(),
            AfterSaleRemarkFlag: $("#AfterSaleRemarkFlag").val(),
            AfterSaleRemark: $("#AfterSaleRemark").val()
        };
        return data;
    }

    var initEvent = function () {
        // 回车查询
        $("#SearchContainer input[type=text]:visible").each(function () {
            $(this).bind("keyup", function (event) {
                if (event.keyCode == "13") {
                    //回车执行查询
                    module.LoadList();
                }
            });
        });

        //重置按钮点击事件
        $("#btn-reset").click(function () {
            wuFormModule.resetConditionsStyle('#searchWrap_Select_key');
            $(".newinputSelectTime").removeClass("activeSelectTime");
            //重置店铺下拉框
            initShopSelectBox(Shops);
            initSupplierSelectBox();
            initAgentSelectBox();

            //重置按钮点击事件
            $("#PlatformType").val("");
            $("#selectShops").val("0");
            $("#Agent").val("0");
            $("#Supplier").val("0");
            $("#QueryDateType").val("ApplyTime");
            $("#LogicOrderId").val("");
            $("#PlatformOrderId").val("");
            $("#LogicOrderIds").val("");
            $("#PlatformOrderIds").val("");
            $("#AfterSaleIds").val("");
            $("#ReturnTrackingNo").val("");
            $("#ResendTrackingNo").val("");
            $("#AfterSaleType").val("");
            $("#AfterSaleStatus").val("");
            $("#InterceptStatus").val("");
            $("#ApplyAfterSaleStatus").val("");
            $("#OrderPrintStatus").val("");
            $("#PlatAfterSaleRemark").val("");
            $("#SellerRemarkFlag").val("");
            $("#SellerRemark").val("");
            $("#AfterSaleRemarkFlag").val("");
            $("#AfterSaleRemark").val("");
            layui.form.render("select");
        });

        //绑定平台
        if (PlatformTypes && PlatformTypes.length > 0) {
            var html = '<option value="">选择平台</option>';
            $(PlatformTypes).each(function (i, s) {
                if (s.Name == "线下单")
                    s.Name = "线下单";
                html += '<option value="' + s.PlatformType + '">' + s.Name + '</option>';
            });
            $("#PlatformType").html(html);
        }

        //绑定店铺
        initShopSelectBox(Shops);

        //绑定我的厂家、我的商家
        initSupplierSelectBox();
        initAgentSelectBox();

        //平台店铺级联处理
        $("#PlatformType").change(function () {
            var val = $(this).val();
            var ptShops = [];
            if (!val)
                ptShops = Shops;
            else {
                $(Shops).each(function (i, s) {
                    if (s.PlatformType == val)
                        ptShops.push(s);
                });
            }
            initShopSelectBox(ptShops);

        });
        //导出任务初始化
        //module.ProgressInit();

        var initQueryDateBox = function (startDate, endDate) {
            var obj = {
                days: 15, //天数
                width: "165px",
                startDate: null, //startDate可以不用填 不用填调用客户端时间  格式：yyyy-MM-dd
                endDate: commonModule.ServerNowDate //endDate可以不用填 不用填调用客户端时间      格式：yyyy-MM-dd
            };
            if (startDate != undefined && startDate != "" && endDate != undefined && endDate != "") {
                obj = {
                    days: 15, //天数
                    startDate: startDate,
                    endDate: endDate
                };
            }
            commonModule.InitNewCalenderTime("#inputSelectTime", obj);
        }

        var startDate = unescape(commonModule.getQueryVariable("st") + "").replace("+", " ");
        var endDate = unescape(commonModule.getQueryVariable("et") + "").replace("+", " ");
        initQueryDateBox(startDate, endDate);


        //多单号查询分隔符号转换
        $('#SearchContainer .MultipleQuery').on("blur", function () {
            $(this).val($(this).val().multipleQueryFormatString());
        });


        //数据动态渲染后，需要调该方法刷新
        layui.form.render("select");
    }

    module.Init = function () {
        _pageSize = PageSize || 50;
        initEvent();

        if (outAfterSaleType != undefined && outAfterSaleType != "") {
            $("#AfterSaleType").val(outAfterSaleType);
        }

        var defautIndex = 0;
        //outSourceFlag="1"，默认标签为“人工售后单”，outSendState="all"时，子标签为“全部”
        if (outSourceFlag != undefined && outSourceFlag == "1") {
            defautIndex = 1;
            $("#sourceflag>li").each(function (i, item) {
                $(this).removeClass("active");
                if ($(this).attr("data-sourceflag") == 1) {
                    $(this).addClass("active");
                }
            })
            $("#layui_mynav_childrenNav").hide();
            //待处理....
        }
        if (outSendState != undefined && outSendState == "all") {
            $("#layui_mynav_childrenNav>li").each(function (i, item) {
                $(this).removeClass("active");
                if ($(this).attr("data-target") == 2) {
                    $(this).addClass("active");
                }
            })
        }


        $$.navActive("#sourceflag", function (index, item) {
            if (index == 0) {
                $("#layui_mynav_childrenNav").css({ display: "flex" });
                $(".sourceflag_0").css({ display: "block" });
            } else {
                $("#layui_mynav_childrenNav").css({ display: "none" });
                $(".sourceflag_0").css({ display: "none" });
            }
            module.LoadList();
        });
        $$.navActive("#layui_mynav_childrenNav", function (index, item) {
            module.LoadList();
        });

        module.LoadList();
    }

    //获取指定id的value
    //flag = 1替换分隔字符
    //flag 其他值只trim()
    var getVal = function (objid, flag) {
        var val = $("#" + objid).val() || "";
        val = val.trim();
        if (flag == 1) {
            val = val.replace(/ +/g, ',');
            val = val.replace(/,+/g, ',');
        }
        return val;
    }

    //绑定店铺
    var initShopSelectBox = function (shops) {
        shops = shops || [];
        var selectboxArr = [];
        for (var i = 0; i < shops.length; i++) {
            var obj = {};
            obj.Value = shops[i].ShopId;
            obj.Text = shops[i].NickName;
            selectboxArr.push(obj);
        }

        var selectInit = {
            eles: '#selectShops',
            emptyTitle: '全部店铺', //设置没有选择属性时，出现的标题
            data: selectboxArr,
            searchType: 1, //1出现搜索框，不设置不出现搜索框
            showWidth: '250px', //显示下拉的宽
            isRadio: false, //有设置，下拉框改为单选
            allSelect: true,
        };

        var selectBox = new selectBoxModule2();
        selectBox.initData(selectInit);
    }

    //我的厂家
    var initSupplierSelectBox = function () {
        commonModule.initSupplierSelectBox("Supplier", Suppliers);
    }

    //我的商家
    var initAgentSelectBox = function () {
        commonModule.initAgentSelectBox("Agent", Agents);
    }

    //全选订单
    module.allCheck = function (isThis) {
        var isChecked = isThis.checked;
        $(".order-chx").each(function (index, chx) {
            chx.checked = isChecked;
        });
        $("#orderNum").text("[" + $(".order-chx:checked").length + "]");
    }

    //统计选中数
    module.setCheck = function (isThis) {
        $("#orderNum").text("[" + $(".order-chx:checked").length + "]");
        $("#allcheckorder")[0].checked = $(".order-chx:checked").length == $(".order-chx").length;
    }
    module.stop = function () {
        event.stopPropagation();
    }
    module.opanStockControl = function () {
        var linkPageUrl = '/StockControl/StoreManagement';
        var url = commonModule.rewriteUrlToMainDomainNotDbName(linkPageUrl);
        console.log(url);
        commonModule.OpenNewTab(url);
    }

    var TaobaoFlagList = [];
    //加载淘宝商家设置的旗帜标签
    function LoadTaobaoSellerFlagTag(shopId) {
        common.Ajax({
            url: '/NewOrder/GetTaoBaoSellerFlagTags',
            type: 'post',
            async: false,
            data: { shopId: shopId },
            success: function (rsp) {
                if (rsp.Success) {
                    TaobaoFlagList = rsp.Data || [];
                }
            }
        })
    }


    var historyRemarkList = [];//历史备注
    function GetMessageHRecord(afterSaleCode) {
        commonModule.Ajax({
            type: "GET",
            url: "/AfterSale/GetMessageHRecord?afterSaleCode=" + afterSaleCode,
            loading: true,
            success: function (rsp) {
                if (rsp.Success) {
                    var data = rsp.Data.List || [];
                    historyRemarkList = data;
                    historyRemarkList.length && historyRemarkList.forEach(function (item) {
                        if (item.PrintscreenPics && item.PrintscreenPics.length) {
                            item.PrintscreenPics.forEach(function (item1) {
                                item1.ImageUrl = newTransformImgSrc(item1.ImageUrl);
                            })
                        }
                    })
                }
                renderhistoryReamrk();
            }
        });
    }

    var atvLogicorderid = '';
    var atvRow = {};
    //批量卖家备注
    module.batchSellerRemark = function (isBatch, remarkType, rowIndex, PIndex) {
        
        if (remarkType == 2) {
            commonModule.FxPermission(function (p) {
                commonModule.CheckPermission(function (success) {
                    if (success) {
                        batchSellerRemark(isBatch, remarkType, rowIndex, PIndex);
                    }
                    else return;
                }, p.UpdateOrderSellerRemark);
            });
        } else {
            batchSellerRemark(isBatch, remarkType, rowIndex, PIndex);
        }
    }
    function batchSellerRemark(isBatch, remarkType, rowIndex, PIndex) {
        var sourceFlag = $("#sourceflag .active").attr("data-sourceflag") || "0";
        if (sourceFlag != 0) {
            if (remarkType == 1) {
                // layer.msg("人工售后单暂不支持批量操作售后备注");
                wuFormModule.wu_alert({content: "人工售后单暂不支持批量操作售后备注", type: 2 })
                return
            } else if (remarkType == 2) {
                // layer.msg("人工售后单暂不支持批量操作卖家备注");
                wuFormModule.wu_alert({content: "人工售后单暂不支持批量操作卖家备注", type: 2 })
                return
            }
        }
        if (isBatch) {
            var $checkedList = $(".order-chx:checked");
            if ($checkedList.length == 0) {
                // layer.msg(remarkType == 3 ? '请先选中订单再进行代发售后留言' : remarkType == 2 ? "请先选中您店铺的订单再进行卖家备注" : "请先选中您店铺的订单再进行售后订单备注");
                wuFormModule.wu_alert({content:remarkType == 3 ? '请先选中订单再进行代发售后留言' : remarkType == 2 ? "请先选中您店铺的订单再进行卖家备注" : "请先选中您店铺的订单再进行售后订单备注", type: 2 })
                return;
            }

            var afterSaleCodeList = []; // 支持修改的订单
            var taobaoPids = []; // 支持修改的淘宝订单
            var sameShopIds = []; // 不同淘宝店铺的数量
            var noLimitsAfterSaleRemarkList = []; // 没有权限的售后备注订单
            var noSupportAfterSaleRemarkList = [] // 不支持修改售后备注的平台订单
            var nonsupportPids = [] // 不支持修改卖家备注的平台订单
            var noAccessPids = [] // 没有权限卖家备注订单
            var XHSPids = []; // 支持修改的小红书平台订单
            var JDPids = []; // 支持修改的京东平台订单
            var AliPids = []; // 支持修改的1688平台订单

            $checkedList.each(function () {
                var afterSaleCode = $(this).attr("data-ascode");
                var IsEditAfterSaleRemark = $(this).attr("data-editafrml");
                var IsEditSellerRemark = $(this).attr("data-editserml");
                var PlatformType = $(this).attr("data-pt");
                var ShopId = $(this).attr("data-shopid");

                // 1-售后备注；2-卖家备注；3-售后留言
                if (remarkType == 1) {
                    // 仅支持抖店修改
                    if (PlatformType == 'TouTiao') {
                        // 存在权限
                        if (IsEditAfterSaleRemark == "true") {
                            afterSaleCodeList.push(afterSaleCode);
                        } else if (IsEditAfterSaleRemark == "false") {
                            noLimitsAfterSaleRemarkList.push(afterSaleCode);
                        }
                    } else {
                        noSupportAfterSaleRemarkList.push(afterSaleCode);
                    }
                } else if (remarkType == 3) {
                    afterSaleCodeList.push(afterSaleCode);
                } else if (remarkType == 2) {
                    // 支持修改卖家备注的平台
                    if (supportPlatformtTypes.indexOf(PlatformType) > -1) {
                        // 存在权限
                        if (IsEditSellerRemark == "true") {
                            if (PlatformType == 'Taobao') {
                                // 存在多个不同店铺的淘宝订单
                                if (sameShopIds.indexOf(ShopId) == -1) {
                                    sameShopIds.push(ShopId);
                                }
                                taobaoPids.push(afterSaleCode);
                            } else {
                                afterSaleCodeList.push(afterSaleCode);
                                if (PlatformType == 'XiaoHongShu') {
                                    XHSPids.push(afterSaleCode);
                                } else if (PlatformType == 'Jingdong') {
                                    JDPids.push(afterSaleCode);
                                } else if (PlatformType == 'Alibaba') {
                                    AliPids.push(afterSaleCode);
                                }
                            }
                        } else if (IsEditSellerRemark == "false") {
                            noAccessPids.push(afterSaleCode);
                        }
                    } else {
                        nonsupportPids.push(afterSaleCode);
                    }
                }
            });

            // 1-售后备注；2-卖家备注
            if (remarkType == 1) {
                if (afterSaleCodeList.length == 0 && noLimitsAfterSaleRemarkList.length == 0 && noSupportAfterSaleRemarkList.length > 0) {
                    // layer.msg("您勾选的订单条件不支持批量操作售后备注，原因：当前平台不支持回传售后备注");
                    wuFormModule.wu_alert({content: "您勾选的订单条件不支持批量操作售后备注，原因：当前平台不支持回传售后备注", type: 2 })
                } else if (afterSaleCodeList.length == 0 && noSupportAfterSaleRemarkList.length == 0 && noLimitsAfterSaleRemarkList.length > 0) {
                    wuFormModule.wu_alert({content: "您勾选的订单条件不支持批量操作售后备注，原因：订单操作权限不支持，请联系分销商设置操作权限", type: 2 })
                    // layer.msg("您勾选的订单条件不支持批量操作售后备注，原因：订单操作权限不支持，请联系分销商设置操作权限");
                } else if (afterSaleCodeList.length == 0 && noSupportAfterSaleRemarkList.length == 0 && noLimitsAfterSaleRemarkList.length == 0) {
                    wuFormModule.wu_alert({content: "您勾选的订单条件不支持批量操作售后备注，原因1：部分订单操作权限不支持，请联系分销商设置操作权限，原因2：部分订单对应的平台不支持回传售后备注", type: 2 })
                    // layer.msg("您勾选的订单条件不支持批量操作售后备注，原因1：部分订单操作权限不支持，请联系分销商设置操作权限，原因2：部分订单对应的平台不支持回传售后备注");
                } else if (afterSaleCodeList.length > 0 && (noSupportAfterSaleRemarkList.length > 0 || noLimitsAfterSaleRemarkList.length > 0)) {
                    var aftLoadIndex = layer.open({
                        type: 1,
                        resize: false,
                        move: false,
                        skin: 'wu-dailog',
                        title: "忽略异常订单操作提示",
                        content: $('#abnormalReamrkDialog'),
                        area: "500px", //宽高
                        btn: ['取消', '确认过滤，继续操作'],
                        success: function () {
                            $('#abnormalReamrkDialog .text-content .title').text("您勾选的订单中部分订单不支持批量操作售后备注，是否过滤异常订单继续操作？")
                        },
                        btn1: function () {
                            layer.close(aftLoadIndex);
                        },
                        btn2: function (index) {
                            layer.close(index);
                            openNewRemarkDialog(true, 1, afterSaleCodeList)
                        }
                    })
                } else {
                    openNewRemarkDialog(true, 1, afterSaleCodeList)
                }
            }
            else if (remarkType == 2) {
                if (afterSaleCodeList.length == 0 && taobaoPids.length == 0 && noAccessPids.length == 0 && nonsupportPids.length > 0) {
                    // layer.msg("您勾选的订单条件不支持批量操作卖家备注，原因：当前平台不支持回传卖家备注");
                    wuFormModule.wu_alert({content: "您勾选的订单条件不支持批量操作卖家备注，原因：当前平台不支持回传卖家备注", type: 2 });
                } else if (afterSaleCodeList.length == 0 && taobaoPids.length == 0 && nonsupportPids.length == 0 && noAccessPids.length > 0) {
                    // layer.msg("您勾选的订单条件不支持批量操作卖家备注，原因：订单操作权限不支持，请联系分销商设置操作权限");
                    wuFormModule.wu_alert({content: "您勾选的订单条件不支持批量操作卖家备注，原因：订单操作权限不支持，请联系分销商设置操作权限", type: 2 });
                } else if (afterSaleCodeList.length == 0 && taobaoPids.length == 0 && nonsupportPids.length == 0 && noAccessPids.length == 0) {
                    // layer.msg("您勾选的订单条件不支持批量操作卖家备注，原因1：部分订单操作权限不支持，请联系分销商设置操作权限，原因2：部分订单对应的平台不支持回传卖家备注");
                    wuFormModule.wu_alert({content: "您勾选的订单条件不支持批量操作卖家备注，原因1：部分订单操作权限不支持，请联系分销商设置操作权限，原因2：部分订单对应的平台不支持回传卖家备注", type: 2 });
                } else if (afterSaleCodeList.length > 0 && taobaoPids.length == 0 && (nonsupportPids.length > 0 || noAccessPids.length > 0)) {
                    var serLoadIndex = layer.open({
                        type: 1,
                        resize: false,
                        move: false,
                        skin: 'wu-dailog',
                        title: "忽略异常订单操作提示",
                        content: $('#abnormalReamrkDialog'),
                        area: "500px", //宽高
                        btn: ['取消', '确认过滤，继续操作'],
                        success: function () {
                            $('#abnormalReamrkDialog .text-content .title').text("您勾选的订单中部分订单不支持批量操作卖家备注，是否过滤异常订单继续操作？")
                        },
                        btn1: function () {
                            layer.close(serLoadIndex);
                        },
                        btn2: function (index) {
                            layer.close(index);
                            openNewRemarkDialog(true, 2, afterSaleCodeList, XHSPids.length ? 'XiaoHongShu' : JDPids.length ? 'Jingdong' : AliPids.length ? 'Alibaba' : undefined)
                        }
                    })
                } else if (taobaoPids.length > 0 && sameShopIds.length == 1 && nonsupportPids.length > 0 && noAccessPids.length == 0) {
                    layer.open({
                        type: 1,
                        resize: false,
                        move: false,
                        skin: 'wu-dailog',
                        title: "操作异常提示",
                        content: $('#abnormalReamrkDialog'),
                        area: "500px", //宽高
                        btn: afterSaleCodeList.length > 0 ? ['优先备注其他平台', '优先备注淘宝平台'] : ['继续备注淘宝平台'],
                        success: function () {
                            $('#abnormalReamrkDialog .text-content .title').text("您勾选的部分订单条件不支持批量操作卖家备注，已为您自动过滤。原因①部分订单对应的平台不支持回传卖家备注，剩余所选订单包含淘宝订单，需单独处理，请确认")
                        },
                        btn1: function (index) {
                            layer.close(index);
                            if (afterSaleCodeList.length > 0) {
                                TaobaoFlagList = [];
                                openNewRemarkDialog(true, 2, afterSaleCodeList, XHSPids.length ? 'XiaoHongShu' : JDPids.length ? 'Jingdong' : AliPids.length ? 'Alibaba' : undefined)
                            } else {
                                LoadTaobaoSellerFlagTag(sameShopIds[0]);
                                openNewRemarkDialog(true, 2, taobaoPids)
                            }
                        },
                        btn2: function (index) {
                            layer.close(index);
                            LoadTaobaoSellerFlagTag(sameShopIds[0]);
                            openNewRemarkDialog(true, 2, taobaoPids)
                        }
                    })
                } else if (taobaoPids.length > 0 && sameShopIds.length == 1 && nonsupportPids.length == 0 && noAccessPids.length > 0) {
                    layer.open({
                        type: 1,
                        resize: false,
                        move: false,
                        skin: 'wu-dailog',
                        title: "操作异常提示",
                        content: $('#abnormalReamrkDialog'),
                        area: "500px", //宽高
                        btn: afterSaleCodeList.length > 0 ? ['优先备注其他平台', '优先备注淘宝平台'] : ['继续备注淘宝平台'],
                        success: function () {
                            $('#abnormalReamrkDialog .text-content .title').text("您勾选的部分订单条件不支持批量操作卖家备注，已为您自动过滤。原因①部分订单操作权限不支持，请联系分销商设置操作权限，剩余所选订单包含淘宝订单，需单独处理，请确认")
                        },
                        btn1: function (index) {
                            layer.close(index);
                            if (afterSaleCodeList.length > 0) {
                                TaobaoFlagList = [];
                                openNewRemarkDialog(true, 2, afterSaleCodeList, XHSPids.length ? 'XiaoHongShu' : JDPids.length ? 'Jingdong' : AliPids.length ? 'Alibaba' : undefined)
                            } else {
                                LoadTaobaoSellerFlagTag(sameShopIds[0]);
                                openNewRemarkDialog(true, 2, taobaoPids)
                            }
                        },
                        btn2: function (index) {
                            layer.close(index);
                            LoadTaobaoSellerFlagTag(sameShopIds[0]);
                            openNewRemarkDialog(true, 2, taobaoPids);
                        }
                    })
                } else if (taobaoPids.length > 0 && sameShopIds.length == 1 && nonsupportPids.length > 0 && noAccessPids.length > 0) {
                    layer.open({
                        type: 1,
                        resize: false,
                        move: false,
                        skin: 'wu-dailog',
                        title: "操作异常提示",
                        content: $('#abnormalReamrkDialog'),
                        area: "500px", //宽高
                        btn: afterSaleCodeList.length > 0 ? ['优先备注其他平台', '优先备注淘宝平台'] : ['继续备注淘宝平台'],
                        success: function () {
                            $('#abnormalReamrkDialog .text-content .title').text("您勾选的部分订单条件不支持批量操作卖家备注，已为您自动过滤。原因①部分订单操作权限不支持，请联系分销商设置操作权限，②部分订单对应的平台不支持回传卖家备注，剩余所选订单包含淘宝订单，需单独处理，请确认")
                        },
                        btn1: function (index) {
                            layer.close(index);
                            if (afterSaleCodeList.length > 0) {
                                TaobaoFlagList = [];
                                openNewRemarkDialog(true, 2, afterSaleCodeList, XHSPids.length ? 'XiaoHongShu' : JDPids.length ? 'Jingdong' : AliPids.length ? 'Alibaba' : undefined)
                            } else {
                                LoadTaobaoSellerFlagTag(sameShopIds[0]);
                                openNewRemarkDialog(true, 2, taobaoPids);
                            }
                        },
                        btn2: function (index) {
                            layer.close(index);
                            LoadTaobaoSellerFlagTag(sameShopIds[0]);
                            openNewRemarkDialog(true, 2, taobaoPids);
                        }
                    })
                } else if (taobaoPids.length > 0 && nonsupportPids.length == 0 && noAccessPids.length == 0) {
                    if (sameShopIds.length > 1 && afterSaleCodeList.length == 0) {
                        layer.open({
                            type: 1,
                            resize: false,
                            move: false,
                            skin: 'wu-dailog',
                            title: "特殊订单操作提示",
                            content: $('#abnormalReamrkDialog'),
                            area: "500px", //宽高
                            btn: ['继续备注淘宝订单'],
                            success: function () {
                                $('#abnormalReamrkDialog .text-content .title').text("您勾选的订单包含多个淘宝店铺订单，需单独使用无标签旗帜批量处理，请确认操作");
                            },
                            btn1: function (index) {
                                layer.close(index);
                                TaobaoFlagList = [];
                                openNewRemarkDialog(true, 2, taobaoPids)
                            }
                        })
                    } else if (sameShopIds.length == 1) {
                        if (afterSaleCodeList.length == 0) {
                            LoadTaobaoSellerFlagTag(sameShopIds[0]);
                            openNewRemarkDialog(true, 2, taobaoPids);
                        } else {
                            layer.open({
                                type: 1,
                                resize: false,
                                move: false,
                                skin: 'wu-dailog',
                                title: "特殊订单操作提示",
                                content: $('#abnormalReamrkDialog'),
                                area: "500px", //宽高
                                btn: ['优先备注其他平台', '优先备注淘宝平台'],
                                success: function () {
                                    $('#abnormalReamrkDialog .text-content .title').text("您勾选的部分订单包含淘宝订单，需单独处理，请确认操作")
                                },
                                btn1: function (index) {
                                    layer.close(index);
                                    TaobaoFlagList = [];
                                    openNewRemarkDialog(true, 2, afterSaleCodeList, XHSPids.length ? 'XiaoHongShu' : JDPids.length ? 'Jingdong' : AliPids.length ? 'Alibaba' : undefined)
                                },
                                btn2: function (index) {
                                    layer.close(index);
                                    LoadTaobaoSellerFlagTag(sameShopIds[0]);
                                    openNewRemarkDialog(true, 2, taobaoPids);
                                }
                            })
                        }
                    }
                } else {
                    openNewRemarkDialog(true, 2, afterSaleCodeList, XHSPids.length ? 'XiaoHongShu' : JDPids.length ? 'Jingdong' : AliPids.length ? 'Alibaba' : undefined)
                }
            }
            else {
                openNewRemarkDialog(true, 3, afterSaleCodeList)
            }
        }
        else {
            atvRow = afterSaleDatas[rowIndex].Orders[PIndex];
            atvLogicorderid = atvRow.AfterSaleCode;
            var pfType = atvRow.PlatformType;
            var IsEditAfterSaleRemark = atvRow.IsEditAfterSaleRemark;
            var IsEditSellerRemark = atvRow.IsEditSellerRemark;
            if (supportPlatformtTypes.indexOf(pfType) > -1 && IsEditSellerRemark && sourceFlag == '0') {
                $('#remark_nav_item_2').show();
            } else {
                $('#remark_nav_item_2').hide();
            }

            if (pfType == 'TouTiao' && IsEditAfterSaleRemark && sourceFlag == '0') {
                $('#remark_nav_item_1').show();
            } else {
                $('#remark_nav_item_1').hide();
            }

            if (pfType == 'Taobao' && remarkType == 2) {
                var ShopId = atvRow.ShopId;
                LoadTaobaoSellerFlagTag(ShopId);
            } else {
                TaobaoFlagList = [];
            }

            openNewRemarkDialog(false, remarkType, [atvLogicorderid], pfType)
        }
    }


    function openNewRemarkDialog(isBatch, remarkType, afterSaleCodes, pfType) {
        layer.closeAll();
        var loadIndex = layer.open({
            type: 1,
            title: '编辑留言/备注',
            resize: false,
            move: false,
            zIndex: 10000,
            skin: 'wu-dailog',
            content: $("#sellerRemark"),
            btn: ['保存', '取消'],
            area: ["640px", "100"],
            success: function () {
                if (isBatch) {
                    remarkNavHtml(remarkType, true);
                } else {
                    remarkNavHtml(remarkType, false, atvRow);
                }
            },
            btn2: function () {
                if (remarkType == 3) {
                    productImages = [];
                    renderProductImages();
                }
                layer.close(loadIndex);
            },
            btn1: function (index) {
                if (!$('#txt_area_seller_remark_flag').val()) {
                    // layer.msg("请填写备注信息");
                    wuFormModule.wu_alert({content: "请填写备注信息", type: 2 });
                    return false
                } else if (atvRemarkType == 1) {
                    commonModule.Ajax({
                        type: "POST",
                        url: "/AfterSale/UpdatePlatfAfterSaleRemark",
                        data: { AfterSaleCodes: afterSaleCodes, platAfterSaleRemark: $('#txt_area_seller_remark_flag').val() },
                        loadingMessage: '保存中...',
                        success: function (rsp) {
                            if (rsp.Success) {
                                // layer.msg("保存成功");
                                wuFormModule.wu_alert({content: "保存成功", type: 4 });
                                module.LoadList();
                                layer.close(index);
                            } else {
                                // layer.msg(rsp.Message);
                                wuFormModule.wu_alert({content: rsp.Message, type: 2 });
                                return false;
                            }
                        }
                    });
                } else if (atvRemarkType == 2) {
                    if (checkValue === '' && pfType == 'XiaoHongShu') {
                        // layer.msg("小红书要求：卖家备注必须标记旗帜，请勾选旗帜后再进行认回传操作。");
                        wuFormModule.wu_alert({content: "小红书要求：卖家备注必须标记旗帜，请勾选旗帜后再进行认回传操作。", type: 2 });
                        return false;
                    } else if (checkValue === '' && pfType == 'Alibaba') {
                        // layer.msg("1688要求：卖家备注必须标记旗帜，请勾选旗帜后再进行认回传操作。");
                        wuFormModule.wu_alert({content: "1688要求：卖家备注必须标记旗帜，请勾选旗帜后再进行认回传操作。", type: 2 });
                        return false;
                    } else if (checkValue === '' && pfType == 'Jingdong') {
                        // layer.msg("平台要求：卖家备注必须标记旗帜，请勾选旗帜后再进行认回传操作。");
                        wuFormModule.wu_alert({content: "平台要求：卖家备注必须标记旗帜，请勾选旗帜后再进行认回传操作。", type: 2 });
                        return false;
                    } else {
                        commonModule.Ajax({
                            type: "POST",
                            url: "/AfterSale/UpdateAfterSaleSellerRemark",
                            data: { AfterSaleCodes: afterSaleCodes, SellerRemark: $('#txt_area_seller_remark_flag').val(), SellerFlag: checkValue, AfterSaleSendState: $("#layui_mynav_childrenNav .active").attr("data-sendstate") },
                            loadingMessage: '保存中...',
                            success: function (rsp) {
                                if (rsp.Success) {
                                    // layer.msg("保存成功");
                                    wuFormModule.wu_alert({content: "保存成功", type: 4 });
                                    module.LoadList();
                                    layer.close(index);
                                } else {
                                    // layer.msg(rsp.Message);
                                    wuFormModule.wu_alert({content: rsp.Message, type: 2 });
                                    return false;
                                }
                            }
                        });
                    }
                } else if (atvRemarkType == 3) {
                    var PrintscreenPics = productImages.map(function (item) {
                        return { ImageUrl: item.ImageUrl, FileName: item.FileName }
                    })
                    commonModule.Ajax({
                        type: "POST",
                        url: "/AfterSale/UpdateRemark",
                        data: { AfterSaleCodes: afterSaleCodes, Remark: $('#txt_area_seller_remark_flag').val(), RemarkFlag: checkValue, PrintscreenPic: PrintscreenPics },
                        loadingMessage: '保存中...',
                        success: function (rsp) {
                            if (rsp.Success) {
                                // layer.msg("保存成功");
                                wuFormModule.wu_alert({content: "保存成功", type: 4 });
                                module.LoadList();
                                layer.close(index);
                            } else {
                                // layer.msg(rsp.Message);
                                wuFormModule.wu_alert({content: rsp.Message, type: 2 });
                                return false;
                            }
                        }
                    });
                }
                return false;
            }
        });
    }

    var atvRemarkType = '';
    
    // 选择备注标签
    module.selectRemarkNavBtn = function (remarkType) {
        var thisFunc = function () {
            if (atvRemarkType != remarkType) {
                remarkNavHtml(remarkType, false, atvRow);
            }
        }
        if (remarkType == 2) {
            commonModule.FxPermission(function (p) {
                commonModule.CheckPermission(function (success) {
                    if (success) {
                        thisFunc();
                    }
                    else return;
                }, p.UpdateOrderSellerRemark);
            });
        } else {
            thisFunc();
        }
        

    }

    // 选择旗帜
    var checkValue = '';
    module.onChangeFlagV1 = function (val) {
        if (checkValue != val) {
            checkValue = val;
            $("#new-convention-flag-box .new-convention-flag-item .new-convention-flag-item-radio-dot").parent().css('border-color', '#dbdbdb');
            $("#new-convention-flag-box .new-convention-flag-item .new-convention-flag-item-radio-dot").removeClass("new-convention-flag-item-radio-dot");
            $("#new-convention-flag-box .new-convention-flag-item .new-convention-flag-item-radio" + val).css('border-color', '#0888ff');
            $("#new-convention-flag-box .new-convention-flag-item .new-convention-flag-item-radio" + val).find('span').addClass("new-convention-flag-item-radio-dot");
        }
    }

    function remarkFlagHtmlV2(remarkType) {
        if (remarkType == '2' && TaobaoFlagList && TaobaoFlagList.length) {
            var systemFlags = [
                { "Value": "0", "Text": "灰旗/无旗帜", "Color": "#dddddd" },
                { "Value": "1", "Text": "红旗", "Color": "#ea3323" },
                { "Value": "4", "Text": "黄旗", "Color": "#f8d35d" },
                { "Value": "3", "Text": "绿旗", "Color": "#b6dd53" },
                { "Value": "2", "Text": "蓝旗", "Color": "#1335f5" },
                { "Value": "5", "Text": "紫旗", "Color": "#8720be", },
                { "Value": "6", "Text": "橙旗", "Color": "#f48804" },
                { "Value": "7", "Text": "浅蓝旗", "Color": "#41b4fa" },
                { "Value": "8", "Text": "浅粉旗", "Color": "#e5b6b6" },
                { "Value": "9", "Text": "深绿旗", "Color": "#9ba217" },
                { "Value": "10", "Text": "桃红旗", "Color": "#e4248e" }
            ];
            systemFlags.forEach(function (item) {
                TaobaoFlagList.forEach(function (item1) {
                    if (item.Value == item1.SystemFlag) {
                        item.Text = item1.FlagTag ? item1.FlagTag : '无标签'
                    }
                })
            })
            var tplt = $.templates("#convention-flag-tmpl");
            var html = tplt.render({ conFlagList: systemFlags, remarkType: remarkType });
            $("#new-convention-flag-box").html(html);
        } else {
            if (remarkType == "2") {
                var conFlagList = [
                    { "Value": "0", "Text": "灰旗/无旗帜", "Color": "#dddddd" },
                    { "Value": "1", "Text": "红旗", "Color": "#ea3323" },
                    { "Value": "4", "Text": "黄旗", "Color": "#f8d35d" },
                    { "Value": "3", "Text": "绿旗", "Color": "#b6dd53" },
                    { "Value": "2", "Text": "蓝旗", "Color": "#1335f5" },
                    { "Value": "5", "Text": "紫旗", "Color": "#8720be" },
                ];
                var tplt = $.templates("#convention-flag-tmpl");
                var html = tplt.render({ conFlagList: conFlagList, remarkType: remarkType });
                $("#new-convention-flag-box").html(html);
            } else {
                var conFlagList = [
                    { "Value": "-1", "Text": "无旗帜" },
                    { "Value": "0", "Text": "灰旗", "Color": "#dddddd" },
                    { "Value": "1", "Text": "红旗", "Color": "#ea3323" },
                    { "Value": "4", "Text": "黄旗", "Color": "#f8d35d" },
                    { "Value": "3", "Text": "绿旗", "Color": "#b6dd53" },
                    { "Value": "2", "Text": "蓝旗", "Color": "#1335f5" },
                    { "Value": "5", "Text": "紫旗", "Color": "#8720be" },
                ];
                var tplt = $.templates("#convention-flag-tmpl");
                var html = tplt.render({ conFlagList: conFlagList, remarkType: remarkType });
                $("#new-convention-flag-box").html(html);
            }
        }
    }


    function remarkNavHtml(remarkType, isBatch, row) {
        if (remarkType == '1') {
            $(".newAmendRemark .remark_nav_title .remark_nav_text").text('备注内容会同步到店铺后台的售后订单上。');
            $(".newAmendRemark .new_reamrk_left_title").text('备注内容');
            $('#txt_area_seller_remark_flag').attr('placeholder', '请输入售后备注信息').css('height', isBatch ? '456px' : '408px');

            $('#txt_area_seller_remark_flag').val('');
            $('#txt_area_seller_remark_count').text(0);

            $(".newAmendRemark .new_reamrk_img").hide();
            $(".newAmendRemark .history_reamrk_box").hide();
            $(".newAmendRemark .new_reamrk_flag_list_box").hide();
        } else if (remarkType == '2') {
            var remarkNavTitle1 = '旗帜与备注内容会同步到店铺后台订单的卖家备注上。';
            if(!isBatch) {
                if (row && row.PlatformType == 'Taobao') {
                    var skip = '<a href="https://myseller.taobao.com/home.htm/trade-platform/tp/sold" target="_blank" style="color: #0888FF;">前往店铺后台</a>'
                    remarkNavTitle1 = '<span>旗帜与备注内容会同步到店铺后台订单的卖家备注上，可'+ skip +'新增、修改标签。</span>'
                } else {
                    remarkNavTitle1 = '旗帜与备注内容会同步到店铺后台订单的卖家备注上。';
                }
            } 
            $(".newAmendRemark .remark_nav_title .remark_nav_text").html(remarkNavTitle1);
            $(".newAmendRemark .new_reamrk_left_title").text('备注内容');
            $('#txt_area_seller_remark_flag').attr('placeholder', '请输入卖家备注信息').css('height', isBatch ? '392px' : '344px');
            $(".newAmendRemark .new_reamrk_img").hide();
            $(".newAmendRemark .history_reamrk_box").hide();
            remarkFlagHtmlV2(remarkType);
            $(".newAmendRemark .new_reamrk_flag_list_box").css('display', 'flex');
            if (!isBatch && row && row.SellerRemarkFlag) {
                checkValue = row.SellerRemarkFlag;
                $("#new-convention-flag-box .new-convention-flag-item .new-convention-flag-item-radio" + row.SellerRemarkFlag).css('border-color', '#0888ff');
                $("#new-convention-flag-box .new-convention-flag-item .new-convention-flag-item-radio" + row.SellerRemarkFlag).find('span').addClass("new-convention-flag-item-radio-dot");
            } else {
                checkValue = '';
            }

            $('#txt_area_seller_remark_flag').val('');
            $('#txt_area_seller_remark_count').text(0);

        } else if (remarkType == '3') {
            $(".newAmendRemark .remark_nav_title .remark_nav_text").text('留言内容仅同步给合作方可见，不会同步到店铺后台。');
            $(".newAmendRemark .new_reamrk_left_title").text('留言内容');
            $('#txt_area_seller_remark_flag').attr('placeholder', '请输入售后留言信息').css('height', isBatch ? '320px' : '224px');
            remarkFlagHtmlV2(remarkType);
            $(".newAmendRemark .new_reamrk_flag_list_box").css('display', 'flex');
            if (!isBatch && row) {
                checkValue = row.AfterSaleRemarkFlag;
                if (row.AfterSaleRemarkFlag) {
                    $("#new-convention-flag-box .new-convention-flag-item .new-convention-flag-item-radio" + row.AfterSaleRemarkFlag).css('border-color', '#0888ff');
                    $("#new-convention-flag-box .new-convention-flag-item .new-convention-flag-item-radio" + row.AfterSaleRemarkFlag).find('span').addClass("new-convention-flag-item-radio-dot");
                } else {
                    $("#new-convention-flag-box .new-convention-flag-item .new-convention-flag-item-radio-1").css('border-color', '#0888ff');
                    $("#new-convention-flag-box .new-convention-flag-item .new-convention-flag-item-radio-1").find('span').addClass("new-convention-flag-item-radio-dot");
                } 
            } else {
                checkValue = '';
            }

            if (!isBatch && row && row.AfterSaleRemark) {
                $('#txt_area_seller_remark_flag').val(row.AfterSaleRemark);
                $('#txt_area_seller_remark_count').text(row.AfterSaleRemark.length);
            } else {
                $('#txt_area_seller_remark_flag').val('');
                $('#txt_area_seller_remark_count').text(0);
            }

            $(".newAmendRemark .new_reamrk_img").css('display', 'flex');
            if (productImages && productImages.length) {
                renderProductImages();
            } else {
                if (row && row.PrintscreenPic && row.PrintscreenPic.length && !isBatch) {
                    productImages = row.PrintscreenPic;
                    productImages.forEach(function (item) {
                        var ImageUrl = item.ImageUrl;
                        item.ImageUrl = !item.TransitUrl ? ImageUrl.split('?objectKey=')[1] : item.ImageUrl;
                        item.TransitUrl = item.TransitUrl ? item.TransitUrl : newTransformImgSrc(ImageUrl);
                    })
                } else {
                    productImages = [];
                }
                renderProductImages();
            }
            
            if (isBatch) {
                $(".newAmendRemark .history_reamrk_box").hide();
            } else {
                $(".newAmendRemark .history_reamrk_box").css('display', 'flex');
                $("#history_reamrk_content").empty();
                GetMessageHRecord(atvLogicorderid);
            }
        }

        if (isBatch) {
            $(".newAmendRemark .remark_nav_box").hide();
        } else {
            $(".newAmendRemark .remark_nav_box").css('display', 'flex');
            // 移除其他项的 "remark_nav_item_active" 类
            $(".newAmendRemark .remark_nav_box .remark_nav_item").removeClass("remark_nav_item_active");

            // 添加 "remark_nav_item_active" 类到当前项
            $("#remark_nav_item_" + remarkType).addClass("remark_nav_item_active");
        }
        
        atvRemarkType = remarkType;
    }

    module.onkeydownRemark = function () {
        $('#txt_area_seller_remark_count').text($('#txt_area_seller_remark_flag').val().length);
    }

    var productImages = [] // 备注主图
    //初始化主图
	function renderProductImages() {
		var tplt = $.templates("#newProductPicShow_temp");
		var html = tplt.render({ productImages: productImages });
        var restrictNumber = productImages.length + '/6';
		$("#newProductPicShow").html(html);
		$("#newProductPicShow .restrictNumber").text(restrictNumber);

		var productPicsOptions = {};
		productPicsOptions.isMultiple = true;
		productPicsOptions.callBack = getProductPic;
		productPicsOptions.limit = 6;
		productPicsOptions.isMultiple = true;
        productPicsOptions.businessType = 'AfterSale';

		$(".addnewProductPicShow_item").each(function (i, item) {
			productPicsOptions.ele = "#" + $(item).attr("id");
			var upProductPicsOptionsMenu = new upPicFileModule();
			upProductPicsOptionsMenu.initData(productPicsOptions);
		})

		function getProductPic(resultData) {
			if (resultData.length + productImages.length > 6) {
				// layer.msg("主图最多只能上传6张！")
                wuFormModule.wu_alert({content: "主图最多只能上传6张！", type: 2 });
				return;
			}
            for (var i = 0; i < resultData.length; i++) {
                resultData[i].TransitUrl = newTransformImgSrc(resultData[i].TransitUrl);
                productImages.push(resultData[i]);
			}
			renderProductImages();
		}
    }

    function newTransformImgSrc(src) {
        var newSrc = '';
        if (src != null && src != undefined && src != "//.") {
            if (!src || (src.indexOf('http') > -1 || src.indexOf('https') > -1)) {
                newSrc = src
            } else {
                var token = $("#token_input").val() ? $("#token_input").val() : '';
                newSrc = src + '&platform=' + commonModule.CloudPlatformType + '&token=' + token;
            }
        }
        return newSrc;
    }

    module.delProductPic = function (index) {
        if (event) {
            event.stopPropagation();
        }
		for (var i = 0; i < productImages.length; i++) {
			if (index == i) {
				productImages.splice(i, 1);
			}
		}
		renderProductImages();
    }

    module.onUploadPreviewImages = function (index) {
        if (productImages && productImages.length) {
            var images = productImages.map(function (item) {
                return item.TransitUrl
            })
            commonModule.onShowPreviewImages(index, images);
        }
    }
    // 售后图片大图预览
    module.onAfterSalesPreviewImages = function (index, orderCode, afterSaleCode) {
        var rowData = afterSaleDatas.find(function (item) {
            return item.OrderCode == orderCode
        });
        var currentOrder = rowData.Orders.find(function (item) {
            return item.AfterSaleCode == afterSaleCode
        });
        var images = JSON.parse(currentOrder.Ext.Evidence);
        //console.log("images", images);
        commonModule.onShowPreviewImages(index, images, 'TouTiaoAfterSalesImg');
    }


    module.onPreviewImages = function (id, index) {
        var findData = historyRemarkList.find(function (item) {
            return item.Id == id
        })

        if (findData.PrintscreenPics && findData.PrintscreenPics.length) {
            var images = findData.PrintscreenPics.map(function (item) {
                return item.ImageUrl
            })
            commonModule.onShowPreviewImages(index, images);
        } 
    }

    module.onTablePreviewImages = function (rowIndex, PIndex, index) {
        event.stopPropagation();
        var PrintscreenPic = afterSaleDatas[rowIndex].Orders[PIndex].PrintscreenPic;
        if (PrintscreenPic && PrintscreenPic.length) {
            var images = PrintscreenPic.map(function (item) {
                return newTransformImgSrc(item.ImageUrl);
            })
            commonModule.onShowPreviewImages(index, images);
        } 
    }

    function renderhistoryReamrk() {
		var tplt = $.templates("#history_reamrk_box_temp");
		var html = tplt.render({ historyRemarkList: historyRemarkList });
        $("#history_reamrk_content").show();
		$("#history_reamrk_content").html(html);
        if (historyRemarkList.length > 0) {
            $(".newAmendRemark .history_reamrk_content_isShow").hide();
        } else {
            $(".newAmendRemark .history_reamrk_content_isShow").show();
            $("#history_reamrk_content").hide();
        }
	}
    //批量设置拦截状态
    module.batchInterceptStatus = function (status) {
        var $checkedList = $(".order-chx:checked");
        if ($checkedList.length == 0) {
            // layer.msg("请先勾选订单");
            wuFormModule.wu_alert({content: "请先勾选订单", type: 2 });
            return;
        }
        var afterSaleCodes = [];

        var haveIntercept = false;
        var haveNoIntercept = false;
        $checkedList.each(function (i, item) {
            var inte = item.getAttribute("data-intercept")
            if (!!inte && inte !== "0") {
                haveIntercept = true;
                if (status == "0") {
                    afterSaleCodes.push(item.getAttribute("data-ascode"));
                }
            } else if (!inte || inte === "0") {
                haveNoIntercept = true;
                if (status == "1") {
                    afterSaleCodes.push(item.getAttribute("data-ascode"));
                }
            }
        });

        var showMsg = "";
        var submitBtnText = "确认操作";
        if (status == "1") {
            if (haveIntercept) {
                showMsg = "您勾选的部分订单已标记为已拦截，是否过滤？";
                submitBtnText = "继续操作剩余订单";
            } else {
                showMsg = "是否标记订单为已拦截？";
            }
        } else if (status == "0") {
            if (haveNoIntercept) {
                showMsg = "您勾选的部分订单不属于拦截订单，是否过滤？";
                submitBtnText = "继续操作剩余订单"
            } else {
                showMsg = "是否取消标记订单为已拦截？";
            }
        }

        var html = '<div class="" style="display: flex;font-size:15px;flex-direction:column">';
        html += '<div ><div style="width:100%;margin-bottom:20px;" class="wu-f14 wu-c09">' + showMsg + '</div></div>';
        html += '<div style="display: flex;justify-content: flex-end;"><button class="wu-btn wu-btn-mid wu-primary closeInterceptDialog" style="margin-right: 10px;">取消操作</button><button class="wu-btn wu-btn-mid submitIntercept">' + submitBtnText + '</button></div>';
        html += '</div>';

        var dialog = layer.open({
            type: 1,
            title: '已拦截标记确认', //不显示标题
            content: html,
            skin: 'wu-dailog',
            area: '400px', //宽高
            btn: 0
        });

        $(".closeInterceptDialog").click(function () {
            layer.close(dialog);  // 关闭对话框
        })

        $(".submitIntercept").click(function () {
            if (afterSaleCodes.length === 0) {
                if (status === "1")
                    // layer.msg("勾选的订单都已设置拦截");
                    wuFormModule.wu_alert({content: "勾选的订单都已设置拦截", type: 2 });
                else
                    // layer.msg("勾选的订单都已取消拦截");
                    wuFormModule.wu_alert({content: "勾选的订单都已取消拦截", type: 2 });
                layer.close(dialog);
                return;
            }

            commonModule.Ajax({
                type: "POST",
                url: "/AfterSale/UpdateInterceptStatus",
                data: { "AfterSaleCodes": afterSaleCodes, "InterceptFxUserId": status },
                success: function (rsp) {
                    // console.log(rsp);
                    if (rsp.Success) {
                        if(!!rsp.Data.Message)
                            layer.alert(rsp.Data.Message,{ skin: 'wu-dailog'});
                        else
                            wuFormModule.wu_alert({content: "操作成功", type: 4 });
                            // layer.msg("操作成功");

                        var successDatas = rsp.Data.ReturnData;
                        // console.log(successDatas);
                        // 更新页面数据并重新渲染表格
                        var titles = $('.tr-main-title');
                        titles.each(function (i, item) {
                            var ascode = $(item).attr("data-ascode");
                            var input = $(item).find("input:first");
                            if (successDatas.indexOf(ascode)!==-1) {
                                $(input).attr("data-intercept", status);
                                var button = $(item).find('button:first');
                                if (status == "0") {
                                    if (button) {
                                        button.hide();
                                    }
                                } else {
                                    if (button) {
                                        button.show();
                                    }
                                }
                            }
                        });
                    } else {
                        if (!!rsp.Message)
                            wuFormModule.wu_alert({content: rsp.Message, type: 2 });
                            // layer.msg(rsp.Message);
                        else
                            wuFormModule.wu_alert({content: "系统繁忙，请稍后再试", type: 2 });
                            // layer.msg("系统繁忙，请稍后再试");
                    }
                    layer.close(dialog);
                }
            });
        })
    }

    //设置状态
    module.setStatus = function (afterSaleCode, status) {
        if (event) {
            event.stopPropagation();
        } else {
            window.e.returnValue = false;
        };

        var platStatus = "MANUAL_PROCESSING";
        var statusName = "售后中";
        var sureAfterSaleDailogTitle = "您确定打开售后？";
        var replace_html = $("#td_opt_" + afterSaleCode).html();

        if (status == 22) {//关闭售后
            platStatus = "MANUAL_CLOSE";
            statusName = "售后关闭";
            sureAfterSaleDailogTitle = "您确定关闭售后？";
            replace_html = replace_html.replace("关闭", "打开");
            replace_html = replace_html.replace("AfterSaleModule.setStatus('" + afterSaleCode + "',22)", "AfterSaleModule.setStatus('" + afterSaleCode + "',21)");
        }
        else {
            replace_html = replace_html.replace("打开", "关闭");
            replace_html = replace_html.replace("AfterSaleModule.setStatus('" + afterSaleCode + "',21)", "AfterSaleModule.setStatus('" + afterSaleCode + "',22)");
        }

        var url = "/AfterSale/UpdateStatus";
        $("#sureAfterSaleDailog-title").html(sureAfterSaleDailogTitle);
        var loadIndex = layer.open({
            type: 1,
            title: "确认操作", //不显示标题
            content: $("#sureAfterSaleDailog"),
            skin: 'wu-dailog',
            area: ['350'], //宽高
            btn: ['确认', '取消'],
            yes: function () {
                var afterSaleCodes = [];
                afterSaleCodes.push(afterSaleCode);
                commonModule.Ajax({
                    type: "POST",
                    url: url,
                    data: { "AfterSaleCodes": afterSaleCodes, "Status": status, "PlatStatus": platStatus },
                    success: function (rsp) {
                        if (rsp.Success) {
                            // layer.msg("操作成功");
                            wuFormModule.wu_alert({content: "操作成功", type: 4 });
                            //更新状态显示
                            $("#status-" + afterSaleCode).html('<span class="aftersalesatus_' + status + '"><i class="dot"></i>' + statusName + '</span>');
                            $("#td_opt_" + afterSaleCode).html(replace_html);
                        } else {
                            // layer.msg("设置失败" + rsp.ErrorCode + rsp.Message, { time: 4000 });
                            wuFormModule.wu_alert({content: "设置失败" + rsp.ErrorCode + rsp.Message, type: 2 });
                        }
                        layer.close(loadIndex);
                    }
                });
            },
            cancel: function () {
            }
        });
    }

    //退货入库
    module.returnStockIn = function (afterSaleItemCode) {
        if (event) {
            event.stopPropagation();
        } else {
            window.e.returnValue = false;
        };
        var url = "/AfterSale/ReturnStockIn";
        $("#sureAfterSaleDailog-title").html("您确定退货入库?");
        var loadIndex = layer.open({
            type: 1,
            title: "确认操作", //不显示标题
            content: $("#sureAfterSaleDailog"),
            skin: 'wu-dailog',
            area: ['350'], //宽高
            btn: ['确认', '取消'],
            yes: function () {
                var afterSaleItemCodes = [];
                afterSaleItemCodes.push(afterSaleItemCode);
                commonModule.Ajax({
                    type: "POST",
                    url: url,
                    data: { "AfterSaleItemCodes": afterSaleItemCodes },
                    success: function (rsp) {
                        if (rsp.Success) {
                            // layer.msg("操作成功");
                            wuFormModule.wu_alert({content: "操作成功", type: 4 });
                            //更新状态显示
                        } else {
                            // layer.msg("设置失败" + rsp.ErrorCode + rsp.Message, { time: 4000 });
                            wuFormModule.wu_alert({content: "设置失败" + rsp.ErrorCode + rsp.Message, type: 2 });
                        }
                        layer.close(loadIndex);
                    }
                });
            },
            cancel: function () {
            }
        });
    }

    //跳转到打单发货页
    module.toWaitOrderPage = function () {
        var $checkedList = $(".order-chx:checked");
        if ($checkedList.length == 0) {
            // layer.msg("请先勾选订单");
            wuFormModule.wu_alert({content: "请先勾选订单", type: 2 });
            return;
        }
        var pids = [];
        $checkedList.each(function (i, item) {
            var pid = $(this).attr("data-pid");
            if (pids.indexOf(pid) < 0) {
                pids.push(pid);
            }
        });
        if (pids.length > 100) {
            // layer.msg("不能超过100个订单");
            wuFormModule.wu_alert({content: "不能超过100个订单", type: 2 });
            return;
        }
        var url = AlibabaFenFaSystemUrl + "/Common/Page/NewOrder-WaitOrder?from=oo";
        var newUrl = commonModule.rewriteUrl(url);//token
        newUrl = commonModule.dbnameToAjaxUrl(newUrl);//dbname
        var f = $('<form>');
        var p = $('<input>');
        f.attr("target", "_parent");
        //f.attr("target", "cloudFrame");
        f.attr("method", "post");
        f.attr("action", newUrl);
        p.attr("name", "pids");
        p.val(pids);
        f.append(p);
        $('body').append(f);
        f.submit().end().remove();
    }

    module.newToAllOrderPage = function (platformType, platformOrderId, areaName) {

        var url = AlibabaFenFaSystemUrl + "/Common/Page/NewOrder-AllOrder?isTar=all&fromUrl=AfterSale&platformTypeName=" + platformType + "&platformOrderId=" + platformOrderId + "&areaName=" + areaName;
        var newUrl = commonModule.rewriteUrl(url);//token
        newUrl = commonModule.dbnameToAjaxUrl(newUrl);//dbname
        window.open(newUrl, '_blank')

    }


    //跳转到百度查询
    module.viewLogisticTraces = function (that, expressName) {
        expressName = commonModule.RenameExpressName(expressName);
        var val = $(that).text().trim();
        //var url = 'https://www.baidu.com/s?wd=' + expressName + " " + val;
        //if (expressName.indexOf("顺丰") != -1 || expressName.indexOf("丰网") != -1)
        //    url = 'https://www.sf-express.com/cn/sc/dynamic_function/waybill/#search/bill-number/' + val;
        //window.open(url);

        commonModule.ViewLogisticTraces(val, expressName);
    }


    /*******************Excel导出*********************/
    module.ExportToExcel = function () {
        common.CheckExportPermission(function (success) {
            if (success) {
                thisFunc();
            } else return;
        }, 'afterSale');
        var thisFunc = function () {
            var len = $("#ShareAfterSaleList_body .layui-mytable-tr").length;
            if (len == 0) {
                // layer.msg("无数据，请不要导出");
                wuFormModule.wu_alert({content: "无数据，请不要导出", type: 2 });
                return;
            }

        //if (AfterSaleExportTask && AfterSaleExportTask.Status >= 0 && AfterSaleExportTask.Status < 4) {
        //    layer.msg("已存在订单导出任务，如需重新导出，请先取消再创建新导出任务");
        //    return;
        //}

            var requestModel = _getAfterSaleRequertModel();
            if (requestModel == null)
                return;
            var url = "/AfterSale/ExportExcel";
            exportOrderModule.ExportExcel({
                url: url,
                type: 'POST',
                data: requestModel,
                loadMsg: "正在导出Excel，请稍等……",
                success: function () {
                    // layer.msg("导出成功");
                    wuFormModule.wu_alert({content: "导出成功", type: 4 });
                }
            });
        } 
    }

    //导出选中项
    module.ExportCheckedOrder = function () {
        common.CheckExportPermission(function (success) {
            if (success) {
                thisFunc();
            } else return;
        }, 'afterSale');
        var thisFunc = function () {
            var $checkedList = $(".order-chx:checked");
            if ($checkedList.length == 0) {
                // layer.msg("请先勾选售后单再导出");
                wuFormModule.wu_alert({content: "请先勾选售后单再导出", type: 2});
                return;
            }
            var exportIds = [];
            $checkedList.each(function (i, item) {
                var id = $(this).attr("data-id");
                exportIds.push(id);
            });

            if (exportIds == "" || exportIds.length == 0) {
                // layer.msg("请先勾选售后单再导出.");
                wuFormModule.wu_alert({content: "请先勾选售后单再导出", type: 2});
                return;
            }

            var sourceFlag = $("#sourceflag .active").attr("data-sourceflag") || "0";
            var sendState = $("#layui_mynav_childrenNav .active").attr("data-sendstate") || "";

            requestModel = {};
            requestModel.Ids = exportIds;
            requestModel.SourceFlag = sourceFlag;
            requestModel.SendState = sendState;
        requestModel.DisabledTaskVerification = true;

            var url = '/AfterSale/ExportExcel';
            exportOrderModule.ExportExcel({
                url: url,
                type: 'POST',
                data: requestModel,
                loadMsg: "正在导出Excel，请稍等……",
                success: function () {
                    // layer.msg("导出成功");
                    wuFormModule.wu_alert({content: "导出成功", type: 4});
                }
            });
        }
    }


    common.cursorShowUl("#ExportExcelBtn", 29);

    module.SaveExportSet = function () {
        common.CheckExportPermission(function (success) {
            if (success) {
                thisFunc();
            } else return;
        }, 'afterSale');
        var thisFunc = function () {
            var tmpl = $.templates("#newExportSet-detail-tmpl");
            var html = tmpl.render();
            layer.open({
                type: 1,
                title: "导出设置", //不显示标题
                content: html,
                area: ['650px', '100'], //宽高
                btn: ['保存', '关闭'],
                skin: 'wu-dailog',
                success: function () {
                    _loadOrderExportSet();
                    $("#checkall_setting").on("change", function () {
                        var checked = this.checked;
                        $("#setting_items input[type=checkbox]").prop("checked", checked)
                    })
                },
                yes: function () {
                    _saveOrderExportSet();
                },
                cancel: function () {

                }
            });
        }
    }

    var _loadOrderExportSet = function () {
        var items = AfterSaleOrderExportSet.CheckedItems || [];
        if (items.length > 0) {
            commonModule.Foreach(items, function (i, item) {
                $(".setting_content :checkbox[value='" + item.Value + "']").prop("checked", true);
            });
        }
        var showli = $("#setting_items li input[type=checkbox]").length;
        if (showli == items.length) {
            $("#checkall_setting").prop("checked", true);
        }
    }

    var _saveOrderExportSet = function () {
        var checkedItems = [];
        var $checkbox = $("#setting_items>ul>li :checkbox:checked");
        $checkbox.each(function () {
            var val = $(this).val();
            var text = $(this).parent().text();
            var from = $(this).attr("name");
            checkedItems.push({ "Text": text, "Value": val, "From": from });
        });

        if (checkedItems.length == 0) {
            // layer.msg("导出订单字段项不能为空，请修改设置。");
            wuFormModule.wu_alert({content: "导出订单字段项不能为空，请修改设置。", type: 2});
            return;
        }

        // 保存Excel导出配置信息
        AfterSaleOrderExportSet.CheckedItems = checkedItems;
        var key = "/ErpWeb/AfterSale/AfterSaleOrderExportSet";
        commonModule.SaveCommonSetting(key, JSON.stringify(AfterSaleOrderExportSet), function (rsp) {
            if (!rsp.Success) {
                // layer.msg("Excel导出配置保存失败");
                wuFormModule.wu_alert({content: "Excel导出配置保存失败", type: 2});
            }
            layer.closeAll(); //关闭弹出框
        });

    }

    /*******************Excel导出*********************/

    module.afterSaleInfoShowOrHide = function (id) {
        $(this).toggleClass("zk");
        $("#afterSaleInfoShow_" + id).toggleClass("hideAfterSaleInfoShow");
    }

    module.allAfterSaleInfoShowOrHide = function () {

        if (this.checked) {
            $(".aliAftersale-trWrap .dsistributionOrderShow").addClass("hideAfterSaleInfoShow");
            $(".aliAftersale-trWrap .searchShowOrHide-wrap-icon").addClass("zk");
        } else {
            $(".aliAftersale-trWrap .dsistributionOrderShow").removeClass("hideAfterSaleInfoShow");
            $(".aliAftersale-trWrap .searchShowOrHide-wrap-icon").removeClass("zk");

        }

    }
    common.cursorShowUl("#remarksBtn", -124);

    // 跳转到对应的页面，带token、dbname
    module.tarUrl = function(url) {
        var newUrl = commonModule.rewriteUrlToMainDomain(url);//token
        window.open(newUrl);
    }


    return module;
}(AfterSaleModule || {}, commonModule, jQuery, layer));