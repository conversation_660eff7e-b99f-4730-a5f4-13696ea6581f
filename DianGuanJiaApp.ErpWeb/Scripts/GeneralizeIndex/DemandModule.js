//; (function (win) {




//    var dgjUrl_origin = "https://www.dgjapp.com/";
//    //var dgjUrl_origin = "http://127.0.0.7:30002/";

//    win.newDemandModule = {};
//    var demandPicArry = [];
//    var complaintTypeNum = 0;

//    newDemandModule.addDemandPic = function () { //添加图片		
//        $(this).next().click();
//    }


//    newDemandModule.changeDemandPic = function () { //选择图片
//        var e = event || window.event;
//        var that = this;

//        $(".productPicWrap .noInputData").remove();//选择图片时，删除警告框和提示语

//        demandPicArry = demandPicArry.sort(function (a, b) {
//            return a.sort - b.sort
//        });
//        var selectedPicFiles = this.files; //获取所有选中的文件
//        selectedPicShow(selectedPicFiles,this);
//    }

//    function selectedPicShow(selectedPicFiles, that) {

//        console.log("thithisthiss", that)

//        if (demandPicArry.length + selectedPicFiles.length < 6) {
//            $(that).prevAll('.addDemandPicBt').css({
//                display: 'flex'
//            });
//        } else {
//            $(that).prevAll('.addDemandPicBt').hide();
//        }

//        if (selectedPicFiles.length > 6 || demandPicArry.length + selectedPicFiles.length > 6) { //所选择的图片不要超过5张
//            layer.msg("不要超过6张");
//            return;
//        }

//        var promiseSelectPicArry = []; //解决异步任务  Promise.all调用
//        var maxSort = demandPicArry.length ? demandPicArry[demandPicArry.length - 1].sort + 1 : 0; //取产品图片数组 序号最大的值
//        for (var i = 0; i < selectedPicFiles.length; i++) {
//            if (!$$.checkFileExt(selectedPicFiles[i].name, ["jpg", "png", "gif"])) {
//                layer.msg("文件格式不对,jpg,png,gif");
//                return;
//            }
//            if (selectedPicFiles[i].size > 1000000) { //大于1m不上传 1000000
//                layer.msg("文件大小不能大于1000k");
//                return;
//            }
//            var p = new Promise(function (resolve, reject) { //用Promise 有异步任务 load				
//                var fileRead = new FileReader(); //      新建一个文件对象 读取文件信息  主要作显示用的 把flies图片转为base64位
//                fileRead.readAsDataURL(selectedPicFiles[i]);
//                var picName = selectedPicFiles[i].name;
//                var $sort = maxSort + i;
//                fileRead.addEventListener("load", function () {
//                    var param = {};
//                    var imgBase64 = this.result;
//                    param.Id = new Date().getTime();
//                    param.FileName = new Date().getTime() + picName;
//                    param.ImageUrl = imgBase64;
//                    param.sort = $sort;   //用于排序
//                    commonModule.Ajax({
//                        url: dgjUrl_origin + 'login/manageArticle/fkUpPic',
//                        data: param,
//                        async: true,
//                        loading: false,
//                        type: 'POST',
//                        success: function (rsp) {
//                            resolve(param);
//                            //console.log(rsp)
//                        }
//                    });

//                })
//            })
//            promiseSelectPicArry.push(p);
//        }

//        Promise.all(promiseSelectPicArry).then(function (resultData) {
//            that.value = "";
//            demandPicArry = demandPicArry.concat(resultData);
//            newDemandModule.initProductPicShow(demandPicArry, that);
//        })

//    }


//    newDemandModule.initProductPicShow = function (picData, that) { //渲染商品主图图片

//        var html = "";
//        for (var i = 0; i < picData.length; i++) {
//            html += '<div class="productPic-list" data-sort="' + picData[i].sort + '"  id=div' + i +
//                ' ondrop="newDemandModule.drop(event,this)" ondragover="newDemandModule.allowDrop(event)" draggable="true" ondragstart="newDemandModule.drag(event, this)">'
//            html += '<img src="' + picData[i].ImageUrl + '" alt="">'
//            html += '<i class="iconfont icon-laji" onclick=\'newDemandModule.delDemandPic("' + picData[i].Id + '",this)\' ></i>';
//            html += '</div>'
//        }
//        $(that).prevAll(".productPicShow").html(html);
//        if (picData.length < 6) { //图片小于5张时，显示添加按钮
//            $(that).prevAll(".addDemandPicBt").css({
//                display: 'flex'
//            });
//        } else {
//            $(that).prevAll(".addDemandPicBt").hide();
//        }
//    }
//    newDemandModule.delDemandPic = function (id,that) { //删除商品主图图片
//        var that = productPicWrap = $(that).closest('.productPicWrap').find('.addDemandPicFile')[0];
//        for (var i = 0; i < demandPicArry.length; i++) {
//            if (demandPicArry[i].Id == id) {
//                demandPicArry.splice(i, 1);
//                break;
//            }
//        }
//        newDemandModule.initProductPicShow(demandPicArry, that);
//    }




//    newDemandModule.downPicdrop = function (ev,that) {
//        ev.preventDefault();
//        var aFiles = ev.dataTransfer.files;
//        selectedPicShow(aFiles, $(that).next().get(0));
//    }



//    //以下是拖拽商品主图片图片更换位置js----------------------------------------------------

//    newDemandModule.allowDrop = function (ev) {
//        ev.preventDefault();
//    }
//    var srcdiv = null; //刚才开始拖元素
//    var srcSort = ""; //刚才开始拖序号			
//    newDemandModule.drag = function (ev, divdom) { //开始移动时触发函数 divdom为鼠标移动的元素
//        srcdiv = divdom;
//        ev.dataTransfer.setData("text/html", divdom.innerHTML); //设置一个名为text/html变量来存html
//        srcSort = $(divdom).attr("data-sort");
//    }
//    newDemandModule.drop = function (ev, divdom) { //移动后松开鼠标时触发   divdom为移到目标元素不是托动的元素
//        ev.preventDefault();
//        if (srcdiv != divdom) {
//            srcdiv.innerHTML = divdom.innerHTML;
//            divdom.innerHTML = ev.dataTransfer.getData("text/html"); //从text/html变量取值 			
//            var tarSort = $(divdom).attr("data-sort"); //获取当前拖放到目标元素序号
//            var srcIndex = null;
//            var tarIndex = null; //获取开始拖元素和拖放到目标元素 所在索引

//            for (var i = 0; i < demandPicArry.length; i++) {
//                if (demandPicArry[i].sort == srcSort) {
//                    srcIndex = i
//                } else if (demandPicArry[i].sort == tarSort) {
//                    tarIndex = i
//                }
//            }
//            demandPicArry[srcIndex].sort = tarSort - 0;
//            demandPicArry[tarIndex].sort = srcSort - 0;
//            demandPicArry = demandPicArry.sort(function (a, b) {
//                return a.sort - b.sort
//            });
//        }
//    }



//    newDemandModule.submitSuggest = function () {
//        if ($("#userRole_ul>li.active").length == 0) {
//            layer.msg('请选择您的身份');
//            return;
//        }
//        if ($("#disposeRank_ul>li.active").length == 0) {
//            layer.msg('请选择您的需求紧急程度');
//            return;
//        }
//        if ($('#isOtherISV').val() == "") {
//            layer.msg('请选择其他系统是否有同款功能');
//            return;
//        }
//        if ($('#isOtherISV').val() == 1 && $("#otherISVName").val().trim()=="") {
//            layer.msg('请输入竞品应用名称');
//            return;
//        }

//        if ($('#isCallBack').val() == "") {
//            layer.msg('请是否愿意接受产品经理电话回访');
//            return;
//        }
        
//        if ($('#isCallBack').val() == 1 && !checkMoveAndTel($("#callBackContact").val().trim())) {
//            layer.msg('请输入您的需求对接人正确联系方式');
//            return;
//        }

//        if ($('#disposeContent').val().length >500 ) {
//            layer.msg('请输入500字以内');
//            return;
//        }

//        var subimitObj = {};
//        subimitObj.submitType = 1;//提交类型  1为需求反馈  2为用户投诉
//        subimitObj.id = new Date().getTime();
//        subimitObj.userInfo = $("#userInfo s").text();
//        subimitObj.currShop = commonModule.CurrShop;
//        subimitObj.userContact = $("#userInfo s").text();
//        subimitObj.userRole = $("#userRole_ul>li.active").text();
//        subimitObj.disposeRank = $("#disposeRank_ul>li.active").text();
//        subimitObj.isOtherISV = $("#isOtherISV").val();
//        subimitObj.otherISVName = $("#otherISVName").val().trim();
//        subimitObj.disposeContent = $("#disposeContent").val().trim();
//        subimitObj.isCallBack = $("#isCallBack").val();
//        subimitObj.callBackContact = $("#callBackContact").val().trim();
//        var adPics = [];
//        demandPicArry.forEach(function (item) {
//            var obj = {};
//            obj.Id = item.Id;
//            obj.FileName = item.FileName;
//            obj.Sort = item.sort;
//            adPics.push(obj);
//        })
//        subimitObj.adPicss = JSON.stringify(adPics);
//        commonModule.Ajax({
//            url: dgjUrl_origin + 'newDemand/newDemandSuggest',
//            data: subimitObj,
//            async: true,
//            loading: true,
//            type: 'POST',
//            success: function (rsp) {
//                if (rsp.success) {
//                    reset("suggestFile");
//                    layer.close(showSuggestDailog)
//                    layer.msg(rsp.message);
                    
//                }
//            }
//        });
//    }

//    newDemandModule.submitComplaint = function () {

//        var subimitObj = {};

//        if (complaintTypeNum == 0) {  //0 客服人员接待问题    1 技术处理bug时效
//            if ($("#complaintType_uls_none>li.active").length == 0) {
//                layer.msg('请选择您要投诉的客服内容');
//                return;
//            }

//            if ($("#complaintName").val().trim() == "" || $("#complaintContactName").val().trim() == "") {
//                layer.msg('请选择您投诉的昵称和咨询方式');
//                return;
//            }
//            if ($("#isDeal").val() == "") {
//                layer.msg('请选择您问题后续是否得到处理');
//                return;
//            }
//            subimitObj.complaintContent = $("#complaintType_uls_none>li.active").text();

//        } else if (complaintTypeNum == 1) {

//            if ($("#complaintType_uls_two>li.active").length == 0) {
//                layer.msg('请选择您提交的bug多久未处理');
//                return;
//            }

//            if ($("#isAnswer").val() == "") {
//                layer.msg('请选择是否有客服同事为您后续反馈进度');
//                return;
//            }

//            subimitObj.untreatedTime = $("#complaintType_uls_two>li.active").text();

//        }

//        if ($("#isSupervisorCallBack").val()=="") {
//            layer.msg('请选择是否愿意接受电话回访');
//            return;
//        }
//        if ($("#complaintTel").val().trim() == "" && $("#complaintQQ").val().trim() == "") {
//            layer.msg('请您填写联系方式或QQ号码');
//            return;
//        }

//        if ($("#complaintOtherContent").val().length>500) {
//            layer.msg('反馈的内容在500字以内！');
//            return;
//        }
//        subimitObj.id = new Date().getTime();
//        subimitObj.userInfo = $("#userInfo s").text();
//        subimitObj.currShop = commonModule.CurrShop;
//        subimitObj.userContact = $("#userInfo s").text();

//        subimitObj.submitType = 2;
//        subimitObj.complaintType = complaintTypeNum == 0 ? '客服人员接待问题' : '技术处理bug时效';
//        subimitObj.complaintTypeNum = complaintTypeNum;

//        subimitObj.complaintName = $("#complaintName").val().trim();



//        subimitObj.complaintContactName = $("#complaintContactName").val().trim();
//        subimitObj.complaintOtherContent = $("#complaintOtherContent").val().trim();
//        subimitObj.isDeal = $("#isDeal").val();
//        subimitObj.isAnswer = $("#isAnswer").val();
//        subimitObj.isSupervisorCallBack = $("#isSupervisorCallBack").val();
//        subimitObj.complaintTel = $("#complaintTel").val();
//        subimitObj.complaintQQ = $("#complaintQQ").val();


//        var adPics = [];
//        demandPicArry.forEach(function (item) {
//            var obj = {};
//            obj.Id = item.Id;
//            obj.FileName = item.FileName;
//            obj.Sort = item.sort;
//            adPics.push(obj);
//        })
//        subimitObj.adPicss = JSON.stringify(adPics);

//        commonModule.Ajax({
//            url: dgjUrl_origin + 'newDemand/newDemandSuggest',
//            data: subimitObj,
//            async: true,
//            loading: true,
//            type: 'POST',
//            success: function (rsp) {
//                if (rsp.success) {
//                    reset('ComplaintFile');
//                    layer.close(showComplaintDailog);
//                    layer.msg(rsp.message);
//                }
//            }
//        });



//    }

//    function reset(ele) {
//        $("#userRole_ul>li").removeClass("active");
//        $("#disposeRank_ul>li").removeClass("active");
//        $("#isOtherISV").find('option[value=""]').prop("selected", true);
//        $("#otherISVName").val("").css({ display:'none' });
//        $("#disposeContent").val("");
//        $("#isCallBack").find('option[value=""]').prop("selected", true);
//        $("#callBackContact").val("");

//        $("#complaintType_ul>li").removeClass("active");
//        $("#complaintType_ul>li:eq(0)").addClass("active");
//        $("#complaintType_uls_none>li").removeClass("active");
//        $("#complaintType_uls_two>li").removeClass("active");
//        $("#complaintName").val("");
//        $("#complaintContactName").val("");
//        $("#complaintOtherContent").val("");
//        $("#isDeal").find('option[value=""]').prop("selected", true);
//        $("#isAnswer").find('option[value=""]').prop("selected", true);
//        $("#isSupervisorCallBack").find('option[value=""]').prop("selected", true);
//        $("#complaintTel").val("");
//        $("#complaintQQ").val("");
        
//        layui.form.render("select");

//        demandPicArry = [];
//        newDemandModule.initProductPicShow(demandPicArry, document.getElementById(ele));
//    }

//    function checkMoveAndTel(mobile, type) { // 匹配手机号和固话，正确返回true 错误返回false

//        if (typeof mobile == 'number') {              //  type 取值move为验证手机，type 取值tel为固定电话  其它值为验证两都符合一种都返回true
//            mobile = mobile.toString();
//        }
//        var tel = /^0\d{2,3}-?\d{7,8}$/;
//        var phone = /^1(3|4|5|7|8|9)\d{9}$/;
//        var isTrue = false;

//        if (type == "move") {
//            if (phone.test(mobile)) {
//                isTrue = true;
//            }
//        } else if (type == "tel") {
//            if (tel.test(mobile)) {
//                isTrue = true;
//            }
//        } else {
//            if (mobile.length == 11) { //手机号码
//                if (phone.test(mobile)) {
//                    isTrue = true;
//                }
//            } else if (mobile.length == 12 && mobile.indexOf("-") != -1 || mobile.length == 13 && mobile.indexOf("-") != -1) { //电话号码
//                if (tel.test(mobile)) {
//                    isTrue = true;
//                }
//            }
//        }

//        return isTrue;
//    }


//    $(function () {

//        $$.navActive("#userRole_ul", function (index, item) { }, 'active');

//        $$.navActive("#disposeRank_ul", function (index, item) { }, 'active');

//        $$.navActive("#complaintType_ul", function (index, item) {
//            complaintTypeNum = index;
//            $("#complaintType_uls .newDemandContentWrap-main-item-ul02").hide();
//            $("#complaintType_uls .newDemandContentWrap-main-item-ul02:eq('" + index + "')").show();

//            if (index == 0) {
//                $("#complaintType_picTitle").text('与客服对话相关截图');
//                $("#complaintType_picText").text('请上传与客服对话导致您需要投诉的相关截图证明(最多六张截图)');
//                $("#complaintType_kefuTitle").text('您要投诉的客服内容');
//                $(".newDemandContentWrap_content_itemThree").show();
//                $(".newDemandContentWrap_content_itemFourth").hide();

//            } else {
//                $("#complaintType_picTitle").text('该bug紧急程度是否影响您的亏损');
//                $("#complaintType_picText").text('请上传提交bug的截图资料或者对您造成损失的截图证明(最多六张截图)');
//                $("#complaintType_kefuTitle").text('您提交的bug多久未处理');
//                $(".newDemandContentWrap_content_itemThree").hide();
//                $(".newDemandContentWrap_content_itemFourth").show();

//            }



//        }, 'active');
//        $$.navActive("#complaintType_uls_none", function (index, item) { }, 'active');
//        $$.navActive("#complaintType_uls_two", function (index, item) { }, 'active');

//        layui.form.on('select(isOtherISV)', function (data) {
//            if (data.value == 1) {
//                $("input[name=otherISVName]").css({ display: 'inline-block' })
//            } else {
//                $("input[name=otherISVName]").css({ display: 'none' })
//            }
//        });
//        layui.form.on('select(isCallBack)', function (data) {

//        });
//    });

//    var showSuggestDailog = null;
//    var showComplaintDailog = null;

//    newDemandModule.showSuggest = function () {

//        showSuggestDailog=layer.open({
//            type: 1,
//            title: false, //不显示标题
//            content: $("#suggest_dailog"),
//            area: '760', //宽高
//            btn: false,
//            skin: 'adialog-Shops-skin',
//        });


//    }

//    newDemandModule.showComplaint = function () {

//        showComplaintDailog=layer.open({
//            type: 1,
//            title: false, //不显示标题
//            content: $("#complaint_dailog"),
//            area: '760', //宽高
//            btn: false,
//            skin: 'adialog-Shops-skin',
//        });


//    }

//})(window);