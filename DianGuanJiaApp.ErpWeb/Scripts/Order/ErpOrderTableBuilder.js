
///订单列表渲染
var optionsData = null;
var orderTableBuilder = (function (table, common, $, layer) {
    var _pageIndex = 1, _pageSize = 20;
    var isRenderFinished = true;
    var _triggerRecycle = false;
    var _isWhite = null;
    var isManualAuth = true; // 子账号-所有订单-手工发货权限校验

    var _pt = commonModule.CloudPlatformType;
    if (_pt == undefined)
        _pt = "";
    var _businessType = _pt.toLowerCase() == 'pinduoduo' ? '&businessType=cloudplatform' : '';
    // 支持卖家备注的平台---目前支持头条，拼多多，淘宝，快手，小红书，微信视频号，京东，度小店，阿里巴巴，淘工厂,哔哩哔哩
    var supportPlatformtTypes = ["TouTiao", "Pinduoduo", "Taobao", "KuaiShou", "XiaoHongShu", "WxVideo", "Jingdong", "DuXiaoDian", "Alibaba", "AlibabaC2M", "BiliBili"];

    $(function () {
        window.addEventListener('message', function (e) {
            if (e.data.operateType && e.data.operateType == "skuRelationList") {   //ifarme关联商品
                if (e.data.type == 'refresh') {
                    setTimeout(function () {
                        $('#SeachConditions').click();
                    }, 1000)
                    return
                } else {
                    $("#tarSkuRelationListWrap").removeClass('active');
                    $('body').css({ overflow: 'auto' });
                }
                if (e.data.type == 'complete') {
                    window.tarSkuRelationListWrapStatus = e.data.type;
                    setTimeout(function () {
                        $('#SeachConditions').click();
                    }, 1000)
                }

            }
        })
    })
    //var _order_waybillCode_dict = {};
    var defaultHeaderFormatter = function (col) {
        if (col.headerFormattter) {
            return col.headerFormattter(col);
        }
        else {
            return (col.aliasName && col.aliasName != "" ? col.aliasName : col.name);
        }
    };

    var DetailHeaderFormatter = function () {
        var html = '<label for="" class="showMoreOrHide wu-checkboxWrap wu-flex wu-yCenter">';
        html += '<input class="allshoworder wu-mR4" id="allshoworder" type="checkbox" onclick="waitOrderModule.AllShowCheck()">展开';
        html += '</label>';
        return html;
    }


    var defaultContentFormatter = function (row, col, index, ext) {
        if (!row.Id)
            return "";
        if (col.contentFormatter) {
            return col.contentFormatter(row, col, index, ext);
        }
        else if (row && col.field) {
            var content = row[col.field];
            if (!content)
                content = "";
            return content;
        }
        else
            return "";
    };
    var checkboxContentFormatter = function (row, col, index, classNamePrefix) {
        var className = "order-chx";
        if (classNamePrefix)
            className = classNamePrefix + className;
        var html = '<input type="checkbox" class="' + className + '" data-id="' + row.Id + '" data-oid="' + row.PlatformOrderId + '" data-pid="' + row.LogicOrderId + '" data-sid="' + row.ShopId + '" data-index="' + index + '" ' + ((classNamePrefix || row.checked) ? " checked" : "") + '>';
        return html;
    }
    var PrintStateContentFormatter = function (row, col, index, isNotFromOrderList) {
        var html = '';
        var platformStatus = row.PlatformStatus || "";
        var PlatformType = row.PlatformType || "";
        var status = "";
        if (platformStatus == "waitbuyerpay")
            status = "待付款";
        else if (platformStatus == "waitsellersend")
            status = "待发货";
        else if (platformStatus == "waitbuyerreceive")
            status = "已发货";
        else if (platformStatus == "success")
            status = "交易成功";
        else if (platformStatus == "cancel")
            status = "交易关闭";
        else if (platformStatus == "confirm_goods_but_not_fund")
            status = "货到付款";
        if (status && PlatformType != 'TikTok')
            html += '<span style="border-radius:3px;color:#fff;background-color:#3aadff;padding:1px 5px;margin-right:5px;display:inline-block;margin-top:2px;text-align: center;"><span style="display:inline-block;">' + status + '</span></span>';

        if (row.PrintState == 1)
            html += '<div title="已打印快递单" class="wu-f16 wu-color-d wu-weight600">' + ("√") + '</div>';
        if (row.PrintState == 2)
            html += '<div title="[部分]已打印快递单" class="wu-f16 wu-color-d wu-weight600">' + ("//") + '</div>';
        //else
        //    html += "&nbsp;";
        if (row.SendPrintTime)
            html += '<div style="color:#1ec6fe;font-size:16px;font-weight:bold;" title="已打印发货单">' + ("√") + '</div>';
        //else
        //    html += "&nbsp;";
        if (row.NahuoPrintTime)
            html += '<div style="color:#ff0000;font-size:16px;font-weight:bold;" title="已打印拿货单">' + ("√") + '</div>';
        //else
        //    html += "&nbsp;";
        if (row.MergeredType == 3 || row.MergeredType == 4) {
            var titlePrefix = "自动";
            if (row.MergeredType == 4) {
                titlePrefix = "手工";
            }
            //html += '<span title="' + titlePrefix + '合并的订单" style="border-radius:3px;color:#fff;background-color:#f5821f;padding:1px 2px;display:inline;margin-top:2px;text-align: center;" data-num="' + row.SubOrders.length + '" class="subOrdersNum"><span style="display:inline-block;">合单</span>' + row.SubOrders.length + '</span>';
            //html += '<span title="' + titlePrefix + '合并的订单" class="heTarget" data-num="' + row.MergerOrderCount + '" class="subOrdersNum"><span style="display:inline-block;">合单</span>' + row.MergerOrderCount + '<span title="点此拆分合单" class="splitItemOrder newSplitItemOrder" onclick="splitOrderModule.splitItemOrder(0)">拆</span></span>';
            html += '<div class="wu-flex wu-column">';
            html += '<span title="' + titlePrefix + '合并的订单" class="wu-tag wu-error-dev wu-dot" data-num="' + row.MergerOrderCount + '"><i>合单</i>' + row.MergerOrderCount + '</span>';
            html += '<span style="flex-shrink: 0;"><span title="点此拆分合单" class="wu-tag wu-success-dev wu-dot wu-hover wu-mT8" onclick="waitOrderModule.SingleSplitOrder(' + row.Index + ')">拆</span></span>';
            html += '</div>';
        }
        return html;
    }

    var ProductContentFormatter = function (row, col, index, classNamePrefix) {
        return "";
    }

    var PriceCountContentFormatter = function (row, col, index, classNamePrefix) {
        return "";
    }

    var RecieverContentFormatter = function (row, col, index, classNamePrefix) {
        var html = '<span id="orderToName' + row.LogicOrderId + '" class="ToName">' + (row.ToName || "") + '</span><span id="orderToPhone' + row.LogicOrderId + '" class="ToPhone">' + (row.ToPhone || "") + '</span>';
        return html;
    }
    var RemarkContentFormatter = function (row, col, index, classNamePrefix) {
        return "";
    }

    //是否为自己的订单
    var GetIsSelf = function (row) {
        if (!row.SubOrders || row.SubOrders.length == 0)
            return true;
        for (var i = 0; i < row.SubOrders.length; i++) {
            if (row.SubOrders[i].UpFxUserId != undefined && row.SubOrders[i].UpFxUserId == 0)
                return true;
        }
        return false;
    }

    var AllRecieverContentFormatter = function (row, col, index, classNamePrefix) {
        var orderDisplaySet = OrderDisplaySet;
        var isWxVideoExt = (row.PlatformType == "WxVideo" && row.ExtField2 != undefined && row.ExtField2 != "");
        var displayRecipientHtml = "";
        if (orderDisplaySet.RecipientSetting != "DisplayRecipient") {
            displayRecipientHtml = " style='display:none' ";
        }

        var html = "";
        html += '<div ' + displayRecipientHtml + '>';
        html += '<div>';
        if (row.PlatformType == "KuaiShou" && commonModule.HasTag(row.OrderTags, 'Kuaishou_orderPiecingGroup', 'Order') && !row.ToAddress) {
            html += '<span>拼团中，暂未拼成<span class="commonWenHao popoverCommon hover">?<span class="popoverCommon-warn" style="width:240px;">该拼单暂无收件地址，无需打印发货。</span></span></span>';
        } else if (row.PlatformType == "KuaiShou" && commonModule.HasTag(row.OrderTags, 'Present_Order', 'OrderItem') && !row.ToAddress) {
            html += '<span>待收礼人填写地址</span>';
        }
        else {
            html += '<input type="hidden" id="orderToAddress' + row.LogicOrderId + '" value="' + row.ToAddress + '" />';
            //拼多多暂不发货不显示收件人信息
            if (row.PddShipHold == "1" || row.ReceiverIsEmpty == 1) {
                html += '<span class="ToName" id="orderToName' + row.LogicOrderId + '"></span>';
                html += '<span class="ToPhone" id="orderToPhone' + row.LogicOrderId + '"></span>';
            } else {
                html += '<span class="ToName" id="orderToName' + row.LogicOrderId + '"' + displayRecipientHtml + '>' + (row.ToName || "") + '</span>，';
                html += '<span class="ToPhone" id="orderToPhone' + row.LogicOrderId + '"' + displayRecipientHtml + '>' + (row.ToPhone || "") + (isWxVideoExt ? ("-" + row.ExtField2) : "") + '</span>';
                
                if (isWxVideoExt) {
                    if (row.IsMainOrder) {
                        html += '<span class="heIconWrap"><i>?</i><div class="heIconWrap-content">平台为保护消费者隐私，部分订单会新增虚拟号<br />该订单展示的收件人电话号码并非真实用户手机号,<br />可正常打单发货，打印后面单上展示的虚拟号可正常联系到消费者，<br />如果您有联系消费者的需求，请您前往店铺后台查看用户隐私信息，<br />其他文档请参考官方回复<a class="dColor" target="_blank" href="https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/EQBKa4AIver3UPux">点击查看</a><div style="margin-top:15px">合单规则：<br/>因所有子单实际收件人号码为同一个，但虚拟手机号码不一致,<br />为满足合并要求，<br />系统默认读取子单创建时间最早的虚拟号为打印在面单上的虚拟号码<br />该合并规则不影响订单派送，请勿担心。</div></div></span>'
                    } else {
                        html += '<span class="heIconWrap"><i>?</i><div class="heIconWrap-content">平台为保护消费者隐私，部分订单会新增虚拟号<br />该订单展示的收件人电话号码并非真实用户手机号,<br />可正常打单发货，打印后面单上展示的虚拟号可正常联系到消费者，<br />如果您有联系消费者的需求，请您前往店铺后台查看用户隐私信息，<br />其他文档请参考官方回复<a class="dColor" target="_blank" href="https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/EQBKa4AIver3UPux">点击查看</a></div></span>'
                    }
                }
            }

            html += '</div>';
            html += '<div class="RecieverAddress">'
            if (row.PddShipHold == "1") {
                html += '<span style="color:#04385d" class="ToAddress" id="orderToFullAddress' + row.LogicOrderId + '">' + "该订单因发货地疫情影响，暂不支持发货，当疫情限制解除时，订单将重启承诺发货时间倒计时并支持发货" + '</span>';
            } else {

                if (row.ReceiverIsEmpty == 1) {
                    html += '<span style="color:#f7941f" class="ToAddress" id="orderToFullAddress' + row.LogicOrderId + '">' + "该订单正在店铺后台审核中需审核通过再展示订单地址" + '</span>';
                }
                else {
                    ///跨境 20240802地址栏 增加国家
                    if (commonModule.IsCrossBorderSite && row.PlatformType == "TikTok") {
                        row.ToFullAddress = row.ToCountry + " " + row.ToFullAddress;
                    }
                    /*if (orderDisplaySet.RecipientSetting == "DisplayRecipient") {*/
                    html += '<span class="ToAddress" id="orderToFullAddress' + row.LogicOrderId + '">' + (row.ToFullAddress || "") + '</span>';
                    /*}*/
                }
            }
            //html += '<span class="ToAddress" id="orderToFullAddress' + row.LogicOrderId + '">' + (row.ToFullAddress || "") + '</span>';
            //不允许厂家修改地址
            //拼多多跨境单不允许修改地址
            //拼多多集运单不允许修改地址
            if (orderDisplaySet.RecipientSetting == "DisplayRecipient") {
                row.IsSelf = GetIsSelf(row);
                if (row.IsSelf && row.PddConsolidate != "0"
                    && row.PddConsolidate != "1" && row.ReceiverIsEmpty != 1
                    && !row.IsPddCrossBorderOrder
                    && row.PlatformType != "TikTok"
                    && (row.PddConsolidate == undefined || row.PddConsolidate == "")) {
                        html += '<input type="hidden" id="edmitAddress' + row.LogicOrderId + '" class="edmitBuyerInfo" data-province="' + row.ToProvince + '" data-city="' + row.ToCity + '" data-county="' + row.ToCounty + '" />';
                        html += '<i class="iconfont icon-a-edit1x wu-color-k wu-operate wu-f16 UpdateOrderReceiver hide" onclick="orderPrintModule.amendWaitorReceiver(' + index + ',\'' + row.LogicOrderId + '\',\'' + row.OrderCode + '\')"></i>';
                }
            }
            //if (row.PddConsolidate != "0" && row.PddConsolidate != "1")
            //{
            //    html += '<i class="iconfont icon-bianji dColor hover" onclick=\'orderPrintModule.amendWaitorReceiver("' + index + '","' + row.LogicOrderId + '")\'></i>'
            //}   
            html += '</div>';

            if (row.PlatformType == 'TouTiaoSaleShop') {
                html += '<div class="wu-flex wu-yCenter wu-mT4">';
                if (row.OrderTags && row.OrderTags.length > 0) {
                    var obj = row.OrderTags.find(function (item) {
                        return item.Tag === 'next_day_delivery';
                    });
                    if (obj) {
                        html += '<span class="wu-tag wu-processing-dev">次日达</span>';
                    }
                }
                var warehouseData = commonModule.allWarehouseInfoList.find(function (item) {
                    return item.Id == row.WarehouseId;
                });
                if (warehouseData) {
                    html += '<span class="wu-tag wu-warning-dev wu-mL8">' + warehouseData.Name + '</span>';
                }
                html += '</div>';
            }

            //剩余时间预警
            row.LastShipTimeWarn = 0;
            if (commonModule.FxPageType() == 2 && !(LastShipTimeWarnHours == undefined || LastShipTimeWarnHours == "0") && row.LastShipTime != undefined && row.LastShipTime != null) {
                var seconds = parseInt(LastShipTimeWarnHours) * 60 * 60;
                var val = row.LastShipTime;
                var nowTime = new Date().getTime();
                var LastTime = new Date(val).getTime();
                var LastShipTime = LastTime - nowTime;
                if (parseInt(LastShipTime) / 1000 <= seconds) {
                    //  拼多多新增预警单悬浮提示
                    html += '<span class="wu-tag wu-warning-dev wu-mR4">预警单</span>';
                    if(row.PlatformType == "Pinduoduo") {
                        html += '<i class="warn-iconTar wu-tooltip">预警单';
                        html +=     '<div class="wu-pop wu-leftBottom" style="bottom:23px;">';
                        html +=         '<div class="wu-pop-title">新版《拼多多发货规则》，将于2025年5月15日正式生效。其中延迟发货、缺货、欺诈发货、虚假发货、虚假轨迹相关内容均有调整，';
                        html +=             '<a href="https://mms.pinduoduo.com/other/rule?listId=6&id=1751" target="_blank" style="cursor: pointer; color: #0888ff;">了解详情</a></div>'
                        html +=         '<span class="wu-pop-icon"></span>';
                        html +=     '</div>'
                        html += '</i>';
                    } else {
                        html += '<i class="warn-iconTar">预警单</i>';
                    }
                    
                    row.LastShipTimeWarn = 1;
                }
            }

            // 地址更改后异常单标记
            //if (row.ReceiverIsChange) {
            //    if (isWxVideoExt) {
            //        html += '<i class="tarTxt tarTxt03" style="margin-top:3px;" title="平台已更新虚拟号信息，为保证快递能正常联系到消费者，请您重新打印快递面单，重新打印后，该标签会取消展示">异常单</i>';
            //    }
            //    //else {
            //    //    html += '<i class="tarTxt tarTxt03" style="margin-top:3px;" title="该订单已变更买家地址，请打印最新快递单再发货">异常单</i>';
            //    //}
            //}

            html += '<div class="address_type">';
            if (commonModule.FxPageType() == 2) {
                html += addressTagFormatter(row, col);
            }

            if (row.IsPddCrossBorderOrder) {
                html += '<span class="tagStatus tagFS tooltip">跨境全托管发货单<span class="tooltip-content">该订单为平台跨境单，官方不允许修改订单收件地址，<br/>拆单，合单，无剩余发货时间。</span></span>';
                if (row.IsPddCourierDoorToDoorCollect) {
                    html += '<span class="tagStatus tagGreen tooltip">快递上门揽收<span class="tooltip-content">已自动下单顺丰快递，顺丰将自动上门揽件请及时备货（请您关注顺丰上门揽件时间）。</span></span>';
                }
            }

            if (row.IsFirstDelivery) {
                html += '<span class="tarTxt tarTxt05" style="padding-right:3px;" title="该订单为买家催发货订单，请优先打印发货">优先发货</span>';
            }
        }
        html += table.TagsContentFormatter(row, 'formatter');

        html += "</div>";
        html += '</div>';
        return html
    }


    function gxTarRender(title, name, url, PlatformOrderId) {

        var html = '';
        //html += '<span class="xgTar-icon tagStatus tagWarn '+name+'">';
        //html += '<span class="xgTar-title">' + title + '</span>';
        //var $class = "";
        //if (name == "xg") {
        //    html += '<div class="xgTar-main pddXg-jyWrap pdd-jy-dailog ' + $class + '" onclick="commonModule.stopMP(event)">';
        //    html +=
        //        '<div class="pddXg-jyWrap-title">集运订单请直接发货至集运仓库<i style="color:#888;font-size:14px;margin-left:5px;">(详细信息请<a href="https://mms.pinduoduo.com/daxue/detail?courseId=5460" target="_blank">查看课程</a>)</i></div>';
        //    html += '<ul class="pddXg-jyWrap-main">';
        //    html += '<li class="pddXg-jyWrap-main-title"><i class="iconfont icon-dagou"></i>商家直接发货至集运仓库</li>';
        //    html +=
        //        '<li class="pddXg-jyWrap-main-content">订单收件人信息已替换为集运仓地址，打单发货时请<s style="color:#FF511C">复制完整的收货信息(包括地址后识别码)</s></li>';
        //    html += '<li class="pddXg-jyWrap-main-title"><i class="iconfont icon-dagou"></i>集运仓库二次发货至消费者</li>';
        //    html += '<li class="pddXg-jyWrap-main-content">集运仓入库后将重新打包，并将包裹转运至消费者实际地址</li>';
        //    html +=
        //        '<li class="pddXg-jyWrap-main-title"><i class="iconfont icon-dagou"></i>消费者信息 / 中转状态 / 二段轨迹信息查看请移步至商家后台</li>';
        //    html +=
        //        '<li class="pddXg-jyWrap-main-content">若您需要查看消费者信息/中转状态 / 最新二段物流轨迹，请前往拼多多商家后台<a style="color:#3aadff;" href="https://mms.pinduoduo.com/orders/detail?type=4399&sn=' +
        //    PlatformOrderId + '" target="_blank">查看订单详情</a></li>';
        //    html += '</ul>';
        //    html += '</div>';

        //}
        ////哈萨克斯坦集运 提示信息
        //else if (name == "hskst") {
        //    html += '<div class="xgTar-main pddXg-jyWrap pdd-jy-dailog ' + $class + '" onclick="commonModule.stopMP(event)">';
        //    html += '<div class="pddXg-jyWrap-title">哈萨克斯坦集运订单请直接发货至集运仓库<i style="color:#888;font-size:14px;margin-left:5px;">(详细信息请<a href="https://mms.pinduoduo.com/daxue/detail?courseId=5460" target="_blank">查看课程</a>)</i></div>';
        //    html += '<ul class="pddXg-jyWrap-main">';
        //    html += '<li class="pddXg-jyWrap-main-title"><i class="iconfont icon-dagou"></i>商家直接发货至集运仓库</li>';
        //    html += '<li class="pddXg-jyWrap-main-content">订单收件人信息已替换为集运仓地址，打单发货时请<s style="color:#FF511C">复制完整的收货信息(包括地址后识别码)</s></li>';
        //    html += '<li class="pddXg-jyWrap-main-title"><i class="iconfont icon-dagou"></i>集运仓库二次发货至消费者</li>';
        //    html += '<li class="pddXg-jyWrap-main-content">集运仓入库后将重新打包，并将包裹转运至消费者实际地址</li>';
        //    html += '<li class="pddXg-jyWrap-main-title"><i class="iconfont icon-dagou"></i>消费者信息 / 中转状态 / 二段轨迹信息查看请移步至商家后台</li>';
        //    html += '<li class="pddXg-jyWrap-main-content">若您需要查看消费者信息/中转状态 / 最新二段物流轨迹，请前往拼多多商家后台<a style="color:#3aadff;" href="https://mms.pinduoduo.com/orders/detail?type=4399&sn=' + PlatformOrderId + '" target="_blank">查看订单详情</a></li>';
        //    html += '</ul>';
        //    html += '</div>';

        //}
        //else if (name == "xz") {
        //    html += '<div style="width:670px;" class="xgTar-main pddXj-jyWrap pdd-jy-dailog ' + $class + '" onclick="commonModule.stopMP(event)">';
        //    html += '<div class="pddXj-jyWrap-title">该西藏中转订单只需 <s> 发货至中转仓</s><i style="font-size:14px;color:#888;">（详细信息请<a href="https://mms.pinduoduo.com/daxue/detail?courseId=6164" target="_blank">查看课程</a>）</i></div>';
        //    html += '<ul class="pddXj-jyWrap-main">';
        //    html += '<li class="pddXj-jyWrap-main-title"><i class="iconfont icon-dagou"></i>中转流程</li>';
        //    html += '<li class="pddXj-jyWrap-main-content">';
        //    html += '<div class="pddXj-content-item"><i></i><span>消费者下单</span></div>';
        //    html += '<div class="pddXj-content-item"><i></i><span>商家发货</span></div>';
        //    html += '<div class="pddXj-content-item"><i></i><span>中转仓签收</span></div>';
        //    html += '<div class="pddXj-content-item"><i></i><span>中转仓发货</span></div>';
        //    html += '<div class="pddXj-content-item"><i></i><span>消费者收货</span></div>';
        //    html += '</li>';
        //    html += '<li class="pddXj-jyWrap-main-title"><i class="iconfont icon-dagou"></i>商家只需发货至中转仓(仅需承担该段物流费用)</li>';
        //    html +='<li class="pddXj-jyWrap-main-content02">订单收件人信息已替换为中转仓地址，打单发货时请<s style="color:#f7941f">复制完整的收货信息(包括地址后识别码）</s></li>';
        //    html += '<li class="pddXj-jyWrap-main-title"><i class="iconfont icon-dagou"></i>中转仓收货后二次发货至消费者</li>';
        //    html +='<li class="pddXj-jyWrap-main-content02"> 中转仓凑齐消费者订单后重新打包。贴新运单，第二段直接发货至新疆消费者实际地址<s style="color:#f7941f">(第二段物流运费由平台补贴承担)</s></li>';
        //    html +='<li class="pddXj-jyWrap-main-title"><i class="iconfont icon-dagou"></i>消费者信息/中转状态/二段轨迹信息查看请移步至商家后台</li>';
        //    html += '<li class="pddXj-jyWrap-main-content02">若您需要查看消费者信息/中转状态/最新二段物流轨迹，请前往拼多多商家后台<a class="dColor" href="https://mms.pinduoduo.com/orders/detail?type=4399&amp;sn=' + PlatformOrderId + '" target="_blank">查看订单详情;</a></li>';
        //    html += '<li class="pddXj-jyWrap-main-content03">电话咨询请发打(021)60683100</li>';
        //    html += '</ul>';
        //    html += '</div>';
        //}
        //else {
        //    html += '<div style="width:670px;" class="xgTar-main pddXj-jyWrap pdd-jy-dailog ' + $class + '" onclick="commonModule.stopMP(event)">';
        //    html += '<div class="pddXj-jyWrap-warn">';
        //    html += '<i>!</i>';
        //    html += '<span class="pddXj-jyWrap-warn-content">如果实际打印快递面单收件信息为<s>新疆消费者地址</s>，请放心发往新疆，<s>平台已补贴运费</s>。您与快递网点按照发西安价格结算，若网点按发新疆价格结算，请反馈至商家客服。 <a target="_blank" href="https://mms.pinduoduo.com/other/questionnaire?surveyId=95526459282">反馈入口</a>';
        //    html += '</span>';
        //    html += '</div>';
        //    html +='<div class="pddXj-jyWrap-title">新疆中转订单 <s> 商家发货至中转仓</s> 即可 <i style = "font-size:14px;color:#888;" >（详细信息请 <a href = "https://mms.pinduoduo.com/daxue/detail?courseId=5435" target = "_blank" > 查看课程</a>）</i></div>';
        //    html += '<ul class="pddXj-jyWrap-main">';
        //    html += '<li class="pddXj-jyWrap-main-title"><i class="iconfont icon-dagou"></i>中转流程</li>';
        //    html += '<li class="pddXj-jyWrap-main-content">';
        //    html += '<div class="pddXj-content-item"><i></i><span>消费者下单</span></div>';
        //    html += '<div class="pddXj-content-item"><i></i><span>商家发货</span></div>';
        //    html += '<div class="pddXj-content-item"><i></i><span>中转仓签收</span></div>';
        //    html += '<div class="pddXj-content-item"><i></i><span>中转仓发货</span></div>';
        //    html += '<div class="pddXj-content-item"><i></i><span>消费者收货</span></div>';
        //    html += '</li>';
        //    html += '<li class="pddXj-jyWrap-main-title"><i class="iconfont icon-dagou"></i>商家只需发货至中转仓(仅需承担该段物流费用)</li>';
        //    html += '<li class="pddXj-jyWrap-main-content02">订单收货地址信息已替换为中转仓地址,打单发货时请复制完整的收货信息<s style="color:#f7941f">(包括地址后识别码)</s></li>';
        //    html += '<li class="pddXj-jyWrap-main-title"><i class="iconfont icon-dagou"></i>中转仓收货后二次发货至消费者</li>';
        //    html += '<li class="pddXj-jyWrap-main-content02"> 中转仓收到商家包裹后将重新打包，使用新运单号，作为第二段物流直接发货至消费者实际地址<s style = "color:#f7941f" > (第二段物流运费由平台补贴承担)</s ></li>';
        //    html += '<li class="pddXj-jyWrap-main-title"><i class="iconfont icon-dagou"></i>消费者咨询</li>';
        //    html += '<li class="pddXj-jyWrap-main-content02">消费者咨询时请优先确认包裹的物流状态，并告知消费者准确信息</li>';
        //    html += '<li class="pddXj-jyWrap-main-content03">如遇第二段物流长时间未更新或其他问题，您可拨打(021) 60683100 进线咨询</li>';
        //    html += '</ul>';
        //    html += '</div>';
        //}

        var typeTxet = "集运";
        if (name == "xj" || name == "xz")
            typeTxet = "中转";

        //新标签
        html += '<span class="tarTxt tarTxt09 hover popoverCommon">';
        html += title;
        html += '<span class="popoverCommon-warn">收件人地址及电话均为' + typeTxet + '仓地址及联系电话，若您需要查看消费者信息，请您前往拼多多商家后台进行查看。' + typeTxet + '流程请<a class="dColor" target="_blank" href="' + url + '">查看教程</a></span>';
        html += '</span>';
        return html;
    }

    var ProductShowHeaderFormattter = function () {
        var html = "";
        html += '<span class="productShowHeaer">';
        html += '<s>商品信息</s>';
        html += '<i class="iconfont icon-a-setting1x1 wu-weight500 wu-color-m wu-operate wu-mL4" id="ProductShowSetting" onclick="orderPrintModule.setProductShowCount.bind(this)()"></i>'
        html += '</span>';
        return html;
    }
    // 订单实付总额
    var TotalSumHeaderFormatter = function () {
        var html = "";
        html += '<div class="wu-flex wu-yCenter">';
        html += '<span class="wu-mR2">订单实付总额</span>';
        html += '<div class="wu-flex wu-yCenter wu-tooltip" style="position: relative;">';
        html += '<i class="iconfont icon-a-help-circle1x wu-c06"></i>';
        html += '<div class="wu-pop wu-midBottom" style="left: 50%;bottom:23px;transform: translate(-50%);width: 200px;">';
        html += '<div class="wu-pop-title">展开详情内容为单价和数量，订单实付总额可能因平台优惠与单价*数量之和存在差异。</div>';
        html += '<span class="wu-pop-icon" style="left: 48%;"></span>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        return html;
    }
    var ProductShowContentFormatter = function (row, col, index, classNamePrefix) {
        var orderDisplaySet = OrderDisplaySet;
        var html = "";
        html += '<div class="wu-flex wu-column wu-flex-1">';
        var len = row.SubOrders.length < setProductCount ? row.SubOrders.length : setProductCount;
        for (var i = 0; i < len; i++) {
            var productImage = row.SubOrders[i].ProductImgUrl ? row.SubOrders[i].ProductImgUrl : "/Content/images/nopic.gif";
            var color = row.SubOrders[i].Color ? row.SubOrders[i].Color : "";
            var size = row.SubOrders[i].Size ? row.SubOrders[i].Size : "";
            var sizeAndSize = color;
            if (color && size)
                sizeAndSize = color + "，" + size;
            else if (!color && size)
                sizeAndSize = size;
            var skuShortTitle = row.SubOrders[i].SkuShortTitle ? row.SubOrders[i].SkuShortTitle : "";
            //var sizeAndSize = row.SubOrders[i].Color ? row.SubOrders[i].Color : "" + "，" + (row.SubOrders[i].Size ? row.SubOrders[i].Size : "");
            var productSubject = row.SubOrders[i].ProductSubject ? row.SubOrders[i].ProductSubject : "";
            var shortTitle = row.SubOrders[i].ShortTitle ? row.SubOrders[i].ShortTitle : "";
            var count = row.SubOrders[i].Count;
            html += '<div class="new-productWrap wu-mB8" style="flex:1;">';
            html += '<div class="new-productShow" style="width:100%">';
            html += '<img class="productShow-img wu-6radius" style="width:55px; height: 55px;" src="' + productImage + '" onmouseover=\'commonModule.showLargeProPic02.bind(this)("' + row.SubOrders[i].OrderItemId + '")\' onmouseout="commonModule.hideLargeProPic(this)">';
            html += '<div class="productShowBigPic new-productShowBigPic" style="display: none;"></div>';
            html += '<ul class="product-title">';
            if (setProductTitleShowValue == 0) {
                if (orderDisplaySet.ProductSetting == "DisplayProductShortTitle" && skuShortTitle.length != 0) {
                    html += '<li>' + skuShortTitle + '</li>';
                }
                else {
                    html += '<li>' + common.htmlEncode(sizeAndSize) + '</li>';
                }
            } else {
                if (orderDisplaySet.ProductSetting == "DisplayProductShortTitle" && shortTitle.length != 0) {
                    html += '<li>' + shortTitle + '</li>';
                }
                else {
                    if (productSubject) {
                        html += '<li>' + productSubject + '</li>';
                    }
                }
            }
            html += '<li class="wu-mT4"><span class="wu-tag wu-error-dev wu-dot">×' + count + '</span></li>';
            var orderItmeTags = row.SubOrders[i].OrderItemTags;
            if (orderItmeTags != undefined && orderItmeTags.length != 0) {
                html += '<li>' + row.SubOrders[i].OrderItemTagsHtml + '</li>';
            }
            html += '</ul>';
            html += '</div>';
            html += '</div>';
        }
        if (setProductCount < row.SubOrders.length) {
            html += '<div style="text-align:right;">';
            html += '<span class="showMoreProductBtn wu-btn wu-btn-small wu-icon-right" onclick="orderTableBuilder.CheckShowOrder(this)">';
            html += '<s id="more-product-btn-text">更多</s>';
            html += '<i class="iconfont wu-btn-icon icon-a-chevron-down1x wu-btn-icon-b"></i>';
            html += '</span>';
            //html += '<s class="moreSpan">更多</s>';
            //html += '<i class="icon-more moreIcon"></i>';
            //html += '<s class="upSpan">收起</s>';
            //html += '<i class="upIcon"></i>';
            html += '</div>';
        }
        html += '</div>';
        return html;
    }

    var OrderStatusContentFormatter = function (row, col, index, classNamePrefix) {
        //打印状态
        var html = '';
        var status = "";

        var erpStatus = "";
        var erpPrintStatus = "";
        if (row.ErpState == "sended")
            erpStatus = "已发货";
        else if (row.ErpState == "inrefund")
            erpStatus = "退款中";
        else if (row.ErpState == "success")
            erpStatus = "已完成";
        else if (row.ErpState == "close" || row.ErpState == "cancel")
            erpStatus = "已取消";
        else
            erpStatus = "未发货";

        if (row.PrintState == "1")
            erpPrintStatus = "已打印";
        else if (row.PrintState == "2")
            erpPrintStatus = "部分打印";
        else
            erpPrintStatus = "未打印";

        var color = erpStatus == "未发货" ? "#0888FF" : erpStatus == "已发货" ? "#73ac1f" :
            erpStatus == "退款中" ? "#f18c03" :
                erpStatus == "已完成" ? "#73ac1f" :
                    erpStatus == "已取消" ? "#ff511c" : "#0888FF";


        html += '<i class="dot" style="background-color: ' + color + ';flex-shrink: 0;"></i>';
        status = erpPrintStatus + erpStatus;

        //if (erpStatus == "未发货") {
        //    html += '<i class="dot" style="background-color: #faad14;"></i>';
        //    status = erpPrintStatus + erpStatus;
        //}
        //else if (erpStatus == "已发货" && erpPrintStatus=="" ) {
        //    html += '<i class="dot" style="background-color: #faad14;"></i>';
        //    status = "已打印未发货";
        //}
        //else if (row.ErpState == "sended" && row.PrintState == "1") {
        //    html += '<i class="dot" style="background-color: #3aadff;"></i>';
        //    status = "已打印已发货";
        //}
        //else if (row.ErpState == "sended" && row.PrintState == "0") {
        //    html += '<i class="dot" style="background-color: #3aadff;"></i>';
        //    status = "已打印已发货";
        //}
        //else if (row.ErpState == "inrefund") {
        //    html += '<i class="dot" style="background-color: #faad14;"></i>';
        //    status = "退款中";
        //}
        //else if (row.ErpState == "success") {
        //    html += '<i class="dot" style="background-color: #52c41a;"></i>';
        //    status = "已完成";
        //}
        //else if (row.ErpState == "close") {
        //    html += '<i class="dot" style="background-color: #ff511c;"></i>';
        //    status = "已取消";
        //}
        //else
        //    status = "";
        html += '<span>' + status + '</span>';
        return html;
    }
    var ExpressCodeContentFormatter = function (row) {
        var shopId = row.ShopId;
        var codes = row.WaybillCodes || [];
        /*
        codes.push({
            "TemplateId": 111283,
            "TemplateType": 1,
            "ExpressCpCode": "ZJS",
            "ExpressName": "宅急送",
            "WaybillCode": "ZJS777777777",
            "ChildWaybillCode": null,
            "ChildWaybillCodes":null,
            "CompanyCode": "ZJS",
            "WaybillCodeId": 11112,
            "CreateDate": "2024-03-25 19:58:58",
            "IsSended": false,
            "IsPreviewed": true,
            "TotalWeight": 0.00,
            "SendType": 0,
            "Status": 1,
            "UniqueKey": "1f67d6e703116164"
        });
        */
        if (common.IsCrossBorderSite) {
            return RenderTKExpressNameHtml(codes, shopId, row);
        } else {

            return RenderExpressNameHtml(codes, shopId);
        }
    }

    var ExpressCodeContentAllOrderFormatter = function (row) {
        for (var i = 0; i < row.SecondSubOrders.length; i++) {
            var second = row.SecondSubOrders[i];
            //second.WaybillCodeHtml = row.ErpState == "waitsellersend" ? (second.WaybillCodes && second.WaybillCodes.length > 0 ? "已打印" : "未打印") : RenderExpressNameHtml(second.WaybillCodes);
            if (row.ErpState == "waitsellersend") {
                var printState = 0;
                if (second.ThressSubOrders != undefined && second.ThressSubOrders.length > 0) {
                    for (var x = 0; x < second.ThressSubOrders.length; x++) {
                        if (second.ThressSubOrders[x].PrintState == 1) {
                            printState = 1;
                        }
                    }

                    if (printState == 1 && common.IsCrossBorderSite) {
                        second.WaybillCodeHtml = RenderTKExpressNameHtml(second.WaybillCodes, row.ShopId, row);
                    } else {
                        second.WaybillCodeHtml = printState == 1 ? "已打印" : "未打印";
                    }
                }
            }
            else {
                if (common.IsCrossBorderSite) {
                    second.WaybillCodeHtml = RenderTKExpressNameHtml(second.WaybillCodes, row.ShopId, row);
                } else {
                    second.WaybillCodeHtml = RenderExpressNameHtml(second.WaybillCodes, row.ShopId);
                }
            }
        }
        
    }

    var RenderExpressNameHtml = function (codes, shopId) {
        if (!codes || codes.length == 0)
            return "";

        var html = "";
        var hasNewIcon = codes.length > 1 ? true : false;
        html += '<div style="display:flex;flex-direction: column;">';
        for (var i = 0; i < codes.length; i++) {
            var expressName = codes[i].ExpressName ? codes[i].ExpressName : "";
            var waybillCode = codes[i].WaybillCode ? codes[i].WaybillCode : "";
            var expressCpCode = codes[i].ExpressCpCode ? codes[i].ExpressCpCode : "";//TK对应Pid
            var logisticType = codes[i].LogisticType ? codes[i].LogisticType : "";//区分国内国外物流
            var sended = codes[i].IsSended || false;
            html += '<div class="expressDiv" style="flex-direction: row;">';
            if (hasNewIcon) {
                if (i == 0) {
                    html += '<span class="epressIcon-new" title="最新物流单号"></span>';
                }
            }
            var childWaybillCodes = codes[i].ChildWaybillCodes;
            if (codes[i].IsManual) {
                html += '<span class="tarTxt tarTxt01 tooltip" style="height: 20px; width: 20px; margin-right: 4px;">手<span class="tooltip-content" style="left: -20px;">手工发货：发货人使用手工发货功能，手工填写快递公司和快递单号进行发货</span></span>';
            }
            html += '<div style="display: flex;flex-direction: column;">';
            html += '<span class="expressDiv-title" style="color:#04385d;">' + expressName + '</span>';
            var isChild = false;
            if (childWaybillCodes && childWaybillCodes.length > 0) {
                html += '<div class="waybillCodeWrap">'
                html += '<span class="expressDic-code" data-send="' + sended + '" data-shopId="' + shopId + '" data-logisticType="' + logisticType + '"  data-packageId="' + expressCpCode + '" onclick="orderTableBuilder.ViewLogisticTraces(this,\'' + commonModule.RenameExpressName(expressName) + '\')" title="查看物流轨迹">' + waybillCode + '<br>/' + waybillCode + '</span>';
                html += '<input type="hidden" id="copywaybillcode' + codes[i].CompanyCode + codes[i].WaybillCodeId + '" value="' + waybillCode + '" />';
                html += '<i class="iconfont icon-a-copy1x wu-color-k wu-operate wu-mL4" onclick=\'commonModule.CopyText("#copywaybillcode' + codes[i].CompanyCode + codes[i].WaybillCodeId + '")\'></i>';
                html += '</div>';
                childWaybillCodes.forEach(function (o) {
                    if (o.ChildWaybillCode !== waybillCode) {
                        isChild = true;
                        html += '<div class="waybillCodeWrap">'
                        html += '<span class="expressDic-code" data-send="' + sended + '" data-shopId="' + shopId + '" data-logisticType="' + logisticType + '"  data-packageId="' + expressCpCode + '" onclick="orderTableBuilder.ViewLogisticTraces(this,\'' + commonModule.RenameExpressName(expressName) + '\')" title="查看物流轨迹">' + waybillCode + '<br>/' + o.ChildWaybillCode + '</span>';
                        html += '<input type="hidden" id="copychildwaybillcode' + o.Id + '" value="' + o.ChildWaybillCode + '" />';
                        html += '<i class="iconfont icon-a-copy1x wu-color-k wu-operate wu-mL4" onclick=\'commonModule.CopyText("#copychildwaybillcode' + o.Id + '")\'></i>';
                        html += '</div>';
                    }
                });
            }
            if (!isChild) {
                html += '<div class="waybillCodeWrap">'
                html += '<span class="expressDic-code"  data-send="' + sended + '" data-shopId="' + shopId + '" data-logisticType="' + logisticType + '"  data-packageId="' + expressCpCode + '"onclick="orderTableBuilder.ViewLogisticTraces(this,\'' + commonModule.RenameExpressName(expressName) + '\')" title="查看物流轨迹">' + waybillCode + '</span>';
                html += '<input type="hidden" id="copywaybillcode' + codes[i].CompanyCode + codes[i].WaybillCodeId + '" value="' + waybillCode + '" />';
                html += '<i class="iconfont icon-a-copy1x wu-color-k wu-operate wu-mL4" onclick=\'commonModule.CopyText("#copywaybillcode' + codes[i].CompanyCode + codes[i].WaybillCodeId + '")\'></i>';
                html += '</div>';
            }

            html += '</div>';
            html += '</div>';
        }
        html += '</div>';
        return html;
    }

    var RenderTKExpressNameHtml = function (codes, shopId, row) {
        // console.log('RenderTKExpressNameHtml',codes, shopId, row);
        
        // if (!codes || codes.length == 0)
        //     return "";

        var html = "";
        html += '<div style="display:flex;flex-direction: column;">';

        //过滤出来 国内段物流和 国外段物流
        var internalLogistic = []; //国内段物流
        var overseasLogistic = []; //国外段物流
        for (var i = 0; i < codes.length; i++) {
            var code = codes[i];
            if (code.LogisticType == "1") {
                overseasLogistic.push(code);
            }
            else {
                internalLogistic.push(code);
            }
        }
        // console.log("自行寄送",internalLogistic,"平台物流",overseasLogistic);
        
        //国内段物流为空，且订单已打印已交运，则说明交运时选择的是 上门揽收
        if (internalLogistic.length == 0 && row.LogisticStatus == "2") {
            html += '<div style="color: #3D3D3D;margin: 4px;display: flex;align-items: center;margin-left: -10px;"><i class="dot" style="background-color: #3aadff;"></i><span>上门揽收</span></div>';
        } else if (internalLogistic.length > 0 && row.ShippingType != 'SELLER') {
            var tempExpressCpCode = internalLogistic[0].ExpressCpCode;
            var tempExpressName = internalLogistic[0].ExpressName;
            var tempWaybillCode = internalLogistic[0].WaybillCode;
            //遍历国内段物流
            html += '<div style="color: #3D3D3D; margin: 4px; display: flex; align-items: center; margin-left: -10px;">' +
                    '<i class="dot" style="background-color: #3aadff;"></i>' +
                    '<span>自行寄送</span>' +
                    '<span id="selfDeliveryPen_' + row.Index + '"><i class="iconfont icon-a-edit-11x" style="margin-left: 4px;cursor: pointer;color: #0888FF;" onclick="tkOrderPrintModule.editLogistics(\'' + row.Index + '\', \'自行寄送\', \'' + tempExpressCpCode + '\', \'' + tempExpressName + '\', \'' + tempWaybillCode + '\')"></i></span>' +
                    '</div>';
            for (var i = 0; i < internalLogistic.length; i++) {
                var expressName = internalLogistic[i].ExpressName ? internalLogistic[i].ExpressName : "";
                var waybillCode = internalLogistic[i].WaybillCode ? internalLogistic[i].WaybillCode : "";
                var expressCpCode = internalLogistic[i].ExpressCpCode ? internalLogistic[i].ExpressCpCode : "";//TK对应Pid
                var logisticType = internalLogistic[i].LogisticType ? internalLogistic[i].LogisticType : "";//区分国内国外
                var sended = internalLogistic[i].IsSended || false;
                html += '<div id="selfDeliveryDiv_' + row.Index + '" class="expressDiv" style="margin: 5px;">';

                html += '<div style="display: flex;flex-direction: column;">';
                html += '<span class="expressDiv-title" style="color:#04385d;">' + expressName + '</span>';
                var isChild = false;

                if (!isChild) {
                    html += '<div class="waybillCodeWrap">'
                    html += '<span class="expressDic-code"  data-send="' + sended + '" onclick="orderTableBuilder.ViewLogisticTraces(this,\'' + commonModule.RenameExpressName(expressName) + '\')" title="查看物流轨迹">' + waybillCode + '</span>';
                    html += '<input type="hidden" id="copywaybillcode' + internalLogistic[i].CompanyCode + internalLogistic[i].WaybillCodeId + '" value="' + waybillCode + '" />';
                    html += '<i class="iconfont icon-a-copy1x wu-color-k wu-operate wu-mL4" onclick=\'commonModule.CopyText("#copywaybillcode' + internalLogistic[i].CompanyCode + internalLogistic[i].WaybillCodeId + '")\'></i>';
                    html += '</div>'
                }

                html += '</div>';
                html += '</div>';
            }
        }
        // TIKTOK：平台订单 SELLER：第三方物流订单
        if (row.ShippingType == 'SELLER') {

            //第三方物流订单
            var tempExpressCpCode = overseasLogistic.length > 0 ? overseasLogistic[0].ExpressCpCode : '';
            var tempExpressName = overseasLogistic.length > 0 ? overseasLogistic[0].ExpressName : '';
            var tempWaybillCode = overseasLogistic.length > 0 ? overseasLogistic[0].WaybillCode : '';
            html += '<div style="color: #3D3D3D; margin: 4px; display: flex; align-items: center; margin-left: -10px;">';
            html += '<i class="dot" style="background-color: #3aadff;"></i>';
            html +=  '<span>第三方物流</span>';
                    if (row.ErpState == 'waitsellersend') {
                        // html += '<span id="newExpressPen_' + row.Index + '"><i class="iconfont icon-a-edit-11x" style="margin-left: 4px;cursor: pointer;color: #0888FF;" onclick="tkOrderPrintModule.editLogistics(\'' + row.Index + '\', \'第三方物流\')"></i></span>';
                        html += '<span id="selfDeliveryPen_' + row.Index + '"><i class="iconfont icon-a-edit-11x" style="margin-left: 4px;cursor: pointer;color: #0888FF;" onclick="tkOrderPrintModule.editLogistics(\'' + row.Index + '\', \'第三方物流\', \'' + tempExpressCpCode + '\', \'' + tempExpressName + '\', \'' + tempWaybillCode + '\')"></i></span>';
                    }
            html += '</div>';
    
            if (overseasLogistic.length > 0) {

                for (var i = 0; i < overseasLogistic.length; i++) {
                    var expressName = overseasLogistic[i].ExpressName ? overseasLogistic[i].ExpressName : "";
                    var waybillCode = overseasLogistic[i].WaybillCode ? overseasLogistic[i].WaybillCode : "";
                    var expressCpCode = overseasLogistic[i].ExpressCpCode ? overseasLogistic[i].ExpressCpCode : "";//TK对应Pid
                    var logisticType = overseasLogistic[i].LogisticType ? overseasLogistic[i].LogisticType : "";//区分国内国外物流
                    var sended = overseasLogistic[i].IsSended || false;
                    html += '<div id="newExpressDiv_' + row.Index + '" class="expressDiv" style="margin: 5px;">';
                    html += '<div style="display: flex;flex-direction: column;">';
                    html += '<span class="expressDiv-title" style="color:#04385d;">' + expressName + '</span>';
                    var isChild = false;
        
                    if (!isChild) {
                        html += '<div class="waybillCodeWrap">'
                        html += '<span class="expressDic-code"  data-send="' + sended + '" data-shopId="' + shopId + '"data-logisticType="' + logisticType + '"   data-packageId="' + expressCpCode + '"onclick="orderTableBuilder.ViewLogisticTraces(this,\'' + commonModule.RenameExpressName(expressName) + '\')" title="点击查看物流轨迹">' + waybillCode + '</span>';
                        html += '<input type="hidden" id="copywaybillcode' + overseasLogistic[i].CompanyCode + overseasLogistic[i].WaybillCodeId + '" value="' + waybillCode + '" />';
                        html += '<i class="iconfont icon-fuzhi1" style="margin-left:3px" onclick=\'commonModule.CopyText("#copywaybillcode' + overseasLogistic[i].CompanyCode + overseasLogistic[i].WaybillCodeId + '")\'></i>';
                        html += '</div>'
                    }
                    html += '</div>';
                    html += '</div>';
                }
            } else {
                    // html += '<div id="newExpressDiv_' + row.Index + '" class="expressDiv" style="display: none;margin: 5px;"></div>';
            }
        } else {

            //平台物流
            // if (overseasLogistic.length > 0) {
            html += '<div style="color: #3D3D3D; margin: 4px; display: flex; align-items: center; margin-left: -10px;">' +
                '<i class="dot" style="background-color: #3aadff;"></i>' +
                '<span>平台物流</span> ';

            if (row.DeliveryOptionName == 'Global standard shipping') {
                html += '<span class="wu-tag wu-warning-dev wu-dot" style="margin-left:5px;padding-left: 3px;" title="国内仓库（货在国内，发海外）">国内仓</span>';
            }
            else if (row.DeliveryOptionName == 'Standard shipping') {
                html += '<span class="wu-tag wu-warning-dev wu-dot" style="margin-left:5px;padding-left: 3px;" title="海外仓库（货在海外，发本土）">海外仓</span>';
            }

            html += '</div>';
    
            // }
    
            for (var i = 0; i < overseasLogistic.length; i++) {
                var expressName = overseasLogistic[i].ExpressName ? overseasLogistic[i].ExpressName : "";
                var waybillCode = overseasLogistic[i].WaybillCode ? overseasLogistic[i].WaybillCode : "";
                var expressCpCode = overseasLogistic[i].ExpressCpCode ? overseasLogistic[i].ExpressCpCode : "";//TK对应Pid
                var logisticType = overseasLogistic[i].LogisticType ? overseasLogistic[i].LogisticType : "";//区分国内国外物流
                var sended = overseasLogistic[i].IsSended || false;

                var showExpressName = expressName;

                if (row.DeliveryOptionName == 'Global standard shipping') {
                    showExpressName = "全球标准运输服务-" + expressName;
                }
                else if (row.DeliveryOptionName == 'Standard shipping') {
                    showExpressName = "本地标准运输服务-" + expressName;
                }

                html += '<div class="expressDiv" style="margin: 5px;">';
                html += '<div style="display: flex;flex-direction: column;">';
                html += '<span class="expressDiv-title" style="color:#04385d;">' + showExpressName + '</span>';
                var isChild = false;
    
                if (!isChild) {
                    html += '<div class="waybillCodeWrap">'
                    html += '<span class="expressDic-code"  data-send="' + sended + '" data-shopId="' + shopId + '"data-logisticType="' + logisticType + '"   data-packageId="' + expressCpCode + '"onclick="orderTableBuilder.ViewLogisticTraces(this,\'' + commonModule.RenameExpressName(expressName) + '\')" title="点击查看物流轨迹">' + waybillCode + '</span>';
                    html += '<input type="hidden" id="copywaybillcode' + overseasLogistic[i].CompanyCode + overseasLogistic[i].WaybillCodeId + '" value="' + waybillCode + '" />';
                    html += '<i class="iconfont icon-a-copy1x wu-color-k wu-operate wu-mL4" onclick=\'commonModule.CopyText("#copywaybillcode' + overseasLogistic[i].CompanyCode + overseasLogistic[i].WaybillCodeId + '")\'></i>';
                    html += '</div>'
                }
                html += '</div>';
                html += '</div>';
            }
        }

        html += '</div>';

        return html;
    }

    //var renameExpressName = function (expressName) {

    //    var newName = expressName;
    //    if (expressName.indexOf("邮政") != -1)
    //        newName = "邮政";
    //    //else if (expressName.indexOf("顺丰") != -1)
    //    //    newName = "";
    //    else if (expressName.indexOf("京东快递") != -1)
    //        newName = "京东快递单号查询";
    //    else if (expressName.indexOf("极兔") != -1 && expressName.indexOf("百世") != -1 )
    //        newName = "极兔快递";
    //    return newName;
    //}

    table.ViewLogisticTraces = function (that, expressName) {
        event.stopPropagation();
        var logisticType = $(that).attr("data-logisticType") || "";//国际：1国内：2
        ///跨境站点
        if (commonModule.IsCrossBorderSite && (logisticType == "1" || logisticType == "2")) {
            var sid = $(that).attr("data-ShopId") || "";
            var packageId = $(that).attr("data-packageId") || "";
            commonModule.Ajax({
                url: "/NewOrder/GetPackageShipping",
                data: { sid: sid, packageId: packageId },
                area: ['300px', '200px'], //宽高
                type: "POST",
                loading: true,
                success: function (rsp) {
                    if (!rsp.Success) {
                        layer.msg(rsp.Message, { icon: 2 });
                        return;
                    }
                    var item = rsp.Data || []
                    var date = { items: item, peopleLength: item.length };
                    var orderDetailTmpl = $.templates("#flowPath");
                    var html = orderDetailTmpl.render(date);
                    layer.open({
                        type: 1,
                        title: false, //不显示标题
                        content: html,
                        area: ['426px'],
                        closeBtn: false,
                        btn: ['知道了']
                    });
                }
            });

            return;
        }
        var val = $(that).text().trim();
        var index = val.indexOf('/');
        if (index != -1)
            val = val.substring(index + 1);
        expressName = commonModule.RenameExpressName(expressName);

        //var url = 'https://www.baidu.com/s?wd=' + expressName + " " + val;
        //if (expressName.indexOf("顺丰") != -1 || expressName.indexOf("丰网") != -1)
        //    url = 'https://www.sf-express.com/cn/sc/dynamic_function/waybill/#search/bill-number/' + val;
        //window.open(url);

        commonModule.ViewLogisticTraces(val, expressName);

        return;
        var viewLogisticTracesWin = layer.open({
            type: 2,
            title: "物流轨迹", //不显示标题
            shadeClose: true,
            //maxmin: true, //开启最大化最小化按钮
            content: url, //'https://www.baidu.com/s?wd=' + expressName + " " + val,
            area: ['1025px', '600px'], //宽高
            btn: ['关闭'],
            //yes: function () {
            //    _clearCtrol();
            //    layer.closeAll();
            //    common.CopyText('#ul_print_result');
            //},
            cancel: function () {
                layer.closeAll();
            }
        });
    }


    var DetailContentFormatter = function (row, col, index) {
        return '<i class="iconfont icon-a-chevron-down1x" onclick="orderTableBuilder.CheckShowOrder(this)"></i>';
    }

    var RecieverAddressContentFormatter = function (row, col, index) {
        var style = getColStyle(col);
        var className = col.className ? col.className : "";
        className += " " + col.field;
        className += " order-column-" + col.field;
        var html = '';
        row.LastShipTimeWarn = 0;
        html += '<div class="layui-col-md3 ' + className + '" style="' + style + ' display:block;">';
        html += '<span class="ToAddress" style="min-width:150px;" onclick="commonModule.StopPropagation(event)">';
        if (row.PddShipHold == "1") {
            html += '<span style="color:#04385d" class="ToFullAddress" id="orderToFullAddress' + row.LogicOrderId + '">' + "该订单因发货地疫情影响，暂不支持发货，当疫情限制解除时，订单将重启承诺发货时间倒计时并支持发货" + '</span>';
        } else {
            html += '<span class="ToFullAddress" id="orderToFullAddress' + row.LogicOrderId + '">' + row.ToFullAddress + '</span>';
        }
        if (!row.IsPddCrossBorderOrder) {
            html += '<i class="iconfont icon-bianji dColor hover" onclick="orderPrintModule.amendReceiverV2(' + index + ',' + row.LogicOrderId + ',' + row.OrderCode + ')"></i>';
        }
        html += '</span>';
        if (commonModule.FxPageType() == 2 && !(LastShipTimeWarnHours == undefined || LastShipTimeWarnHours == "0") && row.LastShipTime != undefined && row.LastShipTime != null) {
            var seconds = parseInt(LastShipTimeWarnHours) * 60 * 60;
            var val = row.LastShipTime;
            var nowTime = new Date().getTime();
            var LastTime = new Date(val).getTime();
            var LastShipTime = LastTime - nowTime;
            if (parseInt(LastShipTime) / 1000 <= seconds) {
                html += '<i class="warn-iconTar">预警单</i>';
                row.LastShipTimeWarn = 1;
            }
        }
        html += '<div class="address_type">';
        if (commonModule.FxPageType() == 2) {
            html += addressTagFormatter(row, col);
        }

        html += "</div>";
        html += "</div>";

        return html;
    }
    var TotalSumContentFormatter = function (row, col, index) {
        //是否显示销售价格
        _renderPrice(row);
        //for (var i = 0; i < AgentIsSalePrices.length; i++) {
        //    var isSale = AgentIsSalePrices[i];
        //    if (row.UpFxUserId == isSale.FxUserId) {
        //        if (isSale.Value == "false") {
        //            row.TotalAmount = "**";
        //            for (var s = 0; s < row.SubOrders.length; s++) {
        //                row.SubOrders[s].Price = "**";
        //                row.SubOrders[s].TotalAmount = "**";
        //            }
        //        }
        //    }
        //}
        var productItemCount = row.ProductItemCount || 0;
        var productItemCountHtml = productItemCount > 1 ? '<span style="color:#ea572e;font-weight:700;white-space:nowrap;">' + productItemCount + '件</span>' : '<span style="white-space:nowrap;">' + productItemCount + '件</span>';
        if (row.TotalAmount == "**") {
            return productItemCountHtml;
        }
        if (commonModule.IsCrossBorderSite) {
            var curr = common.getCurrencyToCharacter(row.Currency);
            return curr + (row.TotalAmount || 0) + "/" + productItemCountHtml;
        }
        else {
            var tempTotalAmount = row.TotalAmount ? (row.TotalAmount || 0) + "元/" + productItemCountHtml : productItemCountHtml;
            return tempTotalAmount;
        }

    }
    var OrderRemarkContentFormatter = function (row, col, index) {
        $.views.converters({
            upperImgSrc: function (val) {
                return newTransformImgSrc(val);
            }
        });
        // 是否显示打单留言
        var isShowPrintRemark = row.RemarkSetting && row.RemarkSetting.ShowPrintRemark && row.PrintRemark ? "display: flex;" : "display: none;";
        var lineClampStyle = row.RemarkSetting && row.RemarkSetting.RemarkShowLimitType ? "-webkit-line-clamp:3;" : "-webkit-line-clamp:100;";
        var html = '<div class="typeOne" style="width:100%; display: flow;align-items:flex-start;">';
        // 打单留言
        html += '<div style="' + isShowPrintRemark + '" class="wu-remark-wrap wu-remark-wrap-0fb5c1">'

       

      
        html += '<span class="printRemark-label-style ' + (row.PrintRemark ? 'wu-tag wu-tag-0fb5c1 wu-dot' : 'wu-tag wu-default-dev wu-dot')+'" style="height: 16px;" title="打单备注">打</span>'
       
        html += '<div class="text-container" style="' + lineClampStyle + '" onclick=\'orderPrintModule.batchSellerRemark("' + row.Index + '","' + row.LogicOrderId + '","1","WaitOrder")\'>'
        
        html += '<span class="remark-icon-box">'
        if (row.PrintRemark) {
            html += '<i onclick=\'commonModule.CopyText("#printRemark_text_'+ row.Id +'")\' class="iconfont icon-a-copy1x"></i>'
        }
        html += '<i onclick=\'orderPrintModule.batchSellerRemark("' + row.Index + '","' + row.LogicOrderId + '","1","WaitOrder")\' class="iconfont icon-a-edit1x af-sale-text-edit-icon"></i>'
        html += '</span>'
        
        // 缺少旗帜判断
        // html += '<span class="iconfont icon-flag icon-flag-5"></span>'
        var printRemark = row.PrintRemark;
        html += '<span class="">'
        html += '<span class="PrintRemark" title="' + printRemark + '" id="printRemark_text_'+ row.Id +'">' + printRemark + '</span>'
        if (row.PrintRemark) {
            // html += '<span class="tableconten-left-tar-span PrintRemark-hint">' + printRemark + '</span>'
        } else {
            html += '<span class="textLine"></span>'
            html += '<span class="notText">编辑打单备注</span>'
        }
        html += '</span>'
        // html += '<i onclick=\'orderPrintModule.batchSellerRemark("' + row.Index + '","' + row.LogicOrderId + '","1","WaitOrder")\' class="iconfont icon-a-edit1x af-sale-text-edit-icon"></i>'
        html += '</div>'
        html += '</div>'

        // 合单留言
        if (row.MergeredType == 3 || row.MergeredType == 4) {
            var systemRemark = row.SystemRemark;
            var style = systemRemark ? "display: flex;" : "display: none;";
            html += '<div style="' + style + '" class="wu-remark-wrap wu-remark-wrap-f18c03">'

           

            
            html += '<span class="systemRemark-label-style ' + (row.SystemRemark ? 'wu-tag wu-tag-f18c03 wu-dot' : 'wu-tag wu-default-dev wu-dot') +'" style="height: 16px;" title="合单备注">合</span>'
            html += '<div class="text-container" onclick=\'orderPrintModule.batchSellerRemark("' + row.Index + '","' + row.LogicOrderId + '","4","WaitOrder")\'>'
            
            html += '<span class="remark-icon-box">'
            if (row.SystemRemark) {
                 html += '<i onclick=\'commonModule.CopyText("#SystemRemark_text_'+ row.Id +'")\' class="iconfont icon-a-copy1x"></i>'
            }
            html += '<i onclick=\'orderPrintModule.batchSellerRemark("' + row.Index + '","' + row.LogicOrderId + '","4","WaitOrder")\' class="iconfont icon-a-edit1x af-sale-text-edit-icon"></i>'
            html += '</span>'
            // 缺少旗帜判断
            // html += '<span class="iconfont icon-flag icon-flag-5"></span>'

            html += '<span class="">'
            html += '<span style="color: #04385d;" class="systemRemark" title="' + systemRemark + '" id="SystemRemark_text_'+ row.Id +'">' + systemRemark + '</span>'
            if (row.SystemRemark) {
                // html += '<span class="tableconten-left-tar-span SystemRemark-hint">' + systemRemark + '</span>'
            } else {
                html += '<span class="textLine"></span>'
                html += '<span class="notText">编辑合并备注</span>'
            }
            html += '</span>'

            html += '</div>'
            html += '</div>'
        }
        if (row.MergeredType != 3 && row.MergeredType != 4) {//非合单的备注展示在订单级，合单的展示在子单级

            var orderInfoRemarkTmpl = $.templates("#order_info_remark");
            orderInfoRemarkTmpl = orderInfoRemarkTmpl.render({ orders: [row], supportPlatformtTypes: supportPlatformtTypes })
            //console.log('orderInfoRemarkTmpl==', orderInfoRemarkTmpl)
            html += orderInfoRemarkTmpl;

        }
        /*row.IsAppointExpress = true;
        row.OrderPromises = [
            {
                PromiseType: 1,
                PromiseDesc : "我是服务承诺",
                DetailInfo : "没了"
            }
        ];
        row.SubOrders.forEach(function (o) {
            o.OrderPromises = [
                {
                    PromiseType: 1,
                    PromiseDesc: "我是服务承诺",
                    DetailInfo: "没了"
                }];
        });*/
        if ((row.IsAppointExpress != undefined && row.IsAppointExpress) || (row.IsFirstDelivery != undefined && row.IsFirstDelivery)) {
            html += '<div style="color:#666;display:flex;margin-top:5px">';
            html += '<i style="color:#04385d">服务承诺：</i>' + GetOrderPromiseInfo(row);
            html += '</div>';
        }
        html += '</div>';
        return html;
    }
    //订单服务承诺
    var GetOrderPromiseInfo = function (row) {
        var result = "";
        var orderPromises = row.OrderPromises;
        if (row.IsMainOrder) {//合单
            for (var x = 0; x < row.SubOrders.length; x++) {
                var orderPromises = row.SubOrders[x].OrderPromises;
                if (orderPromises == undefined || orderPromises == null)
                    break;
                for (var i = 0; i < orderPromises.length; i++) {
                    var o = orderPromises[i];
                    if (o.PromiseType == 1) {//指定快递
                        if (result != "")
                            result += "；";
                        try {
                            result += o.PromiseDesc + ":" + GetExpressName(JSON.parse(o.DetailInfo).logistics_code);
                        }
                        catch (e) {
                            result += o.PromiseDesc + ":" + o.DetailInfo;
                        }
                    }
                    else if (o.PromiseType == 2) {//优先发货
                        if (result != "")
                            result += "；";
                        try {
                            result += o.PromiseDesc + ":" + JSON.parse(o.DetailInfo).promise_delivery_time;
                        }
                        catch (e) {
                            result += o.PromiseDesc + ":" + o.DetailInfo;
                        }
                    }
                }
            }
        }
        else {
            if (orderPromises == undefined || orderPromises == null)
                return "";
            for (var i = 0; i < orderPromises.length; i++) {
                var o = orderPromises[i];
                if (o.PromiseType == 1) {//指定快递
                    if (result != "")
                        result += "；";
                    try {
                        result += o.PromiseDesc + ":" + GetExpressName(JSON.parse(o.DetailInfo).logistics_code);
                    }
                    catch (e) {
                        result += o.PromiseDesc + ":" + o.DetailInfo;
                    }
                }
                else if (o.PromiseType == 2) {//优先发货
                    if (result != "")
                        result += "；";
                    try {
                        result += o.PromiseDesc + ":" + JSON.parse(o.DetailInfo).promise_delivery_time;
                    }
                    catch (e) {
                        result += o.PromiseDesc + ":" + o.DetailInfo;
                    }
                }
            }
        }
        return result;
    }

    //根据物流编号获取物流名称
    function GetExpressName(expressCode) {
        var expressName = expressCode;
        if (!!expressCodeMapping) {
            for (var i = 0; i < expressCodeMapping.length; i++) {
                if (expressCodeMapping[i].PlatformExpressCode == expressCode) {
                    expressName = expressCodeMapping[i].PlatformExpressName;
                    break;
                }
            }
        }
        return expressName;
    }

    var OperatorContentFormatter = function (row, col, index) {
        // console.log("OperatorContentFormatter",row);

        // 支持卖家备注的平台---目前支持头条，拼多多，淘宝，快手，小红书，微信视频号，京东，度小店，阿里巴巴，淘工厂,哔哩哔哩
        var supportPlatformtTypes = ["TouTiao", "Pinduoduo", "Taobao", "KuaiShou", "XiaoHongShu", "WxVideo", "Jingdong", "DuXiaoDian", "Alibaba", "AlibabaC2M", "BiliBili"];
        var html = "";
        html += '<div class="typeOne">';
        // html += '<span class="dColor hover" data-index="' + row.Index + '" data-id="' + row.Id + '" data-pid="' + row.LogicOrderId + '" onclick="orderPrintModule.UpdatePrintRemark(' + row.Index + ',\'' + row.LogicOrderId + '\')">打单备注</span>';
        html += '<span class="wu-color-a wu-operate UpdatePrintRemark wu-mB4" data-index="' + row.Index + '" data-id="' + row.Id + '" data-pid="' + row.LogicOrderId + '" onclick=\'orderPrintModule.batchSellerRemark("' + row.Index + '","' + row.LogicOrderId + '","1","WaitOrder")\'>打单备注</span>';
        if ((row.ApprovalStatus != 1 || row.ApprovalStatus != 11) && (row.IsEditSellerRemark && supportPlatformtTypes.indexOf(row.PlatformType) >= 0) && (row.MergeredType != 3 && row.MergeredType != 4)) {
            html += '<span class="wu-color-a wu-operate UpdateOrderSellerRemark wu-mB4" data-index="' + row.Index + '" data-id="' + row.Id + '" data-pid="' + row.LogicOrderId + '" onclick=\'orderPrintModule.batchSellerRemark("' + row.Index + '","' + row.LogicOrderId + '","2","WaitOrder")\'>卖家备注</span>';
        }
        if (row.MergeredType != 3 && row.MergeredType != 4) {
            // html += '<span class="fColor hover" onclick=\'orderPrintModule.UpdateSystemRemark("' + row.Index + '","' + row.LogicOrderId + '")\'>分发备注</span>';
            html += '<span class="wu-color-a wu-operate UpdateSystemRemark wu-mB4" onclick=\'orderPrintModule.batchSellerRemark("' + row.Index + '","' + row.LogicOrderId + '","3","WaitOrder")\'>代发留言</span>';
        } else {
            // html += '<span class="fColor hover" onclick=\'orderPrintModule.UpdateSystemRemark("' + row.Index + '","' + row.LogicOrderId + '")\'>合单备注</span>';
            html += '<span class="wu-color-a wu-operate UpdateSystemRemark wu-mB4" onclick=\'orderPrintModule.batchSellerRemark("' + row.Index + '","' + row.LogicOrderId + '","4","WaitOrder")\'>合单备注</span>';
        }
        if (!commonModule.IsCrossBorderSite) {//非跨境
            // 回收单号(物流状态)
            if (row.WaybillCodes && row.WaybillCodes.length > 0) {
                if (row.WaybillCodes.length > 1) {
                    html += '<span class="wu-color-a wu-operate recyle RecycleWaybillCode" data-pid="' + row.LogicOrderId + '" onclick=\'orderTableBuilder.RowRecycleWaybillCodes(' + row.Index + ')\'>回收单号</span>';
                } else {
                    var isMultipleLogistics = row.WaybillCodes[0].ChildWaybillCodes && row.WaybillCodes[0].ChildWaybillCodes.length > 0 ? true : false;
                    html += '<span class="dColor hover recyle RecycleWaybillCode" data-pid="' + row.LogicOrderId + '" onclick=\'orderTableBuilder.Recycle(' + row.Index + ',' + row.WaybillCodes[0].TemplateId + ',"' + row.LogicOrderId + '",' + row.WaybillCodes[0].WaybillCodeId + ',"' + row.WaybillCodes[0].WaybillCode + '",' + row.WaybillCodes[0].IsManual + ',' + isMultipleLogistics + ')\' >回收单号</span>';
                }
            }
        }
        html += '</div>';
        return html;
    }

    // 回收单条单号
    table.Recycle = function (index, templateId, orderId, waybillCodeId, waybillCode, status, isMultipleLogistics) {
        console.log("status", status, typeof status);
        if (status) {
            layer.msg('该单号为手工发货单号，不支持回收');
            return false;
        }
        // 权限校验
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.RecycleWaybillCode);
        });

        var thisFunc = function () {
            _triggerRecycle = true;
            var row = table.rows[index];
            var waybillCodes = row.WaybillCodes;
            var closestDate = new Date(waybillCodes[0].CreateDate);
            var farthestDate = new Date(waybillCodes[0].CreateDate);
            // console.log(codes);
            // 时间加减1s
            closestDate.setSeconds(closestDate.getSeconds() - 1);
            farthestDate.setSeconds(farthestDate.getSeconds() + 1);
            var requestModel = {
                StartDate: formatDate(closestDate),
                EndDate: formatDate(farthestDate),
                ExpressWaybillCode: waybillCode,
                //OrderByField: 'GETDATE',
                IsOrderDesc: true,
                PageIndex: 1,
                PageSize: 500,
            };
            //console.log(requestModel);
            common.Ajax({
                url: '/WaybillCodeList/LoadList',
                loading: true,
                data: { requestModel: requestModel, Pt: common.getQueryVariable("pt") },
                success: function (rsp) {
                    if (rsp.Success == false) {
                        layer.msg("获取单号失败：" + rsp.Message, { icon: 2 });
                        return;
                    }

                    var getRows = rsp.Data.Rows;
                    var status = getRows[0].Status == null ? -1 : getRows[0].Status;
                    //console.log(status);
                    if (status == -1) {
                        layer.msg("单号状态出错" + msg, { icon: 2 });
                        return;
                    }
                    if (status == 2) {
                        var msg = rsp.Message == null ? "" : rsp.Message;
                        layer.msg("该单号已回收<br>" + msg, { icon: 2 });

                        removeRecyle(orderId);

                        table.rows[index].WaybillCodes = undefined;
                        row.WaybillCodes = undefined;
                        table.refreshColumn('ExpressCode');
                        //table.CheckShowOrder(this);
                        return;
                    }
                    if (status == 1 || status == 3) {
                        waybillCodeListModule.Recycle(templateId, orderId, waybillCodeId, waybillCode, isMultipleLogistics, function () {
                            removeRecyle(orderId);
                            table.rows[index].WaybillCodes = undefined;
                            row.WaybillCodes = undefined;
                            table.refreshColumn('ExpressCode');
                            //table.CheckShowOrder(this);
                        });
                    }
                }
            });
        }
    }
    // 页面移除单号和回收单号按钮
    var removeRecyle = function (orderId) {
        var operator = $('.order-column-Operator');
        if (orderId) {
            operator.each(function (i, o) {
                var span = $(o).find('span.recyle');
                if ($(span).attr('data-pid') == orderId) {
                    $(span).css('display', 'none')
                    return false;
                }
            });
        }
    }

    // 单条记录回收多单号
    table.RowRecycleWaybillCodes = function (index) {
        // 权限校验
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;
            }, p.RecycleWaybillCode);
        });

        var thisFunc = function () {
            _triggerRecycle = true;
            var orderRow = table.rows[index];
            // console.log(orderRow);
            var waybillCodes = orderRow.WaybillCodes;
            // 过滤掉 IsManual 为 true 的元素
            var listData = waybillCodes.filter(function (item) {
                return !item.IsManual;
            });
            listData.forEach(function (row) {
                row.OrderId = orderRow.LogicOrderId;
                var childs = [];
                if (row.ChildWaybillCodes && row.ChildWaybillCodes.length > 0) {
                    row.ChildWaybillCodes.forEach(function (c) {
                        if (c.ChildWaybillCode != row.WaybillCode) {
                            c.CreateDate = row.CreateDate;
                            c.ExpressName = row.ExpressName;
                            c.WaybillCode = row.WaybillCode;
                            childs.push(c);
                        }
                    });
                    row.ChildWaybillCodes = childs;
                }
            });
            console.log('waybillCodes===========', listData);
            var tplt = $.templates("#recovery-detail-tmpl");
            var html = tplt.render(listData);
            $("#recovery-list-body").empty().append(html);
            $("#recovery_chk_allorderitem").prop("checked", false);
            var option = layer.open({
                type: 1,
                title: '多单号回收',
                content: $("#recovery_dailog"),
                area: ['856px', 'auto'], //宽高
                btn: ['确认回收', '取消'],
                skin: 'wu-dailog',
                btn1: function () {
                    RecycleWaybillCodesConfirm(index, waybillCodes, function () {
                        //console.log("close");
                        layer.close(option);
                    });
                },
                btn2: function () {

                },
            });
            /*
            var codes = [];
            var closestDate = new Date(waybillCodes[0].CreateDate);
            var farthestDate = new Date(waybillCodes[0].CreateDate);
            waybillCodes.forEach(function (o) {
    
                codes.push(o.WaybillCode);
                var createDate = new Date(o.CreateDate);
    
                // 更新最近时间和最远时间
                if (createDate < closestDate) {
                    closestDate = createDate;
                }
    
                if (createDate > farthestDate) {
                    farthestDate = createDate;
                }
    
            });
            // console.log(codes);
            // 时间加减1s
            closestDate.setSeconds(closestDate.getSeconds() -1);
            farthestDate.setSeconds(farthestDate.getSeconds() + 1);
            // 查询底单数据
            var requestModel = {
                StartDate: formatDate(closestDate),
                EndDate: formatDate(farthestDate),
                ExpressWaybillCode: codes.join(','),
                //OrderByField: 'GETDATE',
                IsOrderDesc: true,
                PageIndex: 1,
                PageSize: 500,
            };
            //console.log(requestModel);
            common.Ajax({
                url: '/WaybillCodeList/LoadList',
                loading: true,
                data: { requestModel: requestModel, Pt: common.getQueryVariable("pt") },
                success: function (rsp) {
                    if (rsp.Success == false) {
                        layer.msg(rsp.Message, { icon: 2 });
                        return;
                    }
                    var getRows = rsp.Data.Rows;
                   
                    getRows.forEach(function (row) {
                        if (row.ChildWaybillCodes) {
                            row.ChildWaybillCodes.forEach(function (c) {
                                c.CreateDate = row.CreateDate;
                                c.ExpressName = row.ExpressName;
                                c.ExpressWayBillCode = row.ExpressWayBillCode;
                            });
                        }
                    });
                    var tplt = $.templates("#recovery-detail-tmpl");
                    var html = tplt.render(getRows.reverse());
                    $("#recovery-list-body").empty().append(html);
    
                    $("#recovery_chk_allorderitem").prop("checked", false);
                    var option = layer.open({
                        type: 1,
                        title: '多单号回收',
                        content: $("#recovery_dailog"),
                        area: ['700px', 'auto'], //宽高
                        btn: ['确认回收', '取消'],
                        btn1: function () {
                            RecycleWaybillCodesConfirm(index,getRows, function () {
                                //console.log("close");
                                layer.close(option);
                            });
                        },
                        btn2: function () {
    
                        },
                    });
                }
    });
    */
        }

    }

    // 格式化日期方法
    function formatDate(date) {
        var year = date.getFullYear();
        var month = padZero(date.getMonth() + 1);
        var day = padZero(date.getDate());
        var hours = padZero(date.getHours());
        var minutes = padZero(date.getMinutes());
        var seconds = padZero(date.getSeconds());

        return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds;
    }
    // 补零方法
    function padZero(number) {
        return number < 10 ? "0" + number : number;
    }

    // 多单号确认回收
    var RecycleWaybillCodesConfirm = function (index, datas, callback) {
        var $checkedList = $(".recovery-chx:checked");
        if ($checkedList.length == 0) {
            layer.msg("请先勾选需要回收的单号");
            return;
        }
        var checkedRows = [];
        $checkedList.each(function (index, item) {
            datas.forEach(function (data) {
                if (data.WaybillCodeId == $(item).attr("data-id")) {
                    checkedRows.push(data);
                }
            });
        });

        //console.log(checkedRows);
        //1.收集勾选的底单的数据
        var data_group_by_templateId = {}; //按模板id 分组的数据
        var recycled_count = 0; //已回收的数据
        var nomarl_tempalte_count = 0; //普通传统面单底单记录，（回收不了，一般没有单号）
        var express_not_support_recycle_count = 0;
        common.Foreach(checkedRows, function (i, o) {
            //if (o.TemplateType != 1 && o.Status != 2 && o.TemplateType != 3 && o.ExpressCpCode != "ZYKD" && o.ExpressCpCode != "YTO" && o.ExpressCpCode != "ZTO") {//
            //    if (data_group_by_templateId[o.TemplateId]) {
            //        data_group_by_templateId[o.TemplateId].Datas.push(o);
            //    }
            //    else {
            //        data_group_by_templateId[o.TemplateId] = {
            //            Datas: [o]
            //        };
            //    }
            //}
            if (o.TemplateType == 1) {
                nomarl_tempalte_count++;
            }
            else if (o.Status == 2) {
                recycled_count++;
            }
            //else if (o.Status == 3) {
            //    send_count++;
            //}
            else if (data_group_by_templateId[o.TemplateId]) {
                data_group_by_templateId[o.TemplateId].Datas.push(o);
            }
            else if (o.TemplateType == 3 && (o.ExpressNo == "ZYKD" || o.ExpressNo == "YTO" || o.ExpressNo == "ZTO")) {
                express_not_support_recycle_count++;    //只针对网点模板，(众邮)快递不支持回收 / (中通、圆通)不支持手动回收
            }
            else {
                data_group_by_templateId[o.TemplateId] = {
                    Datas: [o],
                    TemplateName: o.TemplateName, //模板名称
                    ParentChildOrderNumber: 0  //字母单数量
                };
            }
            if (data_group_by_templateId[o.TemplateId] && o.ChildWaybillCode) {
                data_group_by_templateId[o.TemplateId].ParentChildOrderNumber += 1; //子母单数量
            }
        });
        if (express_not_support_recycle_count > 0) {
            layer.alert('你所勾选的运单号中包含不支持手动回收的订单，请重新勾选。', { skin: 'wu-dailog' });
            return;
        }
        if (checkedRows.length == nomarl_tempalte_count + recycled_count) {
            layer.alert('您所勾选的订单无需回收或者已经回收过，请注意检查。', { skin: 'wu-dailog' });
            return;
        }
        //console.log(data_group_by_templateId);
        //弹窗
        var msg = '<div><div class="wu-f16 wu-c09 wu-weight500">确定要回收吗？</div><div>如果是子母单号，回收母单号会回收所有的子单号，且只支持母单号回收。</div><div>若有不符合回收条件的单号，将自动过滤。</div></div>';
        layer.confirm(msg, { icon: 3, title: '回收确认', skin: 'wu-dailog' }, function (option) {
            //参数组装
            var data_param = [];
            common.Foreach(data_group_by_templateId, function (i, o) {
                var tmplateId = o.Datas[0].TemplateId;
                var obj = { TemplateId: tmplateId, WaybillCodeRecycleViewModels: [] };
                common.Foreach(o.Datas, function (i, o) {
                    //判段重复
                    var exist = false;
                    common.Foreach(obj.WaybillCodeRecycleViewModels, function (ii, w) {
                        if (w.WaybillCodeId == o.WaybillCodeId || (w.WaybillCode == o.WaybillCode && w.OrderId == o.OrderId)) {
                            exist = true;
                            return 'break;'
                        }
                    });
                    if (exist == false) {
                        obj.WaybillCodeRecycleViewModels.push({
                            WaybillCodeId: o.WaybillCodeId,
                            WaybillCode: o.WaybillCode,
                            OrderId: o.OrderId,//(o.OrderIdJoin ? o.OrderIdJoin : o.OrderId)
                        });
                    }
                });
                data_param.push(obj);
            });
            //console.log(data_param);

            /*var successList = [70,72];
            var row = table.rows[index];
            var waybillCodes = [];
            row.WaybillCodes.forEach(function (w) {
                if (successList.indexOf(w.WaybillCodeId) == -1) {
                    waybillCodes.push(w);
                }
            });
            table.rows[index].WaybillCodes = waybillCodes;
            table.refreshColumn('ExpressCode');
            //console.log(row);
            removeRecyle(row.LogicOrderId);*/


            //发送回收请求
            common.Ajax({
                url: '/WaybillCodeList/BatchCancelWaybillCode',
                loading: true,
                data: { batchWaybillCodeRecycleVmList: data_param },
                success: function (rsp) {
                    if (rsp.Success == false) {
                        layer.msg(rsp.Message, { icon: 2 });
                        return;
                    }
                    if (rsp.Data.IsError == true) {
                        var successList = rsp.Data.SuccessList;

                        var msg = '<p>成功回收' + successList.length + '条单号，部分单号回收失败，失败原因：' + '</p>';
                        common.Foreach(rsp.Data.ErrorList, function (i, o) {
                            msg += "<p>" + o + "</p>";
                        });
                        layer.alert(msg, { skin: 'wu-dailog' });

                        // 页面移除回收成功的单号
                        var row = table.rows[index];
                        var waybillCodes = [];
                        row.WaybillCodes.forEach(function (w) {
                            if (successList.indexOf(w.WaybillCodeId) == -1) {
                                waybillCodes.push(w);
                            }
                        });
                        table.rows[index].WaybillCodes = waybillCodes;
                        table.refreshColumn('ExpressCode');
                    }
                    else {
                        commonModule.w_alert({ type: 4, content: '回收成功' });
                        // 页面移除回收成功的单号
                        var successList = rsp.Data.SuccessList;
                        var row = table.rows[index];
                        var waybillCodes = [];
                        row.WaybillCodes.forEach(function (w) {
                            if (successList.indexOf(w.WaybillCodeId) == -1) {
                                waybillCodes.push(w);
                            }
                        });
                        table.rows[index].WaybillCodes = waybillCodes;
                        table.refreshColumn('ExpressCode');
                        removeRecyle(row.LogicOrderId);
                    }
                    layer.close(option);
                    callback();
                }
            });
        });
    }


    var OrderTimeContentFormatter = function (row, col, index, classNamePrefix) {
        var html = '<input data-PlatformType="' + row.PlatformType + '"  class="order-chx wu-mR12" data-id="' + row.Id + '" data-oid="' + row.PlatformOrderId + '" data-pid="' + row.LogicOrderId + '" data-sid="' + row.ShopId + '" data-index="' + index + '"' + ((classNamePrefix || row.checked) ? " checked" : "") + ' type="checkbox" onclick="orderTableBuilder.CheckOrderBind(this);">';
        html += '<div class="mytable-tr-time">';
        html += '<span>' + (row.Date || "") + '</span>';
        html += '<span>' + (row.Time || "");
        if (row.PublishTime && row.PlatformType == 'Jingdong') {
            var msgDivHtml = '<div class="tooltip-content">';
            msgDivHtml += '预约发货时间：' + new Date(row.PublishTime).Format("yyyy-MM-dd hh:mm:ss") + '<br>';
            html += '<div class="tagStatus tooltip" style="width: 18px;margin-left: 2px;">预' + msgDivHtml + '</div>';
            html += '</div>';
        }
        html += '</div>';
        return html + '</span>';
    }

    var AllProductHeaderFormattter = function (col) {
        var html = '<div class="operateOrder" style="color: rgba(0, 0, 0, 0.9);">';
        html += '<label class="flexAJ wu-checkboxWrap">';
        html += '<input type="checkbox" id="allcheckorder" class="' + col.field + '" onclick="orderPrintModule.AllCheck(this)">';
        html += '<span style="padding-left: 12px;color:rgba(0,0,0,0.9)">商品信息</span>';
        html += '</label>';
        html += '<span style="color:rgba(0,0,0,0.7)">已选择<i id="orderNum">0</i>条</span>';
        html += '</div>';
        return html;
    }
    var AllOperatorHeaderFormatter = function (col) {
        var pt = "";
        commonModule.LoadCommonSetting("/ErpWeb/DefaultPlatform", false, function (rsp) {
            pt = rsp.Data || "";
        });

        //var html = '<span class="operate-title">操作</span>';
        var html = '';
        var status = $("#orderstatus .active").attr("data-status") || "";
        // 不是待审核和待发货和全部订单，批量设置样式调整

        // if (status !== "waitaudit" && status !== "waitsellersend") {
        //     html += '<div class="operate-btn newoperate-btnWrap" style="right:177px;">';
        // } else {
        //     html += '<div class="operate-btn newoperate-btnWrap" style="right:330px;">';
        // } 
        var CloudPlatformNameTab = commonModule.getQueryVariable('pt') || 'Alibaba';
        if (status !== "waitaudit" && status !== "waitsellersend") {
            if (CloudPlatformNameTab && CloudPlatformNameTab.toUpperCase() == 'JINGDONG') {
                // 京东多一个按钮所以right要远一点
                html += '<div class="operate-btn newoperate-btnWrap" id="newoperate_batch_btnWrap" style="right:282px;">';
            } else {
                // 不是京东就正常177
                html += '<div class="operate-btn newoperate-btnWrap" style="right:197px;">';
            }
        } else {
            html += '<div class="operate-btn newoperate-btnWrap operate-btn-batch" style="right:323px;">';
        }

        if (commonModule.FxPageType() == 3) {
            if (status == "pending") {
                html += '<div class="offline_approvaled" style="right:-20px">';
                html += '   <button class="layui-btn layui-btn-sm batchApprovaled CheckOrder " onclick="OfflineOrderModule.BatchUpdateApproval(13)" style="width:80px;">批量审核通过</button>';
                html += '   <button class="layui-btn layui-btn-sm batchApprovalRefused DeleteOfflineOrder" onclick="OfflineOrderModule.BatchUpdateApproval(15)">批量作废订单</button>';
                html += '</div>';
            }
            else {
                if (status == "approvaled") {
                    html += '<div class="offline_approvaled">';
                    html += '   <button class="layui-btn layui-btn-sm batchBackPending DeleteOfflineOrder" onclick="OfflineOrderModule.BatchRevokeApproval()">批量删除订单</button>';
                    html += '   <button class="layui-btn layui-btn-sm batchRemark UpdateSystemRemark" onclick="orderPrintModule.batchRemark()">批量分发备注</button>';
                    html += '   <button class="layui-btn layui-btn-sm batchBackPending CheckOrder" onclick="OfflineOrderModule.BatchUpdateApproval(11)">批量退审订单</button>';
                    //html += '   <button class="layui-btn layui-btn-sm batchToSended" style="background-color:#3aadff;" onclick="OfflineOrderModule.BatchUpdateOrderToSended()">批量标记发货</button>';
                    /*html += '   <button class="layui-btn layui-btn-sm batchApprovaled" onclick="bindSupplierModule.BatchMappingSupplier(\'order\')">批量推送厂家</button>';*/
                    html += '   <button class="layui-btn layui-btn-sm batchApprovaled ManualCheck" onclick="orderPrintModule.batchManualPush(1)">手工推单</button>';

                    html += '</div>';
                } else {
                    html += '<div class="offline_approvaled">';
                    html += '   <button class="layui-btn layui-btn-sm batchRemark UpdateSystemRemark" onclick="orderPrintModule.batchRemark()">批量分发备注</button>';
                    html += '<span class="exportProgress-btn ManualAddAfterSale" onclick="CreateManualAfterSaleModule.showCreateManualAfterSaleDailog(' + "'batchOrder'" + ')" style="position: relative;top: 10px;left:2px; "><span class="exportText">批量转售后工单</span></span>'
                    html += '</div>';

                }

                //html += '   <button class="layui-btn layui-btn-sm" onclick="orderPrintModule.BatchAfterSales()" style="width:80px;background-color:#fe6f4f;">批量转售后</button>';
            }
        }
        else {

            //if (status == "waitsellersend" || status == "all") {
            //    html += '   <button class="layui-btn layui-btn-sm batchUnCheck" style="width:80px;" onclick="orderPrintModule.batchUnCheck()">批量退审</button>';
            //    html += '   <button class="layui-btn layui-btn-sm" onclick="orderPrintModule.BatchAfterSales()" style="width:80px;background-color:#fe6f4f;">批量转售后</button>';
            //} else if (status == "waitaudit") {
            //    //增加两个按钮
            //    html += '   <button class="layui-btn layui-btn-sm batchCheck" onclick="orderPrintModule.batchCheck()" style="width:80px;">批量通过审核</button>';
            //    /*html += '   <button class="layui-btn layui-btn-sm batchManualPush" onclick="orderPrintModule.batchManualPush()">批量手工推送</button>';*/
            //    html += '   <button class="layui-btn layui-btn-sm batchManualPush batchManualPushSelf" onclick="orderPrintModule.batchManualPushSelf()">批量设为自营</button>';
            //    html += '   <button class="layui-btn layui-btn-sm batchManualPush batchManualPushSupplier" onclick="orderPrintModule.batchManualPushSupplier()" style="width:80px;background-color:#f7941f;">批量指定厂家</button>';
            //}
            //else {
            //    html += '   <button class="layui-btn layui-btn-sm" onclick="orderPrintModule.BatchAfterSales()" style="width:80px;background-color:#fe6f4f;">批量转售后</button>';
            //}

            //html += '   <button class="layui-btn layui-btn-sm batchRemark" onclick="orderPrintModule.batchRemark()">批量分发备注</button>';
            //html += '   <button class="layui-btn layui-btn-sm batchRemark" style="background-color:#b2acd9;" onclick="orderPrintModule.batchSellerRemark();">批量卖家备注</button>';


            //if (commonModule.FxPageType() == 1 && status == "waitsellersend")
            //    html += '   <button class="layui-btn layui-btn-sm layui-btn-warm" style="background-color:#f29a1a" onclick="bindSupplierModule.BatchMappingSupplier(\'order\')">批量推送厂家</button>';


            //改动的---------------------
            html += '<span class="newMoreOperate" style="font-size:14px;"> ';
            html += '<span class="new-exportText"><i class="iconfont icon-guanli new-exportText-icon"></i>批量操作 ';
            html += '<i class="iconfont icon-down"></i> ';
            html += '</span>';
            html += '<ul class="new-export-btnShow"> ';
            if (status == "waitsellersend") {
                html += '<li class="CheckOrder" onclick="orderPrintModule.batchUnCheck()">批量退审</li>';
                //    html += '<li onclick="orderPrintModule.BatchAfterSales()">批量转售后</li>';
            }
            else if (status == "all") {
                //    html += '<li onclick="orderPrintModule.BatchAfterSales()">批量转售后</li>';
            }
            else if (status == "waitaudit") {
                //增加两个按钮
                html += '<li class="CheckOrder" onclick="orderPrintModule.batchCheck()">批量通过审核</li>';
                /*html += '   <button class="layui-btn layui-btn-sm batchManualPush" onclick="orderPrintModule.batchManualPush()">批量手工推送</button>';*/
                html += '<li class="ManualCheck" onclick="orderPrintModule.batchManualPushSelf()">批量设为自营</li>';
                html += '<li class="ManualCheck" onclick="orderPrintModule.batchManualPushSupplier()">批量指定厂家</li>';
            }
            else {
                //    html += '<li onclick="orderPrintModule.BatchAfterSales()">批量转售后</li>';
            }
            // if (!(status == "close" && commonModule.IsShowCrossBorder && pt == 'tiktok')) {
            //     html += '   <li onclick="orderPrintModule.batchRemark()">批量分发备注</button>';
            // }
            // if (!(commonModule.IsShowCrossBorder && pt == 'tiktok')) {
            //     html += '   <li onclick="orderPrintModule.batchSellerRemark();">批量卖家备注<span class="remarkIcon" onclick="commonModule.stopM(event)"><i>?</i><s>因订单权限限制，代发订单不允许厂家直接修改卖家备注，系统已为您自动过滤，如有需求请联系商家本人修改或通过分发备注同步商家。</s></span></li>';
            // }
            html += '   <li class="ManualAddAfterSale" onclick="CreateManualAfterSaleModule.showCreateManualAfterSaleDailog(' + "'batchOrder'" + ')">批量转售后工单</li>';
            // html += '   <li onclick="orderPrintModule.batchRemark()">批量分发备注</button>';
            html += '   <li class="UpdateSystemRemark" onclick=\'orderPrintModule.batchSellerRemark("","","3","AllOrder","batch")\'>批量代发留言</button>';
            // html += '   <li onclick="orderPrintModule.batchSellerRemark();">批量卖家备注<span class="remarkIcon" onclick="commonModule.stopM(event)"><i>?</i><s>因订单权限限制，代发订单不允许厂家直接修改卖家备注，系统已为您自动过滤，如有需求请联系商家本人修改或通过分发备注同步商家。</s></span></li>';
            // html += '   <li onclick=\'orderPrintModule.batchSellerRemark("","","2","AllOrder","batch")\'>批量卖家备注<span class="remarkIcon" onclick="commonModule.stopM(event)"><i>?</i><s>因订单权限限制，代发订单不允许厂家直接修改卖家备注，系统已为您自动过滤，如有需求请联系商家本人修改或通过分发备注同步商家。</s></span></li>';
            if (!commonModule.IsCrossBorderSite) {
                html += '   <li class="UpdateOrderSellerRemark" onclick=\'orderPrintModule.batchSellerRemark("","","2","AllOrder","batch")\'>批量卖家备注<span class="remarkIcon" onclick="commonModule.stopM(event)"><i>?</i><s>若厂家需要使用商家备注回传店铺的功能。请让分销商在分销设置里为你开启同步备注到后台的权限。<a style="color: #0888FF;" onclick=\'orderPrintModule.tarUrl("/System/DistributeSet")\'>查看页面配置</a></s></span></li>';
            } else {
                // 待审核操作
                if (status == "waitaudit") {
                    html += '<li class="alifx-HorS" onclick="orderPrintModule.batchUploadInvoice();">上传发票</li>';
                }
            }


            //1688分销订单-待审核操作-增加两个按钮
            if (status == "waitaudit" && !(commonModule.IsShowCrossBorder && pt == 'tiktok')) {
                html += '<li class="alifx-HorS" onclick="orderPrintModule.batchPreviewFxOrder();">批量下单</li>';
                html += '<li class="alifx-HorS" onclick="orderPrintModule.batchPayFxOrder();">批量付款</li>';
                html += '<li class="alifx-HorS" onclick="orderPrintModule.batchCancelOrders();">批量取消订单</li>';
            }

            html += '</ul> ';
            html += '</span> ';

        }
        //html += '   <button class="layui-btn layui-btn-sm layui-btn-warm" style="background-color:#f29a1a" onclick="orderPrintModule.batchMappingSupplier()">批量匹配厂家</button>';
        html += '</div>';

        //$("#operateWrap").html(html);

        return html;
        //return "<span>操作</span>";

    }
    var OperatorHeaderFormatter = function () {
        var html = '';
        html += '<button class="layui-btn layui-btn-sm batchRemark UpdateSystemRemark" style="background-color:#99cc00">批量分发备注</button>';
        return html;
    }

    var LastShipTimeContentFormatter = function (row, col, index) {
        var html = "";
        html += '<div class="allTimeWrap">'
        if (row.IsPddCrossBorderOrder) {
            html += '<div class="promiseTimeWrap"><span><i class="iconfont icon-jishi"></i>承诺送达时间:</span>';
            html += '<span class="promiseTime">' + row.LastShipTime + '</span>';
            html += '</div>';
        }
        else {
            //拼多多承诺送达标签产品说暂时不放到订单剩余时间列了
            //if (row.PlatformType == "Pinduoduo") {
            //    var tags = GetTagContent(row);
            //    if (commonModule.HasTag(tags, 'pdd_bought_from_vegetable', 'OrderItem')) {
            //        var tagValue = "";
            //        if (tags && tags.length > 0) {
            //            var minValue = "";
            //            for (var i = 0; i < tags.length; i++) {
            //                var tag = tags[i];
            //                if (tag.Tag == 'pdd_bought_from_vegetable') {
            //                    var currDate = new Date(tag.TagValue);
            //                    if (minValue == "") {
            //                        minValue = currDate;
            //                    }
            //                    else if (currDate < minValue) {
            //                        minValue = currDate;
            //                    }
            //                }
            //            }
            //            //对应的标签值是承诺送达时间，取订单标签里的最小值,yyyy-MM-dd HH:mm:ss
            //            if (minValue != "") {
            //                tagValue = minValue.Format("yyyy-MM-dd hh:mm:ss");
            //            }
            //        }
            //        var msgDivHtml = '';
            //        msgDivHtml = '<div class="tooltip-content">';
            //        msgDivHtml += '该订单承诺送达时间：' + tagValue + '<br>';
            //        msgDivHtml += '该订单已约定承诺送达，商家需要按照订单提供的承诺送达时<br>';
            //        msgDivHtml += '间，将货品送达消费者收货地址，未按时送达会造成客诉风险。<br>';
            //        msgDivHtml += '</div>';
            //        html += '<div class="tagStatus tooltip">承诺送达' + msgDivHtml + '</div>';
            //        html += '</div>'
            //        return html;
            //    }
            //}
            html += timeContentFormatter(row, "LastShipTime");
        }
        html += '</div>'
        return html;
    }
    var GetTagContent = function (row) {
        var originalTags = row.OrderTags || [];
        if (originalTags == undefined || originalTags.length == 0) return "";
        //var fxPageType = commonModule.FxPageType(); //(1：所有订单，2：待打单页面，3：线下单页面，4：已发货详细)
        var tags = [];  //订单包含的所有标签
        //标签去重
        var tags_addfunc = function (original) {
            tags.push(original);
        }
        for (var i = 0; i < originalTags.length; i++) {
            var original = originalTags[i];
            if (tags.length == 0) {
                tags_addfunc(original);
            } else {
                var isHas = false;
                for (var t = 0; t < tags.length; t++) {
                    var newTag = tags[t];
                    //if (original.TagType == "Order" || original.TagType == "OrderItem") {
                    //    if (newTag.Tag == original.Tag && newTag.OiCode == original.OiCode) {
                    //        isHas = true;
                    //        break;
                    //    }
                    //}
                    //else {
                    //    if (newTag.Tag == original.Tag && newTag.TagType == original.TagType && newTag.TagValue == original.TagValue) {
                    //        isHas = true;
                    //        break;
                    //    }
                    //}
                    if (newTag.HashCode == original.HashCode) {
                        isHas = true;
                        break;
                    }
                }
                if (!isHas) {
                    tags_addfunc(original);
                }
            }
        }
        return tags;
    }

    var allOrderTimeContentFormatter = function (row) {
        var val = row.LastShipTime || "";
        if (val === "") return "-";
        else if (val == "-") return "-";

        var payTime = new Date(row.OrderTime).getTime();
        var nowTime = new Date().getTime();
        var LastTime = new Date(val).getTime();
        var LastShipTime = LastTime - nowTime;

        //算百分比
        var totalNum = LastTime - payTime;
        var progressNum = nowTime - payTime;

        var percent = parseInt(100 - progressNum / totalNum * 100) <= 100 ? parseInt(100 - progressNum / totalNum * 100) : 100;
        var activeClass = (percent <= 50 ? 'active' : '');//超过50%背景红变

        //时分秒换算
        function MillisecondToDate(msd) {
            var time = parseFloat(msd) / 1000;
            if (null != time && "" != time) {
                if (time > 60 && time < 60 * 60) {
                    time = parseInt(time / 60.0) + "分" + parseInt((parseFloat(time / 60.0) -
                        parseInt(time / 60.0)) * 60) + "秒";
                }
                // else if (time >= 60 * 60 && time < 60 * 60 * 24) {
                else if (time >= 60 * 60) {
                    time = parseInt(time / 3600.0) + "时" + parseInt((parseFloat(time / 3600.0) -
                        parseInt(time / 3600.0)) * 60) + "分" +
                        parseInt((parseFloat((parseFloat(time / 3600.0) - parseInt(time / 3600.0)) * 60) -
                            parseInt((parseFloat(time / 3600.0) - parseInt(time / 3600.0)) * 60)) * 60) + "秒";
                }
                else {
                    time = parseInt(time) + "秒";
                }
            }
            return time;
        }

        var html = "";

        //var $CurrentStatus = $("#orderList_orderState_choose>span.active").attr("data-status");

        //if ($CurrentStatus == "waitsellersend" || $CurrentStatus == "confirm_goods_but_not_fund") {
        html += '<div class="lastShipTimeWrap">'
        if (row.PddShipHold != "1") {
            percent = percent < 0 ? "100" : percent;
            html += '<div class="percent">';
            html += '<span class="jing ' + activeClass + '"   style="width:' + percent + '%"></span>';
            html += '</div>';
            if (LastShipTime > 0) {
                var $time = MillisecondToDate(LastShipTime);
                html += '<span>' + $time + '</span>';
            } else {
                LastShipTime = -LastShipTime;
                var $time = MillisecondToDate(LastShipTime);
                html += '<span class="wu-color-b" style="color:#ea572e">超:' + $time + '</span>';
            }
        }

        //if (platformType == "toutiao" && row["TradeType"] == "0") {
        //    html += '<div class="toutiaoHuo"><span>货</span></div>';
        //}
        html += '</div>'
        //}


        return html;
    }

    var timeContentFormatter = function (row, field) {
        var val = "";
        if (field == "LastShipTime")
            val = row.LastShipTime || "";
        else if (field == "ExtField1")
            val = row.ExtField1 || "";
        else
            return "-";

        if (val === "") return "-";
        else if (!val) return "-";
        var payTime = new Date(row.PayTime).getTime();
        var nowTime = new Date().getTime();
        var LastTime = new Date(val).getTime();
        var LastShipTime = LastTime - nowTime;

        //算百分比
        var totalNum = LastTime - payTime;
        var progressNum = nowTime - payTime;

        var percent = parseInt(100 - progressNum / totalNum * 100) <= 100 ? parseInt(100 - progressNum / totalNum * 100) : 100;
        var activeClass = (percent <= 50 ? 'active' : '');//超过50%背景红变

        //时分秒换算
        function MillisecondToDate(msd) {
            var time = parseFloat(msd) / 1000;
            if (null != time && "" != time) {
                if (time > 60 && time < 60 * 60) {
                    time = parseInt(time / 60.0) + "分" + parseInt((parseFloat(time / 60.0) -
                        parseInt(time / 60.0)) * 60) + "秒";
                }
                // else if (time >= 60 * 60 && time < 60 * 60 * 24) {
                else if (time >= 60 * 60) {
                    time = parseInt(time / 3600.0) + "时" + parseInt((parseFloat(time / 3600.0) -
                        parseInt(time / 3600.0)) * 60) + "分" +
                        parseInt((parseFloat((parseFloat(time / 3600.0) - parseInt(time / 3600.0)) * 60) -
                            parseInt((parseFloat(time / 3600.0) - parseInt(time / 3600.0)) * 60)) * 60) + "秒";
                }
                else {
                    time = parseInt(time) + "秒";
                }
            }
            return time;
        }

        var html = "";

        //var $CurrentStatus = $("#orderList_orderState_choose>span.active").attr("data-status");

        //if ($CurrentStatus == "waitsellersend" || $CurrentStatus == "confirm_goods_but_not_fund") {
        html += '<div class="lastShipTimeWrap">';
        if (row.PddShipHold != "1") {
            if (LastShipTime > 0) {
                percent = percent < 0 ? "100" : percent;
                var $time = MillisecondToDate(LastShipTime);
                html += '<div class="percent normalPercent">';
                html += '<span class="jing ' + activeClass + '" style="width:' + percent + '%"></span>';
                html += '</div>';
                html += '<span>' + $time + '</span>';
            } else {
                LastShipTime = -LastShipTime;
                var $time = MillisecondToDate(LastShipTime);
                html += '<div class="percent overtimePercent">';
                html += '</div>';
                html += '<span class="wu-color-b" style="color:#ea572e">超:' + $time + '</span>';
            }
        }
        //if (platformType == "toutiao" && row["TradeType"] == "0") {
        //    html += '<div class="toutiaoHuo"><span>货</span></div>';
        //}
        html += '</div>'
        //}


        return html;
    }

    //#region 订单标签展示
    /*
     * 标签展示 OrderTags
     * TagsHtml 暂时先适配一段时间，字段后面可能会弃用，
     * method 调用方式
     */
    table.TagsContentFormatter = function (row, method) {
        console.log('row=======',row)
        var originalTags = row.OrderTags || [];
        if (originalTags == undefined || originalTags.length == 0) return "";
        var isWxVideoExt = (row.PlatformType == "WxVideo" && row.ExtField2 != undefined && row.ExtField2 != "");
        var fxPageType = commonModule.FxPageType(); //(1：所有订单，2：待打单页面，3：线下单页面，4：已发货详细)
        var tags = [];  //订单包含的所有标签
        var itemTags = [];  //订单项标签
        //标签去重
        var tags_addfunc = function (original) {
            if (original.TagType == "OrderItem")
                itemTags.push(original);
            tags.push(original);
        }
        for (var i = 0; i < originalTags.length; i++) {
            var original = originalTags[i];
            if (tags.length == 0) {
                tags_addfunc(original);
            } else {
                var isHas = false;
                for (var t = 0; t < tags.length; t++) {
                    var newTag = tags[t];
                    //if (original.TagType == "Order" || original.TagType == "OrderItem") {
                    //    if (newTag.Tag == original.Tag && newTag.OiCode == original.OiCode) {
                    //        isHas = true;
                    //        break;
                    //    }
                    //}
                    //else {
                    //    if (newTag.Tag == original.Tag && newTag.TagType == original.TagType && newTag.TagValue == original.TagValue) {
                    //        isHas = true;
                    //        break;
                    //    }
                    //}
                    if (newTag.HashCode == original.HashCode) {
                        isHas = true;
                        break;
                    }
                }
                if (!isHas) {
                    tags_addfunc(original);
                }
            }
        }
        var html = "";

        //预约发货/预约送达
        if (row.PlatformType != "Jingdong" && commonModule.HasTag(tags, 'appointment_ship_time', 'OrderItem') || commonModule.HasTag(tags, 'appointment_receipt_time', 'OrderItem')) {
            //debugger;
            var msgDivHtml = '<div class="tooltip-content">';
            var tagValue = commonModule.FirstTagValue(tags, "appointment_ship_time", "OrderItem");
            if (tagValue) {
                msgDivHtml += '预约发货时间：' + new Date(tagValue).Format("yyyy-MM-dd") + '<br>';
            }

            var earliestValue = undefined;
            if (tags && tags.length > 0) {
                for (var i = 0; i < tags.length; i++) {
                    var tag = tags[i];
                    if (tag.Tag == "appointment_receipt_time" && tag.TagType == "OrderItem") {
                        if (!earliestValue)
                            earliestValue = tag.TagValue;
                        else if (new Date(earliestValue) > new Date(tag.TagValue)) {
                            earliestValue = tag.TagValue;
                        }
                    }
                }
            }
            if (earliestValue) {
                msgDivHtml += '预约送达时间：' + new Date(earliestValue).Format("yyyy-MM-dd hh:mm:ss");
            }
            html += '<div class="tagStatus tooltip">预约发货' + msgDivHtml + '</div></div>';
        }

        //京东预约发货
        if (row.PlatformType == "Jingdong" && commonModule.HasTag(tags, 'appointment_ship_time', 'Order')) {
            var msgDivHtml = '<div class="tooltip-content">';
            msgDivHtml += '请按照平台规则发货，未按照平台规则发货可能会导致平台处罚。<br>';
            if (fxUserId == row.PurchaseSourceFxUserId) {
                msgDivHtml += '<button class="layui-btn layui-bg-blue layui-btn-xs" onclick="orderPrintModule.showJingdongShipSetting();">京东预约发货设置</button>';
            }
            html += '<div class="tagStatus tooltip">预约发货' + msgDivHtml + '</div></div>';
        }
        //淘大店
        if (commonModule.HasTag(tags, 'tao_big_Shop', 'OrderItem')) {
            //debugger;
            html += '<div class="tagStatus tooltip">官方店</div>';
        }

        // 24小时发货
        if (commonModule.HasTag(tags, 'b_i24h', 'OrderItem')) {
            //debugger;

            html += '<div class="tagStatus tooltip">24小时发货订单</div>';
        }

        //优先发货
        if (commonModule.HasTag(tags, 'remind_shipment', 'Order') || commonModule.HasTag(tags, 'PriorityDelivery', 'Order')) {
            if (row.PlatformType == "KuaiShou" || row.PlatformType == "Taobao")
                html += '<span class="tarTxt tarTxt05" style="padding-right:3px;" title="该订单为买家催发货订单，请优先打印发货">催发货</span>';
            if (row.PlatformType == "TouTiao")
                html += '<span class="tarTxt tarTxt05" style="padding-right:3px;" title="优先发货">优先发货</span>';
        }

        //快手标签
        if (row.PlatformType == "KuaiShou") {
            if (commonModule.HasTag(tags, 'Kuaishou_orderPiecingGroup', 'Order')) {
                html += '<i class="tarTxt tarTxt03 hover popoverCommon" onclick="commonModule.clickPopover.bind(this)()">拼<span class="popoverCommon-warn">该订单为拼团单</span></i>';
                row.IsPinTuanOrder = true;
            }
            // 顺丰包邮
            if (commonModule.HasTag(tags, 'sf_free_shipping', 'Order')) {
                var msgDivHtml = '<div class="tooltip-content">该商家已开通快手顺丰包邮服务，打标"顺丰包邮"的订单请使用顺丰快递配送至消费者，否则商家会受到平台10元/单的处罚。<a style="color:blue" target="_blank" href="https://docs.qingque.cn/d/home/<USER>">官方公告</a>';
                html += '<div class="tagStatus tooltip">顺丰包邮' + msgDivHtml + '</div></div>';
            }
            if (commonModule.HasTag(tags, 'Present_Order', 'OrderItem')) {
                html += '<div class="tagStatus tooltip" title="送礼单：该订单为快手送礼订单，请仔细检查商品后发货\n注：收礼人未填写收货信息前送礼类订单无法正常显示和打单发货">礼物订单</div>';
            }
        }
        
        // 淘宝标签
        if (row.PlatformType === "Taobao") {
            // 顺丰包邮/上门
            var msgDivHtml = '<div class="tooltip-content">承诺顺丰包邮的商品必须使用顺丰快递进行打单发货，若使用其他物流商进行发货，将会向消费者以红包的形式进行赔付(按商品实际成交金额的5%，单笔交易最低不少于5元，不高于50元的红包)。</a>';
            if (commonModule.HasTag(tags, 'sf_free_shipping', 'Order')) {
                html += '<div class="tagStatus tooltip">顺丰包邮' + msgDivHtml + '</div></div>';
            }
            if (commonModule.HasTag(tags, 'sf_door_shipping', 'Order')) {
                html += '<div class="tagStatus tooltip">顺丰上门' + msgDivHtml + '</div></div>';
            }
            if (commonModule.HasTag(tags, 'TaoBaoPreOrder', 'Order')) {
                html += '<div class="tagStatus tooltip">预售订单</div>';
            }
        }

        //抖店赠品后发标签
        if (row.PlatformType == "TouTiao") {
            // 赠品后发
            if (commonModule.HasTag(tags, 'gift_late_delivery', 'Order')) {
                var msgDivHtml = '<div class="tooltip-content">该订单为抖店赠品后发订单，赠品可在主商品发货后五天内发货。<a style="color:blue" target="_blank" href="https://docs.qingque.cn/d/home/<USER>">官方公告</a>';
                html += '<div class="tagStatus tooltip">赠品后发' + msgDivHtml + '</div></div>';
            }
        }

        //1688 分销订单渠道
        if (row.PlatformType == "Alibaba" && commonModule.HasTag(tags, 'OutChannel', 'Order')) {
            var isExitTagVals = [];
            if (row.IsNeedPrint == 0) {
                //html += '<span class="tagStatus tagDanger tooltip">无需打印<span class="tooltip-content">该订单无需打印，由下游客户代发采购，在您打印下游分销订单发货后，该订单会自动发货</span></span>';
            }
            else {
                for (var i = 0; i < tags.length; i++) {
                    var tag = tags[i];
                    if (tag.Tag == "OutChannel" && isExitTagVals.indexOf(tag.TagValue) == -1) {
                        var tagName = "其他分销单";
                        var tooltip = "请优先使用菜鸟电子面单打单，使用其他电子面单会消耗您的店铺解密额度";
                        var fromEncryptOrder = tag.TagValue.indexOf('_plaintext') == -1;
                        if (tag.TagValue.indexOf("thyny") != -1 || tag.TagValue.indexOf("tm") != -1 || tag.TagValue.indexOf("taote") != -1 || tag.TagValue.indexOf("c2m") != -1) {
                            if (tag.TagValue.indexOf("thyny") != -1) {
                                tagName = "淘宝分销单";
                            }
                            if (tag.TagValue.indexOf("c2m") != -1) {
                                tagName = "淘工厂分销单";
                            }
                            if (tag.TagValue.indexOf("tm") != -1) {
                                tagName = "天猫分销单";
                            }
                            if (tag.TagValue.indexOf("taote") != -1) {
                                tagName = "淘特分销单";
                            }

                            if (fromEncryptOrder) {
                                tooltip = "<p class='tooltip-content-title'>密文分销单</p>该订单为淘系代发采购单，需使用对应菜鸟面单打印发货";
                            } else {
                                tooltip = "<p class='tooltip-content-title'>明文分销单</p>该订单为淘系代发采购单，需使用对应菜鸟面单打印发货";
                            }
                        }
                        else if (tag.TagValue.indexOf("jingdong") != -1) {
                            tagName = "京东分销单";
                            if (fromEncryptOrder) {
                                tooltip = "<p class='tooltip-content-title'>密文分销单</p>该订单为京东平台代发采购单，需使用对应京东无界面单打印发货";
                            } else {
                                tooltip = "<p class='tooltip-content-title'>明文分销单</p>该订单为京东平台代发采购单，需使用对应京东无界面单打印发货";
                            }
                        }
                        else if (tag.TagValue.indexOf("jingdong") != -1) {
                            if (fromEncryptOrder) {
                                tagName = "京东密文";
                                tooltip = "该订单为京东平台代发采购单，需使用对应京东面单打印发货";
                            } else {
                                tagName = "京东明文";
                                tooltip = "该订单为京东平台明文分销单，请使用菜鸟电子面单取号，如果使用其他电子面单，会消耗您的店铺解密额度，请谨慎操作";
                            }
                        }
                        else if (tag.TagValue.indexOf("pinduoduo") != -1) {
                            tagName = "拼多多分销单";
                            if (fromEncryptOrder) {
                                tooltip = "<p class='tooltip-content-title'>密文分销单</p>该订单为拼多多平台代发采购单，需使用对应拼多多面单打印发货";
                            } else {
                                tooltip = "<p class='tooltip-content-title'>明文分销单</p>该订单为拼多多平台代发采购单，需使用对应拼多多面单打印发货";
                            }
                        }
                        else if (tag.TagValue == "youzan") { tagName = "有赞分销单"; }
                        else if (tag.TagValue.indexOf("douyin") != -1) {
                            tagName = "抖音分销单";
                            if (fromEncryptOrder) {
                                tooltip = "<p class='tooltip-content-title'>密文分销单</p>该订单为抖音平台代发采购单，需使用对应抖音面单打印发货";
                            } else {
                                tooltip = "<p class='tooltip-content-title'>明文分销单</p>该订单为抖音平台代发采购单，需使用对应抖音面单打印发货";
                            }
                        }
                        else if (tag.TagValue.indexOf("kuaishou") != -1) {
                            tagName = "快手分销单";
                            if (fromEncryptOrder) {
                                tooltip = "<p class='tooltip-content-title'>密文分销单</p>该订单为快手平台代发采购单，需使用对应快手面单打印发货";
                            } else {
                                tooltip = "<p class='tooltip-content-title'>明文分销单</p>该订单为快手平台代发采购单，需使用对应快手面单打印发货";
                            }
                        }
                        else if (tag.TagValue.indexOf("weixin") != -1) {
                            tagName = "视频号分销单";
                            if (fromEncryptOrder) {
                                tooltip = "<p class='tooltip-content-title'>密文分销单</p>该订单为视频号平台代发采购单，需使用对应视频号面单打印发货";
                            } else {
                                tooltip = "<p class='tooltip-content-title'>明文分销单</p>该订单为视频号平台代发采购单，需使用对应视频号面单打印发货";
                            }
                        }
                        else if (tag.TagValue == "siku") { tagName = "寺库分销单"; }
                        else if (tag.TagValue == "meituan") { tagName = "美团团好货分销单"; }
                        else if (tag.TagValue.indexOf("xiaohongshu") != -1) {
                            tagName = "小红书分销单";
                            if (fromEncryptOrder) {
                                tooltip = "<p class='tooltip-content-title'>密文分销单</p>该订单为小红书平台代发采购单，需使用对应小红书面单打印发货";
                            } else {
                                tooltip = "<p class='tooltip-content-title'>明文分销单</p>该订单为小红书平台代发采购单，需使用对应小红书面单打印发货";
                            }
                        }
                        else if (tag.TagValue == "dangdang") { tagName = "当当分销单"; }
                        else if (tag.TagValue == "suning") { tagName = "苏宁分销单"; }
                        else if (tag.TagValue == "davdian") { tagName = "大V店分销单"; }
                        else if (tag.TagValue == "xingyun") { tagName = "行云分销单"; }
                        else if (tag.TagValue == "miya") { tagName = "蜜芽分销单"; }
                        else if (tag.TagValue == "boluo") { tagName = "菠萝派分销单"; }
                        else if (tag.TagValue == "kuajing") { tagName = "跨境分销单"; }
                        else if (tag.TagValue == "offlinestore") { tagName = "线下门店分销单"; }
                        else if (tag.TagValue == "paipai") { tagName = "拍拍店分销单"; }
                        else if (tag.TagValue == "amazon") { tagName = "亚马逊分销单"; }
                        else if (tag.TagValue == "yhd") { tagName = "一号店分销单"; }
                        else if (tag.TagValue == "ecshop") { tagName = "ECshop分销单"; }
                        else if (tag.TagValue == "taogx") { tagName = "淘供销分销单"; }
                        else if (tag.TagValue == "vipshop") { tagName = "唯品会分销单"; }
                        else if (tag.TagValue == "gome") { tagName = "国美分销单"; }
                        else if (tag.TagValue == "jumei") { tagName = "聚美优品分销单"; }
                        else if (tag.TagValue == "meilishuo") { tagName = "美丽说分销单"; }
                        else if (tag.TagValue == "mogujie") { tagName = "蘑菇街分销单"; }
                        else if (tag.TagValue == "zhe888") { tagName = "折888分销单"; }
                        else if (tag.TagValue == "aliexpress") { tagName = "速卖通分销单"; }
                        else if (tag.TagValue == "beibei") { tagName = "贝贝网分销单"; }
                        else if (tag.TagValue == "weimob") { tagName = "微盟分销单"; }
                        else if (tag.TagValue == "mengdian") { tagName = "萌店分销单"; }
                        else if (tag.TagValue == "cuntao") { tagName = "村淘分销单"; }
                        else if (tag.TagValue == "feiniu") { tagName = "飞牛分销单"; }
                        else if (tag.TagValue == "cucujie") { tagName = "楚楚街分销单"; }
                        else if (tag.TagValue == "Higo") { tagName = "Higo分销单"; }
                        else if (tag.TagValue == "juanpi") { tagName = "卷皮分销单"; }
                        else if (tag.TagValue == "renrendian") { tagName = "人人店分销单"; }
                        else if (tag.TagValue == "duoniu") { tagName = "多牛分销单"; }
                        else if (tag.TagValue == "kaola") { tagName = "考拉分销单"; }
                        else if (tag.TagValue == "meitunmama") { tagName = "美囤妈妈分销单"; }
                        else if (tag.TagValue == "ichuanyi") { tagName = "穿衣助手分销单"; }
                        else if (tag.TagValue == "vipshopjit") { tagName = "唯品会JIT分销单"; }
                        else if (tag.TagValue == "xiachufang") { tagName = "下厨房分销单"; }
                        else if (tag.TagValue == "rongegou") { tagName = "融e购分销单"; }
                        else if (tag.TagValue == "fanli") { tagName = "返利网分销单"; }
                        else if (tag.TagValue == "jingdongziying") { tagName = "京东自营分销单"; }
                        else if (tag.TagValue == "weifenxiao") { tagName = "微分销分销单"; }
                        else if (tag.TagValue == "yangmatou") { tagName = "洋码头分销单"; }
                        else if (tag.TagValue == "waimaoyamaxun") { tagName = "外贸亚马逊分销单"; }
                        else if (tag.TagValue == "yibei") { tagName = "易贝分销单"; }
                        else if (tag.TagValue == "huanqiubushou") { tagName = "环球捕手分销单"; }
                        else if (tag.TagValue == "fengye") { tagName = "枫页分销单"; }
                        else if (tag.TagValue == "xinhuanqiubushou") { tagName = "环球捕手分销单"; }
                        else if (tag.TagValue == "yunjiweidian") { tagName = "云集微店分销单"; }
                        else if (tag.TagValue == "haoyiku") { tagName = "好衣库分销单"; }
                        else if (tag.TagValue == "meiriyitao") { tagName = "每日一淘分销单"; }
                        else if (tag.TagValue == "haiziwang") { tagName = "孩子王分销单"; }
                        else if (tag.TagValue == "xiaomiyoupin") { tagName = "小米有品分销单"; }
                        else if (tag.TagValue == "shuaishuaibaobao") { tagName = "甩甩宝宝分销单"; }
                        else if (tag.TagValue == "mengtui") { tagName = "萌推分销单"; }
                        else if (tag.TagValue == "koubei") { tagName = "口碑分销单"; }
                        else if (tag.TagValue == "maibangbang") { tagName = "卖帮帮分销单"; }
                        else if (tag.TagValue == "du") { tagName = "毒分销单"; }
                        else if (tag.TagValue == "dalingjia") { tagName = "达令家分销单"; }
                        else if (tag.TagValue == "wanwuxinxuan") { tagName = "万物心选分销单"; }
                        else if (tag.TagValue == "yueyangdianpu") { tagName = "越洋电铺分销单"; }
                        else if (tag.TagValue == "haosigi") { tagName = "好食期分销单"; }
                        else if (tag.TagValue == "weilaijishi") { tagName = "未来集市分销单"; }
                        else if (tag.TagValue == "yangcong") { tagName = "洋葱分销单"; }
                        else if (tag.TagValue == "yitiao") { tagName = "一条分销单"; }
                        else if (tag.TagValue == "mokuaixingxuan") { tagName = "魔快星选分销单"; }
                        else if (tag.TagValue == "xiugoushangcheng") { tagName = "秀购商城分销单"; }
                        else if (tag.TagValue == "yunshanghulian") { tagName = "云尚互联分销单"; }
                        else if (tag.TagValue == "weiyian") { tagName = "微一案分销单"; }
                        else if (tag.TagValue == "meiriyouxian") { tagName = "每日优鲜分销单"; }
                        else if (tag.TagValue == "heijin") { tagName = "黑金分销单"; }
                        else if (tag.TagValue == "youzanlingshou") { tagName = "有赞零售分销单"; }
                        else if (tag.TagValue == "lama") { tagName = "辣妈分销单"; }
                        else if (tag.TagValue == "gongzhugou") { tagName = "公主购分销单"; }
                        else if (tag.TagValue == "renrendiandailiduan") { tagName = "人人店代理端分销单"; }
                        else if (tag.TagValue == "wangyiyanxuan") { tagName = "网易严选分销单"; }
                        else if (tag.TagValue == "aomaijia") { tagName = "奥买家分销单"; }
                        else if (tag.TagValue == "yaoshibang") { tagName = "药师帮分销单"; }
                        else if (tag.TagValue == "dulib2c") { tagName = "独立B2C分销单"; }
                        else if (tag.TagValue == "aiyue") { tagName = "爱跃分销单"; }
                        else if (tag.TagValue == "haipaike") { tagName = "海拍客分销单"; }
                        else if (tag.TagValue == "weimengxinyun") { tagName = "微盟新云分销单"; }
                        else if (tag.TagValue == "wish") { tagName = "wish分销单"; }
                        else if (tag.TagValue == "lazada") { tagName = "Lazada分销单"; }
                        else if (tag.TagValue == "shopee") { tagName = "shopee分销单"; }
                        else if (tag.TagValue == "dunhuang") { tagName = "敦煌分销单"; }
                        else if (tag.TagValue == "shopify") { tagName = "shopify分销单"; }
                        else if (tag.TagValue == "zhiyu") { tagName = "执御分销单"; }
                        else if (tag.TagValue == "jindie") { tagName = "金蝶分销单"; }
                        else if (tag.TagValue == "Joom") { tagName = "J00M分销单"; }
                        else if (tag.TagValue == "fengile") { tagName = "分期乐分销单"; }
                        else if (tag.TagValue == "meilihui") { tagName = "魅力惠分销单"; }
                        else if (tag.TagValue == "jiuxianwang") { tagName = "酒仙网分销单"; }
                        else if (tag.TagValue == "kongfuzi") { tagName = "孔夫子分销单"; }
                        else if (tag.TagValue == "shanrong") { tagName = "善融分销单"; }
                        else if (tag.TagValue == "zalora") { tagName = "zalora分销单"; }
                        else if (tag.TagValue == "quchenshi") { tagName = "屈臣氏分销单"; }
                        else if (tag.TagValue == "yahoo") { tagName = "yahoo分销单"; }
                        else if (tag.TagValue == "rakuten") { tagName = "rakuten分销单"; }
                        else if (tag.TagValue == "xindan") { tagName = "新蛋分销单"; }
                        else if (tag.TagValue == "tengxunyun") { tagName = "腾讯云分销单"; }
                        else if (tag.TagValue == "xituan") { tagName = "喜团分销单"; }
                        else if (tag.TagValue == "fenxiao") { tagName = "分销分销单"; }
                        else if (tag.TagValue == "weiyianxinlingshou") { tagName = "微一案新零售分销单"; }
                        else if (tag.TagValue == "shunliandongli") { tagName = "顺联动力分销单"; }
                        else if (tag.TagValue == "voyageone") { tagName = "voyageone分销单"; }
                        else if (tag.TagValue == "xshoppy") { tagName = "xshoppy分销单"; }
                        else if (tag.TagValue == "jingdongtaiguo") { tagName = "京东泰国分销单"; }
                        else if (tag.TagValue == "tophatter") { tagName = "tophatter分销单"; }
                        else if (tag.TagValue == "fenxiaowang") { tagName = "分销王分销单"; }
                        else if (tag.TagValue == "yingtu") { tagName = "映兔分销单"; }
                        else if (tag.TagValue == "qingguisanhaoxian") { tagName = "轻轨二号线分销单"; }
                        else if (tag.TagValue == "dingxiangyisheng") { tagName = "丁香医生分销单"; }
                        else if (tag.TagValue == "ele") { tagName = "liao分销单"; }
                        else if (tag.TagValue == "me") { tagName = "饿了么分销单"; }
                        else if (tag.TagValue == "biyao") { tagName = "必要分销单"; }
                        else if (tag.TagValue == "alijiankang") { tagName = "阿里健康分销单"; }
                        else if (tag.TagValue == "xiaomangdianshang") { tagName = "小芒电商分销单"; }
                        else if (tag.TagValue == "zhenkunhang") { tagName = "震坤行分销单"; }
                        else if (tag.TagValue == "kameila") { tagName = "卡美啦分销单"; }
                        else if (tag.TagValue == "huaweishangcheng") { tagName = "华为商城分销单"; }
                        else if (tag.TagValue == "duodian") { tagName = "多点分销单"; }
                        else if (tag.TagValue == "yili") { tagName = "伊利分销单"; }
                        else if (tag.TagValue == "lingshoutong") { tagName = "零售通分销单"; }
                        else if (tag.TagValue == "xiaoepinpin") { tagName = "小鹅拼拼分销单"; }
                        else if (tag.TagValue == "meituanyiyaojiankang") { tagName = "美团医药健康分销单"; }
                        else if (tag.TagValue == "kuaituantuan") { tagName = "快团团分销单"; }
                        else if (tag.TagValue == "shangpa") { tagName = "商派分销单"; }
                        else if (tag.TagValue == "youlewang") { tagName = "邮乐网分销单"; }
                        else if (tag.TagValue == "xiaogongshu") { tagName = "小红书分销单"; }
                        else if (tag.TagValue == "ebai") { tagName = "饿百分销单"; }
                        else if (tag.TagValue == "zhiboba") { tagName = "直播吧分销单"; }
                        else if (tag.TagValue == "pinganyigianbao") { tagName = "平安壹钱包分销单"; }
                        else if (tag.TagValue == "woerma") { tagName = "沃尔玛分销单"; }
                        else if (tag.TagValue == "tianyigou") { tagName = "天翼购分销单"; }
                        else if (tag.TagValue == "jinribaotuan") { tagName = "今日爆团分销单"; }
                        else if (tag.TagValue == "yunhuoyouxuan") { tagName = "云货优选分销单"; }
                        else if (tag.TagValue == "iubaozan") { tagName = "聚宝赞分销单"; }
                        else if (tag.TagValue == "tuanhaohuo") { tagName = "团好货分销单"; }
                        else if (tag.TagValue == "aikucun") { tagName = "爱库存分销单"; }
                        else if (tag.TagValue == "xiyin") { tagName = "希音分销单"; }
                        else if (tag.TagValue == "duxiaodian") { tagName = "度小店分销单"; }
                        else if (tag.TagValue == "vovapingtai") { tagName = "vova平台分销单"; }
                        else if (tag.TagValue == "buluoguanjia") { tagName = "部落管家分销单"; }
                        else if (tag.TagValue == "qunjielong") { tagName = "群接龙分销单"; }
                        else if (tag.TagValue == "dewu") { tagName = "得物分销单"; }
                        else if (tag.TagValue == "bzhanhuiyuangou") { tagName = "B站会员购分销单"; }
                        else if (tag.TagValue == "doudianchangjiadaifa") { tagName = "抖店厂家代发分销单"; }
                        else if (tag.TagValue == "weidian") { tagName = "微店分销单"; }
                        else if (tag.TagValue == "guomeizhenkuaile") { tagName = "国美真快乐分销单"; }
                        else if (tag.TagValue == "dingdangkuaiyao") { tagName = "叮当快药分销单"; }
                        else if (tag.TagValue == "shopee") { tagName = "shopee分销单"; }
                        else if (tag.TagValue == "weinihaigou") { tagName = "唯妮海购分销单"; }
                        else if (tag.TagValue == "yidinghuo") { tagName = "易订货分销单"; }
                        else if (tag.TagValue == "tengxunhuiju") { tagName = "腾讯惠聚分销单"; }
                        else if (tag.TagValue == "jingdonggongxiaopingtai") { tagName = "京东供销分销单"; }
                        html += '<span class="tagStatus tagDanger tooltip">' + tagName + '<span class="tooltip-content">' + tooltip + '</span></span>';
                        isExitTagVals.push(tag.TagValue);
                    }
                }
            }
        }

        //《无需打印》标签处理
        if (row.IsNeedPrint == 0) {
            row.DownFxUserId = -1;
            if (row.SecondSubOrders != undefined && row.SecondSubOrders.length > 0) {
                row.DownFxUserId = row.SecondSubOrders[0].DownFxUserId;
            }
            else if (row.SubOrders != undefined && row.SubOrders.length > 0) {
                row.DownFxUserId = row.SubOrders[0].DownFxUserId;
            }

            //厂家角色提示
            if (row.PlatformType == "Alibaba" || row.DownFxUserId == 0) {

                html += '<span class="tagStatus tagDanger tooltip" style="margin-left: 5px;"><i class="hover">(1688收单)无需打印</i><span class="tooltip-content" style="width:640px;padding:20px;"><span style="margin-bottom:5px;display:block;">该商家预付款打印发货已设置:</span><span class="sColor">不在店管家系统打印发货，使用其他打印软件打印1688订单密文单，打印发货后自动回传到商家后台 <i class="dColor hover" style="margin-left:25px" onclick="orderTableBuilder.openUrlToMyAgent(\'' + row.PurchaseSourceAgentMobile + '\')">修改发货设置》</i></span></span></span>';
            }
            else {
                //商家角色提示
                //不需要2023.12.07
                //html += '<span class="tagStatus tagDanger tooltip" style="margin-left: 5px;">预付单(无需打印)<span class="tooltip-content sColor"  style="padding:20px;">该订单无需打印，由下游客户代发采购，在您打印下游分销订单发货后，该订单会自动发货</span></span>';
            }
        }

        //拼多多标签
        if (row.PlatformType == "Pinduoduo" && row.PddConsolidate) {
            if (row.PddConsolidate == "0") {
                html += gxTarRender("中国香港集运", "xg", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "1") {
                html += gxTarRender("中国新疆中转", "xj", "https://mms.pinduoduo.com/daxue/detail?courseId=5366", row.PlatformOrderId);
            } else if (row.PddConsolidate == "2") {
                html += gxTarRender("哈萨克斯坦集运", "hskst", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "3") {
                html += gxTarRender("中国西藏中转", "xz", "https://mms.pinduoduo.com/daxue/detail?courseId=5366", row.PlatformOrderId);
            } else if (row.PddConsolidate == "5") {
                html += gxTarRender("日本集运", "rb", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "6") {
                html += gxTarRender("中国台湾集运", "tw", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "7") {
                html += gxTarRender("韩国集运", "hg", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "8") {
                html += gxTarRender("新加坡集运", "xjp", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "9") {
                html += gxTarRender("马来西亚集运", "mlxy", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "10") {
                html += gxTarRender("泰国集运", "tg", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "11") {
                html += gxTarRender("越南集运", "yn", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "12") {
                html += gxTarRender("吉尔吉斯斯坦集运", "jejsst", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "13") {
                html += gxTarRender("乌兹别克斯坦集运", "wzbkst", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "14") {
                html += gxTarRender("中国甘肃中转", "xj", "https://mms.pinduoduo.com/daxue/detail?courseId=5366", row.PlatformOrderId);
            } else if (row.PddConsolidate == "15") {
                html += gxTarRender("中国内蒙古中转", "xj", "https://mms.pinduoduo.com/daxue/detail?courseId=5366", row.PlatformOrderId);
            } else if (row.PddConsolidate == "16") {
                html += gxTarRender("中国宁夏中转", "xj", "https://mms.pinduoduo.com/daxue/detail?courseId=5366", row.PlatformOrderId);
            } else if (row.PddConsolidate == "17") {
                html += gxTarRender("中国青海中转", "xj", "https://mms.pinduoduo.com/daxue/detail?courseId=5366", row.PlatformOrderId);
            } else if (row.PddConsolidate == "18") {
                html += gxTarRender("中国澳门集运", "zgamjy", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "19") {
                html += gxTarRender("柬埔寨集运", "jpzjy", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "20") {
                html += gxTarRender("老挝集运", "lwjy", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "21") {
                html += gxTarRender("塔吉克斯坦集运", "tjkstjy", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "22") {
                html += gxTarRender("亚美尼亚集运", "ymnyjy", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "23") {
                html += gxTarRender("格鲁吉亚集运", "gljyjy", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            } else if (row.PddConsolidate == "24") {
                html += gxTarRender("蒙古集运", "mgjy", "https://mms.pinduoduo.com/daxue/detail?courseId=5460", row.PlatformOrderId);
            }
        }
        //拼多多承诺送达标签
        if (row.PlatformType == "Pinduoduo") {
            //如果有拼多多的承诺送达标签，则取最小值展示
            if (commonModule.HasTag(tags, 'pdd_bought_from_vegetable', 'OrderItem')) {
                var tagValue = "";
                if (tags && tags.length > 0) {
                    var minValue = "";
                    for (var i = 0; i < tags.length; i++) {
                        var tag = tags[i];
                        if (tag.Tag == 'pdd_bought_from_vegetable') {
                            var currDate = new Date(tag.TagValue);
                            if (minValue == "") {
                                minValue = currDate;
                            }
                            else if (currDate < minValue) {
                                minValue = currDate;
                            }
                        }
                    }
                    //对应的标签值是承诺送达时间，取订单标签里的最小值,yyyy-MM-dd HH:mm:ss
                    if (minValue != "") {
                        tagValue = minValue.Format("yyyy-MM-dd hh:mm:ss");
                    }
                }
                var msgDivHtml = '';
                msgDivHtml = '<div class="tooltip-content">';
                msgDivHtml += '该订单承诺送达时间：' + tagValue + '<br>';
                msgDivHtml += '该订单已约定承诺送达，承诺送达类订单无确切发货时间，商家需要<br>';
                msgDivHtml += '按照订单提供的承诺送达时间，将货品送达消费者收货地址，未按时<br>';
                msgDivHtml += '送达会造成客诉风险。';
                msgDivHtml += '</div>';
                html += '<div class="tagStatus tooltip">承诺送达' + msgDivHtml + '</div>';
            }
        }

        //异常单，前端不展示
        //if (fxPageType != 1 && commonModule.HasTag(tags, 'ExceptionOrder', 'LogicOrder')) {
        //    html += '<i class="tarTxt tarTxt03" style="margin-top:3px;" title="该订单需人工处理审核，请您查看订单上的小标签，按提示操作对应解决方案。">异常单</i>';
        //}
        //抖店这个时间前的订单密文信息有变化，不能识别为地址变更
        if (row.ReceiverIsChange && row.PlatformType == "TouTiao" && row.OrderTime < '2024-12-25')
            row.ReceiverIsChange = false;
        if (row.ReceiverIsChange || commonModule.HasTag(tags, 'receiver_change', 'LogicOrder')) {
            if (isWxVideoExt)
                html += '<i class="tarTxt tarTxt03" style="margin-top:3px;" title="平台已更新虚拟号信息，为保证快递能正常联系到消费者，请您重新打印快递面单，重新打印后，该标签会取消展示">异常单</i>';
            else {
                // var msgDivHtml = '<div class="tooltip-content">';
                // msgDivHtml += '有什么影响？<br>';
                // msgDivHtml += '1、打印快递单之后，收件人地址发生变化。平台为防止出现原快递单收件人地址和当前订单信息不符，必而造成售后纠纷，发货前对该类订单做拦截校验<br>';
                // msgDivHtml += '2、请勿线下先发出包裹，未及时在系统操作发货动作导致发货后，消费者修改收件地址，使得收件人地址信息不一致，导致无法发货<br>';
                // msgDivHtml += '怎么解决？<br>';
                // msgDivHtml += '<b>重新打印快递单，并使用新打印的快递单发货。</b>';
                // msgDivHtml += '</div>';
                // html += '<div  class="tagStatus tarTxt03 tooltip">地址变更' + msgDivHtml + '</div>';
                var tagValue = "";
                var msgDivHtml = '';
                msgDivHtml = '<div class="tooltip-content">';
                msgDivHtml += '<b>有什么影响？<br></b>';
                msgDivHtml += '<span>打印快递单之后，收件人地址发生变化。为防止出现原快递单收件人电话、地址<span><br>和当前订单信息不符，从而造成售后纠纷，发货前对该类订单做拦截校验。<br>';
                msgDivHtml += '<b>怎么解决？<br></b>';
                msgDivHtml += '回收当前已打印的旧单号，使用新单号重新打印发货。';
                msgDivHtml += '</div>';
                html += '<div class="tagStatus tarTxt03 tooltip">地址变更' + msgDivHtml + '</div>';
            }
        }
        // // 地址变更 （拼多多）
        // if ( common.PlatformType == "Pinduoduo" && commonModule.HasTag(tags, 'receiver_change', 'Order')) {
        //     var tagValue = "";
        //     var msgDivHtml = '';
        //     msgDivHtml = '<div class="tooltip-content">';
        //     msgDivHtml += '<b>有什么影响？<br></b>';
        //     msgDivHtml += '<span>打印快递单之后，收件人地址发生变化。为防止出现原快递单收件人电话、地址<span><br>和当前订单信息不符，从而造成售后纠纷，发货前对该类订单做拦截校验。<br>';
        //     msgDivHtml += '<b>怎么解决？<br></b>';
        //     msgDivHtml += '回收当前已打印的旧单号，使用新单号重新打印发货。';
        //     msgDivHtml += '</div>';
        //     html += '<div class="tagStatus tarTxt03 tooltip">地址变更' + msgDivHtml + '</div>';
        // }
        //debugger;
        //if (commonModule.HasTag(tags, 'AliFxPurchaseException', 'LogicOrder') && (row.RefundStatus == undefined || (row.RefundStatus != "waitselleragree" && row.RefundStatus != "refundsuccess" && row.RefundStatus != "WAIT_SELLER_AGREE" && row.RefundStatus != "REFUND_SUCCESS"))) {
        //    html += '<i class="tarTxt tarTxt03" style="margin-top:3px;" title="该订单买家已申请退款">已申请退款</i>';
        //}

        //抖店送礼单
        if (row.PlatformType == "TouTiao" && commonModule.HasTag(tags, 'give_gift', 'OrderItem')) {
            html += '<div class="tagStatus tooltip" title="送礼单：该订单为抖音送礼订单，该类订单不支持合单操作，请仔细检查商品后发货">礼物订单</div>';
        }

        //淘宝送礼单
        if (fxPageType ==1&&row.PlatformType == "Taobao" && commonModule.HasTag(tags, 'TaobaoGiftOrder', 'OrderItem')) {
            html += '<div class="tagStatus tooltip" title="送礼单：该订单为淘宝礼物订单，请仔细检查商品后发货">礼物订单</div>';
        }

        if (row.PlatformType == "WxVideo" && commonModule.HasTag(tags, 'Present_Order', 'OrderItem')) {
            html += '<div class="tagStatus tooltip" title="该订单为微信礼物订单，请仔细检查商品后发货">礼物订单</div>';
        }

        //京东礼物订单
        if (row.PlatformType == "Jingdong" && commonModule.HasTag(tags, 'Present_Order', 'OrderItem')) {
            var msgDivHtml = '';
            msgDivHtml = '<div class="tooltip-content">';
            msgDivHtml += '礼品订单：该订单为京东送礼订单，请仔细检查商品后发货。送礼功能可<a href="https://helpcenter.jd.com/vender/issue/45623.html" style="color:#3aadff;cursor:pointer" target="_blank">点击查看</a>';
            msgDivHtml += '</div>';
            html += '<div class="tarTxt03 tagStatus tooltip">礼物订单' + msgDivHtml + '</div>';
        }
        // 微信小店-换款中
        if (row.PlatformType == "WxVideo" && commonModule.HasTag(tags, 'ChangeingSku_Order', 'OrderItem')) {
            var changeSkuData = row.OrderTags.find(function (item) {
                return item.Tag === "ChangeingSku_Order";
            });
            // 目标时间
            var estimatedTime = new Date(changeSkuData.TagValue);
            // 加上 24 小时
            var futureDate = new Date(estimatedTime.getTime() + 24 * 60 * 60 * 1000);  // 24小时对应的毫秒数
            // 获取当前时间
            var currentDate = new Date();
            // 计算时间差（毫秒）
            var timeDifference = futureDate - currentDate;
            // 将毫秒转换为小时和分钟
            var hours = Math.floor(timeDifference / (1000 * 60 * 60)); // 计算小时
            if (hours < 0) {
                hours = 0;
            }
            var minutes = Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60)); // 计算分钟
            if (minutes < 0) {
                minutes = 0;
            }
            var countdownTime = hours + " 小时 " + minutes + " 分";
            html += '<div class="tagStatus tarTxt01 tooltip" style="margin-left: 4px;">换款中<div class="tooltip-content"><div style="font-weight: 600;">换款中</div><div>请在 ' + countdownTime + '内处理，超时将自动拒绝，未</div><div>处理订单无法发货。<a class="n-dColor" href="https://store.weixin.qq.com/chengzhang/webdoc/wiki/7486/54a3e53bcac7e2c3/growth_center_manual_for_store" target="_blank">查看换款服务使用指南</a></div></div></div>';
        }
        // 微信小店-已换款
        if (row.PlatformType == "WxVideo" && commonModule.HasTag(tags, 'ChangeSku_Accept_Order', 'OrderItem')) {
            html += '<div class="tagStatus tarTxt02 tooltip" style="margin-left: 4px;">已换款</div>';
        }
        // 微信小店-未同意换款
        if (row.PlatformType == "WxVideo" && commonModule.HasTag(tags, 'ChangeSku_Reject_Order', 'OrderItem')) {
            html += '<div class="tagStatus tarTxt03 tooltip" style="margin-left: 4px;">未同意换款</div>';
        }
        //抖店风控订单
        if (row.PlatformType == "TouTiao" && commonModule.HasTag(tags, 'risk_processing', 'OrderItem')) {
            var msgDivHtml = '';
            msgDivHtml = '<div class="tooltip-content">';
            msgDivHtml += '该订单暂被平台标记为风控订单，24小时内平台会返回风控结果。<br>';
            msgDivHtml += '温馨提示：风控订单不支持打印发货，操作时系统会进行自动拦截。<a href="https://op.jinritemai.com/docs/question-docs/89/4125" class="wu-color-a wu-operate" target="_blank">查看官方公告</a>';
            msgDivHtml += '</div>';
            html += '<div class="tarTxt03 tagStatus tooltip">平台风控' + msgDivHtml + '</div>';
        }
        
        //1688官方仓发订单
        if (row.PlatformType == "Alibaba" && commonModule.HasTag(tags, 'hyperLinkShip', 'OrderItem')) {
            var msgDivHtml = '<div class="tooltip-content">';
            msgDivHtml += '此订单为1688官方仓发订单，由官方托管发货，无需在店管家内打印发货<br><br>';
            msgDivHtml += '说明：<a href="https://peixun.1688.com/space/l2AmoEo9rYgw8zdb/detail/Gl6Pm2Db8D3moaOOTz4MdnMNJxLq0Ee4" target="_blank" style="color: blue;font-size:13px;">什么是1688官方仓发托管服务？</a>';
            msgDivHtml += '</div>';
            html += '<div  class="tagStatus tarTxt01 tooltip">官方仓发' + msgDivHtml + '</div>';
        }
        
        if (commonModule.HasTag(tags, 'Kuaishou_usermodifyaddress', 'OrderItem')) {
            var itTagHtml = '<div class="tagStatus tooltip">申请修改地址中<div class="tooltip-content">买家正在申请修改收件信息，<span style="color: red;">15分钟内未审核将自动拒绝</span>。是否同意买家操作？<br>';
            itTagHtml += '<span style="float:right;">';
            itTagHtml += '<button type="button" class="layui-btn layui-btn-xs layui-btn-normal" style="width: 60px;" onclick="orderPrintModule.KuaiShouAuditOrderAddress(' + row.Index + ',\'agree\')">同意</button>';
            itTagHtml += '&nbsp;';
            itTagHtml += '<button type="button" class="layui-btn layui-btn-xs layui-btn-primary" style="width: 60px;" onclick="orderPrintModule.KuaiShouAuditOrderAddress(' + row.Index + ',\'refuse\')">拒绝</button>';
            itTagHtml += '</span>';
            itTagHtml += '</div></div>';
            html += itTagHtml;
        }
        if (commonModule.HasTag(tags, 'Kuaishou_usermodifyaddress_agree', 'OrderItem')) {
            var msgDivHtml = '';
            var firstTagValue = commonModule.FirstTagValue(tags, "Kuaishou_usermodifyaddress_agree", "OrderItem");
            if (firstTagValue) {
                var tagValueJson = JSON.parse(firstTagValue);
                msgDivHtml = '<div class="tooltip-content">';
                msgDivHtml += '原收件人地址<br>';
                msgDivHtml += tagValueJson.ToName + '，';
                msgDivHtml += tagValueJson.ToMobile + '，';
                msgDivHtml += tagValueJson.ToFullAddress;
                msgDivHtml += '</div>';
            }
            html += '<div class="tagStatus tooltip">已同意修改地址' + msgDivHtml + '</div>';
        }
        if (commonModule.HasTag(tags, 'Kuaishou_usermodifyaddress_refuse', 'OrderItem')) {
            html += '<div class="tagStatus tooltip" title="已拒绝修改地址">已拒绝修改地址</div>';
        }

        if (commonModule.HasTag(tags, 'Kuaishou_priceProtection', 'OrderItem')) {
            html += '<div class="tagStatus tooltip" title="价保">价保</div>';
        }
        if (commonModule.HasTag(tags, 'Kuaishou_refundCash', 'OrderItem')) {
            html += '<div class="tagStatus tooltip" title="退差返现">退差返现</div>';
        }
        if (commonModule.HasTag(tags, 'JingdongPurchase_splitOrder', 'Order')) {
            html += '<div class="tarTxt tarTxt07" title="该订单为拆单代发订单，不会白动回传发货到京东供销平台后台，请到后台手动发货。">拆单代发</div>';
        }
        if (commonModule.HasTag(tags, 'jd_delivery', 'Order')) {
            html += '<div class="tarTxt tarTxt03 tooltip" title="" style="height: 20px;margin-right: 8px;">京东配送<div class="tooltip-content">京东配送订单发货时需使用京东快递，</br>否则会发货失败。</div></div>';
        }
        if (commonModule.HasTag(tags, 'JingdongPurchase_unreturned', 'Order')) {
            html += '<div class="tarTxt tarTxt07 tooltip" style="height: 20px;margin-right: 8px;">未回传<div class="tooltip-content">该订单为京东供销平台采购单，该类型订单不支持拆单发货。</br>由于您将该订单推送给了不同厂家且选择了发货模式为"不回传到店铺后台"，该订单请手动到后台发货。</div></div>';
        }
        //开放平台自有商城的平台单标签
        if (commonModule.HasTag(tags, 'OwnShop_EncryptOrder', 'Order')) {
            var firstTagValue = commonModule.FirstTagValue(tags, "OwnShop_EncryptOrder", "Order");
            if (firstTagValue == 'TouTiao') {
                html += '<div class="tarTxt tarTxt03 tooltip" title="" style="height: 20px;margin-right: 8px;">外部抖音订单<div class="tooltip-content">此订单为第三方系统推送过来的抖音平台订单，请使用抖音电子面单打单发货。</div></div>';
            } else if (firstTagValue == 'KuaiShou') {
                html += '<div class="tarTxt tarTxt03 tooltip" title="" style="height: 20px;margin-right: 8px;">外部快手订单<div class="tooltip-content">此订单为第三方系统推送过来的快手平台订单，请使用快手电子面单打单发货。</div></div>';
            } else if (firstTagValue == 'Taobao') {
                html += '<div class="tarTxt tarTxt03 tooltip" title="" style="height: 20px;margin-right: 8px;">外部淘宝订单<div class="tooltip-content">此订单为第三方系统推送过来的淘宝平台订单，请使用菜鸟电子面单打单发货。</div></div>';
            } else if (firstTagValue == 'Pinduoduo') {
                html += '<div class="tarTxt tarTxt03 tooltip" title="" style="height: 20px;margin-right: 8px;">外部拼多多订单<div class="tooltip-content">此订单为第三方系统推送过来的拼多多平台订单，请使用拼多多电子面单打单发货。</div></div>';
            } else if (firstTagValue == 'XiaoHongShu') {
                html += '<div class="tarTxt tarTxt03 tooltip" title="" style="height: 20px;margin-right: 8px;">外部小红书订单<div class="tooltip-content">此订单为第三方系统推送过来的小红书平台订单，请使用小红书电子面单打单发货。</div></div>';
            }
        }
        //订单项标签赋值
        var subOrder_tags_addfunc = function (suorder, stag) {
            if (suorder.OrderItemTags == undefined)
                suorder.OrderItemTags = [];
            suorder.OrderItemTags.push(stag);
        }
        //所有订单
        if (fxPageType == 1) {
            for (var s = 0; s < row.SecondSubOrders.length; s++) {
                //逻辑单
                var secondSubOrder = row.SecondSubOrders[s];
                secondSubOrder.OrderTagsHtml = table.OrderTagsHtmlFormatter(row, row.PlatformOrderId, tags);
                secondSubOrder.LogicOrderTagsHtml = table.logicOrderTagsHtmlFormatter(secondSubOrder.LogicOrderId, tags, secondSubOrder.DownFxUserId);

                //逻辑单项
                for (var t = 0; t < secondSubOrder.ThressSubOrders.length; t++) {
                    var subOrder = secondSubOrder.ThressSubOrders[t];
                    for (var oi = 0; oi < itemTags.length; oi++) {
                        if (itemTags[oi].OiCode == subOrder.OrderItemCode) {
                            subOrder_tags_addfunc(subOrder, itemTags[oi]);
                        }
                    }
                    //子标签样式
                    subOrder.OrderItemTagsHtml = table.orderItemTagsHtmlFormatter(subOrder.OrderItemTags);
                }
            }
        }
        //打印发货
        else if (fxPageType == 2) {
            html += table.OrderTagsHtmlFormatter(row, null, tags);
            for (var i = 0; i < row.SubOrders.length; i++) {
                var subOrder = row.SubOrders[i];
                for (var oi = 0; oi < itemTags.length; oi++) {
                    if (itemTags[oi].OiCode == subOrder.OrderItemCode) {
                        subOrder_tags_addfunc(subOrder, itemTags[oi]);
                    }
                }
                //子标签样式
                subOrder.OrderItemTagsHtml = table.orderItemTagsHtmlFormatter(subOrder.OrderItemTags);

                //特殊标签，合单情况下需要展示在逻辑单上
                if (subOrder.MergeredOrderId != '' && subOrder.MergeredOrderId != undefined) {
                    subOrder.logicOrderTagsHtml = table.OrderTagsHtmlFormatter(subOrder, subOrder.PlatformOrderId, tags);
                    subOrder.logicOrderTagsHtml += table.OrderTagsHtmlFormatter(subOrder, subOrder.OrderItemCode, tags);
                }
            }
        }
        //线下单
        else if (fxPageType == 3) {
            for (var s = 0; s < row.SecondSubOrders.length; s++) {
                var secondSubOrder = row.SecondSubOrders[s];
                secondSubOrder.OrderTagsHtml = table.OrderTagsHtmlFormatter(row, row.PlatformOrderId, tags);
            }
        }
        //已发货明细
        else if (fxPageType == 4) {
            for (var i = 0; i < row.SendOrders.length; i++) {
                var sendOrder = row.SendOrders[i];
                //平台标签样式
                sendOrder.OrderTagsHtml = table.OrderTagsHtmlFormatter(row, sendOrder.PlatformOrderId, tags);
            }
        }
        return html;
    }

    /** 平台单标签展示样式 OrderTagsHtml*/
    table.OrderTagsHtmlFormatter = function (row, orderId, orderTags) {
        var orderTagsHtml = '';
        var has_tags = function (tagName, tagType) {
            if (orderId) {
                return commonModule.HasTagOiCode(orderTags, orderId, tagName, tagType);
            } else {
                return commonModule.HasTag(orderTags, tagName, tagType);
            }
        }
        var fxPageType = commonModule.FxPageType(); //(1：所有订单，2：待打单页面，3：线下单页面，4：已发货详细)

        //淘宝礼物订单
        if (row.PlatformType == "Taobao" && has_tags('TaobaoGiftOrder', 'OrderItem')) {
            orderTagsHtml += '<div class="tagStatus tooltip" title="送礼单：该订单为淘宝礼物订单，请仔细检查商品后发货">礼物订单</div>';
        }

        //线下单
        if (row.PlatformType == "Virtual" && has_tags('OfflineNoSku', 'Order')) {
            if (fxPageType != 2 || !row.IsMainOrder || orderId != null) {
                orderTagsHtml += '<span class="tagStatus tagDanger tooltip">无商品线下单<span class="tooltip-content">该订单在录入时，关闭了【订单需对账】设置，系统没有强校验商品信息，被标为无商品订单</span></span>';
            }
        }
        // 密文线下单
        if (row.PlatformType == "Virtual" && has_tags('EncryptOfflineOrder', 'Order')) {
            //var showId = orderTags.find(function (item) {
            //    return item.OiCode === orderId;
            //});
            var tagTooltip_content = '';
            if (fxPageType != 2 || !row.IsMainOrder || orderId != null) {
                common.Foreach(orderTags, function (i, item) {
                    if (item.Tag == "EncryptOfflineOrder" && (item.OiCode == orderId || (orderId == null && item.OiCode == row.PlatformOrderId))) {
                        tagTooltip_content = item.TagValue;
                    }
                });
            }
            else if (row.IsMainOrder) {
                common.Foreach(orderTags, function (i, item) {
                    if (item.Tag == "EncryptOfflineOrder") {
                        tagTooltip_content = item.TagValue;
                    }
                });
            }
            if (tagTooltip_content) {
                orderTagsHtml += '<div class="tagStatus tagWarn tooltip" style="z-index: 110;">密文线下单<div class="tooltip-content"><div>关联平台订单编号：' + tagTooltip_content + '</div><div>该订单为手工创建的抖音密文线下单，请使用抖音电子面单打单发货。</div></div></div>';
            }
        }

        //拼多多 承诺送达时间
        if (row.PlatformType == "Pinduoduo" && has_tags('promise_delivery_time', 'Order')) {
            var tagTooltip_content = '';
            if (fxPageType != 2 || !row.IsMainOrder || orderId != null) {
                common.Foreach(orderTags, function (i, item) {
                    if (item.Tag == "promise_delivery_time" && (item.OiCode == orderId || (orderId == null && item.OiCode == row.PlatformOrderId))) {
                        tagTooltip_content = '买家预约送货时间段: ' + item.TagValue + '<br/>';
                    }
                });
            }
            else if (row.IsMainOrder) {
                common.Foreach(orderTags, function (i, item) {
                    if (item.Tag == "promise_delivery_time") {
                        tagTooltip_content += '【' + item.OiCode + '】订单买家预约送货时间段: ' + item.TagValue + '<br/>';
                    }
                });
            }
            if (tagTooltip_content != '') {
                orderTagsHtml += '<span class="tarTxt01 tagStatus tooltip">预约送达<span class="tooltip-content">预约送达：需按照买家预约送达时间将货品送至买家<br/>' + tagTooltip_content + '<a style="color:#3aadff;" href="javascript:orderPrintModule.QueryOrderTagsInputWhere(\'promise_delivery_time\');">查看所有“预约送达”订单</a></span></span>';
            }
        }

        //拼多多 送货上门
        if (row.PlatformType == "Pinduoduo") {
            if (has_tags('home_delivery_door', 'Order'))
                orderTagsHtml += '&nbsp;<span class="tarTxt01 tagStatus tooltip">送货上门<span class="tooltip-content">请按平台规则为买家提供送货上门服务，<a href="https://mms.pinduoduo.com/other/rule?id=1481" target="_blank" style="color:#3aadff;">查看平台规则</a><br/><a style="color:#3aadff;" href="javascript:orderPrintModule.QueryOrderTagsInputWhere(\'home_delivery_door\');">查看所有“送货上门”订单</a></span></span>';
            if (has_tags('direct_mail_activity', 'Order'))
                orderTagsHtml += '&nbsp;<span class="tarTxt01 tagStatus tooltip">直邮活动<span class="tooltip-content">针对该订单的运费，平台会为您补贴广告红包，具体请至拼多多商家后台订单详情页查看，或联系拼多多官方客服了解详情</span></span>';
            if (has_tags('local_depot', 'Order')) {
                row.isPddLocal = true;
                orderTagsHtml += '&nbsp;<span class="tarTxt01 tagStatus tooltip">本地仓<span class="tooltip-content">此类订单无需打单发货，只需将货品派送至指定地点。请前往拼多多后台配送单管理页面进行查询<a href="https://mms.pinduoduo.com/home/" target="_blank" style="color:#3aadff;">点击前往</a></span></span>';
            }
            if (has_tags('has_weipai_service', 'Order'))
                orderTagsHtml += '&nbsp;<span class="tarTxt01 tagStatus tooltip">微派服务<span class="tooltip-content">订单已签署顺丰微派服务，请与物流服务商（如顺丰）确认获取现场拆封激活拍照的核验照片。</span></span>';
            // 不包含合单
            if ((!row.LogicOrderId || row.LogicOrderId.startsWith("C") == false) && has_tags('quality_check_order', 'Order'))
                orderTagsHtml += '&nbsp;<span class="tarTxt01 tagStatus tooltip">质检订单<span class="tooltip-content">拼多多平台随机抽取部分商品订单参与质检，将结合<a href="https://mms.pinduoduo.com/other/rule?id=75&msfrom=mms_globalsearch" target="_blank" style="color:#3aadff;">《拼多多商品描述及质量抽检规则》</a>及用户反馈对商品进行质检，质检结果将3倍权重计入商品评价分排名/商品领航员等品质指标。</br>如有疑问，请前往商家后台-客服通道进行咨询。</span></span>';

            //安装服务
            var tagService = ["pdd_sf_express_fee", "pdd_install_fee", "pdd_store_install_fee", "pdd_take_to_store_install_fee", "pdd_dismantle_and_home_install_fee"];
            common.Foreach(tagService, function (i, s) {
                common.Foreach(orderTags, function (j, item) {
                    if (item.Tag == s) {
                        var tagName = "";
                        var tagRemark = "";
                        switch (s) {
                            case "pdd_sf_express_fee": tagName = "顺丰加价"; break;
                            case "pdd_install_fee": tagName = "上门安装"; break;
                            case "pdd_store_install_fee": tagName = "到店安装"; break;
                            case "pdd_take_to_store_install_fee": tagName = "携货到店"; break;
                            case "pdd_dismantle_and_home_install_fee": tagName = "拆旧+上门安装"; break;
                        }
                        if (item.TagValue != "" && item.TagValue != "0") {
                            try {
                                var tempRemrk = "自选快递金额:" + parseFloat(item.TagValue).toFixed(2);
                                tagRemark = '<span class="tooltip-content">' + tempRemrk + '</span>';
                            } catch (e) {
                            }
                        }
                        orderTagsHtml += '&nbsp;<span class="tarTxt01 tagStatus tooltip">' + tagName + tagRemark + '</span>';
                    }
                });
            });
        }

        //头条
        if (row.PlatformType == "TouTiao") {
            if (has_tags('logistics_transit', 'Order')) {
                var tooltip = "收件人地址为中转地址，若您需要查看消费者信息，请您前往抖店商家后台进行查看。";
                row.IsTouTiaoTransit = true;
                var isExitTagVals = [];
                for (var i = 0; i < orderTags.length; i++) {
                    var tag = orderTags[i];
                    //#region 注释
                    //if (orderId && orderId == tag.OiCode && tag.Tag == "logistics_transit")
                    //{
                    //    var tagName = "";
                    //    if (tag.TagValue == "1") { tagName = "新疆中转"; }
                    //    else if (tag.TagValue == "2") { tagName = "西藏中转"; }
                    //    if (tagName) {
                    //        //新标签
                    //        orderTagsHtml += '<span class="tarTxt tarTxt08 hover popoverCommon">' + tagName + '<span class="popoverCommon-warn">' + tooltip + '</span></span>';
                    //    }
                    //}
                    //else if (!orderId && tag.Tag == "logistics_transit")
                    //{
                    //    var tagName = "";
                    //    if (tag.TagValue == "1") { tagName = "新疆中转"; }
                    //    else if (tag.TagValue == "2") { tagName = "西藏中转"; }
                    //    if (tagName) {
                    //        //新标签
                    //        orderTagsHtml += '<span class="tarTxt tarTxt08 hover popoverCommon">' + tagName + '<span class="popoverCommon-warn">' + tooltip + '</span></span>';
                    //    }
                    //}
                    //#endregion
                    var tagName = "";
                    if (tag.TagValue == "1" && tag.Tag == "logistics_transit") { tagName = "新疆中转"; }
                    else if (tag.TagValue == "2" && tag.Tag == "logistics_transit") { tagName = "西藏中转"; }
                    if (tagName && isExitTagVals.indexOf(tagName) == -1) {
                        //新标签
                        orderTagsHtml += '<span class="tarTxt tarTxt08 hover popoverCommon">' + tagName + '<span class="popoverCommon-warn">' + tooltip + '</span></span>';
                        isExitTagVals.push(tagName);
                    }
                }
            }
            //中转合包
            if (has_tags('transit_merge', 'Order')) {
                orderTagsHtml += '<span class="tarTxt tarTxt08 hover popoverCommon">中转合包<span class="popoverCommon-warn">该订单已在中转仓与其它包裹合包，不支持中途拦截快递、退货、退款，待消费者收到包裹后方可申请。</span></span>';
            }
            if (has_tags('remote_derict', 'Order')) {
                orderTagsHtml += '<span class="tarTxt tarTxt08 hover popoverCommon">偏远直邮</span>';
            }
            //自选快递标签
            if (commonModule.HasTag(orderTags, 'shop_optional_express_info', 'OrderItem')) {
                var exitsExpress = [];
                for (var i = 0; i < orderTags.length; i++) {
                    try {
                        var tag = orderTags[i];
                        if (tag.Tag == "shop_optional_express_info") {
                            var tagValue = tag.TagValue;
                            var arrTagVal = tagValue.split('|');
                            var tagVal = arrTagVal[0];
                            if (exitsExpress.indexOf(tagVal) == -1) {
                                var tagName = "自选" + arrTagVal[1];
                                var tooltip = "自选快递金额:" + parseFloat(arrTagVal[2]).toFixed(2);
                                orderTagsHtml += '<span class="tarTxt tarTxt08 hover popoverCommon">' + tagName + '<span class="popoverCommon-warn">' + tooltip + '</span></span>';
                            }
                        }
                    } catch (e) {

                    }
                }
            }
            //风控不通过标签
            if (commonModule.HasTag(orderTags, 'risk_fail', 'OrderItem')) {
                if (fxPageType != 2) {
                    orderTagsHtml += '<span class="tarTxt03 tagStatus tooltip">风控不通过<span class="tooltip-content">该订单已被平台标记为风险订单且未通过平台风控检测，该订单已被平台关闭。<br/>温馨提示：风控不通过的订单会被平台自动关闭。<a href="https://op.jinritemai.com/docs/question-docs/89/4125" style="color:#3aadff;cursor:pointer" target="_blank">查看官方公告</a></span></span>';
                }
            }
        }

        //阿里巴巴
        if (row.PlatformType == "Alibaba") {
            //1688-定制服务订单
            var isAiCustomerService = false;
            if (fxPageType == 2) {
                if (has_tags('AiCustomerService', 'OrderItem')) {
                    isAiCustomerService = true;
                }
            } else {
                if (commonModule.HasTag(orderTags, 'AiCustomerService', 'OrderItem')) {
                    isAiCustomerService = true;
                }
            }
            if (isAiCustomerService) {
                orderTagsHtml += '<span class="tarTxt12 tagStatus tooltip">定制服务<span class="tooltip-content">请根据商品定制信息发货，<a style="color:#3aadff;" target="_blank" href="https://dgjapp.com/newHelpsShow.html?id=1705914439274">了解详情</a></span>';
            }

            //1688-跨境转运订单
            if (has_tags('logistics_transit', 'Order')) {
                row.IsAlibabaTransit = true;
                orderTagsHtml += '<span class="tagStatus tooltip tarTxt12">跨境转运<span class="tooltip-content">转运仓要求每个运单号仅关联一个订单，不支持合并订单。</span></span>';
            }
        }

        //淘宝
        if (row.PlatformType == "Taobao") {
            //中转合包
            if (has_tags('clearance_order', 'Order')) {
                orderTagsHtml += '<div class="tagStatus tooltip" title="该订单为闲鱼平台清仓订单">清仓订单</div>';
            }
        }

        //快手
        if (row.PlatformType == "KuaiShou") {
            if (has_tags('logistics_transit', 'Order')) {
                var tooltip = "收货信息为中转仓信息，若需要查看消费者收货地址，请前往快手小店商家后台查看。";
                row.IsKuaiShouTransit = true;
                var isExitTagVals = [];
                for (var i = 0; i < orderTags.length; i++) {
                    var tag = orderTags[i];
                    var tagName = "";
                    if (tag.TagValue == "1") { tagName = "新疆中转"; }
                    if (tag.TagValue == "0") { tagName = "青海中转"; }
                    if (tag.TagValue == "3") { tagName = "甘肃中转"; }
                    if (tag.TagValue == "4") { tagName = "宁夏中转"; }
                    if (tag.TagValue == "15") { tagName = "内蒙古中转"; }
                    if (tagName && isExitTagVals.indexOf(tagName) == -1) {
                        //新标签
                        orderTagsHtml += '<span class="tarTxt tarTxt08 hover popoverCommon">' + tagName + '<span class="popoverCommon-warn">' + tooltip + '</span></span>';
                        isExitTagVals.push(tagName);
                    }
                }
            }
        }

        //京东
        if (row.PlatformType == "Jingdong") {
            //大件货上门服务
            if (commonModule.HasTagValue(orderTags, 'home_delivery_door', '1', 'OrderItem')) {
                var tooltip = "大件送货上门订单平台不强制指定快递，若快递未送货上门，可能引起赔付问题。";
                orderTagsHtml += '<span class="tarTxt tarTxt05 hover popoverCommon">大件送货上门<span class="popoverCommon-warn">' + tooltip + '</span></span>';
            }
            //中小件货品
            if (commonModule.HasTagValue(orderTags, 'home_delivery_door', '2', 'OrderItem')) {
                var tooltip = "中小件送货上门订单，需使用平台指定快递发货（中通、圆通、申通、极兔、韵达），若快递未送货上门，可能引起赔付问题。";
                orderTagsHtml += '<span class="tarTxt tarTxt05 hover popoverCommon">中小件送货上门<span class="popoverCommon-warn">' + tooltip + '</span></span>';
            }
            //新疆集运订单
            if (has_tags('consolidateInfo', 'Order')) {
                var platformOrderId = row.PlatformOrderId;
                if (fxPageType == 4)
                    platformOrderId = orderId;
                if (platformOrderId.startsWith("C"))
                    platformOrderId = platformOrderId.substring(1);
                var tooltip = "";
                tooltip = '<div>';
                tooltip += '<span style="font-weight:700;">集运订单</span>';
                tooltip += '<p>1、不支持修改收件信息，如使用自由打印，复制订单信息确保详细地址后 11 位合单码一起复制并打印。</p>';
                tooltip += '<p style="display: list-item;list-style: disc;margin-left: 35px;">示例：姓名，电话，地址[<span style="color:#F7941F">11 位合单码</span>]</p>';
                tooltip += '<p>2、集运订单收件人、地址相同可互相合并，不支持与普通订单合并。</p>';
                tooltip += '<p>3、必须使用京东快递、无界电子面单、或京麦端在线寄件发货</p>';
                tooltip += '<p>4、如需查看订单真实收件信息、一二段物流详情，';
                tooltip += '<a style="color:#3aadff;" target="_blank" href="https://shop.jd.com/jdm/trade/order/orderDetail?orderNo=' + platformOrderId + '">请前往店铺后台查看。</a>';
                tooltip += '</p>';
                tooltip += '</div>';
                orderTagsHtml += '<span class="tarTxt tarTxt05 hover popoverCommon">新疆集运<span class="popoverCommon-warn">' + tooltip + '</span></span>';
            }

        }
        //小红书 送货上门
        if (row.PlatformType == "XiaoHongShu" && commonModule.HasTag(orderTags, 'home_delivery_door', 'OrderItem')) {
            var xhsCallback = function () {
                var supportExPressName = "";
                if (commonModule.xhsDoorSupportExpressModel) {
                    supportExPressName = commonModule.xhsDoorSupportExpressModel.ExpressName;
                }
                orderTagsHtml += '&nbsp;<span class="tarTxt01 tagStatus tooltip">送货上门<span class="tooltip-content">送货上门暂时仅支持' + supportExPressName + '，请按平台规则为买家提供送货上门服务，<a href="https://school.xiaohongshu.com/lesson/normal/c2019bf866fd4ef6b6209fc85d834bdd?jumpFrom=ark" target="_blank" style="color:#3aadff;">查看官方教程</a></span></span>';
            }
            if (commonModule.xhsDoorSupportExpressModel == undefined) {
                commonModule.LoadCommonSetting("/FenFa/System/XiaoHongShu/DoorServiceSuppprtExpressCompany", false, function (res) {
                    if (res.Success && res.Data) {
                        commonModule.xhsDoorSupportExpressModel = JSON.parse(res.Data);
                    } else {
                        commonModule.xhsDoorSupportExpressModel = { ExpressName: "", ExpressCode: "" };
                    }
                    xhsCallback();
                });
            } else {
                xhsCallback();
            }
        }
        return orderTagsHtml;
    }

    /** 逻辑单标签展示样式 LogicOrderTagsHtml*/
    table.logicOrderTagsHtmlFormatter = function (logicOrderId, orderTags, DownFxUserId) {
        var logicOrderTagsHtml = '';

        if (commonModule.HasTagOiCode(orderTags, logicOrderId, 'ManualPushSupplier', 'LogicOrder')) {
            var tagTooltip_content = '';
            common.Foreach(orderTags, function (i, item) {
                if (item.Tag == "ManualPushSupplier" && item.OiCode == logicOrderId && item.TagValue == fxUserId && DownFxUserId>0) {
                    tagTooltip_content = '指定厂家推单时间: ' + item.CreateTime + '<br/>';
                    logicOrderTagsHtml += '<span class=" tarTxt06 tagStatus tooltip">指定厂家推单<span class="tooltip-content">' + tagTooltip_content + '</span></span>';
                }
            });
        }

        //异常单，前端不展示
        //if (commonModule.HasTagOiCode(orderTags, logicOrderId, 'ExceptionOrder', 'LogicOrder')) {
        //    logicOrderTagsHtml += '<i class="tarTxt tarTxt03" style="margin-top:3px;" title="该订单需人工处理审核，请您查看订单上的小标签，按提示操作对应解决方案">异常单</i>';
        //}
        return logicOrderTagsHtml;
    }

    /** 逻辑单项标签展示样式 OrderItemTagsHtml*/
    table.orderItemTagsHtmlFormatter = function (orderItemTags) {
        var itTagHtml = '';
        if (commonModule.HasTag(orderItemTags, 'sf_free_shipping', 'OrderItem')) {
            itTagHtml += '<i class="tarTxt tarTxt05" style="margin-top:2px;" title="商家已选择本商品承诺顺丰包邮，为了保障消费者体验，本商品请选择顺丰速运发货，否则会扣除10元货款赔付给消费者">顺丰包邮</i>';
        }
        //var itTagHtml = '';
        if (commonModule.HasTag(orderItemTags, 'Kuaishou_orderItemGift', 'OrderItem')) {
            itTagHtml += '<i class="tarTxt tarTxt12" style="margin-top:2px;" title="赠品">赠品</i>';
        }
        if (commonModule.HasTag(orderItemTags, 'DigitalProduct', 'OrderItem')) {
            itTagHtml += '<span class="tarTxt tarTxt08" style="margin-top:2px;" title="平台要求数码产品发货上传设备识别码，发货时请根据系统提示填入 SN 码，或者 IMEI 码。">数码产品</span>'
        }
        return itTagHtml;
    }
    //#endregion

    //定义表格列(1：所有订单，2：待打单页面，3：线下单页面)
    var columns = commonModule.FxPageType() != 2 ?
        [
            //{ id: 1, defaults: false, field: "checkbox", name: "checkbox", display: true, order: 1, minWidth: "45px", canMove: true, headerFormattter: AllCheckHeaderFormattter, contentFormatter: AllCheckContentFormatter },
            { id: 1, defaults: true, type: "allorder", field: "Product", name: "商品信息", display: true, order: 1, align: "left", minWidth: "440px", width: "440px", canMove: true, headerFormattter: AllProductHeaderFormattter, contentFormatter: null },
            { id: 2, defaults: true, type: "allorder", field: "PriceCount", name: "单价/数量", display: true, order: 2, minWidth: "120px", width: "120px", canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 3, defaults: true, type: "allorder", field: "OrderStatus", name: "订单状态", display: true, order: 3, minWidth: "150px", canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 4, defaults: true, type: "allorder", field: "Reciever", name: "收件人信息", display: true, order: 4, align: "left", minWidth: "188px",width: '188px', canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 5, defaults: true, type: "allorder", field: "Remark", name: "留言/备注", display: true, order: 5, align: "left", minWidth: "100px", canMove: true, headerFormattter: null, contentFormatter: null },
            { id: 6, defaults: true, type: "allorder", field: "ExpressCode", name: "物流", display: true, order: 6, align: "left", minWidth: "55px", canMove: true, headerFormattter: null, contentFormatter: ExpressCodeContentFormatter },
            { id: 7, defaults: true, type: "allorder", field: "Operator", name: "操作", display: true, order: 7, align: "right", minWidth: "75px", canMove: true, headerFormattter: AllOperatorHeaderFormatter, contentFormatter: null },
            { id: 24, defaults: false, type: "allorder", field: "LastShipTime", name: "订单剩余时间", display: false, align: "left", order: 27, minWidth: "70px", canMove: true, headerFormattter: null, contentFormatter: LastShipTimeContentFormatter },
        ]
        :
        [
            { id: 1, defaults: true, type: "waitprint", field: "PrintState", name: "打印", className: "table_content_dagou wu-flex wu-column", display: true, order: 1, align: "center", minWidth: "54px", width: "54px", canMove: false, headerFormattter: null, contentFormatter: PrintStateContentFormatter },
            { id: 2, defaults: false, type: "waitprint", field: "checkbox", name: "订单时间", display: true, order: 2, minWidth: "120px", width: "120px", canMove: true, headerFormattter: null, contentFormatter: OrderTimeContentFormatter },
            { id: 3, defaults: true, type: "waitprint", field: "Reciever", name: "收件人信息", display: true, order: 3, minWidth: "150px", flex: "1", canMove: true, headerFormattter: null, contentFormatter: AllRecieverContentFormatter },
            { id: 4, defaults: true, type: "waitprint", field: "ProductShow", name: "商品信息", display: true, order: 4, minWidth: "150px", width: "250px", canMove: true, headerFormattter: ProductShowHeaderFormattter, contentFormatter: ProductShowContentFormatter },
            //{ id: 3, defaults: true, type: "waitprint", field: "Reciever", name: "收件人", display: true, order: 3, minWidth: "120px", width: "120px", canMove: true, headerFormattter: null, contentFormatter: RecieverContentFormatter },
            //{ id: 4, defaults: true, type: "waitprint", field: "RecieverAddress", name: "收件地址", display: true, order: 4, align: "left", minWidth: "200px", maxWidth: "350px", canMove: true, headerFormattter: null, contentFormatter: RecieverAddressContentFormatter },
            { id: 5, defaults: true, type: "waitprint", field: "TotalSum", name: "订单实付总额", display: true, order: 5, width: "120px", minWidth: "84px", canMove: true, headerFormattter: TotalSumHeaderFormatter, contentFormatter: TotalSumContentFormatter },
            { id: 6, defaults: true, type: "waitprint", field: "OrderRemark", name: "订单备注", display: true, order: 6, align: "left", minWidth: "150px", maxWidth: "200px", flex: "1", canMove: true, headerFormattter: null, contentFormatter: OrderRemarkContentFormatter },
            { id: 7, defaults: true, type: "waitprint", field: "OrderStatus", name: "订单状态", display: true, order: 7, align: "left", minWidth: "88px", canMove: true, headerFormattter: null, contentFormatter: OrderStatusContentFormatter },
            { id: 8, defaults: true, type: "waitprint", field: "ExpressCode", name: "物流", display: true, order: 8, align: "left", minWidth: "150px", canMove: true, headerFormattter: null, contentFormatter: ExpressCodeContentFormatter },
            { id: 24, defaults: false, type: "waitprint", field: "LastShipTime", name: "订单剩余时间", display: true, align: "left", order: 8, minWidth: "108px", canMove: true, headerFormattter: null, contentFormatter: LastShipTimeContentFormatter },
            { id: 9, defaults: true, type: "waitprint", field: "Operator", name: "操作", display: true, order: 8, align: "left", minWidth: "62px", canMove: true, headerFormattter: null, contentFormatter: OperatorContentFormatter },
            { id: 10, defaults: true, type: "waitprint", field: "Detail", name: "详情", display: true, order: 11, align: "left", minWidth: "78px", width: "78px", canMove: true, headerFormattter: DetailHeaderFormatter, contentFormatter: DetailContentFormatter },
        ];

    //渲染指定的列内容
    table.rederField = function (row, field, index, ext) {
        var column = null;
        for (var i = 0; i < table.columns.length; i++) {
            var col = table.columns[i];
            if (col.field == field) {
                column = col;
                break;
            }
        }
        var html = defaultContentFormatter(row, column, index, ext);
        return html;
    }

    table.columns = columns;
    var _render = function (element, rows) {
        if (!element)
            return;

        ////线下单无打印状态不显示
        //if (commonModule.FxPageType() == 3) {
        //    $(columns).each(function (i, col) {
        //        if (col.field == "ExpressCode") {
        //            columns.splice(i, 1);
        //            return true;
        //        }
        //    });
        //}
        var html = '<div class="layui-mytable"><div class="layui-row layui-mytable-header">';
        var cols = common.Sort(table.columns, "order", false);
        // 生成表格头部
        $(cols).each(function (index, col) {
            var style = "";
            if (col.width) {
                style += "width:" + col.width + ";";
            }
                
            if (col.minWidth) {
                style += "min-width:" + col.minWidth + ";";
            }
                
            if (col.maxWidth) {
                style += "max-width:" + col.maxWidth + ";";
            }
                
            if (col.align) {
                style += "text-align:" + col.align + ";";
            }
            if (col.flex) {
                style += "flex:" + col.flex + ";";
            }

            if (!col.display)
                style += "display:none";
            if (col.type == "waitprint") {
                if (col.field == "checkbox") {
                    html += '<div class="layui-col-md1 wu-checkboxWrap" style="' + style + '">';
                    html += '<input class="allcheckorder order-chx-all wu-mR12" id="allcheckorder" type="checkbox" onclick="waitOrderModule.AllCheck()">订单时间';
                    html += '</div>';

                    //html += '<th style="' + style + '" class="' + col.field + '"><input class="order-chx-all" type="checkbox"></th>';
                } else {
                    // 通过别名字段动态更新字段值
                    var aliasField = col.aliasField && col.aliasField != "" ? 'data-aliasField="' + col.aliasField + '"' : "";
                    if (col.field == "RecieverAddress") {
                        html += '<div class="layui-col-md3 order-column-header-' + col.field + ' ' + col.field + '" ' + aliasField + ' style="' + style + '">' + defaultHeaderFormatter(col) + '</div>';
                    } else if (col.field == "ProductShow") {
                        html += '<div class="layui-col-md2 order-column-header-' + col.field + ' ' + col.field + '" ' + aliasField + ' style="' + style + '">' + defaultHeaderFormatter(col) + '</div>';
                    } else if (col.field == "OrderRemark") {
                        var subHtml = ''
                        if (window.location.href.indexOf('/NewOrder/WaitOrder') > -1 && col.field == "OrderRemark") {
                            subHtml = '<div class="newicon hover" id="RemarkSetBtn" onclick="orderPrintModule.onRemarkSet()"><i class="iconfont icon-a-setting1x1 wu-weight500 wu-color-m wu-operate wu-mL4"></i><img style="display:none;" src="/Content/images/new_icon_2024_11_20.png"/></div>';
                        }
                        html += '<div class="layui-col-md2 order-column-header-' + col.field + ' ' + col.field + '" ' + aliasField + ' style="' + style + '">' + defaultHeaderFormatter(col) + subHtml + '</div>';
                    } else
                        html += '<div class="layui-col-md1 order-column-header-' + col.field + ' ' + col.field + '" ' + aliasField + ' style="' + style + '">' + defaultHeaderFormatter(col) + '</div>';
                    //html += '<th style="' + style + '" ' + aliasField + ' class="' + col.field + '">' + defaultHeaderFormatter(col) + '</th>';
                }
            }
            else {
                // 通过别名字段动态更新字段值
                var aliasField = col.aliasField && col.aliasField != "" ? 'data-aliasField="' + col.aliasField + '"' : "";
                if (col.field == "Reciever" || col.field == "Remark") {
                    var subHtml = ''
                    if (window.location.href.indexOf('/NewOrder/AllOrder') > -1 && col.field == "Remark") {
                        subHtml = '<div class="newicon hover" id="RemarkSetBtn" onclick="orderPrintModule.onRemarkSet()"><i class="iconfont icon-a-setting1x1 wu-weight500 wu-color-m wu-operate wu-mL4"></i><img style="display:none;" src="/Content/images/new_icon_2024_11_20.png"/></div>';
                    }
                    html += '<div class="layui-col-md2 order-column-header-' + col.field + ' ' + col.field + '" ' + aliasField + ' style="' + style + '">' + defaultHeaderFormatter(col) + subHtml + '</div>';
                } else if (col.field == "Operator")
                    html += '<div class="layui-col-md3 operate order-column-header-' + col.field + ' ' + col.field + '" ' + aliasField + ' style="' + style + '">' + defaultHeaderFormatter(col) + '</div>';
                else {
                    if (col.field == "ExpressCode") {
                        var status = $("#orderstatus li.active").attr("data-status");
                        if (status == "waitsellersend" || status == "orderList_refund" || status == "all" || status == "pending" || status == "approvaled")
                            col.name = "打印状态";
                        else
                            col.name = "物流";
                    }
                    html += '<div class="layui-col-md1 order-column-header-' + col.field + ' ' + col.field + '" ' + aliasField + ' style="' + style + '">' + defaultHeaderFormatter(col) + '</div>';
                }
                //html += '<th style="' + style + '" ' + aliasField + ' class="' + col.field + '">' + defaultHeaderFormatter(col) + '</th>';
            }

        });
        html += "</div>";
        html += '<div class="layui-row layui-mytable-tobody wu-f12 wu-c09">';
        //html += '<tbody class="table_content_tbody">';

        //分步渲染表格内容
        var firstrows = [];
        var secondrows = [];
        var thirdrows = [];
        $(rows).each(function (index, row) {

            _renderBefore(row);

            if (index < 10)
                firstrows.push(row);
            else if (index < 100)
                secondrows.push(row);
            else
                thirdrows.push(row);

        });
        //生成表格内容
        //console.time("渲染表格行");
        $(firstrows).each(function (index, row) {
            row.Index = index;
            if (commonModule.FxPageType() == 2) {
                //生成打印数据前先处理销售价格
                _renderPrice(row);
                //row.PrintInfo = printContentFormatSetModule.ForatPrintContent(row); //打印内容后置
                html += _renderRow(cols, row);
            }
            else {
                var status = $("#orderstatus .active").attr("data-status") || "";
                if (status == "waitaudit") {
                    //如果是待审订单列表,单独加载一个模板
                    html += _renderCheckOrderRow(cols, row);
                } else {
                    html += _renderAllOrderRow(cols, row);
                }
            }

        });



        //console.timeEnd("渲染表格行");
        html += "</div>";
        //html += "</tbody></table>";
        //console.time("渲染表格行到dom");
        //$(element).html(html);
        element.innerHTML = html;

        setTimeout(function () {

            initOrderTableEvent();

            //var template = addTmplInOrderListModule.GetCurrentTemplate();
            //table.changeTemplate(template);


            //批量操作按钮文案提示              
            var timerTableContentImgs = null;
            $(".operate-btn button").hover(function () {
                var $this = $(this);
                var clas = $this.attr("class");
                if (clas.indexOf("batchCheck") != -1)
                    $this.html('').append('批量通过审核' + BatchBtnTipRender("batchCheck"));
                else if (clas.indexOf("batchManualPushSelf") != -1)
                    $this.html('').append('批量设为自营' + BatchBtnTipRender("batchManualPushSelf"));
                else if (clas.indexOf("batchManualPushSupplier") != -1)
                    $this.html('').append('批量指定厂家' + BatchBtnTipRender("batchManualPushSupplier"));
                else
                    return;

                timerTableContentImgs = setTimeout(function () {
                    $this.find(".orderOperateExplain-wrap").show();
                }, 100)
            }, function () {
                $(this).find(".orderOperateExplain-wrap").remove();
                //$(this).children(".orderExplain-wrap").hide();
                clearTimeout(timerTableContentImgs);
            });

        }, 1);


        setTimeout(function () {
            _renderOhterRow(element, secondrows, thirdrows, 10, function () {
                // 列表渲染完成后回调
                if (!secondrows || secondrows.length < 1) {
                    //默认展示详情或隐藏
                    orderTableBuilder.DefaultDisplayORHide();

                    ////快递是否可达
                    //orderTableBuilder.checkExpressReach();
                }

                ////不可达过滤                                                                       
                //var isCanFilterReach = $("#ExpressCanReachBtn").hasClass("active"); //过滤所有可达
                //var isFilterReach = $("#ExpressReachBtn").hasClass("active"); //过滤所有不可达
                //if (isFilterReach || isCanFilterReach) {
                //    orderTableBuilder.FilterExpressReach();
                //    //waitOrderModule.AllShowCheck();
                //}
                ////预警单过滤
                //if (!(LastShipTimeWarnHours == undefined || LastShipTimeWarnHours == "0") && $("#LastShipTimeWarnBtn").hasClass("active")) {
                //    orderTableBuilder.SecondFilter();
                //}

                orderTableBuilder.SecondFilter();
            });
        }, 150);
        //无数据页面提示
        if (rows.length == 0 && !rows.length) {
            var noDataHtml = '<div id="tableNoDataShow" class="tableNoDataShow"><img src="/Content/images/noData-icon.png" /><span class="tableNoDataShow-title">暂无数据</span></div>';
            $("#OrderTableList .layui-row.layui-mytable-tobody").append(noDataHtml);
        } else {
            $("#tableNoDataShow").remove();
        }

        //setTimeout(function () {
        //    _renderOhterRow(element, thirdrows, 100);
        //}, 300);

        // 无权限时隐藏某些元素 
        var showPermDict = {};
        if (commonModule.FxPageType() == 1) {
            showPermDict = commonModule.AllOrderShowPermDict;
            commonModule.HideNoPermDiv(showPermDict);
            // 根据【批量操作】下拉框是否有元素 判断是否要隐藏【批量操作】按钮
            var hideBtn = true;
            $(".newoperate-btnWrap li").each(function () {
                if ($(this).css("display") !== "none") {
                    hideBtn = false;
                    return false; // 结束循环
                }
            });
            if (hideBtn) {
                $(".newoperate-btnWrap").hide();
            } else {
                $(".newoperate-btnWrap").show();
            }
        }
        else if (commonModule.FxPageType() == 2) {
            showPermDict = commonModule.WaitOrderShowPermDict;
            commonModule.HideNoPermDiv(showPermDict);

            // 根据底部【更多批量操作】下拉框是否有元素 判断是否要隐藏【更多批量操作】按钮
            var hideBtn = true;
            $(".btnMoreOparte li").each(function () {
                if ($(this).css("display") !== "none") {
                    hideBtn = false;
                    return false; // 结束循环
                }
            });
            if (hideBtn) {
                $(".btnMoreOparte").hide();
            } else {
                $(".btnMoreOparte").show();
            }

            // 根据【页面底部】是否有元素 判断是否要隐藏【页面底部】
            var hideBtn = true;
            $(".layui-footer button").each(function () {
                if ($(this).css("display") !== "none") {
                    hideBtn = false;
                    return false; // 结束循环
                }
            });
            if (hideBtn) {
                $(".layui-footer").hide();
            } else {
                $(".layui-footer").show();
            }

            // 根据顶部【批量操作】是否有元素 判断是否要隐藏【批量操作】
            var hideBtn = true;
            $("#BatchBtn li").each(function () {
                if ($(this).css("display") !== "none") {
                    hideBtn = false;
                    return false; // 结束循环
                }
            });
            if (hideBtn) {
                $("#BatchBtn").hide();
            } else {
                $("#BatchBtn").show();
            }

        }
        else if (commonModule.FxPageType() == 3) {
            showPermDict = commonModule.OfflineOrderShowPermDict;
            commonModule.HideNoPermDiv(showPermDict);

            // 根据顶部导航栏的【订单需审核/订单需对账】是否有元素 判断是否要隐藏父容器
            var hideBtn = true;
            var commonSetShow = false;
            $(".common-content-set-Wrap li").each(function () {
                if ($(this).css("display") !== "none") {
                    hideBtn = false;
                    return false; // 结束循环
                }
            });
            if (hideBtn) {
                $(".common-content-set").hide();
                commonSetShow = false;
            } else {
                $(".common-content-set").show();
                commonSetShow = true;
            }

            // 根据顶部导航栏是否有元素 判断是否要隐藏顶部导航栏
            var importBtnShow = $(".ImportOfflineOrder").css("display") !== "none";
            if (importBtnShow || commonSetShow) {
                $("#importWrapData").show();
                $("#searcWrap").css("margin-top", "0px");
            } else {
                $("#importWrapData").hide();
                $("#searcWrap").css("margin-top", "65px");

            }

        }

        // 根据是否有代发留言/卖家备注权限判断是否显示【订单备注设置】
        if (!!showPermDict && ((showPermDict.hasOwnProperty(".UpdateSystemRemark") && showPermDict[".UpdateSystemRemark"]) || (showPermDict.hasOwnProperty(".UpdateOrderSellerRemark") && showPermDict[".UpdateOrderSellerRemark"]))) {
            $("#RemarkSetBtn").show();
        } else {
            $("#RemarkSetBtn").hide();
        }

    }

    var _renderBefore = function (row) {
        row.ReceiverIsEmpty = 0;

        if (commonModule.FxPageType() == 2) {
            // 虚假地址
            if (filterFakeAddressSet != null && filterFakeAddressSet != "" && filterFakeAddressSet.IsOpen) {
                var fakeKeys = filterFakeAddressSet.KeyStr.split(/[，,\s]/) || [];
                var isFake = false;
                for (var i = 0; i < fakeKeys.length; i++) {
                    if (fakeKeys[i] != null && fakeKeys[i] != "") {
                        var ars = row.ToAddress == null || row.ToAddress == undefined ? "" : row.ToAddress;
                        if (!isFake && ars.indexOf(fakeKeys[i]) != -1) {
                            row.IsFake = true;
                        }
                    }
                }
            }

            //拼多多平台-收件人为空判断
            if (row.PlatformType == "Pinduoduo" && (row.ToName == undefined || row.ToName == null || row.ToName == "") && (row.ToPhone == undefined || row.ToPhone == null || row.ToPhone == "") && (row.ToAddress == undefined || row.ToAddress == null || row.ToAddress == "")) {
                row.ReceiverIsEmpty = 1;
            }

            //debugger;
            ////测试-上线前移除
            //if (row.SystemRemark == "收件人为空") {
            //    row.ToName = "";
            //    row.ToPhone = "";
            //    row.ToMobile = "";
            //    row.ToAddress = "";
            //    row.ToFullAddress = "";
            //    row.ReceiverIsEmpty = 1;
            //}
        }
        else if (commonModule.FxPageType() == 1) {

            if (row.SecondSubOrders != undefined && row.SecondSubOrders.length >= 1) {
                if (row.SecondSubOrders[0].ThressSubOrders != undefined && row.SecondSubOrders[0].ThressSubOrders.length >= 1) {
                    var tRow = row.SecondSubOrders[0].ThressSubOrders[0];
                    //拼多多平台-收件人为空判断
                    if (row.PlatformType == "Pinduoduo" && (tRow.ToName == undefined || tRow.ToName == null || tRow.ToName == "") && (tRow.ToPhone == undefined || tRow.ToPhone == null || tRow.ToPhone == "") && (tRow.ToAddress == undefined || tRow.ToAddress == null || tRow.ToAddress == "")) {
                        row.ReceiverIsEmpty = 1;
                    }
                }

                ////测试-上线前移除
                //if (row.SecondSubOrders[0].SystemRemark == "收件人为空") {
                //    row.SecondSubOrders[0].ToName = "";
                //    row.SecondSubOrders[0].ToPhone = "";
                //    row.SecondSubOrders[0].ToMobile = "";
                //    row.SecondSubOrders[0].ToAddress = "";
                //    row.SecondSubOrders[0].ToFullAddress = "";
                //    row.ReceiverIsEmpty = 1;
                //}
            }
        }
    }

    //加载pdd偏远地区推荐快递
    var _loadRemoteProvinceRecommentLosgitisc = function (rows) {
        if (rows && rows.length == 0)
            return;
        var firstRow = rows[0];
        if (firstRow.PlatformType != "Pinduoduo")
            return;

        //汇总偏远的省名字
        var remoteProvinces = [];
        var shopIds = [];
        var remoteProvinceRows = [];
        for (var i = 0; i < rows.length; i++) {
            var row = rows[i];
            if ((pddRemoteProvinces && pddRemoteProvinces.indexOf(row.ToProvince) > -1)
                || commonModule.HasTag(row.Tags, 'pdd_region_black_delay_shipping', 'OrderItem')) {
                remoteProvinces.push(row.ToProvince);
                shopIds.push(row.ShopId);
                row.RecommendLogistics = []; //清空推荐物流
                remoteProvinceRows.push(row);
            }
        }

        //加载偏远地区推荐快递
        if (remoteProvinces.length <= 0)
            return;

        common.Ajax({
            url: "/NewOrder/GetLogisticsRecommend",
            data: {
                toProvinces: remoteProvinces,
                sids: shopIds
            },
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                var datas = rsp.Data;
                for (var province in datas) {
                    for (var i = 0; i < remoteProvinceRows.length; i++) {
                        var row = remoteProvinceRows[i];
                        //var isRemoteAddress = false; //订单地址是否是偏远地区
                        var isDelayShipping = commonModule.HasTag(row.Tags, 'pdd_region_black_delay_shipping', 'OrderItem'); //是否是延迟发货订单

                        if (row.ToProvince != province)
                            continue;

                        //判断订单是否
                        var provinceRecommendLogistics = datas[province];
                        for (var j = 0; j < provinceRecommendLogistics.length; j++) {
                            var rl = provinceRecommendLogistics[j];
                            if (rl.CityName == row.ToCity) {
                                row.RecommendLogistics = rl.RecommendLogisticList; //推荐物流
                                //isRemoteAddress = true; //订单是否命中偏远地区
                                break;
                            }
                        }

                        if (isDelayShipping) {
                            //延迟发货打标
                            var span_el = $("#order-" + row.Index + " .address_type");
                            span_el.append('<span class="remote-place" style="background-color: #f14874;">延迟发货</span>');
                        }
                        //if (isRemoteAddress) {
                        // 偏远地区打标
                        var span_el = $("#order-" + row.Index + " .address_type");
                        span_el.append('<span class="wu-tag wu-processing-dev">偏远地区</span>');
                        //}

                        //显示延迟发货或者可发快递的提示
                        var expressCodeCloumn = $("#order-" + row.Index + " .ExpressCode");
                        var recommendLogisticsHtml = "";
                        if (row.RecommendLogistics == undefined || (row.RecommendLogistics && row.RecommendLogistics.length == 0)) {
                            //没有获取到推荐快递，判断是
                            //$('#div_recommend_' + row.id).remove();
                            if (isDelayShipping) {
                                //判断是否有延迟发货的标，有则提示延迟发货
                                recommendLogisticsHtml += showDelayShippingHtml(row);
                            }
                        }
                        else {
                            //显示推荐快递
                            var logistics = [];
                            for (var i = 0; i < row.RecommendLogistics.length; i++) {
                                var item = row.RecommendLogistics[i];
                                logistics.push(item.Name);
                            }

                            if (isDelayShipping) {
                                //需求：2022-04-11 zouyi modify，延迟发货 有可用快递的显示
                                recommendLogisticsHtml += '<div class="remoteWran">'
                                recommendLogisticsHtml += '<span class="remoteWran-title" title="如快递停运，可使用：' + logistics[0] + '">如快递停运，可使用：' + logistics.join("/") + '</span>' //顺丰快递、中通快递、圆通快递
                                recommendLogisticsHtml += '<ul class="remoteWran-content">';
                                recommendLogisticsHtml += '<li>如您常用的快递停运，可使用<s>' + logistics.join("</s>/<s>") + '</s>发货</li>'; //顺丰快递</s>/<s>中通快递</s>/<s>圆通快递
                                recommendLogisticsHtml += '<li>1. 平台为您查询到与该订单物流线路相似的包裹，刚刚已通过上述快递成功发出。</li>';
                                recommendLogisticsHtml += '<li>2. 受疫情等客观因素影响，若该订单发货时间延迟，平台不做处罚；若能及时发货，可提升店铺曝光量。</li > ';
                                recommendLogisticsHtml += '<li>3. 当该订单收货区域解除疫情等客观因素影响时，在解除当日订单将重启承诺发货时间倒计时，请您密切关注变化，按照承诺发货时间发货，否则将受平台处罚。</li > ';
                                recommendLogisticsHtml += '</ul>';
                                recommendLogisticsHtml += '</div>'
                            }
                            else {
                                recommendLogisticsHtml += '<div class="remoteWran">'
                                recommendLogisticsHtml += '<span class="remoteWran-title" title="如快递停运可使用：' + logistics[0] + '">如快递停运可使用：' + logistics.join("/") + '</span>' //顺丰快递、中通快递、圆通快递
                                recommendLogisticsHtml += '<ul class="remoteWran-content">';
                                recommendLogisticsHtml += '<li>如快递停运可使用：<s>' + logistics.join("</s>/<s>") + '</s></li>'; //顺丰快递</s>、<s>中通快递</s>、<s>圆通快递
                                recommendLogisticsHtml += '<li>1. 平台为您查询到与该订单物流线路相似的包裹，刚刚已通过上述快递成功发出</li>';
                                recommendLogisticsHtml += '<li>2. 未在承诺发货时间内发货，将受平台处罚，请及时发货</li>';
                                recommendLogisticsHtml += '</ul>';
                                recommendLogisticsHtml += '</div>'
                            }
                        }
                        if (recommendLogisticsHtml != "") {
                            expressCodeCloumn.prepend(recommendLogisticsHtml);
                        }
                        break;
                    }
                }
            }
        });
    }

    //显示发货延迟的提示
    var showDelayShippingHtml = function () {
        var html = "";
        //延迟发货提示
        html += '<div class="remoteWran">'
        html += '<span class="remoteWran-title" title="受客观因素影响，该订单发货时间可延迟">受客观因素影响，该订单发货时间可延迟</span>'
        html += '<ul class="remoteWran-content">';
        html += '<li>1.受疫情等客观因素影响，快递可能无法正常配送；若该订单发货时间延迟，平台不做处罚。</li>';
        html += '<li>2.当该订单收货区域解除上述影响时，解除当日订单将重启承诺发货时间倒计时，请您密切关注变化，按照承诺发货时间发货，否则将受平台处罚。</li>';
        html += '</ul>';
        html += '</div>';
        return html;
    }

    var _renderOhterRow2 = function (element, rows, beginIndex) {
        if (!element)
            return;
        if (!rows || rows.length < 1)
            return;
        var cols = common.Sort(table.columns, "order", false);
        var html = "";
        $(rows).each(function (index, row) {
            row.Index = index + beginIndex;
            if (commonModule.FxPageType() == 2) {
                //生成打印数据前先处理销售价格
                _renderPrice(row);
                row.PrintInfo = printContentFormatSetModule.ForatPrintContent(row);
                html += _renderRow(cols, row);
            }
            else {
                var status = $("#orderstatus .active").attr("data-status") || "";
                if (status == "waitaudit") {
                    html += _renderCheckOrderRow(cols, row);
                } else {
                    html += _renderAllOrderRow(cols, row);
                }

            }

        });

        $("#OrderTableList .layui-mytable-tobody").append(html);

        //默认展示详情或隐藏
        orderTableBuilder.DefaultDisplayORHide();

        ////快递是否可达
        //orderTableBuilder.checkExpressReach();


    }

    var _renderOhterRow = function (element, rows, rows2, beginIndex, callBack) {
        if (!element || !rows || rows.length < 1) {
            changeOrderTimeText();
            if (typeof callBack == "function")
                callBack();
            return;
        }
        var cols = common.Sort(table.columns, "order", false);
        var html = "";
        $(rows).each(function (index, row) {
            row.Index = index + beginIndex;
            if (commonModule.FxPageType() == 2) {
                //生成打印数据前先处理销售价格
                _renderPrice(row);
                row.PrintInfo = printContentFormatSetModule.ForatPrintContent(row);
                html += _renderRow(cols, row);
            }
            else {
                var status = $("#orderstatus .active").attr("data-status") || "";
                if (status == "waitaudit") {
                    //待审订单列表单独加载模板
                    html += _renderCheckOrderRow(cols, row);
                } else {
                    html += _renderAllOrderRow(cols, row);
                }

            }

        });

        $("#OrderTableList .layui-mytable-tobody").append(html);

        if (!rows2 || rows2.length < 1) {
            //默认展示详情或隐藏
            orderTableBuilder.DefaultDisplayORHide();
            ////快递是否可达
            //orderTableBuilder.checkExpressReach();
        }
        if (!rows2 || rows2.length < 1) {
            changeOrderTimeText();
            if (typeof callBack == "function")
                callBack();
            return;
        }
        setTimeout(function () {
            _renderOhterRow2(element, rows2, 100);
            changeOrderTimeText();
            if (typeof callBack == "function")
                callBack();
        }, 150);

    }

    var changeOrderTimeText = function () {
        // 查询时间类型
        var val = $("#QueryDate").val() || "";
        if (val == "CreateTime")
            $(".orderTimeType").text("创建时间");
        else if (val == "PayTime")
            $(".orderTimeType").text("付款时间");
        else if (val == "OnlineSendTime")
            $(".orderTimeType").text("发货时间");

    }

    var initOrderTableEvent = function () {




    }

    table.CheckOrderBind = function (that) {
        if (event) {
            event.stopPropagation();
        } else {
            window.e.returnValue = false;
        };

        var isChecked = that.checked;
        var $this = $(that);
        var index = $this.attr('data-index');
        var row = table.rows[index];
        if (row.IsCantSendGood) {
            $this.checked = false;
            $this.closest(".order-row").removeClass("active");
            layer.msg(row.CantSendReason);
            return;
        }
        row.checked = isChecked; //数据选中标识
        if (isChecked) {
            $this.closest(".layui-row-item").addClass("active");
            $this.closest(".layui-row").addClass("active");
        }
        else {
            $this.closest(".layui-row-item").removeClass("active");
            $this.closest(".layui-row").removeClass("active");

        }

        var isAll = false;
        if (commonModule.FxPageType() != 2) {
            var rowCount = 0;
            $(table.rows).each(function (i, first) {
                $(first.SecondSubOrders).each(function (i2, second) {
                    rowCount++;
                });
            });
            isAll = $(".order-chx:checked").length == rowCount;
        }
        else
            isAll = $(".order-chx:checked").length == table.rows.length;
        $("#orderNum").text($(".order-chx:checked").length);
        $("#allcheckorder")[0].checked = isAll;
    }
    table.CheckOrderBind = function (that) {
        if (event) {
            event.stopPropagation();
        } else {
            window.e.returnValue = false;
        };

        var isChecked = that.checked;
        var $this = $(that);
        var row = table.rows[$this.attr('data-index')];
        if (row.IsCantSendGood) {
            $this.checked = false;
            $this.closest(".order-row").removeClass("active");
            layer.msg(row.CantSendReason);
            return;
        }
        row.checked = isChecked; //数据选中标识
        if (isChecked) {
            $this.closest(".layui-row-item").addClass("active");
            $this.closest(".layui-row").addClass("active");
            $this.closest(".layui-mytable-tr-main").addClass("wu-activeBColor");

        }
        else {
            $this.closest(".layui-row-item").removeClass("active");
            $this.closest(".layui-row").removeClass("active");
            $this.closest(".layui-mytable-tr-main").removeClass("wu-activeBColor");

        }

        var isAll = false;
        if (commonModule.FxPageType() != 2) {
            var rowCount = 0;
            $(table.rows).each(function (i, first) {
                $(first.SecondSubOrders).each(function (i2, second) {
                    rowCount++;
                });
            });
            isAll = $(".order-chx:checked").length == rowCount;
        }
        else
            isAll = $(".order-chx:checked").length == table.rows.length;
        $("#orderNum").text($(".order-chx:checked").length);
        $("#allcheckorder")[0].checked = isAll;
    }
    table.CheckShowOrder = function (that) {
        if (event) {
            event.stopPropagation();
        } else {
            window.e.returnValue = false;
        };
        var isZK = $(that).hasClass("zk");
        if (isZK) {
            $(that).closest(".layui-mytable-tr").find(".icon-a-chevron-down1x").removeClass("zk");
            $(that).closest(".layui-mytable-tr").find(".showMoreProductBtn").removeClass("zk").find("#more-product-btn-text").text("更多").siblings('i').removeClass('icon-a-chevron-up1x').addClass('icon-a-chevron-down1x');
            $(that).closest(".layui-row-item").next(".layui-mytable-tr-showItems").css({ display: "none" });
        } else {
            $(that).closest(".layui-mytable-tr").find(".icon-a-chevron-down1x").addClass("zk");
            $(that).closest(".layui-mytable-tr").find(".showMoreProductBtn").addClass("zk").find("#more-product-btn-text").text("收起").siblings('i').removeClass('icon-a-chevron-down1x').addClass('icon-a-chevron-up1x');
            $(that).closest(".layui-row-item").next(".layui-mytable-tr-showItems").css({ display: "block" });
        }
    }

    var _renderRow = function (cols, row) {
        if (!row.Id)
            return;

        var isWaitPrintOrder = commonModule.FxPageType() == 2;
        row.IsWaitPrintOrder = isWaitPrintOrder; // 是否是待打印列表订单

        //订单项勾选判断
        var status = row.ErpState || "";
        $(row.SubOrders).each(function (index, subRow) {
            var chx = false;//子订单全选状态
            //待买家付款：除了待付款商品，其它商品状态勾选去掉
            //待发货：退款成功、商品关闭、已发货、已确认收货的商品勾选去掉
            //待买家确认收货和交易成功：商品关闭、退款成功的商品勾选去掉
            //交易关闭：所有商品都勾选上
            if (subRow.RefundStatus == "REFUND_CLOSE")
                subRow.RefundStatus = "";

            if (status == "waitsellersend") { //待发货
                if ((subRow.RefundStatus != null && subRow.RefundStatus != '') ||
                    subRow.Status == "waitbuyerreceive" ||
                    subRow.Status == "signinsuccess" ||
                    subRow.Status == "confirm_goods_but_not_fund" ||
                    subRow.Status == 'close' ||
                    subRow.Status == 'cancel' ||
                    subRow.Status == 'success') {
                    if (subRow.RefundStatus == "REFUND_CLOSE")
                        chx = true;
                    else
                        chx = false;
                } else
                    chx = true;
            } else if (status == "waitbuyerreceive" || status == "success") { //待收货
                if (subRow.RefundStatus != "REFUND_SUCCESS")
                    chx = true;
            } else if (status == "close" ||
                status == "cancel" && (subRow.Status == 'close' || subRow.Status == 'cancel')) {
                chx = true;
            }
            //勾选全部商品配置项，勾选后忽略上面的判断默认全部订单项都勾选
            var allorderitemChecked = $("#chk_allorderitem").prop("checked");
            if (allorderitemChecked) {
                chx = true;
            }
            //// 异常地址默认不勾选
            //if (row.IsFake)
            //    chx = false;
            subRow.checked = chx;
        });

        // 商品设置勾选状态后，才能生成打印内容
        row.PrintInfo = printContentFormatSetModule.ForatPrintContent(row);
        var rowClass = "";
        if (row.IsCantSendGood)
            rowClass = " order-row-cantsend ";
        var html = '<div id="order-' + row.Index + '" data-index="' + row.Index + '" data-id="' + row.Id + '" data-pid="' + row.LogicOrderId + '" class="layui-row layui-mytable-tr layui-row-item" onclick="orderTableBuilder.row_click_handler.bind(this)()">';
        //var html = '<tr id="order-' + row.Id + '" data-index="' + row.Index + '" class="order-row' + rowClass + (row.IsLocked == "1" ? " suotouColor" : "") + (row.checked == true ? " onClickColor" : "") + (row.zk == true ? " showMoreColor" : "") + '" onclick="orderTableBuilder.row_click_handler.bind(this)()">';
        $(cols).each(function (index2, col) {
            var style = getColStyle(col);
            var className = col.className ? col.className : "";
            className += " " + col.field;
            className += " order-column-" + col.field;
            //html += "<td style='" + style + "' class='" + className + "'>"
            if (col.field == "checkbox") {
                html += '<div class="layui-col-md1 mytable-tr-oparete wu-checkboxWrap' + className + '" style="' + style + '">';
                html += defaultContentFormatter(row, col, row.Index);
                html += "</div>";
            }
            else if (col.field == "Reciever") {
                html += '<div class="layui-col-md1 typeOne ' + className + '" style="' + style + '">';
                html += defaultContentFormatter(row, col, row.Index);
                html += "</div>";
            }
            else if (col.field == "RecieverAddress") {
                html += defaultContentFormatter(row, col, row.Index);
            }
            else if (col.field == "Detail") {
                html += '<div class="layui-col-md1 mytable-tr-oparete02 ' + className + '" style="' + style + '">';
                html += defaultContentFormatter(row, col, row.Index);
                html += "</div>";
            }
            else if (col.field == "ProductShow") {
                html += '<div class="layui-col-md2 order-column-header-ProductShow ProductShow' + className + '" style="' + style + '">';
                html += defaultContentFormatter(row, col, row.Index);
                html += "</div>";
            }
            else {
                html += '<div class="layui-col-md1 ' + className + '" style="' + style + '">';
                html += defaultContentFormatter(row, col, row.Index);
                html += "</div>";
            }
        });
        html += "</div>";
        //html += "</tr>";
        //TODO:订单详细信息
        if (isWaitPrintOrder)
            html += _renderDetailInfo(row);
        //html += _renderListPicInfo(row);
        return html;
    }

    var getColStyle = function (col) {
        var style = "";
        if (col.align)
            style += "text-align:" + col.align + ";";
        if (!col.display)
            style += "display:none;";
        if (col.width)
            style += "width:" + col.width + ";";
        if (col.minWidth)
            style += "min-width:" + col.minWidth + ";";
        if (col.maxWidth)
            style += "max-width:" + col.maxWidth + ";";
        if (col.flex)
            style += "flex:" + col.flex + ";";
        return style;
    }

    var IsWhite = function (callback) {
        if (_isWhite == null) {
            commonModule.Ajax({
                url: '/Common/IsWhiteFxUser',
                //loading: true,
                async: false,
                data: {},
                success: function (res) {
                    if (res.Success) {
                        _isWhite = res.Data;

                        var pt = commonModule.getQueryVariable("pt") ? commonModule.getQueryVariable("pt") : commonModule.CloudPlatformType;
                        if (pt != undefined) {
                            if (pt.toLowerCase() == "toutiao" && _isWhite) {  //导出显示  只有抖店和白名单用户才会 显示 订单运费 平台补贴 订单实收款
                                $("body").addClass("body-c-toutiao");
                            }
                        }
                    } else {
                        layer.msg("获取部分配置失败，请重试");
                        console.log(res.Message);
                    }
                    callback();
                }
            });
        } else callback();
    }

    var _renderPrice = function (row) {

        if (!row)
            return;
        //是否显示销售价格
        if (commonModule.FxPageType() == 2) {
            if (row.IsShowSalePrice == false) {
                row.TotalAmount = "**";
                for (var s = 0; s < row.SubOrders.length; s++) {
                    row.SubOrders[s].Price = "**";
                    row.SubOrders[s].TotalAmount = "**";
                    row.SubOrders[s].ItemAmount = "**";
                }
            }
        }
        else {
            if (row.IsShowSalePrice == false) {
                row.SumTotalAmount = "**";
                for (var s = 0; s < row.SecondSubOrders.length; s++) {
                    var second = row.SecondSubOrders[s];
                    for (var t = 0; t < second.ThressSubOrders.length; t++) {
                        row.SecondSubOrders[s].ThressSubOrders[t].Price = "**";
                    }
                }
            }
        }


        ////是否显示销售价格
        //for (var i = 0; i < AgentIsSalePrices.length; i++) {
        //    var isSale = AgentIsSalePrices[i];
        //    if (commonModule.FxPageType() == 2) {
        //        // 打单发货价格
        //        if (row.UpFxUserId == isSale.FxUserId) {
        //            if (isSale.Value == "false") {
        //                row.TotalAmount = "**";
        //                for (var s = 0; s < row.SubOrders.length; s++) {
        //                    row.SubOrders[s].Price = "**";
        //                    row.SubOrders[s].TotalAmount = "**";
        //                    row.SubOrders[s].ItemAmount = "**";
        //                }
        //            }
        //        }
        //    }
        //    else {

        //        //所有订单销售价
        //        var newSecondSubOrders = [];
        //        for (var s = 0; s < row.SecondSubOrders.length; s++) {
        //            var second = row.SecondSubOrders[s];
        //            if (second.UpFxUserId == isSale.FxUserId) {
        //                if (isSale.Value == "false") {
        //                    row.SumTotalAmount = "**";
        //                    for (var t = 0; t < second.ThressSubOrders.length; t++) {
        //                        row.SecondSubOrders[s].ThressSubOrders[t].Price = "**";
        //                    }
        //                    //second.ThressSubOrders = newThressSubOrders;
        //                }
        //            }
        //            //newSecondSubOrders.push(second);
        //        }
        //    //row.SecondSubOrders = newSecondSubOrders;
        //    }
        //}
    }

    // 图片显示
    function newTransformImgSrc(src) {
        var newSrc = '';
        if (src != null && src != undefined && src != "//.") {
            if (!src || (src.indexOf('http') > -1 || src.indexOf('https') > -1)) {
                newSrc = src
            } else {
                var token = $("#token_input").val() ? $("#token_input").val() : '';
                newSrc = src + '&platform=' + commonModule.CloudPlatformType + '&token=' + token;
            }
        }
        return newSrc;
    }

    // 所有订单-是否有手工发货的按钮权限
    function CheckOnlineSendManualAuth() {
        commonModule.FxPermission(function (p) {
            commonModule.Ajax({
                type: "POST",
                url: "/SubAccount/CheckPermission",
                data: { permission: p.OnlineSendManual },
                success: function (rsp) {
                    if (rsp.Success) {
                        if (rsp.Data == true) {
                            isManualAuth = true;
                        } else {
                            isManualAuth = false;
                        }
                    } else {
                        isManualAuth = false;
                    }
                }
            });
        });
    }
    var _renderAllOrderRow = function (cols, row) {



        //是否显示销售价格
        _renderPrice(row);
        //for (var i = 0; i < AgentIsSalePrices.length; i++) {
        //    var isSale = AgentIsSalePrices[i];
        //    var newSecondSubOrders = [];
        //    for (var s = 0; s < row.SecondSubOrders.length; s++) {
        //        var second = row.SecondSubOrders[s];
        //        if (second.FxUserId == isSale.FxUserId) {
        //            if (isSale.Value == "false") {
        //                row.SumTotalAmount = "**";
        //                for (var t = 0; t < second.ThressSubOrders.length; t++) {
        //                    row.SecondSubOrders[s].ThressSubOrders[t].Price = "**";
        //                }
        //                //second.ThressSubOrders = newThressSubOrders;
        //            }
        //        }
        //        //newSecondSubOrders.push(second);
        //    }
        //    //row.SecondSubOrders = newSecondSubOrders;
        //}

        $.views.converters({
            upperImgSrc: function (val) {
                return newTransformImgSrc(val);
            }
        })

        row.LastShipTimeHtml = allOrderTimeContentFormatter(row);
        row.TagsHtml = table.TagsContentFormatter(row);
        //alert(OrderDisplaySet);
        row.OrderDisplaySet = OrderDisplaySet;
        ExpressCodeContentAllOrderFormatter(row);
        var orderDetailTmpl = $.templates("#allorder-detail-tmpl");
        var status = $("#orderstatus li.active").attr("data-status");
        row.tabStatus = status;
        row.WarehouseName = ""; // 仓库名称
        var warehouseData = commonModule.allWarehouseInfoList.find(function (item) {
            return item.Id == row.WarehouseId;
        });
        if (warehouseData) {
            row.WarehouseName = warehouseData.Name;
        }

        var waitsellersendManualOrderAuth = window.sessionStorage.getItem("waitsellersendManualOrderAuth");

        var html = orderDetailTmpl.render(
            row, {
                IsWhite: _isWhite,
                OrderCombine: OrderCombine,
                supportPlatformtTypes: supportPlatformtTypes,
                waitsellersendManualOrderAuthFlag: waitsellersendManualOrderAuth,
                isOnlineSendManualAuthFlag: isManualAuth
            });
        return html;
    }

    var _renderCheckOrderRow = function (cols, row) {
        $.views.converters({
            upperImgSrc: function (val) {
                return newTransformImgSrc(val);
            }
        })
        //是否显示销售价格
        _renderPrice(row);
        row.LastShipTimeHtml = allOrderTimeContentFormatter(row);
        row.TagsHtml = table.TagsContentFormatter(row);
        row.OrderDisplaySet = OrderDisplaySet;
        var orderDetailTmpl = $.templates("#checkorder-detail-tmpl");
        var status = $("#orderstatus li.active").attr("data-status");
        row.tabStatus = status;
        row.WarehouseName = ""; // 仓库名称
        var warehouseData = commonModule.allWarehouseInfoList.find(function (item) {
            return item.Id == row.WarehouseId;
        });
        if (warehouseData) {
            row.WarehouseName = warehouseData.Name;
        }
        var html = orderDetailTmpl.render(row, { OrderCombine: OrderCombine, supportPlatformtTypes: supportPlatformtTypes });
        return html;
    }

    var _renderDetailInfo = function (row) {
        //row.TagsHtml = table.TagsContentFormatter(row);
        $.views.converters({
            upperImgSrc: function (val) {
                return newTransformImgSrc(val);
            }
        })
        row.OrderDisplaySet = OrderDisplaySet;
        var orderDetailTmpl = $.templates("#waitorder-detail-tmpl");
        var html = orderDetailTmpl.render(row, { OrderCombine: OrderCombine, supportPlatformtTypes: supportPlatformtTypes });
        return html;

    }

    table.row_click_handler = function () {
        if (_triggerRecycle) {
            _triggerRecycle = false;
            return;
        }
        var chx = $(this).find(".order-chx")[0];
        var newChx = $(chx).prop("checked", !chx.checked);
        table.CheckOrderBind(newChx[0]);
    }

    table.row_clickedit_handler = function () {
        window.e.returnValue = false;
    }
    table.recovery_row_click_handler = function () {
        var chx = $(this).find(".recovery-chx")[0];
        var newChx = $(chx).prop("checked", !chx.checked);
        //table.CheckOrderBind(newChx[0]);
    }


    table.SetPageSize = function (pageSize) {
        _pageSize = pageSize;
    }

    table.GetPageSize = function () {
        return _pageSize || 20;
    }

    table.SetDefaultPageIndex = function () {
        _pageIndex = 1;
    }

    var _isQuerying = false;
    table.IsQuerying = function getIsQuerying() {
        return _isQuerying;
    }
    table.ResetIsQuerying = function () {
        _isQuerying = false;
    }

    table.DefaultDisplayORHide = function () {
        if (commonModule.FxPageType() == 2) {
            var waitorderallshow = WaitorderAllShow;
            if (waitorderallshow == 1) {
                $($("#allshoworder")[0]).prop("checked", true);
                $(".layui-mytable .layui-mytable-tr-showItems").css({ display: "block" });
                $(".layui-row-item .icon-a-chevron-down1x").addClass("zk");
                $(".layui-row-item .showMoreProductBtn").addClass("zk").find("#more-product-btn-text").text("收起").siblings('i').removeClass('icon-a-chevron-down1x').addClass('icon-a-chevron-up1x');
            }
            else {
                $($("#allshoworder")[0]).prop("checked", false);
                $(".layui-mytable .layui-mytable-tr-showItems").css({ display: "none" });
                $(".layui-row-item .icon-a-chevron-down1x").removeClass("zk");
                $(".layui-row-item .showMoreProductBtn").removeClass("zk").find("#more-product-btn-text").text("更多").siblings('i').removeClass('icon-a-chevron-up1x').addClass('icon-a-chevron-down1x');
            }
        }
    }

    table.HotData = [];
    table.ColdData = [];

    //从接口加载数据
    var setProductCount = 2;
    var setProductTitleShowValue = 0;

    var OrderCombine = null;
    
    table.load = function (element, options, isPaging, isPageSizeChange) {
        commonModule.LoadCommonSetting("setProductPicShowCountName", false, function (rsp) {
            if (rsp.Success) {
                if (rsp.Data != null) {
                    setProductCount = rsp.Data;
                }
            }
        });
        commonModule.LoadCommonSetting("setProductTitleShowName", false, function (rsp) {
            if (rsp.Success) {
                if (rsp.Data != null) {
                    setProductTitleShowValue = rsp.Data;
                }
            }
        });

        if (!element)
            element = $("#order-list-table")[0];
        table.target = element;

        options.PageSize = options.PageSize || _pageSize;
        options.PageIndex = options.PageIndex || _pageIndex;
        options.OrderByField = options.OrderByField || "o.CreateTime";
        if (!isPaging || isPageSizeChange) {
            _pageIndex = 1;
            options.PageIndex = _pageIndex;
        }

        element.innerHTML = "";
        options.isFilterDangerChar = false; //不过滤危险字符
        var msg = "查询中...";

        $('#hfIsContinueForStock').val() == "1";  //部分订单项库存不足，是否继续


        options.TarType = commonModule.getQueryVariable("isTar");
        //1688 待付款列表
        url = location.href.split('?')[0];
        if (url.indexOf("AliIncludePayOrder") != -1) {
            var orderStatus = $("#orderstatus .active").attr("data-status") || "";
            if (orderStatus == "purchased_waitpay" || orderStatus == "paid_waitsellersend" || orderStatus == "paid_waitbuyerreceive") {
                options.IsPrepayOrder = true;
            }
        }

        // 查询时禁用查询、高级查询和导出订单
        _isQuerying = true;
        options.requestUrl = location.href.split('?')[0];
        if (options.Filters.length) {
            var newOptions = JSON.parse(JSON.stringify(options));

            var obj = { "TableAlias": "flag", "FieldType": "string", "TableName": "P_OrderTags", "Name": "ExceptionOrder", "Value": "NotRelationBaseProduct", "Contract": "IN", "CustomQuery": "SearchOrderTags", "ExtValue": "LogicOrder" }
            newOptions.Filters.push(obj)
            newOptions.Filters = newOptions.Filters
            localStorage.setItem("newOrderListOptions", JSON.stringify(newOptions))
        }
        optionsData = options;

        CheckOnlineSendManualAuth(); // 所有订单-是否有手工发货的按钮权限

        //TODO:加载数据
        common.Ajax({
            url: '/NewOrder/List',
            data: options,
            type: 'POST',
            loadingMessage: msg,
            showMasker: false,
            success: function (rsp) {
                _isQuerying = false;
                console.timeEnd("获取订单");    
                if (rsp.Success) {
                    //var logId = rsp.Data.LogId;
                    table.Setting = rsp.Data.Setting;
                    table.rows = rsp.Data.Rows;

                    var isFxAutoMerger = rsp.Data.IsFxShopAutoMergerOpened;
                    var RemarkSetting = rsp.Data.RemarkSetting;
                    var isShowTag = true; //订单列表代，自标签显示 ,只在所有订单隐藏
                    if (location.href.indexOf('NewOrder/AllOrder') > -1) {
                        isShowTag = false;
                    }
                    for (var i = 0; i < table.rows.length; i++) {
                        var item = table.rows[i];
                        item.RemarkSetting = RemarkSetting;
                        item.isShowTag = isShowTag;
                        var sub_list = [];
                        if (item.SubOrders && item.SubOrders.length) {
                            item.SubOrders = commonModule.listTransformImgSrc(item.SubOrders)
                            sub_list = item.SubOrders;
                        }
                        if (item.SecondSubOrders && item.SecondSubOrders.length) {

                            for (var j = 0; j < item.SecondSubOrders.length; j++) {
                                var jData = item.SecondSubOrders[j];
                                jData.RemarkSetting = RemarkSetting;
                                jData.ThressSubOrders = commonModule.listTransformImgSrc(jData.ThressSubOrders)
                                if (jData.PrintscreenPics && jData.PrintscreenPics.length) {
                                    for (var PrintscreenPicsIndex = 0; PrintscreenPicsIndex < jData.PrintscreenPics.length; PrintscreenPicsIndex++) {
                                        var kData = jData.PrintscreenPics[PrintscreenPicsIndex];
                                        if (kData.ImageUrl) {
                                            if (!(kData.ImageUrl.indexOf('http') > -1 || kData.ImageUrl.indexOf('https') > -1)) {
                                                kData.ImageUrl = kData.ImageUrl + _businessType;
                                            }
                                        }
                                    }
                                }
                                for (var k = 0; k < jData.ThressSubOrders.length; k++) {
                                    var kData = jData.ThressSubOrders[k];
                                    kData.RemarkSetting = RemarkSetting;
                                }
                            }
                        }

                        if (sub_list.length) {
                            for (var k = 0; k < sub_list.length; k++) {
                                var kData = sub_list[k];
                                kData.RemarkSetting = RemarkSetting;
                            }
                        }

                    }
                    localStorage.removeItem('query_logicorderid_input');
                    var pid = $("#SearchContainer .PlatformOrderId").val() || "";
                    var loid = $("#SearchContainer .LogicOrderId").val() || "";
                    var waybillcode = $("#SearchContainer .LastWaybillCode").val() || "";
                    var erporderstatus = $("#SearchContainer .ErpOrderStatus").val() || "";//订单状态

                    if (commonModule.FxPageType() == 2) {

                        $("#OrderTotalCount").attr("data-total", rsp.Data.RealOrderTotal).html(rsp.Data.RealOrderTotal);
                        //var isSearchWaitSellerSendState = erporderstatus == "waitsellersend";//是否搜索了“待发货”状态
                        //if (isSearchWaitSellerSendState && (pid != "" || loid != "" || waybillcode != "")) {//有这三个搜索条件之一也不禁用
                        //    isSearchWaitSellerSendState = false;
                        //}
                        // 更新隐藏设置非合单的拆字
                        $(table.rows).each(function (i, r) {
                            r.NoPrintFlag = false;
                            for (var i = 0; i < r.WaybillCodes.length; i++) {
                                if (r.WaybillCodes[i].IsPreviewed == false && r.PrintState == 0) {
                                    r.NoPrintFlag = true; // 已打印订单打印状态未更新
                                    break;
                                }
                            }
                            if (r.SubOrders && r.SubOrders.length > 0) {
                                $(r.SubOrders).each(function (j, so) {
                                    so.IsFxAutoMerger = isFxAutoMerger;
                                    //so.StrDisabled = (isSearchWaitSellerSendState && (so.Status == "waitbuyerreceive")) ? " disabled=\"disabled\"" : "";//搜索了“待发货”且当前状态为“已发货”=禁用；先取消此功能。
                                });
                            }

                            // 创建 _order_waybillCode_dict 
                            //_order_waybillCode_dict[r.Id] = r.WaybillCodes;
                        });

                        $("#OrderTagsQueryInput").val("").attr("tagName", "");
                    }
                    else if (commonModule.FxPageType() == 3) {
                        $("#orderstatus li .i-order-count").remove();
                        $("#orderstatus li.active").append('<span class="i-order-count">[' + rsp.Data.RealOrderTotal + ']' + orderExplainRender() + '</span>');
                    }
                    else {
                        //所有订单页面，选项卡不设置为“全部”
                        if ((pid != "" || loid != "" || waybillcode != "") && commonModule.FxPageType() != 1) {
                            $("#orderstatus li .i-order-count").remove();
                            $("#orderstatus li.active").removeClass("active");
                            var $all = $("#orderstatus li[data-status='all']");
                            $all.addClass("active");
                            $all.append('<span class="i-order-count">（平台单' + rsp.Data.PtOrderTotal + '/拆单后' + rsp.Data.RealOrderTotal + '）</span>');

                        }
                        else {
                            //系统订单数量
                            $("#orderstatus li .i-order-count").remove();
                            $("#orderstatus li.active").append('<span class="i-order-count">（平台单' + rsp.Data.PtOrderTotal + '/拆单后' + rsp.Data.RealOrderTotal + '）' + orderExplainRender() + '</span>');

                            //移上标题500毫秒后，出现（平台单、拆单后）说明方案
                            var timerTableContentImgs = null;
                            $("#orderstatus li .i-order-count").hover(function () {
                                var $this = $(this);
                                timerTableContentImgs = setTimeout(function () {
                                    $this.children(".orderExplain-wrap").show();
                                }, 600)
                            }, function () {
                                $(this).children(".orderExplain-wrap").hide();
                                clearTimeout(timerTableContentImgs);
                            });

                            if (rsp.Data.Total == 0) {
                                $(".aliIncludePayOrderTableWrape").addClass("noDataWrap");

                            } else {
                                $(".aliIncludePayOrderTableWrape").removeClass("noDataWrap");

                            }
                        }
                    }

                    if (commonModule.IsCrossBorderSite == true && commonModule.FxPageType() != 3) {
                        $("body").addClass("body-c-tiktok");
                    }
                    //选择数量归零
                    $("#orderNum").text("0");
                    $("#dataFlag_input").val(rsp.Data.dataFlag);
                    //根据订单ID查询，不管什么状态都需要显示发货按钮
                    var poSearchField = $('#SeachConditions  input[name="PlatformOrderId"]');
                    if (poSearchField && poSearchField.length == 1) {
                        var val = poSearchField.val();
                        if (val != "" && val.trim() != "") {
                            $(".batch-send-btnonly-in-order-list").show();
                        }
                    }
                    //// 时间筛选的text和val值
                    //var name = $("#QueryDate>option:selected").text();
                    //var val = $("#QueryDate>option:selected").val();
                    //if (name != "" && val != "") {
                    //    orderTableBuilder.changeOrderTime(val, name, false);
                    //}
                    var date1 = new Date();  //开始时间
                    console.time("渲染表格");
                    
                    // 判断订单关联开关是否有开启
                    commonModule.getBaseProductSetting(function (rspData) {
                        OrderCombine = rspData.OrderCombine


                        //var str = location.href.indexOf('NewOrder/AllOrder');
                        //if (location.href.indexOf('NewOrder/AliIncludePayOrder') > -1) {
                        //    str = location.href.indexOf('NewOrder/AliIncludePayOrder')
                        //}
                        //str = str > -1 ? str : location.href.indexOf('/NewOrder/WaitOrder');
                        //OrderCombine = str > -1 ? OrderCombine : false;
                        if (location.href.indexOf('NewOrder/AliIncludePayOrder') > -1 || location.href.indexOf('NewOrder/AllOrder') > -1 || location.href.indexOf('/NewOrder/WaitOrder') > -1 || location.href.indexOf('/NewOrder/AliIncludeOrder') > -1) {
                            OrderCombine = OrderCombine || false;
                        }
                        
                        // 判断是否为白名单用户
                        IsWhite(function () {
                            //console.log(_isWhite);
                            //2024-01-28 冷热分离 打单发货列表 按订单号/运单号查询 有冷热数据 分tab 页显示

                            if (commonModule.FxPageType() == 2) {
                                table.HotData = []; //热数据
                                table.ColdData = []; //冷数据
                                $("#div_hot_cold_tag").css('display', 'none'); //默认先隐藏 冷热数据分开显示的 按钮
                                var loIdSearchCondition = $.trim($("#SearchContainer .LogicOrderId").val());
                                var poIdSearchCondition = $.trim($("#SearchContainer .PlatformOrderId").val());
                                var wcIdSearchCondition = $.trim($("#SearchContainer .LastWaybillCode").val());
                                if (loIdSearchCondition || poIdSearchCondition || wcIdSearchCondition) {
                                    for (var i = 0; i < table.rows.length; i++) {
                                        var row = table.rows[i];
                                        if (row.IsCold == 1)
                                            table.ColdData.push(row);
                                        else
                                            table.HotData.push(row);
                                    }

                                    //存在冷热数据混合的情况，分tab页显示
                                    if (table.HotData.length > 0 && table.ColdData.length > 0) {
                                        $("#div_hot_cold_tag").css('display', 'block');
                                        $('#sortBtn').show();
                                        table.RenderData(0);//_render(element, table.HotData);
                                    }
                                    else {
                                        _render(element, table.rows);
                                    }
                                }
                                else {
                                    _render(element, table.rows);
                                }
                            }
                            else {
                                _render(element, table.rows);
                            }
                            setTimeout(function () {
                                // 留言/备注的新老样式判断
                                if (commonModule.FxPageType() == 1) {
                                    $('#OrderTableList').find('.old-style-box').hide();
                                    $('#OrderTableList').find('.new-style-box').show();
                                } else if (commonModule.FxPageType() == 3) {
                                    $('#OrderTableList').find('.old-style-box').show();
                                    $('#OrderTableList').find('.new-style-box').hide();
                                }
                            }, 500);
                        });
                        var _selectOrderList = localStorage.getItem("selectOrderList");
                        _selectOrderList = _selectOrderList ? JSON.parse(_selectOrderList) : [];
                        if (_selectOrderList && _selectOrderList.length) {
                            for (var i = 0; i < _selectOrderList.length; i++) {
                                var item = _selectOrderList[i];
                                var isThis = 'div[data-id="' + item.Id + '"]';
                                $(isThis).click();
                            }
                            //localStorage.removeItem("selectOrderList");
                        }
                    })

                    console.timeEnd("渲染表格");
                    var tarHref = window.location.href; 
                    // 所有订单，打单发货开启页面长时间无操作提示        
                    if (tarHref.indexOf("/NewOrder/WaitOrder") != -1 || tarHref.indexOf("/NewOrder/AllOrder") != -1) {
                        commonModule.monitoringWithoutOperation(); //监控用户操作
                    }
                    if (commonModule.FxPageType() == 2)  //待发货列表
                        _loadRemoteProvinceRecommentLosgitisc(table.rows); //渲染推荐物流
                    //默认展示详情或隐藏
                    //orderTableBuilder.DefaultDisplayORHide();

                    //快递是否可达和预警单过滤在_renderOhterRow时过滤



                    //异步执行，不影响页面加载
                    setTimeout(function () {
                        $('#sp_free_order_count').text(rsp.Data.Total);
                        var layout = ['count', 'prev', 'page', 'next', 'limit', 'skip'];
                        if (common.isUseOldTheme)
                            layout = ['limit', 'prev', 'page', 'next'];
                        if (!isPaging || isPageSizeChange) {
                            layui.laypage.render({
                                elem: 'paging'
                                , count: rsp.Data.Total
                                , limit: _pageSize
                                , curr: _pageIndex
                                , limits: [20, 40, 50, 60, 100, 200, 300, 400, 500]
                                , theme: ' wu-page wu-one'
                                ,layout: layout
                                , jump: function (obj, first) {
                                    //$(".layui-laypage-count").hide(); // 隐藏分页总条数
                                    if (!first) {
                                        _pageIndex = obj.curr;
                                        var isPageSizeChange = false;
                                        if (_pageSize != obj.limit) {
                                            _pageSize = obj.limit;
                                            //savePageSize(obj.limit);
                                            orderPrintModule.SavePaggingSet(_pageSize)
                                            isPageSizeChange = true;
                                        }

                                        options.PageSize = _pageSize;
                                        options.PageIndex = _pageIndex;

                                        table.load(table.target, options, true, isPageSizeChange);
                                    }

                                    $('html , body').animate({ scrollTop: 0 }, 0);
                                }
                            });


                        }
                        //var t = addTmplInOrderListModule.GetCurrentTemplate()
                        //table.changeTemplate(t);

                    }, 10);

                       

                    // 是否当前跨境tk云站点
                    if (commonModule.IsCrossBorderSite) {
                        setTimeout(function (){
                            var orderStatus = $("#SearchContainer").find(".ErpOrderStatus").val() || "";
                            if (orderStatus == 'waitsellersend') {
                                $('#OnlineSend').show().removeClass('onlineSendNone');
                            } else {
                                $('#OnlineSend').hide().addClass('onlineSendNone');
                            }
                        }, 50);
                    }
                }
                else {
                    if (rsp.ErrorCode == 'limit_reule_error') {
                        common.LimitTimeDailg(rsp.ErrorCode);
                    }
                    else if (rsp.ErrorCode == 'FX_PAGEDEPTHCONTROL') {
                        common.PageDepthControlDailg('当前操作频繁，请使用导出功能！');
                    }
                    else if (rsp.ErrorCode == 'ParamErr') {
                        layer.msg("输入内容包含无效字符或超过64位限制");
                    }
                    else if (rsp.ErrorCode != 2 && rsp.ErrorCode != 'auth_expires') {
                        layer.msg("数据加载失败，请刷新重试");
                    }
                }
            }
        });

    }

    //冷热数据分开渲染
    table.RenderData = function (isCold) {

        var btnWaitSendData = $("#btnWaitSendData");
        btnWaitSendData.removeClass("layui-btn-normal");
        btnWaitSendData.removeClass("layui-btn-primary");

        var btnOtherData = $("#btnOtherData");
        btnOtherData.removeClass("layui-btn-normal");
        btnOtherData.removeClass("layui-btn-primary");

        if (isCold == 0) {
            table.rows = table.HotData;
            _render(table.target, table.HotData);
            btnWaitSendData.addClass("layui-btn-normal");
            btnOtherData.addClass("layui-btn-primary");
        }
        else {
            table.rows = table.ColdData;
            _render(table.target, table.ColdData);
            btnOtherData.addClass("layui-btn-normal");
            btnWaitSendData.addClass("layui-btn-primary");
        }
    }

    //平台单和、拆单订单说明
    function orderExplainRender() {
        var orderExplainHtml = '';
        orderExplainHtml += '<div class="orderExplain-wrap">';
        orderExplainHtml += '<div>';
        orderExplainHtml += '<span style="color:#04385d;">平台订单数量：</span>';
        orderExplainHtml += '<span>同步店铺后台的待发货数量，但不包括待发货中的退款订单</span>';
        orderExplainHtml += '</div>';
        orderExplainHtml += '<div>';
        orderExplainHtml += '<span style="color:#04385d;">系统订单数量：</span>';
        orderExplainHtml += '<span>平台订单同步到分销系统后，为了保护商家的隐私信息会根据商品代发关系自动拆分系统订单，厂家只能查看属于代发的商品信息</span>'
        orderExplainHtml += '</div>';
        orderExplainHtml += '<div class="orderExplain-wrap-down">';
        orderExplainHtml += '<div style="color:#04385d;">举个例子：</div>';
        orderExplainHtml += '<div><s style="color:#ff511c">一个</s>平台订单含有多件商品，其中只有一件绑定了厂家</div>';
        orderExplainHtml += '<div>那么显示<s style="color:#ff511c">两个</s>系统单数量：1（厂家看的订单）+1（自己发货的订单）</div>';
        orderExplainHtml += '</div>';
        orderExplainHtml += '</div>';
        return orderExplainHtml;
    }

    function BatchBtnTipRender(type) {
        var orderExplainHtml = '';
        orderExplainHtml += '<div class="orderOperateExplain-wrap">';
        orderExplainHtml += '<div>';

        if (type == "batchCheck") // 批量审核
            orderExplainHtml += '<span>点击后商品按原有绑定关系推送给厂家</span>';
        else if (type == "batchManualPushSelf")  // 批量设自营
            orderExplainHtml += '<span>点击后商品将推送到自己的打单发货页面</span>';
        else if (type == "batchManualPushSupplier")  // 批量指定厂家
            orderExplainHtml += '<span>点击后该订单推送给最新指定的厂家发货，不改变商品绑定关系</span>';

        orderExplainHtml += '</div>';
        orderExplainHtml += '</div>';
        return orderExplainHtml;
    }

    var _hideNoPermNav = function () {
        var showPermDict = {};
        if (commonModule.FxPageType() == 1) {
            showPermDict = commonModule.AllOrderShowPermDict;
        }
        else if (commonModule.FxPageType() == 2) {
            showPermDict = commonModule.WaitOrderShowPermDict;
        }
        else if (commonModule.FxPageType() == 3) {
            showPermDict = commonModule.OfflineOrderShowPermDict;
        }
        commonModule.HideNoPermDiv(showPermDict);
    }

    //刷新table
    table.refresh = function (options) {
        table.load(table.target, options);
    }

    //刷新指定的行
    table.refreshRow = function (row) {
        orderTableBuilder.rows[row.Index] = row;
        var isCheck = $(".order-chx[data-index='" + row.Index + "']").prop("checked");
        var cols = common.Sort(table.columns, "order", false);
        if (commonModule.FxPageType() == 2) {
            var html = _renderRow(cols, row);
            var div = $("#order-" + row.Index);
            var divDetail = $("#order-detail-" + row.Index);
            divDetail.remove();
            div.replaceWith(html);
        }
        else {
            var status = $("#orderstatus .active").attr("data-status") || "";
            var html = "";

            var tarHref = window.location.href;
            if (tarHref.indexOf("AliIncludePayOrder") != -1 || tarHref.indexOf("AliIncludeOrder") != -1) {
                if ($("#OrderTableList .aliInclude-body").length > 0) {
                    html = _renderAllOrderRow(cols, row);
                    var div = $("#order-detail-" + row.Index).closest(".aliInclude-body");
                    div.replaceWith(html);

                } else {
                    html = _renderAllOrderRow(cols, row);
                    var div = $("#order-detail-" + row.Index);
                    div.replaceWith(html);
                }
            } else {
                if (status == "waitaudit") {
                    html = _renderCheckOrderRow(cols, row);
                } else {
                    html = _renderAllOrderRow(cols, row);
                }
                var div = $("#order-detail-" + row.Index);
                //div.remove();
                div.replaceWith(html);
            }
        }

        $(".order-chx[data-index='" + row.Index + "']").prop("checked", isCheck);

        _hideNoPermNav();
    }

    //刷新指定的列
    table.refreshColumn = function (columnFieldName, isBatch) {
        var column = {};
        for (var i = 0; i < table.columns.length; i++) {
            var cur = table.columns[i];
            if (cur.field == columnFieldName) {
                column = cur;
                break;
            }
        }
        if (column) {
            $(".order-column-" + column.field).each(function (ix, td) {
                var index = $(td).parent().attr("data-index");
                var row = table.rows[index];
                // 重置订单分类，生成html时重新获取
                if (columnFieldName == "OrderClassify" && ix == 0) {
                    orderClassifyArr = [];
                }

                var html = '';
                if (columnFieldName == "Product") {
                    var trHtml = _renderListPicInfo(row);
                    $("tr.listPicTr[data-id='" + row.Id + "']").replaceWith(trHtml);
                    orderPrintModule.RenderProductPic();
                    html = defaultContentFormatter(row, column, index);
                }
                else if (columnFieldName == "Reciever") {
                    html = defaultContentFormatter(row, column, index);
                    //$(td).replaceWith(html);
                }
                else {
                    html = defaultContentFormatter(row, column, index);
                }
                $(td).html(html);

                //// 快递不可达订单是否勾选
                //if (columnFieldName == "RecieverAddress") {
                //    var isCanFilterReach = $("#ExpressCanReachBtn").hasClass("active"); //过滤所有可达
                //    var isFilterReach = $("#ExpressReachBtn").hasClass("active"); //过滤所有不可达（忽略模板显示所有不可达订单）
                //    var isChecked = (isFilterReach && row.ExpressReachCheck) || (isCanFilterReach && !row.ExpressReachCheck );
                //    SetRowCheck(row, isChecked);
                //}
            });

        }

        // 重新生成html，绑定相应的事件
        if (columnFieldName == "OrderClassify") {
            orderPrintModule.InitOrderClassifyEvent();
        }
        if (columnFieldName == "LastExpressCode") {
            var template = addTmplInOrderListModule.GetCurrentTemplate();
            table.changeTemplate(template);
        }

        if (!isBatch) {
            _hideNoPermNav();
        }
    }

    table.refreshColumns = function (cols) {
        if (cols && cols.length > 0) {
            $(cols).each(function (i, colName) {
                if (colName)
                    table.refreshColumn(colName, true);
            });
            _hideNoPermNav();
        }
        else {
            _render(table.target, table.rows);
            // 商品图片显示位置       
            orderPrintModule.RenderProductPic();
        }
    }

    table.setPrintState = function (datas, printType) {
        if (datas && datas.length > 0) {
            var field = "";
            for (var i = 0; i < datas.length; i++) {
                var kv = datas[i];
                var ix = $(".order-chx[data-id='" + kv.OrderId + "']").attr("data-index");
                var row = table.rows[ix];
                //1.快递 2 发货单 3拿货单
                if (printType == 1) {
                    row.PrintState = kv.PrintState == null ? 1 : (kv.PrintState == 2 ? 2 : 1);
                    row.ExpressPrintTime = 1; //最后打印时间
                    row.PrintedSerialNumber = kv.PrintedSerialNumber; //打印序列号
                    row.ReceiverIsChange = false;//重置异常单标签
                    //对应商品Item标记已打印快递
                    var items = kv.OrderItems;
                    for (var x = 0; x < items.length; x++) {
                        var itemid = items[x];
                        $('#orderitem-' + itemid).html('<div title="已打印快递单" class="wu-f16 wu-color-d wu-weight600">√</div>');
                    }
                }
                else if (printType == 2) {
                    row.LastSendPrintTime = 1;
                }
                else if (printType == 3) {
                    row.LastNahuoPrintTime = 1;
                }
            }
            if (printType == 1)
                table.refreshColumns(["PrintedSerialNumber", "Reciever"]);
            table.refreshColumns(["PrintState", "ExpressCode", "OrderStatus", "Operator"]);
        }
    }

    /*****************
    获取选择的订单数据
    参数 orderId 指定的订单id，获取选中的订单时不用填写
    参数 isValidation 是否验证订单信息：验证商品有没有勾选
    参数 ignoreCheckOrderItem 忽略验证订单项
    参数 ignoreKsCheckOrderItem 忽略快手验证订单项
    *****************/
    table.getSelections = function (orderId, isValidation, needChildOrderId, ignoreCheckOrderItem, ignoreKsCheckOrderItem) {
        var selections = [];
        var ochxs = [];
        if (orderId)
            ochxs = $(".order-chx[data-id='" + orderId + "']")
        if (ochxs && ochxs.length > 0) {
            var $chx = $(ochxs[0])
            var order = {};
            var rowindex = $chx.attr("data-index");
            var row = table.rows[rowindex];
            order.Id = row.Id;
            order.Index = rowindex;
            order.LogicOrderId = row.LogicOrderId;
            order.PathFlowCode = row.PathFlowCode;
            order.OrderCode = row.OrderCode;
            order.PlatformOrderId = row.LogicOrderId;
            order.CustomerOrderId = row.PlatformOrderId;
            order.ChildCustomerOrderIds = row.ChildCustomerOrderIds;
            order.ChildLogicOrderIds = row.ChildLogicOrderIds;
            order.LastExpressPrintTime = row.LastExpressPrintTime;
            order.ExpressPrintTime = row.ExpressPrintTime || null;
            order.ShopId = $chx.attr("data-sid");
            order.ShopName = row.ShopName || '';
            order.PlatformTypeName = row.PlatformTypeName || '';
            order.PlatformType = row.PlatformType;
            order.PlatformStatus = row.ErpState;
            order.PublishTime = row.PublishTime;
            order.RefunStatus = row.ErpRefundState;
            order.FxUserId = row.FxUserId;
            //买家留言,卖家备注
            order.BuyerRemark = "";
            order.SellerRemark = "";
            order.UpFxUserId = row.UpFxUserId;
            order.IsPddCrossBorderOrder = row.IsPddCrossBorderOrder;
            order.IsPddCourierDoorToDoorCollect = row.IsPddCourierDoorToDoorCollect;
            order.ReceiverIsChange = row.ReceiverIsChange;
            order.DataFlag = row.IsCold;
            order.IsOfflineOrder = row.IsOfflineOrder;  // 是否线下单
            order.IsOfflineNoSku = row.IsOfflineNoSku; // 是否线下单无sku
            order.OrderTags = row.OrderTags;
            order.WaybillCodes = row.WaybillCodes;
            order.ShippingType = row.ShippingType; // 是否tk平台订单
            order.LogisticStatus = row.LogisticStatus; // 交运状态
            order.ExtField2 = row.ExtField2; // 关联订单号
            order.WarehouseId = row.WarehouseId; // 仓库id
            var existPtIds = [];
            var existIds = [];
            var isSelectAllNotPrintedItem = 1;//未打印的订单项是否全部勾选了
            for (var j = 0; row.SubOrders && j < row.SubOrders.length; j++) {
                var sub = row.SubOrders[j];

                if (existPtIds.indexOf(sub.PlatformOrderId) == -1) {
                    if (sub.BuyerRemark)
                        order.BuyerRemark += sub.BuyerRemark;
                    if (sub.SellerRemark)
                        order.SellerRemark += sub.SellerRemark;
                    existPtIds.push(sub.PlatformOrderId);
                }
                if (existIds.indexOf(sub.LogicOrderId) == -1) {
                    if (sub.PrintRemark)
                        order.SellerRemark += sub.PrintRemark;
                    existIds.push(sub.LogicOrderId);
                }
                //未打印且未被勾选isSelectAllNotPrintedItem设为0
                if (sub.PrintState == 0 && !sub.checked) {
                    isSelectAllNotPrintedItem = 0;
                }
            }
            order.IsSelectAllNotPrintedItem = isSelectAllNotPrintedItem;

            if (row.PrintRemark)
                order.SellerRemark += row.PrintRemark;
            order = getOrderInfo(row, order, isValidation);
            if (needChildOrderId) {
                order.ChildOrderId = row.ChildOrderId;
            }
            if (commonModule.FxPageType() != 2) {
                order.Items = [];//新参数，增加OrderItemCode及Quantity
                order.OrderItems = [];//原参数，只有OrderItemId
                $(row.SubOrders).each(function (i, o) {
                    order.OrderItems.push(o.OrderItemId);
                    var item = {};
                    item.OrderItemId = o.OrderItemId;
                    item.OrderItemCode = o.OrderItemCode;
                    item.Quantity = o.Count;
                    order.Items.push(item);
                });
            }
            else {
                order.NoPrintFlag = row.NoPrintFlag;
                order.OrderTags = row.OrderTags;
            }
            if (order == null)
                return [];

            if (!ignoreCheckOrderItem && commonModule.FxPageType() == 2 && (order.OrderItems == null || order.OrderItems.length == 0)) {
                if (!ignoreKsCheckOrderItem || order.PlatformType != "KuaiShou")
                    return [];
            }

            selections.push(order);
        }
        else {
            var count = table.rows.length;
            for (var i = 0; i < count; i++) {
                var row = table.rows[i];
                if (!row.checked)
                    continue;
                var order = {};
                order.Id = row.Id;
                order.Index = row.Index;
                order.LogicOrderId = row.LogicOrderId;
                order.OrderCode = row.OrderCode;
                order.PlatformOrderId = row.LogicOrderId;
                order.PathFlowCode = row.PathFlowCode;
                order.CustomerOrderId = row.PlatformOrderId; //PlatformOrderId被占用，CustomerOrderId临时存储PlatformOrderId
                order.ChildCustomerOrderIds = row.ChildCustomerOrderIds;
                order.ChildLogicOrderIds = row.ChildLogicOrderIds;
                order.LastExpressPrintTime = row.LastExpressPrintTime;
                order.ExpressPrintTime = row.ExpressPrintTime || null;
                order.ShopId = row.ShopId;
                order.ShopName = row.ShopName || '';
                order.PlatformTypeName = row.PlatformTypeName || '';
                order.PlatformType = row.PlatformType;
                order.PlatformStatus = row.ErpState;
                order.RefunStatus = row.ErpRefundState;
                order.FxUserId = row.FxUserId;
                order.BuyerRemark = "";
                order.SellerRemark = "";
                order.UpFxUserId = row.UpFxUserId;
                order.IsPddCrossBorderOrder = row.IsPddCrossBorderOrder;
                order.IsPddCourierDoorToDoorCollect = row.IsPddCourierDoorToDoorCollect;
                order.ReceiverIsChange = row.ReceiverIsChange;
                order.DataFlag = row.IsCold;
                order.PrintState = row.PrintState;
                order.SubOrders = row.SubOrders;
                order.IsOfflineOrder = row.IsOfflineOrder;  // 是否线下单
                order.IsOfflineNoSku = row.IsOfflineNoSku; 
                order.PublishTime = row.PublishTime;
                order.OrderTags = row.OrderTags;
                order.PayTime = row.PayTime;
                order.MergeredType = row.MergeredType;
                order.WaybillCodes = row.WaybillCodes;
                order.ShippingType = row.ShippingType; // 是否tk平台订单
                order.LogisticStatus = row.LogisticStatus; // 交运状态
                order.ExtField2 = row.ExtField2; // 关联订单号
                order.WarehouseId = row.WarehouseId; // 仓库id
                var existPtIds = [];
                var existIds = [];
                var isSelectAllNotPrintedItem = 1;//未打印的订单项是否全部勾选了
                for (var j = 0; row.SubOrders && j < row.SubOrders.length; j++) {
                    var sub = row.SubOrders[j];
                    if (existPtIds.indexOf(sub.PlatformOrderId) == -1) {
                        if (sub.BuyerRemark)
                            order.BuyerRemark += sub.BuyerRemark;
                        if (sub.SellerRemark)
                            order.SellerRemark += sub.SellerRemark;
                        existPtIds.push(sub.PlatformOrderId);
                    }
                    if (existIds.indexOf(sub.LogicOrderId) == -1) {
                        if (sub.PrintRemark)
                            order.SellerRemark += sub.PrintRemark;
                        existIds.push(sub.LogicOrderId);
                    }
                    //未打印且未被勾选isSelectAllNotPrintedItem设为0
                    if (sub.PrintState == 0 && !sub.checked) {
                        isSelectAllNotPrintedItem = 0;
                    }
                }
                order.IsSelectAllNotPrintedItem = isSelectAllNotPrintedItem;

                if (row.PrintRemark)
                    order.SellerRemark += row.PrintRemark;
                order = getOrderInfo(row, order, isValidation);

                if (commonModule.FxPageType() != 2) {
                    order.Items = [];//新参数，增加OrderItemCode及Quantity
                    order.OrderItems = [];//原参数，只有OrderItemId
                    $(row.SubOrders).each(function (i, o) {
                        order.OrderItems.push(o.OrderItemId);
                        var item = {};
                        item.OrderItemId = o.OrderItemId;
                        item.OrderItemCode = o.OrderItemCode;
                        item.Quantity = o.Count;
                        order.Items.push(item);
                    });
                }
                else {
                    order.NoPrintFlag = row.NoPrintFlag;
                    order.OrderTags = row.OrderTags;
                }
                if (needChildOrderId)
                    order.ChildOrderId = row.ChildOrderId;
                if (order == null)
                    return [];
                if (!ignoreCheckOrderItem && commonModule.FxPageType() == 2 && (order.OrderItems == null || order.OrderItems.length == 0)) {
                    if (!ignoreKsCheckOrderItem || order.PlatformType != "KuaiShou")
                        return [];
                }
                selections.push(order);
            }
        }
        return selections;
    }

    table.getSelectionsExt = function (orders) {
        //计算退款的订单数量
        if (orders == null || orders.length <= 0)
            return {};
        var refundOrder = [];
        var refundItem = [];
        var productCount = 0;
        var productItemCount = 0;
        $(orders).each(function (index, order) {
            var entity = table.rows[order.Index];
            var cur = { Id: entity.Id, RefundItems: [] };
            var sub = entity;
            for (var j = 0; j < entity.SubOrders.length; j++) {
                var oi = entity.SubOrders[j];
                if (entity.ErpRefundState && oi.RefundStatus && entity.ErpRefundState != 'REFUND_CLOSE' && entity.ErpRefundState != 'refundclose'
                    && oi.RefundStatus != 'REFUND_CLOSE' && oi.RefundStatus != 'refundclose'
                ) {
                    cur.RefundItems.push(oi.Id);
                    refundItem.push(oi.Id);
                }
                //var ischeck = $(".orderitem-chx[data-id='" + oi.Id + "']")[0].checked;
                var ischeck = oi.checked == true;
                if (ischeck) {
                    productCount += 1;
                    if (oi.NewCount != undefined && !isNaN(oi.NewCount) && parseInt(oi.NewCount) > 0) {
                        productItemCount += parseInt(oi.NewCount);
                    }
                    else {
                        productItemCount += oi.Count;
                    }
                }
            }
            if (entity.ErpRefundState && entity.ErpRefundState != 'REFUND_CLOSE' && entity.ErpRefundState != 'refundclose')
                refundOrder.push(cur);
        });
        return { RefundOrders: refundOrder, RefundItems: refundItem, productItemCount: productItemCount, productCount: productCount };
    }

    //是否有勾选至少1个订单，不考虑商品是否勾选。
    table.isSelectOrder = function () {
        var count = table.rows.length;
        for (var i = 0; i < count; i++) {
            var row = table.rows[i];
            if (row.checked)
                return true;
        }
        return false;
    }


    //勾选的订单，是否有商品全部没勾选的订单，若有返回true
    table.isAllNotSelectedOrderItem = function () {
        var count = table.rows.length;
        for (var i = 0; i < count; i++) {
            var row = table.rows[i];
            if (row.checked) {
                var isAllNotSelected = true;
                for (var j = 0; row.SubOrders && j < row.SubOrders.length; j++) {
                    if (row.SubOrders[j].checked) {
                        isAllNotSelected = false;
                        break;
                    }
                }
                if (isAllNotSelected)
                    return true;
            }
        }
        return false;
    }


    function getOrderInfo(row, order, isValidation) {
        //var wc = $("#order-" + order.Index + " .LastWaybillCode_input").val();
        //if (wc && wc.trim() != '打印后返回')
        //    order.WaybillCode = wc;
        //else
        //    order.WaybillCode = "";
        if (row.WaybillCodes && row.WaybillCodes.length > 0) {
            order.WaybillCode = row.WaybillCodes[0].WaybillCode || "";
            order.ExpressCompanyCode = row.WaybillCodes[0].CompanyCode || "";
            order.weight = row.WaybillCodes[0].TotalWeight || 0;
            order.WaybillCodeUniqueKey = row.WaybillCodes[0].UniqueKey || "";
        }
        else {
            order.WaybillCode = "";
        }
        order.Items = [];//新参数，增加OrderItemCode及Quantity
        order.OrderItems = [];//原参数，只有OrderItemId
        order.Receiver = {};
        order.Sender = {};
        order.Buyer = {};
        var orderEntity = table.rows[order.Index];
        var orderIdName = "#order-" + order.Index;

        //子订单
        var orderDetailIdName = "#order-detail-" + order.Index;
        var oichxs = $(orderDetailIdName + " .orderitem-chx:checked");

        if (oichxs != null && oichxs.length > 0) {
            $(oichxs).each(function (index2, ichx) {
                order.OrderItems.push($(ichx).attr("data-id"));
                var item = {};
                item.OrderItemId = $(ichx).attr("data-id");
                item.OrderItemCode = $(ichx).attr("data-itemcode");
                item.Quantity = $(ichx).attr("data-count");
                item.ShortTitle = $(ichx).attr("data-shorttitle");
                item.SkuCode = $(ichx).attr("data-skucode");
                item.ProductCode = $(ichx).attr("data-productcode");
                order.Items.push(item);
            });
        }

        order.ReceiverIsEmpty = orderEntity.ReceiverIsEmpty;

        //收件人信息
        //增加省市区内容
        order.Receiver.ToProvince = orderEntity.ToProvince;
        order.Receiver.ToCity = orderEntity.ToCity;
        order.Receiver.ToCounty = orderEntity.ToCounty;

        order.Receiver.ToMaskAddress = orderEntity.ToTown + orderEntity.ToAddress;   //直接ToAddress会少了街道

        var shortAddress = orderEntity.ToShortAddress;
        var fullAddress = orderEntity.ToFullAddress;
        if (commonModule.FxPageType() == 2) {
            if (fullAddress.length > shortAddress.length && fullAddress.substring(0, shortAddress.length) == shortAddress) {
                order.Receiver.ToMaskAddress = fullAddress.substring(shortAddress.length);
            }

        }
        else {
            var o = orderEntity.SecondSubOrders[0].ThressSubOrders[0];
            var pcc = o.ToProvince + o.ToCity + o.ToCounty + o.ToTown;
            if (o.ToFullAddress.length > pcc.length && o.ToFullAddress.substring(0, pcc.length) == pcc) {
                order.Receiver.ToMaskAddress = o.ToFullAddress.substring(pcc.length);
            }
        }

        //拼多多 非待发货订单 界面不再显示发件人信息明文，所以要取rows里面的数据
        if (orderEntity.PlatformType == "Pinduoduo" && orderEntity.PlatformStatus != 'waitsellersend') {
            order.Receiver.ToName = orderEntity.ToName;
            order.Receiver.ToMobile = orderEntity.ToMobile ? orderEntity.ToMobile : orderEntity.ToPhone;
            order.Receiver.ToAddress = orderEntity.ToFullAddress ? orderEntity.ToFullAddress : (orderEntity.ToProvince + " " + orderEntity.ToCity + " " + orderEntity.ToCounty + " " + orderEntity.ToTown + orderEntity.ToAddress);
        }
        else {
            order.Receiver.ToName = $(orderIdName + " .Reciever .ToName").text();
            order.Receiver.ToMobile = $(orderIdName + " .Reciever .ToPhone").text();
            order.Receiver.ToAddress = $(orderIdName + " .RecieverAddress .ToAddress").text();
        }
        order.Receiver.toFullName = order.Receiver.ToName;
        order.Receiver.toArea = order.Receiver.ToAddress;
        //发件人信息
        order.Sender.SenderName = orderEntity.SenderName;//$(orderIdName + " .SenderName-input").val();
        order.Sender.SenderPhone = orderEntity.SenderMobile ? orderEntity.SenderMobile : orderEntity.SenderPhone;//$(orderIdName + " .SenderPhone-input").val();
        order.Sender.SenderAddress = orderEntity.SenderAddress;//$(orderIdName + " .SenderAddress-input").val();
        order.Sender.SenderCompany = orderEntity.SenderCompany ? orderEntity.SenderCompany : "";
        order.Sender.CompanyName = order.Sender.SenderCompany;
        order.PrintInfo = orderEntity.PrintInfo;
        //买家信息
        order.Buyer.BuyerMemberId = orderEntity.BuyerMemberId;
        order.Buyer.BuyerMemberName = orderEntity.BuyerMemberName;
        order.Buyer.BuyerWangWang = orderEntity.BuyerWangWang;
        if (isValidation && order.OrderItems.length <= 0 && !common.isCustomerOrder) {
            layer.closeAll();
            layer.alert("买家【" + order.Buyer.BuyerMemberName + "】的订单未勾选任何商品，请勾选商品", { skin: 'wu-dailog' });
            return null;
        }
        order.PlatformType = orderEntity.PlatformType;
        order.IsWeiGong = orderEntity.IsWeiGong;
        order.ExtField1 = orderEntity.ExtField1;

        //买家留言,卖家备注
        order.BuyerRemark = "";
        order.SellerRemark = "";
        var existPtIds = [];
        var existIds = [];
        for (var i = 0; orderEntity.SubOrders && i < orderEntity.SubOrders.length; i++) {
            var sub = orderEntity.SubOrders[i];
            if (existPtIds.indexOf(sub.PlatformOrderId) == -1) {
                if (sub.BuyerRemark)
                    order.BuyerRemark += sub.BuyerRemark;
                if (sub.SellerRemark)
                    order.SellerRemark += sub.SellerRemark;
                existPtIds.push(sub.PlatformOrderId);
            }
            if (existIds.indexOf(sub.LogicOrderId) == -1) {
                if (sub.PrintRemark)
                    order.SellerRemark += sub.PrintRemark;
                existIds.push(sub.LogicOrderId);
            }
        }
        if (orderEntity.PrintRemark)
            order.SellerRemark += orderEntity.PrintRemark;
        //if (orderEntity.ChildOrderId) {
        //    var l = orderEntity.SubOrders.length;
        //    common.Foreach(orderEntity.SubOrders, function (i, so) {
        //        order.BuyerRemark += $('#txt_buyer_remark_' + so.Id).val();
        //        order.SellerRemark += $('#txt_seller_remark_' + so.Id).val();
        //        if (i < (l - 1)) {
        //            order.BuyerRemark += '|||';
        //            order.SellerRemark += '|||';
        //        }
        //    });
        //    order.BuyerRemark;
        //    order.SellerRemark;
        //}
        //else {
        //    order.BuyerRemark = $('#txt_buyer_remark_' + orderEntity.Id).val();
        //    order.SellerRemark = $('#txt_seller_remark_' + orderEntity.Id).val();
        //}

        return order;
    }

    var addressTagFormatter = function (row, col) {
        var html = "";
        var showTag = reachCompareModule.AddressIsAllMatch(row, filterExpressReachSet);
        if (showTag) {
            var tempHtml = '<span class="express-reach" style="background-color: #bd7bff;">快递不可达</span>';
            //拼多多官方改造：要求快递不可达排除下面两个地址
            var time = new Date().getTime();
            if (row.PlatformType == "Pinduoduo" && time < 1674144000000 && row.ToProvince == "陕西省" && row.ToCity == "咸阳市" && (row.ToCounty == "秦都区" || row.ToCounty == "渭城区")) {
                tempHtml = '<span class="pdd-express-reach" style="background-color: #bd7bff;">拼多多仓库区域，正常送达</span>';
                row.ExpressReachCheck = false;
            }
            html += tempHtml;
        }

        // 虚假地址
        if (filterFakeAddressSet != null && filterFakeAddressSet != "" && filterFakeAddressSet.IsOpen && row.IsFake)
            html += '<span class="fakeaddr" style="background-color:#ff511c;margin-top:3px;" id="fake_adderss_' + row.Id + '">异常地址</span>';
        //拼多多暂停发货
        if (row.PddShipHold == "1") {
            html += '<span class="tagStatus tagDanger ship-hold" >暂停发货</span>';
        }
        return html;
    }

    table.checkExpressReach = function (template, isselect) {
        if (commonModule.FxPageType() != 2) return false;//不是[待打订单]页面直接return，防止下面找不到方法报错
        if (!template)
            template = addTmplInOrderListModule.GetCurrentTemplate();

        table.refreshColumn("Reciever");
        orderTableBuilder.SecondFilter();
    };

    var isMatchAddress = function (pt, preName, name, filterName, type) {
        if (type == 2) {
            var cityKey = ['蒙古族藏族', '布依族苗族自治州', '苗族侗族自治州', '藏族', '布依族', '苗族', '侗族', '黎族自治', '自治州', '州', '市', '县', '地区'];
            for (var i = 0; i < cityKey.length; i++) {
                filterName = filterName.mineTrimEnd(cityKey[i]);
            }
        }
        if (type == 3) {
            var cityKey = ['旗', '县', '区', '镇', '乡', '市', '街道', '管委会'];
            for (var i = 0; i < cityKey.length; i++) {
                filterName = filterName.mineTrimEnd(cityKey[i]);
            }
        }
        if (name.startsWith(filterName))
            return true;
        if (type == 3) {
            var otherAreas = ["其他", "其它", "其他区", "其它区"];
            if (name.indexOf("其他") != -1 || name.indexOf("其它") != -1) {
                return otherAreas.indexOf(filterName) != -1
            }
            else if (pt == "TouTiao" || pt == "Offline") {
                // 头条特殊区（头条规范地址库前的输入的地址，发现后再补充）
                var teshudizhi = {
                    "阳泉市": [
                        { "郊区": ["郊区", "阳泉郊区"] }
                    ],
                    "晋城市": [
                        { "城区": ["晋城", "晋城城区"] }
                    ],
                    "佳木斯市": [
                        { "郊区": ["郊区", "佳木斯郊区"] }
                    ]
                };
                if (!teshudizhi[preName] || teshudizhi[preName].length == 0)
                    return false;
                var arr = teshudizhi[preName];
                for (var i = 0; i < arr.length; i++) {
                    for (var k in arr[i]) {
                        if (k.startsWith(filterName) && arr[i][k].indexOf(name) != -1) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    //二次筛选合并处理
    table.SecondFilter = function (type) {
        var fxPageType = commonModule.FxPageType();
        if (fxPageType != 2 && fxPageType != 1) return false;
        var isCanFilterReach = $("#ExpressCanReachBtn").hasClass("active");     //显示可达
        var isFilterReach = $("#ExpressReachBtn").hasClass("active");           //显示不可达
        var isLastShipTimeWarn = $("#LastShipTimeWarnBtn").hasClass("active");  //显示预警单      
        var isFake = $("#FakeAddressBtn").hasClass("active");                   //显示虚假地址
        var isAppointExpress = $("#AppointExpressBtn").hasClass("active");      //是否指定快递
        var isFirstDelivery = commonModule.CloudPlatformType != "Pinduoduo" ? false : $("#FirstDeliveryBtn").hasClass("active");        //是否优先发货，20230821改成只针对拼多多平台，其他换成标签查询

        var layui_row_item = ".layui-row-item";
        var isAllShow = $("#allshoworder").prop("checked");
        if (fxPageType == 1) {
            layui_row_item = ".layui-mytable-tr";
            isAllShow = true;
        }

        var $trs = $("#OrderTableList " + layui_row_item);
        $trs.show();
        if (isCanFilterReach || isFilterReach || isLastShipTimeWarn || isFake || isAppointExpress || isFirstDelivery) {
            $trs.each(function (i, tr) {
                var index = $(tr).attr("data-index");
                var row = table.rows[index];
                var isShow1 = true;
                var isShow2 = true;
                var isShow3 = true;
                var isShow4 = true;
                var isShow5 = true;
                var isShow6 = true;

                if (isCanFilterReach) {
                    isShow1 = (row.ExpressReachCheck == undefined || !row.ExpressReachCheck);
                }
                else if (isFilterReach) {
                    isShow2 = (row.ExpressReachCheck != undefined && row.ExpressReachCheck);
                }
                if (isShow1 && isShow2 && isLastShipTimeWarn) {
                    isShow3 = (row.LastShipTimeWarn != undefined && row.LastShipTimeWarn == 1);
                }
                if (isShow1 && isShow2 && isShow3 && isFake) {
                    isShow4 = (row.IsFake != undefined && row.IsFake);
                }
                if (isShow1 && isShow2 && isShow3 && isShow4 && isAppointExpress) {
                    isShow5 = (row.IsAppointExpress != undefined && row.IsAppointExpress);
                }
                if (isShow1 && isShow2 && isShow3 && isShow4 && isShow5 && isFirstDelivery) {
                    isShow6 = (row.IsFirstDelivery != undefined && row.IsFirstDelivery);
                }

                //所有条件都满足显示，否则隐藏
                if (isShow1 && isShow2 && isShow3 && isShow4 && isShow5 && isShow6) {
                    if (isAllShow)
                        $("#order-detail-" + index).show();
                    $(tr).show();
                    //SetRowCheck(row, true);//选择
                }
                else {
                    $("#order-detail-" + index).hide();
                    $(tr).hide();
                    SetRowCheck(row, false);//取消选择
                }
            });
            SetCount();
        }
    }

    //打印时提示，客户选择过滤不可达订单
    table.FilterNotReachOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];

            var isShow1 = (row.ExpressReachCheck == undefined || !row.ExpressReachCheck);
            if (!isShow1) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }

    //打印时提示,客户选择过滤暂停发货订单
    table.FilterNotShipHoldOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];

            var isShow1 = (row.PddShipHold == undefined || row.PddShipHold != "1");
            if (!isShow1) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }

    //打印时提示，客户选择过滤收件人为空的订单
    table.FilterReceiverEmptyOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];

            var isShow1 = (row.ReceiverIsEmpty == undefined || row.ReceiverIsEmpty != 1);
            if (!isShow1) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }

    //合单时提示,客户选择过滤拼多多跨境订单
    table.FilterCrossBorderOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];

            if (row.IsPddCrossBorderOrder) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }

    //合单时提示,客户选择过滤拼多多待审核订单
    table.FilterWaitAuthOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];

            if (row.ReceiverIsEmpty) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }

    //过滤拼多多跨境快递上门揽收
    table.FilterPddDoorOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];

            if (row.IsPddCourierDoorToDoorCollect) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }
    //拼多多-本地仓过滤
    table.FilterPddLocalOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];
            if (commonModule.HasTag(row.OrderTags, 'local_depot', 'Order')) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }

    //过滤地址变更订单
    table.FilterReceiverChangeOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];

            if (row.ReceiverIsChange) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }

    //合单时提示,客户选择过滤头条物流中转订单
    table.FilteTransitOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];

            if (row.IsTouTiaoTransit) {
                SetRowCheck(row, false);//取消选择
            }
            if (row.IsKuaiShouTransit) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }
    //拼多多-中小件货品过滤
    table.FilterJdMidHomeDeliveryDoorOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];
            if (commonModule.HasTagValue(row.OrderTags, 'home_delivery_door', '2', 'OrderItem')) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }
    //小红书-送货上门过滤
    table.FilterXhsHomeDeliveryDoorOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];
            if (commonModule.HasTag(row.OrderTags, 'home_delivery_door', 'OrderItem')) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }
    //阿里巴巴-官方仓发过滤
    table.FilterAlibabaHyperLinkShipOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];
            if (commonModule.HasTag(row.OrderTags, 'hyperLinkShip', 'OrderItem')) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }
    //抖店-风控订单过滤
    table.FilterTouTiaoRiskProcessingOrder = function () {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];
            if (commonModule.HasTag(row.OrderTags, 'risk_processing', 'OrderItem')) {
                SetRowCheck(row, false);//取消选择
            }
        });
        SetCount();
    }
    table.FilterTouTiaoShopExpressOrder = function (filterLogicOrderId) {
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.each(function (i, tr) {
            var index = $(tr).attr("data-index");
            var row = table.rows[index];
            commonModule.Foreach(filterLogicOrderId, function (i, o) {
                if (row.LogicOrderId == o) {
                    SetRowCheck(row, false);//取消选择
                    return;
                }
            });
        });
        SetCount();
    }

    // 过滤不可达订单 
    //--已合并处理，暂时不用
    table.FilterExpressReach = function (type) {
        //var template = addTmplInOrderListModule.GetCurrentTemplate();
        //table.checkExpressReach(template, true);

        table.refreshColumn("Reciever");
        var isCanFilterReach = $("#ExpressCanReachBtn").hasClass("active"); //过滤所有可达（忽略模板显示所有不可达订单）
        var isFilterReach = $("#ExpressReachBtn").hasClass("active"); //过滤所有不可达（忽略模板显示所有不可达订单）
        // 当前订单列表只显示不可达订单
        var $trs = $("#OrderTableList .layui-row-item");
        $trs.show();

        var isAllShow = $("#allshoworder").prop("checked");
        if (isFilterReach || isCanFilterReach) {
            // 不可达筛选当前页数据
            $trs.each(function (i, tr) {
                var index = $(tr).attr("data-index");
                $("#order-detail-" + index).show();
                var $reachTag = $(tr).find(".express-reach");
                if (isFilterReach && $reachTag.length == 0) {
                    $("#order-detail-" + index).hide();
                    $(tr).hide();
                }
                else if (isCanFilterReach) {
                    if ($reachTag.length == 0) {
                        if (isAllShow)
                            $("#order-detail-" + index).show();
                        $(tr).show();
                    }
                    else {
                        $("#order-detail-" + index).hide();
                        $(tr).hide();
                    }
                }
            });
        }
        else {
            $trs.show();
            $("#OrderTableList div[id^=order-detail-]").show();
        }

        if ($("#LastShipTimeWarnBtn").hasClass("active")) {
            //剩余时间过滤
            orderTableBuilder.FilterLastShipTimeWarn();
        }
    }

    // 只显示：剩余时间小于预警时间的订单，目前只有拼多多、抖音、快手三平台有剩余时间 
    //--已合并处理，暂时不用
    table.FilterLastShipTimeWarn = function () {
        var isFilter = $("#LastShipTimeWarnBtn").hasClass("active");
        var $trs = $("#OrderTableList .layui-row-item");
        if (isFilter) {
            // 筛选当前页数据
            $trs.each(function (i, tr) {
                var index = $(tr).attr("data-index");
                var row = table.rows[index];
                if (!(row.LastShipTimeWarn != undefined && row.LastShipTimeWarn == 1)) {
                    $("#order-detail-" + index).hide();
                    $(tr).hide();
                    //SetRowCheck(row, false);//取消勾选非预警单
                }
                else {
                    //SetRowCheck(row, true);//勾选预警单
                }
            });
        }
        else {
            $trs.show();
            $("#OrderTableList div[id^=order-detail-]").show();
            if ($("#ExpressReachBtn").hasClass("active") || $("#ExpressCanReachBtn").hasClass("active")) {
                //快递是否可达
                orderTableBuilder.FilterExpressReach();
            }
        }

    }

    table.SetRowCheck = function (row, isChecked) {
        SetRowCheck(row, isChecked);
    }

    function SetRowCheck(row, isChecked) {
        $this = $(".order-chx[data-index='" + row.Index + "']")

        $this.prop("checked", isChecked);
        $this.checked = isChecked;
        row.checked = isChecked; //数据选中标识
        if (isChecked) {
            $this.closest(".layui-row-item").addClass("active");
            $this.closest(".layui-row").addClass("active");
        }
        else {
            $this.closest(".layui-row-item").removeClass("active");
            $this.closest(".layui-row").removeClass("active");
        }
    }
    //统计勾选数量
    function SetCount() {
        var isAll = false;
        if (commonModule.FxPageType() != 2) {
            var rowCount = 0;
            $(table.rows).each(function (i, first) {
                $(first.SecondSubOrders).each(function (i2, second) {
                    rowCount++;
                });
            });
            isAll = $(".order-chx:checked").length == rowCount;
        }
        else {
            if (table != undefined && table.rows != undefined) {
                isAll = $(".order-chx:checked").length == table.rows.length;
            }
        }
        $("#orderNum").text($(".order-chx:checked").length);
        $("#allcheckorder")[0].checked = isAll;
    }

    //替换省市区的后缀，type：1.省 2.市 3.区
    function addressReplace(areaName, type) {
        if (!!areaName && !!type && !!addressFilterKey && !$.isEmptyObject(addressFilterKey)) {
            var keywords = [];
            switch (type) {
                case 1:
                    keywords = addressFilterKey.provinceKey;
                    break;
                case 2:
                    keywords = addressFilterKey.cityKey;
                    break;
                case 3:
                    keywords = addressFilterKey.areaKey;
                    break;
            }

            for (var i = 0; i < keywords.length; i++) {
                var key = keywords[i];
                if (areaName.endsWith(key)) {
                    var arr = key.split('').reverse();
                    for (var j = 0; j < arr.length; j++) {
                        areaName = areaName.trimEndDgj(arr[j]);
                    }
                    if (type == 3)
                        areaName = (areaName == "其他" || areaName == "其它" || areaName == "其他区" || areaName == "其它区") ? "其他" : areaName;
                    break;
                }
            }

            //$(keywords).each(function (i, key) {
            //    if (areaName.indexOf(key) != -1) {
            //        areaName = areaName.trimEndDgj(key);
            //        if (type == 3)
            //            areaName = (areaName == "其他" || areaName == "其它" || areaName == "其他区" || areaName == "其它区") ? "其他" : areaName;
            //        return;
            //    }
            //});
        }
        return areaName;
    }

    //快递可达设置提示气泡的按钮点击
    table.setCloseExpressReachDialog = function (btn_el, goSet) {
        $(btn_el).closest(".expressReach-dialog").hide();
        var hostUrl = alibabafenfasystemurl ? alibabafenfasystemurl : (defaultfenfasystemurl ? defaultfenfasystemurl : "");
        if (goSet)
            commonModule.OpenNewTab(hostUrl + commonModule.rewriteUrl('/SetInfo/ExpressReach'));
        var cookieName = 'no-show-express-reach-tips';
        $.cookie(cookieName, 1, { expires: 30 });
    }

    //订单项checkbox点击事件
    table.OrderProductItemSeletedHandler = function (chk, orderId, orderItemId) {
        var isChecked = $(chk).is(":checked");
        //2.数据变更
        var orderRow = null;
        $(table.rows).each(function (i, row) {
            if (row.Id == orderId) {
                orderRow = row;
                $(row.SubOrders).each(function (ii, subRow) {
                    if (subRow.OrderItemId == orderItemId) {
                        subRow.checked = isChecked;
                        subRow.NewCount = -1;//设为-1，生成打PrintInfo时用原数量
                    }
                });
            }
        });
        //3.重新生成打印内容
        orderRow.PrintInfo = printContentFormatSetModule.ForatPrintContent(orderRow);
    }

    //二次发货，选择类型订单项checkbox点击事件、数量变化时
    table.ResendOrderProductItemSeletedHandler = function (orderId, orderItemId) {
        var isChecked = $('#aftersales-orderitem-chx-' + orderItemId).is(":checked");
        //2.数据变更
        var orderRow = null;
        $(table.rows).each(function (i, row) {
            if (row.Id == orderId) {
                orderRow = row;
                $(row.SubOrders).each(function (ii, subRow) {
                    if (subRow.OrderItemId == orderItemId) {
                        subRow.checked = isChecked;
                        subRow.NewCount = $("#aftersales-count-" + orderItemId).val();//设为当前数量，生成打PrintInfo时用此数量
                    }
                });
            }
        });
        //2.1原始页订单项同步选中状态
        $("#orderitem-chx-" + orderItemId).prop("checked", isChecked);

        //3.重新生成打印内容
        orderRow.PrintInfo = printContentFormatSetModule.ForatPrintContent(orderRow);
    }

    //重置NewCount=-1
    table.ResetItemCount = function () {
        $(table.rows).each(function (i, row) {
            $(row.SubOrders).each(function (ii, subRow) {
                subRow.checked = $("#orderitem-chx-" + subRow.OrderItemId).is(":checked");
                subRow.NewCount = -1;//设为-1，生成打PrintInfo时用原数量
            });
            row.PrintInfo = printContentFormatSetModule.ForatPrintContent(row);//重新生成打印信息
        });
    }

    //打印前触发事件-生成打印内容
    table.OrderProductItemSetPrintInfo = function (orderId) {
        var printInfo = "";
        $(table.rows).each(function (i, row) {
            if (row.Id == orderId) {
                printInfo = printContentFormatSetModule.ForatPrintContent(row);
                row.PrintInfo = printInfo;
            }
        });
        return printInfo;
    }

    /*****************
    获取选择的二次发货订单数据
    参数 orderId 指定的订单id，获取选中的订单时不用填写
    参数 isValidation 是否验证订单信息：验证商品有没有勾选
    参数 ignoreCheckOrderItem 忽略验证订单项
    
    只取二次选中的项，并为order设置SendType值
    *****************/
    table.getAfterSaleSelections = function (orderId, isValidation, needChildOrderId, ignoreCheckOrderItem) {

        var selections = table.getSelections(orderId, isValidation, needChildOrderId, ignoreCheckOrderItem);
        var oichxs = [];
        var orderItemIds = [];
        var removeIndexs = [];//无商品项的订单index
        var sendType = $("#sendtype").val();//发货类型

        //只取二次选中的项
        oichxs = $(".aftersales-orderitem-chx:checked");
        if (oichxs != null && oichxs.length > 0) {
            $(oichxs).each(function (index, ichx) {
                var itemid = $(this).attr("data-itemid");
                if (orderItemIds.indexOf(itemid) == -1)
                    orderItemIds.push(itemid);
            });
        }
        if (selections != null && selections.length > 0) {
            for (var i = 0; i < selections.length; i++) {
                ichx = selections[i];
                var newOrderItems = [];
                var newItems = [];
                ichx.SendType = sendType;//设置发货类型

                //var selectPacks = $("#selectPacks-" + ichx.LogicOrderId).attr("data-values") || "";
                var selectPacks = "";
                var packs = [];

                $("input[name='packid-" + ichx.LogicOrderId + "']:checked").each(function () {
                    temp = $(this).val() + '||';
                    var pack = {};
                    pack.PackId = temp.split("|")[0];       //包裹Id
                    pack.OWaybillCode = temp.split("|")[1]; //原运单号
                    pack.OLogicOrderId = temp.split("|")[2]; //原逻辑单号
                    packs.push(pack);
                });

                ichx.Packs = packs;

                $(ichx.OrderItems).each(function (index2, itemid) {
                    if (orderItemIds.indexOf(itemid) >= 0)
                        newOrderItems.push(itemid);
                });
                $(ichx.Items).each(function (index2, item) {
                    if (orderItemIds.indexOf(item.OrderItemId) >= 0) {
                        var newCount = $("#aftersales-count-" + item.OrderItemId).val();
                        var deliveryid = $("#aftersales-orderitem-chx-" + item.OrderItemId).data("deliveryid");
                        item.Quantity = newCount;//数量使用二次选择中的数量
                        item.DeliveryId = deliveryid;//历史发货包裹Id
                        newItems.push(item);
                    }
                });
                ichx.OrderItems = newOrderItems;
                ichx.Items = newItems;

                if (newItems.length == 0)
                    removeIndexs.push(i);
            }

            //逆序移除无商品项的订单数据
            if (removeIndexs.length > 0) {
                var len = removeIndexs.length - 1;
                for (var x = len; x >= 0; x--) {
                    rIndex = removeIndexs[x];
                    selections.splice(rIndex, 1);
                }
            }
        }

        return selections;
    }

    //追加渲染新的订单行数据
    table.CreateNewRow = function (rows, isChecked) {
        //0.取消当前所有选中
        var checkOrderDom = $('.order-chx');
        for (var i = 0; i < checkOrderDom.length; i++) {
            if (checkOrderDom[i].checked) {
                $(checkOrderDom[i]).click();
            }
        }
        var newRows = JSON.parse(JSON.stringify(rows));
        var newLength = newRows.length;
        //1.如果当前表格没有数据，就覆盖表格
        if (!table.rows || table.rows.length == 0) {
            var element = table.target;
            if (!element)
                element = $("#order-list-table")[0];

            table.rows = newRows;
            _render(element, table.rows);
        }
        //2.向当前表格前置追加行数据
        else {
            for (var i = 0; i < newRows.length; i++) {
                //2.1 隐藏当前页面存在的订单
                var subOrders = newRows[i].SubOrders;
                for (var s = 0; s < subOrders.length; s++) {
                    if (subOrders[s].MergeredOrderId) {
                        var oldIndex = $('input.order-chx[data-pid="' + subOrders[s].MergeredOrderId + '"]').attr('data-index');
                        if (oldIndex) {
                            $('#order-' + oldIndex).remove();
                            $('#order-detail-' + oldIndex).remove();
                        }
                    }
                    var oldIndex = $('input.order-chx[data-pid="' + subOrders[s].LogicOrderId + '"]').attr('data-index');
                    if (oldIndex) {
                        $('#order-' + oldIndex).remove();
                        $('#order-detail-' + oldIndex).remove();
                    }
                }

                newRows[i].Index = table.rows.length;
                _renderBefore(newRows[i]);
                _renderPrice(newRows[i]);
                //渲染表格
                var cols = common.Sort(table.columns, "order", false);
                var html = _renderRow(cols, newRows[i]);
                var div = $('div.layui-mytable-tobody');
                div.prepend(html);
                table.rows.push(newRows[i]);
            }
        }
        //订单数量累加
        var oldTotal = $("#OrderTotalCount").attr("data-total");
        var newTotal = Number.parseInt(oldTotal) + newLength;
        $("#OrderTotalCount").attr("data-total", newTotal).html(newTotal);
        //默认展示详情或隐藏
        table.DefaultDisplayORHide();

        if (isChecked) {
            for (var i = 0; i < newRows.length; i++) {
                var newRow = table.rows[newRows[i].Index];
                $this = $(".order-chx[data-index='" + newRow.Index + "']");
                $this.prop("checked", isChecked);
                $this.checked = isChecked;
                newRow.checked = isChecked; //数据选中标识
                $this.closest(".layui-row-item").addClass("active");
                $this.closest(".layui-row").addClass("active");
            }
        }
    }

    table.openUrlToMyAgent = function (agentMobile) {
        if (agentMobile == undefined || agentMobile == null) {
            agentMobile = "";
        }
        var url = "/Partner/MyAgent?key=" + agentMobile
        var newUrl = commonModule.rewriteUrlToMainDomain(url);
        window.open(newUrl, '_blank');
    }

    table.getSingleOrder = function (logicOrderId, callBack) {
        if (!logicOrderId) return;

        var orderStatus = $("#orderstatus .active").attr("data-status") || "";
        if (commonModule.FxPageType() != 1) {
            orderStatus = $("select[name=ErpOrderStatus]").val();
        }
        var options = {
            Filters: [
                { TableAlias: 'o', FieldType: 'string', TableName: 'LogicOrder', Name: 'LogicOrderId', Value: logicOrderId, Contract: 'in', CustomQuery: 'LogicOrderIdSearch' },
                { TableAlias: "o", FieldType: "string", TableName: "LogicOrder", Name: "ErpState", Value: orderStatus, Contract: "=" }
            ]
        };
        commonModule.getBaseProductSetting(function (rspData) {
            OrderCombine = rspData.OrderCombine
        })
        optionsData = options;
        common.Ajax({
            url: '/NewOrder/List',
            data: options,
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success) {
                    var rows = rsp.Data.Rows;
                    for (var i = 0; i < rows.length; i++) {
                        var item = rows[i];
                        if (item.SubOrders && item.SubOrders.length) {
                            for (var k = 0; k < item.SubOrders.length; k++) {
                                var kData = item.SubOrders[k];
                                if (kData.IsRelationBaseProduct) {
                                    if (kData.BaseProductInfo && kData.BaseProductInfo.ProductImgUrl) {
                                        kData.BaseProductInfo.ProductImgUrl = commonModule.newTransformImgSrc(kData.BaseProductInfo.ProductImgUrl, '&businessType=baseproduct');
                                    }
                                    if (kData.PtProductInfo && kData.PtProductInfo.ProductImgUrl) {
                                        kData.PtProductInfo.ProductImgUrl = commonModule.newTransformImgSrc(kData.PtProductInfo.ProductImgUrl, '&businessType=baseproduct');
                                    }
                                    kData.ProductImgUrl = commonModule.newTransformImgSrc(kData.ProductImgUrl, '&businessType=baseproduct');
                                }
                            }
                        }
                        if (item.SecondSubOrders && item.SecondSubOrders.length) {
                            for (var j = 0; j < item.SecondSubOrders.length; j++) {
                                var jData = item.SecondSubOrders[j];
                                for (var k = 0; k < jData.ThressSubOrders.length; k++) {
                                    var kData = jData.ThressSubOrders[k];
                                    if (kData.IsRelationBaseProduct) {
                                        if (kData.BaseProductInfo && kData.BaseProductInfo.ProductImgUrl) {
                                            kData.BaseProductInfo.ProductImgUrl = commonModule.newTransformImgSrc(kData.BaseProductInfo.ProductImgUrl, '&businessType=baseproduct');
                                        }
                                        if (kData.PtProductInfo && kData.PtProductInfo.ProductImgUrl) {
                                            kData.PtProductInfo.ProductImgUrl = commonModule.newTransformImgSrc(kData.PtProductInfo.ProductImgUrl, '&businessType=baseproduct');
                                        }
                                        kData.ProductImgUrl = commonModule.newTransformImgSrc(kData.ProductImgUrl, '&businessType=baseproduct');
                                    }
                                }
                            }
                        }
                    }
                    callBack(rows);
                } else {
                    callBack([]);
                }
            }
        });
    }
    return table;
}(orderTableBuilder || {}, commonModule, jQuery, layer));