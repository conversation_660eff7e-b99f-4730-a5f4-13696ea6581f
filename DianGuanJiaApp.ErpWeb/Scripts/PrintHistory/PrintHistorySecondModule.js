/// <reference path="../jquery-1.10.2.min.js" />
/// <reference path="../layer/layer.js" />
/// <reference path="../CommonModule.js" />

var printHistorySecondModule = (function (module, common, $, layer) {

    var _pageIndex = 1, _pageSize = 10;

    var _rows = {};


    //使用前置获取的数据赋值（commonModule.TemplateData）
    module.LoadTemplateNames = function () {
        var selTemplateName = $('#sel_templateName');
        var oldVal = selTemplateName.val();
        selTemplateName.empty();
        common.Foreach(commonModule.TemplateData._result.Data, function (i, o) {
            selTemplateName.append("<option value='" + o.Value + "'>" + o.Text + "</option>")
        });
        if (selTemplateName.find('option[value="' + oldVal + '"]').length > 0)
            selTemplateName.find('option[value="' + oldVal + '"]').prop('selected', 'selected');
        layui.form.render('select');
    }

    module.OrderTrim = function (oid) {
        if (oid[0] == "C") {
            return oid.substring(1)
        }
        return oid;
    }

    module.LoadList = function (isPaging) {

        var requestModel = _getQueryCondition();
        $("input[name=chkAllShowMore]").prop("checked", false);
        $("#chk_all").prop("checked", false);
        
        $("#progressShow").css({ display: "flex" });//加载动画开启
        common.Ajax({
            url: '/PrintHistory/LoadSecondSendList',
            loading: false,
            data: { requestModel: requestModel, Pt: common.getQueryVariable("pt") },
            success: function (rsp) {
                $("#progressShow").css({ display: "none" });//加载动画关闭
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                
                
                _rows = {};

                common.Foreach(rsp.Data.Rows, function (i, r) {
                    _rows[r.ID] = r;
                });

                // 新查询逻辑启用分页
                if (rsp.Data.UseNewQuery) {
                    $(".printHistory_wrapper_main").append('<div id="div_pagination" style="text-align: right"></div>');
                }

                $('#lblTotalRecord').text(rsp.Data.Total);

                var tplt = $.templates("#printHistory_list_tmpl");
                var html = tplt.render(rsp.Data.Rows, { orderIdFormatter: common.OrderIdFormatter, orderTrim: module.OrderTrim });
                $("#tb_data_list").find("tbody").remove();
                $("#tb_data_list").append(html);
                //无数据页面提示
                if (rsp.Data.Rows.length == 0 && !rsp.Data.Rows.length) {
                    var noDataHtml = '<tr id="tableNoDataShow"><td colspan="20"><div class="tableNoDataShow"><img src="/Content/images/noData-icon.png" /><span class="tableNoDataShow-title">暂无数据！</span></div></td></tr>'
                    $("#tb_data_list").append(noDataHtml);
                }

                if (isPaging == true) {
                    return;
                }


                layui.laypage.render({
                    elem: 'div_pagination'
                    , theme: ' wu-page wu-one'
                    , count: rsp.Data.Total
                    , limit: _pageSize
                    , limits: [10, 20, 30, 40, 50, 60, 100, 200, 300, 400, 500]
                    , layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
                    , jump: function (obj, first) {
                        if (!first) {
                            //layer.alert((obj.curr) + "=" + (obj.limit));
                            _pageIndex = obj.curr;
                            _pageSize = obj.limit;
                            module.LoadList(true);
                        }
                    }
                });
            }
        });
    }


    var _getWaybillOrderId = function (orderId, waybillCode) {
        var waybillCodeModel = {};
        common.Foreach(_cancelData, function (ii, w) {
            if (w.OrderId == orderId && w.ExpressWayBillCode == waybillCode) {
                waybillCodeModel = w;
                return 'break;'
            }
        });
        return waybillCodeModel.ID || 0;
    }

    var _getQueryCondition = function () {
        return {
            StartDate: $("#txtDate .QueryDateVal").attr("start-date") || "", 
            EndDate: $("#txtDate .QueryDateVal").attr("end-date") || "",
            TemplateName: $('#sel_templateName').val(),
            OrderByField: 'PrintDate',
            IsOrderDesc: true,
            PageIndex: _pageIndex,
            PageSize: _pageSize,
        };
    }

    module.search = function () {
        _pageIndex = 1;
        module.LoadList();
        //module.LoadStatisticsCount();
    }

    module.row_click = function (tr, key) {
        var $tr = $(tr);
        var $chk = $('#chk_' + key);

        if ($tr.hasClass('activeColor_01') == false) {
            $tr.addClass('activeColor_01');
            _rows[key].checked = true;
            $chk.prop('checked', true);

            //判断是否有回收按钮，给批量回收做判断
            //if ($tr.find(".recycle-btn").length > 0) {
            //    _rows[key].hasRecycleBtn = true;
            //}
        }
        else {
            $tr.removeClass('activeColor_01');
            _rows[key].checked = false;
            $chk.prop('checked', false);
        }

        //全选选中
        var chks = $(':checkbox[name="chk_row"]');
        var chksed = $(':checkbox[name="chk_row"]:checked');
        $('#chk_all').prop('checked', chks.length == chksed.length);
    }

    module.chk_change = function (chk) {
        var $chk = $(chk);
        var key = $chk.val();
        var tr = $('#tr_' + key);
        if (chk.checked == true) {
            if (tr.hasClass('activeColor_01') == false) {
                tr.addClass('activeColor_01');
            }

            ////判断是否有回收按钮，给批量回收做判断
            //if ($chk.parent().siblings().find(".recycle-btn").length > 0) {
            //    _rows[key].hasRecycleBtn = true;
            //}
        }
        else {
            tr.removeClass('activeColor_01');
        }
        _rows[key].checked = chk.checked;

        //全选选中
        var chks = $(':checkbox[name="chk_row"]');
        var chksed = $(':checkbox[name="chk_row"]:checked');
        $('#chk_all').prop('checked', chks.length == chksed.length);
    }

    module.chk_all_change = function (chk) {
        //var chk = $(chk);
        var chkAll = chk.checked;
        var chks = $(':checkbox[name="chk_row"]');
        chks.each(function (i, o) {
            o.checked = chkAll;
            $(o).trigger('change');
            if (chkAll) {
                $(o).closest("tr").addClass("activeColor_01")
            } else {
                $(o).closest("tr").removeClass("activeColor_01")
            }
        });
    }

    //已打印已发货-跳转到已发货明细页面（已发货商品）
    module.jump_sendorder = function () {
        commonModule.transferUrlToMainDomain("/Common/Page/SendOrder-Index?orderstatus=resend", "_parent");
    }
    //批量回收-跳转到底单页面（状态：已打印未发货+单号类型：二次发货）
    module.jump_waybillcode = function () {
        commonModule.transferUrlToMainDomain("/Common/Page/WaybillCodeList-Index?status=1&sendtype=200", "_parent");
    }
    //批量二次发货
    module.batch_resend = function () {
        // 权限校验
        commonModule.FxPermission(function (p) {
            commonModule.CheckPermission(function (success) {
                if (success) {
                    thisFunc();
                }
                else return;

            }, p.OnlineResend);
        });

        var thisFunc = function () {
            var selections = getRequestData();

            if (!selections || selections.length < 1) {
                layer.alert("请先选择打印记录", { icon: 2 , skin: 'wu-dailog'});
                return;
            }

            //检查重复订单
            var selectData = [];
            $("#tb_data_list .printHistory-chx:checked").each(function (index, item) {
                var Id = $(item).val();
                selectData.push(_rows[Id]);
            })
            //CustomerOrderId中相同字段放到同一个数组中
            var reCustomerOrderIdArr = sortCustomerOrderIdArr(selectData, "CustomerOrderId");
            if (reCustomerOrderIdArr.length > 0) {  //有重复订单时

                //取消重复项的勾选
                for (var i = 0; i < reCustomerOrderIdArr.length; i++) {
                    var curO = reCustomerOrderIdArr[i];
                    for (var x = 0; x < curO.list.length; x++) {
                        var Id = curO.list[x].ID;
                        _rows[Id].checked = false;
                        $("#chk_" + Id).prop('checked', false);
                    }
                }

                var tplt = $.templates("#confirmCustomerOrder_list_tmpl");
                var html = tplt.render(reCustomerOrderIdArr);
                $("#moreOrderDailog_main").empty().append(html);
                var confirmCustomerOrderIdArr = layer.open({
                    type: 1,
                    title: "确认重复订单编号", //不显示标题
                    content: $("#moreOrderDailog"),
                    area: ['700'], //宽高
                    skin: 'wu-dailog',
                    btn: ['确定', '取消'],
                    yes: function () {

                        selections = getRequestData();
                        if (!selections || selections.length < 1) {
                            layer.alert("请先选择打印记录", { icon: 2 , skin: 'wu-dailog'});
                            return;
                        }

                        retryOnlineResend(selections);
                    },
                    cancel: function () {
                    }
                });

            } else {

                retryOnlineResend(selections);
            }
        }
        
    }

    //选择
    module.setCheck = function (customerOrderId, id) {
        common.Foreach(_rows, function (i, r) {
            if (r.CustomerOrderId == customerOrderId && r.ID != id) {
                r.checked = false;
                $("#chk_" + r.ID).prop('checked', false);
            }
        });
        _rows[id].checked = true;
        $("#chk_" + id).prop('checked', true);
    }

    //获取选中的请求数据
    function getRequestData() {
        var selections = [];
        common.Foreach(_rows, function (i, r) {
            if (r.checked) {
                var o = {};
                o.Id = r.ID;
                o.Packs = [];

                $(".packid-" + o.Id + ":checked").each(function (index, item) {
                    var packId = $(item).val();
                    var pack = {};
                    pack.PackId = packId;
                    o.Packs.push(pack);
                });
                selections.push(o);
            }
        });
        return selections;
    }

    //重试发货
    function retryOnlineResend(selections) {
        $("#sureDailog-title").html("确认批量二次发货这些记录");
        var loadIndex = layer.open({
            type: 1,
            title: "批量二次发货", //不显示标题
            content: $("#sureDailog"),
            area: ['460'], //宽高
            skin: 'wu-dailog',
            btn: ['确定', '取消'],
            yes: function () {

                commonModule.Ajax({
                    type: "POST",
                    url: "/PrintHistory/RetryOnlineResend",
                    data: { "requests": selections },
                    success: function (data) {

                        if (data.Success) {
                            try {
                                var model = data.Data;
                                //model.Template = template;
                                var btns = ["关闭"];

                                if (model.ErrorCount > 0) {

                                    //显示详细的错误消息
                                    var dialog = $.templates("#send-logistic-error-dialog-tmpl");
                                    for (var i = 0; i < model.Orders.length; i++) {
                                        var ro = model.Orders[i];
                                        if (ro.IsSuccess)
                                            continue;

                                        //二次发货失败结果提示转换
                                        if (ro.ErrorMessage == "接口错误：请先重试发货，如果问题依然存在，请在店铺后台确认店铺是否已发货") {
                                            ro.ErrorMessage = "部分发货订单不支持二次发货，若不属于该类型，请先重试发货，如果问题依然存在，请在店铺后台确认店铺是否已全部发货";
                                        }
                                    }

                                    var html = dialog.render(model);
                                    layer.open({
                                        type: 1,
                                        title: "发货结果",
                                        //closeBtn: 1,
                                        btn: btns,
                                        shadeClose: true,
                                        area: ['600px', '350px'],
                                        skin: 'wu-dailog',
                                        content: html,
                                        yes: function () {
                                            layer.closeAll();
                                        },
                                        btn2: function () {
                                            layer.closeAll();
                                        },
                                        cancel: function () {
                                            layer.closeAll();
                                        }
                                    });

                                }
                                else {
                                    layer.closeAll();
                                    layer.msg("二次发货成功", { time: 500 });
                                }
                                if (model.SuccessCount > 0) {
                                    module.search();
                                }

                            } catch (e) {

                                var errorMsg = "重试二次发货成功后前端操作异常》" + e.stack;
                                //console.log(errorMsg);
                                common.JsExcptionLog("重试二次发货前端异常日志", errorMsg);

                            }

                        } else {
                            layer.closeAll();

                            //二次发货失败结果提示转换
                            if (data.Message == "接口错误：请先重试发货，如果问题依然存在，请在店铺后台确认店铺是否已发货") {
                                data.Message = "部分发货订单不支持二次发货，若不属于该类型，请先重试发货，如果问题依然存在，请在店铺后台确认店铺是否已全部发货";
                            }
                            layer.alert("二次发货失败：" + data.Message);
                        }
                    }
                });
            },
            cancel: function () {
            }
        });
    }

    function sortCustomerOrderIdArr(arr, str) {  //CustomerOrderId中相同字段放到同一个数组中
        var _arr = [], _t = [], _reArr = [], _tmp;
        arr = arr.sort(function (a, b) {
            var s = a[str],
                t = b[str];
            return s < t ? -1 : 1;
        });
        if (arr.length) {
            _tmp = arr[0][str];
        }
        // 将相同类别的对象添加到统一个数组
        for (var i in arr) {
            if (arr[i][str] === _tmp) {
                _t.push(arr[i]);
            } else {
                _tmp = arr[i][str];
                _arr.push(_t);
                _t = [arr[i]];
            }
        }
        _arr.push(_t);
        for (var j = 0; j < _arr.length; j++) {
            if (_arr[j].length > 1) {
                var obj = {};
                obj.CustomerOrderId = _arr[j][0].CustomerOrderId;
                obj.list = _arr[j];
                _reArr.push(obj)
            }
        }
        return _reArr;
    }

    //批量隐藏
    module.batch_hide = function () {

        var printHistoryIds = [];
        common.Foreach(_rows, function (i, r) {
            if (r.checked) {
                printHistoryIds.push(r.ID);
            }
        });

        if (!printHistoryIds || printHistoryIds.length < 1) {
            layer.alert("请先选择打印记录", { icon: 2 , skin: 'wu-dailog'});
            return;
        }

        $("#sureDailog-title").html("确认删除这些的记录");
        var loadIndex = layer.open({
            type: 1,
            title: "批量删除", //不显示标题
            content: $("#sureDailog"),
            area: ['460'], //宽高
            skin: 'wu-dailog',
            btn: ['确定', '取消'],
            yes: function () {
                commonModule.Ajax({
                    type: "POST",
                    url: "/PrintHistory/SetHide",
                    data: { "ids": printHistoryIds },
                    success: function (rsp) {
                        if (rsp.Success) {
                            layer.msg("删除成功");
                            module.LoadList();
                        } else {
                            layer.msg(rsp.Message);
                        }
                        layer.close(loadIndex);
                    },
                    error: function (rsp) {
                        if (rsp.status == 401) {
                            layer.msg("暂无权限，请联系管理员");
                        } else {
                            layer.msg(rsp.message);
                        }
                    }

                });
            },
            cancel: function () {
            }
        });
    }

    module.Initialize = function () {

        //初始化时间控件
        var obj = {
            days: 7, //天数
            width: 180,
            height: 30,
            startDate: null, //startDate可以不用填 不用填调用客户端时间  格式：yyyy-MM-dd
            endDate: commonModule.ServerNowDate //endDate可以不用填 不用填调用客户端时间      格式：yyyy-MM-dd
        };
        commonModule.InitNewCalenderTime("#txtDate", obj);

        module.LoadList();
        //module.LoadStatisticsCount();
        module.LoadTemplateNames();
        //module.LoadProvinces();

    }

    //重置查询条件
    module.Reset = function () {

        $('#sel_templateName').val("0");
        layui.form.render('select');

        //初始化时间控件
        var obj = {
            days: 7, //天数
            width: 180,
            height: 30,
            startDate: null, //startDate可以不用填 不用填调用客户端时间  格式：yyyy-MM-dd
            endDate: commonModule.ServerNowDate //endDate可以不用填 不用填调用客户端时间      格式：yyyy-MM-dd
        };
        commonModule.InitNewCalenderTime("#txtDate", obj);
        wuFormModule.resetConditionsStyle('.second-send-record-form');
        $("#second_send_list_select_time").removeClass('selectTimeAcitve');
        $("#second_send_list_select_time" + " .newcalender-footer-button").on("click", function () {
            $("#second_send_list_select_time").addClass('selectTimeAcitve');
        })
    }
    //是否 显示因各大平台订加密政策 提示
    module.isShowWarnTar = function () {
        var userTel = $("#userInfo").children("s").text();  //用户名称
        var isShowWarnTar = userTel + "PrintHistory";
        common.LoadCommonSetting(isShowWarnTar, true, function (rsp) {
            if (rsp.Success && rsp.Data != "1") {
                $("#my_alert_error").show();
                $("#closeWarnTar").on("click", function () {
                    common.SaveCommonSetting(isShowWarnTar, "1", function (rsp) {
                        $("#my_alert_error").hide();
                    }); //关闭提示
                })
            }
        })
    }
    module.changeNav = function () {
        module.Reset();
        module.LoadList();
    }
    module.showOrderMore = function (isThis, id) {
        event.stopPropagation();
        $(".showMortTr" + id).toggleClass("hide")
        $(isThis).toggleClass("zk");
        $(isThis).closest(".printHistory-row").toggleClass("zk"); 
    }
    module.chkAllShowMore = function (isThis) {
        var isCheck = isThis.checked;
        if (isCheck) {
            $("#tb_data_list .printHistory-row").addClass("zk");
            $("#tb_data_list .icon-zhankai1").addClass("zk");
            $(".showMortTr").removeClass("hide");
        } else {
            $("#tb_data_list .printHistory-row").removeClass("zk");
            $("#tb_data_list .icon-zhankai1").removeClass("zk");
            $(".showMortTr").addClass("hide");
        }
    }

    return module;
}(printHistorySecondModule || {}, commonModule, jQuery, layer));