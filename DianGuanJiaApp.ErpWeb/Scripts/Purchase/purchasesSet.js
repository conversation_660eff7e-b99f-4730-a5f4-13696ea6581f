var PurchasesSetModule = (function (module, $, layer) {

    var activeId = -1;
    var purchasesTemplate = {
        pageType: '210_297',  //A4
        orient: '1', //纵向
        imageSize: '50x50',
        height: 18,
        productCargoNumber: 0, //商品编码相同时:合并/不合并  0代不合并，1代合并
        fontFamily: '宋体',
        fontSize: 14,
        tableColor: '#cccccc',
        lineHeight: "100%",
        cargoNumberNotContains: 0, //Sku编码相同时:合并/不合并  0代不合并，1代合并
        firstSortField: 'sphh', //sphh:商品编码   spjc:商品简称   spbt:商品标题    spsl:商品数量
        ShortTitle: 0, //商品简称相同时:合并/不合并  0代不合并，1代合并
        skuPropsMerge: 0, //规格名称相同时:合并/不合并  0代不合并，1代合并
        skuPriceMerge: 0, //同一SKU价格不同:合并/不合并  0代不合并，1代合并
        skuPropsShortMerge: 0, //规格简称相同时:合并/不合并  0代不合并，1代合并							
    }

    var purchasesCol = [
        { id: 0, textId: "index", title: '序号', type: 0 },
        { id: 1, textId: 'ProductSubject', title: '商品标题', type: 0 },
        { id: 2, textId: "ShortTitle", title: '商品简称', type: 0 },
        { id: 3, textId: 'ProductCargoNumber', title: '商品编码', type: 0 },
        { id: 4, textId: "TotalItemCount", title: '总数量', type: 0 },
        //{ id: 5, textId: 'TotalPurchasePrice', title: '成本总价', type: 0 },
        { id: 23, textId: "TotalAmount", title: '总金额', type: 0 },
        //{ id: 24, textId: "TotalProfit", title: '总商品差价', type: 0 },
        //{ id: 6, textId: "WangWang", title: '店铺旺旺', type: 0 },
        { id: 7, textId: "ProductImgUrl", title: '商品主图', type: 0 },
        { id: 8, textId: 'CargoNumber', title: 'Sku编码', type: 1 },
        { id: 9, textId: "ShortTitle", title: '规格简称', type: 1 },
        { id: 10, textId: "Count", title: '数量', type: 1 },
        //{ id: 11, textId: "SingleCostPrice", title: '单品成本', type: 1 },
        //{ id: 17, textId: "CostPrice", title: '单品总成本', type: 1 },
        //{ id: 18, textId: "Profit", title: '单品差价', type: 1 },
        { id: 12, textId: "ProductId", title: '商品ID', type: 1 },
        { id: 13, textId: "ColorAndSize", title: '规格颜色+规格尺寸', type: 1 },
        { id: 14, textId: "SkuImgUrl", title: '规格图', type: 1 },
        { id: 15, textId: "Price", title: '单价', type: 1 },
        { id: 16, textId: "ItemAmount", title: '金额', type: 1 },
        { id: 21, textId: "br", title: '【换行】', type: 2 },
        { id: 22, textId: "CustomText", title: '【自定义文字】', type: 2 },
        //20240122 1688定制服务
        { id: 23, textId: "ExtAttr4", title: '定制服务', type: 1, coldesc: '仅限1688定制服务商品信息生效' }
    ]

    var isShowTotalFelds = [  //显示统计设置
        { Value: 'TotalItemCount', Text: '总件数' },
        //{ Value: 'TotalPurchasePrice', Text: '总成本' },
        { Value: 'TotalAmount', Text: '总金额' },
        //{ Value: 'TotalProfit', Text: '总商品差价' }
    ]
    var selectTotalFelds = []  //选中统计字段
    var totalFeldsSelect = null//选中统计字段 下拉构造函数

    //初始化表格  调接口
    module.defaultTableData = {
        tableName: '模板(一)', //模板名称
        fontFamily: '宋体', //表格字体
        fontSize: 14, //表格字号	
        fontColor: '#000000', //表格字体颜色
        tableColor: '#cccccc', //表格线条颜色
        imageSize: '50x50', //商品主图片尺寸
        height: 18, //表格高
        lineHeight: 130, //表格行高
        pageType: '210_297', //A4
        orient: '1', //纵向
        productCargoNumber: 0, //商品编码相同时:合并/不合并  0代不合并，1代合并
        cargoNumberNotContains: 0, //Sku编码相同时:合并/不合并  0代不合并，1代合并
        firstSortField: '', //sphh:商品编码   spjc:商品简称   spbt:商品标题    spsl:商品数量
        ShortTitle: 0, //商品简称相同时:合并/不合并  0代不合并，1代合并
        skuPropsMerge: 0, //规格名称相同时:合并/不合并  0代不合并，1代合并
        skuPriceMerge: 0, //同一SKU价格不同:合并/不合并  0代不合并，1代合并
        skuPropsShortMerge: 0, //规格简称相同时:合并/不合并  0代不合并，1代合并	
        isShowTotalFelds: { TotalItemCount: true, TotalPurchasePrice: true, TotalAmount: true, TotalProfit: true },//统计显示设置，默认全开
        tdsData: [
            {
                id: 0,
                title: '商品名称',
                textId: 'headerName01',
                tdWidth: 400,
                fontFamily: '宋体', //默认为空
                fontSize: 12, //默认0
                color: '#000000', //默认为空
                fontWeight: 'normal', //默认为空  normal  bold
                textAlign: 'left', //默认为空
                sort: 0,
                tdContent: [
                    { id: 0, textId: "index", title: '序号', type: 0 },
                    { id: 777, textId: "CustomText", title: ' ', type: 2 },
                    { id: 1, textId: 'ProductSubject', title: '商品标题', type: 0 },
                    { id: 888, textId: "br", title: '【换行】', type: 2 },
                    { id: 7, textId: "ProductImgUrl", title: '商品主图', type: 0 },
                    { id: 14, textId: "br", title: '【换行】', type: 2 },
                    { id: 2, textId: "ShortTitle", title: '商品简称', type: 0 },
                    { id: 3, textId: 'ProductCargoNumber', title: '商品编码', type: 0 },
                ]
            },
            {
                id: 1,
                title: '商品规格',
                textId: 'headerName02',
                tdWidth: 265,
                fontFamily: '', //默认为空
                fontSize: -1, //默认-1
                color: '', //默认为空
                fontWeight: 'normal', //默认为空  normal  bold
                textAlign: '', //默认为空
                sort: 1,
                tdContent: [
                    { id: 13, textId: "ColorAndSize", title: '规格颜色+规格尺寸', type: 1 },
                    { id: 10, textId: "Count", title: '数量', type: 1 },
                ]
            },
            {
                id: 2,
                title: '总数量',
                textId: 'headerName03',
                tdWidth: 60,
                fontFamily: '', //默认为空
                fontSize: 12, //默认0
                color: '', //默认为空
                fontWeight: 'normal', //默认为空  normal  bold
                textAlign: '', //默认为空
                sort: 1,
                tdContent: [
                    { id: 4, textId: "TotalItemCount", title: '总数量', type: 0 },
                ]
            }
        ]
    }

    //固定表格模板 4 个 
    module.tableModule = [
        {
            tableName: '模板(一)', //模板名称
            fontFamily: '宋体', //表格字体
            fontSize: 14, //表格字号	
            fontColor: '#000000', //表格字体颜色
            tableColor: '#cccccc', //表格线条颜色
            imageSize: '50x50', //商品主图片尺寸
            height: 18, //表格高
            lineHeight: 130, //表格行高
            pageType: '210_297', //A4
            orient: '1', //纵向
            productCargoNumber: 0, //商品编码相同时:合并/不合并  0代不合并，1代合并
            cargoNumberNotContains: 0, //Sku编码相同时:合并/不合并  0代不合并，1代合并
            firstSortField: '', //sphh:商品编码   spjc:商品简称   spbt:商品标题    spsl:商品数量
            ShortTitle: 0, //商品简称相同时:合并/不合并  0代不合并，1代合并
            skuPropsMerge: 0, //规格名称相同时:合并/不合并  0代不合并，1代合并
            skuPriceMerge: 0, //同一SKU价格不同:合并/不合并  0代不合并，1代合并
            skuPropsShortMerge: 0, //规格简称相同时:合并/不合并  0代不合并，1代合并	
            isShowTotalFelds: { TotalItemCount: true, TotalPurchasePrice: true, TotalAmount: true, TotalProfit: true },//统计显示设置，默认全开
            tdsData: [
                {
                    id: 0,
                    title: '商品名称',
                    textId: 'headerName01',
                    tdWidth: 400,
                    fontFamily: '宋体', //默认为空
                    fontSize: 12, //默认0
                    color: '#000000', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: 'left', //默认为空
                    sort: 0,
                    tdContent: [
                        { id: 0, textId: "index", title: '序号', type: 0 },
                        { id: 777, textId: "CustomText", title: ' ', type: 2 },
                        { id: 1, textId: 'ProductSubject', title: '商品标题', type: 0 },
                        { id: 888, textId: "br", title: '【换行】', type: 2 },
                        { id: 7, textId: "ProductImgUrl", title: '商品主图', type: 0 },
                        { id: 14, textId: "br", title: '【换行】', type: 2 },
                        { id: 2, textId: "ShortTitle", title: '商品简称', type: 0 },
                        { id: 3, textId: 'ProductCargoNumber', title: '商品编码', type: 0 },
                    ]
                },
                {
                    id: 1,
                    title: '商品规格',
                    textId: 'headerName02',
                    tdWidth: 265,
                    fontFamily: '', //默认为空
                    fontSize: -1, //默认-1
                    color: '', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: '', //默认为空
                    sort: 1,
                    tdContent: [
                        { id: 13, textId: "ColorAndSize", title: '规格颜色+规格尺寸', type: 1 },
                        { id: 10, textId: "Count", title: '数量', type: 1 },
                    ]
                },
                {
                    id: 2,
                    title: '总数量',
                    textId: 'headerName03',
                    tdWidth: 60,
                    fontFamily: '', //默认为空
                    fontSize: 12, //默认0
                    color: '', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: '', //默认为空
                    sort: 2,
                    tdContent: [
                        { id: 4, textId: "TotalItemCount", title: '总数量', type: 0 },
                    ]
                }
            ]
        },
        {
            tableName: '模板(二)', //模板名称
            fontFamily: '宋体', //表格字体
            fontSize: 14, //表格字号	
            fontColor: '#000000', //表格字体颜色
            tableColor: '#cccccc', //表格线条颜色
            imageSize: '50x50', //商品主图片尺寸
            height: 18, //表格高
            lineHeight: 130, //表格行高
            pageType: '210_297', //A4
            orient: '1', //纵向
            productCargoNumber: 0, //商品编码相同时:合并/不合并  0代不合并，1代合并
            cargoNumberNotContains: 0, //Sku编码相同时:合并/不合并  0代不合并，1代合并
            firstSortField: '', //sphh:商品编码   spjc:商品简称   spbt:商品标题    spsl:商品数量
            ShortTitle: 0, //商品简称相同时:合并/不合并  0代不合并，1代合并
            skuPropsMerge: 0, //规格名称相同时:合并/不合并  0代不合并，1代合并
            skuPriceMerge: 0, //同一SKU价格不同:合并/不合并  0代不合并，1代合并
            skuPropsShortMerge: 0, //规格简称相同时:合并/不合并  0代不合并，1代合并
            isShowTotalFelds: { TotalItemCount: true, TotalPurchasePrice: true, TotalAmount: true, TotalProfit: true },//统计显示设置，默认全开
            tdsData: [
                {
                    id: 0,
                    title: '序号',
                    textId: 'headerName01',
                    tdWidth: 30,
                    fontFamily: '宋体', //默认为空
                    fontSize: 12, //默认-1
                    color: '#000000', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: 'left', //默认为空
                    sort: 0,
                    tdContent: [
                        { id: 0, textId: "index", title: '序号', type: 0 }
                    ]
                },
                {
                    id: 1,
                    title: '商品名称',
                    textId: 'headerName02',
                    tdWidth: 400,
                    fontFamily: '', //默认为空
                    fontSize: -1, //默认0
                    color: '', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: 'center', //默认为空
                    sort: 1,
                    tdContent: [
                        { id: 7, textId: "ProductImgUrl", title: '商品主图', type: 0 },
                        { id: 1000, textId: "br", title: '【换行】', type: 2 },
                        { id: 1, textId: 'ProductSubject', title: '商品标题', type: 0 },
                        { id: 1001, textId: "br", title: '【换行】', type: 2 },
                        { id: 2, textId: "ShortTitle", title: '商品简称', type: 0 },
                        { id: 3, textId: 'ProductCargoNumber', title: '商品编码', type: 0 },
                    ]
                }
                ,
                {
                    id: 2,
                    title: 'Sku编码',
                    textId: 'headerName03',
                    tdWidth: 100,
                    fontFamily: '', //默认为空
                    fontSize: -1, //默认-1
                    color: '', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: '', //默认为空
                    sort: 2,
                    tdContent: [
                        { id: 8, textId: 'CargoNumber', title: 'Sku编码', type: 1 },
                    ]
                },
                {
                    id: 3,
                    title: '数量',
                    textId: 'headerName04',
                    tdWidth: 60,
                    fontFamily: '', //默认为空
                    fontSize: 14, //默认0
                    color: '', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: '', //默认为空
                    sort: 3,
                    tdContent: [
                        { id: 10, textId: "Count", title: '数量', type: 1 },
                    ]
                },
                {
                    id: 4,
                    title: '总数量',
                    textId: 'headerName05',
                    tdWidth: 60,
                    fontFamily: '', //默认为空
                    fontSize: 14, //默认0
                    color: '', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: '', //默认为空
                    sort: 4,
                    tdContent: [
                        { id: 4, textId: "TotalItemCount", title: '总数量', type: 0 },
                    ]
                }
            ]
        },
        {
            tableName: '模板(三)', //模板名称
            fontFamily: '宋体', //表格字体
            fontSize: 14, //表格字号	
            fontColor: '#000000', //表格字体颜色
            tableColor: '#cccccc', //表格线条颜色
            imageSize: '50x50', //商品主图片尺寸
            height: 18, //表格高
            lineHeight: 130, //表格行高
            pageType: '210_297', //A4
            orient: '1', //纵向
            productCargoNumber: 0, //商品编码相同时:合并/不合并  0代不合并，1代合并
            cargoNumberNotContains: 0, //Sku编码相同时:合并/不合并  0代不合并，1代合并
            firstSortField: '', //sphh:商品编码   spjc:商品简称   spbt:商品标题    spsl:商品数量
            ShortTitle: 0, //商品简称相同时:合并/不合并  0代不合并，1代合并
            skuPropsMerge: 0, //规格名称相同时:合并/不合并  0代不合并，1代合并
            skuPriceMerge: 0, //同一SKU价格不同:合并/不合并  0代不合并，1代合并
            skuPropsShortMerge: 0, //规格简称相同时:合并/不合并  0代不合并，1代合并
            isShowTotalFelds: { TotalItemCount: true, TotalPurchasePrice: true, TotalAmount: true, TotalProfit: true },//统计显示设置，默认全开
            tdsData: [
                {
                    id: 0,
                    title: '序号',
                    textId: 'headerName01',
                    tdWidth: 30,
                    fontFamily: '宋体', //默认为空
                    fontSize: 12, //默认0
                    color: '#000000', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: 'left', //默认为空
                    sort: 0,
                    tdContent: [
                        { id: 0, textId: "index", title: '序号', type: 0 }
                    ]
                },
                {
                    id: 1,
                    title: '宝贝名称',
                    textId: 'headerName02',
                    tdWidth: 350,
                    fontFamily: '', //默认为空
                    fontSize: 14, //默认0
                    color: '', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: 'center', //默认为空
                    sort: 1,
                    tdContent: [
                        { id: 7, textId: "ProductImgUrl", title: '商品主图', type: 0 },
                        { id: 1000, textId: "br", title: '【换行】', type: 2 },
                        { id: 1, textId: 'ProductSubject', title: '商品标题', type: 0 },
                        { id: 1001, textId: "br", title: '【换行】', type: 2 },
                        { id: 2, textId: "ShortTitle", title: '商品简称', type: 0 },
                        { id: 3, textId: 'ProductCargoNumber', title: '商品编码', type: 0 },
                    ]
                },
                {
                    id: 2,
                    title: '宝贝规格',
                    textId: 'headerName03',
                    tdWidth: 200,
                    fontFamily: '', //默认为空
                    fontSize: -1, //默认-1
                    color: '', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: '', //默认为空
                    sort: 2,
                    tdContent: [
                        { id: 8, textId: 'CargoNumber', title: 'Sku编码', type: 1 },
                        { id: 10, textId: "Count", title: '数量', type: 1 },
                    ]
                },
                {
                    id: 3,
                    title: '总数量',
                    textId: 'headerName04',
                    tdWidth: 60,
                    fontFamily: '', //默认为空
                    fontSize: 14, //默认0
                    color: '', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: '', //默认为空
                    sort: 3,
                    tdContent: [
                        { id: 4, textId: "TotalItemCount", title: '总数量', type: 0 },
                    ]
                }
                //,
                //{
                //    id: 4,
                //    title: '总成本',
                //    textId: 'headerName05',
                //    tdWidth: 60,
                //    fontFamily: '', //默认为空
                //    fontSize: 14, //默认0
                //    color: '', //默认为空
                //    fontWeight: 'normal', //默认为空  normal  bold
                //    textAlign: '', //默认为空
                //    sort: 4,
                //    tdContent: [
                //        { id: 5, textId: 'TotalPurchasePrice', title: '成本总价', type: 0 }
                //    ]
                //}
            ]
        },
        {
            tableName: '模板(四)', //模板名称
            fontFamily: '宋体', //表格字体
            fontSize: 14, //表格字号	
            fontColor: '#000000', //表格字体颜色
            tableColor: '#cccccc', //表格线条颜色
            imageSize: '50x50', //商品主图片尺寸
            height: 18, //表格高
            lineHeight: 130, //表格行高
            pageType: '210_297', //A4
            orient: '1', //纵向
            productCargoNumber: 0, //商品编码相同时:合并/不合并  0代不合并，1代合并
            cargoNumberNotContains: 0, //Sku编码相同时:合并/不合并  0代不合并，1代合并
            firstSortField: 'sphh', //sphh:商品编码   spjc:商品简称   spbt:商品标题    spsl:商品数量
            ShortTitle: 0, //商品简称相同时:合并/不合并  0代不合并，1代合并
            skuPropsMerge: 0, //规格名称相同时:合并/不合并  0代不合并，1代合并
            skuPriceMerge: 0, //同一SKU价格不同:合并/不合并  0代不合并，1代合并
            skuPropsShortMerge: 0, //规格简称相同时:合并/不合并  0代不合并，1代合并
            isShowTotalFelds: { TotalItemCount: true, TotalPurchasePrice: true, TotalAmount: true, TotalProfit: true },//统计显示设置，默认全开
            tdsData: [
                {
                    id: 0,
                    title: '序号',
                    textId: 'headerName01',
                    tdWidth: 30,
                    fontFamily: '宋体', //默认为空
                    fontSize: 12, //默认0
                    color: '#000000', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: 'left', //默认为空
                    sort: 0,
                    tdContent: [
                        { id: 0, textId: "index", title: '序号', type: 0 }
                    ]
                },
                {
                    id: 1,
                    title: '商品主图',
                    textId: 'headerName02',
                    tdWidth: 160,
                    fontFamily: '', //默认为空
                    fontSize: 14, //默认0
                    color: '', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: 'center', //默认为空
                    sort: 1,
                    tdContent: [
                        { id: 7, textId: "ProductImgUrl", title: '商品主图', type: 0 },
                    ]
                },
                {
                    id: 2,
                    title: '商品规格',
                    textId: 'headerName03',
                    tdWidth: 300,
                    fontFamily: '', //默认为空
                    fontSize: 14, //默认0
                    color: '', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: '', //默认为空
                    sort: 2,
                    tdContent: [
                        { id: 13, textId: "ColorAndSize", title: '规格颜色+规格尺寸', type: 1 },
                    ]
                }
                ,
                {
                    id: 3,
                    title: '数量',
                    textId: 'headerName04',
                    tdWidth: 60,
                    fontFamily: '', //默认为空
                    fontSize: 14, //默认0
                    color: '', //默认为空
                    fontWeight: 'normal', //默认为空  normal  bold
                    textAlign: '', //默认为空
                    sort: 3,
                    tdContent: [
                        { id: 10, textId: "Count", title: '数量', type: 1 },
                    ]
                }
            ]
        },
        {
            tableName: '老版模板', //模板名称
            fontFamily: '宋体', //表格字体
            fontSize: 14, //表格字号	
            fontColor: '#000000', //表格字体颜色
            tableColor: '#cccccc', //表格线条颜色
            imageSize: '50x50', //商品主图片尺寸
            height: 18, //表格高
            lineHeight: 130, //表格行高
            pageType: '210_297', //A4
            orient: '1', //纵向
            productCargoNumber: 0, //商品编码相同时:合并/不合并  0代不合并，1代合并
            cargoNumberNotContains: 0, //Sku编码相同时:合并/不合并  0代不合并，1代合并
            firstSortField: 'sphh', //sphh:商品编码   spjc:商品简称   spbt:商品标题    spsl:商品数量
            ShortTitle: 0, //商品简称相同时:合并/不合并  0代不合并，1代合并
            skuPropsMerge: 0, //规格名称相同时:合并/不合并  0代不合并，1代合并
            skuPriceMerge: 0, //同一SKU价格不同:合并/不合并  0代不合并，1代合并
            skuPropsShortMerge: 0, //规格简称相同时:合并/不合并  0代不合并，1代合并			
            tdsData: []
        }
    ]


    $(function () {
        //把设置条件放在弹窗显示
        $("#purchasesSetDialog_main_content").html($("#content_left_up").html());
        initColData(purchasesCol, "#tableContentSelect");
        loadDefaultTableData();
        module.initTable(module.defaultTableData);
        module.initRightMoudelShow(module.tableModule);
    })

    var loadDefaultTableData = function () {
        commonModule.LoadCommonSetting("/ErpWeb/Purchase/TemplateSetting", false, function (rsp) {
            if (rsp.Success && rsp.Data) {
                module.defaultTableData = JSON.parse(rsp.Data) || module.defaultTableData;
            }
        });
    }

    //初始化编辑td select下拉项
    function initColData(purchasesCol, eles) {
        var html = "";
        var tar = "";
        for (var i = 0; i < purchasesCol.length; i++) {
            tar = purchasesCol[i].type == 0 ? ' *' : '';
            if (purchasesCol[i].coldesc) {
                html += '<option data-type="' + purchasesCol[i].type + '" value="' + purchasesCol[i].textId + '" title="' + purchasesCol[i].coldesc + '">' + purchasesCol[i].title + tar + '</option>';
            } else {
                html += '<option data-type="' + purchasesCol[i].type + '" value="' + purchasesCol[i].textId + '">' + purchasesCol[i].title + tar + '</option>';
            }
        }
        $(eles).append(html);
    }

    //初始化右边的模板选项
    module.initRightMoudelShow = function (tableModule) {
        var html = '';
        for (var i = 0; i < tableModule.length; i++) {
            html += '<dd data-index="' + i + '"><span>' + tableModule[i].tableName + '</span><div class="editTable-module-img"  style="background-image: url(/Content/Images/table_style_' + (i + 1) + '.png)"></div></dd>';
        }
        $("#editTable_module").append(html);

        $(".editTable-module>dd").each(function (index, item) {
            $(item).on("mouseover", function () {
                $(item).children('.editTable-module-img').css({
                    backgroundPosition: '0 -56px'
                })
            })
            $(item).on("mouseout", function () {
                $(item).children('.editTable-module-img').css({
                    backgroundPosition: '0 0'
                })
            })
            //点击右边模板，出现弹窗模板
            $(item).on("click", function () {
                $("#editCol").hide();
                $(".purchasesSetDialog").fadeIn(100);
                $(".editTable-module>dd").removeClass("active");
                $(item).addClass("active");
                //如果弹窗有两个表格时，初始删除第二个
                var $tableData = PurchasesSetModule.tableModule[index];
                $("#purchasesSetDialog_title").text($tableData.tableName)
                var tableHtml = '';
                PurchasesSetModule.initTable($tableData);

                if (index == 4) {
                    $("#purchasesSetDialog_main_content").hide();
                    tableHtml = $("#tableWrap>table").html();
                    $("#purchasesSetDialog_table_content").css({ borderLeft: "unset" }).html('<img width="100%" src="/Content/Images/lbbeihuodan.png">')
                } else {
                    $("#purchasesSetDialog_main_content").show();
                    tableHtml = $("#tableWrap>table").html();
                    $("#purchasesSetDialog_table_content").html(tableHtml)
                }
            })
        })

    }

    //初始化表格
    module.initTable = function (tableData) {
        var $table_thead_tr = "";
        var $table_tbody_trH = "";
        var $table_tbody_trC = "";
        var $table_tbody_trO = "";


        var defaultFontSize = tableData.fontSize;
        var defaultFontFamily = tableData.fontFamily;

        //排序
        var $tdsData = tableData.tdsData.sort(function (a, b) {
            return a.sort - b.sort;
        });
        $("#tableWrap").children('#tableWrap_img').remove();

        if (tableData.tableName != "老版模板") {
            $("#addTableCol").show();
            $("#purchasesSet_table_content").show();

            for (var i = 0; i < $tdsData.length; i++) {
                var width = $tdsData[i].tdWidth != '' ? $tdsData[i].tdWidth + 'px' : '50px';

                $table_thead_tr += '<th data-id="' + $tdsData[i].id + '">' + $tdsData[i].title + '</th>';
                $table_tbody_trH = '<td  colspan="' + $tdsData.length +
                    '" style="text-align: left;background-color: #fff;"><span style="color:#fe6f4f">市场/档口名称 </span><span style="color:#888">（如果设置过市场、档口位置则会显示此行）</span></td>';
                $table_tbody_trC += '<td  data-id="' + $tdsData[i].id + '">';
                $table_tbody_trO += '<td  data-id="' + $tdsData[i].id +
                    '" ><div class="table-btn"><i onclick=\'PurchasesSetModule.toggleLocation("' + $tdsData[i].id +
                    '",false)\'></i><span class="td-set">设置</span><i onclick=\'PurchasesSetModule.toggleLocation("' + $tdsData[i].id +
                    '",true)\' style="background-position:-14px -87px"></i></div></td>';

                var $tdContentType0 = [];
                var $tdContentType1 = [];
                var $tdContent = [];
                var isType = 2;
                var forkLength = 1; //如果 列内容维度全部为1时，forkLength取值为2 循环2次
                for (var k = 0; k < $tdsData[i].tdContent.length; k++) {
                    if ($tdsData[i].tdContent[k].type == 0 || $tdsData[i].tdContent[k].type == 2) {
                        $tdContentType0.push($tdsData[i].tdContent[k])
                    }
                    if ($tdsData[i].tdContent[k].type == 1 || $tdsData[i].tdContent[k].type == 2) {
                        $tdContentType1.push($tdsData[i].tdContent[k])
                    }
                }
                for (var m = 0; m < $tdsData[i].tdContent.length; m++) {
                    if ($tdsData[i].tdContent[m].type == 1) {
                        isType = 1;
                    }
                    if ($tdsData[i].tdContent[m].type == 0) {
                        isType = 0;
                        break;
                    }
                }
                if (isType == 0 || isType == 2) {
                    $tdContent = $tdContentType0;
                    forkLength = 1;
                } else {
                    $tdContent = $tdContentType1;
                    forkLength = 2
                }

                var fontFamily = $tdsData[i].fontFamily != '' ? $tdsData[i].fontFamily : '';
                var fontSize = $tdsData[i].fontSize != -1 ? $tdsData[i].fontSize + 'px' : '';
                var fontWeight = $tdsData[i].fontWeight != '' ? $tdsData[i].fontWeight : '';
                var fontColor = $tdsData[i].fontColor != '' ? $tdsData[i].color : '';
                var textAlign = $tdsData[i].textAlign != '' ? $tdsData[i].textAlign : '';
                var width = $tdsData[i].tdWidth != '' ? $tdsData[i].tdWidth + 'px' : '';

                var imageSize = tableData.imageSize.split('x')[0] + 'px';

                $table_tbody_trC += '<div class="morelatitude" style="width:' + width +
                    ';font-family:' + fontFamily +
                    ';font-size:' + fontSize + ';font-weight:' + fontWeight + ';color:' + fontColor + ';text-align:' + textAlign +
                    ';">';
                for (var k = 0; k < forkLength; k++) {
                    $table_tbody_trC += '<div>';
                    for (var j = 0; j < $tdContent.length; j++) {

                        if ($tdContent[j].textId == "br") {
                            $table_tbody_trC += '<br  data-TdId="' + $tdsData[i].id + '" data-tdListId="' + $tdContent[j].id + '" />';
                        } else if ($tdContent[j].textId == "CustomText") {
                            //$table_tbody_trC += $tdContent[j].customText;
                            $table_tbody_trC += '<span data-TdId="' + $tdsData[i].id + '" data-tdListId="' + $tdContent[j].id + '">' +
                                $tdContent[j].title + '</span>';
                        } else if ($tdContent[j].textId == "ProductImgUrl" || $tdContent[j].textId == "SkuImgUrl") {
                            $table_tbody_trC += '<img style="width:' + imageSize + '" src="/Content/Images/beihuodan-pic01.png" data-TdId="' + $tdsData[i].id +
                                '" data-tdListId="' +
                                $tdContent[j].id + '">';
                        } else {
                            //$table_tbody_trC += $tdContent[j].title;
                            $table_tbody_trC += '<span data-TdId="' + $tdsData[i].id + '" data-tdListId="' + $tdContent[j].id + '">' +
                                $tdContent[j].title + '</span>';
                        }
                    }
                    $table_tbody_trC += '</div>';
                }
                $table_tbody_trC += '</div>';
                $table_tbody_trC += '</td>';
                $table_tbody_trO += '</td>';
            }
            $("#purchasesSet_table_thead>tr").html($table_thead_tr);
            $("#tabelContentheader").html($table_tbody_trH);
            $("#tabelContent").css({ fontSize: defaultFontSize + "px", fontFamily: defaultFontFamily }).html($table_tbody_trC);
            $("#tabelOparete").html($table_tbody_trO);

        } else {

            $("#addTableCol").hide();
            $("#purchasesSet_table_content").hide();
            $("#tableWrap").prepend('<img id="tableWrap_img" width="100%" src="/Content/Images/lbbeihuodan.png">')

        }

        module.activeTrColor("#purchasesSet_table_thead>tr>th");
        module.activeTrColor("#purchasesSet_table_tbody>#tabelContent>td");
        module.activeTrColor("#purchasesSet_table_tbody>#tabelOparete>td");


        //初始化下拉框和input的值
        $("#content_left_up select").each(function (index, item) {
            var $name = $(item).attr("name");
            for (var k in tableData) {
                if ($name == k) {
                    $("select[name='" + k + "']").find("option[value='" + tableData[k] + "']").attr("selected", true);
                }
            }
        })
        $("#content_left_up input[name=height]").val(tableData.height);
        //初始表格线条颜色
        $("#purchasesSet_table_content thead tr th").css({
            borderColor: tableData.tableColor
        });
        $("#purchasesSet_table_content tbody tr td").css({
            borderColor: tableData.tableColor
        });
        $("#purchasesSet_table_content").css({
            borderLeftColor: tableData.tableColor
        });



        var getIsShowTotalFelds = tableData.isShowTotalFelds;
        $("#SelectShowTotalFelds").html("");
        selectTotalFelds = [];
        if (typeof getIsShowTotalFelds != "undefined") {
            for (var k in getIsShowTotalFelds) {
                if (getIsShowTotalFelds[k]) {
                    for (var i = 0; i < isShowTotalFelds.length; i++) {
                        if (k == isShowTotalFelds[i].Value) {
                            selectTotalFelds.push(isShowTotalFelds[i]);
                            break;
                        }
                    }
                }
            }

        } else {  //老用户：以前没有isShowTotalFelds字段，全部默认选中
            selectTotalFelds = [
                { Value: 'TotalItemCount', Text: '总件数' },
                //{ Value: 'TotalPurchasePrice', Text: '总成本' },
                { Value: 'TotalAmount', Text: '总金额' },
                //{ Value: 'TotalProfit', Text: '总商品差价' }
            ]
        }

        var selectInit = {
            eles: '#SelectShowTotalFelds',  //
            emptyTitle: '请选择', //设置没有选择属性时，出现的标题
            data: isShowTotalFelds,      //渲染的所有数据 格式：[{Value: '1',Text: 'alitestforisv02'}]
            searchType: 1, //1出现搜索框，不设置不出现搜索框
            showWidth: '250px', //显示下拉box的宽度
            isRadio: false, //有设置，下拉框改为单选
            allSelect: true,//是否显示勾选框
            selectData: selectTotalFelds,  //初始化选中的数据
            skin: 'wu-select-skin',
            leftTitle: '合计字段显示', //左边显示的标题
        };
        totalFeldsSelect = new selectBoxModule2();
        totalFeldsSelect.initData(selectInit);


    }

    module.activeTrColor = function (ele) {
        $(ele).each(function (index, item) {
            $(item).on("click", function () {
                if (!$(item).hasClass("td-active")) {
                    $("#purchasesSet_table_thead>tr>th").removeClass("td-active");
                    $("#purchasesSet_table_tbody>tr>td").removeClass("td-active");
                    $("#purchasesSet_table_tbody>#tabelContent>td:eq(" + index + ")").addClass("td-active");
                    $("#purchasesSet_table_tbody>#tabelOparete>td:eq(" + index + ")").addClass("td-active");
                    $("#purchasesSet_table_thead>tr>th:eq(" + index + ")").addClass("td-active");
                    $(item).addClass("td-active");
                    var id = $(item).attr("data-id");
                    var $defaultTableDataTdsData = module.defaultTableData.tdsData;

                    //渲染 表格列内容: 删除编辑					
                    activeId = id;
                    $("#editCol").show();
                    module.activeColor($defaultTableDataTdsData, id);


                } else {
                    $("#purchasesSet_table_thead>tr>th").removeClass("td-active");
                    $("#purchasesSet_table_tbody>tr>td").removeClass("td-active");
                    $("#editCol").hide();
                }

            })
        })
    }


    module.activeColor = function ($defaultTableDataTdsData, id) {


        for (var i = 0; i < $defaultTableDataTdsData.length; i++) {

            if ($defaultTableDataTdsData[i].id == id) {
                console.log($defaultTableDataTdsData[i].textAlign)
                console.log($defaultTableDataTdsData[i].fontFamily)
                console.log($defaultTableDataTdsData[i].fontSize)

                var $html = "";
                for (var j = 0; j < $defaultTableDataTdsData[i].tdContent.length; j++) {
                    $html += '<li>' + $defaultTableDataTdsData[i].tdContent[j].title +
                        '<i onclick=\'PurchasesSetModule.delTableDataTdContent("' + $defaultTableDataTdsData[i].id + '","' +
                        $defaultTableDataTdsData[i].tdContent[j].id + '",this)\' class="table-labels-delIcon"></i></li>'
                }
                $("#targetPurchasesCol").html($html);

                $("#editCol input[name=title]").val($defaultTableDataTdsData[i].title);
                $("#editCol input[name=tdWidth]").val($defaultTableDataTdsData[i].tdWidth);

                if ($defaultTableDataTdsData[i].textAlign != '') {
                    $("#editColTextAlign").find("option[value=" + $defaultTableDataTdsData[i].textAlign + "]").attr("selected", true);
                }
                if ($defaultTableDataTdsData[i].fontFamily) {
                    $('#editColFontFamily').find("option:selected").prop("selected", false);
                    $("#editColFontFamily").find("option[value=" + $defaultTableDataTdsData[i].fontFamily + "]").prop("selected", true);
                } else {
                    $('#editColFontFamily').find("option:selected").prop("selected", false);
                }

                if ($defaultTableDataTdsData[i].fontColor != '') {
                    $("#editColFontColor").find("option[value=" + $defaultTableDataTdsData[i].fontColor + "]").attr("selected", true);
                }
                if ($defaultTableDataTdsData[i].fontSize != -1) {
                    $('#editColFontSize').find("option:selected").prop("selected", false);
                    $("#editColFontSize").find("option[value=" + $defaultTableDataTdsData[i].fontSize + "]").prop("selected", true);
                } else {
                    $('#editColFontSize').find("option:selected").prop("selected", false);
                }

                $("#editColFontWeight").find("option[value=" + $defaultTableDataTdsData[i].fontWeight + "]").attr("selected", true);

            }
        }

    }



    //删除列
    module.delTableDataTd = function () {

        var tdsData = module.defaultTableData.tdsData;
        if (tdsData.length == 1) {

            layer.msg("至少保留一列")
            return;

        } else {
            for (var i = 0; i < tdsData.length; i++) {
                if (tdsData[i].id == activeId) {
                    tdsData.splice(i, 1)
                    module.initTable(module.defaultTableData)
                }
            }
        }

        $("#editCol").hide();

    }
    //删除列数组的项
    module.delTableDataTdContent = function (tdId, tdListId, isThis) {
        var tdsData = module.defaultTableData.tdsData;
        for (var i = 0; i < tdsData.length; i++) {
            if (tdsData[i].id == tdId) {
                for (var j = 0; j < tdsData[i].tdContent.length; j++) {
                    if (tdsData[i].tdContent[j].id == tdListId) {
                        tdsData[i].tdContent.splice(j, 1);
                        $(isThis).parent().remove();
                        module.initTable(module.defaultTableData);

                        tdActive("#purchasesSet_table_content #purchasesSet_table_thead>tr>th");
                        tdActive("#purchasesSet_table_content #purchasesSet_table_tbody>tr>td");

                        function tdActive(ele) {
                            $(ele).each(function (index, item) {
                                if ($(item).attr("data-id") == tdId) {
                                    $(item).addClass("td-active")
                                }
                            })

                        }
                    }
                }

            }
        }
    }

    //切换表格位置	
    module.toggleLocation = function (id, isRight) {
        $("#editCol").hide();
        var $tdsData = module.defaultTableData.tdsData;
        var currentItem = ""; //当前点的列
        for (var i = 0; i < $tdsData.length; i++) {
            if ($tdsData[i].id == id) {
                currentItem = $tdsData[i];
            }
        }
        var $newDataArry = []; //新建一个新数组
        for (var i = 0; i < $tdsData.length; i++) {
            var temp = $tdsData[i];
            $newDataArry.push(temp);
        }
        //sort by order asc
        $newDataArry = $newDataArry.sort(function (a, b) {
            return a.order - b.order
        });
        //left
        var index = -1;
        for (var i = 0; i < $newDataArry.length; i++) {
            if ($newDataArry[i].textId == currentItem.textId)
                index = i;
        }
        if (index == 0 && !isRight) //左边第一个无法向左移动
            return;

        if (index == $newDataArry.length - 1 && isRight) //右边最后一个无法向右移动
            return;
        var switcher = $newDataArry[isRight ? index + 1 : index - 1];
        var orderIndex = switcher.sort;
        switcher.sort = currentItem.sort;
        currentItem.sort = orderIndex;
        var leftIndex = -1;
        var rightIndex = -1;
        for (var i = 0; i < $tdsData.length; i++) {
            var temp = $tdsData[i];
            if (temp.textId == switcher.textId)
                leftIndex = i;
            else if (temp.textId == currentItem.textId)
                rightIndex = i;
            if (leftIndex != -1 && rightIndex != -1)
                break;
        }
        $tdsData[leftIndex] = currentItem;
        $tdsData[rightIndex] = switcher;
        module.initTable(module.defaultTableData)
        console.log(module.defaultTableData.tdsData)
    }


    //添加列
    module.addTableCol = function () {
        console.log(module.defaultTableData)
        var $defaultTableData = module.defaultTableData.tdsData;
        var obj = {
            id: new Date().getTime(),
            title: '',
            textId: 'new' + new Date().getTime(),
            tdWidth: 150,
            height: 50,
            fontFamily: '宋体',
            fontSize: -1,
            color: '',
            fontWeight: 'normal', //默认为空  normal  bold
            textAlign: 'left', //默认为空
            sort: $defaultTableData.length,
            tdContent: []
        }

        $defaultTableData.push(obj);
        module.initTable(module.defaultTableData)
        console.log($defaultTableData)
    }

    //编辑表格属性------------------------------------------------------------------------------------------------------
    $("#content_left_up select").each(function (index, item) {
        $(item).on('change', function () {
            console.log($(item).attr('name'))
            var $name = $(item).attr('name');
            var val = $(item).val();
            if ($name == "fontSize") {
                $("#purchasesSet_table_content #tabelContent").css({
                    fontSize: val + 'px'
                });
            } else if ($name == "lineHeight") {
                $("#purchasesSet_table_content #tabelContent").css({
                    lineHeight: val + '%'
                });
            } else if ($name == "height") {
                $("#purchasesSet_table_content #tabelContent").css({
                    height: val + 'px'
                });
            } else if ($name == "fontFamily") {
                $("#purchasesSet_table_content #tabelContent").css({
                    fontFamily: val
                });
            } else if ($name == "imageSize") {
                var imgWidth = val.split("x")[0];
                $("#purchasesSet_table_content #tabelContent .morelatitude img").css({
                    width: imgWidth + "px"
                });
            } else if ($name == "tableColor") {
                $("#purchasesSet_table_content thead tr th").css({
                    borderColor: val
                });
                $("#purchasesSet_table_content tbody tr td").css({
                    borderColor: val
                });
                $("#purchasesSet_table_content").css({
                    borderLeftColor: val
                });
            }
            module.saveChangeTableVal($name, val);
        })
    })

    $("#content_left_up input[name=height]").on("change", function () {
        var val = $(this).val();
        var $name = $(this).attr("name");
        var re = /^\+?[1-9]\d*$/;;
        if (re.test(val) === false) {
            layer.msg("输入大于0正整数");
            $(this).val("18");
            return;
        }
        $("#purchasesSet_table_content #tabelContent").css({
            height: val
        });
        module.saveChangeTableVal($name, val);
    })


    //保存table数据
    module.saveChangeTableVal = function (fontType, val) {
        var defaultTableData = module.defaultTableData;
        defaultTableData[fontType] = val;
    }

    //编辑表格列的属性
    changeTableTdType("#editCol input[name=title]", 'title');
    changeTableTdType("#editCol input[name=tdWidth]", 'tdWidth');
    changeTableTdType("#editCol #editColTextAlign", 'textAlign');
    changeTableTdType("#editCol #editColFontFamily", 'fontFamily');
    changeTableTdType("#editCol #editColFontSize", 'fontSize');
    changeTableTdType("#editCol #editColFontWeight", 'fontWeight');
    changeTableTdType("#editCol #editColFontColor", 'color');

    function changeTableTdType(ele, fontType) {
        $(ele).on('change', function () {
            var val = $(ele).val();
            if (fontType == "title") {
                $("#purchasesSet_table_content #purchasesSet_table_thead>tr>th").each(function (index, item) {
                    if ($(item).hasClass("td-active")) {
                        $(item).html(val)
                    }
                })
            } else if (fontType == "tdWidth") {
                var re = /^[1-9][0-9][0-9]?$/;
                if (re.test(val) === false) {

                    layer.msg("输入1-1000正整数");
                    return;
                }
                var $val = val + 'px';
                changeStyle('width', $val)

            } else if (fontType == "fontFamily") {
                changeStyle(fontType, val);
            }
            else {
                var $val = fontType == 'fontSize' ? val == -1 ? "" : val + 'px' : val;
                changeStyle(fontType, $val);
            }

            function changeStyle(type, val) {
                $("#purchasesSet_table_content #tabelContent>td").each(function (index, item) {
                    if ($(item).hasClass("td-active")) {
                        $(item).children().css({
                            [type]: val
                        })
                    }
                })
            }
            //保存tdsData数据			
            saveChangeVal(fontType, val)

            function saveChangeVal(fontType, val) {
                var $tdsData = module.defaultTableData.tdsData;
                for (var i = 0; i < $tdsData.length; i++) {
                    if ($tdsData[i].id == activeId) {
                        $tdsData[i][fontType] = val;
                        console.log($tdsData)
                    }
                }
            }


        })
    }


    $("#tableContentSelect").on("change", function () {

        var textId = $(this).val();
        var type = $(this).find("option:selected").attr("data-type");
        console.log(type)
        var changeObj = getObj(purchasesCol, textId);

        function getObj(purchasesColData, textId) {
            for (var i = 0; i < purchasesColData.length; i++) {
                if (purchasesColData[i].textId == textId && purchasesColData[i].type == type) {
                    return purchasesColData[i];
                }
            }
        }
        var $defaultTableData = module.defaultTableData.tdsData;
        for (var i = 0; i < $defaultTableData.length; i++) {
            if ($defaultTableData[i].id == activeId) {
                if (textId != 'br' && textId != 'CustomText') {
                    var isExist = false; //判断是否已存在;
                    for (var j = 0; j < $defaultTableData[i].tdContent.length; j++) {
                        var $textId = $defaultTableData[i].tdContent[j].textId;
                        if ($defaultTableData[i].tdContent[j].textId == textId) {
                            isExist = true;
                            layer.msg("你添加已存在")
                            return;
                        }
                    }
                    if (!isExist) {
                        var isType0 = false; //判断是否 type: 0
                        var isType1 = false; //判断是否 有type:1
                        for (var k = 0; k < $defaultTableData[i].tdContent.length; k++) {
                            if ($defaultTableData[i].tdContent[k].type == 0) {
                                isType0 = true;
                                break;
                            }
                            if ($defaultTableData[i].tdContent[k].type == 1) {
                                isType1 = true;
                                break;
                            }
                        }

                        if (changeObj.type == 0) {

                            if (isType1) {
                                layer.alert('您设置的数据项中存在带*和不带*的数据，将只显示带*的数据项。', { icon: 7,skin: 'wu-dailog' });
                                $defaultTableData[i].tdContent.push(changeObj);
                            } else {
                                $defaultTableData[i].tdContent.push(changeObj);
                            }
                        } else {
                            if (isType0) {

                                layer.alert('您设置的数据项中存在带*和不带*的数据，将只显示带*的数据项。', { icon: 7,skin: 'wu-dailog' });

                                $defaultTableData[i].tdContent.push(changeObj);
                            } else {
                                $defaultTableData[i].tdContent.push(changeObj);
                            }
                        }

                    } else {
                        $defaultTableData[i].tdContent.push(changeObj)
                    }

                } else {
                    var obj = {};
                    obj.id = new Date().getTime();
                    obj.type = 2;
                    if (textId == 'br') {
                        obj.textId = "br";
                        obj.title = "【换行】";
                        $defaultTableData[i].tdContent.push(obj)
                    } else if (textId == 'CustomText') {

                        var customLayer = layer.open({
                            type: 1,
                            skin: 'wu-dailog',
                            title: "自定内容", //不显示标题
                            content: '<div class="customAialog wu-inputWrap wu-form-mid wu-f14 wu-c09"><div>请输入要显示的自定义文字：</div><input class="wu-input" id="customInput" type="text"><span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span></div>',
                            area: ['500', '300'], //宽高
                            btn: ['保存', '取消'],
                            yes: function () {
                                obj.textId = "CustomText";
                                obj.title = $("#customInput").val();
                                for (var i = 0; i < $defaultTableData.length; i++) {
                                    if ($defaultTableData[i].id == activeId) {
                                        $defaultTableData[i].tdContent.push(obj)
                                    }
                                }

                                $("#customInput").val("");
                                module.changeTableContent();
                                layer.close(customLayer);

                            },
                            cancel: function () {
                                $("#customInput").val("");
                            }
                        });

                    }
                }
            }

        }

        module.changeTableContent();

    })

    module.changeTableContent = function () {

        var $defaultTableDataTdsData = module.defaultTableData;

        module.initTable($defaultTableDataTdsData);
        module.activeColor($defaultTableDataTdsData.tdsData, activeId);

        tdActive("#purchasesSet_table_content #purchasesSet_table_thead>tr>th", activeId);
        tdActive("#purchasesSet_table_content #purchasesSet_table_tbody>tr>td", activeId);

        function tdActive(ele, tdId) {
            $(ele).each(function (index, item) {
                if ($(item).attr("data-id") == tdId) {
                    $(item).addClass("td-active")
                }
            })

        }
    }

    module.purchasesSetDialogClose = function () {
        $(".editTable-module>dd").removeClass("active");
        $(".purchasesSetDialog").fadeOut(100);
        module.initTable(module.defaultTableData);
    }


    //保存模板
    module.saveTableModule = function () {

        var index = -1;
        $("#editTable_module dd").each(function (index, item) {

            if ($(item).hasClass('active')) {
                console.log($(item).attr("data-index"))
                index = $(item).attr("data-index");
                var $tableModule = JSON.parse(JSON.stringify(module.tableModule));
                module.defaultTableData = $tableModule[index];

                module.initTable(module.defaultTableData);

                $(".editTable-module>dd").removeClass("active");
                $(".purchasesSetDialog").fadeOut(100);

            }

        })
    }

    //提交数据
    module.saveSubmit = function (callback) {
        //console.log(module.defaultTableData)
        var selectDates = totalFeldsSelect.selectDates();//页面改变选中统计
        var isShowTotalFelds = { TotalItemCount: false, TotalPurchasePrice: false, TotalAmount: false, TotalProfit: false };
        for (var i = 0; i < selectDates.length; i++) {
            for (var k in isShowTotalFelds) {
                if (k == selectDates[i]) {
                    isShowTotalFelds[k] = true;
                    break
                }
            }
        }
        module.defaultTableData.isShowTotalFelds = isShowTotalFelds;

        console.log(module.defaultTableData)

        if (!module.defaultTableData)
            return;

        commonModule.SaveCommonSetting("/ErpWeb/Purchase/TemplateSetting", JSON.stringify(module.defaultTableData), function (rsp) {
            if (rsp.Success) {
                layer.msg("保存成功");
                if (typeof callback == "function")
                    callback();
            }
        });
    }

    //新手教程
    module.purchasesSetHelps = function () {
        var noviceIntroObj = {
            backgroundColor: "#000", 	//遮罩背景色
            opacity: 0.8, 				//遮罩透明度
            isStartButton: false, 		//是否开启 跳过使用步骤 显示直接去使用按钮
            startButtonTitle: '', 		//直接去使用按钮 名称   不设置默认为跳过:开始使用
            isStartButton02: true, 		//是否开启 跳过使用步骤 显示直接去使用按钮
            callBack: function () {     //最后关闭触发回调函数
                $(".content-left-down").removeClass("oviceIntrog-nosetBackgroundColor");
                $("#editTable_module").removeClass("oviceIntrog-nosetBackgroundColor");
                $("#closeNoviceIntroAialog02").css({ top: "unset", left: "unset" })


            },
            steps: [
                {                   //步骤
                    elem: "#editTable_module", 	//定位到某个元素上			
                    isStopOperate: false,	// 是否可操作指定区 默认为true
                    control: { 			//控制区
                        top: -140,    		//整个控制区的位置  默认可以不设置
                        left: -280,    		//整个控制区的位置  默认可以不设置
                        align: "left", 	//控制区文字 对齐
                        arrowsIcon: false,//是否出现箭头图标
                        arrowsIconDirection: "right", //箭头指向    不设置 箭头指向左
                        imgSrc: "../Content/Images/newNoviceIntro/purchases/setPurchasesSet-1.png", //说明步骤  图片
                        stepCallBack: function () {//步骤回调函数
                            $(".purchasesSetDialog").css({ zIndex: 1000000 });
                            $(".purchasesSetDialog>.purchasesSetDialog-wrap").css({ backgroundColor: "#333" })
                            $("#editTable_module>dd:eq(0)").click();
                            $(".content-right").css({ zIndex: 10000000 });
                            $("#closeNoviceIntroAialog02").css({ top: "unset", left: "unset" })

                        }
                    }
                },
                {                   //步骤
                    elem: ".content-left-down", 	//定位到某个元素上				
                    isStopOperate: false,	// 是否可操作指定区 默认为true
                    control: { 			//控制区
                        top: 200,    		//整个控制区的位置  默认可以不设置
                        left: -330,    		//整个控制区的位置  默认可以不设置
                        arrowsTop: 0, 	//箭头指向位置  默认可以不设置
                        arrowsLeft: 252, 	//箭头指向位置  默认可以不设置
                        align: "left", 	//控制区 对齐
                        arrowsIcon: true,
                        arrowsIconDirection: "right",
                        imgSrc: "../Content/Images/newNoviceIntro/purchases/setPurchasesSet-4.png", //说明步骤  图片
                        stepCallBack: function () {//步骤回调函数 
                            PurchasesSetModule.purchasesSetDialogClose()
                            $("#purchasesSet_table_thead>tr>th:eq(0)").click();
                            $(".purchasesSetDialog").css({ zIndex: 999 });
                            $(".purchasesSetDialog>.purchasesSetDialog-wrap").css({ backgroundColor: "#000" });
                            $(".content-right").css({ zIndex: 99 });
                            $("#closeNoviceIntroAialog02").css({ top: 200, left: 0 })
                        }
                    }
                }
                ,
                {                   //步骤
                    elem: ".content-left-down", 	//定位到某个元素上			
                    isStopOperate: false,	// 是否可操作指定区 默认为true
                    control: { 			//控制区
                        top: 0,    		//整个控制区的位置  默认可以不设置
                        left: 1000,    		//整个控制区的位置  默认可以不设置
                        arrowsTop: 0, 	//箭头指向位置  默认可以不设置
                        arrowsLeft: 0, 	//箭头指向位置  默认可以不设置
                        align: "left", 	//控制区 对齐
                        arrowsIcon: true,
                        arrowsIconDirection: "left",
                        imgSrc: "../Content/Images/newNoviceIntro/purchases/setPurchasesSet-3.png", //说明步骤  图片
                        stepCallBack: function () {
                            $("#closeNoviceIntroAialog02").css({ top: "unset", left: "unset" })
                        }
                    }
                }
                ,
                {                   //步骤
                    elem: ".content-left-down", 	//定位到某个元素上			
                    isStopOperate: false,	// 是否可操作指定区 默认为true
                    control: { 			//控制区
                        top: 450,    		//整个控制区的位置  默认可以不设置
                        left: 450,    		//整个控制区的位置  默认可以不设置
                        align: "left", 	//控制区 对齐
                        arrowsIcon: false,
                        imgSrc: "../Content/Images/newNoviceIntro/purchases/setPurchasesSet-5.png", //说明步骤  图片
                        buttonTitle: "开始使用", //下一步 按钮标题
                    }
                }

            ]
        };

        var newFun = new noviceIntro();
        newFun.initData(noviceIntroObj);

    }

    return module;
}(PurchasesSetModule || {}, jQuery, layer));
