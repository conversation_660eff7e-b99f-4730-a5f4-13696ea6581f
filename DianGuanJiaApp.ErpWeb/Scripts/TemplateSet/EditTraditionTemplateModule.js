/// <reference path="../CommonModule.js" />
/// <reference path="../layer/layer.js" />
/// <reference path="EditTemplateModule.js" />
/// <reference path="../jquery-1.10.2.min.js" />

var editTraditionTemplateModule = (function (module, editTemplate, common, $, layer) {

    var _imgurl = "";
    var _template = null;
    module.LoadTempLate = function (templateId) {
        if (templateId == 0) {
            return;
        }

        var arrChk = document.getElementsByName("chk");
        for (var i = 0; i < arrChk.length; i++) {
            var chk = arrChk[i];
            if (chk.checked == true) {
                chk.checked = false;
                document.getElementById("div_" + chk.id).style.display = "none";
            }
        }

        common.Ajax({
            url: '/TemplateSet/LoadTemplate',
            data: { templateId: templateId },
            loading: true,
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                _template = rsp.Data;
                var itemList = _template.TemplateControls;
                $("#templateName").val(_template.TemplateName);
                //$("#expressCompany").attr("value", _template.DeliveryExpressCompanyId);
                $("#template_width").val(_template.PicWidth);
                $("#template_height").val(_template.PicHeight);
                $("#template_offsetx").val(_template.ContentWidth);
                $("#template_offsety").val(_template.ContentHeight);
                _imgurl = _template.ExpressPic.replace("\"", "");
                $('#printPic').attr("src", "http://kdimg.dgjapp.com/" + _imgurl);

                var w_h = common.MmToPx(_template.PicWidth, _template.PicHeight);
                var t_l = common.MmToPx(_template.ContentWidth, _template.ContentHeight);


                $("#printPic").css("width", w_h.width);
                $("#printPic").css("height", w_h.height);

                $("#wrape-printbox").css("width", w_h.width);
                $("#wrape-printbox").css("height", w_h.height);
                $("#pb").css("left", t_l.width);
                $("#pb").css("top", t_l.height);


                for (var i = 0; i < itemList.length; i++) {
                    var item = itemList[i];
                    var id = item.ControlId;
                    var txt = item.ControlName;
                    var w = item.ControlWidth;
                    var h = item.ControlHeight;
                    var l = item.XOffset;
                    var t = item.YOffset;
                    var size = item.FontSize;
                    var family = item.FontFamily;
                    var weight = item.FontWeight;
                    var type = item.ControlType;

                    editTemplateModule.SetItem(id, w, h, l, t, family, size, weight, txt, type);
                    if (i == 0) {
                        editTemplateModule.SetId(id);
                    }
                }
            }
        });
    }

    module.Save = function () {
        if ($('#templateName').val() == "") {
            layer.alert("请填写模板名称!", {skin:"wu-dailog"});
            return;
        }
        //if ($('#expressCompany').val() == 0) {
        //    JShow("请选择快递公司!");
        //    return;
        //}
        if ($('#template_width').val() == "") {
            layer.alert("请填写快递单宽度!", { skin: "wu-dailog" });
            return;
        }
        if ($('#template_height').val() == 0) {
            layer.alert("请填写快递单高度!", { skin: "wu-dailog" });
            return;
        }
        var objImg = editTemplate.ImageWidthHeight();
        //var reqpara = {
        //    Id = printTemplateModel.Id,
        _template.TemplateName = $('#templateName').val().trim();
        //TemplateType = printTemplateModel.TemplateType,
        //RulePic = printTemplateModel.RulePic,
        _template.ExpressPic = _imgurl;
        _template.PicWidth = objImg.width;
        _template.PicHeight = objImg.height;
        _template.ContentWidth = objImg.offsetx;
        _template.ContentHeight = objImg.offsety;
        //    ExpressCompanyId = printTemplateModel.ExpressCompanyId,
        //    ShopId = printTemplateModel.ShopId,
        //    FromId = printTemplateModel.FromId,
        //    AddDate = printTemplateModel.AddDate,
        //    UpdateDate = printTemplateModel.UpdateDate,
        //    IsDeleted = printTemplateModel.IsDeleted,
        //    CpCode = cp_code,
        //    Uplogo = uplogo,
        //    Downlogo = downlogo,
        //    TemplateControls = new List<PrintControl>(),
        //    CaiNiaoAuthInfo = templateRelationAuthInfo,
        //    LogisticsServiceList = templateLogisticsServiceList,
        //    UserSiteAcountInfo = userSiteAccountInfo,
        //};

        //提交
        var list = $("input[name='chk']:checked");
        if (list.length < 3) {
            layer.alert("至少选择 3 个打印项！", {skin:"wu-dailog"});
            return;
        }
        $("#btnSubmit").attr("disabled", "disabled");

        _template.TemplateControls = [];
        for (var i = 0; i < list.length; i++) {
            var item = {};
            var chk = list.eq(i);
            item.TemplateId = _template.Id;
            item.ControlId = chk.attr("id");
            item.ControlName = $("#font_" + chk.attr("id")).text();
            item.ControlType = chk.attr("stype");
            var values = chk.val().split(',');
            item.XOffset = values[0];
            item.YOffset = values[1];
            item.ControlWidth = values[2];
            item.ControlHeight = values[3];
            var proms = $("#input_" + chk.attr("id")).val().split(',');
            item.FontFamily = proms[1];
            item.FontSize = proms[2];
            item.FontWeight = proms[3];
            _template.TemplateControls.push(item);
        }

        common.Ajax({
            url: '/TemplateSet/SaveTemplate',
            type: 'POST',
            data: { editModel: _template },
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    $("#btnSubmit").removeAttr("disabled");
                    return;
                }
                layer.msg('保存成功');
                $("#btnSubmit").removeAttr("disabled");
            }
        });

    }

    return module;
}(editTraditionTemplateModule || {}, editTemplateModule, commonModule, jQuery, layer));