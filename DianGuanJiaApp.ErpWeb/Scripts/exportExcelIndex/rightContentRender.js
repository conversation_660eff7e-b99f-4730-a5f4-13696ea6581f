/// <reference path="../jquery-1.10.2.min.js" />
/// <reference path="../layer/layer.js" />
/// <reference path="../CommonModule.js" />

var rightContentRenderModule = (function (module, $, common, ly) {
    var accountStatus = "MySupplier"; // 账单下载页  顶部导航状态
    var selectNav = '';
    var _pageIndex = 1, _pageSize = 10;
    var taskFlag = "";
    var taskList = [];
    var mainTaskCode = "";
    var billDetail = undefined;


    var navs = [
        { id: 'AllOrder', type: '9', title: '所有订单', isSelect: false, isHaCloudPlatform: true },
        { id: 'WaitOrder', type: '10', title: '打单发货', isSelect: false, isHaCloudPlatform: true },
        { id: 'OfflineOrder', type: '12', title: '线下单', isSelect: false, isHaCloudPlatform: false },
        //{ id: 'Purchases', type: '13', title: '备货单', isSelect: false, isHaCloudPlatform: true },
        { id: 'WaybillCodeList', type: '11', title: '底单查询', isSelect: false, isHaCloudPlatform: true },
        { id: 'ErpWaybill', type: '16', title: '厂家底单', isSelect: false, isHaCloudPlatform: true },
        { id: 'PrintHistory', type: '22', title: '打印记录', isSelect: false, isHaCloudPlatform: true },
        { id: 'SendOrder', type: '18', title: '已发货明细', isSelect: false, isHaCloudPlatform: true },
        { id: 'AfterSale', type: '19', title: '售后管理', isSelect: false, isHaCloudPlatform: true },
        { id: 'StockDetail', type: '20', title: '实时库存', isSelect: false, isHaCloudPlatform: false },
        { id: 'DownBill', type: '34', title: '账单下载', isSelect: false, isHaCloudPlatform: false},

    ]


    //账单下载所在顺序号，从0开始
    var downBillSeqNumber = 9;
    var isShowDownBill = true;

    if (commonModule.IsWhiteList != '1') {
        navs = [
            { id: 'AllOrder', type: '9', title: '所有订单', isSelect: false, isHaCloudPlatform: true },
            { id: 'WaitOrder', type: '10', title: '打单发货', isSelect: false, isHaCloudPlatform: true },
            { id: 'OfflineOrder', type: '12', title: '线下单', isSelect: false, isHaCloudPlatform: false },
            //{ id: 'Purchases', type: '13', title: '备货单', isSelect: false, isHaCloudPlatform: true },
            { id: 'WaybillCodeList', type: '11', title: '底单查询', isSelect: false, isHaCloudPlatform: true },
            { id: 'ErpWaybill', type: '16', title: '厂家底单', isSelect: false, isHaCloudPlatform: true },
            { id: 'PrintHistory', type: '22', title: '打印记录', isSelect: false, isHaCloudPlatform: true },
            { id: 'SendOrder', type: '18', title: '已发货明细', isSelect: false, isHaCloudPlatform: true },
            { id: 'AfterSale', type: '19', title: '售后管理', isSelect: false, isHaCloudPlatform: true },
            { id: 'StockDetail', type: '20', title: '实时库存', isSelect: false, isHaCloudPlatform: false },
        ];
        isShowDownBill = false;
    }
    
    //initNavs();
    function initNavs() {
        var html = '';
        navs.forEach(function (item, i) {
            var className = item.isSelect ? 'active' : '';
            html += '<dd id="nav_' + item.id + '" class="' + className + '" data-type="' + item.type + '" onclick=\'rightContentRenderModule.chooseNav("' + item.id + '")\'>' + item.title + '</dd>';
        });
        if (isShowDownBill && navs[downBillSeqNumber].isSelect == true) {  //初次进入账单下载默认查询我的厂家任务数据
            taskFlag = "MySupplier";
        }
        else {//否则给全局变量一个空值，以防其他模块受影响
            taskFlag = "";
        }
        $("#exportExcelIndex_navs dd").remove();
        $("#exportExcelIndex_navs").append(html);

    }

    $(function () {

        var exportTaskType = commonModule.type;//commonModule.getStorage("exportTaskType");
        var tarDownBill = commonModule.getStorage("tarDownBill");
        var navs = common.getQueryVariable("nav");
        var accountStatus = common.getQueryVariable("accountStatus");

        if (navs == "DownBill"){
            $("#downBillNavs").show();
            taskFlag = "MySupplier";
        }

        if (accountStatus) {
            taskFlag = accountStatus;
            $("#changeOnload_nva_ul>li").removeClass("layui-this");
            $("#changeOnload_nva_ul>li#" + accountStatus).addClass("layui-this");
        }


        module.LoadList(false, '', navs);
        wuFormModule.initblurInput('#search_task_id');
        wuFormModule.initLayuiSelect('active-select-filter');

    })

    module.chooseNav = function (id) {
        commonModule.clearStorage("tarDownBill");
        isChangeNav = true;
        var isHaCloudPlatform = null;
        var selectType = "";
        navs.forEach(function (item) {
            item.isSelect = false;
            if (item.id == id) {
                item.isSelect = true;
                isHaCloudPlatform = item.isHaCloudPlatform;
                selectType = item.type;
            }
        })
        selectNav = id;
        initNavs();
        $("#noNavContent").hide();
        $("#changeNavContent").show();



        if (isHaCloudPlatform) {
            $("#haCloudPlatform").show();
            $("#no_Iframe").hide();
            $("#have_iframewrap").show();

            //document.getElementById("cloudFrame").contentWindow.rightContentRenderModule.LoadList();

        } else {
            $("#haCloudPlatform").hide();
            $("#no_Iframe").show();
            $("#have_iframewrap").hide();

            commonModule.setStorage('exportTaskType', selectType);
            rightContentRenderModule.initNewCalenderTime();
            initPlatformAndArea();
            //module.LoadList(false);

        }


        if (selectNav == 'DownBill') {
            $("#downBillNavs").show();
        } else {
            $("#downBillNavs").hide();
        }




    }

    module.exportBillExcel = function () {
        if (taskList.length == 0) {
            layer.msg("无任务信息，无法导出");
            return;
        }
        exportOrderModule.ExportExcel({
            url: '/ExportTask/ExportExcel',
            type: 'POST',
            data: { mainTaskCode:mainTaskCode },
            loadMsg: "正在导出Excel，请稍等……",
            success: function () {
                layer.msg("导出成功");
            }
        });
    }

    module.RemoveExportTask = function (taskId) {

        // var isAddOk=layer.confirm('确定要删除该导出任务吗？', { icon: 3, title:'确认', skin: 'wu-8radius', btn: ['确定删除', '取消'] },
        //     function () {
        //         common.Ajax({
        //             url: '/ExportTask/RemoveExportTask',
        //             loading: false,
        //             data: { id: taskId },
        //             success: function (rsp) {
        //                 if (rsp.Success == false) {
        //                     layer.msg(rsp.Message, { icon: 2 });
        //                     return;
        //                 }
        //                 layer.msg("导出任务删除成功", { time: 4000 });
        //                 module.LoadList(false);
        //             }
        //         });

        //     }, function () {
        //         layer.close(isAddOk);
        //     }
        // );
        var isAddOk=layer.confirm('<i class="iconfont icon-a-error-circle-filled1x wu-color-b wu-mR8"></i><span class="wu-c09">确定要删除该导出任务吗？</span>', { icon: 3, title:'确认', skin: 'wu-8radiusI', area: ['320px', '112px'], btn: ['确定删除', '取消'] ,
            success: function(layero) {
                layero.find('.layui-layer-title').css({'height':'28px', 'lineHeight': '28px', 'border-bottom': 'none', 'color':'#fff', 'backgroundColor': '#fff', 'border-radius': '8px'});
                layero.find('.layui-layer-ico3').css({'display': 'none'});
                layero.find('.layui-layer-btn').css({'borderRadius': '8px'});
                layero.find('.layui-layer-content').css({ 'height': '36px','lineHeight': '36px', 'padding': '0px 16px', 'overflow-y': 'hidden'});
                layero.find('.layui-layer-btn0').addClass('wu-btn wu-btn-mid wu-primary wu-three').css({'border-radius': '6px', 'height': '32px', 'line-height': 'unset', 'padding': '6px 12px'});
                layero.find('.layui-layer-btn1').addClass('wu-btn wu-btn-mid wu-primary').css({'border-radius': '6px', 'height': '32px', 'line-height': 'unset', 'padding': '6px 12px'});
            }},
            function () {
                common.Ajax({
                    url: '/ExportTask/RemoveExportTask',
                    loading: false,
                    data: { id: taskId },
                    success: function (rsp) {
                        if (rsp.Success == false) {
                            layer.msg(rsp.Message, { icon: 2 });
                            return;
                        }
                        layer.msg("导出任务删除成功", { time: 4000 });
                        module.LoadList(false);
                    }
                });

            }, function () {
                layer.close(isAddOk);
            }
        );
    }

    //更新任务名称
    module.updateExportTaskNameShow = function (taskId,isClose) {
        $("#taskNameExt" + taskId).toggleClass("hide");
        $("#taskNameInputWrap" + taskId).toggleClass("hide");
        $("#icon_bianji" + taskId).toggleClass("hide");
        if (isClose) {
            $("#exportExcelNameInput_" + taskId).val($("#taskNameExt" + taskId).text());
        }
    }

    //任务名称
    module.updateExportTaskName = function (taskId) {
        var taskName = $("#exportExcelNameInput_" + taskId).val().trim();
        if (taskName == "") {
            layer.msg("请输入任务名称");
            return;
        }
        common.Ajax({
            url: '/ExportTask/UpdateExportTaskName',
            loading: false,
            data: { id: taskId, taskName: taskName },
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                layer.msg("任务名称保存成功", { time: 4000 });
                module.LoadList(false);
                module.updateExportTaskNameShow(taskId);
            }
        });

    }

    //更新任务备注
    module.updateExportTaskRemarkShow = function (taskId,isClose) {
        $("#taskRemark" + taskId).toggleClass("hide");
        $("#taskRemark_icon" + taskId).toggleClass("hide");
        $("#taskRemarkInputWrap" + taskId).toggleClass("hide");
        if (isClose) {
            $("#taskRemarkNameInput" + taskId).val($("#taskRemark" + taskId).text());
        }

    }

    //主任务备注
    module.updateExportTaskRemark = function (taskId) {
        var taskRemark = $("#taskRemarkNameInput" + taskId).val().trim();
        if (taskRemark == "") {
            layer.msg("请输入备注");
            return;
        }
        common.Ajax({
            url: '/ExportTask/UpdateExportTaskRemark',
            loading: false,
            data: { id: taskId, taskRemark: taskRemark },
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }

                layer.msg("任务备注保存成功", { time: 4000 });
                module.LoadList(false);
            }
        });
    }

    //子任务备注
    module.updateExportChildTaskRemark = function (taskId) {
        var Remark = $("#childTaskRemarkNameInput" + taskId).val().trim();
        if (Remark == "") {
            layer.msg("请输入备注");
            return;
        }
        common.Ajax({
            url: '/ExportTask/UpdateExportChildTaskRemark',
            loading: false,
            data: { id: taskId, remark: Remark },
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                layer.msg("账单备注保存成功", { time: 4000 });
                module.LoadList(false);
            }
        });

    }

    fromTarUrl();
    function fromTarUrl() {
        var tarUrlName = common.getQueryVariable('tarUrlName');
        var tarCloudplatform = common.getQueryVariable('tarCloudplatform');
        var tarArea = common.getQueryVariable('tarArea');
        if (tarUrlName) {
            module.chooseNav(tarUrlName);
        }
        if (tarUrlName && tarCloudplatform) {
            gotoCloudPlatform(tarCloudplatform);
        }
        if (tarUrlName && tarArea) {
            gotoDbArea(tarArea);

        }

    }

    module.downloadChange = function () {
        $(this).text('已下载').removeClass("dColor").addClass("fColor");

    }

    module.initNewCalenderTime = function () {
        //初始化时间控件
        var obj = {
            maxDays: 100000,
            maxDaysTitle: '导出任务查询范围仅支持',
            days: 7, //天数
            width: 180,
            height: 35,
            startDate: null, //startDate可以不用填 不用填调用客户端时间  格式：yyyy-MM-dd
            endDate: null //endDate可以不用填 不用填调用客户端时间      格式：yyyy-MM-dd
        };
        common.InitNewCalenderTime("#inputSelectTime", obj);

    }


    $$.navActive("#changeOnload_nva_ul", function (index, item) {
        accountStatus = $(item).attr("data-id");
        //if (accountStatus != null && accountStatus != undefined) {
        //    taskFlag = accountStatus;
        //}
        if (accountStatus == 'BatchOutAcount') {

            $(".checkAccountsBtn").css({ display: 'inline-block' });

        } else {
            $(".checkAccountsBtn").css({ display: 'inline-block' });

        }

    }, 'layui-this');


    module.LoadList = function (isPaging, flag, navType) {

        var tarType = common.getQueryVariable("tarType");//判断是否从ifarm过来的

        var type = "";
        var platformType = "";
        var dbname = "";

        type = common.getQueryVariable("type");
        platformType = common.getQueryVariable("platformType");
        dbname = common.getQueryVariable("dbname");
        //if (tarType == "ifarme") {  //ifarme
        //    type = common.getQueryVariable("type");
        //    platformType = common.getQueryVariable("platformType");
        //    dbname = common.getQueryVariable("dbname");

        //} else {

        //    type = $("#exportExcelIndex_navs dd.active").data("type");
        //    platformType = $("#cloudplatform_nva_ul li.active").data("cloud");
        //    dbname = $("#cloudplatform_Area li.layui-this").attr("id");
        //}


        var taskId = $("#iup_key").val();
        var taskStatus = $("#sel_status").val();
        var startDate = $("#inputSelectTime .QueryDateVal").attr("start-date") || "";
        var endDate = $("#inputSelectTime .QueryDateVal").attr("end-date") || "";
        taskFlag = flag == null || flag == undefined || flag == "" ? taskFlag : flag;
        if (flag == false || isPaging == false) {
            _pageIndex = 1;
            _pageSize = 10;
        }
        var requestModel = { Type: type, PlatformType: platformType, Id: taskId, Status: taskStatus, CreateTimeFrom: startDate, CreateTimeTo: endDate, DbName: dbname, PageIndex: _pageIndex, PageSize: _pageSize }

        //console.log("requestModel", requestModel)

        common.Ajax({
            url: '/ExportTask/GetExportTaskList',
            loading: true,
            data: { model: requestModel, type: taskFlag },
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                // 自测数据
                // rsp.Data = {
                //     "PageIndex": 1,
                //     "PageSize": 10,
                //     "OrderByField": null,
                //     "IsOrderDesc": false,
                //     "PageCount": 0,
                //     "Total": 8,
                //     "RealOrderTotal": 0,
                //     "PtOrderTotal": 0,
                //     "RefundTotal": 0,
                //     "OtherRefundTotal": 0,
                //     "Rows": [
                //         {
                //             "ExpiredDateFrom": "2025-02-20 21:00",
                //             "ExpiredDateTo": "2025-02-27 21:00",
                //             "FileIsValidation": true,
                //             "StatusDescription": "文件已生成",
                //             "hideDetail": true,
                //             "Id": 95658248,
                //             "IP": null,
                //             "Message": null,
                //             "ExecSql": null,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "DealTime": null,
                //             "HopeExecuteTime": "2025-02-20 20:59:00",
                //             "UploadToServerTime": "2025-02-20 21:00:01",
                //             "PlatformType": "Alibaba",
                //             "ShopId": 252019,
                //             "UserId": null,
                //             "SubFxUserId": "0",
                //             "Status": 4,
                //             "SpendTime": 0.0,
                //             "Type": 34,
                //             "PageIndex": 1,
                //             "PageSize": 1,
                //             "TotalCount": 0,
                //             "ParamJson": null,
                //             "FromModule": "分单系统-生成对账结算账单",
                //             "FilePath": "/files/20250220-21//47557948.zip",
                //             "FileName": null,
                //             "FileSize": 0.62,
                //             "RetryTimes": 0,
                //             "ExtField1": "4430d2ef0f5913d4",
                //             "ExtField2": null,
                //             "ExtField3": null,
                //             "ErrorType": null,
                //             "ExtField4": null,
                //             "ExtField5": null,
                //             "SyncLockStatus": null,
                //             "ProcessSyncServerIP": null,
                //             "QueueDealTime": null,
                //             "TaskName": "分单系统-生成对账结算账单20250220094741",
                //             "TaskRemark": null,
                //             "IsHide": false,
                //             "TaskFlag": null,
                //             "IsSubTask": false,
                //             "MainTaskCode": null,
                //             "UpdateTime": "2025-02-20 21:00:02",
                //             "TaskCode": null,
                //             "ExtField6": null,
                //             "TaskNameExt": "分单系统-生成对账结算账单20250220094741"
                //         },
                //         {
                //             "ExpiredDateFrom": "2025-02-18 17:19",
                //             "ExpiredDateTo": "2025-02-25 17:19",
                //             "FileIsValidation": true,
                //             "StatusDescription": "文件已生成",
                //             "hideDetail": true,
                //             "Id": 95390162,
                //             "IP": null,
                //             "Message": null,
                //             "ExecSql": null,
                //             "CreateTime": "2025-02-18 17:19:07",
                //             "DealTime": null,
                //             "HopeExecuteTime": null,
                //             "UploadToServerTime": "2025-02-18 17:19:30",
                //             "PlatformType": "Alibaba",
                //             "ShopId": 252019,
                //             "UserId": null,
                //             "SubFxUserId": "0",
                //             "Status": 5,
                //             "SpendTime": 0.0,
                //             "Type": 34,
                //             "PageIndex": 1,
                //             "PageSize": 1,
                //             "TotalCount": 0,
                //             "ParamJson": null,
                //             "FromModule": "分单系统-生成对账结算账单",
                //             "FilePath": "/files/20250218-17//-762408049.zip",
                //             "FileName": null,
                //             "FileSize": 0.08,
                //             "RetryTimes": 0,
                //             "ExtField1": "ce8651ef7a167709",
                //             "ExtField2": null,
                //             "ExtField3": null,
                //             "ErrorType": null,
                //             "ExtField4": null,
                //             "ExtField5": null,
                //             "SyncLockStatus": null,
                //             "ProcessSyncServerIP": null,
                //             "QueueDealTime": null,
                //             "TaskName": null,
                //             "TaskRemark": null,
                //             "IsHide": false,
                //             "TaskFlag": null,
                //             "IsSubTask": false,
                //             "MainTaskCode": null,
                //             "UpdateTime": "2025-02-18 17:20:06",
                //             "TaskCode": null,
                //             "ExtField6": null,
                //             "TaskNameExt": "分单系统-生成对账结算账单20250218171907"
                //         },
                //         {
                //             "ExpiredDateFrom": "2025-02-18 15:46",
                //             "ExpiredDateTo": "2025-02-25 15:46",
                //             "FileIsValidation": true,
                //             "StatusDescription": "文件已生成",
                //             "hideDetail": true,
                //             "Id": 95342819,
                //             "IP": null,
                //             "Message": null,
                //             "ExecSql": null,
                //             "CreateTime": "2025-02-18 15:19:47",
                //             "DealTime": null,
                //             "HopeExecuteTime": null,
                //             "UploadToServerTime": "2025-02-18 15:46:25",
                //             "PlatformType": "Alibaba",
                //             "ShopId": 252019,
                //             "UserId": null,
                //             "SubFxUserId": "0",
                //             "Status": 5,
                //             "SpendTime": 0.0,
                //             "Type": 34,
                //             "PageIndex": 1,
                //             "PageSize": 1,
                //             "TotalCount": 0,
                //             "ParamJson": null,
                //             "FromModule": "分单系统-生成对账结算账单",
                //             "FilePath": "/files/20250218-15//1139985465.zip",
                //             "FileName": null,
                //             "FileSize": 0.34,
                //             "RetryTimes": 0,
                //             "ExtField1": "070c655cc5111138",
                //             "ExtField2": null,
                //             "ExtField3": null,
                //             "ErrorType": null,
                //             "ExtField4": null,
                //             "ExtField5": null,
                //             "SyncLockStatus": null,
                //             "ProcessSyncServerIP": null,
                //             "QueueDealTime": null,
                //             "TaskName": null,
                //             "TaskRemark": "0218-灰度3-小胡出账-New",
                //             "IsHide": false,
                //             "TaskFlag": null,
                //             "IsSubTask": false,
                //             "MainTaskCode": null,
                //             "UpdateTime": "2025-02-18 16:00:30",
                //             "TaskCode": null,
                //             "ExtField6": null,
                //             "TaskNameExt": "分单系统-生成对账结算账单20250218151947"
                //         },
                //         {
                //             "ExpiredDateFrom": "2025-02-18 10:27",
                //             "ExpiredDateTo": "2025-02-25 10:27",
                //             "FileIsValidation": true,
                //             "StatusDescription": "文件已生成",
                //             "hideDetail": true,
                //             "Id": 95175604,
                //             "IP": null,
                //             "Message": null,
                //             "ExecSql": null,
                //             "CreateTime": "2025-02-17 16:24:06",
                //             "DealTime": null,
                //             "HopeExecuteTime": null,
                //             "UploadToServerTime": "2025-02-18 10:27:10",
                //             "PlatformType": "Alibaba",
                //             "ShopId": 252019,
                //             "UserId": null,
                //             "SubFxUserId": "0",
                //             "Status": 5,
                //             "SpendTime": 0.0,
                //             "Type": 34,
                //             "PageIndex": 1,
                //             "PageSize": 1,
                //             "TotalCount": 0,
                //             "ParamJson": null,
                //             "FromModule": "分单系统-生成对账结算账单",
                //             "FilePath": "/files/20250218-10//-1261440879.zip",
                //             "FileName": null,
                //             "FileSize": 0.92,
                //             "RetryTimes": 0,
                //             "ExtField1": "cc67c26ec0a4ccd0",
                //             "ExtField2": null,
                //             "ExtField3": null,
                //             "ErrorType": null,
                //             "ExtField4": null,
                //             "ExtField5": null,
                //             "SyncLockStatus": null,
                //             "ProcessSyncServerIP": null,
                //             "QueueDealTime": null,
                //             "TaskName": null,
                //             "TaskRemark": "0217-灰度3-小胡出账，跑了脚本",
                //             "IsHide": false,
                //             "TaskFlag": null,
                //             "IsSubTask": false,
                //             "MainTaskCode": null,
                //             "UpdateTime": "2025-02-18 10:35:26",
                //             "TaskCode": null,
                //             "ExtField6": null,
                //             "TaskNameExt": "分单系统-生成对账结算账单20250217162406"
                //         },
                //         {
                //             "ExpiredDateFrom": "2025-02-10 21:16",
                //             "ExpiredDateTo": "2025-02-17 21:16",
                //             "FileIsValidation": false,
                //             "StatusDescription": "文件已失效",
                //             "hideDetail": true,
                //             "Id": 93766431,
                //             "IP": null,
                //             "Message": null,
                //             "ExecSql": null,
                //             "CreateTime": "2025-02-10 10:11:34",
                //             "DealTime": null,
                //             "HopeExecuteTime": "2025-02-10 20:59:00",
                //             "UploadToServerTime": "2025-02-10 21:16:17",
                //             "PlatformType": "Alibaba",
                //             "ShopId": 252019,
                //             "UserId": null,
                //             "SubFxUserId": null,
                //             "Status": 5,
                //             "SpendTime": 0.0,
                //             "Type": 34,
                //             "PageIndex": 1,
                //             "PageSize": 1,
                //             "TotalCount": 0,
                //             "ParamJson": null,
                //             "FromModule": "分单系统-生成对账结算账单",
                //             "FilePath": "/files/20250210-21//-312456292.zip",
                //             "FileName": null,
                //             "FileSize": 1.2,
                //             "RetryTimes": 0,
                //             "ExtField1": "72ed8a26263cadb2",
                //             "ExtField2": null,
                //             "ExtField3": null,
                //             "ErrorType": null,
                //             "ExtField4": null,
                //             "ExtField5": null,
                //             "SyncLockStatus": null,
                //             "ProcessSyncServerIP": null,
                //             "QueueDealTime": null,
                //             "TaskName": null,
                //             "TaskRemark": "小胡一键出账--测试",
                //             "IsHide": false,
                //             "TaskFlag": null,
                //             "IsSubTask": false,
                //             "MainTaskCode": null,
                //             "UpdateTime": "2025-02-11 09:28:45",
                //             "TaskCode": null,
                //             "ExtField6": null,
                //             "TaskNameExt": "分单系统-生成对账结算账单20250210101134"
                //         },
                //         {
                //             "ExpiredDateFrom": "2025-02-10 21:17",
                //             "ExpiredDateTo": "2025-02-17 21:17",
                //             "FileIsValidation": false,
                //             "StatusDescription": "文件已失效",
                //             "hideDetail": true,
                //             "Id": 93765958,
                //             "IP": null,
                //             "Message": null,
                //             "ExecSql": null,
                //             "CreateTime": "2025-02-10 10:10:10",
                //             "DealTime": null,
                //             "HopeExecuteTime": "2025-02-10 20:59:00",
                //             "UploadToServerTime": "2025-02-10 21:17:00",
                //             "PlatformType": "Alibaba",
                //             "ShopId": 252019,
                //             "UserId": null,
                //             "SubFxUserId": null,
                //             "Status": 4,
                //             "SpendTime": 0.0,
                //             "Type": 34,
                //             "PageIndex": 1,
                //             "PageSize": 1,
                //             "TotalCount": 0,
                //             "ParamJson": null,
                //             "FromModule": "分单系统-生成对账结算账单",
                //             "FilePath": "/files/20250210-21//298044727.zip",
                //             "FileName": null,
                //             "FileSize": 1.19,
                //             "RetryTimes": 0,
                //             "ExtField1": "6cfdf85292c7f014",
                //             "ExtField2": null,
                //             "ExtField3": null,
                //             "ErrorType": null,
                //             "ExtField4": null,
                //             "ExtField5": null,
                //             "SyncLockStatus": null,
                //             "ProcessSyncServerIP": null,
                //             "QueueDealTime": null,
                //             "TaskName": null,
                //             "TaskRemark": "小胡一键出账--正式",
                //             "IsHide": false,
                //             "TaskFlag": null,
                //             "IsSubTask": false,
                //             "MainTaskCode": null,
                //             "UpdateTime": "2025-02-10 21:17:00",
                //             "TaskCode": null,
                //             "ExtField6": null,
                //             "TaskNameExt": "分单系统-生成对账结算账单20250210101010"
                //         },
                //         {
                //             "ExpiredDateFrom": "2025-02-07 11:56",
                //             "ExpiredDateTo": "2025-02-14 11:56",
                //             "FileIsValidation": false,
                //             "StatusDescription": "文件已失效",
                //             "hideDetail": true,
                //             "Id": 93306360,
                //             "IP": null,
                //             "Message": null,
                //             "ExecSql": null,
                //             "CreateTime": "2025-02-07 10:53:28",
                //             "DealTime": null,
                //             "HopeExecuteTime": null,
                //             "UploadToServerTime": "2025-02-07 11:56:19",
                //             "PlatformType": "Alibaba",
                //             "ShopId": 252019,
                //             "UserId": null,
                //             "SubFxUserId": null,
                //             "Status": 5,
                //             "SpendTime": 0.0,
                //             "Type": 34,
                //             "PageIndex": 1,
                //             "PageSize": 1,
                //             "TotalCount": 0,
                //             "ParamJson": null,
                //             "FromModule": "分单系统-生成对账结算账单",
                //             "FilePath": "/files/20250207-11//-68696173.zip",
                //             "FileName": null,
                //             "FileSize": 0.8,
                //             "RetryTimes": 0,
                //             "ExtField1": "e7cda9fa3edcbd21",
                //             "ExtField2": null,
                //             "ExtField3": null,
                //             "ErrorType": null,
                //             "ExtField4": null,
                //             "ExtField5": null,
                //             "SyncLockStatus": null,
                //             "ProcessSyncServerIP": null,
                //             "QueueDealTime": null,
                //             "TaskName": null,
                //             "TaskRemark": "小胡出账----正式",
                //             "IsHide": false,
                //             "TaskFlag": null,
                //             "IsSubTask": false,
                //             "MainTaskCode": null,
                //             "UpdateTime": "2025-02-10 09:56:10",
                //             "TaskCode": null,
                //             "ExtField6": null,
                //             "TaskNameExt": "分单系统-生成对账结算账单20250207105328"
                //         },
                //         {
                //             "ExpiredDateFrom": "2025-02-10 15:44",
                //             "ExpiredDateTo": "2025-02-17 15:44",
                //             "FileIsValidation": false,
                //             "StatusDescription": "文件已失效",
                //             "hideDetail": true,
                //             "Id": 93305208,
                //             "IP": null,
                //             "Message": null,
                //             "ExecSql": null,
                //             "CreateTime": "2025-02-07 10:43:55",
                //             "DealTime": null,
                //             "HopeExecuteTime": null,
                //             "UploadToServerTime": "2025-02-10 15:44:20",
                //             "PlatformType": "Alibaba",
                //             "ShopId": 252019,
                //             "UserId": null,
                //             "SubFxUserId": null,
                //             "Status": 5,
                //             "SpendTime": 0.0,
                //             "Type": 34,
                //             "PageIndex": 1,
                //             "PageSize": 1,
                //             "TotalCount": 0,
                //             "ParamJson": null,
                //             "FromModule": "分单系统-生成对账结算账单",
                //             "FilePath": "/files/20250210-15//-2083140765.zip",
                //             "FileName": null,
                //             "FileSize": 0.29,
                //             "RetryTimes": 0,
                //             "ExtField1": "5575adf5334741e2",
                //             "ExtField2": null,
                //             "ExtField3": null,
                //             "ErrorType": null,
                //             "ExtField4": null,
                //             "ExtField5": null,
                //             "SyncLockStatus": null,
                //             "ProcessSyncServerIP": null,
                //             "QueueDealTime": null,
                //             "TaskName": null,
                //             "TaskRemark": "小胡出账----测试",
                //             "IsHide": false,
                //             "TaskFlag": null,
                //             "IsSubTask": false,
                //             "MainTaskCode": null,
                //             "UpdateTime": "2025-02-10 17:50:35",
                //             "TaskCode": null,
                //             "ExtField6": null,
                //             "TaskNameExt": "分单系统-生成对账结算账单20250207104355"
                //         }
                //     ],
                //     "ProductAtrs": null,
                //     "Setting": {
                //         "IsNotAutoMerger": false,
                //         "IsShowMergerTips": false,
                //         "CaiNiaoBatchPrintCount": 10,
                //         "OrderCombine": false
                //     },
                //     "QueryCondition": null,
                //     "LogId": null,
                //     "IsSyncing": false,
                //     "IsFxShopAutoMergerOpened": false,
                //     "SupplierProducts": null,
                //     "dataFlag": 0,
                //     "GlobalSearchCloudPlatform": null,
                //     "GlobalSearchDbName": null,
                //     "UseNewQuery": false,
                //     "RemarkSetting": null,
                //     "ShopAtrs": null,
                //     "QuerySecond": null
                // }    
                
                taskList = rsp.Data.Rows || [];
                
                //账单下载模块且不是账单下载Tab才渲染查看明细按钮
                if (((isShowDownBill == true && navs[downBillSeqNumber].isSelect == true) || navType == "DownBill") && taskFlag != "BatchOutAcount") {
                    if (navType == "DownBill") {
                        navs[downBillSeqNumber].isSelect = true;
                    }
                    taskList.forEach(function (item, i) {
                        item.hideDetail = false;
                    });
                }

                var tplt = $.templates("#exportTask_list_tmpl")
                var token = commonModule.getToken();
                var html = tplt.render({ TaskList: taskList, Token: token });
                $("#exportTask_body").empty().append(html);

                //无数据页面提示
                if (rsp.Data.Rows.length == 0 && !rsp.Data.Rows.length) {
                    var noDataHtml = '<tr><td colspan="20"><div class="tableNoDataShow"><img src="/Content/images/noData-icon.png" /><span class="tableNoDataShow-title">暂无数据！</span></div></td></tr>'
                    $("#exportTask_body").append(noDataHtml);
                }
                if (isPaging == true) {
                    return;
                }
                layui.laypage.render({
                    elem: 'layui-myPage',
                    theme: ' wu-page wu-one'
                    , count: rsp.Data.Total
                    , limit: _pageSize
                    //, limits: [2, 4, 6, 8, 10, 20, 50]
                    , layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
                    , jump: function (obj, first) {
                        if (!first) {
                            //layer.alert((obj.curr) + "=" + (obj.limit));
                            _pageIndex = obj.curr;
                            _pageSize = obj.limit;
                            module.LoadList(true);
                        }
                    }
                });
            }
        });

    }

    //查看明细
    var checkAccountsInfoDailog = null;
    module.checkAccountsInfo = function (isPaging, Code, billTime, updateTime,taskFlag) {
        var tplt1 = $.templates("#checkAccountsInfo_list_tmpl")
        var html1 = tplt1.render({});
        $('#bill_time').html('');
        $('#total_count').html('');
        $('#success_count').html('');
        $('#fail_count').html('');
        $('#wait_count').html('');
        if (isPaging == false) {
            _pageIndex = 1;
            _pageSize = 50;
        }
        common.Ajax({
            url: '/ExportTask/GetBillByTaskChildList',
            loading: true,
            data: { mainTaskCode: Code == null || Code == undefined ? mainTaskCode : Code, pageSize: _pageSize, pageIndex: _pageIndex, billTimeStart: billTime, billTimeEnd: updateTime,taskFlag:taskFlag},
            success: function (res) {
                if (isPaging == false) {
                    checkAccountsInfoDailog = layer.open({
                        type: 1,
                        title: "查看明细", //不显示标题
                        content: html1,
                        // area: '1000', //宽高
                        area: '1000px',
                        // area: ['1000px', '570px'],
                        skin: 'wu-dailog',
                        offset: 'auto',
                        btn: false,
                        success: function () {
                        }
                    });
                }
                // 自测数据
                //  res.Data = {
                //     "Total": 110,
                //     "List": [
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 2,
                //             "TaskStatus": null,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036647,
                //             "FxUserId": 42,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "7C410173DF21BEAC7F59E734A3AE22E5",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036646,
                //             "FxUserId": 38,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "49478CB0C5D1414C70E0D83492988A93",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036645,
                //             "FxUserId": 166,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "4D1CA492EEF81F07FAD529AE1887CCA9",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036644,
                //             "FxUserId": 382,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "55A832E85AA6F1CCAF46FD1D81D262DF",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036643,
                //             "FxUserId": 509,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "7B3B8E78D4180CF00F64A481078D8603",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036642,
                //             "FxUserId": 2719,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "9B606C230162950AC983BB9E6ECBD1BA",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036641,
                //             "FxUserId": 12756,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "8BFF9EE542B03BE9DF8D75C8BE50A5B8",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036640,
                //             "FxUserId": 10,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "33E337CE587F64137C94C7915DF8999A",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036639,
                //             "FxUserId": 18993,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "3EE541BBA13BF547D767A294359C9757",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036638,
                //             "FxUserId": 124144,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "A1BF8F4B8CB93E8AA3E85F053FB20F2A",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036637,
                //             "FxUserId": 127689,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "63B8991801102282E78FF166AAEFEFF2",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036636,
                //             "FxUserId": 127690,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "8074F7A3B553F87E28488760E71D34CF",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036635,
                //             "FxUserId": 15596,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "4822C585CD462E57AD160986ED889130",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036634,
                //             "FxUserId": 23239,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "310A7938DFC606A7A1BF8A89605F11EE",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036633,
                //             "FxUserId": 74077,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "66DE9D0814671EF37890740A02608C2D",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036632,
                //             "FxUserId": 296024,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "303A67402AAC2EDE6C153B4816A6EFD8",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036631,
                //             "FxUserId": 320575,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "A81A7E4E83BC6A7401E0C0363E02791D",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036630,
                //             "FxUserId": 65537,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "53B040C7C6F12E0561A8797FCC22C09E",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036629,
                //             "FxUserId": 351832,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "75F4462BCFB95F4D466C9538B0BA6085",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036628,
                //             "FxUserId": 31893,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "A30AB44D9721833C4D290E9B3F4EB2EC",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036627,
                //             "FxUserId": 680338,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "5F274F281AC309C7917A58943F3ABBBB",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036626,
                //             "FxUserId": 720616,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "D919C60C40742467A56AA2A3AB485809",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036625,
                //             "FxUserId": 475715,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "BB5D1FA98B315C21281E38BD3CDAF4EB",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036624,
                //             "FxUserId": 669515,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "B980E621A077F02600A264C3EE55B83D",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036623,
                //             "FxUserId": 117576,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "DA63B2F0E3252EF433C2886E9405AD87",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036622,
                //             "FxUserId": 137345,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "85119E7F666BEB9183477CE7BDA704D3",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036621,
                //             "FxUserId": 618626,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "7C2EA2CB2D96EA9A34AC2687C8223917",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036620,
                //             "FxUserId": 842209,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "3C9D66A56C5A2FEB49830D83D0347D60",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036619,
                //             "FxUserId": 842210,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "9E906BDF2C2B6D98582F5B5B13ACF317",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036618,
                //             "FxUserId": 842212,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "26BEEA842B7BAD2B178CA287892D0ABC",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036617,
                //             "FxUserId": 842213,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "38EFF5E6C1F4853C9B85BF8CC0CDC8B1",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036616,
                //             "FxUserId": 842215,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "D55A9A7373840F4A3535852AD45A3915",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036615,
                //             "FxUserId": 842216,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "C5F32DCE4092CF5A8FF4E889FA43BEA6",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036614,
                //             "FxUserId": 842217,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "806C2E915AE5001C5E15F1512A375C2A",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036613,
                //             "FxUserId": 842218,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "3D9F6D6CA38EBD5746F7BDBAEF9ED66F",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036612,
                //             "FxUserId": 842219,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "70BADE554487CDD648AF9857502B4837",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036611,
                //             "FxUserId": 842220,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "20F63D5D0B4AC32C1677E248F8D044A1",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036610,
                //             "FxUserId": 842221,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "1A2885B8F5459A1A594A8C0A215DE56B",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036609,
                //             "FxUserId": 842222,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "77B2FE2283C6EB723EDEEB8145F32A41",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036608,
                //             "FxUserId": 842223,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "08C7396DA1B23C88EBF0E5BF2E5FF480",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036607,
                //             "FxUserId": 842224,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "8A5D9D0678155E333F79244A569C52E8",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036606,
                //             "FxUserId": 842225,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "55D93A6A1C7A3DEA5C2D93B772FC4E5D",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036605,
                //             "FxUserId": 842226,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "3206BDF137088745C95762E883E15026",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036604,
                //             "FxUserId": 842228,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "F9722E5B224E10B59253A3083F9A2F32",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036603,
                //             "FxUserId": 842229,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "EB6D7CF581512AAFEE6A0C31CE247C8B",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036602,
                //             "FxUserId": 842230,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "7835DC8B3E8D18647A5AEBDBB6CDFF1A",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036601,
                //             "FxUserId": 842231,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "457E3117C03D2D3F14BCCF53069DA182",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036600,
                //             "FxUserId": 842232,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "6A33967F8D352DA901F3D73872C8FF67",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036599,
                //             "FxUserId": 842233,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "817EB2996CF18C7878661466D46C8BE9",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         },
                //         {
                //             "CreateUserNickName": null,
                //             "CreateRemarkName": null,
                //             "CreateRemark": null,
                //             "ExpirationTime": null,
                //             "UserFxNickName": null,
                //             "UserFxRemarkName": null,
                //             "UserFxRemark": null,
                //             "CurLoginFxUserId": 0,
                //             "LastShowNickName": null,
                //             "Percent": 0.0,
                //             "TaskStatus": 5,
                //             "Flag": null,
                //             "MainTaskStatus": null,
                //             "BillRemark": null,
                //             "Id": 89036598,
                //             "FxUserId": 842234,
                //             "BeginTime": "2025-02-13 00:00:00",
                //             "EndTime": "2025-02-20 23:59:59",
                //             "TotalPrice": 0.00,
                //             "TotalProductPrice": 0.00,
                //             "TotalFreight": 0.00,
                //             "TotalProfit": 0.0,
                //             "TotalSkuCount": 0,
                //             "Remark": null,
                //             "FilePath": null,
                //             "FilePath2": null,
                //             "ParamJson": null,
                //             "BillCode": "E443A0E94E098C40D859FAC809A16B7B",
                //             "BillStatus": 1,
                //             "CreateTime": "2025-02-20 09:47:41",
                //             "UpdateTime": "0001-01-01 00:00:00",
                //             "BillTime": null,
                //             "ConfirmTime": null,
                //             "CreateUser": 74,
                //             "SubFxUserId": 0,
                //             "SettlementType": 1,
                //             "HopeExecuteTime": null,
                //             "IsHide": false,
                //             "IsSubTask": false
                //         }
                //     ],
                //     "SuccessCount": 110,
                //     "FailCount": 0,
                //     "WaitCount": 0
                // }
                var data = billDetail = res.Data.List || [];

                //2.渲染
                var tplt2 = $.templates("#childTaskList_data_tr");
                var html2 = tplt2.render({ childTaskList: data });
                $("#ChildTaskList_body").html(html2);

                $('#bill_time').html(billTime);
                $('#total_count').html(res.Data.Total);
                $('#success_count').html(res.Data.SuccessCount);
                $('#fail_count').html(res.Data.FailCount);
                $('#wait_count').html(res.Data.WaitCount);
                
                if(res.Data.List.length == 0 ) {
                    $("#ChildTaskList_body").find(".tdNodata").attr('colspan', '7');
                }
                // 2.1 店铺出账样式渲染
                if (data[0] && data[0].SettlementType == 3) {
                    //console.log(666);
                    $('.checkAccountsInfoDailog .settlementObject_th').text('店铺名称');
                    $('.checkAccountsInfoDailog .billRemark_th').text('账单备注(自己可见)');
                    $('.checkAccountsInfoDailog .billStatus_th').hide();

                }

                //3.分页
                if (isPaging == true) {
                    return;
                }
                mainTaskCode = Code;
                layui.laypage.render({
                    elem: 'layui-layChildPage',
                    theme: ' wu-page wu-one',
                    count: res.Data.Total,
                    limit: _pageSize,
                    curr: _pageIndex,
                    limits: [50, 100, 200, 300, 400, 500],
                    layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                    jump: function (obj, first) {
                        $(".layui-layChildPage-count").html()
                        if (!first) {
                            _pageSize = obj.limit;
                            _pageIndex = obj.curr;
                            module.checkAccountsInfo(true);
                        }
                    }
                });

            }
        });
    }
       
    //店铺出账更新备注
    module.UpdateBillRemark = function (billCode) {
        common.stopM(event);
        console.log(2);
        var billArr = $.grep(billDetail, function (e) {
            return (e.BillCode === billCode);
        });
        if (billArr.length > 0) {
            var flag = billArr[0].Flag;
            $('input:radio[name="seller_flag"]').prop('checked', false);
            $('input:radio[name="seller_flag"][value="' + flag + '"]').prop('checked', true);
            $("#txt_area_seller_remark_flag").val(billArr[0].BillRemark);
        } else {
            $("#txt_area_seller_remark_flag").val("");
        }
        var updateRemark = layer.open({
            type: 1,
            title: "仅自己可见标签", //不显示标题
            content: $('#sellerRemark'),
            area: ['460'], //宽高
            skin: 'wu-dailog',
            btn: ['保存', '取消'],
            yes: function () {
                var billRemark = $("#txt_area_seller_remark_flag").val();
                var flag = $('input:radio[name="seller_flag"]:checked').val();
                if (flag == undefined) flag = null;
                billRemark = commonModule.DelSpecialChar(billRemark);

                commonModule.Ajax({
                    url: '/FinancialSettlement/UpdateBillRemark',
                    loading: true,
                    data: { billCodes: billCode, remark: $.trim(billRemark), Flag: flag },
                    success: function (rsp) {
                        if (rsp.Success === false) {
                            //layer.msg(rsp.Message, { icon: 2 });
                            layer.msg("操作异常,请稍后再试");
                            return;
                        }

                        for (var index in billDetail) {
                            var item = billDetail[index];
                            if (item.BillCode == billCode) {
                                if (!!flag)
                                    item.Flag = parseInt(flag);
                                item.BillRemark = $.trim(billRemark);
                                break;
                            }
                        }
                        var tplt2 = $.templates("#childTaskList_data_tr");
                        var html2 = tplt2.render({ childTaskList: billDetail });
                        $("#ChildTaskList_body").html(html2);

                        //$("#Flag_" + billCode).html('<i class="iconfont icon-qizhi qizhiColor' + flag + '"></i>');
                        layer.close(updateRemark);
                        layer.msg("操作成功");

                    }
                });
            },
            cancel: function () { }
        });
    }

    module.closeCheckAccountsInfo = function () {
        layer.close(checkAccountsInfoDailog);
    }


    module.showTip = function () {
        layer.msg("批量出账任务未完成，暂时无法查看明细，请耐心等待");
    }

    module.showLeaveMessage = function () {

        var loadIndex = layer.open({
            type: 1,
            title: "账单留言", //不显示标题
            content: $('#leaveMessage'),
            area: ['460'], //宽高
            btn: ['保存', '取消'],
            yes: function () {


            },
            cancel: function () {
            }
        });


    }

    function _getSendFailRequertModel(isPaging) {

        var data = {
            PageIndex: _pageIndex,
            PageSize: _pageSize,
        };
        return data;
    }
    return module;
}(rightContentRenderModule || {}, jQuery, commonModule, layer));
