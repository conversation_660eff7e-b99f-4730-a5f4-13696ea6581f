var funSelectbox = (function (module, common, layer, $) {

    function selectMenu() {
        this.eles = ""; //元素
        this.emptyTitle = ""; //无选择数据标题内容
        this.isRadio = null; //是否为单选
        this.allSelect = null; //.是显示多选框
        this.searchType = false; // false不显示搜索框
        this.showWidth = "";
        this.selectCallBack = null;//点击选择项时回调
        this.delTitleListCallBack = null; //点击标题删除触发回调
        this.successInitCallBack = null;//成功初始代后回调
        this.sync = null;//是否异步获取数据
        this.successSyncCallBack = null;//异步调用回调
        this.isLoad = null;//是否已异步加载

        this.data = []; //所有数据
        this.selectData = []; //提交数据

        this.mainHtml = "";

    }

    selectMenu.prototype.initData = function (obj) { //初始化页面

        this.eles = obj.eles ? obj.eles : null; //元素
        this.emptyTitle = obj.emptyTitle ? obj.emptyTitle : "请选择"; //无选择数据标题内容
        this.isRadio = obj.isRadio ? obj.isRadio : null; //是否为单选
        this.allSelect = obj.allSelect ? obj.allSelect : null; //.是显示多选框
        this.searchType = obj.searchType ? obj.searchType : null; // false不显示搜索框			
        this.showWidth = obj.showWidth ? obj.showWidth : ""; // false不显示搜索框

        this.data = obj.data ? obj.data : []; //所有数据
        this.selectData = obj.selectData ? obj.selectData : []; //选择的数据
        this.selectCallBack = obj.selectCallBack ? obj.selectCallBack : null; // 点击选择项时回调
        this.successInitCallBack = obj.successInitCallBack ? obj.successInitCallBack : null;//成功初始代后回调
        this.delTitleListCallBack = obj.delTitleListCallBack ? obj.delTitleListCallBack : null;

        this.isLoad = obj.isLoad ? obj.isLoad : null; //异步调用是否完成
        this.sync = obj.sync ? obj.sync : null; //是否异步调用
        this.successSyncCallBack = obj.successSyncCallBack ? obj.successSyncCallBack : null;//成功初始代后回调        

        this.mainHtml += '<div class="selectMore">';
        this.mainHtml += '<ul class="selectMore-choose clearfix" id="selectMore_choose">';
        this.mainHtml += '<li style="background-color:#fff">' + this.emptyTitle + '</li>'; //输入框请选择提示			
        this.mainHtml += '</ul>';
        this.mainHtml += '</div>';
        if (this.showWidth) {
            this.mainHtml += '<div style="width:' + this.showWidth +
                '" class="selectWrap-box" onclick="event.stopPropagation()">';
        } else {

            this.mainHtml += '<div class="selectWrap-box" onclick="event.stopPropagation()">';
        }
        if (this.searchType == 1) {
            this.mainHtml +=
                '<div class="electWrap_search_wrap"><input type="text" placeholder="搜索内容" class="selectWrap-search-input"></div>';
        }

        if (!this.isRadio) {

            if (this.allSelect && this.data.length > 0) {
                this.mainHtml +=
                    '<div class="electWrap-allSelect" ><label><input type="checkbox"  class="allSelect-checkbox">全选</label></div>';
            }

        }

        this.mainHtml += '<ul class="selectMore-ul">';
        this.mainHtml += '</ul>';
        this.mainHtml += '</div>';
        this.mainHtml += '<i class="showMoreicon"></i>';

        $(this.eles).empty().append(this.mainHtml); //初始化整体结构	

        this.initSelect(this.data); //初始化下拉框

        this.initTitleShow(this.selectData); //初始化标题

        if (this.isRadio) {
            // this.changeItemsRadio();	//初始化下拉框选择时事件--单选				
        } else {
            this.changeItemsChoose(); 	//初始化下拉框选择时事件--多选				
        }

        this.allSelectCheckbox(); //全选事件
        this.searchInput();
        this.showSelectBox();  //点击出现下拉框事件

        if (typeof this.successInitCallBack == "function") {  //渲染后的回调函数
            this.successInitCallBack();
        }

    }

    selectMenu.prototype.initSelect = function (data) { //初始化下拉框数据
        

        var that = this;
        $(that.eles + " .selectMore-ul").html("");

        //var isLoad = $(that.eles + " .selectWrap-box").attr("data-load");
        if (this.sync && !that.isLoad) {
            $(this.eles + " .selectMore-ul").append('<li onclick="event.stopPropagation()" class="list noData"><span class="loading"></span></li>');

        }
        else {
            if (data.length == 0) {
                $(this.eles + " .selectMore-ul").append('<li onclick="event.stopPropagation()" class="list noData">没有匹配到数据</li>');

            } else {

                for (var i = 0; i < data.length; i++) {
                    var icon = data[i].Icon ? data[i].Icon : '';
                    var iconTitle = data[i].Icon ? showPlatformTitle(data[i].Icon)  : '';
                    var iconHtml = data[i].Icon && data[i].Icon != 'other' ? '<i class="wu-pintaiIcon wu-small wu-hover ' + icon + '" ' + 'title="'+ iconTitle + '"></i>' : '';

                    if (that.isRadio) {

                        $(this.eles + " .selectMore-ul").append(
                            '<li  class="list isRadio" data-icon="' + icon + '" data-id="' + data[i].Value +
                            '"><label class="radio-label">' + iconHtml + data[i].Text + '</label><i class="list-icon">√</i></li>');

                    } else {
                        $(this.eles + " .selectMore-ul").append('<li onclick="event.stopPropagation()" class="list"  data-icon="' + icon + '" data-id="' + data[i]
                            .Value +
                            '"><label><input class="selectMore-items-input" type="checkbox" value="' +
                            data[i].Value + '" data-icon="' + icon + '"><span>' + iconHtml + data[i].Text +
                            '</span></label></li>')
                    }

                }
            }
        }


        if (that.isRadio) {

            $(that.eles + " .selectMore-ul .isRadio").each(function (index, item) {
                for (var i = 0; i < that.selectData.length; i++) {
                    if ($(item).attr("data-id") == that.selectData[i].Value) {
                        $(item).addClass("active").children(".list-icon").css({ display: "inline-block" })
                    }
                }

            })

        }
        else {
            $(that.eles + " .selectMore-ul>li .selectMore-items-input").each(function (index, item) {
                for (var i = 0; i < that.selectData.length; i++) {
                    if ($(item).val() == that.selectData[i].Value) {
                        $(item).prop("checked", "checked");
                        $(item).parent().parent().addClass("active");
                    }
                }
            });
            //如果有全选框并且数据被全选中则把全选框也选中
            if (that.allSelect && that.IsAllCheck()) {
                $(that.eles + " .allSelect-checkbox").prop("checked", "checked");
            }

        }


        if (that.isRadio) {
            that.changeItemsRadio();  //初始化下拉框选择时事件--单选					
        }
        else {
            that.changeItemsChoose(); //初始化下拉框选择时事件--多选 				
        }


    }
    selectMenu.prototype.initTitleShow = function (selectData) { //初始化标题数据

        var selectHtml = "";
        if (selectData.length == 0) {
            selectHtml = '<li style="background-color:#fff"  class="list noData">' + this.emptyTitle + '</li>';
            $(this.eles).removeClass("activeChoose")

        } else {

            var iconHtml = selectData[0].Icon && selectData[0].Icon != 'other' ? '<i class="wu-pintaiIcon wu-small ' + selectData[0].Icon + '"></i>' : '';

            if (this.isRadio) {
                selectHtml += '<li class="isRadio"><span class="selectMore-choose-title">' + iconHtml + selectData[0].Text + '</li>';
            } else {


                if (selectData.length == 1) {
                    selectHtml += '<li class="onlyList-title" ><span class="selectMore-choose-title">' + iconHtml + selectData[0].Text + '</span><i data-id="' +
                        selectData[0].Value +
                        '" class="selectMore-choose-deleicon">✕</i></li>';
                } else {
                    selectHtml += '<li><span class="selectMore-choose-title">' + iconHtml + selectData[0].Text + '</span><i data-id="' +
                        selectData[0].Value +
                        '" class="selectMore-choose-deleicon">✕</i></li>';

                }

                if (selectData.length > 1) {
                    var num = selectData.length - 1;
                    selectHtml += '<li><span class="selectMore-choose-title" style="color:#3aadff;margin-top:0px;">+' + num + '</span></li>';
                }



            }

            $(this.eles).addClass("activeChoose")
        }

        $(this.eles + " .selectMore-choose").html(selectHtml);

        if (!this.isRadio) {
            this.deleTitleShow(); //初始化标题删除事件				
        }


        this.writeTitleValues();


    }

    selectMenu.prototype.deleTitleShow = function () {
        var that = this;
        $(this.eles + " .selectMore-choose li").on("click", function (e) {
            var deleEle = e.target;
            if (deleEle.nodeName.toUpperCase() == "I") {
                e.stopPropagation();
                var val = $(deleEle).attr("data-id");

                for (var i = 0; i < that.selectData.length; i++) {
                    if (that.selectData[i].Value == val) {
                        that.selectData.splice(i, 1);

                    }
                }
                //把下拉select对应checkbox取消选中
                var inputs = $("#selectMore_items>li .selectMore-items-input");
                for (var j = 0; j < inputs.length; j++) {
                    if ($(inputs[j]).val() == val) {
                        inputs[j].checked = false;
                    }
                }

            }
            that.isSelectAllCheck();
            that.initTitleShow(that.selectData);
            that.initSelect(that.data);
            that.changeItemsChoose();
            if (typeof that.delTitleListCallBack == "function") {
                that.delTitleListCallBack(that.selectData);
            }
        })

    }
    selectMenu.prototype.changeItemsRadio = function () {
        var that = this;
        $(that.eles + " .selectMore-ul li.isRadio").each(function () {
            $(this).on("click", function () {
                if (that.isRadio) {
                    $(".selectWrap-box").hide();//可多选情况下,不需要每选中一个选项就关闭窗口,所以注释掉
                }
                if ($(this).hasClass("active")) {
                    $(this).removeClass("active").children(".list-icon").css({ display: 'none' });
                    that.selectData = [];
                    that.initTitleShow([]); //初始化标题

                } else {
                    that.selectData = [];
                    $(that.eles + " .selectMore-ul li.isRadio").removeClass("active").children(".list-icon").css({ display: 'none' });
                    $(this).addClass("active").children(".list-icon").css({ display: 'inline-block' });
                    var val = $(this).attr("data-id");
                    var icon = $(this).attr("data-icon");

                    var text = $(this).children(".radio-label").text();
                    var obj = {};
                    obj.Value = val;
                    obj.Text = text;
                    obj.Icon = icon;
                    that.selectData.push(obj);
                    that.initTitleShow(that.selectData); //初始化标题

                }

                if (typeof that.selectCallBack == "function") { //选中回调函数                    
                    that.selectCallBack(that.selectData);
                }

            })

        })


    };



    selectMenu.prototype.changeItemsChoose = function () { //下拉 checkbox 点击事件
        var that = this;
        $(this.eles + " .selectMore-ul li .selectMore-items-input").each(function () {
            $(this).on("change", function () {
                var val = $(this).val();
                var icon = $(this).attr('data-icon');
                var text = $(this).next().text();

                if ($(this).is(":checked")) {
                    that.selectData.push({
                        "Text": text,
                        "Value": val,
                        "Icon": icon
                    });
                    $(this).parent().parent().addClass("active");

                } else {
                    for (var i = 0; i < that.selectData.length; i++) {
                        if (that.selectData[i].Value == val) {
                            that.selectData.splice(i, 1);
                        }
                    }
                    $(this).parent().parent().removeClass("active");

                }

                that.selectData = that.uniqueObj("Value", that.selectData);
                if (typeof that.selectCallBack == "function") {  //选中回调函数 
                    that.selectCallBack(that.selectData);
                }
                that.initTitleShow(that.selectData); //初始化标题
                that.deleTitleShow(); //初始化标题删除事件
                that.isSelectAllCheck(); //全选按钮是否勾选

            })

        })

    }

    selectMenu.prototype.allSelectCheckbox = function () {
        var that = this;

        $(that.eles + " .allSelect-checkbox").on("change", function () {
            if ($(this).is(':checked')) {

                $(that.eles + " .selectMore-ul>li").each(function (index, item) {

                    var val = $(item).attr("data-id");
                    var text = $(item).children().children("span").text();
                    var icon = $(item).attr("data-icon");
                    var obj = {};
                    obj.Text = text;
                    obj.Value = val;
                    obj.Icon = icon;

                    $(item).children().children("input").prop("checked", "checked");
                    $(item).addClass("active");
                    that.selectData.push(obj);
                    that.selectData = that.uniqueObj("Value", that.selectData);

                })

            } else {

                $(that.eles + " .selectMore-ul>li").each(function (index, item) {
                    var val = $(item).attr("data-id");
                    for (var i = 0; i < that.selectData.length; i++) {
                        if (that.selectData[i].Value == val) {
                            that.selectData.splice(i, 1)
                        }
                    }

                    $(item).children().children("input").prop("checked", false);
                    $(item).removeClass("active");
                })

            }
            if (typeof that.selectCallBack == "function") { //选中回调函数
                that.selectCallBack(that.selectData);
            }
            that.initTitleShow(that.selectData); //初始化标题

        });


    }

    selectMenu.prototype.uniqueObj = function (key, arr) {
        var result = [];
        var obj = {};
        for (var i = 0; i < arr.length; i++) {
            if (!obj[arr[i][key]]) {
                result.push(arr[i]);
                obj[arr[i][key]] = true;
            }
        }
        return result;
    }

    selectMenu.prototype.writeTitleValues = function () {  //选中的selectData值Value 会拼到最外层div上，在 data-values 里

        var vals = "";
        for (var i = 0; i < this.selectData.length; i++) {
            if (i == this.selectData.length - 1) {
                vals += this.selectData[i].Value;

            } else {
                vals += this.selectData[i].Value + ",";
            }
        }

        $(this.eles).attr("data-values", vals);

    }




    selectMenu.prototype.isSelectAllCheck = function () {

        var isAllSelect = true;
        $(this.eles + " .selectMore-ul li").each(function (index, item) {
            if (!$(item).hasClass("active")) {
                isAllSelect = false;
                return;
            }
        })
        if (isAllSelect) {
            $(this.eles + " .allSelect-checkbox").prop("checked", "checked")
        } else {
            $(this.eles + " .allSelect-checkbox").prop("checked", false)

        }

    }

    selectMenu.prototype.searchInput = function () {
        var that = this;

        $(this.eles + " .selectWrap-search-input").on("input", function () {

            var val = $(this).val().replace(/(^\s*)|(\s*$)/g, '') || "";
            val = val.toLocaleLowerCase();
            var searcSelectData = [];
            for (var i = 0; i < that.data.length; i++) {

                if (that.data[i].Text.toLocaleLowerCase().indexOf(val) != -1) {
                    searcSelectData.push(that.data[i])
                }
            }

            if (that.isRadio) {
                that.initSelect(searcSelectData);
            } else {
                that.initSelect(searcSelectData);
                that.isSelectAllCheck();
            }


        })

    }

    selectMenu.prototype.selectDates = function () {
        var vals = [];
        $(this.selectData).each(function (i, item) {
            vals.push(item.Value);
        })
        return vals;
    };

    selectMenu.prototype.showSelectBox = function () {
        var that = this;
        $(this.eles + " .selectMore").on("click", function (e) {
            e.stopPropagation();
            //$(".selectWrap-box").hide();//可多选情况下,不需要每选中一个选项就关闭窗口,所以注释掉
            //var datas = that.data;
            $(".selectWrap-box").hide();
            $(that.eles + " .selectWrap-box").show();
            $(that.eles + " .selectWrap-search-input").val("");
            that.initSelect(that.data);
            //var isLoad = $(that.eles + " .selectWrap-box").attr("data-load");
            if (that.sync && !that.isLoad && typeof that.successSyncCallBack == "function")
                that.successSyncCallBack();
        });
    };

    selectMenu.prototype.Clear = function () {
        this.initSelect([]);
    }


    $(document).on("click", function () {

        if ($("#ProductSubjectCheck .selectWrap-box") != undefined && $("#ProductSubjectCheck .selectWrap-box").is(":visible")) {
            $("#ProductAttrCheck .selectMore").click();
            $("#ProductSubjectCheck .selectWrap-box").hide();
            //$(".selectWrap-box").hide();
        } else {
            $(".selectWrap-box").hide();
        }
    })

    selectMenu.prototype.GetLoadStatus = function () {
        return this.isLoad;
    }
    selectMenu.prototype.SetLoadStatus = function (status) {
        this.isLoad = status ? status : null;
    }

    selectMenu.prototype.IsAllCheck = function () {
        var isAllCheck = this.data.length == this.selectData.length;
        return isAllCheck;
    }
    return selectMenu;

})(funSelectbox || {}, commonModule, layer, jQuery);


