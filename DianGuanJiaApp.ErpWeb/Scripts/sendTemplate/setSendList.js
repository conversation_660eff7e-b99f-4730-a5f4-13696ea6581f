//用户自定义的发货单
var $data = [];
//系统发货单
var $data2 = [];


$(function () {
    initSendGoodList();
});

function initSendGoodList() {
    //$("#templateLoading").show();
    var loding = layer.load(2, { shade: false});
    commonModule.getJSON("/SendGoodTemplate/List?t=" + new Date().getTime(), function (json) {
        //$("#templateLoading").hide();
        layer.close(loding);
        if (json.Success && json.Data && json.Data.length > 0) {
            var templates = json.Data;
            for (var i = 0; i < templates.length; i++) {
                var t = templates[i];
                if (t.IsSystemTemplate)
                    $data2.push(t);
                else
                    $data.push(t);
            }
            $data2.push({ "Id": -1, "Snapshot": null, "TemplateName": "空白模板", "MemberId": "!System", "Config": "", "IsDefault": false, "IsDeleted": false, "IsOldVersion": false, "IsSystemTemplate": true, "SizeInfo": { "Width": "自定义", "Height": "自定义", "OffsetWidth": 0, "OffsetHeight": 0 } });
            addSetSendList();
            onloadData();     //数据初始化
            addSetSendListhShow();
            slimScroll();
            $(".slimScrollDiv li:first").click();
        }
    });

}

function openLinkWithPara(url, isNewTab) {
    url = commonModule.rewriteUrl(url);
    if (isNewTab)
        window.open(url);
    else
        window.location.href = url;
}

function onloadData() {
    var str = "";
    var defaultId = "";
    for(var i=0;i<$data.length;i++){
        var cur = $data[i];
        if (cur.IsDefault)
            defaultId = cur.Id;
        str += '<div class="container03_tabale_content sendGoodTemplateRow" id=containerList' + $data[i].Id + '>'
        str+='    <div style="padding: 0 12px; box-sizing: border-box;">'+$data[i].TemplateName+'</div>'
        str+='    <div><span class="tabale_content_spanOne">'+$data[i].SizeInfo.Width+'*'+$data[i].SizeInfo.Height+'</span><span class="tabale_content_spanTwo">Left: '+$data[i].SizeInfo.OffsetWidth+' Top: '+$data[i].SizeInfo.OffsetHeight+'</span></div>'
        
        if (cur.DefaultPrinter) {
            str += '<div>' + (cur.DefaultPrinter) + '</div>'
        }else{
            str += '<div><span class="wu-badge wu-processing"><i class="wu-badge-dot"></i><s class="wu-badge-title">未绑定</s></span></div>'
        }
        str += '    <div class="container03_tabale_content_print" ><span class="wu-btn wu-btn-small wu-primary" onclick="bindingPrint(' + cur.Id + ')">绑定打印机</span></div>'
        str += '    <div class="container03_tabale_content_set">';
        if (cur.IsOldTemplate)
            str += '        <div class="wu-color-a wu-operate wu-f12" onclick="openLinkWithPara(\'OldVersion\',true)">编辑</div>';
        else
            str += '        <div class="wu-color-a wu-operate wu-f12" onclick="openLinkWithPara(\'Edit?templateId=' + $data[i].Id + '\',true)">编辑</div>';
        if (!cur.IsOldTemplate)
            str += '    <div class="wu-color-b wu-operate wu-f12"  onclick=\'deleSetSendListShow("' + $data[i].TemplateName + '","' + $data[i].Id + '")\'>删除</div>'
        str += '    <div  class="setSendGoodTemplateDefaultBtn wu-color-c wu-operate wu-f12" onclick=\'SetSendListCutorm("' + $data[i].TemplateName + '","' + $data[i].Id + '","' + $data[i].IsOldTemplate + '")\'>设置默认</div> <span class="defaultTemplateSpan wu-color-d wu-operate wu-f12">默认模板</span>'
        str+='    </div>'
        str+='</div>'
    }
    $(".container03_tabaleContent").html(str);
    showDefaultTemplate(defaultId);

}

function addSetSendListhShow(){
    var addListStrContent="";//右边显示展示模板
    var addListStrTitle="";  //左边显示展示模板标题
    var addListStrBtn="";    //下边显示展示按钮
    for(var j=0;j<$data2.length;j++){
        var cur = $data2[j];
        if(j==0){
            addListStrContent+='<li class="active" style="opacity:1; filter:alpha(opacity=100);">'
            addListStrContent+='<div class="focusBackground"></div>'
            addListStrContent+='<div class="focusBackgroundP">'+$data2[j].TemplateName+'&nbsp;&nbsp;点击添加，自定义编辑</div>'
            addListStrContent += '<img src="/content/images/1.jpg"/>'
            addListStrContent+='</li>'
            addListStrTitle+='<li id='+$data2[j].Id+' class="active"></i>'+$data2[j].TemplateName+'</li>'
            addListStrBtn+='<span class="addSetSendList_bottom_btnOne active wu-btn wu-btn-mid" onclick=\'addSetSendLists('+$data2[j].Id+')\'>添加</span>'
        }else{
            addListStrContent+='<li>'
            addListStrContent+='<div class="focusBackground"></div>'
            addListStrContent+='<div class="focusBackgroundP">'+$data2[j].TemplateName+'&nbsp;&nbsp;'+$data2[j].SizeInfo.Width+'x'+$data2[j].SizeInfo.Height+' 毫米</div>'
            if(cur.Snapshot)
                addListStrContent += '<img  style="width: 600px;" src="' + cur.Snapshot + '"/>'
            else
                addListStrContent += '<img style="width: 600px;" src="/content/images/1.jpg"/>'
            addListStrContent += '</li>'
            addListStrTitle+='<li id='+$data2[j].Id+'>'+$data2[j].TemplateName+'</li>'
            addListStrBtn+='<span class="addSetSendList_bottom_btnOne wu-btn wu-btn-mid" onclick=\'addSetSendLists('+$data2[j].Id+')\'>添加</span>'
        }
    }
    $(".left ul").append(addListStrContent);
    $(".right ul").append(addListStrTitle);
    $(".addSetSendList_bottom_btn").append(addListStrBtn);
    addSetSendList();  //切换标题和模板展示重新渲染

}



function deleSetSendListShow(templateName, id) {
    var str = '<div class="wu-f14 wu-c09">您确定要删除模板【' + templateName + '】吗？</div>';

    var deleSetSendListDialog = layer.confirm(str, {
        skin: 'wu-dailog', 
        btn: ['删除', '取消'],
        yes: function (index, layero) {
            commonModule.getJSON("/SendGoodTemplate/Delete?templateId=" + id, function (json) {
                if (json.Success) {
                    deleSetSendList(id);
                } else {
                    layer.alert("删除失败：" + json.Message);
                }
            });
            layer.close(deleSetSendListDialog);
            return true;
            
        },
        cancel: function (index, layero) {
            
        }

    });
}

function deleSetSendListShow2(templateName,id) {
    var str = '您确定要删除模板【' + templateName + '】吗？';


    art.dialog({
        content: str,
        top:'150px',
        ok: function () {
            commonModule.getJSON("/SendGoodTemplate/Delete?templateId=" + id, function (json) {
                if (json.Success) {
                    deleSetSendList(id);
                } else {
                    layer.alert("删除失败："+json.Message);
                }
            });
            return true;
        },
        cancelVal: '关闭',
        cancel: true //为true等价于function(){}
    });
}

function GetTemplateById(id) {
    for (var i = 0; i < $data.length; i++) {
        var cur = $data[i];
        if (cur.Id == id)
            return cur;
    }
    return null;
}

function bindingPrint2(id) {
    if (!lodopPrinter.check(true)) {
        return;
    }
    var template = GetTemplateById(id);
    var pCount = LODOP.GET_PRINTER_COUNT();
    var machines = "";
    for (var i = 0; i < pCount ; i++) {
        var printerM = LODOP.GET_PRINTER_NAME(i);
        if (printerM == template.DefaultPrinter) {
            machines += "<option value='" + printerM + "' selected=\"selected\">" + printerM + "</option>";
        } else {
            machines += "<option value='" + printerM + "'>" + printerM + "</option>";
        }
    }
    var str="";
    var str2="";
    str+='<div class="dialog_windows_content clearfix">';
    str+='<div><img src="/content/images/tabale_content_print.png" style="width: 90px" alt=""></div>'
    str+='<div style="font-size: 14px;">打印机：</div>';
    str+='    <div class="dialog_select">';
    str += '    <select class="dialog_form-control " id="default-printer" >';
    str += machines;
    str+='    </select>';
    str+='    </div>';
    str+='</div>';

    art.dialog({
        width:'450px',
        content: str,
        ok: function () {
            var printer = $("#default-printer").val();
            template.DefaultPrinter = printer;
            commonModule.SetPrinterBind(template.Id,2,printer)
        },
        cancelVal: '关闭',
        cancel: true //为true等价于function(){}
    });

}

function bindingPrint(id) {
    if (!lodopPrinter.check(true)) {
        return;
    }
    var template = GetTemplateById(id);
    var pCount = LODOP.GET_PRINTER_COUNT();
    var machines = "<option value=''>请选择打印机</option>";
    for (var i = 0; i < pCount ; i++) {
        var printerM = LODOP.GET_PRINTER_NAME(i);
        if (printerM == template.DefaultPrinter) {
            machines += "<option value='" + printerM + "' selected=\"selected\">" + printerM + "</option>";
        } else {
            machines += "<option value='" + printerM + "'>" + printerM + "</option>";
        }
    }

    var str = "";
    str += '<div class="dialog_windows_content clearfix" style="display: flex;align-items: center;padding:0;">';
    str += ''
    str += '<div style="font-size: 14px; color: rgba(0,0,0,0.9)">打印机：</div>';
    str += '    <div class="dialog_select wu-selectWrap wu-form-mid">';
    str += '    <select class="dialog_form-control wu-select" id="default-printer" >';
    str += machines;
    str += '    </select>';
    str += '    </div>';
    str += '</div>';

    var bindingPrintDialog = layer.open({
        type: 1,
        title: "绑定打印机",
        content: str,
        skin: 'wu-dailog',
        area: ['720', '100'], //宽高
        btn: ['保存', '取消'],
        yes: function () {
            var printer = $("#default-printer").val();
            if (printer) {
                template.DefaultPrinter = printer;
                commonModule.SetPrinterBind(template.Id, 2, printer);
                layer.close(bindingPrintDialog);
                onloadData();
            } else {
                layer.msg("请选择打印机");
            }

        },
        cancel: function () {

        }
    });

}




function deleSetSendListHide() {
    $(".dialog").hide();
    $(".dialogTwo").hide();
}
function SetSendListCutorm(templateName, id, isOldTemplate) {

    //设置发货单为默认模板
    isOldTemplate=isOldTemplate=="true";
    var str ="设置成功：【"+ templateName + "】已设置为默认模板"
    commonModule.getJSON("/SendGoodTemplate/SetAsDefault?templateId=" + id + "&isOldTemplate=" + isOldTemplate, function (json) {
        if (json.Success) {
            showDefaultTemplate(id);
        } else {
            layer.alert("设置失败：" + json.Message,{ skin: 'wu-dailog'});
        }
    });
}

function showDefaultTemplate(id) {
    $(".defaultTemplateSpan").hide();
    $(".setSendGoodTemplateDefaultBtn").show();
    $("#containerList" + id + " .defaultTemplateSpan").show();
    $("#containerList" + id + " .setSendGoodTemplateDefaultBtn").hide();
}

function copySendList() {
    $(".dialogTwo").show(200);
    $(".layer_notice").css({backgroundColor:'#5eb978'}).children("li:last").html("发货单模板（A4）：已完成复制！");
    setTimeout(function () {
        $(".dialogTwo").hide(100);
    },3000)
}

function deleSetSendList(id) {    //删除模板
    $("#containerList"+id).remove();
    deleSetSendListHide();
}
function SetSendList() {
    alert("这是设置打印机事件：SetSendList")
}


function addSetSendList() {
    var oFocus=$('#focus'),
        oRight=oFocus.find('.right'),
        oLeft=oFocus.find('.left'),
        aRLi=oRight.find('li'),
        aLLi=oLeft.find('li'),
        index=0,
        addSetSendList_bottom_btnOne=$(".addSetSendList_bottom_btnOne");
        timer = null;

    aRLi.click(function(){
        $('#focus .right li').removeClass('active');
        index=$(this).index();
        $(this).addClass('active').siblings().removeClass();
        aLLi.eq(index).addClass('active').siblings().removeClass();
        addSetSendList_bottom_btnOne.eq(index).show().siblings().hide();
        $(".addSetSendList_bottom_btnTwo").show();
        aLLi.eq(index).stop().animate({'opacity':1},300).siblings().stop().animate({'opacity':0},300);
        // stopFoucs();
    })

}

$(".addSetSendList_bottom_btnOne").click(function () {
})

function addSetSendListHide() {
    $(".addSetSendList").hide();
}

function addSetSendListShow() {
    $(".addSetSendList").show();

}



function addSetSendLists(id) {
    openLinkWithPara('Edit?templateId=' + id, true)
    //var dt= new Date(),
    //     LongTime=dt.getTime();
    //var newObject={}

    //    for(var i=0;i<$data2.length;i++){
    //        if($data2[i].Id==id){
    //            newObject=$.extend({},$data2[i])
    //        }
    //    }
    //    newObject.Id=LongTime;
    //    newObject.IsSystemTemplate=false;
    //    newObject.MemberId="b2b-1623492085";   //写死的
    //    $data.push(newObject);
    //    $(".addSetSendList").hide();
    //    onloadData()      // 添加后重新渲染页面

}

function slimScroll(){
    $('.right').slimScroll({
        distance: '0px', //组件与侧边之间的距离
        height: '404px',
        position: 'left', //组件位置：left/right

    });

    $('#innerDiv').slimScroll().bind('slimscroll', function(e, pos){
        if(pos=='bottom'){
            // 执行其他逻辑
        }
    });
}









