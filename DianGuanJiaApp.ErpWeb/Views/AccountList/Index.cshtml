
@{
    ViewBag.Title = "电子面单账号";
    ViewBag.MenuId = "PrintSetting";
    ViewBag.MenuTopNav = "AccountList";
    ViewBag.MenuActive = "OrderManagement";
}


@section Header{
    <style type="text/css">
        .color {
            border-bottom: 0px;
        }

        .hei {
            height: 39px;
        }

        .moduleDiv {
            width: 1200px;
            margin-top: 30px;
            border: 1px solid #ddd;
            /*overflow:hidden;*/
            display: -webkit-box;
        }

        .divLeft {
            /*border:1px solid #ddd;*/
            border-right: 1px solid #ddd;
            width: 190px;
        }

        .divReight {
            padding: 12px;
        }

        .pConn {
            margin-top: 50px;
            font-size: 10px;
        }

        .wangdian {
            width: 150px;
        }

        .address {
            width: 260px;
        }

        .infos {
            width: 300px;
        }

        .hrClass {
            margin-top: 8px;
            margin-bottom: 8px;
        }

        .tdWidth1 {
            width: 212px;
        }

        .tdWidth2 {
            width: 130px;
        }

        .tdWidth3 {
            width: 102px;
        }

        .tdWidth4 {
            width: 273px;
        }

        .tdWidth5 {
            width: 90px;
        }

        .tdWidth5 {
            width: 92px;
        }


        .newModuleDiv {
            display: flex;
            margin-top: 16px;
            min-height: 140px;
            border-radius: 6px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

            .newModuleDiv > div {
                float: left;
                min-height: 140px;
            }

            .newModuleDiv > .newModuleDiv_left {
                width: 20%;
                min-width: 20%;
                position: relative;
                padding: 12px;
                box-sizing: border-box;
                /* padding-top: 10px; */
            }

            .newModuleDiv > .newModuleDiv_right {
                width: 100%;
                box-sizing: border-box;
                border-left: 1px solid #e2e2e2;
            }

        .newModuleDiv_left_taobo {
            width: 100%;
            /* text-align: center; */
        }

        .newModuleDiv_left_btn {
            width: 100%;
            text-align: center;
        }

        .newModuleDiv_left_taobo > span {
            font-size: 13px;
        }


        .newModuleDiv_right_table {
            /* width: 97%; */
            /*min-height: 101px;*/
            /* border: 1px solid #e2e2e2; */
            /* margin: 15px; */
            /*padding: 1px;*/
        }

        .newModuleDiv_right_table_two .right_table_two_title {
            width: 100%;
        }

            .newModuleDiv_right_table_two .right_table_two_title span {
                display: inline-block;
                font-size: 14px;
                height: 33px;
                line-height: 33px;
                border-bottom: 1px solid #e2e2e2;
                font-weight: bold;
                padding-left: 5px;
                background-color: #f5f5f5;
                border-right: 1px solid #e2e2e2;
            }

        .newModuleDiv_right_table_two .right_table_two_content {
            width: 100%;
        }

            .newModuleDiv_right_table_two .right_table_two_content span {
                display: inline-block;
                font-size: 14px;
                height: 33px;
                /*border-bottom: 1px solid #e2e2e2;*/
                padding-left: 5px;
                word-break: break-word;
            }

        .clearfix:before, .clearfix:after {
            content: "";
            display: table;
        }

        .clearfix:after {
            clear: both;
        }

        .clearfix {
            *zoom: 1; /*IE/7/6*/
        }

        .newModuleDiv_right_table tr th, .newModuleDiv_right_table tr td {
            /* border: 1px solid #e2e2e2; */
            font-size: 12px;
        }

        table {
            border-collapse: collapse;
            padding-left: 5px;
        }

        .right_table_one {
            width: 20%;
        }

        .newModuleDiv_right_table tr td {
            padding: 8px;
        }

        .right_table_two {
            width: 40%;
        }

        .right_table_three {
            width: 40%;
        }

        .accountList_wrapper {
            min-width: 1500px;
            margin: 50px 10px;
        }

        .accountList_right {
            width: 80%;
            float: left;
        }

        .accountList_right {
            position: relative;
            padding-bottom: 20px;
        }

            .accountList_right::before {
                content: "";
                display: block;
                position: absolute;
                top: 0;
                left: -11px;
                width: 2px;
                height: 100%;
                background-color: #f0f0f0;
                border-left: 1px solid #ddd;
            }

        .errorBranchcss {
            /* margin: 5px 0px 0px 10px; */
            color: #EA572E;
            font-family: Source Han Sans;
            font-size: 12px;
            font-weight: normal;
        }

        #div_add_waybill_auth div {
            @* padding: 10px; *@
        }


        #div_add_waybill_auth > label {
            padding: 10px;
            display: flex;
            flex-direction: row;
            align-items: center;
            width: 200px;
        }

            #div_add_waybill_auth > label > input {
                margin-right: 3px;
            }

        .layui-mywrap {
            padding: 0;
            margin-top: 75px;
        }

        .layui-this > a {
            color: #3aadff;
        }

        .common_wrapper {
            /* min-width: 1143px;
            margin: 15px;
            background-color: #fff;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
            border-radius: 2px;
            padding: 15px;
            min-height: 50px;
            margin-bottom: 70px; */
            border-top-left-radius: 0;
            border-top-right-radius: 0;
            margin-top: 0;
        }

        .newmy-btn {
            display: flex;
            background-color: #3aadff;
            color: #fff;
            border-radius: 2px;
            cursor: pointer;
            height: 35px;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            padding: 0 15px;
            width: 165px;
        }

            .newmy-btn:hover {
                opacity: 0.8;
            }

        .newModuleDiv-left-title {
            display: flex;
            box-sizing: border-box;
            /* color: #EA572E;
            font-family: Source Han Sans; */
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 12px;
            color: rgba(0, 0, 0, 0.9);
        }
        .newModuleDiv-left-title label{
            flex: 1;
        }
        .newModuleDiv-left-content > div {
            /* padding: 0 10px 10px 10px; */
            margin-bottom: 8px;
        }

        .table-header {
            background: rgba(0, 0, 0, 0.05);
            font-weight: 700;
        }

        .layui-tab-title li {
            padding: 0;
        }

            .layui-tab-title li > a {
                /* padding: 0 15px; */
            }

        .icon-tianjia {
            position: relative;
            top: 2px;
            left: -2px;
        }

        #div_add_waybill_auth {
            /* padding: 15px; */
            box-sizing: border-box;
            height: 100%;
            overflow: hidden;
        }

            #div_add_waybill_auth .addwaybill-title {
                width: 100%;
                display: flex;
                flex-direction: row;
                padding: 6px 8px;
                border-radius: 4px;
                /* 警告色/Warning 1 */
                background: rgba(241, 140, 3, 0.08);
                box-sizing: border-box;
                /* 警告色/Warning 2 */
                border: 0.5px solid rgba(241, 140, 3, 0.2);
                color: rgba(0, 0, 0, 0.9);
            }

            #div_add_waybill_auth .addwaybill-item {
                display: flex;
                    flex-direction: column;
            }

                #div_add_waybill_auth .addwaybill-item .addwaybill-item-left {
                    /* font-size: 16px; */
                    /* font-weight: 700; */
                    @* width: 146px; *@
                    /* color: #999; */
                    position: relative;
                    @* top: 5px; *@
                    margin-top: 16px;
                    margin-bottom: 8px;
                }

                #div_add_waybill_auth .addwaybill-item .addwaybill-item-right {
                    display: flex;
                    flex-direction: row;
                    flex: 1;
                    flex-wrap: wrap;
                    border-radius: 6px;
                    @* background: rgba(0, 0, 0, 0.05);
                    padding: 8px 8px 0 8px; *@
                    justify-content: space-between;
                }

                    #div_add_waybill_auth .addwaybill-item .addwaybill-item-right label {
                        border-radius: 6px;
                        /* 中性色/White 1 */
                        border: 1px solid rgba(0, 0, 0, 0.1);
                        width: 300px;
                        height: 36px;
                        /* 自动布局 */
                        display: flex;
                        align-items: center;
                        padding: 12px;
                        z-index: 0;
                        box-sizing: border-box;
                        margin-bottom: 8px;
                    }

                        #div_add_waybill_auth .addwaybill-item .addwaybill-item-right label > input[type=radio] {
                            margin-right: 3px;
                            width: 16px;
                            height: 16px;
                        }

                        #div_add_waybill_auth .addwaybill-item .addwaybill-item-right label .waybill-platform-wrap {
                            @* border: 1px solid #e2e2e2; *@
                            border-radius: 6px;
                            @* width: 170px; *@
                            height: 32px;
                            display: flex;
                            align-items: center;
                            @* padding-left: 10px; *@
                            box-sizing: border-box;
                            cursor: pointer;
                            width: 170px;
                            margin-left: 5px;
                            color: rgba(0, 0, 0, 0.9);
                        }

                            #div_add_waybill_auth .addwaybill-item .addwaybill-item-right label .waybill-platform-wrap .pintaiIcon {
                                margin-right: 5px;
                                top: 0;
                            }

                    #div_add_waybill_auth .addwaybill-item .addwaybill-item-right > label .waybill-platform-wrapNew {
                        border: unset;
                        background-color: #3aadff;
                        border-radius: 4px;
                        color: #fff;
                        justify-content: center;
                        padding-left: 0;
                        width: 190px;
                    }

                        #div_add_waybill_auth .addwaybill-item .addwaybill-item-right > label .waybill-platform-wrapNew:hover {
                            opacity: 0.8;
                        }

                    #div_add_waybill_auth .addwaybill-item .addwaybill-item-right.class {
                        flex-direction: column;
                    }

        /*                        #div_add_waybill_auth .addwaybill-item .addwaybill-item-right.class > label {
                            width: 320px;
                        }

                            #div_add_waybill_auth .addwaybill-item .addwaybill-item-right.class > label .waybill-platform-wrap {
                                width: 320px;
                            }*/






        .togglePlatform {
            width: 100%;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }

            .togglePlatform .togglePlatform-title {
                padding: 10px;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                font-weight: 700;
                font-size: 20px;
                color: #333;
                border-bottom: 1px solid #e2e2e2;
                padding-bottom: 30px;
                margin-bottom: 10px;
            }

            .togglePlatform .togglePlatform-content {
                display: flex;
                flex-direction: row;
                justify-content: space-around;
                align-items: center;
                margin-top: 10px;
                padding: 0 20px;
            }

                .togglePlatform .togglePlatform-content .togglePlatform-content-item {
                    padding: 15px;
                    flex-direction: column;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    border: 1px solid #fff;
                    border-radius: 7px;
                }

                    .togglePlatform .togglePlatform-content .togglePlatform-content-item:hover {
                        box-shadow: 0 0 12px 1px #90d1ff;
                    }

                    .togglePlatform .togglePlatform-content .togglePlatform-content-item.active {
                        box-shadow: 0 0 12px 1px #90d1ff;
                    }

                        .togglePlatform .togglePlatform-content .togglePlatform-content-item.active .togglePlatform-content-item-title {
                            color: #f69906;
                        }

                    .togglePlatform .togglePlatform-content .togglePlatform-content-item .togglePlatform-content-item-img {
                        width: 140px;
                        height: 140px;
                        background-image: url('/Content/images/toutiao-platform.png');
                        background-position: 0 0;
                        display: inline-block;
                    }
                    .togglePlatform .togglePlatform-content .togglePlatform-content-item .togglePlatform-content-item-saleShop-image {
                        width: 140px;
                        height: 140px;
                        background: url('/Content/images/touTiaoSaleShopPlatform.png') no-repeat;
                        background-size: 100% 100%;
                        display: inline-block;
                        border-radius: 10px;
                    }

        .togglePlatform .togglePlatform-content .togglePlatform-content-item:nth-child(2) .togglePlatform-content-item-img {
            background-position: -140px 0;
        }

                    .togglePlatform .togglePlatform-content .togglePlatform-content-item .togglePlatform-content-item-title {
                        font-size: 16px;
                        margin-top: 20px;
                    }

        .support-platform-into {
            display: none;
        }

        .newAddwaybill {
            @* flex-direction: column !important; *@
        }

            .newAddwaybill .newAddwaybillWrap {
                display: flex;
                flex-direction: row;
                padding: 0 !important;
                align-items: center;
            }

                .newAddwaybill .newAddwaybillWrap a {
                    font-size: 14px;
                }

                    .newAddwaybill .newAddwaybillWrap a .iconfont {
                        font-size: 14px;
                    }

                    .newAddwaybill .newAddwaybillWrap a span {
                        position: relative;
                        left: -3px;
                    }
        .printSetNavWrape {
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
        }
        .togglePlatformWrap{
            width: 100% !important;
        }
        .wu-tableWrap .wu-table-one tbody tr td{
            border-right: 1px solid #e2e2e2;
        }
        .wu-pintaiIcon.wu-mid.taobao {
            background-position: -80px -348px !important;
        }
        .weightDailog.wu-dailog .layui-layer-content{
            padding: 20px 20px 20px 55px;
        }
        .wu-pintaiIcon.wu-small.TouTiaoSaleShop {
            background-position: -2px -350px !important;
        }
    </style>
}

@section scripts{

    <script>
        var IsWxVideoOldUser = "@ViewBag.IsWxVideoOldUser" == "true" ? true : false;
        //IsWxVideoOldUser = true;

        var taobaoAuthPriUrl = "";
        var taobaoAuthUrl = "";

        var cainiaoAuthPriUrl = "";
        var cainiaoAuthUrl = "";

        var pinDuoDuoAuthPriUrl = "";
        var pinDuoDuoAuthUrl = "";

        var pinDuoDuoDdAuthPriUrl = "";
        var pinDuoDuoDdAuthUrl = "";

        var toutiaoAuthPriUrl = "";
        var toutiaoAuthUrl = "";

        var kuaishouAuthPriUrl = "";
        var kuaishouAuthUrl = "";

        var xiaohongshuAuthPriUrl = "";
        var xiaohongshuAuthUrl = "";

        var wxvideoAuthPriUrl = "";
        var wxvideoAuthUrl = "";
        var toutiaoSaleShopAuthUrl = "";

        var masterToken = "";
        var token = "";
        var jdAuthUrl ="";
        var allShops = [];

        var hasPermission =  @Html.Raw(ViewBag.ViewPermission ?? "false");
        function OpenWin_ChoseWaybillPlatform(taobaoAuthUrl, cainiaoAuthUrl, pddAuthUrl, pddDbAuthUrl, toutiaoUrl, jdAuthUrl, kuaiShouAuthUrl, xiaohongshuAuthUrl, wxvideoAuthUrl, toutiaoSaleShopAuthUrl, masterShopToken) {
            layer.open({
                type: 1,
                title: "添加电子面单授权",
                content: $('#div_add_waybill_auth'),
                area: ['640px', 'auto'], //宽高
                btn: ['添加', '取消'],
                skin: 'wu-dailog',
                yes: function () {
                    var platformType = $(":radio[name='waybill_platform']:checked").val();
                    if (!platformType) {
                        layer.msg('请选择电子面单类型');
                        return false;
                    }
                    // 抖音即时零售电子面单
                    if (platformType.toLowerCase() == "saleshop") {
                        console.log('抖音即时零售电子面单', toutiaoSaleShopAuthUrl);
                        commonModule.OpenNewTab(toutiaoSaleShopAuthUrl);
                    }
                    if (platformType.toLowerCase() == "top") {
                        //window.location.href = (taobaoAuthUrl);
                        commonModule.OpenNewTab(taobaoAuthUrl);
                    }
                    else if (platformType.toLowerCase() == "link") {
                        //window.location.href = (cainiaoAuthUrl);
                        commonModule.OpenNewTab(cainiaoAuthUrl);
                    }
                    else if (platformType.toLowerCase() == "pdd") {
                        //window.location.href = (pddAuthUrl);
                        commonModule.OpenNewTab(pddAuthUrl);
                    }
                    else if (platformType.toLowerCase() == "pddddsys") {
                        //window.location.href = (pddDbAuthUrl);
                        commonModule.OpenNewTab(pddDbAuthUrl);
                    }
                    else if (platformType.toLowerCase() == "jd") {
                        //window.location.href = (jdAuthUrl);
                        commonModule.OpenNewTab(jdAuthUrl);
                    }
                    else if (platformType.toLowerCase() == "pddwaybill") {
                        //var redirect_url = window.location.protocol + "//" + window.location.host + "/auth/AuthSuccess" + location.search;
                        //var redirect_url = window.location.protocol + "//auth.dgjapp.com/auth/AuthSuccess" + location.search;
                        var redirect_url = "http://auth.dgjapp.com/auth/AuthSuccess" + location.search;
                        var url = '@DianGuanJiaApp.Utility.CustomerConfig.AuthCallbackUrl.TrimEnd('/')' + '/auth/pddwaybill?redirect_uri=' + redirect_url;
                        if (masterShopToken)
                            url += '&mastertoken=' + masterShopToken;
                        else
                            url += '&mastertoken=';
                        //debugger;
                        //window.location.href = url;
                        commonModule.OpenNewTab(url);
                    }
                    //else if (platformType.toLowerCase() == "toutiao") {
                    //    commonModule.OpenNewTab(toutiaoUrl);
                    //}
                    //    //抖店非店铺电子面单（旧）
                    //else if (platformType.toLowerCase() == "toutiaowaybill") {
                    //    toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyinfx?", "/auth/douyin?");
                    //    toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyinfxnew?", "/auth/douyin?");
                    //    commonModule.OpenNewTab(toutiaoUrl + "&ebill=1");
                    //}
                    ////抖店非店铺电子面单（新）
                    //else if (platformType.toLowerCase() == "toutiaowaybillv2") {
                    //    toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyin?", "/auth/douyinfx?");
                    //    toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyinfxnew?", "/auth/douyinfx?");
                    //    commonModule.OpenNewTab(toutiaoUrl + "&ebill=1");
                    //} else if (platformType.toLowerCase() == "toutiaowaybillv3") {
                    //    toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyin?", "/auth/douyinfxnew?");
                    //    toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyinfx?", "/auth/douyinfxnew?");
                    //    commonModule.OpenNewTab(toutiaoUrl + "&ebill=1");
                    //}
                    else if (platformType.toLowerCase() == "kuaishou") {
                        commonModule.OpenNewTab(kuaiShouAuthUrl);
                    }
                    else if (platformType.toLowerCase() == "xiaohongshu") {
                        commonModule.OpenNewTab(xiaohongshuAuthUrl + "&Ebill=0");
                    }
                    else if (platformType.toLowerCase() == "xhswaybill") {
                        commonModule.OpenNewTab(xiaohongshuAuthUrl + "&Ebill=1");
                    }
                    else if (platformType.toLowerCase() == "wxvideo") {
                        commonModule.OpenNewTab('https://channels.weixin.qq.com/shop/servicemarket/myServices?status=2');
                        //commonModule.OpenNewTab('https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/LGSfFz0ZhVQAQMSt');
                        //layer.msg('视频号面单暂不支持跨店铺、跨平台使用，从店铺后台进入系统后会自动绑定关系');
                        return;
                    }
                    else if (platformType.toLowerCase() == "wxxiaodian") {

                        if (!IsWxVideoOldUser) {
                            commonModule.OpenNewTab('https://store.weixin.qq.com/shop/servicemarket/myServices?status=2');
                            //commonModule.OpenNewTab('https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/LGSfFz0ZhVQAQMSt');
                            //layer.msg('视频号面单暂不支持跨店铺、跨平台使用，从店铺后台进入系统后会自动绑定关系');
                            return;
                        }
                    }

                    var isAddOk = layer.confirm('<div class="wu-f14 wu-c09">是否已经成功授权电子面单？</div>', { icon: 3, title: '授权结果确认',skin: 'weightDailog wu-dailog', btn: ['确定', '取消'] },
                        function () {
                            window.location.reload();
                        }, function () {
                            //window.location.reload();
                        }
                    );
                }
            });
        }
        function AddtaobaoAcount() {
            //layer.alert("暂时不可用！", { icon: 2 });
            var domain = window.location.protocol + "//" + window.location.host;
            var url = domain;
            jdAuthUrl = jdAuthUrl + "?state=" + encodeURI(JSON.stringify({ "Url": url, "AType": "Waybill", "Token": token }));
            jdAuthPriUrl = jdAuthUrl + "?state=" + encodeURI(JSON.stringify({ "Url": url, "Token": token, "AType": "Waybill", "MToken": masterToken }));
            OpenWin_ChoseWaybillPlatform(
                taobaoAuthUrl,
                cainiaoAuthUrl,
                pinDuoDuoAuthUrl,
                pinDuoDuoDdAuthUrl,
                toutiaoAuthUrl,
                jdAuthUrl,
                kuaishouAuthUrl,
                xiaohongshuAuthUrl,
                wxvideoAuthUrl,
                toutiaoSaleShopAuthUrl
            );
        }
        function GetQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }
        // 重新授权
        function ReAuthAccount(appKey, isShopWaybill, authurl) {
            var authurl = authurl || "";
            var token = GetQueryString("token");
            commonModule.OpenNewTab(authurl + "&rp=" + token);

            var isAddOk = layer.confirm('<div class="wu-f14 wu-c09">是否已经成功授权电子面单？</div>', { icon: 3, title: '授权结果确认',skin: 'weightDailog wu-dailog', btn: ['确定', '取消'] },
                function () {
                    window.location.reload();
                }, function () {
                    window.location.reload();
                }
            );
        }
        function RelieveAccount(cainiaoAuthId, authSourceType, shopId, userId, isXHS) {

            var confirmContent = isXHS ? '<div style="font-size:16px;">确认要解除绑定?<div style="font-size:14px;">温馨提示：新旧小红书电子面单会同时解绑！</div></div>' : '<div style="">确认要解除绑定?</div>';

            var relieveA = layer.confirm(confirmContent, {icon: 3, title: '解除绑定',skin: 'weightDailog wu-dailog' }, function () {
                var datas = {
                    cainiaoAuthId: cainiaoAuthId,
                    authSourceType: authSourceType,
                    shopId: shopId,
                }
                commonModule.ajax({
                    type: 'post',
                    url: '/AccountList/Relieve',
                    data: datas,
                    success: function (data) {
                        if (data == "1") {
                            //$("#div_" + userId).remove();
                            $("#account-detail-list .newModuleDiv[data-userid='"+userId+"']").remove();
                            layer.msg("成功", { icon: 1 });
                        } else
                            layer.msg("失败", { icon: 2 });
                    }
                });

                layer.close(relieveA);
            });

        }


        function GotoWxVideoAuthAccount() {

            layer.open({
                type: 1,
                title: false,
                content: $("#support_platform_WxVideoDailog"),
                area: '620px',
                btn: false,
                skin: 'adialog-Shops-skin',
                success: function () {
                    $$.navActive("#togglePlatform_content_WxVideo", function (i, item) {

                        var wxVideoStatus = $(item).attr("data-status") == "old" ? false : true;
                        var $content = $("#new_support_platform_wxvinto");
                        $("#new_support_platform_" + platform + ">input[name='shopId']").val("");

                    });
                },
            });
        }



        var loadList = function () {

            var accId = commonModule.GetQueryString("accId") || 0;

            commonModule.Ajax({
                url: "/AccountList/LoadList",
                type: "POST",
                loadingMessage: "正在加载电子面单账号信息…",
                success: function (rsp) {
                    console.log("电子面单账号信息",rsp);
                    if (rsp.Success) {
                        var data = rsp.Data || [];
                        allShops = data.AllShops || [];
                        token = data.Token || "";
                        masterToken = data.MasterToken || "";
                        taobaoAuthPriUrl = data.TaobaoAuthPriUrl || "";
                        taobaoAuthUrl = data.TaobaoAuthUrl || "";
                        cainiaoAuthPriUrl = data.CaiNiaoAuthPriUrl || "";
                        cainiaoAuthUrl = data.CaiNiaoAuthUrl || "";
                        pinDuoDuoAuthPriUrl = data.PinduoduoAuthPriUrl || "";
                        pinDuoDuoAuthUrl = data.PinduoduoAuthUrl || "";
                        pinDuoDuoDdAuthPriUrl = data.PinduoduoDdAuthPriUrl || "";
                        pinDuoDuoDdAuthUrl = data.PinduoduoDdAuthUrl || "";
                        toutiaoAuthPriUrl = data.ToutiaoAuthPriUrl || "";
                        toutiaoAuthUrl = data.ToutiaoAuthUrl || "";
                        jdAuthUrl = data.JdAuthUrl || "";
                        kuaishouAuthPriUrl = data.KuaiShouAuthPriUrl || "";
                        kuaishouAuthUrl = data.KuaiShouAuthUrl || "";
                        xiaohongshuAuthPriUrl = data.XiaoHongShuAuthPriUrl || "";
                        xiaohongshuAuthUrl = data.XiaoHongShuAuthUrl || "";
                        wxvideoAuthPriUrl = data.WxVideoAuthPriUrl || "";
                        wxvideoAuthUrl = data.WxVideoAuthUrl || "";
                        toutiaoSaleShopAuthUrl = data.ToutiaoSaleShopAuthUrl || "";

                        var accountList = data.AccountList || [];
                        var detailTmpl = $.templates("#accountlist-detail-tmpl");
                        var html = detailTmpl.render(accountList);
                        $("#account-detail-list").html(html);
                        // 头条触发重新授权按钮
                        if (accId > 0) {
                            var sourcetype = commonModule.GetQueryString("sourcetype") || -1;
                            var stype = $(".account-" + accId).attr("data-stype");
                            if (sourcetype == stype) {
                                setTimeout(function () {
                                    $(".account-" + accId).click();
                                    var href = window.location.href.split("&")[0];
                                    window.location.href = href;
                                },500);
                            }
                        }
                    }
                    else {
                        layer.msg(rsp.Message);
                    }
                }
            });
        }
        $(function () {
            if (hasPermission == true) {
                loadList();
            }

        });
    </script>
}
@{Html.RenderPartial("~/Views/Common/printSetNav.cshtml");}
<div class="common_wrapper wu-container showPage" style="">
    <div class="common_right">
        <span class="wu-btn wu-btn-mid" onclick="AddtaobaoAcount()">添加电子面单账号授权</span>
        <div id="account-detail-list">

        </div>
        @*<span class="newmy-btn" onclick="AddtaobaoAcount()"><i class="iconfont icon-tianjia"></i>添加电子面单账号授权</span>
            @foreach (var account in Model.AccountList)
            {
                <div class="newModuleDiv  clearfix" id="<EMAIL>">
                    <div class="newModuleDiv_left">
                        <div class="newModuleDiv_left_taobo newModuleDiv-left-title">
                            @if (account.Types?.ToLower() == "taobao")
                            {
                                <label>淘宝电子面单</label>
                            }
                            else if (account.Types?.ToLower() == "pinduoduo")
                            {
                                <label>拼多多电子面单</label>
                            }
                            else if (account.Types?.ToLower() == "link")
                            {
                                <label>菜鸟电子面单</label>
                            }
                            else if (account.Types?.ToLower() == "jingdong")
                            {
                                <label>京东无界电子面单</label>
                            }
                            else if (account.Types?.ToLower() == "pddwaybill")
                            {
                                <label>拼多多电子面单(非店铺)</label>
                            }
                            else if (account.Types?.ToLower() == "toutiao")
                            {
                                <label>抖音电子面单</label>
                            }
                        </div>
                        <div class="newModuleDiv-left-content">
                            <div>
                                <span>电子面单账户:</span>
                                <span style="color:#04385d">@account.UserName</span>
                            </div>
                            <div>
                                <span>授权给店铺:</span>
                                <span style="color:#04385d">@Model.AllShops.FirstOrDefault(f => f.Id == account.ShopId).NickName</span>
                            </div>
                            <div>
                                <span class="layui-btn layui-btn-danger layui-btn-xs" onclick="RelieveAccount(@account.CaiNiaoAuthInfoId,@account.AuthSourceType,@account.ShopId,'@account.UserId')">解除绑定</span>
                            </div>
                        </div>
                    </div>
                    <div class="newModuleDiv_right">
                        @if (account.BranchAddressList.Count == 0)
                        {
                            if (account.Types?.ToLower() == "jingdong")
                            {
                                <div class="errorBranchcss">
                                    <span>获取网点失败：</span><br />
                                    <span>1.若该账号用于京东快递，请忽略该错误，不影响使用。</span><br />
                                    <span>2.若该账号是京东无界电子面单账号或京东店铺账号，请重试授权。</span><br />
                                    <span>如果还有问题请联系下客服。</span>
                                </div>
                            }
                            else
                            {
                                <div class="errorBranchcss">
                                    <span>获取网点失败。请检查：</span><br />
                                    <span>1.去商家后台看看电子面单账号“@account.UserName”是否有开通网点。</span><br />
                                    <span>2.点击左上角"添加电子面单账号授权" 重新登录授权。</span><br />
                                    <span>如果还没有显示请联系下客服。</span>
                                </div>
                            }
                        }

                        @foreach (var branchList in account.BranchAddressList)
                        {
                            <table class="newModuleDiv_right_table">
                                <tr>
                                    <td class="right_table_one" colspan="3">快递:@branchList.CompanyName</td>
                                </tr>
                                <tr class="table-header">
                                    <td class="right_table_one">网点名称</td>
                                    <td class="right_table_two">发货地址</td>
                                    <td class="right_table_three">账号信息</td>
                                </tr>
                                @foreach (var branch in branchList.BranchAddress)
                                {
                            <tr>
                                @if (account.Types?.ToLower() == "taobao" && branch.CpCode == "SF")
                                {
                                    if (branch.BrandCode == "FW")
                                    {
                                    <td class="right_table_one">@branch.BranchName<span>(品牌丰网)</span></td>
                                    }
                                    else if (branch.BrandCode == "FOP")
                                    {
                                        <td class="right_table_one">@branch.BranchName<span>(品牌SFKY)</span></td>
                                    }
                                    else
                                    {
                                        <td class="right_table_one">@branch.BranchName<span>(品牌顺丰)</span></td>
                                    }
                                }
                                else
                                {
                                    <td class="right_table_one">@branch.BranchName</td>
                                }
                                <td class="right_table_two">@branch.Province @branch.City @branch.Area @branch.Detail</td>
                                @if (branch.CpType == 1 || branch.CpType == 4)
                                {
                                    <td class="right_table_three">可用单数:直营(无限可用单号)   已用单数:@branch.PrintQuantity 回收单数:@branch.CancelQuantity</td>
                                }
                                else
                                {
                                    <td class="right_table_three">可用单数:@branch.Quantity   已用单数:@branch.PrintQuantity 回收单数:@branch.CancelQuantity</td>
                                }
                            </tr>
                                }

                            </table>
                        }

                    </div>
                </div>

            }*@
    </div>
</div>
<div class="minSpace"></div>
<div class="topSpace"></div>

<div id="div_add_waybill_auth" style="display:none;">
    <div class="addwaybill-title"><i style="color: #f18c03;margin-right: 4px;" class="iconfont icon-a-error-circle-filled1x"></i><span class="wu-alert-title">请根据你打印的订单平台选择添加对应电子面单类型</span></div>
    <div class="addwaybill-item" style="padding-bottom:0;">
        <div class="addwaybill-item-left wu-f14 wu-c09">店铺类型面单</div>
        <div class="addwaybill-item-right">
            <label><input type="radio" name="waybill_platform" value="toutiao" onclick="douyinLayer('shop')" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small TouTiao"></span>抖音电子面单</span></label>
            <label><input type="radio" name="waybill_platform" value="SaleShop" onclick="douyinLayer('TouTiaoSaleShop')" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small TouTiao"></span>抖音即时零售电子面单</span></label>
            <label><input type="radio" name="waybill_platform" value="Pdd" onclick="pingduoduoLayer()" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small Pinduoduo"></span>拼多多电子面单</span></label>
            <label style="display:none;"><input type="radio" name="waybill_platform" value="PddDdSys" /><span class="waybill-platform-wrap Pinduoduo"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small Pinduoduo"></span>拼多多电子面单(打单系统)</span></label>
            <label><input type="radio" name="waybill_platform" value="Top" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small Taobao"></span>1688/淘宝菜鸟电子面单</span></label>
            <label><input type="radio" name="waybill_platform" value="kuaishou" onclick="kuaishouLayer()" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small KuaiShou"></span>快手电子面单</span></label>
            <label><input type="radio" name="waybill_platform" value="Jd" onclick="jingdongLayer()" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small Jingdong"></span>京东无界电子面单</span></label>
            <label><input type="radio" name="waybill_platform" value="Link" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small Link"></span>菜鸟link电子面单</span></label>
            @*<label><input type="radio" name="waybill_platform" /><span class="waybill-platform-wrap"><i class="pintaiIcon" style="background-position: -325px -482px !important;"></i>唯品会电子面单</span></label>*@
            <label><input type="radio" name="waybill_platform" value="xiaohongshu" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small XiaoHongShu"></span>小红书电子面单</span></label>
            <label><input type="radio" name="waybill_platform" value="wxvideo" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small WxVideo"></span>视频号电子面单</span></label>
            @* <label><input type="radio" name="waybill_platform" value="wxxiaodian" onclick="wxvideoLayer()" /><span class="waybill-platform-wrap"><i class="pintaiIcon WxVideo"></i>微信小店电子面单</span></label> *@
            <label style='margin-bottom: 0;'><input type="radio" name="waybill_platform" value="wxxiaodian" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small WxVideo"></span>微信小店电子面单</span></label>
        </div>
    </div>
    <div class="addwaybill-item" style="padding-top:0;padding-bottom:0">
        <div class="addwaybill-item-left wu-f14 wu-c09">
            非店铺类型面单
            <a href="https://docs.qq.com/doc/DQkhPWnlYWk1ZeUZT" style="color: #0888FF;margin-left: 13px;" target="_blank" class="wu-color-a wu-operate wu-f14">
                <i style="color: #0888FF;" class="iconfont icon-a-help-circle1x"></i>
                <span style="font-size: 14px;">厂家开通非店铺面单教程</span>
            </a>
        </div>
        <div class="newAddwaybill addwaybill-item-right @ViewBag.ShowOldToutiaoWaybillLinkSylte">
            @*@{
                    if (ViewBag.isShowOldToutiaoWaybillLink == true)
                    {
                        <div style="position: relative;top: -5px;"><img src="~/Content/images/noviceIntroPic/waybill-icon.gif" style="width:550px" alt="Alternate Text" /></div>
                        <label><input type="radio" name="waybill_platform" value="toutiaoWaybill" /><span class="waybill-platform-wrap"><i class="pintaiIcon TouTiao"></i>抖音非店铺面单(店管家打单付费授权入口)</span></label>
                        <label><input type="radio" name="waybill_platform" value="toutiaoWaybillV2" /><span class="waybill-platform-wrap"><i class="pintaiIcon TouTiao"></i>抖音非店铺面单(店管家铺货订购授权入口)</span></label>
                        <label><input type="radio" name="waybill_platform" value="toutiaoWaybillV3" /><span class="waybill-platform-wrap"><i class="pintaiIcon TouTiao"></i>抖音非店铺面单(店管家分销付费授权入口)</span></label>
                    }
                    else
                    {
                        <label><input type="radio" name="waybill_platform" value="toutiaoWaybillV3" /><span class="waybill-platform-wrap"><i class="pintaiIcon TouTiao"></i>抖音非店铺面单</span></label>
                    }
                }*@

            <div class="newAddwaybillWrap">
                <label><input type="radio" name="waybill_platform" value="toutiaoWaybillV3" onclick="douyinLayer('noshop')" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small TouTiao"></span>抖音非店铺面单</span></label>

            </div>
            <label><input type="radio" name="waybill_platform" value="PddWaybill" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small  Pinduoduo"></span>拼多多非店铺面单</span></label>
            <label><input type="radio" name="waybill_platform" value="XhsWaybill" /><span class="waybill-platform-wrap"><span class="wu-mR4 wu-hover wu-pintaiIcon wu-small  XiaoHongShu"></span>小红书非店铺面单</span></label>
        </div>
    </div>

    @*<label><input type="radio" name="waybill_platform" value="Top" />淘宝菜鸟电子面单</label>
        <label><input type="radio" name="waybill_platform" value="Pdd" />拼多多电子面单</label>
        <label style="display:none;"><input type="radio" name="waybill_platform" value="PddDdSys" />拼多多电子面单(打单系统)</label>
        <label><input type="radio" name="waybill_platform" value="PddWaybill" />拼多多电子面单(非店铺)</label><br />
        <label><input type="radio" name="waybill_platform" value="Jd" />京东无界电子面单</label>
        <label><input type="radio" name="waybill_platform" value="Link" />菜鸟link电子面单</label>
        <label><input type="radio" name="waybill_platform" value="toutiao" />抖音电子面单</label>
        <label><input type="radio" name="waybill_platform" value="kuaishou" />快手电子面单</label>
        <label style="display:none;"><input type="radio" name="waybill_platform" value="toutiaoWaybill" />抖音电子面单(非店铺)</label>*@


</div>

<!--抖店即时零售-->
<div class="support-platform-into" id="support_platform_TouTiaoSaleShop">
    <div class="togglePlatform" style="padding: 0;">
        <ul class="togglePlatform-content" id="togglePlatform_touTiaoSaleShop_content" style="margin-top: 0;padding: 0;">
            <li class="togglePlatform-content-item" data-status="touTiaoSaleShop">
                <span class="togglePlatform-content-item-saleShop-image"></span>
                <span class="togglePlatform-content-item-title">店管家分销代发</span>
            </li>
        </ul>
    </div>
</div>

<div class="support-platform-into" id="support_platform_toutiao">

    @*<div><span>请选择店铺已订购的应用类型</span></div>
        <div><img src="" alt="店管家分铺货代发(旧)" onclick="partnerModule.imgToutiao('old')" /><span>店管家分铺货代发(旧)</span></div>
        <div><img src="" alt="店管家分销货代发(新)" onclick="partnerModule.imgToutiao('new')" /><span>店管家分销货代发(新)</span></div>*@

    <div class="togglePlatform" style="padding: 0;">
        @* <div class="togglePlatform-title">请选择店铺已订购的应用类型</div> *@
        <ul class="togglePlatform-content" id="togglePlatform_content" style="margin-top: 0;padding: 0;">
            @*<li class="togglePlatform-content-item" data-status="shop_goods">
                    <span class="togglePlatform-content-item-img"></span>
                    <span class="togglePlatform-content-item-title">店管家分铺货代发(旧)</span>
                </li>*@
            <li class="togglePlatform-content-item" data-status="shop_fx">
                <span class="togglePlatform-content-item-img" style="background-position: -140px 0;"></span>
                <span class="togglePlatform-content-item-title">店管家分销代发(新)</span>
            </li>
        </ul>
    </div>

</div>

<div class="support-platform-into" id="support_platform_toutiao_noshop">

    @*<div><span>请选择店铺已订购的应用类型</span></div>
        <div><img src="" alt="店管家分铺货代发(旧)" onclick="partnerModule.imgToutiao('old')" /><span>店管家分铺货代发(旧)</span></div>
        <div><img src="" alt="店管家分销货代发(新)" onclick="partnerModule.imgToutiao('new')" /><span>店管家分销货代发(新)</span></div>*@

    <div class="togglePlatform" style="padding: 0;">
        @* <div class="togglePlatform-title">请选择店铺已订购的应用类型</div> *@
        <ul class="togglePlatform-content" id="togglePlatform_content_noshop" style="margin-top: 0;padding: 0;">
            <li class="togglePlatform-content-item" data-status="noshop_fx">
                <span class="togglePlatform-content-item-img" style="background-position: -140px 0;"></span>
                <span class="togglePlatform-content-item-title">店管家分销付费授权入口</span>
            </li>
            @*<li class="togglePlatform-content-item" data-status="noshop_print">
                    <span class="togglePlatform-content-item-img" style="background-position: -280px 0;"></span>
                    <span class="togglePlatform-content-item-title">店管家打单付费授权入口</span>
                </li>
                <li class="togglePlatform-content-item" data-status="noshop_goods">
                    <span class="togglePlatform-content-item-img" style="background-position: 0 0;"></span>
                    <span class="togglePlatform-content-item-title">店管家铺货订购授权入口</span>
                </li>*@
        </ul>
    </div>

</div>

<div class="support-platform-into" id="support_platform_kuaishou">

    <div class="togglePlatform" style="padding: 0;">
        @* <div class="togglePlatform-title">请选择店铺已订购的应用类型</div> *@
        <ul class="togglePlatform-content" id="togglePlatform_content_kuaishou" style="margin-top: 0;padding: 0;">
            <li class="togglePlatform-content-item" data-status="kuaishou">
                <span class="togglePlatform-content-item-img" style="background-position: -420px 0;"></span>
                <span class="togglePlatform-content-item-title" style="display: flex; flex-direction: column; align-items: center; margin-top: 10px;">
                    <i>店管家_分销管理</i>
                    <i>(旧)</i>
                </span>
            </li>
            <li class="togglePlatform-content-item" data-status="kuaishouv2">
                <span class="togglePlatform-content-item-img" style="background-position: -140px 0;"></span>
                <span class="togglePlatform-content-item-title" style="display: flex; flex-direction: column; align-items: center; margin-top: 10px;">
                    <i>店管家_分销代发</i>
                    <i>(新)</i>
                </span>
            </li>
        </ul>
    </div>

</div>

<div class="support-platform-into" id="support_platform_jingdong">
    <div class="togglePlatform" style="padding: 0;">
        @* <div class="togglePlatform-title">请选择店铺已订购的应用类型</div> *@
        <ul class="togglePlatform-content" id="togglePlatform_content_jingdong" style="margin-top: 0;padding: 0;">
            <li class="togglePlatform-content-item" data-status="dd">
                <span class="togglePlatform-content-item-img" style="background-position: -280px 0;"></span>
                <span class="togglePlatform-content-item-title" style="display: flex; flex-direction: column; align-items: center; margin-top: 10px;">
                    <i>店管家打单</i>
                </span>
            </li>
            <li class="togglePlatform-content-item" data-status="fx">
                <span class="togglePlatform-content-item-img" style="background-position: -140px 0;"></span>
                <span class="togglePlatform-content-item-title" style="display: flex; flex-direction: column; align-items: center; margin-top: 10px;">
                    <i>店管家_分销代发</i>
                </span>
            </li>
        </ul>
    </div>
</div>

<div class="support-platform-into" id="support_platform_WxVideoDailog" style="width:620px;">
    <div class="togglePlatform">
        <div class="togglePlatform-title">请选择店铺已订购的应用类型</div>
        <ul class="togglePlatform-content" id="support_platform_WxVideoContent">
            <li class="togglePlatform-content-item" data-status="old">
                <span class="togglePlatform-content-item-img" style="background-image: url(/Content/images/fxdf-platform.png);width:210px;height:140px"></span>
                <span class="togglePlatform-content-item-title">中恒分销代发_订单代发</span>
            </li>
            <li class="togglePlatform-content-item" data-status="new">
                <span class="togglePlatform-content-item-img"></span>
                <span class="togglePlatform-content-item-title">店管家_分销代发</span>
            </li>
        </ul>
    </div>
</div>

<script id="accountlist-detail-tmpl" type="text/x-jsrender">
    {^{for }}
    <div class="newModuleDiv clearfix" id="div_{{:UserId}}" data-userid="{{:UserId}}">
        <div class="newModuleDiv_left">
            <div class="newModuleDiv_left_taobo newModuleDiv-left-title" data-type="{{:Types}}">
                <span title="{{:~getPlatformTitle(Types)}}" class="wu-mR4 wu-hover wu-pintaiIcon wu-mid {{:Types == 'PddWaybill'? 'Pinduoduo' : Types}}"></span>
                {{if ~MyFormat(Types,"tolower") == "taobao"}}
                <label>淘宝电子面单</label>
                {{else ~MyFormat(Types,"tolower") == "pinduoduo"}}
                <label>拼多多电子面单</label>
                {{else ~MyFormat(Types,"tolower") == "link"}}
                <label>菜鸟电子面单</label>
                {{else ~MyFormat(Types,"tolower") == "jingdong"}}
                <label>京东无界电子面单</label>
                {{else ~MyFormat(Types,"tolower") == "pddwaybill"}}
                <label>拼多多电子面单(非店铺)</label>
                {{else ~MyFormat(Types,"tolower") == "toutiao"}}
                {{if IsEbillWaybill==true}}
                <label>抖音电子面单(非店铺)</label>
                {{else}}
                <label>抖音电子面单</label>
                {{/if}}
                {{else ~MyFormat(Types,"tolower") == "toutiaosaleshop"}}
                <label>抖音即时零售电子面单</label>
                {{else ~MyFormat(Types,"tolower") == "kuaishou"}}
                <label>快手电子面单</label>
                {{else ~MyFormat(Types,"tolower") == "xiaohongshu"}}
                {{if IsEbillWaybill==true}}
                <label>{{:BillVersion==2?"新版-":"旧版-"}}小红书电子面单(非店铺)</label>
                {{else}}
                <label>{{:BillVersion==2?"新版-":"旧版-"}}小红书电子面单</label>
                {{/if}}
                {{else ~MyFormat(Types,"tolower") == "wxvideo"}}
                <label>视频号(微信小店)电子面单</label>
                {{/if}}
            </div>
            <div class="newModuleDiv-left-content wu-f12 wu-c09">
                <div data-appkey="{{:AppKey}}" style="display: flex;">
                    <span class="wu-c07" style="min-width: 72px; display: inline-block;">电子面单账户</span>
                    <span style="flex: 1; margin-left: 4px;word-break: break-all;">
                        {{:UserName}}{{if ~MyFormat(Types,"tolower") == "toutiaosaleshop"}}<i>(即时零售)</i>{{/if}}
                    </span>
                </div>
                <div style="display: flex;">
                    <span class="wu-c07" style="min-width: 72px; display: inline-block;">授权给店铺</span>
                    <span style="flex: 1; margin-left: 4px;">{{:~getShopName(ShopId)}}</span>
                </div>
                {{if  ~MyFormat(Types,"tolower") == "toutiao"}}
                <div style="display: flex;">
                    <span class="wu-c07" style="min-width: 72px; display: inline-block;">授权应用</span>
                    <span style="flex: 1; margin-left: 4px;">{{:AppName}}</span>
                </div>
                <div style="display: flex;">
                    <span class="wu-c07" style="min-width: 72px; display: inline-block;">服务到期时间</span>
                    <span style="flex: 1; margin-left: 4px;">{{:ServiceEndDate}}</span>
                </div>
                <div class="wu-pT4">
                    <span class="wu-btn wu-btn-small" account-{{:CaiNiaoAuthInfoId}}" data-accId="{{:CaiNiaoAuthInfoId}}" data-stype="{{:AuthSourceType}}" onclick="ReAuthAccount( '{{:AppKey}}', {{:IsEbillWaybill }},'{{:AuthUrl}}')">重新授权</span>
                    <span class="wu-btn wu-btn-small wu-primary wu-four wu-flex wu-mL12" onclick="RelieveAccount({{:CaiNiaoAuthInfoId}}, {{:AuthSourceType}}, {{:ShopId}}, '{{:UserId}}', {{:~MyFormat(Types,"tolower") == "xiaohongshu" }})">解除绑定</span>
                </div>
                {{else}}
                <div class="wu-pT4">
                    <span class="wu-btn wu-btn-small wu-primary wu-four" onclick="RelieveAccount({{:CaiNiaoAuthInfoId}}, {{:AuthSourceType}}, {{:ShopId}}, '{{:UserId}}', {{:~MyFormat(Types,"tolower") == "xiaohongshu" }})">解除绑定</span>
                </div>
                {{/if}}
            </div>
        </div>
        <div class="newModuleDiv_right wu-pL12 wu-pR12 wu-pT12" data-addrCount="{{:BranchAddressList.length}}">
            {{if BranchAddressList.length == 0}}
            {{if Types=="Jingdong"}}
            <div class="errorBranchcss">
                <span>获取网点失败：</span><br />
                <span>1.若该账号用于京东快递，请忽略该错误，不影响使用。</span><br />
                <span>2.若该账号是京东无界电子面单账号或京东店铺账号，请重试授权。</span><br />
                <span>如果还有问题请联系下客服。</span>
            </div>
            {{else}}
            <div class="errorBranchcss">
                <span>获取网点失败。请检查：</span><br />
                <span>1.去商家后台看看电子面单账号“{{:UserName}}”是否有开通网点。</span><br />
                <span>2.点击左上角"添加电子面单账号授权" 重新登录授权。</span><br />
                <span>如果还没有显示请联系下客服。</span>
            </div>
            {{/if}}
            {{else ~MyFormat(Types,"tolower") == "xiaohongshu" && BillVersion!=2}}
            <div class="addAccountList-xhs-tip" style="display: flex; margin:0 0 12px 0;height: auto;">
                <i class="iconfont icon-a-error-circle-filled1x"></i>
                <span style="display: flex; flex-direction: column; justify-content: center;">
                    @* <span style="white-space: nowrap;">小红书新版电子面单已上线！平台将于<span style="color: #EA572E; font-weight: 500;">2025年3月31日</span>起不再支持旧版电子面单取号，为避免无法正常打单发货，请您尽快切换为新版电子面单。</span>
                        <a href="https://school.xiaohongshu.com/lesson/normal/ba4395753d134b2c95b2c9b0db0f715f?jumpFrom=school&uba_pre=8.xhsschool_search_list.school_search_card.*************&uba_ppre=8.school_rule_detail..*************&uba_index=3" target="_blank">如何切换新版电子面单？</a> *@
                    <span style="font-size: 12px;">小红书新版电子面单已上线！平台将于2025年3月31日起不再支持旧版电子面单取号，为避免无法正常打单发货，请您尽快切换为新版电子面单。<a href="https://school.xiaohongshu.com/lesson/normal/ba4395753d134b2c95b2c9b0db0f715f?jumpFrom=school&uba_pre=8.xhsschool_search_list.school_search_card.*************&uba_ppre=8.school_rule_detail..*************&uba_index=3" target="_blank">如何切换新版电子面单？</a></span>

                </span>
            </div>
            {{/if}}
            {^{for BranchAddressList ~Types=Types}}
            <div class="wu-tableWrap wu-mB12" style='border-right: none;'>
                <table class="newModuleDiv_right_table wu-table-one">
                    <tr>
                        <td class="right_table_one" style="border-top: unset;" colspan="3">快递:{{:CompanyName}}</td>
                    </tr>
                    <tr class="table-header">
                        <td class="right_table_one">网点名称</td>
                        <td class="right_table_two">发货地址</td>
                        <td class="right_table_three">账号信息</td>
                    </tr>
                    {^{for BranchAddress }}
                    <tr>
                        {{if ~MyFormat(~Types,"tolower")=="taobao" && CpCode == "SF"}}
                        {{if BrandCode == "FW"}}
                        <td class="right_table_one">{{:BranchName}}<span>(品牌丰网)</span></td>
                        {{else BrandCode == "FOP"}}
                        <td class="right_table_one">{{:BranchName}}<span>(品牌SFKY)</span></td>
                        {{else}}
                        <td class="right_table_one">{{:BranchName}}<span>(品牌顺丰)</span></td>
                        {{/if}}
                        {{else ~MyFormat(~Types,"tolower")=="wxvideo" && (NickName != null && NickName != "")}}
                        <td class="right_table_one">{{:BranchName}}<div class="tagStatus tooltip" title="共享方店铺名称：{{:NickName}}，详情请前往店铺后台查看">共享</div></td>
                        {{else}}
                        <td class="right_table_one">{{:BranchName}}</td>
                        {{/if}}
                        <td class="right_table_two">{{:Province}} {{:City}} {{:Area}} {{:Town}} {{:Detail}}</td>
                        {{if ~MyFormat(~Types,"tolower")=="taobao" && CpCode == "SF" && BrandCode == "FW"}}
                        <td class="right_table_three">可用单数:{{:Quantity}}   已用单数:{{:PrintQuantity}} 回收单数:{{:CancelQuantity}}</td>
                        {{else CpType == 1 || CpType == 4}}
                        <td class="right_table_three">可用单数:直营(无限可用单号)   已用单数:{{:PrintQuantity}} 回收单数:{{:CancelQuantity}}</td>
                        {{else CpType == 5}}
                        <td class="right_table_three">可用单数:统采(无限可用单号)   已用单数:{{:PrintQuantity}} 回收单数:{{:CancelQuantity}}</td>
                        {{else}}
                        <td class="right_table_three">可用单数:{{:Quantity}}   已用单数:{{:PrintQuantity}} 回收单数:{{:CancelQuantity}}</td>
                        {{/if}}
                    </tr>
                    {{/for}}
                </table>
            </div>
            {{/for}}

        </div>
    </div>
    {{/for}}

</script>

<div class="layui-mywrap noPermission" style="margin-top:15px;display:none">
    <span style="display:flex;font-size:15px;justify-content:center;padding-top:0.5%">暂无权限，请联系管理员</span>
</div>

<script>
    var hasPermission =  @Html.Raw(ViewBag.ViewPermission ?? "false");
    if (hasPermission == false) {
        $(".showPage").hide();
        $(".noPermission").show();
    } else {
        $(".showPage").show();
        $(".noPermission").hide();
    }
    var pddIsUsePrintSystemAppForBill = '@ViewBag.PddIsUsePrintSystemAppForBill';
    var isJingDongTwoApp = '@ViewBag.isJingDongTwoApp';
    //console.log("pddIsUsePrintSystemApp", pddIsUsePrintSystemApp)

    $.views.helpers({
        getShopName: function (shopId) {
            if (allShops && allShops.length > 0) {
                for (var i = 0; i < allShops.length; i++) {
                    if (allShops[i].Id == shopId)
                        return allShops[i].NickName;
                }
            }
            return "";
        },
        MyFormat: function (str, type) {
            if (!str)
                return "";
            if (type == "tolower")
                str = str.toLowerCase();
            else if (type == "toupper")
                str = str.toUpperCase();
            return str;
        },
        getPlatformTitle: function (platform) {
            return showPlatformTitle(platform);//获取中文平台名称
        },
    })



    function douyinLayer(type) {
        //var contentid = (type == "shop" ? "support_platform_toutiao" : "support_platform_toutiao_noshop");
        var contentid = '';
        if (type == "shop") {
            contentid = "support_platform_toutiao";
        } else if (type == "noshop") {
            contentid = "support_platform_toutiao_noshop";
        } else if (type == "TouTiaoSaleShop") {
            contentid = "support_platform_TouTiaoSaleShop";
        }
        layer.open({
            type: 1,
            title: '请选择店铺已订购的应用类型',
            content: $("#" + contentid + ""),
            area: '528px',
            skin: 'wu-dailog',
            btn: ['确定', '取消'],
            success: function () {
                $("#togglePlatform_content>.togglePlatform-content-item").removeClass("active");
                $$.navActive("#togglePlatform_content", function () { })

                $("#togglePlatform_content_noshop>.togglePlatform-content-item").removeClass("active");
                $$.navActive("#togglePlatform_content_noshop", function () { })

                $("#togglePlatform_touTiaoSaleShop_content>.togglePlatform-content-item").removeClass("active");
                $$.navActive("#togglePlatform_touTiaoSaleShop_content", function () { })
            },
            btn1: function () {
                var status = "";
                $("#togglePlatform_content>.togglePlatform-content-item").each(function (index, item) {
                    if ($(item).hasClass("active")) {
                        status = $(item).attr("data-status")
                    }
                })
                $("#togglePlatform_content_noshop>.togglePlatform-content-item").each(function (index, item) {
                    if ($(item).hasClass("active")) {
                        status = $(item).attr("data-status")
                    }
                })
                // 抖店即时零售
                $("#togglePlatform_touTiaoSaleShop_content>.togglePlatform-content-item").each(function (index, item) {
                    if ($(item).hasClass("active")) {
                        status = $(item).attr("data-status")
                    }
                })
                if (status == "") {
                    layer.msg("请选择应用类型！")
                } else {
                    imgToutiao(status);
                    var isAddOk = layer.confirm('<div class="wu-f14 wu-c09">是否已经成功授权电子面单？</div>', { icon: 3, title: '授权结果确认',skin: 'weightDailog wu-dailog', btn: ['确定', '取消'] },
                        function () {
                            window.location.reload();
                        }, function () {
                            window.location.reload();
                        }
                    );
                }
            },
            btn2: function (index) {
                layer.close(index);
            }
        });
    }

    function imgToutiao(state) {
        //抖店店铺电子面单
        var toutiaoUrl = toutiaoAuthUrl;
        if (state == "shop_goods") {
            commonModule.OpenNewTab(toutiaoUrl);
        } else if (state == "shop_fx") {
            toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyin?", "/auth/douyinfxnew?");
            toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyinfx?", "/auth/douyinfxnew?");
            commonModule.OpenNewTab(toutiaoUrl);
        }
        //抖店非店铺电子面单（旧）
        else if (state == "noshop_print") {
            toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyinfx?", "/auth/douyin?");
            toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyinfxnew?", "/auth/douyin?");
            commonModule.OpenNewTab(toutiaoUrl + "&ebill=1");
        }
        //抖店非店铺电子面单（新）
        else if (state == "noshop_goods") {
            toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyin?", "/auth/douyinfx?");
            toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyinfxnew?", "/auth/douyinfx?");
            commonModule.OpenNewTab(toutiaoUrl + "&ebill=1");
        } else if (state == "noshop_fx") {
            toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyin?", "/auth/douyinfxnew?");
            toutiaoUrl = toutiaoUrl.replaceAll("/auth/douyinfx?", "/auth/douyinfxnew?");
            commonModule.OpenNewTab(toutiaoUrl + "&ebill=1");
        }
        // 即时零售
        else if (state == "touTiaoSaleShop") {
            commonModule.OpenNewTab(toutiaoSaleShopAuthUrl);
        }
    }


    function kuaishouLayer() {
        var contentid = "support_platform_kuaishou";
        layer.open({
            type: 1,
            title: '请选择店铺已订购的应用类型',
            @* closeBtn: 0, *@
            skin: 'wu-dailog',
            content: $("#" + contentid + ""),
            area: '528px',
            btn: ['确定', '取消'],
            success: function () {
                $("#togglePlatform_content_kuaishou>.togglePlatform-content-item").removeClass("active");
                $$.navActive("#togglePlatform_content_kuaishou", function () { })

            },
            btn1: function () {
                var status = "";
                $("#togglePlatform_content_kuaishou>.togglePlatform-content-item").each(function (index, item) {
                    if ($(item).hasClass("active")) {
                        status = $(item).attr("data-status")
                    }
                })
                if (status == "") {
                    layer.msg("请选择应用类型！")
                } else {
                    imgKuaiShou(status);
                    var isAddOk = layer.confirm('<div class="wu-f14 wu-c09">是否已经成功授权电子面单？</div>', { icon: 3, title: '授权结果确认',skin: 'weightDailog wu-dailog', btn: ['确定', '取消'] },
                        function () {
                            window.location.reload();
                        }, function () {
                            window.location.reload();
                        }
                    );
                }
            },
            cancel: function () {

            }
        });
    }

    function wxvideoLayer() {
        layer.open({
            type: 1,
            title: false,
            closeBtn: 0,
            content: $("#support_platform_WxVideoDailog"),
            skin: 'wu-dailog',
            area: '620px',
            btn: ['确定', '取消'],
            success: function () {

                $$.navActive("#support_platform_WxVideoContent", function () {})

            },
            yes: function () {
                var status = "";
                $("#support_platform_WxVideoContent>.togglePlatform-content-item").each(function (index, item) {
                    if ($(item).hasClass("active")) {
                        status = $(item).attr("data-status")
                    }
                })
                if (status == "") {

                    layer.msg("请选择应用类型！");

                } else {

                    var url = "";
                    if (status == "old") {
                        url = "https://channels.weixin.qq.com/shop/servicemarket/myServices?status=2"
                    } else {
                        url = "https://channels.weixin.qq.com/shop/servicemarket/myServices?status=2"
                    }
                    commonModule.OpenNewTab(url);
                    var isAddOk = layer.confirm('<div class="wu-f14 wu-c09">是否已经成功授权电子面单？</div>', { icon: 3, title: '授权结果确认',skin: 'weightDailog wu-dailog', btn: ['确定', '取消'] },
                        function () {
                            window.location.reload();
                        }, function () {
                            window.location.reload();
                        }
                    );
                }
            }
        });
    }

    function pingduoduoLayer() {
        if (pddIsUsePrintSystemAppForBill == "True") {
            commonModule.togglePddXuFei(2);
        }
    }

    function imgKuaiShou(state) {
        var targetUrl = kuaishouAuthUrl;
        if (state == "kuaishouv2") {
            targetUrl = targetUrl.replaceAll("/fxauth/kuaishou?", "/fxauth/kuaishouv2?");
        }
        commonModule.OpenNewTab(targetUrl);

    }

    function jingdongLayer() {
        if (isJingDongTwoApp == "True") {
            var contentid = "support_platform_jingdong";
            layer.open({
                type: 1,
                title: '请选择店铺已订购的应用类型',
                @* closeBtn: 0, *@
                content: $("#" + contentid + ""),
                skin: 'wu-dailog',
                area: '528px',
                btn: ['确定', '取消'],
                success: function () {
                    $("#togglePlatform_content_jingdong>.togglePlatform-content-item").removeClass("active");
                    $$.navActive("#togglePlatform_content_jingdong", function () { })

                },
                btn1: function () {
                    var status = "";
                    $("#togglePlatform_content_jingdong>.togglePlatform-content-item").each(function (index, item) {
                        if ($(item).hasClass("active")) {
                            status = $(item).attr("data-status")
                        }
                    })
                    if (status == "") {
                        layer.msg("请选择应用类型！")
                    } else {
                        var url = window.location.protocol + "//" + window.location.host;
                        var targetUrl = jdAuthUrl;
                        if (status == "dd") {
                            targetUrl = targetUrl.replaceAll("/fxauth/jingdong?", "/auth/jingdong?");
                        }
                        commonModule.OpenNewTab(targetUrl);
                        var isAddOk = layer.confirm('<div class="wu-f14 wu-c09">是否已经成功授权电子面单？</div>', { icon: 3, title: '授权结果确认',skin: 'weightDailog wu-dailog', btn: ['确定', '取消'] },
                            function () {
                                window.location.reload();
                            }, function () {
                                window.location.reload();
                            }
                        );
                    }
                },
                cancel: function () {

                }
            });
        }
    }
</script>