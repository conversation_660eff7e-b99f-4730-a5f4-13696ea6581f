
@{
    ViewBag.Title = "关联同款";
}

@section Header {
        <style type="text/css">
            .flex {
                display: flex;
                align-items: center;
            }
            .flex-r {
                display: flex;
                align-items: center;
                justify-content: flex-end;
            }
            .layui-tab-brief > .layui-tab-title .layui-this {
                font-size: 14px;
                font-weight: 600;
                letter-spacing: 0em;

                font-variation-settings: "opsz" auto;
                /* 品牌色/Brand5 */
                color: #0888FF;
            }
            .layui-tab-brief > .layui-tab-more li.layui-this:after, .layui-tab-brief > .layui-tab-title .layui-this:after{
                width: calc(100% - 12px);
                border-bottom: 2px solid #0888FF;
                margin-left: 6px;
            }
            .layui-form-item {
                margin-bottom:0;
            }
            .layui-form-item .n-mButton {
                margin-right: 8px;
            }

            .layui-form-item .n-mButton.n-pActive {
                border: 1px solid rgba(8, 136, 255, 0.1);
            }
            .layui-mytable .productShow .productShow-img {
                width: 56px;
                height: 56px;
                border-radius: 4px;
                border: 0.5px solid rgba(0, 0, 0, 0.14);
                box-sizing: border-box;
                margin-right: 8px;
            }

            .n-skin .layui-layer-content {
                padding: 0;
            }

                .n-skin .layui-layer-content .dialog-wrap {
                    padding: 16px;
                }

            .n-skin .dialogBtns {
                border-top: 1px solid #e5e9f2;
                padding: 12px 16px;
                justify-content: space-between;
            }

            .n-skin .dialogBtns .n-mButton {
                margin-left: 10px;
            }

            .n-skin .dialog-wrap .dialog-wrap-cont {
                padding-top: 8px;
            }

            .n-skin .dialog-wrap .dialog-wrap-cont .item-column-input {
                height: 32px;
                line-height: 32px;
                border-radius: 4px;
                border: 1px solid rgba(0, 0, 0, 0.14);
            }

            .n-skin .dialog-wrap .dialog-wrap-cont .item-column-input > span {
                width: 24px;
                text-align: center;
            }

            .n-skin .dialog-wrap .dialog-wrap-cont .item-column-input > input {
                border: none;
                width: 100%;
                height: 32px;
                line-height: 32px;
                padding: 8px;
                box-sizing: border-box;
                border-radius: 4px;
            }
            .n-skin .dialog-wrap .dialog-wrap-cont .table-field {
                width: 528px;
                border-radius: 8px;
                border: 0.5px solid rgba(0, 0, 0, 0.14);
            }
            .n-skin .dialog-wrap .dialog-wrap-cont .table-field .item-column-checkbox {
                align-items:flex-start;
                padding: 14px 16px;
                border-bottom: 0.5px solid rgba(0, 0, 0, 0.14);
            }
            .n-skin .dialog-wrap .dialog-wrap-cont .table-field .item-column-checkbox:last-child{
                border-bottom: none;
            }
            .n-skin .dialog-wrap .dialog-wrap-cont .table-field .item-column-checkbox-input {
                margin: 3px 8px 0 0;
            }
            .n-skin .dialog-wrap .dialog-wrap-cont .table-field .item-column-checkbox-title {
                color: #333;
                font-size: 14px;
            }
            .n-skin .dialog-wrap .dialog-wrap-cont .table-field .item-column-checkbox .tips {
                font-size: 12px;
                color: #666;
                margin-top:4px;
            }
            .n-skin .dialog-wrap label {
                margin-bottom: 8px;
            }

                .n-skin .dialog-wrap label:last-child {
                    margin-bottom: 0px;
                }

            .n-skin .dialog-wrap .radioSpan {
                color: #333;
                margin-left: 8px;
            }
             .n-skin .dialog-wrap .dialog-wrap-cont .dialog-wrap-cont-statusbox {
     width: 156px;
     height: 80px;
     border-radius: 4px;
     box-sizing: border-box;
     /* 品牌色/Brand2 */
     border: 0.5px solid rgba(8, 136, 255, 0.2);
     background: rgba(8, 136, 255, 0.1);
     margin-right: 16px;
     justify-content: center;
     flex-direction: column;
 }

     .n-skin .dialog-wrap .dialog-wrap-cont .dialog-wrap-cont-statusbox:last-child {
         margin-right: 0;
         background: rgba(234, 87, 46, 0.1);
         border: 0.5px solid rgba(234, 87, 46, 0.3);
     }

     .n-skin .dialog-wrap .dialog-wrap-cont .dialog-wrap-cont-statusbox .count {
         font-size: 24px;
         font-weight: 600;
         color: #0888FF;
     }
            .stockup_table_content {
                overflow-x:auto;
                border-left: none;
            }

                .stockup_table_content thead tr {
                    border-top: 1px solid #e6e6e6;
                    border-bottom: 1px solid #e6e6e6;
                    background-color: rgba(0, 0, 0, 0.04);
                }

                    .stockup_table_content thead tr th {
                        text-align: center;
                        border: none;
                    }

                        .stockup_table_content thead tr th .hover:hover {
                            color: #0888FF;
                        }

                .stockup_table_content tbody tr {
                    border-bottom: 1px solid #e6e6e6;
                    cursor: pointer
                }
                .stockup_table_content tbody tr .hovericon {
                    display:none;
                }
                .stockup_table_content tbody tr:hover .hovericon{
                    display:block;
                }
                .stockup_table_content tbody tr .hover :hover .iconfont{
                    color: #0888FF;
                }

                    .stockup_table_content tbody tr td {
                        transition: all 1s ease 0s;
                        border: none;
                        text-align: center;
                    }

                        .stockup_table_content tbody tr td.hover:hover .iconfont {
                            color: #0888FF;
                        }
            #productAssociation {
                /* padding: 24px; */
                padding: 24px 24px 0px 24px;
            }

            #productAssociation .header {
                padding-bottom: 24px;
                justify-content: space-between;
            }
            #productAssociation .header .iconfont {
                color: #666;
            }
            #productAssociation .header .h-title{
                font-weight: bold;
                font-size: 20px;
                color:#181818;
            }
            #productAssociation .boxcss {
                border-radius: 8px;
                background-color: #fff;
            }
            .container {
                display:flex;
            }
            .container .container-l{
                min-width: 288px;
                width: 288px;
                padding: 16px;  
                box-sizing: border-box;
                margin-right: 16px;
            }
            .container .container-l .title{
                color: rgba(0, 0, 0, 0.9);
                font-size:16px;
                font-weight: 600;
                margin-bottom:16px;
            }
            .container .container-l .goodsinfo .goodsinfo-img>img{
                width: 54px;
                height: 54px;
                /* border: 0.5px solid rgba(0, 0, 0, 0.14); */
                /* border-radius: 4px; */
                margin-right: 8px;
            }
            .container .container-l .goodsinfo .goodsinfo-text .goodsinfo-text-title{
                width:192px;
                color: rgba(0, 0, 0, 0.6);
                font-size:14px;
                margin-bottom: 5px;
            }
            .container .container-l .goodsinfo .goodsinfo-text .goodsinfo-text-sku {
                color: #000;
                width: 192px;
            }
            .container .container-l .container-l-list {
                margin-top: 16px;
            }
            .container .container-l .container-l-list .list-item {  
                justify-content: space-between;
                margin-bottom: 8px;
                height: 40px;
                line-height: 40px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.09);
                box-sizing: border-box;
            }
            .container .container-l .container-l-list .list-item:last-child {
                margin-bottom: 0;
            }
             .container .container-l .container-l-list .list-item .list-item-text{
                max-width: 150px;
            } 
            .container .container-l .container-l-list .list-item .hovericon{
                display:none;
            }
            .container .container-l .container-l-list .list-item:hover .hovericon {
                display:block;
                color: #0888FF;
            }
            .container .container-l .container-l-list .list-item .skucode {
                word-break: break-all;
                line-height: 14px;
            }
            .container .container-r{
                width: 100%;
                height: calc(100vh - 122px);
                box-sizing: border-box;
            }
            #productAssociation  .container .container-r .container-r-header {
                justify-content: space-between;
                padding: 0 16px;
                position:relative;
            }
            #productAssociation  .container .container-r .container-r-header .headerBtns  {
                position: absolute;
                right: 0px;
                top: -54px;
            }
            #productAssociation  .container .container-r .container-r-header .headerBtns .btn {
                margin-left: 12px;
            }
            .container .container-r .title{
                color: #000;
                font-size:16px;
                font-weight: 600;
                padding: 16px 0;
            }
            #productAssociation .platform-area {
                width: 100%;
                height:40px;
                padding: 0 16px;
                box-sizing: border-box;
                background: rgba(8, 136, 255, 0.1);
            }
            #productAssociation .platform-area ul{
                height: 40px;
            }
            #productAssociation .platform-area li{
                color: #666;
                padding: 4px 8px;
                height: 16px;
                line-height: 16px;
                border-radius: 4px;
                margin-right:4px;
            }
            #productAssociation .platform-area li.active{
                color: #111;
                background:#fff;
            }
            #productAssociation .query_form {
                padding: 0 16px 16px;
            }
            #productAssociation .query_form .automaticAssociation {
                display: inline-block;
                padding: 6px 12px;
                color: rgba(0, 0, 0, 0.3);
                box-sizing: border-box;
                /* 中性色/Gray2 */
                border: 1px solid rgba(0, 0, 0, 0.09);
                border-radius: 4px;
            }
            #productAssociation input[type=checkbox] {
                margin-right: 3px;
            }

            #productAssociation .layui-input-inline {
                width: 290px;
            }

            #productAssociation .layui-input-inline-combination select, {
                width: 96px;
                margin-right: 2px;
            }
            #productAssociation .layui-input-inline-combination .layui-form-select {
                margin-right: 2px;
            }
            #productAssociation .layui-input-inline-label {
                height:32px;
                line-height:32px;
                border-radius:4px;
                box-sizing:border-box;
                border:1px solid rgba(0,0,0,0.14);
            }
            #productAssociation .layui-input-inline-label .label{
                padding:0px 8px;
                color: rgba(0, 0, 0, 0.6);
            }
            #productAssociation .layui-input-inline-label .layui-input{
                border:none;
                width: calc(100% - 64px);
                height:30px;
                line-height:30px;
                padding-left: 0;
            }
            #productAssociation .layui-input-inline-label .layui-select{
                border:none;
                width: calc(100% - 64px);
                height:30px;
                line-height:30px;
                padding-left: 0;
                -webkit-appearance:auto;
            }
            #productAssociation .layui-input-inline-label .layui-form-select {
                width: calc(100% - 64px);
            }

            #popUp-right {
                width:100%;
                height:100%;
                position: fixed;
                top: 0;
                left: 0;
                z-index: 10000;
            }
             #popUp-right .popUp-hidebox {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;
                z-index: 100001;
                background: rgba(0, 0, 0, 0.4);
            }
            #popUp-right .popUp-container{
                width: 888px;
                height: 100%;
                background: #fff;
                position: absolute;
                top:0;
                right:-900px;
                 z-index: 100002;
                overflow: hidden;
                animation: operateLasChild  0.4s 0.2s linear  1 alternate forwards; 
     
            }

            @@keyframes operateLasChild {
                0% {
                    right:-900px;
                }

                100% {
                    right:0;
                }
            }


            #popUp-right .popUp-container .popUp-title {
                justify-content: space-between;
                height: 44px;
                line-height: 44px;
                padding: 0 16px;
                font-size: 14px;
                color: #333;
                background: rgba(0, 0, 0, 0.04);
            }

            #popUp-right .popUp-container .popUp-title .popUp-title-right .iconfont {
                font-size: 22px;
            }
            #iframe_main {
                width: 100%;
                height: calc(100% - 85px);
                border: none;
            }
            #popUp-right .popUp-right-footer {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                justify-content: flex-end;
                height: 56px;
                padding: 0 16px;
                background:#fff;
                box-sizing: border-box;
                border-width: 0.5px 0px 0px 0px;
                border-style: solid;
                border-color: rgba(0, 0, 0, 0.09);
            }
            #popUp-right .popUp-right-footer .n-mButton {
                margin-left: 12px;
            }
            #popUp-right .popUp-right-footer input{
                margin-right: 8px;
            }
            #popUp-right .popUp-right-footer .n-mButton {
                margin-left: 12px;
            }
                    .n-carMain {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }

            .n-carMain-left {}

            .n-carMain-right {
                width: 280px;
                height: 180px;
                border-radius: 8px;
                opacity: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                background-size: 100% 100%;
            }

            .n-carMain-left .n-font5 {
                color: rgba(0, 0, 0, 0.6);
                margin: 8px 0;
            }

            .hideActive .popUp-hidebox{
                animation: operateLasChild03  0.2s linear  1 alternate forwards; 

            }
            #popUp-right.hideActive .popUp-container{
                animation: operateLasChild02  0.2s linear  1 alternate forwards; 
            }
            
            @@keyframes operateLasChild02 {
                0% {
                    right:0;
                }

                100% {
                    right:-900px;
                }
            }
            @@keyframes operateLasChild03 {
                0% {
                    opacity:1;
                }

                100% {
                    opacity:0;
                }
            }
            .dialogSupply-skin .selectWrap-box{
                position: fixed;
                top:unset;
                bottom:unset;
            }
            .ellipsis{
			overflow: hidden;    
			text-overflow:ellipsis;    
			white-space: nowrap;
		}
        .popoverCommon-warn04:after {
            display: block;
            content: "";
            position: absolute;
            /* bottom: 0; */
            top: -6px;
            left: 11px;
            width: 0;
            height: 0;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-bottom: 7px solid #fff;
            border-top: none;
        }
        #hasDataList {
            height: auto;
        }
        #smallShop {
            overflow-y: auto;
            /* background: #fff;
            padding-bottom: 60px; */
        }
        /* 修改滚动条的样式 */
        #smallShop::-webkit-scrollbar {
            height: 8px;/* 设置滚动条的轨道背景色 */
            width: 8px; /* 设置滚动条的宽度 */
        }

        #smallShop::-webkit-scrollbar-track {
            background: #F5F5F5; /* 设置滚动条的轨道背景色 */
        }

        #smallShop::-webkit-scrollbar-thumb {
            border-radius: 18px;
            opacity: 1;
            background: rgba(0, 0, 0, 0.14); /* 设置滚动条滑块的背景色 */
        }
        .level-sell-price-wrap {
            margin: 4px 0 0 14px;
            background: rgba(0, 0, 0, 0.04);
            border-radius: 4px;
            padding: 8px;
            box-sizing: border-box;
            width: 481px;
        }

        .level-sell-rule-ul {
            margin: 8px 0 0 20px;
        }

        .level-sell-rule-ul > li {
            margin-bottom: 4px;
        }

        .level-sell-rule-ul > li:last-child {
            margin-bottom: 0;
        }
        .hover-back-icon {
            border-radius: 4px;
            padding: 8px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            margin-right: 4px;
        }
        .hover-back-icon:hover {
            background: rgba(0, 0, 0, 0.09);
        }
        </style>
}

<div class="my-body">
<div id="reactAppBindSku"></div>
</div>

<script>
    var WareHouseStatus = @(Html.Raw(ViewBag.WareHouseStatus ?? "[]"));
    
    var AllAgents = @(Html.Raw(ViewBag.AllAgents ?? "[]"));
    var Shops = @(Html.Raw(ViewBag.Shops ?? "[]"));
    var Agents = @(Html.Raw(ViewBag.Agents ?? "[]"));
    @*var SupplierUsers = @(Html.Raw(ViewBag.SupplierUsers ?? "[]"));*@
    var Suppliers = @(Html.Raw(ViewBag.Suppliers ?? "[]"));
    var platformType = 'Alibaba';
    commonModule.CloudPlatformType = '@(Html.Raw(ViewBag.CloudPlatformType ?? ""))';
		
		//绑定厂家
		var initSupplierSelectBox = function (ele,isRadio) {

			var selectboxArr = [];
			for (var i = 0; i < Suppliers.length; i++) {
                if (Suppliers[i].Status != undefined && Suppliers[i].Status == 1) {
				var obj = {};
				obj.Value = Suppliers[i].FxUserId;
				obj.Text = Suppliers[i].UserName;
				if (Suppliers[i].IsTop != undefined && Suppliers[i].IsTop) {
					obj.Text = '<i class="iconfont icon-zhuangtai zhidingIcon"></i>' + Suppliers[i].UserName;
				}
					selectboxArr.push(obj);
                }
			}
			var selectInit = {
				eles: '#' + ele,
				emptyTitle: '请选择厂家', //设置没有选择属性时，出现的标题
				data: selectboxArr,
				searchType: 1, //1出现搜索框，不设置不出现搜索框
				showWidth: '250px', //显示下拉的宽
				isRadio: isRadio, //有设置，下拉框改为单选筛选
				allSelect: false,
				selectData: []  //初始化数据
			};
			var selectBox = new selectBoxModule2();
			selectBox.initData(selectInit);
		}

    function onGobackWrap() {
        var url = commonModule.rewriteUrl("/BaseProduct/NewBaseProduct?");
        window.location.href =url + '&dbname=@(Request.QueryString["dbname"])'
    };

    var isFirst=true;
    function relationListShowFunction(getRelationCount){
        var relationCount = ""
       if(getRelationCount==undefined){
        relationCount = commonModule.getQueryVariable('relationCount')||0;
        relationCount = parseInt(relationCount);    
       }else{
        relationCount = parseInt(getRelationCount);    
       }
        if(isFirst){
            if(relationCount==0){
                $("#noDataList").show();
                $("#hasDataList").hide();

            }else{
                $("#noDataList").hide();
                $("#hasDataList").show();
            }
        }
        if(relationCount>0){
            $("#noDataList").hide();
            $("#hasDataList").show();
        }
        isFirst =false;

    }


</script>



@section scripts {
    <script src="~/Scripts/react/react.production.min.js"></script>
    <script src="~/Scripts/react/react-dom.production.min.js"></script>
    <script src="~/Scripts/react/babel.min.js"></script>
    <script type="text/babel">
    commonModule.CloudPlatformType = '@(Html.Raw(ViewBag.CloudPlatformType ?? ""))';
        // 1.创建类式组件
        class BaseProductSkuRelationBindIndex extends React.Component {
            constructor() {
                super()
                this.state = {
                    list: [],
                    // 顶部tab
                    // 平台
                    platformType: 'TouTiao',
                    tabList: [
                        { id: 4, name: '抖店', value: 'TouTiao', checked: true, count: 0 },
                        { id: 1, name: '精选平台', value: 'Alibaba', count: 0 },
                        { id: 2, name: '拼多多', value: 'Pinduoduo', count: 0 },
                        { id: 3, name: '京东', value: 'Jingdong', count: 0 },
                        { id: 5, name: '我的小站', value: 'MyStation', count: 0 },
                    ],
                    tabListEqual: [
                        { id: 4, name: '抖店', value: 'TouTiao',checked: true },
                        { id: 1, name: '精选平台', value: 'Alibaba' ,},
                        { id: 2, name: '拼多多', value: 'Pinduoduo' },
                        { id: 3, name: '京东', value: 'JingDong' },

                    ],
                    // 平台分区列表
                    platformArea: [],
                    platformAreaEqual:[],
                    // 全选状态
                    allCheckSku: false,
                    // 全部展开
                    allIsShowSku: false,
                    // 列表头编辑状态
                    theadEditStatus: false,
                    // 已选sku列表
                    checkSkuList: [],

                    // 弹窗参数
                    dialogData:{
                        isSave: false,
                        //已选sku数量
                        skuCount: 0,
                        label: '',
                        inputValue: '',
                        inputType: '',
                        main:null,
                        supply: '',
                        selectFields:[
                            {
                                id: 0,
                                name: '全选',
                                value: 'all',
                                checked: false,
                            },
                            {
                                id: 1,
                                name: '采购价',
                                value: 'IsUseSettlePrice',
                                tips: '使用最新采购价对厂家进行财务结算。',
                                checked: false,
                            },
                            {
                                id: 2,
                                name: '成本价',
                                value: 'IsUseCostPrice',
                                tips: '使用最新成本价用于自营店铺统计总成本。',
                                checked: false,
                            },
                            {
                                id: 3,
                                name: '分销价',
                                value: 'IsUseDistributePrice',
                                tips: '使用最新分销价对下游分销品进行财务结算。',
                                checked: false,
                            },
                            @*{
                                id: 4,
                                name: '库存',
                                value: 'IsAllUseWarehouse',
                                tips: '一键开启所有分销品使用该库存进行打单发货扣减。',
                                checked: false,
                            },*@
                            {
                                id: 5,
                                name: '供货方式',
                                value: 'IsUseSupplierFxUser',
                                tips: '使用最新供货方式对所有关联分销品的订单推送。',
                                checked: false,
                            },
                            {
                                id: 6,
                                name: '简称',
                                value: 'IsUseShortName',
                                tips: '默认已关联所有的分销品使用统一简称。',
                                checked: true,
                                disabled: true,
                                absoluteChecked: true,
                            },
                        ],
                        // 是否同步更新
                        isAutoChecked: false,
                    },

                    queryQaram:{
                        // 同款类型：0 自营同款、1 下游分销商同款
                        Type: '',
                        // 基础商品skuid
                        BaseProductSkuUid: '',
                        // 基础商品id
                        BaseProductUid: '',
                        PageIndex:1,
                        PageSize:50,
                    },
                    productSkuDetail:{},
                    // 查看明细src
                    ifarmDetailSrc:'',
                    // 关联同款src
                    ifarmSrc: '',
                    iframeHeight: 0,
                    iframeMainDetailHeight: 0,
                    timer: null,
                    timerCount: 0,
                    // 同步进度
                    AutoRelationProces: {
                        "TaskCode": "",
                        "SuccessCount": 0,
                        "FailCount": 0,   // 异常数量
                        "Process": -1, // 进度
                    },
                    checkEqualProducts:[],
                    updateFieldsChecked: false,
                    // 是否显示小站
                    isShowSite: false,
                    // 商品数量
                    listCount: 0,
                    tempPlatformType: '',
                }
            }
            LoadList(isPaging) {
                let {queryQaram} = this.state;
                var that = this;

                if (isPaging) {
                        queryQaram.PageIndex = 1;
                    }
                this.setState({ list: [],queryQaram })
                commonModule.Ajax({
                    url: '/api/BaseProduct/GetSupplierProductSkuRelations',
                    showMasker: false,
                    data: queryQaram,
                    success: function (rsp) {
                        if (rsp.Success) {
                            var list = rsp.Data.Rows || [];
                            for (var i = 0; i < list.length; i++) {
                                var obj = list[i];
                                obj.isShowSku = false;
                                obj.isCheckSku = false;
                                obj.isEdit = false;
                                obj.ImageUrl = commonModule.newTransformImgSrc(obj.ImageUrl);
                                if (obj.Skus && obj.Skus.length > 0) {
                                    obj.Skus.forEach(function (item, index) {
                                        item.isCheckSku = false;
                                        if(item.Attributes){
                                            item.AttributeValue = that.onColumnSkuFormat(item.Attributes)
                                        }
                                    })
                                }
                            }
                            that.setState({ list: list,queryQaram,listCount: list.length })
                        } else {
                            layer.msg(rsp.Message);
                        }

                        layui.laypage.render({
                            elem: 'stationPage',
                            count: rsp.Data.Total||0,
                            limit: queryQaram.PageSize,
                            curr: queryQaram.PageIndex,
                            limits: [50, 100, 200, 300, 400, 500],
                            layout: ['count', 'prev', 'next', 'limit'],
                            theme: 'n-page',
                            jump: function (obj, first) {
                                if (!first) {
                                    queryQaram.PageIndex = obj.curr;
                                    queryQaram.PageSize = obj.limit;
                                    that.setState({ queryQaram: queryQaram }, function () {
                                        that.LoadList();
                                    })
                                }
                            }
                        });

                    }
                })

            }
            componentDidMount() {
                let { queryQaram, tabList } = this.state;
                queryQaram.BaseProductUid = commonModule.getQueryVariable('baseProductUid')||'';
                queryQaram.BaseProductSkuUid = commonModule.getQueryVariable('baseProductSkuUid')||'';
                var platformType = 'TouTiao';
                this.setState({
                    queryQaram,
                    platformType
                }, function () {
                    this.onCloudPlatform(platformType,'')
                    this.getBaseProduct();
                    //this.LoadList();
                })
                var that = this;
                initSupplierSelectBox("supplier_name_select",true);
                layui.use('form', function(){
                    var $ = layui.$;
                    var form = layui.form;
                    form.render();
                    // 提交事件
                    form.on('submit(productAssociation)', function (data) {
                        var field = data.field; // 获取表单字段值
                        var queryQaram = that.state.queryQaram;
                        queryQaram = Object.assign(queryQaram,field)
                        that.setState({ queryQaram: queryQaram }, function () {
                            that.LoadList(true);
                        })
                        return false; // 阻止默认 form 跳转
                    });
                });
                window.addEventListener('message',function(e){

                    if(e.data.hidePopUp){
                        that.hideBindProductPopUp();
                    }


                    if(e.data.checkEqualProducts) {
                        that.setState({
                            checkEqualProducts: e.data.checkEqualProducts
                        })
                    }
                    if(e.data.type == "bindProduct"){
                        that.getBaseProduct();
                        //that.onBindProduct(e.data.data);
                    }
                    if (e.data.operateType && e.data.operateType == "relationDetailList") { //已关联列表返回
                        const relationData = e.data.RelationCountDict;
                        tabList.forEach(item => {
                            if (item.checked) {
                                item.count = relationData.CurrentCheckedCount;
                            } else {
                                item.count = relationData[item.value] || 0;
                            }
                        });
                        that.setState({
                            listCount: e.data.listCount,
                            tabList
                        });
                    }
                })
                //this.getAutoRelationProcess();
            }

            // 商品详情
            getBaseProduct (callback) {
                var that = this;
                var baseProductUid = commonModule.getQueryVariable('baseProductUid')||'';
                var baseProductSkuUid = commonModule.getQueryVariable('baseProductSkuUid') || '';
                commonModule.Ajax({
                    url: '/BaseProduct/GetBaseProductSkuDetail',
                    loadingMessage: "查询中",
                    showMasker: false,
                    data: {
                        "baseProductUid": baseProductUid,
                        "baseProductSkuUid": baseProductSkuUid,
                    },
                    success: function (rsp) {
                        if (rsp.Success && rsp.Data) {
                            var productSkuDetail = rsp.Data;

                            if( productSkuDetail.Attributes){
                                productSkuDetail.AttributeValue = that.onColumnSkuFormat(productSkuDetail.Attributes)
                            }
                            productSkuDetail.ImageUrl = commonModule.newTransformImgSrc(productSkuDetail.ImageUrl)
                            // 已关联数
                            relationListShowFunction(Number(productSkuDetail.RelationCount) || 0);
                            that.setState({
                                productSkuDetail:productSkuDetail
                            })
                            if (callback) {
                                callback(productSkuDetail)
                            }
                            that.oninitIframeHeight();
                        } else {
                            layer.msg(rsp.Message);
                        }
                    },
                    error: function (err) {
                        layer.msg("查询失败", { icon: 5 });
                    }
                });
            }
            // 规格格式化
            onColumnSkuFormat(AttributeValue) {
				var skuName = '';
				var Attributes = JSON.parse(AttributeValue);
				if (Attributes.length) {
					for (var i = 0; i < Attributes.length; i++) {
						var attributesItem = Attributes[i];
                        if (attributesItem.v !="无规格") {
		                    skuName += attributesItem.v + ";";
	                    }
					}
					skuName = skuName.substring(0, skuName.length - 1)
				}
				return skuName
            }
            // 全选规格
            onCheckAllSku() {
                let { list, allCheckSku } = this.state;
                allCheckSku = !allCheckSku;
                list.forEach((iData, i) => {
                    iData.isCheckSku = allCheckSku;
                    // iData.Skus.forEach((jData, j) => {
                    //     jData.isCheckSku = allCheckSku;
                    // })
                })
                this.setState({ list: list, allCheckSku })
            }
            // 选择规格
            onCheckSubAllSku(item, index) {
                let {list} = this.state;
                list[index].isCheckSku = !list[index].isCheckSku;
                var theadEditStatus = false;
                var allCheckSku = true;
                list.forEach((iData, i) => {
                    if (iData.isCheckSku) {
                        theadEditStatus = true;
                    } else {
                        allCheckSku = false;
                    }
                })
                this.setState({ list: list, theadEditStatus,allCheckSku})
            }
            // 获取已选sku
            onCheckSkuList() {
                let { list } = this.state;
                var checkSkuList = [];
                list.forEach((iData, i) => {
                    if (iData.isCheckSku) {
                        checkSkuList.push(iData);
                    }
                })
                return checkSkuList;
            }
            // 快速编辑价格
            onColumnPrice(keys,item) {
                var checkSkuList = [];
                var productSkuDetail = this.state.productSkuDetail;
                var dialogData = this.state.dialogData;
                if (productSkuDetail) {
                    checkSkuList = [productSkuDetail];
                }
                var that = this;
                var title = '编辑采购价';
                switch (keys) {
                    case 'CostPrice':
                        title = '编辑成本价'
                        break;
                    case 'DistributePrice':
                        title = '编辑分销价'
                        break;
                    case 'ShortTitle':
                        title = '编辑简称';
                        dialogData.inputType = 'text';
                        dialogData.isAutoChecked = true;
                        break;
                    default:
                        title = '编辑采购价';
                        break;
                }

                dialogData.checkedKeys = keys;
                dialogData.skuCount = checkSkuList.length;

                dialogData.main = layer.open({
                    type: 1,
                    title: title, //不显示标题
                    content: $('#dialogPrice'),
                    skin: 'n-skin',
                    area: '400px', //宽高
                    success:function(){
                    },cancel: function () {
                        that.onDialogClose()
                    },
                });
                this.setState({ dialogData,checkSkuList });
            }
            // 快速编辑价格(小站)
            onColumnSitePrice(keys,item) {
                var that = this;
                var checkSkuList = [];
                // 可编辑校验
                //var keysEnable = keys + 'Enable';
                if (item) {
                    checkSkuList = [item];
                } else {
                    checkSkuList = this.onCheckSkuList();
                }
                var title = '编辑采购价';
                if (checkSkuList.length == 0) {
					layer.msg("请选择可编辑价格的商品");
					return false;
				}
                switch (keys) {
                    case 'CostPrice':
                        title = '编辑成本价'
                        break;
                    case 'DistributePrice':
                        title = '编辑分销价'
                        break;
                    default:
                        title = '编辑采购价';
                        break;
                }
                var dialogData = this.state.dialogData;
                dialogData.checkedKeys = keys;
                dialogData.type = 'site';

                dialogData.skuCount = checkSkuList.length;
                dialogData.main = layer.open({
                    type: 1,
                    title: title, //不显示标题
                    content: $('#dialogPrice'),
                    skin: 'n-skin',
                    area: '400px', //宽高
                    success:function(){
                    },
                    cancel: function () {
                        that.onDialogClose()
                    },
                });
                this.setState({ dialogData,checkSkuList:checkSkuList  });
            }
            // 快速编辑价格弹窗 - input改变
            onDialogHandleChange(event) {
                var val = event.target.value || '';
                var dialogData = this.state.dialogData;
				if (val < 0 && dialogData.inputType != 'text') {
					layer.msg("请输入正确的金额");
					return false;
				}

				dialogData.inputValue = val;
				dialogData.isSave = val ? true : false;
				this.setState({ dialogData });
            }

            // 快速编辑价格弹窗提交
            onDialogSubmit(){
                var that = this;
                const {platformType} = this.state;

                var checkSkuList = this.state.checkSkuList;
                var dialogData = this.state.dialogData;
                if (platformType == 'Site' && dialogData.type == 'site') {
                    this.onDialogSiteSubmit()
                    return false
                }
                var productSkuDetail = this.state.productSkuDetail;
				var obj = {};
				var NoCheckedProductUidStr = '';
				var NoCheckedSkuUidStr = '';
				if (checkSkuList.length) {
					checkSkuList.forEach((iData, i) => {
                        var _uid = iData.UidStr;
                        var _baseproductuid = iData.BaseProductUidStr;
                        if (!obj[_baseproductuid]) {
                            var arr = [];
                            arr.push(_uid)
                            Object.defineProperty(obj, [_baseproductuid], {
                                value: arr,
                                enumerable: true,
                                configurable: true,
                                writable: true
                            });
                        } else {
                            obj[_baseproductuid].push(_uid);
                        }
                    })
				}
				// 清除后面逗号
				NoCheckedProductUidStr = NoCheckedProductUidStr.substring(0, NoCheckedProductUidStr.length - 1);
				NoCheckedSkuUidStr = NoCheckedSkuUidStr.substring(0, NoCheckedSkuUidStr.length - 1);
				let data = {
					SkuUidStrDic: obj,
                    //SkuUid: productSkuDetail.UidStr || '',
                    ShortTitle: productSkuDetail.ShortTitle || '',
                    BaseProductSkuSupplierConfig: productSkuDetail.BaseProductSkuSupplierConfigs || '',
                    //CostPrice: productSkuDetail.CostPrice||'',
                    //DistributePrice: productSkuDetail.DistributePrice||'',
                    //SettlePrice: productSkuDetail.SettlePrice||'',
                    BaseProductUidStr: productSkuDetail.BaseProductUidStr || '',
				}
				data[dialogData.checkedKeys] = dialogData.inputValue;
				data['IsUpdatePtSku'+dialogData.checkedKeys] = dialogData.isAutoChecked;
                commonModule.Ajax({
                    url: '/BaseProduct/SaveEditBaseProductSku',
                    data: {model:data},
                    async: true,
                    loading: true,
                    type: 'POST',
                    success: function (rsp) {
                        if (rsp.Success) {

							layer.msg('设置成功', {
                                icon: 1,
                                time: 1000
                            }, function () {
                                that.getBaseProduct();
                                //that.LoadList(true);
                                var iframe = document.getElementById('iframe_main_detail');
                                if (iframe) {
                                    iframe.contentWindow.postMessage({ hidePopUp: true}, '*');
                                }
                            });


                            that.onDialogClose();
                        } else {
                            layer.msg(rsp.Message || '失败')
                        }
                    },
                    error: function () {
                        layer.msg("网络错误，请稍后重试");
                    }
                })
            }

            // 快速编辑价格弹窗提交 (小站)
            onDialogSiteSubmit () {
                var that = this;
                var checkSkuList = this.state.checkSkuList;
                var dialogData = this.state.dialogData;
                var productSkuDetail = this.state.productSkuDetail;
				var Relations = [];

                if (checkSkuList.length) {
					checkSkuList.forEach((item, i) => {
                        var obj = {
                            "SupplierProductUid": item.SupplierProductUid,
                            "SupplierProductSkuUid": item.SupplierProductSkuUid,
                            "BaseProductUid": productSkuDetail.BaseProductUidStr,
                            "BaseProductSkuUid": productSkuDetail.UidStr,
                            "SkuCode": item.SkuCode,
                            "DistributePrice": 0,
                        }
                        obj[dialogData.checkedKeys] = dialogData.inputValue;
                        Relations.push(obj)
                    })
                    var data = {
                        Relations: Relations
                    }
                    commonModule.Ajax({
                        url: '/api/BaseProduct/EditSupplierProductSkuRelations',
                        data: data,
                        async: true,
                        loading: true,
                        type: 'POST',
                        success: function (rsp) {
                            if (rsp.Success) {
                                that.getBaseProduct();
                                that.onDialogClose();
                                that.LoadList();
                            } else {
                                layer.msg(rsp.Message || '失败')
                            }
                        },
                        error: function () {
                            layer.msg("网络错误，请稍后重试");
                        }
                    })
                }
            }

            // 快速编辑价格弹窗关闭
            onDialogClose() {
                var dialogData = this.state.dialogData;
                if(dialogData.main) {
                    dialogData.main = layer.close(dialogData.main);
                }
                var AutoRelationProces = this.state.AutoRelationProces;

				if (dialogData.type == 'autoRelation') {
					AutoRelationProces = {}
				}
                dialogData.inputValue = '';
                dialogData.supply = '';
                dialogData.isSave = false;
                dialogData.inputType = '';
                dialogData.type = '';
                dialogData.isAutoChecked = '';
                dialogData.isAutoChecked = false;
                dialogData.selectFields.forEach((iData, i) => {
                    iData.checked = false;
                    iData.disabled = false;
                })
                this.setState({ dialogData ,AutoRelationProces});
            }
            // 添加关联商品（右侧弹出）
            onAddProduct () {
                let {queryQaram,productSkuDetail,platformType,tabListEqual} = this.state;
                var _url = '/BaseProduct/BaseOfPtSkuRelation_equal_products?dbname=@(Request.QueryString["dbname"])&BaseProductUid='+queryQaram.BaseProductUid+'&BaseProductSkuUid='+queryQaram.BaseProductSkuUid+'&pt='+platformType;
                var _ifarmSrc = commonModule.rewriteUrl(_url);
                this.onCloudPlatform(platformType,'equal',true);
                tabListEqual.forEach((item,i)=>{
                    if (item.value == platformType) {
                        item.checked = true;
                    } else {
                        item.checked = false;
                    }
                })
                this.setState({ ifarmSrc: _ifarmSrc,tabListEqual },()=>{
                    var _ifarm  = $('#popUp-right #iframe_main').offset();
                    var _height = 0 ;
                    if (_ifarm) {
                        _height = _ifarm.top;
                    }
                   var iframeHeight = $(window).height() - _height - 40
                    this.setState({iframeHeight: iframeHeight > 500 ? iframeHeight : 500 })
                });
            }
            onPopUpClose(){
                this.setState({ ifarmSrc: '' });
            }
            // 关联同款 - 选择字段 (打开弹窗)
            onBindProduct () {
                var checkEqualProducts = this.state.checkEqualProducts;
                var dialogData = this.state.dialogData;
                var that = this;
                if(checkEqualProducts.length > 0) {
                    dialogData.skuCount = checkEqualProducts.length;
                    dialogData.main = layer.open({
                        type: 1,
                        title: '请选择更新字段',
                        content: $('#dialogBindProduct'),
                        skin: 'n-skin',
                        area: ['anto', 'auto'], //宽高
                        success:function(){
                        },
                        end: function () {
                            that.onDialogClose()
                        },

                    });
                    dialogData.type = 'bindProduct';
                    dialogData.isSave = true;
                    this.setState({ dialogData });
                }
            }
            // 关联同款 - 更新字段
            onDialogHandleCheckbox (item, index) {
                const { dialogData } = this.state;
                dialogData.isSave = false;
                const _checked = item.checked;
                dialogData.selectFields.forEach((iData, i) => {
                    if (item.value == 'all') {
                        iData.checked = !_checked;
                    } else if (item.value != 'all' && index == i){
                        iData.checked = !iData.checked;
                    }
                    if (iData.checked) { // 有选中保存按钮可按
                        dialogData.selectFields[0].checked = true;
                        dialogData.isSave = true;
                    }
                    if (!iData.checked && i ) { // 有字段没选全选不选中
                        dialogData.selectFields[0].checked = false;
                    }
                    if (iData.absoluteChecked) {
                        iData.checked = true;
                    }
                });
                this.setState({ dialogData });
            }
            // 关联同款 - 选择更新字段提交
            onBindProductSave () {
                var that = this;
                var dialogData = this.state.dialogData;
                var productSkuDetail = this.state.productSkuDetail;
                // 自动关联的
                if (dialogData.type && dialogData.type == 'autoRelationBindProduct') {
                    this.onAutoBindProductSave()
                    return false
                }
                // 更新商品
                if (dialogData.type == 'update') {
                    that.getUpateProducts();
                    return false;
                }
                // 已选关联同款商品
                var checkEqualProducts = this.state.checkEqualProducts;
                if (checkEqualProducts.length == 0) {
                    layer.msg('请选择关联同款商品', { icon: 2 });
                    return false;
                }
                var SkuList = [];
                checkEqualProducts.forEach(function (item) {
                    var obj = {
                        IsUseWarehouse: item.IsUseWarehouse,
                        IsDisabledUseWarehouse: item.IsDisabledUseWarehouse,
                        ProductSkuCode: item.SkuCode,
                        ProductCode: item.ProductCode,
                    }
                    SkuList.push(obj)
                })
                var data = {
                    BaseProductSkuUid: productSkuDetail.UidStr,
                    BaseProductUid: productSkuDetail.BaseProductUidStr,
                    SkuCode: productSkuDetail.SkuCode || '',
                    IsAllUseWarehouse: false,
                    IsUseDistributePrice: false,
                    IsUseSupplierFxUser: false,
                    IsUseSettlePrice: false,
                    IsUseCostPrice: false,
                    SkuList: SkuList,
                    SettlePrice: productSkuDetail.SettlePrice ||"",
                    DistributePrice: productSkuDetail.DistributePrice || "",
                    CostPrice: productSkuDetail.CostPrice || "",
                    ShortTitle: productSkuDetail.ShortTitle || "",
                    SupplierFxUserId: productSkuDetail.SupplierFxUserId || "",
                    BaseProductSkuSupplierConfigs: productSkuDetail.BaseProductSkuSupplierConfigs || ""
                }
                dialogData.selectFields.forEach((iData, i) => {
                    if(i) {
                        data[iData.value] = iData.checked;
                    }
                })
                data['IsUpdatePtSku'+dialogData.checkedKeys] = dialogData.isAutoChecked;
                commonModule.Ajax({
                    type: "POST",
                    url: "/BaseProduct/BaseProductSkuRelationBind",
                    loadingMessage: "保存中",
                    showMasker: false,
                    data: data,
                    success: function (rsp) {
                        if (rsp.Success) {
                            that.onDialogClose();
                            layer.msg(rsp.Data ||'成功', {
                                    icon: 1,
                                    time: 1000
                                }, function () {
                                    that.LoadList(true);
                                });
                        } else {
                            layer.msg(rsp.Message || '失败')
                        }
                    }
                })
            }
            onDialogCheckbox (isCheck) {
                var dialogData = this.state.dialogData;
                dialogData.isAutoChecked = !dialogData.isAutoChecked;
                this.setState({ dialogData });
            }
            getUpateProducts() {
                var that = this;
                var dialogData = this.state.dialogData;
                var productSkuDetail = this.state.productSkuDetail;
				var checkEqualProducts = this.state.checkSkuList;

				if (checkEqualProducts.length == 0) {
					layer.msg('请选择商品', { icon: 2 });
					return false;
				}
				var SkuCodes = {};
                var WarehouseRelationList = [];
				checkEqualProducts.forEach(function (item) {
                    var _baseproductuid = item.ProductCode;
                    if (!SkuCodes[_baseproductuid]) {
                        var arr = [];
                        arr.push(item.SkuCode)
                        Object.defineProperty(SkuCodes, [_baseproductuid], {
                            value: arr,
                            enumerable: true,
                            configurable: true,
                            writable: true
                        });
                    } else {
                        SkuCodes[_baseproductuid].push(item.SkuCode);
                    }

                    WarehouseRelationList.push({
                        "SkuUid": productSkuDetail.UidStr||"", // 基础商品Sku的Uid
                        "ProductSkuCode": item.SkuCode||"", // 店铺商品的SkuCode
                        "SkuCode": productSkuDetail.SkuCode || "",// 基础商品的SkuCode
                        "ShopId": item.ShopId,
                    })
				})

				// 清除后面逗号
                var data = {
					BaseProductSkuUid: productSkuDetail.UidStr,
					BaseProductUid: productSkuDetail.BaseProductUidStr,
					SkuCodes: SkuCodes,
					IsAllUseWarehouse: false,
					IsUseDistributePrice: false,
					IsUseSupplierFxUser: false,
					IsUseSettlePrice: false,
					IsUseCostPrice: false,
					SettlePrice: productSkuDetail.SettlePrice ||"",
					DistributePrice: productSkuDetail.DistributePrice || "",
					CostPrice: productSkuDetail.CostPrice || "",
                    ShortTitle: productSkuDetail.ShortTitle || "",
					BaseProductSkuSupplierConfig: productSkuDetail.BaseProductSkuSupplierConfigs || [],
                    WarehouseRelationList:WarehouseRelationList || [],
				}
				dialogData.selectFields.forEach((iData, i) => {
					if(i) {
						data[iData.value] = iData.checked;
					}
				})
				commonModule.Ajax({
					type: "POST",
					url: "/BaseProduct/SyncToPtSku",
					loadingMessage: "同步中",
					showMasker: false,
					data: {model:data},
					success: function (rsp) {
						if (rsp.Success) {
							that.onDialogClose();
                            layer.msg(rsp.Data || '成功', {
                                icon: 1,
                                time: 1000
                            }, function () {
                                that.LoadList(true);
                            });
						} else {
							layer.msg(rsp.Message || '失败')
						}
					}
				})
            }

            // 供货方式
			onColumnSupply() {
                var productSkuDetail = this.state.productSkuDetail;
                var checkSkuList = [productSkuDetail];
				var dialogData = this.state.dialogData;
				dialogData.skuCount = checkSkuList.length;
				dialogData.main = layer.open({
					type: 1,
					title: '编辑供货方式', //不显示标题
					content: $('#dialogSupply'),
					skin: 'n-skin dialogSupply-skin',
					success:function(){
						$("input[name=supply]").each(function(index, el) {
							el.checked= false;
						})
               			layui.form.render();
						initSupplierSelectBox("supplier_name_select",true);
					},
					area: '400px', //宽高
				});
				dialogData.isSave = false;
				this.setState({ dialogData,checkSkuList });
			}

			onChangeSupply (value){
				var dialogData = this.state.dialogData;
				dialogData.supply = value;
                dialogData.isSave = value || value == 0 ? true : false;
				this.setState({ dialogData });
			}
			// 绑定厂家
			onSupplySave () {
				var that = this;
				var dialogData = this.state.dialogData;
				var checkSkuList = this.state.checkSkuList;
				var list = this.state.list;
				// 厂家id
				var supplier = $('#supplier_name_select').attr('data-values');
				var obj = {};
				var NoCheckedProductUidStr = '';
				var NoCheckedSkuUidStr = '';
				if (checkSkuList.length ) {
					checkSkuList.forEach((iData, i) => {
                        var _uid = iData.UidStr;
                        var _baseproductuid = iData.BaseProductUidStr;
                        if (!obj[_baseproductuid]) {
                            var arr = [];
                            arr.push(_uid)
                            Object.defineProperty(obj, [_baseproductuid], {
                                value: arr,
                                enumerable: true,
                                configurable: true,
                                writable: true
                            });
                        } else {
                            obj[_baseproductuid].push(_uid);
                        }
					})
				}
				// 清除后面逗号
				NoCheckedProductUidStr = NoCheckedProductUidStr.substring(0, NoCheckedProductUidStr.length - 1)
				NoCheckedSkuUidStr =NoCheckedSkuUidStr.substring(0, NoCheckedSkuUidStr.length - 1)
				var reqModel = {
					SkuUidStrDic: obj,
					//SkuUidStrDic: obj,
					BaseProductConfigModels: [
						{
							//Config: type == self ? selfConfig : supplierConfig, // 绑定规则 同理商品列表页面的绑定厂家传参
							ConfigType: 0, //绑定类型：0：默认，1：地址，2：下单时间
							SupplierId: supplier, // 供应商Id
							IsDelete: dialogData.supply == 0 ? true:false, // 是否删除 设为自营时传入true

						}
					],
					BindProductType: 'all',
					// 未勾选的规格Uid，多个逗号分隔
					NoCheckedSkuUidStr: NoCheckedSkuUidStr,
					// 未勾选的商品Uid，多个逗号分隔
					NoCheckedProductUidStr: NoCheckedProductUidStr,
					isSelf: dialogData.supply == 0 ? true:false,
				}
				commonModule.Ajax({
					url: '/BaseProduct/TriggerBaseSkuBindSupplier',
					loadingMessage: "绑定中",
					showMasker: false,
					data: reqModel,
					success: function (rsp) {
						if (rsp.Success) {
							layer.msg(rsp.Data || '成功', {
                                icon: 1,
                                time: 1000
                            }, function () {
								that.getBaseProduct();
                            });
							that.onDialogClose();

						} else {
							layer.msg(rsp.Message)
						}
					}
				})
			}



            hideBindProductPopUp() {
                $("#popUp-right").addClass("hideActive");
                setTimeout(()=>{
                    this.setState({ ifarmSrc: '' });
                    var iframe = document.getElementById('iframe_main_detail');
                        if (iframe) {
                            iframe.contentWindow.postMessage({ hidePopUp: true}, '*');
                        }

                },200)
            }
            // 平台切换
            onTabChange(item,type) {
                var { tabList,iframeMainDetailHeight,tempPlatformType } = this.state;
                console.log("平台切换",item,type);
                var tempIframeMainDetailHeight = 0;
                this.setState({ isShowSite: item.value == 'MyStation' ? true:false })
                if (item.value != 'MyStation') {
                    this.onCloudPlatform(item.value,type);
                } else {
                    this.setState({ platformArea: [], ifarmDetailSrc: '' })
                    this.LoadList(true)
                }
                tabList.forEach((iData, i) => {
                    if (iData.value == item.value) {
                        iData.checked = true;
                    } else {
                        iData.checked = false;
                    }
                })

                // 只有Alibaba精选，iframe高度不同，其他平台高度不一样，需要重新计算加40px
                if(item.value != "Alibaba") {
                    var dom = $('#iframe_main_detail');
                    if (!dom.offset()) return
                    var _height = $('#iframe_main_detail').offset().top || 300;
                    if(tempPlatformType == "Alibaba") { // 判断上一个平台是Alibaba
                        tempIframeMainDetailHeight = $(window).height() - _height + 40;
                    }else {
                        tempIframeMainDetailHeight = $(window).height() - _height;
                    }
                    tempPlatformType = item.value;
                    console.log("tempIframeMainDetailHeight",tempIframeMainDetailHeight,_height);
                } else {
                    var _height = $('#iframe_main_detail').offset().top || 300;
                    tempPlatformType = item.value;
                    console.log("淘宝",_height);

                }

                this.setState({ tabList: tabList,
                    platformType: item.value,
                    isShowSite: item.value == 'MyStation' ? true : false,
                    listCount: 0,
                    iframeMainDetailHeight: tempIframeMainDetailHeight,
                    tempPlatformType
                 },() => {

                 }
                 )
            }
            // 平台切换(关联同款)
            ontabEqualChange(item,type) {
                var tabListEqual = this.state.tabListEqual;
                if (item.value != 'More') {
                    this.onCloudPlatform(item.value,type)
                }
                // 兼容关联同款
                if (type == 'equal') {
                    tabListEqual.forEach((iData, i) => {
                        if (iData.value == item.value) {
                            iData.checked = true;
                        } else {
                            iData.checked = false;
                        }
                    })
                    this.setState({ tabListEqual: tabListEqual,platformType: item.value })
                } else {
                    this.setState({ platformType: item.value})
                }
            }

            onStorageData () {
                let productSkuDetail  = this.state.productSkuDetail;
                var str = JSON.stringify(productSkuDetail);
                setTimeout(() => {
                    var iframe = document.getElementById('iframe_main_detail');
                    if (iframe) {
                        iframe.contentWindow.postMessage({ bindProductSkuDetail: productSkuDetail }, '*');
                    }

                    var iframe_main = document.getElementById('iframe_main');
                    if (iframe_main) {
                        iframe_main.contentWindow.postMessage({ bindProductSkuDetail: productSkuDetail }, '*');
                    }
                    //iframe.contentWindow.postMessage('bindProductSkuDetail', JSON.stringify(productSkuDetail));
                }, 100);

            }
            // 平台分区切换
            onPlatformAreaChange (item,type){
                var platformArea = this.state.platformArea;
                var platformType = this.state.platformType;
                var productSkuDetail = this.state.productSkuDetail;

                platformArea.forEach((iData, i) => {
                    if (iData.DbName == item.DbName) {
                        iData.checked = true;
                    } else {
                        iData.checked = false;
                    }
                })
                var src = '';
                var tempsrc = "/BaseProduct/BaseOfPtSkuRelation_detail_products?token=@(Request.QueryString["token"])&pt=" + platformType +"&dbname="+item.DbName+"&BaseProductSkuUid="+productSkuDetail.UidStr+"&BaseProductUid="+productSkuDetail.BaseProductUidStr+"&relationCount="+(Number(productSkuDetail.RelationCount) || 0);
                platformType = platformType.toLowerCase() || "";
                if (platformType == "pinduoduo") {
                    src = "@DianGuanJiaApp.Utility.CustomerConfig.PinduoduoFenFaSystemUrl" + tempsrc;
                } else {
                    src = tempsrc
                }
                this.setState({ platformArea: platformArea, ifarmDetailSrc: src })
            }
            // 平台分区切换 （关联同款）
            onPlatformAreaChangeEqual (item,type){
                var platformAreaEqual = this.state.platformAreaEqual;
                var platformType = this.state.platformType;
                var productSkuDetail = this.state.productSkuDetail;
                platformAreaEqual.forEach((iData, i) => {
                    if (iData.DbName == item.DbName) {
                        iData.checked = true;
                    } else {
                        iData.checked = false;
                    }
                })
                var src = '';
                platformType = platformType.toLowerCase() || "";
                var tempsrc = "/BaseProduct/BaseOfPtSkuRelation_equal_products?token=@(Request.QueryString["token"])&pt=" + platformType +"&dbname="+item.DbName+"&BaseProductSkuUid="+productSkuDetail.UidStr+"&BaseProductUid="+productSkuDetail.BaseProductUidStr;
                if (platformType == "pinduoduo") {
                    src = "@DianGuanJiaApp.Utility.CustomerConfig.PinduoduoFenFaSystemUrl" + tempsrc;
                } else {
                    src = tempsrc
                }
                this.setState({ platformAreaEqual: platformAreaEqual, ifarmSrc: src })
            }

            onGobackWrap() {
                var url = commonModule.rewriteUrl("/BaseProduct/NewBaseProduct?operateType=BaseProductSkuRelationBindIndex");
                window.location.href =url + '&dbname=@(Request.QueryString["dbname"])'
            };
            goTarUrl (url) {
                event.stopPropagation();
                var dbname = $("#dbname_input").val();
                var _url = url + '&dbname=' + dbname;
                window.open(commonModule.rewriteUrl(_url),'_parent')
            }
            // 切换平台
            onCloudPlatform(pt,type,first) {
                var bindSkuCode = $('#bindSkuCode-hiddeninput').val();
                var productSkuDetail = this.state.productSkuDetail;
                var BaseProductSkuUid = commonModule.getQueryVariable('baseProductSkuUid')||'';
                var BaseProductUidStr = commonModule.getQueryVariable('baseProductUid')||'';
                var RelationCount = commonModule.getQueryVariable('relationCount')|| 0 ;
                pt = pt.toLowerCase() || "";

                var src = '';
                var _dbname = "@(Request.QueryString["dbname"])" || '';


                var tempsrc = "/BaseProduct/BaseOfPtSkuRelation_detail_products?token=@(Request.QueryString["token"])&pt=" + pt+"&BaseProductSkuUid="+BaseProductSkuUid+"&BaseProductUid="+BaseProductUidStr+"&relationCount="+RelationCount;
                // 兼容关联同款
                if (type == 'equal') {
                    tempsrc = "/BaseProduct/BaseOfPtSkuRelation_equal_products?token=@(Request.QueryString["token"])&pt=" + pt+"&BaseProductSkuUid="+BaseProductSkuUid+"&BaseProductUid="+BaseProductUidStr;
                }
                if ( pt == "kuaituantuan") {
                    src = "@DianGuanJiaApp.Utility.CustomerConfig.PinduoduoFenFaSystemUrl" + tempsrc+"&dbname=@(Request.QueryString["dbname"])";
                }
                else if (pt == "jingdong") {
                    src = "@DianGuanJiaApp.Utility.CustomerConfig.JingdongFenFaSystemUrl" + tempsrc+"&dbname=@(Request.QueryString["dbname"])";
                }else if (pt == "toutiao") {
                    src = "@DianGuanJiaApp.Utility.CustomerConfig.ToutiaoFenFaSystemUrl" + tempsrc+"&dbname=@(Request.QueryString["dbname"])";
                }
                else {
                    src = tempsrc;
                }
                if (pt == "alibaba" || pt == "pinduoduo") {
                    //显示或隐藏分区
                    if (pt == "pinduoduo") {
                         src = "@DianGuanJiaApp.Utility.CustomerConfig.PinduoduoFenFaSystemUrl" + tempsrc;
                    }
                    // 兼容关联同款
                    if (type == 'equal') {
                        this.getDbAreasEqual(type, src,pt,_dbname,first);

                    }else {
                        this.getDbAreas(type, src,pt,_dbname);
                    }

                } else {
                    // 兼容关联同款
                    if (type == 'equal') {
                        this.setState({
                            platformAreaEqual: [],
                            ifarmSrc: src
                        })
                        if(first) {
                            setTimeout(function(){
                                $('#popUpChangePartner>li:eq(0)').trigger("click");
                            },100)

                        }

                    }else {
                        this.setState({
                            platformArea: [],
                            ifarmDetailSrc: src
                        })
                    }
                }

            }
            //获取数据分区信息
            getDbAreas(type, defaultSrc,pt,_dbname) {
                var platform = pt || "alibaba";
                var that = this;
                commonModule.Ajax({
                    type: "POST",
                    url: "/Common/GetDbAreas",
                    data: { cloudPlatformType: platform },
                    success: function (rsp) {
                        if (rsp == undefined || rsp == null || rsp.Data == undefined || !rsp.Success) {
                            // 兼容关联同款
                            if (type == 'equal') {
                                that.setState({ platformArea: [], ifarmSrc: defaultSrc + "&dbname="+_dbname })
                            } else {
                                that.setState({ platformArea: [], ifarmDetailSrc: defaultSrc + "&dbname="+_dbname })
                            }
                            return;
                        }
                        if (!rsp.Data.DbAreas) {
                            return false;
                        }
                        let dbareaobj = JSON.parse(rsp.Data.DbAreas);
                        if (dbareaobj && dbareaobj.length > 1) {
                            let isDefault = true;
                            dbareaobj.forEach(function (item, index) {
                                if (_dbname && item.DbName == _dbname) {
                                    item.checked = true;
                                    isDefault = false;
                                } else {
                                    item.checked = false;
                                }
                            })
                            if (isDefault) {
                                dbareaobj[0].checked = true;
                                _dbname = dbareaobj[0].DbName;
                            }
                            // 兼容关联同款
                            if (type == 'equal') {
                                that.setState({ platformArea: dbareaobj, ifarmSrc: defaultSrc + "&dbname="+_dbname })
                            }else {
                                that.setState({ platformArea: dbareaobj, ifarmDetailSrc: defaultSrc + "&dbname="+_dbname })
                            }
                        }
                        else {

                            // 兼容关联同款
                            if (type == 'equal') {
                                that.setState({ platformArea: [], ifarmSrc: defaultSrc + "&dbname="+_dbname })
                            }else {
                                that.setState({ platformArea: [], ifarmDetailSrc: defaultSrc + "&dbname="+_dbname })
                            }
                        }
                        that.oninitIframeHeight();

                    },
                });
            }
            //获取数据分区信息 (关联同款)
            getDbAreasEqual(type, defaultSrc,pt,_dbname) {
                var platform = pt || "pinduoduo";
                var that = this;

                commonModule.Ajax({
                    type: "POST",
                    url: "/Common/GetDbAreas",
                    data: { cloudPlatformType: platform },
                    success: function (rsp) {
                        if (rsp == undefined || rsp == null || rsp.Data == undefined || !rsp.Success) {
                            that.setState({ platformAreaEqual: [], ifarmSrc: defaultSrc + "&dbname="+_dbname })
                            return;
                        }
                        if (!rsp.Data.DbAreas) {
                            return false;
                        }
                        let dbareaobj = JSON.parse(rsp.Data.DbAreas);
                        if (dbareaobj && dbareaobj.length > 1) {
                            let isDefault = true;
                            dbareaobj.forEach(function (item, index) {
                                if (_dbname && item.DbName == _dbname) {
                                    item.checked = true;
                                    isDefault = false;
                                } else {
                                    item.checked = false;
                                }
                            })
                            if (isDefault) {
                                dbareaobj[0].checked = true;
                                _dbname = dbareaobj[0].DbName;
                            }
                            that.setState({ platformAreaEqual: dbareaobj, ifarmSrc: defaultSrc + "&dbname="+_dbname })
                        }
                        else {
                            that.setState({ platformAreaEqual: [], ifarmSrc: defaultSrc + "&dbname="+_dbname })

                        }

                    },
                });
            }
            // 初始化iframe高度
            oninitIframeHeight () {
                const dom = $('#iframe_main_detail');
                if (!dom.offset()) return
                var _height = $('#iframe_main_detail').offset().top || 300;
                // var iframeMainDetailHeight = $(window).height() - _height + 20;
                var iframeMainDetailHeight = $(window).height() - _height;
                console.log('初始化iframe高度',iframeMainDetailHeight);

                this.setState({iframeMainDetailHeight: iframeMainDetailHeight>300 ? iframeMainDetailHeight : 300 })
            }
            // 自动关联前置弹窗
			onDialogAutoRelationBind() {
				var productSkuDetail = this.state.productSkuDetail;
                    var checkSkuList = [productSkuDetail];
				var dialogData = this.state.dialogData;
				if(checkSkuList.length == 0) {
					layer.msg('请选择需要自动映射编码的商品数据', { icon: 2 });
					return false;
				}
				dialogData.skuCount = checkSkuList.length;
				dialogData.main = layer.open({
					type: 1,
					title: '自动关联商品',
					content: $('#dialogAutoRelationBind'),
					skin: 'n-skin',
					area: ['600px', 'auto'], //宽高
					success:function(){
						$('.explain-img').attr('src',"/Content/Images/automaticExplanation.png")
					},
				});
				dialogData.label ='';
                dialogData.type = 'autoRelationBindProduct';
                dialogData.isSave = true;
				this.setState({ checkSkuList,dialogData });
			}
			onDialogAutoRelationBindNext () {
				var updateFieldsChecked = this.state.updateFieldsChecked;
				var dialogData = this.state.dialogData;
				if (updateFieldsChecked) {
					if(dialogData.main) {
						dialogData.main = layer.close(dialogData.main);
					}
					this.setState({ dialogData }, () => {
						this.onAutoRelationBind();
					});
					return false
				}
				this.onBindProductSave()
			}
            // 自动关联
            onAutoRelationBind () {
                const { dialogData, productSkuDetail } = this.state;
                var checkSkuList = [productSkuDetail];
                var that = this;
                if(checkSkuList.length == 0) {
                    layer.msg('请选择关联同款商品', { icon: 2 });
                    return false;
                }
                dialogData.skuCount = checkSkuList.length;
                dialogData.main = layer.open({
                    type: 1,
                    title: '请选择更新字段',
                    content: $('#dialogBindProduct'),
                    skin: 'n-skin',
                    area: ['600px', 'auto'], //宽高
                    success:function(){
                    },cancel: function () {
                        that.onDialogClose()
                    },
                });
                dialogData.type = 'autoRelationBindProduct';
                this.setState({ dialogData,checkSkuList });
            }
            // 自动关联 - 更新字段
            onDialogHandleCheckbox (item,index) {
                var dialogData = this.state.dialogData;
                dialogData.isSave = false;
                var _checked = item.checked;
                dialogData.selectFields.forEach((iData, i) => {
                    if (item.value == 'all') {
                        iData.checked = !_checked;
                    } else if (item.value != 'all' && index == i){
                        iData.checked = !iData.checked;
                    }

                    if (iData.checked) { // 有选中保存按钮可按
                        dialogData.selectFields[0].checked = true;
                        dialogData.isSave = true;
                    }
                    if (!iData.checked && i ) { // 有字段没选全选不选中
                        dialogData.selectFields[0].checked = false;
                    }
                })
                this.setState({ dialogData });
            }
            // 自动关联 - 选择更新字段提交
            onAutoBindProductSave () {
                const that = this;
                const { dialogData, list } = this.state;
                // 已选关联同款商品
                let checkEqualProducts = this.state.checkSkuList;
                var productSkuDetail = checkEqualProducts[0];
                if (checkEqualProducts.length == 0) {
                    layer.msg('请选择关联同款商品', { icon: 2 });
                    return false;
                }
                var SkuList = [];
                var SkuCodeList = [];
                var skuUidStrDicObj = {};
                var NoCheckedProductUidStr = '';
                var NoCheckedSkuUidStr = '';
                checkEqualProducts.forEach(function (item) {
                    var obj = {
                        IsUseWarehouse: item.IsUseWarehouse,
                        IsDisabledUseWarehouse: item.IsDisabledUseWarehouse,
                        ProductSkuCode: item.SkuCode,
                        ProductCode: item.ProductCode,
                    }
                    var iData = item;

                            var _uid = iData.UidStr;
                            var _baseproductuid = iData.BaseProductUidStr;
                            if (!skuUidStrDicObj[_baseproductuid]) {
                                var arr = [];
                                arr.push(_uid)
                                Object.defineProperty(skuUidStrDicObj, [_baseproductuid], {
                                    value: arr,
                                    enumerable: true,
                                    configurable: true,
                                    writable: true
                                });
                            } else {
                                skuUidStrDicObj[_baseproductuid].push(_uid);
                            }
                    SkuList.push(obj);
                    SkuCodeList.push(item.SkuCode);
                })

                // 清除后面逗号
                NoCheckedProductUidStr = NoCheckedProductUidStr.substring(0, NoCheckedProductUidStr.length - 1)
                NoCheckedSkuUidStr =NoCheckedSkuUidStr.substring(0, NoCheckedSkuUidStr.length - 1)
                var data = {
                    BaseProductSkuUid: productSkuDetail.UidStr,
                    BaseProductUid: productSkuDetail.BaseProductUidStr,
                    SkuCode: productSkuDetail.SkuCode || '',
                    IsAllUseWarehouse: false,
                    IsUseDistributePrice: false,
                    IsUseSupplierFxUser: false,
                    IsUseSettlePrice: false,
                    IsUseCostPrice: false,
                    IsUseSkuShortTitle: true,
                    SkuList: SkuList,
                    SettlePrice: productSkuDetail.SettlePrice ||"",
                    DistributePrice: productSkuDetail.DistributePrice || "",
                    CostPrice: productSkuDetail.CostPrice || "",
                    ShortTitle: productSkuDetail.ShortTitle || "",
                    SupplierFxUserId: productSkuDetail.SupplierFxUserId || "",
                    BaseProductSkuSupplierConfigs: productSkuDetail.BaseProductSkuSupplierConfigs || "",
                    SkuUidStrDic: skuUidStrDicObj,
                    NoCheckedSkuUidStr: NoCheckedSkuUidStr,
                    NoCheckedProductUidStr: NoCheckedProductUidStr,
                }
                dialogData.selectFields.forEach((iData, i) => {
                    if(i) {
                        data[iData.value] = iData.checked;
                    }
                })
                commonModule.Ajax({
                    type: "POST",
                    url: "/BaseProduct/AutoRelationBind",
                    loadingMessage: "关联中",
                    showMasker: false,
                    data: data,
                    success: function (rsp) {
                        if (rsp.Success || (rsp.Data && rsp.Data.Process >= 100)) {
                            that.onDialogClose();
                            that.getAutoRelationProcess();
                        } else {
                            layer.msg(rsp.Message || '失败')
                        }
                    }
                })
            }

            // 同步进度
            getAutoRelationProcess () {
                var that = this;
                var timer = this.state.timer;
                var timerCount = that.state.timerCount;
                var CurrShop = commonModule.CurrShop
                timer = setInterval(function () {
                    ++timerCount;
                    if (timerCount > 0) {
                        commonModule.Ajax({
                            type: "POST",
                            url: "/BaseProduct/GetAutoRelationProcess",
                            showMasker: false,
                            data: {
                                fxShopId: CurrShop.Id,
                            },
                            success: function (rsp) {
                                if (rsp.Success ) {
                                    var rspData = rsp.Data
                                    // data = {
                                    // 	"TaskCode": "12dsad12d",
                                    // 	"SuccessCount": 12,
                                    // 	"FailCount": 13,   // 异常数量
                                    // 	"Process": 56, // 进度
                                    // }
                                    if (rspData == -1 || rspData.Process == 100) {
                                        if(timer) {
                                            clearInterval(timer);
                                        }
                                        that.onDialogClose();
                                        that.setState({timer,timerCount:0})
                                        that.setState({AutoRelationProces:rspData})
                                        that.LoadList(true);
                                        if ((rsp.Data &&rsp.Data.Process >= 100)) {
                                            that.onDialogAutoRelation()

                                            that.hideBindProductPopUp()
                                        }
                                    } else {

                                        that.setState({AutoRelationProces:rspData})
                                    }

                                }
                            }
                        })
                        that.setState({
                            timerCount: timerCount
                        })
                    }

                },1000)
            }
            // 同步弹窗
            onDialogAutoRelation () {
                var that = this;
                var dialogData = this.state.dialogData;
                dialogData.main = layer.open({
                    type: 1,
                    title: '关联完成', //不显示标题
                    content: $('#dialogAutoBindProduct'),
                    skin: 'n-skin',
                });
                dialogData.isSave = true;
                dialogData.type = 'autoRelation';
                this.setState({
                    dialogData: dialogData,
                })
            }
            // 中止关联
            onAbortAssociation () {
                var that = this;
                var CurrShop = commonModule.CurrShop;
                var timer = this.state.timer;
                var timerCount = that.state.timerCount;
                commonModule.Ajax({
                    type: "POST",
                    url: "/BaseProduct/BreakTask",
                    loadingMessage: "正在中止",
                    showMasker: false,
                    data: {
                        fxShopId: CurrShop.Id,
                    },
                    success: function (rsp) {
                        if (rsp.Success) {
                            layer.msg(rsp.Message ||"已成功中止任务")
                            if(timer) {
                                clearInterval(timer);
                            }

                            that.onDialogClose();
                            that.setState({timer,timerCount:0,AutoRelationProces:{}})
                        } else {
                            layer.msg(rsp.Message || '失败')
                        }
                    }
                })
            }
            // 价格格式化
            onPriceFormat(value) {
                if ( !value && value !== 0) {
                    return '-';
                } else {
                    return '￥'+ value
                }
            }
            // 更新商品
            onUpdateProduct() {
                var that = this;
                var dialogData = this.state.dialogData;
                var checkSkuList = this.onCheckSkuList();

                if (checkSkuList.length == 0) {
					layer.msg('请选择商品', { icon: 2 });
					return false;
				}
                if(checkSkuList.length > 0) {
                    dialogData.skuCount = checkSkuList.length;
                    that.getBaseProduct(function(){
                        var productSkuDetail = that.state.productSkuDetail;
                        dialogData.main = layer.open({
                            type: 1,
                            title: '请选择更新字段',
                            content: $('#dialogBindProduct'),
                            skin: 'n-skin',
                            area: ['600px', 'auto'], //宽高
                            success:function(){
                                $("#goBackskinBtn").text("保存");
                                $("#goBackskinBtn_stop").text("保存");
                            },
                            cancel: function () {
                                that.onDialogClose()
                            },
                        });
                        dialogData.selectFields.forEach((item,index)=>{
                            if(!productSkuDetail.SettlePrice && item.value == 'IsUseSettlePrice') {
                                item.disabled = true
                            }
                            if(!productSkuDetail.CostPrice && item.value == 'IsUseCostPrice') {
                                item.disabled = true
                            }
                            if(!productSkuDetail.DistributePrice && item.value == 'IsUseDistributePrice') {
                                item.disabled = true
                            }
                            if ((!productSkuDetail.BaseProductSkuSupplierConfigs == null || productSkuDetail.BaseProductSkuSupplierConfigs.length == 0) && item.value == 'IsUseSupplierFxUser') {
                                item.disabled = true
                            }
                        })
                        dialogData.type="update";
                        dialogData.label ='更新商品后，以下字段可更新为基础商品的信息:';
                        dialogData.isSave = true;
                        console.log("dialogData==",dialogData)
                        that.setState({ dialogData,checkSkuList });
                    })
                }
            }
            render() {
                const { list, platformType, tabList, theadEditStatus, allCheckSku, allIsShowSku, dialogData, productSkuDetail,queryQaram ,platformArea,platformAreaEqual ,ifarmDetailSrc,AutoRelationProces,
                iframeMainDetailHeight,tabListEqual,updateFieldsChecked,isShowSite,listCount  } = this.state
                return (
                    <div id="productAssociation">
                        <div className="header flex">
                            <div className="gobackWrap" onClick={()=>this.onGobackWrap()}>
                                <div className="hover-back-icon">
                                    <i className="iconfont icon-xiayi"></i>
                                </div>
                                <span className="h-title">同款明细</span>
                            </div>
                        </div>
                        <div className="container ">
                            <div className="container-l boxcss" style={{height:'fit-content'}}>
                                <div className="title">基础商品</div>
                                <div className="goodsinfo flex" style={{alignItems:'flex-start'}}>
                                    <div className="goodsinfo-img">
                                        <img src={productSkuDetail.ImageUrl || '/Content/images/nopic.gif'} />
                                    </div>
                                    <div className="goodsinfo-text">
                                        <div className="goodsinfo-text-title ellipsis" title={productSkuDetail.ProSubject}>{productSkuDetail.ProSubject}</div>
                                        <div className="goodsinfo-text-sku c90 ellipsis" title={productSkuDetail.AttributeValue}>{productSkuDetail.AttributeValue}</div>
                                    </div>
                                </div>
                                <div className="container-l-list">
                                    <div className="list-item flex hover" onClick={()=>this.onColumnPrice('SettlePrice')}>
                                        <div className="list-item-label c06 f14">采购价</div>
                                        <div className="list-item-text c09 f14 flex"  >
                                            <span>{productSkuDetail.SettlePrice || productSkuDetail.SettlePrice == 0 ? '￥'+productSkuDetail.SettlePrice : '-'}</span>
                                            <i className="iconfont icon-a-edit1x hovericon"></i>
                                        </div>
                                    </div>
                                    <div className="list-item flex hover" onClick={()=>this.onColumnPrice('CostPrice')}>
                                        <div className="list-item-label c06 f14">成本价</div>
                                        <div className="list-item-text c09 f14 flex">
                                            <span>{productSkuDetail.CostPrice|| productSkuDetail.CostPrice == 0 ? '￥'+productSkuDetail.CostPrice : '-'}</span>
                                             <i className="iconfont icon-a-edit1x hovericon"></i>
                                        </div>
                                    </div>
                                    <div className="list-item flex hover" onClick={()=>this.onColumnPrice('DistributePrice')}>
                                        <div className="list-item-label c06 f14">分销价</div>
                                        <div className="list-item-text c09 f14 flex">
                                            <span>{productSkuDetail.DistributePrice|| productSkuDetail.DistributePrice == 0 ? '￥'+productSkuDetail.DistributePrice : '-'}</span>
                                             <i className="iconfont icon-a-edit1x hovericon"></i>
                                        </div>
                                    </div>
                                    @*<div className="list-item flex">
                                        <div className="list-item-label c06 f14">库存</div>
                                        <div className="list-item-text c09 f14 ">
                                            <span>{productSkuDetail.StockCount|| productSkuDetail.StockCount == 0 ? productSkuDetail.StockCount : '-'}</span>
                                        </div>
                                    </div>*@
                                    <div className="list-item flex hover" onClick={()=>this.onColumnSupply()}>
                                        <div className="list-item-label c06 f14 hover">供货方式</div>
                                        {
                                            productSkuDetail.SupplierFxUserId == 0?
                                                <div className="list-item-text c09 f14 flex " >
                                                <span>未设置</span>
                                                <i className="iconfont icon-a-edit1x hovericon"></i>
                                            </div>
                                            : <div className="list-item-text c09 f14 flex  " >
                                            <span style={{maxWidth: '150px'}} title={productSkuDetail.SupplierName} className="ellipsis">{productSkuDetail.SupplierFxUserId == -1 ? '自营' :productSkuDetail.SupplierFxUserId == 0? '未设置':productSkuDetail.SupplierName }</span>
                                            <i className="iconfont icon-a-edit1x hovericon"></i>
                                        </div>
                                        }


                                    </div>
                                    <div className="list-item flex hover" onClick={()=>this.onColumnPrice('ShortTitle')}>
                                        <div className="list-item-label c06 f14">简称</div>
                                        <div className="list-item-text c09 f14 ellipsis flex" >
                                            <span className="ellipsis">{productSkuDetail.ShortTitle}</span>
                                            <i className="iconfont icon-a-edit1x hovericon"></i>
                                        </div>
                                    </div>
                                    <div className="list-item flex" style={{height: 'auto'}}>
                                        <div className="list-item-label c06 f14">SKU 编码</div>
                                        <div className="list-item-text c09 f14" style={{lineHeight: 'normal'}} title={productSkuDetail.SkuCode}>
                                            <span className="skucode">{productSkuDetail.SkuCode}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="container-r boxcss" id="noDataList" style={{height:'228px',display:'none'}}>
                                <div class="n-carMain">
                                    <div class="n-carMain-left">
                                        <div class="n-font3">添加商品</div>
                                        <div class="n-font5">选择要关联的同款商品，即可通过基础商品统一维护。</div>
                                        <div class="n-carMain-left-btns" style={{paddingTop:'8px'}}>
                                            <span class="n-mButton" onClick={() => this.onAddProduct()}>关联商品</span>
                                            {
                                                AutoRelationProces && AutoRelationProces.Process >= 0 && AutoRelationProces.Process < 100 ?
                                                <div class="btn n-mButton " style={{marginLeft:'16px'}} >自动关联中 {AutoRelationProces.Process}%</div>
                                                :<div class="btn n-mButton n-sActive"style={{marginLeft:'16px'}}  onClick={()=>this.onDialogAutoRelationBind()}>自动关联</div>
                                            }
                                        </div>
                                    </div>
                                    <div class="n-carMain-right hover" style={{backgroundImage:"url(/Content/Images/noviceIntroPic/relationProduct0626.jpg)"}} onClick={()=>window.open('https://xtx0o8yn7x.feishu.cn/docx/VRLMdwPGXoWCP7xjvqfcgQLhnPf?from=from_copylink')}><span class="n-font5"></span></div>
                                </div>
                            </div>

                            <div className="container-r boxcss" id="hasDataList">
                                <div className="container-r-header flex">
                                    <span className="title">已关联规格{productSkuDetail.RelationCount ? "("+ productSkuDetail.RelationCount +")":''}</span>
                                    <div className="headerBtns flex">
                                        {
                                            AutoRelationProces && AutoRelationProces.Process >= 0 && AutoRelationProces.Process < 100 ?
                                            <div class="btn n-mButton ">自动关联中 {AutoRelationProces.Process}%</div>
                                            :<div class="btn n-mButton n-sActive" onClick={()=>this.onDialogAutoRelationBind()}>自动关联</div>
                                        }
                                        <div className="btn n-mButton" onClick={()=>this.onAddProduct()}>关联商品</div>
                                    </div>
                                </div>

                                <div className="layui-tab layui-tab-brief " style={{ margin: '0'}}>
                                    <ul className="layui-tab-title" id="changePartner">
                                        {
                                            tabList.length && tabList.map((item, index) => {
                                                return (
                                                    <li className={item.checked ? "layui-this" : ''}
                                                        data-type={item.value} onClick={() => this.onTabChange(item,'')}
                                                        key={item.id}
                                                    >
                                                        {item.name}({item.count})
                                                    </li>
                                                )
                                            })
                                        }
                                    </ul>
                                </div>
                                {
                                    platformArea.length ?
                                        <div className="platform-area">
                                        <ul className="flex">
                                            {
                                                platformArea.length && platformArea.map((item, index) => {
                                                    return (
                                                        <li className={item.checked ? "hover active" : 'hover'}
                                                            data-type={item.value} onClick={() => this.onPlatformAreaChange(item)}
                                                            key={item.DbName}
                                                        >
                                                            {item.NickName}
                                                        </li>

                                                    )
                                                })
                                            }
                                        </ul>
                                    </div>:null
                                }
                                {
                                    ifarmDetailSrc?

                                    <div style={{display:'flex'}}><iframe id="iframe_main_detail" style={{flex:1,border:'none',height:this.state.iframeMainDetailHeight+"px" }} src={ifarmDetailSrc}></iframe>{this.onStorageData()}</div>
                                    :null
                                }
                                {
                                    isShowSite ?
                                    <div id="smallShop" style={{ height:this.state.iframeMainDetailHeight+"px" }}>
                                        <div className="query_form" style={{display:'none'}}>
                                            <div class="automaticAssociation hover" style={theadEditStatus?{color:'#000'}:{}} onClick={() => this.onUpdateProduct()} >更新商品信息</div>
                                        </div>
                                        <div className="layui-mytable">
                                            <table className="n-table" style={{ marginTop: '16px' }}>
                                                <thead>
                                                    {
                                                        theadEditStatus ?
                                                            <tr >
                                                                <th style={{ width: '16px' }}>
                                                                    <div className="flex" style={{ textAlign: 'left' }}>
                                                                        <input type="checkbox" lay-skin="primary" name="allCheckbox" checked={allCheckSku} onChange={() => this.onCheckAllSku()} />
                                                                    </div>
                                                                </th>
                                                                <th style={{ minWidth: '200px' }}>
                                                                    <div className="" style={{ textAlign: 'left' }}>
                                                                        <span>商品信息</span>
                                                                    </div>
                                                                </th>

                                                                <th style={{width: '100px'}} >
                                                                    <div className=" flex hover t-r" onClick={()=>this.onColumnSitePrice('DistributePrice')} style={{justifyContent:"end"}}>
                                                                        <span>分销价</span>
                                                                        <i className="iconfont icon-a-edit1x hovericon" ></i>
                                                                    </div>
                                                                </th>
                                                                <th style={{minWidth: '200px'}} ></th>
                                                            </tr>
                                                            :
                                                            <tr>
                                                                <th style={{ width: '120px' }}>
                                                                    <div className="" style={{ textAlign: 'left' }}>
                                                                    <input type="checkbox" lay-skin="primary" name="allCheckbox" checked={allCheckSku} onChange={() => this.onCheckAllSku()} />
                                                                        <span>规格来源</span>
                                                                    </div>
                                                                </th>

                                                                <th style={{ minWidth: '200px' }}>
                                                                    <div className="" style={{ textAlign: 'left' }}>
                                                                        <span>商品信息</span>
                                                                    </div>
                                                                </th>

                                                                <th style={{width: '100px'}}>
                                                                     <div className="t-r flex" style={{justifyContent: "end"}}>
                                                                         <span>分销价</span>
                                                                         <i className="commonWenHao02 popoverCommon hovericon iconfont icon-a-help-circle1x" style={{ backgroundColor: 'transparent' }}>
                                                                             <span className="popoverCommon-warn popoverCommon-warn04" style={{ width: '260px', top: '22px', bottom: 'auto' }}>分销价应用于财务结算对下游分销商设置的规格单品结算价</span>
                                                                         </i>
                                                                     </div>
                                                                </th>
                                                                <th style={{minWidth: '200px'}} ></th>
                                                            </tr>
                                                    }
                                                </thead>
                                                <tbody >
                                                    {
                                                    this.state.list && this.state.list.length > 0 ? this.state.list.map((item, index) => {
                                                            var listItem = []
                                                            listItem[0] = <tr key={item.index}>
                                                                <td >
                                                                    <div className="flex">
                                                                        <input type="checkbox" name="productCheckbox" lay-skin="primary" checked={item.isCheckSku} onChange={() => this.onCheckSubAllSku(item, index)} />
                                                                    </div>
                                                                </td>
                                                                <td >
                                                                    <div className="productWrap flex">
                                                                        <div className="productShow flex">
                                                                            <img src={item.ImageUrl || '/Content/images/nopic.gif'} className="productShow-img" />
                                                                            <ul className="product-title grid" style={{textAlign: 'left'}}>
                                                                                <li className="f12 grid-column">{item.Subject}</li>
                                                                                {
                                                                                    item.ShortTitle ? <li className="ellipsis grid-column fz12 c06">简称：{item.ShortTitle}</li>:null
                                                                                }
                                                                                {
                                                                                    item.PlatformTypeStr ? <li className="ellipsis grid-column fz12 c06">规格来源：{item.PlatformTypeStr}</li>:null
                                                                                }
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td className="hover t-r"  >
                                                                    <div className='flex t-r' onClick={()=>this.onColumnSitePrice('DistributePrice',item)} style={{justifyContent:"end"}}>
                                                                        {this.onPriceFormat(item.DistributePrice)}<i className="iconfont icon-a-edit1x hovericon" ></i>
                                                                    </div>
                                                                </td>
                                                                <td></td>
                                                            </tr>
                                                        return listItem
                                                        }) :
                                                            <tr>
                                                                <td colSpan="20" className="tdNodata" style={{ padding: "24px 16px" }}>
                                                                    @* <div className="tableNoDataShow">
                                                                        <img src="/Content/images/noData-icon.png" /><span className="tableNoDataShow-title">暂无数据！</span>
                                                                    </div> *@
                                                                    <div className="n-tableNoDataShow">
                                                                        <span className="iconfont icon-wushuju1"></span>
                                                                        <span className="tableNoDataShow-title">未找到符合条件的内容</span>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                    }
                                                </tbody>
                                            </table>

                                            <div className="layui-myPage" id="stationPage"></div>
                                            @* <div className="n-footerWrap">
                                                <div className="n-footerWrap-right">
                                                    <div className="layui-myPage" id="stationPage"></div>
                                                </div>
                                            </div> *@
                                        </div>
                                    </div>
                                    : null
                                }
                            </div>
                        </div>

                        <div id="dialogPrice" style={{ display: 'none'}}>
                            <div class="dialog-wrap">
                                <div className="title">{ dialogData.label }</div>
                                <div className="dialog-wrap-cont">
                                    {
                                        dialogData.inputType == 'text' ?
                                        <div class="item-column-input flex">
                                            <input type="text" value={dialogData.inputValue} onChange={(event)=>this.onDialogHandleChange(event)}/>
                                        </div>
                                        :<div class="item-column-input flex">
                                        <span>￥</span>
                                        <input type="number" value={dialogData.inputValue} onChange={(event)=>this.onDialogHandleChange(event)}/>
                                    </div>
                                    }
                                    {
										dialogData.checkedKeys == 'ShortTitle' || dialogData.type == 'site' ? <div></div>
										:<div className="flex" style={{ marginTop: '8px' }}>
											<input type="checkbox" lay-skin="primary" name="productCheckbox" checked={dialogData.isAutoChecked} onChange={() => this.onDialogCheckbox(dialogData.isAutoChecked)} />
											同步更新到已关联商品
										</div>
									}
                                </div>
                            </div>
                            <div class="dialogBtns flex">
                                <div></div>
                                <div class="btnBox flex">
                                    <div class="n-mButton n-sActive" onClick={()=>this.onDialogClose()}>取消</div>
                                    {
                                        dialogData.isSave ? <div class="n-mButton" onClick={()=>this.onDialogSubmit()}>保存</div> : <div class="n-mButton stop" >保存</div>
                                    }
                                </div>
                            </div>
                        </div>
                        <div id="dialogAutoBindProduct" style={{ display: 'none'}}>
                                <div class="dialog-wrap">
                                    <div className="dialog-wrap-cont flex">
                                        <div className="dialog-wrap-cont-statusbox flex">
                                            <span className="count">{AutoRelationProces.SuccessCount || 0}</span>
                                            <span>关联成功</span>
                                        </div>
                                        <div className="dialog-wrap-cont-statusbox flex">
                                            <span className="count" style={{color: "#EA572E"}}>{AutoRelationProces.FailCount || 0}</span>
                                            <span>关联异常</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="dialogBtns flex">
                                    <div></div>
                                    <div class="btnBox flex">
                                        <div class="n-mButton n-sActive" onClick={()=>this.onDialogClose()}>关闭</div>
                                        <div class="n-mButton" style={{background: '#EA572E',display: 'none'}} onClick={()=>this.onAbortAssociation()}>中止关联</div>
                                    </div>
                                </div>
                            </div>
                        <div id="dialogBindProduct" style={{ display: 'none'}}>
                            <div class="dialog-wrap" style={{maxHeight: this.state.bodyHeight ? (this.state.bodyHeight > 300 ? this.state.bodyHeight-100 : 300) + 'px' : '500px'}}>
                                <div className="title">{dialogData.label ||''}</div>
                                <div className="dialog-wrap-cont">
                                    <div className="table-field">
                                        {
                                            dialogData.selectFields.map((item,index)=>{
                                                var listItem = <div class="item-column-checkbox flex">
                                                    <div className="flex item-column-checkbox-input">
                                                        <input type="checkbox" className="hover" disabled={item.absoluteChecked || item.disabled} checked={item.absoluteChecked ||item.checked} onChange={(event)=>this.onDialogHandleCheckbox(item,index)}/>
                                                    </div>
                                                    <div>
                                                        <div className="item-column-checkbox-title hover" disabled={item.absoluteChecked || item.disabled} onClick={(event)=>this.onDialogHandleCheckbox(item,index)}>{ item.name }</div>
                                                        {
                                                            item.tips ?
                                                            <div className="flex tips">
                                                                <i class="iconfont icon-a-info-circle1x"></i>
                                                                <span style={{ marginLeft: '4px'}}>{item.tips}</span>
                                                            </div>
                                                            : ''
                                                        }
                                                    </div>
                                                </div>
                                                return listItem
                                            })
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="dialogBtns flex">
                                <div>对 <span class="count">{ dialogData.skuCount }</span> 个规格生效</div>
                                <div class="btnBox flex">
                                    <div class="n-mButton n-sActive" onClick={()=>this.onDialogClose()}>取消</div>
                                    {
                                        dialogData.isSave ? <div id="goBackskinBtn" class="n-mButton" onClick={()=>this.onBindProductSave()}>保存</div> : <div id="goBackskinBtn_stop" class="n-mButton stop" >保存</div>
                                    }
                                </div>
                            </div>
                        </div>

                        <div id="dialogSupply" style={{ display: 'none'}}>
							<div class="dialog-wrap">
								<div className="title">{ dialogData.label }</div>
								<div className="dialog-wrap-cont">
									<label className="flex" for="">
										<input type="radio" name="supply" value='0' checked={dialogData.supply == '0' ? true : false} onChange={()=>this.onChangeSupply(0)} title="自营供货" /><span className="radioSpan">自营供货</span>
									</label>
									<label className="flex" for="">
										<input type="radio" name="supply" value='1' checked={dialogData.supply == '1' ? true : false} onChange={()=>this.onChangeSupply(1)}  title="厂家供货" /><span className="radioSpan">厂家供货</span>
										<div id="supplier_name_select" class="Supplier selectWrap" name="supplier-name-select" style={dialogData.supply == '1' ? {display:'block'}: { display: 'none' }}></div>
									</label>
								</div>
							</div>
							<div class="dialogBtns flex">
								<div>对 <span class="count">{ dialogData.skuCount }</span> 个规格生效</div>
								<div class="btnBox flex">
									<div class="n-mButton n-sActive" onClick={()=>this.onDialogClose()}>取消</div>
									{
										dialogData.isSave ? <div class="n-mButton" onClick={()=>this.onSupplySave()}>保存</div> : <div class="n-mButton stop" >保存</div>
									}
								</div>
							</div>
						</div>

                        <div id="dialogAutoRelationBind" style={{ display: 'none'}}>
							<div class="dialog-wrap">
								<div className="dialog-wrap-cont">
									<div className="explain flex">
										<img className="explain-img" src="/Content/Images/automaticExplanation.png" className="img"/>
										<div>
											<p className="n-font3">高效关联同款商品</p>
											<p style={{color: "rgba(0, 0, 0, 0.6)"}}>系统将识别店铺商品中相同的 SKU 编码，进行自动关联。</p>
										</div>
									</div>
								</div>
							</div>
							<div class="dialogBtns flex">
								<div></div>
								<div class="btnBox flex">
									<input type="checkbox" checked={updateFieldsChecked} onChange={(event)=>this.setState({updateFieldsChecked:!updateFieldsChecked})} />
                                    <span>更新商品信息</span>
									<div class="n-mButton n-sActive" onClick={()=>this.onDialogClose()}>取消</div>
									{
										dialogData.isSave ? <div id="goBackskinBtn" class="n-mButton goBackskinBtn" onClick={()=>this.onDialogAutoRelationBindNext()}>开始关联</div> : <div id="goBackskinBtn_stop" class="n-mButton goBackskinBtn stop" >开始关联</div>
									}
								</div>
							</div>
						</div>

                        {
                            this.state.ifarmSrc ?
                            <div id="popUp-right">
                            <div className="popUp-hidebox" onClick={()=>this.hideBindProductPopUp()}></div>
                                <div className="popUp-container">
                                    <div className="popUp-title flex">
                                        <div className="popUp-title-left">
                                            <span class="c09">选择同款商品</span>
                                        </div>
                                        <div className="popUp-title-right">
                                            <i className="iconfont icon-chuyidong hover" onClick={()=>this.hideBindProductPopUp()}></i>
                                        </div>
                                    </div>
                                     <div className="layui-tab layui-tab-brief n-tab" style={{ margin: '0'}}>
                                            <ul className="layui-tab-title" id="popUpChangePartner">
                                                {
                                                    tabListEqual.length && tabListEqual.map((item, index) => {
                                                        return (
                                                            <li className={item.checked ? "layui-this" : ''}
                                                                data-type={item.value} onClick={() => this.ontabEqualChange(item,'equal')}
                                                                key={item.id+'equal'}
                                                            >
                                                                {item.name}
                                                            </li>

                                                        )
                                                    })
                                                }
                                            </ul>
                                        </div>
                                        {
                                            platformAreaEqual.length ?
                                                <div className="platform-area">
                                                    <ul className="flex">
                                                        {
                                                            platformAreaEqual.length && platformAreaEqual.map((item, index) => {
                                                                return (
                                                                    <li className={item.checked ? "hover active" : 'hover'}
                                                                        data-type={item.value} onClick={() => this.onPlatformAreaChangeEqual(item)}
                                                                        key={item.DbName}
                                                                    >
                                                                        {item.NickName}
                                                                    </li>
                                                                )
                                                            })
                                                        }
                                                    </ul>
                                                </div>:null
                                        }

                                    <iframe id="iframe_main" src={this.state.ifarmSrc}>{this.onStorageData()}</iframe>

                                </div>
                            </div>
                            : null
                        }
                    </div>
                )
            }
        }

        // 2.渲染组件到页面
        ReactDOM.render(<BaseProductSkuRelationBindIndex />, document.getElementById('reactAppBindSku'))
    </script>
}