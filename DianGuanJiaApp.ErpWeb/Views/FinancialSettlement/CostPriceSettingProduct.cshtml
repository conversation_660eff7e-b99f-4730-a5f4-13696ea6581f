@{
    ViewBag.Title = "成本结算价设置";
    Layout = "~/Views/Shared/_CloudPlatformLayout.cshtml";
    ViewBag.MenuId = "FinancialSettlement_PriceSetting";
}
@section Header{
    <style>
        .icon-tianjia {
            margin-right: 3px;
        }

        .layui-mysearch {
            padding: 15px;
            background-color: #f8f8f8;
        }

        .layui-mysearch .layui-form {
            width: unset;
            display: flex;
            flex-wrap: wrap;
        }

        .layui-mysearch .layui-form .mysearch-partOne input {
            width: 160px;
        }

        .mysearch-partOne {
            margin-right: 0;
        }

        .mysearch-partOne .layui-input-inline {
            margin-right: 10px;
        }

        .mysearch-partOne {
            margin-bottom: 0;
        }

        .mysearch-partOne {
            margin-right: 10px;
        }

        .mysearch-partOne .layui-input-inline {
            width: 160px;
        }

        .mysearch-partTwo {
            display: flex; /* flex-direction: column; */
            padding-left: 15px;
            border-left: 1px solid #e2e2e2;
            height: 35px;
            width: 175px;
            flex-direction: row;
            /* justify-content: space-between; */
        }

        .mysearch-partTwo .layui-btn + .layui-btn {
            margin-left: 0;
        }

        .mysearch-styleTwo {
            display: flex;
            align-items: center;
        }

        .mysearch-styleTwo > span {
            width: 75px;
        }

        .showOrHideSku {
            display: flex;
            align-items: center;
            margin: 0 15px;
            /* color: #3aadff; */
        }

        .stockup_table_content thead tr {
            background-color: #f6f9fd;
        }

        .stockup_table_content {
            border-left: none;
        }

        .stockup_table_content thead tr th,
        .stockup_table_content tbody tr td {
            border-right: none;
            border-top: 1px solid #e5e9f2;
            border-bottom: 1px solid #e5e9f2;
        }

        .layui-mywrap .productShow {
            display: flex;
            align-items: center;
        }

        .productShow > img {
            width: 60px;
            height: 60px;
            margin: 0 5px;
        }

        .productShow > ul {
            height: 60px;
            justify-content: space-around;
            display: flex;
            flex-direction: column;
        }

        .productSkuTr {
            background-color: #f8f8f8;
        }

        .skuProductShow {
            display: flex;
            align-items: center;
            padding-left: 65px;
        }

        .skuProductShow > img {
            margin: 0 5px;
            width: 50px;
            height: 50px;
        }

        .skuProductShow > ul {
            height: 50px;
            display: flex;
            flex-direction: column;
        }

        .productSkuTr .hideSkuSpan {
            cursor: pointer;
            color: #3aadff;
            margin-left: 135px;
        }

        .productSkuTr .hideSkuSpan > i {
            display: inline-block;
            transform: rotate(180deg);
            font-size: 12px;
            margin-left: 5px;
        }

        .hideSkuTr {
            display: none;
        }

        .stockup_table_content tbody tr.productSkuTr td {
            border-top: none !important;
            border-bottom: none !important;
        }

        .stockup_table_content tbody tr.productSkuTr td.hideTd {
            border-top: 1px solid #e5e9f2 !important;
            border-bottom: 1px solid #e5e9f2 !important;
        }

        .stockup_table_content tbody tr td.tableOparete {
            text-align: right;
        }

        .stockup_table_content tbody tr td.tableOparete > a {
            border-right: 1px solid #ddd;
            padding-right: 5px;
            display: inline-block;
        }

        .stockup_table_content tbody tr td.tableOparete > a:last-child {
            border-right: none;
            padding-right: 0;
        }

        .PriceSettingNumWrap .Edit {
            height: 30px;
            line-height: 30px;
            width: 60px;
        }

        .PriceSettingNumWrap .unEdit {
            border: none;
            background-color: #f8f8f8;
            color: #f29a1a
        }

        .productSkuTr.skuItem {
            border-bottom: 1px solid #f0f0f0;
        }

        .goBack {
            color: #999
        }

        .goBack:hover {
            color: #3aadff
        }

        .Edit[value*="未设置"] {
            color: #666
        }

        .skudel, .skudel:hover {
            color: #ff511c;
        }

        .noProductData {
            text-align: center;
            margin-top: 40px;
        }

        .noProductData .icon-wushuju {
            font-size: 50px;
            margin-right: 5px;
        }

        .settlementPriceLogDailog {
            min-height: 200px;
            padding: 15px;
            box-sizing: border-box;
            background-color: #fff;
            display: none;
        }

        .settlementPriceLogDailog-nodata {
            width: 100%;
            height: 205px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .settlementPriceLogDailog-nodata .icon-wushuju {
            font-size: 60px;
            margin-right: 10px;
        }

        .settlementPriceLogDailog-hasdata-item {
            padding: 10px 10px 0 10px;
            background-color: #fff6e8;
            margin-bottom: 15px;
        }

        .settlementPriceLogDailog-hasdata-item > li {
            padding-bottom: 10px;
        }

        .settlementPriceLogDailog-hasdata-item > li .settlementPriceLogDailog-title {
            color: #04385d;
        }

        .icon-xiayi {
            display: inline-block;
            transform: rotate(90deg);
            position: relative;
            top: 1px;
        }

        .layui-mysearch {
            padding-bottom: 0;
        }

        .mysearch-partOne {
            margin-bottom: 15px;
        }

        .layui-mytable .layui-mytable-title {
            /* margin-bottom: 10px; */
            margin-bottom: 16px;
        }

        .batch-priceWrap {
            display: flex;
            flex-direction: column;
        }

        .batch-priceWrap .batch-priceWrap-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-bottom: 10px;
        }

        .batch-priceWrap .batch-priceWrap-item:last-child {
            margin-bottom: 0;
        }

        .batch-priceWrap .batch-priceWrap-item .batch-priceWrap-item-title {
            width: 80px;
            text-align: right;
        }

        .batch-priceWrap .batch-priceWrap-item .pltipmsg {
            flex: 1;
            max-height: 50px;
            overflow-y: auto;
        }

        .batchSetPriceBtn {
            background-color: #77bf04;
            padding: 0 15px;
        }

        .batchSetPriceBtn:hover {
            opacity: 1 !important;
        }

        .batchSetPriceBtn:hover .popoverCommon-warn {
            opacity: 1;
        }

        .batchSetPriceBtn .popoverCommon-warn {
            word-break: break-all;
            background: #303133;
            color: #fff;
            opacity: 1;
            bottom: 34px;
        }

        .batchSetPriceBtn .popoverCommon-warn span {
            font-size: 12px;
            text-align: left;
            display: flex;
        }

        .batchSetPriceBtn .popoverCommon-warn:after {
            border-top-color: #303133;
        }

        .warn-wrap {
            display: flex;
            justify-content: space-between;
        }

        .batchDelSkuBtn {
            font-size: 12px;
            color: #666;
            cursor: pointer;
            position: relative;
            top: 12px;
            display: flex;
            align-items: center;
        }

        .batchDelSkuBtn .iconfont {
            margin-right: 3px;
            font-size: 14px;
        }

        .operateLogoDailog {
            width: 468px;
            /* padding: 15px; */
            background-color: #fff;
            box-sizing: border-box;
        }

        .operateLogoDailog .operateLogoDailog-main {
            display: flex;
            flex-direction: column;
            max-height: 400px;
            overflow-y: auto;
        }

        .operateLogoDailog .operateLogoDailog-main .operateLogoDailog-main-item {
            display: flex;
            flex-direction: column;
            margin-bottom: 15px;
            background-color: #fff6e8;
            padding: 8px;
            border-radius: 6px;
            background: rgba(0, 0, 0, 0.05);
        }

        .operateLogoDailog .operateLogoDailog-main .operateLogoDailog-main-item > div {
            padding: 5px;
        }

        .operateLogoDailog .operateLogoDailog-main .operateLogoDailog-main-item > div > span:nth-child(1) {
            /* color: #04385d; */
            margin-right: 3px;
            color: rgba(0, 0, 0, 0.7);
        }
        .operateLogoDailog .operateLogoDailog-main .operateLogoDailog-main-item > div > span:nth-child(2) {
            color: rgba(0, 0, 0, 0.9);
        }
        .operateLogoDailog .operateLogoDailog-main .operateLogoDailog-main-item:last-child {
            margin-bottom: 0;
        }

        .sku-edmit-price {
            display: flex;
            flex-direction: column;
        }

        .sku-edmit-price .sku-edmit-price-item {
            display: flex;
            flex-direction: row;
            margin-bottom: 10px;
        }

        .sku-edmit-price .sku-edmit-price-item:last-child {
            margin-bottom: 0;
        }

        .sku-edmit-price .sku-edmit-price-item > div {
            flex: 1;
            padding: 0 10px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
        }

        .flex-center {
            display: flex;
            align-items: center;
        }

        .mT16 {
            margin-top: 16px;
        }

        .mT8 {
            margin-top: 8px;
        }

        .mL4 {
            margin-left: 4px;
        }

        .mL8 {
            margin-left: 8px;
        }

        .mL24 {
            margin-left: 24px;
        }

        .addWarnInput {
            border: 1px solid #fe6f4f !important;
        }

        .disabledInput {
            background: rgba(0, 0, 0, 0.04);
            color: rgba(0, 0, 0, 0.3);
            pointer-events: none;
        }

        .warnInput {
            border: 1px solid #EA572E !important;
        }
        .cost-price-set-product-title {
            background: none;
            box-shadow: none;
        }  
        .cost-price-set-product-main {
            background: none;
        } 
        .cost-price-set-product-main .wu-layui-select {
            width: unset;
        }
        .cost-price-set-product-main .wu-tableWrap .wu-table-one .tableNoDataShow .tableNoDataShow-title:before {
            margin: 0 auto;
        }
        
    </style>
}
<div class="layui-mywrap cost-price-set-product-title wu-mB0" style="min-height: unset; padding-bottom: 15px;">
    <div>
        <span class="wu-flex wu-f20 wu-weight600"><a class="goBack wu-flex wu-yCenter" id="goBack" target="_top" href="@(DianGuanJiaApp.Utility.CustomerConfig.AlibabaFenFaSystemUrl)/FinancialSettlement/PriceSetting?targetFrom=costPrice" onclick="CostPriceSettingProductModule.targetToSetLoction()"><i class="iconfont icon-xiayi wu-c09 wu-mR8"></i><span class="wu-c09 go-back">返回</span><span class="mLR10 wu-c09">/</span></a><span class="wu-c09">设置成本价</span></span>
    </div>
</div>
<div class="layui-mywrap cost-price-set-product-main wu-p0 wu-mT0" style="margin-bottom: 70px;">
    <div class="layui-mysearch wu-color-n wu-background wu-8radius" id="SearchContainer" style="margin-bottom:15px;">
        <form class="layui-form wu-searchWrap wu-p0">
            <div class="layui-inline mysearch-partOne wu-layui-select wu-form-mid" style="display:none">
                <select id="productStatusSelectId" lay-filter="active-select-filter">
                    <option value="0" selected="selected">全部商品</option>
                    <option value="1">上架商品</option>
                    <option value="2">下架商品</option>
                </select><div class="layui-unselect layui-form-select"><div class="layui-select-title"><input type="text" placeholder="请选择" value="全部商品" readonly="" class="layui-input layui-unselect"><i class="layui-edge"></i></div><dl class="layui-anim layui-anim-upbit"><dd lay-value="0" class="">全部商品</dd><dd lay-value="1" class="layui-this">上架商品</dd><dd lay-value="2" class="">下架商品</dd></dl></div>
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-inputWrap wu-form-mid">
                <input id="inputProductNameId" type="text" class="layui-input wu-input" placeholder="请输入商品名称">
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-inputWrap wu-form-mid">
                <input name="shortTitleName" class="layui-input wu-input" id="shortTitleNameId" placeholder="请输入商品简称">
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-inputWrap wu-form-mid">
                <input name="nameGui" id="nameGuiId" type="text" class="layui-input wu-input" placeholder="请输入规格简称">
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-inputWrap wu-form-mid">
                <input name="skuName" id="skuName" type="text" class="layui-input wu-input" placeholder="请输入规格属性">
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-inputWrap wu-form-mid">
                <input name="skuId" id="skuId" type="text" class="layui-input wu-input" placeholder="批量输入SKUID,隔开">
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-inputWrap wu-form-mid">
                <input name="cargoNumber" id="cargoNumber" type="text" class="layui-input wu-input" placeholder="请输入规格Sku编码">
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-layui-select wu-form-mid">
                <select id="productPriceelectId" lay-filter="active-select-filter">
                    <option value="0" selected="selected"> 成本价是否价设置 </option>
                    <option value="1">已设置</option>
                    <option value="2">未设置</option>
                </select>
            </div>
        </form>
        <div class="layui-inline mysearch-partTwo">
            <button type="button" class="layui-btn layui-btn-normal layui-btn35 wu-btn wu-btn-mid wu-primary wu-two wu-mR8" id="btn-query" onclick="CostPriceSettingProductModule.Search();">查询</button>
            <button type="button" class="layui-btn layui-btn-primary layui-btn35 wu-btn wu-btn-mid wu-primary" id="btn-reset" onclick="CostPriceSettingProductModule.Reset();">重置</button>
        </div>
    </div>
    <div class="layui-mytable wu-color-n wu-background wu-8radius wu-p16">
        <div class="layui-mytable-title">
            @*<button type="button" onclick="CostPriceSettingProductModule.syncCostPriceSetting()" class="layui-btn layui-btn-sm layui-btn-normal" style="padding:0 15px;">同步分销价为成本价</button>*@
            <button onclick="CostPriceSettingProductModule.unifyCostPriceSetting()" type="button" class="layui-btn layui-btn-sm layui-btn-normal wu-btn wu-btn-mid" style="padding:0 15px;">批量设置成本价</button>
            @*<button onclick="CostPriceSettingProductModule.batchCostPriceSetting()" type="button" class="layui-btn layui-btn-sm layui-btn-normal" style="padding:0 15px;">批量设置成本价</button>*@
        </div>
        <div class="wu-tableWrap min1200">
            <table class="stockup_table_content wu-table-one" id="productlist">
            <thead>
                <tr>
                    <th style="min-width:200px;">
                        <div style="display:flex;">
                            <label style="display: flex;align-items: center;">商品名称/SKU规格</label>
                            <label class="showOrHideSku wu-checkboxWrap wu-hover ">
                                <input class="wu-mR8 wu-f12 wu-c09" type="checkbox" id="showOrHideSku" onchange="CostPriceSettingProductModule.showOrHideSku(this)">展开规格
                            </label>
                        </div>
                    </th>
                    @*<th style="min-width:100px;">创建时间</th>*@
                    <th style="min-width:100px;">商品/SkuId</th>
                    <th style="min-width:100px;">商品/Sku编码</th>
                    <th style="min-width: 100px;">成本价</th>
                    <th style="min-width: 100px;">结算厂家</th>
                    <th style="min-width: 80px; text-align: right;">对厂家结算价</th>
                    <th style="min-width: 80px; text-align: right; ">厂家结算价</th>
                    <th style="min-width:100px;">操作</th>
                </tr>
            </thead>
            <tbody id="PriceSettingClound_tbody_data"></tbody>
            </table>
        </div>
        <div class="layui-myPage" id="paging"></div>
    </div>
</div>
<div class="layui-footer" style="left: 0px; padding-right: 0px; z-index: 10;">
    <div class="layui-footer-btns">
        @*<button type="button" class="layui-btn layui-btn-normal" style="background-color: #9e9e9e;" onclick="CostPriceSettingProductModule.cancelEdit()">取消编辑</button>*@
        <button type="button" class="layui-btn layui-btn-normal wu-btn wu-btn-mid" onclick="CostPriceSettingProductModule.saveEdit()">保存当前页修改</button>
    </div>
</div>

<!-- 价格设置 商品列表模样 -->
<script id="PriceSettingClound_data" type="text/x-jsrender">
    {{if Products.length>0}}
    {{for Products}}
    <tr>
        <td>
            <div class="productShow">
                <input class="order-chx product-chx" type="checkbox" data-pid="{{:Id}}" onchange="CostPriceSettingProductModule.checkAllSku(this)">
                {{if ImageUrl}}
                <img src="{{:ImageUrl}}">
                {{else}}
                <img src="/Content/images/nopic.gif">
                {{/if}}
                <ul>
                    <li>{{:Subject}}</li>
                    {{if ShortTitle}}
                    <li style="color:#888" title="商品简称">{{:ShortTitle}}</li>
                    {{/if}}
                    <li id="upOrDown{{:Id}}" class="dColor hover upOrDown zk" onclick="CostPriceSettingProductModule.showMoreSize(this,'{{:Id}}')">收起规格</li>
                </ul>
            </div>
        </td>
        <td>{{:PlatformId}}</td>
        <td>{{:CargoNumber}}</td>
        <td></td>
        <td>{{:SupplerName}}</td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    {{for Skus ~ProductId=Id ~PlatformType=PlatformType ~ShopId=ShopId}}
    <tr class="productSkuTr skuItem skuTr{{:~ProductId}}" id="skuItemTr_{{:SettlementSkuUniqueKey}}">
        <td>
            <div class="skuProductShow">
                <input class="order-chx sku-chx" name="sku-chx" type="checkbox" data-pid="{{:~ProductId}}" data-productcode="{{:ProductCode}}" data-skucode="{{:SkuCode}}">
                {{if ImgUrl}}
                <img src="{{:ImgUrl}}">
                {{else}}
                <img src="/Content/images/nopic.gif">
                {{/if}}
                <ul>
                    <li>
                        <span class="attributes-litem">{{:(AttributeValue1 || "") +'  '+(AttributeValue2 || "")}}</span>
                    </li>
                    {{if ShortTitle}}
                    <li style="color:#888" title="规格简称">{{:ShortTitle}}</li>
                    {{/if}}
                </ul>
            </div>
        </td>
        @*<td><span class="attributes-litem">{{:SettlementSkuCreateTime}}</span></td>*@
        <td><span class="attributes-litem">{{:SkuId}}</span></td>
        <td><span class="attributes-litem">{{:CargoNumber}}</span></td>
        <td>
            <div class="PriceSettingNumWrap">
                <input
                    type="text"
                    value="{{:CostPrice}}"
                    data-id="{{:CostPriceId}}"
                    data-skuId="{{:SkuId}}"
                    data-plattype="{{:~PlatformType}}"
                    data-curprice="{{:CostPrice}}"
                    data-pcode="{{:ProductCode}}"
                    data-scode="{{:SkuCode}}"
                    class="layui-input n-layui-input editing"
                    placeholder="请输入"
                    style="width: 120px; font-size: 12px;"
                    onblur="CostPriceSettingProductModule.blurCostPriceSetting(this)"
                />
                <div class="input-warnTitle">最大7位整数，2位小数</div>
                @*class="Edit unEdit" readonly*@
            </div>
        </td>
        <td colspan="3" style="padding:8px 0;">
            <ul class="sku-edmit-price">
                {{if SupplierSettlementModels && SupplierSettlementModels.length > 0 }}
                    {{for SupplierSettlementModels }}
                    <li class="sku-edmit-price-item">
                        <div style="width: 214px; max-width: 214px;">
                            {{:SupplierName}}
                        </div>
                        <div style="justify-content: flex-end; width: 110px;">
                            {{:SettlementPrice}}
                        </div>
                        <div style="justify-content: flex-end; width: 110px;">
                            {{:OppositeSettlementPrice}}
                        </div>
                    </li>
                    {{/for}}
                {{/if}}
            </ul>
        </td>
        <td><span class="dColor hover" onclick="CostPriceSettingProductModule.costPriceLogs('{{:CostPriceId}}')">成本价日志</span></td>
    </tr>
    {{/for}}
    <tr class="productSkuTr skuTr{{:Id}}">
        <td class="hideTd" colspan="20"><span class="hideSkuSpan" onclick="CostPriceSettingProductModule.showMoreSize(this,'{{:Id}}',true)">点击收起规格<i class="iconfont icon-down"></i></span></td>
    </tr>
    {{/for}}
    {{else}}
    <tr>
        <td colspan="20" class="tdNodata">
            <div class="tableNoDataShow"><img src="/Content/images/noData-icon.png"><span class="tableNoDataShow-title">此店铺还未有商品数据</span></div>
        </td>
    </tr>
    {{/if}}
</script>

<!-- 成本价日志 弹窗-->
<script id="operateLogo_data" type="text/x-jsrender">
    <div class="operateLogoDailog">
        {{if data.length==0}}
        <div class="settlementPriceLogDailog-nodata"> <i class="iconfont icon-wushuju"></i><span style="color:#f59c1a;font-size:14px;">暂无数据，还未设置默认成本价！</span></div>
        {{else}}
        <ul class="operateLogoDailog-main">
            {{for data}}
            <li class="operateLogoDailog-main-item">
                <div>
                    <span class="mai">操作时间:</span>
                    <span>{{:CreateTime}}</span>
                </div>
                <div>
                    <span>操作账号:</span>
                    <span>{{:Mobile}}({{:NickName}})</span>
                </div>
                <div>
                    <span>设置价格:</span>
                    <span>（旧）{{:Price_Old}} -->（新）{{:Price}}</span>
                </div>
                <div>
                    <span>变更类型：</span>
                    <span>{{:ChangeTypeText}}</span>
                </div>
            </li>
            {{/for}}
        </ul>
        {{/if}}
    </div>
</script>

<!-- 批量设置成本价 -->
<div id="set_cost_price_wrapper_layer" style="display: none;">
    <div class="n-font5">
        <div class="flex-center">
            <input type="radio" name="ChangePriceType" value="1" class="hover" checked /><span class="mL4">价格修改为</span>
            <input type="text"
                   class="layui-input n-layui-input mL4"
                   style="width: 120px;"
                   placeholder="请输入新价格"
                   value=""
                   id="NewPriceInput"
                   onblur="checkPriceInputVal.bind(this)('NewPrice')"
                   onfocus="focusPriceInputVal.bind(this)('NewPrice')" />
            <span class="mL4">元</span>
            <div class="input-warnTitle mL8" id="show_price_warn_tip" style="font-size: 12px;">错误提示</div>
        </div>
        <div class="mT16">
            <div class="flex-center">
                <input type="radio" name="ChangePriceType" value="2" class="hover" /><span class="mL4">公式改价</span>
            </div>
            <div style="display: none;" id="show_formula_price_content">
                <div class="mT8" style="padding-left: 18px; box-sizing: border-box; display: flex;">
                    <div class="flex-center" style="height: 32px;">
                        <span>按所选商品</span>
                        <div class="n-mySelect n-single n-myCommonSelect mL4" style="width: 120px;" id="price_type_select">
                            <div class="n-mySelect-title">
                                <div class="n-mySelect-title-left">
                                    <span class="n-mySelect-title-left-title"></span>
                                    <span class="n-mySelect-title-placeholder">请选择</span>
                                    <span class="n-mySelect-title-chooseItem"></span>
                                </div>
                                <i class="iconfont icon-a-chevron-down1x"></i>
                            </div>
                            <div class="n-mySelect-showContent">
                                <ul class="n-mySelect-showContent-ul">
                                    <li class="n-mySelect-showContent-ul-li" data-value="1" onclick="batchPriceComSelectOptions.bind(this)('Price')">成本价</li>
                                    <li class="n-mySelect-showContent-ul-li" data-value="2" onclick="batchPriceComSelectOptions.bind(this)('Price')">对厂家结算价</li>
                                    <li class="n-mySelect-showContent-ul-li" data-value="3" onclick="batchPriceComSelectOptions.bind(this)('Price')">厂家结算价</li>
                                </ul>
                            </div>
                        </div>
                        <span class="mL4">价格</span>
                    </div>
                    <div>
                        <div class="n-mySelect n-single n-myCommonSelect mL4" style="width: 90px;" id="price_compute_symbol">
                            <div class="n-mySelect-title">
                                <div class="n-mySelect-title-left">
                                    <span class="n-mySelect-title-left-title"></span>
                                    <span class="n-mySelect-title-placeholder">请选择</span>
                                    <span class="n-mySelect-title-chooseItem"></span>
                                </div>
                                <i class="iconfont icon-a-chevron-down1x"></i>
                            </div>
                            <div class="n-mySelect-showContent">
                                <ul class="n-mySelect-showContent-ul">
                                    <li class="n-mySelect-showContent-ul-li" data-value="1" onclick="batchPriceComSelectOptions.bind(this)('ComputeSymbol')">加</li>
                                    <li class="n-mySelect-showContent-ul-li" data-value="2" onclick="batchPriceComSelectOptions.bind(this)('ComputeSymbol')">减</li>
                                    <li class="n-mySelect-showContent-ul-li" data-value="3" onclick="batchPriceComSelectOptions.bind(this)('ComputeSymbol')">乘</li>
                                </ul>
                            </div>
                        </div>
                        <div class="input-warnTitle mL4" id="show_symbol_warn_tip" style="font-size: 12px;">错误提示</div>
                    </div>
                    <div>
                        <input type="text"
                               class="layui-input n-layui-input mL4"
                               style="width: 130px;"
                               placeholder="请输入"
                               value=""
                               id="FormulaPriceInput"
                               onblur="checkPriceInputVal.bind(this)('FormulaPrice')"
                               onfocus="focusPriceInputVal.bind(this)('FormulaPrice')" />
                        <div class="input-warnTitle mL4" id="show_number_warn_tip" style="font-size: 12px;">错误提示</div>
                    </div>
                    <div class="mL4 flex-center" id="ComputeUnit" style="height: 32px;">元</div>
                    <div style="display: none; height: 32px;" id="show_decimal_point_select">
                        <div class="flex-center">
                            <div class="n-mySelect n-single n-myCommonSelect mL4" style="width: 140px;" id="price_decimal_point">
                                <div class="n-mySelect-title">
                                    <div class="n-mySelect-title-left">
                                        <span class="n-mySelect-title-left-title"></span>
                                        <span class="n-mySelect-title-placeholder">请选择</span>
                                        <span class="n-mySelect-title-chooseItem"></span>
                                    </div>
                                    <i class="iconfont icon-a-chevron-down1x"></i>
                                </div>
                                <div class="n-mySelect-showContent">
                                    <ul class="n-mySelect-showContent-ul">
                                        <li class="n-mySelect-showContent-ul-li" data-value="0" onclick="batchPriceComSelectOptions.bind(this)('DecimalPoint')">不保留小数点</li>
                                        <li class="n-mySelect-showContent-ul-li" data-value="1" onclick="batchPriceComSelectOptions.bind(this)('DecimalPoint')">保留1位小数点</li>
                                        <li class="n-mySelect-showContent-ul-li" data-value="2" onclick="batchPriceComSelectOptions.bind(this)('DecimalPoint')">保留2位小数点</li>
                                    </ul>
                                </div>
                            </div>
                            <span>（四舍五入）</span>
                        </div>
                        <div class="input-warnTitle mL4" id="show_point_warn_tip" style="font-size: 12px;">小数点错误提示</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(function () {
        // 分页
        layui.laypage.render({
            elem: 'paging',
            theme: ' wu-page wu-one',
            count: 50,
            limit: 20,
            curr: 1,
            limits: [50, 100, 200, 300, 400, 500],
            layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
            jump: function (obj, first) {

            }
        });
        $('#goBack').hover(
            function() {
                
                $('.cost-price-set-product-title .goBack i').css({"color": "#0888ff"});
                $('.cost-price-set-product-title .goBack span').css({"color": "#0888ff"});
            },
            function() {
                $('.cost-price-set-product-title .goBack i').css({"color": "rgba(0, 0, 0, 0.9)"});
                $('.cost-price-set-product-title .goBack span').css({"color": "rgba(0, 0, 0, 0.9)"});
            }
        )
        wuFormModule.initblurInput('.wu-inputWrap');
        wuFormModule.initLayuiSelect('active-select-filter');
    });
    var batchPriceComSelectOptions = null;
    var checkPriceInputVal = null;
    var focusPriceInputVal = null;
    
    var CostPriceSettingProductModule = (function (module, commmon, $, layer) {
        var zkSettingKey = "/FinancialSettlement/CostPriceSettingProduct";
        var shopid = '@(Request.QueryString["shopid"])';
        // var moneyreg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/; //金额正则
        var moneyreg = /^([0-9]{1,7}(\.[0-9]{0,2})?)$/;
        var _pageIndex = 1, _pageSize = window.PageSize || 20;
        var CostPriceSetProductList = []; // 列表数据
        var selectSymbolValue = '';
        var selectPointValue = '';
        var selectPriceTypeValue = '1'; // 价格类型

        var reqModel = {
            PageIndex: _pageIndex,
            PageSize: _pageSize,
            //ProductType: "all",
            //ProductStatus:0,
            SelectShopId: shopid,
            InputProductName:'',
            ProductShortTitle: '',
            ProductSkuShortTitle: '',
            ProductSkuName: '',
            SkuId: '',
            SkuCargoNumber: '',// skucodes
            IsDeleted: false
        };

        $(function () {
            module.LoadList(false);
            initCommonEvent();
        });
        // 重置批量设置结算价弹框表单
        function resetSetPriceDialogFormData() {
            $('input[name="ChangePriceType"][value="1"]').prop('checked', true);
            $("#NewPriceInput").prop('disabled', false).removeClass("disabledInput").removeClass("addWarnInput").val('');
            $("#show_price_warn_tip").css({ display: 'none' }).text('');
            $("#show_formula_price_content").hide();
            $("#price_type_select").addClass("hasActive").find(".n-mySelect-title-chooseItem").text("成本价");
            $("#price_compute_symbol").removeClass("hasActive addWarnInput").find(".n-mySelect-title-chooseItem").text("");
            $("#price_decimal_point").removeClass("hasActive addWarnInput").find(".n-mySelect-title-chooseItem").text("");
            $("#FormulaPriceInput").removeClass("addWarnInput").val('');
            $("#ComputeUnit").text('元');
            $("#show_decimal_point_select").hide();
            $("#show_number_warn_tip").css({ display: 'none' }).text('');
            $("#show_symbol_warn_tip").css({ display: 'none' }).text('');
            $("#show_point_warn_tip").css({ display: 'none' }).text('');

            $(".n-myCommonSelect .n-mySelect-showContent-ul .n-mySelect-showContent-ul-li").removeClass("selected-li-active");
            $('#price_type_select ul.n-mySelect-showContent-ul li:first').addClass("selected-li-active").siblings().removeClass("selected-li-active");
        }
        // 初始化事件
        function initCommonEvent() {
            $(".n-myCommonSelect .n-mySelect-title").on("click", function (event) {
                event.stopPropagation();
                $(".n-myCommonSelect").removeClass("active");
                $(this).closest(".n-myCommonSelect").addClass("active");
                $(this).closest(".n-myCommonSelect").find(".n-mySelect-showContent-ul-li").css({ display: 'flex' });
            });
            $('input[name="ChangePriceType"]').on('change', function () {
                // 获取选中的 radio 按钮的值
                var selectedValue = $('input[name="ChangePriceType"]:checked').val();
                if (selectedValue == '2') {
                    resetSetPriceDialogFormData();
                    $("#show_formula_price_content").show();
                    $("#NewPriceInput").prop('disabled', true).addClass("disabledInput");
                    $('input[name="ChangePriceType"][value="2"]').prop('checked', true);
                } else {
                    resetSetPriceDialogFormData();
                    $("#show_formula_price_content").hide();
                    $("#NewPriceInput").prop('disabled', false).removeClass("disabledInput");
                }
                // 设置当前被点击的 radio 按钮为选中状态
                $(this).prop('checked', true);
                // 设置其他 radio 按钮为未选中状态
                $('input[name="ChangePriceType"]').not(this).prop('checked', false);
            });
        }

        // 点击document
        $(document).on("click", function () {
            $(".n-myCommonSelect").removeClass("active");
        });
        // 公用的选择
        batchPriceComSelectOptions = function (type) {
            var text = $(this).text();
            $(this).closest(".n-mySelect").addClass("hasActive").find(".n-mySelect-title-chooseItem").html(text);
            $(this).addClass("selected-li-active").siblings().removeClass("selected-li-active");
            if (type == 'ComputeSymbol') {
                selectSymbolValue = '';
                selectSymbolValue = $(this).attr("data-value");
                $("#FormulaPriceInput").val('');
                if (selectSymbolValue == '3') {
                    $("#ComputeUnit").text('%');
                    $("#show_decimal_point_select").show();
                } else {
                    $("#ComputeUnit").text('元');
                    $("#show_decimal_point_select").hide();
                }
                $("#price_compute_symbol").removeClass("addWarnInput");
                $("#show_symbol_warn_tip").css({ display: 'none' }).text('');
                $("#price_decimal_point").removeClass("addWarnInput");
                $("#show_point_warn_tip").css({ display: 'none' }).text('');
            }
            if (type == 'DecimalPoint') {
                selectPointValue = '';
                selectPointValue = $(this).attr("data-value");
                $("#price_decimal_point").removeClass("addWarnInput");
                $("#show_point_warn_tip").css({ display: 'none' }).text('');
            }
            if (type == 'Price') {
                selectPriceTypeValue = $(this).attr("data-value");
            }
        }
        checkPriceInputVal = function (typeName) {
            var value = $(this).val().trim();
            if (typeName == 'NewPrice' && value) {
                if (!commonModule.validateInputNumLen(value)) {
                    $(this).addClass("addWarnInput");
                    $("#show_price_warn_tip").css({ display: 'block' }).text('最大7位整数，2位小数');
                }
            }
            if (typeName == 'FormulaPrice' && value) {
                if (!commonModule.validateInputNumLen(value)) {
                    $(this).addClass("addWarnInput");
                    $("#show_number_warn_tip").css({ display: 'block' }).text('最大7位整数，2位小数');
                }
            }
        }
        // 输入框聚焦时，移除样式和警告
        focusPriceInputVal = function (typeName) {
            $(this).removeClass("addWarnInput");
            if (typeName == 'NewPrice') {
                $("#show_price_warn_tip").css({ display: 'none' }).text('');
            }
            if (typeName == 'FormulaPrice') {
                $("#show_number_warn_tip").css({ display: 'none' }).text('');
            }
        }

        // 渲染模版
        function renderTableDataTemplate(data) {
            var tplt = $.templates("#PriceSettingClound_data");
            var html = tplt.render({
                Products: data
            });
            $("#PriceSettingClound_tbody_data").html(html);
        }

        //$(module.LoadList.bind(null, false));
        module.LoadList = function (isPaging, callBack) {
            // module.cancelEdit();
            // reqModel.SelectShopId = 1627;
            reqModel.InputProductName = $.trim($("#inputProductNameId").val());
            reqModel.ProductShortTitle = $.trim($("#shortTitleNameId").val());
            reqModel.ProductSkuShortTitle = $.trim($("#nameGuiId").val());
            reqModel.ProductSkuName = $("#skuName").val();
            reqModel.SkuId = $("#skuId").val();
            reqModel.SkuCargoNumber = $("#cargoNumber").val();
            var isSetCost = $("#productPriceelectId").val();
            commonModule.Ajax({
                url: '/FinancialSettlement/GetCostPriceProductList',
                loadingMessage: "加载中...",
                data: {
                    query:reqModel,
                    isSetCost:isSetCost
                },
                // async: true,
                loading: true,
                type: 'POST',
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return;
                    }
                    CostPriceSetProductList = rsp.Data.Rows || [];
                    CostPriceSetProductList.forEach(function (item) {
                        item.Skus.forEach(function (sku) {
                            sku.SupplierSettlementModels.forEach(function (value) {
                                if (value.OppositeSettlementPrice === "未设置") {
                                    value.OppositeSettlementPrice = '';
                                }
                                if (value.SettlementPrice === "未设置") {
                                    value.SettlementPrice = '';
                                }
                            });
                        });
                    });
                    var index = 1;
                    commonModule.Foreach(CostPriceSetProductList, function (i, obj) {
                        obj.Index = index;
                        index++;
                    });
                    renderTableDataTemplate(CostPriceSetProductList);

                    if (typeof callBack === "function") {
                        callBack();
                    }
                    var $guigeAllCheckbox = $("#showOrHideSku");
                    commonModule.LoadCommonSetting(zkSettingKey, true, function (rsp) {
                        if (rsp.Success) {
                            if (rsp.Data == 1) {
                                $guigeAllCheckbox.prop("checked", true);
                                // 分步渲染表格内容
                                var secondrows = [];
                                var thirdrows = [];
                                $(CostPriceSetProductList).each(function (index, p) {
                                    $("#PriceSettingClound_tbody_data tr.skuTr" + p.Id).removeClass("hideSkuTr");
                                    $("#upOrDown" + p.Id).addClass("zk").text("收起规格");
                                });
                                setTimeout(function () {
                                    $(secondrows).each(function (index, p) {
                                        $("#PriceSettingClound_tbody_data tr.skuTr" + p.Id).removeClass("hideSkuTr");
                                        $("#upOrDown" + p.Id).addClass("zk").text("收起规格");
                                    });
                                }, 30);
                                setTimeout(function () {
                                    $(thirdrows).each(function (index, p) {
                                        $("#PriceSettingClound_tbody_data tr.skuTr" + p.Id).removeClass("hideSkuTr");
                                        $("#upOrDown" + p.Id).addClass("zk").text("收起规格");
                                    });
                                }, 60);
                            }
                            else {
                                $guigeAllCheckbox.prop("checked", false).change();
                            }
                        }
                    });
                    if (!isPaging) {
                        layui.laypage.render({
                            elem: 'paging',
                            theme: ' wu-page wu-one',
                            count: rsp.Data.Total,
                            limit: reqModel.PageSize,
                            curr: reqModel.PageIndex,
                            limits: [20, 50, 100, 200, 300, 500],
                            layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                            jump: function (obj, first) {
                                if (!first) {
                                    reqModel.PageSize = obj.limit;
                                    reqModel.PageIndex = obj.curr;
                                    module.LoadList(true);
                                }
                            }
                        });
                    }
                }
            });
        };

        module.showMoreSize = function (_this, id, isTrue) {
            event.stopPropagation();
            if (!isTrue) {
                if ($(_this).hasClass("zk")) {
                    $("#productlist tr.skuTr" + id).addClass("hideSkuTr");
                    $(_this).removeClass("zk").text("展开规格");
                    $(_this).closest("tr").find(".setWareHouseItem").removeClass("zk");

                } else {
                    $("#productlist tr.skuTr" + id).removeClass("hideSkuTr");
                    $(_this).addClass("zk").text("收起规格");
                }
            } else {
                $("#productlist tr.skuTr" + id).addClass("hideSkuTr");
                $("#upOrDown" + id).removeClass("zk").text("展开规格");
                $(_this).closest("tr").find(".setWareHouseItem").removeClass("zk");
            }
        }
        // 成本价输入校验
        module.blurCostPriceSetting = function (_this) {
            var tempval = $.trim($(_this).val());
            if (tempval != '' && !moneyreg.test(tempval)) {
                $(_this).closest(".PriceSettingNumWrap").find(".n-layui-input").addClass("warnInput").siblings(".input-warnTitle").show();
            } else {
                $(_this).closest(".PriceSettingNumWrap").find(".n-layui-input").removeClass("warnInput").siblings(".input-warnTitle").hide();
            }
        }
        // 不保留小数点, 保留 1 位小数, 保留 2 位小数
        function getFormattedResults(value, decimalPlaces) {
            // 使用 Math.round() 四舍五入的方式
            var multiplier = Math.pow(10, decimalPlaces); // 计算十的幂次
            var result = (Math.round(value * multiplier) / multiplier).toFixed(decimalPlaces); // 四舍五入后除以相应的十的幂次
            return result;
        }
        // 获取计算结果渲染
        function getComputeResultFn(way, count, selectedSkuCodes) {
            if (way == 'ManualInput') {
                CostPriceSetProductList.forEach(function (item) {
                    item.Skus.forEach(function (sku) {
                        if (selectedSkuCodes.includes(sku.SkuCode)) {
                            sku.CostPrice = String(Number(count).toFixed(2));
                        }
                    });
                });
            }
            if (way == 'FormulaInput') {
                var countIndex = 0;
                CostPriceSetProductList.forEach(function (item) {
                    item.Skus.forEach(function (sku) {
                        if (selectedSkuCodes.includes(sku.SkuCode)) {
                            if (countIndex < count.length) {
                                if (count[countIndex]) {
                                    sku.CostPrice = count[countIndex];
                                }
                                countIndex++;
                            }
                        }
                    });
                });
            }
            renderTableDataTemplate(CostPriceSetProductList); // 渲染render模版
        }
        // id去重
        function filterIds(list) {
            var ids = [];
            ids = list.filter(function (value, index, self) {
                return self.indexOf(value) === index;
            });
            return ids;
        }
        // 批量设置成本价
        module.unifyCostPriceSetting = function () {
            var $checkedList = $(".productShow .product-chx:checked");
            var $checkedSkuList = $(".skuProductShow .sku-chx:checked");
            if ($checkedList.length == 0 && $checkedSkuList.length == 0) {
                commonModule.w_alert({ type: 3, content: '请先勾选需要修改的商品/规格' });
                return false;
            }
            var list = $checkedSkuList;
            var selectedSkuCodes = [];
            // 遍历所有勾选中的 checkbox，并将其 data-skucode 添加到数组中
            list.each(function () {
                selectedSkuCodes.push($(this).data('skucode'));
            });
            console.log("selectedSkuCodes", selectedSkuCodes);

            layer.open({
                type: 1,
                title: '批量设置成本价', // 标题
                content: $('#set_cost_price_wrapper_layer'),
                offset: '160px',
                area: ['750px', '350px'], // 宽高
                skin: 'n-skin',
                success: function (layerp) {
                    selectPriceTypeValue = '1'; // 价格类型
                    resetSetPriceDialogFormData();
                    layerp.find(".layui-layer-content").css({'overflow': 'visible'});
                },
                btn: ['取消', '确定'],
                btn2: function (index, layero, that) {
                    var initialSelectedValue = $('input[name="ChangePriceType"]:checked').val();
                    // 手动输入价格
                    var newPriceVal = $("#NewPriceInput").val();
                    if (initialSelectedValue == '1') {
                        if (!newPriceVal) {
                            $("#NewPriceInput").addClass("addWarnInput");
                            $("#show_price_warn_tip").css({ display: 'block' }).text('请输入新价格');
                            return false;
                        }
                        if (!commonModule.validateInputNumLen(newPriceVal)) {
                            $("#NewPriceInput").addClass("addWarnInput");
                            $("#show_price_warn_tip").css({ display: 'block' }).text('最大7位整数，2位小数');
                            return false;
                        }
                        getComputeResultFn('ManualInput', newPriceVal, selectedSkuCodes);
                    }
                    // 公式改价
                    var formulaPriceVal = $("#FormulaPriceInput").val();
                    if (initialSelectedValue == '2') {
                        var SymbolText = $("#price_compute_symbol").find(".n-mySelect-title-chooseItem").text().trim();
                        if (SymbolText == '') {
                            $("#price_compute_symbol").addClass("addWarnInput");
                            $("#show_symbol_warn_tip").css({ display: 'block' }).text('请选择');
                            return false;
                        }
                        if (!formulaPriceVal) {
                            $("#FormulaPriceInput").addClass("addWarnInput");
                            $("#show_number_warn_tip").css({ display: 'block' }).text('请输入数字');
                            return false; // 点击该按钮后不关闭弹层
                        }
                        if ($("#price_decimal_point").find(".n-mySelect-title-chooseItem").text().trim() == '' && selectSymbolValue == '3') {
                            $("#price_decimal_point").addClass("addWarnInput");
                            $("#show_point_warn_tip").css({ display: 'block' }).text('请选择');
                            return false;
                        }
                        if (!commonModule.validateInputNumLen(formulaPriceVal)) {
                            $("#FormulaPriceInput").addClass("addWarnInput");
                            $("#show_number_warn_tip").css({ display: 'block' }).text('最大7位整数，2位小数');
                            return false;
                        }
                        var CheckSkuRow = [];
                        // 成本价
                        if (selectPriceTypeValue == '1') {
                            CostPriceSetProductList.forEach(function (item) {
                                item.Skus.forEach(function (sku) {
                                    if (selectedSkuCodes.includes(sku.SkuCode)) {
                                        CheckSkuRow.push(sku.CostPrice);
                                    }
                                });
                            });
                        }
                        // 对厂家结算价 / 厂家结算价
                        if (selectPriceTypeValue == '2' || selectPriceTypeValue == '3') {
                            for (var i = 0; i < CostPriceSetProductList.length; i++) {
                                var item = CostPriceSetProductList[i];
                                for (var j = 0; j < item.Skus.length; j++) {
                                    var sku = item.Skus[j];
                                    if (selectedSkuCodes.includes(sku.SkuCode)) {
                                        var isHaveData = sku.SupplierSettlementModels === null || sku.SupplierSettlementModels.length === 0;
                                        if (isHaveData) {
                                            sku.SupplierSettlementModels = [];
                                            sku.SupplierSettlementModels.push({
                                                SettlementPrice: '',
                                                OppositeSettlementPrice: ''
                                            });
                                        }
                                        if (selectPriceTypeValue == '2') {
                                            CheckSkuRow.push(sku.SupplierSettlementModels[0].SettlementPrice);
                                        }
                                        if (selectPriceTypeValue == '3') {
                                            CheckSkuRow.push(sku.SupplierSettlementModels[0].OppositeSettlementPrice);
                                        }
                                    }
                                }
                            }
                        }
                        // 使用 $.map() 修改每个值并返回新的数组
                        var AddSubMultiResult = [];
                        if (selectSymbolValue == '1') {
                            AddSubMultiResult = $.map(CheckSkuRow, function (value) {
                                // 如果是空值，保持不变，直接返回该值
                                if (value == '' || value == null) {
                                    return value;
                                }
                                // 其他值正常进行计算
                                return (Number(value) + Number(formulaPriceVal)).toFixed(2);
                            });
                        }
                        if (selectSymbolValue == '2') {
                            AddSubMultiResult = $.map(CheckSkuRow, function (value) {
                                // 如果是空值，保持不变，直接返回该值
                                if (value == '' || value == null) {
                                    return value;
                                }
                                // 其他值正常进行计算
                                return (Number(value) - Number(formulaPriceVal)).toFixed(2);
                            });
                        }
                        if (selectSymbolValue == '3') {
                            AddSubMultiResult = $.map(CheckSkuRow, function (value) {
                                // 如果是空值，保持不变，直接返回该值
                                if (value == '' || value == null) {
                                    return value;
                                }
                                // 其他值正常进行计算
                                return getFormattedResults((Number(value) * (Number(formulaPriceVal) / 100)), Number(selectPointValue));
                            });
                        }
                        // 检查公式计算结果是否为负数
                        var hasNegative = AddSubMultiResult.some(function (num) {
                            return num < 0;
                        });
                        if (hasNegative) {
                            commonModule.w_alert({ type: 3, content: '公式计算结果不能为负数' });
                            return false;
                        }
                        getComputeResultFn('FormulaInput', AddSubMultiResult, selectedSkuCodes);
                    }
                    commonModule.w_alert({ type: 4, content: '操作成功' });
                    layer.close(index);
                },
            });
            /** 
                var skuCodes = [];
                $checkedSkuList.each(function () {
                    skuCodes.push($(this).attr('data-skucode'));
                });
                // 遍历当前成本价并去重展示
                var cuurSetPrices = [];
                var models = [];
                $('.PriceSettingNumWrap input').each(function (i,obj) {
                    var skucode = $(obj).attr("data-scode");
                    skuCodes.forEach(function (s) {
                        if (s == skucode) {
                            cuurSetPrices.push($(obj).attr('data-curprice'));
                            models.push({
                                Id: $(obj).attr('data-id'),
                                ProductCode: $(obj).attr('data-pcode'),
                                ProductSkuCode: skucode,
                                PlatformType: $(obj).attr('data-plattype'),
                                //ShopId: 1657,

                                CreateTime: undefined,
                                UpdateTime: undefined,
                                SettlementType:3,
                                CreateUser: undefined,
                                FxUserId:0,
                                PriceRecordId: undefined,
                                UniqueKey: undefined,
                                DbUniqueKey:undefined
                            });
                        }
                    });
                });
                cuurSetPrices = [...new Set(cuurSetPrices)];
                var showHtml = "<div class='batch-priceWrap'>";
                if (cuurSetPrices.length > 0) {
                    showHtml += "<div class='tColor batch-priceWrap-item'><span class='batch-priceWrap-item-title'>原价格：</span><span class='pltipmsg' title=" + cuurSetPrices.join("，") + ">" + cuurSetPrices.join("，") + "</span></div>";
                } else {
                    showHtml += "<div class='tColor batch-priceWrap-item'><span class='batch-priceWrap-item-title'>原价格：</span><span class='pltipmsg'>未设置</span></div>";
                }
                showHtml += "<div class='batch-priceWrap-item'><span class='batch-priceWrap-item-title'>新成本价：</span><input type='text' value='' style='width:200px; height:32px;' class='batch_price_input' />";
                showHtml += '<span class="tColor" style="display:none;padding-left:5px" id="setCostWarn">成本价格式错误</span>';
                showHtml += "</div>";
                showHtml += "</div>";
                var batchdialog = layer.open({
                    title: "统—设置成本价",
                    content: showHtml,
                    area: '450px', // 宽高
                    btn: ["确定", "取消"],
                    closeBtn: 0,
                    btn1: function (index, layero) {
                        var batch_price = $.trim($(".batch_price_input").val());
                        if (!moneyreg.test(batch_price)) {
                            // layer.msg("结算价格式错误", { icon: 2 });
                            $("#setCostWarn").css({ display: 'inline-block' });
                            var timer = setTimeout(function () {
                                $("#setCostWarn").hide();
                                clearTimeout(timer);
                            }, 5000)
                            return false;
                        }
                        models.forEach(function (m) {
                            m.Price = batch_price;
                        });
                        commonModule.Ajax({
                            url: '/FinancialSettlement/SetProductCostPrice',
                            loading: true,
                            data: { pricelist:models},
                            type: 'POST',
                            success: function (rsp) {
                                if (rsp.Success == false) {
                                    layer.msg(rsp.Message, { icon: 2 });
                                    return;
                                }
                                layer.msg("统一设置成本价成功");
                                // 重新渲染前端
                                $('.PriceSettingNumWrap input').each(function (i, obj) {
                                    var skucode = $(obj).attr("data-scode");
                                    if (skuCodes.indexOf(skucode) !== -1) {
                                        $(obj).removeAttr("readonly")

                                        $(obj).attr("value",parseFloat(batch_price).toFixed(2));
                                        $(obj).attr("data-curprice", parseFloat(batch_price).toFixed(2));

                                        $(obj).attr("readonly", "readonly");
                                    }
                                });
                                layer.close(batchdialog);
                                //module.LoadList();
                            }
                        });
                    },
                    btn2: function () {
                        layer.close(batchdialog);
                    }
                });
            */
        }
        //sku全选
        module.checkAllSku = function (_this) {
            var pid = $(_this).data("pid");
            $(".skuProductShow input[data-pid='" + pid + "']").prop("checked", _this.checked);
        }

        // 批量设置成本价
        module.batchCostPriceSetting = function () {
            $(".layui-footer").show();
            $("#PriceSettingClound_tbody_data .upOrDown").addClass("zk");
            $("#PriceSettingClound_tbody_data .productSkuTr").removeClass("hideSkuTr");
            $("#PriceSettingClound_tbody_data .productSkuTr input.Edit").removeClass("unEdit").addClass("editing");
            $("#PriceSettingClound_tbody_data .productSkuTr input.Edit").removeAttr("readonly");

        }

        // 获取成本价集合
        function GetPriceSetList() {
            var pricelist = [];
            var skuCodes = [];
            var items = $(".PriceSettingNumWrap .editing");
            for (var i = 0; i < items.length; i++) {
                var item = items[i];
                var _price = {};
                var curprice = $(item).attr("data-curprice");
                _price.Price = $.trim($(item).val());
                //if (!!_price.Price && curprice != _price.Price) {
                    var scode = $(item).attr("data-scode")
                    skuCodes.push(scode);
                    _price.Id = $(item).attr("data-id");
                    _price.ProductCode = $(item).attr("data-pcode");
                    _price.ProductSkuCode = scode;
                    _price.SettlementType = 3;
                    _price.PlatformType = $(item).attr("data-plattype");
                    _price.FxUserId = 0;
                    _price.ShopId = $(item).attr("data-shopid");
                    if (_price.Price != '') {
                        pricelist.push(_price);
                    }
                //}
            }
            return pricelist;
        }

        function checkPriceList(pricelist) {
            if (!pricelist.length) {
                commonModule.w_alert({ type: 3, content: '请选择一条规格信息并输入成本价' });
                return false;
            }
            for (var i in pricelist) {
                var item = pricelist[i];
                if (!moneyreg.test(item.Price)) {
                    commonModule.w_alert({ type: 3, content: '成本价格式错误' });
                    // $(".PriceSettingNumWrap input[data-scode='" + item.ProductSkuCode + "']").focus();
                    return false;
                }
            }
            return true;
        }
        // 保存编辑
        module.saveEdit = function () {
            var pricelist = GetPriceSetList();
            if (checkPriceList(pricelist)) {
                commonModule.Ajax({
                    url: '/FinancialSettlement/SetProductCostPrice',
                    data: { pricelist: pricelist },
                    async: true,
                    loading: true,
                    type: 'POST',
                    success: function (rsp) {
                        if (rsp.Success) {
                            /*
                            var priceDic = {};
                            pricelist.forEach(function (p) {
                                priceDic[p.ProductSkuCode] = parseFloat(p.Price).toFixed(2);
                            });

                            $(".layui-footer").hide();
                            $("#PriceSettingClound_tbody_data .productSkuTr input.Edit").removeClass("editing").addClass("unEdit");
                            $("#PriceSettingClound_tbody_data .productSkuTr input.Edit").attr("readonly", "readonly");

                            $(".PriceSettingNumWrap input").each(function (i, obj) {

                                $(obj).removeAttr("readonly")
                                var inputScode = $(obj).attr("data-scode");
                                var price = priceDic[inputScode];
                                $(obj).attr("value",price);
                                $(obj).attr("data-curprice", price);
                                $(obj).attr("readonly", "readonly");

                            });
                            */
                            commonModule.w_alert({ type: 4, content: '设置成功' });
                            module.LoadList(false);
                        } else {
                            commonModule.w_alert({ type: 3, content: rsp.Message });
                        }
                    },
                    error: function () {
                        commonModule.w_alert({ type: 2, content: '网络错误，请稍后重试' });
                    }
                });
            }
        }

        //取消编辑
        module.cancelEdit = function () {
            var items = $(".PriceSettingNumWrap .editing");
            for (var i = 0; i < items.length; i++) {
                var item = items[i];
                $(item).val($(item).attr("data-curprice"));
                $(item).attr("readonly", "readonly");
                $(item).removeClass("editing").addClass("unEdit");
            }
            // $(".layui-footer").hide();
        }

        // 成本价日志
        module.costPriceLogs = function (priceId) {
            console.log(priceId)
            commonModule.Ajax({
                url: '/FinancialSettlement/GetSettlementPriceRecord',
                data: { priceId: priceId},
                async: true,
                loading: true,
                type: 'POST',
                success: function (rsp) {
                    console.log(rsp);
                    if (!rsp.Success) {
                        layer.msg(rsp.Message);
                    } else {
                        var tplt = $.templates("#operateLogo_data");
                        var html = tplt.render({
                            data: rsp.Data
                        });
                        layer.open({
                            type: 1,
                            title: "历史成本价设置日志", //不显示标题
                            content: html,
                            area: '500px', //宽高
                            skin: 'wu-dailog',
                            btn: false,
                        });
                    }


                }
            })
        }

        module.showOrHideSku = function (_this) {
            if (_this.checked) {
                $("#PriceSettingClound_tbody_data .productSkuTr").removeClass("hideSkuTr");
                $("#batchSetWareHouseNum").removeClass("zk");
                $("#PriceSettingClound_tbody_data .upOrDown").text("收起规格").addClass("zk");
                commonModule.SaveCommonSetting(zkSettingKey, "1");


            } else {
                $("#PriceSettingClound_tbody_data .productSkuTr").addClass("hideSkuTr");
                $("#PriceSettingClound_tbody_data .upOrDown").text("展开规格").removeClass("zk");
                commonModule.SaveCommonSetting(zkSettingKey, "0");

            }
        }

        //查询
        module.Search = function () {
            reqModel.PageIndex = 1;
            module.LoadList(false);
        }

        //重置
        module.Reset = function () {
            $("#inputProductNameId").val("");
            $("#shortTitleNameId").val("");
            $("#nameGuiId").val("");
            $("#skuId").val("");
            $("#cargoNumber").val("");
            $("#skuName").val("");
            $("#productStatusSelectId").val("1");
            $("#productPriceelectId").val("0");
            layui.form.render("select");
            wuFormModule.resetConditionsStyle('.wu-searchWrap');
        }

        module.targetToSetLoction = function () {
            commmon.setStorage('intoIsCostPrice', true);
        }

        return module;


    }(CostPriceSettingProductModule || {}, commonModule, jQuery, layer));

</script>


