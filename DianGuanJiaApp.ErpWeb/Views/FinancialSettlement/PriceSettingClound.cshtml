@{
    ViewBag.Title = "对账设置，设置平台价格";
    Layout = "~/Views/Shared/_CloudPlatformLayout.cshtml";
}
@section Header{
    <style>
    .layui-mywrap {
        padding: 15px;
    }
    .icon-tianjia {
        margin-right: 3px;
    }

    .layui-mysearch {
        /* padding: 15px; */
        padding: 16px 16px 8px 16px;
        background-color: #f8f8f8;
    }

    .layui-mysearch .layui-form {
        width: unset;
        display: flex;
        flex-wrap: wrap;
    }

    .layui-mysearch .layui-form .mysearch-partOne input[type=text] {
        width: 160px;
    }

    .mysearch-partOne {
        margin-right: 0;
    }

    .mysearch-partOne .layui-input-inline {
        margin-right: 10px;
    }

    .mysearch-partOne {
        margin-bottom: 0;
    }

    .mysearch-partOne {
        margin-right: 10px;
    }

    .mysearch-partOne .layui-input-inline {
        width: 160px;
    }

    .mysearch-partTwo {
        display: flex; /* flex-direction: column; */
        padding-left: 15px;
        border-left: 1px solid #e2e2e2;
        height: 35px;
        width: 175px;
        flex-direction: row;
        /* justify-content: space-between; */
    }

    .mysearch-partTwo .layui-btn + .layui-btn {
        margin-left: 0;
    }

    .mysearch-styleTwo {
        display: flex;
        align-items: center;
    }

    .mysearch-styleTwo > span {
        width: 75px;
    }

    .showOrHideSku {
        display: flex;
        align-items: center;
        margin: 0 15px;
        /* color: #3aadff; */
    }

    .stockup_table_content thead tr {
        background-color: #f6f9fd;
    }

    .stockup_table_content {
        border-left: none;
    }

    .stockup_table_content thead tr th,
    .stockup_table_content tbody tr td {
        border-right: none;
        border-top: 1px solid #e5e9f2;
        border-bottom: 1px solid #e5e9f2;
    }

    .layui-mywrap .productShow {
        display: flex;
        align-items: center;
    }

    .productShow > img {
        width: 60px;
        height: 60px;
        margin: 0 5px;
        border-radius: 6px;
    }

    .productShow > ul {
        height: 60px;
        justify-content: space-around;
        display: flex;
        flex-direction: column;
    }

    .productSkuTr {
        background-color: #f8f8f8;
    }

    .skuProductShow {
        display: flex;
        /* align-items: center; */
        align-items: flex-start;
        padding-left: 65px;
    }

    .skuProductShow > img {
        margin: 0 5px;
        width: 50px;
        height: 50px;
        border-radius: 6px;
    }

    .skuProductShow > ul {
        min-height: 50px;
        display: flex;
        flex-direction: column;
    }
    .wu-checkboxWrap input[type="checkbox"] {
        flex-shrink: 0;
    }
    .productSkuTr .hideSkuSpan {
        cursor: pointer;
        color: #0888ff;
        margin-left: 135px;
    }

    .productSkuTr .hideSkuSpan > i {
        display: inline-block;
        transform: rotate(180deg);
        font-size: 12px;
        margin-left: 5px;
    }

    .hideSkuTr {
        display: none;
    }

    .stockup_table_content tbody tr.productSkuTr td {
        border-top: none !important;
        border-bottom: none !important;
    }

    .stockup_table_content tbody tr.productSkuTr td.hideTd {
        border-top: 1px solid #e5e9f2 !important;
        border-bottom: 1px solid #e5e9f2 !important;
    }

    .stockup_table_content tbody tr td.tableOparete {
        text-align: right;
    }

    .stockup_table_content tbody tr td.tableOparete > a {
        border-right: 1px solid #ddd;
        padding-right: 5px;
        display: inline-block;
    }

    .stockup_table_content tbody tr td.tableOparete > a:last-child {
        border-right: none;
        padding-right: 0;
    }

    .PriceSettingNumWrap .Edit {
        height: 30px;
        line-height: 30px;
        width: 60px;
    }

    .PriceSettingNumWrap .unEdit {
        border: none;
        background-color: #f8f8f8;
        color: #f29a1a
    }

    .productSkuTr.skuItem {
        border-bottom: 1px solid #f0f0f0;
    }

    .goBack {
        /* color: #999; */
       
    }

    .goBack:hover {
        color: #3aadff
    }

    .Edit[value*="未设置"] {
        color: #666
    }

    .skudel, .skudel:hover {
        color: #ff511c;
    }

    .noProductData {
        text-align: center;
        margin-top: 40px;
    }

    .noProductData .icon-wushuju {
        font-size: 50px;
        margin-right: 5px;
    }

    .settlementPriceLogDailog {
        min-height: 200px;
        padding: 15px;
        box-sizing: border-box;
        background-color: #fff;
        display: none;
    }

    .settlementPriceLogDailog-nodata {
        width: 100%;
        height: 205px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .settlementPriceLogDailog-nodata .icon-wushuju {
        font-size: 60px;
        margin-right: 10px;
    }

    .settlementPriceLogDailog-hasdata-item {
        padding: 10px 10px 0 10px;
        background-color: #fff6e8;
        margin-bottom: 15px;
    }

    .settlementPriceLogDailog-hasdata-item > li {
        padding-bottom: 10px;
    }

    .settlementPriceLogDailog-hasdata-item > li .settlementPriceLogDailog-title {
        color: rgba(0, 0, 0, 0.6);
    }

    .settlementPriceLogDailog-hasdata-item > li .settlementPriceLogDailog-content {
        color: rgba(0, 0, 0, 0.9);
    }

    .settlementPriceLogDailog-hasdata-item > li .attribute-wrap {
        display: inline-block;
        padding: 0 6px;
        box-sizing: border-box;
        border: 1px solid rgba(230, 158, 62, 0.3);
        background: rgba(230, 158, 62, 0.2);
    }

    .icon-xiayi {
        display: inline-block;
        transform: rotate(90deg);
        position: relative;
        top: 1px;
    }
    .selectMore-ul > li > label input[type="checkbox"] {
        display: inline-block;
    }

    .selectMore-ul > li > label {
        display: flex;
        align-items: center;
    }

    #SearchContainer .selectWrap {
        width: 160px;
    }

    #SearchContainer .selectWrap .selectMore {
        height: 32px;
        line-height: 32px;
        background-color: #fff;
    }

    #SearchContainer .selectWrap .showMoreicon {
        /* top: 18px !important; */
        top: 12px !important;
        right: 8px;
    }

    #SearchContainer .selectWrap .selectWrap-box {
        top: 40px;
    }

    #SearchContainer .selectMore-choose-title {
        height: 32px;
    }

    #SearchContainer .selectWrap .showMoreicon {
        /* border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-top: 6px solid #c2c2c2;
        top: 18px !important;
        right: 8px; */
    }

    .batch-priceWrap {
        display: flex;
        flex-direction: column;
    }

    .batch-priceWrap .batch-priceWrap-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 10px;
    }

    .batch-priceWrap .batch-priceWrap-item:last-child {
        margin-bottom: 0;
    }

    .batch-priceWrap .batch-priceWrap-item .batch-priceWrap-item-title {
        width: 80px;
        text-align: right;
    }

    .batch-priceWrap .batch-priceWrap-item .pltipmsg {
        flex: 1;
        max-height: 50px;
        overflow-y: auto;
    }

    .batchSetPriceBtn {
        background-color: #77bf04;
        padding: 0 15px;
    }

    .batchSetPriceBtn:hover {
        opacity: 1 !important;
    }

    .batchSetPriceBtn:hover .popoverCommon-warn {
        opacity: 1;
    }

    .batchSetPriceBtn .popoverCommon-warn {
        word-break: break-all;
        background: #303133;
        color: #fff;
        opacity: 1;
        bottom: 34px;
    }

    .batchSetPriceBtn .popoverCommon-warn span {
        font-size: 12px;
        text-align: left;
        display: flex;
    }

    .batchSetPriceBtn .popoverCommon-warn:after {
        border-top-color: #303133;
    }

    .warn-wrap {
        display: flex;
        justify-content: space-between;
    }

    .batchDelSkuBtn {
        font-size: 14px;
        color: #666;
        cursor: pointer;
        position: relative;
        top: 12px;
        display: flex;
        align-items: center;
    }

    .batchDelSkuBtn .iconfont {
        margin-right: 3px;
        font-size: 14px;
    }

    .rightOpeate {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .rightOpeate .newoperate-btnWrap {
        font-size: 12px;
        margin-right: 15px;
    }

    .batchDelSkuBtn {
        top: 0;
    }

    .rightOpeate .newMoreOperate .new-export-btnShow {
        /* width: 200px; */
        width: 225px;
    }

    .rightOpeate .newMoreOperate .new-export-btnShow > li {
        display: flex;
        justify-content: space-between;
    }

    .rightOpeate .newMoreOperate .new-export-btnShow > li:hover {
        background-color: unset !important;
        color: unset !important;
    }

    .new-export-btnShow-left:hover {
        color: #3aadff;
    }

    .new-export-btnShow-right {
        padding: 2px;
        color: #3aadff;
        background-color: #f3faff;
    }



    .checkNewBulkImportDailog {
        padding: 10px 10px 0 10px;
        background-color: #fff;
        box-sizing: border-box;
        background-color: #fff;
        display: none;
    }

    .checkNewBulkImportDailog-title {
        font-size: 14px;
    }

    .checkNewBulkImportTableWrap {
        display: flex;
        flex-direction: column;
    }

    .checkNewBulkImportTableWrap-table {
        width: 100%;
        max-height: 400px;
        overflow-y: auto;
        overflow-x:hidden;
    }

    .wu-row {
        padding: 8px 5px;
    }

    .wu-row.header {
        padding: 8px 5px;
        background-color: #f5f5f5;
        /* border-top: 1px solid #e2e2e2; */
        border-bottom: 1px solid #e2e2e2;
    }
    .wu-tbody .wu-row:only-child {
        border-bottom: 1px solid rgba(0, 0, 0, 0.09);
    }
    .wu-tbody .wu-row:last-child:not(:only-child) {
        border-bottom: none;
    }
    .wu-dailog .layui-layer-btn a:not(:first-child):hover {
        border-color: rgba(8, 136, 255, 0.8);
        color: rgba(8, 136, 255, 0.8);
    }
    .checkNewBulkImport-select {
        height: 30px;
        width: 85%;
    }

    .wu-tbody .wu-row {
        border-bottom: 1px solid rgba(0, 0, 0, 0.09);
    }

    .wu-tbody .wu-row input {
        height: 30px;
        width:120px;
    }

    .wu-tbody .wu-row > div {
        display: flex;
        align-items: center;
    }

    .skuProductShow > img {
        margin: 0 5px;
        width: 50px;
        height: 50px;
    }

    .skuProductShow > ul {
        min-height: 50px;
        display: flex;
        flex-direction: column;
    }

    .checkNewBulkImportDailog .checkNewBulkImport-select.active {
        border: 1px solid #ff8a00;
        box-shadow: #fbb65e 0px 0px 6px 0px;
    }

    .importantXlsDailogSkin .layui-layer-btn a:first-child {
        border: 1px solid #dedede;
        background-color: #fff;
        color: #333;
    }

    .importantXlsDailogSkin .layui-layer-btn a:last-child {
        border-color: #1E9FFF;
        background-color: #1E9FFF;
        color: #fff;
    }

    .col-comtent {
        display: flex;
        flex-direction: column;
    }

    .col-comtent-item {
        display: flex;
        flex-direction: row;
        width: 100%;
        box-sizing: border-box;
    }

    .col-comtent-item-left {
        width: 144.13px;
        word-break: break-word;
    }

    .col-comtent-item-right {
        flex: 1;
    }

    .wu-col-01 {
        width: 144.13px;
    }

    .wu-col-02 {
        width: 144.13px;
    }

    .wu-col-03 {
        width: 240.2px;
    }

    .wu-col-04 {
        width: 96.08px;
    }

    .wu-col-05 {
        width: 192.16px;
    }

    .col-second-item {
        margin-bottom: 10px;
        padding: 0 5px;
    }

    .col-second-item.more-second-item {
        border: 1px dotted #e2e2e2;
        padding: 5px 0;
    }

    .col-comtent .col-second-item:last-child {
        margin-bottom: 0;
    }

    .col-comtent .col-comtent-item {
        margin-bottom: 5px;
        padding-bottom: 5px;
    }

    .moreSupper-left {
        flex-direction: column;
        display: flex;
    }

    .col-comtent .exceptionMessageItem {
        margin-bottom: 5px;
    }

    .col-comtent .exceptionMessageItem:last-child {
        margin-bottom: 0;
    }

    .renderCheckImportDailog {
        width: 700px;
        padding:0 16px;
        box-sizing: border-box;
        background-color: #fff;
        box-sizing: border-box;
    }

    .renderCheckImportDailog .renderCheckImport-title {
        padding: 16px;
        text-align: center;
        font-size: 16px;
    }

    .renderCheckImport-ul {
        display: flex;
        flex-direction: column;
    }

    .renderCheckImport-ul .renderCheckImport-li {
        display: flex;
        padding: 16px 0;
    }

    .renderCheckImport-ul .renderCheckImport-li .renderCheckImport-li-left {
        width: 120px;
        font-size: 14px;
    }

    .renderCheckImport-ul .renderCheckImport-li .renderCheckImport-li-right {
        flex: 1;
        display: flex;
    }

    .renderCheckImport-ul .renderCheckImport-li .renderCheckImport-li-right > li {
        flex: 1;
    }

    .renderCheckImport-ul .renderCheckImport-li .renderCheckImport-li-right > li select {
        border: 1px solid #e2e2e2;
        height: 30px;
        width: 140px;
    }

    .renderCheckImport-ul .renderCheckImport-li .renderCheckImport-li-right > li select.active {
        border: 1px solid #fe6f4f;
    }

    .renderCheckImport-ul .renderCheckImport-li .renderCheckImport-li-right.rightF > li {
        font-size: 14px;
        font-weight: 700;
    }

    #renderCheckImportDailog {
        display: none;
    }
    .wu-tbody .wu-row > div {
        word-break: break-all;
    }
    .renderCheckImportTableSkin .layui-layer-btn .layui-layer-btn0 {
        border: 1px solid #dedede;
        background-color: #fff;
        color: #333;
    }
    .renderCheckImportTableSkin .layui-layer-btn .layui-layer-btn1 {
        border-color: #1E9FFF;
        background-color: #1E9FFF;
        color: #fff;
    }

    #upImportantXlsfileWrap {
        display: none;
    }
    .newTips {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        border-radius: 2px;
        background: rgba(234, 87, 46, 0.1);
        padding: 2px;
        box-sizing: border-box;
        color: #EA572E;
        font-size: 12px;
        margin-left: 10px;
    }


        .flex-center {
            display: flex;
            align-items: center;
        }

        .mT16 {
            margin-top: 16px;
        }

        .mT8 {
            margin-top: 8px;
        }

        .mL4 {
            margin-left: 4px;
        }

        .mL8 {
            margin-left: 8px;
        }

        .mL24 {
            margin-left: 24px;
        }

        .addWarnInput {
            border: 1px solid #fe6f4f !important;
        }

        .disabledInput {
            background: rgba(0, 0, 0, 0.04);
            color: rgba(0, 0, 0, 0.3);
            pointer-events: none;
        }
        .warnInput {
            border: 1px solid #EA572E !important;
        }

        .settlementPriceFixSelectWrap .n-mySelect-showContent {
            position: fixed !important;
            animation: settlementPriceSelectAnimation 0.3s ease-in-out 0s 1 alternate forwards;
        }
        @@keyframes settlementPriceSelectAnimation {
            0% {
                opacity: 0;
                top: unset;
            }

            100% {
                opacity: 1;
                top: unset;
            }
        }
    #SearchContainer .selectMore {
        display: inline-block;
    }    
    #SearchContainer .mysearch-partOne {
        height: 32px;
        margin-bottom: 8px;
    }
    #SearchContainer .wu-searchWrap {
        padding: 0px;
    }
    #SearchContainer .wu-searchWrap .wu-layui-select {
        width: unset;
    }
    .price-set-main .newMoreOperate {
            height: 32px;
            border-radius: 6px;
            font-size: 14px;
       }
    .price-set-main .newMoreOperate .new-export-btnShow > li, ul.export-btnShow {
            border-bottom: unset;
            padding: 6px 8px;
            box-sizing: border-box;
            color: rgba(0, 0, 0, 0.9);
            border-radius: 6px;
            line-height: initial;
        }  
    .price-set-main .newMoreOperate .new-export-btnShow > li:hover, ul.export-btnShow > li:hover, #sortBtn_ul .active {
            background-color: #f2f2f2 !important;
            color: rgba(0, 0, 0, 0.9) !important;
        } 
    .price-set-main .newMoreOperate .new-export-btnShow, ul.export-btnShow {
            padding: 8px;
            border-radius: 6px;
            border: unset;
            box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 8px 16px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.08);
        }
    .price-set-main .wu-tableWrap {
        width: unset;
    }
    .price-set-main .tableOparete {
        text-align: left !important;
        /* white-space: nowrap; */
    }
    .price-set-main .tableOparete a {
        border-right: none !important;
        padding-right: 0px !important;
        margin-right: 12px;
    }
    .price-set-main .wu-tableWrap .wu-table-one .tableNoDataShow .tableNoDataShow-title:before {
            margin: 0 auto;
    }
    .wu-inputWrap .wu-input:focus {
        padding-right: 8px;
    }
    .wu-tableWrap .wu-checkboxWrap {
        /* display: table-cell; */
    }
    .layui-input:hover {
        border-color: #0888ff !important;
    }
    .selectWrap.wu-select-skin .selectMore-choose > li {
        max-width: 60% !important;
    }
    .wu-checkboxWrap input[type="checkbox"]::after {
        z-index: 8 !important;
    }
    .wu-dailog.custom-dailog .layui-layer-btn a:nth-child(1) {
        color: #333;
        background: #fff;
        border: 1px solid #dedede;
    }
    .wu-dailog.custom-dailog .layui-layer-btn a.layui-layer-btn0:hover {
        border-color: rgba(8, 136, 255, 0.8) !important;
        color: rgba(8, 136, 255, 0.8) !important;
        background: #fff !important;
    }
    .wu-dailog.custom-dailog .layui-layer-btn a:last-child {
        color: #fff; 
        background: #0888ff; 
        border: 1px solid #0888ff;
    }
    .wu-dailog.custom-dailog .layui-layer-btn a:last-child:hover {
        background: rgba(8, 136, 255, 0.8) !important;
        border: 1px solid rgba(8, 136, 255, 0.8) !important;
        color: #fff !important;
    }
    #PriceSettingClound_tbody_data>tr {
        border-top: 1px solid #e2e2e2;
    }
    </style>
}

<div class="renderCheckImportDailog" id="renderCheckImportDailog">
    <div class="renderCheckImport-title" id="renderCheckImportDailog-title">系统无法识别您上传的文件，请手动下拉本地文件字段对应系统字段</div>
    <ul class="renderCheckImport-ul">
        <li class="renderCheckImport-li">
            <div class="renderCheckImport-li-left">本地文件字段</div>
            <ul class="renderCheckImport-li-right" id="renderCheckImport_ul">
            </ul>
        </li>
        <li class="renderCheckImport-li">
            <div class="renderCheckImport-li-left">对应系统字段</div>
            <ul class="renderCheckImport-li-right rightF">
                <li>商品ID</li>
                <li id="renderCheckImportText">SKUID</li>
                <li>结算价</li>
            </ul>
        </li>
    </ul>
</div>

<script id="renderCheckImportDailog_data" type="text/x-jsrender">
    {{for selects ~columnNames=columnNames}}
    <li>
        <select class="active" name="CustomProductIdColumnName" onchange="PriceSettingCloundModule.selectCheckImport.bind(this)('{{:name}}')">
            <option value="">请选择</option>
            {{if select}}
            <option selected value="{{:select}}">{{:select}}</option>
            {{/if}}
            {{for ~columnNames}}
            {{if !isSelect}}
            <option value="{{:name}}">{{:name}}</option>
            {{/if}}
            {{/for}}
        </select>
    </li>
    {{/for}}
</script>

<div class="checkNewBulkImportDailog" id="checkNewBulkImportDailog">
    <div class="checkNewBulkImportTableWrap">
        <div class="checkNewBulkImportTableWrap-table wu-tableWrap" id="checkNewBulkImportTableWrap_table">
            <div class="wu-row header" id="important_header">


            </div>
            <div class="wu-tbody" id="important_tbody">

            </div>
        </div>
        <div class="layui-myPage" id="importantPaging"></div>
    </div>
</div>

<div class="layui-mywrap wu-m0" style="min-height:unset; padding-bottom: 16px; background: unset; box-shadow: none">
    <div style="font-size: 20px; font-weight: 600;">
        @if (Request.QueryString["type"] == "1")
        {
            <span class="wu-flex"><a class="goBack wu-c09 wu-flex wu-yCenter" target="_top" href="@(DianGuanJiaApp.Utility.CustomerConfig.AlibabaFenFaSystemUrl)/FinancialSettlement/PriceSetting?token=@(Request.QueryString["token"])&type=1"><i class="iconfont icon-xiayi wu-mR8"></i><span>返回</span><span class="mLR10">/</span></a><span  class="goBack_span wu-c09">@(HttpUtility.UrlDecode(Request.QueryString["name"]))</span></span>
        }
        else
        {
            <span class="wu-flex"><a class="goBack wu-c09 wu-flex wu-yCenter" target="_top" href="@(DianGuanJiaApp.Utility.CustomerConfig.AlibabaFenFaSystemUrl)/FinancialSettlement/PriceSetting?token=@(Request.QueryString["token"])&type=2"><i class="iconfont icon-xiayi wu-mR8"></i><span>返回</span><span class="mLR10">/</span></a><span  class="goBack_span wu-c09">@(HttpUtility.UrlDecode(Request.QueryString["name"]))</span></span>
        }
    </div>
</div>

<div class="layui-mywrap wu-mT0 wu-p0" style="margin-bottom: 70px; background-color: unset;">
    @*<div>
            @if (Request.QueryString["type"] == "1")
            {
                <span style="color:#999">厂家<span class="mLR10" style="color:#999">/</span>结算价设置<span class="mLR10">/</span><span style="color:#666">@(HttpUtility.UrlDecode(Request.QueryString["name"]))</span></span>
            }
            else
            {
                <span>商家：@(HttpUtility.UrlDecode(Request.QueryString["name"])),结算价设置</span>
            }
        </div>*@
    <div class="layui-mysearch wu-color-n wu-background wu-8radius" id="SearchContainer" style="margin-bottom:15px;">
        <form class="layui-form wu-searchWrap">
            <div class="layui-inline mysearch-partOne wu-mR8" style="display:none">
                <div class="layui-input-inline" style="width: 160px; margin-right: 0;">
                    <label for="" class="hide search_one_labelOne self-build-select-wrap" style="height: 32px; display: block;">
                        <!--厂家-->
                        <div id="sel_supplier" class="Supplier selectWrap wu-form-mid wu-select-skin" name="sel_supplier" style="height: 32px;"></div>
                    </label>
                </div>
            </div>
            <div class="layui-inline mysearch-partOne " style="display:none">
                <div class="layui-input-inline" style="width: 160px; margin-right: 0;">
                    <label for="" class="hide search_one_labelOne self-build-select-wrap">
                        <!--商家-->
                        <div id="sel_agent" class="Agent selectWrap wu-form-mid wu-select-skin" name="sel_agent"></div>
                    </label>
                </div>
            </div>
            <div class="layui-inline mysearch-partOne wu-layui-select wu-form-mid" style="display:none">
                <select id="productStatusSelectId " lay-filter="active-select-filter">
                    <option value="0" selected="selected">全部商品</option>
                    <option value="1">上架商品</option>
                    <option value="2">下架商品</option>
                </select><div class="layui-unselect layui-form-select"><div class="layui-select-title"><input type="text" placeholder="请选择" value="全部商品" readonly="" class="layui-input layui-unselect"><i class="layui-edge"></i></div><dl class="layui-anim layui-anim-upbit"><dd lay-value="0" class="">全部商品</dd><dd lay-value="1" class="layui-this">上架商品</dd><dd lay-value="2" class="">下架商品</dd></dl></div>
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-inputWrap wu-form-mid wu-mR8">
                <input id="inputProductNameId" type="text" class="layui-input wu-input" placeholder="请输入商品名称">
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-inputWrap wu-form-mid wu-mR8">
                <input name="shortTitleName" class="layui-input wu-input" id="shortTitleNameId" placeholder="请输入商品简称">
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-inputWrap wu-form-mid wu-mR8">
                <input name="nameGui" id="nameGuiId" type="text" class="layui-input wu-input" placeholder="请输入规格简称">
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-inputWrap wu-form-mid wu-mR8">
                <input name="skuName" id="skuName" type="text" class="layui-input wu-input" placeholder="请输入规格属性">
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-inputWrap wu-form-mid wu-mR8">
                <input name="skuId" id="skuId" type="text" class="layui-input MultipleQuery wu-input" placeholder="多个SkuId(,)分隔" title="多个SkuId(,)分隔">
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-inputWrap wu-form-mid wu-mR8">
                <input name="cargoNumber" id="cargoNumber" type="text" class="layui-input wu-input" placeholder="请输入规格Sku编码">
            </div>
            <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-layui-select wu-form-mid wu-mR8 IsReconVipFxUser">
                <select id="setProductStatus" lay-filter="active-select-filter">
                    <option value="">商品状态</option>
                    <option value="1">已上架</option>
                    <option value="2">已下架</option>
                </select>
            </div>
             <div class="layui-inline mysearch-partOne mysearch-styleTwo wu-layui-select wu-form-mid wu-mR8">
                <select id="productPriceelectId" lay-filter="active-select-filter">
                    <option value="0" selected="selected">--- 结算价设置 ---</option>
                    <option value="1">未设置</option>
                    <option value="2">已设置</option>
                </select>
            </div>
        </form>
        <div class="layui-inline mysearch-partTwo">
            <button type="button" style="margin-right:8px;" class="layui-btn layui-btn-normal layui-btn35 wu-btn wu-btn-mid wu-primary wu-two" id="btn-query" onclick="PriceSettingCloundModule.Search();">查询</button>
            <button type="button" style="margin-right:8px;" class="layui-btn layui-btn-primary layui-btn35 wu-btn wu-btn-mid wu-primary" id="btn-reset" onclick="PriceSettingCloundModule.Reset();">重置</button>
            @*<button type="button" style="background-color: #f29a1a; width:200px;" class="layui-btn layui-btn-warm layui-btn35" onclick="PriceSettingCloundModule.batchDelSku();">批量删除结算价</button>*@
        </div>
    </div>
    <div class="price-set-main wu-8radius wu-color-n wu-background wu-p16 wu-mT0"  >
    
        <div class="warn-wrap wu-mT0 wu-pL0 wu-pR0" style="margin-left:0;margin-right:0;">
        <span class="wu-color-c">
            <!-- <i class="icon">!</i> -->
            厂家和商家都可以同时设置结算价，为方便核对价格，结算价格只对自己出账的账单生效
        </span>
        <div class="rightOpeate">
            <div class="operate-btn newoperate-btnWrap" id="upImportantXlsfileWrap">
                <span class="newMoreOperate wu-c09">
                    <span class="new-exportText">
                        <!-- <i class="iconfont icon-guanli new-exportText-icon"></i> -->
                        批量导入结算价 <i class="iconfont icon-down"></i> </span>
                    <ul class="new-export-btnShow">
                        <li>
                            <span class="new-export-btnShow-left" onclick="PriceSettingCloundModule.upImportantXlsfile('SkuId')">按SKUID导入</span>
                            <a class="new-export-btnShow-right" href="~/Files/FinancialSettlement/SKUID导入模板.xls">(下载导入模板)</a>
                        </li>
                        <li>
                            <span class="new-export-btnShow-left" onclick="PriceSettingCloundModule.upImportantXlsfile('SkuCode')">按SKU编码导入</span>
                            <a class="new-export-btnShow-right" href="~/Files/FinancialSettlement/SKU编码导入模板.xls" style="background-color: #fff2e0; color: #a76e26;">(下载导入模板))</a>
                        </li>
                    </ul>
                    <input type="file" id="input-choice-file" name="upfile" size="1" style="display:none" onchange="PriceSettingCloundModule.changeFile.bind(this)()">
                </span>
            </div>
            <span class="batchDelSkuBtn wu-btn wu-btn-mid wu-primary" onclick="PriceSettingCloundModule.batchDelSku();"><i class="iconfont icon-laji"></i>批量删除结算价</span>
        </div>
        </div>

        <div class="layui-mytable wu-tableWrap min1200">
            <table class="stockup_table_content wu-table-one" id="productlist">
            <thead>
                <tr>
                    <th style="min-width:20px;display: flex; align-items: end;" class="wu-checkboxWrap wu-hover">
                        <input id="allCheck" onclick="PriceSettingCloundModule.checkAll(this)" title="全选" type="checkbox">
                    </th>
                    <th style="min-width:200px;">
                        <div style="display:flex;">
                            <label style="display: flex;align-items: center; white-space: nowrap;">商品名称/SKU规格</label>
                            <label class="showOrHideSku wu-checkboxWrap wu-hover wu-f12 wu-c09" style="white-space: nowrap; display: flex;">
                                <input class="wu-mR4" type="checkbox" checked="checked" id="showOrHideSku" onchange="PriceSettingCloundModule.showOrHideSku(this)">展开规格
                            </label>
                            @*<button type="button" onclick="PriceSettingCloundModule.batchDelSku()" class="layui-btn layui-btn-sm layui-btn-warm" style="background-color:#f29a1a;padding:0 15px;">批量删除出账规格</button>*@
                            @*<button type="button" onclick="PriceSettingCloundModule.BatchSetPriceDialog()" class="layui-btn layui-btn-sm layui-btn-warm popoverCommon batchSetPriceBtn">
                    <span>批量设置结算价</span>
                    <span class="popoverCommon-warn" style="width:500px;">
                        <span>操作流程:批量输入同平台的多个商品信息查询，可以进行快捷修改结算价。</span>
                        <span>1：输入商品信息并查询 2：点击批量设置结算价 3：输入修改后的结算价，点击保存并生效</span>
                    </span>
                </button>*@
                            <button type="button" onclick="PriceSettingCloundModule.BatchChangeSettlementPrice()" class="layui-btn layui-btn-sm wu-btn wu-three wu-btn-small layui-btn-warm popoverCommon batchSetPriceBtn">
                                批量设置结算价
                            </button>
                            <button type="button" class="layui-btn layui-btn-sm wu-btn wu-three wu-btn-small batchSetPriceBtn" onclick="PriceSettingCloundModule.BatchSyncPriceDialog()">同步结算价</button>
                        </div>
                    </th>
                    <th style="min-width:100px;">创建时间</th>
                    <th style="min-width:100px;">SkuId</th>
                    <th style="min-width:100px;">商品/Sku编码</th>
                    <th style="min-width:100px;">结算价</th>
                    <th style="min-width:100px; text-align: right;">对方结算价</th>
                    <th style="min-width: 90px;" class="IsReconVipFxUser">商品状态</th>
                    <th style="width:80px;" class="from-type">商家</th>
                    <th style="min-width:100px;text-align: left;">
                        <span id="batchSetPrice" class="layui-btn layui-btn-sm setPriceBtn" style="background-color: #99cc00;display:none" onclick="PriceSettingCloundModule.setAllPrice(this,true)">
                            批量设置结算价
                        </span>
                        操作
                    </th>
                </tr>
            </thead>
            <tbody id="PriceSettingClound_tbody_data"></tbody>
            </table>
            <div class="layui-myPage" id="paging"></div>
        </div>
    </div>
</div>
<div class="layui-footer" style="left: 0px; padding-right: 0px;">
    <div class="layui-footer-btns">
        @*<button type="button" class="layui-btn layui-btn-normal" style="background-color: #f59c1a;" onclick="PriceSettingCloundModule.cancelEdit()">取消编辑</button>*@
        <button type="button" class="layui-btn layui-btn-normal wu-btn wu-btn-mid" onclick="PriceSettingCloundModule.saveEdit()">保存当前页修改</button>
    </div>
</div>

<input type="hidden" id="toFxUserIds" value="@Request.QueryString["fxUserId"]" />
<input type="hidden" id="toFxType" value="@Request.QueryString["type"]" />

<!-- 价格设置 商品列表模样 -->
<script id="PriceSettingClound_data" type="text/x-jsrender">
    {{if Products.length>0}}
    {{for Products ~IsReconVipFxUser=IsReconVipFxUser}}
    <tr>
        <td class="wu-checkboxWrap wu-hover" style="border-top: none;">
            <input class="order-chx product-chx" type="checkbox" data-index="{{:Index}}" data-pid="{{:Id}}" onchange="PriceSettingCloundModule.checkAllSku(this)">
        </td>
        <td>
            <div class="productShow">
                {{if ImageUrl}}
                <img src="{{:ImageUrl}}">
                {{else}}
                <img src="/Content/images/nopic.gif">
                {{/if}}
                <ul>
                    {{if Subject}}
                    <li>{{:Subject}}</li>
                    {{/if}}
                    {{if ShortTitle}}
                    <li style="color:#888" title="商品简称">{{:ShortTitle}}</li>
                    {{/if}}
                    <li id="upOrDown{{:Index}}" class="  upOrDown zk wu-color-a wu-operate" onclick="PriceSettingCloundModule.showMoreSize(this,'{{:Index}}')">收起规格</li>
                </ul>
            </div>
        </td>
        <td></td>
        <td></td>
        <td>{{:CargoNumber}}</td>
        <td></td>
        <td><span class="dColor"></span></td>
        {{if ~IsReconVipFxUser == true}}
        <td>
            {{if Status && Status == 'published'}}
            <span class="wu-badge wu-processing">
                <i class="wu-badge-dot"></i>
                <s class="wu-badge-title">已上架</s>
            </span>
            {{else}}
            <span class="wu-badge">
                <i class="wu-badge-dot"></i>
                <s class="wu-badge-title">已下架</s>
            </span>
            {{/if}}
        </td>
        {{/if}}
        <td style="min-width:100px;">
            {{if AgentShopName}}
            <div class="AgentName-box" style="display: none;">
                <span style="margin-right: 5px;" class="tr-title-left-pintaiIcon pintaiIcon {{:PlatformType}}"></span>
                <span>{{:AgentShopName}}</span>
            </div>
            {{/if}}
        </td>
        <td class="tableOparete">
            <a class=" setWareHouseItem wu-color-a wu-operate" title="设置商品结算价" onclick="PriceSettingCloundModule.SetProductPrice('{{:Index}}', '{{:Id}}')">
                批量设置结算价
            </a>
            <a class="skudel  wu-color-b wu-operate" onclick="PriceSettingCloundModule.singleProduct('{{:Index}}')" href="javascript:;">删除</a>
        </td>
    </tr>
    {{for Skus ~Index=Index ~ProductId=Id ~PlatformType=PlatformType ~ShopId=ShopId}}
    <tr class="productSkuTr skuItem skuTr{{:~Index}}" id="skuItemTr_{{:SettlementSkuUniqueKey}}">
        <td></td>
        <td>
            <div class="skuProductShow wu-checkboxWrap wu-hover">
                <input class="order-chx sku-chx" name="sku-chx" type="checkbox" data-sIndex="{{:#getIndex()}}" data-index="{{:~Index}}" data-pid="{{:~ProductId}}" data-skucode="{{:SkuCode}}"
                       data-settlementskuid="{{:SettlementSkuId}}" data-settlementskukey="{{:SettlementSkuUniqueKey}}" data-isshowsaleprice="{{:IsShowSalePrice}}" onchange="PriceSettingCloundModule.checkSku(this)" />
                {{if ImgUrl}}
                <img src="{{:ImgUrl}}">
                {{else}}
                <img src="/Content/images/nopic.gif">
                {{/if}}
                <ul>
                    <li>
                        <div style="display: flex; align-items: center;">
                            <div class="attributes-litem">{{:(AttributeValue1 || "") +'  '+(AttributeValue2 || "")}}</div>
                            {{if IsSkuNameChanged == true}}
                            <div class="newTips">新</div>
                            {{/if}}
                        </div>
                        {{if IsUseBakSku == true}}
                        <span class="tagDanger tagStatus tooltip">
                            历史绑定
                            <span class="tooltip-content">注意:该商品所在的店铺已经被解绑，<br>所以使用名称、SKUID、SKU编码搜索时会查询不到该条数据<br>且批量导入结算价时，会以实际的商品绑定关系为准。</span>
                        </span>
                        {{/if}}
                    </li>
                    {{if ShortTitle}}
                    <li style="color:#888" title="规格简称">{{:ShortTitle}}</li>
                    {{/if}}
                </ul>
            </div>
        </td>
        <td><span class="attributes-litem" style="white-space: pre-wrap; word-break: normal;">{{:SettlementSkuCreateTime}}</span></td>
        <td><span class="attributes-litem">{{:SkuId}}</span></td>
        <td><span class="attributes-litem">{{:CargoNumber}}</span></td>
        <td>
            <div class="PriceSettingNumWrap">
                <input type="text"
                       value="{{:SettlementPrice}}"
                       data-fxuid="{{:ToFxUserId}}"
                       data-shopid="{{:~ShopId}}"
                       data-id="{{:SettlementId}}"
                       data-pid="{{:~ProductId}}"
                       data-index="{{:~Index}}"
                       data-plattype="{{:~PlatformType}}"
                       data-curprice="{{:SettlementPrice}}"
                       data-oldsettlementpricee="{{:OldSettlementPricee}}"
                       data-pcode="{{:ProductCode}}"
                       data-scode="{{:SkuCode}}"
                       data-name="{{:Id}}"
                       data-skuname="{{:Name}}"
                       data-newid="{{:newId}}"
                       class="layui-input n-layui-input editing"
                       placeholder="请输入"
                       style="width: 100px; font-size: 12px;"
                       onblur="PriceSettingCloundModule.blurPriceSetting(this)" />
                <div class="input-warnTitle">最大7位整数，2位小数</div>
                @*class="Edit unEdit" readonly*@
            </div>
        </td>
        @*{{if ReverseFxUserId > 0}}
                <td><span class="attributes-litem">{{:OppositeSettlementPrice}} <br/> <span class="attributes-litem">{{:ReverseSettlementPrice}}</span></span></td>
                <td><span class="attributes-litem">{{:ToFxUserName}}</span> <br/> <span class="attributes-litem">{{:ReverseFxUserName}}</span></td>
            {{else}}
                <td><span class="attributes-litem">{{:OppositeSettlementPrice}}</span></td>
                <td><span class="attributes-litem">{{:ToFxUserName}}</span></td>
            {{/if}}*@
        <td style="text-align: right;"><span class="attributes-litem">{{:OppositeSettlementPrice}}</span></td>
        {{if ~IsReconVipFxUser == true}}
        <td><span></span></td>
        {{/if}}
        <td><span class="attributes-litem">{{:ToFxUserName}}</span></td>
        <td class="tableOparete">
            @*设置结算价输入框保持开启状态，不需要设置按钮，暂时隐藏*@
            @*<a class="dColor hover" title="设置规格结算价" onclick="PriceSettingCloundModule.SetOnePrice('{{:~ProductId}}','{{:Id}}')" href="javascript:;">设置结算价</a>*@
            @* <a class="dColor hover" title="设置规格结算价" onclick="PriceSettingCloundModule.SetOnePrice('{{:SkuCode}}', {{:ToFxUserId}}, '{{:Name}}')" href="javascript:;">设置结算价</a> *@
            <a class=" wu-color-a wu-operate" onclick="PriceSettingCloundModule.ClickPriceLog('{{:SettlementUniqueKey}}')" href="javascript:;">结算价日志</a>
            <a class="skudel  wu-color-b wu-operate" onclick="PriceSettingCloundModule.singleDelSku('{{:SettlementSkuUniqueKey}}')" href="javascript:;">删除</a>
        </td>
    </tr>
    {{/for}}
    <tr class="productSkuTr skuTr{{:Index}}">
        <td class="hideTd" colspan="20"><span class="hideSkuSpan wu-color-a wu-operate " onclick="PriceSettingCloundModule.showMoreSize(this,'{{:Index}}',true)">点击收起规格<i class="iconfont icon-down wu-color-a wu-operate"></i></span></td>
    </tr>
    {{/for}}
    {{else}}
    <tr>
        <td colspan="20" class="tdNodata">
            <div class="tableNoDataShow"><img src="/Content/images/noData-icon.png"><span class="tableNoDataShow-title">SKU结算价只能在已产生的发货订单后设置</span></div>
        </td>
    </tr>
    {{/if}}
</script>

<!-- 结算价日志 弹窗-->
<div class="settlementPriceLogDailog">
    <div class="settlementPriceLogDailog-nodata" style="display:none"> <i class="iconfont icon-wushuju"></i><span style="color:#f59c1a;font-size:14px;">暂无数据，还未设置结算价！</span></div>
    <div class="settlementPriceLogDailog-hasdata"></div>
</div>
<!-- 加载动画-->
<div class="myloadingDailog">
    <div class="myloadingDailog-center">
        <div class="myloadingDailog-center-main">
            <span class="myloadingDailog-center-icon"></span>
            <span class="myloadingDailog-center-icon"></span>
            <span class="myloadingDailog-center-icon"></span>
            <span class="myloadingDailog-center-icon"></span>
            <span class="myloadingDailog-center-icon"></span>
            <span class="myloadingDailog-center-icon"></span>
            <span class="myloadingDailog-center-icon"></span>
            <span class="myloadingDailog-center-icon"></span>
            <span class="myloadingDailog-center-icon"></span>
            <span class="myloadingDailog-center-icon"></span>
        </div>
        <span class="myloadingDailog-center-text">保存结算价</span>
    </div>
</div>

<!-- 批量设置结算价 -->
<div id="settlement_price_wrapper_layer" style="display: none;">
    <div class="n-font5">
        <div class="flex-center">
            <input type="radio" name="ChangePriceType" value="1" class="hover" checked /><span class="mL4">价格修改为</span>
            <input type="text"
                   class="layui-input n-layui-input mL4"
                   style="width: 120px;"
                   placeholder="请输入新价格"
                   value=""
                   id="NewPriceInput"
                   onblur="checkPriceInputVal.bind(this)('NewPrice')"
                   onfocus="focusPriceInputVal.bind(this)('NewPrice')" />
            <span class="mL4">元</span>
            <div class="input-warnTitle mL8" id="show_price_warn_tip" style="font-size: 12px;">错误提示</div>
        </div>
        <div class="mT16">
            <div class="flex-center">
                <input type="radio" name="ChangePriceType" value="2" class="hover" /><span class="mL4">公式改价</span>
            </div>
            <div style="display: none;" id="show_formula_price_content">
                <div class="mT8" style="padding-left: 18px; box-sizing: border-box; display: flex;">
                    <div class="flex-center" style="height: 32px;">
                        <span>按所选商品</span>
                        <div class="n-mySelect n-single n-myCommonSelect mL4 settlementPriceFixSelectWrap" style="width: 120px;" id="price_type_select">
                            <div class="n-mySelect-title">
                                <div class="n-mySelect-title-left">
                                    <span class="n-mySelect-title-left-title"></span>
                                    <span class="n-mySelect-title-placeholder">请选择</span>
                                    <span class="n-mySelect-title-chooseItem"></span>
                                </div>
                                <i class="iconfont icon-a-chevron-down1x"></i>
                            </div>
                            <div class="n-mySelect-showContent" style="width: 120px;">
                                <ul class="n-mySelect-showContent-ul">
                                    <li class="n-mySelect-showContent-ul-li" data-value="1" onclick="selectSettlementPriceOptions.bind(this)('Price')">结算价</li>
                                    <li class="n-mySelect-showContent-ul-li" data-value="2" onclick="selectSettlementPriceOptions.bind(this)('Price')">对方结算价</li>
                                    <li class="n-mySelect-showContent-ul-li IsReconVipFxUser" data-value="3" onclick="selectSettlementPriceOptions.bind(this)('Price')" style="display:none;">平台销售价</li>
                                </ul>
                            </div>
                        </div>
                        <span class="mL4">价格</span>
                    </div>
                    <div>
                        <div class="n-mySelect n-single n-myCommonSelect mL4 settlementPriceFixSelectWrap" style="width: 90px;" id="price_compute_symbol">
                            <div class="n-mySelect-title">
                                <div class="n-mySelect-title-left">
                                    <span class="n-mySelect-title-left-title"></span>
                                    <span class="n-mySelect-title-placeholder">请选择</span>
                                    <span class="n-mySelect-title-chooseItem"></span>
                                </div>
                                <i class="iconfont icon-a-chevron-down1x"></i>
                            </div>
                            <div class="n-mySelect-showContent" style="width: 90px;">
                                <ul class="n-mySelect-showContent-ul">
                                    <li class="n-mySelect-showContent-ul-li" data-value="1" onclick="selectSettlementPriceOptions.bind(this)('ComputeSymbol')">加</li>
                                    <li class="n-mySelect-showContent-ul-li" data-value="2" onclick="selectSettlementPriceOptions.bind(this)('ComputeSymbol')">减</li>
                                    <li class="n-mySelect-showContent-ul-li" data-value="3" onclick="selectSettlementPriceOptions.bind(this)('ComputeSymbol')">乘</li>
                                    <li class="n-mySelect-showContent-ul-li IsReconVipFxUser" data-value="4" onclick="selectSettlementPriceOptions.bind(this)('ComputeSymbol')" style="display:none;">除</li>
                                </ul>
                            </div>
                        </div>
                        <div class="input-warnTitle mL4" id="show_symbol_warn_tip" style="font-size: 12px;">错误提示</div>
                    </div>
                    <div>
                        <input type="text"
                               class="layui-input n-layui-input mL4"
                               style="width: 130px;"
                               placeholder="请输入"
                               value=""
                               id="FormulaPriceInput"
                               onblur="checkPriceInputVal.bind(this)('FormulaPrice')"
                               onfocus="focusPriceInputVal.bind(this)('FormulaPrice')" />
                        <div class="input-warnTitle mL4" id="show_number_warn_tip" style="font-size: 12px;">错误提示</div>
                    </div>
                    <div class="mL4 flex-center" id="ComputeUnit" style="height: 32px;">元</div>
                    <div style="display: none;" id="show_decimal_point_select">
                        <div class="flex-center" style="height: 32px;">
                            <div class="n-mySelect n-single n-myCommonSelect mL4 settlementPriceFixSelectWrap" style="width: 140px;" id="price_decimal_point">
                                <div class="n-mySelect-title">
                                    <div class="n-mySelect-title-left">
                                        <span class="n-mySelect-title-left-title"></span>
                                        <span class="n-mySelect-title-placeholder">请选择</span>
                                        <span class="n-mySelect-title-chooseItem"></span>
                                    </div>
                                    <i class="iconfont icon-a-chevron-down1x"></i>
                                </div>
                                <div class="n-mySelect-showContent" style="width: 140px;">
                                    <ul class="n-mySelect-showContent-ul">
                                        <li class="n-mySelect-showContent-ul-li" data-value="0" onclick="selectSettlementPriceOptions.bind(this)('DecimalPoint')">不保留小数点</li>
                                        <li class="n-mySelect-showContent-ul-li" data-value="1" onclick="selectSettlementPriceOptions.bind(this)('DecimalPoint')">保留1位小数点</li>
                                        <li class="n-mySelect-showContent-ul-li" data-value="2" onclick="selectSettlementPriceOptions.bind(this)('DecimalPoint')">保留2位小数点</li>
                                    </ul>
                                </div>
                            </div>
                            <span>（四舍五入）</span>
                        </div>
                        <div class="input-warnTitle mL4" id="show_point_warn_tip" style="font-size: 12px;">小数点错误提示</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

    commonModule.changePtNameShow=commonModule.changePtName('@(Html.Raw(ViewBag.CloudPlatformType ?? ""))');

    var _agentList = @(Html.Raw(ViewBag.Agents ?? "[]")) ;
    var _supplierList = @(Html.Raw(ViewBag.Suppliers ?? "[]"));

    // 结算价设置状态
    var isUnSetPrice = '@Request["isUnSetPrice"]' || "0";
    $("#productPriceelectId").val(isUnSetPrice);

    var toFxUserIds='@Request.QueryString["fxUserId"]';
    var toFxType = '@Request.QueryString["type"]';
    var IsWhiteList = "@(Html.Raw(ViewBag.IsWhiteList ?? false))"=="True";
    if (IsWhiteList) {
        $("#upImportantXlsfileWrap").show();
    }


    if (toFxType == "1"){
        $(".from-type").text("厂家");
    } else if (toFxType == "2") {
        $(".from-type").text("商家");
    } 
    //不指定对方，展示不同信息，取决于toFxType传的参数
    if (toFxUserIds == "0")
    {
        //$(".from-type").text("对方信息");
        $(".goBack_span").text("批量设置SKU结算价");

        //#region 隐藏下拉框
        if (toFxType == "1") {
            $('#sel_supplier').parent().parent().parent().show();
            toFxUserIds = joinTofxUserIdFunc(_supplierList);
            $("#toFxUserIds").val(toFxUserIds);
        }
        else if (toFxType == "2") {
            $('#sel_agent').parent().parent().parent().show();
            toFxUserIds = joinTofxUserIdFunc(_agentList);
            $("#toFxUserIds").val(toFxUserIds);
        }
        else {
            $('#sel_supplier').parent().parent().parent().hide();
            $('#sel_agent').parent().parent().parent().hide();
        }
        //#endregion

    }

    //查询分隔符号转换
    $('.MultipleQuery').on("blur", function () {
        $(this).val($(this).val().multipleQueryFormatString());
    });

    function joinTofxUserIdFunc(collection) {
        let concatenatedString = '';
        for (let i = 0; i < collection.length; i++) {
            concatenatedString += collection[i].FxUserId;
            if (i !== collection.length - 1) {
                concatenatedString += ', ';
            }
        }
        return concatenatedString;
    }
    //console.log("FxUserId:", toFxUserIds);
    //console.log("type:", toFxType);
</script>
@Scripts.Render("~/bundles/pricesettingclound")
