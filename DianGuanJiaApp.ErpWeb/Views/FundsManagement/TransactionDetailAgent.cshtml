
@{
    ViewBag.Title = "交易明细-我的商家";
    ViewBag.MenuId = "TransactionDetailAgent";
    ViewBag.MenuActive = "SupplySet1688_1";
    ViewBag.MenuTopNav = "TransactionDetailSupplier";
    var fromUrl = Request.QueryString["fromUrl"] + "";
    if (fromUrl == "AliIncludeOrder")
    {
        ViewBag.MenuId = "TransactionDetailAgent";
    }
    else if(fromUrl == "AliIncludePayOrder")
    {
        ViewBag.MenuId = "TransactionDetailSupplier";
    }
}

@section Header{
    <style type="text/css">
        .icon-tianjia {
            margin-right: 3px;
        }

        .layui-mysearch {
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f8f8;
        }

        .layui-mytable {
            width: 100%;
            display: inline-block;
        }

        .layui-mywrap {
            padding-bottom: 15px;
        }
        .accountManagement-mid-title {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .accountManagement-mid-nav {
            display: flex;
        }
            .accountManagement-mid .wu-tabNav-two .wu-tabNav-li, .wu-tabNav-two > li {
                padding: 6px 12px;
            }   
            .accountManagement-mid .wu-tabNav-two > li:last-child {
                margin-right: 8px;
            }
            .accountManagement-mid-nav > li {
                /* padding: 10px 5px; */
                /* margin: 0 5px; */
                cursor: pointer;
                width: 52px;
                height: 32px;
                padding: 6px 12px;
                margin-right: 8px;
                line-height: 20px;
                text-align: center;
                color: rgba(0,0,0,0.9);
            }

                .accountManagement-mid-nav > li.active {
                    color: #3aadff;
                    position: relative;
                }

                    /* .accountManagement-mid-nav > li.active::before {
                        width: 100%;
                        height: 2px;
                        display: block;
                        
                        content: '';
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        background-color: #3aadff;
                    } */

        .accountManagement-mid-title .layui-btn {
            margin-left: 10px;
        }

        .newinputSelectTime .newwrapperTime_input {
            /* justify-content: center; */
        }

        .accountManagement-mid-content {
            display: flex;
            flex-direction: row;
            /* margin: 15px 0; */
            margin-top: 16px;
        }

        .accountManagement-mid-ul {
            width: 600px;
            border: 1px solid #e2e2e2;
            padding: 15px;
            display: flex;
            flex-direction: column;
            /* margin-bottom: 15px; */
            margin-right: 15px;
            /* box-shadow: 0 0 6px 5px #f2f2f2; */
        }

            .accountManagement-mid-ul .accountManagement-mid-ul-li {
                display: flex;
            }

                .accountManagement-mid-ul .accountManagement-mid-ul-li:nth-child(1) {
                    border-bottom: 1px solid #e2e2e2;
                    display: flex;
                    align-items: center;
                    padding-bottom: 15px;
                    justify-content: space-between;
                }

                    .accountManagement-mid-ul .accountManagement-mid-ul-li:nth-child(1) input {
                        width: 150px;
                        height: 35px;
                    }

        .accountManagement-mid-ul-title > span:nth-child(1) {
            /* font-size: 17px;
            color: #999; */
            /* margin-right: 15px; */
        }
        .accountManagement-mid-ul-title .icon-wrap {
            width: 20px;
            height: 20px;
            border-radius: 50%;
           
        }
        .accountManagement-mid-ul-title .icon-wrap.incomeIcon {
            background-color: rgba(234, 87, 46, 0.1);
            background-image: url("/Content/images/incomeWallet.png");
        }
        .accountManagement-mid-ul-title .icon-wrap.expenditrueIcon {
            background-color: rgba(136, 189, 58, 0.1);
            background-image: url("/Content/images/expenditureWallet.png");
        }
        .accountManagement-mid-ul .accountManagement-mid-ul-li:nth-child(2) {
            /* padding: 25px 15px; */
            padding: 16px 24px;
            display: flex;
            flex-direction: row;
            box-sizing: border-box;
        }

        .accountManagement-mid-ul .accountManagement-mid-ul-li-item {
            display: flex;
            flex-direction: column;
            /* border-right: 1px solid #e2e2e2; */
            flex: 1;
            /* align-items: center; */
        }

            .accountManagement-mid-ul .accountManagement-mid-ul-li-item:last-child {
                border-right: unset;
            }

        .accountManagement-mid-ul-li .icon-xiangxia {
            display: inline-block;
            transform: rotate(275deg);
            font-size: 12px;
        }

        .accountManagement-mid-ul-li-item .accountManagement-itemNum {
            /* font-size: 20px; */
            font-weight: 700;
            /* margin-top: 10px; */
            margin-top: 4px;
            /* color: #333; */
        }

        .accountManagement-down-title {
            font-size: 17px;
            color: #999;
            padding-bottom: 15px;
        }

        .accountManagement-down-search {
            display: flex;
            flex-direction: column;
        }

        .accountManagement-down-searchWrap {
            display: flex;
        }

        .mysearch-partTwo {
            border-left: unset;
            /* padding-left: 5px; */
            padding-left: 16px;
            flex-direction: row;
        }

            .mysearch-partTwo .layui-btn {
                margin-right: 15px;
            }


        #changePartner.layui-tab-title li {
            padding: 0;
        }

            #changePartner.layui-tab-title li a {
                /* padding: 0 15px; */
                padding: 0 12px;
            }
        .accountManagement-down {
            padding-bottom:0;
        }
        .exportExcelBtn {
            background-color: #fff;
            color: #77bf04;
            border: 1px solid #77bf04;
        }
        .exportExcelBtn:hover {
            color: #77bf04!important;
        }


        #helpImageWrap {
            margin-top:75px;
        }
        .newVersionsLeftNav #helpImageWrap {
            margin-top:65px;
        }
        .newVersionsLeftNav.hasTopBanner #helpImageWrap {
            margin-top: 15px;
        }
        .accountManagement-mid .wu-time .newinputSelectTime {
                width: 240px !important;
                height: 32px !important;
                /* background-color: rgba(8, 136, 255, 0.08);
                border-color: rgba(8, 136, 255, 0.2); */
            }
        .accountManagement-down-search .wu-timeWrap .newinputSelectTime {
            width: 240px !important;
            height: 32px !important;
        }  
        .accountManagement-down {
            display: flex;
            flex-direction: column;
        }   
        .accountManagement-down .wu-tableWrap {
            width: unset;
            min-width: unset;
        }
        .accountManagement-down .layui-input:hover {
            border-color: #0888ff !important;
        }
        .concatLayoutWrap .hasTopBanner .layui-bodys {
            margin-top: 114px !important;
        }
        .layui-tab-title .layui-this a {
            color: #0888ff !important;
        }
    </style>
}

@{Html.RenderPartial("~/Views/FundsManagement/FundsManagementNav.cshtml");}
@{Html.RenderPartial("~/Views/FundsManagement/index.cshtml");}

<div class="layui-mywrap accountManagement-down wu-8radius wu-p0" id="agent_payment_statement" >
    <!-- <div class="accountManagement-down-title">
        <span>支付记录</span>
    </div> -->
    <div class="accountManagement-down-search">
        <div class="layui-tab wu-layui-tab" style="margin:0;padding-bottom: 15px;">
            <ul class="layui-tab-title" id="changePartner">
                <li class="layui-this" data-type="MyAgent">
                    <a href="javascript:void(0);" onclick="transactionDetailAgentModule.toPaymentStatement('/FundsManagement/TransactionDetailAgent">我的商家</a>
                </li>
                <li data-type="MySupplier">
                    <a href="javascript:void(0);" onclick="transactionDetailAgentModule.toPaymentStatement('/FundsManagement/TransactionDetailSupplier')">我的厂家</a>
                </li>
            </ul>
        </div>
        <div class="accountManagement-down-searchWrap wu-pL16 ">
            <form class="layui-form wu-flex wu-mB16 wu-pR8 accountManagement-down-searchWrap-form" style="border-right: 1px solid rgba(0, 0, 0, 0.1);">
                <!-- <div class="layui-inline mysearch-partOne">
                    <div class="layui-input-inline">支付创建时间</div>
                </div> -->

                <div class="layui-inline mysearch-partOne wu-timeWrap wu-mR8 wu-mB0 ">
                    <div class="wu-selectWrap wu-form-mid">
						<span class="wu-timeWrap-left">支付创建时间</span>
					</div>
                    <div class="newinputSelectTime" id="inputSelectTime"></div>
                    <i class="iconfont icon-a-calendar1x"></i>
                </div>
                <div class="layui-inline mysearch-partOne wu-mR8 wu-mB0" id="search_input">
                    <div class="layui-input-inline input-MyAgent wu-inputWrap wu-form-mid" style="width: 160px;">
                        <input type="text" class="layui-input wu-input" placeholder="搜索已合作商家账号" id="agentAccount" value="@ViewBag.AgentAccount">
                    </div>
                </div>
                <!-- 						<div class="layui-inline mysearch-partOne">
                                            <div class="layui-input-inline" style="width: 160px;">
                                                <input type="text" class="layui-input" name="PlatformOrderId" placeholder="多个订单编号(,)分隔" title="多个订单编号(,)分隔" >
                                            </div>
                                        </div>
                                        <div class="layui-inline mysearch-partOne">
                                            <div class="layui-input-inline" style="width: 160px;">
                                                <input type="text" class="layui-input" name="PlatformOrderId" placeholder="多个系统编号(,)分隔" title="多个系统编号(,)分隔">
                                            </div>
                                        </div> -->
                <div class="layui-inline mysearch-partOne wu-mR8 wu-mB0">
                    <div class="layui-input-inline wu-inputWrap wu-form-mid" style="width: 160px;">
                        <input type="text" class="layui-input wu-input" id="applyNo" placeholder="多个支付批次(,)分隔">
                    </div>
                </div>
            </form>
            <div class="layui-inline mysearch-partTwo">
                <button type="button" class="layui-btn layui-btn-normal layui-btn35 wu-btn wu-btn-mid wu-primary wu-two" onclick="transactionDetailAgentModule.loadList()">查询</button>
                <button type="button" id="ResetConditions" class="layui-btn layui-btn-primary layui-btn35 wu-btn wu-btn-mid wu-primary" onclick="transactionDetailAgentModule.reset()">重置</button>
                <button type="button" class="layui-btn layui-btn35  exportExcelBtn wu-btn wu-btn-mid wu-primary" onclick="transactionDetailAgentModule.ExportExcel()">导出</button>
            </div>
        </div>

    </div>
    <div class="layui-mytable wu-tableWrap min1200 wu-mL16 wu-mR16">
        <table class="stockup_table_content new-mytable wu-table-one">
            <thead>
            <tr>
                <th style="width:50px;max-width:50px">序号</th>
                <th style="min-width:100px">付款方</th>
                <th style="min-width:100px">交易类型</th>
                <th>批次支付时间</th>
                <th style="min-width:100px">交易批次</th>
                <th style="min-width:100px;">已支付订单数量</th>
                <th style="min-width:100px;">已收款总金额(元)</th>
                <th style="min-width:100px;">收款账户</th>
                <th style="min-width:100px;text-align: center;">操作</th>
            </tr>
            </thead>
            <tbody id="tb_data_list">
                
            </tbody>
        </table>
        <!-- <div class="layui-myPage" id="paging"></div> -->
    </div>
    <div class="layui-myPage wu-mR16" id="paging"></div>
</div>


<script id="agent_payment_statement_list_tmpl" type="text/x-jsrender">
    <tr>
        <td>{{:#index + 1}}</td>
        <td>{{:PayAccountName}}</td>
        <td>
            {{if PayChannel == 'wangshangpay'}}
            <span>采购金支付</span>
            {{/if}}
        </td>
        <td>{{:PayResultTime}}</td>
        <td>{{:ApplyNo}}	</td>
        <td>
            <span class="dColor">{{:OrderSuccessNums}}</span>
        </td>
        <td>{{:OrderTotalPaySuccessAmount}}</td>
        <td>
            <a href="https://anxingou.1688.com/page/BANK_CLOUD_PAY/apply?role=PAYEE&tracelog=dingqun" class="dColor hover">{{:SupplierName}}</a>
        </td>
        <td style="text-align: center;">
            <span class="dColor hover" onclick="transactionDetailAgentModule.toPaymentStatementOrderList(this)">查看订单明细</span>
            <input type="hidden" data-outPayNo="{{:OutPayNo}}" />
        </td>
    </tr>
</script>

<script type="text/javascript">
    var transactionDetailAgentModule = (function (module, common, $, ly) {
        var _pageIndex = 1, _pageSize = 10;
        var startTime = "@ViewBag.StartTime";
        var endTime = "@ViewBag.EndTime";
        var _rows = [];
        $(function () {
            localStorage.setItem("FundsManagement/TransactionDetail",'agent');
			//分页
			layui.laypage.render({
				elem: 'paging',
                theme: ' wu-page wu-one',
                limit: _pageSize,
                curr: _pageIndex,
				layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
				jump: function(obj, first) {

				}
			});
            wuFormModule.initblurInput('.wu-inputWrap');

			$$.navActive('#changeNavList', function(i, item) { //切换
				var status = $(item).attr("data-status");
				if (status == 'fail') { //自动发货失败
					$("#mysearch_right_fail").css({
						display: "flex"
					});
					$("#mysearch_right_success").css({
						display: "none"
					});

				} else {
					$("#mysearch_right_fail").css({
						display: "none"
					});
					$("#mysearch_right_success").css({
						display: "flex"
					});
				}
			}, 'layui-this');

            module.initSelectTime(startTime, endTime);
            module.loadList();
        })

        module.initSelectTime = function (startTime, endTime) {
            var startDate = startTime || new Date().Format("yyyy-MM-dd");
            var endDate = endTime || new Date().Format("yyyy-MM-dd");
            var option = {
                ele: "#inputSelectTime",
                defaultDayOption: {
                    startDate: startDate, //startDate可以不用填 不用填调用客户端时间  格式：yyyy-MM-dd
                    endDate: endDate, //endDate可以不用填 不用填调用客户端时间      格式：yyyy-MM-dd
                },
                width: 165,
                height: 35,
                hideChecked:true,
                sureCallBack: function (obj) { }
            };
            //时期组件
            var timePicker = new wu_TimePicker();
            timePicker.initData(option);
        }

        module.reset = function () {
            _pageIndex = 1;
            $("#agentAccount").val('');
            $("#applyNo").val('');
            transactionDetailAgentModule.initSelectTime();
            wuFormModule.resetConditionsStyle('.accountManagement-down-searchWrap-form')
        }

        module.targetUrl = function (url) {

            window.open(common.rewriteUrl(url),'_self');

        }

        module.loadList = function (isPaging) {
            if (isPaging != true)
                _pageIndex = 1;
            const data = module.getCondition();
            
            commonModule.Ajax({
                url: "/PaymentStatement/PaymentStatementList",
                type: "GET",
                loading: true,
                data: data,
                success: function (rsp) {
                    if (commonModule.IsError(rsp)) {
                        return;
                    }

                    $('#lblTotalRecord').text(rsp.Data.Total);
                    var tplt = $.templates("#agent_payment_statement_list_tmpl")
                    var html = tplt.render(rsp.Data.Rows, { orderIdFormatter: common.OrderIdFormatter});
                    $("#tb_data_list").empty().append(html);
                    //无数据页面提示
                    if (rsp.Data.Rows.length == 0 && !rsp.Data.Rows.length) {
                        var noDataHtml = '<tr id="tableNoDataShow"><td colspan="9"><div class="tableNoDataShow"><img src="/Content/images/noData-icon.png" /><span class="tableNoDataShow-title">暂无数据！</span></div></td></tr>'
                        $("#tb_data_list").empty().append(noDataHtml);
                    }

                    if (isPaging == true) {
                        return;
                    }
                    layui.laypage.render({
                        elem: 'paging',
                        theme: ' wu-page wu-one',
                        count: rsp.Data.Total,
                        limit: _pageSize,
				        layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                        jump: function (obj, first) {
                            if (!first) {
                                _pageIndex = obj.curr;
                                _pageSize = obj.limit;
                                module.loadList(true);
                            }
                        }
                    });
                }
            });
        }

        module.initialization = function ()
        {
            module.loadList();
        }

        module.toPaymentStatementOrderList = function (ele) {
            const data = $(ele).next().data();
            const url = '/FundsManagement/ShowTransactionDetailAgent?fromUrl=@fromUrl&OutPayNo=' + data.outpayno;
            window.open(common.rewriteUrl(url), '_self');
        }
        module.toPaymentStatement = function (src) {
            
            var thisFunc = function () {
                var fromUrl = commonModule.getQueryVariable("fromUrl");
                if (fromUrl) {
                    src = src + "?fromUrl=" + fromUrl;
                }
                window.open(common.rewriteUrl(src), '_self');
            }

            commonModule.FxPermission(function (p) {
                if (p.TransactionDetailSupplier == src) {
                    commonModule.CheckPermission(function (success) {
                        if (success) {
                            thisFunc();
                        }
                        else return;

                    }, p.TransactionDetailSupplier);
                } else thisFunc();
            });
        }
        module.ExportExcel = function () {
            const data = module.getCondition();
            exportOrderModule.ExportExcel({
                url: '/PaymentStatement/ExportExcel',
                type: 'POST',
                data: { options: data },
                loadMsg: "正在导出Excel，请稍等……",
                success: function () {
                    layer.msg("导出成功");
                }
            });
        }
        module.getCondition = function () {
            const data = {};
            data.AgentKey = $("#agentAccount").val();
            data.ApplyNo = $("#applyNo").val();
            data.OutBizNo = $("#outBizNo").val();
            data.PayType = 0;
            data.StartTime = $("#agent_payment_statement .QueryDateVal").attr('start-date');
            data.EndTime = $("#agent_payment_statement .QueryDateVal").attr('end-date');
            data.PageIndex = _pageIndex;
            data.PageSize = _pageSize;
            return data;
        }



		return module;
    }(transactionDetailAgentModule || {}, commonModule, jQuery, layer));
</script>