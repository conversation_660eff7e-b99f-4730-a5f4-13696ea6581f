<style>
    .newDemandContent {
    }
    .newDemandContentWrap {
        width:830px;
        padding: 15px;
        box-sizing: border-box;
        background-color: #fff;
        display:none;
    }
        .newDemandContentWrap .newDemandContentWrap-main {
            display:flex;
            flex-direction:column;
        }

            .newDemandContentWrap .newDemandContentWrap-main .newDemandContentWrap-main-title {
                font-size: 17px;
                color: #262626;
                font-weight: 700;
                padding: 25px 5px 25px 5px;
                display: flex;
                flex-direction: row;
                justify-content:center;
            }

        .newDemandContentWrap .newDemandContentWrap-content {

        }
       .newDemandContentWrap .newDemandContentWrap-content .newDemandContentWrap-content-item {
           padding: 10px 15px;
           position: relative;
           display:flex;
           align-items:center;
       }
    .newDemandContentWrap-main-item-ul {
        display: flex;
    }
        .newDemandContentWrap-main-item-ul > li {
            margin-right: 20px;
            width: 120px;
            height: 35px;
            background-color: #f5f5f5;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
            position: relative;
        }
    .newDemandContentWrap-main-item-title {
        margin-right:15px;
        width:192px;
        text-align:right;
        display:inline-block;
    }
    .newDemandContentWrap-content-item-right {
        display:flex;
    }
    .newDemandContentWrap-select-wrap {
        width: 120px;
        margin-right:20px;
    }
    .newDemandContentWrap .layui-form-select .layui-input {
        width: 120px;
    }
    .newDemandContentWrap .productPicWrap {
        display: flex;
    }
    .newDemandContentWrap .productPicShow {
        display: flex;
    }
    .newDemandContentWrap .icon-tubiaolunkuo- {
        border: 1px solid #e6e6e6;
        width: 70px;
        height: 70px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 2px;
        font-size: 40px;
        color: #aaa;
        cursor: pointer;
    }
    .picTitle {
        color: #04385d;
        padding: 5px 0 0 0;
    }
    .flexColumn {
        flex-direction:column;
    }
    .newDemandContentWrap-content-item-one {
        display:flex;
        flex-direction:column;
    }
    .newDemandContentWrap-content-item-one-title {
        font-size: 15px;
        line-height: 26px;
        color: #666;
        margin-bottom: 8px;
    }
    .newDemandContentWrap-content-item-one textArea {
        width: 500px;
        height: 100px;
        padding: 10px;
        box-sizing: border-box;
    }
    .newDemandContentWrap-main-item-ul02 > li {
        width:85px;
    }
    .newDemandContentWrap-main-item-ul02 > li:last-child {
        margin-right:0;
    }
    .newDemandContentWrap-main-item-ul > li.active {
        color: #0888ff;
        background-color: #f0f9ff;
        overflow: hidden;
    }
        .newDemandContentWrap-main-item-ul > li.active::before {
            border-bottom: 20px solid #0888ff;
            border-left: 20px solid transparent;
            position: absolute;
            bottom: 0;
            right: 0;
            display: block;
            content: "";
        }
        .newDemandContentWrap-main-item-ul > li.active::after {
            display: block;
            content: "";
            width: 8px;
            height: 5px;
            border-left: 1px solid #fff;
            border-bottom: 1px solid #fff;
            position: absolute;
            bottom: 6px;
            right: 1px;
            transform: rotate(313deg);
        }
    .productPicShow > .productPic-list {
        margin-right: 13px;
        position: relative;
        border: 1px solid #e2e2e2;
        border-radius: 2px;
        cursor: move;
    }

        .productPicShow > .productPic-list > img {
            width: 70px;
            height: 70px;
        }

        .productPicShow > .productPic-list > .icon-laji {
            width: 70px;
            background: rgba(0, 0, 0, 0.4);
            height: 20px;
            justify-content: center;
            align-items: center;
            color: rgba(255, 255, 255, 0.9);
            position: absolute;
            bottom: 0;
            left: 0;
            cursor: pointer;
            display: none;
        }

        .productPicShow > .productPic-list:hover > .icon-laji {
            display: flex;
        }
    .icon-tubiaolunkuo- {
        position:relative;
        justify-content:center;
        align-items:center;
    }
    .addDemandPicBtn-title {
        left: 0;
        font-size: 12px;
        font-weight: 400;
        width: 100%;
        position: absolute;
        bottom: 2px;
        text-align:center;
    }
    .newDemandContentWrap .icon-tubiaolunkuo-:before {
        position:relative;
        top:-5px;
    }
    .newDemandContentWrap-content .sColor {
        position: relative;
        top: 3px;
        margin-right: 5px;
        color: #ff4050!important;
    }
    .newDemandContentWrap .layui-input, .newDemandContentWrap .layui-select {
        border-radius: 6px;
        height: 32px;
    }
    .newDemandContentWrap #complaintOtherContent, .newDemandContentWrap .addDemandPicBt, #disposeContent {
        border-radius: 6px;
    }
</style>

<div class="newDemandContent">

    <div class="newDemandContentWrap" id="suggest_dailog">
        <div class="newDemandContentWrap-main">
            <div class="newDemandContentWrap-main-title">
                <span>请描述您的需求情况，产品部门会认真确认您的需求，若需求采纳，会优先为您配置功能使用</span>
            </div>
            <ul class="newDemandContentWrap-content">
                <li class="newDemandContentWrap-content-item">
                    <div class="newDemandContentWrap-main-item-title"><i class="sColor">*</i>您的身份</div>
                    <div class="newDemandContentWrap-content-item-right">
                        <ul class="newDemandContentWrap-main-item-ul" id="userRole_ul">
                            <li class="">下游分销商</li>
                            <li>上游供货商</li>
                            <li>多级分销商(中间商)</li>
                        </ul>
                    </div>
                </li>
                <li class="newDemandContentWrap-content-item">
                    <div class="newDemandContentWrap-main-item-title"><i class="sColor">*</i>请选择您的需求紧急程度</div>
                    <div class="newDemandContentWrap-content-item-right">
                        <ul class="newDemandContentWrap-main-item-ul" id="disposeRank_ul">
                            <li>—般程度</li>
                            <li>紧急需求</li>
                            <li>严重紧急</li>
                        </ul>
                    </div>
                </li>
                <li class="newDemandContentWrap-content-item">
                    <div class="newDemandContentWrap-main-item-title"><i class="sColor">*</i>其他系统是否有同款功能</div>
                    <div class="newDemandContentWrap-content-item-right layui-form my-form">
                        <div class="newDemandContentWrap-select-wrap">
                            <select class="newDemandContentWrap-select" id="isOtherISV" lay-filter="isOtherISV">>
                                <option value="">请选择</option>
                                <option value="1">是</option>
                                <option value="2">否</option>
                            </select>
                        </div>
                        <input id="otherISVName" name="otherISVName" type="text" class="layui-input" style="width:260px;display:none;" placeholder="请输入竞品应用名称">
                    </div>
                </li>
                <li class="newDemandContentWrap-content-item" style="align-items: flex-start;">
                    <div class="newDemandContentWrap-main-item-title">其他系统该功能截图</div>
                    <div class="newDemandContentWrap-content-item-right flexColumn">
                        <div class="newDemandContentWrap-content-item-one">
                            <div class="productPicWrap">
                                <div class="productPicShow" ></div>
                                <span ondrop="newDemandModule.downPicdrop(event,this)" ondragover="newDemandModule.allowDrop(event)" draggable="true" ondragstart="newDemandModule.allowDrop(event)"  class="addDemandPicBt iconfont icon-tubiaolunkuo-" onclick="newDemandModule.addDemandPic.bind(this)()"><span class="addDemandPicBtn-title">可拖图到这</span></span>
                                <input multiple type="file" name="addDemandPic" id="suggestFile"  class="addDemandPicFile" onchange="newDemandModule.changeDemandPic.bind(this)()" style="display:none">
                            </div>
                            <div class="picTitle sColor">请上传截图资料(最多六张截图)</div>
                        </div>

                        <div class="newDemandContentWrap-content-item-one" style="margin-top:15px;">
                            <div class="newDemandContentWrap-content-item-one-title">您需要的功能是为了解决什么业务问题</div>
                            <textarea id="disposeContent" placeholder="请输入您需要的功能是为了解决什么业务问题"></textarea>
                        </div>
                    </div>
                </li>
                <li class="newDemandContentWrap-content-item">
                    <div class="newDemandContentWrap-main-item-title"><i class="sColor">*</i>是否愿意接受产品经理电话回访</div>
                    <div class="newDemandContentWrap-content-item-right layui-form my-form">
                        <select class="newDemandContentWrap-select" id="isCallBack" lay-filter="isCallBack">
                            <option value="">请选择</option>
                            <option value="1">愿意</option>
                            <option value="2">不愿意</option>
                        </select>
                    </div>
                </li>
                <li class="newDemandContentWrap-content-item">
                    <div class="newDemandContentWrap-main-item-title">您的需求对接人联系方式</div>
                    <div class="newDemandContentWrap-content-item-right layui-form my-form">
                        <input id="callBackContact" name="newDemandContentWrap-tel" type="text" class="layui-input" style="width:500px;" placeholder="请输入电话号码,请放心该号码只用户需求后续跟进反馈">

                    </div>
                </li>

                <li class="newDemandContentWrap-content-item" style="display: flex; justify-content: center; padding: 0; padding-top: 6px;">
                    <span class="wu-btn wu-btn-mid" onclick="newDemandModule.submitSuggest()">提交需求反馈</span>
                </li>

            </ul>
        </div>
    </div>

    <div class="newDemandContentWrap" id="complaint_dailog">
        <div class="newDemandContentWrap-main">
            <div class="newDemandContentWrap-main-title">
                <span>请您详细描述投诉内容，客服主管会认真处理投诉问题并及时培训其他客服同事，很抱歉为您带来的不便</span>
            </div>
            <ul class="newDemandContentWrap-content">
                <li class="newDemandContentWrap-content-item">
                    <div class="newDemandContentWrap-main-item-title"><i class="sColor">*</i>您要投诉的内容</div>
                    <div class="newDemandContentWrap-content-item-right">
                        <ul class="newDemandContentWrap-main-item-ul" id="complaintType_ul">
                            <li class="active">客服人员接待问题</li>
                            <li>技术处理bug时效</li>
                        </ul>
                    </div>
                </li>
                <li class="newDemandContentWrap-content-item">
                    <div class="newDemandContentWrap-main-item-title" id="complaintType_kefuTitle"><i class="sColor">*</i>您要投诉的客服内容</div>
                    <div class="newDemandContentWrap-content-item-right" id="complaintType_uls">
                        <ul class="newDemandContentWrap-main-item-ul newDemandContentWrap-main-item-ul02" id="complaintType_uls_none">
                            <li>客服态度差</li>
                            <li>响应太慢</li>
                            <li>无法联系客服</li>
                            <li>问题处理时效慢</li>
                            <li>业务不熟悉</li>
                        </ul>
                        <ul class="newDemandContentWrap-main-item-ul newDemandContentWrap-main-item-ul02" style="display:none" id="complaintType_uls_two">
                            <li>1小时内</li>
                            <li>24小时内</li>
                            <li>3天内</li>
                            <li>超过3天</li>
                        </ul>
                    </div>
                </li>
                <li class="newDemandContentWrap-content-item newDemandContentWrap_content_itemThree">
                    <div class="newDemandContentWrap-main-item-title"><i class="sColor">*</i>您投诉的客服昵称是</div>
                    <div class="newDemandContentWrap-content-item-right layui-form my-form">
                        <input name="kefuName" id="complaintName" type="text" class="layui-input" style="width:240px;margin-right:20px" placeholder="请输入客服昵称">
                        <i class="sColor">*</i>
                        <input name="concatType" id="complaintContactName" type="text" class="layui-input" style="width:240px;" placeholder="请备注咨询方式旺旺，QQ，企业微信">
                    </div>
                </li>


                <li class="newDemandContentWrap-content-item" style="align-items: flex-start;">
                    <div class="newDemandContentWrap-main-item-title" id="complaintType_picTitle">与客服对话相关截图</div>
                    <div class="newDemandContentWrap-content-item-right flexColumn">
                        <div class="newDemandContentWrap-content-item-one">

                            <div class="productPicWrap">
                                <div class="productPicShow"></div>
                                <span ondrop="newDemandModule.downPicdrop(event,this)" ondragover="newDemandModule.allowDrop(event)" draggable="true" ondragstart="newDemandModule.allowDrop(event)" class="addDemandPicBt iconfont icon-tubiaolunkuo-" onclick="newDemandModule.addDemandPic.bind(this)()"><span class="addDemandPicBtn-title">可拖图到这</span></span>
                                <input multiple type="file" name="addDemandPic" class="addDemandPicFile" id="ComplaintFile"  onchange="newDemandModule.changeDemandPic.bind(this)()" style="display:none">
                            </div>


                            <div class="picTitle sColor" id="complaintType_picText">请上传与客服对话导致您需要投诉的相关截图证明(最多六张截图)</div>
                        </div>

                        <div class="newDemandContentWrap-content-item-one" style="margin-top:15px;">
                            <textarea id="complaintOtherContent" placeholder="如有其他反馈,请留言备注"></textarea>
                        </div>
                    </div>
                </li>

                <li class="newDemandContentWrap-content-item newDemandContentWrap_content_itemThree" >
                    <div class="newDemandContentWrap-main-item-title"><i class="sColor">*</i>问题后续是否得到处理</div>
                    <div class="newDemandContentWrap-content-item-right layui-form my-form">
                        <select class="newDemandContentWrap-select" id="isDeal">
                            <option value="">请选择</option>
                            <option value="1">有</option>
                            <option value="2">无</option>
                        </select>
                    </div>
                </li>

                <li class="newDemandContentWrap-content-item newDemandContentWrap_content_itemFourth" style="display:none;">
                    <div class="newDemandContentWrap-main-item-title"><i class="sColor">*</i>是否有客服同事为您后续反馈进度</div>
                    <div class="newDemandContentWrap-content-item-right layui-form my-form">
                        <select class="newDemandContentWrap-select" id="isAnswer">
                            <option value="">请选择</option>
                            <option value="1">有</option>
                            <option value="2">无</option>
                        </select>
                    </div>
                </li>
                <li class="newDemandContentWrap-content-item">
                    <div class="newDemandContentWrap-main-item-title"><i class="sColor">*</i>是否愿意接受电话回访</div>
                    <div class="newDemandContentWrap-content-item-right layui-form my-form">
                        <select class="newDemandContentWrap-select" id="isSupervisorCallBack">
                            <option value="">请选择</option>
                            <option value="1">愿意</option>
                            <option value="2">不愿意</option>
                        </select>
                    </div>
                </li>
                <li class="newDemandContentWrap-content-item">
                    <div class="newDemandContentWrap-main-item-title"><i class="sColor">*</i>您的联系方式</div>
                    <div class="newDemandContentWrap-content-item-right layui-form my-form">
                        <input id="complaintTel" type="text" class="layui-input" style="width:500px;" placeholder="请输入电话号码，请放心该号码只用于反馈跟进">
                    </div>
                </li>
                <li class="newDemandContentWrap-content-item">
                    <div class="newDemandContentWrap-main-item-title">您的QQ或微信号</div>
                    <div class="newDemandContentWrap-content-item-right layui-form my-form">
                        <input id="complaintQQ" type="text" class="layui-input" style="width:500px;" placeholder="请输入 QQ：xxxxxx，或 微信号：xxxxxx">
                    </div>
                </li>
                <li class="newDemandContentWrap-content-item" style="display: flex; justify-content: center; padding: 0; padding-top: 6px; ">
                    <span class="wu-btn wu-btn-mid" onclick="newDemandModule.submitComplaint()">提交投诉</span>
                </li>

            </ul>
        </div>
    </div>

</div>
<script>
    ; (function (win) {

        var dgjUrl_origin = "https://www.dgjapp.com/";
        //var dgjUrl_origin = "http://*********:30002/";

        win.newDemandModule = {};
        var demandPicArry = [];
        var complaintTypeNum = 0;

        newDemandModule.addDemandPic = function () { //添加图片		
            $(this).next().click();
        }


        newDemandModule.changeDemandPic = function () { //选择图片
            var e = event || window.event;
            var that = this;

            $(".productPicWrap .noInputData").remove();//选择图片时，删除警告框和提示语

            demandPicArry = demandPicArry.sort(function (a, b) {
                return a.sort - b.sort
            });
            var selectedPicFiles = this.files; //获取所有选中的文件
            selectedPicShow(selectedPicFiles, this);
        }

        function selectedPicShow(selectedPicFiles, that) {



            if (selectedPicFiles.length > 6 || demandPicArry.length + selectedPicFiles.length > 6) { //所选择的图片不要超过5张
                layer.msg("不要超过6张");
                return;
            }

            if (demandPicArry.length + selectedPicFiles.length < 6) {
                $(that).prevAll('.addDemandPicBt').css({
                    display: 'flex'
                });
            } else {
                $(that).prevAll('.addDemandPicBt').hide();
            }

            var promiseSelectPicArry = []; //解决异步任务  Promise.all调用
            var maxSort = demandPicArry.length ? demandPicArry[demandPicArry.length - 1].sort + 1 : 0; //取产品图片数组 序号最大的值
            for (var i = 0; i < selectedPicFiles.length; i++) {
                if (!$$.checkFileExt(selectedPicFiles[i].name, ["jpg", "png", "gif"])) {
                    layer.msg("文件格式不对,jpg,png,gif");
                    return;
                }
                if (selectedPicFiles[i].size > 1000000) { //大于1m不上传 1000000
                    layer.msg("文件大小不能大于1000k");
                    return;
                }
                var p = new Promise(function (resolve, reject) { //用Promise 有异步任务 load				
                    var fileRead = new FileReader(); //      新建一个文件对象 读取文件信息  主要作显示用的 把flies图片转为base64位
                    fileRead.readAsDataURL(selectedPicFiles[i]);
                    var picName = selectedPicFiles[i].name;
                    var $sort = maxSort + i;
                    fileRead.addEventListener("load", function () {

                        var param = {};
                        var imgBase64 = this.result;
                        param.Id = new Date().getTime();
                        param.FileName = new Date().getTime() + picName;
                        param.ImageUrl = imgBase64;
                        param.sort = $sort;   //用于排序
                        commonModule.Ajax({
                            url: dgjUrl_origin + 'login/manageArticle/fkUpPic',
                            data: param,
                            async: true,
                            loading: false,
                            type: 'POST',
                            success: function (rsp) {
                                resolve(param);
                                //console.log(rsp)
                            }
                        });

                    })
                })
                promiseSelectPicArry.push(p);
            }

            Promise.all(promiseSelectPicArry).then(function (resultData) {
                that.value = "";
                demandPicArry = demandPicArry.concat(resultData);
                newDemandModule.initProductPicShow(demandPicArry, that);
            })

        }


        newDemandModule.initProductPicShow = function (picData, that) { //渲染商品主图图片

            var html = "";
            for (var i = 0; i < picData.length; i++) {
                html += '<div class="productPic-list" data-sort="' + picData[i].sort + '"  id=div' + i +
                    ' ondrop="newDemandModule.drop(event,this)" ondragover="newDemandModule.allowDrop(event)" draggable="true" ondragstart="newDemandModule.drag(event, this)">'
                html += '<img src="' + picData[i].ImageUrl + '" alt="">'
                html += '<i class="iconfont icon-laji" onclick=\'newDemandModule.delDemandPic("' + picData[i].Id + '",this)\' ></i>';
                html += '</div>'
            }
            $(that).prevAll(".productPicShow").html(html);
            if (picData.length < 6) { //图片小于5张时，显示添加按钮
                $(that).prevAll(".addDemandPicBt").css({
                    display: 'flex'
                });
            } else {
                $(that).prevAll(".addDemandPicBt").hide();
            }
        }
        newDemandModule.delDemandPic = function (id, that) { //删除商品主图图片
            var that = productPicWrap = $(that).closest('.productPicWrap').find('.addDemandPicFile')[0];
            for (var i = 0; i < demandPicArry.length; i++) {
                if (demandPicArry[i].Id == id) {
                    demandPicArry.splice(i, 1);
                    break;
                }
            }
            newDemandModule.initProductPicShow(demandPicArry, that);
        }




        newDemandModule.downPicdrop = function (ev, that) {
            ev.preventDefault();
            var aFiles = ev.dataTransfer.files;
            selectedPicShow(aFiles, $(that).next().get(0));
        }



        //以下是拖拽商品主图片图片更换位置js----------------------------------------------------

        newDemandModule.allowDrop = function (ev) {
            ev.preventDefault();
        }
        var srcdiv = null; //刚才开始拖元素
        var srcSort = ""; //刚才开始拖序号			
        newDemandModule.drag = function (ev, divdom) { //开始移动时触发函数 divdom为鼠标移动的元素
            srcdiv = divdom;
            ev.dataTransfer.setData("text/html", divdom.innerHTML); //设置一个名为text/html变量来存html
            srcSort = $(divdom).attr("data-sort");
        }
        newDemandModule.drop = function (ev, divdom) { //移动后松开鼠标时触发   divdom为移到目标元素不是托动的元素
            ev.preventDefault();
            if (srcdiv != divdom) {
                srcdiv.innerHTML = divdom.innerHTML;
                divdom.innerHTML = ev.dataTransfer.getData("text/html"); //从text/html变量取值 			
                var tarSort = $(divdom).attr("data-sort"); //获取当前拖放到目标元素序号
                var srcIndex = null;
                var tarIndex = null; //获取开始拖元素和拖放到目标元素 所在索引

                for (var i = 0; i < demandPicArry.length; i++) {
                    if (demandPicArry[i].sort == srcSort) {
                        srcIndex = i
                    } else if (demandPicArry[i].sort == tarSort) {
                        tarIndex = i
                    }
                }
                demandPicArry[srcIndex].sort = tarSort - 0;
                demandPicArry[tarIndex].sort = srcSort - 0;
                demandPicArry = demandPicArry.sort(function (a, b) {
                    return a.sort - b.sort
                });
            }
        }



        newDemandModule.submitSuggest = function () {
            if ($("#userRole_ul>li.active").length == 0) {
                layer.msg('请选择您的身份');
                return;
            }
            if ($("#disposeRank_ul>li.active").length == 0) {
                layer.msg('请选择您的需求紧急程度');
                return;
            }
            if ($('#isOtherISV').val() == "") {
                layer.msg('请选择其他系统是否有同款功能');
                return;
            }
            if ($('#isOtherISV').val() == 1 && $("#otherISVName").val().trim() == "") {
                layer.msg('请输入竞品应用名称');
                return;
            }

            if ($('#isCallBack').val() == "") {
                layer.msg('请是否愿意接受产品经理电话回访');
                return;
            }

            if ($('#isCallBack').val() == 1 && !checkMoveAndTel($("#callBackContact").val().trim())) {
                layer.msg('请输入您的需求对接人正确联系方式');
                return;
            }

            if ($('#disposeContent').val().length > 500) {
                layer.msg('请输入500字以内');
                return;
            }

            var subimitObj = {};
            subimitObj.submitType = 1;//提交类型  1为需求反馈  2为用户投诉
            subimitObj.id = new Date().getTime();
            subimitObj.userInfo = $("#userInfo s").text();
            subimitObj.currShop = commonModule.CurrShop;
            subimitObj.userContact = $("#userInfo s").text();
            subimitObj.userRole = $("#userRole_ul>li.active").text();
            subimitObj.disposeRank = $("#disposeRank_ul>li.active").text();
            subimitObj.isOtherISV = $("#isOtherISV").val();
            subimitObj.otherISVName = $("#otherISVName").val().trim();
            subimitObj.disposeContent = $("#disposeContent").val().trim();
            subimitObj.isCallBack = $("#isCallBack").val();
            subimitObj.callBackContact = $("#callBackContact").val().trim();
            var adPics = [];
            demandPicArry.forEach(function (item) {
                var obj = {};
                obj.Id = item.Id;
                obj.FileName = item.FileName;
                obj.Sort = item.sort;
                adPics.push(obj);
            })
            subimitObj.adPicss = JSON.stringify(adPics);
            commonModule.Ajax({
                url: dgjUrl_origin + 'newDemand/newDemandSuggest',
                data: subimitObj,
                async: true,
                loading: true,
                type: 'POST',
                success: function (rsp) {
                    if (rsp.success) {
                        reset("suggestFile");
                        layer.close(showSuggestDailog)
                        layer.msg(rsp.message);

                    }
                }
            });
        }

        newDemandModule.submitComplaint = function () {

            var subimitObj = {};
            if (complaintTypeNum == 0) {  //0 客服人员接待问题    1 技术处理bug时效

                if ($("#complaintType_uls_none>li.active").length == 0) {
                    layer.msg('请选择您要投诉的客服内容');
                    return;
                }

                if ($("#complaintName").val().trim() == "" || $("#complaintContactName").val().trim() == "") {
                    layer.msg('请选择您投诉的昵称和咨询方式');
                    return;
                }
                if ($("#isDeal").val() == "") {
                    layer.msg('请选择您问题后续是否得到处理');
                    return;
                }
                subimitObj.complaintContent = $("#complaintType_uls_none>li.active").text();

            } else if (complaintTypeNum == 1) {

                if ($("#complaintType_uls_two>li.active").length == 0) {
                    layer.msg('请选择您提交的bug多久未处理');
                    return;
                }

                if ($("#isAnswer").val() == "") {
                    layer.msg('请选择是否有客服同事为您后续反馈进度');
                    return;
                }

                subimitObj.untreatedTime = $("#complaintType_uls_two>li.active").text();

            }

            if ($("#isSupervisorCallBack").val() == "") {
                layer.msg('请选择是否愿意接受电话回访');
                return;
            }
            if ($("#complaintTel").val().trim() == "" && $("#complaintQQ").val().trim() == "") {
                layer.msg('请您填写联系方式或QQ号码');
                return;
            }

            if ($("#complaintOtherContent").val().length > 500) {
                layer.msg('反馈的内容在500字以内！');
                return;
            }
            subimitObj.id = new Date().getTime();
            subimitObj.userInfo = $("#userInfo s").text();
            subimitObj.currShop = commonModule.CurrShop;
            subimitObj.userContact = $("#userInfo s").text();

            subimitObj.submitType = 2;
            subimitObj.complaintType = complaintTypeNum == 0 ? '客服人员接待问题' : '技术处理bug时效';
            subimitObj.complaintTypeNum = complaintTypeNum;

            subimitObj.complaintName = $("#complaintName").val().trim();



            subimitObj.complaintContactName = $("#complaintContactName").val().trim();
            subimitObj.complaintOtherContent = $("#complaintOtherContent").val().trim();
            subimitObj.isDeal = $("#isDeal").val();
            subimitObj.isAnswer = $("#isAnswer").val();
            subimitObj.isSupervisorCallBack = $("#isSupervisorCallBack").val();
            subimitObj.complaintTel = $("#complaintTel").val();
            subimitObj.complaintQQ = $("#complaintQQ").val();


            var adPics = [];
            demandPicArry.forEach(function (item) {
                var obj = {};
                obj.Id = item.Id;
                obj.FileName = item.FileName;
                obj.Sort = item.sort;
                adPics.push(obj);
            })
            subimitObj.adPicss = JSON.stringify(adPics);

            commonModule.Ajax({
                url: dgjUrl_origin + 'newDemand/newDemandSuggest',
                data: subimitObj,
                async: true,
                loading: true,
                type: 'POST',
                success: function (rsp) {
                    if (rsp.success) {
                        reset('ComplaintFile');
                        layer.close(showComplaintDailog);
                        layer.msg('感谢您的反馈，我们将尽快通知客服主管处理您的投诉。');
                    }
                }
            });



        }

        function reset(ele) {
            $("#userRole_ul>li").removeClass("active");
            $("#disposeRank_ul>li").removeClass("active");
            $("#isOtherISV").find('option[value=""]').prop("selected", true);
            $("#otherISVName").val("").css({ display: 'none' });
            $("#disposeContent").val("");
            $("#isCallBack").find('option[value=""]').prop("selected", true);
            $("#callBackContact").val("");

            $("#complaintType_ul>li").removeClass("active");
            $("#complaintType_ul>li:eq(0)").addClass("active");
            $("#complaintType_uls_none>li").removeClass("active");
            $("#complaintType_uls_two>li").removeClass("active");
            $("#complaintName").val("");
            $("#complaintContactName").val("");
            $("#complaintOtherContent").val("");
            $("#isDeal").find('option[value=""]').prop("selected", true);
            $("#isAnswer").find('option[value=""]').prop("selected", true);
            $("#isSupervisorCallBack").find('option[value=""]').prop("selected", true);
            $("#complaintTel").val("");
            $("#complaintQQ").val("");

            layui.form.render("select");

            demandPicArry = [];
            newDemandModule.initProductPicShow(demandPicArry, document.getElementById(ele));
        }

        function checkMoveAndTel(mobile, type) { // 匹配手机号和固话，正确返回true 错误返回false

            if (typeof mobile == 'number') {              //  type 取值move为验证手机，type 取值tel为固定电话  其它值为验证两都符合一种都返回true
                mobile = mobile.toString();
            }
            var tel = /^0\d{2,3}-?\d{7,8}$/;
            var phone = /^1(3|4|5|7|8|9)\d{9}$/;
            var isTrue = false;

            if (type == "move") {
                if (phone.test(mobile)) {
                    isTrue = true;
                }
            } else if (type == "tel") {
                if (tel.test(mobile)) {
                    isTrue = true;
                }
            } else {
                if (mobile.length == 11) { //手机号码
                    if (phone.test(mobile)) {
                        isTrue = true;
                    }
                } else if (mobile.length == 12 && mobile.indexOf("-") != -1 || mobile.length == 13 && mobile.indexOf("-") != -1) { //电话号码
                    if (tel.test(mobile)) {
                        isTrue = true;
                    }
                }
            }

            return isTrue;
        }


        $(function () {

            $$.navActive("#userRole_ul", function (index, item) { }, 'active');

            $$.navActive("#disposeRank_ul", function (index, item) { }, 'active');

            $$.navActive("#complaintType_ul", function (index, item) {
                complaintTypeNum = index;
                $("#complaintType_uls .newDemandContentWrap-main-item-ul02").hide();
                $("#complaintType_uls .newDemandContentWrap-main-item-ul02:eq('" + index + "')").show();

                if (index == 0) {
                    $("#complaintType_picTitle").text('与客服对话相关截图');
                    $("#complaintType_picText").text('请上传与客服对话导致您需要投诉的相关截图证明(最多六张截图)');
                    $("#complaintType_kefuTitle").html('<i class="sColor">*</i>您要投诉的客服内容');
                    $(".newDemandContentWrap_content_itemThree").show();
                    $(".newDemandContentWrap_content_itemFourth").hide();

                } else {
                    $("#complaintType_picTitle").text('该bug紧急程度是否影响您的亏损');
                    $("#complaintType_picText").text('请上传提交bug的截图资料或者对您造成损失的截图证明(最多六张截图)');
                    $("#complaintType_kefuTitle").html('<i class="sColor">*</i>您提交的bug多久未处理');
                    $(".newDemandContentWrap_content_itemThree").hide();
                    $(".newDemandContentWrap_content_itemFourth").show();

                }



            }, 'active');
            $$.navActive("#complaintType_uls_none", function (index, item) { }, 'active');
            $$.navActive("#complaintType_uls_two", function (index, item) { }, 'active');

            layui.form.on('select(isOtherISV)', function (data) {
                if (data.value == 1) {
                    $("input[name=otherISVName]").css({ display: 'inline-block' })
                } else {
                    $("input[name=otherISVName]").css({ display: 'none' })
                }
            });
            layui.form.on('select(isCallBack)', function (data) {

            });
        });

        var showSuggestDailog = null;
        var showComplaintDailog = null;

        newDemandModule.showSuggest = function () {

            showSuggestDailog = layer.open({
                type: 1,
                title: false, //不显示标题
                content: $("#suggest_dailog"),
                area: '760', //宽高
                btn: false,
                skin: 'adialog-Shops-skin wu-dailog',
            });


        }

        newDemandModule.showComplaint = function () {

            showComplaintDailog = layer.open({
                type: 1,
                title: false, //不显示标题
                content: $("#complaint_dailog"),
                area: '760', //宽高
                btn: false,
                skin: 'adialog-Shops-skin wu-dailog',
            });


        }

    })(window);
</script>
