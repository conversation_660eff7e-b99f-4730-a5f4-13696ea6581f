@{
    Layout = "~/Views/Shared/_CloudPlatformLayout.cshtml";
    ViewBag.Title = "线下单商品库";
    ViewBag.MenuId = "OfflineProduct";
}

@section Header{
    <style>
        .allUpAndDown {
            display: none;
        }
        body .bindWrap {
            padding: 16px;
        }
        body .bindWrap-chooseProduct .bindProduct-content .bindProduct-content-title {
            color:rgba(0,0,0,0.9);       
        }
        body .bindWrap-chooseProduct.bindWrap .selectWrap, body .bindWrap .mysearch-partOne .layui-input-inline {
            margin-right: 8px;
            margin-bottom:8px;
        }
        body .bindWrap.bindWrap-chooseProduct .bindProduct-content {
            padding-top:0;
        }
        body .bindWrap-chooseProduct.bindWrap .layui-form-right {
            position: relative;
            left: -35px;
        }
        body .bindWrap-chooseProduct .checkLeftProductList-sku-title {
            background: rgba(0, 0, 0, 0.04);
        }
        body .bindProduct-content {
            padding:0;
        }
        body .bindWrap-chooseProduct .bindProduct-content .bindProduct-content-title > span > i {
            color: #0888ff;
        }

        .bindProduct-content-tableWrap.wu-tableWrap .wu-table-one, .bindProduct-content-tableWrap.wu-tableWrap {
            min-width:unset;
        }
        .bindWrap-chooseProduct .clooseSkuWrap-showSku-main > li {
            word-break: break-word;
        }

        .bindWrap-chooseProduct .selectWrap.wu-select-skin.wu-form-mid .selectMore-choose > li:first-child {
            max-width: 65%;
        }
        .bindWrap-chooseProduct .selectWrap.wu-select-skin .selectMore-choose > li:nth-child(2) {
            padding: 0;
            font-size:12px;
        }
        .bindWrap-chooseProduct .selectMore-choose-title {
            height: 26px;
        }
        .bindWrap-chooseProduct.bindWrap input[type=text], .bindWrap select {
            height:32px!important;
        }
    </style>
}
@Styles.Render("~/css/skubind")


<div class="bindWrap bindWrap-chooseProduct">
    <form class="layui-form">
        <div class="layui-inline mysearch-partOne layui-form-left">
            <div id="selectShopId" class="ShopId selectWrap wu-form-mid wu-select-skin" style="width:160px;"></div>
            <div id="selectSupplierId" class="Supplier selectWrap wu-form-mid wu-select-skin" name="Supplier" style="width:160px;"></div>
            <div id="selectAgentId" class="Agent selectWrap wu-form-mid wu-select-skin" name="Agent" style="width:160px;"></div>
            <div class="layui-input-inline wu-inputWrap wu-form-mid" style="width:160px;">
                <input id="BindSku-ProductName-input" type="text" class="layui-input wu-input" placeholder="商品名称">
                <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
            </div>
            <div class="layui-input-inline wu-inputWrap wu-form-mid" style="width:160px;">
                <input id="BindSku-ProductShortTitle-input" type="text" class="layui-input wu-input" placeholder="商品简称">
                <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
            </div>
            <div class="layui-input-inline wu-inputWrap wu-form-mid" style="width:160px;">
                <input id="BindSku-SkuName-input" type="text" class="layui-input wu-input" placeholder="规格名称">
                <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
            </div>
            <div class="layui-input-inline wu-inputWrap wu-form-mid" style="width:160px;">
                <input id="BindSku-SkuShortTitle-input" type="text" class="layui-input wu-input" placeholder="规格简称">
                <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
            </div>
            <div class="layui-input-inline wu-inputWrap wu-form-mid" style="width:160px;">
                <input id="BindSku-SkuCargoNumber-input" type="text" class="layui-input wu-input" placeholder="SKU编码">
                <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
            </div>
        </div>
        <div class="layui-inline  layui-form-right">
            <button type="button" onclick="offlineProductModule.SearchProducts()" class="wu-btn wu-btn-mid wu-primary wu-two wu-mR8">查询</button>
            <button type="button" onclick="offlineProductModule.ResetProducts()" class="wu-btn wu-btn-mid wu-primary">重置</button>
        </div>
    </form>
    <div class="bindProduct-content">
        <div class="bindProduct-content-left">
            <div class="bindProduct-content-title"><span>商品共有<i id="allProuctMun">0</i>条</span></div>
            <div class="clooseSkuWrap">

                <div class="bindProduct-content-tableWrap mCustomScrollbar wu-tableWrap" style="width:330px;height:380px;">
                    <table class="stockup_table_content wu-table-one" style="height: 380px;">
                        <thead>
                            <tr>
                                @*<th style="width:20px;text-align: center;"><input type="checkbox" id="allCheckProduct" onclick="offlineProductModule.allCheck(this)"></th>*@
                                <th style="width:260px;">商品信息</th>
                            </tr>
                        </thead>
                        <tbody id="table_tbody_productList">
                            <tr id="tableNoDataShow">
                                <td colspan="15" class="tdNodata"><div class="tableNoDataShow">
                                    <img src="/Content/images/noData-icon.png" /><span class="tableNoDataShow-title">暂无数据！</span></div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="clooseSkuWrap-showSku wu-6radius">                 
                    <ul class="clooseSkuWrap-showSku-main wu-checkboxWrap" id="clooseSkuWrap_showSku_main">
                        <li>
                            <div class="clooseSkuWrap-showSku-warn">
                                <i class="iconfont icon-xiayi"></i>
                                <span>选择左侧商品</span>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div style="margin-bottom: 0;" class="layui-myPage" id="paging2"></div>
        </div>



        <div class="bindProduct-content-right">
            <div class="bindProduct-content-title">
                <span>已选择<i id="checkProductMun">0</i>条</span>
                <span class="dColor hover" onclick="offlineProductModule.clearCheckProduct()">全部清空</span>
            </div>
            <div class="bindProduct-content-tableWrap mCustomScrollbar wu-tableWrap" style="width:420px;">
                <table class="stockup_table_content wu-table-one">
                    <thead>
                        <tr>
                            <th style="width:260px;">商品</th>
                            <th style="width:150px;">规格/编码</th>
                        </tr>
                    </thead>
                    <tbody id="checkproductLists">
                        <tr id="tableNoDataShow">
                            <td colspan="15" class="tdNodata">
                                <div class="tableNoDataShow">
                                    <img src="/Content/images/noData-icon.png" /><span class="tableNoDataShow-title">暂无数据！</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div class="footer">
    <span class="wu-btn wu-btn-mid wu-mR8" onclick="offlineProductModule.saveBindProdcu()">确定</span>
    <span class="wu-btn wu-btn-mid wu-primary" onclick="offlineProductModule.initBindProdcutHide()">取消</span>
</div>


<!-- 关联店铺商品列表模样 -->
<script id="productList_data" type="text/x-jsrender">
    <tr onclick="offlineProductModule.checkLeftOfflineProduct.bind(this)('{{:Id}}')">
        <td>
            <div class="productShow">
                {{if ImageUrl}}
                <img src="{{:ImageUrl}}">
                {{else}}
                <img src="/Content/images/nopic.gif">
                {{/if}}
                <ul>
                    <li>
                        <span class="wu-pintaiIcon wu-small {{:PlatformType}}" title="{{:~getPlatformTitle(PlatformType)}}"></span>
                        <span>{{:AgentName}}</span>
                    </li>
                    {{if Subject}}
                    <li style="color:#04385d" title="{{:Subject}}">{{:Subject}}</li>
                    {{/if}}
                </ul>
            </div>
        </td>
        @*<td>
                <ul>
                    {{for Skus}}
                        <li>
                            <label>
                                <span style="text-align: center;"><input data-productCode="{{:ProductCode}}" data-skuCode="{{:SkuCode}}" class="product-chx" type="checkbox"></span>
                                <span class="spgg" title="规格颜色">{{:Name}}/{{:CargoNumber}}</span>
                            </label>
                        </li>
                    {{/for}}
                </ul>
            </td>*@
    </tr>
</script>

<!-- 关联店铺商品 选中商品表模样 -->
<script id="checkproductList_data" type="text/x-jsrender">
    <tr>
        <td>
            <div class="productShow">
                {{if ImageUrl}}
                <img src="{{:ImageUrl}}">
                {{else}}
                <img src="/Content/images/nopic.gif">
                {{/if}}
                <ul>
                    <li>
                        <span class="wu-pintaiIcon wu-small {{:PlatformType}}" title="{{:~getPlatformTitle(PlatformType)}}"></span>
                        <span>{{:AgentName}}</span>
                    </li>
                    <li style="color:#04385d" title="{{:Subject}}">{{:Subject}}</li>
                </ul>
            </div>
        </td>
        <td>
            <ul class="checkproductLists-ul">
                {{for Skus}}
                <li>
                    <span class="spgg" title="规格颜色">{{:Name}}/{{:CargoNumber}}</span>
                    <span class="tColor hover" onclick="offlineProductModule.delSelectProcut.bind(this)('{{:ProductCode}}','{{:SkuCode}}')">删除</span>
                </li>
                {{/for}}
            </ul>
        </td>
    </tr>
</script>

<!-- 关联店铺商品 选中商品表模样 -->
<script id="checkLeftProductList_data_jsrender" type="text/x-jsrender">
    {{if Skus.length==0}}   
        <li>
            <div class="clooseSkuWrap-showSku-warn">
                <i class="iconfont icon-xiayi"></i>
                <span>选择左侧商品</span>
            </div>
        </li>
    {{else}}
        <li class="checkLeftProductList-sku-title">规格/编码</li>
        {{for Skus}}
        <li>
            <label>
                <span style="text-align: center;"><input data-productCode="{{:ProductCode}}" data-skuCode="{{:SkuCode}}" class="product-chx" type="checkbox"></span>
                <span class="spgg" title="规格颜色">{{:Name}}/{{:CargoNumber}}</span>
            </label>
        </li>
        {{/for}}
    {{/if}}
</script>


<script>
    var isIframePage = true; //是否是iframe页面
    var Shops = @(Html.Raw(ViewBag.Shops ?? "")) || [];
    var ShopsEffect =@(Html.Raw(ViewBag.ShopsEffect ?? "")) || [];
    var PlatformTypes = @(Html.Raw(ViewBag.PlatformTypes ?? "")) || [];
    var Agents = @(Html.Raw(ViewBag.Agents ?? "[]"));
    var Suppliers = @(Html.Raw(ViewBag.Suppliers ?? "")) || [];
    $.views.helpers({
        getPlatformTitle: function (platform) {
            return showPlatformTitle(platform);//获取中文平台名称
        },
    });
</script>
@Scripts.Render("~/bundles/offlineProduct")

