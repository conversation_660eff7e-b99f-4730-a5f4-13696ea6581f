@using DianGuanJiaApp.Utility;
@using DianGuanJiaApp.Utility.Extension;
@using DianGuanJiaApp.Data.Entity;
@{
    var platformType = DianGuanJiaApp.Services.SiteContext.Current?.CurrentLoginShop?.PlatformType?.ToString();
    //var fenDanSystemNavs = ViewBag.FenDanSystemNav as List<FenDanSystemNav>;
    //var navQuickEntry = ViewBag.NavQuickEntry as List<FenDanSystemNav>;
    //var navQuickEntrySort = ViewBag.NavQuickEntrySort as List<int>;
    var isNewCorp = false;// ViewBag.IsNewCorp;   //是否新主体过审
                          //var currenUserModel = DianGuanJiaApp.Services.SiteContext.Current.CurrenUserModel;
    var show1688Class = "";
    var isShow1688Menu = (bool)(ViewBag.IsShow1688Menu ?? false);//是否显示1688菜单
    if (isShow1688Menu == true)
    {
        show1688Class = "showAliClass";
    }

    var isShowCrossBorder = (bool)(ViewBag.IsShowCrossBorder ?? false);//是否跨境
    var qqLink = "https://wpa1.qq.com/biYkihFL?_type=wpa&qidian=true";//联系客服的链接
    if (isShowCrossBorder)
    {
        qqLink = "https://wpa1.qq.com/L18kefQo?_type=wpa&qidian=true";
    }
}

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit" />
    <meta name="force-rendering" content="webkit" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <meta name="robots" content="noindex,nofollow" />
    <meta name="referrer" content="no-referrer" />
    <meta name="version" content="v20221213" />
    <title>
        @if (ViewBag.ShopName != null)
        {
            @(ViewBag.Title + "-" + ViewBag.ShopName)
        }
        else
        {
            @ViewBag.Title
        }
    </title>
    @Styles.Render("~/css/layout")
    <link href="~/Content/css/newLayout.css?v=@ViewBag.SystemVersion" rel="stylesheet" />
    <link href="~/Content/css/concatLayout.css?v=@ViewBag.SystemVersion" rel="stylesheet" />

    <link rel="stylesheet" href="~/Content/css/editPswdLayout.css?v=@ViewBag.SystemVersion">
    <link rel="stylesheet" href="~/Content/css/abnormalProduct/abnormal-product-content.css?v=@ViewBag.SystemVersion">
    @{
        var menuId = @ViewBag.MenuId;
        var menuActive = @ViewBag.MenuActive;

        <script>
            var menuId = '@menuId';
            var menuActive = '@menuActive';
            var isOldNavStyle = true;
            @*var GobalModuleCurrUser = @Html.Raw(currenUserModel.ToJson());*@
        </script>
    }
    @{
        if (menuId == "OrderPrint")
        {

        }
        var iframeClass = "";
        if (ViewBag.IsIFrame == true)
        {
            iframeClass = "layui-layout-iframe-body";
        }
    }
    @RenderSection("Header", false)

    <script type="text/javascript">
        function Logout() {
            commonModule.ajax({
                url: "/Auth/Logout",
                success: function () {
                    layer.open({
                        title: "提示",
                        content: "<div style='font-size:16px;line-height:30px;'>已退出登陆，您可以关闭当前页面或重新登陆</div>",
                        skin: 'wu-dailog',
                        btn: ['重新登陆', '关闭窗口'],
                        yes: function () {
                            window.location.href = "/";
                        },
                        btn2: function () {
                            layer.closeAll();
                            //window.location.href="/Auth/Empty";
                            commonModule.closeWindows();
                        },
                    });
                }
            });
        }

        //临时处理，审核用 微信视频号新应用 ，页面所有跳"www.dgjapp.com" 都处理成 跳回当前页面。
        window.addEventListener("click", function (event) {
            var isNewCorp = eval('@isNewCorp'.toLowerCase()) || false;
            if (isNewCorp) {
                var target = event.target || event.srcElement;
                if (target.nodeName.toLocaleLowerCase() == "a") {    //判断是否匹配目标元素
                    var href = target.getAttribute("href");
                    var targetWay = target.getAttribute("target");
                    if (href != null && href.indexOf("www.dgjapp.com") > 0) {
                        window.open("#", "_blank");
                    }
                }
            }
        });
        var platformType = "@DianGuanJiaApp.Services.SiteContext.Current.CurrentLoginShop.PlatformType";
        document.parentLocationOrigin = '@DianGuanJiaApp.Utility.CustomerConfig.AlibabaFenFaSystemUrl';
    </script>
    <!--1.在引入阿里云验证码JS脚本的位置之前，或者在html的head标签最前的位置添加一个script脚本，里面保存一个含有region和prefix参数的全局变量AliyunCaptchaConfig即可-->
    <script>
        window.AliyunCaptchaConfig = {
            // 必填，验证码示例所属地区，支持中国内地（cn）、新加坡（sgp）
            region: "cn",
            // 必填，身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
            prefix: "iu8ogo",
        };
    </script>
    <!--2.集成主JS-->
    <script type="text/javascript" src="https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js"></script>
</head>

<script src="~/Scripts/jquery-1.12.4.min.js?v=@ViewBag.SystemVersion"></script>
@Scripts.Render("~/bundles/common")
<script src="~/Scripts/layer/layer.js?v=@ViewBag.SystemVersion"></script>
@Scripts.Render("~/bundles/layout")



@*<script src="~/Scripts/layer/layer.js"></script>
    <script src="~/Scripts/laypage/laypage.js"></script>*@
@*class="@(DianGuanJiaApp.Services.SiteContext.Current.CurrentLoginShop.PlatformType == "Offline" ? "weishang-body" : "") <EMAIL>"*@
<body class="concatLayoutWrap layui-layout-body @iframeClass @ViewBag.PageVersionClassName  @show1688Class">
    <div id="waterproof_wall_verify_code"></div>
    <div id="waterproof_wall_verify_element"></div>
    @{ Html.RenderPartial("~/Views/GeneralizeIndex/demandContent.cshtml"); }

    @{
        //链路追踪，必须放到body的第一行
        var openTelemetryWebConfig = new DianGuanJiaApp.Services.CommonSettingService().GetOpenTelemetryWebConfig();
        if (openTelemetryWebConfig != null && openTelemetryWebConfig.IsOpenWeb && !string.IsNullOrWhiteSpace(openTelemetryWebConfig.Pid))
        {
            <input type="hidden" id="HID_OpenTelemetryWebPid" value="@openTelemetryWebConfig.Pid">
            <input type="hidden" id="HID_FxUserId" value="@DianGuanJiaApp.Services.SiteContext.CurrentNoThrow.CurrentFxUserId">
            <input type="hidden" id="HID_OpenTelemetryWebSampleRate" value="@openTelemetryWebConfig.SampleRate">
            <input type="hidden" id="HID_OpenTelemetryWebFilterPage" value="@(openTelemetryWebConfig.FilterPage)">
            <input type="hidden" id="HID_Environment" value="@(openTelemetryWebConfig.Environment)">
            <input type="hidden" id="HID_Release" value="@(openTelemetryWebConfig.Release)">

            @*@Scripts.Render("~/bundles/LoadOpenTelemetry")
            <script type="text/javascript" src="https://sdk.rum.aliyuncs.com/v1/bl.js" crossorigin></script>*@
        }
        
        //获取监控配置
        var monitorConfig = new DianGuanJiaApp.Services.CommonSettingService().GetMonitoringConfig();
        var isMonitor = monitorConfig != null && monitorConfig.IsEnabled;
        if (isMonitor)
        {
            <script type="text/javascript">
                // 直接将配置注入到全局变量
                window.monitorConfig = @Html.Raw(Json.Encode(monitorConfig));
            </script>
        }
        
        if (openTelemetryWebConfig != null)
        {
            <input type="hidden" id="HID_OpenTelemetryWebPid" value="@openTelemetryWebConfig.Pid">
            <input type="hidden" id="HID_FxUserId" value="@DianGuanJiaApp.Services.SiteContext.CurrentNoThrow.CurrentFxUserId">
            <input type="hidden" id="HID_OpenTelemetryWebSampleRate" value="@openTelemetryWebConfig.SampleRate">
            <input type="hidden" id="HID_OpenTelemetryWebFilterPage" value="@(openTelemetryWebConfig.FilterPage)">
            <input type="hidden" id="HID_Environment" value="@(openTelemetryWebConfig.Environment)">
            <input type="hidden" id="HID_Release" value="@(openTelemetryWebConfig.Release)">

            if (isMonitor)
            {
                @Scripts.Render("~/bundles/ArmsTool") 
                <script type="text/javascript">
                    ArmsTool.init(function () {
                        ArmsTool.setUsername('@DianGuanJiaApp.Services.SiteContext.Current.CurrentFxUser.UserFlag');
                    });
                </script>  
            }
        
        }
        else if (isMonitor)
        {
            @Scripts.Render("~/bundles/ArmsTool")
            <script type="text/javascript">
                try {
                    ArmsTool.init(function () {
                        ArmsTool.setUser(@DianGuanJiaApp.Services.SiteContext.CurrentNoThrow.CurrentFxUserId);
                        ArmsTool.setUsername('@DianGuanJiaApp.Services.SiteContext.Current.CurrentFxUser.UserFlag');
                    });
                  
                } catch (e) {
                    console.error("ARMS监控工具初始化失败:", e);
                }
            </script>
        }

        <script>
            commonModule.userFlag = "@ViewBag.UserFlag"; // 判断是否纯铺货用户
            try {
                var IsShrinkLeftNav = localStorage.getItem('IsShrinkLeftNav') || null;
                if (IsShrinkLeftNav) {
                    var getIsShrinkLeftNav = JSON.parse(IsShrinkLeftNav);
                    if (getIsShrinkLeftNav.data) {
                        document.getElementsByTagName('body')[0].classList.add("wu-shrinkSky"); //收超导航
                        if(commonModule.userFlag && commonModule.userFlag.includes("only_listing")) {
                            document.getElementsByTagName('body')[0].classList.add("wu-shrinkSky-puhuo");
                        }
                    }
                }
            } catch (e) {}
        </script>
    }

    <div class="newtopBanner" id="newtopBanner" style="display:none;"></div>

    <div class="layui-layout layui-layout-admin newVersionsLeftNav">
        <div class="layui-header" id="top_layui_header">
            <input type="hidden" value="@ViewBag.Token" id="token_input" />
            <input type="hidden" value="@ViewBag.TraceId" id="traceid_input" />
            <input type="hidden" value="@ViewBag.DbName" id="dbname_input" />
            <input type="hidden" value="@Request["dbname"]" id="dbname_input" />
            <!-- 头部区域 -->
            <div class="newVersions-logo">
                @if (!isNewCorp)
                {
                    <img src="~/Content/images/logo2023-3-3.png">
                    @*<img src="~/Content/images/logo-2022-3-25.png">*@
                }
                else
                {
                    <img src="~/Content/images/logo2023-3-3-02.png">
                }
            </div>
            <ul class="layui-nav layui-layout-left" id="layadmin_flexible">
                <li class="layui-nav-item layadmin-flexible" id="layadmin_flexible" lay-unselect="" style="display:flex">
                    @*<i id="header_shanchu_ad" class="iconfont icon-shanchu header-shanchu" title="删除顶部广告"></i>*@
                    <a href="javascript:;" layadmin-event="flexible" title="侧边栏伸缩">
                        <i class="iconfont icon-layui-icon-shrink-right header-flexible zk" id="layui_icon_left" onclick="$$.showOrHideNavleft(this)"></i>
                    </a>
                </li>
            </ul>

            <div class="banner-operate wu-flex wu-yCenter wu-betteen">
                <ul>
                    <li class="layui-nav-item n-typeOne">
                        @{ Html.RenderPartial("~/Views/Common/comSearchOrder.cshtml"); }
                    </li>
                </ul>
                <ul class="layui-nav layui-layout-right" id="header_right" lay-filter="layadmin-layout-right" style="display:flex;justify-content:flex-end;">
                    @*<li class="layui-nav-item">
            <div>
                <div class="shopPastWrap" style="display: none;">
                    <i class="shopPastWrap-num"></i>
                    <span>个店铺同步异常</span>
                    <i class="iconfont icon-down"></i>
                    <div class="shopPastWrapShow">
                        <form class="layui-form" action="/">
                            <span class="layui-form-title">提示设置：</span>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <input checked type="checkbox" name="IsSetWarnPublic" lay-skin="switch" lay-filter="switchTop01" lay-text="已开启|已关闭 ">
                                </div>
                            </div>
                            <div class="warn-tar">? <div class="warn-tar-title">关闭之后可重新前往:<br /> <a href="/System/DistributeSet?type=invalidShop">分发设置</a>- 失效店铺提醒 开启</div></div>
                        </form>
                        <ul class="shopPastWrap-main">
                            <li class="shopPastWrap-main-item shopPastWrap-main-last"></li>
                        </ul>
                    </div>
                </div>
            </div>
        </li>*@

                    <li class="layui-nav-item contact-wrap old-layui-nav-item">
                        <div class="flex-center" id="NoticeWrapShow">
                            <div class="notice-wrap-li mRight30" id="ShowAbnormalProductLi" style="display: none;">
                                <div class="abnormal-product-count" id="AbnormalUnreadCount">
                                    0
                                </div>
                                <span class="old-title" onclick="ViewAbnormalProductDialog()">待处理异常商品</span>
                                <div class="abnormal-product-tip" id="ShowAbnormalProductTip" style="display: none;">
                                    <div class="n-tooltip n-rightUp n-font5">
                                        <div class="flex-center">
                                            <img src="~/Content/images/abnormal-product-tip-icon.png" />
                                            <span class="title">异常商品提醒</span>
                                        </div>
                                        <div class="content">下游分销商品信息有变更，可能影响发货与结算的误判。</div>
                                        <div class="flex-end mTop16">
                                            <span class="btn mRight12" onclick="CloseAbnormalProductTip()">我已知悉</span>
                                            <span class="n-mButton" onclick="ViewAbnormalProductDialog()">立即查看</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @if (ViewBag.IsShowSiteMessage != null && ViewBag.IsShowSiteMessage)
                            {
                                @*<div id="SiteMessage" onclick="commonModule.notice()">
                        <span class="old-title flex-center"><i class="iconfont icon-lingdang" style="font-size:18px"></i>通知</span>
                        <span class="layui-badge notice-unreadcount" style="border-radius:8px;font-size:11px;position:relative;top:-5%;margin:0;display:none">10</span>
                    </div>*@
                                <div class="notice-wrap-li  mRight30 hide" id="SiteMessage" style="display: none;">
                                    <div class="abnormal-product-count notice-unreadcount">
                                        0
                                    </div>
                                    <span class="old-title flex-center" onclick="commonModule.notice()">
                                        <i class="iconfont icon-lingdang" style="font-size:18px"></i>通知
                                    </span>
                                </div>
                            }
                        </div>
                    </li>

                    @* <li class="layui-nav-item" style="cursor:pointer;" onclick="window.open(commonModule.rewriteUrl('/System/CreateUrl'));">
            <i class="iconfont icon-zhuomianfenxiang dColor"></i>
            <span class="header-nav-title">添加到桌面快捷方式</span>
        </li> *@

                    <li class="layui-nav-item contact-edmit" onclick="newDemandModule.showComplaint()">
                        @*<img src="~/Content/images/edit-kefu.gif" />*@
                        <i class="iconfont icon-a-mail1x"></i>
                        <span class="old-title">投诉</span>
                    </li>
                    <li class="layui-nav-item contact-wrap newcontact-wrap" id="contact_wrap">
                        <div class="newcontact-wrap-main">
                            <a href="@qqLink" style="padding: 0;" target="_blank">
                                @*<i class="layui-icon iconfont icon-kefu1 ke" style="font-size:17px;color: #0888ff!important;margin-right: 0;"></i>*@
                                <i class="iconfont icon-a-service1x" style="color: #0888ff"></i>
                                <span class="header-nav-title animateText">客服</span>
                            </a>
                        </div>
                    </li>
                    <li class="layui-nav-item contact-edmit" onclick="newDemandModule.showSuggest()">
                        @*<img src="~/Content/images/edit.49fa.gif" />*@
                        <i class="iconfont icon-a-edit-11x"></i>
                        <span class="old-title">反馈</span>
                    </li>

                    <li class="layui-nav-item contact-wrap" id="contact_wrap02">
                        <a href="https://www.dgjapp.com/newHelpsClassify.html?id=&columnId=1643102261066&type=helps" target="_blank" style="padding:0;display:flex;color:#000;">
                            <i class="layui-icon iconfont icon-jiaocheng1" style="font-size: 14px; transform: rotate(0deg);"></i>
                            <span class="header-nav-title">教程</span>
                        </a>
                    </li>
                    <li class="layui-nav-item contact-wrap" id="quickInvite">
                        <span class="inviteWrap" onclick="inviteDailog()">
                            <i class="iconfont icon-a-user-add1x1"></i>
                            <span class="inviteWrap-text">邀请</span>
                        </span>
                    </li>

                    <li class="level-explain layui-nav-item" id="level-explain" style="cursor:pointer;" title="点击查看详情" onclick="$('.LevelExplain a')[0].click();">
                    </li>

                    <li class="layui-nav-item" style="margin-right:8px;padding-left:0;">
                        <div class="sortWrap" id="logoShow">
                            <span id="userInfo" style="position: relative; margin-right: 16px;">
                                <span class="userInfo-logo">
                                    <i class="iconfont icon-a-user1x1"></i>
                                </span>
                                <s>@ViewBag.NickName</s>
                                <i class="iconfont icon-a-chevron-down1x" id="user-info-card-down-icon" style="position: absolute; margin-right:0;"></i>
                            </span>
                            <div id="userInfoCard" style="width: 240px; display: none; font-size: 14px; line-height: 20px; position: absolute; top: 48px; right: 0; background-color: #ffffff; border-radius: 8px; box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.04), 0px 8px 16px 0px rgba(0, 0, 0, 0.04), 0px 2px 4px 0px rgba(0, 0, 0, 0.08);"></div>
                        </div>
                    </li>
                </ul>

            </div>
        
        </div>

        <!-- 侧边菜单 -->
        <div class="layui-side layui-side-menu layui-bg-black" id="layui_side_left_new">
            <div class="layui-side-scroll" id="dgjLeftNav_wrap">
                <div class="layui-logo">
                    <span class="layui-logo-wrap">
                        @if (!isNewCorp)
                        {
                            <img id="newlogo_1" src="~/Content/images/logo2023-3-3.png">
                            @*<img id="newlogo_1" src="~/Content/images/logo-2022-3-25.png">*@
                            <img id="newlogo_2" src="~/Content/images/logo-2022-3-25-2.png" style="width:35px;height:35px;">
                        }
                        else
                        {
                            <img id="newlogo_1" src="~/Content/images/logo-2022-3-25-02.png" />
                        }
                    </span>
                    @*<a class="layui-logo-flexible" id="layui_logo_flexible" href="javascript:;" layadmin-event="flexible" title="侧边栏伸缩">
                            <i class="iconfont icon-layui-icon-shrink-right zk" id="layui_icon_left" onclick="$$.showOrHideNavleft(this)"></i>
                        </a>*@
                </div>
                <!--侧边菜单-->
                @*@foreach (var _nav in fenDanSystemNavs)
                    {
                        <span class="@_nav.CalssName">@_nav.Id</span>
                    }*@

                <div class="newChangeNav" id="newChangeNav">
                    <div class="newchangeNav-logoWrap">
                        <div class="newchangeNav-logoWrap-left">
                            <img src="~/Content/images/svg/logo-2025-6-25-01.jpg" style="width:144px;">
                        </div>
                        <div class="newchangeNav-logoWrap-right"><i class="iconfont icon-a-menu-unfold1x" onclick="changeLeftNavStyle()"></i></div>
                    </div>


                </div>

            </div>
        </div>
        <!-- 主体内容 -->
        <div class="layui-bodys" id="LAY_app_body">
            @RenderBody()
        </div>
    </div>

    @*新手指南*@
    <div class="noviceIntro-main" id="noviceIntroDailog">
        <span class="iconfont icon-jia-copy1" id="close_noviceIntro" onclick="closeLayoutNoviceIntro()"></span>
        <div class="noviceIntro-main-title">感谢使用@(isNewCorp? "分销代发系统" : "店管家分发系统")，请查阅新手攻略</div>
        <div class="noviceIntro-main-btn">
            <ul id="noviceIntro_nav">
                <li class="active"><i class="iconfont icon-gongyingshang"></i>我是厂家</li>
                <li><i class="iconfont icon-_fazhanfenxiaoshang"></i>我是商家</li>
            </ul>
        </div>
        <ul class="noviceIntro-main-content" id="noviceIntro_mySupplier">
            <li><a href="/Partner/MyAgent" target="_blank">① 邀请商家注册  添加商家账号 建立合作关系</a></li>
            <li><a href="/Product/Index?type=all" target="_blank">② 点击“商品管理”功能 了解商家指定代发的商品明细</a></li>
            <li><a href="/NewOrder/AllOrder" target="_blank">③ 点击“订单管理”功能，查看所有订单和代发订单</a></li>
            <li><a href="/TemplateSet" target="_blank">④ 点击“打单配置”，设置快递模板和授权电子面单</a></li>
            <li><a href="/NewOrder/WaitOrder" target="_blank">⑤ 点击“待打订单”，批量勾选订单开始打单发货</a></li>
        </ul>
        <ul class="noviceIntro-main-content" id="noviceIntro_myAgent" style="display:none">
            <li><a href="/Partner/Index" target="_blank">① 点击“我的店铺”，授权店铺信息</a></li>
            <li><a href="/Partner/MySupplier" target="_blank">② 点击“我的厂家”，邀请厂家注册，建立合作关系</a></li>
            <li><a href="/Product/Index?type=all" target="_blank">③ 点击“商品管理”，将指定商品绑定给代发的厂家</a></li>
            <li><a href="/NewOrder/AllOrder" target="_blank">④ 点击“订单管理-全部订单”，查看全部订单信息和物流信息</a></li>
            <li><a href="/TemplateSet" target="_blank">⑤ 对于自营供货订单，点击“打单配置”完成相关信息设置</a></li>
            <li><a href="/NewOrder/WaitOrder" target="_blank">⑥ 点击“待打订单”，勾选订单打印并发货</a></li>
        </ul>
        <div class="noviceIntro-main-video" id="noviceIntro_main_video"><a href="//v2.sohu.com/v/url/649873862_120542292.MP4" target="_blank">我已了解，查看新手教程视频<span class="iconfont icon-bofang"></span></a></div>
        <img class="noviceIntro-img" src="~/Content/images/noviceIntro-img.png" />
    </div>

    @*续费02*@
    @*<div class="Renew-dialog" id="Renew_dialog_02">
            <div class="Renew-dialog-title clearfix">
                <div class="Renew-dialog-titleLeft fonts">
                    <div style="margin-bottom:15px;">亲，为避免您的订购失误，</div>
                    <div>请确认<i class="activeFont">您订购的店铺与当前电商后台登录店铺</i>名称一致！</div>
                </div>
                <div class="Renew-dialog-titleRight fonts">
                    <div style="margin-bottom:15px;">当前订购店铺：<i class="activeFont renewShopName"></i></div>
                    <div>店铺不一致时，需去电商后台切换到您需要续费的店铺</div>
                </div>
            </div>
            <div class="Renew-dialog-content" id="Renew_dialog_content_02">
                <div class="Renew-dialog-content-name">
                    <span class="renewShopName"></span>
                </div>
            </div>
            <div class="Renew-dialog-btn">
                <a class="index_btn1" id="renew_orderUrl_02" target="_blank">确认无误，前往订购</a>
            </div>
        </div>*@

    <div class="newRenew-dialog" id="newRenew_dialog_03">
        <div class="newRenew-header">
            <div class="newRenew-header-up" style="display:none">
                <span>当前订购的店铺：</span>
                <span class="newRenew-header-shopName "></span>
            </div>
            <div class="newRenew-header-down">
                请您前往服务市场续费时，请查看服务市场当前登录店铺，店铺不一致时，请切换电商后台登录店铺再续费。
            </div>
        </div>
        <div class="newRenew-main">
            <div class="newRenew-name newRenewamItemL"></div>
            <div class="newRenew-platform newRenewamItemL newRenew-platform-name"></div>
            <div class="newRenew-icon">订购时，请确认服务市场登录店铺</div>
        </div>
        <div class="newRenew-footer">
            <a id="renew_orderUrl_03" href="" target="_blank">前往订购</a>
        </div>
    </div>
    <!-- 首次登录修改密码弹窗 -->
    <div class="editPswd-commonDailog" id="showFirstEditPswdDailog">
        <div class="editPswd-commonDailog-title">
            <i class="iconfont icon-a-error-circle-filled1x"></i>
            <span>您的账号密码为默认密码，为确保账号安全，请修改密码。</span>
        </div>
        <div class="editPswd-commonDailog-main">
            <div class="editPswd-commonDailog-main-label">新密码</div>
            <div style="position: relative;">
                <input class="editPswd-commonDailog-main-input" id="onInputCode121" type="password" placeholder="设置8-20位，由数字、字母或特殊符号组成">
                <i class="iconfont icon-a-browse-off1x editPswd-commonDailog-main-right" onclick="onLookFirstEditPswd1(1)" id="iconChevronDown121"></i>
                <input class="editPswd-commonDailog-main-input" id="onInputCode1211" style="display: none;" type="text" placeholder="设置8-20位，由数字、字母或特殊符号组成">
                <i class="iconfont icon-a-browse1x editPswd-commonDailog-main-right" style="display: none;" onclick="onLookFirstEditPswd11(0)" id="iconChevronDown1211"></i>
            </div>
            <div class="editPswd-commonDailog-main-label">确认新密码</div>
            <div style="position: relative;">
                <input class="editPswd-commonDailog-main-input" id="onInputCode122" type="password" placeholder="设置8-20位，由数字、字母或特殊符号组成">
                <i class="iconfont icon-a-browse-off1x editPswd-commonDailog-main-right" onclick="onLookFirstEditPswd2(1)" id="iconChevronDown111"></i>
                <input class="editPswd-commonDailog-main-input" id="onInputCode1222" style="display: none;" type="text" placeholder="设置8-20位，由数字、字母或特殊符号组成">
                <i class="iconfont icon-a-browse1x editPswd-commonDailog-main-right" style="display: none;" onclick="onLookFirstEditPswd22(0)" id="iconChevronDown1222"></i>
            </div>
        </div>
        <div class="editPswd-commonDailog-footer">
            <div class="cancel-first-editPswd-btn" onclick="cancelFirstEditPswd()">暂不修改</div>
            <div class="confirm-first-editPswd-btn" onclick="confirmFirstEditPswd()">确定修改</div>
        </div>
    </div>

    <!-- 修改密码弹窗 -->
    <div class="editPswd-commonDailog" id="showEditPswdDailog">
        <div class="editPswd-commonDailog-title">
            <i class="iconfont icon-a-error-circle-filled1x"></i>
            <span>修改后，原密码将无法登录系统，需使用新密码登录。</span>
        </div>
        <div class="editPswd-commonDailog-main">
            <div class="editPswd-commonDailog-main-label">手机号</div>
            <div style="position: relative;">
                <input class="editPswd-commonDailog-main-input-disabled" disabled id="onInputPhone12" type="text" placeholder="请输入">
            </div>
            <div class="editPswd-commonDailog-main-label">验证码</div>
            <div style="position: relative;">
                <input class="editPswd-commonDailog-main-input" id="onInputCode13" type="text" placeholder="请输入">
                <span class="editPswd-commonDailog-main-right" style="color: #0888FF; cursor: pointer;" id="obtainCodeNum12" onclick="onGetObtainPhoneCode()">获取验证码</span>
                <span class="editPswd-commonDailog-main-right" style="display: none; color: #b3b3b3" id="countdownBox12">
                    重新发送（<span id="countdown12">59</span>）
                </span>
                <div class="ipnut-code-red-tips">验证码输入错误</div>
            </div>
            <div class="editPswd-commonDailog-main-label">新密码</div>
            <div style="position: relative;">
                <input class="editPswd-commonDailog-main-input" id="onInputCode131" type="password" placeholder="设置6-20位，由数字和字母组成">
                <i class="iconfont icon-a-browse-off1x editPswd-commonDailog-main-right" onclick="onLookFirstEditPswd12(1)" id="iconChevronDown131"></i>
                <input class="editPswd-commonDailog-main-input" id="onInputCode1311" style="display: none;" type="text" placeholder="设置6-20位，由数字和字母组成">
                <i class="iconfont icon-a-browse1x editPswd-commonDailog-main-right" style="display: none;" onclick="onLookFirstEditPswd121(0)" id="iconChevronDown1311"></i>
            </div>
            <div class="editPswd-commonDailog-main-label">确认新密码</div>
            <div style="position: relative;">
                <input class="editPswd-commonDailog-main-input" id="onInputCode132" type="password" placeholder="设置6-20位，由数字和字母组成">
                <i class="iconfont icon-a-browse-off1x editPswd-commonDailog-main-right" onclick="onLookFirstEditPswd21(1)" id="iconChevronDown132"></i>
                <input class="editPswd-commonDailog-main-input" id="onInputCode1333" style="display: none;" type="text" placeholder="设置6-20位，由数字和字母组成">
                <i class="iconfont icon-a-browse1x editPswd-commonDailog-main-right" style="display: none;" onclick="onLookFirstEditPswd221(0)" id="iconChevronDown1333"></i>
            </div>
        </div>
        <div class="editPswd-commonDailog-footer">
            <div class="cancel-first-editPswd-btn" onclick="cancelFirstEditPswd()">暂不修改</div>
            <div class="confirm-first-editPswd-btn" onclick="confirmEditPswd()">确定修改</div>
        </div>
    </div>

    @*联系客服二维码*@
    <div class="contactDailog" id="commonContactDailog" style="display: none;">
        <div class="contactDailog-content">
            <img src="https://www.dgjapp.com/public/images/xianyinERWEIMA.png" alt="">
            <span>微信扫一扫，联系客服</span>
            <i class="closeIcon" onclick="commonModule.contactOpenOrClose(false)"></i>
        </div>
    </div>

    <div id="fazhanfenxiaoshang" class="inviteDailogWrap">
        <ul class="inviteDailog">
            <li>
                <div class="warn-wrap" style="margin-bottom:10px;">对方通过您的二维码注册后，自动建立合作关系，请确认对方身份</div>
            </li>
            <li class="inviteDailog-change" style="justify-content:space-between;display: flex;margin-bottom:5px;padding:0 40px;position:relative;">
                <label id="inviterQrCode_mySupplierWrap"><input type="radio" id="inviterQrCode_mySupplier" name="inviterQrCodeName" value="MySupplier" onchange="inviterQrCodeRadio()" /><i class="iconfont icon-gongchang"></i>邀请厂家</label>
                <span style="color: #ff3c00; font-size: 12px; position: absolute; top: 20px;">供应商/厂家/工厂/仓库/云仓</span>
                <label id="inviterQrCode_myAgentWrap"><input type="radio" id="inviterQrCode_myAgent" name="inviterQrCodeName" value="MyAgent" onchange="inviterQrCodeRadio()" /><i style="top:-1px;" class="iconfont icon-_fazhanfenxiaoshang"></i>邀请商家</label>
            </li>

            <li class="invitesupplier-main" style="justify-content: center;display: flex;margin-top:35px">
                <div class="layui-tab-content invitesupplier-content">

                    <div class="invitesupplier-main-middle" style="display:none" id="layoutSupplierDivId">
                        <div style="width:150px; height:150px;border-radius: 2px;" id="layoutSupplierQrcodeId"></div>
                        <div class="main-middle-right">
                            <span class="wu-btn wu-btn-mid" style="background-color:#0888ff" onclick="onloadQRC('#layoutSupplierQrcodeId')">下载二维码</span>
                            <span class="iconfont icon-tongbu" style="color:#0888ff" onclick="refreshQrCode()"></span>
                        </div>
                    </div>
                    <div class="invitesupplier-main-middle" style="display:none" id="layoutAgentDivId">
                        <div style="width:150px; height:150px;border-radius: 2px;" id="layoutAgentQrcodeId"></div>
                        <div class="main-middle-right">
                            <span class="wu-btn wu-btn-mid" style="background-color:#0888ff" onclick="onloadQRC('#layoutAgentQrcodeId')">下载二维码</span>
                            <span class="iconfont icon-tongbu" style="color:#0888ff" onclick="refreshQrCode()"></span>

                        </div>
                    </div>
                </div>
            </li>
            <li style="line-height:18px;padding-right: 15px;color:#f59c1a;display:none;flex-direction:column;align-items:flex-start" id="layoutSupplierQrcodeWarmPromptId"><div style="display:flex;flex-direction:row;">注意:该二维码24小时内有效，有效期至<span style="font-size:12px;font-weight:700;color: #ff3c00;margin-left: 3px;" id="layoutSupplierQrcodeExceedTimeId"></span></div><div>扫描注册后自动绑定合作关系，请勿随意传播该二维码。</div></li>
            <li style="line-height:18px;padding-right: 15px;color:#f59c1a;display:none;flex-direction:column;align-items:flex-start" id="layoutAgentQrcodeWarmPromptId"><div style="display:flex;flex-direction:row;">注意:该二维码24小时内有效，有效期至<span style="font-size:12px;font-weight:700;color: #ff3c00;margin-left: 3px;" id="layoutAgentQrcodeExceedTimeId"></span></div><div>扫描注册后自动绑定合作关系，请勿随意传播该二维码。</div></li>
        </ul>
    </div>
    @*快捷入口弹窗*@
    <div class="quickNav-dailog" id="quickNav-dailog">
        <div class="quickNav-main">
            @*<div class="quickNav-main-title">请勾选常用功能入口</div>*@
            <ul class="quickNav-main-content" id="quickNav_content"></ul>
        </div>
        <div class="quickNav-main" style="padding-bottom: 0; border: 1px solid rgba(0, 0, 0, 0.1); border-radius: 6px; ">
            <div class="quickNav-main-title" style="font-size: 14px;">常用功能排序<s style="color: rgba(0, 0, 0, 0.4); font-size: 14px;margin-left:8px;">可拖拽标签调整显示顺序</s></div>
            <ul class="quickNav-main-changede" id="quickNav_changede"></ul>
        </div>
    </div>

    @*意见及建议----------------*@
    <div class="edmitDemandDailog" id="edmitDemandDailog" style="display:none;">
        <div class="edmitDemandDailog-title">
            <span>请您留言您的意见及建议，产品部门会认真确认您的需求，若需求采纳，会优先为您配置功能使用</span>
        </div>
        <ul class="edmitDemandDailog-main">
            <li class="edmitDemandDailog-main-item">
                <div class="edmitDemandDailog-main-item-title"><span class="req">*</span>1∶您的联系方式/注册账号(为了后续给您优先配置功能)</div>
                <input id="demand_tel" type="text" name="name" value="" placeholder="请输入您的联系方式" />
            </li>
            <li class="edmitDemandDailog-main-item">
                <div class="edmitDemandDailog-main-item-title">2∶您需要的功能是为了解决什么业务问题</div>
                <textArea id="demand_content" placeholder="请输入您需要的功能是为了解决什么业务问题"></textArea>
            </li>
            <li class="edmitDemandDailog-main-item">
                <div class="edmitDemandDailog-main-item-title">3∶您的投诉与对产品建议（如遇任何导致您生气的事情，您可以留言在下方)</div>
                <ul class="edmitDemandDailog-main-item-ul" id="edmitDemandDailog_type">
                    <li>需求类</li>
                    <li>投诉类</li>
                    <li>建议类</li>
                </ul>
                <textArea id="demand_submitSuggest" placeholder="请输入您的投诉与对产品建议"></textArea>
            </li>

            <li class="edmitDemandDailog-main-item" style="display:flex;justify-content:center;">
                <span class="layui-btn layui-btn-normal layui-btn35" onclick="commonModule.submitSuggest()">提交留言</span>
            </li>
        </ul>
    </div>
    @*消息通知---------------*@
    <div class="noticePopUp" id="noticePopUp">
        <div class="noticePopUp-shade"></div>
        <div class="noticePopUp-container">
            <div class="noticePopUp-container-title">
                通知中心
            </div>
            <div class="layui-layer-setwin">
                <a class="layui-layer-ico layui-layer-close layui-layer-close1" href="javascript:;" onclick="commonModule.noticePopUp()"></a>
            </div>
            <div class="noticePopUp-main"></div>

        </div>
    </div>

    @{
        var currShopModel = DianGuanJiaApp.Services.SiteContext.Current.CurrentLoginShopModel;
        var currShop = currShopModel.ToJson();
    }

    @{
        var shopId = currShopModel.GetType().GetProperty("ShopId").GetValue(currShopModel);
        if (!shopId.ToString().IsMobile())
        {
            Html.RenderPartial("~/Views/Partner/registerIntoContent.cshtml");
        }
        Html.RenderPartial("~/Views/Shared/contact.cshtml"); //联系客服组件
    }


</body>
<script src="~/Scripts/layui/layui.all.js"></script>

@RenderSection("scripts", required: false)
@RenderSection("OutBodyHtml", required: false)
</html>

@{
    //var menuId = @ViewBag.MenuId;
    <script>
        commonModule.FxUserAddres = @(Html.Raw(ViewBag.FxUserAddres));
        var platformType = "@platformType";
        commonModule.PlatformType= platformType;
        commonModule.ShopName = '@ViewBag.ShopName';
        commonModule.Token = '@Request["token"]';
        commonModule.DbName = '@Request["dbname"]';
        commonModule.Shops = @ViewBag.AllShopsModel;
        commonModule.CurrShop = @Html.Raw(currShop);
        commonModule.ServerNowDate = '@DateTime.Now.ToString("yyyy-MM-dd 23:59:59")';
        commonModule.ServerNowTime = '@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")';
        commonModule.SystemConfig = { "AutoSendSetVal": 1 };
        //var AgentIsSalePrices = @(Html.Raw(ViewBag.AgentIsSalePrices ?? "")) || []; //我的商家是否显示销售价配置集合
        //console.log("我的商家是否显示销售价配置集合==>", AgentIsSalePrices)
        commonModule.IsNewCorp = eval('@isNewCorp'.toLowerCase()) || false; //是否新主体过审

        commonModule.FenDanSystemNavs = @(Html.Raw(ViewBag.FenDanSystemNavs ?? "")) || []; //菜单配置集合
        commonModule.NavQuickEntry = @(Html.Raw(ViewBag.NavQuickEntry ?? "")) || []; //菜单快捷入口
        commonModule.NavQuickEntrySort = @(Html.Raw(ViewBag.NavQuickEntrySort ?? "")) || []; //菜单快捷入口排序
        console.log("NavQuickEntry", commonModule.NavQuickEntry);
        commonModule.IsShowCrossBorder = '@ViewBag.IsShowCrossBorder' == 'True';//用户是否开启了跨境功能
        commonModule.IsCrossBorderSite = '@ViewBag.IsCrossBorderSite' == 'True';//当前站点是否是跨境站点
        //commonModule.FxPermission = @(Html.Raw(ViewBag.FxPermission ?? "")) || {};// 系统权限FxPermission 非全部系统权限
        //console.log(commonModule.FxPermission);

           var topOrDailogADLocalStorage = commonModule.getStorage("topOrDailogADLocalStorage");
            if (topOrDailogADLocalStorage) { //从缓存拿广告数据，
                commonModule.renderTopOrDailogADHtml(topOrDailogADLocalStorage, true);
            } else {
                commonModule.showTopOrDailogAD(true);//广告展示
            }

        var onShowFirstEditPswdDailog = null;
        var topBtnPermDict = @(Html.Raw(ViewBag.TopBtnPermDict ?? "")) || {};
        var showInvite = false;
        var hasAbnormalProductShowPerm = false;
        for (var key in topBtnPermDict) {
	        if (topBtnPermDict.hasOwnProperty(key)) {
		        var value = topBtnPermDict[key];
		        switch (key) {
		        case 'AgentInvite':
		        case 'SupplierInvite':
		        {
			        if (value) showInvite = true;

			        if (key === 'AgentInvite') {
				        if (value) {
					        $('#inviterQrCode_myAgentWrap').css("display","flex");
				        } else {
					        $('#inviterQrCode_myAgentWrap').css("display", "none");
				        }

			        }
			        if (key === 'SupplierInvite') {
				        if (value) {
					        $('#inviterQrCode_mySupplierWrap').css("display", "flex");
				        } else {
					        $('#inviterQrCode_mySupplierWrap').css("display", "none");
				        }
			        }
		        }break;
		        case 'SiteMessage':
			        if (value)
				        $('#' + key).show();
			        else
				        $('#' + key).hide();
			        break;
		        case 'AbnormalProduct':
			        hasAbnormalProductShowPerm = value;
			        break;
		        default:
			        break;
		        }
	        } else {
		        $('#' + key).hide();
	        }
        }
        if (showInvite) {
	        $('#quickInvite').show();
        } else {
	        $('#quickInvite').hide();
        }

        function FirstEditPswd() {
            onShowFirstEditPswdDailog = layer.open({
                type: 1,
                title: '修改密码',
                btn: false,
                move: false,
                resize: false,
                skin: 'n-skin',
                content: $("#showFirstEditPswdDailog"),
                area: ["560px", "320px"], //宽高
                success: function () {
                    $('#onInputCode121').val('');
                    $('#onInputCode122').val('');
                },
                cancel: function () {
                    //layer.close(onShowFirstEditPswdDailog);
                }
            });
        }

        var menuId = '@menuId';
        var menuActive = '@menuActive';
        var editPswdMobile = '@ViewBag.Mobile';
        var onShowEditPswdDailog = null;

        //console.log("commonModule.FenDanSystemNavs1", commonModule.FenDanSystemNavs)
        //console.log("commonModule.NavQuickEntry", commonModule.NavQuickEntry)
        //console.log("commonModule.NavQuickEntrySort", commonModule.NavQuickEntrySort )

        var CurrentPageUrl = window.location.href;
        //console.log(CurrentPageUrl);
        var isGeneralizeIndexShow = CurrentPageUrl.includes("GeneralizeIndex/Index"); // 首页
        var isAllOrderShow = CurrentPageUrl.includes("NewOrder-AllOrder"); // 所有订单
        var isWaitOrderShow = CurrentPageUrl.includes("NewOrder-WaitOrder"); // 打单发货
        var isSendOrderShow = CurrentPageUrl.includes("SendOrder-Index"); // 已发货明细
        var isPriceSettingShow = CurrentPageUrl.includes("PriceSetting"); // 对账设置
        var isAllProductShow = CurrentPageUrl.includes("type=all"); // 商品列表
        var isBaseProductShow = CurrentPageUrl.includes("BaseProduct/BaseProduct"); // 商品库

        $(function () {
            
            //小红书新旧面单切换强引导需求弹窗-要求每个页面都触发
            try { commonModule.CheckNewXiaoHongShuWindow(); } catch (e) { }
            GetSubPermissionsByParentTag();
            // 全局弹窗
            const name = '@ViewBag.ShopName';
            const value = '@ViewBag.ShopName';
            const decodedCookie = decodeURIComponent(document.cookie);
            const cookieArray = decodedCookie.split(';');
            var popCookie = null
            var isPart = window.location.href.indexOf('/Partner/MyAgent') > -1
            for (let i = 0; i < cookieArray.length; i++) {
                let cookie = cookieArray[i];
                const cookiePair = cookie.split('=');
                const cookieName = cookiePair[0];
                const cookieValue = cookiePair[1];
                if (cookieName == name) {
                    popCookie = cookieValue
                    console.log('cookie')
                    console.log(cookieName, cookieValue)
                }
            }
            if (popCookie == null) {
                var minutes = 0.5
                const expirationDate = new Date();
                expirationDate.setTime(expirationDate.getTime() + (minutes * 60 * 1000));
                const cookieValue = encodeURIComponent(value) + "; expires=" + expirationDate.toUTCString();
                document.cookie = name + "=" + cookieValue;
                commonModule.PopPrePayData(0, [1, 2, 3], false);

                if (isPart) {
                    commonModule.PopPrePayData(1, [5], false)
                } else {
                    commonModule.PopPrePayData(1, [4, 5], false)
                }
            }


            // 局部弹窗
            if (isPart) {
                commonModule.PopPrePayData(1, [4], true)
            }

            if (menuId) {//左侧导航选中着色高亮
                $("." + menuId).addClass("layui-this");
            };

            //newLeftNavModule.initRenderLeftNavs(commonModule.FenDanSystemNavs);

            $("#edmitDemandDailog_type>li").on("click", function () {
                $(this).toggleClass("active");
            })




            commonModule.Ajax({
                url: '/SubAccount/IsFirstLogin',
                type: 'GET',
                loading: false,
                async: true,
                success: function (rsp) {
                    if (!rsp.Success) {
                        w_alertEdit({ type: 2, content: rsp.Message });
                    } else {
                        var data = rsp.Data;
                        if (data.IsFirstLogin) {
                            FirstEditPswd();
                        }
                    }
                }
            });

            commonModule.Ajax({
                url: '/SubAccount/GetSubAccountById',
                type: 'GET',
                loading: false,
                async: true,
                success: function (rsp) {
                    if (!rsp.Success) {
                        w_alertEdit({ type: 2, content: rsp.Message });
                    } else {
                        var data = rsp.Data;
                        firstEditPswdPid = data.Pid;
                        data.token = commonModule.Token;
                        data.dbname = commonModule.DbName;

                        if(data.WxNickName && !data.Mobile) {
                           data.showId = true;
                        }

                        var tplt = $.templates("#account-information-card");
                        var html = tplt.render(data);
                        $("#userInfoCard").html(html);

                        commonModule.FxPermission(function (p) {
				        	commonModule.CheckPermission(function (success) {

				        		if (!success) {
                                    $("#new_account_settings_no_permission").css("display", "flex");
                                    $("#new_account_settings").css("display", "none");
				        		} else {
                                    $("#new_account_settings_no_permission").css("display", "none");
                                    $("#new_account_settings").css("display", "flex");
                                }

				        	}, '/System/Index', undefined, undefined, 4);
				        });
                    }
                }
            });

            var isUserInfoCardVisible = false;
            $('#logoShow').on("click", function () {
                 isUserInfoCardVisible = !isUserInfoCardVisible;
                 if (isUserInfoCardVisible) {
                    $('#userInfoCard').show();
                    $('#user-info-card-down-icon').css('transform', 'rotate(180deg)');
                } else {
                    $('#userInfoCard').hide();
                    $('#user-info-card-down-icon').css('transform', 'rotate(0deg)');
                }

            })

            commonModule.InitUserVersionInfo();
            //触发自动同步商品
            if ((menuId == "Product_all") && getCookie("SysAutoSyncProduct-" + commonModule.CurrShop.Id) == null) {
                commonModule.Ajax({
                    url: '/Product/SyncProduct',
                    data: { isAuto: true },
                    loading: false,
                    success: function (rsp) {
                        setCookie("SysAutoSyncProduct-" + commonModule.CurrShop.Id, "1", 8);
                    },
                    error: function (rsp) {
                        if (rsp.status == 401) {
                            //layer.msg("暂无权限，请联系管理员");
                        } else {
                            layer.msg(rsp.message);
                        }
                    }
                });
            }


            //获取失效店铺
            //queryInvalid();

            //隐藏失效店铺
            layui.form.on('switch(switchTop01)', function (data) {
                var key = data.othis.prev().attr("name");
                var idStr = data.othis.prev().attr("data-ids");
                var isCheck = this.checked;
                commonModule.Ajax({
                    url: '/common/seveFilterInvalidShopId',
                    data: { 'idStr': idStr, 'isCheck': isCheck },
                    loading: false,
                    success: function (rsp) {
                        if (rsp.Success) {
                            $('.shopPastWrap').hide();
                            $('#AccessExpireShopCount-div').hide();
                        }
                        layer.msg(rsp.Data || rsp.Message);
                    }
                });
            });
            // 获取未读消息数
            commonModule.getUnReadCount();
            getAbnormalProductCount();
        });
        // 获取异常商品变更消息未读数量
        function getAbnormalProductCount() {
            commonModule.Ajax({
                url: "/api/ProductSkuHistory/GetUnreadCount",
                type: "GET",
                success: function (rsp) {
                    if (rsp.Success) {
                        var resData = rsp.Data;
                        if (resData.TotalUnreadCount > 0) {
                            $("#AbnormalUnreadCount").text(resData.TotalUnreadCount);
                            // 判断是否显示商品变更异常提示
                            if (hasAbnormalProductShowPerm && (isGeneralizeIndexShow || isAllProductShow || isAllOrderShow || isWaitOrderShow || isSendOrderShow || isPriceSettingShow || isBaseProductShow)) {
                                $("#ShowAbnormalProductLi").show();
                            } else {
                                $("#ShowAbnormalProductLi").hide();
                            }
                        } else {
                            $("#ShowAbnormalProductLi").hide();
                        }
                        // 48小时内页面提醒
                        if (resData.In48H) {
                            getReminderSetting();
                        }
                    }
                }
            });
        }
        // 获取消息提醒设置
        function getReminderSetting() {
            commonModule.Ajax({
                url: "/api/Common/GetReminderSetting",
                type: "GET",
                success: function (rsp) {
                    if (rsp.Success) {
                        var data = rsp.Data;
                        var list = data.SkuChangedRemViewList;
                        // 设置了推送范围
                        if (list.length > 0) {
                            var isHomePage = list.includes('home');
                            var isOrderList = list.includes('orderList');
                            var isProductList = list.includes('productList');
                            var isFinancialSettlement = list.includes('financialSettlement');
                            // 判断是否在页面推送提示
                            var HomeShowTip = isGeneralizeIndexShow && isHomePage; // 在首页推送提示
                            var AllOrderShowTip = isAllOrderShow && isOrderList; // 在所有订单推送提示
                            var WaitOrderShowTip = isWaitOrderShow && isOrderList; // 在打单发货推送提示
                            var SendOrderShowTip = isSendOrderShow && isOrderList; // 在已发货明细推送提示
                            var PriceSettingShowTip = isPriceSettingShow && isFinancialSettlement; // 在对账设置推送提示
                            var AllProductShowTip = isAllProductShow && isProductList; // 在商品列表推送提示
                            var BaseProductShowTip = isBaseProductShow && isProductList; // 在商品库推送提示

                            if (HomeShowTip || AllOrderShowTip || WaitOrderShowTip || SendOrderShowTip || PriceSettingShowTip || AllProductShowTip || BaseProductShowTip) {
                                $("#ShowAbnormalProductTip").show();
                                $("#NoticeWrapShow").addClass('notice-wrap');
                            } else {
                                $("#ShowAbnormalProductTip").hide();
                                $("#NoticeWrapShow").removeClass('notice-wrap');
                            }
                        } else {
                            $("#ShowAbnormalProductTip").hide();
                            $("#NoticeWrapShow").removeClass('notice-wrap');
                        }
                    }
                }
            });
        }
        // 关闭提示
        function CloseAbnormalProductTip() {
            $("#ShowAbnormalProductTip").hide();
            $("#NoticeWrapShow").removeClass('notice-wrap');
        }
        // 立即查看
        function ViewAbnormalProductDialog() {
            $("#ShowAbnormalProductTip").hide();
            $("#NoticeWrapShow").removeClass('notice-wrap');
            commonModule.AbnormalProductChangeDetail();
        }

        //服务器时间
        setInterval(function () {
            try {
                commonModule.ServerNowTime = new Date(commonModule.ServerNowTime).DateAdd('s', 1).Format();
            } catch (e) {
                commonModule.ServerNowTime = new Date().DateAdd('s', 1);
            }
        }, 1000);

        function GetSubPermissionsByParentTag() {
            commonModule.Ajax({
                url: '/SubAccount/GetSubPermissionsByParentTag',
                loading: false,
                async: true,
                data: { tag: 'NewOrder/PrintSetting' },
                success: function (rsp) {
                    if (rsp.Success) {
                        if (rsp.Data && rsp.Data.length) {
                            if (rsp.Data.indexOf('SetInfoController/View') > -1) {
                                return commonModule.PrintSettingUrl = '/SetInfo';
                            }
                            var url = '/' + rsp.Data[0].replace('Controller', '').replace('/View', '');
                            if (url == '/SellerInfo/ExpressReach') {
                                commonModule.PrintSettingUrl = '/SetInfo/ExpressReach'
                            } else if (url == '/SellerInfo/ManyCodeSendSet') {
                                commonModule.PrintSettingUrl = '/SetInfo/ManyCodeSendSet'
                            } else {
                                commonModule.PrintSettingUrl = url + '/Index';
                            }
                        } else {
                            commonModule.PrintSettingUrl = '/SetInfo';
                        }
                    }
                }
            });
        }

        function OnMyShopTap (url){
            commonModule.OpenNewTab(url + "&rp=@Request["token"]");
        }

        function inviteDailog() {


            var checkResult = commonModule.CheckVirtualRegMobile();
            if (!checkResult) {
                return;
            }

            layer.open({
                type: 1,
                title: "扫码邀请注册，自动绑定合作",
                btn: ["关闭"],
                shadeClose: true,
                area: '532px',
                skin: 'wu-dailog',
                content: $(".inviteDailogWrap"),
                success: function () {
                    //$("#inviterQrCode_myAgentWrap").show();
                    //$("#inviterQrCode_mySupplierWrap").show();
                },
                cancel: function () {
                }
            });
        }

        var firstInputNum1 = 0;
        var firstInputNum2 = 0;

        // 首次修改密码--新密码--默认
        function onLookFirstEditPswd1(num) {
            firstInputNum1 = num;
            $('#onInputCode1211').val($('#onInputCode121').val());
            $('#onInputCode1211').show();
            $('#iconChevronDown1211').show();
            $('#onInputCode121').hide();
            $('#iconChevronDown121').hide();
        }

        // 首次修改密码--新密码--打开
        function onLookFirstEditPswd11(num) {
            firstInputNum1 = num;
            $('#onInputCode1211').hide();
            $('#iconChevronDown1211').hide();
            $('#onInputCode121').val($('#onInputCode1211').val());
            $('#onInputCode121').show();
            $('#iconChevronDown121').show();
        }

        // 首次修改密码--确认新密码--默认
        function onLookFirstEditPswd2(num) {
            firstInputNum2 = num;
            $('#onInputCode1222').val($('#onInputCode122').val());
            $('#onInputCode1222').show();
            $('#iconChevronDown1222').show();
            $('#onInputCode122').hide();
            $('#iconChevronDown122').hide();
        }

        // 首次修改密码--确认新密码--打开
        function onLookFirstEditPswd22(num) {
            firstInputNum2 = num;
            $('#onInputCode1222').hide();
            $('#iconChevronDown1222').hide();
            $('#onInputCode122').val($('#onInputCode1222').val());
            $('#onInputCode122').show();
            $('#iconChevronDown122').show();
        }


        // 修改密码--新密码--默认
        function onLookFirstEditPswd12(num) {
            firstInputNum1 = num;
            $('#onInputCode131').hide();
            $('#iconChevronDown131').hide();
            $('#onInputCode1311').val($('#onInputCode131').val());
            $('#onInputCode1311').show();
            $('#iconChevronDown1311').show();
        }

        // 修改密码--新密码--打开
        function onLookFirstEditPswd121(num) {
            firstInputNum1 = num;
            $('#onInputCode131').show();
            $('#iconChevronDown131').show();
            $('#onInputCode131').val($('#onInputCode1311').val());
            $('#onInputCode1311').hide();
            $('#iconChevronDown1311').hide();
        }

        // 修改密码--确认新密码--默认
        function onLookFirstEditPswd21(num) {
            firstInputNum2 = num;
            $('#onInputCode132').hide();
            $('#iconChevronDown132').hide();
            $('#onInputCode1333').val($('#onInputCode132').val());
            $('#onInputCode1333').show();
            $('#iconChevronDown1333').show();
        }

        // 修改密码--确认新密码--打开
        function onLookFirstEditPswd221(num) {
            firstInputNum2 = num;
            $('#onInputCode1333').hide();
            $('#iconChevronDown1333').hide();
            $('#onInputCode132').val($('#onInputCode1333').val());
            $('#onInputCode132').show();
            $('#iconChevronDown132').show();
        }

        $('#onInputCode121').on('input', function () {
            handleInputEditPswd();
        });
        $('#onInputCode1211').on('input', function () {
            handleInputEditPswd();
        });
        $('#onInputCode122').on('input', function () {
            handleInputEditPswd();
        });
        $('#onInputCode1222').on('input', function () {
            handleInputEditPswd();
        });

        $('#onInputCode131').on('input', function () {
            handleInputEditPswd1();
        });
        $('#onInputCode1311').on('input', function () {
            handleInputEditPswd1();
        });
        $('#onInputCode132').on('input', function () {
            handleInputEditPswd1();
        });
        $('#onInputCode1333').on('input', function () {
            handleInputEditPswd1();
        });
        $('#onInputCode13').on('input', function () {
            $(this).val(($(this).val()).substring(0, 6));
            $(this).val($(this).val().trim());
            if (isNaN($(this).val())) {
                $(this).val($(this).val().replace(/[^\d]/g, ''));
            }
            handleInputEditPswd1();
        });

        function handleInputEditPswd() {
            var Password = firstInputNum1 == 0 ? $('#onInputCode121').val() : $('#onInputCode1211').val();
            var ConfirmPassword = firstInputNum2 == 0 ? $('#onInputCode122').val() : $('#onInputCode1222').val();
            if (Password && ConfirmPassword) {
                $('.confirm-first-editPswd-btn').css({ 'cursor': 'pointer', 'color': '#e6f3ff', 'background-color': '#0888ff' });
            } else {
                $('.confirm-first-editPswd-btn').css({ 'cursor': 'no-drop', 'color': '#f8fbff', 'background-color': '#b5dcff' });
            }
        }

        function handleInputEditPswd1() {
            var Password = firstInputNum1 == 0 ? $('#onInputCode131').val() : $('#onInputCode1311').val();
            var ConfirmPassword = firstInputNum2 == 0 ? $('#onInputCode132').val() : $('#onInputCode1333').val();
            if (Password && ConfirmPassword && $('#onInputCode13').val().length == 6) {
                $('.confirm-first-editPswd-btn').css({ 'cursor': 'pointer', 'color': '#e6f3ff', 'background-color': '#0888ff' });
            } else {
                $('.confirm-first-editPswd-btn').css({ 'cursor': 'no-drop', 'color': '#f8fbff', 'background-color': '#b5dcff' });
            }
        }

        function cancelFirstEditPswd() {
            $('#onInputCode1211').hide();
            $('#iconChevronDown1211').hide();
            $('#onInputCode121').val('').show();
            $('#iconChevronDown121').show();
            $('#onInputCode1222').hide();
            $('#iconChevronDown1222').hide();
            $('#onInputCode122').val('').show();
            $('#iconChevronDown122').show();

            $('#onInputCode1311').hide();
            $('#iconChevronDown1311').hide();
            $('#onInputCode131').val('').show();
            $('#iconChevronDown131').show();
            $('#onInputCode1333').hide();
            $('#iconChevronDown1322').hide();
            $('#onInputCode132').val('').show();
            $('#iconChevronDown132').show();
            $('.confirm-first-editPswd-btn').css({ 'cursor': 'no-drop', 'color': '#f8fbff', 'background-color': '#b5dcff' });

            layer.close(onShowFirstEditPswdDailog);
            layer.close(onShowEditPswdDailog);
        }

        var countEditdowNum = 59;
        function onGetObtainPhoneCode() {
            commonModule.Ajax({
                url: '/FxAccount/PostMobileMessageCode',
                data: { phone: editPswdMobile, types: '2' },
                async: true,
                type: 'POST',
                success: function (rsp) {
                    if (!rsp.Success) {
                        w_alertEdit({ type: 2, content: rsp.Message });
                    } else {
                        $('#obtainCodeNum12').hide(); // 隐藏按钮
                        countEditdowNum = 59;
                        $('#countdown12').text(countEditdowNum);
                        $('#countdownBox12').show().css({"cursor":"no-drop"}).off('click');// 显示倒计时禁用倒计时按钮
                        var timer = setInterval(function () {
                            if (countEditdowNum > 1) {
                                countEditdowNum -= 1;
                                $('#countdown12').text(countEditdowNum);
                            } else {
                                clearInterval(timer);
                                $('#countdownBox12').hide();
                                $('#obtainCodeNum12').show().on('click', onGetobtainPhoneCode);//恢复按钮添加点击事件
                            }
                        }, 1000);
                    }
                },
                cancel: function () {
                    cancelFirstEditPswd();
                }
            });
        }

        // type 等于1是新账户中心里的设置密码  不是1是新账户中心里的设置密码
        function EditPswd(type) {
            var newTitle = '修改密码';
            var newArea = ["560px", "472px"];
            if(type == 1) {
                newTitle = '设置密码';
                newArea = ["480px", "468px"];
            }
            onShowEditPswdDailog = layer.open({
                type: 1,
                title: newTitle,
                btn: false,
                move: false,
                resize: false,
                skin: 'n-skin',
                content: $("#showEditPswdDailog"),
                area: newArea, //宽高
                success: function () {

                    if(type == 1) {
                        $(".icon-a-error-circle-filled1x-span").text('设置后，原密码将无法登录系统，需使用新密码登录。');
                        $(".confirm-first-editPswd-btn").text('保存').addClass("new-confirm-first-editPswd-btn");

                        $(".cancel-first-editPswd-btn").text('暂不设置');

                         $("#onInputCode13").on('input', function () {
						    $(this).val(($(this).val()).substring(0, 6));
						    $(this).val($(this).val().trim());
						    if (isNaN($(this).val())) {
							    $(this).val($(this).val().replace(/[^\d]/g, ''));
						    }
						    inputCodeValue = $(this).val();

					    });
                        // 监听验证码输入框 失去焦点的时候校验验证码格式不对红框提示
					    $('#onInputCode13').on('blur', function () {
						    if (inputCodeValue.length != 6) {
							    $(this).addClass("check-error-red-tip");
							    $(".ipnut-code-red-tips").show();
						    } else {
							    $(this).removeClass("check-error-red-tip");
							    $(".ipnut-code-red-tips").hide();
						    }
					    });
					    // 监听验证码输入框 输入位数到6位的时候去掉红框提示
					    $('#onInputCode13').on('input', function () {
						    if (inputCodeValue.length == 6) {
							    $(this).removeClass("check-error-red-tip");
							    $(".ipnut-code-red-tips").hide();
						    }
					    })
                        // 监听密码输入框 不允许输入中文
                        $('#onInputCode1311').on('input', function() {
                            $(this).val($(this).val().replace(/[\u4e00-\u9fa5]/g, ''));
                        })
                        $('#onInputCode1333').on('input', function() {
                            $(this).val($(this).val().replace(/[\u4e00-\u9fa5]/g, ''));
                        })
                    }


                    $('#onInputPhone12').val(editPswdMobile);
                    $('#onInputCode13').val('');
                    if (countEditdowNum != 59) {
                        $('#obtainCodeNum12').css({ 'cursor': 'no-drop', 'color': '#b3b3b3' })
                    }
                },
                cancel: function () {
				    cancelFirstEditPswd();
                }
            });
        }

        function w_alertEdit(options) {
            var html = "";
            var type = options.type ? options.type : 1;
            var content = options.content;
            var className = '';
            if (type == 1) {
                className = "icon-a-heart-filled1x";
            } else if (type == 2) {
                className = "icon-a-error-circle-filled1x";
            } else if (type == 3) {
                className = "icon-a-error-circle-filled1x";
            } else if (type == 4) {
                className = "icon-a-check-circle-filled1x";
            }
            var id = 'alert' + new Date().getTime();
            if (true) {
                html += '<div class="w-msg" style="width: unset;left: 50%;transform: translateX(-50%);" id=' + id + '>';
                html += '<div class="n-toast n-toast-0' + type + '" ><i class="iconfont ' + className + '"></i>' + content + '</div>';
                html += '</div>'
                $("body").append(html);
            }
            var times = options.times ? options.times : 2000;
            setTimeout(function () {
                $("#" + id).remove();
            }, times)
        }

        // 首次修改密码
        //var regEditPswd = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,20}$/
        function confirmFirstEditPswd() {
            var Password = firstInputNum1 == 0 ? $('#onInputCode121').val() : $('#onInputCode1211').val();
            var ConfirmPassword = firstInputNum2 == 0 ? $('#onInputCode122').val() : $('#onInputCode1222').val();
            if (Password && ConfirmPassword) {
                var result = commonModule.checkPwdNew(Password);
                @*if (!regEditPswd.test(Password) || !regEditPswd.test(ConfirmPassword)) {
                    w_alertEdit({ type: 2, content: '密码必须由8-20位数字、字母或特殊符号组成' })
                    return
                }*@
            if (!result.success) {
                    w_alertEdit({ type: 2, content: result.msg, times: result.times })
                    return
                }
                if (Password != ConfirmPassword) {
                    w_alertEdit({ type: 2, content: '两次密码不一致' })
                    return
                }
                commonModule.Ajax({
                    url: '/SubAccount/ResetPasswordNoVCode',
                    data: { Id: firstEditPswdPid, Password: Password, ConfirmPassword: ConfirmPassword },
                    async: true,
                    loading: false,
                    type: 'POST',
                    success: function (rsp) {
                        if (!rsp.Success) {
                            w_alertEdit({ type: 2, content: rsp.Message });
                        } else {
                            cancelFirstEditPswd();
                            w_alertEdit({ type: 4, content: '修改成功' });
                        }
                    }
                });
            }
        }

        var regEditPswd = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,20}$/
        function confirmEditPswd() {
            var Password = firstInputNum1 == 0 ? $('#onInputCode131').val() : $('#onInputCode1311').val();
            var ConfirmPassword = firstInputNum2 == 0 ? $('#onInputCode132').val() : $('#onInputCode1333').val();
            var MobileMeessageCode = $('#onInputCode13').val();
            if (Password && ConfirmPassword) {
                if (MobileMeessageCode.length != 6) {
                    w_alertEdit({ type: 2, content: '请输入6位验证码' })
                    return
                }
                @*if (!regEditPswd.test(Password) || !regEditPswd.test(ConfirmPassword)) {
                    w_alertEdit({ type: 2, content: '密码必须由6-20位数字和字母组成' })
                    return
                }*@
                var result = commonModule.checkPwdNew(Password);
                if (!result.success) {
                    w_alertEdit({ type: 2, content: result.msg, times: result.times })
                    return
                }
                if (Password != ConfirmPassword) {
                    w_alertEdit({ type: 2, content: '两次密码不一致' })
                    return
                }
                commonModule.Ajax({
                    url: '/System/UpdatePassWord',
                    data: { Mobile: editPswdMobile, MobileMeessageCode: MobileMeessageCode, Password: Password, ConfirmPassword: ConfirmPassword },
                    async: true,
                    loading: false,
                    type: 'POST',
                    success: function (rsp) {
                        if (!rsp.Success) {
                            w_alertEdit({ type: 2, content: rsp.Message });
                        } else {
                            cancelFirstEditPswd();

                            if($(".confirm-first-editPswd-btn").hasClass("new-confirm-first-editPswd-btn")) {
                                w_alertEdit({ type: 4, content: '设置密码成功' });
                            } else {
                                w_alertEdit({ type: 4, content: '修改成功' });
                            }

                        }
                    }
                });
            }
        }


        function setCookie(name, value, expiresHours) {
            if (expiresHours == undefined) {
                expiresHours = 8;
            }
            var exp = new Date();
            exp.setTime(exp.getTime() + expiresHours * 60 * 60 * 1000);
            document.cookie = name + "=" + escape(value) + ";path=/;expires=" + exp.toGMTString();
        }
        function getCookie(name) {
            var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
            if (arr = document.cookie.match(reg))
                return unescape(arr[2]);
            else
                return null;
        }

        /*-----------------新导航----------------------------*/

        //顶部失效店铺
        function queryInvalid() {
            $('.shopPastWrap').hide();
            $('.shopPastWrap-num').text('');
            $('.shopPastWrap-main-last').nextAll().remove();
            $('input[lay-filter="switchTop01"]').attr('data-ids', '');
            var shoptype = $("#ulShopType .active").attr("data-shopType");
            commonModule.LoadExpiredShop({ queryType: 'top_invalid_shop', shoptype: shoptype }, function (rsp) {
                if (rsp.Success) {
                    var shops = JSON.parse(rsp.Data.exData) || [];
                    if (shops.length == 0) {
                        $('.shopPastWrap').hide();
                        return;
                    }

                    if (rsp.Data.exCount && rsp.Data.exCount > 0 && menuId == "Partner") {
                        $("#layui_warnin").css({ display: "flex" });
                        $("#warnin_mun").text(rsp.Data.exCount);
                    }

                    var invalidIds = [];
                    var html = "";
                    for (var i = 0; i < shops.length; i++) {
                        html += '<li class="shopPastWrap-main-item">';
                        html += '<div class="shopPastWrap-main-item-left">';
                        html += '<i class="pintaiIcon ' + shops[i].PlatformType + '"></i>';
                        html += '<span class="shopName">' + shops[i].NickName + '</span>';
                        html += '</div>';
                        html += '<div class="shopPastWrap-main-item-right">';
                        if (shops[i].IsExpire && shops[i].PlatformPayUrl != '')
                            html += '<span class="status">已失效</span>';
                        else {
                            if (shops[i].Status == 1)
                                html += '<span class="status">授权成功</span>';
                            if (shops[i].Status == 2)
                                html += '<span class="status">解除关联</span>';
                            if (shops[i].Status == 3)
                                html += '<span class="status">授权过期</span>';
                        }

                        if (shops[i].platformType == "TouTiao") {
                            if (shops[i].PlatformPayUrl2 != '') {
                                html += '<span class="layui-btn layui-btn-normal layui-btn-sm" onclick="commonModule.latformRenewUrlTouTiao(\'' + shops[i].PlatformType + '\',\'' + shops[i].NickName + '\',\'' + shops[i].PlatformPayUrl2 + '\',\'' + shops[i].TouTiaoOldOrNew + '\')">续费</span>';
                            }
                        } else if (shops[i].IsExpire && shops[i].PlatformPayUrl != '') {
                            html += '<span class="layui-btn layui-btn-normal layui-btn-sm" onclick="commonModule.latformRenewUrl(\'' + shops[i].PlatformType + '\',\'' + shops[i].NickName + '\',\'' + shops[i].PlatformPayUrl + '\')">续费</span>';
                        } else {
                            if (shops[i].Status == 3) {
                                if (shops[i].IsAuthUrl) {
                                    html += '<span class="layui-btn layui-btn-normal layui-btn-sm" onclick="OnMyShopTap(\'' + shops[i].AuthUrl + '\')">重新授权</span>';
                                } else {
                                    html += '<span class="layui-btn layui-btn-normal layui-btn-sm" onclick="layer.msg(\'该平台不支持重新授权，请从店铺后台进入刷新授权/解除店铺再操作绑定\');">重新授权</span>';
                                }
                            }
                        }
                        html += '</div>';
                        html += '</li>';

                        invalidIds.push(shops[i].ShopId);
                    }
                    $('.shopPastWrap-num').text(shops.length);
                    $('.shopPastWrap-main-last').after(html);
                    $('input[lay-filter="switchTop01"]').attr('data-ids', invalidIds.join());
                    $('.shopPastWrap').show();
                }
                else {
                    console.error('顶部失效店铺', rsp);
                }
            });
        }
        /*-----------------end----------------------------*/
        $('.noticePopUp-shade').on("click", function () {
            if ($("#noticePopUp") != undefined) {
                $("#noticePopUp").hide();
            }
        })
        commonModule.userFlag = "@ViewBag.UserFlag"; // 判断是否纯铺货用户
        // 显示订购提示
        function activeShowTips() {
            commonModule.orderingTips({});
        }
    </script>
}
<script src="~/Scripts/layoutNew.js?v=@ViewBag.SystemVersion"></script>
<script src="~/Scripts/concatlayout.js?v=@ViewBag.SystemVersion"></script>

<script id="expired-shop-dialog-tmpl" type="text/x-jsrender">
    <div class="print-express-error-dialog-tmpl">
        <div class="wu-alert wu-warning wu-mB16 wu-flex wu-yCenter">
            <i class="iconfont icon-a-error-circle-filled1x" style="font-size: 20px;position: relative; top: 0;"></i>
            <span class="wu-alert-title">您有<span class="wu-f16 wu-color-c wu-mL4 wu-mR4 wu-weight600">{{:shops.length}}</span>个店铺已失效，请重新授权/续费。</span>
        </div>
        <div id='auth-expired-dialog'></div>
        <div class="dialog-content-row wu-tableWrap">
            <table class="error-message-table wu-table-one">
                <thead>
                    <tr>
                        <th>店铺名称</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {^{for shops}}
                    <tr>
                        <td>
                            {{:NickName}}
                            {{if PlatformTypeName}}
                            <span class="wu-color-c">
                                ({{:PlatformTypeName}})
                            </span>
                            {{/if}}
                        </td>
                        <td>
                            <div class="wu-flex wu-yCenter">
                                {{if PlatformType == "TouTiao" && PlatformPayUrl2}}
                                <span class="wu-color-a wu-operate wu-mR12" onclick="commonModule.latformRenewUrlTouTiao('{{:PlatformType}}','{{:NickName}}','{{:PlatformPayUrl2}}','{{:TouTiaoOldOrNew}}')">续费</span>
                                {{else IsExpire && PlatformPayUrl}}
                                {{if (PlatformType == "Pinduoduo") && PddIsUsePrintSystemApp }}
                                <span class="wu-color-a wu-operate wu-mR12" onclick="commonModule.latformRenewUrl('togglePinduoduo','{{:NickName}}','{{:PlatformPayUrl}}')">续费</span>
                                {{else}}
                                <span class="wu-color-a wu-operate wu-mR12" onclick="commonModule.latformRenewUrl('{{:PlatformType}}','{{:NickName}}','{{:PlatformPayUrl}}')">续费</span>
                                {{/if}}
                                {{/if}}
                                {{if IsAuthUrl}}
                                <a href="javascript:OnMyShopTap('{{:AuthUrl}}');" data-url="" class="wu-color-a wu-operate">重新授权</a>
                                {{else}}
                                <a href="javascript:void(0);" data-url="" onclick="layer.msg('该平台不支持重新授权，请从店铺后台进入刷新授权/解除店铺再操作绑定');" class="wu-color-a wu-operate">重新授权</a>
                                {{/if}}
                            </div>
                        </td>
                    </tr>
                    {{/for}}
                </tbody>
            </table>
        </div>
    </div>
</script>

<script id="notice-dailog-tmpl" type="text/x-jsrender">
    <div class="noticeDailog" id="noticeDailog">
        <div class="noticeDailog-main">
            <div class="layui-tab" lay-filter="docDemoTabBrief">
                <div class="layui-tab-brief flex noticeDailog-nav">
                    <ul class="layui-tab-title">
                        <li class="layui-this" data-type="Recommend" onclick="commonModule.chooseNoticeTab(this)">
                            推荐
                            {{if noticeData["Recommend"].count > 0 && noticeData["Recommend"].count < 100}}
                            <span>{{:noticeData["Recommend"].count}}</span>
                            {{else noticeData["Recommend"].count > 99}}
                            <span>99+</span>
                            {{/if}}
                        </li>
                        <li data-type="Public" onclick="commonModule.chooseNoticeTab(this)">
                            公告
                            {{if noticeData["Public"].count > 0 && noticeData["Public"].count < 100}}
                            <span>{{:noticeData["Public"].count}}</span>
                            {{else noticeData["Public"].count > 99}}
                            <span>99+</span>
                            {{/if}}
                        </li>
                        <li data-type="Order" onclick="commonModule.chooseNoticeTab(this)">
                            订单
                            {{if noticeData["Order"].count > 0 && noticeData["Order"].count < 100}}
                            <span>{{:noticeData["Order"].count}}</span>
                            {{else noticeData["Order"].count > 99}}
                            <span>99+</span>
                            {{/if}}
                        </li>
                        <li data-type="Product" onclick="commonModule.chooseNoticeTab(this)">
                            商品
                            {{if noticeData["Product"].count > 0 && noticeData["Product"].count < 100}}
                            <span>{{:noticeData["Product"].count}}</span>
                            {{else noticeData["Product"].count > 99}}
                            <span>99+</span>
                            {{/if}}
                        </li>
                        <li data-type="Cooperation" onclick="commonModule.chooseNoticeTab(this)">
                            合作
                            {{if noticeData["Cooperation"].count > 0 && noticeData["Cooperation"].count < 100}}
                            <span>{{:noticeData["Cooperation"].count}}</span>
                            {{else noticeData["Cooperation"].count > 99}}
                            <span>99+</span>
                            {{/if}}
                        </li>
                        <li data-type="Bill" onclick="commonModule.chooseNoticeTab(this)">
                            账单
                            {{if noticeData["Bill"].count > 0 && noticeData["Bill"].count < 100}}
                            <span>{{:noticeData["Bill"].count}}</span>
                            {{else noticeData["Bill"].count > 99}}
                            <span>99+</span>
                            {{/if}}
                        </li>
                    </ul>
                    <div class="setting">
                        <span>消息设置</span>
                        <div class="setting-box">
                            <div class="setting-item" onclick="commonModule.allMark()">全部标为已读</div>
                            <div class="setting-item" onclick="commonModule.subscribe()">消息订阅设置</div>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-content" id="noticeContent">
                    {{include tmpl="#notice-items-tmpl"/}}
                </div>
            </div>
        </div>
    </div>

</script>
<script id="notice-items-tmpl" type="text/x-jsrender">

    @*<div class="layui-tab-item layui-show"></div>*@
    <div class="noticeList">
        {{if noticeItems.count!=0}}
        {{for noticeItems.messages}}
        <div class="notice-item" data-mcode="{{:MessageCode}}" data-mid="{{:MessageId}}">
            <div class="notice-title flex">
                <div class="notice-title-left flex">
                    <i class="icon iconfont icon-shangpinguanli2" style="font-size:16px"></i>
                    <span>{{:TypeText}}</span>
                    {{if !IsRead}}
                    <i class="unread"></i>
                    {{/if}}
                </div>
                <div class="notice-title-right">
                    {{:CreateTime}}
                </div>
            </div>
            <div class="notice-item-cont">
                {{:Content}}
            </div>
            {{if !IsRead}}
            <div class="notice-btn-box flex">
                <span class="notice-btn" style="" onclick="commonModule.readedMark('{{:MessageCode}}')">我已知悉</span>
                {{if JumpUrl}}
                <span class="notice-btn" style="color: #0888ff; margin-left: 6px;" onclick="commonModule.noticeLook('{{:MessageCode}}', '{{:FirstType}}')">立即查看</span>
                {{/if}}
                @*<button type="button" class="layui-btn layui-btn-normal layui-btn-sm" onclick="commonModule.readedMark('{{:MessageCode}}')">我已知悉</button>
                    <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="commonModule.noticeLook('{{:MessageCode}}')">立即查看</button>*@
            </div>
            {{/if}}
        </div>
        {{/for}}
        {{else}}
        <div class="notice-item">
            <div class="notice-title flex">
                <div class="notice-title-left flex">
                </div>
                <div class="notice-title-right">
                </div>
            </div>
            <div class="notice-item-cont" style="text-align:center">
                暂无消息
            </div>
        </div>
        {{/if}}
    </div>
</script>

<script id="subscribe-dailog-tmpl" type="text/x-jsrender">
    <div class="subscribe" id="subscribeDailog">
        <div class="subscribe-list">
            {{for subscribeData}}
            {{if #index!=0}}
            <hr>
            {{/if}}
            <span>{{:title}}</span>
            {{for data}}
            {{if FirstType!="Order" || !!SecondType==true }}
            <div class="subscribe-item flex">
                <div class="l">
                    <div class="title2 flex">
                        <i class="icon iconfont icon-dingyue" style="font-size:16px"></i>
                        {{if SecondType}}
                        {{:SecondTypeText}}
                        {{else}}
                        {{:FirstTypeText}}
                        {{/if}}
                        {{if IsEnabled}}
                        <span>已订阅</span>
                        {{/if}}
                    </div>
                    <span>{{:Description}}</span>
                </div>
                <div class="r">
                    {{if IsEnabled}}
                    <div class="btn" onclick="commonModule.subscribeStatus('{{:FirstType}}','{{:SecondType}}')">取消订阅</div>
                    {{else}}
                    <div class="btn btn2" onclick="commonModule.subscribeStatus('{{:FirstType}}','{{:SecondType}}')">订阅</div>
                    {{/if}}
                </div>
            </div>
            {{/if}}
            {{/for}}
            {{/for}}
        </div>
    </div>
</script>
<style>
    .SRcotainer {
        left: 120px;
    }
</style>

<!-- 获取账号信息卡片 -->
<script type="text/x-jsrender" id="account-information-card">
    <div style="font-weight: 500; padding: 12px 16px; color: #1a1a1a;">账号信息</div>
    <div style="padding: 0 16px 4px; display: flex;">
        <span style="color: #999999; min-width: 68px;">账号名称</span>
        <span style="color: #1a1a1a; word-wrap: break-word; word-break: break-all;">{{:NickName}}</span>
    </div>
    <div style="padding: 4px 16px; display: flex;">
        <span style="color: #999999; min-width: 68px;">职位</span>
        <span style="color: #1a1a1a; word-wrap: break-word; word-break: break-all; ">{{:PostName}}</span>
    </div>
    {{if showId}}
    <div style="padding: 4px 16px; display: flex;">
        <span style="color: #999999; min-width: 68px;">用户ID</span>
        <span style="color: #1a1a1a; word-wrap: break-word; word-break: break-all; ">{{:Pid}}</span>
    </div>
    {{/if}}
    <!-- 新版只显示账号名称和职位 下面这些注释掉 -->
    <!-- <div style="padding:4px 16px; display: flex;">
        <span style="color: #999999; min-width: 68px;">手机号</span>
        {{if IsBindMobile}}
        <span style="color: #1a1a1a;">{{:Mobile}}</span>
        {{else}}
        <span style="color: #666666">未绑定</span>
        {{/if}}
    </div>
    <div style="padding:4px 16px ;display: flex; ">
        <span style="color: #999999; min-width: 68px;">微信</span>
        {{if IsBindWeChat}}
        <span style="color: #1a1a1a;">{{:WxNickName}}</span>
        {{else}}
        <span style="color: #666666">未绑定</span>
        {{/if}}
    </div>
    {{if !IsBindMobile && IsBindWeChat}}
    <div style="padding: 4px 16px 12px; display: flex; ">
        <span style="color: #999999; min-width: 68px;">用户Id</span>
        <span style="color: #1a1a1a;">{{:Pid}}</span>
    </div>
    {{/if}} -->
    <div style="display: flex; width: 100%; border-top: 1px solid #e8e8e8; cursor: pointer; height: 48px;">

        <a href="{{>'/System/Index'+'?token='+ token+'&dbname='+ dbname}}" id="new_account_settings" style="flex: 1; color: #666666; display: flex; align-items: center; justify-content: center; border-right: 1px solid #e8e8e8; padding: 0px;">
            <span style="display: flex; align-items: center; justify-content: center; ">账号设置</span>
        </a>
        <div href="" id="new_account_settings_no_permission" onclick="commonModule.accoutnSetTip()" style="flex: 1; color: #666666; display: none; align-items: center; justify-content: center; border-right: 1px solid #e8e8e8; padding: 0px;">
            <span style="display: flex; align-items: center; justify-content: center; ">账号设置</span>
        </div>

        <span style="display: flex; flex: 1; align-items: center; justify-content: center; color: #EA572E;" onclick="Logout()">退出</span>
    </div>

</script>

<!-- 提示弹窗 -->
<script id="accountSetTipsDialog" type="text/x-jsrender">
<div class="tips-dialog dialog-content">
    <div>暂无权限，请联系管理员设置。</div> 
 </div>
</script>
<!-- 提示弹窗 -->
<script id="accountSetTipsDialog" type="text/x-jsrender">
    <div class="tips-dialog dialog-content">
        <div>暂无权限，请联系管理员设置。</div>
    </div>
</script>