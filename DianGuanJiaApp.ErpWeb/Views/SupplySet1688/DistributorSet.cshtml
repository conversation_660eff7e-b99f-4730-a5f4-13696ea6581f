@{
    ViewBag.Title = "1688分销-下游分销商设置";
    ViewBag.MenuId = "TradeSetting";
    ViewBag.MenuTopNav = "DistributorSet";
    ViewBag.MenuActive = "SupplySet1688";
}
@section Header{
    <style type="text/css">
        .layui-mysearch {
            width: 1310px;
            display: flex;
        }

            .layui-mysearch .layui-form {
                width: 1122px;
            }

        .mysearch-partOne {
            margin-right: 10px;
            margin-bottom: 15px;
        }

        .mysearch-partTwo {
            display: flex;
            /* flex-direction: column; */
            padding-left: 15px;
            border-left: 1px solid #e2e2e2;
            height: 35px;
            width: 174px;
            flex-direction: row;
            justify-content: space-between;
        }

            .mysearch-partTwo button {
                width: 80px;
            }

                .mysearch-partTwo button.layui-btn-normal {
                    margin-bottom: 15px;
                }

        .layui-btn + .layui-btn {
            margin-left: 0;
        }

        .layui-mynav {
            display: flex;
            margin-bottom: 5px;
        }

            .layui-mynav > li {
                margin-right: 45px;
                height: 25px;
            }

                .layui-mynav > li.active {
                    color: #3aadff;
                    position: relative;
                }

                .layui-mynav > li a {
                    color: #666;
                }

                .layui-mynav > li.active a {
                    color: #3aadff;
                }

                .layui-mynav > li.active::after {
                    width: 100%;
                    position: absolute;
                    height: 2px;
                    background-color: #3aadff;
                    content: "";
                    left: 0;
                    bottom: 0;
                    display: block;
                }

                .layui-mynav > li a:hover {
                    color: #3aadff;
                }

        .layui-mytable {
            margin-bottom: 0;
            border-bottom: 1px solid #e5e9f2;
        }

            .layui-mytable .layui-mytable-header {
                border-top: 1px solid #e5e9f2;
                height: 40px;
                display: flex;
                align-items: center;
                background-color: #f6f9fd;
            }

                .layui-mytable .layui-mytable-header > div {
                    text-align: center;
                    color: #04385d;
                    position: relative;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                }

            .layui-mytable .allcheckorder,
            .layui-mytable .order-chx {
                position: absolute;
                left: 15px;
            }

        .expressTemplate {
            display: flex;
            padding: 15px 15px 10px 15px;
            min-height: unset;
        }

            .expressTemplate .expressTemplate-title {
                font-size: 14px;
                color: #04385d;
            }

            .expressTemplate .expressTemplate-content {
                display: flex;
                flex-wrap: wrap;
                flex: 1;
            }

                .expressTemplate .expressTemplate-content > li {
                    display: flex;
                    align-items: center;
                    margin-right: 15px;
                    margin-bottom: 5px;
                }

            .expressTemplate .expressTemplate-content-operate {
                position: relative;
                top: 1px;
            }

                .expressTemplate .expressTemplate-content-operate > span {
                    margin-right: 5px;
                    color: #57beea;
                    font-weight: 700;
                    font-size: 14px;
                    cursor: pointer;
                }

            .expressTemplate .expressTemplate-content > li > label {
                cursor: pointer;
                display: flex;
                align-items: center;
            }

                .expressTemplate .expressTemplate-content > li > label > input[type=radio] {
                    margin-right: 3px;
                }

        .layui-mytable .layui-mytable-tr {
            padding: 15px 0;
            color: #666;
            display: flex;
            align-items: center;
        }

            .layui-mytable .layui-mytable-tr.layui-row-item {
                border-top: 1px solid #e5e9f2;
            }

                .layui-mytable .layui-mytable-tr.layui-row-item.active {
                    background-color: #99d699;
                    color: #04385d;
                }

            .layui-mytable .layui-mytable-tr > div {
                display: flex;
                align-items: center;
            }

        .layui-mytable .layui-mytable-tr-showItems {
            display: none;
        }

        .layui-mytable .layui-mytable-tr.layui-mytable-tr-show {
            background-color: #f8f8f8;
            align-items: flex-start;
        }

        .layui-mytable .layui-mytable-tr .mytable-tr-oparete {
            display: flex;
            align-items: center;
            padding-left: 35px;
        }

        .layui-mytable .layui-mytable-tr .mytable-tr-oparete02 {
            justify-content: center;
        }

            .layui-mytable .layui-mytable-tr .mytable-tr-oparete02 .icon-zhankai1 {
                color: #3aadff;
                font-size: 12px;
                cursor: pointer;
                display: inline-block;
            }

                .layui-mytable .layui-mytable-tr .mytable-tr-oparete02 .icon-zhankai1.zk {
                    transform: rotate(180deg);
                }

        .layui-mytable .layui-mytable-tr .mytable-tr-time {
            display: flex;
            flex-direction: column;
        }

        .layui-mytable .layui-mytable-tr > div.typeOne {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: flex-start;
        }

        .layui-mytable .layui-mytable-tr-show-lists {
            background-color: #eaf6ff;
            display: flex;
            padding: 3px 5px;
            width: 100%;
            box-sizing: border-box;
        }

        .layui-mytable .layui-mytable-tr-showItem {
            min-width: 240px;
            display: flex;
            flex-direction: column;
            padding: 0 5px;
        }

            .layui-mytable .layui-mytable-tr-showItem .icon-flag-warning {
                margin-right: 3px;
                position: relative;
                top: 5px;
            }

        .layui-mytable-tr-show-listsNum > li {
            padding: 1px 0;
        }

        .layui-mytable .layui-mytable-tr-show-remark {
            border: 1px solid #ffe1af;
            padding: 5px 10px;
            border-radius: 2px;
            background-color: #fffefc;
            margin-top: 3px;
            width: 100%;
            box-sizing: border-box;
            color: #3aadff;
        }

        .layui-row-item .productShow,
        .showlayui-row-item .productShow {
            display: flex;
        }

            .layui-row-item .productShow img,
            .showlayui-row-item .productShow img {
                width: 60px;
                height: 60px;
                margin-right: 3px;
            }

            .layui-row-item .productShow > ul,
            .showlayui-row-item .productShow > ul {
                display: flex;
                flex-direction: column;
                justify-content: space-around;
            }

        .layui-mytable-header .showMoreOrHide {
            display: flex;
            align-items: center;
        }



        .layui-mywrap .layui-tab {
            margin: 0;
        }

        .layui-tab-title .layui-this {
            color: #3aadff;
        }

        .layui-mywrap .layui-mywrap-partOne {
            padding: 15px 0;
            display: flex;
            align-items: center;
        }

            .layui-mywrap .layui-mywrap-partOne .layui-nav-my01-div .dColor {
                padding-left: 0;
            }

        .layui-mytable-header .colFrist {
            padding-left: 35px;
            display: flex;
            justify-content: center !important;
        }

            .layui-mytable-header .colFrist .layui-btn-warm {
                background-color: #f29a1a;
                margin-left: 50px;
            }

        .layui-mytable .layui-mytable-tr > div.col-typeOne {
            flex-direction: column;
            align-items: flex-start;
            justify-content: space-between;
        }

            .layui-mytable .layui-mytable-tr > div.col-typeOne > span {
                padding: 1px 0;
            }

                .layui-mytable .layui-mytable-tr > div.col-typeOne > span .icon {
                    display: inline;
                    border-radius: 2px;
                    padding: 1px 3px;
                    background-color: #ff511c;
                    color: #fff;
                    margin-right: 3px;
                }

        .layui-mytable .showlayui-row-item {
            padding-left: 100px;
        }

        .layui-mytable .showOrHide-col-item {
            padding-left: 100px;
            color: #3aadff;
            cursor: pointer;
            padding-bottom: 10px;
        }

            .layui-mytable .showOrHide-col-item .icon-down {
                font-size: 12px;
                display: inline-block;
                transform: rotate(180deg);
                margin-left: 10px;
            }

        .layui-mytable .layui-mytable-tr.layui-mytable-tr-show {
            padding-bottom: 0;
        }


        .adialog-bindsupplier {
            width: 580px;
            padding: 15px;
            box-sizing: border-box;
            display: none;
        }

            .adialog-bindsupplier > div {
                padding: 10px;
            }

        .bindsupplier-title {
            font-size: 16px;
            color: #04385d;
        }

            .bindsupplier-title > i {
                color: #ff511c;
                font-size: 12px;
                padding-left: 10px;
            }

        .adialog-bindsupplier .bindsupplier-main {
            display: flex;
        }

            .adialog-bindsupplier .bindsupplier-main label {
                margin-right: 50px;
                display: flex;
                align-items: center;
                font-size: 14px;
            }

                .adialog-bindsupplier .bindsupplier-main label input {
                    margin-right: 5px;
                }

        .adialog-bindsupplier .bindsupplier-mainTwo {
            display: flex;
            flex-direction: column;
            background-color: #f5f5f5;
            height: 50px;
            padding: 10px;
            justify-content: space-around;
        }


        @@media(max-width:1280px) {

            .layui-mywrap {
                width: 1160px;
            }
        }


        @@media(max-width:1440px) {

            .layui-mytable {
                min-width: 1150px;
            }
        }

        .layui-mysearch {
            margin-top: 15px;
        }

        .layui-mytable-title {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            color: #04385d;
            position: relative;
            justify-content: space-between;
        }

            .layui-mytable-title .batch-btns {
                margin: 0 25px;
            }

            .layui-mytable-title .product-mun > i {
                color: #3aadff;
            }

        .layui-mytable-title-left {
            display: flex;
            flex-direction: row;
            align-items: center;
        }

        .layui-mytable-title-right {
            display: flex;
        }

            .layui-mytable-title-right > label {
                display: flex;
                align-items: center;
                margin-right: 15px;
            }

        .mytable-tr-oparete02 {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: flex-start !important;
        }

            .mytable-tr-oparete02 .mytable-tr-oparete02-main {
                width: 120px;
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: space-between;
            }

            .mytable-tr-oparete02 > span {
                width: 50%;
            }

        .setWrapMain {
            background-color: #fff;
            /* box-shadow: 0 1px 2px 0 rgb(0 0 0 / 5%); */
            height: 100%;
            margin-right: 15px;
            display: flex;
            flex-direction: column;
            margin: 15px;
        }

        .setWrapContent-title {
            padding: 12px 0;
            /* border-bottom: 1px dotted #ddd; */
            margin: 0 15px;
            display: flex;
            flex-direction: row;
            align-items: center;
            position: relative;
        }

        .newhelpSteps {
            display: flex;
            flex-direction: row;
            justify-content: space-around;
            padding: 20px;
            box-sizing: border-box;
            align-items: center;
        }

            .newhelpSteps .newhelpSteps-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                position: relative;
            }

                .newhelpSteps .newhelpSteps-item .newhelpSteps-item-up {
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                }

                .newhelpSteps .newhelpSteps-item .newhelpSteps-item-title {
                    font-size: 14px;
                    color: #d1d5da;
                    margin-right: 5px;
                }

                .newhelpSteps .newhelpSteps-item .newhelpSteps-item-mid-btn {
                    min-width: 105px;
                    box-sizing: border-box;
                    height: 25px;
                    background-color: #3aadff;
                    cursor: pointer;
                    color: #fff;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    padding: 0 8px;
                    margin: 8px 0;
                    background-color: #fff;
                    border: 1px solid #3aadff;
                    border-radius: 3px;
                    color: #3aadff;
                    height: 35px;
                    min-width: 80px;
                }

                    .newhelpSteps .newhelpSteps-item .newhelpSteps-item-mid-btn:hover {
                        opacity: 0.8;
                    }

                .newhelpSteps .newhelpSteps-item .newhelpSteps-item-down {
                    display: flex;
                    flex-direction: column;
                    color: #888;
                }

            .newhelpSteps .newhelpSteps-active {
                width: 120px;
                height: 17px;
                background-image: url(/Content/images/generalizeIndex-icons.png);
                background-position: 0px -38px;
            }

        .newhelpSteps-item-title-btn {
            border: 1px solid #3aadff;
            border-radius: 2px;
            color: #3aadff;
            height: 24px;
            min-width: 60px;
            margin-left: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
        }

        .setWrapContent-title .setWrapContent-title-fText {
            /* font-size: 14px; */
            /* color: #385067; */
            font-size: 16px;
            color: rgba(0, 0, 0, 0.9);
        }

        .setWrapContent-main {
            /* padding: 20px; */
            padding: 16px;
            display: flex;
            flex-direction: row;
        }

            .setWrapContent-main label {
                display: flex;
                align-items: center;
                margin-right: 25px;
            }

                .setWrapContent-main label input {
                    margin-right: 3px;
                }

        .setWrapContent-main {
            flex-direction: column;
        }

        .setWrapContent-main-item {
            /* padding: 10px 0; */
        }

            .setWrapContent-main-item .setWrapContent-main-item-left {
                /* width: 64px; */
                width: 85px;
                text-align: right;
                display: inline-block;
                padding-right: 5px;
            }

            .setWrapContent-main-item .setWrapContent-main-item-right {
                /* color: #142535; */
            }

        .tarTxt {
            padding: 3px 5px;
        }

        .stopOpenBtn {
            background-color: #f5f5f5;
            border: 1px solid #e2e2e2;
            color: #888;
        }

            .stopOpenBtn:hover {
                background-color: #f5f5f5;
                border: 1px solid #e2e2e2;
                color: #888;
                cursor: pointer;
            }

            .stopOpenBtn i {
                margin-left: 10px;
            }

        .setWrapMain-imgWrap {
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #feb3c7;
        }

            .setWrapMain-imgWrap img {
                width: 1200px;
                height: 180px;
            }

        .setWrapContent-main-item02 {
            /* padding: 15px 5px; */
            padding: 8px 16px;
            background-color: #f5f5f5;
        }

        .afterSalesWrap-stepsWrap {
            background-color: #f5f5f5;
            padding: 13px;
            display: flex;
            flex-direction: column;
            margin-bottom: 15px;
        }

        .afterSalesWrap-stepsWrap-title {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            margin-bottom: 10px;
            width: 1100px;
        }

        .afterSalesWrap-stepsWrap-ul {
            display: flex;
        }

            .afterSalesWrap-stepsWrap-ul .afterSalesWrap-stepsWrap-ul-item {
                display: flex;
                align-items: center;
                margin-right: 15px;
            }

                .afterSalesWrap-stepsWrap-ul .afterSalesWrap-stepsWrap-ul-item .afterSalesWrap-stepsWrap-ul-item-num {
                    width: 25px;
                    height: 25px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border: 1px solid #97979a;
                    border-radius: 13px;
                    color: #666;
                    box-sizing: border-box;
                    font-size: 14px;
                }

                .afterSalesWrap-stepsWrap-ul .afterSalesWrap-stepsWrap-ul-item .afterSalesWrap-stepsWrap-ul-item-title {
                    font-size: 12px;
                    margin: 0 5px;
                }

                .afterSalesWrap-stepsWrap-ul .afterSalesWrap-stepsWrap-ul-item .afterSalesWrap-stepsWrap-ul-item-line {
                    width: 120px;
                    height: 1px;
                    background-color: #c0bfc4;
                }

        .setWrapMain-imgWrap {
            position: relative;
        }

        .setWrapMain-imgWrap-title {
            position: absolute;
            color: #fff;
            display: flex;
            flex-direction: column;
            font-size: 26px;
            font-weight: 700;
            width: 920px;
            height: 100%;
            justify-content: center;
            font-style: italic;
            letter-spacing: 3px;
        }

            .setWrapMain-imgWrap-title > span:nth-child(1) {
                padding-left: 50px;
                margin-bottom: 10px;
            }

            .setWrapMain-imgWrap-title > span:nth-child(2) {
                padding-left: 250px;
            }
        .layui-bodys {
            margin-top: 75px;
        }

        .newVersionsLeftNav .layui-bodys {
            margin-top: 65px;
        }

        .newVersionsLeftNav.hasTopBanner .layui-bodys {
            margin-top: 50px;
        }
        .videBtn {
            height: 35px;
            padding: 0 15px;
            color: #fff;
            background-color: #1966ff;
            width: 160px;
            border-radius: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            cursor: pointer;
            margin-right:25px;
        }
        .videBtn:hover {
            opacity:0.8;
            color:#fff;
        }
        .videBtnWrape {
            display: flex;
            flex-direction: row;
            align-items: center;
        }
        .videBtnWrape .icon-bofang {
            margin-right:3px;
        }
        .aliDistributionIntroduce-steps {
            flex:1;
        }

        .tip {
            /* width: 440px; */
            box-sizing: border-box;
            padding: 25px;
            display: flex;
            flex-direction: column;
            font-size: 16px;
        }

            .tip .tip {
                line-height: 30px;
            }

                .tip .tip i {
                    color: #000;
                }
    .transaction-set-main .tab-control {
        width: 4px;
        height: 16px;
        background: rgba(8, 136, 255, 1);
        margin-right: 8px;
    }  
    .transaction-set-main .aliDistributionIntroduce-steps-item-up .steps-num {
        background-color: rgba(8, 136, 255, 0.08);
    }  
    .transaction-set-main .aliDistributionIntroduce-steps-item-up .steps-title {
        color: rgba(0, 0, 0, 0.9);
    }      
    .transaction-set-main .aliDistributionIntroduce-steps-line {
         border-top: 1px dashed #0888FF;
         margin: 10px 16px;
    } 
    </style>
}
@{Html.RenderPartial("~/Views/Common/SupplySet1688Nav.cshtml");}

<div class="setWrapMain setWrapMain-imgWrap wu-8radius">
    @*<div class="setWrapMain-imgWrap-title">
        <span>采购金支付快人一步</span>
        <span>分销付款推荐首选</span>
    </div>*@
    <a href="https://anxingou.1688.com/page/BANK_CLOUD_PAY/apply?role=PAYER&tracelog=dingqun" target="_blank">
        <img src="/Content/images/noviceIntroPic/cgj2023-12-22-1200px180px.png" alt="">
    </a>
</div>
<div class="wu-8radius wu-m16 wu-p16 wu-color-n wu-background transaction-set-main" >
    <div class="setWrapMain wu-mL0 wu-mT0 wu-mR0 wu-mB16" style="border-bottom: 1px solid rgba(0, 0, 0, 0.1);">
        <div class="setWrapContent-title wu-m0 wu-p0">
            <div class="tab-control"></div>
            <span class="setWrapContent-title-fText wu-f16 wu-c09 wu-weight600 wu-mR8">线上推单付款流程</span>
            
            <a class="wu-color-a wu-operate wu-f14" href="https://www.yuque.com/xiangying-len/zhy7ft/zobei25w1g3sxg6c?singleDoc#" target="_blank"><i class="iconfont icon-a-play-circle-stroke1x wu-mR4 wu-color-a"></i>查看视频教程</a>
        </div>
        <div class="setWrapContent-main videBtnWrape">
        <!-- <a class="videBtn" href="https://www.yuque.com/xiangying-len/zhy7ft/zobei25w1g3sxg6c?singleDoc#" target="_blank">
            <span class="iconfont icon-bofang"></span>
            <span class="videBtn-title">查看操作视频教程</span>
        </a> -->
            <a  href="https://www.yuque.com/xiangying-len/zhy7ft/zobei25w1g3sxg6c?singleDoc#" target="_blank"><div class="wu-btn wu-btn-mid wu-mR16"><i class="iconfont icon-a-play-circle-stroke1x wu-mR4 wu-mT2"></i><span class="">查看视频教程</span></div></a>
            <ul class="aliDistributionIntroduce-steps">
            <li class="aliDistributionIntroduce-steps-item">
                <div class="aliDistributionIntroduce-steps-item-up">
                    <!-- <span class="steps-num iconfont icon-dagou" style="font-size: 12px;"></span> -->
                    <span class="steps-num" style="background-color: rgba(8, 136, 255, 1); color:#fff">1</span>
                    <span class="steps-title wu-f14 wu-weight600">厂家开启担保交易设置</span>
                </div>
            </li>
            <li class="aliDistributionIntroduce-steps-line"></li>
            <li class="aliDistributionIntroduce-steps-item">
                <div class="aliDistributionIntroduce-steps-item-up">
                    <span class="steps-num" style="background-color: rgba(8, 136, 255, 1); color:#fff">2</span>
                    <span class="steps-title wu-f14 wu-weight600">授权1688/淘宝买家账号</span>
                </div>
            </li>
            <li class="aliDistributionIntroduce-steps-line"></li>
            <li class="aliDistributionIntroduce-steps-item">
                <div class="aliDistributionIntroduce-steps-item-up">
                    <span class="steps-num" style="background-color: rgba(8, 136, 255, 1); color:#fff">3</span>
                    <span class="steps-title wu-f14 wu-weight600">—键下单支付货款，即可推单成功</span>
                    
                </div>
            </li>
            </ul>
        <!-- <a href="/Common/Page/NewOrder-AliIncludePayOrder" target="_blank" class="layui-btn layui-btn-primary layui-btn35" style=" margin-left:25px; height: 30px; line-height: 28px; color: #1966ff;">立即推单付款</a> -->
            <div class="wu-btn wu-btn-mid wu-primary wu-two wu-mL16"><a href="/Common/Page/NewOrder-AliIncludePayOrder" target="_blank" class="wu-color-a wu-operate wu-f14" >立即推单付款</a></div>
        </div>
    </div>

    <div class="setWrapMain buyerinfo wu-mL0 wu-mT0 wu-mR0 wu-mB16 " style="border-bottom: 1px solid rgba(0, 0, 0, 0.1);">
    <div class="setWrapContent-title wu-m0 wu-p0">
        <div class="tab-control"></div>
        <span class="setWrapContent-title-fText wu-f16 wu-c09 wu-weight600 wu-mR8">采购单支付方式设置</span>
        <a class="wu-color-a wu-operate wu-f14" href="/FundsManagement/TransactionDetailSupplier" >支付交易记录</a>
    </div>
    <div class="setWrapContent-main">

        <div class="setWrapContent-main-item" style="display:flex;justify-content:flex-start; flex-direction: column;">
            <label class="wu-radioWrap wu-hover wu-mB16">
                <input type="radio" name="PasswordFreePaySet" value="1" @(ViewBag.PasswordFreePaySet == "1" ? "checked" : string.Empty) />
                <span class="wu-f14 wu-c09 wu-mL8">优先手动支付</span>
            </label>
            <label class="wu-radioWrap wu-hover wu-mB16">
                <input type="radio" name="PasswordFreePaySet" value="2" @(ViewBag.PasswordFreePaySet == "2" ? "checked" : string.Empty) />
                <span class="wu-f14 wu-c09 wu-mL8">优先免密代扣，失败时使用手动支付</span>
                <span class="tarTxt tarTxt07 PasswordFreePaySetUseDefault" style="margin-left: 5px; @(ViewBag.PayTypeUseDefault != true ? "display:none;" : string.Empty)">默认</span>
            </label>
            <label class="wu-radioWrap wu-hover wu-mB16">
                <input type="radio" name="PasswordFreePaySet" value="3" @(ViewBag.PasswordFreePaySet == "3" ? "checked" : string.Empty)>
                <span class="wu-f14 wu-c09 wu-mL8">优先使用采购金支付（大批量付款支付成功率高，速度快）</span>
            </label>
        </div>
        <div class="setWrapContent-main-item02 wu-6radius">
            <span class="wu-color-b">厂家未开通网商支付时，会导致您使用网商支付时的付款失败！请复制链接邀请对方开通！</span>
            <a href="/Partner/MySupplier" target="my-iframe" class="layui-btn layui-btn-sm layui-btn-primary wu-btn wu-btn-mid wu-primary" style="width: 142px;margin-right: 10px;">查看厂家是否开通</a>
            <a  class="layui-btn layui-btn-sm  layui-border-blue wu-btn wu-btn-mid wu-primary wu-two" onclick="commonModule.CopyText('#copyInviteUrl', '复制成功!');">复制链接邀请厂家开通</a>
            <span id="copyInviteUrl" style="display: none">https://anxingou.1688.com/page/BANK_CLOUD_PAY/apply?role=PAYEE&tracelog=dingqun</span>
        </div>
    </div>
    </div>

    <div class="setWrapMain wu-mL0 wu-mT0 wu-mR0 wu-mB16 " style="border-bottom: 1px solid rgba(0, 0, 0, 0.1);">
    <div class="setWrapContent-title wu-m0 wu-p0">
        <div class="tab-control"></div>
        <span class="setWrapContent-title-fText wu-f16 wu-c09 wu-weight600 wu-mR8">买家账号设置<i class="iconfont icon-a-info-circle-filled1x wu-color-c wu-weight400 wu-mL8 wu-mR4 "></i><span class=" wu-weight400 wu-f14 wu-color-c">需授权买家账号1688买家账号/淘宝账号后才能进行下单操作</span></span>
    </div>

    @*空状态展示授权买家账号*@
    <div class="setWrapContent-main buyerinfo buyerinfo-null" style="display:none">
        <span class="layui-btn layui-btn-normal layui-btn35 wu-btn wu-two wu-btn-mid" style="background-color: #ff7e3e; width: 180px" onclick="openAuthUrl()">添加买家账号</span>
    </div>


    <div class="setWrapContent-main buyerinfo" style="display:none">
        <div class="setWrapContent-main-item wu-mB16">
            <span class="setWrapContent-main-item-left wu-c07 wu-f14">已授权账号 :</span>
            <span class="setWrapContent-main-item-right auth-nickname wu-f14 wu-c09 wu-mL8 wu-mR8">小姐泡芙</span>
            <span class="setWrapContent-main-item-right buyerinfo-unbind wu-f14 wu-color-b" style="cursor:pointer" onclick="unbindBuyerShopRelation()">解绑</span>
        </div>
        <div class="setWrapContent-main-item wu-mB16">
            <span class="setWrapContent-main-item-left wu-c07 wu-f14">到期时间 :</span>
            <span class="setWrapContent-main-item-right auth-time wu-f14 wu-c09 wu-mL8">-</span>
        </div>
        <div class="setWrapContent-main-item wu-flex wu-yCenter">
            <span class="setWrapContent-main-item-left wu-c07 wu-f14">授权状态 :</span>
            <!-- <span class="tarTxt tarTxt03 auth-no">授权已失效</span>
            <span class="tarTxt tarTxt02  auth-ok ">授权成功</span> -->
            <span class="  auth-no wu-badge wu-error wu-mL8">
				<i class="wu-badge-dot"></i>
				<s class="wu-badge-title wu-f14">授权已失效</s>
			</span>
            <span class="   auth-ok wu-badge wu-success wu-mL8">
				<i class="wu-badge-dot"></i>
				<s class="wu-badge-title wu-f14">授权成功</s>
			</span>
            <i class="hover  auth-no wu-btn wu-btn-small" onclick="openAuthUrl()" style="margin-left: 15px;">重新授权</i>
        </div>
    </div>
    </div>


    <div class="setWrapMain buyerinfo wu-mL0 wu-mT0 wu-mR0 wu-mB16" style="border-bottom: 1px solid rgba(0, 0, 0, 0.1);">
    <div class="setWrapContent-title wu-m0 wu-p0">
        <div class="tab-control"></div>
        <span class="setWrapContent-title-fText wu-f16 wu-c09 wu-weight600 wu-mR8">免密代扣授权设置</span>
        <div class="warn-wrap" style="background-color: unset; color: #333; padding: 0; justify-content: space-between; margin: 0; margin-left: 0px; height: unset;">
            <div>
                <!-- <i class="icon">!</i> -->
                <span><i class="iconfont icon-a-info-circle-filled1x wu-color-c wu-weight400 wu-mL8 wu-mR4 "></i><span class="wu-weight400 wu-f14 wu-color-c">开启免密支付后，一定要把采购金额从银行卡转到支付宝账户余额，优先使用支付宝余额扣款，避免银行卡流水交易次数频繁，造成银行卡冻结!</span></span>
            </div>
        </div>
    </div>
    <div class="setWrapContent-main">
        <div class="setWrapContent-main-item passwordFreePayClose">
            <span class="layui-btn layui-btn-normal layui-btn35 wu-btn wu-btn-mid"  onclick="openPasswordFreePayTip()">开启免密支付</span>
        </div>
        <div class="setWrapContent-main-item passwordFreePayOpen">
            <span class="setWrapContent-main-item-left wu-c07 wu-f14" style="width:117px;">当前免密支付状态:</span>
            <span class="tarTxt tarTxt02  auth-ok">已开启</span>
        </div>
    </div>
    </div>


    <div class="setWrapMain wu-mL0 wu-mT0 wu-mR0 wu-mB16" style="border-bottom: 1px solid rgba(0, 0, 0, 0.1);">
    <div class="setWrapContent-title wu-m0 wu-p0">
        <div class="tab-control"></div>
        <span class="setWrapContent-title-fText wu-f16 wu-c09 wu-weight600 wu-mR8">自动取消待付款订单温馨提醒</span>
    </div>
    <div class="setWrapContent-main">
        <div style="display: flex;align-items: center;">
            <!-- <i class="iconfont icon-tongzhi sColor" style="margin-right: 5px;"></i> -->
            <i class="iconfont icon-tongzhi wu-color-c wu-mR4"></i>
            <span class="wu-c09 wu-f14" > 为保证您的买家账号授权正常，超过平台规定付款时间的待付款订单，系统将会为您自动申请取消下单，暂不支持关闭该功能。</span>
        </div>
    </div>
    </div>

</div>

@section scripts{
    <script type="text/javascript">
        var buyerAuthInfo = @(Html.Raw(ViewBag.BuyerAuthInfo ?? "null"));
        var passwordFreePaySet = @(Html.Raw(ViewBag.PasswordFreePaySet ?? "null"));
        $(function () {
            if (buyerAuthInfo.IsOk == false) {
                $('.buyerinfo').hide();
                $('.buyerinfo-null').show();
            }
            else {
                $('.buyerinfo').show();
                $('.buyerinfo-null').hide();
                $('.auth-nickname').text(buyerAuthInfo.NickName);
                $('.auth-time').text(buyerAuthInfo.ExpireTime);
                if (buyerAuthInfo.IsAuthExpired) {
                    $('.auth-no').show();
                    $('.auth-ok').hide();
                } else {
                    $('.auth-ok').show();
                    $('.auth-no').hide();
                }
            }
            if (buyerAuthInfo.IsOpenPasswordFreePay) {
                $(".passwordFreePayClose").hide();
                $(".passwordFreePayOpen").show();
            }
            else {
                $(".passwordFreePayOpen").hide();
                $(".passwordFreePayClose").show();
            }
            if (passwordFreePaySet == "3") {
                $(".setWrapContent-main-item02").show();
            } else {
                $(".setWrapContent-main-item02").hide();
            }

            //免密支付开启
            $("input[name='PasswordFreePaySet']").click(function () {
                var val = $(this).val();
                $(".PasswordFreePaySetUseDefault").hide();
                aliDistributionModule.saveSet("PasswordFreePaySet", $(this).val());
                if (val != "3") $(".setWrapContent-main-item02").hide();
                else $(".setWrapContent-main-item02").show();
            });
        });

        var openAuthUrl = function () {
            if (buyerAuthInfo != null) {
                commonModule.OpenNewTab(buyerAuthInfo.AuthUrl);
                var isAddOk = layer.confirm('是否授权成功？', { icon: 3, title: '授权结果确认', skin: 'wu-dailog', btn: ['确定', '取消'] },
                    function () {
                        location.href = location.href;
                        layer.closeAll();
                    }, function () {
                        layer.close(isAddOk);
                    }
                );
            }
        };

        //解绑买家账号
        var unbindBuyerShopRelation = function () {
            //检查
            commonModule.Ajax({
                url: "/SupplySet1688/CheckBuyerPurchaseOrder",
                type: "POST",
                success: function (rsp) {
                    if (rsp.Success) {
                        var html = '<div class="tip">';
                        html += '<div class="tip-item">担保交易下单功能只有授权了买家账号1688账号/淘宝账号才可以使用，确定要解除绑定吗？</div>';
                        html += '</div>';
                        layer.open({
                            type: 1,
                            title: '解绑提示', //不显示标题
                            content: html,
                            area: '450px', //宽高
                            skin: 'wu-dailog',
                            btn: ['确认', '取消'],
                            btn1: function () {
                                commonModule.Ajax({
                                    url: '/SupplySet1688/UnbindBuyerShopRelation',
                                    loading: false,
                                    success: function (rsp) {
                                        if (rsp.Success) {
                                            $('.buyerinfo').hide();
                                            $('.buyerinfo-null').show();
                                            buyerAuthInfo.IsOk = false;
                                            buyerAuthInfo.IsOpenPasswordFreePay = false;
                                            layer.closeAll();
                                            layer.msg("解绑成功"); 
                                        }
                                        else {
                                            layer.msg(rsp.Message);
                                        }
                                    }
                                });
                            },
                            btn2: function () {
                                layer.closeAll();
                            },
                        });
                    } else {
                        var html = '<div class="tip">';
                        html += '<div class="tip-item">您当前有已下单未付款的采购单，请先取消掉该种订单后再进行解绑操作。</div>';
                        html += '<div class="tip-item"><br/>各平台已下单未付款订单数量：</div>';
                        html += '<div class="tip-item">' + rsp.Message + '</div>';
                        html += '</div>';
                        layer.open({
                            type: 1,
                            title: '解绑提示', //不显示标题
                            content: html,
                            area: '450px', //宽高
                            skin: 'wu-dailog',
                            btn: ['返回'],
                            btn1: function () {
                                layer.closeAll();
                            },
                        });
                    }
                }
            });
        }

        //打开免密支付
        function openPasswordFreePayUrl() {
            if (buyerAuthInfo != null) {
                commonModule.OpenNewTab(buyerAuthInfo.OpenPasswordFreePayUrl);
                var isAddOk = layer.confirm('<i class="iconfont icon-a-info-circle-filled1x wu-color-a wu-mR8"></i><span class="wu-weight500">是否已开启免密支付？</span>', { area: ['320px','113px'], skin: 'wu-dailog', btn: ['已开启', '未开启'], success: function(layero) {
                    layero.find(".layui-layer-title").hide();
                    layero.find(".layui-layer-content").css({'padding': '28px 16px 16px 16px', 'min-height': '25px'})
                } },
                    function () {
                        location.href = location.href;
                        layer.closeAll();
                    }, function () {
                        location.href = location.href;
                        layer.close(isAddOk);
                    }
                );
            }
        }
        //开启免密支付提醒
        function openPasswordFreePayTip() {
            var num = 10;
            var timer = null;
            // var html = '<div class="checkMigrateDailog" style="width:600px;padding-bottom: 20px;">';
            var html = '<div class="checkMigrateDailog" style="width: unset;padding: 0px;">';
            html += '<div class="checkMigrate-main">';
            html += '<div class="checkMigrate-main-text"  style="padding:0px;max-height:unset; overflow-y: unset; line-height: unset;">';
            html +=
                '<span style="font-size: 14px;color: rgba(0, 0, 0, 0.9);margin-bottom: 12px; line-height: unset;">1.为保证授权成功，请注意登录信息需为已授权的买家账号<i class="wu-color-c">' + buyerAuthInfo.NickName +'</i></span>';
            html +=
                '<span style="font-size: 14px;color: rgba(0, 0, 0, 0.9);margin-bottom: 28px; line-height: unset;">2.一定要把采购金额从银行卡转到支付宝账户余额，优先使用支付宝余额扣款，避免多笔支付银行卡流水交易频繁，造成银行卡冻结!!</span>';
            html +=
                '<div class="checkMigrate-main-btns wu-mT0" style="justify-content: flex-end;"><span  class="layui-btn  layui-btn35 stopOpenBtn wu-6radius" id="withoutCodeSureOpen">我已确认，前往开启<i id="withoutCodeNum">10S</i></span></div>';
            html += '</div>'
            html += '</div>'
            html += '</div>'
            layer.open({
                type: 1,
                title: '开启免密支付提醒', //不显示标题
                content: html,
                area: '480px', //宽高
                // skin: 'adialog-Shops-skin',
                skin: 'wu-dailog',
                btn: false,
                success: function () {
                    num = 10;
                    timer = setInterval(function () {
                        num = num - 1;
                        if (num < 0) {
                            $("#withoutCodeSureOpen").removeClass('stopOpenBtn');
                            $("#withoutCodeSureOpen").addClass('wu-btn wu-btn-mid');
                            $("#withoutCodeSureOpen #withoutCodeNum").remove();
                            $("#withoutCodeSureOpen").click(function () {
                                layer.closeAll();
                                openPasswordFreePayUrl()
                            });
                            clearTimeout(timer);
                        } else {
                            $("#withoutCodeNum").html(num + "S");
                        }
                    }, 1000)
                },
                cancel: function () {
                    clearTimeout(timer);
                }
            });
        }

        var aliDistributionModule = (function (module, common, $, layer) {
            //保存设置
            module.saveSet = function (businessSettingType, settingValue) {
                common.Ajax({
                    url: "/SupplySet1688/SaveSet",
                    type: "POST",
                    loading: true,
                    data: { businessSettingType: businessSettingType, settingValue: settingValue },
                    success: function (rsp) {
                        if (commonModule.IsError(rsp)) {
                            return;
                        }
                    }
                });
            }
            return module;
        }(aliDistributionModule || {}, commonModule, jQuery, layer));

    </script>
}