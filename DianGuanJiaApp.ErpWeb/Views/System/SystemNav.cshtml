<style type="text/css">
    .set-layui-NavWrap .layui-tab-title li a {
        padding: 0 12px;
        color: rgba(0, 0, 0, 0.9);
    }
    .wu-layui-tab .layui-tab-title .layui-this a{
        color: #0888ff !important;
    }
    .wu-layui-tab .layui-tab-title li:hover a{
        color: #0888ff !important;
    }
    .set-layui-NavWrap .layui-tab-title li {
        padding: 0;
    }

    .layui-tab-title .layui-this a {
        color: #3aadff;
        display: inline-block;
    }

    .childrenNav {
        display: flex;
        flex-direction: row;
        display: none;
        padding: 0 16px;
    }

        .childrenNav > li {
            
            line-height: 56px;
            min-width: unset;
            font-size: 14px;
            margin-right: 8px;
            color: rgba(0, 0, 0, 0.9);
        }

            .childrenNav > li a {
                /* padding: 15px 15px 0 15px; */
                padding: 0 12px;
                font-size: 14px;
                cursor: pointer;
                display: inline-block;
            }
            .childrenNav > li a:hover {
               color: rgba(8, 136, 255, 0.8);
            }
            .childrenNav > li.active a {
                color: #0888FF;
               
            }
    .layui-mywrap.set-layui-NavWrap {
        min-width: 1300px;
        margin: 0px 24px 0px;
        border-radius: 8px 8px 0px 0px;
        box-shadow: none;
        padding: unset;
        min-height: unset;
    }
</style>

<div class="layui-mywrap set-layui-NavWrap" style="margin-top:75px;">
    <div class="layui-tab wu-layui-tab">
        <ul class="layui-tab-title">
            <li class="SystemSettings"><a href="/System/Index">账号设置</a></li>
            <li class="Qualification show_white_user_tab"><a href="/System/Qualification">编辑名片</a></li>    
        </ul>
        <ul class="childrenNav">
            <li class="Qualification"><a href="/System/Qualification">我的名片</a></li>
            <li class="Evaluates"><a href="/System/Evaluates">合作留言</a></li>
        </ul>
    </div>
</div>


@{
    <script>
        var menuTopNav = "@ViewBag.menuTopNav";
        var menuTopChilrenNav = "@ViewBag.menuTopChilrenNav";
       
        $(function () {
            var $ele = ".layui-tab-title  ." + menuTopNav;
            $(".layui-tab-title  ." + menuTopNav).addClass("layui-this");
            if (menuTopNav == "Qualification") {
                $(".childrenNav").css({ display: 'flex' });
            }
            if (menuTopNav == "SystemSettings") {
                $(".childrenNav").css({ display: 'none' });
            }
            $(".childrenNav  ." + menuTopChilrenNav).addClass("active");
            getIsWhiteUserData();
        });
        // 是否白名单用户
        function getIsWhiteUserData() {
            commonModule.ajax({
                type: 'GET',
                url: '/api/Common/GetIsWhiteUser',
                success: function (res) {
                    if (res.Success) {
                        var IsWhiteUserFlag = res.Data;
                        
                        if (IsWhiteUserFlag) {
                            $(".show_white_user_tab").hide();
                        } else {
                            $(".show_white_user_tab").show();
                        }
                    }
                }
            });
        }
    </script>
}
