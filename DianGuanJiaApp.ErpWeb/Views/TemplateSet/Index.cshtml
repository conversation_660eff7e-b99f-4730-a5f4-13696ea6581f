@{
    ViewBag.Title = "模板设置页面";
    ViewBag.MenuId = "PrintSetting";
    ViewBag.MenuTopNav = "TemplateSet";
    ViewBag.MenuActive = "OrderManagement";
}
@section Header{
    <style>
        .template_wrapper {
            margin: 10px;
            min-width: 1280px;
        }

        .template_wrapper_right {
            padding: 2px 4px;
            box-sizing: border-box;
            position: relative;
        }


        .template_wrapper_right_content {
           /* margin-top: 10px;*/
        }

            .template_wrapper_right_content > h1 {
                font-size: 14px;
                font-weight: bold;
            }

            .template_wrapper_right_content > h1 {
                font-size: 14px;
                font-weight: bold;
            }

            .template_wrapper_right_content > h1 {
                font-size: 14px;
                font-weight: bold;
            }

            .template_wrapper_right_content table {
              /*  margin-top: 10px;*/
            }

        .electronicAccount {
            display: none;
        }

            .electronicAccount > ul {
                margin-top: 10px;
            }

                .electronicAccount > ul > li {
                    padding: 10px;
                }

                    .electronicAccount > ul > li > label {
                        padding: 10px;
                        width: 120px;
                        text-align: right;
                        display: inline-block;
                    }

                    .electronicAccount > ul > li > select {
                        width: 250px;
                    }

                    .electronicAccount > ul > li > input {
                        margin-left: 10px;
                    }

        .SellerSet {
            display: none;
        }

            .SellerSet > div {
                padding: 15px;
            }

                .SellerSet > div > select {
                    width: 320px;
                    height: 22px;
                }

        #div_site_account_set {
            padding: 10px;
            width: 710px;
            box-sizing: border-box;
        }

            #div_site_account_set > span {
                color: #1295c1;
                font-size: 14px;
                display: inline-block;
                padding: 5px 0;
            }

            #div_site_account_set ul {
                display: inline;
                white-space: nowrap;
                width: 760px;
                display: flex;
                flex-wrap: wrap;
            }

                #div_site_account_set ul li {
                    background-color: #f4f4f4;
                    border: 1px solid #f4f4f4;
                    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.4);
                    border-radius: 2px;
                }

        #div_cainiao_auth_set {
            padding: 10px;
            /*width: 710px;*/
            box-sizing: border-box;
        }

            #div_cainiao_auth_set > span {
                color: #1295c1;
                font-size: 14px;
                display: inline-block;
                padding: 5px 0;
            }

            #div_cainiao_auth_set > div {
                padding: 5px 0;
                margin-bottom: 5px;
            }

        #div_cainiao_auth_account_set > span {
            display: inline-block;
            padding: 2px 12px;
            border: 1px solid #e2e2e2;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            background-color: #fff;
            margin-left: 10px;
            font-family: 宋体;
        }

            #div_cainiao_auth_account_set > span a:hover {
                color: #1295c1;
            }

        #div_cainiao_auth_set #div_cainiao_auth_account_set, #div_cainiao_auth_set #div_express_branch_set, #div_cainiao_auth_set #div_service_item_attr_set li {
            background-color: #f4f4f4;
            border: 1px solid #f4f4f4;
            box-shadow: 0 2px 2px #e2e2e2;
            border-radius: 2px;
            padding-left: 10px;
        }

        #div_cainiao_auth_set #div_service_item_attr_set > li {
            float: left;
            margin-right: 15px;
        }

        #div_cainiao_auth_set #div_cainiao_auth_account_set {
            width: 280px;
        }

        #slt_choose_waybill_branch_set {
            width: 450px;
        }

        #slt_choose_waybill_acount_set {
            width: 200px;
        }

        #div_cainiao_auth_set #div_express_branch_set {
            width: 460px;
        }

        body .commonDialog-wrap, body .commonDialog-wrap > .commonDialog-wrap-background {
            left: 200px;
            top: 40px;
        }

        .dialog-upgrade {
            left: -100px;
        }


        .layui-mywrap {
            padding: 16px;
            margin-top: 75px;
        }

        .template_wrapper {
            min-width: 1143px;
            margin: 15px;
            background-color: #fff;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
            border-radius: 2px;
            padding: 15px;
            min-height: 50px;
            margin-bottom: 70px;
        }

        .template_wrapper_right {
            padding-bottom: 10px;
        }

            .template_wrapper_right #btnAddTemplate, .template_wrapper_right #btnSetPrintContent {
                box-sizing: border-box;
                font-size: 14px;
            }

        .layui-tab-title .layui-this {
            color: #0888ff;
        }

        .inputSort {
            width: 50px;
            height: 20px;
            border: 1px solid #e2e2e2;
            padding-left: 5px;
        }

        .layui-btn-xs {
            padding: 0 10px;
        }

        .layui-this > a {
            color: #0888ff;
        }

        .layui-tab-title li {
            padding: 0;
        }

/*            .layui-tab-title li > a {
                padding: 0 15px;
            }*/

        .layui-btn-warm {
            background-color: #f29a1a;
        }

        input[type=text], input[type=password] {
            height: 30px;
            border: 1px solid #e2e2e2;
        }

        .otherPlatformSetAccount {
            border: unset;
        }

        .my-btn-common {
            padding: 4px 8px;
            background-color: #fff;
            border-radius: 4px;
            font-weight: 400;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            height: 24px;
            border: 1px solid #0888ff;
            color: #0888ff !important;
            border: 1px solid #0888ff !important;
            color: #0888ff !important;
            box-sizing: border-box;
        }
            .my-btn-common:hover {
                opacity:0.8;
            }
            .template_wrapper_right .commonbtn {
                border: none;
                height: 35px;
                box-sizing: border-box;
                font-size: 14px;
            }
        .templateBtns .iconfont {
            font-size:14px;
            margin-right:3px;
        }
        .templateBtns {
            display:flex;
            flex-direction:row;
        }
        .templateBtns button {
            display: flex;
            justify-content: center;
            align-items:center;
            margin-right:8px;
        }
        .layui-form-wrap .layui-input, .layui-form-wrap .layui-select, .layui-form-wrap .layui-textarea {
            height: 32px;
        }
        .layui-mysearch {
            display: flex;
  /*          background-color: #f8f8f8;*/
            padding: 16px 0;
        }
        .layui-input-inline {
            margin-right: 8px;
        }
        .layui-form-wrap select, .layui-form-wrap input[type=text] {
            width: 160px;
        }
        #templateList tr:nth-child(2n) {
            background-color:#f8f8f8;
        }
        .stockup_table_content tbody tr:hover {
            background-color: unset;
        }
        .selectIconWrap {
            position:relative;
        }
            .selectIconWrap img {
                position: absolute;
                top: 8px;
                right: 17px;
                z-index:1000;
            }
        .printSetNavWrape {
            margin-top: 75px;
            padding: 0;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
        }
        .wu-containerTwo {
            margin-top: 0;
            border-radius: 0;
            padding-bottom: 0;
        }
        .wu-selectWrap.wu-form-mid {
            width:160px;
        }
        .wu-dailog .addAccountList {
            width:unset;
            padding: 0;
        }

        .wu-dailog .addAccountList-title {
            line-height: unset;
            box-sizing: border-box;
            background-color: #fefce8;
            margin-bottom: 8px;
            border: unset;
            border-radius: 6px;
            background-color: rgba(0, 0, 0, 0.04);
            padding:12px 16px;
        }
            .wu-dailog .addAccountList-title > ul {
                padding: 0;
            }
        .wu-dailog .addAccountList-title-btn {
            padding-left:40px;
        }
        .wu-dailog .addAccountList-title-btn, .wu-dailog .addAccountList-title-btn {
            color: #0888ff !important;
        }

        .wu-dailog #btn_go_add {
            height: 32px;
            font-size: 14px;
            padding: 6px 12px;
            border-radius: 6px;
            background-color: #0888ff;
            border: unset;
            display: inline-flex;
            box-sizing: border-box;
        }

        .wu-dailog .otherPlatformSetAccount {
            width:unset;
            padding:0;
        }
        .wu-dailog .comomDan {
            margin-bottom: 8px;
            border: unset;
            border-radius: 6px;
            background-color: rgba(0, 0, 0, 0.04);
            padding: 12px 16px;
        }
        #templateList .wu-pintaiIcon {
            position:relative;
            top:2px;
        }
        .otherPlatformSetAccount-contentPart01-title {
            color: rgba(0, 0, 0, 0.9);
        }
        #addAccountList input[type=radio], #addAccountList input[type=checkbox] {
            width: 16px !important;
            height: 16px !important;
            border: 1px solid rgba(0, 0, 0, 0.14);
            opacity: 0.8;
            box-sizing: border-box;
        }
        .otherPlatformSetAccount-contentPart01-ul > li label, .otherPlatformSetAccount-contentPart03-ul > li>label {
            display: flex;
            align-items: center;
        }
            .otherPlatformSetAccount-contentPart03-ul > li > label input[type=checkbox] {
                margin-right:4px;
            }

        #addAccountList input[type=text] {
            height: 24px;
            font-size: 12px;
            padding: 4px 6px;
            border-radius: 4px;
            border: 1px solid rgba(0, 0, 0, 0.14);
            color: rgba(0, 0, 0, 0.9);
            box-sizing: border-box;
        }
            #addAccountList input[type=text]:hover {
                border: 1px solid #0888ff;
            }
            #addAccountList input[type=text]:focus {
                border: 1px solid #0888ff;
                box-shadow: 0px 0px 0px 2px rgba(8, 136, 255, 0.2);
            }
    </style>

}


@{Html.RenderPartial("~/Views/Common/printSetNav.cshtml");}
<div class="template_wrapper_right layui-mywrap showPage wu-containerTwo wu-container">
    <div class="templateBtns">
        <button id="btnAddTemplate" class="wu-btn wu-btn-mid"><span>添加快递模板</span></button>
        <button id="btnSetPrintContent" onclick="printContentFormatSetModule.OpenSettingWindow()" class="wu-btn wu-two wu-btn-mid ">打印商品信息设置</button>
        <button onclick="printContentFormatSetModule.showConfigurationTemplateDailog()" class="wu-btn wu-btn-mid wu-primary">快递单模板展示设置</button>
    </div>
    <div class="template_wrapper_right_content">
        <div class="layui-mysearch">
            <div class="layui-form-wrap wu-flex">
                <div class="layui-input-inline selectIconWrap wu-selectWrap wu-form-mid">
                    <img src="/Content/Images/loading.gif" width="20" height="20" style="display:none;" />
                    <select class="wu-select" name="" id="TemplateType" lay-filter="templateTypeSelect">
                        <option value="">模版类型</option>
                    </select>
                    <i class="iconfont icon-a-chevron-down1x"></i>
                </div>

                <div class="layui-input-inline selectIconWrap wu-selectWrap wu-form-mid">
                    <img src="/Content/Images/loading.gif" width="20" height="20" style="display:none;" />
                    <select class="wu-select" name="" id="ExpressCompany">
                        <option value="">快递公司</option>
                    </select>
                    <i class="iconfont icon-a-chevron-down1x"></i>
                </div>

                <div class="layui-input-inline wu-inputWrap wu-form-mid">
                    <input class="layui-input wu-input" type="text" id="BranchNameInput" placeholder="输入搜索快递网点名称" />
                    <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
                </div>
                <div class="layui-input-inline wu-inputWrap wu-form-mid">
                    <input class="layui-input wu-input" type="text" id="TemplateNameInput" placeholder="输入搜索快递模板名称" />
                    <span class="iconfont icon-a-close-circle-filled1x" onclick="wuFormModule.clearInput(this)"></span>
                </div>
                @*查询按钮*@
                <button class="wu-btn wu-btn-mid wu-mR8" onclick="templateListModule.Search()">查询</button>
                <button type="button" class="wu-btn wu-btn-mid wu-primary" onclick="templateListModule.Reset()"> 重置</button>
                @*}*@
            </div>
        </div>
        <div class="wu-tableWrap">
            <table class="stockup_table_content wu-table-one">
                <thead>
                    <tr>
                        <!--<th style="width:50px;">序号</th>-->
                        <th style="max-width:100px;min-width:70px;">默认模板</th>
                        <th style="max-width:100px;">常用模板</th>
                        <th style="min-width:120px;">快递模板名称</th>
                        <th style="min-width:100px;">快递公司</th>
                        @*<th style="width:60px;">模板类型</th>*@
                        <th style="min-width:100px;">尺寸/偏移</th>
                        <th style="max-width:220px;">电子面单账号</th>
                        <th style="max-width:150px;">绑定打印机</th>
                        <th style="max-width:100px;">发件人</th>
                        <th style="max-width:100px;">排序</th>
                        <!--<th style="width:85px;">是否是默认模板</th>-->
                        <th style="width:130px;">操作</th>
                    </tr>
                </thead>
                <tbody id="templateList"></tbody>
            </table>
        </div>


        <div class="layui-myPage wu-commonPage wu-one" id="paging"></div>

    </div>

</div>

<div id="div_bind_printer" style="margin-top:20px;display:none;">
    <p><span id="printTip">选择打印机</span>:
       <span class="wu-selectWrap wu-form-mid" id="chooseMachine" style="margin-left: 10px;width:260px">
           <select class="wu-select" id="selPrinterName"><option value="">请选择打印机</option></select>
           <i class="iconfont icon-a-chevron-down1x"></i>
       </span>
    </p>
</div>

<div id="div_site_account_set" style="display:none;">
    <span>业务类型：</span>
    <div id="div_site_logistic_ExpType"></div>
    <span>支付方式：</span>
    <div id="div_site_logistic_PayType"></div>
    <span>增值服务：</span>
    <div id="div_site_logistic_AddService" style="padding:0"></div>
    <span>账号信息：</span>
    <div id="div_site_logistic_AccountInfo" style="padding:0"></div>
    <span id="div_site_logistic_account_id">电子面单账号：</span>
    <div id="div_site_logistic_account_set"></div>
</div>

<div id="div_cainiao_auth_set" style="display:none;">
    <span>电子面单账号：</span>
    <div id="div_cainiao_auth_account_set"></div>
    <span>快递网点：</span><div id="div_express_branch_set"></div>
    <span>快递服务项：</span><div id="div_service_item_set" style="padding:0"></div><div><ul id="div_service_item_attr_set"></ul></div>
</div>

<!--发件人设置 -->
<div class="SellerSet">
    <div class="wu-color-b" style="padding: 15px 5px 0px 15px;">注意：模板绑定的发件人优先级高于系统设置的默认发件人</div>
    <div class="wu-flex wu-center">发件人：<div class="wu-selectWrap wu-form-mid" style="width:290px"><select class="wu-select" id="sel_seller"><option value="">广东省汕头市澄海区连上镇涂城崇德南路100号</option></select><i class="iconfont icon-a-chevron-down1x"></i></div></div>
</div>

@Html.Partial("AddTemplate")

<!--打印内容设置-->
@{Html.RenderPartial("~/Views/Order/PrintContentInfoPartialView.cshtml");}
@{Html.RenderPartial("~/Views/Order/ConfigurationTemplateView.cshtml");}

<script id="tempalte_list_tmpl" type="text/x-jsrender">
    {{if dataList.length>0}}
    {{for dataList}}
    <tr id="tempalte-row-{{:Id}}">
        <!--<td>{{:#index+1}}</td>-->
        <td>
            {{if IsDefault==1}}
            <span style="color:#0888ff">默认模板</span>
            {{else}}
            <input class="wu-btn wu-btn-small" type="button" value="设为默认" onclick="templateListModule.SetDefaultTemplate('{{:Id}}')" />
            {{/if}}
        </td>
        <td>
            {{if IsShow==1}}
            <input class="wu-btn wu-two wu-btn-small" type="button" value="取消常用" onclick="templateListModule.ChangeTemplateIsShow('{{:Id}}')" />
            {{else}}
            <input class="wu-btn wu-btn-small" type="button" value="设为常用" onclick="templateListModule.ChangeTemplateIsShow('{{:Id}}')" />
            {{/if}}
        </td>
        <td>
            {{if TemplateTypeShort != 'other'}}
            <i class="wu-pintaiIcon wu-small {{:TemplateTypeShort}}" title="{{:~getPlatformTitle(TemplateTypeShort)}}"></i>
            {{/if}}
            {{if TemplateTypeShort == "XiaoHongShu"}}
            {{if  TemplateType >= 180 && TemplateType < 190}}
            【新版】
            {{else TemplateType >= 150 && TemplateType < 160}}
            【旧版】
            {{/if}}
            {{/if}}
            {{:TemplateName}}<br />
            <span style="color:#EA572E;">所属店铺：{{:ShopName}}</span>
        </td>
        <td>{{:ExpressCompanyName}}</td>
        @*<td>
                {{if TemplateType == '1'}}
                传统模板
                {{else TemplateType == '3'}}
                网点模板
                {{else}}
                菜鸟模板
                {{/if}}
            </td>*@
        <td>
            宽：{{:PicWidth}}<br /> 高：{{:PicHeight}}<br />
            左：{{:ContentWidth}}<br /> 上：{{:ContentHeight}}
        </td>
        <td>
            {{if TemplateType==1}}
            {{else (TemplateType>80 && TemplateType<90) || (TemplateType>=91 && TemplateType< 96)}}
            {{if CustomerName}}
            客户编码：{{:CustomerName?CustomerName:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if WareHouseID}}
            发货仓库：{{:WareHouseID?WareHouseID:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if SendSite}}
            商家地址：{{:SendSite?SendSite:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if ExpTypeName}}
            业务类型：{{:ExpTypeName?ExpTypeName:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if AuthAccountName}}
            授权账号：{{:AuthAccountName}}
            {{if AuthAccountFromShare}}
            <label style="color: #EA572E;">({{:AuthAccountFromShare}})</label>
            {{/if}}
            <br />
            {{else}}
            授权账号：<label style="color: #EA572E;">未设置</label><br />
            {{/if}}
            <input class="wu-btn wu-btn-small" type="button" value="账号设置" onclick="templateListModule.OpenAccountSetWin('{{:Id}}')" />
            {{else TemplateType==3}}
            {{if CustomerName}}
            账号：{{:CustomerName?CustomerName:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if CustomerPwd}}
            密码：{{:CustomerPwd?"******":'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if MonthCode}}
            月结号：{{:MonthCode?MonthCode:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if PayTypeName}}
            支付方式：{{:PayTypeName?PayTypeName:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if ExpTypeName}}
            业务类型：{{:ExpTypeName?ExpTypeName:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if SendSite}}
            网点：{{:SendSite?SendSite:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if ExpressCompanyCode=="ZTO" || ExpressCompanyCode=="STO" || ExpressCompanyCode=="YTO" || ExpressCompanyCode=="HTKY" || ExpressCompanyCode=="JT"}}
            <span>单号余额：</span>
            <strong>
                <span id="sp_usable_count_{{:Id}}">
                    <span class="hover dColor" onclick="templateListModule.LoadBranchInfo.bind(this)('{{:Id}}','{{:CaiNiaoAuthInfoId}}','{{:BranchCode}}','{{:AuthSourceType}}','{{:BranchShareRelationId}}','{{:ExpressCompanyCode}}','{{:BrandCode}}')">查看余额</span>
                    <img src="/Content/Images/loading.gif" width="20" height="20" style="display:none" />
                </span>
            </strong><br />
            {{/if}}
            {{if OldId!=1&&OldId!=2}}
            <input class="wu-btn wu-btn-small" type="button" value="账号设置" onclick="templateListModule.OpenAccountSetWin('{{:Id}}')" />
            {{/if}}
            {{else TemplateType==10|| TemplateType==13}}
            {{if CustomerName}}
            顾客编码：{{:CustomerName?CustomerName:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if CustomerPwd}}
            校验码：{{:CustomerPwd?CustomerPwd:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if MonthCode}}
            月结号：{{:MonthCode?MonthCode:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if PayTypeName}}
            支付方式：{{:PayTypeName?PayTypeName:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if ExpTypeName}}
            业务类型：{{:ExpTypeName?ExpTypeName:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            {{if SendSite}}
            网点：{{:SendSite?SendSite:'<label style="color:#EA572E;">未设置</label>'}}<br />
            {{/if}}
            <input class="wu-btn wu-btn-small" type="button" value="账号设置" onclick="templateListModule.OpenAccountSetWin('{{:Id}}')" />
            {{else}}
            {{if AuthAccountName}}
            授权账号：{{:AuthAccountName}}
            {{if AuthAccountFromShare}}
            <label style="color: #EA572E;">({{:AuthAccountFromShare}})</label>
            {{/if}}
            <br />
            {{else}}
            授权账号：<label style="color: #EA572E;">未设置</label><br />
            {{/if}}
            网点名称：{{:BranchName?BranchName:(BranchAddress?BranchName:'<label style="color:#EA572E;">未设置</label>')}}<br />
            网点地址：{{:BranchAddress?BranchAddress:'<label style="color:#EA572E;">未设置</label>'}}<br />
            <span>单号余额：</span>
            <span id="sp_usable_count_{{:Id}}">
                {{if BranchAddress }}
                <span class="hover dColor" onclick="templateListModule.LoadBranchInfo.bind(this)('{{:Id}}','{{:CaiNiaoAuthInfoId}}','{{:BranchCode}}','{{:AuthSourceType}}','{{:BranchShareRelationId}}','{{:ExpressCompanyCode}}','{{:BrandCode}}')">查看余额</span>
                <img src="/Content/Images/loading.gif" width="20" height="20" style="display:none" />
                {{else}}
                {{/if}}
            </span>
            <br />
            <input class="wu-btn wu-btn-small" type="button" value="账号设置" onclick="templateListModule.OpenAccountSetWin('{{:Id}}')" />
            {{/if}}
        </td>
        <td>
            {{if OldId!=1&&OldId!=2}}
            <div id="sp_default_printer_{{:Id}}">{{:DefaultPrinter.PrinterName}}</div>
            <input class="wu-btn wu-btn-small" type="button" value="绑定打印机" onclick="templateListModule.BindPrinter('{{:Id}}','{{:TemplateType}}')" />
            {{/if}}
        </td>
        <td>
            {{if OldId!=1&&OldId!=2}}
            {{if SenderId>0}}
            <div>
                {{:SellerName}},{{:SellerMobile?SellerMobile:SellerPhone}},{{:SellerProvince}}{{:SellerProvince}}{{:SellerCity}}{{:SellerArea}}{{:SellerAddress}}
            </div>
            {{/if}}
            <input class="wu-btn wu-btn-small wu-primary wu-two" style="line-height: 12px;" type="button" value="绑定发件人" onclick="templateListModule.SellerSet('{{:Id}}')" />
            {{/if}}
        </td>
        <td>

            <div class="wu-inputWrap wu-form-mid">
                <input type="text" value="{{:Sort}}" class="inputSort wu-input"
                       data-tid="{{:Id}}"
                       onfocus="templateListModule.TemplateSortForcusHandle.bind(this)();"
                       onblur="templateListModule.ChangeTemplateSort.bind(this)();" />
            </div>




        </td>
        <td>
            {{if !EwaybillTemplateId }}
            <span class="hover dColor" style="margin-right:15px;" onclick="templateListModule.EditTemplate('{{:TemplateType}}','{{:Id}}')">编辑</span>
            @*<input class="layui-btn layui-btn-xs layui-btn-normal" type="button" value="编辑" onclick="templateListModule.EditTemplate('{{:TemplateType}}','{{:Id}}')" />*@
            {{/if}}
            <span class="hover tColor" onclick="templateListModule.DeleteTemplate('{{:Id}}')">删除</span>
            @*<input class="layui-btn layui-btn-xs layui-btn-danger" type="button" value="删除" onclick="templateListModule.DeleteTemplate('{{:Id}}')" />*@
        </td>
    </tr>
    {{/for}}
    {{else}}
    <tr>
        <td colspan="20" class="tdNodata"><div class="tableNoDataShow"><span class="tableNoDataShow-title">暂无数据</span></div></td>
    </tr>
    {{/if}}

</script>

<div class="layui-mywrap noPermission" style="margin-top:15px;display:none">
    <span style="display:flex;font-size:15px;justify-content:center;padding-top:0.5%">暂无权限，请联系管理员</span>
</div>
@section scripts{
    <script type="text/javascript">
        var hasPermission =  @Html.Raw(ViewBag.ViewPermission ?? "false");
        if (hasPermission == false) {
            $(".showPage").hide();
            $(".noPermission").show();
        } else {
            $(".showPage").show();
            $(".noPermission").hide();
        }
        var keywordFilterReplaceModule = null;

    </script>
    <script src="~/Scripts/KuaiShouPrinter.js"></script>
    <script src="~/Scripts/selectbox/funSelectbox.js"></script>
    @Scripts.Render("~/bundles/templateset")
    <script type="text/javascript">
        var hasPermission =  @Html.Raw(ViewBag.ViewPermission ?? "false");

        $(function () {

            if (hasPermission) {
                //加载打印机bind信息
                commonModule.LoadPrinterBind();

                addTemplateModule.Initialize('btnAddTemplate', function () {
                    templateListModule.LoadTemplateList();
                });

                templateListModule.eventListenSelect();

                templateListModule.LoadTemplateList();

                //if (lodopPrinter.check(true)) {
                //    var printerList = lodopPrinter.printers();
                //}

                //if (caiNiaoPrinter.check(true)) {
                //    var printerList = caiNiaoPrinter.printers;
                //}

                function AccountSettings() {
                }
            }
            

            //判断url中是否包含option，且值为：OpenSettingWindow，则打开打印信息设置弹窗
            var url = window.location.search;
            var params = new URLSearchParams(url);
            var option = params.get("option");
            if (option && option == "OpenSettingWindow") {
                printContentFormatSetModule.OpenSettingWindow();
            }

            wuFormModule.initSelect('.layui-mysearch');	//wu-searchWrap为父元素类名    针对原生select
            wuFormModule.initblurInput('.layui-mysearch');//wu-searchWrap为父元素类名    针对input输入框

        });

        $.views.helpers({
            getPlatformTitle: function (platform) {
                return showPlatformTitle(platform);//获取中文平台名称
            },
        });
    </script>
}

