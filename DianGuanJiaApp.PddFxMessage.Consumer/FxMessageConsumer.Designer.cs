namespace DianGuanJiaApp.PddFxMessage.Consumer
{
    partial class FxMessageConsumer
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FxMessageConsumer));
            this.startBtn = new System.Windows.Forms.Button();
            this.stopBtn = new System.Windows.Forms.Button();
            this.DebugModeChk = new System.Windows.Forms.CheckBox();
            this.groupBoxOfSetting = new System.Windows.Forms.GroupBox();
            this.textThreadCount = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.LogList = new System.Windows.Forms.TextBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.statusTextBox = new System.Windows.Forms.RichTextBox();
            this.syncStatusLabel = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.allShopCountLabel = new System.Windows.Forms.Label();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.退出ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.notifyIcon1 = new System.Windows.Forms.NotifyIcon(this.components);
            this.groupBoxOfSetting.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // startBtn
            // 
            this.startBtn.Location = new System.Drawing.Point(246, 8);
            this.startBtn.Name = "startBtn";
            this.startBtn.Size = new System.Drawing.Size(75, 23);
            this.startBtn.TabIndex = 2;
            this.startBtn.Text = "开始";
            this.startBtn.UseVisualStyleBackColor = true;
            this.startBtn.Click += new System.EventHandler(this.startBtn_Click);
            // 
            // stopBtn
            // 
            this.stopBtn.Enabled = false;
            this.stopBtn.Location = new System.Drawing.Point(327, 8);
            this.stopBtn.Name = "stopBtn";
            this.stopBtn.Size = new System.Drawing.Size(75, 23);
            this.stopBtn.TabIndex = 3;
            this.stopBtn.Text = "停止";
            this.stopBtn.UseVisualStyleBackColor = true;
            this.stopBtn.Click += new System.EventHandler(this.stopBtn_Click);
            // 
            // DebugModeChk
            // 
            this.DebugModeChk.AutoSize = true;
            this.DebugModeChk.Location = new System.Drawing.Point(408, 12);
            this.DebugModeChk.Name = "DebugModeChk";
            this.DebugModeChk.Size = new System.Drawing.Size(96, 16);
            this.DebugModeChk.TabIndex = 4;
            this.DebugModeChk.Text = "显示调试信息";
            this.DebugModeChk.UseVisualStyleBackColor = true;
            this.DebugModeChk.CheckedChanged += new System.EventHandler(this.DebugModeChk_CheckedChanged);
            // 
            // groupBoxOfSetting
            // 
            this.groupBoxOfSetting.Controls.Add(this.textThreadCount);
            this.groupBoxOfSetting.Controls.Add(this.label1);
            this.groupBoxOfSetting.Controls.Add(this.DebugModeChk);
            this.groupBoxOfSetting.Controls.Add(this.stopBtn);
            this.groupBoxOfSetting.Controls.Add(this.startBtn);
            this.groupBoxOfSetting.Location = new System.Drawing.Point(12, 4);
            this.groupBoxOfSetting.Name = "groupBoxOfSetting";
            this.groupBoxOfSetting.Size = new System.Drawing.Size(534, 72);
            this.groupBoxOfSetting.TabIndex = 5;
            this.groupBoxOfSetting.TabStop = false;
            this.groupBoxOfSetting.Text = "设置";
            // 
            // textThreadCount
            // 
            this.textThreadCount.Location = new System.Drawing.Point(79, 28);
            this.textThreadCount.Name = "textThreadCount";
            this.textThreadCount.Size = new System.Drawing.Size(65, 21);
            this.textThreadCount.TabIndex = 6;
            this.textThreadCount.Text = "1";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(8, 32);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 5;
            this.label1.Text = "消费者数量：";
            // 
            // LogList
            // 
            this.LogList.Location = new System.Drawing.Point(13, 188);
            this.LogList.Multiline = true;
            this.LogList.Name = "LogList";
            this.LogList.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.LogList.Size = new System.Drawing.Size(533, 278);
            this.LogList.TabIndex = 6;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.statusTextBox);
            this.groupBox1.Controls.Add(this.syncStatusLabel);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Location = new System.Drawing.Point(12, 90);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(534, 81);
            this.groupBox1.TabIndex = 6;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "状态";
            // 
            // statusTextBox
            // 
            this.statusTextBox.BackColor = System.Drawing.SystemColors.ActiveCaptionText;
            this.statusTextBox.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.statusTextBox.ForeColor = System.Drawing.Color.Red;
            this.statusTextBox.Location = new System.Drawing.Point(66, 20);
            this.statusTextBox.Name = "statusTextBox";
            this.statusTextBox.Size = new System.Drawing.Size(337, 55);
            this.statusTextBox.TabIndex = 7;
            this.statusTextBox.Text = "订单消息消费OFFSET：0\n服务承诺消费OFFSET： 0";
            // 
            // syncStatusLabel
            // 
            this.syncStatusLabel.AutoSize = true;
            this.syncStatusLabel.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.syncStatusLabel.ForeColor = System.Drawing.Color.Red;
            this.syncStatusLabel.Location = new System.Drawing.Point(415, 32);
            this.syncStatusLabel.Name = "syncStatusLabel";
            this.syncStatusLabel.Size = new System.Drawing.Size(59, 16);
            this.syncStatusLabel.TabIndex = 6;
            this.syncStatusLabel.Text = "未开始";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(6, 32);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(65, 12);
            this.label5.TabIndex = 4;
            this.label5.Text = "队列信息：";
            // 
            // allShopCountLabel
            // 
            this.allShopCountLabel.AutoSize = true;
            this.allShopCountLabel.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.allShopCountLabel.Location = new System.Drawing.Point(88, 233);
            this.allShopCountLabel.Name = "allShopCountLabel";
            this.allShopCountLabel.Size = new System.Drawing.Size(0, 16);
            this.allShopCountLabel.TabIndex = 5;
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.退出ToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(101, 26);
            // 
            // 退出ToolStripMenuItem
            // 
            this.退出ToolStripMenuItem.Name = "退出ToolStripMenuItem";
            this.退出ToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
            this.退出ToolStripMenuItem.Text = "退出";
            this.退出ToolStripMenuItem.Click += new System.EventHandler(this.退出ToolStripMenuItem_Click);
            // 
            // notifyIcon1
            // 
            this.notifyIcon1.BalloonTipText = "Fx消息消费程序";
            this.notifyIcon1.BalloonTipTitle = "Fx消息消费程序";
            this.notifyIcon1.ContextMenuStrip = this.contextMenuStrip1;
            this.notifyIcon1.Icon = ((System.Drawing.Icon)(resources.GetObject("notifyIcon1.Icon")));
            this.notifyIcon1.Text = "Fx消息消费程序";
            this.notifyIcon1.Visible = true;
            this.notifyIcon1.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.notifyIcon1_MouseDoubleClick);
            // 
            // FxMessageConsumer
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(566, 482);
            this.Controls.Add(this.allShopCountLabel);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.LogList);
            this.Controls.Add(this.groupBoxOfSetting);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "FxMessageConsumer";
            this.Text = "拼多多Fx消息消费程序";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.SyncOrder_FormClosing);
            this.Load += new System.EventHandler(this.SyncOrder_Load);
            this.groupBoxOfSetting.ResumeLayout(false);
            this.groupBoxOfSetting.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.Button startBtn;
        private System.Windows.Forms.Button stopBtn;
        private System.Windows.Forms.CheckBox DebugModeChk;
        private System.Windows.Forms.GroupBox groupBoxOfSetting;
        private System.Windows.Forms.TextBox LogList;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label allShopCountLabel;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label syncStatusLabel;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem 退出ToolStripMenuItem;
        private System.Windows.Forms.NotifyIcon notifyIcon1;
        private System.Windows.Forms.TextBox textThreadCount;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.RichTextBox statusTextBox;
    }
}

