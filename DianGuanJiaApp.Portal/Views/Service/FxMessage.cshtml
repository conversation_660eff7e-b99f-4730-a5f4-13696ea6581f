@{
    ViewBag.Title = "订购完成";
    Layout = null;
}
<html>
<head>
    <meta charset="utf-8">
    <title></title>
</head>
<style type="text/css">
    body {
        padding: 0;
        margin: 0;
    }

    a,
    s,
    i,
    em {
        font-style: normal;
        text-decoration: none;
    }

    .wrap {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

        .wrap .main {
            width: 500px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .wrap .success-title {
            font-size: 30px;
            font-weight: 700;
            font-family: 微软雅黑;
            padding: 40px 0;
        }

        .wrap .content {
            font-size: 18px;
            color: #999999;
        }

            .wrap .content > span > i {
                color: #fe6f4f;
                font-weight: 700;
            }

        .wrap .btns {
            margin-top: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

            .wrap .btns > a {
                background-color: #ddd;
                height: 40px;
                width: 210px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 20px;
                margin: 0 10px;
                cursor: pointer;
                color: #fff;
                font-size: 17px;
                font-weight: 700;
                font-family: 微软雅黑;
            }

                .wrap .btns > a:hover {
                    opacity: 0.85;
                }

    .contactDailog {
        display: flex;
        justify-content: center;
        align-items: center;
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 10000;
    }

        .contactDailog .contactDailog-content {
            display: flex;
            flex-direction: column;
            background-color: #fff;
            width: 450px;
            padding: 30px;
            box-shadow: 0 0 20px 0px #ada6a6;
            border-radius: 8px;
            position: relative;
            align-items: center;
        }

            .contactDailog .contactDailog-content > img {
                width: 200px;
            }

            .contactDailog .contactDailog-content > span {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 20px;
                color: #3aadff;
                font-size: 20px;
                font-weight: 700;
            }

            .contactDailog .contactDailog-content > .closeIcon {
                position: absolute;
                width: 25px;
                height: 25px;
                top: 14px;
                right: 0;
                cursor: pointer;
            }

                .contactDailog .contactDailog-content > .closeIcon::after {
                    position: absolute;
                    top: 0;
                    left: 0;
                    content: "";
                    width: 22px;
                    height: 2px;
                    display: block;
                    background-color: #e2e2e2;
                    transform: rotate(45deg);
                }

                .contactDailog .contactDailog-content > .closeIcon::before {
                    position: absolute;
                    top: 0;
                    left: 0;
                    content: "";
                    width: 22px;
                    height: 2px;
                    display: block;
                    background-color: #e2e2e2;
                    transform: rotate(135deg);
                }
</style>
<body>
    <div class="wrap">
        <div class="main">
            <img src="https://www.dgjapp.com/public/images/dingouSuccessIcon.png" alt="">
            <span class="success-title">恭喜您已成功订购！</span>
            <div class="content">
                <span style="margin-right: 30px;">订购账号：<i>@ViewBag.Mobile</i></span>
                <span>订购版本：<i>@ViewBag.VersionName</i></span>
            </div>
            <div class="btns">
                <a href="https://tfxportal.dgjapp.com/ffaccount" target="_blank">返回系统</a>
                <a href="javascript:;" onclick="contactOpenOrClose(true)" style="background-image: linear-gradient(145deg, #ff8737, #ffaf26);">联系客服领取神秘礼包</a>
            </div>
        </div>
    </div>

    <div class="contactDailog" id="contactDailog" style="display: none;">
        <div class="contactDailog-content">
            <img src="https://www.dgjapp.com/public/images/xianyinERWEIMA.png" alt="">
            <span>请截图你的订购成功页面向客服领取神秘礼包</span>
            <i class="closeIcon" onclick="contactOpenOrClose(false)"></i>
        </div>
    </div>
</body>
</html>
<script>
    function contactOpenOrClose(isShow) {
        var ele = document.getElementById("contactDailog");
        if (isShow) {
            ele.style.display = "flex";
        } else {
            ele.style.display = "none";
        }

    }

</script>