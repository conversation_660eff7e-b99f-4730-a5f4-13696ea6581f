using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Messaging;
using System.Web;

namespace DianGuanJiaApp.Queue.Common
{
    public class AppSiteContext
    {
        public static readonly string _siteKey = "CurrentAppSiteContextKey";
        public AppSiteContext(LogisticsAppInfo app)
        {
            App = app;
            CallContext.LogicalSetData(_siteKey, this);
        }

        public AppSiteContext(string appKey)
        {
            var service = new LogisticsAppInfoService();
            var app = service.GetByAppKey(appKey);
            App = app;
            CallContext.LogicalSetData(_siteKey, this);
        }

        public LogisticsAppInfo App { get; set; }

        public static AppSiteContext Current
        {
            get
            {
                var data =  CallContext.LogicalGetData(_siteKey);
                if (data != null)
                    return data as AppSiteContext;
                return null;
            }
        }

    }
}