using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;


namespace DianGuanJiaApp.Queue.Controllers
{
    /// <summary>
    /// 商家售后关联店铺全量同步队列
    /// </summary>
    public class SyncFxAfterSaleQueueController : BaseController
    {
        /// <summary>
        /// 要同步的店铺信息
        /// </summary>
        public static ConcurrentDictionary<int, Shop> _shops = new ConcurrentDictionary<int, Shop>();

        private const SyncTaskType taskType = SyncTaskType.AfterSale;

        private static readonly string platformTypes = CustomerConfig.PlatformAfterSale;

        private const int QUEUE_MAX_SIZE = 100;

        /// <summary>
        /// 获取未曾同步过售后的店铺，并将其加入队列中
        /// </summary>
        public static void GetShopUnSynced(ShopService shopService)
        {
            if (_shops.Count > QUEUE_MAX_SIZE)
                return;

            var _shopService = shopService;
            try
            {
                var shops = _shopService.GetFxOrderSyncedList(platformTypes, CustomerConfig.CloudPlatformType, taskType, QUEUE_MAX_SIZE);
                if (shops != null && shops.Any())
                {
                    shops.ToList().ForEach(s =>
                    {
                        var shop = _shops.FirstOrDefault(x => x.Key == s.Id);
                        if (shop.Value == null)
                        {
                            _shops.TryAdd(s.Id, s);
                            Log.WriteLine($"【Producer】：新增待同步售后店铺：{s.ShopName} {s.Id}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取售后未同步的店铺时发生错误：{ex}");
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="platformType"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        public ActionResult Fetch(string platformType = "", int count = 1)
        {
            if (count <= 0)
                count = 1;
            var shops = new List<Shop>();
            if (_shops.Values.Any(s => s.SyncLockStatus != Data.Enum.ShopSyncStatusType.Syncing.ToString()))
            {
                for (var i = 0; i < count; i++)
                {
                    Shop temp = null;
                    if (string.IsNullOrEmpty(platformType) || platformType == "*")
                        temp = _shops.Values.FirstOrDefault(s => s.SyncLockStatus != Data.Enum.ShopSyncStatusType.Syncing.ToString());
                    else if (platformType.StartsWith("!"))
                        temp = _shops.Values.FirstOrDefault(s => s.SyncLockStatus != Data.Enum.ShopSyncStatusType.Syncing.ToString() && s.PlatformType != platformType);
                    else
                        temp = _shops.Values.FirstOrDefault(s => s.SyncLockStatus != Data.Enum.ShopSyncStatusType.Syncing.ToString() && platformType.Split(',').Contains(s.PlatformType));
                    if (temp != null)
                    {
                        temp.SyncLockStatus = Data.Enum.ShopSyncStatusType.Syncing.ToString();
                        temp.ProcessSyncServerIP = Request?.UserHostName;
                        var copy = temp.ToJson().ToObject<Shop>();
                        copy.SyncLockStatus = null;
                        shops.Add(copy);
                    }
                    else
                        break;
                }
            }
            return Json(shops, JsonRequestBehavior.AllowGet);
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="shop"></param>
        /// <returns></returns>
        public ActionResult Finish(Shop shop)
        {
            if (shop == null)
                return Content("error");
            Shop curShop = null;
            try
            {
                _shops.TryRemove(shop.Id, out curShop);
            }
            catch (Exception e1)
            {
                Log.WriteError($"从队列中移除售后店铺【{curShop?.ShopName}  {curShop?.Id}】时发生错误，重试一次：{e1}");
                _shops.TryRemove(shop.Id, out curShop);
            }
            return Content("ok");
        }

        /// <summary>
        /// 查看状态
        /// </summary>
        /// <returns></returns>
        public ActionResult Status()
        {
            //当前情况
            if (!CustomerConfig.IsEnabledFxShopSyncQueue)
                return Content("售后店铺同步队列未曾启用，若需启用，请在AppSettings中添加IsEnabledFxShopSyncQueue键，并设置为true");
            var sb = new StringBuilder();
            sb.AppendLine($"当前售后队列中共【{_shops.Count()}】个店铺，正在同步中的有：【{_shops.Values.Count(s => s.SyncLockStatus == "Syncing")}】个<br/>");
            if (_shops.Any())
            {
                sb.AppendLine($"店铺详情：<br/>");
                foreach (var shop in _shops.Values)
                {
                    var extMsg = string.IsNullOrEmpty(shop.SyncType) ? "" : "【补漏】";
                    sb.AppendLine($"{extMsg}售后店铺名：【{shop?.ShopName}】，ShopId：【{shop?.ShopId}】，Id：【{shop?.Id}】。同步锁定状态：【{shop?.SyncLockStatus ?? "待同步"}】，同步状态：【{shop.OrderSyncStatus.FullSyncStatus}】，重试次数：【{shop.OrderSyncStatus.FullSyncErrorTimes}】，同步程序所在服务器IP：【{shop.ProcessSyncServerIP ?? "未分配"}】<br/> ");
                }
            }
            return Content(sb.ToString());
        }

        /// <summary>
        /// 重置队列
        /// </summary>
        /// <returns></returns>
        public ActionResult Reset()
        {
            Log.WriteLine($"【{Request.UserHostAddress}】清空全量同步队列");
            _shops.Clear();
            Response.Write($"已清空队列，清空后的状态为：<br/>");
            return Status();
        }

        /// <summary>
        /// 添加一个全量同步任务
        /// </summary>
        /// <returns></returns>
        public ActionResult Push(Shop shop)
        {
            if (shop == null)
                return Content("售后店铺不能为空");
            if (_shops.Keys.Any(s => s == shop.Id))
                return Content("售后店铺已存在于同步任务中");
            _shops.TryAdd(shop.Id, shop);
            return Content("ok");
        }
    }
}