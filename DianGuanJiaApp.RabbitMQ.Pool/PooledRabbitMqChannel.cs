using System;
using System.Collections.Generic;
using CodeProject.ObjectPool;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;

namespace DianGuanJiaApp.RabbitMQ.Pool
{
    public class PooledRabbitMqChannel : PooledObject, IModel
    {
        private readonly IModel _imodel;

        public PooledRabbitMqChannel()
        {
            this._imodel = RabbitMqClientFactory.CreateModel();
            this.OnValidateObject += context => this._imodel.IsOpen;
            this.OnReleaseResources += () => {
                this._imodel.Dispose();
            };
        }
        public bool IsConnecte => this._imodel.IsOpen;

        public int ChannelNumber => this._imodel.ChannelNumber;

        public ShutdownEventArgs CloseReason => this._imodel.CloseReason;


        public bool IsClosed => this._imodel.IsClosed;

        public bool IsOpen => this._imodel.IsOpen;

        public ulong NextPublishSeqNo => this._imodel.NextPublishSeqNo;

        IBasicConsumer IModel.DefaultConsumer { get => this._imodel.DefaultConsumer; set => this._imodel.DefaultConsumer = value; }
        TimeSpan IModel.ContinuationTimeout { get => this._imodel.ContinuationTimeout; set => this._imodel.ContinuationTimeout = value; }

        public event EventHandler<BasicAckEventArgs> BasicAcks;
        public event EventHandler<BasicNackEventArgs> BasicNacks;
        public event EventHandler<EventArgs> BasicRecoverOk;
        public event EventHandler<BasicReturnEventArgs> BasicReturn;
        public event EventHandler<CallbackExceptionEventArgs> CallbackException;
        public event EventHandler<FlowControlEventArgs> FlowControl;
        public event EventHandler<ShutdownEventArgs> ModelShutdown;

        public void Abort()
        {
            this._imodel.Abort();
        }

        public void Abort(ushort replyCode, string replyText)
        {
            this._imodel.Abort();
        }

        public void BasicAck(ulong deliveryTag, bool multiple)
        {
            this._imodel.Abort();
        }

        public void BasicCancel(string consumerTag)
        {
            this._imodel.Abort();
        }

        public string BasicConsume(string queue, bool autoAck, string consumerTag, bool noLocal, bool exclusive, IDictionary<string, object> arguments, IBasicConsumer consumer)
        {
            return this._imodel.BasicConsume(queue,autoAck,consumerTag,noLocal,exclusive,arguments,consumer);
        }

        public BasicGetResult BasicGet(string queue, bool autoAck)
        {
            return this._imodel.BasicGet(queue,autoAck);
        }

        public void BasicNack(ulong deliveryTag, bool multiple, bool requeue)
        {
            this._imodel.BasicNack(deliveryTag,multiple,requeue);
        }

        public void BasicPublish(string exchange, string routingKey, bool mandatory, IBasicProperties basicProperties, byte[] body)
        {
            this._imodel.BasicPublish(exchange,routingKey,mandatory,basicProperties,body);
        }

        public void BasicPublish(string exchange, string routingKey, IBasicProperties basicProperties, byte[] body)
        {
            this._imodel.BasicPublish(exchange, routingKey, basicProperties, body);
        }


        public void BasicQos(uint prefetchSize, ushort prefetchCount, bool global)
        {
            this._imodel.BasicQos(prefetchSize,prefetchCount,global);
        }

        public void BasicRecover(bool requeue)
        {
            this._imodel.BasicRecover(requeue);
        }

        public void BasicRecoverAsync(bool requeue)
        {
            this._imodel.BasicRecoverAsync(requeue);
        }

        public void BasicReject(ulong deliveryTag, bool requeue)
        {
            this._imodel.BasicReject(deliveryTag, requeue);
        }

        public void Close()
        {
            this._imodel.Close();
        }

        public void Close(ushort replyCode, string replyText)
        {
            this._imodel.Close(replyCode, replyText);
        }

        public void ConfirmSelect()
        {
            this._imodel.ConfirmSelect();
        }

        public uint ConsumerCount(string queue)
        {
            return this._imodel.ConsumerCount(queue);
        }

        public IBasicProperties CreateBasicProperties()
        {
            return this._imodel.CreateBasicProperties();
        }

        public IBasicPublishBatch CreateBasicPublishBatch()
        {
            return this._imodel.CreateBasicPublishBatch();
        }

        public void ExchangeBind(string destination, string source, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.ExchangeBind(destination,source,routingKey,arguments);
        }

        public void ExchangeBindNoWait(string destination, string source, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.ExchangeBindNoWait(destination, source, routingKey, arguments);
        }

        public void ExchangeDeclare(string exchange, string type, bool durable, bool autoDelete, IDictionary<string, object> arguments)
        {
            this._imodel.ExchangeDeclare(exchange, type, durable, autoDelete,arguments);
        }

        public void ExchangeDeclareNoWait(string exchange, string type, bool durable, bool autoDelete, IDictionary<string, object> arguments)
        {
            this._imodel.ExchangeDeclareNoWait(exchange, type, durable, autoDelete,arguments);
        }

        public void ExchangeDeclarePassive(string exchange)
        {
            this._imodel.ExchangeDeclarePassive(exchange);
        }

        public void ExchangeDelete(string exchange, bool ifUnused)
        {
            this._imodel.ExchangeDelete(exchange,ifUnused);
        }

        public void ExchangeDeleteNoWait(string exchange, bool ifUnused)
        {
            this._imodel.ExchangeDeleteNoWait(exchange,ifUnused);
        }

        public void ExchangeUnbind(string destination, string source, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.ExchangeUnbind(destination,source,routingKey,arguments);
        }

        public void ExchangeUnbindNoWait(string destination, string source, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.ExchangeUnbindNoWait(destination,source,routingKey,arguments);
        }

        public uint MessageCount(string queue)
        {
            return this._imodel.MessageCount(queue);
        }

        public void QueueBind(string queue, string exchange, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.QueueBind(queue,exchange,routingKey,arguments);
        }

        public void QueueBindNoWait(string queue, string exchange, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.QueueBindNoWait(queue,exchange,routingKey,arguments);
        }

        public QueueDeclareOk QueueDeclare(string queue, bool durable, bool exclusive, bool autoDelete, IDictionary<string, object> arguments)
        {
            return this._imodel.QueueDeclare(queue,durable,exclusive,autoDelete,arguments);
        }

        public void QueueDeclareNoWait(string queue, bool durable, bool exclusive, bool autoDelete, IDictionary<string, object> arguments)
        {
            this._imodel.QueueDeclareNoWait(queue,durable,exclusive,autoDelete,arguments);
        }

        public QueueDeclareOk QueueDeclarePassive(string queue)
        {
            return this._imodel.QueueDeclarePassive(queue);
        }

        public uint QueueDelete(string queue, bool ifUnused, bool ifEmpty)
        {
            return this._imodel.QueueDelete(queue, ifUnused, ifEmpty);
        }

        public void QueueDeleteNoWait(string queue, bool ifUnused, bool ifEmpty)
        {
            this._imodel.QueueDeleteNoWait(queue,ifUnused,ifEmpty);
        }

        public uint QueuePurge(string queue)
        {
            return this._imodel.QueuePurge(queue);
        }

        public void QueueUnbind(string queue, string exchange, string routingKey, IDictionary<string, object> arguments)
        {
            this._imodel.QueueUnbind(queue,exchange,routingKey,arguments);
        }

        public void TxCommit()
        {
            this._imodel.TxCommit();
        }

        public void TxRollback()
        {
            this._imodel.TxRollback();
        }

        public void TxSelect()
        {
            this._imodel.TxSelect();
        }

        public bool WaitForConfirms()
        {
            return this._imodel.WaitForConfirms();
        }

        public bool WaitForConfirms(TimeSpan timeout)
        {
            return this._imodel.WaitForConfirms(timeout);
        }

        public bool WaitForConfirms(TimeSpan timeout, out bool timedOut)
        {
            return this._imodel.WaitForConfirms(timeout,out timedOut);
        }

        public void WaitForConfirmsOrDie()
        {
            this._imodel.WaitForConfirmsOrDie();
        }

        public void WaitForConfirmsOrDie(TimeSpan timeout)
        {
            this._imodel.WaitForConfirmsOrDie();
        }
    }
}