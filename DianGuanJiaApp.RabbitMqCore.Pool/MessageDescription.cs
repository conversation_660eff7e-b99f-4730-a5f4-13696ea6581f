using System.Collections.Generic;
using System.Threading;
using RabbitMQ.Client;

namespace DianGuanJiaApp.RabbitMqCore.Pool
{
    public class MessageDescription
    {
        /// <summary>
        /// 消息描述信息
        /// </summary>
        public MessageDescription(string exchangeType = null, byte deliveryMode = 1)
        {
            ExchangeTypeName = string.IsNullOrEmpty(exchangeType) ? ExchangeType.Direct : exchangeType;
            ResetEvent = new ManualResetEvent(true);
            DeliveryMode = deliveryMode;
        }

        /// <summary>
        /// 交换类型名，发送类型
        /// </summary>
        public string ExchangeName { get; set; }
        /// <summary>
        /// 队列名
        /// </summary>
        public string QueueName { get; set; }

        /// <summary>
        /// 队列名
        /// </summary>

        public string RouterName
        {
            get
            {
                return QueueName;
            }
        }

        /// <summary>
        /// 交换机类型
        /// </summary>
        public string ExchangeTypeName { get; set; }

        public bool IsDelayQueue { get; set; }
        public long XMessageTtl { get; set; }
        public string XDeadLetterExchange { get; set; }
        public string XDeadLetterRoutingKey { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="channel"></param>
        /// <returns></returns>
        public IModel InitIModel(IModel channel)
        {
            channel.ExchangeDeclare(this.ExchangeName, ExchangeTypeName, true, false);
            if (IsDelayQueue)
            {
                var dict = new Dictionary<string, object>
                {
                    { "x-message-ttl", XMessageTtl },
                    { "x-dead-letter-exchange", XDeadLetterExchange },
                    { "x-dead-letter-routing-key", XDeadLetterRoutingKey }
                };
                channel.QueueDeclare(QueueName, true, false, false, dict);
            }
            else
                channel.QueueDeclare(QueueName, true, false, false);

            channel.QueueBind(this.QueueName, this.ExchangeName, this.RouterName);
            return channel;
        }

        /// <summary>
        /// 线程中用于暂停和继续
        /// </summary>
        public ManualResetEvent ResetEvent { get; set; }

        public string ConsumerFlag { get; set; }
        /// <summary>
        /// 消息持久化（1：不持久化，2.持久化）
        /// </summary>
        public byte DeliveryMode { get; set; }

    }
}