using DianGuanJiaApp.Data.Entity;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services
{
    /// <summary>
    /// 对象存储
    /// </summary>
    public interface ICloudStorageService
    {
        /// <summary>
        /// 存储对象上传
        /// </summary>
        /// <param name="bucketName">存储桶名称</param>
        /// <param name="filePath">存储路径</param>
        /// <param name="bytes">文件字节</param>
        /// <returns></returns>
        WebAjaxResult UploadFile(string bucketName, string filePath, byte[] bytes);

        /// <summary>
        /// 获取对象存储元素
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        byte[] GetObjectStorage(string key);
    }
}
