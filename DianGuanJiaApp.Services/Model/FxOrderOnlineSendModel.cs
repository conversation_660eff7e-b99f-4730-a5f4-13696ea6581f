using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;

namespace DianGuanJiaApp.Services.Model
{
    /// <summary>
    /// 分销订单发货模型
    /// </summary>
    public class BindSupplieTempModel
    {
        public ProductFx Product { get; set; }
        public PathFlow OldPathFlow { get; set; }
        public PathFlow NewPathFlow { get; set; }
        public List<PathFlowReference> OldRefs { get; set; }
    }

    public class BindPathChangeModel 
    {
        public BindPathChangeModel() 
        {
            OldPathFlow = new PathFlow();
            NewPathFlow = new PathFlow();
        }

        public PathFlow OldPathFlow { get; set; }
        public PathFlow NewPathFlow { get; set; }
    }

    public class PathFlowReferenceChangeModel
    {
        public PathFlowReferenceChangeModel()
        {
            OldPathFlowReferences = new List<PathFlowReference>();
            NewPathFlowReferences = new List<PathFlowReference>();
        }

        public string PathFlowRefCode { get; set; }
        public bool IsSkuBind { get; set; }

        public List<string> AllRelationCodes { get; set; }

        /// <summary>
        /// 根据ID更新数据库的Status状态为-1
        /// </summary>
        public List<PathFlowReference> OldPathFlowReferences { get; set; }
        /// <summary>
        /// 都需要新增到数据库
        /// </summary>
        public List<PathFlowReference> NewPathFlowReferences { get; set; }
    }

}



