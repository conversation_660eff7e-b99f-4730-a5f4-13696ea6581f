using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Extension;
using System.Collections.Concurrent;

namespace DianGuanJiaApp.Services.PlatformService
{
    public class DuXiaoDianPlatformService : IPlatformService, ISyncOrderJsonService
    {
        #region 私有变量

        private DuXiaoDianApiClient _client = null;
        private Shop _shop = null;

        /// <summary>
        /// 度小店订单状态
        /// </summary>
        public enum PlatformOrderStatusType
        {
            /// <summary>
            /// 下单成功（货到付款订单可发货）
            /// </summary>
            ORDER_CREATE,
            /// <summary>
            /// 支付完成（可发货）
            /// </summary>
            ORDER_PAID,
            /// <summary>
            /// 商家已确认（货到付款订单可发货）
            /// </summary>
            ORDER_ACCEPTED,
            /// <summary>
            /// 订单已发货（可发货）
            /// </summary>
            ORDER_SENT,
            /// <summary>
            /// 订单已签收
            /// </summary>
            ORDER_RECEIPT,
            /// <summary>
            /// 订单已取消
            /// </summary>
            ORDER_CANCEL,
            /// <summary>
            /// 在线支付订单交易完成
            /// </summary>
            ORDER_CONSUMED,
            /// <summary>
            /// 交易已关闭
            /// </summary>
            ORDER_CLOSE
        }
        /// <summary>
        /// 度小店退款状态
        /// </summary>
        public enum PlatformRefundStatus
        {
            /// <summary>
            /// 无售后
            /// </summary>
            REFUND_DEFAULT,
            /// <summary>
            /// 售后待处理（不能发货）
            /// </summary>
            REFUND_PENDING,
            /// <summary>
            /// 售后处理中（不能发货）
            /// </summary>
            REFUND_PROCESSING,
            /// <summary>
            /// 售后处理完成
            /// </summary>
            REFUND_FINISHED
        }
        #endregion

        public DuXiaoDianPlatformService(Shop shop)
        {
            _client = new DuXiaoDianApiClient(shop);
            _shop = shop;
        }
        public Shop RefreshShopToken()
        {
            _client.RefreshToken();
            return _shop;
        }

        public Shop SyncShopInfo()
        {
            var apiName = "trade/v1.0/orders/list";
            var orders = new List<Order>();
            var pageCount = 50; //取值范围:大于零的整数; 默认值:10;最大值:50
            var dic = new Dictionary<string, string>();
            dic.Add("fieldList", "shopName"); //需要返回的字段列表，多个字段用半角逗号分隔，可选值为OrderInfo数据结构中所有字段。
            dic.Add("startTime", DateTime.Now.AddDays(-28).ToString("yyyy-MM-dd HH:mm:ss"));
            dic.Add("endTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            dic.Add("pageNum", "1");
            dic.Add("pageCount", pageCount.ToString());
            var result = _client.Execute(apiName, dic);
            var jtoken = JsonCheck(result, apiName).Item3;
            if (!jtoken.HasValues) return _shop;    //有可能获取不到订单,使用用户名

            var orderInfos = jtoken?.Value<JArray>("orderInfos");
            var shopName = orderInfos[0].Value<string>("shopName");
            _shop.ShopName = shopName;
            _shop.NickName = shopName;
            return _shop;
        }

        #region 获取订单

        public List<string> SyncOrderJsons(List<string> pids)
        {
            if (IsManualRefund()) return new List<string>();
            var orders = new ConcurrentBag<string>();
            if (pids == null || !pids.Any())
                return new List<string>();

            Parallel.ForEach(pids, new ParallelOptions { MaxDegreeOfParallelism = 5 }, pid =>
            {
                var o = SyncOrderJson(pid);
                if (o != null)
                    orders.Add(o);
            });
            return orders.ToList();
        }

        public string SyncOrderJson(string platformOrderId)
        {
            if (IsManualRefund()) return null;
            var apiName = "trade/v1.0/orders/info";
            var dic = new Dictionary<string, string>();
            dic.Add("fieldList", "orderId,status,payType,totalAmount,discountAmount,paymentAmount,freightAmount,message,remark,createTime,updateTime,payTime,refundStatus,shopName,orderExpressInfo,orderDetailInfos"); //需要返回的字段列表，多个字段用半角逗号分隔，可选值为OrderInfo数据结构中所有字段。
            dic.Add("orderId", platformOrderId);
            var result = _client.Execute(apiName, dic);
            return result;
        }

        public List<Order> SyncOrders(List<string> pids)
        {
            if (IsManualRefund()) return new List<Order>();
            var orders = new ConcurrentBag<Order>();
            if (pids == null || !pids.Any())
                return new List<Order>();

            Parallel.ForEach(pids, new ParallelOptions { MaxDegreeOfParallelism = 5 }, pid =>
            {
                var o = SyncOrder(pid);
                if (o != null)
                    orders.Add(o);
            });
            return orders.ToList();
        }

        public Order SyncOrder(string platformOrderId)
        {
            if (IsManualRefund()) return null;
            Order _order = new Order();
            var apiName = "trade/v1.0/orders/info";
            var result = SyncOrderJson(platformOrderId);
            var jtoken = JsonCheck(result, apiName).Item3;
            if (!jtoken.HasValues) return _order;
            _order = TransferJsonToOrders(jtoken);
            return _order;
        }

        public List<Order> SyncOrders(SyncOrderParametersModel parameter,bool isPinged=false)
        {
            if(IsManualRefund()) return new List<Order>();
            var apiName = "trade/v1.0/orders/list";
            var orders = new List<Order>();
            var pageCount = 50; //取值范围:大于零的整数; 默认值:10;最大值:50
            var end = parameter.EndTime.HasValue ? parameter.EndTime.Value : DateTime.Now;
            var start = parameter.StartTime.HasValue ? parameter.StartTime.Value.AddMinutes(-30) : end.AddDays(-7);

            var dic = new Dictionary<string, string>();
            dic.Add("fieldList", "orderId,status,payType,totalAmount,discountAmount,paymentAmount,freightAmount,message,remark,createTime,updateTime,payTime,refundStatus,shopName,orderExpressInfo,orderDetailInfos"); //需要返回的字段列表，多个字段用半角逗号分隔，可选值为OrderInfo数据结构中所有字段。
            //如果开始时间大于一个月就按开始一个月来查询
            var newStart = DateTime.Now.AddDays(-30);
            if (start < newStart)
            {
                start = newStart;
                end = DateTime.Now;
            }
            if (parameter.IsFullSync)
            {

                dic.Add("startTime", start.ToString("yyyy-MM-dd HH:mm:ss"));
                dic.Add("endTime", end.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            else
            {
                dic.Add("updateStartTime", start.ToString("yyyy-MM-dd HH:mm:ss"));
                dic.Add("updateEndTime", end.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            //var status = ParseOrderStatusToPlatformStatus(parameter.OrderStatus);
            //dic.Add("status", status);
            dic.Add("pageNum", "1");
            dic.Add("pageCount", pageCount.ToString());

            var result = _client.Execute(apiName, dic);
            var jtoken = JsonCheck(result, apiName).Item3;
            if (!jtoken.HasValues) return orders;

            var pageInfo = jtoken?.Value<JToken>("pageInfo");
            var orderInfos = jtoken?.Value<JArray>("orderInfos");
            var totalPage = pageInfo.Value<int>("totalPage");
            var totalCount = pageInfo.Value<int>("totalCount");
            orderInfos.ToList().ForEach(x =>
            {
                orders.Add(TransferJsonToOrders(x));
            });

            //判断总订单数是否大于第一次获取的数量
            if (totalCount > pageCount)
            {
                var requests = new List<Dictionary<string, string>>();
                for (int i = 1; i < totalPage; i++)
                {
                    var tempDict = new Dictionary<string, string>(dic);
                    tempDict.Remove("timeStamp");
                    tempDict.Remove("signature");
                    tempDict.Remove("nonce");
                    tempDict.Remove("openId");
                    tempDict["pageNum"] = (i + 1).ToString();
                    requests.Add(tempDict);
                }

                var allOrders = new ConcurrentBag<Order>();
                Parallel.ForEach(requests, new ParallelOptions { MaxDegreeOfParallelism = 5 }, (req) =>
                {
                    var _result = _client.Execute(apiName, req);
                    var _jtoken = JsonCheck(_result, apiName).Item3;
                    var _orderInfos = _jtoken?.Value<JArray>("orderInfos");
                    _orderInfos.ToList().ForEach(o =>
                    {
                        allOrders.Add(TransferJsonToOrders(o));
                    });
                });
                orders.AddRange(allOrders.ToList());
            }
            if (totalCount != orders.Count())
                Log.WriteWarning($"同步订单时，实际数量({orders.Count()})与接口总数量({totalCount})不一致，同步参数：{parameter.ToJson()}");
            return orders;
        }
        public Order TransferJsonToOrders(JToken _jtoken)
        {
            Order _order = new Order();
            _order.ShopId = _shop.Id;
            _order.PlatformType = _shop.PlatformType.ToString();
            _order.PlatformOrderId = _jtoken?.Value<string>("orderId");
            var order_status = _jtoken?.Value<string>("status");
            OrderStatusType orderType = ParsePlatformStatusToOrderStatus(order_status);
            _order.PlatformStatus = orderType.ToString();
            _order.BackUpPlatformStatus = _order.PlatformStatus;

            _order.TradeType = _jtoken?.Value<string>("payType");   //PAYTYPE_ONLINE - 在线支付  PAYTYPE_OFFLINE - 货到付款
            var orderAmount = _jtoken?.Value<decimal>("totalAmount");
            if (orderAmount != null && orderAmount > 0)
                _order.TotalAmount = orderAmount / 100;     //订单金额(分)

            var discountAmount = _jtoken?.Value<decimal>("discountAmount"); //优惠总金额
            if (discountAmount != null && discountAmount > 0)
                _order.Discount = discountAmount / 100; //订单金额(分)

            var paymentAmount = _jtoken?.Value<decimal>("paymentAmount");   //实际支付  

            var freightAmount = _jtoken?.Value<decimal>("freightAmount");   //运费
            if (freightAmount != null && freightAmount > 0)
                _order.ShippingFee = freightAmount / 100;  //运费(分)

            _order.BuyerRemark = _jtoken?.Value<string>("message");    //买家留言
            _order.SellerRemark = _jtoken?.Value<string>("remark");

            var createTime = _jtoken?.Value<string>("createTime");  //创建时间
            if (!createTime.IsNullOrEmpty())
                _order.CreateTime = DateTime.Parse(createTime);
            var updateTime = _jtoken?.Value<string>("updateTime");  //更新时间
            if (!updateTime.IsNullOrEmpty())
                _order.UpdateTime = DateTime.Parse(updateTime);
            var payTime = _jtoken?.Value<string>("payTime");  //支付时间
            if (!payTime.IsNullOrEmpty())
                _order.PayTime = DateTime.Parse(payTime);
            _order.LastSyncTime = DateTime.Now;

            var refundStatus = _jtoken?.Value<string>("refundStatus");
            var orderRefundStatus = ParsePlatformRefundStatusToOrderItemRefundStatus(refundStatus);
            _order.RefundStatus = orderRefundStatus?.ToString();
            if (orderRefundStatus == RefundStatusType.REFUND_SUCCESS)
            {
                if (_order.PlatformStatus == OrderStatusType.waitsellersend.ToString())
                {
                    _order.RefundStatus = RefundStatusType.REFUND_CLOSE.ToString();
                }
            }

            //货到付款
            if (_order.TradeType == "PAYTYPE_OFFLINE")
            {
                _order.IsSvcCOD = true;
                _order.SvcCODAmount = _order.TotalAmount;
            }

            //物流信息
            var orderExpressInfo = _jtoken?.Value<JToken>("orderExpressInfo");
            _order.ToName = orderExpressInfo?.Value<string>("name");
            _order.ToMobile = orderExpressInfo?.Value<string>("mobile");
            _order.ToPhone = orderExpressInfo?.Value<string>("phone");
            _order.ToProvince = orderExpressInfo?.Value<string>("province");
            _order.ToCity = orderExpressInfo?.Value<string>("city");
            _order.ToCounty = orderExpressInfo?.Value<string>("area");
            _order.ToAddress = orderExpressInfo?.Value<string>("address");
            var town = orderExpressInfo?.Value<string>("town");
            _order.ToAddress = _order.ToAddress.Contains(town) ? _order.ToAddress : town + _order.ToAddress;
            var pcc = _order.ToProvince + _order.ToCity + _order.ToCounty;
            _order.ToFullAddress = _order.ToAddress.StartsWith(pcc) ? _order.ToAddress : (pcc + _order.ToAddress);
            _order.PlatformRemark = orderExpressInfo?.Value<string>("memo");

            _order.BuyerWangWang = _order.ToName;

            //明细信息
            var orderDetailInfos = _jtoken?.Value<JArray>("orderDetailInfos").ToList();
            if (orderDetailInfos.Count > 0)
            {
                orderDetailInfos.ForEach(x =>
                {
                    OrderItem _orderItem = new OrderItem();
                    _orderItem.Status = _order.PlatformStatus;
                    _orderItem.CreateTime = DateTime.Now;
                    _orderItem.ShopId = _order.ShopId;
                    _orderItem.PlatformOrderId = _order.PlatformOrderId;
                    _orderItem.ProductID = x?.Value<string>("productId");
                    _orderItem.ProductSubject = x?.Value<string>("productName");    //大表盘时尚防水夜光·石英腕表
                    _orderItem.ProductImgUrl = x?.Value<string>("skuImage");
                    _orderItem.Count = x?.Value<int>("buyNum") ?? 0;
                    _orderItem.RefundStatus = _order.RefundStatus;
                    //_orderItem.Count = x?.Value<string>("packageDesc");   //套餐描述  【专柜正品】MQM3眼6针·大表盘时尚防水夜光·石英腕表 高端手表
                    var packageDesc = x?.Value<string>("packageDesc");
                    var skuDesc = x?.Value<string>("skuDesc")?.TrimEnd(';');

                    _orderItem.Color = skuDesc;
                    if (!string.IsNullOrWhiteSpace(packageDesc))
                    {
                        _orderItem.Color = packageDesc;
                        _orderItem.Size = skuDesc;
                    }

                    _orderItem.SkuID = x?.Value<string>("skuId");
                    _orderItem.Price = 0;
                    var actualPrice = x?.Value<decimal>("actualPrice"); //优惠总金额
                    _orderItem.ItemAmount = actualPrice != null && actualPrice > 0 ? (actualPrice / 100) * _orderItem.Count : 0; //订单金额(分)
                    _orderItem.productCargoNumber = x?.Value<string>("skuCode");
                    _orderItem.SubItemID = _order.PlatformOrderId + _orderItem.SkuID;

                    _order.OrderItems.Add(_orderItem);
                });
            }
            return _order;
        }
        #endregion

        #region 发货

        public List<DeliverySendOrderResultModel> OnlineSend(DeliverySendOrderModel model)
        {
            //循环发货，支持合并订单发货，一次只能发货一个订单
            //无需物流发货可以批量发
            var orders = model.Orders;
            var ecm = model.ExpressCodeMapping;
            var logisticSendCode = model.ExpressCodeMapping.PlatformExpressCode;
            var requests = new List<KeyValuePair<int, Dictionary<string, string>>>();
            //用户勾选的数量
            var results = new List<DeliverySendOrderResultModel>();
            foreach (var orderEntity in orders)
            {
                var orderRequest = model.OrderRequests.FirstOrDefault(o => o.Id == orderEntity.Id);
                var result = new DeliverySendOrderResultModel
                {
                    ErrorCode = "",
                    ErrorMessage = "",
                    ExtErrorMessage = "",
                    IsSuccess = false,
                    OrderEntity = orderEntity,
                    OrderRequest = orderRequest
                };
                //订单信息
                var ois = orderRequest.OrderItems;
                var successOrderItems = new List<int>();
                var pids = orderEntity.OrderItems.Where(oi => orderRequest.OrderItems.Contains(oi.Id)).Select(oi => string.IsNullOrEmpty(oi.OrignalOrderId) ? oi.PlatformOrderId : oi.OrignalOrderId).Distinct().ToList();
                pids.ForEach(pid =>
                {
                    //var dborder = new OrderService().Get($" where PlatformOrderId=@sid and ShopId={orderEntity.ShopId}", new { sid = pid }).FirstOrDefault();
                    //if (dborder.RefundStatus == "WAIT_SELLER_AGREE")
                    //{
                    //    result.IsSuccess = false;
                    //    result.ErrorCode = "";
                    //    result.ErrorMessage += $"订单【{pid}】正在售后中,无法发货";
                    //}
                    //else
                    //{
                    var curOrderItems = orderEntity.OrderItems.Where(oi => oi.PlatformOrderId == pid || oi.OrignalOrderId == pid); //当前订单的订单项
                    var selectedOrderItems = curOrderItems.Where(t => ois.Contains(t.Id)).ToList();//选中的订单项


                    var dict = new Dictionary<string, string>();
                    dict.Add("orderId", pid);
                    dict.Add("trackingNumber", orderRequest.WaybillCode);
                    dict.Add("expressName", model.ExpressCodeMapping.PlatformExpressName);
                    dict.Add("memo", orderRequest.SellerRemark ?? " "); //备注必须要传,要么给个空值
                    var json = "";
                    try
                    {
                        var apiName = "trade/v1.0/orders/deliver";
                        json = _client.Execute(apiName, dict);
                        var jtoken = JToken.Parse(json);
                        var code = jtoken?.Value<string>("error_code");
                        var msg = jtoken?.Value<string>("error_msg");
                        var data = jtoken?.Value<JToken>("data");
                        if (data.HasValues && code == "0")
                        {
                            var isSuccess = data.Value<bool>("isSuccess");
                            if (isSuccess)
                            {
                                result.IsSuccess = true;
                                successOrderItems.AddRange(curOrderItems.Select(f => f.Id).Distinct());
                            }
                            else
                                result.OriginalResult = json;//原始内容
                        }
                        else
                        {
                            result.OriginalResult = json;//原始内容
                            if (pids.Count() > 1)
                            {
                                result.ErrorMessage += $"订单【{pid}】发货失败： {msg} {code}</br>";
                            }
                            else
                            {
                                result.IsSuccess = false;
                                result.ErrorCode = code;
                                result.ErrorMessage = msg;
                            }
                        }
                    }
                    catch (LogicException ex)
                    {
                        Utility.Log.WriteError($"度小店订单【{pid}】发货失败：请求参数：{dict.ToJson()} 返回：{json} 错误：{ex}");
                        result.OriginalResult = json;//原始内容
                        if (ex?.Message?.Contains("授权过期") == true)
                        {
                            result.ErrorMessage += $"订单【{pid}】发货失败：店铺授权过期，请从商家后台进入后重试";
                            result.ErrorCode += "";
                        }
                        else
                        {
                            result.OriginalResult = json;//原始内容
                            result.ErrorMessage += $"订单【{pid}】发货失败：{ex.Message}";
                            result.ErrorCode += "Logic Error </br>";
                        }
                    }
                    catch (Exception ex)
                    {
                        Utility.Log.WriteError($"度小店订单【{pid}】发货失败：请求参数：{dict.ToJson()} 返回：{json} 错误：{ex}");
                        result.OriginalResult = json;//原始内容
                        if (ex?.Message?.Contains("授权过期") == true)
                        {
                            result.ErrorMessage += $"订单【{pid}】发货失败：店铺授权过期，请从商家后台进入后重试";
                            result.ErrorCode += "";
                        }
                        else
                        {
                            result.ErrorMessage += $"订单【{pid}】发货失败：系统错误";
                            result.ErrorCode += "System Error </br>";
                        }
                    }
                    //}
                    //Thread.Sleep(5);
                });
                if (!successOrderItems.Any())
                {
                    result.IsSuccess = false;
                    successOrderItems = ois;
                }
                else
                    result.OrderRequest.OrderItems = successOrderItems;
                results.Add(result);
            }
            return results;
        }

        /// <summary>
        /// 线上二次发货
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<DeliverySendOrderResultModel> OnlineResend(DeliverySendOrderModel model)
        {
            List<DeliverySendOrderResultModel> list = new List<DeliverySendOrderResultModel>();
            model.Orders.ForEach(x =>
            {
                DeliverySendOrderResultModel result = new DeliverySendOrderResultModel()
                {
                    IsSuccess = false,
                    ErrorCode = "PLATFORM_DOES_NOT_SUPPORT",
                    ExtErrorMessage = $"订单【{x.PlatformOrderId}】发货失败,平台暂时不支持二次发货"
                };
                list.Add(result);
            });
            return list;
        }
        #endregion


        /// <summary>
        /// 获取可用scope，用于拼接百度用户登录授权页面url的scope参数，即https://openauth.baidu.com/doc/doc.html文档中【2引导用户完成授权获取code】接口参数中的【scope】值
        /// </summary>
        /// <returns></returns>
        public string GetScope()
        {
            return _client.GetScopes();
        }

        public int GetPlatformOrderNumber()
        {
            var apiName = "trade/v1.0/orders/list";
            var orders = new List<Order>();
            var pageCount = 1; //取值范围:大于零的整数; 默认值:10;最大值:50
            var end = DateTime.Now;
            var start = DateTime.Now.AddDays(-29);
            var dic = new Dictionary<string, string>();
            dic.Add("fieldList", "orderId"); //需要返回的字段列表，多个字段用半角逗号分隔，可选值为OrderInfo数据结构中所有字段。
            dic.Add("updateStartTime", start.ToString("yyyy-MM-dd HH:mm:ss"));
            dic.Add("updateEndTime", end.ToString("yyyy-MM-dd HH:mm:ss"));
            dic.Add("status", "ORDER_PAID");
            dic.Add("pageNum", "1");
            dic.Add("pageCount", pageCount.ToString());

            var result = _client.Execute(apiName, dic);
            var jtoken = JsonCheck(result, apiName).Item3;
            var pageInfo = jtoken?.Value<JToken>("pageInfo");
            var totalCount = pageInfo.Value<int>("totalCount");
            return totalCount;
        }

        private Tuple<int, string, JToken> JsonCheck(string json, string apiName = null)
        {
            if (string.IsNullOrEmpty(json))
                throw new LogicException($"度小店接口【{apiName}】返回数据为空");

            JToken jtoken = null;
            try
            {
                jtoken = JsonConvert.DeserializeObject<JToken>(json);
            }
            catch (Exception ex)
            {
                //Utility.Log.WriteError($"度小店接口【{apiName}】返回数据格式不正确，无法解析：{json}，错误信息：{ex}");
                throw new LogicException("接口返回数据格式不正确，无法解析");
            }
            var error = _client.GetErrorMessage(jtoken);
            var errorCode = error.Item1;
            var errorMessage = error.Item2;
            if (errorCode != 0)
            {
                if (errorCode == 111)
                {
                    throw new LogicException($"店铺【{_shop.ShopName}】授权过期 ，返回结果：{json}");
                }
                else if (errorCode == 450002)
                {
                    throw new LogicException($"账号【{_shop.ShopName}】非度小店店铺账号!");
                }
                else
                {
                    throw new LogicException($"店铺【{_shop.ShopName}】调用API接口【{apiName}】错误 ，返回结果：{json}");
                }
            }
            return new Tuple<int, string, JToken>(errorCode, errorMessage, jtoken?.Value<JToken>("data"));
        }

        /// <summary>
        /// 我们系统的状态转度小店订单状态
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private string ParseOrderStatusToPlatformStatus(OrderStatusType? status)
        {
            if (status == null)
                return "";
            PlatformOrderStatusType pos = PlatformOrderStatusType.ORDER_PAID;
            if (status != null)
            {
                switch (status)
                {
                    case OrderStatusType.waitbuyerpay:
                        break;
                    case OrderStatusType.cancel:
                        pos = PlatformOrderStatusType.ORDER_CLOSE;
                        break;
                    case OrderStatusType.waitsellersend:
                        pos = PlatformOrderStatusType.ORDER_PAID;
                        break;
                    case OrderStatusType.waitbuyerreceive:
                        pos = PlatformOrderStatusType.ORDER_SENT;
                        break;
                    case OrderStatusType.success:
                        pos = PlatformOrderStatusType.ORDER_CONSUMED;
                        break;
                    case OrderStatusType.confirm_goods_but_not_fund:
                        pos = PlatformOrderStatusType.ORDER_CREATE;
                        break;
                    case OrderStatusType.confirm_goods:
                        break;
                    case OrderStatusType.signinsuccess:
                        pos = PlatformOrderStatusType.ORDER_RECEIPT;
                        break;
                    case OrderStatusType.terminated:
                        break;
                    case OrderStatusType.wait_seller_accept_order:
                        break;
                    case OrderStatusType.send_goods_but_not_fund:
                        break;
                    case OrderStatusType.locked:
                        break;
                    case OrderStatusType.locked_sended:
                        break;
                    default:
                        break;
                }
            }
            return pos.ToString();
        }

        public OrderStatusType ParsePlatformStatusToOrderStatus(string platformOrderStatus)
        {
            OrderStatusType pos = OrderStatusType.waitsellersend;
            switch (platformOrderStatus)
            {
                case "ORDER_CLOSE":
                case "ORDER_CANCEL":
                    pos = OrderStatusType.cancel;
                    break;
                case "ORDER_PAID":
                    pos = OrderStatusType.waitsellersend;
                    break;
                case "ORDER_SENT":
                    pos = OrderStatusType.waitbuyerreceive;
                    break;
                case "ORDER_CONSUMED":
                    pos = OrderStatusType.success;
                    break;
                case "ORDER_CREATE":
                    pos = OrderStatusType.confirm_goods_but_not_fund;
                    break;
                case "ORDER_RECEIPT":
                    pos = OrderStatusType.signinsuccess;
                    break;
                default:
                    break;
            }
            return pos;
        }

        /// <summary>
        /// 平台退款状态转我们系统的状态
        /// </summary>
        /// <param name="platformRefundStatus"></param>
        /// <returns></returns>
        private RefundStatusType? ParsePlatformRefundStatusToOrderItemRefundStatus(string platformRefundStatus)
        {
            RefundStatusType? rs = null;
            if (platformRefundStatus.IsNullOrEmpty() || platformRefundStatus == "REFUND_DEFAULT")
                return rs;
            switch (platformRefundStatus)
            {
                case "REFUND_PENDING":
                case "REFUND_PROCESSING":
                    rs = RefundStatusType.WAIT_SELLER_AGREE;
                    break;
                case "REFUND_FINISHED":
                    rs = RefundStatusType.REFUND_SUCCESS;
                    break;
            }
            return rs;
        }

        public UserInfo BasicMember()
        {
            throw new NotImplementedException();
        }
        public string ConfirmOrder(List<string> platformOrderIds)
        {
            throw new NotImplementedException();
        }

        public List<GetPriceRangesModel> DangPriceList(List<string> itemList)
        {
            throw new NotImplementedException();
        }

        public string GetAppPayUrl()
        {
            throw new NotImplementedException();
        }

        public string GetBuyerMemberIdByLoginId(string strLoginId)
        {
            throw new NotImplementedException();
        }

        public DateTime? GetExpiredTime()
        {
            return _shop.ExpireTime;
        }

        public List<KeyValuePair<string, string>> GetLoginIdByMemberId(string buyerMemberId)
        {
            throw new NotImplementedException();
        }

        public int GetLogisticsAddress()
        {
            throw new NotImplementedException();
        }

        public Shop GetPlatformShopInfo()
        {
            return _shop;
        }
        public bool Ping()
        {
            try
            {
                GetPlatformOrderNumber();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public bool SaveLogisticsAddress(LogisticsAddressModel model, out string errMsg)
        {
            throw new NotImplementedException();
        }

        public string SaveOrderPrice(Dictionary<string, string> dc)
        {
            throw new NotImplementedException();
        }

        public Product SyncProduct(string platformId)
        {
            if (IsManualRefund()) return null;
            var apiName = "product/v1.0/product/getDetail";
            var dic = new Dictionary<string, string>();
            dic.Add("spuId", platformId);
            var reuslt = _client.Execute(apiName, dic);
            var jtoken = JsonCheck(reuslt).Item3;

            var product = TransferJTokenToProduct(jtoken);
            return product;
        }

        public List<Product> SyncProduct(int statusType, DateTime? start = null, DateTime? end = null,
            bool isNotPinged = false)
        {
            if (IsManualRefund()) return new List<Product>();
            var isShelf = string.Empty;
            if (statusType == 1)
            {
                isShelf = "1"; //上架产品
                return SyncProductAction(isShelf);
            }
            else if (statusType == 2)
            {
                isShelf = "2"; //下架产品
                return SyncProductAction(isShelf);
            }
            else
            {
                isShelf = "-1";
                var products = new List<Product>();
                products.AddRange(SyncProductAction(isShelf));
                return products;
            }
        }

        public List<Product> SyncProductAction(string statusType)
        {
            if (IsManualRefund()) return new List<Product>();
            List<Product> list = new List<Product>();
            var apiName = "product/v1.0/product/getProductList";
            var pageSize = 50;
            var dic = new Dictionary<string, string>();
            dic.Add("status", statusType);
            dic.Add("pageNum", "1");
            dic.Add("pageSize", pageSize.ToString());

            var reuslt = _client.Execute(apiName, dic);
            var jtoken = JsonCheck(reuslt).Item3;
            var totalNum = jtoken?.Value<int>("totalNum");
            var tarray = jtoken?.Value<JArray>("productLists");
            for (int i = 0; i < tarray.Count; i++)
            {
                var product = tarray[i]?.Value<string>("spuId");
                list.Add(SyncProduct(product));
            }

            if (totalNum > pageSize)
            {
                var pageCount = (int)Math.Ceiling(totalNum.Value / (float)pageSize);
                var requests = new List<Dictionary<string, string>>();
                for (int i = pageCount; i > 1; i--)
                {
                    var _dic = new Dictionary<string, string>();
                    _dic.Add("status", statusType);
                    _dic.Add("pageNum", i.ToString());
                    _dic.Add("pageSize", pageSize.ToString());
                    requests.Add(_dic);
                }

                var allProduct = new ConcurrentBag<Product>();
                Parallel.ForEach(requests, new ParallelOptions { MaxDegreeOfParallelism = 3 }, (req) =>
                {
                    var _reslut = _client.Execute(apiName, req);
                    var _jtoken = JsonCheck(_reslut).Item3;
                    var _tarray = _jtoken?.Value<JArray>("productLists");  //集合
                    _tarray.ToList().ForEach(o =>
                    {
                        var _product = o?.Value<string>("spuId");
                        allProduct.Add(SyncProduct(_product));
                    });
                });
                if (allProduct.Count > 0)
                    list.AddRange(allProduct);
            }
            return list;
        }

        private Product TransferJTokenToProduct(JToken _token)
        {
            var product = new Product();
            product.PlatformId = _token?.Value<string>("spuId");
            product.ShopId = _shop.Id;
            product.ShopName = _shop.NickName;
            product.PlatformType = _shop.PlatformType;
            product.Subject = _token?.Value<string>("productName");
            product.CategoryId = _token?.Value<string>("categoryId");
            product.CargoNumber = "";
            product.ImageUrl = _token?.Value<string>("productPicUrl");
            product.ShortTitle = "";
            product.Status = _token.Value<string>("status").Contains("上架") ? "published" : "deleted";
            product.Weight = 0;
            product.SetWeight = 0;
            product.GroupID = "";

            product.Bazaar = "";
            product.Stall = "";
            product.CostPrice = 0;
            product.IsComm = false;
            product.IsSetWeight = false;

            var packages = _token?.Value<JArray>("packages")?.ToList();
            var skuInfos = _token?.Value<JArray>("skus")?.ToList();
            if (skuInfos != null && skuInfos.Count > 0)
            {
                skuInfos.ForEach(p =>
                {
                    var sku = new ProductSku();
                    sku.Price = p?.Value<decimal>("salePrice");
                    sku.CargoNumber = "";
                    sku.SkuId = p?.Value<string>("skuId");
                    sku.SpecId = sku.SkuId;

                    ProductSkuAttribute attr = new ProductSkuAttribute();
                    var specifications = p?.Value<JArray>("specifications");
                    var attrValue = "";
                    var attrName = "";
                    if (specifications != null && specifications.Any())
                    {
                        for (int i = 0; i < specifications.Count; i++)
                        {
                            if (i == 0)
                                attrName = specifications[i].Value<string>("value");// 兼容历史数据
                            else
                                attrValue += specifications[i].Value<string>("value") + ";";
                        }

                    }

                    attr.AttributeName = attrName;
                    attr.AttributeValue = attrValue.ToString2().TrimEnd(";");
                    attr.ShortTitle = "";
                    attr.Weight = 0;
                    attr.Bazaar = "";
                    attr.Stall = "";
                    attr.CostPrice = 0;
                    sku.ProductSkuAttr = attr;
                    product.Skus.Add(sku);
                });
            }
            else if (packages != null && packages.Count > 0)
            {
                packages.ForEach(pe =>
                {
                    var packName = pe?.Value<string>("name");
                    var peskus = pe?.Value<JArray>("skus")?.ToList();
                    peskus.ForEach(p =>
                    {
                        var sku = new ProductSku();
                        sku.Price = p?.Value<decimal>("salePrice");
                        sku.CargoNumber = "";
                        sku.SkuId = p?.Value<string>("skuId");
                        sku.SpecId = sku.SkuId;

                        ProductSkuAttribute attr = new ProductSkuAttribute();
                        var specifications = p?.Value<JArray>("specifications");
                        var attrValue = "";
                        var attrName = "";
                        if (specifications != null && specifications.Any())
                        {
                            for (int i = 0; i < specifications.Count; i++)
                            {
                                if (i == 0)
                                    attrName = specifications[i].Value<string>("value");// 兼容历史数据
                                else
                                    attrValue += specifications[i].Value<string>("value") + ";";
                            }

                        }

                        var newVal = string.IsNullOrWhiteSpace(attrValue) ? attrName : (attrName + ";" + attrValue.ToString2().TrimEnd(";"));
                        attr.AttributeName = packName;
                        attr.AttributeValue = newVal;
                        attr.ShortTitle = "";
                        attr.Weight = 0;
                        attr.Bazaar = "";
                        attr.Stall = "";
                        attr.CostPrice = 0;
                        sku.ProductSkuAttr = attr;
                        product.Skus.Add(sku);
                    });
                });
            }
            return product;
        }

        public List<ProductCategory> CategoryList()
        {
            List<ProductCategory> list = new List<ProductCategory>();
            var apiName = "product/v1.0/product/getCategoryList";
            var dic = new Dictionary<string, string>();
            dic.Add("parentId", "-1");
            var result = _client.Execute(apiName, dic);
            var resultJson = JsonCheck(result)?.Item3.ToList();
            if (resultJson != null && resultJson.Count() > 0)
            {
                var index = 1;
                resultJson.ForEach(item =>
                {
                    ProductCategory cat = new ProductCategory();
                    cat.id = item?.Value<string>("id");
                    cat.name = item?.Value<string>("name");
                    cat.ordering = index;
                    index++;
                    if (!cat.id.IsNullOrEmpty())
                    {
                        SyncSubCategoryList(cat, 1);
                        list.Add(cat);
                    }
                });
            }
            return list;
        }

        private void SyncSubCategoryList(ProductCategory parentCategory, int level)
        {
            if (parentCategory == null || parentCategory.id.IsNullOrEmpty() || level > 2)
                return;

            var list = new List<ProductCategory>();
            Dictionary<string, string> dc = new Dictionary<string, string>();
            dc.Add("parentId", parentCategory.id);

            //开始请求接口                                                                                    
            var json = _client.Execute("product/v1.0/product/getCategoryList", dc);
            var resultJson = JsonCheck(json)?.Item3.ToList();
            if (resultJson != null && resultJson.Count() > 0)
            {
                var index = 1;
                resultJson.ForEach(item =>
                {
                    ProductCategory cat = new ProductCategory();
                    cat.id = item?.Value<string>("id");
                    cat.name = item?.Value<string>("name");
                    cat.ordering = (parentCategory.ordering.ToString2() + index).ToInt();
                    cat.pid = parentCategory.id;
                    index++;
                    parentCategory.subCategoryList.Add(cat);
                });

                parentCategory.subCategoryList?.ForEach(sub =>
                {
                    SyncSubCategoryList(sub, level++);
                });
            }
        }

        public bool UpdateOrderRemark(string orderId, string remark, string iconNumber, string iconTag = "")
        {
            var apiName = "PlatOrderBusinessService.updateRemark";
            var dic = new Dictionary<string, string>();
            dic.Add("orderId", orderId);
            dic.Add("remark", remark);
            dic.Add("appId", "5");  //5 - 综合电商 269 - 本地生活
            var result = _client.Execute(apiName, dic);
            var error = _client.GetErrorMessage(result);
            var isSuccess = error.Item1 == 0;
            return isSuccess;
            throw new NotImplementedException();
        }

        public string SyncOrdersJson(SyncOrderParametersModel parameter)
        {
            if (IsManualRefund()) return null;
            var end = parameter.EndTime.Value;
            var start = parameter.StartTime.Value;
            var dic = new Dictionary<string, string>();
            dic.Add("fieldList", "orderId,status,payType,totalAmount,discountAmount,paymentAmount,freightAmount,message,remark,createTime,updateTime,payTime,refundStatus,shopName,orderExpressInfo,orderDetailInfos"); //需要返回的字段列表，多个字段用半角逗号分隔，可选值为OrderInfo数据结构中所有字段。
            if (parameter.IsFullSync)
            {
                dic.Add("startTime", start.ToString("yyyy-MM-dd HH:mm:ss"));
                dic.Add("endTime", end.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            else
            {
                dic.Add("updateStartTime", start.ToString("yyyy-MM-dd HH:mm:ss"));
                dic.Add("updateEndTime", end.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            var pageSize = parameter.DefaultPageSize;
            if (pageSize <= 0 || pageSize > 200)
                pageSize = 1;
            var status = ParseOrderStatusToPlatformStatus(parameter.OrderStatus);
            dic.Add("status", status);
            dic.Add("pageNum", "1");
            dic.Add("pageCount", pageSize.ToString());
            var json = _client.Execute("trade/v1.0/orders/list", dic);
            if (string.IsNullOrEmpty(json))
                throw new LogicException("接口返回数据为空");
            return json;
        }

        /// <summary>
        /// 修改订单收件人信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public KeyValuePair<bool, string> UpdateReceiverInfo(UpdateReceiverModel model)
        {
            throw new NotImplementedException();
        }
        public MergeOrderQueryResponseModel MergeOrderQuery(List<MergeOrderQueryRequstModel> requests)
        {
            throw new NotImplementedException();
        }

        public int GetOrderCountByConditions(SyncOrderParametersModel syncOrderParameters)
        {
           return 0;
        }
		private bool IsManualRefund()
        {
            // 标志P_ShopExtension是否有手动退款
            if (_shop.ShopExtension != null)
            {
                var isShopExtRefund = _shop.ShopExtension.RefundExpiredTime != null &&  _shop.ShopExtension.ExpireTime != null && _shop.ShopExtension.ExpireTime <= _shop.ShopExtension.RefundExpiredTime;
                if (isShopExtRefund)
                {
                    //Log.WriteLine($"{_shop.ShopName}-{_shop.PlatformType}-{_shop.ShopExtension.AppKey}-->已经手动退款了");
                    throw new LogicException($"该店铺已经申请线下退款，中断同步");
                    return true;
                }
            }
            return false;
        }
    }
}
