using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Jd.ACES;
using Jd.Api;
using Jd.Api.Request;
using Jd.Api.Response;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services.PlatformService
{
    /// <summary>
    ///RunApi 的摘要说明
    /// </summary>
    public class JingDongApiClient
    {
        private string strAppKey = CustomerConfig.JingDongAppKey;
        private string strAppSecret = CustomerConfig.JingDongAppSecret;
        private string openApiUrl = "https://api.jd.com/routerjson";
        //本地调试使用
        //private string openApiUrl = CustomerConfig.JingDongIsApiUseProxy ? $"http://jdi.dgjapp.com/jingdong/?apiUrl=https://api.jd.com/routerjson" : "https://api.jd.com/routerjson";
        private string refreshToken = string.Empty;
        private string accessToken = string.Empty;
        //京东灰度环境-测试
        //private string openApiUrl = CustomerConfig.JingDongIsApiUseProxy ? "https://api-dev.jd.com/routerjson" : "https://api.jd.com/routerjson";
        private DateTime? lastRefreshTokenTime = null;
        private int RefreshTokenCount = 0; //防止死循环刷token
        private Shop _shop { get; set; }

        public JingDongApiClient(Shop shop)
        {
            if (shop == null)
                throw new ArgumentException("shop参数不能为空");
            _shop = shop;
            //_shop = new ShopService().ChoonseApp(_shop);
            strAppKey = !string.IsNullOrEmpty(_shop.AppKey) ? _shop.AppKey : strAppKey;
            strAppSecret = !string.IsNullOrEmpty(_shop.AppSecret) ? _shop.AppSecret : strAppSecret;
            refreshToken = _shop.RefreshToken;
            accessToken = _shop.AccessToken;
            if (_shop.ShopExtension != null && _shop.SystemVersion == "ForFxSystem")
            {
                strAppKey = _shop.ShopExtension.AppKey;
                strAppSecret = _shop.ShopExtension.AppSecret;
                refreshToken = _shop.ShopExtension.RefreshToken;
                accessToken = _shop.ShopExtension.AccessToken;
            }
        }

        public string GetStrAppKey()
        {
            return this.strAppKey;
        }

        public string RefreshToken(bool isExpired = false)
        {
            var shopService = new DianGuanJiaApp.Services.ShopService();
            // 判断是否有新增应用需判断ShopExtension是否有其他异步操作更新过了Token
            var shopExtensionService = new ShopExtensionService();
            if (_shop.ShopExtension != null)
            {
                var old = shopExtensionService.Get(_shop.ShopExtension.Id);
                //判断是否有其他异步操作更新过了Token，若更新了则直接获取更新过后的token
                if (old != null && old.LastRefreshTokenTime > _shop.LastRefreshTokenTime && !string.IsNullOrEmpty(old.AccessToken) && old.AccessToken != _shop.AccessToken)
                {
                    _shop.AccessToken = old.AccessToken;
                    _shop.LastRefreshTokenTime = old.LastRefreshTokenTime;
                    _tdeClient = TDEClient.GetInstance(openApiUrl, strAppKey, strAppSecret, _shop.AccessToken);
                    return _shop.AccessToken;
                }
            }
            else
            {
                var old = shopService.Get(_shop.Id);
                //判断是否有其他异步操作更新过了Token，若更新了则直接获取更新过后的token
                if (old != null && old.LastRefreshTokenTime > _shop.LastRefreshTokenTime && !string.IsNullOrEmpty(old.AccessToken) && old.AccessToken != _shop.AccessToken)
                {
                    _shop.AccessToken = old.AccessToken;
                    _shop.LastRefreshTokenTime = old.LastRefreshTokenTime;
                    _tdeClient = TDEClient.GetInstance(openApiUrl, strAppKey, strAppSecret, _shop.AccessToken);
                    return _shop.AccessToken;
                }
            }

            RefreshTokenCount++; //刷新token 次数
            var refresh_token = _shop.RefreshToken;
            string urlStr = $"https://open-oauth.jd.com/oauth2/refresh_token?app_key={strAppKey}&app_secret={strAppSecret}&grant_type=refresh_token&refresh_token={refresh_token}";
            var response = WebHelper.HttpWebRequest(urlStr);

            if (!string.IsNullOrEmpty(response))
            {
                var repResult = response.ToObject<dynamic>();
                string access_token = repResult?.access_token;
                _shop.RefreshToken = repResult?.refresh_token;
                _shop.AccessToken = access_token;
                //修改token
              
                if (string.IsNullOrWhiteSpace(access_token) == false)
                {
                    accessToken = repResult?.access_token;
                    refreshToken = repResult?.refresh_token;
                    shopService.UpdateShopAccessToken(_shop.Id, _shop.AppKey, _shop.AccessToken, _shop.RefreshToken);
                    _tdeClient = TDEClient.GetInstance(openApiUrl, strAppKey, strAppSecret, _shop.AccessToken);
                }
                else
                {
                    _shop.AccessToken = "";
                    Log.WriteError($"京东店铺【{_shop.ShopName}-{_shop.Id} 】刷新token错误：error：{response}");
                }
            }
            return _shop.AccessToken;
        }

        /// <summary>
        /// 调用京东api接口并返回数据，数据为json格式化后的string
        /// </summary>
        /// <param name="strAccToken">当前用户的access_token 通过登录授权后返回的code获取</param>
        /// <param name="paramDic">接口所需要的参数集合</param>
        /// <returns></returns>
        public string Execute<T>(IJdRequest<T> request) where T : JdResponse
        {
            return Retryer.Retry(() =>
            {
                var str = ExecuteWithRetry(request);
                return str;
            });
        }

        public string Execute<T>(IJdRequest<T> request,bool isWyb=false) where T : JdResponse
        {
            return Retryer.Retry(() =>
            {
                var str = ExecuteWithRetry(request, isWyb);
                return str;
            });
        }

        public string ExecuteWithRetry<T>(IJdRequest<T> request, bool isWyb = false) where T : JdResponse
        {
            string result;
            try
            {       
                var client = new DefaultJdClient(openApiUrl, strAppKey, strAppSecret);
                result = client.Execute_v2(request, accessToken, DateTime.Now.ToLocalTime());
                if (result == null)
                    throw new RetryException("京东接口请求返回对象为空，请求信息：" + request.ToJson());
                if (CustomerConfig.IsDebug)
                    Log.Debug($"请求参数：{request.ToJson()}，返回结果：{result}");
            }
            catch (Exception ex)
            {
                Utility.Log.WriteError($"调用京东接口报错：{request.ToJson()}，错误信息：{ex}");
                throw new LogicException("调用京东接口报错,请联系我们！");
            }

            //判断是否需要刷新access_token
            bool relToken = false;
            var error = GetError(result);
            if (string.IsNullOrWhiteSpace(error.Key) == false)
            {
                //43:系统处理错误
                //65:平台连接后端服务超时
                //66:平台连接后端服务不可用
                //67:平台连接后端服务处理过程中出现未知异常信息
                //69:获取数据失败
                //78:HTTP 调用异常
                //85:解密失败
                var err_cods = new List<string> { "43", "65", "66", "67", "69", "78", "85" };
                if (err_cods.Contains(error.Key) || err_cods.Contains(error.Key))
                {
                    throw new RetryException(GetErrMsg(error, request.ApiName));
                }

                //18 缺少access_token参数
                //19 无效access_token
                err_cods = new List<string> { "18", "19" };
                if (err_cods.Contains(error.Key) || err_cods.Contains(error.Key))
                {
                    relToken = true;
                }
                else {
                    throw new LogicException(GetErrMsg(error, request.ApiName));
                }
            }

            if (relToken)
            {
                var shopType = isWyb ? "京东电子面单账号" : "店铺";
                if (RefreshTokenCount > 3)
                    throw new LogicException($"{shopType}【{_shop.ShopName}】授权过期，请重新授权", "auth_expires");
                var access_token = RefreshToken(relToken);
                if (string.IsNullOrEmpty(access_token))
                    throw new LogicException($"{shopType}【{_shop.ShopName}】授权过期，请重新授权", "auth_expires");
                result = Execute(request);
            }

            return result;
        }

        /// <summary>
        /// 获取错误
        /// </summary>
        /// <param name="rsp"></param>
        /// <returns></returns>
        public KeyValuePair<string, string> GetError(string rsp)
        {
            try
            {
                rsp = rsp.ToString2().Trim().TrimStart("\"").TrimEnd("\"");
                var code = string.Empty;
                var jToken = JToken.Parse(rsp);
                //HasValues为false说明rsp被转义了，需要再反序列化一次，类似这种："{\"data\":{\"platformOrderNo\":\"291474387457\",\"waybillCodeList\":[\"9831105620251\"]},\"statusMessage\":\"调用成功\",\"statusCode\":0}"
                if (jToken != null && jToken.HasValues == false)
                    jToken = JToken.Parse(jToken.ToString());
                var errRsp = jToken.Value<JToken>("error_response");
                if (errRsp == null)
                {
                    return new KeyValuePair<string, string>(string.Empty, string.Empty);
                }
                var errCode = errRsp?.Value<string>("code");
                var errMsg = errRsp?.Value<string>("zh_desc");
                return new KeyValuePair<string, string>(errCode, errMsg);
            }
            catch (Exception ex)
            {
                Log.WriteError($"GetError获取返回状态，参数：{rsp}，异常信息：{ex}");
                throw new LogicException($"GetError获取返回状态，参数：{rsp}，异常信息：{ex.Message}");
            }
           
        }

        public string GetErrMsg(KeyValuePair<string, string> error, string apiName)
        {
            return $"京东接口【{apiName}】调用报错（ErrCode:{error.Key},ErrMsg:{error.Value}）";
        }

        private TDEClient _tdeClient = null;
        public TDEClient EncryptClient
        {
            get
            {
                if (_tdeClient == null)
                    _tdeClient = TDEClient.GetInstance(openApiUrl,strAppKey,strAppSecret,accessToken);
                return _tdeClient;
            }
        }
    }

}
