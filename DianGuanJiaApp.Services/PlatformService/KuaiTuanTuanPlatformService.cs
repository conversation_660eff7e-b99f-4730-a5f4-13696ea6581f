using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Other;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services.PlatformService
{
    public class KuaiTuanTuanPlatformService : BasePlatformService, IPlatformService, ISyncOrderJsonService
    {
        private KuaiTuanTuanApiClient _client = null;
        private Shop _shop = null;
        public KuaiTuanTuanPlatformService(Shop shop) : base(shop)
        {
            _client = new KuaiTuanTuanApiClient(shop);
            _shop = shop;
        }

        #region 未实现
        public UserInfo BasicMember()
        {
            throw new NotImplementedException();
        }

        public string ConfirmOrder(List<string> platformOrderIds)
        {
            throw new NotImplementedException();
        }

        public List<GetPriceRangesModel> DangPriceList(List<string> itemList)
        {
            throw new NotImplementedException();
        }

        public string GetAppPayUrl()
        {
            return "";
        }

        public string GetBuyerMemberIdByLoginId(string strLoginId)
        {
            throw new NotImplementedException();
        }

        public DateTime? GetExpiredTime()
        {
            return _shop.ExpireTime;
        }

        public List<KeyValuePair<string, string>> GetLoginIdByMemberId(string buyerMemberId)
        {
            throw new NotImplementedException();
        }

        public int GetLogisticsAddress()
        {
            return 2;
        }

        public bool SaveLogisticsAddress(LogisticsAddressModel model, out string errMsg)
        {
            errMsg = string.Empty;
            return true;
        }

        public string SaveOrderPrice(Dictionary<string, string> dc)
        {
            throw new NotImplementedException();
        }
        public bool UpdateOrderRemark(string orderId, string remark, string iconNumber, string iconTag = "")
        {
            return true;
        }
        public Shop RefreshShopToken()
        {
            return _shop;
        }
        public Shop SyncShopInfo()
        {
            return _shop;
        }
        public List<ProductCategory> CategoryList()
        {
            throw new NotImplementedException();
        }
        public List<DeliverySendOrderResultModel> OnlineResend(DeliverySendOrderModel model)
        {
            List<DeliverySendOrderResultModel> list = new List<DeliverySendOrderResultModel>();
            model.Orders.ForEach(x =>
            {
                DeliverySendOrderResultModel result = new DeliverySendOrderResultModel()
                {
                    IsSuccess = false,
                    ErrorCode = "PLATFORM_DOES_NOT_SUPPORT",
                    ExtErrorMessage = $"订单【{x.PlatformOrderId}】发货失败,平台暂时不支持二次发货"
                };
                list.Add(result);
            });
            return list;
        }
        #endregion

        public string GetPlatformApiOriginalData(string name, string parameter1 = "", string parameter2 = "", string parameter3 = "")
        {
            throw new NotImplementedException();
        }

        public int GetPlatformOrderNumber()
        {
            throw new NotImplementedException();
        }

        public Shop GetPlatformShopInfo()
        {
            return _shop;
        }

        #region 发货
        public List<DeliverySendOrderResultModel> OnlineSend(DeliverySendOrderModel model)
        {
            var orders = model.Orders;
            var ecm = model.ExpressCodeMapping;
            var logisticSendCode = model.ExpressCodeMapping.PlatformExpressCode;
            var requests = new List<KeyValuePair<int, Dictionary<string, string>>>();

            //用户勾选的数量
            var results = new List<DeliverySendOrderResultModel>();
            foreach (var orderEntity in orders)
            {
                var orderRequest = model.OrderRequests.FirstOrDefault(o => o.Id == orderEntity.Id);
                var result = new DeliverySendOrderResultModel
                {
                    ErrorCode = "",
                    ErrorMessage = "",
                    ExtErrorMessage = "",
                    IsSuccess = false,
                    OrderEntity = orderEntity,
                    OrderRequest = orderRequest
                };
                //订单信息
                var ois = orderRequest.OrderItems;
                var successOrderItems = new List<int>();
                var pids = orderEntity.OrderItems.Where(oi => orderRequest.OrderItems.Contains(oi.Id)).Select(oi => string.IsNullOrEmpty(oi.OrignalOrderId) ? oi.PlatformOrderId : oi.OrignalOrderId).Distinct().ToList();
                pids.ForEach(pid =>
                {
                    var curOrderItems = orderEntity.OrderItems.Where(oi => oi.PlatformOrderId == pid || oi.OrignalOrderId == pid);
                    var oids = curOrderItems.Select(oi => oi.Id).Distinct().OrderBy(id => id).ToList();
                    var soids = orderRequest.OrderItems.Where(id => oids.Contains(id)).OrderBy(id => id).Distinct().ToList();
                    var product_infos = curOrderItems.Where(c => soids.Contains(c.Id)).ToList();

                    var dic = new Dictionary<string, string>();
                    dic.Add("orderSn", pid);
                    dic.Add("logisticsId", logisticSendCode);
                    dic.Add("waybillNo", orderRequest.WaybillCode);
                    var isAllSend = string.Join(",", oids) == string.Join(",", soids);
                    //判断是否整单发货
                    if (!isAllSend)
                    {
                        //发货子单号列表，无子单号视为整单发货
                        var subOrderSnList = product_infos.Select(p => p.SubItemID).ToList();
                        dic.Add("subOrderSnList", subOrderSnList.ToJson());
                    }
                    var relPost = "";
                    var tuple = Tuple.Create<JToken, string, string>(null, "", "");
                    try
                    {
                        var errorCode = "";
                        var errorMessage = "";
                        if (CustomerConfig.IsRepairSendHistory())
                        {
                            result.IsSuccess = true;
                            successOrderItems.AddRange(soids);
                        }
                        else
                        {
                            relPost = _client.Execute("pdd.ktt.order.logistic.create", dic);
                            tuple = _client.GetErrorMessage(relPost);
                            errorCode = tuple.Item2;
                            errorMessage = tuple.Item3;
                        }

                        var response = tuple.Item1?.Value<JToken>("response");
                        if (response != null)
                        {
                            var success = response.Value<bool>("success");
                            if (success)
                            {
                                result.IsSuccess = true;
                                successOrderItems.AddRange(soids);
                            }
                            else
                            {
                                result.IsSuccess = false;
                                result.ErrorCode = response.Value<string>("errorCode");
                                result.ErrorMessage = response.Value<string>("errorMsg");
                            }
                        }
                    }
                    catch (LogicException ex)
                    {
                        result.OriginalResult = relPost;//原始内容
                        result.ErrorMessage += $"订单【{pid}】发货失败：{ex.Message}";
                        result.ErrorCode += "Logic Error </br>";
                    }
                    catch (Exception ex)
                    {
                        Utility.Log.WriteError($"快团团【{pid}】发货失败：请求参数：{dic.ToJson()} 返回：{relPost} 错误：{ex}");
                        result.ErrorMessage += $"订单【{pid}】发货失败：系统错误";
                        result.ErrorCode += "System Error </br>";
                    }
                });
                if (!successOrderItems.Any())
                {
                    result.IsSuccess = false;
                    successOrderItems = ois;
                }
                else
                    result.OrderRequest.OrderItems = successOrderItems;
                results.Add(result);
            }
            return results;
        }
        #endregion

        public bool Ping()
        {
            try
            {
                var dict = new Dictionary<string, string>();
                dict.Add("page", "1");
                dict.Add("size", "1");
                var relPost = _client.Execute("pdd.ktt.goods.query.list", dict);
                var tuple = _client.GetErrorMessage(relPost);
                var errorCode = tuple.Item2;
                var errorMessage = tuple.Item3;
                var goodsToken = tuple.Item1.Value<JToken>("ktt_goods_query_list_response");
                return true;
            }
            catch (Exception ex)
            {
                //只有授权过期的错误，才说明店铺是授权过期了，不然会误判店铺授权过期，弹出授权的窗口
                var errMsg = ex?.Message.ToString2() ?? "";
                if (errMsg.Contains("授权过期") == true || errMsg.Contains("授权已过期") == true)
                    throw ex;
                return true;
            }
        }

        #region 订单
        public List<string> SyncOrderJsons(List<string> pids)
        {
            var orders = new ConcurrentBag<string>();
            if (pids == null || !pids.Any())
                return new List<string>();

            Parallel.ForEach(pids, new ParallelOptions { MaxDegreeOfParallelism = 5 }, pid =>
            {
                var o = SyncOrderJson(pid);
                if (o != null)
                    orders.Add(o);
            });
            return orders.ToList();
        }

        public string SyncOrderJson(string platformOrderId)
        {
            if (string.IsNullOrWhiteSpace(platformOrderId))
                return string.Empty;

            var dict = new Dictionary<string, string>();
            dict.Add("order_sn", platformOrderId);
            var json = _client.Execute("pdd.ktt.order.get", dict);
            return json;
        }

        public Order SyncOrder(string platformOrderId)
        {
            var order = new Order();
            if (string.IsNullOrWhiteSpace(platformOrderId))
                return order;

            var relPost = SyncOrderJson(platformOrderId);
            if (string.IsNullOrWhiteSpace(relPost))
                return order;

            var tuple = _client.GetErrorMessage(relPost);
            var errorCode = tuple.Item2;
            var errorMessage = tuple.Item3;
            var orderToken = tuple.Item1.Value<JToken>("ktt_order_get_response");
            if (orderToken != null)
            {
                var order_info = orderToken?.Value<JToken>("order_info");
                order = TransferJTokenToOrder(order_info);
            }
            if (!string.IsNullOrEmpty(errorCode))
                throw new LogicException($"快团团订单查询接口返回错误：{errorMessage} {errorCode}");
            return order;
        }

        public List<Order> SyncOrders(List<string> pids)
        {
            var orders = new ConcurrentBag<Order>();
            if (pids == null || !pids.Any())
                return new List<Order>();

            Parallel.ForEach(pids, new ParallelOptions { MaxDegreeOfParallelism = 5 }, pid =>
            {
                var o = SyncOrder(pid);
                if (o != null)
                    orders.Add(o);
            });
            return orders.ToList();
        }
        private int GetTotalCountByRangeTime(DateTime startTime, DateTime endTime, SyncOrderParametersModel parameter)
        {
            var orders = new List<Order>();
            int pageIndex = 1, pageSize = 1;
            var dict = new Dictionary<string, string>();
            dict.Add("start_updated_at", DateConverter.ConvertDateTimeToInt(startTime).ToString());
            dict.Add("end_updated_at", DateConverter.ConvertDateTimeToInt(endTime).ToString());
            var shipping_status = ParseOrderStatusToPlatformStatus(parameter.OrderStatus);
            if (shipping_status != "")
                dict.Add("shipping_status", shipping_status);
            dict.Add("page_number", pageIndex.ToString());
            dict.Add("page_size", pageSize.ToString());
            var relPost = _client.Execute("pdd.ktt.increment.order.query", dict);
            var tuple = _client.GetErrorMessage(relPost);
            var errorCode = tuple.Item2;
            var errorMessage = tuple.Item3;
            var totalRecord = 0;
            var orderToken = tuple.Item1.Value<JToken>("ktt_increment_order_query_response");
            if (orderToken != null)
            {
                var jarray = orderToken.Value<JArray>("order_list");
                totalRecord = orderToken.Value<int>("total_count");
            }
            return totalRecord;
        }

        /// <summary>
        /// 使用增量同步接口,可以同步90天的数据
        /// </summary>
        /// <param name="parameter"></param>
        /// <returns></returns>
        public List<Order> SyncOrders(SyncOrderParametersModel parameter, bool isPinged = false)
        {
            var currTime = DateTime.Now;
            var startTime = (parameter.StartTime ?? currTime.AddDays(-30)).AddMinutes(-3);
            var endTime = (parameter.EndTime ?? currTime).AddMinutes(1);

            parameter.StartTime= startTime;
            parameter.EndTime = endTime;

            //var requests = new List<SyncOrderParametersModel>();
            //var maxTimeSpan = 60 * 4; //跨度不能大于一天
            //var totalDays = (endTime - startTime).TotalMinutes;    // 相差总分钟
            //var count = Math.Ceiling(totalDays / maxTimeSpan);
            //if (count > 1)
            //{
            //    var index = 0;
            //    while (count > 0)
            //    {
            //        var tmpStartTime = startTime.AddMinutes(index * maxTimeSpan);
            //        var tmpEndTime = tmpStartTime.AddMinutes(maxTimeSpan);

            //        if (tmpStartTime > endTime)
            //            currTime.AddMinutes(-3);
            //        if (tmpEndTime > endTime)
            //            tmpEndTime = currTime;

            //        var request = parameter.ToJson().ToObject<SyncOrderParametersModel>();
            //        request.StartTime = tmpStartTime;
            //        request.EndTime = tmpEndTime;
            //        requests.Add(request);
            //        count--;
            //        index++;
            //    }
            //}
            //else
            //{
            //    requests.Add(parameter);
            //}

            //对时间再次切片，防止时间段内数据翻页报错
            var requests = new List<SyncOrderParametersModel>();
            Dictionary<DateTime, DateTime> timeLines = new Dictionary<DateTime, DateTime>();
            SplitSyncTime(parameter, timeLines, 9000, GetTotalCountByRangeTime);
            foreach (var item in timeLines)
            {
                var req = parameter.ToJson().ToObject<SyncOrderParametersModel>();
                req.StartTime = item.Key;
                req.EndTime = item.Value;
                requests.Add(req);
            }
            //Log.Debug($"startTime={startTime.Format()},endTime={endTime.Format()},\nparameter==>{parameter.ToJson()}\nrequests==>{requests.ToJson()}");

            var allOrders = new ConcurrentBag<Order>();
            Parallel.ForEach(requests, new ParallelOptions { MaxDegreeOfParallelism = 10 }, (req) =>
            {
                var orders = SyncPageOrders(req);
                orders.ForEach(o => allOrders.Add(o));
                CheckMaxSyncOrder(allOrders.Count);
            });
            return allOrders.ToList();
        }

        private List<Order> SyncPageOrders(SyncOrderParametersModel parameter)
        {
            var orders = new List<Order>();
            int pageIndex = 1, pageSize = 100;
            var dict = new Dictionary<string, string>();
            dict.Add("start_updated_at", DateConverter.ConvertDateTimeToInt(parameter.StartTime.Value).ToString());
            dict.Add("end_updated_at", DateConverter.ConvertDateTimeToInt(parameter.EndTime.Value).ToString());
            var shipping_status = ParseOrderStatusToPlatformStatus(parameter.OrderStatus);
            if (shipping_status != "")
                dict.Add("shipping_status", shipping_status);
            dict.Add("page_number", pageIndex.ToString());
            dict.Add("page_size", pageSize.ToString());
            var relPost = _client.Execute("pdd.ktt.increment.order.query", dict);
            var tuple = _client.GetErrorMessage(relPost);
            var errorCode = tuple.Item2;
            var errorMessage = tuple.Item3;
            var totalRecord = 0;
            var orderToken = tuple.Item1.Value<JToken>("ktt_increment_order_query_response");
            if (orderToken != null)
            {
                var jarray = orderToken.Value<JArray>("order_list");
                totalRecord = orderToken.Value<int>("total_count");
                if (jarray != null && jarray.Count() > 0)
                {
                    jarray.ToList().ForEach(item =>
                    {
                        orders.Add(TransferJTokenToOrder(item));
                    });
                }
            }
            if (!string.IsNullOrEmpty(errorCode))
                throw new LogicException($"快团团订单列表接口返回错误：{errorMessage} {errorCode}");

            if (totalRecord > pageSize)
            {
                int pageCount = (int)Math.Ceiling(totalRecord / (float)pageSize);
                for (int i = pageCount; i > 1; i--)
                {
                    dict["page_number"] = i.ToString();
                    var subDic = dict.ToJson().ToObject<Dictionary<string, string>>();
                    var subrelPost = _client.Execute("pdd.ktt.increment.order.query", subDic);
                    var subtuple = _client.GetErrorMessage(subrelPost);
                    var suberrorCode = subtuple.Item2;
                    var suberrorMessage = subtuple.Item3;
                    var suborderToken = subtuple.Item1.Value<JToken>("ktt_increment_order_query_response");
                    if (suborderToken != null)
                    {
                        var jarray = suborderToken.Value<JArray>("order_list");
                        if (jarray != null && jarray.Count() > 0)
                        {
                            jarray.ToList().ForEach(item =>
                            {
                                orders.Add(TransferJTokenToOrder(item));
                            });
                        }
                    }
                    if (!string.IsNullOrEmpty(suberrorCode))
                    {
                        Log.WriteError($"快团团订单列表【{i}】页接口返回总订单数：{totalRecord}，总页数：{pageCount}，请求参数：{subDic.ToJson()}，错误信息：{subrelPost}");
                        throw new LogicException($"快团团订单列表【{i}】页接口返回总订单数：{totalRecord}，请求参数：{subDic.ToJson()}，错误信息：{suberrorMessage} {suberrorCode}");
                    }
                }
            }

            if (totalRecord != orders.Count())
                Log.WriteWarning($"快团团同步订单时，实际数量({orders.Count()})与接口总数量({totalRecord})不一致，同步参数：{parameter.ToJson()}");
            return orders;
        }
        private Order TransferJTokenToOrder(JToken jtoken)
        {
            var order = new Order();
            try
            {
                order.PlatformOrderId = jtoken?.Value<string>("order_sn");
                order.ShopId = _shop.Id;
                order.PlatformType = _shop.PlatformType;
                var shipping_status = jtoken?.Value<string>("shipping_status");
                order.PlatformStatus = ParseOrderStatusToPlatformStatus(shipping_status).ToString();
                order.BackUpPlatformStatus = order.PlatformStatus;
                //售后状态
                var after_sales_status = jtoken?.Value<string>("after_sales_status");
                order.RefundStatus = ParseOrderStatusToRefundStatus(after_sales_status)?.ToString();
                order.RefundPayment = jtoken?.Value<decimal>("theoretical_refund_amount") / 100;
                //取消状态 0-未取消 1-已取消
                var cancel_status = jtoken?.Value<string>("cancel_status");
                if (cancel_status == "1")
                    order.PlatformStatus = OrderStatusType.cancel.ToString();

                //物流方式 0-无需物流 10-普通快递 20-自提 30-同城配送
                order.BusinessType = jtoken?.Value<string>("logistics_type");
                //订单来源 0-普通团 1-店铺 2-积分商城
                order.TradeType = jtoken?.Value<string>("mall_activity_type");

                //团员昵称
                order.BuyerWangWang = jtoken.Value<string>("nick_name");
                order.BuyerMemberId = order.BuyerWangWang;

                //私密备注
                order.PlatformRemark = jtoken?.Value<string>("secret_remark");
                //团长备注
                order.SellerRemark = jtoken?.Value<string>("business_note");
                //团员备注
                order.BuyerRemark = jtoken?.Value<string>("buyer_memo");

                //成交时间
                order.ModifyTime = DateConverter.GetTime(jtoken?.Value<string>("updated_at"));
                order.ConfirmedTime = DateConverter.GetTime(jtoken?.Value<string>("confirm_at"));
                order.PayTime = order.ConfirmedTime;
                order.CreateTime = order.ConfirmedTime;

                order.TotalAmount = jtoken?.Value<decimal>("order_amount") / 100;//单位：分
                order.ShippingFee = jtoken?.Value<decimal>("shipping_amount") / 100;//单位：分
                                                                                    //优惠金额(分)
                order.Discount = jtoken?.Value<decimal>("discount_amount") / 100;
                //平台优惠金额(分)
                var platform_discount_amount = jtoken?.Value<decimal>("platform_discount_amount");

                order.ToName = jtoken.Value<string>("receiver_name");
                order.ToPhone = "";
                order.ToMobile = jtoken.Value<string>("receiver_mobile");
                order.ToPost = "";
                order.ToProvince = jtoken.Value<string>("receiver_address_province");
                order.ToCity = jtoken.Value<string>("receiver_address_city");
                order.ToCounty = jtoken.Value<string>("receiver_address_district");
                order.ToAddress = jtoken.Value<string>("receiver_address_detail");
                var pcc = order.ToProvince + order.ToCity + order.ToCounty;
                order.ToFullAddress = order.ToAddress.StartsWith(pcc) ? order.ToAddress : (pcc + order.ToAddress);


                //购买商品列表
                var sub_order_list = jtoken?.Value<JArray>("sub_order_list").ToList();
                if (sub_order_list != null && sub_order_list.Count > 0)
                {
                    sub_order_list.ForEach(p =>
                    {
                        var oi = new OrderItem();
                        oi.CreateTime = DateTime.Now;
                        oi.Count = p?.Value<int>("goods_number");
                        oi.Price = p?.Value<decimal>("goods_price") / 100;
                        var sub_shipping_status = jtoken?.Value<string>("shipping_status");
                        oi.Status = ParseOrderStatusToPlatformStatus(sub_shipping_status).ToString();
                        var sub_cancel_status = p?.Value<string>("cancel_status");
                        if (sub_cancel_status == "1")
                            oi.Status = OrderStatusType.cancel.ToString();

                        oi.ProductImgUrl = p?.Value<string>("thumb_url");
                        oi.ItemAmount = oi.Price * oi.Count;
                        oi.ProductSubject = p?.Value<string>("goods_name");
                        oi.ProductID = p?.Value<string>("goods_id");
                        oi.RefundAmount = p?.Value<decimal>("theoretically_refund_amount") / 100;
                        if (oi.RefundAmount != null && oi.RefundAmount > 0)
                        {
                            //oi.RefundStatus = RefundStatusType.REFUND_SUCCESS.ToString();
                            oi.RefundStatus = order.RefundStatus;
                        }

                        oi.SkuID = p?.Value<string>("sku_id");
                        oi.SpecId = p?.Value<string>("sku_id");
                        oi.SubItemID = p?.Value<string>("sub_order_sn");
                        oi.PlatformOrderId = order.PlatformOrderId;
                        oi.ShopId = order.ShopId;
                        oi.CargoNumber = p?.Value<string>("external_sku_id");
                        //oi.productCargoNumber = p?.Value<string>("external_sku_id");
                        var goods_specification = p?.Value<string>("goods_specification")??"";
                        if(goods_specification.IsNotNullOrEmpty())
                        {
                            var specification = goods_specification.SplitToList(",");
                            oi.Color = specification?.FirstOrDefault()??"";
                            var count = specification.Count();
                            if (count > 1)
                            {
                                oi.Color = "";
								oi.Size = specification[0];
                                for (int i = 1; i < count; i++)
                                {
                                    oi.Color += specification[i] + ",";
                                }
                                oi.Color = oi.Color.TrimEnd(",");
                            }
                        }
                        
                        order.OrderItems.Add(oi);
                    });
                }

                //赠品列表
                var gift_order_list = jtoken?.Value<JArray>("gift_order_list")?.ToList();

                //售后信息查询
                OrderRefundGet(order);

                //重新判断发货状态
                //var refunItemCount = order.OrderItems.Count(oi => string.IsNullOrWhiteSpace(oi.RefundStatus));
                //var sendItemCount = order.OrderItems.Count(oi => oi.Status == "waitbuyerreceive");
                //if((refunItemCount + sendItemCount) == order.OrderItems.Count)
                //{
                //    order.PlatformStatus = OrderStatusType.waitbuyerreceive.ToString();
                //}
            }
            catch (Exception e)
            {
                Log.WriteError($"快团团{_shop.NickName}解析订单【{order?.PlatformOrderId}】失败：{e}");
            }
            return order;
        }

        /// <summary>
        /// 生成订单查询参数
        /// </summary>
        /// <param name="startTime">起始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">页尺寸</param>
        /// <param name="parameter">同步参数</param>
        /// <returns></returns>
        private Dictionary<string, string> BuildGetOrderParameter(DateTime startTime, DateTime endTime, int page, int pageSize, SyncOrderParametersModel parameter)
        {
            var dict = new Dictionary<string, string>();
            dict.Add("start_updated_at", DateConverter.ConvertDateTimeToInt(startTime).ToString());
            dict.Add("end_updated_at", DateConverter.ConvertDateTimeToInt(endTime).ToString());
            dict.Add("shipping_status", ParseOrderStatusToPlatformStatus(parameter.OrderStatus));
            dict.Add("page_number", page.ToString());
            dict.Add("page_size", pageSize.ToString());
            return dict;
        }

        /// <summary>
        /// 通过父单号获取对应订单的售后单列表
        /// </summary>
        private void OrderRefundGet(Order order)
        {
            if (string.IsNullOrWhiteSpace(order.PlatformOrderId))
                return;

            var dict = new Dictionary<string, string>();
            dict.Add("order_sn", order.PlatformOrderId);
            dict.Add("page_number", "1");
            dict.Add("page_size", "5");
            var relPost = _client.Execute("pdd.ktt.order.refund.get", dict);
            var tuple = _client.GetErrorMessage(relPost);
            var errorCode = tuple.Item2;
            var errorMessage = tuple.Item3;
            var orderToken = tuple.Item1.Value<JToken>("ktt_refund_get_response");
            if (orderToken != null)
            {
                var list = orderToken?.Value<JArray>("list").ToList();
                list.ForEach(o =>
                {
                    var order_sn = o?.Value<string>("order_sn");
                    if (order_sn == order.PlatformOrderId)
                    {
                        var after_sales_status = o?.Value<string>("after_sales_status");
                        order.RefundStatus = ParseOrderStatusToRefundStatus(after_sales_status)?.ToString();

                        //售后单用户申请信息
                        var apply_extension = o?.Value<JToken>("apply_extension");
                        order.RefundPayment = o?.Value<decimal>("refund_amount") / 100;

                        //申请售后的子单信息列表
                        var sub_extensions = apply_extension?.Value<JArray>("sub_extensions").ToList();
                        sub_extensions.ForEach(sub =>
                        {
                            var sub_order_sn = sub?.Value<string>("sub_order_sn");
                            order.OrderItems.ForEach(oi =>
                            {
                                if (oi.SubItemID == sub_order_sn)
                                {
                                    oi.RefundAmount = sub?.Value<decimal>("refund_amount") / 100;
                                    //oi.RefundStatus = RefundStatusType.WAIT_SELLER_AGREE.ToString();
                                    //if (after_sales_status == "2")
                                    //    oi.RefundStatus = RefundStatusType.REFUND_SUCCESS.ToString();
                                    oi.RefundStatus = order.RefundStatus;
                                }
                            });
                        });
                    }
                });
            }
        }
        #endregion

        #region 商品

        public List<Product> SyncProduct(int statusType, DateTime? start = null, DateTime? end = null,
            bool isNotPinged = false)
        {
            ConcurrentBag<Product> products = new ConcurrentBag<Product>();
            var dict = new Dictionary<string, string>();
            dict.Add("page", "1");
            dict.Add("size", "1");
            if(start != null)
            {
                dict.Add("update_time_start", start.Value.ToStamp(DateTimeExtension.DateTimeUnit.Milliseconds, false).ToString());
            }
            if(end != null)
            {
                dict.Add("update_time_end", end.Value.ToStamp(DateTimeExtension.DateTimeUnit.Milliseconds, false).ToString());
            }
            var relPost = _client.Execute("pdd.ktt.goods.query.list", dict);
            var tuple = _client.GetErrorMessage(relPost);
            var errorCode = tuple.Item2;
            var errorMessage = tuple.Item3;
            var goodsToken = tuple.Item1.Value<JToken>("ktt_goods_query_list_response");
            if (goodsToken != null)
            {
                int pageSize = 100;
                int total_num = goodsToken.Value<int>("total");
                int pageCount = (int)Math.Ceiling(total_num / (float)pageSize);
                var dicts = new List<Dictionary<string, string>>();
                for (int i = 1; i <= pageCount; i++)
                {
                    dict["page"] = i.ToString();
                    dict["size"] = pageSize.ToString();
                    var subDict = dict.ToJson().ToObject<Dictionary<string, string>>();
                    dicts.Add(subDict);
                }

                Parallel.ForEach(dicts, new ParallelOptions { MaxDegreeOfParallelism = 5 }, (req) =>
                {
                    var subrelPost = _client.Execute("pdd.ktt.goods.query.list", req);
                    var subtuple = _client.GetErrorMessage(subrelPost);
                    var suberrorCode = subtuple.Item2;
                    var suberrorMessage = subtuple.Item3;
                    var subgoodsToken = subtuple.Item1.Value<JToken>("ktt_goods_query_list_response");
                    if (subgoodsToken != null)
                    {
                        var goods_list = subgoodsToken?.Value<JArray>("goods_list")?.ToList();
                        if (goods_list != null && goods_list.Count > 0)
                        {
                            goods_list.ForEach(g =>
                            {
                                var p = TransferJsonToProduct(g);
                                products.Add(p);
                            });
                        }
                    }
                });
            }
            if (!string.IsNullOrEmpty(errorCode))
                throw new LogicException($"快团团商品列表接口返回错误：{errorMessage} {errorCode}");
            return products.ToList();
        }

        public Product SyncProduct(string platformId)
        {
            var product = new Product();
            if (string.IsNullOrWhiteSpace(platformId))
                return product;

            var dict = new Dictionary<string, string>();
            dict.Add("goods_id", platformId);
            var relPost = _client.Execute("pdd.ktt.goods.query.single", dict);
            var tuple = _client.GetErrorMessage(relPost);
            var errorCode = tuple.Item2;
            var errorMessage = tuple.Item3;
            var response = tuple.Item1.Value<JToken>("response");
            if (response != null)
            {
                var result = response?.Value<JToken>("result");
                product = TransferJsonToProduct(result);
            }
            if (!string.IsNullOrEmpty(errorCode))
                throw new LogicException($"快团团商品查询接口返回错误：{errorMessage} {errorCode}");
            return product;
        }

        private Product TransferJsonToProduct(JToken _token)
        {
            var product = new Product();
            try
            {
                product.ShopId = _shop.Id;
                product.ShopName = _shop.NickName;
                product.PlatformType = _shop.PlatformType;
                product.PlatformId = _token?.Value<string>("goods_id");
                product.Subject = _token?.Value<string>("goods_name");
                product.CreateTime = DateTime.Now;
                product.CategoryId = null;
                product.CategoryName = _token?.Value<string>("category_name");
                product.ShortTitle = "";
                product.Weight = 0;
                product.SetWeight = 0;
                product.GroupID = "";
                product.Bazaar = "";
                product.Stall = "";
                product.CostPrice = 0;
                product.IsComm = false;
                product.IsSetWeight = false;


                //详情
                product.Description = _token.Value<string>("goods_desc");
                //图片列表
                var imageUrls = _token?.Value<JArray>("goods_image_list");
                if (imageUrls != null && imageUrls.Any())
                {
                    product.ImageUrls = imageUrls.Select(a => a.ToString()).ToList();
                }

                var is_activity_delete = _token?.Value<string>("is_activity_delete");
                product.Status = is_activity_delete == "1" ? "deleted" : "published";
                var goods_image_list = _token?.Value<JArray>("goods_image_list");
                if (goods_image_list != null && goods_image_list.Count > 0)
                    product.ImageUrl = goods_image_list.First.ToString();
                var sku_list = _token?.Value<JArray>("sku_list")?.ToList();
                if (sku_list != null && sku_list.Count > 0)
                {
                    sku_list.ForEach(p =>
                    {
                        var sku = new ProductSku();
                        sku.SkuId = p?.Value<string>("sku_id");
                        sku.SpecId = sku.SkuId;
                        sku.Price = p?.Value<decimal>("price_in_fen") / 100;
                        sku.CargoNumber = p?.Value<string>("external_sku_id");
                        var thumb_url = p?.Value<string>("thumb_url");
                        var quantity = p?.Value<int>("quantity");
                        var quantity_type = p?.Value<int>("quantity_type");


                        var spec_name = p?.Value<string>("spec_name")?.Split(',');
                        var attrName = spec_name[0];
                        var attrValue = "";
                        if(spec_name.Length == 2)
                            attrValue = spec_name[1];
                        else if(spec_name.Length > 2)
                        {
                            //不确定规格有多少就全部给一个字段。
                            attrName = p?.Value<string>("spec_name");
                        }
                        var psa = new ProductSkuAttribute()
                        {
                            AttributeName = attrName,
                            AttributeValue = attrValue,
                            ShortTitle = "",
                            Weight = 0,
                            Bazaar = "",
                            Stall = "",
                            SkuImgUrl = thumb_url
                        };
                        sku.ProductSkuAttr = psa;

                        var skuProps = p?.Value<JArray>("spec_list"); 
                        skuProps?.ToList().ForEach(skuProp =>
                        {
                            //属性k-v值
                            var att_k = skuProp?.Value<string>("parent_name") ?? "";
                            var att_v = skuProp?.Value<string>("name") ?? "";
                            if (att_k.IsNullOrEmpty() == false && att_v.IsNullOrEmpty() == false && sku.SkuAttrs.Any(a => a.k == att_k) == false)
                            {
                                sku.SkuAttrs.Add(new SkuAttr { k = att_k, v = att_v });
                            }
                        });

                        product.Skus.Add(sku);
                    });
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"快团团{_shop.NickName}解析商品【{product?.PlatformId}】失败：{e}");
            }
            return product;
        }
        #endregion

        #region 状态
        /// <summary>
        /// 快团团平台订单状态
        /// </summary>
        public enum PlatformOrderStatusType
        {
            /// <summary>
            /// 待发货
            /// </summary>
            Delivered = 0,
            /// <summary>
            /// 待收货
            /// </summary>
            Delivery = 1,
            /// <summary>
            /// 交易成功
            /// </summary>
            Success = 3
        }

        /// <summary>
        /// 我们系统的订单状态转换为团好货平台订单状态
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private string ParseOrderStatusToPlatformStatus(OrderStatusType? status)
        {
            if (status == null)
                return "";
            PlatformOrderStatusType pos = PlatformOrderStatusType.Delivered; ;
            switch (status)
            {
                case OrderStatusType.waitsellersend:
                    pos = PlatformOrderStatusType.Delivered;
                    break;
                case OrderStatusType.waitbuyerreceive:
                    pos = PlatformOrderStatusType.Delivery;
                    break;
                case OrderStatusType.success:
                    pos = PlatformOrderStatusType.Success;
                    break;
                default:
                    break;
            }
            return pos.ToInt().ToString();
        }

        /// <summary>
        /// 平台订单状态转换为我们系统的订单状态
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private OrderStatusType ParseOrderStatusToPlatformStatus(string status)
        {
            OrderStatusType orderStatusType = OrderStatusType.waitsellersend;
            switch (status)
            {
                case "0":
                    orderStatusType = OrderStatusType.waitsellersend;
                    break;
                case "1":
                case "2":
                    orderStatusType = OrderStatusType.waitbuyerreceive;
                    break;
                case "3":
                    orderStatusType = OrderStatusType.success;
                    break;
                default:
                    break;
            }
            return orderStatusType;
        }

        /// <summary>
        /// 平台退款状态 转换为 我们系统的退款状态
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private RefundStatusType? ParseOrderStatusToRefundStatus(string platformRefundStatus)
        {
            //售后状态, 可选 0 - 未发起售后 1 - 退款中 2 - 退款成功 3 - 待处理 4 - 拒绝退款 6 - 待(顾客)退货 7 - 待(团长)确认退货 8 - (顾客)撤销 9 - (系统)关闭
            RefundStatusType? rs = null;
            if (platformRefundStatus.IsNullOrEmpty() || platformRefundStatus == "0")
                return rs;
            switch (platformRefundStatus)
            {
                case "8":
                case "9":
                    rs = RefundStatusType.REFUND_CLOSE;
                    break;
                case "1":
                case "3":
                case "6":
                case "7":
                    rs = RefundStatusType.WAIT_SELLER_AGREE;
                    break;
                case "2":
                    rs = RefundStatusType.REFUND_SUCCESS;
                    break;
                default:
                    break;
            }
            return rs;
        }
        #endregion

        
        public string SyncOrdersJson(SyncOrderParametersModel parameter)
        {
            var pageSize = parameter.DefaultPageSize;
            if (pageSize <= 0 || pageSize > 200)
                pageSize = 1;
            var startTime = parameter.StartTime.Value;
            var endTime = parameter.EndTime.Value;
            var dict = new Dictionary<string, string>();
            dict.Add("start_updated_at", DateConverter.ConvertDateTimeToInt(startTime).ToString());
            dict.Add("end_updated_at", DateConverter.ConvertDateTimeToInt(endTime).ToString());
            var shipping_status = ParseOrderStatusToPlatformStatus(parameter.OrderStatus);
            if (shipping_status != "")
                dict.Add("shipping_status", shipping_status);
            dict.Add("page_number", "1");
            dict.Add("page_size", pageSize.ToString());
            var json = _client.Execute("pdd.ktt.increment.order.query", dict);
            if (string.IsNullOrEmpty(json))
                throw new LogicException("接口返回数据为空");
            return json;
        }

        /// <summary>
        /// 修改订单收件人信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public KeyValuePair<bool, string> UpdateReceiverInfo(UpdateReceiverModel model)
        {
            throw new NotImplementedException();
        }

        public MergeOrderQueryResponseModel MergeOrderQuery(List<MergeOrderQueryRequstModel> requests)
        {
            throw new NotImplementedException();
        }

        public int GetOrderCountByConditions(SyncOrderParametersModel syncOrderParameters)
        {
            return 0;
        }
    }
}
