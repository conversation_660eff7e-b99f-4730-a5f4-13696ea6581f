using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using JuHaoMai = DianGuanJiaApp.Data.Model.OtherPlatforms.JuHaoMai;

namespace DianGuanJiaApp.Services.PlatformService.OtherPlatforms
{
    public class JuHaoMaiPlatformService : BasePlatformService, IPlatformService, IAfterSaleService
    {
        #region 私有变量

        private JuHaoMaiApiClient _client = null;
        private Shop _shop = null;

        private int _pageWaitTime;

        /// <summary>
        /// 订单分页等待时间（单位毫秒）
        /// </summary>
        private int PageWaitTime
        {
            get
            {
                if (_pageWaitTime != 0)
                    return _pageWaitTime;
                var waitTime = RedisHelper.Get<int>(CacheKeys.JuHaoMaiSyncOrderPageWaitTime);
                if (waitTime == 0)
                    waitTime = 1000;//1秒
                _pageWaitTime = waitTime;
                return _pageWaitTime;
            }
        }

        #endregion 私有变量

        #region 构造函数

        public JuHaoMaiPlatformService(Shop shop) : base(shop)
        {
            _client = new JuHaoMaiApiClient(shop);
            _shop = shop;
        }

        #endregion 构造函数

        #region 私有方法

        /// <summary>
        /// 根据聚好麦订单状态解析成订单状态
        /// </summary>
        /// <param name="platformOrderStatus"></param>
        /// <param name="waybill">通过是否有物流单号来判断售后中是已发货售后中还是待发货售后中</param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        private OrderStatusType? ParsePlatformStatusToOrderStatus(string platformOrderStatus, string waybill)
        {
            //订单状态1：待支付，2：待发货，3：待收货，4：已完成，5：售后中，-1已取消，6：支付失败
            if (string.IsNullOrWhiteSpace(platformOrderStatus))
                return null;
            OrderStatusType? os = null;
            switch (platformOrderStatus)
            {
                case "6":
                case "1":
                    os = OrderStatusType.waitbuyerpay;
                    break;

                case "2":
                    os = OrderStatusType.waitsellersend;
                    break;

                case "3":
                    os = OrderStatusType.waitbuyerreceive;
                    break;

                case "4":
                    os = OrderStatusType.success;
                    break;

                case "5":
                    //售后中状态 可能是已发货或者待发货
                    if (waybill.IsNotNullOrEmpty())//已发货售后中
                        os = OrderStatusType.waitbuyerreceive;
                    else //待发货售后中
                        os = OrderStatusType.waitsellersend;
                    break;

                case "-1":
                    os = OrderStatusType.cancel;
                    break;

                default:
                    Log.WriteLine("无法识别聚好麦订单状态：" + platformOrderStatus);
                    throw new LogicException("无法识别聚好麦订单状态：" + platformOrderStatus);
            }
            return os;
        }

        /// <summary>
        /// 聚好麦退款状态转系统退款状态
        /// </summary>
        /// <param name="afterSaleStatus"></param>
        /// <param name="num">购买数量</param>
        /// <param name="refundNum">售后数量 判断是全部退款还是部分退款</param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        private RefundStatusType? ParsePlatformAfterSaleStatusToRefundStatus(string afterSaleStatus, int num, int refundNum)
        {
            //售后状态（待处理1.待卖家退货2.待商家收货3.换货待商家发货4.待买家收获5.退款完成6.售后关闭7.退款中8.极速退款成功9.售后完成10.待买家处理11.退款失败12） 13.用户取消售后
            //平台增加 15：售后协商 16：商家拒绝换货发货-待买家确认
            RefundStatusType? rs = null;
            if (string.IsNullOrWhiteSpace(afterSaleStatus))
                return rs;
            //判断是否部分退款 购买数量 != 退款数量
            refundNum = refundNum == 0 ? num : refundNum;
            var isPart = num != refundNum;
            switch (afterSaleStatus.ToUpper())
            {
                case "13":
                case "7":
                    rs = RefundStatusType.REFUND_CLOSE;
                    break;

                case "1":
                case "2":
                case "3":
                case "4":
                case "5":
                case "8":
                case "11":
                case "12":
                case "15":
                case "16":
                    rs = isPart ? RefundStatusType.REFUND_PART : RefundStatusType.WAIT_SELLER_AGREE;
                    break;

                case "6":
                case "9":
                case "10":
                    rs = isPart ? RefundStatusType.REFUND_PART_SUCCESS : RefundStatusType.REFUND_SUCCESS;
                    break;

                default:
                    Log.WriteLine("无法识别聚好麦退款状态：" + afterSaleStatus);
                    throw new LogicException("无法识别聚好麦退款状态：" + afterSaleStatus);
            }
            return rs;
        }

        /// <summary>
        /// 将平台退款状态转换为系统退款状态
        /// </summary>
        /// <param name="afterSaleStatus"></param>
        /// <returns></returns>
        private int ParsePlatformRefundStatusPlatToSys(string afterSaleStatus)
        {
            /*
             * 售后状态
             * 1.待处理2.待买家退货3.待商家收货
             * 4.换货待商家发货5.待买家收货
             * 6.退款完成7.售后关闭8.退款中
             * 9.极速退款成功10.售后完成
             * 11.待买家处理12.退款失败13.用户取消售后
             */
            switch (afterSaleStatus)
            {
                //已申请, 待处理
                case "1":
                case "2":
                case "3":
                case "4":
                case "5":
                case "11":
                case "12":
                    return RefundStatusEnum.WAIT_REFUND.ToInt();

                case "6":
                    return RefundStatusEnum.REFUND_SUCCESS.ToInt();

                case "7":
                case "13":
                    return RefundStatusEnum.NO_REFUND.ToInt();

                case "8":
                    return RefundStatusEnum.REFUND_PROCESSING.ToInt();

                case "9":
                case "10":
                    return RefundStatusEnum.REFUND_SUCCESS.ToInt();

                default:
                    return 99;
            }
        }

        /// <summary>
        /// 将平台售后状态转换为系统售后状态
        /// </summary>
        /// <param name="afterSaleStatus"></param>
        /// <returns></returns>
        private AfterSaleStatusEnum ParsePlatformAfterSaleStatusToSys(string afterSaleStatus)
        {
            /*
             * 售后状态
             * 1.待处理2.待买家退货3.待商家收货
             * 4.换货待商家发货5.待买家收货
             * 6.退款完成7.售后关闭8.退款中
             * 9.极速退款成功10.售后完成
             * 11.待买家处理12.退款失败13.用户取消售后	
             */
            switch (afterSaleStatus)
            {
                //已申请, 待处理
                case "1":
                case "8":
                case "12":
                    return AfterSaleStatusEnum.BUYER_APPLY;

                case "2":
                    return AfterSaleStatusEnum.WAIT_BUYER_RETURN;

                case "3":
                    return AfterSaleStatusEnum.WAIT_SELLER_CONFIRM_RECEIPT;

                case "4":
                    return AfterSaleStatusEnum.EXCHANGE_WAIT_SELLER_SEND;

                case "5":
                    return AfterSaleStatusEnum.EXCHANGE_SELLER_SENDED;

                case "11":
                    return AfterSaleStatusEnum.WAIT_BUYER_MODIFY;
                //售后已完成
                case "6":
                case "9":
                case "10":
                    return AfterSaleStatusEnum.SUCCESS;
                //售后申请已取消
                case "7":
                case "13":
                    return AfterSaleStatusEnum.CLOSE;

                default:
                    return AfterSaleStatusEnum.OTHER;
            }
        }

        /// <summary>
        /// 将平台售后类型转换为系统售后类型
        /// </summary>
        /// <param name="afterSaleType"></param>
        /// <returns></returns>
        private int ParsePlatformAfterSaleTypeToSys(string afterSaleType)
        {
            /*
             * 业务类型
             * 1.仅退款2.退货退款
             * 3.换货4.极速退款
             */
            switch (afterSaleType)
            {
                //仅退款
                case "1":
                case "4":
                    return 1;
                //退货退款
                case "2":
                    return 0;
                //换货
                case "3":
                    return 2;

                default:
                    return 99;
            }
        }

        /// <summary>
        /// json check
        /// </summary>
        /// <param name="json"></param>
        /// <param name="isCheckSuccess"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        private JToken JsonCheck(string json, bool isCheckSuccess = true)
        {
            if (string.IsNullOrEmpty(json))
                throw new LogicException("接口返回数据为空");
            JToken jToken = null;
            try
            {
                jToken = JsonConvert.DeserializeObject<JToken>(json);
            }
            catch (Exception ex)
            {
                Utility.Log.WriteError($"接口返回数据格式不正确，无法解析：{json}，错误信息：{ex}");
                throw new LogicException("接口返回数据格式不正确，无法解析");
            }
            if (isCheckSuccess)
            {
                var code = jToken.Value<int>("code");
                if (code != 10000)
                {
                    throw new LogicException($"失败:【{code}】");
                }
            }
            return jToken;
        }

        /// <summary>
        /// json to product
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        private Product TransferJTokenToProduct(JToken token)
        {
            var product = new Product();
            product.PlatformId = token?.Value<string>("goods_id");
            product.ShopId = _shop.Id;
            product.ShopName = _shop.NickName;
            product.Subject = token?.Value<string>("goods_name");
            product.Status = token?.Value<int>("on_sale") == 1 ? "published" : "OFF_SHELVES";
            product.PlatformType = _shop.PlatformType;
            product.ImageUrl = token?.Value<string>("image") ?? "";
            product.CargoNumber = token?.Value<string>("goods_code");
            product.ImageUrls = (token?.Value<string>("image_ids") ?? "").Split(',')?.ToList();
            product.Description = token?.Value<string>("detail");

            var isUnit = token?.Value<int>("is_unit") == 1;
            if (isUnit)
            {
                //开启多规格
                var skuInfos = token?.Value<JArray>("skus")?.ToList();
                if (skuInfos.IsNotNullAndAny())
                {
                    skuInfos.ForEach(p =>
                    {
                        var sku = new ProductSku();
                        sku.SkuId = p.Value<string>("sku_id");
                        sku.CargoNumber = p.Value<string>("sku_code");
                        sku.Price = p.Value<decimal>("price") / 100;
                        sku.ProductSkuAttr.AttributeName = p.Value<string>("sku_name");
                        sku.ProductSkuAttr.SkuImgUrl = p.Value<string>("image_url") ?? product.ImageUrl;
                        product.Skus.Add(sku);
                    });
                }
            }
            else
            {
                //不开启多规格
                var sku = new ProductSku();
                sku.SkuId = product.PlatformId;
                sku.CargoNumber = product.CargoNumber;
                sku.Price = token?.Value<decimal>("price") / 100;
                sku.ProductSkuAttr.AttributeName = product.Subject;
                sku.ProductSkuAttr.SkuImgUrl = product.ImageUrl;
                product.Skus.Add(sku);
            }
            return product;
        }

        /// <summary>
        /// json to order
        /// </summary>
        /// <param name="jtoken"></param>
        /// <returns></returns>
        private Order TransferJTokenToOrder(JToken jtoken)
        {
            var order = new Order();
            // 订单类型：1.普通订单、2.全球购订单、3.香港直邮、4.微信小店
            var orderType = jtoken?.Value<int>("order_type");
            if (orderType.HasValue && orderType.Value == 4)
            {
                //不处理微信小店的订单，因为是密文单，无法取号
                return null;
            }
            order.PlatformOrderId = jtoken?.Value<string>("order_num");
            order.ShopId = _shop.Id;
            order.PlatformType = _shop.PlatformType.ToString();
            order.CreateTime = (jtoken?.Value<long>("created_at") ?? 0).ConvertSecondToDateTime();
            order.PayTime = (jtoken?.Value<long>("pay_time") ?? 0).ConvertSecondToDateTime();
            order.ModifyTime = (jtoken?.Value<long>("updated_at") ?? 0).ConvertSecondToDateTime();

            //买家用户id
            order.BuyerMemberId = jtoken?.Value<string>("union_id");
            //收货地址
            var buyer = jtoken?.Value<JToken>("buyer");
            order.ToName = buyer?.Value<string>("consignee");
            order.ToPhone = buyer?.Value<string>("receive_phone");
            order.ToMobile = buyer?.Value<string>("receive_phone");
            order.ToProvince = buyer?.Value<string>("province");
            order.ToCity = buyer?.Value<string>("city");
            order.ToCounty = buyer?.Value<string>("area");
            order.ToAddress = buyer?.Value<string>("address_detail");

            //买家备注
            order.BuyerRemark = jtoken?.Value<string>("user_remark");
            //商家备注
            order.SellerRemark = jtoken?.Value<string>("shop_remark");

            //快递单号
            var waybill = buyer?.Value<string>("express_number");
            //订单状态
            var platformStatus = jtoken?.Value<string>("status");
            var orderStatus = ParsePlatformStatusToOrderStatus(platformStatus, waybill);
            order.BackUpPlatformStatus = orderStatus.ToString2();
            order.PlatformStatus = order.BackUpPlatformStatus;

            var totalAmount = jtoken?.Value<decimal>("money") ?? 0;
            var freightMoney = jtoken?.Value<decimal>("freight_money") ?? 0;
            var discount = jtoken?.Value<decimal>("discount") ?? 0;
            order.TotalAmount = totalAmount / 100;
            order.ShippingFee = freightMoney / 100;
            order.Discount = discount / 100;
            //订单项
            var goods = jtoken?.Value<JToken>("goods");
            //是否有售后
            var afterSales = jtoken?.Value<JArray>("after_sales");
            //购买数量
            var count = goods?.Value<int>("num") ?? 0;
            //售后数量（包含售后中的数量-售后拒绝后会回滚）
            var afterNum = goods?.Value<int>("after_num") ?? 0;
            //退款状态
            var afterStatus = jtoken?.Value<string>("after_status");
            //存在售后单 并且 退款状态不为0(0表示售后单取消或者驳回)
            if (afterSales.IsNotNullAndAny() && afterStatus != "0")
            {
                //存在售后
                var refundStatus = ParsePlatformAfterSaleStatusToRefundStatus(afterStatus, count, afterNum);
                order.RefundStatus = refundStatus.ToString2();
            }
            if (goods.IsNotNullAndAny())
            {
                //一单一品
                var oi = new OrderItem();
                oi.ShopId = order.ShopId;
                oi.CreateTime = (goods?.Value<long>("created_at") ?? 0).ConvertSecondToDateTime() ?? DateTime.Now;
                oi.PlatformOrderId = order.PlatformOrderId;

                oi.ProductID = goods?.Value<string>("product_id");
                oi.ProductSubject = goods?.Value<string>("name");
                oi.productCargoNumber = goods?.Value<string>("product_code");

                oi.SkuID = goods?.Value<string>("sku_id");
                oi.CargoNumber = goods?.Value<string>("sku_code");
                oi.Color = goods?.Value<string>("sku_name");
                //规格id 为0 或者规格名称为空，则表示这个商品为无规格商品
                if (oi.SkuID == "0" || string.IsNullOrWhiteSpace(oi.Color))
                {
                    oi.SkuID = oi.ProductID;
                    oi.CargoNumber = oi.ProductSubject;
                    oi.Color = oi.productCargoNumber;
                }
                oi.Count = count;

                oi.SubItemID = order.PlatformOrderId + "_" + oi.SkuID + "_" + oi.Count;
                oi.Price = order.TotalAmount / oi.Count;
                oi.ItemAmount = order.TotalAmount;

                oi.RefundStatus = order.RefundStatus;
                oi.BackUpPlatformStatus = order.PlatformStatus;
                oi.Status = oi.BackUpPlatformStatus;
                oi.ProductImgUrl = goods?.Value<string>("image");
                order.OrderItems.Add(oi);
            }
            return order;
        }

        /// <summary>
        /// json to AfterSaleOrders
        /// </summary>
        /// <param name="jtoken"></param>
        /// <returns></returns>
        private List<AfterSaleOrder> TransferJTokenToAfterSaleOrders(JToken jtoken)
        {
            var afterSales = jtoken?.Value<JArray>("after_sales");
            if (afterSales.IsNullOrEmptyList())
                return null;
            var platformOrderId = jtoken?.Value<string>("order_num");
            var afterSaleOrders = new List<AfterSaleOrder>();
            var shopId = _shop.Id;
            var orderCode = (platformOrderId + shopId).ToShortMd5();
            var goods = jtoken?.Value<JToken>("goods");

            var skuId = goods?.Value<string>("sku_id");
            var skuName = goods?.Value<string>("sku_name");
            var productSubject = goods?.Value<string>("name");
            var productID = goods?.Value<string>("product_id");
            //判断是否为无规格商品
            if (skuId == "0" || string.IsNullOrWhiteSpace(skuName))
            {
                skuId = productID;
            }
            afterSales.ToList().ForEach(t =>
            {
                var afterSaleId = t?.Value<string>("refund_sn");
                var afterSaleCode = (afterSaleId + shopId).ToShortMd5();
                var number = t.Value<int>("number");
                var afterSaleOrder = new AfterSaleOrder()
                {
                    ShopId = shopId,
                    FxUserId = 0,
                    PlatformType = _shop.PlatformType,
                    AfterSaleId = afterSaleId,
                    AfterSaleCode = afterSaleCode,
                    PlatformOrderId = platformOrderId,
                    OrderCode = orderCode,
                    PlatAfterSaleType = t.Value<string>("after_sale_type"),
                    PlatAfterSaleStatus = t.Value<string>("after_sale_status"),
                    RealRefundAmount = t.Value<decimal>("price") / 100,//退款金额
                    RefundPostAmount = 0,
                    RefundTotalAmount = t.Value<decimal>("price") / 100,//退款金额,
                    Reason = t?.Value<string>("reason"),
                    ReasonCode = string.Empty,
                    ReasonRemark = t?.Value<string>("reason"),
                    ReturnTrackingNo = t.Value<string>("express_number"),
                    ReturnCompanyName = t.Value<string>("express_name"),
                    ApplyTime = (t?.Value<long>("apply_at") ?? 0).ConvertSecondToDateTime() ?? DateTime.Now
                };
                //平台状态转换成系统状态
                afterSaleOrder.AfterSaleType = ParsePlatformAfterSaleTypeToSys(afterSaleOrder.PlatAfterSaleType);
                afterSaleOrder.AfterSaleStatus = ParsePlatformAfterSaleStatusToSys(afterSaleOrder.PlatAfterSaleStatus).ToInt();
                afterSaleOrder.RefundStatus = ParsePlatformRefundStatusPlatToSys(afterSaleOrder.PlatAfterSaleStatus);
                afterSaleOrder.AfterSaleOrderItems.Add(new AfterSaleOrderItem
                {
                    AfterSaleId = afterSaleId,
                    AfterSaleCode = afterSaleCode,
                    PlatformOrderId = platformOrderId,
                    SubItemId = platformOrderId + "_" + skuId + "_" + number,
                    AfterSaleCount = number,
                    OrderCode = orderCode,
                    ShopId = shopId,
                    UserId = 0,
                    ProductSubject = productSubject,
                    ProductId = productID,
                    SkuId = skuId,
                    RefundAmount = afterSaleOrder.RefundTotalAmount
                });
                afterSaleOrders.Add(afterSaleOrder);
            });
            return afterSaleOrders;
        }

        /// <summary>
        /// 获取订单信息json
        /// </summary>
        /// <param name="pids"></param>
        /// <returns></returns>
        private List<JToken> SyncOrdersJson(List<string> pids)
        {
            //需要对 pids 进行100个为一批处理
            var pageSize = 100;
            var pageCount = (int)Math.Ceiling(pids.Count() / (pageSize * 1.0));
            var orderInfos = new List<JuHaoMai.OrderInfo>();
            for (var i = 0; i < pageCount; i++)
            {
                var temps = pids.Skip(i * pageSize).Take(pageSize).Distinct().Select(t => t.ToLong()).ToList();
                var orderInfo = new JuHaoMai.OrderInfo();
                orderInfo.OrderNum = temps.Select(t => t.ToLong()).ToList();
                orderInfo.Page = 1;
                orderInfo.PageSize = 100;
                orderInfo.StartTime = DateTime.Now.AddYears(-1);
                orderInfo.EndTime = DateTime.Now;
                orderInfos.Add(orderInfo);
            }
            var jTokens = new ConcurrentBag<JToken>();
            Parallel.ForEach(orderInfos, new ParallelOptions { MaxDegreeOfParallelism = pageCount }, item =>
            {
                var result = _client.Execute(item);
                //result = "{\"code\":10000,\"data\":{\"list\":[{\"union_id\":\"oIpcW6f3Y9RKY6LrtbT_IcoDiQoY\",\"order_num\":\"6984965061439504756\",\"status\":-1,\"order_type\":4,\"after_status\":6,\"after_status_remark\":\"\",\"cancel_type\":0,\"total_money\":9002,\"money\":4502,\"discount\":4500,\"freight_money\":0,\"order_source\":2,\"pay_mode\":2,\"pay_sn\":\"698496506143950475600\",\"pay_time\":**********,\"pay_money\":4502,\"pay_state_desc\":\"支付成功\",\"transaction_id\":\"4200002013202310286666511389\",\"complete_time\":0,\"receipt_time\":0,\"deliver_time\":0,\"ad_account_id\":********,\"shop_remark\":\"\",\"user_remark\":\"\",\"updated_at\":**********,\"created_at\":**********,\"goods\":{\"product_id\":\"************\",\"product_code\":\"\",\"sku_id\":10199,\"sku_code\":\"************003\",\"price\":4501,\"num\":2,\"image\":\"https://127.0.0.1/wxFile/prod/1697182002882_n0dnfutl67yy.jpg\",\"name\":\"测试-INS床单四件套学生工作老人母婴均可使用\",\"sku_name\":\"金金拉拉-100*120cm\",\"created_at\":**********,\"updated_at\":**********,\"after_num\":2},\"buyer\":{\"express\":\"\",\"express_number\":\"\",\"consignee\":\"zmm\",\"receive_phone\":\"***********\",\"address\":\"河南省驻马店市驿城区\",\"province\":\"\",\"city\":\"\",\"area\":\"\",\"address_detail\":\"顺河街道升龙广场\",\"area_id\":411702,\"area_ids\":\"410000,411700,411702\",\"edit_address_code\":0,\"is_update\":0,\"express_update_time\":0,\"created_at\":**********,\"updated_at\":0},\"after_sales\":[{\"refund_sn\":\"6984965527056169190\",\"after_sale_status\":6,\"after_sale_type\":1,\"reason\":\"rfefevrevrtbv\",\"number\":1,\"price\":2251,\"refund_money\":2251,\"refuse_reason\":\"\",\"refuse_fail\":\"\",\"apply_at\":1698496552,\"confirm_at\":0,\"audit_at\":0,\"cancel_at\":1698496552,\"updated_at\":1698496576,\"success_at\":1698496589,\"refuse_at\":0,\"after_sale\":0,\"after_status_remark\":\"\"},{\"refund_sn\":\"6984966489460364759\",\"after_sale_status\":6,\"after_sale_type\":1,\"reason\":\"继续皮笑肉我\",\"number\":1,\"price\":2251,\"refund_money\":2251,\"refuse_reason\":\"\",\"refuse_fail\":\"\",\"apply_at\":**********,\"confirm_at\":0,\"audit_at\":0,\"cancel_at\":**********,\"updated_at\":1698496666,\"success_at\":1698496680,\"refuse_at\":0,\"after_sale\":0,\"after_status_remark\":\"\"}]}],\"total\":1},\"success\":true,\"trace_id\":\"26e3dfcc-3028-40f7-94b1-9e4f3ec1052d\"}";
                var jToken = JsonCheck(result);
                var list = jToken?.SelectToken("data.list")?.Value<JToken>();
                if (list.IsNotNullAndAny())
                {
                    list?.ToList().ForEach(t =>
                    {
                        jTokens.Add(t);
                    });
                }
            });
            return jTokens.ToList();
        }

        /// <summary>
        /// 获取订单信息json
        /// </summary>
        /// <param name="parameter"></param>
        /// <returns></returns>
        private List<JToken> SyncOrdersJson(SyncOrderParametersModel parameter)
        {
            DateTime startTime, endTime = DateTime.Now;
            if (parameter.EndTime != null)
                endTime = parameter.EndTime.Value;
            if (parameter.StartTime != null && parameter.StartTime.Value < DateTime.Now)
                startTime = parameter.StartTime.Value;
            else if (parameter.IsFullSync)
                startTime = DateTime.Now.AddMonths(-3);
            else
                startTime = _shop.LastSyncTime ?? _shop.CreateTime.Value.AddDays(-5);
            startTime = startTime.AddMinutes(-3); //往前移三分钟

            var pageSize = 100;
            var pageIndex = 1;

            var orderInfo = new JuHaoMai.OrderInfo();
            orderInfo.Page = pageIndex;
            orderInfo.PageSize = pageSize;
            orderInfo.StartTime = startTime;
            orderInfo.EndTime = endTime;

            var result = _client.Execute(orderInfo);
            var jToken = JsonCheck(result);
            var total = jToken?.SelectToken("data.total")?.Value<int>() ?? 0;
            var jTokens = new List<JToken>();
            var list = jToken?.SelectToken("data.list")?.Values<JToken>();
            if (list.IsNotNullAndAny())
            {
                list?.ToList().ForEach(t =>
                {
                    jTokens.Add(t);
                });
            }
            if (total > pageSize)
            {
                //需要分页查询
                var tempJTokens = new List<JToken>();
                for (var i = 2; i <= total / pageSize + 1; i++)
                {
                    var temp = new JuHaoMai.OrderInfo();
                    temp.Page = i;
                    temp.PageSize = pageSize;
                    temp.StartTime = orderInfo.StartTime;
                    temp.EndTime = orderInfo.EndTime;

                    //等待后再调用接口
                    System.Threading.Thread.Sleep(PageWaitTime);
                    var rs = _client.Execute(temp);
                    var tmepJToken = JsonCheck(rs);
                    var tempList = tmepJToken?.SelectToken("data.list")?.Values<JToken>();
                    if (tempList.IsNotNullAndAny())
                    {
                        tempList?.ToList().ForEach(t =>
                        {
                            tempJTokens.Add(t);
                        });
                    }
                }
                if (tempJTokens.IsNotNullAndAny())
                    jTokens.AddRange(tempJTokens);
            }
            return jTokens;
        }

        /// <summary>
        /// 根据商品id获取商品信息
        /// </summary>
        /// <param name="pids"></param>
        /// <returns></returns>
        private List<Product> SyncProducts(List<string> pids)
        {
            //需要对 pids 进行100个为一批处理
            var pageSize = 100;
            var pageCount = (int)Math.Ceiling(pids.Count() / (pageSize * 1.0));
            var goodsInfos = new List<JuHaoMai.GoodsInfo>();
            for (var i = 0; i < pageCount; i++)
            {
                var temps = pids.Skip(i * pageSize).Take(pageSize).Distinct().Select(t => t.ToLong()).ToList();
                var goodsInfo = new JuHaoMai.GoodsInfo();
                goodsInfo.GoodsId = temps.Select(t => t.ToLong()).ToList();
                goodsInfo.Page = 1;
                goodsInfo.PageSize = 100;
                goodsInfo.StartTime = DateTime.Now.AddYears(-1);
                goodsInfo.EndTime = DateTime.Now;
                goodsInfos.Add(goodsInfo);
            }
            var jTokens = new ConcurrentBag<JToken>();
            Parallel.ForEach(goodsInfos, new ParallelOptions { MaxDegreeOfParallelism = pageCount }, item =>
            {
                var result = _client.Execute(item);
                var jToken = JsonCheck(result);
                var list = jToken?.SelectToken("data.list")?.Values<JToken>();
                if (list.IsNotNullAndAny())
                {
                    list?.ToList().ForEach(t =>
                    {
                        jTokens.Add(t);
                    });
                }
            });
            var products = new ConcurrentBag<Product>();
            Parallel.ForEach(jTokens, new ParallelOptions { MaxDegreeOfParallelism = 5 }, item =>
            {
                var product = TransferJTokenToProduct(item);
                if (product != null)
                    products.Add(product);
            });
            return products.ToList();
        }

        #endregion 私有方法

        #region 订单接口

        /// <summary>
        /// 根据条件同步订单
        /// </summary>
        /// <param name="parameter"></param>
        /// <param name="isPinged"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        public List<Order> SyncOrders(SyncOrderParametersModel parameter, bool isPinged)
        {
            if (!isPinged && !Ping())
                throw new LogicException($"店铺【{_shop.ShopName}】授权过期，请重新授权", "auth_expires");
            var jTokens = SyncOrdersJson(parameter);
            var orders = new ConcurrentBag<Order>();
            Parallel.ForEach(jTokens, new ParallelOptions { MaxDegreeOfParallelism = 5 }, item =>
            {
                var order = TransferJTokenToOrder(item);
                if (order != null)
                    orders.Add(order);
            });
            return orders.ToList();
        }

        /// <summary>
        /// 根据平台订单号同步订单
        /// </summary>
        /// <param name="pids"></param>
        /// <returns></returns>
        public List<Order> SyncOrders(List<string> pids)
        {
            var jTokens = SyncOrdersJson(pids);
            var orders = new ConcurrentBag<Order>();
            Parallel.ForEach(jTokens, new ParallelOptions { MaxDegreeOfParallelism = 5 }, item =>
            {
                var order = TransferJTokenToOrder(item);
                if (order != null)
                    orders.Add(order);
            });
            return orders.ToList();
        }

        /// <summary>
        /// 根据平台订单号同步订单
        /// </summary>
        /// <param name="platformOrderId"></param>
        /// <returns></returns>
        public Order SyncOrder(string platformOrderId)
        {
            return SyncOrders(new List<string> { platformOrderId }).FirstOrDefault();
        }

        #endregion 订单接口

        #region 发货接口

        /// <summary>
        /// 发货
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<DeliverySendOrderResultModel> OnlineSend(DeliverySendOrderModel model)
        {
            var orders = model.Orders;
            var companyCode = model?.ExpressCodeMapping?.PlatformExpressCode;
            var companyName = model?.ExpressCodeMapping?.PlatformExpressName;
            //用户勾选的数量
            var results = new List<DeliverySendOrderResultModel>();
            orders.ForEach(orderEntity =>
            {
                var orderRequest = model.OrderRequests.FirstOrDefault(o => o.Id == orderEntity.Id);
                var rsp = new DeliverySendOrderResultModel
                {
                    ErrorCode = "",
                    ErrorMessage = "",
                    ExtErrorMessage = "",
                    IsSuccess = true,
                    OrderEntity = orderEntity,
                    OrderRequest = orderRequest
                };
                if (CustomerConfig.IsDebug && CustomerConfig.IsRepairSendHistory())
                {
                    rsp.IsSuccess = true;
                    results.Add(rsp);
                    return;
                }
                var req = new JuHaoMai.OrderUpdate();
                req.List.Add(new JuHaoMai.OrderUpdateItem()
                {
                    OrderNum = orderEntity.PlatformOrderId,
                    ExpressCode = companyCode,
                    ExpressName = companyName,
                    ExpressNumber = orderRequest.WaybillCode
                });
                var result = string.Empty;
                //订单信息
                try
                {
                    result = _client.Execute(req);
                    var jToken = JsonCheck(result);
                    var data = jToken?.Value<JToken>("data");
                    //订单号错误的订单
                    var orderErr = data?.Value<JArray>("order");
                    if (orderErr.IsNotNullAndAny())
                        new LogicException("订单号错误");
                    //物流信息错误的订单
                    var expressErr = data?.Value<JArray>("express");
                    if (expressErr.IsNotNullAndAny())
                        new LogicException("物流信息错误");
                    //订单更新失败的订单
                    var orderUpdateErr = data?.Value<JArray>("order_update");
                    if (orderUpdateErr.IsNotNullAndAny())
                        new LogicException("订单更新失败");
                    //物流更新失败的订单
                    var expressUpdateErr = data?.Value<JArray>("express_update");
                    if (expressUpdateErr.IsNotNullAndAny())
                        new LogicException("物流更新失败");
                    results.Add(rsp);
                }
                catch (LogicException ex)
                {
                    Utility.Log.WriteError($"聚好麦订单【{orderEntity.PlatformOrderId}】发货失败：请求参数：{req.ToJson()} 返回：{result} 错误：{ex}");
                    rsp.IsSuccess = false;
                    rsp.OriginalResult = result;//原始内容
                    rsp.ErrorMessage += $"订单【{orderEntity.PlatformOrderId}】发货失败：{ex.Message}";
                    rsp.ErrorCode += "Logic Error </br>";
                    results.Add(rsp);
                }
                catch (Exception ex)
                {
                    Utility.Log.WriteError($"聚好麦平台订单【{orderEntity.PlatformOrderId}】发货失败：请求参数：{req.ToJson()} 返回：{result} 错误：{ex}");
                    rsp.IsSuccess = false;
                    rsp.OriginalResult = result;//原始内容
                    rsp.ErrorMessage += $"订单【{orderEntity.PlatformOrderId}】发货失败：系统错误";
                    rsp.ErrorCode += "System Error </br>";
                    results.Add(rsp);
                }
            });
            return results;
        }

        /// <summary>
        /// 二次发货
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<DeliverySendOrderResultModel> OnlineResend(DeliverySendOrderModel model)
        {
            //二次发货也是走发货接口
            return OnlineSend(model);
        }

        #endregion 发货接口

        #region 售后接口

        /// <summary>
        /// 同步售后订单详情
        /// </summary>
        /// <param name="oid"></param>
        /// <returns></returns>
        public List<AfterSaleOrder> SyncAfterSaleOrderDetail(string oid)
        {
            return SyncAfterSaleOrderDetails(new List<string> { oid });
        }

        /// <summary>
        /// 同步售后订单详情
        /// </summary>
        /// <param name="oids"></param>
        /// <returns></returns>
        public List<AfterSaleOrder> SyncAfterSaleOrderDetails(List<string> oids)
        {
            //聚好麦的售后订单是和订单一起返回的，所以不需要单独查询
            var jTokens = SyncOrdersJson(oids);
            var orders = new ConcurrentBag<AfterSaleOrder>();
            Parallel.ForEach(jTokens, new ParallelOptions { MaxDegreeOfParallelism = 5 }, item =>
            {
                var temps = TransferJTokenToAfterSaleOrders(item);
                if (temps.IsNotNullAndAny())
                    temps.ForEach(t => orders.Add(t));
            });
            return orders.ToList();
        }

        /// <summary>
        /// 同步售后订单
        /// </summary>
        /// <param name="parameter"></param>
        /// <returns></returns>
        public List<AfterSaleOrder> SyncAfterSaleOrders(SyncOrderParametersModel parameter)
        {
            //聚好麦的售后订单是和订单一起返回的，所以不需要单独查询
            var jTokens = SyncOrdersJson(parameter);
            var orders = new ConcurrentBag<AfterSaleOrder>();
            Parallel.ForEach(jTokens, new ParallelOptions { MaxDegreeOfParallelism = 5 }, item =>
            {
                var temps = TransferJTokenToAfterSaleOrders(item);
                if (temps.IsNotNullAndAny())
                    temps.ForEach(t => orders.Add(t));
            });
            return orders.ToList();
        }

        #endregion 售后接口

        #region 商品接口

        /// <summary>
        /// 同步商品
        /// </summary>
        /// <param name="statusType"></param>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <param name="isNotPinged"></param>
        /// <returns></returns>
        public List<Product> SyncProduct(int statusType, DateTime? start = null, DateTime? end = null,
            bool isNotPinged = false)
        {
            var pageSize = 100;
            var pageIndex = 1;
            var goodsInfo = new JuHaoMai.GoodsInfo();
            goodsInfo.Page = pageIndex;
            goodsInfo.PageSize = pageSize;
            goodsInfo.StartTime = start ?? DateTime.Now.AddDays(-30);
            goodsInfo.EndTime = end ?? DateTime.Now;
            var result = _client.Execute(goodsInfo);
            var jToken = JsonCheck(result);
            var total = jToken?.SelectToken("data.total")?.Value<int>() ?? 0;
            var jTokens = new List<JToken>();
            var list = jToken?.SelectToken("data.list")?.Values<JToken>();
            if (list.IsNotNullAndAny())
            {
                list?.ToList().ForEach(t =>
                {
                    jTokens.Add(t);
                });
            }
            if (total > pageSize)
            {
                //需要分页查询
                var goodsInfos = new List<JuHaoMai.GoodsInfo>();
                for (var i = 2; i <= total / pageSize + 1; i++)
                {
                    var temp = new JuHaoMai.GoodsInfo();
                    temp.Page = i;
                    temp.PageSize = pageSize;
                    temp.StartTime = goodsInfo.StartTime;
                    temp.EndTime = goodsInfo.EndTime;
                    goodsInfos.Add(temp);
                }
                var tempJTokens = new List<JToken>();
                Parallel.ForEach(goodsInfos, new ParallelOptions { MaxDegreeOfParallelism = goodsInfos.Count }, item =>
                {
                    var rs = _client.Execute(item);
                    var tmepJToken = JsonCheck(rs);
                    var tempList = tmepJToken?.SelectToken("data.list")?.Values<JToken>();
                    if (tempList.IsNotNullAndAny())
                    {
                        tempList?.ToList().ForEach(t =>
                        {
                            tempJTokens.Add(t);
                        });
                    }
                });
                if (tempJTokens.IsNotNullAndAny())
                    jTokens.AddRange(tempJTokens);
            }
            var products = new ConcurrentBag<Product>();
            Parallel.ForEach(jTokens, new ParallelOptions { MaxDegreeOfParallelism = 5 }, item =>
            {
                var product = TransferJTokenToProduct(item);
                if (product != null)
                    products.Add(product);
            });
            return products.ToList();
        }

        /// <summary>
        /// 同步指定商品
        /// </summary>
        /// <param name="platformId">商品的平台ID</param>
        /// <param name="shop">所属店铺</param>
        /// <returns></returns>
        public Product SyncProduct(string platformId)
        {
            return SyncProducts(new List<string> { platformId }).FirstOrDefault();
        }

        #endregion 商品接口

        #region 其他接口

        /// <summary>
        /// 检测是否正常授权访问
        /// </summary>
        /// <returns></returns>
        public bool Ping()
        {
            try
            {
                SyncShopInfo();
            }
            catch (Exception)
            {
                return false;
            }
            return true;
        }

        /// <summary>
        /// 同步店铺信息
        /// </summary>
        /// <returns></returns>
        public Shop SyncShopInfo()
        {
            var shopInfo = new JuHaoMai.ShopInfo();
            var result = _client.Execute(shopInfo);
            var jToken = JsonCheck(result);
            var shopName = jToken?.SelectToken("data.info.name")?.Value<string>();
            if (!string.IsNullOrWhiteSpace(shopName))
            {
                _shop.ShopName = shopName;
                _shop.NickName = _shop.ShopName;
            }
            return _shop;
        }

        /// <summary>
        ///  从平台获取店铺信息
        /// </summary>
        /// <returns></returns>
        public Shop GetPlatformShopInfo()
        {
            return SyncShopInfo();
        }

        /// <summary>
        /// 调用平台接口刷新店铺Token
        /// </summary>
        /// <returns></returns>
        public Shop RefreshShopToken()
        {
            return _shop;
        }

        /// <summary>
        /// 获取店铺过期时间
        /// 约定：需按照Base.ExecuteSyncExpiredTime统一更新店铺过期时间
        /// </summary>
        /// <returns></returns>
        public DateTime? GetExpiredTime()
        {
            string appKey = _client.GetStrAppKey();
            var newExpiredTime = ExecuteSyncExpiredTime(_shop, appKey,
                () =>
                {
                    if (Ping())
                    {
                        //可以访问授权成功就设置7天过期
                        return DateTime.Now.AddDays(7);
                    }
                    return null;
                },
                () => base.GetDbExpireTime());
            return newExpiredTime;
        }

        #endregion 其他接口

        #region 未实现接口

        public int GetLogisticsAddress()
        {
            int res = 1;
            return res;
        }

        public int GetPlatformOrderNumber()
        {
            return 0;
        }

        public List<CheckResult> AfterSaleOperate(AfterSaleOperateModel model)
        {
            throw new NotImplementedException();
        }

        public string GetAppPayUrl()
        {
            throw new NotImplementedException();
        }

        public UserInfo BasicMember()
        {
            throw new NotImplementedException();
        }

        public string ConfirmOrder(List<string> platformOrderIds)
        {
            throw new NotImplementedException();
        }

        public List<GetPriceRangesModel> DangPriceList(List<string> itemList)
        {
            throw new NotImplementedException();
        }

        public string GetBuyerMemberIdByLoginId(string strLoginId)
        {
            return strLoginId; //不支持，直接返回
        }

        public List<KeyValuePair<string, string>> GetLoginIdByMemberId(string buyerMemberId)
        {
            //没有此接口，直接返回
            if (string.IsNullOrEmpty(buyerMemberId))
                return new List<KeyValuePair<string, string>>();
            return buyerMemberId?.Split(',').Select(s => new KeyValuePair<string, string>(s, s))?.ToList();
        }

        public MergeOrderQueryResponseModel MergeOrderQuery(List<MergeOrderQueryRequstModel> requests)
        {
            throw new NotImplementedException();
        }

        public bool SaveLogisticsAddress(LogisticsAddressModel model, out string errMsg)
        {
            errMsg = "";
            return true;
        }

        public string SaveOrderPrice(Dictionary<string, string> dc)
        {
            return "true";
        }

        public List<ProductCategory> CategoryList()
        {
            throw new NotImplementedException();
        }

        public bool UpdateOrderRemark(string orderId, string remark, string iconNumber, string iconTag = "")
        {
            throw new NotImplementedException();
        }

        public bool UpdatePlatAfterSaleRemark(string platformOrderId, string afterSaleId, string afterSaleRemark)
        {
            throw new NotImplementedException();
        }

        public KeyValuePair<bool, string> UpdateReceiverInfo(UpdateReceiverModel model)
        {
            throw new NotImplementedException();
        }

        public int GetOrderCountByConditions(SyncOrderParametersModel syncOrderParameters)
        {
            return 0;
        }

        public List<AfterSaleRejectReason> GetRejectReasonList(string afterSaleId)
        {
            throw new NotImplementedException();
        }

        public List<SellerAddressModel> GetAfterSaleAllAddres()
        {
            throw new NotImplementedException();
        }
        public Tuple<bool, string, AfterSaleOperatResult> AgreeToRefund<T>(T model, List<AfterSaleOrder> afterSaleOrders) where T : class { throw new NotImplementedException(); }
        public Tuple<bool, string, AfterSaleOperatResult> RefuseRefund<T>(T model, List<AfterSaleOrder> afterSaleOrders) where T : class { throw new NotImplementedException(); }
        public Tuple<bool, string, AfterSaleOperatResult> AgreeToReturnAndRefund<T>(T model, List<AfterSaleOrder> afterSaleOrders, int exectype) where T : class { throw new NotImplementedException(); }

        #endregion 未实现接口
    }
}