using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services.PlatformService.OtherPlatforms;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Linq;

namespace DianGuanJiaApp.Services.PlatformService
{
    /// <summary>
    /// 平台工厂
    /// </summary>
    public class PlatformFactory
    {
        /// <summary>
        /// 获取平台服务类
        /// </summary>
        /// <param name="shop"></param>
        /// <param name="isLoadShopExtension"></param>
        /// <param name="scene">场景：listing表示铺货场景，all表示全部，其他值表示常规场景</param>
        /// <returns></returns>
        public static IPlatformService GetPlatformService(Shop shop, bool isLoadShopExtension = false, string scene = "")
        {
            if (shop == null) throw new Exception("GetPlatformService不能传入空shop");
            //初始化P_Shop.ShopExtension
            //分单站点的同步才初始化
            if (shop.PlatformType == PlatformType.AlibabaZhuKe.ToString())
            {
                shop.ShopExtension = (new ShopExtensionService()).GetShopExtensionByShopId(shop.Id, CustomerConfig.AlibabaZkphAppKey);
            }
            else if ((CustomerConfig.IsFendanSite || isLoadShopExtension) && shop.ShopExtension == null)
            {
                shop.ShopExtension = (new ShopExtensionService(scene)).GetShopExtensionByShopId(shop.Id);
            }
            //兼容拼多多打单应用 2024.06.18
            //shop = ProcessShopExtension(shop);

            //if (shop.ShopExtension == null)
            //{
            //    //分单站点的同步才初始化
            //    if (CustomerConfig.IsFendanSite || isLoadShopExtension)
            //    {
            //        shop.ShopExtension = (new ShopExtensionService()).GetShopExtensionByShopId(shop.Id);
            //    }
            //}

            return GetIPlatformService(shop);
        }

        /// <summary>
        /// 获取平台服务类
        /// Shop必须从外面初始化好再传对象进来，由外部决定需不需要ShopExtension
        /// </summary>
        /// <param name="shop"></param>
        /// <returns></returns>
        public static IPlatformService GetPlatformServiceByShop(Shop shop)
        {
            //兼容拼多多打单应用 2024.06.18
            //shop = ProcessShopExtension(shop);
            return GetIPlatformService(shop);
        }

        /// <summary>
        /// 获取平台服务类
        /// </summary>
        /// <param name="shop"></param>
        /// <returns></returns>
        public static IPlatformService GetPlatformService(int shopId)
        {
            var shop = SiteContext.Current.AllShops.FirstOrDefault(s => s.Id == shopId);
            if (shop == null)
                throw new LogicException("未查找到指定店铺，请确认店铺是否已经取消关联");
            return GetPlatformService(shop);
        }

        /// <summary>
        /// 获取实现平台类
        /// </summary>
        /// <param name="shop"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        private static IPlatformService GetIPlatformService(Shop shop)
        {
            if (shop.ShopExtension != null)
            {
                shop.AppKey = shop.ShopExtension.AppKey;
                shop.AppSecret = shop.ShopExtension.AppSecret;
                shop.AccessToken = shop.ShopExtension.AccessToken;
                shop.RefreshToken = shop.ShopExtension.RefreshToken;
                shop.LastRefreshTokenTime = shop.ShopExtension.LastRefreshTokenTime;
            }

            //Utility.Log.WriteError("1688订单处理--取消交易，店铺：" + shop.ToJson(), $"AutoCancelOrder_{DateTime.Now.ToString("yy-MM-dd")}.txt");

            IPlatformService service;
            switch (shop.PlatformType.ToLower())
            {
                case "1688":
                case "alibaba":
                    if (shop?.ShopExtension != null && shop?.ShopExtension?.AppKey == CustomerConfig.AlibabaQingAppKey)
                        service = new AlibabaQingPlatformService(shop);
                    else
                        service = new AlibabaPlatformService(shop);
                    break;
                case "alibabazhuke":
                    service = new AlibabaZhuKePlatformService(shop);
                    break;
                case "pinduoduo":
                case "pdd":
                    service = new PinduoduoPlatformService(shop);
                    break;
                case "taobao":
                case "tb":
                    service = new TaobaoPlatformService(shop);
                    break;
                case "xiaodian":
                case "xd":
                    service = new XiaoDianPlatformService(shop);
                    break;
                case "mogujie":
                case "mg":
                case "meilishuo":
                case "mls":
                    service = new MoGuJiePlatformService(shop);
                    break;
                case "youzan":
                case "yz":
                    service = new YouZanPlatformService(shop);
                    break;
                case "weidian":
                case "wd":
                    service = new WeiDianPlatformService(shop);
                    break;
                case "weimeng":
                case "wm":
                    {
                        //老应用
                        service = new WeiMengPlatformService(shop);
                        if (shop.ShopExtension != null)
                        {
                            if (!string.IsNullOrEmpty(shop.ShopExtension.AppKey) && shop.ShopExtension.AppKey == CustomerConfig.WeiMengFxAppKey)
                            {
                                service = new WeiMengPlatformServiceV2(shop);//分销V2.0新应用
                            }
                        }

                    }
                    break;
                case "vipshop":
                case "vshop":
                    service = new VipShopPlatformService(shop);
                    break;
                case "toutiao":
                case "tt":
                case "zhidian":
                case "zd":
                case "douyinxiaodian":
                case "dyxd":
                case "toutiaoxiaodian":
                case "ttxd":
                case "luban":
                case "lb":
                case "douyinmicroapp":
                case "toutiaosaleshop":
                    service = new ZhiDianNewPlatformService(shop);
                    break;
                case "suning":
                case "sn":
                    service = new SuningPlatformService(shop);
                    break;
                case "jingdong":
                case "jd":
                    service = new JingDongPlatformService(shop);
                    break;
                case "jingdongpurchase":
                    service = new JingDongPurchasePlatformService(shop);
                    break;
                case "mengtui":
                case "mt":
                    service = new MengTuiPlatformService(shop);
                    break;
                case "yunji":
                case "yj":
                    service = new YunJiPlatformService(shop);
                    break;
                case "kuaishou":
                case "ks":
                case "kuaishousupplier":
                    service = new KuaiShouPlatformService(shop);
                    break;
                case "beibei":
                case "bb":
                    service = new BeiBeiPlatformService(shop);
                    break;
                case "open":
                case "openv1":
                case "openv2":
                    service = new OpenPlatformService(shop); // OpenPlatformService
                    break;
                case "Offline":
                case "offline":
                case "ol":
                    service = new OfflinePlatformService(shop);
                    break;
                case "alibabac2m":
                    service = new AlibabaC2MService(shop);
                    break;
                case "xiaohongshu":
                case "xhs":
                    service = new XiaoHongShuV2PlatformService(shop);
                    break;
                case "wxxiaoshangdian":
                case "wxxsd":
                    service = new WxXiaoShangDianPlatformService(shop);
                    break;
                case "wxvideo":
                    service = new WxVideoPlatformService(shop);
                    break;
                case "mokuai":
                case "mk":
                    service = new MoKuaiPlatformService(shop);
                    break;
                case "duxiaodian":
                case "dxd":
                    if (shop.VenderId == "V2")
                        service = new DuXiaoDianV2PlatformService(shop);
                    else
                        service = new DuXiaoDianPlatformService(shop);
                    break;
                case "tuanhaohuo":
                case "thh":
                    service = new TuanHaoHuoPlatformService(shop);
                    break;
                case "kuaituantuan":
                case "pddktt":
                    service = new KuaiTuanTuanPlatformService(shop);
                    break;
                case "virtual":
                    service = new OfflineVirtualPlatformService(shop);
                    break;
                case "tiktok":
                    service = new TikTokPlatformService(shop);
                    break;
                case "ownshop":
                    service = new OwnShopPlatformService(shop);
                    break;
                case "taobaomaicai":
                    service = new TaobaoMaiCaiPlatformService(shop);
                    break;
                case "other_heliang":
                    service = new HeliangPlatformService(shop);
                    break;
                case "other_juhaomai":
                    service = new JuHaoMaiPlatformService(shop);
                    break;
                case "bilibili":
                    service = new BiliBiliPlatformService(shop);
                    break;
                case "other_haoyouduo":
                    service = new HaoYouDuoPlatformService(shop);
                    break;
                default:
                    throw new LogicException("暂不支持该平台：" + shop.PlatformType);
            }
            return service;
        }

        /// <summary>
        /// 查询数据库店铺信息
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        public static IPlatformService GetPlatformServiceByShopId(int shopId)
        {
            /////
            var shop = new ShopService().GetShopAndShopExtension(shopId);
            if (shop == null)
                throw new LogicException("未查找到指定店铺，请确认店铺是否已经取消关联");
            return GetPlatformService(shop);
        }

        /// <summary>
        /// 获取平台售后服务类
        /// </summary>
        /// <param name="shop"></param>
        /// <returns></returns>
        public static IAfterSaleService GetAfterSaleService(Shop shop, bool isLoadShopExtension = false)
        {
            //初始化P_Shop.ShopExtension
            if (shop.ShopExtension == null)
            {
                //分单站点的同步才初始化
                if (CustomerConfig.IsFendanSite || isLoadShopExtension)
                {
                    shop.ShopExtension = (new ShopExtensionService()).GetShopExtensionByShopId(shop.Id);
                }
            }

            if (shop.ShopExtension != null)
            {
                shop.AppKey = shop.ShopExtension.AppKey;
                shop.AppSecret = shop.ShopExtension.AppSecret;
                shop.AccessToken = shop.ShopExtension.AccessToken;
                shop.RefreshToken = shop.ShopExtension.RefreshToken;
                shop.LastRefreshTokenTime = shop.ShopExtension.LastRefreshTokenTime;
            }

            IAfterSaleService service;
            switch (shop.PlatformType.ToLower())
            {
                case "toutiao":
                case "tt":
                case "zhidian":
                case "zd":
                case "douyinxiaodian":
                case "dyxd":
                case "toutiaoxiaodian":
                case "ttxd":
                case "luban":
                case "lb":
                case "toutiaosaleshop":
                    service = new ZhiDianNewPlatformService(shop);
                    break;
                case "pinduoduo":
                case "pdd":
                    service = new PinduoduoPlatformService(shop);
                    break;
                case "1688":
                case "alibaba":
                    service = new AlibabaPlatformService(shop);
                    break;
                case "alibabac2m":
                    service = new AlibabaC2MService(shop);
                    break;
                case "taobao":
                case "tb":
                    service = new TaobaoPlatformService(shop);
                    break;
                case "kuaishou":
                case "ks":
                    service = new KuaiShouPlatformService(shop);
                    break;
                case "wxvideo":
                    service = new WxVideoPlatformService(shop);
                    break;
                case "jingdong":
                    service = new JingDongPlatformService(shop);
                    break;
                case "jingdongpurchase":
                    service = new JingDongPurchasePlatformService(shop);
                    break;
                case "taobaomaicai":
                    service = new TaobaoMaiCaiPlatformService(shop);
                    break;
                case "other_heliang":
                    service = new HeliangPlatformService(shop);
                    break;
                case "other_juhaomai":
                    service = new JuHaoMaiPlatformService(shop);
                    break;
                case "bilibili":
                    service = new BiliBiliPlatformService(shop);
                    break;
                case "other_haoyouduo":
                    service = new HaoYouDuoPlatformService(shop);
                    break;
                case "xiaohongshu":
                    service = new XiaoHongShuV2PlatformService(shop);
                    break;
                default:
                    throw new LogicException("暂不支持该平台：" + shop.PlatformType);
            }
            return service;
        }

        /// <summary>
        /// 获取平台售后服务类
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public static IAfterSaleService GetAfterSaleService(int shopId)
        {
            var shop = SiteContext.Current.AllShops.FirstOrDefault(s => s.Id == shopId);
            if (shop == null)
                throw new LogicException("未查找到指定店铺，请确认店铺是否已经取消关联");
            return GetAfterSaleService(shop);
        }
      /// <summary>
        /// 指定商店ID获取相关信息
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        public static IAfterSaleService GetAfterSaleServiceByShopId(int shopId)
        {
            var shop = new ShopService().GetShopAndShopExtension(shopId);
            if (shop == null)
                throw new LogicException("未查找到指定店铺，请确认店铺是否已经取消关联");
            return GetAfterSaleService(shop);
        }

        ///// <summary>
        ///// 处理授权，多应用兼容
        ///// </summary>
        ///// <param name="shop"></param>
        ///// <returns></returns>
        //public static Shop ProcessShopExtension(Shop shop)
        //{
        //    //兼容拼多多打单应用-过期时间大的为准 2024.06.18
        //    if (shop.SystemVersion.ToString2() == "ForFxSystem" && (shop.PlatformType == PlatformType.Pinduoduo.ToString() || shop.PlatformType == PlatformType.KuaiTuanTuan.ToString()))
        //    {
        //        if (shop.ShopExtension != null && shop.ShopExtension.ExpireTime != null && shop.ExpireTime != null && shop.ShopExtension.ExpireTime.Value < shop.ExpireTime.Value)
        //        {
        //            shop.ShopExtension = null;
        //            shop.AppKey = null;
        //            shop.AppSecret = null;
        //        }
        //    }
        //    return shop;
        //}
    }
}
