using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Trace.ViewModels;
using DianGuanJiaApp.Trace.ViewModels.Models;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using NPOI.DDF;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using ReturnedModel = DianGuanJiaApp.ViewModels.Models.ReturnedModel;

namespace DianGuanJiaApp.Services.PlatformService
{
    /// <summary>
    /// 同步订单服务
    /// 1、通过指定的服务器Id，获取服务器的数据库连接字符串，查找该实例下的全部数据库
    /// 2、根据指定的数据库，自动获取指定个数的未迁移的用户，创建任务，已在任务中的忽略
    /// 3、目标库，指定实例，根据规则分配冷库
    /// 4、在指定是时间下执行
    /// </summary>
    public class SyncOrderService
    {

        public SyncOrderService()
        {
            orderService = new OrderService();
            _mergerOrderService = new MergerOrderService();
            _orderSyncService = new OrderSyncLogService();
        }
        public SyncOrderService(string connectionString)
        {
            //var connectionString = CustomerConfigExt.GetConnectString(platformType);
            orderService = new OrderService(connectionString);
            _mergerOrderService = new MergerOrderService(connectionString);
            _orderSyncService = new OrderSyncLogService(connectionString);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="shop">店铺信息需要包含数据库配置信息，如果是从SiteContext中获取的店铺，是有包含数据库配置信息的</param>
        public SyncOrderService(Shop shop)
        {
            var current = CustomerConfigExt.GetConnectString(shop);
            orderService = new OrderService(current);
            _mergerOrderService = new MergerOrderService(current);
            _orderSyncService = new OrderSyncLogService(current);
        }

        #region 成员变量

        public OrderService orderService { get; private set; }

        private ShopService _shopService;
        private ShopService shopService { get { if (_shopService == null) _shopService = new ShopService(); return _shopService; } }

        private MergerOrderService _mergerOrderService { get; set; }
        private OrderSyncLogService _orderSyncService { get; set; }
        private CommentOrderService _commentOrderService = new CommentOrderService();
        private CommonSettingService _commonSettingService = new CommonSettingService();

        #endregion

        #region 平台相关

        /// <summary>
        /// 根据店铺获取平台所对应的服务
        /// </summary>
        /// <param name="shop"></param>
        /// <returns></returns>
        public IPlatformService GetPlatformService(Shop shop)
        {
            return PlatformFactory.GetPlatformService(shop);
        }

        #endregion

        #region 同步订单

        /// <summary>
        /// 同步订单
        /// </summary>
        public void SyncOrder(SyncOrderParametersModel parameter, SiteContext sc = null)
        {
            if (sc == null)
                sc = SiteContext.Current;
            var shops = sc.AllShops;
            var platform = sc.CurrentLoginShop.PlatformType;
            //var platform = CustomerConfig.ConvertPlatformType(CustomerConfig.Platform);//SiteContext.ConvertPlatformType(CustomerConfig.Platform);
            //var platform = CustomerConfig.ConvertPlatformType(sc.CurrentLoginShop.PlatformType);//SiteContext.ConvertPlatformType(CustomerConfig.Platform);
            // 当前登录的商店
            var currentShop = sc.CurrentLoginShop;
            // 初始化商店列表
            var curShops = new List<Shop>();

            if (shops != null && shops.Any())
            {
                // 获取当前商店的数据库配置 ID
                var currentDbId = currentShop.DbConfig?.DbNameConfig?.Id ?? 0;

                // 过滤店铺
                foreach (var s in shops)
                {
                    if (s.IsServiceEnd)
                    {
                        continue;
                    }

                    var storeDbId = s.DbConfig?.DbNameConfig?.Id ?? 0;
                    if (storeDbId != currentDbId)
                    {
                        continue;
                    }

                    if (curShops.Any(c => c.Id == s.Id))
                    {
                        continue;
                    }

                    if (s.PlatformType == PlatformType.Offline.ToString() ||
                        (s.PlatformType != platform && !platform.Contains(s.PlatformType)))
                    {
                        continue;
                    }

                    // 添加到当前商店列表
                    curShops.Add(s);

                    // 检查是否开启淘大店服务
                    if (s.PlatformType == PlatformType.Alibaba.ToString() &&  new AlibabaPlatformService(s).IsOpenTaoBigShopService())
                    {
                        var newShop = s.ToJson().ToObject<Shop>();
                        newShop.IsTaoaoBigShop = true;
                        curShops.Add(newShop);
                    }
                }
            }

            var now = DateTime.Now;
            if (parameter.EndTime == null)
                parameter.EndTime = now;
            var para = parameter.ToJson();
            if (curShops != null && curShops.Any())
            {
                Parallel.ForEach(curShops, shop =>
                {
                    var pt = para.ToObject<SyncOrderParametersModel>();
                    if (pt.StartTime == null)
                    {
                        if (pt.IsFullSync)
                        {
                            if (pt.StartTime == null)
                                pt.StartTime = DateTime.Now.AddDays(-46);
                        }
                        else
                        {
                            //全量同步完成后，增量同步需要从全量同步开始时间同步
                            if (
                            shop.FullSyncCompleteTime != null
                            && shop.StartFullSyncTime != null
                            && shop.FullSyncCompleteTime > DateTime.Now.AddMonths(-1)
                            && shop.FullSyncStatus == "Finished"
                            && shop.LastSyncTime == null
                            )
                            {
                                pt.StartTime = shop.StartFullSyncTime.Value.AddDays(-2);
                                pt.IsStartSyncTimeFromFullSyncStartTime = true;
                            }
                            else
                            {
                                //第一次增量同步，若是新用户，仅同步最近15天待发货订单
                                //若是迁移过来的用户，增量往前挪5天
                                if (shop.LastSyncTime == null)
                                {
                                    //var migrateShop = new ShopService().GetMigrateShop(shop.ShopId, shop.PlatformType);
                                    ////新用户
                                    //if (migrateShop == null)
                                    //{
                                    //    pt.OrderStatus = OrderStatusType.waitsellersend;
                                    //    pt.StartTime = shop.CreateTime.Value.AddDays(-15);
                                    //}
                                    //else
                                    //    pt.StartTime = shop.CreateTime.Value.AddDays(-2);
                                    pt.StartTime = DateTime.Now.Date.AddDays(-2);
                                }
                                else
                                    pt.StartTime = shop.LastSyncTime;
                            }
                        }
                    }
                    SyncOrderByShopWithCutTime(pt, shop);
                    IncrimentSyncProduct(pt, shop);
                });
            }
        }
        /// <summary>
        /// 按时间分段同步,目前仅对拼多多和有赞店铺有效
        /// </summary>
        /// <param name="parameter"></param>
        /// <param name="shop"></param>
        public void SyncOrderByShopWithCutTime(SyncOrderParametersModel parameter, Shop shop)
        {
            CommonSettingService commonSettingService = new CommonSettingService();

            //全量同步中
            if (shop.FullSyncStatus == ShopSyncStatusType.SyncingWaitSellerSend.ToString() || shop.FullSyncStatus == ShopSyncStatusType.Syncing.ToString())
                return;
            var now = DateTime.Now;
            //满足这两个条件的店铺需要同步：【不是同步中的店铺 】 或  【状态为同步中且离上次同步超过半个小时】（防止异常情况导致的同步死锁，一旦进入，则更新起始同步时间，防止重复进入）
            var maxLockMinutes = 30;
            var isReadyToSync = false;
            if (
                shop.LastSyncStatus != Data.Enum.ShopSyncStatusType.Syncing.ToString()
                || (shop.LastSyncStatus == Data.Enum.ShopSyncStatusType.Syncing.ToString() && shop.StartSyncTime != null && (now - shop.StartSyncTime.Value).TotalMinutes > maxLockMinutes)
                )
            {
                isReadyToSync = true;
            }

            var isSyncPddFactorerOrder = false; //是否是同步拼多多代发订单

            //拼多多厂家用户，如果进入的是代发订单列表，则判断条件是读取配置中的同步时间及状态
            if (shop.IsPddFactorer == true && parameter?.IsSyncPddFdsOrder == true)
            {
                isSyncPddFactorerOrder = true;

                isReadyToSync = false; //重新计算

                var pddFdsSyncParamter = _commonSettingService.GetOrderSyncStatus(shop.Id);
                if (pddFdsSyncParamter == null)
                    isReadyToSync = true;
                else if (pddFdsSyncParamter.LastSyncStatus != Data.Enum.ShopSyncStatusType.Syncing.ToString()
                    || (pddFdsSyncParamter.LastSyncStatus == Data.Enum.ShopSyncStatusType.Syncing.ToString()
                    && pddFdsSyncParamter.StartSyncTime != null && (now - pddFdsSyncParamter.StartSyncTime.Value).TotalMinutes > maxLockMinutes)
                )
                {
                    if (pddFdsSyncParamter.LastSyncTime.HasValue && pddFdsSyncParamter.LastSyncTime.Value != null)
                        parameter.StartTime = pddFdsSyncParamter.LastSyncTime;
                    isReadyToSync = true;
                }
            }
            //1688淘大店店铺 读取P_SyncStatus表的同步时间
            if (shop.IsTaoaoBigShop)
            {
                var syncStatusService = new SyncStatusService();

                var existStatus = syncStatusService.Get(shop.FxUserIds, shop.Id, ShopSyncType.TaoBigShopOrder);

                if (existStatus == null)
                {
                    shopService.InitTaoBigShopSyncStatus(shop, (res) =>
                    {
                        isReadyToSync = true;
                        //第一次增量同步最少同步一个月的订单
                        var oneMonthTime = DateTime.Now.AddMonths(-1);
                        if (parameter?.StartTime > oneMonthTime)
                        {
                            parameter.StartTime = oneMonthTime;
                        }

                        res.StartSyncTime = parameter?.StartTime;
                        res.LastSyncTime = parameter?.StartTime;
                    });
                }
                else if (existStatus.LastSyncStatus != ShopSyncStatusType.Syncing.ToString()
                           || (existStatus.LastSyncStatus == ShopSyncStatusType.Syncing.ToString()
                               && existStatus.StartSyncTime != null
                               && (now - existStatus.StartSyncTime.Value).TotalMinutes > maxLockMinutes)
                        )
                {
                    if (existStatus.LastSyncTime.HasValue && existStatus.LastSyncTime.Value != null)
                    {
                        parameter.StartTime = existStatus.LastSyncTime;
                    }
                    isReadyToSync = true;
                }
            }
            //还在上一次同步中，则直接返回，不同步
            if (isReadyToSync == false)
                return;

            parameter.IsLastInThisSync = true;
            var syncInfo = new ShopSynInfoModel();
            //var cutTimePlatform = new List<string>() { PlatformType.YouZan.ToString(), PlatformType.Pinduoduo.ToString(), PlatformType.KuaiShou.ToString() };
            if (parameter.StartTime == null || parameter.EndTime == null || parameter.IsFullSync)
            {
                SyncOrderByShop(parameter, shop);
                syncInfo.IsFinished = true;
                UpdateShopSyncInfo(commonSettingService, syncInfo, shop, isSyncPddFactorerOrder);
            }
            else
            {
                var days = (parameter.EndTime - parameter.StartTime).Value.TotalDays;
                var maxDays = 0.25;
                if (days <= maxDays)
                {
                    SyncOrderByShop(parameter, shop);
                    syncInfo.IsFinished = true;
                    UpdateShopSyncInfo(commonSettingService, syncInfo, shop, isSyncPddFactorerOrder);
                }
                else
                {
                    var endTime = parameter.EndTime;
                    var startTime = parameter.StartTime;
                    var totalPage = (int)Math.Ceiling(days / maxDays);
                    parameter.IsLastInThisSync = false;
                    syncInfo.TotalStep = totalPage;
                    for (int i = 0; i < totalPage; i++)
                    {
                        parameter.EndTime = startTime.Value.AddDays((i + 1) * maxDays);
                        parameter.StartTime = startTime.Value.AddDays(i * maxDays);
                        if (i == totalPage - 1)
                        {
                            parameter.IsLastInThisSync = true;
                            parameter.EndTime = endTime;
                        }
                        var syncStep = new ShopSynInfoStepModel
                        {
                            StartTime = parameter.StartTime.Value,
                            EndTime = parameter.EndTime.Value,
                            Index = i,
                            Status = "Syncing",
                        };
                        syncInfo.Steps.Add(syncStep);
                        UpdateShopSyncInfo(commonSettingService, syncInfo, shop, isSyncPddFactorerOrder);
                        var isSuccess = false;
                        try
                        {
                            isSuccess = SyncOrderByShop(parameter, shop);
                            if (!isSuccess || i == totalPage - 1)
                            {
                                syncStep.Status = "Finished";
                                syncInfo.IsFinished = true;
                                UpdateShopSyncInfo(commonSettingService, syncInfo, shop, isSyncPddFactorerOrder);
                            }
                            if (!isSuccess)
                                break;
                        }
                        catch (Exception ex)
                        {
                            UpdateShopSyncInfo(commonSettingService, syncInfo, shop, isSyncPddFactorerOrder);
                            Utility.Log.WriteError($"分步同步订单时发生错误：{ex}");
                            if (!isSuccess)
                                break;
                        }
                    }
                }
            }
        }

        public void UpdateShopSyncInfo(CommonSettingService commonSettingService, ShopSynInfoModel model, Shop shop, bool isSyncPddFactorerOrder)
        {
            var key = "/User/Shop/SyncInfo";
            if (isSyncPddFactorerOrder)
            {
                key = "/User/Shop/SyncInfo_PddFds";
            }
            else if (shop.IsTaoaoBigShop)
            {
                key = DataConstants.TaoBigShopSyncKey;
            }
            commonSettingService.Set(key, model.ToJson(), shop.Id);
        }

        /// <summary>
        /// 同步某个店铺某段时间的订单
        /// </summary>
        /// <param name="parameter"></param>
        /// <param name="shop">店铺</param>
        /// <returns>成功返回true,否则返回false</returns>
        public bool SyncOrderByShop(SyncOrderParametersModel parameter, Shop shop)
        {
            var syncLog = new OrderSyncLog() { ShopId = shop.Id, CreateTime = DateTime.Now, IsError = false };
            var logContext = LogSyncOperationLog(parameter, shop);
            var service = GetPlatformService(shop);
            var baseService = service as BasePlatformService;
            var isEnabledSyncProcess = baseService?.IsSyncProcessEnabled == true;
            try
            {
                //shopService.SetShopStatus(new List<int> { shop.Id }, Data.Enum.ShopSyncStatusType.Syncing, null, "", false, isEnabledSyncProcess);
                shopService.SetShopStatus(shop, Data.Enum.ShopSyncStatusType.Syncing, parameter, "", false, isEnabledSyncProcess);
                //更新同步时间到最后
                var querylog = logContext.StartStep(new LogForOperator { OperatorType = "接口查询" });
                var orders = service.SyncOrders(parameter);
                if (orders == null)
                    orders = new List<Order>();
                orders = CorrectPayTime(orders);
                querylog.TotalCount = orders?.Count() ?? 0;
                querylog.Detail = new { Pids = orders?.Select(x => x.PlatformOrderId)?.ToList() };
                logContext.EndStep();
                //进行漏单查询
                if (parameter.IsLastInThisSync)
                {
                    IfIsPddShopThenCheckOrders(service, orders, shop, logContext);
                    IfIsTaobaoShopThenCheckOrders(service, orders, shop, logContext);
                }
                //IfIsKuaiShouShopThenCheckOrders(service, orders, shop, logContext);
                var orderItemKeys = new List<OrderItemSearchModel>();
                var refundOrders = new List<Order>();
                //有赞添加了增量同步退款订单
                if (shop.PlatformType == PlatformType.YouZan.ToString())
                {
                    try
                    {
                        var youzanService = service as YouZanPlatformService;
                        var tempRefunds = youzanService.GetRefundOrders(parameter);
                        if (tempRefunds != null && tempRefunds.Any())
                        {
                            //去除增量同步里面有的订单，以退款增量同步的订单为准，防止同一个订单更新两次
                            var dictOrders = new Dictionary<string, Order>();
                            orders?.ForEach(o =>
                            {
                                if (!dictOrders.ContainsKey(o.PlatformOrderId))
                                    dictOrders.Add(o.PlatformOrderId, o);
                            });
                            refundOrders = tempRefunds;
                            refundOrders.ForEach(r =>
                            {
                                if (dictOrders.ContainsKey(r.PlatformOrderId))
                                    dictOrders[r.PlatformOrderId] = null;
                            });
                            orders = dictOrders.Where(kv => kv.Value != null)?.Select(kv => kv.Value)?.ToList();
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"同步有赞店铺【{shop.ShopName}】的退款订单时发生错误：{ex}");
                    }
                }
                else if (shop.PlatformType == PlatformType.TouTiao.ToString() || shop.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
                {
                    // 头条系接口同步退款订单
                    try
                    {
                        var zhidianService = service as ZhiDianNewPlatformService;
                        var tempRefunds = zhidianService.SyncRefundOrderList(parameter);
                        if (tempRefunds != null && tempRefunds.Any())
                        {
                            //去除增量同步里面有的订单，以退款增量同步的订单为准，防止同一个订单更新两次
                            var dictOrders = new Dictionary<string, Order>();
                            orders?.ForEach(o =>
                            {
                                if (!dictOrders.ContainsKey(o.PlatformOrderId))
                                    dictOrders.Add(o.PlatformOrderId, o);
                            });
                            refundOrders = tempRefunds;
                            refundOrders.ForEach(r =>
                            {
                                if (dictOrders.ContainsKey(r.PlatformOrderId))
                                {
                                    dictOrders[r.PlatformOrderId] = r;
                                }
                            });
                            orders = dictOrders.Where(kv => kv.Value != null)?.Select(kv => kv.Value)?.ToList();
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"店铺【{shop.Id}】同步退款订单异常：{ex}");
                        //throw new LogicException($"店铺【{shop.Id}】同步退款订单异常：{ex}");
                    }

                    ////头条订单项上的商品货号，接口取不到，需要从产品上获取
                    //UpdateOrderItems(orders, shop, service);

                    // 头条系平台后台拆分包裹操作，删除历史订单项
                    var splitOrders = orders.Where(m => !m.OriginOrderId.IsNullOrEmpty()).ToList();
                    if (splitOrders != null && splitOrders.Any())
                    {
                        splitOrders.ForEach(o =>
                        {
                            // 原始订单标记为平台拆分订单
                            var originOrder = orders.FirstOrDefault(m => m.ShopId == o.ShopId && m.PlatformOrderId == o.OriginOrderId);
                            if (originOrder == null)
                                originOrder = orderService.GetOrder(new OrderSelectKeyModel { ShopId = o.ShopId, PlatformOrderId = o.OriginOrderId }, queryReceiver: new QueryReceiverModel { IsFromApi = true, IsPrintSystem = true });
                            if (originOrder != null)
                                originOrder.IsWeiGong = true;
                            orderItemKeys.AddRange(o.OrderItems.Select(m => new OrderItemSearchModel { ShopId = m.ShopId, PlatformOrderID = o.OriginOrderId, SubItemID = m.SubItemID }).ToList());
                        });
                    }

                    #region 头条商品图片为空的情况，头条修复后可删除

                    UpdateNoImgageOrders(shop, service, orders);

                    #endregion

                    #region 头条订单额外信息数据并保存
                    try
                    {
                        new OrderExtraService().CreateOrderExtra(orders);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"插入订单额外信息数据发生错误：{ex}");
                    }
                    #endregion
                }

                var any = (orders != null && orders.Any()) || (refundOrders.Any());
                if (orders == null)
                    orders = new List<Order>();
                if (logContext.logInfo == null)
                    logContext.logInfo = new LogForOperator { };
                var totalOrderCount = orders.Count();
                logContext.logInfo.TotalCount = totalOrderCount;
                if (any)
                {
                    #region MyRegion
                    //订单保存、合并、拆分
                    var slog = logContext.StartStep(new LogForOperator { TotalCount = totalOrderCount, OperatorType = "保存订单" });
                    var mergerParameter = new OrderMergerParameterModel { IsFromMessage = false };
                    if (shop.PlatformType == PlatformType.YouZan.ToString() || shop.PlatformType == PlatformType.TouTiao.ToString() || shop.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
                        mergerParameter.IsDontUpdateRefundStatus = true;
                    var updateCommentOrders = new List<Order>();
                    if (shop.PlatformType == PlatformType.Alibaba.ToString() || shop.PlatformType == PlatformType.Taobao.ToString())
                    {
                        updateCommentOrders = orders.Where(order => order.SellerRateStatus == 5 && order.PlatformStatus.ToString2().ToLower() == "success").ToList();
                    }
                    if (!parameter.IsFullSync)
                        slog.Detail = orders?.Select(o => new { IsIgnoreUpdated = o.IsManualUpdateSeller, o.PlatformOrderId, o.PlatformStatus, o.RefundStatus, OrderItems = o.OrderItems.Select(oi => new { oi.Status, oi.RefundStatus, oi.SubItemID }) })?.ToList();

                    var needUpdateMergeredOrders = orderService.BulkMerger(orders, mergerParameter, tag: "SyncOrderByShop");
                    slog.SuccessCount = totalOrderCount;
                    logContext.EndStep();
                    #endregion

                    // 删除店管家中已存在订单的拆分包裹订单项
                    if (shop.PlatformType == PlatformType.TouTiao.ToString() && orderItemKeys.Any())
                    {
                        logContext.StartStep(new LogForOperator { TotalCount = totalOrderCount, OperatorType = "头条平台拆分包裹" });
                        try
                        {
                            orderService.DeleteOrderItem(orderItemKeys);
                            logContext.EndStep();
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"头条平台拆分包裹，更新平台拆单状态失败：{ex}");
                            logContext.EndStep(null, ex.ToString());
                        }
                    }
                    //有赞退款订单单独处理，有赞的也要更新退款状态
                    if (refundOrders.Any())
                    {
                        var refundOrderCount = refundOrders.Count();
                        var slog2 = logContext.StartStep(new LogForOperator { TotalCount = refundOrderCount, OperatorType = "保存退款订单" });
                        if (!parameter.IsFullSync)
                            slog2.Detail = refundOrders.Select(o => new { IsIgnoreUpdated = o.IsManualUpdateSeller, o.PlatformOrderId, o.PlatformStatus, o.RefundStatus, OrderItems = o.OrderItems.Select(oi => new { oi.Status, oi.RefundStatus, oi.SubItemID }) })?.ToList();
                        var needUpdateMergeredRefundOrders = orderService.BulkMerger(refundOrders, tag: "SyncOrderByShop");
                        slog2.SuccessCount = refundOrderCount;
                        logContext.EndStep();
                        if (needUpdateMergeredRefundOrders != null && needUpdateMergeredRefundOrders.Any())
                        {
                            if (needUpdateMergeredOrders == null)
                                needUpdateMergeredOrders = needUpdateMergeredRefundOrders;
                            else
                                needUpdateMergeredOrders.AddRange(needUpdateMergeredRefundOrders);
                        }
                    }
                    try
                    {
                        //logContext.StartStep(new LogForOperator { OperatorType = "自动拆单" });
                        var temp = _mergerOrderService.SplitOrderWhenPlatformStatusNotSame(new List<Shop> { shop }, isSyncPddFdsOrder: parameter.IsSyncPddFdsOrder);
                        logContext.logInfo.SubLogs.Add(temp);
                        //logContext.EndStep();
                    }
                    catch (Exception e1)
                    {
                        Log.WriteError($"自动拆分订单时发生错误：{e1}");
                        logContext.EndStep(null, e1.ToString());
                    }
                    try
                    {
                        logContext.StartStep(new LogForOperator { OperatorType = "重置拆单失败的订单" });
                        _mergerOrderService.ResetSplitedFailedOrder(new List<Shop> { shop });
                        logContext.EndStep();
                    }
                    catch (Exception e1)
                    {
                        Log.WriteError($"重置拆单失败的订单：{e1}");
                        logContext.EndStep(null, e1.ToString());
                    }
                    try
                    {
                        var log = logContext.StartStep(new LogForOperator { OperatorType = "自动合单" });
                        var count = _mergerOrderService.AutoMergerOrder(new List<Shop> { shop }, "", log, parameter.IsSyncPddFdsOrder);
                        log.TotalCount = count;
                        logContext.EndStep();
                    }
                    catch (Exception e2)
                    {
                        Log.WriteError($"合并订单时发生错误：{e2}");
                        logContext.EndStep(null, e2.ToString());
                    }
                    try
                    {
                        logContext.StartStep(new LogForOperator { OperatorType = "更新合并订单", TotalCount = needUpdateMergeredOrders?.Count() ?? 0 });
                        UpdateMergeredOrderCaculateFields(needUpdateMergeredOrders, "SyncOrderByShop");
                        logContext.EndStep();
                    }
                    catch (Exception e3)
                    {
                        Log.WriteError($"更新合并订单时发生错误：{e3} InnerException:{e3?.InnerException}");
                        logContext.EndStep(null, e3.ToString());
                    }

                    UpdateCommentOrder(updateCommentOrders, shop, logContext);
                    //shopService.SetShopStatus(new List<int> { shop.Id }, Data.Enum.ShopSyncStatusType.Finished, parameter.EndTime, $"同步完成：同步到【{orders?.Count() ?? 0}】个订单，详情请查看日志，ID：{logContext?.logInfo?._Id}", parameter.IsStartSyncTimeFromFullSyncStartTime, isEnabledSyncProcess);
                    shopService.SetShopStatus(shop, Data.Enum.ShopSyncStatusType.Finished, parameter, $"同步完成：同步到【{totalOrderCount}】个订单，详情请查看日志，ID：{logContext?.logInfo?._Id}", parameter.IsStartSyncTimeFromFullSyncStartTime, isEnabledSyncProcess);
                }
                //没有同步到订单，增量同步时间不变
                else
                {
                    //shopService.SetShopStatus(new List<int> { shop.Id }, Data.Enum.ShopSyncStatusType.Finished, parameter.EndTime, "同步完成，但未同步到任何订单", false, isEnabledSyncProcess);
                    shopService.SetShopStatus(shop, Data.Enum.ShopSyncStatusType.Finished, parameter, "同步完成，但未同步到任何订单", false, isEnabledSyncProcess);
                }
                if (CustomerConfig.IsDebug)
                    Log.WriteLine($"店铺【{shop.Id}】增量同步完成，共同步到{totalOrderCount}个订单，设置最后同步时间：{parameter.StartTime} 至 {(any ? parameter.EndTime : shop.LastSyncTime)}");
                syncLog.StartTime = parameter.StartTime;
                syncLog.EndTime = parameter.EndTime;
                syncLog.LastSyncTime = any ? parameter.EndTime : shop.LastSyncTime;
                syncLog.Message = $"同步完成：同步到【{totalOrderCount}】个订单";

                if (shop.PlatformType == PlatformType.MengTui.ToString() || shop.PlatformType == PlatformType.KuaiShou.ToString() || shop.PlatformType == PlatformType.TouTiao.ToString()
                     || shop.PlatformType == PlatformType.MeiLiShuo.ToString() || shop.PlatformType == PlatformType.MoGuJie.ToString() || shop.PlatformType == PlatformType.AlibabaC2M.ToString())
                {
                    if (parameter.IsLastInThisSync)
                    {
                        ThreadPool.QueueUserWorkItem(state =>
                        {
                            try
                            {
                                //KuaiShouShopThenCheckOrders(service, shop);
                                SyncedThenCheckOrders(shop, parameter);
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError($"店铺【{shop.ShopName}-{shop.Id}】订单补漏检测时发生错误：{ex}");
                            }
                        });
                    }
                    if (shop.PlatformType == PlatformType.KuaiShou.ToString())
                    {
                        try
                        {
                            KuaiShouCheckRefundOrders(shop, parameter);
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"快手店铺【{shop.ShopName}-{shop.Id}】同步退款订单时发生错误：{ex}");
                            throw new LogicException($"同步退款订单时发生错误：{ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                syncLog.IsError = true;
                syncLog.Message = "发生错误：" + ex + ex.InnerException;
                syncLog.StartTime = parameter.StartTime;
                syncLog.LastSyncTime = shop.LastSyncTime;
                Log.WriteError($"同步订单时发生错误，店铺ID:{shop.Id} ，错误详情：{ex} {ex.InnerException}");
                //设置店铺的状态为：同步完成，取消锁定
                var errorMsg = string.Empty;
                if (ex.InnerException != null)
                {
                    errorMsg = ex.InnerException.Message;
                }
                else if (string.IsNullOrEmpty(ex.Message) == false)
                {
                    errorMsg = ex.Message;
                }
                var length = errorMsg.Length > 100 ? 100 : errorMsg.Length;
                //shopService.SetShopStatus(new List<int> { shop.Id }, Data.Enum.ShopSyncStatusType.Error, null, string.IsNullOrEmpty(errorMsg) ? "" : ($"发生错误：{errorMsg?.Substring(0, length)}，详情请查看日志，ID：{logContext?.logInfo?._Id}"), false, isEnabledSyncProcess);
                shopService.SetShopStatus(shop, Data.Enum.ShopSyncStatusType.Error, parameter, string.IsNullOrEmpty(errorMsg) ? "" : ($"发生错误：{errorMsg?.Substring(0, length)}，详情请查看日志，ID：{logContext?.logInfo?._Id}"), false, isEnabledSyncProcess);
            }
            finally
            {
                if (logContext?.logInfo != null)
                {
                    logContext.logInfo.Response = syncLog;
                    logContext.logInfo.Exception = syncLog.IsError ? syncLog.Message : "";
                    logContext.End();
                }
            }
            return !syncLog.IsError;
        }

        /// <summary>
        /// 链路监控收集（方便消息推送）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ReturnedModel TraceDataCollect(TraceDataModel<TraceDataMetaDataModel> model)
        {
            return orderService.TraceDataCollect(model);
        }

        private void UpdateNoImgageOrders(Shop shop, IPlatformService service, List<Order> orders)
        {

            #region 头条商品图片为空的情况，头条修复后可删除
            if (shop.PlatformType != PlatformType.TouTiao.ToString())
                return;

            try
            {
                //头条商品图片为空的情况，头条修复后可删除
                var noImgOrderItems = orders.SelectMany(x => x.OrderItems).Where(n => n.ProductImgUrl.IsNullOrEmpty() || n.ProductSubject.IsNullOrEmpty()).ToList();
                if (noImgOrderItems != null && noImgOrderItems.Any())
                {
                    var logSb = new StringBuilder();
                    var orerGroup = noImgOrderItems.GroupBy(x => x.PlatformOrderId).ToList();
                    logSb.AppendLine($"店铺【{shop.Id}】,监测到{orerGroup.Count()}个订单没有商品图片");

                    foreach (var g in orerGroup)
                    {
                        var o = service.SyncOrder(g.Key);
                        foreach (var item in g)
                        {
                            var newOrderItem = o.OrderItems.FirstOrDefault(x => x.PlatformOrderId == item.PlatformOrderId && x.SubItemID == item.SubItemID && x.ShopId == item.ShopId);
                            if (newOrderItem != null)
                            {
                                item.ProductImgUrl = item.ProductImgUrl.IsNullOrEmpty() ? newOrderItem.ProductImgUrl : item.ProductImgUrl;
                                item.ProductSubject = item.ProductSubject.IsNullOrEmpty() ? newOrderItem.ProductSubject : item.ProductSubject;
                                item.Color = item.Color.IsNullOrEmpty() ? newOrderItem.Color : item.Color;
                                item.Size = item.Size.IsNullOrEmpty() ? newOrderItem.Size : item.Size;
                            }
                        }
                    }

                    noImgOrderItems = noImgOrderItems.Where(x => x.ProductImgUrl.IsNullOrEmpty() || x.ProductSubject.IsNullOrEmpty()).ToList();
                    if (noImgOrderItems.Any() == false)
                        return;

                    orerGroup = noImgOrderItems.GroupBy(x => x.PlatformOrderId).ToList();
                    logSb.AppendLine($"1.1、店铺【{shop.Id}】,通过订单详情接口同步每个订单详情，仍有【{orerGroup.Count()}】个订单未获取到商品图片\n【{string.Join(",", noImgOrderItems.Select(m => m.PlatformOrderId).Distinct().ToList())}】");

                    //var keys = notImgOrderItems.Select(m => new OrderSelectKeyModel { PlatformOrderId = m.PlatformOrderId, ShopId = m.ShopId }).ToList();
                    //var oldOrders = new OrderService().GetOrders(keys);
                    //var oldOrderItems = oldOrders?.SelectMany(m => m.OrderItems).ToList();
                    //var notExistProducts = new ConcurrentBag<Product>();

                    //var pids = new List<string>();
                    //foreach (var oi in notImgOrderItems)
                    //{
                    //    var oldOi = oldOrderItems?.FirstOrDefault(m => (m.SkuID == oi.SkuID || m.SpecId == oi.SpecId) && !m.ProductImgUrl.IsNullOrEmpty());
                    //    if (oldOi != null && oldOi.ProductImgUrl.IsNullOrEmpty())
                    //    {
                    //        oi.ProductImgUrl = oldOi.ProductImgUrl;
                    //        oi.Color = oi.Color.IsNullOrEmpty() ? oldOi.Color : oi.Color;
                    //        oi.Size = oi.Size.IsNullOrEmpty() ? oldOi.Size : oi.Size;
                    //    }
                    //    else
                    //    {
                    //        logSb.AppendLine($"2、店铺【{shop.Id}】,订单【{oi.PlatformOrderId}】在数据库中订单没有商品图片");
                    //        pids.Add(oi.ProductID);
                    //    }
                    //}

                    var productService = new ProductService();
                    var notExistProducts = new ConcurrentBag<Product>();
                    var pids = noImgOrderItems.Select(x => x.ProductID).Distinct().ToList();
                    var productList = productService.GetList(new List<int> { shop.Id }, pids);
                    foreach (var oi in noImgOrderItems)
                    {
                        var p = productList?.FirstOrDefault(x => x.PlatformId == oi.ProductID);
                        var sku = p?.Skus?.FirstOrDefault(m => m.SkuId == oi.SkuID || m.SpecId == oi.SpecId);
                        if (sku != null)
                        {
                            oi.ProductSubject = oi.ProductSubject.IsNullOrEmpty() ? p?.Subject : oi.ProductSubject;
                            oi.ProductImgUrl = oi.ProductImgUrl.IsNullOrEmpty() ? sku.ProductSkuAttr?.SkuImgUrl : oi.ProductImgUrl;
                            oi.Color = oi.Color.IsNullOrEmpty() ? sku.ProductSkuAttr?.AttributeName : oi.Color;
                            oi.Size = oi.Size.IsNullOrEmpty() ? sku.ProductSkuAttr?.AttributeValue : oi.Size;
                        }

                        // 商品库信息不全，重新同步商品
                        if (oi.ProductSubject.IsNullOrEmpty() || oi.ProductImgUrl.IsNullOrEmpty())
                        {
                            var newProduct = service.SyncProduct(oi.ProductID);
                            if (newProduct != null)
                            {
                                notExistProducts.Add(newProduct);
                                sku = newProduct.Skus?.FirstOrDefault(m => m.SkuId == oi.SkuID || m.SpecId == oi.SpecId);
                                if (sku == null)
                                {
                                    oi.ProductImgUrl = oi.ProductImgUrl.IsNullOrEmpty() ? newProduct.ImageUrl : oi.ProductImgUrl;
                                    oi.ProductSubject = oi.ProductSubject.IsNullOrEmpty() ? newProduct.Subject : oi.ProductSubject;
                                    logSb.AppendLine($"2、店铺【{shop.Id}】,订单【{oi.PlatformOrderId}】通过商品接口未能获取规格【{oi.ProductID.ToString2()}-{oi.SkuID.ToString2()}】");
                                    continue;
                                }
                                oi.ProductImgUrl = oi.ProductImgUrl.IsNullOrEmpty() ? sku.ProductSkuAttr?.SkuImgUrl : oi.ProductImgUrl;
                                oi.ProductSubject = oi.ProductSubject.IsNullOrEmpty() ? newProduct.Subject : oi.ProductSubject;
                                oi.Color = oi.Color.IsNullOrEmpty() ? sku.ProductSkuAttr?.AttributeName : oi.Color;
                                oi.Size = oi.Size.IsNullOrEmpty() ? sku.ProductSkuAttr?.AttributeValue : oi.Size;
                            }
                            else
                            {
                                logSb.AppendLine($"2、店铺【{shop.Id}】,订单【{oi.PlatformOrderId}】通过商品接口未获取到商品【{oi.ProductID.ToString2()}】信息");
                            }
                        }
                    }

                    noImgOrderItems = noImgOrderItems.Where(x => x.ProductImgUrl.IsNullOrEmpty() || x.ProductSubject.IsNullOrEmpty()).ToList();
                    if (noImgOrderItems.Any())
                    {
                        orerGroup = noImgOrderItems.GroupBy(x => x.PlatformOrderId).ToList();
                        logSb.AppendLine($"3、店铺【{shop.Id}】仍有【{orerGroup.Count()}】个订单未获取到商品图片\n【{string.Join(",", noImgOrderItems.Select(m => m.PlatformOrderId).Distinct().ToList())}】");
                    }

                    CommUtls.WriteToLog($"{logSb.ToString2()}", $"noImageLog-{shop.Id}.txt", "TouTiaoNoImage");
                    if (notExistProducts.Any())
                    {
                        var shopIds = noImgOrderItems.GroupBy(m => m.ShopId).Select(m => m.Key).ToList();
                        shopIds.ForEach(sid =>
                        {
                            var s = SiteContext.Current.AllShops.FirstOrDefault(m => m.Id == sid);
                            if (s != null)
                                productService.BulkMerger(notExistProducts.ToList(), s);//保存与更新
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                CommUtls.WriteToLog($"头条同步图片异常：{ex}", $"noImageLog-{shop.Id}.txt", "TouTiaoNoImage");
            }
            #endregion
        }

        public int SyncOrderFullfillByShop(SyncOrderParametersModel parameter, Shop shop)
        {
            int number = 0; //同步到的订单数量
            var logContext = LogSyncOperationLog(parameter, shop);
            try
            {
                var service = GetPlatformService(shop);
                var orders = new List<Order>();
                logContext.StartStep(new LogForOperator { OperatorType = "同步订单" });
                var temps = service.SyncOrders(parameter);
                logContext.EndStep();
                if (temps != null && temps.Any())
                    orders.AddRange(temps);
                //若是补漏同步待发货订单则需要对数据库中的待发货订单和当前的进行比对，以接口获取的为准
                if (!string.IsNullOrEmpty(shop.SyncType) && shop.SyncType == "WaitSellerSend" && orders.Any())
                {
                    var log = logContext.StartStep(new LogForOperator { OperatorType = "订单补漏-比对" });
                    var startTime = parameter.StartTime ?? DateTime.Now.AddDays(-7);
                    var endTime = parameter.EndTime ?? DateTime.Now;
                    var pids = orderService.GetWaitSellerSendOrderIds(startTime, endTime, shop.Id);
                    log.Remark = $"订单补漏同步完成，数据库查询到待发货订单：{pids?.Count()}个，接口查询到：{orders?.Count()}个，查询时间范围：{startTime}至{endTime}";
                    if (pids != null && pids.Any())
                        ReverseCheckOrders(service, orders, shop, logContext, pids);
                    logContext.EndStep();
                }

                ////头条订单项上的商品货号，接口取不到，需要从产品上获取
                //if (shop.PlatformType == PlatformType.TouTiao.ToString())
                //{
                //    UpdateOrderItems(temps, shop, service);
                //}

                logContext.StartStep(new LogForOperator { OperatorType = "保存订单" });
                logContext.logInfo.TotalCount = orders?.Count() ?? 0;
                orderService.BulkMerger(orders?.ToList(), tag: "SyncOrderFullfillByShop");
                logContext.EndStep();
                if (shop.SyncType != "WaitSellerSend" && orders.Any())
                {
                    try
                    {
                        var log = logContext.StartStep(new LogForOperator { OperatorType = "自动合单" });
                        var count = _mergerOrderService.AutoMergerOrder(new List<Shop> { shop });
                        log.TotalCount = count;
                        logContext.EndStep();
                    }
                    catch (Exception e2)
                    {
                        Log.WriteError($"合并订单时发生错误：{e2}");
                        logContext.EndStep(null, e2.ToString());
                    }
                }
                number = orders?.Count ?? 0;
                var msg = $"店铺【{shop.ShopName} {shop.Id}】同步到：{orders?.Count()}个【{parameter?.OrderStatus}】订单";
                Log.WriteLine(msg);
                logContext.logInfo.Response = msg;
                logContext.End();
                if (shop.PlatformType == "Taobao")
                {
                    //记录御城河日志
                    var tids = orders.Select(x => x.PlatformOrderId).ToList();
                    ych_sdk.YchRequestLogger.Order("系统", "同步订单", tids);
                }
            }
            catch (Exception ex)
            {
                logContext.logInfo.IsError = true;
                logContext.logInfo.Exception = ex.ToString();
                logContext.End();
                throw;
            }
            return number;
        }

        /// <summary>
        /// 更新订单项
        /// </summary>
        /// <param name="orders"></param>
        private void UpdateOrderItems(List<Order> orders, Shop shop, IPlatformService platformService)
        {
            try
            {
                if (!orders.Any())
                    return;
                var pids = orders.SelectMany(o => o.OrderItems).Where(oi => !oi.ProductID.IsNullOrEmpty()).Select(oi => oi.ProductID).Distinct().ToList();
                var shopIds = orders.Select(o => o.ShopId).Distinct().ToList();
                if (!pids.Any()) return;

                ////获取订单商品信息进行更新
                //var ps = SyncProducts(pids);
                //new ProductRepository().BulkMerger(ps, _shop);

                ProductService productService = new ProductService();
                var products = productService.GetProductList(shopIds, pids);
                if (products == null || !products.Any())
                {
                    products = (platformService as ZhiDianNewPlatformService).SyncProducts(pids) ?? new List<Product>();
                    if (products.Any())
                        new ProductRepository().BulkMerger(products, shop);
                }
                if (products != null && products.Any())
                {
                    orders.ForEach(o =>
                    {
                        o.OrderItems.ForEach(oi =>
                        {
                            //更新商品货号
                            var product = products.FirstOrDefault(p => oi.ProductID == p.PlatformId);
                            if (product != null && !product.CargoNumber.IsNullOrEmpty())
                                oi.productCargoNumber = product.CargoNumber;
                        });
                    });
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"更新订单项信息失败：{ex.Message}");
            }
        }

        public void IncrimentSyncProduct(SyncOrderParametersModel parametersModel, Shop shop)
        {
            try
            {
                var sp = new SyncProductService();
                sp.IncrimentSyncTaobaoProduct(shop, parametersModel);
            }
            catch (Exception ex)
            {
                Log.WriteError($"增量同步店铺【{shop.Id} {shop.ShopName}】商品时发生错误，同步参数：{parametersModel?.ToJson()}：{ex}");
            }
        }

        public LogForOperatorContext LogSyncOperationLog(SyncOrderParametersModel parameter, Shop shop)
        {
            var current = LogForOperatorContext.CurrentOnlynInThisThread;
            var name = parameter.IsFullSync ? "全量同步订单" : "增量同步订单";
            var log = current.Begin(new LogForOperator
            {
                Description = new OperationDescription()
                {
                    Url = System.Web.HttpContext.Current?.Request?.Url?.ToString(),
                    Referrer = System.Web.HttpContext.Current?.Request?.UrlReferrer?.ToString(),
                    UserAgent = System.Web.HttpContext.Current?.Request?.UserAgent,
                    Name = name
                },
                TraceId = System.Web.HttpContext.Current?.Request?["traceId"],
                DBType = SiteContext.Current.DataBaseType.ToString(),
                OperatorType = name,
                ShopId = shop.Id,
                Request = parameter,
                DbNameConfigId = shop?.DbConfig?.DbConfig?.DbNameConfigId ?? -1
                //SyncOrderDetail = new LogForSyncOrderDetailModel { }
            });
            return current;
        }

        /// <summary>
        /// 更新待评论订单状态
        /// </summary>
        /// <param name="orders"></param>
        /// <param name="shop"></param>
        /// <param name="logContext"></param>
        private void UpdateCommentOrder(List<Order> orders, Shop shop, LogForOperatorContext logContext)
        {
            if (orders == null || orders.Any() == false)
                return;

            if (shop.PlatformType != PlatformType.Alibaba.ToString() && shop.PlatformType != PlatformType.Taobao.ToString())
                return;
            Task.Factory.StartNew(() =>
            {
                var newLogContext = logContext ?? LogForOperatorContext.CurrentOnlynInThisThread;
                try
                {
                    var commentSet = _commonSettingService.GetCommentSetting(shop.Id);
                    if (commentSet != null && commentSet.Enable)
                    {
                        if (logContext != null)
                            newLogContext.StartStep(new LogForOperator { OperatorType = "插入未评论的订单" });
                        else
                            newLogContext.Begin(new LogForOperator { OperatorType = "插入未评论的订单" });
                        // 插入未评论的订单
                        var os = orders.Where(order => order.SellerRateStatus == 5 && order.PlatformStatus.ToString2().ToLower() == "success").ToList();
                        //淘宝：end_time 如果超过15天，不能再通过该接口进行评价
                        if (shop.PlatformType == PlatformType.Taobao.ToString2())
                        {
                            os = os?.Where(m => (DateTime.Now - (m.CompleteTime ?? DateTime.Now)).TotalDays <= 15).ToList();
                            if (os != null && os.Any())
                            {
                                //oid 子订单对应的商品是赠品类目（cid=50023728）下的商品 赠品类目下的商品是不能评价的
                                var pids = os?.SelectMany(m => m.OrderItems.Select(n => n.ProductID)).Distinct().ToList();
                                ProductService _productService = new ProductService();
                                var products = _productService.GetProductList(new List<int> { shop.Id }, pids)?.Where(m => m.CategoryId.ToString2() == "50023728").ToList();
                                products?.ForEach(product =>
                                {
                                    var order = os?.FirstOrDefault(m => m.OrderItems.Any(n => n.ProductID == product.PlatformId));
                                    if (order != null)
                                        os.Remove(order);
                                });
                            }
                        }

                        if (os != null && os.Count > 0)
                            MergerCommentOrders(os);

                        if (logContext != null)
                            newLogContext.EndStep();
                        else
                            newLogContext.End();
                    }
                }
                catch (Exception e4)
                {
                    Log.WriteError($"插入未评论订单时发生错误：{e4}");
                    if (logContext != null)
                        newLogContext.EndStep(null, e4.ToString());
                    else
                    {
                        LogForOperatorContext.Current.logInfo.Exception = e4.ToString();
                        newLogContext.End();
                    }
                }
            });

        }

        public void MergerCommentOrders(List<Order> os)
        {
            List<CommentOrder> cOrders = new List<CommentOrder>();
            os.ForEach(order =>
            {
                var subItemIds = order.PlatformType == PlatformType.Taobao.ToString() ? string.Join(",", order.OrderItems.Select(x => x.SubItemID).Distinct().ToList()) : "";
                cOrders.Add(new CommentOrder()
                {
                    PlatformOrderId = order.PlatformOrderId,

                    ShopId = order.ShopId,
                    PlatformType = order.PlatformType,
                    CreateTime = order.CreateTime ?? DateTime.Now,
                    CompleteTime = order.CompleteTime ?? order.ModifyTime ?? DateTime.Now,
                    SubItemID = subItemIds,
                    //CommentTime = null,
                    BuyerCommentTime = order.PublishTime,
                    IsPublished = false,
                    IsCommented = false,
                    IsRefunded = !order.RefundStatus.IsNullOrEmpty(),
                    BuyerRateStatus = order.BuyerRateStatus ?? 0,
                    //CommentContent = null
                });

                //if (order.PlatformType == PlatformType.Taobao.ToString() && order.OrderItems.Count > 1)
                //{      
                //    order.OrderItems.ForEach(oi =>
                //    {
                //        cOrders.Add(new CommentOrder()
                //        {
                //            PlatformOrderId = order.PlatformOrderId + "|||" + oi.SubItemID,
                //            ShopId = order.ShopId,
                //            PlatformType = order.PlatformType,
                //            CreateTime = order.CreateTime ?? DateTime.Now,
                //            CompleteTime = order.CompleteTime ?? DateTime.Now,
                //            //CommentTime = null,
                //            BuyerCommentTime = order.PublishTime,
                //            IsPublished = false,
                //            IsCommented = false,
                //            BuyerRateStatus = order.BuyerRateStatus ?? 0,
                //            //CommentContent = null
                //        });
                //    });
                //}
                //else
                //{
                //    cOrders.Add(new CommentOrder()
                //    {
                //        PlatformOrderId = order.PlatformOrderId,
                //        ShopId = order.ShopId,
                //        PlatformType = order.PlatformType,
                //        CreateTime = order.CreateTime ?? DateTime.Now,
                //        CompleteTime = order.CompleteTime ?? DateTime.Now,
                //        //CommentTime = null,
                //        BuyerCommentTime = order.PublishTime,
                //        IsPublished = false,
                //        IsCommented = false,
                //        BuyerRateStatus = order.BuyerRateStatus ?? 0,
                //        //CommentContent = null
                //    });
                //}    
            });

            _commentOrderService.BulkMerger(cOrders, false);
        }

        public void UpdateMergeredOrderCaculateFields(List<Order> os, string tag = "")
        {
            if (os == null || !os.Any())
                return;
            var updates = new List<Order>();
            //var _orderService = new OrderService();
            var gs = os.GroupBy(x => x.PlatformOrderId + x.ShopId).Select(x => x.FirstOrDefault()).ToList();
            if (gs.Count < os.Count)
                Log.Debug($"更新合并订单计算列时发现订单有重复，原始数量：{os.Count} 去重后数量：{gs.Count}");
            os = gs;
            //是否为打单系统
            var isPrintSystem = os.First().FxUserId == 0;
            foreach (var m in os)
            {
                if (m == null || string.IsNullOrEmpty(m.PlatformOrderId) || m.ShopId <= 0)
                    continue;
                if (orderService == null)
                {
                    Log.WriteWarning($"UpdateMergeredOrderCaculateFields中的orderService为null，当前订单：{m.PlatformOrderId},ShopId:{m.ShopId}");
                    continue;
                }
                var tuple = orderService.GetMergerOrderWithChilds(new OrderSelectKeyModel { PlatformOrderId = m.PlatformOrderId, ShopId = m.ShopId }, new QueryReceiverModel { IsPrintSystem = isPrintSystem });
                if (tuple != null && tuple.Item1 != null && tuple.Item2 != null && tuple.Item2.Any())
                {
                    //重算
                    var mainOrder = tuple.Item1;
                    var curChilds = tuple.Item2;
                    var old = new
                    {
                        mainOrder.RefundStatus,
                        mainOrder.PayTime,
                        mainOrder.TotalWeight,
                        mainOrder.BuyerRemark,
                        mainOrder.SellerRemark,
                        mainOrder.SellerRemarkFlag,
                        mainOrder.TotalAmount,
                        mainOrder.Discount,
                        mainOrder.ModifyTime
                    };
                    mainOrder.RefundStatus = curChilds.OrderByDescending(c => c.RefundStatus ?? "")?.FirstOrDefault()?.RefundStatus;
                    mainOrder.PayTime = curChilds.Where(c => c.PayTime != null)?.OrderByDescending(c => c.PayTime)?.FirstOrDefault()?.PayTime;
                    mainOrder.TotalWeight = curChilds.Sum(c => c.TotalWeight ?? 0);
                    mainOrder.BuyerRemark = string.Join("|||", curChilds.OrderBy(f => f.PlatformOrderId).Select(o => o.BuyerRemark ?? "").ToList());
                    mainOrder.SellerRemark = string.Join("|||", curChilds.OrderBy(f => f.PlatformOrderId).Select(o => o.SellerRemark ?? "").ToList());
                    mainOrder.SellerRemarkFlag = string.Join("|||", curChilds.OrderBy(f => f.PlatformOrderId).Select(o => o.SellerRemarkFlag ?? "").ToList());
                    mainOrder.ShippingFee = curChilds.Sum(o => o.ShippingFee ?? 0);
                    mainOrder.TotalAmount = curChilds.Sum(o => o.TotalAmount ?? 0);
                    mainOrder.Discount = curChilds.Sum(o => o.Discount ?? 0);
                    mainOrder.ModifyTime = curChilds.Where(c => c.ModifyTime != null)?.OrderByDescending(c => c.ModifyTime)?.FirstOrDefault()?.PayTime;

                    if (mainOrder.RefundStatus != old.RefundStatus
                        || mainOrder.PayTime != old.PayTime
                        || mainOrder.TotalWeight != old.TotalWeight
                        || mainOrder.BuyerRemark != old.BuyerRemark
                        || mainOrder.SellerRemark != old.SellerRemark
                        || mainOrder.SellerRemarkFlag != old.SellerRemarkFlag
                        || mainOrder.TotalAmount != old.TotalAmount
                        || mainOrder.Discount != old.Discount
                        || mainOrder.ModifyTime != old.ModifyTime
                        )
                    {
                        updates.Add(mainOrder);
                    }
                }
            };
            if (updates.Any())
                orderService.BulkMergerUpdate(updates, true, tag: tag);
        }

        /// <summary>
        /// 非待付款订单如果没有付款时间就已下单时间为准
        /// </summary>
        /// <param name="orders"></param>
        /// <returns></returns>
        public List<Order> CorrectPayTime(List<Order> orders)
        {
            orders?.ForEach(order =>
            {
                //非待付款订单如果没有付款时间就已下单时间为准
                if (order != null && order.PlatformStatus != "waitbuyerpay" && (order.PayTime == null || order.PayTime < DateTime.Now.AddYears(-1)))
                    order.PayTime = order.CreateTime;
                //针对订单上超长的字符进行截断处理防止报错
                order.BuyerMemberName = order.BuyerMemberName.ToCutString(512);
                if (order.PlatformType != "Pinduoduo" && order.PlatformType != "TouTiao")
                {
                    order.BuyerWangWang = order.BuyerWangWang.ToCutString(64);
                    order.BuyerMemberId = order.BuyerMemberId.ToCutString(64);
                    order.ToPhone = order.ToPhone.ToCutString(64);
                    order.ToMobile = order.ToMobile.ToCutString(512);
                    order.ToName = order.ToName.ToCutString(512);
                    order.ToAddress = order.ToAddress.ToCutString(512);
                    order.ToFullAddress = order.ToFullAddress.ToCutString(2048);
                }

                order.ToProvince = order.ToProvince.ToCutString(64);
                order.ToCity = order.ToCity.ToCutString(64);
                order.ToPost = order.ToPost.ToCutString(64);
                order.ToCounty = order.ToCounty.ToCutString(64);
                order.ToTown = order.ToTown.ToCutString(64);
                order.ExtField1 = order.ExtField1.ToCutString(1024);
                order.ExtField2 = order.ExtField2.ToCutString(512);
                order.ExtField3 = order.ExtField3.ToCutString(512);
                order.ExtField4 = order.ExtField4.ToCutString(512);
                order.ExtField5 = order.ExtField5.ToCutString(512);
                order.BuyerRemark = order.BuyerRemark.ToCutString(512);
                order.OrderItems?.ForEach(oi =>
                {
                    oi.ProductSubject = oi.ProductSubject.ToCutString(1024);
                    if (order.PlatformType == PlatformType.TouTiao.ToString() || order.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
                    {
                        var cargoNumber = oi.CargoNumber;
                        if (string.IsNullOrEmpty(cargoNumber))
                            cargoNumber = oi.productCargoNumber;
                        oi.productCargoNumber = cargoNumber.ToCutString(1024);
                        oi.CargoNumber = oi.productCargoNumber;
                    }
                    else
                    {
                        oi.productCargoNumber = oi.productCargoNumber.ToCutString(1024);
                        oi.CargoNumber = oi.CargoNumber.ToCutString(1024);
                    }
                    oi.ProductImgUrl = oi.ProductImgUrl.ToCutString(1024);
                    oi.SkuID = oi.SkuID.ToCutString(64);
                    oi.SpecId = oi.SpecId.ToCutString(64);
                    oi.Unit = oi.Unit.ToCutString(64);
                    oi.Color = oi.Color.ToCutString(50);
                    oi.Size = oi.Size.ToCutString(50);
                    oi.ExtAttr1 = oi.ExtAttr1.ToCutString(50);
                    oi.ExtAttr2 = oi.ExtAttr2.ToCutString(50);
                    oi.ExtAttr3 = oi.ExtAttr3.ToCutString(50);
                    oi.ExtAttr4 = oi.ExtAttr4.ToCutString(50);
                    oi.ExtAttr5 = oi.ExtAttr5.ToCutString(50);
                });
            });
            return orders;
        }
        ///// <summary>
        ///// 同步指定店铺的订单
        ///// </summary>
        ///// <param name="parameter"></param>
        ///// <param name="shop"></param>
        //public void SyncOrder(SyncOrderParametersModel parameter, Shop shop)
        //{
        //    var service = GetPlatformService(shop);
        //    var orders = service.SyncOrders(parameter);
        //    var oldMergeredOrSplitedOrders = orderService.BulkMerger(orders);
        //    //订单合并
        //    _mergerOrderService.SplitOrderWhenPlatformStatusNotSame();
        //    _mergerOrderService.AutoMergerOrder();
        //}
        public Order SyncSingleOrder(string platformOrderId, Shop shop, string promiseId = "", string sellerRemark = "", string sellerRemarkFlag = "", string mallMaskId = "")
        {
            Order apiOrder = null;
            return SyncSingleOrderOld(platformOrderId, shop, out apiOrder, promiseId, sellerRemark, sellerRemarkFlag, mallMaskId);
        }

        public Order SyncSingleOrderOld(string platformOrderId, Shop shop, out Order apiOrder, 
            string promiseId = "", 
            string sellerRemark = "", 
            string sellerRemarkFlag = "", 
            string mallMaskId = "",
            string batchId="")
        {
            apiOrder = null;
            //Log.WriteLine($"根据消息同步单个订单：{platformOrderId} ，店铺ID:{shop.Id}  .start");
            var service = GetPlatformService(shop);
            Order order = null;
            if (shop.IsPddFactorer == true)
            {
                var pddPlatformService = service as PinduoduoPlatformService;
                if (mallMaskId.IsNotNullOrEmpty())
                    order = pddPlatformService?.SyncOrder_fds(platformOrderId, mallMaskId);
                else
                {
                    //拼多多厂商用户，同步单个订单，要先检查订单是代打订单，是代打印的订单，则调不同方法同步
                    order = orderService.GetOrderIdAndOrderFromId(new List<string> { platformOrderId }, shop.Id)?.FirstOrDefault();
                    //代商家打印的订单
                    if (order?.BusinessType == "1")
                    {
                        try
                        {
                            if (!promiseId.IsNullOrEmpty())
                                Log.WriteError($"拼多多店铺【{shop.Id}】承诺信息【{promiseId}】走代打流程");
                            order = pddPlatformService?.SyncOrder_fds(platformOrderId, order.OrderFrom);
                        }
                        catch
                        {
                            order = pddPlatformService?.SyncOrder_fds(platformOrderId, order.OrderFrom);
                        }
                    }
                    else
                    {
                        order = null;
                    }
                }

            }
            else if (shop.PlatformType == PlatformType.KuaiShouSupplier.ToString())
            {
                var ksSupplierService = service as KuaiShouPlatformService;
                if (promiseId.IsNotNullOrEmpty())
                {
                    order = ksSupplierService.SyncOrderBySupplier(platformOrderId, promiseId);
                }
            }
            if (order == null)
            {
                try
                {
                    if (promiseId.IsNullOrEmpty())
                        order = service.SyncOrder(platformOrderId);
                    else if (!promiseId.IsNullOrEmpty() && shop.PlatformType == PlatformType.Pinduoduo.ToString())
                        order = new PinduoduoPlatformService(shop).SyncOrder(platformOrderId, promiseId);
                }
                catch
                {
                    if (promiseId.IsNullOrEmpty())
                        order = service.SyncOrder(platformOrderId);
                    else if (!promiseId.IsNullOrEmpty() && shop.PlatformType == PlatformType.Pinduoduo.ToString())
                        order = new PinduoduoPlatformService(shop).SyncOrder(platformOrderId, promiseId);
                }
            }

            if (order != null)
            {
                //链路日志-调用API-成功
                if (!string.IsNullOrWhiteSpace(batchId))
                {
                    TraceDataCollect(new TraceDataModel<TraceDataMetaDataModel>()
                    {
                        BatchId = batchId,
                        ShopId = shop.Id,
                        BusinessId = order.PlatformOrderId,
                        CloudPlatformType = CustomerConfig.CloudPlatformType,
                        PlatformType = shop.PlatformType,
                        OperationType = TraceOperationType.OrderMessagePush.ToString(),
                        TraceType = TraceType.InvokeOrderApi.ToString(),
                        CreateTime = DateTime.Now,
                        TraceStatus = TraceStatus.Success,
                        MetaData = new TraceDataMetaDataModel()
                        {
                            CreateTime=order.CreateTime,
                            ModifyTime=order.ModifyTime,
                            PlatformStatus=order.PlatformStatus,
                            RefundStatus=order.RefundStatus,
                        }
                    });
                }

                //头条商品图片为空的情况，头条修复后可删除
                UpdateNoImgageOrders(shop, service, new List<Order> { order });

                if (!string.IsNullOrEmpty(sellerRemark))
                    order.SellerRemark = sellerRemark;
                if (!string.IsNullOrEmpty(sellerRemarkFlag))
                    order.SellerRemarkFlag = sellerRemarkFlag;
                //非待付款订单如果没有付款时间就已下单时间为准
                if (order.PlatformStatus != "waitbuyerpay" && (order.PayTime == null || order.PayTime < DateTime.Now.AddYears(-1)))
                    order.PayTime = order.CreateTime;

                ////头条订单项上的商品货号，接口取不到，需要从产品上获取
                //if (shop.PlatformType == PlatformType.TouTiao.ToString())
                //{
                //    UpdateOrderItems(new List<Order> { order }, shop, service);
                //}

                //判断店铺是否被分销店铺关联
                var fxUser_PlatformShop_SystemShop = (new UserFxService()).GetUserFxAndShopsByShopIds(null, new List<int>() { shop.Id });
                if (fxUser_PlatformShop_SystemShop != null && fxUser_PlatformShop_SystemShop.Any())
                {
                    var ois = order.OrderItems.ToJson().ToObject<List<OrderItem>>();
                    apiOrder = order.ToJson().ToObject<Order>();
                    apiOrder.OrderItems = ois;
                }

                var needUpdateMergeredOrder = orderService.BulkMerger(new List<Order> { order }, new OrderMergerParameterModel
                { IsFromMessage = true, IsDontUpdateRefundStatus = false, BatchId=batchId }, tag: "SyncSingleOrderOld");
                UpdateMergeredOrderCaculateFields(needUpdateMergeredOrder, "SyncSingleOrderOld");
                //Log.WriteLine($"根据消息同步单个订单：{order.PlatformOrderId} {order.IsWeiGong}");
                //订单合并
                //if(shop.LastSyncStatus!=ShopSyncStatusType.Syncing.ToString())
                //{
                //    _mergerOrderService.SplitOrderWhenPlatformStatusNotSame(new List<Shop> { shop }, platformOrderId);
                //    _mergerOrderService.AutoMergerOrder(new List<Shop> { shop }, platformOrderId);
                //}
                UpdateCommentOrder(new List<Order> { order }, shop, null);
            }
            //else
            //    Log.WriteLine($"根据消息同步单个订单：未能从接口获取到订单");
            //Log.WriteLine($"根据消息同步单个订单：{platformOrderId} ，店铺ID:{shop.Id}  .end");
            //头条收件人脱敏信息单独提前获取并保存
            if (shop.PlatformType == PlatformType.TouTiao.ToString() || shop.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
            {
                var maskDataService = new ReceiverMaskDataService();
                //ThreadPool.QueueUserWorkItem(state=> {
                try
                {
                    maskDataService.TryCreateMaskData(service as ZhiDianNewPlatformService, order);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"处理头条收件人打码信息时发生错误：{ex}");
                }
                //});
                //订单额外项插入，抖店承诺日达功能
                var orderExtraService = new OrderExtraService();
                try
                {
                    orderExtraService.CreateOrderExtra(new List<Order>() { order });
                }
                catch (Exception ex)
                {
                    Log.WriteError($"插入订单额外信息数据发生错误：{ex}");
                }
            }
            return order;
        }

        public List<Order> SyncSingleOrders(List<string> platformOrderIds, Shop shop, string sellerRemark = "", string sellerRemarkFlag = "")
        {
            var service = GetPlatformService(shop);
            List<Order> orders = new List<Order>();
            foreach (var oid in platformOrderIds)
            {
                Order order = null;
                try
                {
                    order = service.SyncOrder(oid);
                }
                catch
                {
                    order = service.SyncOrder(oid);
                }

                if (order != null)
                {
                    //头条商品图片为空的情况，头条修复后可删除
                    UpdateNoImgageOrders(shop, service, new List<Order> { order });
                    if (!string.IsNullOrEmpty(sellerRemark))
                        order.SellerRemark = sellerRemark;
                    if (!string.IsNullOrEmpty(sellerRemarkFlag))
                        order.SellerRemarkFlag = sellerRemarkFlag;
                    //非待付款订单如果没有付款时间就已下单时间为准
                    if (order.PlatformStatus != "waitbuyerpay" && (order.PayTime == null || order.PayTime < DateTime.Now.AddYears(-1)))
                        order.PayTime = order.CreateTime;

                    orders.Add(order);
                }
            }

            ////头条订单项上的商品货号，接口取不到，需要从产品上获取
            //if (shop.PlatformType == PlatformType.TouTiao.ToString())
            //{
            //    UpdateOrderItems(orders, shop, service);
            //}

            var needUpdateMergeredOrder = orderService.BulkMerger(orders, new OrderMergerParameterModel { IsFromMessage = true, IsDontUpdateRefundStatus = false }, false);
            UpdateMergeredOrderCaculateFields(needUpdateMergeredOrder, "SyncSingleOrders");

            UpdateCommentOrder(orders, shop, null);

            #region 头条订单额外信息数据并保存
            if (shop.PlatformType == PlatformType.TouTiao.ToString() || shop.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
            {
                try
                {
                    new OrderExtraService().CreateOrderExtra(orders);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"插入订单额外信息数据发生错误：{ex}");
                }
            }
            #endregion

            return orders;
        }

        #endregion

        #region 拼多多同步退款订单

        /// <summary>
        /// 拼多多 同步售后订单，处理极速退款
        /// </summary>
        /// <param name="parameter"></param>
        /// <param name="sc"></param>
        public void PddSyncRefundOrder(SyncOrderParametersModel parameter, SiteContext sc = null)
        {
            if (sc == null)
                sc = SiteContext.Current;
            var shops = sc.AllShops;
            //var platform = CustomerConfig.ConvertPlatformType(CustomerConfig.Platform);//SiteContext.ConvertPlatformType(CustomerConfig.Platform);
            var platform = CustomerConfig.ConvertPlatformType(sc.CurrentLoginShop.PlatformType);//SiteContext.ConvertPlatformType(CustomerConfig.Platform);
            var curShops = new List<Shop>();
            if (shops != null && shops.Any())
            {
                shops.ForEach(s =>
                {
                    if (!curShops.Any(c => c.Id == s.Id) && s.PlatformType != PlatformType.Offline.ToString() && (s.PlatformType == platform || platform.Contains(s.PlatformType)))
                        curShops.Add(s);
                });
            }
            var now = DateTime.Now;
            if (parameter.EndTime == null)
                parameter.EndTime = now;
            var para = parameter.ToJson();
            if (curShops != null && curShops.Any())
            {
                Parallel.ForEach(curShops, shop =>
                {
                    if (shop.RefundOrderSyncStatus != ShopSyncStatusType.Syncing.ToString() || (shop.RefundOrderSyncStatus == ShopSyncStatusType.Syncing.ToString() && (DateTime.Now - shop.RefundOrderSyncStartExecTime).Value.TotalMinutes > 30))
                    {
                        var pt = para.ToObject<SyncOrderParametersModel>();
                        if (shop.RefundOrderSyncStartTime == null)
                        {
                            //第一次增量同步，同步前1个月的售后订单
                            pt.StartTime = DateTime.Now.AddDays(-7);
                        }
                        else
                        {
                            pt.StartTime = shop.RefundOrderSyncStartTime;
                        }
                        PddSyncRefundOrderByShop(pt, shop);

                    }
                });
            }
        }

        /// <summary>
        /// 处理极速退款逻辑 
        /// </summary>
        /// <param name="parameter"></param>
        /// <param name="shop"></param>
        public void PddSyncRefundOrderByShop(SyncOrderParametersModel parameter, Shop shop)
        {
            var now = DateTime.Now;
            IPddJxtkService service;
            //满足这两个条件的店铺需要同步：【不是同步中的店铺 】 或  【状态为同步中且离上次同步超过半个小时】（防止异常情况导致的同步死锁，一旦进入，则更新起始同步时间，防止重复进入）
            if (
                shop.RefundOrderSyncStatus != Data.Enum.ShopSyncStatusType.Syncing.ToString()
                || (shop.RefundOrderSyncStatus == Data.Enum.ShopSyncStatusType.Syncing.ToString() && shop.RefundOrderSyncStartExecTime != null && (now - shop.RefundOrderSyncStartExecTime.Value).TotalMinutes > 30)
                )
            {
                var logContext = LogSyncOperationLog(parameter, shop);
                try
                {
                    shopService.SetShopRefundOrderSyncStatus(new List<int> { shop.Id }, Data.Enum.ShopSyncStatusType.Syncing, parameter.StartTime);
                    service = new PinduoduoPlatformService(shop);//GetPlatformService(shop);
                    //更新同步时间到最后
                    var querylog = logContext.StartStep(new LogForOperator { OperatorType = "接口查询" });
                    var orders = service.SyncRefundOrders(parameter);
                    querylog.TotalCount = orders?.Count() ?? 0;
                    querylog.Detail = new { Pids = orders?.Select(x => x.order_sn)?.ToList() };
                    logContext.EndStep();
                    var any = orders != null && orders.Any();
                    logContext.logInfo.TotalCount = orders.Count();

                    shopService.SetShopRefundOrderSyncStatus(new List<int> { shop.Id }, Data.Enum.ShopSyncStatusType.Finished, parameter.EndTime, $"同步完成：同步到【{orders?.Count() ?? 0}】个订单");
                    if (CustomerConfig.IsDebug)
                        Log.WriteLine($"店铺【{shop.Id}】退款订单增量同步完成，共同步到{orders?.Count()}个订单，设置最后同步时间：{parameter.StartTime} 至 {parameter.EndTime}");

                    //逻辑计算，回传 取消发货结果

                    //1.取出：售后状态为：买家申请退款，待商家处理；订单状态为：待发货（未发货）；售后类型为：仅退款 的订单
                    var tempOrderList = orders?.Where(f => f.after_sales_status == "2" && f.shipping_status == "0" && f.after_sales_type == "1")?.ToList();

                    //2.遍历，一个个处理
                    var datas = new List<CancelSendGoodsRequest>();
                    for (int i = 0; i < tempOrderList?.Count; i++)
                    {
                        var refundOrder = tempOrderList?[i];

                        //查询出订单
                        var orderEntity = orderService.GetOrder(new OrderSelectKeyModel()
                        {
                            ShopId = refundOrder.ShopId,
                            PlatformOrderId = refundOrder.order_sn,
                        }, queryReceiver: new QueryReceiverModel { IsFromApi = true, IsPrintSystem = true });

                        //1.判断订单是否存在
                        if (orderEntity == null)
                        {
                            //不存在
                            datas.Add(new CancelSendGoodsRequest()
                            {
                                rid = refundOrder.id,
                                oid = refundOrder.order_sn,
                                status = "FAIL",
                                fail_reason_code = "1001",
                            });
                            continue;
                        }

                        //2.判断是否发货
                        if (orderEntity.PlatformStatus == OrderStatusType.waitbuyerreceive.ToString() || orderEntity.LastSendTime != null)
                        {
                            //已发货
                            datas.Add(new CancelSendGoodsRequest()
                            {
                                rid = refundOrder.id,
                                oid = refundOrder.order_sn,
                                status = "FAIL",
                                fail_reason_code = "1002",
                            });
                            continue;
                        }

                        //3.判断是否打单
                        if (orderEntity.LastExpressPrintTime == null)
                        {
                            //未打单
                            datas.Add(new CancelSendGoodsRequest()
                            {
                                rid = refundOrder.id,
                                oid = refundOrder.order_sn,
                                status = "SUCCESS"
                            });
                            continue;
                        }
                        else
                        {
                            //已打单
                            datas.Add(new CancelSendGoodsRequest()
                            {
                                rid = refundOrder.id,
                                oid = refundOrder.order_sn,
                                status = "FAIL",
                                fail_reason_code = "1003",
                            });
                            continue;
                        }
                    }

                    //回传结果
                    Parallel.ForEach(datas, new ParallelOptions { MaxDegreeOfParallelism = 3 }, (req) =>
                    {
                        service.CancelSendGoods(req);
                    });

                }
                catch (Exception ex)
                {
                    Log.WriteError($"同步退款订单时发生错误，店铺ID:{shop.Id} ，错误详情：{ex} {ex.InnerException}");
                    //设置店铺的状态为：同步完成，取消锁定
                    var errorMsg = string.Empty;
                    if (ex.InnerException != null)
                    {
                        errorMsg = ex.InnerException.Message;
                    }
                    else if (string.IsNullOrEmpty(ex.Message) == false)
                    {
                        errorMsg = ex.Message;
                    }
                    var length = errorMsg.Length > 100 ? 100 : errorMsg.Length;
                    shopService.SetShopRefundOrderSyncStatus(new List<int> { shop.Id }, Data.Enum.ShopSyncStatusType.Error, parameter.StartTime, string.IsNullOrEmpty(errorMsg) ? "" : ($"发生错误：{errorMsg?.Substring(0, length)}，详情请查看日志，ID：{logContext?.logInfo?._Id}"));

                    logContext.logInfo.IsError = true;
                    logContext.logInfo.Exception = ex.ToString();
                }
                finally
                {
                    if (logContext?.logInfo != null)
                    {
                        logContext.End();
                    }
                }
            }
        }

        #endregion

        #region 拆分订单

        /// <summary>
        /// 从合并订单中拆出订单
        /// </summary>
        /// <param name="mainOrder"></param>
        /// <param name="toSplitOrder"></param>
        /// <param name="isSplitByUser">是否被用户手动拆单</param>
        public void SplitOrderFromMergeredOrder(OrderSelectKeyModel mainOrderKey, OrderSelectKeyModel toSplitOrderKey, bool isSplitByUser = false)
        {
            var orders = orderService.GetOrders(new List<OrderSelectKeyModel> { mainOrderKey, toSplitOrderKey }, queryReceiver: new QueryReceiverModel { IsFromApi = true, IsPrintSystem = true });
            var mainOrder = orders.FirstOrDefault(o => o.Id == mainOrderKey.Id || o.PlatformOrderId == mainOrderKey.PlatformOrderId);
            var splitOrder = orders.FirstOrDefault(o => o.Id == toSplitOrderKey.Id || o.PlatformOrderId == toSplitOrderKey.PlatformOrderId);
            if (mainOrder == null || splitOrder == null)
                throw new LogicException("未查询到订单数据，请刷新重试");
            var childs = orderService.GetOrders(mainOrder.ChildOrderId.Split(',').Select(s => new OrderSelectKeyModel
            {
                ShopId = mainOrder.ShopId,
                PlatformOrderId = s
            }).ToList(), queryReceiver: new QueryReceiverModel { IsFromApi = true, IsPrintSystem = true });
            if (childs == null)
                throw new LogicException("未查询到订单相关的子订单信息，请刷新重试");
            //两种情况
            //1. 这个合并订单中只有两个订单，拆分后需删除合并订单并还原订单状态
            var splitOrders = new List<string>();
            if (mainOrder.ChildOrderId.Split(',').Count() == 2)
            {
                orderService.RestoreMergeredOrder(mainOrder, childs, isSplitByUser);
            }
            else
            {
                var notSameStatusOrder = new List<Order> { splitOrder };
                orderService.RemoveOrderFromMergeredOrder(mainOrder, childs, notSameStatusOrder, isSplitByUser);
                //移除对应的子订单项
                for (int i = mainOrder.OrderItems.Count - 1; i >= 0; i--)
                {
                    var oi = mainOrder.OrderItems[i];
                    if (notSameStatusOrder.Any(x => x.PlatformOrderId == oi.OrignalOrderId))
                        mainOrder.OrderItems.Remove(oi);
                }
                mainOrder.LastExpressPrintTime = mainOrder.OrderItems.OrderByDescending(oi => oi.LastExpressPrintTime)?.FirstOrDefault()?.LastExpressPrintTime;
                mainOrder.LastNahuoPrintTime = mainOrder.OrderItems.OrderByDescending(oi => oi.LastNahuoPrintTime)?.FirstOrDefault()?.LastNahuoPrintTime;
                mainOrder.LastSendPrintTime = mainOrder.OrderItems.OrderByDescending(oi => oi.LastFahuoPrintTime)?.FirstOrDefault()?.LastFahuoPrintTime;
                if (mainOrder.LastSendPrintTime != null)
                    mainOrder.SendPrintTimes = 1;
                if (mainOrder.LastExpressPrintTime != null)
                    mainOrder.ExpressPrintTimes = 1;
                if (mainOrder.LastNahuoPrintTime != null)
                    mainOrder.NahuoPrintTimes = 1;
                var curChilds = childs.Where(c => c.PlatformOrderId != toSplitOrderKey.PlatformOrderId);
                mainOrder.RefundStatus = curChilds.OrderByDescending(c => c.RefundStatus)?.FirstOrDefault()?.RefundStatus;
                mainOrder.PayTime = curChilds.OrderByDescending(c => c.PayTime)?.FirstOrDefault()?.PayTime;
                mainOrder.TotalWeight = curChilds.Sum(c => c.TotalWeight);
                mainOrder.BuyerRemark = string.Join("|||", curChilds.OrderBy(f => f.PlatformOrderId).Select(o => o.BuyerRemark).ToList());
                mainOrder.SellerRemark = string.Join("|||", curChilds.OrderBy(f => f.PlatformOrderId).Select(o => o.SellerRemark).ToList());
                mainOrder.SellerRemarkFlag = string.Join("|||", curChilds.OrderBy(f => f.PlatformOrderId).Select(o => o.SellerRemarkFlag).ToList());
                mainOrder.ShippingFee = curChilds.Sum(o => o.ShippingFee);
                mainOrder.TotalAmount = curChilds.Sum(o => o.TotalAmount);
                mainOrder.Discount = curChilds.Sum(o => o.Discount);
                mainOrder.IsWeiGong = curChilds.All(o => o.IsWeiGong);
                mainOrder.IsPreordain = curChilds.All(o => o.IsPreordain);
                mainOrder.ModifyTime = curChilds.OrderByDescending(c => c.ModifyTime)?.FirstOrDefault()?.PayTime;
                mainOrder.Warehouse = curChilds.FirstOrDefault()?.Warehouse;
                mainOrder.LastShipTime = curChilds.OrderBy(c => c.LastShipTime)?.FirstOrDefault()?.LastShipTime;
                mainOrder.IsSvcCOD = curChilds.Any(c => c.IsSvcCOD);
                mainOrder.IsSvcInsure = curChilds.Any(c => c.IsSvcInsure);
                mainOrder.SvcCODAmount = curChilds.Sum(c => c.SvcCODAmount);
                mainOrder.SvcInsureAmount = curChilds.Sum(c => c.SvcInsureAmount);
                var cat = curChilds.Where(x => x.CategoryId > 0).Select(x => x.CategoryId).Distinct().ToList();
                if (cat != null && cat.Count() == 1)
                    mainOrder.CategoryId = cat.FirstOrDefault();
                orderService.BulkMergerUpdate(new List<Order> { mainOrder }, true, "SplitOrderFromMergeredOrder");
            }
        }


        #endregion

        #region  如果是拼多多，进行漏单查询

        public void IfIsPddShopThenCheckOrders(IPlatformService service, List<Order> orders, Shop shop, LogForOperatorContext logContext)
        {
            var pddService = service as PinduoduoPlatformService;
            if (pddService != null)
            {
                try
                {
                    var cs = new CommonSettingService();
                    var key = "/User/Shop/CheckPddWaitSellerSendOrderIds";
                    var systemSetting = cs.GetString(key, shop.Id);
                    if (systemSetting == "false")
                        return;
                    var log = logContext.StartStep(new LogForOperator { TotalCount = orders.Count, OperatorType = "拼多多接口查询待发货订单" });
                    var waitsellersendOrders = pddService.GetWaitsellersendOrders();
                    log.TotalCount = waitsellersendOrders?.Count() ?? 0;
                    logContext.EndStep();
                    CheckPddOrders(service, orders, shop, logContext, waitsellersendOrders);
                    cs.Set(key, "false", shop.Id);
                }
                catch (System.Exception ex)
                {
                    logContext.EndStep(null, ex.ToString());
                }

            }
        }

        private void CheckPddOrders(IPlatformService service, List<Order> orders, Shop shop, LogForOperatorContext logContext, List<Order> waitsellersendOrders)
        {
            var ids = new List<string>();
            var pids = waitsellersendOrders.Select(x => x.PlatformOrderId);
            if (pids != null && pids.Any())
            {
                var queryIds = pids.Where(pid => !orders.Any(x => x.PlatformOrderId == pid)).ToList();
                if (queryIds != null && queryIds.Any())
                {
                    //数据库查询是否包含
                    var log1 = logContext.StartStep(new LogForOperator { TotalCount = orders.Count, OperatorType = "查询数据库不存在的订单" });
                    ids = orderService.GetNotExistOrderIds(queryIds, shop.Id);
                    log1.TotalCount = ids?.Count() ?? 0;
                    logContext.EndStep();
                }
            }
            //获取数据库中所有的待发货订单
            var log3 = logContext.StartStep(new LogForOperator { TotalCount = orders.Count, OperatorType = "获取数据库中所有的待发货订单" });
            //获取数据库中所有的待发货订单
            var dbWaitsellersendIds = orderService.GetWaitSellerSendOrderIds(shop.Id);
            if (dbWaitsellersendIds != null && dbWaitsellersendIds.Any())
            {
                var statusNotRightIds = dbWaitsellersendIds.Where(x => pids.Contains(x) == false);
                if (statusNotRightIds != null && statusNotRightIds.Any())
                    ids.AddRange(statusNotRightIds);
            }
            log3.TotalCount = dbWaitsellersendIds?.Count() ?? 0;
            logContext.EndStep();
            ids = ids.Distinct().ToList();
            if (ids != null && ids.Any())
            {
                var log2 = logContext.StartStep(new LogForOperator { TotalCount = orders.Count, OperatorType = "同步数据库不存在的订单" });
                log2.TotalCount = ids.Count();
                var successCount = 0;

                foreach (var id in ids)
                {
                    try
                    {
                        if (orders.Exists(o => o.PlatformOrderId == id))
                            continue;
                        Order order = waitsellersendOrders?.FirstOrDefault(x => x.PlatformOrderId == id);
                        if (order == null)
                            order = orders?.FirstOrDefault(x => x.PlatformOrderId == id);
                        if (order == null)
                            order = service.SyncOrder(id);
                        if (order != null)
                        {
                            orders.Add(order);
                            successCount++;
                        }
                    }
                    catch
                    {

                    }
                }
                log2.SuccessCount = successCount;
                logContext.EndStep();
            }
        }

        #endregion

        #region  如果是淘宝，进行漏单查询

        public void IfIsTaobaoShopThenCheckOrders(IPlatformService service, List<Order> orders, Shop shop, LogForOperatorContext logContext)
        {
            var tbService = service as TaobaoPlatformService;
            if (orders == null)
                orders = new List<Order>();
            if (tbService != null)
            {
                try
                {
                    var cs = new CommonSettingService();
                    var key = "/User/Shop/CheckTaobaoWaitSellerSendOrderIds";
                    var systemSetting = cs.GetString(key, shop.Id);
                    if (systemSetting == "false")
                        return;
                    var log = logContext.StartStep(new LogForOperator { TotalCount = orders.Count, OperatorType = "淘宝接口查询待发货订单" });
                    var pids = tbService.GetWaitsellersendPlatformOrderIds();
                    log.TotalCount = pids?.Count() ?? 0;
                    logContext.EndStep();
                    CheckTaobaoOrders(service, orders, shop, logContext, pids);
                    cs.Set(key, "false", shop.Id);
                    //记录御城河日志
                    if (orders.Any())
                    {
                        var tids = orders.Select(x => x.PlatformOrderId).ToList();
                        ych_sdk.YchRequestLogger.Order("系统", "同步订单", tids);
                    }
                    ThreadPool.QueueUserWorkItem(s =>
                    {
                        try
                        {
                            var sp = new SyncProductService();
                            sp.SyncProduct(SiteContext.Current.CurrentLoginShop, 0);
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"补漏同步商品时发生错误：{ex}");
                        }
                    });
                }
                catch (System.Exception ex)
                {
                    logContext.EndStep(null, ex.ToString());
                }
            }
        }

        private void CheckTaobaoOrders(IPlatformService service, List<Order> orders, Shop shop, LogForOperatorContext logContext, List<string> pids)
        {
            var ids = new List<string>();
            if (pids != null && pids.Any())
            {
                var queryIds = pids.Where(pid => !orders.Any(x => x.PlatformOrderId == pid)).ToList();
                if (queryIds != null && queryIds.Any())
                {
                    //数据库查询是否包含
                    var log1 = logContext.StartStep(new LogForOperator { TotalCount = orders.Count, OperatorType = "查询数据库不存在的订单" });
                    ids = orderService.GetNotExistOrderIds(queryIds, shop.Id);
                    log1.TotalCount = ids?.Count() ?? 0;
                    logContext.EndStep();
                }
            }
            var log3 = logContext.StartStep(new LogForOperator { TotalCount = orders.Count, OperatorType = "获取数据库中所有的待发货订单" });
            //获取数据库中所有的待发货订单
            var dbWaitsellersendIds = orderService.GetWaitSellerSendOrderIds(shop.Id);
            if (dbWaitsellersendIds != null && dbWaitsellersendIds.Any())
            {
                var statusNotRightIds = dbWaitsellersendIds.Where(x => pids.Contains(x) == false);
                if (statusNotRightIds != null && statusNotRightIds.Any())
                    ids.AddRange(statusNotRightIds);
            }
            log3.TotalCount = dbWaitsellersendIds?.Count() ?? 0;
            logContext.EndStep();
            ids = ids.Distinct().ToList();
            if (ids != null && ids.Any())
            {
                var log2 = logContext.StartStep(new LogForOperator { TotalCount = orders.Count, OperatorType = "同步数据库不存在的订单" });
                log2.TotalCount = ids.Count();
                var successCount = 0;

                if (shop.IsPddFactorer == true)
                {
                    //拼多多厂商用户，同步单个订单，要先检查订单是代打订单，是代打印的订单，则调不同方法同步
                    var pddOrderDict = orderService.GetOrderIdAndOrderFromId(ids, shop.Id).ToDictionary(f => f.PlatformOrderId, f => f.OrderFrom);
                    var pddPlatfromService = service as PinduoduoPlatformService;
                    foreach (var id in ids)
                    {
                        try
                        {
                            if (orders.Exists(o => o.PlatformOrderId == id))
                                continue;
                            Order order = null;
                            var orderFrom = "";
                            if (pddOrderDict.TryGetValue(id, out orderFrom))
                            {
                                order = pddPlatfromService?.SyncOrder_fds(id, orderFrom);
                            }
                            if (order != null)
                            {
                                orders.Add(order);
                                successCount++;
                            }
                        }
                        catch
                        {

                        }
                    }
                }
                else
                {
                    foreach (var id in ids)
                    {
                        try
                        {
                            if (orders.Exists(o => o.PlatformOrderId == id))
                                continue;
                            var order = service.SyncOrder(id);
                            if (order != null)
                            {
                                orders.Add(order);
                                successCount++;
                            }
                        }
                        catch
                        {

                        }
                    }
                }

                log2.SuccessCount = successCount;
                logContext.EndStep();
            }
        }

        private void ReverseCheckOrders(IPlatformService service, List<Order> orders, Shop shop, LogForOperatorContext logContext, List<string> pids)
        {
            var ids = new List<string>();
            if (pids != null && pids.Any())
            {
                ids = pids.Where(pid => !orders.Any(x => x.PlatformOrderId == pid)).ToList();
            }
            if (ids != null && ids.Any())
            {
                var log2 = logContext.StartStep(new LogForOperator { TotalCount = orders.Count, OperatorType = "同步数据库中状态不一致的订单" });
                log2.TotalCount = ids.Count();
                var tempOrders = new ConcurrentBag<Order>();
                Parallel.ForEach(ids, new ParallelOptions { MaxDegreeOfParallelism = 10 }, id =>
                 {
                     try
                     {
                         var order = service.SyncOrder(id);
                         if (order != null)
                             tempOrders.Add(order);
                     }
                     catch (Exception ex)
                     {

                     }
                 });
                log2.Remark = $"共重新同步了【{tempOrders.Count()}】个状态不一致的订单";
                logContext.EndStep();
                orders.AddRange(tempOrders.ToList());
            }
        }

        #endregion


        #region 其他平台订单补漏查询
        public void SyncedThenCheckOrders(Shop shop, SyncOrderParametersModel parameter)
        {
            //数据库查询上次补漏时间，15分钟内跳过
            //var settingService = new CommonSettingService();
            var setting = _commonSettingService.Get(CommonSettingService.LastOrderSyncCheckTime, shop.Id)?.Value;
            var time = DateTime.Now.AddDays(-7);
            if (!string.IsNullOrEmpty(setting) && shop.PlatformType != PlatformType.AlibabaC2M.ToString())
            {
                var temp = setting.toDateTime();
                if (temp != null && temp > DateTime.Now.AddMinutes(-240))
                    return;
            }
            //time = DateTime.Now.AddDays(-1);
            var tempTime = orderService.GetOldestWaitSellerOrderCreateTime(shop.Id);
            //补漏起始时间，至少两天起
            if (tempTime == null || tempTime > DateTime.Now.AddDays(-2))
                time = DateTime.Now.AddDays(-2);
            else
                time = tempTime.Value;

            dynamic tbService = null;
            var ptService = PlatformFactory.GetPlatformService(shop);
            if (shop.PlatformType == PlatformType.KuaiShou.ToString())
                tbService = ptService as KuaiShouPlatformService;
            else if (shop.PlatformType == PlatformType.TouTiao.ToString() || shop.PlatformType == PlatformType.TouTiaoSaleShop.ToString())
                tbService = ptService as ZhiDianNewPlatformService;
            else if (shop.PlatformType == PlatformType.MoGuJie.ToString() || shop.PlatformType == PlatformType.MeiLiShuo.ToString())
                tbService = ptService as MoGuJiePlatformService;
            else if (shop.PlatformType == PlatformType.MengTui.ToString())
                tbService = ptService as MengTuiPlatformService;
            else if (shop.PlatformType == PlatformType.AlibabaC2M.ToString())
                tbService = ptService as AlibabaC2MService;
            if (tbService != null)
            {
                var operatorType = CustomerConfig.ConvertPlatformTypeToName(shop.PlatformType) + "订单补漏";
                LogForOperatorContext logContext = LogForOperatorContext.CurrentOnlynInThisThread;
                logContext.logInfo = new LogForOperator
                {
                    OperatorType = operatorType,
                    StartTime = DateTime.Now,
                    ShopId = shop.Id
                };
                try
                {
                    var log = logContext.StartStep(new LogForOperator { TotalCount = 0, OperatorType = "查询数据库待发货数量" });
                    var startTime = time.AddMinutes(-10).ToString("yyyy-MM-dd HH:mm:ss").toDateTime();
                    var endTime = DateTime.Now.AddMinutes(-5).ToString("yyyy-MM-dd HH:mm:ss").toDateTime();
                    if (parameter?.EndTime != null)
                        endTime = parameter.EndTime.Value.ToString("yyyy-MM-dd HH:mm:ss").toDateTime();
                    var dbCount = orderService.GetWaitSellerSendOrderCount(startTime, endTime, shop.Id);
                    log.TotalCount = dbCount;
                    log.Detail = new { startTime, endTime };
                    logContext.EndStep();
                    log = logContext.StartStep(new LogForOperator { TotalCount = 0, OperatorType = "查询接口待发货数量" });
                    var count = tbService.GetWaitSellerSendOrderCount(startTime, endTime);
                    log.TotalCount = count;
                    log.Detail = new { startTime, endTime };
                    logContext.EndStep();
                    //同数据库中的比对
                    if (count != dbCount)
                    {
                        var copy = shop.ToJson().ToObject<Shop>();
                        copy.StartFullSyncTime = startTime;
                        copy.SyncType = "WaitSellerSend";
                        var rsp = SystemApiQueueService.PushSyncShop(copy);
                        //添加同步任务
                        log.OperatorType = "添加补漏任务";
                        log.Remark = $"订单补漏，同步任务队列服务返回：{rsp}，数据库查询到待发货订单：{dbCount}个，接口查询到：{count}个，查询时间范围：{startTime}至{endTime}";
                        logContext.EndStep();
                        logContext.End();
                    }
                    _commonSettingService.Set(CommonSettingService.LastOrderSyncCheckTime, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), shop.Id);
                }
                catch (System.Exception ex)
                {
                    logContext.EndStep(null, ex.ToString());
                }
            }
        }

        public List<Order> KuaiShouCheckRefundOrders(Shop shop, SyncOrderParametersModel pt)
        {
            //查询数据库退款订单信息
            //查询接口退款订单信息
            var ks = new KuaiShouPlatformService(shop);
            //var end = DateTime.Now;
            //var start = end.AddDays(-7);
            var refundsOrders = ks.GetRefundOrders(pt);
            if (refundsOrders == null || !refundsOrders.Any())
                return null;
            var orderService = new OrderService(shop);
            var orders = orderService.GetOrders(refundsOrders, false, queryReceiver: new QueryReceiverModel { IsFromApi = true, IsPrintSystem = true });
            //若数据库中不存在对应的订单，需要单独同步
            var notExistOrders = refundsOrders.Where(r => !orders.Exists(o => o.PlatformOrderId == r.PlatformOrderId)).ToList();
            if (notExistOrders != null && notExistOrders.Any())
            {
                var temps = new ConcurrentBag<Order>();
                Parallel.ForEach(notExistOrders, new ParallelOptions { MaxDegreeOfParallelism = 10 }, o =>
                  {
                      try
                      {
                          var temp = ks.SyncOrder(o.PlatformOrderId);
                          if (temp != null)
                              temps.Add(temp);
                      }
                      catch
                      {

                      }
                  });
                orders.AddRange(temps.ToList());
            }

            //数据对比,不一致的需要更新
            var notSameOrders = new List<Order>();
            foreach (var cur in refundsOrders)
            {
                var order = orders.FirstOrDefault(o => o.PlatformOrderId == cur.PlatformOrderId);
                if (order == null)
                    continue;
                var isNotSame = order.RefundStatus != cur.RefundStatus;
                if (!isNotSame)
                {
                    //再根据OrderItems判断
                    var ois1 = order.OrderItems.OrderBy(oi => oi.SkuID).ToList();
                    var ois2 = cur.OrderItems.OrderBy(oi => oi.SkuID).ToList();
                    if (ois1.Count() != ois2.Count())
                    {
                        Log.WriteWarning($"订单【{cur.PlatformOrderId}】的退款订单和实际订单的订单项数量不一样：数据库中：{ois1.Count()}，退款中：{ois2.Count()}");
                        continue;
                    }
                    else
                    {
                        for (int i = 0; i < ois1.Count(); i++)
                        {
                            var oi1 = ois1[i];
                            var oi2 = ois2[i];
                            if (oi1.RefundStatus != oi2.RefundStatus)
                            {
                                isNotSame = true;
                                break;
                            }
                        }
                    }
                }
                if (isNotSame || order.Id <= 0)
                {
                    order.RefundStatus = cur.RefundStatus;
                    foreach (var oi in order.OrderItems)
                    {
                        var refundOi = cur.OrderItems.FirstOrDefault(coi => coi.SubItemID == oi.SubItemID);
                        if (refundOi != null)
                        {
                            oi.RefundStatus = refundOi.RefundStatus;
                            oi.RefundAmount = refundOi.RefundAmount;
                        }
                    }
                    notSameOrders.Add(order);
                }
            }

            //更新到数据库
            try
            {
                var needUpdateMergeredOrder = orderService.BulkMerger(notSameOrders, null, false, tag: "KuaiShouCheckRefundOrders");
                UpdateMergeredOrderCaculateFields(needUpdateMergeredOrder, "KuaiShouCheckRefundOrders");
            }
            catch (Exception ex)
            {
                Log.WriteError($"更新店铺【{shop.ShopId}】退款订单时发生错误：{ex}");
            }
            return notSameOrders;
        }

        #region  如果是快手，进行漏单检测，确认漏单，添加仅同步待发货订单的异步全量同步任务
        public void KuaiShouShopThenCheckOrders(Shop shop, DateTime time)
        {
            var tbService = PlatformFactory.GetPlatformService(shop) as KuaiShouPlatformService;
            if (tbService != null)
            {
                var operatorType = CustomerConfig.ConvertPlatformTypeToName(shop.PlatformType) + "订单补漏";
                LogForOperatorContext logContext = LogForOperatorContext.CurrentOnlynInThisThread;
                logContext.logInfo = new LogForOperator
                {
                    OperatorType = operatorType,
                    StartTime = DateTime.Now,
                    ShopId = shop.Id
                };
                try
                {
                    var log = logContext.StartStep(new LogForOperator { TotalCount = 0, OperatorType = "查询数据库待发货数量" });
                    var startTime = time;
                    var endTime = DateTime.Now.AddMinutes(-5);
                    var dbCount = orderService.GetWaitSellerSendOrderCount(startTime, endTime, shop.Id);
                    log.TotalCount = dbCount;
                    log.Detail = new { startTime, endTime };
                    logContext.EndStep();
                    log = logContext.StartStep(new LogForOperator { TotalCount = 0, OperatorType = "查询接口待发货数量" });
                    var count = tbService.GetWaitSellerSendOrderCount(startTime, endTime);
                    log.TotalCount = count;
                    log.Detail = new { startTime, endTime };
                    logContext.EndStep();
                    //同数据库中的比对
                    if (count != dbCount)
                    {
                        var copy = shop.ToJson().ToObject<Shop>();
                        copy.StartFullSyncTime = startTime;
                        copy.SyncType = "WaitSellerSend";
                        var rsp = SystemApiQueueService.PushSyncShop(copy);
                        //添加同步任务
                        log.OperatorType = "添加补漏任务";
                        log.Remark = $"订单补漏，同步任务队列服务返回：{rsp}，数据库查询到待发货订单：{dbCount}个，接口查询到：{count}个，查询时间范围：{startTime}至{endTime}";
                        logContext.EndStep();
                    }
                    logContext.End();
                    _commonSettingService.Set(CommonSettingService.LastOrderSyncCheckTime, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), shop.Id);
                }
                catch (System.Exception ex)
                {
                    logContext.EndStep(null, ex.ToString());
                }
            }
        }
        #endregion

        #endregion

    }
}
