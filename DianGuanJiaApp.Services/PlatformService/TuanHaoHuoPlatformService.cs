using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility.Other;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using Newtonsoft.Json.Linq;
using System.Collections.Concurrent;

namespace DianGuanJiaApp.Services.PlatformService
{
    public class TuanHaoHuoPlatformService : BasePlatformService, IPlatformService, ISyncOrderJsonService
    {
        private TuanHaoHuoApiClient _client = null;
        private Shop _shop = null;
        private AreaCodeInfoService _acs = new AreaCodeInfoService();
        public TuanHaoHuoPlatformService(Shop shop) : base(shop)
        {
            _client = new TuanHaoHuoApiClient(shop);
            _shop = shop;
        }

        #region 未实现
        public UserInfo BasicMember()
        {
            throw new NotImplementedException();
        }

        public string ConfirmOrder(List<string> platformOrderIds)
        {
            throw new NotImplementedException();
        }

        public List<GetPriceRangesModel> DangPriceList(List<string> itemList)
        {
            return null;
        }

        public string GetAppPayUrl()
        {
            return "";
        }

        public string GetBuyerMemberIdByLoginId(string strLoginId)
        {
            return "";
        }

        public DateTime? GetExpiredTime()
        {
            return _shop.ExpireTime;
        }

        public List<KeyValuePair<string, string>> GetLoginIdByMemberId(string buyerMemberId)
        {
            throw new NotImplementedException();
        }

        public int GetLogisticsAddress()
        {
            return 2;
        }

        public Shop RefreshShopToken()
        {
            return _shop;
        }

        public bool SaveLogisticsAddress(LogisticsAddressModel model, out string errMsg)
        {
            throw new NotImplementedException();
        }

        public string SaveOrderPrice(Dictionary<string, string> dc)
        {
            throw new NotImplementedException();
        }
        public Shop GetPlatformShopInfo()
        {
            return _shop;
        }
        #endregion


        public Shop SyncShopInfo()
        {
            string apiName = "poi.detail";
            var dic = new Dictionary<string, string>();
            dic.Add("poi_id", _shop.AccessToken);
            var result = _client.Execute(apiName, dic);
            var data = _client.GetErrorMessage(result);
            if (data.Item1 != 0)
                throw new LogicException($"店铺{_shop.NickName}:{data.Item2}");

            var nickname = data.Item3?.Value<string>("name");
            _shop.NickName = nickname;
            _shop.ShopName = _shop.NickName;
            return _shop;
        }
        public bool Ping()
        {
            try
            {
                SyncShopInfo();
                return true;
            }
            catch (Exception ex)
            {
                //只有授权过期的错误，才说明店铺是授权过期了，不然会误判店铺授权过期，弹出授权的窗口
                var errMsg = ex?.Message.ToString2() ?? "";
                if (errMsg.Contains("授权过期") == true || errMsg.Contains("授权已过期") == true)
                    throw ex;
                return true;
            }
        }

        public Tuple<bool, string> CheckAuth()
        {
            try
            {
                //Tips("没有授权失效的说法")
                return Tuple.Create(true, "");
            }
            catch (Exception ex)
            {
                //接口不通两种情况1.授权过期，2.接口超时/其他(不是授权问题需要清空消息字段)
                if (ex.Message?.Contains("授权过期") == true || ex.Message?.Contains("授权已过期") == true)
                    return Tuple.Create(false, ex.Message);

                return Tuple.Create(true, ex.Message);
            }
        }

        public bool UpdateOrderRemark(string orderId, string remark, string iconNumber, string iconTag = "")
        {
            string apiName = "order.remark.update";
            var dic = new Dictionary<string, string>();
            dic.Add("poi_id", _shop.AccessToken);
            dic.Add("order_view_id", orderId);
            dic.Add("poi_remark", remark);
            var result = _client.Execute(apiName, dic);
            var data = _client.GetErrorMessage(result);
            if (data.Item1 != 0)
            {
                Log.Debug($"店铺{_shop.NickName}修改备注失败:{data.Item2}");
                return false;
            }
            return true;
        }

        #region 获取商品分类
        public List<ProductCategory> CategoryList()
        {
            var list = new List<ProductCategory>();
            var apiName = "goods.getCategory";
            var dic = new Dictionary<string, string>();
            dic.Add("poi_id", _shop.AccessToken);
            var result = _client.Execute(apiName, dic);
            var data = _client.GetErrorMessage(result);

            var category_list = data.Item3?.Value<JArray>("category_list").ToList();
            if (category_list != null && category_list.Count() > 0)
            {
                var index = 1;
                category_list.ForEach(item =>
                {
                    ProductCategory cat = new ProductCategory();
                    cat.id = item?.Value<string>("category_id");
                    cat.name = item?.Value<string>("category_name");
                    cat.ordering = index;
                    index++;
                    list.Add(cat);
                });
            }
            return list;
        }
        #endregion

        #region 订单
        public int GetPlatformOrderNumber()
        {
            var list = new List<ProductCategory>();
            var apiName = "order.list";
            var dic = new Dictionary<string, string>();
            dic.Add("poi_id", _shop.AccessToken);
            dic.Add("start_time", DateTime.Now.AddDays(-30).ToSeconds().ToString());
            dic.Add("end_time", DateTime.Now.ToSeconds().ToString());
            var result = _client.Execute(apiName, dic);
            var data = _client.GetErrorMessage(result);
            return 1;
        }
        /// <summary>
        /// 获取api原始数据
        /// 工具箱用到
        /// </summary>
        /// <param name="name"></param>
        /// <param name="parameter1"></param>
        /// <param name="parameter2">备用，暂时用不到</param>
        /// <param name="parameter3">备用，暂时用不到</param>
        /// <returns></returns>
        public string GetPlatformApiOriginalData(string name, string parameter1 = "", string parameter2 = "", string parameter3 = "")
        {
            var json = "无";
            if (name == "getOrder")
            {
                var apiName = "order.detail";
                var dic = new Dictionary<string, string>();
                dic.Add("poi_id", _shop.AccessToken);
                dic.Add("order_view_id", parameter1);
                json = _client.Execute(apiName, dic);
            }
            else if (name == "getProduct")
            {
                var apiName = "goods.detail";
                var dic = new Dictionary<string, string>();
                dic.Add("poi_id", _shop.AccessToken);
                dic.Add("goods_id", parameter1);
                json = _client.Execute(apiName, dic);
            }
            return json;
        }

        public Order SyncOrder(string platformOrderId)
        {
            var result = SyncOrderJson(platformOrderId);
            var data = _client.GetErrorMessage(result);
            if (data.Item1 != 0)
                throw new LogicException($"查询失败,错误消息:{data.Item2}");
            var order_list = data.Item3?.Value<JArray>("order_list").FirstOrDefault();
            if (order_list == null)
                throw new LogicException("未找到该订单");

            return TransferJTokenToOrder(order_list);
        }

        public List<Order> SyncOrders(SyncOrderParametersModel parameter,bool isPinged=false)
        {
            var orders = new ConcurrentDictionary<string, Order>();

            //1.此接口仅支持团好货商家查询团好货订单详情列表，当前支持查询90天内的订单.
            //2.接口QPS为app应用维度100/s。
            //3.每页默认返回10条数据。
            //4.查询订单时间有更新时间接口和创建时间接口
            var apiName = "order.increment.list";
            if (parameter.IsFullSync)
                apiName = "order.list";

            var currTime = DateTime.Now;
            var startTime = (parameter.StartTime ?? currTime.AddDays(-30)).AddMinutes(-3);
            var endTime = (parameter.EndTime ?? currTime).AddMinutes(-1);

            //按时间1小时递增 不然按天数
            var isHours = true;
            if ((endTime - startTime).TotalDays > 1)
                isHours = false;

            List<DateTime> times = new List<DateTime>();
            times.Add(startTime);
            while (startTime < endTime)
            {
                if (isHours)
                    startTime = startTime.AddHours(1);
                else
                    startTime = startTime.AddDays(1);

                if (startTime >= endTime)
                {
                    times.Add(endTime);
                    break;
                }
                else
                    times.Add(startTime);
            }

            Log.Debug(() => "时间分段：" + times.ToJson());

            var requests = new List<Dictionary<string, string>>();
            for (int i = 0; i < times.Count - 1; i++)
            {
                var start = times[i];
                var end = times[i + 1];
                var dic = new Dictionary<string, string>();
                dic.Add("poi_id", _shop.AccessToken);
                dic.Add("start_time", start.AddMinutes(-1).ToSeconds().ToString());
                dic.Add("end_time", end.ToSeconds().ToString());
                var status = ParseOrderStatusToPlatformStatus(parameter.OrderStatus);
                dic.Add("status", status.ToInt().ToString());
                requests.Add(dic);
            }
            #region 弃用
            //var maxTimeSpan = GetMaxSyncSeconds() - 5;
            //var timespan = endTime - startTime;
            //var totalSeconds = (endTime - startTime).TotalSeconds;
            //var maxParallelism = 5;
            //var hopeMaxTimeSpan = timespan.TotalSeconds / maxParallelism;
            //if (timespan.TotalMinutes <= 30)
            //    maxTimeSpan = 30f * 60;//30分钟内只开一个线程
            //else if (hopeMaxTimeSpan < maxTimeSpan)
            //    maxTimeSpan = (float)hopeMaxTimeSpan;
            //var count = Math.Ceiling(totalSeconds / maxTimeSpan);
            //var requests = new List<Dictionary<string, string>>();
            //var index = 0;
            //while (count > 0)
            //{
            //    var dic = new Dictionary<string, string>();
            //    var tmpStartTime = startTime.AddSeconds(index * maxTimeSpan).AddSeconds(-1);
            //    //var tmpEndTime = tmpStartTime.AddSeconds(maxTimeSpan).AddSeconds(1);
            //    var tmpEndTime = tmpStartTime.AddSeconds(maxTimeSpan);

            //    dic.Add("poi_id", _shop.AccessToken);
            //    dic.Add("start_time", tmpStartTime.ToSeconds().ToString());
            //    dic.Add("end_time", tmpEndTime.ToSeconds().ToString());
            //    var status = ParseOrderStatusToPlatformStatus(parameter.OrderStatus);
            //    dic.Add("status", status.ToInt().ToString());
            //    requests.Add(dic);
            //    if (tmpEndTime > parameter.EndTime)
            //    {
            //        dic["end_time"] = parameter.EndTime.Value.ToSeconds().ToString();
            //        break;
            //    }
            //    count--;
            //    index++;
            //} 
            #endregion

            var orderCountBag = new ConcurrentBag<int>();
            Parallel.ForEach(requests, new ParallelOptions { MaxDegreeOfParallelism = 5 }, request =>
            {
                try
                {
                    request.Remove("cursor");
                    var tmpOrders = SyncPageOrdersOld(request, apiName);
                    if (tmpOrders != null)
                    {
                        tmpOrders.ForEach(t =>
                        {
                            if (!orders.ContainsKey(t.PlatformOrderId))
                                orders.TryAdd(t.PlatformOrderId, t);
                        });
                    }
                    CheckMaxSyncOrder(orders.Count);
                }
                catch (Exception ex)
                {
                    Utility.Log.WriteError($"店铺{_shop.NickName}获取订单列表失败，参数：{request.ToJson()}，\n异常消息：{ex}");
                    throw new LogicException($"获取订单列表失败\n异常消息：{ex}");
                }
            });
            return orders.Values.ToList();
        }

        public List<Order> SyncOrders(List<string> pids)
        {
            List<Order> orders = new List<Order>();
            pids.ForEach(p =>
            {
                orders.Add(SyncOrder(p));
            });
            return orders;
        }

        public List<Order> SyncPageOrdersOld(Dictionary<string, string> pdic, string apiName)
        {
            var newdic = pdic;
            var orders = new List<Order>();
            var result = _client.Execute(apiName, newdic);
            var data = _client.GetErrorMessage(result);
            if (data.Item1 != 0)
            {
                throw new LogicException($"店铺{_shop.NickName}获取订单:{data.Item2}");
                //return orders;
            }

            var has_next = data.Item3.Value<bool>("has_next"); //是否有下一页
            var cursor = data.Item3.Value<string>("cursor")?.ToJson()?.TrimStart('"')?.TrimEnd('"');    //分页游标
            var order_list = data.Item3?.Value<JArray>("order_list").ToList();
            order_list.ForEach(p =>
            {
                orders.Add(TransferJTokenToOrder(p));
            });

            if (has_next)
            {
                if (newdic.ContainsKey("cursor"))
                {
                    newdic["cursor"] = cursor;
                }
                else
                {
                    newdic.Add("cursor", cursor);
                }
                orders.AddRange(SyncPageOrdersOld(newdic, apiName));
            }
            return orders;
        }
        public float GetMaxSyncSeconds(bool isRefund = false)
        {
            var maxTimeSpan = 1 * 24 * 60 * 60f; //时间分片段并发处理，最大一天
            if (isRefund)
                maxTimeSpan = 7 * 24 * 60 * 59f; //时间分片段并发处理
            return maxTimeSpan - 10;
        }
        private Order TransferJTokenToOrder(JToken _token)
        {
            var order = new Order();
            order.PlatformOrderId = _token?.Value<string>("order_view_id");
            order.ShopId = _shop.Id;
            order.PlatformType = _shop.PlatformType.ToString();

            var status = _token?.Value<string>("status");
            var platformStatus = ParseOrderStatusToPlatformStatus(status).ToString();
            order.PlatformStatus = platformStatus;

            /*
             *  //after_sale_status：订单当前售后状态： (对多商品订单无效) 2-无售后/取消售后 3-售后待处理 4-售后已驳回 5-退款中 6-退款成功 7-退款失败 
             *  order_after_sale_status： 订单售后状态（新） 
             *  0-无售后/取消售后 
             *  1-售后待处理：订单中，有商品存在 [售后待处理] 状态 
             *  2-售后已处理：订单中，所有商品都在 [售后已驳回] 
             *  [5退款中]
             *  [6退款成功]
             *  [7退款失败] 
             */
            //var after_sale_status = _token?.Value<string>("after_sale_status");
            //var refundStatus = ParseOrderStatusToRefundStatus(after_sale_status).ToString2();
            //order.RefundStatus = refundStatus;

            order.ToName = _token?.Value<string>("recipient_name") ?? "收件人";
            order.ToPhone = _token?.Value<string>("recipient_phone");
            order.ToMobile = order.ToPhone;
            order.ToProvince = _token?.Value<string>("province");
            order.ToCity = _token?.Value<string>("city");
            order.ToCounty = _token?.Value<string>("town");
            var street = _token?.Value<string>("street");
            var detail_address = _token?.Value<string>("detail_address");
            order.ToTown = street;
            order.ToAddress = detail_address;
            //var pcc = order.ToProvince + order.ToCity + order.ToCounty;
            //order.ToFullAddress = order.ToAddress?.StartsWith(pcc) ?? false ? order.ToAddress : (pcc + order.ToAddress);
            order.ToFullAddress = _token?.Value<string>("recipient_address");
            if (string.IsNullOrEmpty(order.ToProvince) || string.IsNullOrEmpty(order.ToCity))
            {
                var tuple = _acs.AddressSplit(order.ToFullAddress);
                order.ToProvince = tuple.Item1;
                order.ToCity = tuple.Item2;
                order.ToCounty = tuple.Item3;
                order.ToAddress = tuple.Item4;
                order.ToTown = "";
            }
            if (string.IsNullOrEmpty(order.ToFullAddress))
                order.ToFullAddress = order.ToProvince + order.ToCity + order.ToCounty + order.ToTown + order.ToAddress;

            //order.CreateTime = _token?.Value<long>("ctime").ConvertTimeStampToDateTime();
            var ctime = _token?.Value<string>("ctime").ToLong() ?? 0;
            if (ctime > 0)
                order.CreateTime = DateConverter.ConvertTicksToDateTime(ctime);

            //order.UpdateTime = _token?.Value<long>("utime").ConvertTimeStampToDateTime();
            var utime = _token?.Value<string>("utime").ToLong() ?? 0;
            if (utime > 0)
                order.UpdateTime = DateConverter.ConvertTicksToDateTime(utime);

            //order.PayTime = _token?.Value<long>("pay_time").ConvertTimeStampToDateTime();
            var pay_time = _token?.Value<string>("pay_time").ToLong() ?? 0;
            if (pay_time > 0)
                order.PayTime = DateConverter.ConvertTicksToDateTime(pay_time);

            order.BuyerWangWang = order.ToName;
            order.BuyerRemark = _token?.Value<string>("poi_remark");
            order.TotalAmount = _token?.Value<decimal>("settle_amount");   //订单实付价格
            if (order.TotalAmount == null)
                order.TotalAmount = _token?.Value<decimal>("original_price");   //订单总价

            var activity_meituan_amount = _token?.Value<decimal>("activity_meituan_amount");
            var activity_poi_amount = _token?.Value<decimal>("activity_poi_amount");
            var platform_charge_fee = _token?.Value<decimal>("platform_charge_fee");
            order.Discount = activity_meituan_amount + activity_poi_amount + platform_charge_fee;

            var product_list = _token?.Value<JArray>("product_list"); // 订单项
            if (product_list != null && product_list.Any())
            {
                product_list.ToList().ForEach(p =>
                {
                    var skuId = p?.Value<string>("sku_id");
                    var subItemId = order.PlatformOrderId + skuId;
                    var existItem = order.OrderItems.FirstOrDefault(x => x.SubItemID == subItemId);

                    var oi = new OrderItem();
                    oi.PlatformOrderId = order.PlatformOrderId;
                    oi.SkuID = skuId;
                    oi.SubItemID = subItemId;

                    // 根据商品是否存在退款商品
                    // 商品售后状态 2无售后 / 取消售后; 3售后待处理; 4售后已驳回; 5退款中; 6退款成功; 7退款失败
                    oi.RefundStatus = order.RefundStatus;
                    var sku_after_sale_status = p?.Value<string>("sku_after_sale_status");
                    if (sku_after_sale_status.IsNotNullOrEmpty())
                    {
                        var oiRefundStatus = ParseOrderStatusToRefundStatus(sku_after_sale_status).ToString2();
                        oi.RefundStatus = oiRefundStatus;
                    }

                    oi.Count = p?.Value<int>("product_count");
                    oi.Price = p?.Value<decimal>("product_price") ?? 0;
                    if (existItem != null)
                    {
                        existItem.Count += oi.Count;
                        existItem.Price = existItem.Price > oi.Price ? existItem.Price : oi.Price;
                        if (oi.RefundStatus.IsNotNullOrEmpty() && existItem.RefundStatus.IsNullOrEmpty())
                            existItem.RefundStatus = oi.RefundStatus;
                    }
                    else
                    {
                        oi.ShopId = order.ShopId;
                        oi.CreateTime = DateTime.Now;
                        oi.Status = order.PlatformStatus;
                        // 根据商品是否存在发货单号判断商品是否发货
                        var express_no = p?.Value<string>("express_no");
                        if (express_no.IsNotNullOrEmpty() && order.PlatformStatus == OrderStatusType.waitsellersend.ToString())
                            oi.Status = OrderStatusType.waitbuyerreceive.ToString();


                        oi.CargoNumber = p?.Value<string>("sku_code");
                        oi.productCargoNumber = oi.CargoNumber;
                        oi.SpecId = oi.SkuID;
                        oi.Color = p?.Value<string>("product_spec");
                        oi.ProductID = p?.Value<string>("spu_id");
                        oi.ProductImgUrl = p?.Value<string>("sku_pic");
                        oi.ProductSubject = p?.Value<string>("spu_name");
                        order.OrderItems.Add(oi);
                    }
                });
            }

            //加密密文
            var encrypt_recipient_name = _token?.Value<string>("encrypt_recipient_name");
            var encrypt_recipient_phone = _token?.Value<string>("encrypt_recipient_phone");
            var encrypt_detail_address = _token?.Value<string>("encrypt_detail_address");
            if (!string.IsNullOrWhiteSpace(encrypt_recipient_name)
                && !string.IsNullOrWhiteSpace(encrypt_recipient_phone)
                && !string.IsNullOrWhiteSpace(encrypt_detail_address))
            {
                //收件人信息，只要打码数据，其他值后续BulkMergerFx时统一赋值
                order.Receiver = new Receiver();
                order.Receiver.ToNameMask = order.ToName;
                order.Receiver.ToPhoneMask = order.ToPhone;
                order.Receiver.ToAddressMask = order.ToAddress;

                //全部设为密文，2022.11.23
                //order.ToName = encrypt_recipient_name;
                //order.ToMobile = encrypt_recipient_phone;
                //order.ToAddress = encrypt_detail_address;
                //order.ToPhone = order.ToMobile;

                var encryptedReceiverInfo = new KuaiShouEncryptedReceiverInfo();
                encryptedReceiverInfo.ShopId = order.ShopId;
                encryptedReceiverInfo.PlatformOrderId = order.PlatformOrderId;
                encryptedReceiverInfo.EncryptedToName = _token?.Value<string>("encrypt_recipient_name");
                encryptedReceiverInfo.EncryptedToMobile = _token?.Value<string>("encrypt_recipient_phone");
                encryptedReceiverInfo.EncryptedToAddress = _token?.Value<string>("encrypt_detail_address");
                //团好货没有索引串,使用oaid标识来生成RelationCode
                encryptedReceiverInfo.ToAddressIndex = _token?.Value<string>("oaid");

                encryptedReceiverInfo.CreateTime = DateTime.Now;
                encryptedReceiverInfo.OrderCode = (order.PlatformOrderId + order.ShopId).ToShortMd5(); //对应P_Order.OrderCode 按这个生成规则一样

                order.ExtField1 = encryptedReceiverInfo.RelationCode;
                order.EncryptedReceiverInfo = encryptedReceiverInfo;
            }
            //备用合并订单oid
            //收货人信息标识，长度64字符 通过(收货人+手机号+收货地址)来生成oaid , 三个信息都不能为空才能生成oaid，否则oaid为空
            order.ExtField2 = _token?.Value<string>("oaid");
            TransferOrderRefundStatus(order);
            return order;
        }
        #endregion

        #region 商品
        public List<Product> SyncProduct(int statusType, DateTime? start = null, DateTime? end = null,
            bool isNotPinged = false)
        {
            var apiName = "goods.list";
            var page = 20;
            var pageCount = 40;
            List<Dictionary<string, string>> dics = new List<Dictionary<string, string>>();
            for (int i = 1; i <= page; i++)
            {
                var dic = new Dictionary<string, string>();
                dic.Add("poi_id", _shop.AccessToken);
                dic.Add("page_index", i.ToString());
                dic.Add("page_size", pageCount.ToString());
                dics.Add(dic);
            }

            var productBag = new ConcurrentBag<Product>();
            Parallel.ForEach(dics, new ParallelOptions { MaxDegreeOfParallelism = 5 }, dic =>
            {
                try
                {
                    var result = _client.Execute(apiName, dic);
                    var data = _client.GetErrorMessage(result);
                    if (data.Item1 != 0)
                        throw new LogicException($"店铺{_shop.NickName}获取商品:{data.Item2}");

                    var goods_list = data.Item3?.Value<JArray>("goods_list").ToList();
                    goods_list.ForEach(g =>
                    {
                        var pid = g?.Value<string>("goods_id");
                        productBag.Add(SyncProduct(pid));
                    });
                }
                catch (Exception ex)
                {
                    throw new LogicException($"获取商品列表失败");
                }
            });
            return productBag.ToList();
        }

        public Product SyncProduct(string platformId)
        {
            var apiName = "goods.detail";
            var dic = new Dictionary<string, string>();
            dic.Add("poi_id", _shop.AccessToken);
            dic.Add("goods_id", platformId);
            var result = _client.Execute(apiName, dic);
            var data = _client.GetErrorMessage(result);
            if (data.Item1 != 0)
                throw new LogicException($"店铺{_shop.NickName}获取商品:{data.Item2}");
            try
            {

                return TransferJTokenToProduct(data.Item3);
            }
            catch (Exception e)
            {
                throw new LogicException($"店铺【{_shop.NickName}】同步商品格式错误");
            }
        }

        private Product TransferJTokenToProduct(JToken jToken)
        {
            var _product = new Product();
            _product.ShopId = _shop.Id;
            _product.ShopName = _shop.NickName;
            _product.PlatformId = jToken?.Value<string>("goods_id");
            _product.CategoryId = jToken?.Value<string>("category_id");
            _product.Subject = jToken?.Value<string>("title");
            _product.ImageUrl = jToken?.Value<JArray>("loop_video_list").FirstOrDefault().ToString();
            _product.CostPrice = jToken.Value<decimal>("market_price");
            _product.PlatformType = _shop.PlatformType.ToString2();  //平台类型
            _product.Status = jToken.Value<int>("sell_status") > 0 ? "deleted" : "published";
            _product.CargoNumber = "";
            _product.ShortTitle = "";

            try
            {
                //详情
                var detail_image_list = jToken?.Value<JArray>("detail_image_list");
                _product.Description = detail_image_list?.FirstOrDefault(a => a.Value<int>("type") == 0)?.Value<string>("src");
                //详情图片列表
                if (detail_image_list != null && detail_image_list.Any(a => a.Value<int>("type") == 1))
                    _product.DetailImageUrls = detail_image_list.Where(a => a.Value<int>("type") == 1).Select(a => a.Value<string>("src")).ToList();
            }
            catch(Exception ex) { }

            _product.Weight = 0;
            _product.SetWeight = 0;
            _product.GroupID = "";
            _product.Bazaar = "";
            _product.Stall = "";
            _product.CostPrice = 0;
            _product.IsComm = false;
            _product.IsSetWeight = false;

            var sku_list = jToken?.Value<JArray>("sku_list")?.ToList();
            if (sku_list != null)
            {
                sku_list.ForEach(p =>
                {
                    var sku = new ProductSku();
                    sku.Price = p?.Value<decimal>("single_price");
                    if (sku.Price <= 0)
                        sku.Price = p?.Value<decimal>("group_price");
                    sku.CargoNumber = p?.Value<string>("sku_code");
                    sku.SkuId = p?.Value<string>("sku_id");
                    sku.SpecId = sku.SkuId;

                    var standard_list = p?.Value<JArray>("standard_list").ToList();
                    var name = string.Empty;
                    var value = string.Empty;
                    standard_list.ForEach(s =>
                    {
                        string standard_value = s?.Value<string>("standard_value");
                        if (name.IsEmpty())
                            name = standard_value;
                        if (value.IsEmpty() && !name.Equals(standard_value) && !standard_value.IsEmpty())
                            value = s?.Value<string>("standard_value");


                        //属性k-v值
                        var att_k = s?.Value<string>("standard_name") ?? "";
                        var att_v = s?.Value<string>("standard_value") ?? "";
                        if (att_k.IsNullOrEmpty() == false && att_v.IsNullOrEmpty() == false && sku.SkuAttrs.Any(a => a.k == att_k) == false)
                        {
                            sku.SkuAttrs.Add(new SkuAttr { k = att_k, v = att_v });
                        }
                    });
                    ProductSkuAttribute attr = new ProductSkuAttribute();
                    attr.AttributeName = name;
                    attr.AttributeValue = value;
                    attr.ShortTitle = "";
                    attr.Weight = 0;
                    attr.Bazaar = "";
                    attr.Stall = "";
                    attr.CostPrice = sku.Price;
                    attr.SkuImgUrl = p?.Value<string>("preview_image");
                    sku.ProductSkuAttr = attr;
                    _product.Skus.Add(sku);
                });
            }
            return _product;
        }
        #endregion
        public List<DeliverySendOrderResultModel> OnlineSend(DeliverySendOrderModel model)
        {
            //循环发货，支持合并订单发货，一次只能发货一个订单
            var orders = model.Orders;
            var ecm = model.ExpressCodeMapping;
            //用户勾选的数量
            var results = new List<DeliverySendOrderResultModel>();
            foreach (var orderEntity in orders)
            {
                var orderRequest = model.OrderRequests.FirstOrDefault(o => o.Id == orderEntity.Id);
                var result = new DeliverySendOrderResultModel
                {
                    ErrorCode = "",
                    ErrorMessage = "",
                    ExtErrorMessage = "",
                    IsSuccess = false,
                    OrderEntity = orderEntity,
                    OrderRequest = orderRequest
                };
                //订单信息
                var ois = orderRequest.OrderItems;
                var successOrderItems = new List<int>();
                var pids = orderEntity.OrderItems.Where(oi => orderRequest.OrderItems.Contains(oi.Id)).Select(oi => string.IsNullOrEmpty(oi.OrignalOrderId) ? oi.PlatformOrderId : oi.OrignalOrderId).Distinct().ToList();
                pids.ForEach(pid =>
                {
                    var curOrderItems = orderEntity.OrderItems.Where(oi => oi.PlatformOrderId == pid || oi.OrignalOrderId == pid).Select(oi => oi.Id).Distinct();
                    var dict = new Dictionary<string, string>();
                    dict.Add("poi_id", _shop.ShopId); //美团平台商家ID
                    dict.Add("order_view_id", pid);
                    dict.Add("express_info_list", new List<object>() {new
                    {
                        expressCode = ecm.PlatformExpressCode,
                        expressNo = orderRequest.WaybillCode
                    }}.ToJson());

                    var json = "";
                    try
                    {
                        var apiName = "order.perform";
                        json = _client.Execute(apiName, dict);
                        var data = _client.GetErrorMessage(json);
                        if (data.Item1 == 0)
                        {
                            result.IsSuccess = true;
                            successOrderItems.AddRange(curOrderItems);
                        }
                        else
                        {
                            result.OriginalResult = json;//原始内容
                            if (pids.Count() > 1)
                            {
                                result.ErrorMessage += $"订单【{pid}】发货失败： {data.Item2} {data.Item1}</br>";
                                //用于平台接口提示码优化 去重
                                var code = data.Item1.ToString();
                                if (result.ErrorCode == "")
                                {
                                    result.ErrorCode = code;
                                }
                                else
                                {
                                    var tempCode = result.ErrorCode.Split(',');
                                    if (!tempCode.Where(x => x == code).Any())
                                    {
                                        result.ErrorCode += "," + code;
                                    }
                                }
                            }
                            else
                            {
                                result.IsSuccess = false;
                                result.ErrorCode = data.Item1.ToString();
                                result.ErrorMessage = data.Item2;
                            }
                        }
                    }
                    catch (LogicException ex)
                    {
                        result.OriginalResult = json;//原始内容
                        result.ErrorMessage += $"订单【{pid}】发货失败：{ex.Message}";
                        result.ErrorCode += "Logic Error </br>";
                    }
                    catch (Exception ex)
                    {
                        result.OriginalResult = json;//原始内容
                        Log.WriteError($"团好货订单【{pid}】发货失败：请求参数：{dict.ToJson()} 返回：{json} 错误：{ex}");
                        result.ErrorMessage += $"订单【{pid}】发货失败：系统错误";
                        result.ErrorCode += "System Error </br>";
                    }
                });
                if (!successOrderItems.Any())
                {
                    result.IsSuccess = false;
                    successOrderItems = ois;
                }
                else
                    result.OrderRequest.OrderItems = successOrderItems;
                results.Add(result);
            }
            return results;
        }

        #region 状态转换
        /// <summary>
        /// 团好货平台订单状态
        /// </summary>
        public enum PlatformOrderStatusType
        {
            /// <summary>
            /// 全部
            /// </summary>
            ALL = 1,
            /// <summary>
            /// 待发货
            /// </summary>
            Delivered = 2,
            /// <summary>
            /// 待收货
            /// </summary>
            Delivery = 3,
            /// <summary>
            /// 交易成功
            /// </summary>
            Success = 4,
            /// <summary>
            /// 交易关闭
            /// </summary>
            ClosureLL = 7,
            /// <summary>
            /// 待付款
            /// </summary>
            Payment = 9,
        }

        /// <summary>
        /// 我们系统的订单状态转换为团好货平台订单状态
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private PlatformOrderStatusType ParseOrderStatusToPlatformStatus(OrderStatusType? status)
        {
            if (status == null)
                return PlatformOrderStatusType.ALL;
            PlatformOrderStatusType pos = PlatformOrderStatusType.Delivered; ;
            switch (status)
            {
                case OrderStatusType.waitsellersend:
                    pos = PlatformOrderStatusType.Delivered;
                    break;
                case OrderStatusType.waitbuyerreceive:
                    pos = PlatformOrderStatusType.Delivery;
                    break;
                case OrderStatusType.success:
                    pos = PlatformOrderStatusType.Success;
                    break;
                case OrderStatusType.cancel:
                    pos = PlatformOrderStatusType.ClosureLL;
                    break;
                case OrderStatusType.waitbuyerpay:
                    pos = PlatformOrderStatusType.Payment;
                    break;
                default:
                    break;
            }
            return pos;
        }

        /// <summary>
        /// 团好货平台订单状态转换为我们系统的订单状态
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private OrderStatusType ParseOrderStatusToPlatformStatus(string status)
        {
            OrderStatusType orderStatusType = OrderStatusType.waitsellersend;
            switch (status)
            {
                case "2":
                    orderStatusType = OrderStatusType.waitsellersend;
                    break;
                case "3":
                    orderStatusType = OrderStatusType.waitbuyerreceive;
                    break;
                case "4":
                    orderStatusType = OrderStatusType.success;
                    break;
                case "7":
                    orderStatusType = OrderStatusType.cancel;
                    break;
                case "9":
                    orderStatusType = OrderStatusType.waitbuyerpay;
                    break;
                default:
                    break;
            }
            return orderStatusType;
        }

        /// <summary>
        /// 团好货平台退款状态 转换为 我们系统的退款状态
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private RefundStatusType? ParseOrderStatusToRefundStatus(string platformRefundStatus)
        {
            /*  订单当前售后状态：
                2 - 无售后/取消售后
                3 - 售后待处理
                4 - 售后已驳回
                5 - 退款中
                6 - 退款成功
                7 - 退款失败
            */
            RefundStatusType? rs = null;
            if (platformRefundStatus.IsNullOrEmpty() || platformRefundStatus == "0")
                return rs;
            switch (platformRefundStatus)
            {
                case "4":
                case "7":
                    rs = RefundStatusType.REFUND_CLOSE;
                    break;
                case "3":
                case "5":
                    rs = RefundStatusType.WAIT_SELLER_AGREE;
                    break;
                case "6":
                    rs = RefundStatusType.REFUND_SUCCESS;
                    break;
                default:
                    break;
            }
            return rs;
        }

        public List<DeliverySendOrderResultModel> OnlineResend(DeliverySendOrderModel model)
        {
            List<DeliverySendOrderResultModel> list = new List<DeliverySendOrderResultModel>();
            model.Orders.ForEach(x =>
            {
                DeliverySendOrderResultModel result = new DeliverySendOrderResultModel()
                {
                    IsSuccess = false,
                    ErrorCode = "PLATFORM_DOES_NOT_SUPPORT",
                    ExtErrorMessage = $"订单【{x.PlatformOrderId}】发货失败,平台暂时不支持二次发货"
                };
                list.Add(result);
            });
            return list;
        }
        #endregion

        /// <summary>
        /// 解密
        /// </summary>
        /// <param name="orders"></param>
        /// <param name="isMask">是否打码</param>
        /// <param name="field"></param>
        /// <returns></returns>
        public List<Order> DecryptBatch(List<Order> orders, string field = "")
        {
            if (orders == null || orders.Any() == false)
                return orders;

            var decrypt_data = new Dictionary<string, Order>();
            foreach (var order in orders)
            {
                //if (order.EncryptedReceiverInfo == null)
                //    continue;

                var pid = order.PlatformOrderId.TrimStart("C");
                if (!decrypt_data.ContainsKey(pid))
                    decrypt_data.Add(pid, order);
            }
            if (decrypt_data.Any() == false)
                return orders;

            var pageSize = string.IsNullOrWhiteSpace(field) ? 16 : 50;  //解密数据列表，每次脱敏的数据 16个订单/3条加密串 = 48 < 小于50
            var totalPageCount = decrypt_data.Count() / pageSize + 1;
            Parallel.For(0, totalPageCount, new ParallelOptions { MaxDegreeOfParallelism = 1 }, i =>
            {
                int index = i * pageSize;
                var temps = decrypt_data.Skip(index).Take(pageSize).ToList();
                if (temps == null || temps.Any() == false)
                    return;
                var tempOrder = temps.ToDictionary(f => f.Key, f => f.Value);
                var orderBatchs = new List<OrderBatchInfo>();
                foreach (var item in tempOrder)
                {
                    orderBatchs.Add(new OrderBatchInfo
                    {
                        index = 1,
                        order_view_id = item.Key,
                        cipher_text = item.Value.ToName
                    });
                    orderBatchs.Add(new OrderBatchInfo
                    {
                        index = 2,
                        order_view_id = item.Key,
                        cipher_text = item.Value.ToMobile
                    });
                    orderBatchs.Add(new OrderBatchInfo
                    {
                        index = 3,
                        order_view_id = item.Key,
                        cipher_text = item.Value.ToAddress
                    });
                }
                var decrypts = DecryptBatch(orderBatchs);
                if (decrypts != null && decrypts.Any())
                {
                    var lookups = decrypts.ToLookup(s => s.order_view_id, s => s);
                    foreach (var lookup in lookups)
                    {
                        orders.ForEach(o =>
                        {
                            if (o.PlatformOrderId.TrimStart('C') == lookup.Key)
                            {
                                var name = lookup.FirstOrDefault(l => l.index == 1)?.plain_text;
                                if (!string.IsNullOrWhiteSpace(name))
                                    o.ToName = name;

                                var phone = lookup.FirstOrDefault(l => l.index == 2)?.plain_text;
                                if (!string.IsNullOrWhiteSpace(phone))
                                    o.ToPhone = phone;

                                var address = lookup.FirstOrDefault(l => l.index == 3)?.plain_text;
                                if (!string.IsNullOrWhiteSpace(address))
                                {
                                    var streetIndex = 0;
                                    if (o.ToAddress.Contains("街道"))
                                    {
                                        streetIndex = o.ToAddress.IndexOf("街道") > 0 ? o.ToAddress.IndexOf("街道") + 2 : 0;
                                    }
                                    else if (o.ToAddress.Contains("镇"))
                                    {
                                        streetIndex = o.ToAddress.IndexOf("镇") > 0 ? o.ToAddress.IndexOf("镇") + 1 : 0;
                                    }

                                    if (streetIndex == 0)
                                    {
                                        if (address.Length < o.ToAddress.Length)
                                        {
                                            var firstIndex = o.ToAddress.IndexOf(address[0]);
                                            if (firstIndex > 1)
                                                streetIndex = firstIndex;
                                        }
                                    }
                                    if (streetIndex > 0)
                                    {
                                        var street = o.ToAddress.Substring(0, streetIndex);
                                        address = street + address;
                                    }
                                    o.ToAddress = address;
                                }

                                o.BuyerWangWang = o.ToName;
                                o.ToMobile = o.ToPhone;
                                o.ToFullAddress = o.ToProvince + o.ToCity + o.ToCounty + o.ToTown + o.ToAddress;
                            }
                        });
                    }
                }
            });
            return orders;
        }

        /// <summary>
        /// 批量解密
        /// </summary>
        /// <returns></returns>
        private List<OrderBatchInfo> DecryptBatch(List<OrderBatchInfo> cipher_infos)
        {
            var apiName = "order.batchDecrypt";
            var dic = new Dictionary<string, string>();
            dic.Add("poi_id", _shop.AccessToken);
            dic.Add("cipher_info_list", cipher_infos.ToJson());
            var result = _client.Execute(apiName, dic);
            var data = _client.GetErrorMessage(result);
            var errMsg = data.Item2.ToString2();
            if (data.Item1 != 0)
            {
                Log.WriteError($"团好货平台接口：店铺【{_shop.ShopName}】调用API接口【{apiName}】错误，请求参数：{dic.ToJson()} ，返回结果：{result}");
                if (errMsg.Contains("频繁") == true)
                    throw new LogicException($"团好货平台接口：解密操作过于频繁，请过会儿再试");
                else if (errMsg.Contains("过期") == true || errMsg.Contains("授权已被关闭") == true)
                    throw new LogicException($"团好货平台接口：店铺授权过期，请重新从团好货后台进入店管家", "Shop_Auth_Expired");
                else if (errMsg.Contains("额度不够") == true || errMsg.Contains("解密上限") == true)
                    throw new LogicException($"团好货平台接口：店铺已达解密额度上限");
                else
                    throw new LogicException($"团好货平台接口：{errMsg}");
            }

            var plain_info_list = data.Item3?.Value<JToken>("plain_info_list")?.ToList();
            if (plain_info_list != null && plain_info_list.Any())
            {
                plain_info_list.ForEach(pl =>
                {
                    var order_view_id = pl?.Value<string>("order_view_id");
                    var cipher_text = pl?.Value<string>("cipher_text");
                    var plain_text = pl?.Value<string>("plain_text");
                    cipher_infos.ForEach(c =>
                    {
                        if (c.order_view_id == order_view_id && c.cipher_text == cipher_text)
                            c.plain_text = plain_text;
                    });
                });
            }
            return cipher_infos;
        }

        public List<string> SyncOrderJsons(List<string> pids)
        {
            var orders = new ConcurrentBag<string>();
            if (pids == null || !pids.Any())
                return new List<string>();

            Parallel.ForEach(pids, new ParallelOptions { MaxDegreeOfParallelism = 5 }, pid =>
            {
                var o = SyncOrderJson(pid);
                if (o != null)
                    orders.Add(o);
            });
            return orders.ToList();
        }

        public string SyncOrdersJson(SyncOrderParametersModel parameter)
        {
            throw new LogicException("通过游标获取订单，无法获取总数");
        }
        public string SyncOrderJson(string platformOrderId)
        {
            var apiName = "order.detail";
            var dic = new Dictionary<string, string>();
            dic.Add("poi_id", _shop.AccessToken);
            dic.Add("order_view_id", platformOrderId);
            var result = _client.Execute(apiName, dic);
            return result;
        }

        /// <summary>
        /// 修改订单收件人信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public KeyValuePair<bool, string> UpdateReceiverInfo(UpdateReceiverModel model)
        {
            throw new NotImplementedException();
        }

        public MergeOrderQueryResponseModel MergeOrderQuery(List<MergeOrderQueryRequstModel> requests)
        {
            throw new NotImplementedException();
        }

        public int GetOrderCountByConditions(SyncOrderParametersModel syncOrderParameters)
        {
            return 0;
        }
    }

    class OrderBatchInfo
    {
        public int index { get; set; }
        public string order_view_id { get; set; }
        public string cipher_text { get; set; }
        public string plain_text { get; set; }
    }
}
