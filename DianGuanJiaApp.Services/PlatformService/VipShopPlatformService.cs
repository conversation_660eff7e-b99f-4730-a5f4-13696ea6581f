using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using System.Collections.Concurrent;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Enum;
using vipapis.marketplace.store;
using vipapis.marketplace.delivery;
using Log = DianGuanJiaApp.Utility.Log;
using static vipapis.marketplace.asc.AscServiceHelper;
using vipapis.marketplace.asc;
using static vipapis.marketplace.brand.StoreBrandServiceHelper;
using static vipapis.marketplace.product.ProductServiceHelper;
using static vipapis.marketplace.price.PriceServiceHelper;
using static vipapis.marketplace.category.CategoryServiceHelper;

namespace DianGuanJiaApp.Services.PlatformService
{
    public class VipShopPlatformService : BasePlatformService, IPlatformService
    {
        #region 私有变量

        private VipShopApiClient _client = null;
        private Shop _shop = null;
        private AreaCodeInfoService _acs = new AreaCodeInfoService();


        /// <summary>
        /// 唯品会平台订单状态
        /// </summary>
        public enum PlatformOrderStatusType
        {
            /// <summary>
            /// 待确认
            /// </summary>
            WAIT_CONFIRM = 0,
            /// <summary>
            /// 待发货-订单已审核
            /// </summary>
            WAIT_SELLER_SEND_GOODS = 10,
            /// <summary>
            /// 等待买家确认-订单已发货
            /// </summary>
            WAIT_BUYER_CONFIRM_GOODS = 22,
            /// <summary>
            /// 订单完成-已签收
            /// </summary>
            TRADE_SUCCESS = 25,
            /// <summary>
            /// 订单关闭-订单已取消
            /// </summary>
            TRADE_CLOSE = 97,
            /// <summary>
            /// 退货中-已拒收
            /// </summary>
            TRADE_REFUSAL = 70,
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 我们系统的订单状态 转换为  唯品会平台订单状态
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private int ParseOrderStatusToPlatformStatus(OrderStatusType? status)
        {
            //订单状态. 10:订单已审核, 22:订单已发货, 25:已签收, , 70:已拒收, 97:订单已取消
            if (status == null)
                return 10;

            int result = 10;
            switch (status)
            {
                case OrderStatusType.waitsellersend:
                    result = 10;
                    break;
                case OrderStatusType.waitbuyerreceive:
                    result = 22;
                    break;
                case OrderStatusType.success:
                    result = 25;
                    break;
                case OrderStatusType.cancel:
                    result = 97;
                    break;
                case OrderStatusType.waitbuyerpay:
                case OrderStatusType.confirm_goods_but_not_fund:
                    break;
                case OrderStatusType.confirm_goods:
                    break;
                case OrderStatusType.signinsuccess:
                    break;
                case OrderStatusType.terminated:
                    break;
                case OrderStatusType.wait_seller_accept_order:
                    break;
                case OrderStatusType.send_goods_but_not_fund:
                    break;
                default:
                    break;
            }
            return result;
        }

        /// <summary>
        /// 唯品会平台订单状态 转换为 我们系统的订单状态
        /// </summary>
        /// <param name="status">10:订单已审核, 22:订单已发货, 25:已签收, , 70:已拒收, 97:订单已取消</param>
        /// <returns></returns>
        private OrderStatusType? ParsePlatformStatusToOrderStatus(string platformOrderStatus)
        {
            if (string.IsNullOrWhiteSpace(platformOrderStatus) == true)
                return null;
            OrderStatusType? os = null;
            switch (platformOrderStatus)
            {
                case "10":
                    os = OrderStatusType.waitsellersend;
                    break;
                case "22":
                    os = OrderStatusType.waitbuyerreceive;
                    break;
                case "25":
                    os = OrderStatusType.success;
                    break;
                //case "70":
                //    os = OrderStatusType.wait_seller_accept_order;  // 拒收
                //    break;
                case "97":
                    os = OrderStatusType.cancel;
                    break;
                default:
                    os = OrderStatusType.waitbuyerpay;
                    break;
            }
            return os;
        }

        /// <summary>
        /// 唯品会平台退款状态 转换为 我们系统的退款状态
        /// </summary>
        /// <param name="platformRefundStatus"></param>
        /// <returns></returns>
        private string ParsePlatformRefundStatusToOrderRefundStatus(string platformRefundStatus)
        {
            //售后申请单状态：0 待审核，1 已审核，2 审核不通过，3 已同意退款，4 已拒绝退款，9 已完成，10 已取消
            string rs = null;
            if (string.IsNullOrWhiteSpace(platformRefundStatus) == true)
                return null;
            switch (platformRefundStatus)
            {
                case "0":
                    rs = RefundStatusType.WAIT_SELLER_AGREE.ToString();
                    break;
                case "1":
                case "3":
                case "9":
                    rs = RefundStatusType.REFUND_SUCCESS.ToString();
                    break;
                case "2":
                case "4":
                case "10":
                    rs = RefundStatusType.REFUND_CLOSE.ToString();
                    break;
            }
            return rs;
        }

        /// <summary>
        /// 生成订单查询参数
        /// </summary>
        /// <param name="startTime">查询时间段的开始时间,最多支持最近3个月内的订单查询,默认返回最近一个月内的订单,格式:yyyy-MM-dd HH:mm:SS，</param>
        /// <param name="endTime">查询时间段的结束时间,最多支持最近3个月内的订单查询,格式:yyyy-MM-dd HH:mm:SS</param>
        /// <param name="status">订单状态. 10:订单已审核, 22:订单已发货, 25:已签收, , 70:已拒收, 97:订单已取消</param>
        /// <param name="page">页数，默认1</param>
        /// <param name="pageSize">每页数量，默认50 最大200</param>
        /// <param name="order_ids">订单号</param>
        /// <param name="is_export">导出状态,0:未导出 1：已导出</param>
        /// <param name="date_type">查询时间类型，默认按修改时间查询。1为按订单创建时间查询；其它数字为按订单最后修改时间</param>
        /// <returns></returns>
        private GetOrdersRequest BuildGetOrderParameter(DateTime? startTime = null, DateTime? endTime = null, List<string> status = null, int page = 1, int pageSize = 50, List<string> order_ids = null, bool is_export = false, int date_type = 1)
        {
            GetOrdersRequest req = new GetOrdersRequest();
            if (startTime != null)
                req.SetQuery_start_time(startTime?.ToString("yyyy-MM-dd HH:mm:ss"));
            if (endTime != null)
                req.SetQuery_end_time(endTime?.ToString("yyyy-MM-dd HH:mm:ss"));
            if (is_export)
                req.SetIs_export(is_export ? "1" : "0");
            if (date_type > 0)
                req.SetDate_type(date_type);
            if (pageSize > 0)
                req.SetLimit(pageSize);
            if (page > 0)
                req.SetPage(page);
            if (status != null && status.Any())
                req.SetStatus(status);
            if (order_ids != null && order_ids.Any())
                req.SetOrder_ids(order_ids);
            return req;
        }

        /// <summary>
        /// SovOrder转系统订单类型
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        private Order TransferSovOrderToOrder(SovOrder sov)
        {
            var order = new Order();
            order.PlatformOrderId = sov.GetOrder_id();
            order.ShopId = _shop.Id;
            order.PlatformType = _shop.PlatformType.ToString();
            var platformStatus = sov.GetStatus(); //10:订单已审核, 22:订单已发货, 25:已签收, , 70:已拒收, 97:订单已取消
            var orderStatus = platformStatus == "70" ? OrderStatusType.waitbuyerreceive.ToString() : ParsePlatformStatusToOrderStatus(platformStatus).ToString2();
            order.BackUpPlatformStatus = orderStatus.ToString();
            order.PlatformStatus = order.BackUpPlatformStatus;
            order.OrderFrom = "Sov";


            EncryptData _data = new EncryptData();
            List<string> info = new List<string>()
            {
                sov.GetReceiver_name(),
                sov.GetReceiver_mobile(),
                sov.GetReceiver_address()?.ToString2() ?? ""
            };
            _data.SetEncrypt_codes(info);
            _data.SetOrder_id(order.PlatformOrderId);
            var mingwen = decryptOrderInfo(new List<EncryptData>()
            {
                _data
            });

            order.ToName = mingwen[0].GetDecrypt_code() ?? mingwen[0].GetEncrypt_code();
            order.ToMobile = mingwen[1].GetDecrypt_code() ?? mingwen[1].GetEncrypt_code();
            //order.ToPhone = sov.GetReceiver_phone();
            order.ToPhone = order.ToMobile;
            order.ToPost = sov.GetReceiver_zip();

            order.ToCounty = sov.GetReceiver_district();
            order.ToCity = sov.GetReceiver_city();
            order.ToProvince = sov.GetReceiver_state();
            order.ToAddress = mingwen[2].GetDecrypt_code() ?? mingwen[2].GetEncrypt_code();

            order.ToAddress = order.ToAddress.Replace(".", "");
            if (order.ToAddress?.StartsWith(order.ToProvince) == true)
                order.ToAddress = order.ToAddress.TrimStart(order.ToProvince);
            if (order.ToAddress?.StartsWith(order.ToCity) == true)
                order.ToAddress = order.ToAddress.TrimStart(order.ToCity);
            if (order.ToAddress?.StartsWith(order.ToCounty) == true)
                order.ToAddress = order.ToAddress.TrimStart(order.ToCounty);

            var pcc = order.ToProvince + order.ToCity + order.ToCounty;
            order.ToFullAddress = order.ToAddress.Contains(order.ToProvince) && order.ToAddress.Contains(order.ToCity) && order.ToAddress.Contains(order.ToCounty) ? order.ToAddress : (pcc + order.ToAddress);
            if (order.ToFullAddress.Contains("."))
            {
                order.ToFullAddress = order.ToFullAddress.Replace(".", "");
            }

            order.BuyerRemark = sov.GetRemark();
            order.SellerRemark = sov.GetVendor_memo(); //厂家备注
            order.BuyerWangWang = order.ToName;
            order.BuyerMemberId = sov.GetOpen_user_id();
            order.BuyerMemberName = "";

            var totalFee = sov.GetTotal_fee(); //整张出库单商品金额总和(正常单价之和，不计折扣，不包含运费) 
            order.ShippingFee = sov.GetPost_fee().ToDecimal(); //快递费用
            var cuxiaoFee = sov.GetEx_discount_fee().ToDecimal();//促销优惠金额
            order.Discount = sov.GetDiscount_fee().ToDecimal() + cuxiaoFee; //优惠金额
            order.TotalAmount = sov.GetPayable_fee().ToDecimal();//客户应付金额（客户应付金额=订单商品总金额+运费-优惠金额） 

            order.Warehouse = sov.GetWarehouse();
            order.BusinessType = sov.GetShipping_method()?.ToString2() ?? "0"; //发货方式：0，快递（默认） 1，门店自提
            order.TradeType = sov.GetPay_type(); //支付方式（可使用的支付方式，唯品支付或者微信支付） 

            order.CreateTime = sov.GetCreated().ToDateTime();  //订单下单时间
            order.ModifyTime = sov.GetLast_update_time().ToDateTime();
            order.PayTime = sov.GetStore_add_time().ToDateTime(); //订单流入店铺的时间
            order.ConfirmedTime = sov.GetStore_add_time().ToDateTime(); //订单流入店铺的时间   
            order.LastSyncTime = DateTime.Now;
            order.ReceivingTime = sov.GetTransport_day().ToDateTime(); //客户要求送货时间 
                                                                       //order.AllDeliveredTime = DateConverter.ConvertStringToDateTime(_token?.Value<string>("deliveryTime"));

            //order.RefundStatus = platformStatus == "70" ? RefundStatusType.WAIT_SELLER_AGREE.ToString() : "";
            //order.RefundPayment = 0;
            //order.SellerRateStatus = null; //卖家评价状态
            //order.BuyerRateStatus = null; //买家评价状态

            return order;
        }


        #endregion

        #region 构造函数

        public VipShopPlatformService(Shop shop):base(shop)
        {
            _client = new VipShopApiClient(shop);
            _shop = shop;
        }

        #endregion

        #region 接口实现

        /// <summary>
        /// 刷新店铺token
        /// </summary>
        /// <returns></returns>
        public Shop RefreshShopToken()
        {
            _client.RefreshToken();
            return _shop;
        }

        public Shop SyncShopInfo()
        {
            try
            {
                StoreInfoServiceHelper.StoreInfoServiceClient client = new StoreInfoServiceHelper.StoreInfoServiceClient();
                client.SetClientInvocationContext(_client.GetInvocationContext());
                var result = client.getStoreInfo();

                if (result != null)
                {
                    _shop.ShopId = result.GetStore_id();
                    _shop.ShopName = result.GetStore_name();
                    _shop.NickName = _shop.ShopName;
                }
            }
            catch (Osp.Sdk.Exception.OspException e)
            {
                Log.WriteLine($"唯品会同步店铺信息异常：{e.ToString()}");
                _shop = null;
            }
            return _shop;
        }

        public string SyncBrandInfo()
        {
            var brandId = string.Empty;
            StoreBrandServiceClient client = new StoreBrandServiceClient();
            client.SetClientInvocationContext(_client.GetInvocationContext());
            var result = client.getStoreBrands();
            if (result != null)
            {
                result.ForEach(b =>
                {
                    brandId = b.GetBrand_id();
                });
            }

            return brandId;
        }

        public string GetProductPreviewUrl(string pid, string skuId)
        {
            ProductServiceClient client = new ProductServiceClient();
            client.SetClientInvocationContext(_client.GetInvocationContext());
            var result = client.getProductPreviewUrl(pid, skuId);
            return result;
        }

        /// <summary>
        /// 同步订单
        /// </summary>
        /// <param name="parameter"></param>
        /// <returns></returns>
        public List<Order> SyncOrders(SyncOrderParametersModel parameter,bool isPinged=false)
        {
            var orders = new List<Order>();
            var pageSize = 100;  //	每页数量，默认50 最大200
            DateTime startTime, endTime = DateTime.Now;
            if (parameter.EndTime != null)
                endTime = parameter.EndTime.Value;
            if (parameter.StartTime != null && parameter.StartTime.Value < DateTime.Now)
                startTime = parameter.StartTime.Value;
            else if (parameter.IsFullSync)
                startTime = DateTime.Now.AddMonths(-3);
            else
                startTime = _shop.LastSyncTime ?? _shop.CreateTime.Value.AddDays(-5);
            if (startTime.AddMonths(-3) > DateTime.Now)
                startTime = startTime.AddMonths(-3);
            parameter.StartTime = startTime;
            startTime = startTime.AddMinutes(-3); //往前移三分钟
            parameter.EndTime = endTime;
            //Ping();
            //查询时间段的开始时间,最多支持最近3个月内的订单查询,默认返回最近一个月内的订单,格式:yyyy-MM-dd HH:mm:SS，如'2018-03-01 10:01:30'
            if (startTime < DateTime.Now.AddMonths(-3))
            {
                //throw new LogicException("最多支持最近3个月内的订单查询");
            }

            List<string> status = null;
            if (!parameter.OrderStatus.IsNullOrEmpty())
                status = new List<string>() { ParseOrderStatusToPlatformStatus(parameter.OrderStatus).ToString2() };
            //查询时间类型，默认按修改时间查询。1为按订单创建时间查询；其它数字为按订单最后修改时间
            var date_type = parameter.IsFullSync ? 1 : 0;
            var req = BuildGetOrderParameter(startTime: startTime, endTime: endTime, status: status, page: 1, pageSize: pageSize, date_type: date_type);
            var total = 0;
            var sovOrders = new List<SovOrder>();
            try
            {
                SovDeliveryServiceHelper.SovDeliveryServiceClient client = new SovDeliveryServiceHelper.SovDeliveryServiceClient();
                client.SetClientInvocationContext(_client.GetInvocationContext());
                var rsp = client.getOrders(req);
                total = rsp.GetTotal() ?? 0;
                sovOrders = rsp.GetOrders();
                sovOrders?.ForEach(sov =>
                {
                    orders.Add(TransferSovOrderToOrder(sov));
                });
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取订单列表失败，参数：{req.ToJson()}，异常消息：{ex.Message}");
                throw new LogicException($"获取订单列表失败：{ex.Message}");
            }

            CheckMaxSyncOrder(total);
            if (total > pageSize)
            {
                //循环读取订单
                var pageCount = (int)Math.Ceiling(total / (float)pageSize);
                var requests = new List<GetOrdersRequest>();
                for (var i = 1; i < pageCount; i++)
                {
                    req = BuildGetOrderParameter(startTime: startTime, endTime: endTime, status: status, page: (i + 1), pageSize: pageSize, date_type: date_type);
                    requests.Add(req);
                }
                var allOrders = new ConcurrentBag<Order>();
                Parallel.ForEach(requests, new ParallelOptions { MaxDegreeOfParallelism = 5 }, (request) =>
                {
                    try
                    {
                        SovDeliveryServiceHelper.SovDeliveryServiceClient client = new SovDeliveryServiceHelper.SovDeliveryServiceClient();
                        client.SetClientInvocationContext(_client.GetInvocationContext());
                        var rsp = client.getOrders(request);
                        var tmpSovOrders = rsp.GetOrders();
                        //total = rsp.GetTotal().ToInt();
                        tmpSovOrders?.ForEach(sov =>
                         {
                             allOrders.Add(TransferSovOrderToOrder(sov));
                         });
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"获取订单列表失败，参数：{req.ToJson()}，异常消息：{ex.Message}");
                        throw new LogicException($"获取订单列表失败：{ex.Message}");
                    }
                });
                orders.AddRange(allOrders.ToList());
            }
            if (total != orders.Count())
                Log.WriteWarning($"同步订单时，实际数量({orders.Count()})与接口总数量({total})不一致，同步参数：{parameter.ToJson()}");

            //同步订单商品信息
            var orderProducts = SyncOrderDetails(orders);

            // 同步订单退款信息                 
            var minUpdateTime = orders.Min(m => m.ModifyTime);
            if (minUpdateTime == null)
                minUpdateTime = orders.Min(m => m.CreateTime);
            minUpdateTime = minUpdateTime ?? DateTime.Now.AddDays(-1);
            var ascs = SyncRefundOrders(minUpdateTime.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            if (ascs.Any())
            {
                orders.ForEach(o =>
                {
                    var ascBriefs = ascs.Select(m => m.GetAsc_brief()).ToList();
                    var refundOrder = ascBriefs.Where(m => m.GetOrder_id() == o.PlatformOrderId).OrderByDescending(m => m.GetAsc_time()).FirstOrDefault();
                    if (refundOrder != null)
                    {
                        //售后申请单状态：0 待审核，1 已审核，2 审核不通过，3 已同意退款，4 已拒绝退款，9 已完成，10 已取消
                        var refundStatus = refundOrder.GetAsc_status();
                        o.RefundStatus = ParsePlatformRefundStatusToOrderRefundStatus(refundStatus);
                        o.OrderItems.ForEach(oi => oi.RefundStatus = o.RefundStatus);
                        BasePlatformService.TransferOrderRefundStatus(o);
                    }
                });
            }
            return orders;
        }

        /// <summary>
        /// 同步订单商品详情
        /// </summary>
        /// <param name="orders"></param>
        /// <returns></returns>
        public List<Order> SyncOrderDetails(List<Order> orders)
        {
            var platformOrderIds = orders.Select(m => m.PlatformOrderId).Distinct().ToList();
            if (platformOrderIds == null || !platformOrderIds.Any())
                return orders;

            List<OrderDetail> orderDetails = new List<OrderDetail>();
            var groupLst = new List<List<string>>();
            var pageSize = 100;
            var count = Math.Ceiling(platformOrderIds.Count * 1.0 / pageSize);
            for (var i = 0; i < count; i++)
            {
                var groupPids = platformOrderIds.Skip(i * pageSize).Take(pageSize).ToList();
                groupLst.Add(groupPids);
            }

            // 同步订单商品信息
            var orderProducts = new ConcurrentBag<OrderDetail>();
            Parallel.ForEach(groupLst, new ParallelOptions { MaxDegreeOfParallelism = 5 }, groupPids =>
            {
                try
                {
                    SovDeliveryServiceHelper.SovDeliveryServiceClient client = new SovDeliveryServiceHelper.SovDeliveryServiceClient();
                    client.SetClientInvocationContext(_client.GetInvocationContext());
                    var items = client.getOrderDetail(groupPids);
                    items?.ForEach(item => orderProducts.Add(item));
                }
                catch (Exception ex)
                {
                    Utility.Log.WriteError($"获取订单详情失败，参数：{groupPids.ToJson()}，\n异常消息：{ex.Message}");
                    throw new Exception($"获取订单详情失败");
                }
            });

            if (orderProducts.Any())
            {
                orderDetails.AddRange(orderProducts);
                UpdateOrderItem(orderDetails, orders);
            }

            return orders;
        }

        /// <summary>
        /// 更新OrderItem内容
        /// </summary>
        /// <param name="orderDetails"></param>
        /// <param name="orders"></param>
        private void UpdateOrderItem(List<OrderDetail> orderDetails, List<Order> orders)
        {
            var productIds = orderDetails.SelectMany(m => m.GetOrder_products()).Select(m => m.GetSpu_id()).Where(id => !id.IsNullOrEmpty()).Distinct().ToList();
            var pLst = (new ProductFxService()).GetFxProductList(new List<int>() { _shop.Id }, productIds);
            var notMatchPids = new List<string>();
            orders.ForEach(order =>
            {
                var orderDetail = orderDetails.Where(m => m.GetOrder_id() == order.PlatformOrderId).ToList();
                orderDetail?.ForEach(detail =>
                {
                    var products = detail.GetOrder_products();
                    products?.ForEach(p =>
                    {
                        OrderItem oi = new OrderItem();
                        oi.PlatformOrderId = order.PlatformOrderId;
                        oi.Status = order.PlatformStatus;
                        oi.BackUpPlatformStatus = oi.Status;
                        oi.ShopId = order.ShopId;
                        oi.CreateTime = order.CreateTime ?? DateTime.Now;

                        var skuId = p.GetSku_id();
                        oi.SkuID = skuId;
                        oi.ProductID = p.GetSpu_id(); //商品spu编码
                        oi.SpecId = skuId;
                        oi.SubItemID = oi.PlatformOrderId + "_" + oi.SkuID;  // 唯一码
                        oi.Count = p.GetNum().ToInt();
                        oi.Price = p.GetPrice().ToDecimal();
                        oi.ProductSubject = p.GetTitle();
                        oi.Size = p.GetSize();
                        oi.Color = p.GetColor();
                        oi.productCargoNumber = p.GetOuter_spu_id();//款号（商家新增spu时录入）
                        oi.CargoNumber = p.GetOuter_sku_id();//条码（商家新增sku时录入）

                        // 从商品库中获取Sku图片地址
                        var product = pLst.FirstOrDefault(m => m.Skus.Any(n => !n.SkuId.IsNullOrEmpty() && n.SkuId == skuId));
                        if (product != null)
                            oi.ProductImgUrl = product.Skus?.FirstOrDefault(m => m.SkuId == skuId)?.ImgUrl ?? product.ImageUrl;

                        if (oi.ProductImgUrl.IsNullOrEmpty())
                            notMatchPids.Add(oi.ProductID);

                        var buyerRemark = p.GetCustomization();
                        if (!buyerRemark.IsNullOrEmpty() && !order.BuyerRemark.Contains(buyerRemark))
                            order.BuyerRemark += buyerRemark;

                        oi.ItemAmount = p.GetGoods_line_money().ToDecimal();//  商品小计（商品小计=商品单价*数量-商品优惠金额）
                        var discount = p.GetAll_subtotal().ToDecimal(); // 优惠总金额（单件商品）
                        oi.EntryDiscount = discount * oi.Count * 100;// 元->分    

                        order.OrderItems.Add(oi);
                    });
                });
            });
            // 重新同步无商品图片的商品
            if (notMatchPids.Any())
            {
                var newSyncProducts = SyncProductByIds(notMatchPids);
                if (newSyncProducts.Any())
                {
                    orders.ForEach(o =>
                    {
                        o.OrderItems.ForEach(oi =>
                        {
                            var product = newSyncProducts.FirstOrDefault(m => m.PlatformId == oi.ProductID);
                            if (product != null)
                                oi.ProductImgUrl = product.Skus?.FirstOrDefault(m => m.SkuId == oi.SkuID)?.ProductSkuAttr?.SkuImgUrl ?? product.ImageUrl;
                        });
                    });

                    var fxUserId = SiteContext.Current.CurrentFxUserId;
                    // 保存新商品到商品库
                    var _syncFxProduct = new SyncFxProductService(fxUserId);
                    var _productfxService = new ProductFxService();

                    //平台商品，转换成ProductFx的表
                    var productFxList = _productfxService.TransferModelToProductFx(newSyncProducts, fxUserId, _shop.PlatformType);

                    //保存与更新
                    _productfxService.BulkMerger(productFxList, _shop.Id, isSyncProductType: true); // [20250220] 唯品会
                }
            }
        }

        /// <summary>
        /// 同步单个订单的售后信息
        /// </summary>
        /// <param name="orderid"></param>
        public Asc SyncRefundOrder(string orderid)
        {
            //BasePlatformService.TransferOrderRefundStatus(order);
            AscServiceClient client = new AscServiceClient();
            client.SetClientInvocationContext(_client.GetInvocationContext());
            GetAscsRequest req = new GetAscsRequest();
            req.SetOrder_id(orderid);
            var rsp = client.getAscs(req);
            var ascs = rsp.GetAscs();
            var asc = ascs?.OrderByDescending(m => m.GetAsc_brief().GetAsc_time()).FirstOrDefault();
            return asc;
        }

        public List<Asc> SyncRefundOrders(string minCreateTime = "")
        {
            var allAscs = new List<Asc>();
            var pageIndex = 1;
            var pageSize = 200;
            AscServiceClient client = new AscServiceClient();
            client.SetClientInvocationContext(_client.GetInvocationContext());
            GetAscsRequest req = new GetAscsRequest();

            req.SetRefund_status(2); //发起退款的售后
            var date = DateTime.Now;
            if (DateTime.TryParse(minCreateTime, out date))
                req.SetQuery_start_time(minCreateTime);
            req.SetPage(pageIndex);
            req.SetLimit(pageSize);
            var rsp = client.getAscs(req);
            var ascs = rsp.GetAscs();
            allAscs.AddRange(ascs);
            while (rsp.GetHas_next() ?? false)
            {
                pageIndex++;
                req.SetPage(pageIndex);
                rsp = client.getAscs(req);
                ascs = rsp.GetAscs();
                allAscs.AddRange(ascs);
            }
            return allAscs;
        }

        /// <summary>
        /// 同步指定订单
        /// </summary>
        /// <param name="platformOrderId">平台订单号</param>
        /// <param name="shop">所属店铺</param>
        /// <returns></returns>
        public Order SyncOrder(string platformOrderId)
        {
            var pids = new List<string> { platformOrderId };
            var orders = SyncOrders(pids);
            if (orders.Count < 1) return null;
            var order = orders.FirstOrDefault();
            var asc = SyncRefundOrder(order.PlatformOrderId);
            if (asc != null)
            {
                order.OrderItems.ForEach(oi =>
                {
                    //售后申请单状态：0 待审核，1 已审核，2 审核不通过，3 已同意退款，4 已拒绝退款，9 已完成，10 已取消
                    var refundStatus = asc.GetAsc_brief()?.GetAsc_status();
                    order.RefundStatus = ParsePlatformRefundStatusToOrderRefundStatus(refundStatus);
                    oi.RefundStatus = order.RefundStatus;
                    BasePlatformService.TransferOrderRefundStatus(order);
                });
            }
            
            return order;
        }

        /// <summary>
        /// 同步指定订单
        /// </summary>
        /// <param name="platformOrderId">平台订单号</param>
        /// <param name="shop">所属店铺</param>
        /// <returns></returns>
        public List<Order> SyncOrders(List<string> platformOrderIds)
        {
            var orders = new List<Order>();
            platformOrderIds = platformOrderIds.Distinct().ToList();
            if (!platformOrderIds.Any())
                return orders;

            var req = BuildGetOrderParameter(order_ids: platformOrderIds);
            try
            {
                SovDeliveryServiceHelper.SovDeliveryServiceClient client = new SovDeliveryServiceHelper.SovDeliveryServiceClient();
                client.SetClientInvocationContext(_client.GetInvocationContext());
                var rsp = client.getOrders(req);
                var sovOrders = rsp.GetOrders();
                sovOrders?.ForEach(sov =>
                {
                    orders.Add(TransferSovOrderToOrder(sov));
                });

                SyncOrderDetails(orders);
                return orders;
            }
            catch (Exception ex)
            {
                throw new LogicException($"获取订单列表失败，参数：{req.ToJson()}，异常消息：{ex.Message}");
            }
        }
        
        /// <summary>
        /// 发货
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<DeliverySendOrderResultModel> OnlineSend(DeliverySendOrderModel model)
        {
            /*
             * 发货单,发货单个数不能超过50
             * 承运商编码（当发货方式为‘门店自提’时，该字段无效；为‘快递’时必传）
             * 承运商名称（当发货方式为‘门店自提’时，该字段无效；为‘快递’时必传）
             * 包裹类型：1，单包裹；2，多包裹（当发货方式为‘门店自提’时，传‘单包裹’）
             * 包裹信息，规则：当发货方式为快递时必填,门店自提时该字段无效；当包裹类型为“多包裹”时，必须传多个
             * 发货方式：0，快递（默认） 1，门店自提             
            */
            var results = SovOrderSend(model);

            if (CustomerConfig.IsRepairSendHistory())//修复数据的模式，try catch 不影响
            {
                try
                {
                    results.ForEach(r =>
                    {
                        r.IsSuccess = true;
                        r.ErrorMessage = "";
                        r.ErrorCode = "";
                   });
                }
                catch (Exception e) {
                    Log.WriteError($"修复数据模式失败：{e.Message}");
                }
            }

            ////循环发货，支持合并订单发货，一次只能发货一个订单
            ////无需物流发货可以批量发
            //var orders = model.Orders; //订单
            //var ecm = model.ExpressCodeMapping;
            //var nologisticsModel = model.NoLogisticsInfoModel;

            //var results = new List<DeliverySendOrderResultModel>();

            //var normalOrder = new List<Order>(); //正常订单
            //var tcpsOrder = new List<Order>(); //同城配送订单
            //orders.ForEach(item =>
            //{
            //    if (item.BusinessType == "1") //配送方式 (1-物流配送),2-同城限时达，3-到店自提，4-门店交易)
            //    {
            //        normalOrder.Add(item);
            //    }
            //    else
            //    {
            //        tcpsOrder.Add(item);
            //    }
            //});
            //if (normalOrder.Any())
            //{

            //}
            //if (tcpsOrder.Any())
            //{

            //}

            return results.ToList();
        }

        /// <summary>
        /// Marketplace订单发货，发货单个数不能超过50
        /// </summary>
        /// <param name="model"></param>       
        /// <returns></returns>
        private List<DeliverySendOrderResultModel> SovOrderSend(DeliverySendOrderModel model)
        {
            //发货单,发货单个数不能超过50
            var batchSendCount = 50;
            var shipInfos = new List<ShipInfo>();
            var orders = model.Orders;
            var ecm = model.ExpressCodeMapping;
            var logisticSendCode = ecm?.PlatformExpressCode.ToString2();  // 快递公司编码
            var logisticName = ecm?.PlatformExpressName.ToString2(); // 快递公司名称
            var nologisticsModel = model.NoLogisticsInfoModel;

            var requests = new List<KeyValuePair<int, Dictionary<string, string>>>();
            //用户勾选的数量
            var results = new List<DeliverySendOrderResultModel>();
            foreach (var orderEntity in orders)
            {
                var orderRequest = model.OrderRequests.FirstOrDefault(o => o.Id == orderEntity.Id);
                var result = new DeliverySendOrderResultModel
                {
                    ErrorCode = "",
                    ErrorMessage = "",
                    ExtErrorMessage = "",
                    IsSuccess = false,
                    OrderEntity = orderEntity,
                    OrderRequest = orderRequest
                };
                //订单信息
                var ois = orderRequest.OrderItems;
                var successOrderItems = new List<int>();

                ShipInfo shipInfo = new ShipInfo();
                if (model.IsDontNeedLogisticInfo)
                {
                    // 门店自提（无物流发货）  
                    shipInfo.SetOrder_id(orderEntity.PlatformOrderId);
                    shipInfo.SetShipping_method(1); //发货方式：0，快递（默认） 1，门店自提
                    shipInfo.SetPackage_type(1);  // 包裹类型：1，单包裹；2，多包裹（当发货方式为‘门店自提’时，传‘单包裹’）
                    shipInfos.Add(shipInfo);
                }
                else
                {
                    if (logisticSendCode.IsNullOrEmpty() || logisticName.IsNullOrEmpty())
                        throw new Exception($"快递公司信息不能为空");

                    // 快递发货
                    shipInfo.SetOrder_id(orderEntity.PlatformOrderId);
                    //shipInfo.SetCarrier_code(logisticSendCode);
                    //shipInfo.SetCarrier_name(logisticName);
                    shipInfo.SetPackage_type(1); // 包裹类型：1，单包裹；2，多包裹（当发货方式为‘门店自提’时，传‘单包裹’）
                    shipInfo.SetShipping_method(0); // 发货方式：0，快递（默认） 1，门店自提

                    //当包裹类型为“多包裹”时，必须传多个
                    var packages = new List<ShipPackage>();
                    // 一单多包处理？？
                    var package = new ShipPackage();
                    var pkgProducts = new List<ShipPackageProduct>();
                    //var curOrderItems = orderEntity.OrderItems.Where(oi => oi.PlatformOrderId == pid || oi.OrignalOrderId == pid).Select(oi => oi.Id).Distinct(); //当前订单的订单项
                    var checkedOrderItems = orderEntity.OrderItems.Where(oi => ois.Contains(oi.Id)).ToList(); //选中的订单项
                    checkedOrderItems.ForEach(item =>
                    {

                        package.SetTransport_no(orderRequest.WaybillCode);
                        package.SetCarrier_code(logisticSendCode);

                        ShipPackageProduct shipProduct = new ShipPackageProduct();
                        shipProduct.SetSku_id(item.SkuID); //产品ID
                        shipProduct.SetAmount(item.Count); //产品数量
                        pkgProducts.Add(shipProduct);
                    });

                    package.SetPackage_product_list(pkgProducts);
                    packages.Add(package);

                    shipInfo.SetPackages(packages); //包裹信息，规则：当发货方式为快递时必填,门店自提时该字段无效；当包裹类型为“多包裹”时，必须传多个    
                    shipInfos.Add(shipInfo);
                };
            }

            var count = shipInfos.Count * 1.0 / batchSendCount;
            var reqLst = new List<ShipRequest>();
            for (int i = 0; i < count; i++)
            {
                ShipRequest req = new ShipRequest();
                var subShipInfos = shipInfos.Skip(i * batchSendCount).Take(batchSendCount).ToList();
                req.SetShips(subShipInfos);
                reqLst.Add(req);
            }

            // 批量发货                                                  
            var shipResponses = new List<ShipResponse>();
            Parallel.ForEach(reqLst, new ParallelOptions { MaxDegreeOfParallelism = 5 }, req =>
            {
                try
                {
                    SovDeliveryServiceHelper.SovDeliveryServiceClient client = new SovDeliveryServiceHelper.SovDeliveryServiceClient();
                    client.SetClientInvocationContext(_client.GetInvocationContext());
                    var rsp = client.ship(req);
                    shipResponses.Add(rsp);
                }
                catch (Exception ex)
                {
                    var orderIds = req.GetShips()?.Select(m => m?.GetOrder_id() ?? "").ToList();
                    orderIds?.ForEach(oid =>
                    {
                        var orderRequest = model.OrderRequests.FirstOrDefault(o => o.PlatformOrderId == oid);
                        var orderEntity = model.Orders.FirstOrDefault(m => m.PlatformOrderId == oid);

                        var result = new DeliverySendOrderResultModel
                        {
                            ErrorCode = "1",
                            ErrorMessage = ex.Message,
                            ExtErrorMessage = "",
                            IsSuccess = false,
                            OrderEntity = orderEntity,
                            OrderRequest = orderRequest
                        };
                        results.Add(result);
                    });
                }
            });

            // 发货结果汇总统计
            shipResponses.ForEach(rsp =>
            {
                // 发送失败的订单数据
                var shipFailResults = rsp.GetFail_data();
                shipFailResults?.ForEach(shipResult =>
                {
                    var errMessage = shipResult.GetResult_desc();
                    var ship = shipResult.GetShip();
                    var oid = ship?.GetOrder_id() ?? "";

                    var orderRequest = model.OrderRequests.FirstOrDefault(o => o.CustomerOrderId == oid);//OrderRequests 的平台订单编码在CustomerOrderId，不是PlatformOrderId，PlatformOrderId会获取不到，导致报错
                    var orderEntity = model.Orders.FirstOrDefault(m => m.PlatformOrderId == oid);
                    var result = new DeliverySendOrderResultModel
                    {
                        ErrorCode = "1",
                        ErrorMessage = errMessage,
                        ExtErrorMessage = "",
                        IsSuccess = false,
                        OrderEntity = orderEntity,
                        OrderRequest = orderRequest,
                        OriginalResult = shipResult.ToJson()
                    };
                    results.Add(result);
                });

                // 发送成功的订单数据
                var shipSuccessResults = rsp.GetSuccess_data();
                shipSuccessResults?.ForEach(shipResult =>
                {
                    var message = shipResult.GetResult_desc();
                    var ship = shipResult.GetShip();
                    var oid = ship?.GetOrder_id() ?? "";

                    var orderRequest = model.OrderRequests.FirstOrDefault(o => o.CustomerOrderId == oid);//OrderRequests 的平台订单编码在CustomerOrderId，不是PlatformOrderId，PlatformOrderId会获取不到，导致报错
                    var orderEntity = model.Orders.FirstOrDefault(m => m.PlatformOrderId == oid);
                    var checkedOrderItems = orderEntity.OrderItems.Where(oi => orderRequest.OrderItems.Contains(oi.Id)).ToList(); //选中的订单项                     
                    orderRequest.OrderItems = checkedOrderItems.Select(m => m.Id).Distinct().ToList(); // 发送成功的OrderItem的Id集合
                    var result = new DeliverySendOrderResultModel
                    {
                        ErrorCode = "",
                        ErrorMessage = "",
                        ExtErrorMessage = "",
                        IsSuccess = true,
                        OrderEntity = orderEntity,
                        OrderRequest = orderRequest
                    };
                    results.Add(result);
                });
            });

            return results;
        }

        /// <summary>
        /// 线上二次发货
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<DeliverySendOrderResultModel> OnlineResend(DeliverySendOrderModel model)
        {
            List<DeliverySendOrderResultModel> list = new List<DeliverySendOrderResultModel>();
            model.Orders.ForEach(x =>
            {
                DeliverySendOrderResultModel result = new DeliverySendOrderResultModel()
                {
                    IsSuccess = false,
                    ErrorCode = "PLATFORM_DOES_NOT_SUPPORT",
                    ExtErrorMessage = $"订单【{x.PlatformOrderId}】发货失败,平台暂时不支持二次发货"
                };
                list.Add(result);
            });
            return list;
        }

        /// <summary>
        /// 更新订单卖家注备
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="remark"></param>
        /// <param name="iconNumber"></param>
        /// <returns></returns>
        public bool UpdateOrderRemark(string orderId, string remark, string iconNumber, string iconTag = "")
        {
            return true;
        }

        public string GetBuyerMemberIdByLoginId(string strLoginId)
        {
            return strLoginId; //不支持，直接返回
        }

        public List<KeyValuePair<string, string>> GetLoginIdByMemberId(string buyerMemberId)
        {
            //没有此接口，直接返回
            if (string.IsNullOrEmpty(buyerMemberId))
                return new List<KeyValuePair<string, string>>();
            return buyerMemberId?.Split(',').Select(s => new KeyValuePair<string, string>(s, s))?.ToList();
        }

        #endregion

        //开始

        /// <summary>
        /// 同步商品列表  
        /// </summary>
        /// <param name="statusType">平台shelf_status: 	1.上架 0,下架  （兼容其他平台方法：0：全部，1：上架，2：下架）</param>
        /// <param name="start"></param>
        /// <param name="end"></param>
        /// <param name="isNotPinged"></param>
        /// <returns></returns>
        public List<Product> SyncProduct(int statusType, DateTime? start = null, DateTime? end = null,
            bool isNotPinged = false)
        {
            var isShelf = string.Empty;
            if (statusType == 1)
            {
                isShelf = "0"; //上架产品
                return SyncProductAction(isShelf);
            }
            else if (statusType == 2)
            {
                isShelf = "1"; //下架产品
                return SyncProductAction(isShelf);
            }
            else
            {
                var products = new List<Product>();
                isShelf = "0";
                products.AddRange(SyncProductAction(isShelf));
                isShelf = "1";
                products.AddRange(SyncProductAction(isShelf));
                return products;
            }
        }

        /// <summary>
        /// 同步商品列表
        /// </summary>
        /// <param name="isShelf">商品状态编码 （1：上架，0：下架）</param>
        /// <returns></returns>
        private List<Product> SyncProductAction(string isShelf)
        {
            var pLst = new List<Product>();
            var pageSize = 100; //每页数量，默认50 最大200
            var page = 1;
            ProductServiceClient client = new ProductServiceClient();
            client.SetClientInvocationContext(_client.GetInvocationContext());
            vipapis.marketplace.product.GetProductRequest req = new vipapis.marketplace.product.GetProductRequest();
            req.SetPage(page);
            req.SetLimit(pageSize);
            req.SetShelf_status(isShelf);
            //req.SetAudit_status("13");  //商品审核状态（11待提交，12待审核，13审核通过，14审核不通过）
            var result = client.getProducts(req);
            var products = result.GetProducts();
            //var has_next = result.GetHas_next() ?? false;
            var skuIdList = new List<string>();

            // 同步店铺商品基本信息
            if (products != null && products.Any())
            {
                products.ForEach(p =>
                {
                    var status = p.GetShelf_status().ToString2();
                    pLst.Add(new Product
                    {
                        PlatformId = p.GetSpu_id(),
                        CargoNumber = p.GetOuter_spu_id(),
                        Status = status == "1" ? "published" : status,
                        CategoryName = p.GetThird_category_name().ToString2(),
                        ImageUrl = p.GetImage_url().ToString2()
                    });
                    var skuIds = p.GetSku_ids();
                    if (skuIds != null && skuIds.Any())
                        skuIdList.AddRange(skuIds);
                });
            }

            while (result.GetHas_next() ?? false)
            {
                page++;
                req = new vipapis.marketplace.product.GetProductRequest();
                req.SetPage(page);
                req.SetLimit(pageSize);
                req.SetShelf_status(isShelf);
                //req.SetAudit_status("13");  //商品审核状态（11待提交，12待审核，13审核通过，14审核不通过）
                result = client.getProducts(req);

                products = result.GetProducts();

                if (products != null && products.Any())
                {
                    products.ForEach(p =>
                    {
                        var status = p.GetShelf_status().ToString2();
                        pLst.Add(new Product
                        {
                            PlatformId = p.GetSpu_id(),
                            CargoNumber = p.GetOuter_spu_id(),
                            Status = status == "1" ? "published" : status,
                            CategoryName = p.GetThird_category_name().ToString2(),
                            ImageUrl = p.GetImage_url().ToString2()
                        });
                        var skuIds = p.GetSku_ids();
                        if (skuIds != null && skuIds.Any())
                            skuIdList.AddRange(skuIds);
                    });
                }
            }

            var productList = new ConcurrentBag<Product>();
            // 同步商品详细信息
            if (pLst != null && pLst.Any())
            {
                Parallel.ForEach(pLst, new ParallelOptions { MaxDegreeOfParallelism = 5 }, p =>
                {
                    try
                    {
                        var product = SyncProduct(p.PlatformId);
                        product.CategoryName = p.CategoryName; // 三级分类名称，商品详情中只有id
                        product.Status = p.Status;
                        productList.Add(product);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"同步商品【{p.PlatformId}】详情异常：{ex.Message}");
                        throw new LogicException($"同步商品详情异常");
                    }
                });
            }

            return productList.ToList();
        }

        /// <summary>
        /// 同步指定商品
        /// </summary>
        /// <param name="platformId">商品的平台ID</param>
        /// <param name="shop">所属店铺</param>
        /// <returns></returns>
        public List<Product> SyncProductByIds(List<string> platformIds)
        {
            var products = new List<Product>();
            if (platformIds == null || !platformIds.Any())
                return products;

            platformIds = platformIds.Distinct().ToList();
            platformIds.ForEach(pid =>
            {
                var product = SyncProduct(pid);
                products.Add(product);
            });

            return products;
        }

        /// <summary>
        /// 同步指定商品
        /// </summary>
        /// <param name="platformId">商品的平台ID</param>
        /// <param name="shop">所属店铺</param>
        /// <returns></returns>
        public Product SyncProduct(string platformId)
        {
            vipapis.marketplace.product.ProductServiceHelper.ProductServiceClient client = new vipapis.marketplace.product.ProductServiceHelper.ProductServiceClient();
            client.SetClientInvocationContext(_client.GetInvocationContext());
            var productDetail = client.getProductById(platformId);
            var product = TransferMqProductDetailToProduct(productDetail);

            return product;
        }

        //JSON转成对象
        private Product TransferMqProductDetailToProduct(vipapis.marketplace.product.ProductDetail p)
        {
            var product = new Product();
            product.ShopId = _shop.Id;
            product.ShopName = _shop.ShopName;
            product.PlatformType = PlatformType.VipShop.ToString();

            product.PlatformId = p.GetSpu_id().ToString2();
            product.Subject = p.GetTitle().ToString2() + " " + p.GetSub_title().ToString2();
            product.CargoNumber = p.GetOuter_spu_id().ToString2(); // 款号(商家在唯品会新增商品时自己录入的spu标识编码)(不可重复)

            var images = p.GetImages();
            product.ImageUrl = images?.FirstOrDefault()?.GetUrl();
            product.CategoryId = p.GetThird_category_id().ToString2();
            product.Weight = p.GetWeight().ToDecimal() / 1000; //g->kg
            product.SetWeight = 0;
            product.GroupID = p.GetBrand_name();
            product.Bazaar = "";
            product.ShortTitle = "";
            product.Stall = "";
            product.ShortTitle = "";
            product.CostPrice = 0;
            product.IsComm = false;

            //商品属性
            var props = p.GetProd_props();
            // Sku详情
            var skus = p.GetSkus();
            if (skus != null && skus.Any())
            {
                skus.ForEach(skuDetail =>
                {
                    var sku = new ProductSku();
                    sku.SkuId = skuDetail.GetSku_id();
                    sku.Price = 0;
                    sku.SpecId = sku.SkuId;
                    sku.CargoNumber = skuDetail.GetOuter_sku_id() ?? ""; //外部商品sku编号（商家侧编码，由商家在系统中录入或同步)

                    try
                    {
                        //通过获取sku价格
                        PriceServiceClient client = new PriceServiceClient();
                        client.SetClientInvocationContext(_client.GetInvocationContext());
                        var skuPrice = client.getSkuPrice(sku.SkuId);
                        sku.Price = skuPrice.GetSale_price().ToDecimal(); // 销售价
                        var market_price = skuPrice.GetMarket_price().ToDecimal(); //市场价
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"唯品会Sku信息：{skuDetail.ToJson()},错误信息：{ex}");
                    }

                    var size_detail_id = skuDetail.GetSize_detail_id();
                    var color_images = skuDetail.GetColor_images();

                    var psa = new ProductSkuAttribute();
                    psa.SkuImgUrl = color_images?.GetImages()?.FirstOrDefault()?.GetUrl() ?? "";
                    psa.AttributeName = color_images?.GetColor().ToString2() ?? "";
                    psa.ShortTitle = "";
                    psa.Weight = 0;
                    psa.Bazaar = "";
                    psa.Stall = "";
                    psa.CostPrice = 0;

                    var saleProps = skuDetail.GetSale_props() ?? new Dictionary<string, string>();
                    foreach (var key in saleProps.Keys)
                    {
                        var val = saleProps[key].ToString2();
                        if (val != psa.AttributeName)
                        {
                            psa.AttributeValue += " " + val;
                        }
                    }
                    sku.ProductSkuAttr = psa;
                    product.Skus.Add(sku);
                });
            }

            return product;
        }

        private List<DecryptItem> decryptOrderInfo(List<EncryptData> encrypt_datas)
        {
            List<DecryptItem> _decryptItem = new List<DecryptItem>();
            SovDeliveryServiceHelper.SovDeliveryServiceClient client = new SovDeliveryServiceHelper.SovDeliveryServiceClient();
            client.SetClientInvocationContext(_client.GetInvocationContext());
            DecryptRequest _req = new DecryptRequest();
            _req.SetEncrypt_datas(encrypt_datas);
            var rsp = client.decrypt(_req);
            rsp.ForEach(x => {
                _decryptItem.AddRange(x.GetDecrypt_items());
            });
            return _decryptItem;
        }

        //获取商品分类
        public List<ProductCategory> CategoryList()
        {
            var cs = new List<ProductCategory>();
            CategoryServiceClient client = new CategoryServiceClient();
            client.SetClientInvocationContext(_client.GetInvocationContext());
            var rsp = client.getStoreCategories();
            var categories = rsp.GetCategories();
            if (categories != null && categories.Any())
            {
                categories.ForEach(c =>
                {
                    vipapis.category.CategoryServiceHelper.CategoryServiceClient cclient = new vipapis.category.CategoryServiceHelper.CategoryServiceClient();
                    cclient.SetClientInvocationContext(_client.GetInvocationContext());
                    var cid = c.GetCategory_id().ToInt();
                    if (cid > 0)
                    {
                        var category = cclient.getCategoryById(cid);
                        //过滤停用的类目
                        if (category != null && !category.GetCategory_name().Contains("停用"))
                        {
                            cs.Add(new ProductCategory
                            {
                                id = cid.ToString(),
                                pid = category.GetMajor_parent_category_id().ToString2(),
                                name = category.GetCategory_name(),
                            });
                        }
                    }
                });
            }
            return cs;
        }

        public List<GetPriceRangesModel> DangPriceList(List<string> itemList)
        {
            return new List<GetPriceRangesModel>();
        }

        public string SaveOrderPrice(Dictionary<string, string> dc)
        {
            return "true";
        }

        public DateTime? GetExpiredTime()
        {
            return _shop.ExpireTime;
        }

        /// <summary>
        /// //1是有地址库、2是没地址库、3是子账号没权限、4是其他的异常
        /// </summary>
        /// <param name="dic"></param>
        /// <returns></returns>
        public int GetLogisticsAddress()
        {
            int res = 1;

            return res;
        }
        public bool SaveLogisticsAddress(LogisticsAddressModel model, out string errMsg)
        {
            errMsg = "";
            return true;
        }

        public void GetLogisticList2()
        {

        }

        public List<ExpressCodeMapping> GetLogisticList()
        {
            try
            {
                ExpressCodeMappingService expressCodeMappingService = new ExpressCodeMappingService();
                var expressLst = new List<ExpressCodeMapping>();

                SovDeliveryServiceHelper.SovDeliveryServiceClient client = new SovDeliveryServiceHelper.SovDeliveryServiceClient();
                client.SetClientInvocationContext(_client.GetInvocationContext());
                var carriers = client.getCarriers();
                var list = new List<Carrier2>();
                carriers?.ForEach(carrier =>
                {
                    var carrier_code = carrier.GetCarrier_code();
                    var carrier_name = carrier.GetCarrier_name();
                    var carrier_shortname = carrier.GetCarrier_shortname();

                    var ss = new Carrier2
                    {
                        carrier_code = carrier_code,
                        carrier_name = carrier_name,
                        carrier_shortname = carrier_shortname
                    };
                    list.Add(ss);
                    expressLst.Add(new ExpressCodeMapping() { PlatformExpressCode = carrier_code, PlatformExpressName = carrier_name, PlatformType = PlatformType.VipShop.ToString() });
                });
                var json = list.ToJson();
                MappingToPlatfromExpressCode(expressLst);
                var notMappingExpressLst = expressLst.Where(m => m.ExpressCompanyCode.IsNullOrEmpty()).ToList();
                var MappedExpressLst = expressLst.Where(m => !m.ExpressCompanyCode.IsNullOrEmpty()).ToList();
                //expressCodeMappingService.BulkMerger(MappedExpressLst);   //需要时才开启
                return expressLst;

            }
            catch (Exception ex)
            {
                throw new Exception($"获取承运商列表异常：{ex.Message}");
            }
        }

        private void MappingToPlatfromExpressCode(List<ExpressCodeMapping> expressLst)
        {
            ExpressCompanyService ecService = new ExpressCompanyService();
            var ecLst = ecService.Get();

            expressLst.ForEach(express =>
            {
                var ec = ecLst.FirstOrDefault(m => m.CompanyName == express.PlatformExpressName || m.Names == express.PlatformExpressName
                || m.CompanyName.TrimEnd(new string[] { "快递", "物流", "快运", "速运", "速递" }) == express.PlatformExpressName.TrimEnd(new string[] { "快递", "物流", "快运", "速运", "速递" })
                || m.Names.TrimEnd(new string[] { "快递", "物流", "快运", "速运", "速递" }) == express.PlatformExpressName.TrimEnd(new string[] { "快递", "物流", "快运", "速运", "速递" }));
                if (ec != null)
                {
                    express.ExpressCompanyCode = ec.CompanyCode;
                }
            });
        }

        /// <summary>
        /// 获取接口数据。
        /// </summary>
        /// <returns></returns>
        public int GetPlatformOrderNumber()
        {
            return 0;
        }

        public bool Ping()
        {
            try
            {
                /*
                 * 请求接口检测
                 * var json = _client.Execute("com.alibaba.account", "alibaba.account.basic", new Dictionary<string, string>(), 4);
                 */
                var info = SyncShopInfo() != null ?  true :  false;
                return info;
            }
            catch (Exception ex)
            {
                //只有授权过期的错误，才说明店铺是授权过期了，不然会误判店铺授权过期，弹出授权的窗口
                var errMsg = ex?.Message.ToString2() ?? "";
                if (errMsg.Contains("授权过期") == true || errMsg.Contains("授权已过期") == true)
                    throw ex;
                return true;
            }
        }

        /// <summary>
        /// 批量评价
        /// </summary>
        /// <param name="dic"></param>
        /// <returns></returns>
        public AlibabaCommentReturnModel BatchCommentOrders(Dictionary<string, string> dic)
        {
            return null;
        }

        public virtual string GetAppPayUrl()
        {
            return "http://pc.1688.com/product/detail.htm?productCode=Tz%2BIZt9qCGKsMpNFCCCY9%2BmqRnw6h1ZBD3N%2Fli2CCyg%3D&productType=GROUP&tracelog=app_map_dgj";
        }


        public UserInfo BasicMember()
        {

            return null;

        }


        public Shop GetPlatformShopInfo()
        {
            //var serviceName = "vipapis.marketplace.store.StoreInfoService";
            //var methodName = "getStoreInfo";
            //var json = _client.Execute(serviceName, methodName);

            return null;
        }

        public string ConfirmOrder(List<string> platformOrderIds)
        {
            throw new NotImplementedException();
        }

        public void GetSkuById(string skuid)
        {
            ProductServiceClient client = new ProductServiceClient();
            client.SetClientInvocationContext(_client.GetInvocationContext());
            var sku = client.getSkuById(skuid);
            var color = sku.GetColor_images();
            var images = color.GetImages();
        }

        /// <summary>
        /// 修改订单收件人信息
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public KeyValuePair<bool, string> UpdateReceiverInfo(UpdateReceiverModel model)
        {
            throw new NotImplementedException();
        }

        public MergeOrderQueryResponseModel MergeOrderQuery(List<MergeOrderQueryRequstModel> requests)
        {
            throw new NotImplementedException();
        }

        public int GetOrderCountByConditions(SyncOrderParametersModel syncOrderParameters)
        {
            return 0;
        }
    }
}
