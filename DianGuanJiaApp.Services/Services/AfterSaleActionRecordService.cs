using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Services.Services.CrossCloud;
using Dapper;
using DianGuanJiaApp.Services.Services.DataDuplication;
using DianGuanJiaApp.Services.Services.SyncDataInterface;
using DianGuanJiaApp.Utility.Web;
using DianGuanJiaApp.Data.MessageModel;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Data.FxModel;

namespace DianGuanJiaApp.Services.Services
{
    public class AfterSaleActionRecordService : BaseService<AfterSaleActionRecord>
    {
        private AfterSaleActionRecordRepository _repository = null;
        private readonly DbConfigRepository _configRepository;
        private string _connectionString = string.Empty;

        public AfterSaleActionRecordService()
        {
            _repository = new AfterSaleActionRecordRepository();
            this._baseRepository = _repository;
            //数据库配置
            _configRepository = new DbConfigRepository();
        }
        public AfterSaleActionRecordService(string connectionString) : base(connectionString)
        {
            _repository = new AfterSaleActionRecordRepository(connectionString);
            this._baseRepository = _repository;
            //数据库配置
            _configRepository = new DbConfigRepository();

            _connectionString = connectionString;
        }

        /// <summary>
        /// 获取信息为复制副本
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public List<AfterSaleActionRecord> GetListForDuplication(List<string> codes, string selectFields = null)
        {
            if (codes == null || !codes.Any())
                return new List<AfterSaleActionRecord>();

            var list = new List<AfterSaleActionRecord>();
            var batchSize = 500;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetListForDuplication(batchCodes, selectFields);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 返回结果带Items
        /// </summary>
        /// <param name="pIds"></param>
        /// <param name="fields"></param>
        /// <param name="status"></param>
        /// <param name="whereFieldName">默认：o.RefundActionRecordCode</param>
        /// <returns></returns>
        public List<AfterSaleActionRecord> GetListAndItems(List<string> codes, List<string> fields = null,
            int? status = null, string whereFieldName = "o.RefundActionRecordCode")
        {
            if (codes == null || !codes.Any())
                return new List<AfterSaleActionRecord>();

            var list = new List<AfterSaleActionRecord>();
            var batchSize = 500;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetListAndItems(batchCodes, fields, status, whereFieldName);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 保存自动申请退款记录（带复制副本、同步厂家精选库操作）
        /// </summary>
        /// <param name="models">自动申请退款记录</param>
        /// <param name="curFxUserId"></param>
        /// <param name="isNeedSDI">是否要复制副本、同步厂家精选库</param>
        /// <returns></returns>
        public void Save(List<AfterSaleActionRecord> models, int curFxUserId, bool isNeedSDI = true)
        {
            if (models == null || models.Any() == false)
                return;
            var itemRepository = new AfterSaleActionItemRecordRepository(_connectionString);
            try
            {
                foreach (var model in models)
                {
                    // 过滤SourceLogicOrderId为空的数据
                    if (string.IsNullOrEmpty(model.SourceLogicOrderId)) continue;
                    _repository.Add(model);
                    foreach (var itemModel in model.Items)
                    {
                        itemRepository.Add(itemModel);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"Database：{_repository.DbConnection?.Database}，异常：{ex}，原始：{models.ToJson(true)}", "AfterSaleActionRecord-ERROR.txt");
            }

            if (isNeedSDI == false)
                return;

            //同步到精选厂家库
            SyncDataToAlibabaCloud(models);

            //实时同步副本
            var pathFlowCodes = models.Select(a => a.SourcePathFlowCode).Distinct().ToList();
            new SyncDataInterfaceService(curFxUserId).PathFlowOpt(pathFlowCodes, (string targetConnectionString, List<string> targetPathFlowCodes) =>
            {
                if (targetPathFlowCodes != null && targetPathFlowCodes.Any())
                {
                    var targetModels = models.Where(a => targetPathFlowCodes.Contains(a.SourcePathFlowCode)).ToList();
                    new AfterSaleActionRecordService(targetConnectionString).Save(targetModels, curFxUserId, false);
                }
            });
        }

        /// <summary>
        /// 批量插入数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        public void InsertsForDuplication(List<AfterSaleActionRecord> models)
        {
            //判空处理
            if (models == null || !models.Any())
            {
                return;
            }

            var batchSize = 50;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();

                //唯一代码列表
                var codes = batchModels.Select(m => m.RefundActionRecordCode).Distinct().ToList();
                //存在的代码列表
                var idAndCodes = _repository.GetExistIdAndCodes(codes);
                //全部不存在
                if (idAndCodes == null || !idAndCodes.Any())
                {
                    try
                    {
                        baseRepository.BulkWrite(batchModels, "AfterSaleActionRecord", maxSingleNum: 1);
                    }
                    catch (Exception ex)
                    {
                        var db = baseRepository.DbConnection;
                        if (db.State == System.Data.ConnectionState.Closed)
                        {
                            db.Open();
                        }
                        using (db)
                        {
                            //单条
                            batchModels.ForEach(item =>
                            {
                                try
                                {
                                    db.Insert(item);
                                }
                                catch (Exception ex2)
                                {
                                    var errMsg = ex2.Message.ToLower();
                                    if (errMsg.Contains("pk_") || errMsg.Contains("primary key"))
                                    {
                                        //忽略
                                    }
                                    else
                                    {
                                        throw ex2;
                                    }
                                }
                            });
                        }
                    }
                    continue;
                }
                //存在
                var existsCodes = idAndCodes.Select(m => m.Code).ToList();
                var updates = batchModels.Where(m => existsCodes.Contains(m.RefundActionRecordCode)).ToList();
                if (updates.Any())
                {
                    updates.ForEach(o =>
                    {
                        var model = idAndCodes.FirstOrDefault(m => m.Code == o.RefundActionRecordCode);
                        if (model == null)
                        {
                            return;
                        }
                        o.Id = model.Id;
                    });
                    _repository.BatchUpdateByNotUpdateFields(updates, new List<string> { "RefundActionRecordCode" });
                }
                //不存在
                var inserts = batchModels.Where(m => !existsCodes.Contains(m.RefundActionRecordCode)).ToList();
                if (inserts.Any())
                {
                    try
                    {
                        baseRepository.BulkWrite(inserts, "AfterSaleActionRecord", maxSingleNum: 1);
                    }
                    catch (Exception ex)
                    {
                        var db = baseRepository.DbConnection;
                        if (db.State == System.Data.ConnectionState.Closed)
                        {
                            db.Open();
                        }
                        using (db)
                        {
                            //单条
                            inserts.ForEach(item =>
                            {
                                try
                                {
                                    db.Insert(item);
                                }
                                catch (Exception ex2)
                                {
                                    var errMsg = ex2.Message.ToLower();
                                    if (errMsg.Contains("pk_") || errMsg.Contains("primary key"))
                                    {
                                        //忽略
                                    }
                                    else
                                    {
                                        throw ex2;
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 根据原始单处理自动退款
        /// </summary>
        /// <param name="models"></param>
        public void AddRecordForOrder(List<Order> models)
        {
            if (models == null || models.Any() == false)
                return;

            var curFxUserId = models.First().UserId;

            #region 过滤：只取支持1688代销的平台的订单+子项存在退款成功
            var needModels = models.Where(a => CustomerConfig.Fx1688SupportPlatformTypes.Contains(a.PlatformType) && a.OrderItems != null && a.OrderItems.Any(x => x.RefundStatus == RefundStatusType.REFUND_SUCCESS.ToString())).Select(m => new AfterSaleActionRecord
            {
                SourceFxUserId = m.UserId,
                SourceShopId = m.ShopId,
                SourcePlatformOrderId = m.PlatformOrderId,
                SourcePlatformStatus = m.PlatformStatus,
                SourcePlatformType = m.PlatformType,
                SourceRefundStatus = m.RefundStatus,
                SourceRefundType = "",
                Items = m.OrderItems.Where(a => a.RefundStatus == RefundStatusType.REFUND_SUCCESS.ToString()).Select(a => new AfterSaleActionItemRecord
                {
                    SourceOrderItemCode = a.OrderItemCode,
                    SourceSubItemId = a.SubItemID,
                    SourceRefundQuality = a.Count.HasValue ? a.Count.Value : 0
                }).ToList(),
            }).ToList();
            #endregion

            if (needModels.Any() == false)
                return;

            //判断用户是否开启过预付
            var isOpenedPrePay = new SupplierUserRepository().SupplierOrAgentIsOpenedPrePay(curFxUserId);
            if (isOpenedPrePay == false)
                return;

            AddAutoRefundRecord(needModels, "FromOrder");
        }

        /// <summary>
        /// 根据售后单处理自动退款
        /// </summary>
        /// <param name="models"></param>
        public void AddRecordForAfterSaleOrder(List<AfterSaleOrder> models)
        {
            if (models == null || models.Any() == false)
                return;

            var curFxUserId = models.First().FxUserId;

            #region 过滤：只取支持1688代销的平台的订单+退款成功+退款类型为仅退款
            var needModels = models.Where(a => CustomerConfig.Fx1688SupportPlatformTypes.Contains(a.PlatformType) && a.AfterSaleType == 1 && a.RefundStatus == 3 && a.AfterSaleOrderItems != null).Select(m => new AfterSaleActionRecord
            {
                SourceFxUserId = m.FxUserId,
                SourceShopId = m.ShopId,
                SourcePlatformOrderId = m.PlatformOrderId,
                SourceAfterSaleId = m.AfterSaleId,
                SourceAfterSaleCode = m.AfterSaleCode,
                SourcePlatformType = m.PlatformType,
                SourceRefundStatus = m.PlatAfterSaleStatus,
                SourceRefundType = m.PlatAfterSaleType,
                Items = m.AfterSaleOrderItems.Select(a => new AfterSaleActionItemRecord
                {
                    SourceOrderItemCode = a.OrderItemCode,
                    SourceSubItemId = a.SubItemId,
                    SourceRefundQuality = a.AfterSaleCount
                }).ToList(),
            }).ToList();
            #endregion

            if (needModels.Any() == false)
                return;

            //判断用户是否开启过预付
            var isOpenedPrePay = new SupplierUserRepository().SupplierOrAgentIsOpenedPrePay(curFxUserId);
            if (isOpenedPrePay == false)
                return;

            AddAutoRefundRecord(needModels, "FromAfterSaleOrder");
        }

        /// <summary>
        /// 添加自动退款记录
        /// </summary>
        /// <param name="models"></param>
        /// <param name="tag"></param>
        public void AddAutoRefundRecord(List<AfterSaleActionRecord> needModels, string tag = "")
        {
            var porService = new PurchaseOrderRelationService(_connectionString);
            var sourcePlatformOrderIds = needModels.Select(a => a.SourcePlatformOrderId).ToList();

            var methodName = "AutoRefundForOrder";
            var traceBatchId = Guid.NewGuid().ToString().ToShortMd5();
            var curDbName = _repository.DbConnection?.Database ?? "";
            var curFxUserId = SiteContext.Current?.CurrentFxUserId ?? 0;

            #region 业务日志1，处理前的数据
            var businessLogs = needModels.Select(m =>
            {
                return new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = traceBatchId,
                    SourceType = "添加自动退款记录处理前的数据",
                    PlatformType = m.SourcePlatformType,
                    CreatorId = curFxUserId,
                    FxUserId = m.SourceFxUserId,
                    ShopId = m.SourceShopId,
                    BusinessType = BusinessTypes.AddAutoRefundRecord.ToString(),
                    SubBusinessType = SubBusinessTypes.NeedModels.ToString(),
                    BusinessId = m.SourcePlatformOrderId,
                    Content = new
                    {
                        m.SourcePlatformStatus,
                        m.SourceRefundStatus,
                        Items = m.Items?.Select(s => new { s.SourceOrderItemCode, s.SourceSubItemId, s.SourceRefundQuality }),
                        curDbName,
                        tag
                    }.ToJson(true),
                };
            }).ToList();
            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs);
            #endregion

            //1.查询关联表数据
            var purchaseOrderRelations = porService.GetListAndItemByPids(sourcePlatformOrderIds, null, 1);

            if (purchaseOrderRelations == null || purchaseOrderRelations.Any() == false)
                return;

            #region 业务日志2，查到的关联表数据
            var businessLogs2 = purchaseOrderRelations.Select(m =>
            {
                return new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = traceBatchId,
                    SourceType = "查到的关联表数据",
                    CreatorId = curFxUserId,
                    FxUserId = m.CreateFxUserId,
                    ShopId = m.SourceShopId,
                    BusinessType = BusinessTypes.AddAutoRefundRecord.ToString(),
                    SubBusinessType = SubBusinessTypes.PurchaseOrderRelations.ToString(),
                    BusinessId = m.SourcePlatformOrderId,
                    Content = new
                    {
                        m.SourceLogicOrderId,
                        m.SourcePathFlowCode,
                        m.PurchaseOrderFxUserId,
                        m.PurchaseOrderShopId,
                        m.PurchasePlatformOrderId,
                        ItemRelations = m.ItemRelations?.ToJson(true),
                        curDbName,
                        tag
                    }.ToJson(true),
                };
            }).ToList();
            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs2);
            #endregion

            //2.整理需要写入的记录
            var lastSourcePlatformOrderIds = purchaseOrderRelations.Select(a => a.SourcePlatformOrderId).ToList();
            var lastModels = needModels.Where(a => lastSourcePlatformOrderIds.Contains(a.SourcePlatformOrderId)).ToList();

            if (lastModels.Any() == false)
            {
                Log.WriteLine($"{lastSourcePlatformOrderIds.ToJson()}无相关关联数据{purchaseOrderRelations.Select(a => new { a.SourcePlatformOrderId }).ToJson()}","AddAutoRefundRecordWarn.txt");
                return;
            }

            #region 3.补充数据
            var dtNow = DateTime.Now;
            lastModels.ForEach(m =>
            {
                var recordCode = Guid.NewGuid().ToString().ToShortMd5();
                m.ActionType = AutoRefundActionType.None.ToString();
                m.RefundActionRecordCode = recordCode;
                m.CreateTime = dtNow;

                var curApiOrderItemCodes = m.Items.Select(a => a.SourceOrderItemCode).ToList();

                //一个SourcePlatformOrderId可能对应多个采购单，增加子项条件 2023.12.21邱
                var exist = purchaseOrderRelations.FirstOrDefault(a => a.SourcePlatformOrderId == m.SourcePlatformOrderId && a.ItemRelations != null && a.ItemRelations.Any(b => curApiOrderItemCodes.Contains(b.SourceOrderItemCode)));
                if (exist != null)
                {
                    m.SourceFxUserId = exist.CreateFxUserId;
                    m.SourceShopId = exist.SourceShopId;
                    m.SourceLogicOrderId = exist.SourceLogicOrderId;
                    m.SourcePathFlowCode = exist.SourcePathFlowCode;
                    m.UpFxUserId = exist.PurchaseOrderFxUserId;
                    m.UpPlatformOrderId = exist.PurchasePlatformOrderId;
                    m.UpShopId = exist.PurchaseOrderShopId;
                    m.CreateFxUserId = curFxUserId;

                    var curItems = new List<AfterSaleActionItemRecord>();

                    //有子项+状态为待发货+子项无退款状态：才需要申请退款
                    if (m.Items != null && m.Items.Any() && exist.PurchaseOrderStatus == OrderStatusType.waitsellersend.ToString() && exist.ItemRelations != null && exist.ItemRelations.Any(a => m.SourceOrderItemCodes.Contains(a.SourceOrderItemCode) && string.IsNullOrEmpty(a.PurchaseOrderItemRefundStatus)))
                    {
                        m.ActionType = AutoRefundActionType.Apply.ToString();
                    }
                    var seqNumber = 0;
                    m.Items?.ForEach(item =>
                    {
                        seqNumber++;
                        item.RefundActionRecordCode = recordCode;
                        item.RefundActionItemRecordCode = (Guid.NewGuid().ToString() + recordCode + "-" + seqNumber.ToString()).ToShortMd5();
                        var existItem = exist.ItemRelations.FirstOrDefault(a => a.SourceOrderItemCode == item.SourceOrderItemCode);
                        if (existItem != null)
                        {
                            //同一PurchaseOrderSubItemId可能对应多条SourceOrderItemCode
                            var sourceOrderItemCodes = exist.ItemRelations.Where(a => a.PurchaseOrderSubItemId == existItem.PurchaseOrderSubItemId).Select(a => a.SourceOrderItemCode).Distinct().ToList();
                            //此次申请退款的总数量
                            var sumSourceRefundQuality = m.Items.Where(a => sourceOrderItemCodes.Contains(a.SourceOrderItemCode)).Sum(a=> a.SourceRefundQuality);

                            //增加条件：数量要一致
                            if (string.IsNullOrEmpty(existItem.PurchaseOrderItemRefundStatus) && existItem.PurchaseOrderItemCount == existItem.SkuMappingCount * sumSourceRefundQuality && curItems.Any(a => a.UpSubItemId == existItem.PurchaseOrderSubItemId) == false)
                            {
                                item.UpOrderItemCode = existItem.PurchaseOrderItemCode;
                                item.UpSubItemId = existItem.PurchaseOrderSubItemId;

                                curItems.Add(item);
                            }
                            else
                            {
                                //忽略退款状态非空的商品项
                            }
                        }
                        else
                        {
                            //允许部分商品项退款2023.12.25 by 邱
                            //m.ActionType = AutoRefundActionType.None.ToString();
                            m.ErrorMessage += $"找不到对应商品项{item.SourceOrderItemCode}";
                        }
                    });

                    m.Items = curItems;
                    if (curItems.Any() == false)
                    {
                        m.ActionType = AutoRefundActionType.None.ToString();
                        m.ErrorMessage += $"无可申请的商品项";
                    }
                }
            });
            #endregion


            if (lastModels != null && lastModels.Any())
            {
                //过滤SourceLogicOrderId为空的数据
                lastModels = lastModels.Where(a => string.IsNullOrEmpty(a.SourceLogicOrderId) == false).ToList();
            }

            //保存数据
            Save(lastModels, curFxUserId);

            #region 4.Post数据到【精选消息接收站点】
            var url = CustomerConfig.DgjAutoAfterSaleUrl;
            var encryptUrl = CustomerConfig.DgjAutoAfterSaleEncryptUrl;
            //拼多多-使用加密
            var isUseEncrypt = CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString();
            if (isUseEncrypt)
                url = encryptUrl;
            if (string.IsNullOrEmpty(url))
                return;

            var msgs = lastModels.Where(a => a.ActionType == AutoRefundActionType.Apply.ToString()).Select(a => new PlatformMessageModel
            {
                PT = PlatformType.Alibaba.ToString(),
                MID = a.UpFxUserId.ToString(),
                PID = a.RefundActionRecordCode,
                State = "AutoRefund"
            }).ToList();

            if (msgs.Any() == false)
                return;

            #region 业务日志3，Post到【精选消息接收站点】数据
            var businessLog3 = new BusinessLogModel
            {
                MethodName = methodName,
                BatchId = traceBatchId,
                SourceType = "Post到精选消息接收站点的消息",
                CreatorId = curFxUserId,
                BusinessType = BusinessTypes.AddAutoRefundRecord.ToString(),
                SubBusinessType = SubBusinessTypes.PostToDgjMessageSite.ToString(),
                Content = msgs.ToJson(true),
                Remark = url,
            }; ;
            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(new List<BusinessLogModel> { businessLog3 });
            #endregion

            var wu = new WebUtils();
            msgs.ForEach(msg =>
            {
                try
                {
                    var content = string.Empty;
                    if (isUseEncrypt)
                    {
                        //加密
                        var lastMsg = DES.EncryptDES(msg.ToJson(true), CustomerConfig.LoginCookieEncryptKey);
                        var requestBody = Encoding.UTF8.GetBytes(lastMsg);
                        content = wu.DoPostByRequestStream(url, requestBody);
                    }
                    else
                    {
                        var requestBody = Encoding.UTF8.GetBytes(msg.ToJson(true));
                        content = wu.DoPostByRequestStream(url, requestBody);
                    }

                    Log.WriteLine($"自动申请退款{url}，isUseEncrypt={isUseEncrypt}，model={msg.ToJson(true)}，返回content={content}", $"1688-autorefund-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                }
                catch (Exception ex)
                {
                    Log.WriteError($"自动申请退款发送到{url}时发生错误，消息内容：{msg.ToJson(true)}，错误信息：{ex}", $"1688-autorefund-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                }
            });
            #endregion
        }

        /// <summary>
        /// 同步业务数据到阿里云
        /// </summary>
        /// <param name="models">带Items</param>
        /// <param name="otherCloudPlatformTypes">除精选外：追加的其他平台</param>
        public void SyncDataToAlibabaCloud(List<AfterSaleActionRecord> models, List<string> otherCloudPlatformTypes = null)
        {
            //判空处理
            if (models == null || !models.Any())
            {
                return;
            }

            try
            {
                //同步到阿里云
                SyncAfterSaleActionRecordToAlibabaCloud(models, otherCloudPlatformTypes);
            }
            catch (Exception e)
            {
                WriteSyncToAlibabaCloudDebugLog($"自动申请退款同步到Alibaba、{otherCloudPlatformTypes?.ToJson()}失败，失败原因：{e.Message}，堆栈信息：{e.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// 同步自动申请退款到阿里云+抖店云
        /// </summary>
        /// <param name="models">带Items</param>
        /// <param name="otherCloudPlatformTypes">除精选外：追加的其他平台</param>
        private void SyncAfterSaleActionRecordToAlibabaCloud(List<AfterSaleActionRecord> models, List<string> otherCloudPlatformTypes = null)
        {
            WriteSyncToAlibabaCloudDebugLog(
                $@"自动申请退款同步到Alibaba、{otherCloudPlatformTypes?.ToJson()}平台，同步开始，相关信息：{models?.Select(m => new
                {
                    m.SourcePlatformOrderId,
                    m.SourceFxUserId,
                    m.UpPlatformOrderId,
                    m.UpFxUserId,
                    m.CreateFxUserId
                }).Distinct().ToList().ToJson(true)}");
            //判空处理
            if (models == null || !models.Any())
            {
                return;
            }

            //基准云平台类型
            var baseCloudPlatformType = CloudPlatformType.Alibaba.ToString();
            //1688供应商用户ID
            var supplierFxUserIds = models.Select(m => m.UpFxUserId).Distinct().ToList();
            var agentFxUserIds = models.Select(m => m.SourceFxUserId).Distinct().ToList();
            //追加源商家用户ID
            supplierFxUserIds.AddRange(agentFxUserIds);

            var cloudPlatformTypes = new List<string> { baseCloudPlatformType };
            var isOnlyAlibabaPlatform = true;

            var dicSupportPlatform = new Dictionary<string, List<string>>();
            dicSupportPlatform.Add("TouTiao", new List<string> { "TouTiao" });
            dicSupportPlatform.Add("Pinduoduo", new List<string> { "Pinduoduo", "KuaiTuanTuan" });
            dicSupportPlatform.Add("Jingdong", new List<string> { "Jingdong", "JingdongPurchase" });
            //从精选云-->同步到其他云时，追加云平台
            if (otherCloudPlatformTypes != null && otherCloudPlatformTypes.Any())
            {
                cloudPlatformTypes.AddRange(otherCloudPlatformTypes);
                isOnlyAlibabaPlatform = false;
            }

            //商家+厂家所有的库
            var supplierDbConfigs =
                _configRepository.GetListByFxUserIds(supplierFxUserIds, cloudPlatformTypes);
            WriteSyncToAlibabaCloudDebugLog(
                $@"自动申请退款同步到{baseCloudPlatformType}，同步获取1688数据库配置，相关信息：{supplierDbConfigs?.Select(m => new
                {
                    m.DbConfig?.UserId,
                    m.ConnectionString
                }).Distinct().ToList().ToJson(true)}");
            //判空处理
            if (supplierDbConfigs == null || !supplierDbConfigs.Any())
            {
                return;
            }

            var currentDbConfig = SiteContext.Current.CurrentDbConfig;
            var targetDbConfigs = supplierDbConfigs
                .Where(m => !m.ConnectionString.Equals(currentDbConfig.ConnectionString)).ToList();
            var targetDbConfigsByGroup = targetDbConfigs.GroupBy(m => m.ConnectionString).ToList();

            targetDbConfigsByGroup.ForEach(group =>
            {
                var targetFxUserIds = group.Select(m => m.DbConfig.UserId).Distinct();
                //过滤数据
                var targetModels = models.Where(m => targetFxUserIds.Contains(m.UpFxUserId) || targetFxUserIds.Contains(m.SourceFxUserId)).ToList();
                var gFirst = group.First();
                var targetCloudPlatform = gFirst.DbServer.Location;

                //只取云平台对应店铺类型的数据
                if (isOnlyAlibabaPlatform == false && dicSupportPlatform.ContainsKey(targetCloudPlatform))
                    targetModels = targetModels.Where(m => dicSupportPlatform[targetCloudPlatform].Contains(m.SourcePlatformType)).ToList();

                if (targetCloudPlatform == CustomerConfig.CloudPlatformType)
                {
                    //同云同步
                    var service = new AfterSaleActionRecordService(group.Key);
                    service.InsertsForDuplication(targetModels);

                    var targetItems = targetModels.Where(m => m.Items != null).SelectMany(m => m.Items).ToList();
                    var itemService = new AfterSaleActionItemRecordService(group.Key);
                    itemService.InsertsForDuplication(targetItems);

                    //更新关联表退款状态
                    var relations = targetModels.Where(a => a.Relation != null && a.Relation.Status == 1).Select(a => a.Relation).ToList();
                    if (relations != null && relations.Any())
                    {
                        var porService = new PurchaseOrderRelationService(group.Key);
                        var codes = relations.Select(a => a.PurchaseRelationCode).ToList();
                        porService.UpdateRefundStatus(codes);
                    }
                }
                else
                {
                    //跨云同步
                    //初始化同步业务数据服务
                    var apiDbConfig = new ApiDbConfigModel(targetCloudPlatform, targetCloudPlatform,
                        gFirst.DbNameConfig.Id);
                    var syncBusinessDataService = new SyncBusinessDataService(apiDbConfig);
                   
                    //日志
                    WriteSyncToAlibabaCloudDebugLog(
                        $@"自动申请退款同步到{apiDbConfig.ToJson(true)}，跨云同步中，targetFxUserIds：{targetFxUserIds.ToJson()}");
                    //跨云同步
                    var chunks = targetModels.ChunkList(50);
                    chunks.ForEach(chunk =>
                    {
                        syncBusinessDataService.SyncData(chunk, "RefundActionRecordCode",
                            new List<string> { "RefundActionRecordCode" });
                    });

                    var targetItems = targetModels.Where(m => m.Items != null).SelectMany(m => m.Items).ToList();
                    var itemChunks = targetItems.ChunkList(50);
                    itemChunks.ForEach(chunk =>
                    {
                        syncBusinessDataService.SyncData(chunk, "RefundActionItemRecordCode",
                            new List<string> { "RefundActionItemRecordCode", "RefundActionRecordCode" });
                    });

                    //更新关联表退款状态
                    var relations = targetModels.Where(a => a.Relation != null && a.Relation.Status == 1).Select(a => a.Relation).ToList();
                    if (relations != null && relations.Any())
                    {
                        var codes = relations.Select(a => a.PurchaseRelationCode).ToList();
                        var sql = $"UPDATE dbo.PurchaseOrderRelation SET PurchaseOrderRefundStatus='AUTO_REFUND' WHERE PurchaseRelationCode IN ('{string.Join("','", codes)}')";
                        syncBusinessDataService.ExecuteSql(sql);
                    }
                }
            });
        }

        /// <summary>
        /// 写同步到阿里云验证日志
        /// </summary>
        private void WriteSyncToAlibabaCloudDebugLog(string message)
        {
            Log.WriteLine(message, $"SyncToAlibabaCloud-{DateTime.Now.ToString("yyyyMMdd")}.log");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="codes">RefundActionRecordCode</param>
        public void ExeRefundFromMessage(List<string> codes)
        {
            if (codes == null || codes.Any() == false)
                return;

            Log.WriteLine(codes.ToJson(), $"ExeRefundFromMessage-{DateTime.Now.ToString("yyyyMMdd")}.log");

            //1. 查询自动退款记录（带Items）
            var models = GetListAndItems(codes, null, 0);

            models = models?.Where(a => a.ActionType == AutoRefundActionType.Apply.ToString()).ToList();

            var curFxUserId = SiteContext.Current?.CurrentFxUserId ?? 0;
            var methodName = "ExeRefundFromMessage";
            var traceBatchId = Guid.NewGuid().ToString().ToShortMd5();
            var curDbName = _repository.DbConnection?.Database ?? "";
            #region 业务日志1，处理前的数据
            var businessLogs = models?.Select(m =>
            {
                return new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = traceBatchId,
                    SourceType = "查询自动售后记录",
                    PlatformType = m.SourcePlatformType,
                    CreatorId = curFxUserId,
                    FxUserId = m.UpFxUserId,
                    ShopId = m.UpShopId,
                    BusinessType = BusinessTypes.ExeRefundFromMessage.ToString(),
                    BusinessId = m.UpPlatformOrderId,
                    Content = new
                    {
                        m.RefundActionRecordCode,
                        m.SourceFxUserId,
                        m.SourceLogicOrderId,
                        m.SourcePlatformOrderId,
                        Items = m.Items?.Select(s => new { s.UpOrderItemCode, s.UpSubItemId }),
                        curDbName
                    }.ToJson(true),
                };
            }).ToList();
            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs);
            #endregion

            if (models == null || models.Any() == false)
                return;

            //2.查询关联表及原始单数据
            var upPids = models.Select(a => a.UpPlatformOrderId).Distinct().ToList();
            var upSids = models.Select(a => a.UpShopId).Distinct().ToList();
            var porService = new PurchaseOrderRelationService(_connectionString);
            var orderService = new OrderService(_connectionString);
            var fields = new List<string> { "o.Id", "o.ShopId", "o.PlatformOrderId", "o.TotalAmount", "o.ShippingFee", "oi.Id", "oi.PlatformOrderId", "oi.SubItemID", "oi.ShopId", "oi.ItemAmount", "oi.Count" };
            var relations = porService.GetListAndItems(upPids, null, 1, "o.PurchasePlatformOrderId");
            if (relations == null || relations.Any() == false)
                return;

            var orders = orderService.GetOrderAndItemsForSimple(upPids, upSids, fields).ToList();

            #region 业务日志2，查询关联表及原始单数据
            var businessLogs2 = relations.Select(m =>
            {
                return new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = traceBatchId,
                    SourceType = "查询关联表及原始单数据",
                    CreatorId = curFxUserId,
                    FxUserId = m.PurchaseOrderFxUserId,
                    ShopId = m.PurchaseOrderShopId,
                    BusinessType = BusinessTypes.ExeRefundFromMessage.ToString(),
                    BusinessId = m.PurchasePlatformOrderId,
                    Content = new
                    {
                        m.PurchaseRelationCode,
                        m.CreateFxUserId,
                        m.SourceLogicOrderId,
                        m.SourcePlatformOrderId,
                        m.PurchaseOrderHopeSettlementPrice,
                        m.ShippingFee,
                        Items = m.ItemRelations?.Select(s => new { s.PurchaseOrderItemCode, s.PurchaseOrderSubItemId, s.PurchaseOrderUnitPrice, s.PurchaseOrderItemCount }),
                        curDbName
                    }.ToJson(true),
                };
            }).ToList();

            //追加原始单查询结果的数据
            if (businessLogs2.Any())
                businessLogs2.First().Remark = orders?.Select(s => new { s.PlatformOrderId, s.TotalAmount, s.ShippingFee, OrderItems = s.OrderItems?.Select(a => new { a.ItemAmount, a.Count }).ToJson() }).ToJson(true);

            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs2);
            #endregion

            //3. 整理发起退款申请的模型
            //退款金额优先使用原始单数据，再使用关联表里数据
            var needRefundModels = new List<PurchaseOrderRelation>();
            relations.ForEach(relation =>
            {
                var existModel = models.FirstOrDefault(a => a.UpPlatformOrderId == relation.PurchasePlatformOrderId);
                var existOrder = orders.FirstOrDefault(a => a.PlatformOrderId == relation.PurchasePlatformOrderId);
                if (existModel != null && relation.ItemRelations != null && relation.ItemRelations.Any())
                {
                    existModel.Relation = relation;

                    var allCountItem = relation.ItemRelations.Count();
                    //其他项是否都是退款成功状态
                    var otherItemIsAllRefundSuccess = relation.ItemRelations.All(a => existModel.UpSubItemIds.Contains(a.PurchaseOrderSubItemId) == false && a.PurchaseOrderItemRefundStatus == RefundStatusType.REFUND_SUCCESS.ToString());
                    //其他项是否都是退款中/退款成功的状态
                    var otherItemIsAllRefundStatus = relation.ItemRelations
                    .Where(a => existModel.UpSubItemIds.Contains(a.PurchaseOrderSubItemId) == false)
                    .All(a =>a.PurchaseOrderItemRefundStatus == RefundStatusType.WAIT_SELLER_AGREE.ToString() || a.PurchaseOrderItemRefundStatus == RefundStatusType.REFUND_SUCCESS.ToString());

                    //只取指定需退款的商品项
                    relation.ItemRelations = relation.ItemRelations.Where(a => existModel.UpSubItemIds.Contains(a.PurchaseOrderSubItemId)).ToList();
                    var newCountItem = relation.ItemRelations.Count();

                    if (existOrder != null && existOrder.OrderItems != null && existOrder.OrderItems.Any())
                    {
                        //使用原始单的实付金额
                        relation.PurchaseOrderHopeSettlementPrice = existOrder.TotalAmount ?? 0;
                        relation.ShippingFee = existOrder.ShippingFee ?? 0;

                        relation.ItemRelations.ForEach(item =>
                        {
                            //var existOrderItem = existOrder.OrderItems.FirstOrDefault(a => a.OrderItemCode == item.PurchaseOrderItemCode);
                            //改为SubItemID条件，历史数据PurchaseOrderItemRelation.OrderItemCode可能是以买家身份生成 2023.11.06
                            var existOrderItem = existOrder.OrderItems.FirstOrDefault(a => a.SubItemID == item.PurchaseOrderSubItemId);
                            //计算此商品项的总实付金额
                            item.PurchaseOrderItemTotalAmount = item.PurchaseOrderUnitPrice * item.PurchaseOrderItemCount + item.HopePurchasePrice;
                            if (existOrderItem != null && existOrderItem.Count.HasValue && existOrderItem.Count.Value >= 1 && existOrderItem.ItemAmount.HasValue)
                            {
                                //商品项总实付金额：优先用原始单数据
                                item.PurchaseOrderItemTotalAmount = existOrderItem.ItemAmount.Value;
                            }
                        });

                        //部分退款，重新计算退款金额
                        if (allCountItem > newCountItem)
                        {
                            if(otherItemIsAllRefundStatus)
                                relation.IsPartRunfund = false;//其他项是否都是退款中/退款成功的状态 + 本次申请退款 = 整单退款
                            else
                                relation.IsPartRunfund = true;
                            var oldSumPrice = relation.PurchaseOrderHopeSettlementPrice;
                            //退款金额，相同的PurchaseOrderSubItemId只取一条
                            relation.PurchaseOrderHopeSettlementPrice = relation.ItemRelations.GroupBy(g => g.PurchaseOrderSubItemId).Sum(a => a.First().PurchaseOrderItemTotalAmount);
                            //平摊运费
                            //if (relation.ShippingFee > 0 && oldSumPrice > 0 && oldSumPrice > relation.PurchaseOrderHopeSettlementPrice)
                            //{
                            //    relation.ShippingFee = relation.ShippingFee * relation.PurchaseOrderHopeSettlementPrice / oldSumPrice;
                            //}
                            //发货前整单退款时退含运费的总额，如果是部分退款不退运费，最后一次退款退运费
                            if (relation.IsPartRunfund)
                                relation.ShippingFee = 0;

                            //上面拿的是订单项实付金额，执行退款接口里面又有扣除运费步骤，这里要把运费也加上，否则会再次扣减运费导致退款失败
                            relation.PurchaseOrderHopeSettlementPrice += (relation.ShippingFee ?? 0);
                        }
                    }
                    relation.PurchaseOrderSubItemIds = relation.ItemRelations?.Select(a => a.PurchaseOrderSubItemId).Distinct().ToList();
                    needRefundModels.Add(relation);
                }
            });

            if (needRefundModels.Any() == false)
                return;

            #region 业务日志3，申请退款数据
            var businessLogs3 = needRefundModels.Select(m =>
            {
                return new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = traceBatchId,
                    SourceType = "申请退款数据",
                    CreatorId = curFxUserId,
                    FxUserId = m.PurchaseOrderFxUserId,
                    ShopId = m.PurchaseOrderShopId,
                    BusinessType = BusinessTypes.ExeRefundFromMessage.ToString(),
                    BusinessId = m.PurchasePlatformOrderId,
                    Content = new
                    {
                        m.PurchaseRelationCode,
                        m.CreateFxUserId,
                        m.SourceLogicOrderId,
                        m.SourcePlatformOrderId,
                        m.PurchaseOrderHopeSettlementPrice,
                        m.ShippingFee,
                        m.IsPartRunfund,
                        PurchaseOrderSubItemIds = m.PurchaseOrderSubItemIds.ToJson(),
                        Items = m.ItemRelations?.Select(s => new { s.PurchaseOrderItemCode, s.PurchaseOrderSubItemId, s.PurchaseOrderUnitPrice, s.HopePurchasePrice, s.PurchaseOrderItemCount }),
                        curDbName
                    }.ToJson(true),
                };
            }).ToList();
            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs3);
            #endregion

            //执行退款接口
            var returnedModels = new AlibabaFxOrderService().DoRefund(needRefundModels, "ExeRefundFromMessage");

            #region 业务日志4，退款结果数据
            var businessLogs4 = returnedModels.Select(m =>
            {
                return new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = traceBatchId,
                    SourceType = "退款结果数据",
                    CreatorId = curFxUserId,
                    BusinessType = BusinessTypes.ExeRefundFromMessage.ToString(),
                    BusinessId = m.Key,
                    Content = new
                    {
                        m.Success,
                        m.Message,
                        curDbName
                    }.ToJson(true),
                };
            }).ToList();
            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs4);
            #endregion

            //4. 回更自动申请退款状态、回更关联表状态（跨库，跨云同步）

            var needUpdateModels = new List<AfterSaleActionRecord>();
            returnedModels.ForEach(m =>
            {
                var existModel = models.FirstOrDefault(a => a.UpPlatformOrderId == m.Key);
                if (existModel != null)
                {
                    if (m.Success)
                    {
                        existModel.Status = 1;
                        existModel.UpAfterSaleId = m.Message;
                        existModel.UpAfterSaleCode = (m.Message + existModel.UpShopId).ToShortMd5();
                        existModel.UpRefundReason = m.ExtField1;
                        existModel.UpRefundType = "ONLY_REFUND";//固定：仅退款ONLY_REFUND
                    }
                    else
                    {
                        existModel.Status = -1;
                        existModel.ErrorMessage = m.Message;
                        existModel.UpRefundReason = m.ExtField1;
                        existModel.UpRefundType = "ONLY_REFUND";//固定：仅退款ONLY_REFUND
                    }
                    needUpdateModels.Add(existModel);
                }
            });

            if (needUpdateModels.Any() == false)
                return;

            #region 业务日志5，退款结果回更数据
            var businessLogs5 = needUpdateModels.Select(m =>
            {
                return new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = traceBatchId,
                    SourceType = "退款结果回更数据",
                    PlatformType = m.SourcePlatformType,
                    CreatorId = curFxUserId,
                    FxUserId = m.UpFxUserId,
                    ShopId = m.UpShopId,
                    BusinessType = BusinessTypes.ExeRefundFromMessage.ToString(),
                    BusinessId = m.UpPlatformOrderId,
                    Content = new
                    {
                        m.Status,
                        m.UpAfterSaleId,
                        m.ErrorMessage,
                        m.RefundActionRecordCode,
                        m.SourceFxUserId,
                        m.SourceLogicOrderId,
                        m.SourcePlatformOrderId,
                        Items = m.Items?.Select(s => new { s.UpOrderItemCode, s.UpSubItemId }),
                        curDbName
                    }.ToJson(true),
                };
            }).ToList();
            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs5);
            #endregion

            //更新执行结果
            UpdateExeResult(needUpdateModels, curFxUserId);
        }

        /// <summary>
        /// 更新执行结果
        /// </summary>
        /// <param name="models"></param>
        /// <param name="curFxUserId"></param>
        /// <param name="isNeedSDI"></param>
        public void UpdateExeResult(List<AfterSaleActionRecord> models, int curFxUserId, bool isNeedSDI = true)
        {
            _repository.UpdateExeResult(models);

            if (isNeedSDI == false)
                return;

            //实时同步副本
            var pathFlowCodes = models.Select(a => a.SourcePathFlowCode).Distinct().ToList();
            new SyncDataInterfaceService(curFxUserId).PathFlowOpt(pathFlowCodes, (string targetConnectionString, List<string> targetPathFlowCodes) =>
            {
                if (targetPathFlowCodes != null && targetPathFlowCodes.Any())
                {
                    var targetModels = models.Where(a => targetPathFlowCodes.Contains(a.SourcePathFlowCode)).ToList();
                    new AfterSaleActionRecordService(targetConnectionString).Save(targetModels, curFxUserId, false);
                }
            });

            //同步到商家和厂家精选/抖店库/拼多多库
            SyncDataToAlibabaCloud(models, new List<string> { CloudPlatformType.TouTiao.ToString(), CloudPlatformType.Pinduoduo.ToString() });
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public Tuple<int, List<AfterSaleActionRecord>> GetPageList(AfterSaleActionRecordQuery query)
        {
            if (query.SourceFxUserId <= 0)
                return Tuple.Create(0, new List<AfterSaleActionRecord>());

            var result = _repository.GetPageList(query);
            var seqNumber = 0;//顺序号
            var addNumber = query.PageIndex * query.PageSize - query.PageSize;
            result.Item2?.ForEach(row =>
            {
                seqNumber++;
                row.SeqNumber = seqNumber + addNumber;
            });

            return result;
        }

    }
}