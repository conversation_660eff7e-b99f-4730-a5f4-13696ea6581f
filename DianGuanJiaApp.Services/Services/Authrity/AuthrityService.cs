using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services.Services.Authrity
{
    /// <summary>
    /// 权限类
    /// </summary>
    public class AuthrityService
    {
        public string Name { get;private set; }//权限点名称
        public string Group { get;private set; }//分组
        public string Url { get;private set; }//Url
        public AuthMessage Message { get;private set; }//消息



        public AuthrityService(string url)
        {
            Init(url, "", "", null);
        }

        public AuthrityService(string url,string name)
        {
            Init(url, name, "", null);
        }

        public AuthrityService(string url,string name,string group)
        {            
            Init(url, name, group, null);
        }

        public AuthrityService(string url,string name,string group, AuthMessage message)
        {
            Init(url,name,group,message);
        }



        private void Init(string url,string name,string group,AuthMessage message)
        {
            this.Url = url;
            this.Name = name;
            this.Group = group;
            this.Message = message;
        }




        /// <summary>
        /// 判断输入是否有权限,基类只进行最简单的字符串判断
        /// true有权限,false无权限
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public virtual bool Auth(string key)
        {
            bool result = true;
            if(key == Url)
            {                
                return false;
            }

            return result;
        }




    }
}
