using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.RabbitMQ;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility.Web;
using DianGuanJiaApp.Warehouse.Entity;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Model.BaseProduct;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using DianGuanJiaApp.Warehouse.Sdk;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Warehouse.Model.Request;
using static DianGuanJiaApp.Data.Repository.FinancialSettlementRepository;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Data.Repository.Settings;
using RabbitMQ.Client.Framing.Impl;

namespace DianGuanJiaApp.Services.BaseProduct
{
    public class MessageRecordService : BaseService<MessageRecord>
    {
        private readonly MessageRecordRepository _repository;
        private readonly string _businessConnectionString;
        private int _abnormalNum;
        private int _successNum;

        public MessageRecordService()
        {
            _repository = new MessageRecordRepository();
        }

        /// <summary>
        /// 冷库配置
        /// </summary>
        /// <param name="coldConnectionString"></param>
        public MessageRecordService(string connectionString)
        {
            _repository=new MessageRecordRepository(connectionString);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="connectionString">消息库指定连接字符串</param>
        /// <param name="businessConnectionString">业务库指定连接字符串</param>
        public MessageRecordService(string connectionString, string businessConnectionString = "") : base(connectionString)
        {
            _repository = new MessageRecordRepository(connectionString);
            _businessConnectionString = businessConnectionString;
        }

        /// <summary>
        /// 发送基础商品的业务消息
        /// </summary>
        /// <param name="models"></param>
        /// <param name="isCompensate">是否是补偿，默认false</param>
        /// <returns></returns>
        public int SendBusinessMessage(List<MessageRecord> models, bool isCompensate = false)
        {
            if (models == null || models.Any() == false)
            {
                return 0;
            }

            var curCloudPlatformType = CustomerConfig.CloudPlatformType;

            //基础商品主库的时间
            var dtNow = new ProductDbConfigRepository().GetNowTime();

            //初始默认值
            models.ForEach(model =>
            {
                if (model.TargetCloud.IsNullOrEmpty())
                    model.TargetCloud = model.GetTargetCloud;
                if (model.SourceCloud.IsNullOrEmpty())
                    model.SourceCloud = curCloudPlatformType;
                if (isCompensate == false)
                {
                    model.CreateTime = dtNow;
                    model.UpdateTime = dtNow;
                    model.NextExeTime = dtNow;
                }
            });

            var methodName = "BaseProductSendMessage";
            var batchId = Guid.NewGuid().ToString().ToShortMd5();
            var businessDbNameForLog = new ProductFxRepository().DbConnection?.Database ?? "";
            var curFxUserId = SiteContext.CurrentNoThrow?.CurrentFxUserId ?? 0;
            var logFileName = $"baseproduct-{DateTime.Now.ToString("yyyyMMdd")}.txt";

            #region 业务日志1，处理前的数据
            var businessLogs = models?.Select(m =>
            {
                return new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = batchId,
                    SourceType = "基础商品消息-发送前的数据",
                    PlatformType = m.ProductPlatformType,
                    CreatorId = curFxUserId,
                    FxUserId = m.FxUserId,
                    BusinessType = BusinessTypes.BaseProduct.ToString(),
                    SubBusinessType = SubBusinessTypes.BaseProductSendMessage.ToString(),
                    BusinessId = m.BusinessId,
                    Content = m.DataJson,
                    DbName = businessDbNameForLog,
                    Remark = $"isCompensate={isCompensate.ToString()}",
                };
            }).ToList();
            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs);
            #endregion

            var successBusinessIds = new List<string>();
            var successIds = new List<long>();
            var failModels = new ConcurrentBag<MessageRecord>();
            var wu = new WebUtils();
            //重试次数
            var retryTime = 3;


            #region 执行发送消息
            Parallel.ForEach(models, new ParallelOptions() { MaxDegreeOfParallelism = 5 }, (message) =>
            {
                if (message.TargetCloud == curCloudPlatformType)
                {
                    //直接发送消息到当前云队列
                    var result = false;
                    for (var x = 0; x < retryTime; x++)
                    {
                        result = RabbitMQService.SendMessage(message, RabbitMQService.FxBaseProductMessageDescription);
                        if (result)
                            break;
                    }
                    if (result == false)
                    {
                        //失败
                        failModels.Add(message);
                    }
                    else
                    {
                        successBusinessIds.Add(message.BusinessId);
                        successIds.Add(message.Id);
                    }
                    Log.Debug($"基础商品消息发送消息-直发：{result}，消息内容：{message.ToJson(true)}", logFileName);
                }
                else
                {
                    //使用WebApi向目标url-->Post数据
                    var url = CustomerConfig.GetMessageTargetUrl(message.TargetCloud);

                    //加密
                    var lastMsg = DES.EncryptDES(message.ToJson(true), CustomerConfig.LoginCookieEncryptKey);
                    var requestBody = Encoding.UTF8.GetBytes(lastMsg);

                    Log.Debug($"基础商品消息发送消息-转发：{url}，消息内容：{message.ToJson(true)}，加密后：{lastMsg}", logFileName);

                    string content = wu.DoPostByRequestStream(url, requestBody);
                    if (content == "1")
                    {
                        successBusinessIds.Add(message.BusinessId);
                        successIds.Add(message.Id);
                    }
                    else
                    {
                        //失败
                        failModels.Add(message);
                    }

                }

            });
            #endregion

            #region 业务日志2，Post到【各云的基础商品接收站点】数据
            var businessLog2 = new BusinessLogModel
            {
                MethodName = methodName,
                BatchId = batchId,
                SourceType = "基础商品消息-发送结果",
                CreatorId = curFxUserId,
                BusinessType = BusinessTypes.BaseProduct.ToString(),
                SubBusinessType = SubBusinessTypes.BaseProductSendMessage.ToString(),
                BusinessId = successBusinessIds.ToJson(),                       //成功的业务Id
                SysBusinessId = failModels.Select(a => a.BusinessId).ToJson(),  //失败的业务Id
                DbName = businessDbNameForLog,
                Remark = $"isCompensate={isCompensate.ToString()}",
            }; ;
            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(new List<BusinessLogModel> { businessLog2 });
            #endregion

            #region 发送失败的，保存到数据库
            if (failModels != null && failModels.Any() && isCompensate == false)
            {
                if (curCloudPlatformType == CloudPlatformType.Alibaba.ToString())
                    _repository.BulkInsert(failModels.ToList());
                else
                    Log.WriteError($"{failModels.ToJson(true)}", $"MessageRecordFail-{DateTime.Now.ToString("yyyyMMdd")}.txt");
            }
            #endregion

            #region 针对补偿，更新状态
            if (isCompensate && successIds.Any())
            {
                _repository.UpdateMessageRecordStatus(successIds, 1);
            }
            if (isCompensate && failModels.Any())
            {
                var failIds = failModels.Select(a => a.Id).ToList();
                _repository.UpdateMessageRecordNextExeTimeAndRetryTime(failIds);
            }
            #endregion

            return successBusinessIds.Count();
        }

        /// <summary>
        /// 基础商品消息处理
        /// </summary>
        /// <param name="models"></param>
        /// <param name="tag"></param>
        /// <returns>处理失败的消息</returns>
        public List<MessageRecord> ProcessFxBaseProductMessage(List<MessageRecord> models, string tag = "")
        {
            if (models == null || models.Any() == false)
            {
                return null;
            }

            var failMessages = new List<MessageRecord>();

            var methodName = "ProcessFxBaseProductMessage";
            var batchId = Guid.NewGuid().ToString().ToShortMd5();
            var curBusinessDbName = new ProductFxRepository().DbConnection?.Database ?? "";
            var curFxUserId = SiteContext.CurrentNoThrow?.CurrentFxUserId ?? 0;
            var logFileName = $"baseproduct-{DateTime.Now.ToString("yyyyMMdd")}.txt";

            #region 业务日志1，处理前的数据
            var businessLogs = models?.Select(m =>
            {
                return new BusinessLogModel
                {
                    MethodName = methodName,
                    BatchId = batchId,
                    SourceType = "基础商品消息-消费前的数据",
                    PlatformType = m.ProductPlatformType,
                    CreatorId = curFxUserId,
                    FxUserId = m.FxUserId,
                    BusinessType = BusinessTypes.BaseProduct.ToString(),
                    SubBusinessType = SubBusinessTypes.BaseProductProcessMessage.ToString(),
                    BusinessId = m.BusinessId,
                    Content = m.DataJson,
                    DbName = curBusinessDbName,
                    SysBusinessId = tag,
                    Remark = m.MsgType,
                };
            }).ToList();
            //上传日志
            BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs);
            #endregion

            try
            {
                //按消息类型分组处理
                models.GroupBy(g => g.MsgType).ToList().ForEach(g =>
                {
                    var curFailMessages = new List<MessageRecord>();
                    switch (g.Key)
                    {
                        case BaseProductMsgType.BaseOfPtSkuRelationAdd:
                            curFailMessages = SyncBaseOfPtSkuRelationToBusinessDb(g.ToList(), 0);//不需要处理冷库
                            break;
                        case BaseProductMsgType.BatchRelationUnBind:
                            curFailMessages = batchRelationUnBind(g.ToList());
                            break;
                        case BaseProductMsgType.BaseOfPtSkuRelationDel:
                            curFailMessages = SyncBaseOfPtSkuRelationToBusinessDb(g.ToList(), 1);//不需要处理冷库
                            break;
                        case BaseProductMsgType.BaseOfPtSkuRelationAddByAuto:
                            curFailMessages = SyncBaseOfPtSkuRelationToBusinessDb(g.ToList(), 0, true);//需要处理冷库
                            break;
                        case BaseProductMsgType.WareHouseProduct:
                            curFailMessages = SyncProductToWareHouseDb(g.ToList());
                            break;
                        case BaseProductMsgType.ProductSettlementPrice:
                            curFailMessages = SyncProductSettlementPriceToBusinessDb(g.ToList());
                            break;
                        case BaseProductMsgType.WareHouseRelationToBaseProductNew://需要处理冷库
                            SyncWareHouseRelationToBaseProductDbNew(g.ToList());
                            break;
                        case BaseProductMsgType.BindSupplier:
                            SyncSupplierToBusinessDb(g.ToList());
                            break;
                        case BaseProductMsgType.WareHouseRelationToBaseProduct:
                            SyncWareHouseRelationToBaseProductDb(g.ToList());
                            break;
                        //case BaseProductMsgType.BindSupplierWithModify:
                        //SyncSupplierWithModifyToBusinessDb(g.ToList());
                        //break;
                        case BaseProductMsgType.SyncPtSku:
                            SyncBaseSkuDataToBusinessDb(g.ToList());
                            break;
                        case BaseProductMsgType.WarehouseStockIn:
                            SyncWarehouseStockIn(g.ToList());
                            break;
                        case BaseProductMsgType.WarehouseStockOut:
                            SyncWarehouseStockOut(g.ToList());
                            break;
                        case BaseProductMsgType.RelationBindSyncPtSku:
                            SyncPtSkuFromRelationBind(g.ToList());
                            break;
                        case BaseProductMsgType.SyncPtRelationStatus://需要处理冷库
                            SyncPtRelationStatus(g.ToList());
                            break;
                        case BaseProductMsgType.AutoRelationBind://需要处理冷库,更新基础库，然后通过转发BaseOfPtSkuRelationAdd消息到业务库
                            AutoRelationBind(g.ToList());
                            break;
                        case BaseProductMsgType.AddBaseProductAbnormal:
                            AddBaseProductAbnormal(g.ToList());
                            break;
                        case BaseProductMsgType.WarehouseSkuBind:
                            SyncWarehouseSkuBind(g.ToList());
                            break;
                        case BaseProductMsgType.BaseProductRelationModify://需要处理冷库
                            BaseProductRelationModify(g.ToList());
                            break;
                        case BaseProductMsgType.AutoRelationBindNew://不需要直接处理冷库,更新基础库，然后通过转发BaseOfPtSkuRelationAdd消息到业务库
                            AutoRelationBindNew(g.ToList());
                            break;
                        case BaseProductMsgType.AutoRelationBindComplete://需要处理冷库
                            AutoRelationBindComplete(g.ToList());
                            break;
                        case BaseProductMsgType.SyncRelationFromEdit://需要处理冷库
                            SyncRelationFromEdit(g.ToList());
                            break;
                        case BaseProductMsgType.MemberSettlementPriceChange:
                            MemberSettlementPriceChange(g.ToList());
                            break;
                    }
                    if (curFailMessages != null && curFailMessages.Any())
                        failMessages.AddRange(curFailMessages);
                });
            }
            catch (Exception ex)
            {
                var m = models.First();
                #region 业务日志2，发生异常时
                var businessLogs2 = new List<BusinessLogModel>
                {
                    new BusinessLogModel
                    {
                        MethodName = methodName,
                        BatchId = batchId,
                        SourceType = "基础商品消息-消费时发生异常",
                        PlatformType = m.ProductPlatformType,
                        CreatorId = curFxUserId,
                        FxUserId = m.FxUserId,
                        BusinessType = BusinessTypes.BaseProduct.ToString(),
                        SubBusinessType = SubBusinessTypes.BaseProductProcessMessage.ToString(),
                        BusinessId = m.BusinessId,
                        Content = ex.ToJson(),
                        DbName = curBusinessDbName,
                        SysBusinessId = tag,
                        Remark = m.MsgType
                    }
                };
                //上传日志
                BusinessLogDataEventTrackingService.Instance.WriteLog(businessLogs2);
                #endregion
                failMessages = models;
            }
            return failMessages;
        }




        /// <summary>
        /// 变更等级更新历史结算价
        /// </summary>
        /// <param name="models"></param>
        private void MemberSettlementPriceChange(List<MessageRecord> models)
        {
            var list = new List<SettlementPriceMsgModel>();
            var logFileName = $"MemberSettlementPriceChange-{DateTime.Now.Format("yyyyMMdd")}.txt";
            models.ForEach(model =>
            {
                var msg = model.DataJson.ToObject<SettlementPriceMsgModel>();
                list.Add(msg);
            });
            var memberLevelService = new MemberLevelService();
            
            list.ForEach(model =>
            {
                try
                {
                    memberLevelService.UpdatePriceHistoryByBaseProduct(model);
                    memberLevelService.UpdatePriceHistoryBy1688(model);
                }
                catch (Exception e)
                {
                    Log.WriteError($"消费程序变更等级更新历史结算价错误：{e.ToJson()}", logFileName);
                }
            });
        }

        /// <summary>
        /// 同步关联关系到各业务库
        /// </summary>
        /// <param name="models"></param>
        private void SyncRelationFromEdit(List<MessageRecord> models)
        {
            var list = new List<BaseOfPtSkuRelation>();
            models.ForEach(model =>
            {
                var relation = model.DataJson.ToObject<BaseOfPtSkuRelation>();
                list.Add(relation);
            });
            var fxUserId = models.First().FxUserId;

            var service = new BaseOfPtSkuRelationService(false, fxUserId);
            service.UpdateOrDelete(list, true);
            try
            {
                var coldDbConfig = GetFxUserDbConfig(fxUserId);
                var enableColdGlobal = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
                if (enableColdGlobal&&coldDbConfig!=null&&coldDbConfig.EnableColdDb)
                {
                    var coldService = new BaseOfPtSkuRelationService(coldDbConfig.ColdDbConnectionString,false);
                    coldService.UpdateOrDelete(list, true);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"修改冷库失败:{ex}", $"{nameof(MessageRecordService)}-{DateTime.Now.FormatDate()}.log");
            }
        }

        /// <summary>
        /// 同步仓库Sku关联关系到基础商品库（过渡期使用，后期无用）
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        private void SyncWareHouseRelationToBaseProductDbNew(List<MessageRecord> models)
        {
            if (models.IsNullOrEmptyList())
                return;
            var list = new List<BaseOfPtSkuRelation>();
            var fxUserId = models.First().FxUserId;
            models.ForEach(model =>
            {
                var relation = model.DataJson.ToObject<BaseOfPtSkuRelation>();
                list.Add(relation);
            });

            var deleteList = list.Where(x => x.Type == 0).ToList();
            var insertList = list.Where(x => x.Type == 1).ToList();
            var updateList = list.Where(x => x.Type == 2).ToList();

            try
            {
                var service = new BaseOfPtSkuRelationService(fxUserId);
                service.BatchAdd(insertList);
                service.BulkUpdate(updateList);
                service.BulkDelete(deleteList);

                //冷库处理
                try
                {
                    var dbConfig = GetFxUserDbConfig(fxUserId);
                    var enableColdGlobal = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
                    if (enableColdGlobal&&dbConfig!=null&&dbConfig.EnableColdDb)
                    {
                        var coldService = new BaseOfPtSkuRelationService(dbConfig.ColdDbConnectionString,false);
                        coldService.BatchAdd(insertList);
                        coldService.BulkUpdate(updateList);
                        coldService.BulkDelete(deleteList);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"冷库写入失败,原因:{ex}", $"{nameof(MessageRecordService)}-{DateTime.Now.FormatDate()}.log");
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"消息队列消费批量插入关联关系表错误：{e.ToJson()}");
            }
        }

        /// <summary>
        /// 同步库存商品关联关系到各平台业务库
        /// </summary>
        /// <param name="models"></param>
        private void SyncWarehouseSkuBind(List<MessageRecord> models)
        {
            if (models == null || models.Count == 0) return;
            var _wareHouseService = new WareHouseService();

            models.ForEach(x =>
            {
                var inModel = x.DataJson.ToObject<WarehouseSkuBindRequest>();
                var rsp = _wareHouseService.BindProduct(inModel, true, true);
                if (CustomerConfig.IsDebug) Log.WriteLine($"基础商品到库存系统绑定商品：{inModel.ToJson()}，结果：{rsp.ToJson()}");
            });
        }

        /// <summary>
        /// 自动关联
        /// </summary>
        /// <param name="models"></param>
        private void AutoRelationBind(List<MessageRecord> models)
        {
            if (models == null || models.Count == 0) return;

            var taskService = new AsyncTaskRepository();
            var dbConfigRepository = new DbConfigRepository();

            // 查找任务并更新任务状态
            var taskModel = models.First().DataJson.ToObject<AsyncTask>();
            var task = taskService.GetTaskByTaskCode(taskModel.TaskCode);

            if (task == null) return;

            var fxUserId = task.FxUserId;
            var bindModel = task.CData.ToObject<BaseProductAutoMappingModel>();
            var request = bindModel.Request;
            var redisKey = CacheKeys.BaseProductAutoRelationTaskProgressKey.Replace("{FxUserId}", fxUserId.ToString());
            AutoRelationTaskViewModel taskViewModel;
            if (RedisHelper.Exists(redisKey)) taskViewModel = RedisHelper.Get<AutoRelationTaskViewModel>(redisKey);
            else
            {
                taskViewModel = new AutoRelationTaskViewModel
                {
                    TaskCode = task.TaskCode,
                    SuccessCount = 0,
                    FailCount = 0,
                    Process = 0,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };
            }

            if (task.Status == -1 || task.Status == -10)
            {
                if (CustomerConfig.IsDebug) Log.WriteLine($"自动关联任务已中断：{task?.ToJson()}", $"AutoRelationBind-{DateTime.Now.Format("yyyy-MM-dd")}.log");
                RedisHelper.Del(redisKey);
                return;
            }

            task.Status = 1;
            task.UpdateTime = DateTime.Now;
            taskService.UpdateStatus(task);

            taskViewModel.UpdateTime = DateTime.Now;
            RedisHelper.Set(redisKey, taskViewModel.ToJson(), 60);

            var cloudPlatformTypeList = new List<string> { "TouTiao", "Pinduoduo", "Jingdong", "Alibaba" };
            var dbConfigList = dbConfigRepository.GetListByFxUserIds(new List<int> { fxUserId }, cloudPlatformTypeList);
            var oldConfigList = dbConfigRepository.GetFxAllCloudPlatformBusinessDbConfigs()
                .Where(x => x.DbServer.Location == "Alibaba" || x.DbServer.Location == "Pinduoduo")
                .ToList();
            dbConfigList.AddRange(oldConfigList);

            const string searchSql = @"
SELECT distinct sku.SkuId       AS SkuId,
                sku.PlatformId  AS PlatformId,
                sku.SkuCode     AS SkuCode,
                sku.CargoNumber AS CargoNumber,
                p.ProductCode   AS ProductCode,
                p.ShopId        AS ShopId,
                p.SourceUserId  AS SourceUserId,
                p.PlatformType  AS PlatformType
FROM Product p WITH (NOLOCK)
         INNER JOIN ProductSku sku WITH (NOLOCK) ON sku.ProductCode = p.ProductCode
         INNER JOIN PathFlowReference pfr WITH (NOLOCK) ON p.ProductCode = pfr.ProductCode AND pfr.Status = 0
         INNER JOIN PathFlowNode pfn WITH (NOLOCK) ON pfn.PathFlowCode = pfr.PathFlowCode
         LEFT JOIN PathFlowReference pfr2 WITH (NOLOCK) ON pfr2.PathFlowRefCode = sku.SkuCode AND pfr2.Status = 0
         LEFT JOIN PathFlowNode pfn2 WITH (NOLOCK) ON pfn2.PathFlowCode = pfr2.PathFlowCode AND pfn2.FxUserId = @FxUserId
         INNER JOIN FunStringToTable( @Codes , ',') t ON t.item = sku.CargoNumber
WHERE pfn.FxUserId = @FxUserId
    AND (pfr.PathFlowRefCode= p.ProductCode OR pfr.PathFlowRefCode=sku.SkuCode)
    AND (pfr2.PathFlowRefCode IS NULL OR pfn2.FxUserId = @FxUserId)
";

            const string searchSqlByProductSkuCode = @"
SELECT
	s.SkuId AS SkuId,
	s.PlatformId AS PlatformId,
	s.SkuCode AS SkuCode,
    s.CargoNumber AS CargoNumber,
	p.ProductCode AS ProductCode,
	p.ShopId AS ShopId,
	p.SourceUserId AS SourceUserId,
	p.PlatformType AS PlatformType
FROM
	ProductSku s WITH (NOLOCK)
	LEFT JOIN Product p WITH (NOLOCK ) ON s.ProductCode = p.ProductCode
	INNER JOIN FunStringToTable ( @CodeList, ',' ) t ON t.item = s.SkuCode";

            // 分批处理SkuCodeList，500一批
            var codesList = bindModel.SkuCodeList;
            const int count = 500;
            var total = codesList.Count;
            var times = total / count;
            if (total % count > 0) times++;

            // 结果保存
            var resultDic = new Dictionary<string, Tuple<bool, string>>();
            var abnormalNum = 0;
            var successNum = 0;

            // 基础商品库，非业务库
            var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService();
            var baseProductSkuService = new BaseProductSkuService();
            var baseProductSkuSupplierConfigService = new BaseProductSkuSupplierConfigService();
            var baseProductAbnormalService = new BaseProductAbnormalService();

            try
            {
                for (var i = 0; i < times; i++)
                {
                    // 每批在处理前，需要判断任务是否已经中断
                    task = taskService.GetTaskByTaskCode(taskModel.TaskCode);
                    if (task.Status == -10) return;

                    var codes = codesList.Skip(i * count).Take(count).ToList();
                    var productSkuFxList = new List<ProductSkuFx>();
                    var productSkuByRelation = new List<ProductSkuFx>();
                    var abnormalList = new List<BaseProductAbnormal>();

                    // 获取实体相关配置
                    var skuEntList = baseProductSkuService.GetList(codes);
                    skuEntList = skuEntList.Where(x => x.Status == 1 && x.FxUserId == fxUserId).ToList();
                    var skuUidList = skuEntList.Select(x => x.Uid).ToList();
                    //var proUidList = skuEntList.Select(x => x.BaseProductUid).ToList();
                    //var proUidDic = skuEntList.GroupBy(x => x.BaseProductUid).ToDictionary(g => g.Key, g=>g.ToList());

                    // 根据连接串分组
                    var groupBy = dbConfigList.GroupBy(x => x.ConnectionString).ToList();
                    var existPtSkuRelationsDic = baseOfPtSkuRelationService.GetListBySkuUids(skuUidList, fxUserId);

                    // 找到自动关联的关联数据
                    var existPtSkuRelations = existPtSkuRelationsDic.Values
                        .SelectMany(x => x.Where(y => y.RelationType == 1)).ToList();
                    var codeList = existPtSkuRelations.Select(x => x.ProductSkuCode).ToList();

                    // 跨云查询
                    groupBy.ForEach(g =>
                    {
                        var dbConfig = g.First();
                        var dbName = dbConfig.DbNameConfig.DbName;
                        var platform = dbConfig.DbServer.Location;

                        var apiDbConfig = new ApiDbConfigModel { DbNameConfigId = dbConfig.DbNameConfig.Id, Location = dbConfig.DbServer.Location, PlatformType = dbConfig.DbServer.Location };
                        var dbApi = new DbAccessUtility(apiDbConfig);

                        var resultByRelation = dbApi.Query<ProductSkuFx>(searchSqlByProductSkuCode, new { CodeList = string.Join(",", codeList) }).ToList();
                        var result = dbApi.Query<ProductSkuFx>(searchSql, new { FxUserId = fxUserId, Codes = string.Join(",", codes) }).ToList();

                        if (resultByRelation.Any()) productSkuByRelation.AddRange(resultByRelation);

                        if (result.Any())
                        {
                            result.ForEach(x =>
                            {
                                x.DbName = dbName;
                                x.CloudPlatform = platform;
                            });
                            productSkuFxList.AddRange(result);
                        }
                    });

                    Log.WriteLine($"自动关联到的店铺商品数据：{productSkuFxList.ToJson()}", $"AutoRelationBind-{DateTime.Now.Format("yyyy-MM-dd")}.log");

                    // 去重
                    var seenSkuCodes = new HashSet<string>();
                    var uniqueProductSkuFxList = productSkuFxList.Where(item => seenSkuCodes.Add(item.SkuCode)).ToList();

                    var seenSkuCodesByRelation = new HashSet<string>();
                    var uniqueProductSkuByRelation = productSkuByRelation.Where(item => seenSkuCodesByRelation.Add(item.SkuCode)).ToList();

                    productSkuFxList = uniqueProductSkuFxList;
                    productSkuByRelation = uniqueProductSkuByRelation;

                    var baseProductSkuSupplierConfigs = baseProductSkuSupplierConfigService.GetListBySkus(skuEntList);
                    var baseOfPtSkuRelations = baseOfPtSkuRelationService
                        .GetListBySkuCode(productSkuFxList.Select(x => x.SkuCode).ToList(), fxUserId)
                        .Where(x => x.Status == 1).ToList();

                    // 查询到通过自动编码匹配的数据，编码发生了变更
                    existPtSkuRelations.ForEach(x =>
                    {
                        // 找到对应的实体
                        var skuEnt = skuEntList.Find(y => y.Uid == x.BaseProductSkuUid);
                        if (skuEnt == null) return;

                        // 找到对应的商品列表
                        var productSkuFx = productSkuByRelation.FirstOrDefault(y => y.SkuCode == x.ProductSkuCode);
                        if (productSkuFx == null) return;

                        // 判断商品的CargoNumber是否发生了变更
                        if (productSkuFx.CargoNumber != skuEnt.SkuCode)
                        {
                            var abnormal = new BaseProductAbnormal
                            {
                                AbnormalType = BaseProductAbnormalType.AutoMapping,
                                AbnormalReason = "该分销品在店铺后台已变更商品编码。",
                                FxUserId = fxUserId,
                                BaseProductUid = x.BaseProductUid,
                                BaseProductSkuUid = x.BaseProductSkuUid,
                                ProductPtId = x.ProductPtId,
                                ProductSkuPtId = x.ProductSkuPtId,
                                ProductCode = x.ProductCode,
                                ProductSkuCode = x.ProductSkuCode,
                                ProductShopId = x.ProductShopId,
                                ProductFxUserId = x.ProductFxUserId,
                                ProductPlatformType = x.ProductPlatformType,
                                ProductCloudPlatform = productSkuFx.CloudPlatform ?? CommUtls.GetPlatformCloudByType(x.ProductPlatformType),
                                CreateTime = DateTime.Now,
                                UpdateTime = DateTime.Now
                            };

                            abnormalList.Add(abnormal);
                            abnormalNum++;
                            taskViewModel.FailCount++;
                            taskViewModel.UpdateTime = DateTime.Now;
                            RedisHelper.Set(redisKey, taskViewModel.ToJson(), 60);
                        }
                    });

                    var isUseWareHouse = bindModel.IsAllUseWarehouse == true;
                    isUseWareHouse = true;//强制为true
                    var existWarehouseSkuCode = new List<string>();
                    if (isUseWareHouse)
                    {
                        // 需判断SkuCode是否存在库存系统
                        var warehouseService = new WareHouseService();
                        var req = new WarehouseSkuGetOneRequest
                        {
                            WareHouseSkuCode = "-",
                            SkuCodeList = codes
                        };

                        var res = warehouseService.WarehouseSkuGetOne(req);
                        if (res.IsSucc)
                        {
                            existWarehouseSkuCode = res.ExistSkuCode;
                        }
                    }

                    codes.ForEach(skuCode =>
                    {
                        #region 组装模型

                        // 找到skuCode与商家编码相等的商品
                        var productSkuFx = productSkuFxList.FindAll(x => x.CargoNumber == skuCode);
                        if (productSkuFx.Any() == false) return;

                        // 判断是否被其他商品库关联
                        var itemsToRemove = new List<ProductSkuFx>();

                        // 找到skuCode对应的基础商品Sku
                        var skuEnt = skuEntList.Find(x => x.SkuCode == skuCode);
                        // 找到skuCode对应的基础商品Sku的供应商配置
                        var supplierConfig = baseProductSkuSupplierConfigs.FindAll(x => x.SkuUid == skuEnt.Uid);
                        var modelConfigs = new List<BaseProductSkuSupplierConfigModel>();
                        supplierConfig.ForEach(conf =>
                        {
                            modelConfigs.Add(new BaseProductSkuSupplierConfigModel
                            {
                                ApplyScope = conf.ApplyScope,
                                Config = conf.Config,
                                ConfigType = conf.ConfigType,
                                SupplierFxUserId = conf.SupplierFxUserId
                            });
                        });

                        // 标记要删除的元素
                        productSkuFx.ForEach(sku =>
                        {
                            var existRelation = baseOfPtSkuRelations.FirstOrDefault(x => x.ProductSkuCode == sku.SkuCode);
                            if (existRelation == null) return;

                            // 包含关联关系的商品
                            var abnormal = new BaseProductAbnormal
                            {
                                AbnormalType = BaseProductAbnormalType.AutoMappingExist,
                                AbnormalReason = "该分销品已绑定商品库，不允许关联多个。",
                                FxUserId = fxUserId,
                                BaseProductUid = existRelation.BaseProductUid,
                                BaseProductSkuUid = existRelation.BaseProductSkuUid,
                                ProductPtId = sku.PlatformId,
                                ProductSkuPtId = sku.SkuId,
                                ProductCode = sku.ProductCode,
                                ProductSkuCode = sku.SkuCode,
                                ProductShopId = sku.ShopId,
                                ProductFxUserId = sku.SourceUserId ?? 0,
                                ProductPlatformType = sku.PlatformType,
                                ProductCloudPlatform = sku.CloudPlatform ?? CommUtls.GetPlatformCloudByType(sku.PlatformType),
                                CreateTime = DateTime.Now,
                                UpdateTime = DateTime.Now
                            };

                            abnormalList.Add(abnormal);
                            itemsToRemove.Add(sku);
                            abnormalNum++;
                            taskViewModel.FailCount++;
                            taskViewModel.UpdateTime = DateTime.Now;
                            RedisHelper.Set(redisKey, taskViewModel.ToJson(), 60);
                        });

                        // 从原始列表中删除标记的元素
                        foreach (var sku in itemsToRemove) productSkuFx.Remove(sku);

                        // 如果没有找到匹配的商品，直接返回
                        if (productSkuFx.Any() == false) return;

                        // 需判断该SkuCode是否存在库存系统中
                        var useWareHouse = false;
                        if (existWarehouseSkuCode.Any()) useWareHouse = existWarehouseSkuCode.Contains(skuCode) && isUseWareHouse;

                        bindModel.SkuCode = skuCode;
                        bindModel.BaseProductSkuUid = skuEnt.Uid;
                        bindModel.BaseProductUid = skuEnt.BaseProductUid;
                        bindModel.ShortTitle = skuEnt.ShortTitle;
                        bindModel.SettlePrice = skuEnt.SettlePrice;
                        bindModel.CostPrice = skuEnt.CostPrice;
                        bindModel.DistributePrice = skuEnt.DistributePrice;
                        bindModel.BaseProductSkuSupplierConfigs = modelConfigs;
                        var bindSkuList = new List<BindSku>();
                        productSkuFx.ForEach(proSku =>
                        {
                            var bindSku = new BindSku
                            {
                                IsUseWarehouse = useWareHouse,
                                ProductSkuCode = proSku.SkuCode,
                                ProductCode = proSku.ProductCode
                            };
                            bindSkuList.Add(bindSku);
                        });
                        bindModel.SkuList = bindSkuList;

                        #endregion
                        // 基础库修改，这里不需要冷库处理，通过发送消息，转发到消息类型：BaseOfPtSkuRelationAddByAuto处理
                        var result = baseOfPtSkuRelationService.BaseProductSkuRelationBind(bindModel, request, fxUserId, productSkuFx);
                        if (!result.Item1) Log.WriteError($"自动关联失败：{skuCode}，{result.Item2}", "AutoRelationBind.log");
                        else
                        {
                            successNum += productSkuFx.Count;
                            taskViewModel.SuccessCount += productSkuFx.Count;
                        }
                        // 更新任务进度到Redis
                        var j = codes.IndexOf(skuCode);
                        var progress = (int)((i * count + j + 1) * 100.0 / total);
                        taskViewModel.Process = progress;
                        taskViewModel.UpdateTime = DateTime.Now;
                        RedisHelper.Set(redisKey, taskViewModel.ToJson(), 60);

                        resultDic.Add(skuCode, result);
                    });

                    // 保存异常数据
                    if (abnormalList.Any())
                    {
                        baseProductAbnormalService.BatchAdd(abnormalList);
                    }
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"自动关联异常：{e.Message}", "AutoRelationBind.log");
                var taskEntErr = taskService.GetTaskByTaskCode(taskModel.TaskCode);
                taskEntErr.FailCount = abnormalNum;
                taskEntErr.SuccessCount = successNum;
                taskEntErr.Status = -1;
                taskEntErr.ExceptionDesc = e.Message;
                taskEntErr.UpdateTime = DateTime.Now;
                taskService.Update(taskEntErr);

                taskViewModel.SuccessCount = successNum;
                taskViewModel.FailCount = abnormalNum;
                taskViewModel.Process = 100;
                taskViewModel.UpdateTime = DateTime.Now;
                taskViewModel.ExceptionDesc = e.Message;

                // 更新任务进度到Redis，1分钟后删除
                RedisHelper.Set(redisKey, taskViewModel.ToJson(), 60);
            }

            var taskEnt = taskService.GetTaskByTaskCode(taskModel.TaskCode);
            if (taskEnt.Status == -1) return;

            taskEnt.FailCount = abnormalNum;
            taskEnt.SuccessCount = successNum;
            taskEnt.Status = 5;
            taskEnt.UpdateTime = DateTime.Now;
            taskService.Update(taskEnt);

            taskViewModel.SuccessCount = successNum;
            taskViewModel.FailCount = abnormalNum;
            taskViewModel.Process = 100;
            taskViewModel.UpdateTime = DateTime.Now;

            // 更新任务进度到Redis，1分钟后删除
            RedisHelper.Set(redisKey, taskViewModel.ToJson(), 60);
        }

        /// <summary>
        /// 同步更新关联关系
        /// </summary>
        /// <param name="models"></param>
        private void SyncPtRelationStatus(List<MessageRecord> models)
        {
            if (models == null || models.Count == 0) return;

            var fxUserId = models.First().FxUserId;

            var list = new List<BaseOfPtSkuRelation>();
            models.ForEach(model =>
            {
                list.Add(model.DataJson.ToObject<BaseOfPtSkuRelation>());
            });

            var service = new BaseOfPtSkuRelationService(fxUserId);
            service.BulkUpdate(list);
            try
            {
                var dbConfigModel = GetFxUserDbConfig(fxUserId);
                var enableColdGlobal = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
                if (enableColdGlobal&&dbConfigModel!=null&&dbConfigModel.EnableColdDb)
                {
                    var coldService = new BaseOfPtSkuRelationService(dbConfigModel.ColdDbConnectionString, false);
                    coldService.BulkUpdate(list);
                }
            }
            catch (Exception ex)
            {
                Log.WriteLine($"冷库状态修改错误:{ex}", $"{nameof(BaseOfPtSkuRelationService)}-{DateTime.Now.FormatDate()}");
            }
        }

        private DbConfigModel GetFxUserDbConfig(int fxUserId)
        {
            var dbConfigModel = new DbConfigService().GetFxDbConfigFxUserIds(new List<int> { fxUserId }, new List<string> { CustomerConfig.CloudPlatformType }).Where(d => d.FromFxDbConfig==1).FirstOrDefault();
            return dbConfigModel;
        }

        /// <summary>
        /// 同步修改入库数量到库存系统
        /// </summary>
        /// <param name="models"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void SyncWarehouseStockIn(List<MessageRecord> models)
        {
            if (models == null || models.Count == 0) return;

            var ownerCode = new WareHouseService().GetOwnerCode(models.First().FxUserId);
            var req = new WarehouseStockInRequest()
            {
                Items = new List<InventoryStockInItemRequest>(),
                IsFromBaseProduct = true,
                OwnerCode = ownerCode
            };
            models.ForEach(x =>
            {
                var inModel = x.DataJson.ToObject<InventoryStockInItemRequest>();
                req.Items.Add(inModel);
            });

            new WarehouseClient(CustomerConfig.WarehouseApiUrl, CustomerConfig.WarehouseAppkey,
                CustomerConfig.WarehouseAppsecret).Execute(req);
        }

        /// <summary>
        /// 同步修改出库数量到库存系统
        /// </summary>
        /// <param name="models"></param>
        private void SyncWarehouseStockOut(List<MessageRecord> models)
        {
            if (models == null || models.Count == 0) return;

            var ownerCode = new WareHouseService().GetOwnerCode(models.First().FxUserId);
            var req = new WarehouseStockOutRequest()
            {
                Items = new List<InventoryStockOutItemRequest>(),
                IsFromBaseProduct = true,
                OwnerCode = ownerCode
            };
            models.ForEach(x =>
            {
                var inModel = x.DataJson.ToObject<InventoryStockOutItemRequest>();
                req.Items.Add(inModel);
            });

            new WarehouseClient(CustomerConfig.WarehouseApiUrl, CustomerConfig.WarehouseAppkey,
                CustomerConfig.WarehouseAppsecret).Execute(req);
        }

        /// <summary>
        ///  同步规格关系到业务库 0: add : update
        /// </summary>
        /// <param name="models"></param>
        /// <param name="type"></param>
        /// <param name="isAutoRelation">是否自动关联,如果是，冷数据需要写到冷库</param>
        /// <returns></returns>
        public List<MessageRecord> SyncBaseOfPtSkuRelationToBusinessDb(List<MessageRecord> models, int type = 0, bool isAutoRelation = false)
        {
            var list = new List<BaseOfPtSkuRelation>();
            models.ForEach(model =>
            {
                var relation = model.DataJson.ToObject<BaseOfPtSkuRelation>();
                relation.Temp = model.CreateTime;
                list.Add(relation);
            });
            var enableColdGlobal = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
            if (type == 0)
            {
                #region 关联
                //基础商品主库的时间
                var dtNow = new ProductDbConfigRepository().GetNowTime();
                var service = isAutoRelation ? new BaseOfPtSkuRelationService(false) : new BaseOfPtSkuRelationService(true);
                BaseOfPtSkuRelationService coldService = null;
                // 如果isAutoRelation是自动关联，并且启用了冷库，则使用冷库服务
                if (isAutoRelation&&enableColdGlobal&&SiteContext.Current.CurrentDbConfig.EnableColdDb)
                {
                    coldService = new BaseOfPtSkuRelationService(SiteContext.Current.CurrentDbConfig.ColdDbConnectionString, false);
                }
                //数据获取
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var baseProductSkuUid = list.Select(a => a.BaseProductSkuUid).Distinct().ToList();
                var productSkuCode = list.Select(a => a.ProductSkuCode).Distinct().ToList();
                var baseOfPtSkuRelations = service.GetAllListBySkuCode(productSkuCode, fxUserId);

                // 已存在数据--(已绑定)
                var relationsExistsY =
                    list.Where(a => baseOfPtSkuRelations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.BaseProductSkuUid == a.BaseProductSkuUid && b.Status == 1)).ToList();
                // 已存在数据--(已解绑)
                var relationsExistsYB =
                    list.Where(a => baseOfPtSkuRelations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.BaseProductSkuUid == a.BaseProductSkuUid && b.Status == 0)).ToList();
                // 不存在数据：所有未关联
                var relationsExistsN =
                    list.Where(a => !baseOfPtSkuRelations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode)).ToList();
                // 不存在数据：其他未关联（已解绑）
                var relationsExistsNB =
                    list.Where(a => baseOfPtSkuRelations.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.BaseProductSkuUid != a.BaseProductSkuUid && b.Status == 0)).ToList();

                // 去除重复(已解绑)
                relationsExistsNB =
                    relationsExistsNB.Where(a => !relationsExistsYB.Any(b => b.ProductSkuCode.Trim() == a.ProductSkuCode && b.BaseProductSkuUid == a.BaseProductSkuUid)).ToList();

                relationsExistsYB.ForEach(a =>
                {
                    a.UpdateTime = dtNow;
                    a.Status = 1;
                });
                if (relationsExistsN.Count > 0)
                {
                    // 未存在绑定
                    service.BatchAdd(relationsExistsN);
                    coldService?.BatchAdd(relationsExistsN);
                }
                if (relationsExistsNB.Count > 0)
                {
                    // 未存在绑定
                    service.BatchAdd(relationsExistsNB);
                    coldService?.BatchAdd(relationsExistsNB);
                }
                if (relationsExistsYB.Count > 0)
                {
                    // 已存在绑定
                    service.BulkUpdate(relationsExistsYB);
                    coldService?.BulkUpdate(relationsExistsYB);
                }
                //已存在：校验消息数据的时间戳是否大于目标数据的时间戳，若小于等于，不做处理
                var updateList = new List<BaseOfPtSkuRelation>();
                if (relationsExistsY.Count > 0)
                {
                    foreach (var item in relationsExistsY)
                    {
                        var relation = baseOfPtSkuRelations
                            .Where(a => a.BaseProductSkuUid == item.BaseProductSkuUid && a.ProductSkuCode.Trim() == item.ProductSkuCode.Trim())
                            .FirstOrDefault();
                        if (relation != null && item.Temp > relation.UpdateTime)
                        {
                            //item.IsUseWarehouse = item.IsUseWarehouse;
                            //item.Status = item.Status;
                            item.UpdateTime = dtNow;
                            updateList.Add(item);
                        }
                    }
                }
                if (updateList.Count > 0)
                {
                    service.BulkUpdate(updateList);
                    coldService?.BulkUpdate(updateList);
                }
                #endregion
            }
            else
            {
                #region 解绑
                //基础商品主库的时间
                var dtNow = new ProductDbConfigRepository().GetNowTime();
                var service = isAutoRelation ? new BaseOfPtSkuRelationService(false) : new BaseOfPtSkuRelationService(true);
                BaseOfPtSkuRelationService coldService = null;
                if (enableColdGlobal&& SiteContext.Current.CurrentDbConfig.EnableColdDb)
                {
                    coldService = new BaseOfPtSkuRelationService(SiteContext.Current.CurrentDbConfig.ColdDbConnectionString, false);
                }
                //数据获取
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                var baseProductSkuUid = list.Select(a => a.BaseProductSkuUid).Distinct().ToList();
                var productSkuCode = list.Select(a => a.ProductSkuCode).Distinct().ToList();
                var baseOfPtSkuRelations = service.GetListBySkuModel(baseProductSkuUid, productSkuCode, fxUserId);
                if (baseOfPtSkuRelations.Count != 0)
                {
                    var updateList = new List<BaseOfPtSkuRelation>();
                    foreach (var item in list)
                    {
                        var relation = baseOfPtSkuRelations
                            .Where(a => a.BaseProductSkuUid == item.BaseProductSkuUid && item.ProductSkuCode.Trim() == item.ProductSkuCode.Trim())
                            .FirstOrDefault();
                        if (relation != null)
                        {
                            item.UpdateTime = dtNow;
                            updateList.Add(item);
                        }
                    }
                    service.BulkUpdate(updateList);
                    coldService?.BulkUpdate(updateList);
                }
                #endregion
            }
            return null;
        }


        /// <summary>
        ///  同步规格关系到业务库 0: add : update
        /// </summary>
        /// <param name="models"></param>
        /// <param name="type"></param>
        /// <param name="isAutoRelation">是否自动关联,如果是，冷数据需要写到冷库</param>
        /// <returns></returns>
        public List<MessageRecord> batchRelationUnBind(List<MessageRecord> models)
        {
            var list = new List<BatchBaseProductSkuUnbindModel>();
            models.ForEach(model =>
            {
                var unbindModel = model.DataJson.ToObject<BatchBaseProductSkuUnbindModel>();
                list.Add(unbindModel);
            });

            var service = new BaseProductSkuCommonService();
            var data = service.BatchBaseProductSkuRelationUnbind(list, false);
            return null;
        }

        /// <summary>
        /// 同步数据到库存系统
        /// </summary>
        /// <param name="models"></param>
        /// <returns>处理失败的消息</returns>
        public List<MessageRecord> SyncProductToWareHouseDb(List<MessageRecord> models)
        {
            var list = new List<WareHouseProduct>();
            models.ForEach(model =>
            {
                list.Add(model.DataJson.ToObject<WareHouseProduct>());
            });

            var result = new WareHouseService().WarehouseProductMerger(list);

            if (result.IsSucc)
                return null;
            else
                return models;
        }

        /// <summary>
        /// 同步结算价到业务库
        /// </summary>
        /// <param name="models"></param>
        /// <returns>处理失败的消息</returns>
        public List<MessageRecord> SyncProductSettlementPriceToBusinessDb(List<MessageRecord> models)
        {

            var list = new List<ProductSettlementPrice>();
            models.ForEach(model =>
            {
                list.Add(model.DataJson.ToObject<ProductSettlementPrice>());
            });


            //TODO:更新结算价的业务逻辑及日志

            return null;
        }

        /// <summary>
        /// 同步供应商（厂家）到平台商品
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public int SyncSupplierToBusinessDb(List<MessageRecord> models)
        {

            var list = new List<BaseProductBindSupplierRequestModel>();
            models.ForEach(model =>
            {
                list.Add(model.DataJson.ToObject<BaseProductBindSupplierRequestModel>());
            });

            //TODO:执行平台商品换绑逻辑 内部允许重试3次
            var productFxService = new ProductFxService(_businessConnectionString);
            list.ForEach(m =>
            {
                // 筛选商品类型（商家推送/自营商品/全部）的数据

                if (!m.BindProductType.Equals("all"))
                {
                    SiftBindSkuByBindType(m);
                }

                for (var i = 0; i < 3; i++)
                {
                    try
                    {
                        if (m.skuCodes.Any())
                            productFxService.BindSupplier(m, userId: m.FxUserId);
                        break;
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"基础商品关联平台商品绑定厂家发生异常，重试第{i + 1}次，\n绑定模型：{m.ToJson()}，\n异常消息：{ex}");
                        if (i >= 2)
                        {
                            throw new LogicException("基础商品关联平台商品绑定厂家失败");
                        }
                        else Thread.Sleep(500);
                    }
                }

            });
            return models.Count();
        }

        /// <summary>
        /// 平台商品绑定厂家 根据绑定类型筛选数据 自营+商家推送 /仅自营 /仅商家推送（商品有上游UpFxUserId）
        /// </summary>
        /// <param name="m"></param>
        private void SiftBindSkuByBindType(BaseProductBindSupplierRequestModel m)
        {
            var skuCodes = m.skuCodes.SelectMany(c => c.Value.Split(',')).ToList();
            var productCodes = m.productCodes;
            var allCodes = new List<string>(skuCodes);
            allCodes.AddRange(productCodes);
            // 查询代发商品的上下游
            var supplierAgentResults = new ProductFxRepository().GetSupplierAndAgentByAllCodes(productCodes, skuCodes, m.FxUserId, true);
            // 其他商家推送的商品/sku
            var otherRefCodes = supplierAgentResults.Where(r => r.UpFxUserId > 0).Select(r => r.ProductRefCode).ToList();
            // 自营店铺商品/sku
            var oneselfRefCodes = allCodes.Except(otherRefCodes).ToList();
            bool? isSelf = null;
            if (m.BindProductType.Equals("oneself"))
                isSelf = true;
            if (m.BindProductType.Equals("other"))
                isSelf = false;

            var skuCodeDict = m.skuCodes.ToDictionary(d => d.Key, d => d.Value.Split(',').ToList());

            var removeDict = new Dictionary<string, List<string>>();
            foreach (var pcode in skuCodeDict.Keys)
            {
                var tempSkuCodes = skuCodeDict[pcode];
                tempSkuCodes.ForEach(scode =>
                {
                    // 属于商家推送商品的关联
                    if (isSelf == true && (otherRefCodes.Contains(scode) || (otherRefCodes.Contains(pcode) && !oneselfRefCodes.Contains(scode))))
                    {
                        //保留自营，移除商家推送
                        if (!removeDict.ContainsKey(pcode))
                        {
                            removeDict.Add(pcode, new List<string>() { scode });
                        }
                        else
                        {
                            removeDict[pcode].Add(scode);
                        }
                    }
                    // 属于自营商品的关联
                    else if (isSelf == false && (oneselfRefCodes.Contains(scode) || (oneselfRefCodes.Contains(pcode) && !otherRefCodes.Contains(scode))))
                    {
                        // 保留商家推送，移除自营
                        if (!removeDict.ContainsKey(pcode))
                        {
                            removeDict.Add(pcode, new List<string>() { scode });
                        }
                        else
                        {
                            removeDict[pcode].Add(scode);
                        }
                    }

                });

            }

            foreach (var pcode in removeDict.Keys)
            {
                var removeSkuCodes = removeDict[pcode];
                if (skuCodeDict.ContainsKey(pcode))
                {
                    skuCodeDict[pcode].RemoveAll(s => removeSkuCodes.Contains(s));
                    if (skuCodeDict[pcode].Count() <= 0)
                    {
                        skuCodeDict.Remove(pcode);
                    }
                }
            }

            m.skuCodes = skuCodeDict.ToDictionary(d => d.Key, d => string.Join(",", d.Value));
            m.productCodes = skuCodeDict.Keys.ToList();
        }

        /// <summary>
        /// 同步基础商品sku的数据到平台商品 结算价、成本价、简称
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public int SyncBaseSkuDataToBusinessDb(List<MessageRecord> models)
        {

            var list = new List<SyncPtSkuModel>();
            models.ForEach(model =>
            {
                list.Add(model.DataJson.ToObject<SyncPtSkuModel>());
            });

            //TODO:同步基础商品sku的数据到平台商品
            var productInfoFxService = new ProductInfoFxService();

            list.ForEach(m =>
            {
                // 更新简称
                productInfoFxService.SaveShortTitleOrWeight(m.FxUserId, m.UpdateShortTitleModels);
                // 更新价格
                SavePtSkuPriceData(m);
            });

            return models.Count();
        }

        /// <summary>
        /// 关联同款同步平台商品数据
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public int SyncPtSkuFromRelationBind(List<MessageRecord> models)
        {
            var list = new List<RelationBindSyncPtSkuModel>();
            models.ForEach(model =>
            {
                list.Add(model.DataJson.ToObject<RelationBindSyncPtSkuModel>());
            });

            var productFxService = new ProductFxService();
            var financialSettlementService = new FinancialSettlementService();
            var financialSettlementRepository = new FinancialSettlementRepository();

            var updatePriceModels = new List<ProductSettlementPrice>();
            var settlePriceModels = new List<ProductSettlementPrice>();
            var saveShortTitleModels = new List<SaveShortTitleOrWeightModel>();
            var bindSuccess = true;
            list.ForEach(syncModel =>
            {
                var fxUserId = syncModel.FxUserId;
                var relations = syncModel.BaseOfPtSkuRelations;
                var skuCodes = relations.Select(r => r.ProductSkuCode).Distinct().ToList();
                var productCodes = relations.Select(r => r.ProductCode).ToList();

                var supplierAgentResults = productFxService.GetSupplierAndAgentByAllCodes(productCodes, skuCodes, fxUserId, true);
                var supplierAgentDict = supplierAgentResults.GroupBy(r => r.ProductRefCode).ToDictionary(r => r.Key, r => r.ToList());
                Log.Debug($"SyncPtSkuFromRelationBind：{SiteContext.CurrentNoThrow.CurrentDbConfig.DbServer.Location}平台消费关联同款同步平台商品数据方法，同步模型：{syncModel.ToJson()} \n上下游：{supplierAgentResults.ToJson()}");

                var bindConfigs = new List<BindConfigModel>();
                var baseProductSkuConfigs = syncModel.BaseProductSkuSupplierConfigs?.Where(b => b.SupplierFxUserId != null).ToList() ?? new List<BaseProductSkuSupplierConfigModel>();
                var bscFxUserIds = baseProductSkuConfigs?.Select(bsc => bsc.SupplierFxUserId.ToInt())?.Where(i => i > 0 && i != fxUserId)?.Distinct()?.ToList() ?? new List<int>();
                var addedConfigs = new HashSet<int>();
                foreach (var relation in relations)
                {
                    var agent = 0;
                    var suppliers = new List<int>();// 平台商品绑定的厂家 sku绑定的 和 商品绑定且sku非自营，sku使用商品的默认厂家
                    productFxService.GetSkuSupplierAndAgentFromResult(supplierAgentDict, ref suppliers, ref agent, relation.ProductSkuCode, relation.ProductCode);
                    // 构建简称更新模型
                    if (syncModel.IsUpdateShortTitle)
                    {
                        saveShortTitleModels.Add(new SaveShortTitleOrWeightModel()
                        {
                            ProductCode = relation.ProductCode,
                            SkuCode = relation.ProductSkuCode,
                            SkuShortTitle = syncModel.ShortTitle,
                            IsShortTitleChange = true
                        });
                    }

                    #region 构建成本价更新模型
                    if (syncModel.IsUpdateCostPrice && syncModel.CostPrice != null)
                    {
                        updatePriceModels.Add(new ProductSettlementPrice()
                        {
                            ProductCode = relation.ProductCode,
                            ProductSkuCode = relation.ProductSkuCode,
                            CreateUser = fxUserId,
                            ShopId = relation.ProductShopId,
                            FxUserId = 0,
                            SettlementType = FinancialSettlementRepository.SettlementType.CostPrice.ToInt(),
                            Price = syncModel.CostPrice.ToDecimal()
                        });
                    }

                    #endregion

                    #region 构建分销价更新模型
                    if (syncModel.IsUpdateDistributePrice && syncModel.DistributePrice != null && agent > 0)
                    {
                        updatePriceModels.Add(new ProductSettlementPrice()
                        {
                            ProductCode = relation.ProductCode,
                            ProductSkuCode = relation.ProductSkuCode,
                            CreateUser = fxUserId,
                            ShopId = relation.ProductShopId,
                            FxUserId = agent,
                            SettlementType = FinancialSettlementRepository.SettlementType.Manufacturer.ToInt(),
                            Price = syncModel.DistributePrice.ToDecimal()
                        });
                    }

                    #endregion



                    #region 构建结算价更新模型
                    if (syncModel.IsUpdateSettlePrice && syncModel.SettlePrice != null)
                    {
                        if (syncModel.IsUserDefaultSupplier && !baseProductSkuConfigs.IsNullOrEmptyList()) // 使用默认发货厂家 修改结算价
                        {
                            suppliers = bscFxUserIds;
                        }
                        suppliers?.Distinct()?.ToList()?.ForEach(suId =>
                        {
                            settlePriceModels.Add(new ProductSettlementPrice()
                            {
                                ProductCode = relation.ProductCode,
                                ProductSkuCode = relation.ProductSkuCode,
                                SettlementType = FinancialSettlementRepository.SettlementType.Merchant.ToInt(),
                                ShopId = relation.ProductShopId,
                                Price = syncModel.SettlePrice.ToDecimal(),
                                CreateUser = fxUserId,
                                FxUserId = suId
                            });
                        });
                    }
                    #endregion

                }

                #region 构建绑定厂家模型
                if (syncModel.IsUserDefaultSupplier && !baseProductSkuConfigs.IsNullOrEmptyList()) // 使用默认发货厂家 需要平台商品变更厂家后再修改结算价
                {
                    foreach (var bsc in baseProductSkuConfigs)
                    {
                        var supplierId = bsc.SupplierFxUserId.ToInt();
                        if (supplierId > 0 && supplierId != fxUserId)
                        {
                            bindConfigs.Add(new BindConfigModel()
                            {
                                SupplierId = supplierId,
                                RefType = 2,
                                Config = bsc.Config,
                                ConfigType = bsc.ConfigType
                            });
                        }
                    }
                }

                BindSupplierRequestModel bindModel = null;
                if (syncModel.IsUserDefaultSupplier && !baseProductSkuConfigs.IsNullOrEmptyList())
                {
                    bindModel = new BindSupplierRequestModel()
                    {
                        Configs = bindConfigs,
                        skuCodes = relations.GroupBy(re => re.ProductCode).ToDictionary(d => d.Key, d => string.Join(",", d.Select(dr => dr.ProductSkuCode))),
                        productCodes = productCodes,
                        isSelf = baseProductSkuConfigs.Any(bsc => bsc.SupplierFxUserId.ToInt() == fxUserId),
                        IsBindSku = true,
                        IP = syncModel.IP,
                        FxUserId = fxUserId
                    };
                }
                #endregion

                // 执行平台商品换绑逻辑 内部允许重试3次
                if (bindModel != null)
                {
                    for (var i = 0; i < 3; i++)
                    {
                        try
                        {
                            productFxService.BindSupplier(bindModel, userId: bindModel.FxUserId);
                            break;
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"基础商品关联平台商品绑定厂家发生异常，重试第{i + 1}次，\n绑定模型：{bindModel.ToJson()}，\n异常消息：{ex}");
                            bindSuccess = false;
                            Thread.Sleep(500);
                        }
                    }
                }

            });

            new ProductInfoFxService().SaveShortTitleOrWeight(list.FirstOrDefault().FxUserId, saveShortTitleModels);

            if (bindSuccess)
                updatePriceModels.AddRange(settlePriceModels);

            financialSettlementService.SetProductSettlementPrice(updatePriceModels, list.FirstOrDefault().FxUserId,
                            changeType: FinancialSettlementRepository.ChangeType.SyncBaseProduct.ToInt(),
                            needSetIdFromDb: true);

            return models.Count();
        }


        /// <summary>
        /// 保存平台商品价格字段
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        private int SavePtSkuPriceData(SyncPtSkuModel m)
        {
            var productFxService = new ProductFxService();
            var productSkuFxService = new ProductSkuFxService();
            var financialSettlementService = new FinancialSettlementService();
            var allPriceModels = m.UpdateCostPriceModels ?? new List<ProductSettlementPrice>();
            var isNeedUpdateHistory = BaseSiteContext.Current.BaseProductSetting.DistributePriceSetting ==
                                      DistributePriceChangeSetting.UpdateHistory;
            var fxUserId = m.FxUserId;
            // 初始化变量
            var finalPriceRule = FinalDistributePriceCorrectRule.RoundToWholeNumber;
            var memberLevelDict = new Dictionary<int, RuleModel>();
            if (m.UpdateDistributePriceModels.Any() || m.UpdateSettlePriceModels.Any())
            {
                var skuCodes = m.GetUpdatePriceSkuCodes();
                var productCodes = m.GetUpdatePriceProductCodes();
                var skus = productSkuFxService.GetListBySkuCode(skuCodes);
                // 获取sku上下游 sku没有自己上下游的 取商品的上下游
                var supplierAgentResult = productFxService.GetSupplierAndAgentByAllCodes(productCodes, skuCodes, m.FxUserId, true);
                var supplierAgentDict = supplierAgentResult.GroupBy(r => r.ProductRefCode).ToDictionary(d => d.Key, g => g.ToList());
                // 使用等级分销价
                if (isNeedUpdateHistory)
                {
                    // 获取所有的下游用户
                    var agentIds = supplierAgentResult.Select(r => r.UpFxUserId).Distinct().ToList();
                    // 去除自己的FxUserId
                    agentIds.Remove(fxUserId);
                    agentIds.Remove(0);
                    // 先获取最终结算价修正规则
                    finalPriceRule = MemberLevelService.FinalPriceRule(0, SiteContext.Current.CurrentShopId);
                    // 获取对应分销商用户的换算规则
                    memberLevelDict = new MemberLevelService().GetMemberLevelList(agentIds, fxUserId);
                }

                m.UpdateDistributePriceModels?.ForEach(model =>
                {
                    var suppliers = new List<int>();
                    var agent = 0;
                    productFxService.GetSkuSupplierAndAgentFromResult(supplierAgentDict, ref suppliers, ref agent, model.ProductSkuCode, model.ProductCode);
                    if (agent > 0)
                    {
                        var sku = skus.FirstOrDefault(s => s.SkuCode == model.ProductSkuCode);
                        model.SkuName = sku?.Name ?? string.Empty;
                        model.FxUserId = agent;
                        // 厂家
                        if (model.SettlementType == 2 && isNeedUpdateHistory)
                        {
                            memberLevelDict.TryGetValue(agent, out var rule);
                            if (rule != null)
                            {
                                var (newPrice, isChanged) = MemberLevelService.DistributePriceChange(model.Price, rule, finalPriceRule);
                                if (isChanged)
                                {
                                    model.Price = newPrice ?? model.Price;
                                    model.IsMemberLevelPrice = true;
                                }
                            }
                        }
       
                        allPriceModels.Add(model);
                    }
                });
                m.UpdateSettlePriceModels?.ForEach(model =>
                {
                    var suppliers = new List<int>();
                    var agent = 0;
                    productFxService.GetSkuSupplierAndAgentFromResult(supplierAgentDict, ref suppliers, ref agent, model.ProductSkuCode, model.ProductCode);
                    //Log.WriteError($"同步信息默认价格处理：{model.ToJson()}");
                    if (suppliers == null || suppliers.Count == 0)
                    {
                        var sku = skus.FirstOrDefault(s => s.SkuCode == model.ProductSkuCode);
                        // 自营状态默认结算价格处理
                        allPriceModels.Add(new ProductSettlementPrice()
                        {
                            ProductCode = model.ProductCode,
                            ProductSkuCode = model.ProductSkuCode,
                            SettlementType = (int)SettlementType.DefaultMerchant,
                            PlatformType = model.PlatformType,
                            Price = model.Price,
                            CreateUser = model.CreateUser,
                            FxUserId = 0,
                            SkuName = sku?.Name ?? string.Empty
                        });
                        Log.WriteError($"同步信息默认价格处理：添加默认价格");
                    }
                    else
                    {
                        suppliers?.ForEach(suId =>
                        {
                            var sku = skus.FirstOrDefault(s => s.SkuCode == model.ProductSkuCode);
                            allPriceModels.Add(new ProductSettlementPrice()
                            {
                                ProductCode = model.ProductCode,
                                ProductSkuCode = model.ProductSkuCode,
                                SettlementType = model.SettlementType,
                                PlatformType = model.PlatformType,
                                Price = model.Price,
                                CreateUser = model.CreateUser,
                                FxUserId = suId,
                                SkuName = sku?.Name ?? string.Empty
                            });
                        });
                    }
                });
            }

            if (!allPriceModels.IsNullOrEmptyList())
            {
                if (m.IsUnBind)
                {
                    var changeType = FinancialSettlementRepository.ChangeType.UnBindBaseSku.ToInt();
                    Log.WriteError($"同步信息默认价格处理：解绑删除默认价格");
                    return financialSettlementService.BatchDelete(allPriceModels, m.FxUserId);
                }
                else
                {
                    var memberPriceModels = allPriceModels.Where(p => p.IsMemberLevelPrice).ToList();
                    if (memberPriceModels.Any())
                    {
                        // 等级分销价变更
                        Log.WriteLine("同步信息默认价格处理：等级分销价变更");
                        financialSettlementService.SetProductSettlementPrice(memberPriceModels, m.FxUserId,
                            changeType: ChangeType.MemberChange.ToInt(),
                            needSetIdFromDb: true);
                    }
                    var normalPriceModels = allPriceModels.Where(p => !p.IsMemberLevelPrice).ToList();
                    var changeType = ChangeType.SyncBaseProduct.ToInt();
                    Log.WriteError($"同步信息默认价格处理：自营设置默认价格");
                    return financialSettlementService.SetProductSettlementPrice(normalPriceModels, m.FxUserId,
                                changeType: changeType,
                                needSetIdFromDb: true);
                }
            }
            else return 0;

        }


        /// <summary>
        /// 消息补偿推送
        /// </summary>
        /// <param name="retryTime"></param>
        public void ProcessMessageRecordCompensate(int retryTime = 3)
        {
            var query = new MessageRecordQuery
            {
                RetryTime = retryTime,
            };
            var compensateList = _repository.GetRetryList(query);
            if (compensateList.Any() == false)
            {
                Log.WriteLine("无待补偿记录");
                return;
            }
            var pageSize = 50;
            var pageTotal = (int)Math.Ceiling(compensateList.Count / (double)pageSize);
            Parallel.For(0, pageTotal, new ParallelOptions() { MaxDegreeOfParallelism = 5 }, (index) =>
            {
                var curList = compensateList.Skip(index * pageSize).Take(pageSize).ToList();
                SendBusinessMessage(curList, isCompensate: true);

            });

        }

        /// <summary>
        /// 同步仓库Sku关联关系到基础商品库
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        private void SyncWareHouseRelationToBaseProductDb(List<MessageRecord> models)
        {
            var list = new List<BaseProductRelationModifyModel>();
            var fxUserId = models.First().FxUserId;
            var dbName = models.First().DbName;
            models.ForEach(model =>
            {
                var relation = model.DataJson.ToObject<BaseProductRelationModifyModel>();
                list.Add(relation);
            });

            // 找到对应的基础商品sku
            var skuCodeList = list.Select(x => x.SkuCode).ToList();
            var proSkuCodeList = list.Select(x => x.ProductSkuCode).ToList();
            var baseProductSkuList = new BaseProductSkuRepository(fxUserId)
                .GetList(skuCodeList)
                .Where(x => x.Status == 1)
                .ToList();
            var baseOfPtSkuRelationsExist = new BaseOfPtSkuRelationRepository()
                .GetAllListBySkuCode(proSkuCodeList, fxUserId)
                .ToList();

            // 找到需要删除的关联关系
            var deleteCode = list.Where(x => x.IsDelete).Select(x => x.ProductSkuCode).ToList();
            var existSkuUidList = baseProductSkuList.Select(x => x.Uid).ToList();
            var baseOfPtSkuRelations = new BaseOfPtSkuRelationRepository()
                .GetAllListBySkuCode(deleteCode, fxUserId)
                .Where(x => existSkuUidList.Contains(x.BaseProductSkuUid))
                .ToList();

            var baseProductSkuRelationList = new List<BaseOfPtSkuRelation>();
            var msgList = new List<MessageRecord>();

            baseProductSkuList.ForEach(baseProductSku =>
            {
                var relation = list.Find(x => x.SkuCode == baseProductSku.SkuCode);
                if (relation == null)
                    return;

                if (relation.IsDelete == false)
                {
                    var existRelation = baseOfPtSkuRelationsExist.FirstOrDefault(x => x.ProductSkuCode == relation.ProductSkuCode);

                    if (existRelation == null)
                    {
                        var entRelation = new BaseOfPtSkuRelation
                        {
                            BaseProductSkuUid = baseProductSku.Uid,
                            BaseProductUid = baseProductSku.BaseProductUid,
                            ProductSkuCode = relation.ProductSkuCode,
                            ProductCode = relation.ProductCode,
                            ProductShopId = relation.ProductShopId,
                            ProductFxUserId = relation.ProductFxUserId,
                            ProductPlatformType = relation.ProductPlatformType,
                            ProductPtId = relation.ProductPtId,
                            ProductSkuPtId = relation.ProductSkuPtId,
                            CloudPlatform = relation.CloudPlatform,
                            FxUserId = fxUserId,
                            CreateTime = DateTime.Now,
                            UpdateTime = DateTime.Now,
                            IsUseWarehouse = true,
                            Type = 1
                        };
                        baseProductSkuRelationList.Add(entRelation);

                        msgList.Add(new MessageRecord
                        {
                            DataJson = entRelation.ToJson(),
                            CreateTime = DateTime.Now,
                            FxUserId = relation.FxUserId,
                            MsgType = BaseProductMsgType.BaseProductRelationModify,
                            TargetCloud = relation.CloudPlatform,
                            DbName = dbName
                        });
                    }
                    else
                    {
                        existRelation.IsUseWarehouse = true;
                        existRelation.Status = 1;
                        existRelation.UpdateTime = DateTime.Now;
                        existRelation.Type = 2;
                        baseProductSkuRelationList.Add(existRelation);

                        msgList.Add(new MessageRecord
                        {
                            DataJson = existRelation.ToJson(),
                            CreateTime = DateTime.Now,
                            FxUserId = relation.FxUserId,
                            MsgType = BaseProductMsgType.BaseProductRelationModify,
                            TargetCloud = relation.CloudPlatform,
                            DbName = dbName
                        });
                    }
                }
                else
                {
                    // 找到需要删除的关联关系
                    var deleteRelation = baseOfPtSkuRelations.FirstOrDefault(x =>
                        x.ProductCode == relation.ProductCode && x.ProductSkuCode == relation.ProductSkuCode);

                    if (deleteRelation == null) return;

                    deleteRelation.IsUseWarehouse = false;
                    deleteRelation.UpdateTime = DateTime.Now;
                    deleteRelation.Type = 0;
                    baseProductSkuRelationList.Add(deleteRelation);


                    msgList.Add(new MessageRecord
                    {
                        DataJson = deleteRelation.ToJson(),
                        CreateTime = DateTime.Now,
                        FxUserId = relation.FxUserId,
                        MsgType = BaseProductMsgType.BaseProductRelationModify,
                        TargetCloud = relation.CloudPlatform,
                        DbName = dbName
                    });
                }
            });

            try
            {
                var service = new BaseOfPtSkuRelationService(fxUserId);
                var addList = baseProductSkuRelationList.Where(x => x.Type == 1).ToList();
                var updateList = baseProductSkuRelationList.Where(x => x.Type == 2).ToList();
                var delList = baseProductSkuRelationList.Where(x => x.Type == 0).ToList();

                service.BatchAdd(addList);
                service.BulkUpdate(updateList);
                service.BulkUpdate(delList);

                if (msgList.Any()) new MessageRecordService().SendBusinessMessage(msgList);
            }
            catch (Exception e)
            {
                Log.WriteError($"消息队列消费批量插入关联关系表错误：{e.ToJson()}");
            }
        }

        /// <summary>
        /// 复制副本到各业务库
        /// </summary>
        /// <param name="models"></param>
        private void BaseProductRelationModify(List<MessageRecord> models)
        {
            var list = new List<BaseOfPtSkuRelation>();
            var fxUserId = models.First().FxUserId;
            models.ForEach(model =>
            {
                var relation = model.DataJson.ToObject<BaseOfPtSkuRelation>();
                list.Add(relation);
            });

            try
            {
                var service = new BaseOfPtSkuRelationService(false, fxUserId);
                var addList = list.Where(x => x.Type == 1).ToList();
                var updateList = list.Where(x => x.Type == 2).ToList();
                var delList = list.Where(x => x.Type == 0).ToList();

                service.BatchAdd(addList);
                service.BulkUpdate(updateList);
                service.BulkUpdate(delList);
                // 冷库处理
                try
                {
                    var coldDbConfig = GetFxUserDbConfig(fxUserId);
                    var enableColdGlobal = DuplicationColdStorageSwitchRepository.Instance.IsEnabledColdStorage();
                    if (enableColdGlobal&&coldDbConfig!=null&&coldDbConfig.EnableColdDb)
                    {
                        var coldService = new BaseOfPtSkuRelationService(coldDbConfig.ColdDbConnectionString,false);
                        coldService.BatchAdd(addList);
                        coldService.BulkUpdate(updateList);
                        coldService.BulkUpdate(delList);
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"BaseProductRelationModify写入冷库异常，原因：{ex}", $"{nameof(MessageRecordService)}-{DateTime.Now.FormatDate()}.log");
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"消息队列消费复制副本批量插入关联关系表错误：{e.ToJson()}");
            }
        }

        /// <summary>
        /// 添加基础商品异常数据
        /// </summary>
        /// <param name="models"></param>
        /// <returns></returns>
        public void AddBaseProductAbnormal(List<MessageRecord> models)
        {
            //只会在精选云
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                return;
            var list = new List<BaseProductAbnormal>();
            models.ForEach(model =>
            {
                var curModel = model.DataJson.ToObject<BaseProductAbnormal>();
                list.Add(curModel);
            });

            if (list == null || list.Any() == false)
                return;

            try
            {
                var fxUserId = list.First().FxUserId;
                var service = new BaseProductAbnormalService(fxUserId);
                service.BatchAdd(list);
            }
            catch (Exception e)
            {
                Log.WriteError($"消息队列消费批量插入异常数据表错误：{e.ToJson()}");
            }
        }

		/// <summary>
        /// 自动关联（各业务库消费）
        /// </summary>
        /// <param name="models"></param>
        private void AutoRelationBindNew(List<MessageRecord> models)
        {
            if (models == null || models.Count == 0) return;

            var curPlatform = CustomerConfig.CloudPlatformType;
            var dbConfigRepository = new DbConfigRepository();

            var fxUserId = models.First().FxUserId;
            var bindModel = models.First().DataJson.ToObject<AutoMappingModel>();
            var request = bindModel.Request;
            bindModel.TaskCodeDic.TryGetValue(curPlatform, out var taskCode);
            var logfileName = $"AutoRelationBind_{DateTime.Now:yyyyMMdd}.log";

            // 获取当前云的数据库配置
            var cloudPlatformTypeList = new List<string> { curPlatform };
            var dbConfigList = dbConfigRepository.GetListByFxUserIds(new List<int> { fxUserId }, cloudPlatformTypeList);
            var oldConfigList = dbConfigRepository.GetFxAllCloudPlatformBusinessDbConfigs()
                .Where(x => x.DbServer.Location == curPlatform &&(x.DbServer.Location == "Alibaba" || x.DbServer.Location == "Pinduoduo"))
                .ToList();
            dbConfigList.AddRange(oldConfigList);

            const string searchSql = @"
SELECT distinct sku.SkuId       AS SkuId,
                sku.PlatformId  AS PlatformId,
                sku.SkuCode     AS SkuCode,
                sku.CargoNumber AS CargoNumber,
                p.ProductCode   AS ProductCode,
                p.ShopId        AS ShopId,
                p.SourceUserId  AS SourceUserId,
                p.PlatformType  AS PlatformType
FROM Product p WITH (NOLOCK)
         INNER JOIN ProductSku sku WITH (NOLOCK) ON sku.ProductCode = p.ProductCode
         INNER JOIN PathFlowReference pfr WITH (NOLOCK) ON p.ProductCode = pfr.ProductCode AND pfr.Status = 0
         INNER JOIN PathFlowNode pfn WITH (NOLOCK) ON pfn.PathFlowCode = pfr.PathFlowCode
         LEFT JOIN PathFlowReference pfr2 WITH (NOLOCK) ON pfr2.PathFlowRefCode = sku.SkuCode AND pfr2.Status = 0
         LEFT JOIN PathFlowNode pfn2 WITH (NOLOCK) ON pfn2.PathFlowCode = pfr2.PathFlowCode AND pfn2.FxUserId = @FxUserId
         INNER JOIN FunStringToTable( @Codes , ',') t ON t.item = sku.CargoNumber
WHERE pfn.FxUserId = @FxUserId
    AND (pfr.PathFlowRefCode= p.ProductCode OR pfr.PathFlowRefCode=sku.SkuCode)
    AND (pfr2.PathFlowRefCode IS NULL OR pfn2.FxUserId = @FxUserId)
";

            const string searchSqlByProductSkuCode = @"
SELECT
	s.SkuId AS SkuId,
	s.PlatformId AS PlatformId,
	s.SkuCode AS SkuCode,
    s.CargoNumber AS CargoNumber,
	p.ProductCode AS ProductCode,
	p.ShopId AS ShopId,
	p.SourceUserId AS SourceUserId,
	p.PlatformType AS PlatformType
FROM
	ProductSku s WITH (NOLOCK)
	LEFT JOIN Product p WITH (NOLOCK ) ON s.ProductCode = p.ProductCode
	INNER JOIN FunStringToTable ( @CodeList, ',' ) t ON t.item = s.SkuCode";

            // 分批处理SkuCodeList，500一批
            var codesList = bindModel.SkuCodeList;
            const int count = 500;
            var total = codesList.Count;
            var times = total / count;
            if (total % count > 0) times++;

            // 结果保存
            var resultDic = new ConcurrentDictionary<string, Tuple<bool, string>>();
            var msgList = new ConcurrentBag<MessageRecord>();

            // 基础商品库，业务库
            var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService(false, fxUserId);
            try
            {
                for (var i = 0; i < times; i++)
                {
                    var codes = codesList.Skip(i * count).Take(count).ToList();
                    var productSkuFxList = new List<ProductSkuFx>();
                    var productSkuByRelation = new List<ProductSkuFx>();

                    // 获取codes对应的uid
                    var skuUidList = bindModel.SkuCodeDic.Where(x => codes.Contains(x.Key)).Select(x => x.Value).ToList();

                    // 根据连接串分组
                    var groupBy = dbConfigList.GroupBy(x => x.ConnectionString).ToList();
                    var existPtSkuRelationsDic = baseOfPtSkuRelationService.GetListBySkuUids(skuUidList, fxUserId);

                    // 找到自动关联的关联数据
                    var existPtSkuRelations = existPtSkuRelationsDic.Values
                        .SelectMany(x => x.Where(y => y.RelationType == 1)).ToList();
                    var codeList = existPtSkuRelations.Select(x => x.ProductSkuCode).ToList();

                    // 同云跨库查询
                    groupBy.ForEach(g =>
                    {
                        var dbConfig = g.First();
                        var dbName = dbConfig.DbNameConfig.DbName;
                        var platform = dbConfig.DbServer.Location;

                        var apiDbConfig = new ApiDbConfigModel { DbNameConfigId = dbConfig.DbNameConfig.Id, Location = dbConfig.DbServer.Location, PlatformType = dbConfig.DbServer.Location };
                        var dbApi = new DbAccessUtility(apiDbConfig);

                        var resultByRelation = dbApi.Query<ProductSkuFx>(searchSqlByProductSkuCode, new { CodeList = string.Join(",", codeList) }).ToList();
                        var result = dbApi.Query<ProductSkuFx>(searchSql, new { FxUserId = fxUserId, Codes = string.Join(",", codes) }).ToList();

                        if (resultByRelation.Any()) productSkuByRelation.AddRange(resultByRelation);

                        if (result.Any())
                        {
                            result.ForEach(x =>
                            {
                                x.DbName = dbName;
                                x.CloudPlatform = platform;
                            });
                            productSkuFxList.AddRange(result);
                        }
                    });

                    // 保留原有查询
                    var baseOfPtSkuRelations = baseOfPtSkuRelationService
                        .GetListBySkuCode(productSkuFxList.Select(x => x.SkuCode).ToList(), fxUserId)
                        .Where(x => x.Status == 1).ToList();

                    // 预先建立查找字典，避免循环中重复查找
                    var skuCodeByUidDict = bindModel.SkuCodeDic
                        .ToDictionary(kv => kv.Value, kv => kv.Key);
                    
                    // 创建产品SkuCode到ProductSkuFx的映射
                    var productSkuFxBySkuCodeDict = productSkuByRelation
                        .ToDictionary(p => p.SkuCode, p => p);
                    
                    // 批量处理异常检测
                    var abnormalItems = existPtSkuRelations
                        .Where(x => {
                            // 快速字典查找
                            if (!skuCodeByUidDict.TryGetValue(x.BaseProductSkuUid, out var existSkuCode) || 
                                string.IsNullOrEmpty(existSkuCode))
                                return false;
            
                            // 快速字典查找
                            if (!productSkuFxBySkuCodeDict.TryGetValue(x.ProductSkuCode, out var productSkuFx))
                                return false;
            
                            // 检查是否需要创建异常
                            return productSkuFx.CargoNumber != existSkuCode;
                        })
                        .Select(x => {
                            var productSkuFx = productSkuFxBySkuCodeDict[x.ProductSkuCode];
        
                            return new {
                                Relation = x,
                                ProductSkuFx = productSkuFx
                            };
                        })
                        .ToList();
                    
                    // 批量创建异常消息
                    foreach (var item in abnormalItems)
                    {
                        var x = item.Relation;
                        var productSkuFx = item.ProductSkuFx;
    
                        var abnormal = new BaseProductAbnormal
                        {
                            AbnormalType = BaseProductAbnormalType.AutoMapping,
                            AbnormalReason = "该分销品在店铺后台已变更商品编码。",
                            FxUserId = fxUserId,
                            BaseProductUid = x.BaseProductUid,
                            BaseProductSkuUid = x.BaseProductSkuUid,
                            ProductPtId = x.ProductPtId,
                            ProductSkuPtId = x.ProductSkuPtId,
                            ProductCode = x.ProductCode,
                            ProductSkuCode = x.ProductSkuCode,
                            ProductShopId = x.ProductShopId,
                            ProductFxUserId = x.ProductFxUserId,
                            ProductPlatformType = x.ProductPlatformType,
                            ProductCloudPlatform = productSkuFx.CloudPlatform ?? CommUtls.GetPlatformCloudByType(x.ProductPlatformType),
                            ProductDbName = productSkuFx.DbName,
                            CreateTime = DateTime.Now,
                            UpdateTime = DateTime.Now
                        };

                        msgList.Add(new MessageRecord
                        {
                            CreateFxUserId = fxUserId,
                            CreateTime = DateTime.Now,
                            BusinessId = abnormal.ProductSkuCode,
                            TargetCloud = CloudPlatformType.Alibaba.ToString(),
                            ProductPlatformType = abnormal.ProductPlatformType,
                            MsgType = BaseProductMsgType.AddBaseProductAbnormal,
                            FxUserId = abnormal.FxUserId,
                            DataJson = abnormal.ToJson()
                        });
                        _abnormalNum++;
                    }

                    var existWarehouseSkuCode = new List<string>();
                
                    // 需判断SkuCode是否存在库存系统
                    var warehouseService = new WareHouseService();
                    var req = new WarehouseSkuGetOneRequest
                    {
                        WareHouseSkuCode = "-",
                        SkuCodeList = codes
                    };

                    var res = warehouseService.WarehouseSkuGetOne(req);
                    if (res.IsSucc)
                    {
                        existWarehouseSkuCode = res.ExistSkuCode;
                    }

                    // 在循环前预先创建查找字典
                    var productSkuFxByCargoNumberDict = productSkuFxList
                        .GroupBy(x => x.CargoNumber)
                        .ToDictionary(g => g.Key, g => g.ToList());

                    var baseProductSkuDict = bindModel.BaseProductSkus.ToDictionary(x => x.SkuCode);

                    var supplierConfigBySkuUidDict = bindModel.SupplierConfigList
                        .GroupBy(x => x.SkuUid)
                        .ToDictionary(g => g.Key, g => g.ToList());

                    var existRelationDict = baseOfPtSkuRelations
                        .ToDictionary(x => x.ProductSkuCode.Trim());

                    var existWarehouseSkuCodeSet = new HashSet<string>(existWarehouseSkuCode);
                    var tempCodes = new ConcurrentBag<string>(codes);
                    
                    // 并行处理每个SKU码
                    Parallel.ForEach(
                        tempCodes,
                        new ParallelOptions { MaxDegreeOfParallelism = 10 },
                        skuCode =>
                        {
                            // 1. 快速查找匹配商品
                            if (!productSkuFxByCargoNumberDict.TryGetValue(skuCode, out var productSkuFx) ||
                                !productSkuFx.Any()) return;

                            // 创建工作副本以免修改原始数据
                            productSkuFx = new List<ProductSkuFx>(productSkuFx);
                            var autoBindModel = CommUtls.DeepClone(bindModel);

                            // 2. 快速查找基础商品SKU
                            if (!baseProductSkuDict.TryGetValue(skuCode, out var skuEnt)) return;

                            // 3. 快速获取供应商配置
                            var modelConfigs = new List<BaseProductSkuSupplierConfigModel>();
                            if (supplierConfigBySkuUidDict.TryGetValue(skuEnt.Uid, out var supplierConfig))
                                modelConfigs.AddRange(supplierConfig.Select(conf => new BaseProductSkuSupplierConfigModel
                                {
                                    ApplyScope = conf.ApplyScope,
                                    Config = conf.Config,
                                    ConfigType = conf.ConfigType,
                                    SupplierFxUserId = conf.SupplierFxUserId
                                }));

                            // 4. 批量检查和标记需要删除的项
                            var itemsToRemove = new List<ProductSkuFx>();

                            foreach (var sku in productSkuFx)
                            {
                                if (!existRelationDict.TryGetValue(sku.SkuCode, out var existRelation)) continue;

                                // 创建异常对象
                                var abnormal = new BaseProductAbnormal
                                {
                                    AbnormalType = BaseProductAbnormalType.AutoMappingExist,
                                    AbnormalReason = "该分销品已绑定商品库，不允许关联多个。",
                                    FxUserId = fxUserId,
                                    BaseProductUid = existRelation.BaseProductUid,
                                    BaseProductSkuUid = existRelation.BaseProductSkuUid,
                                    ProductPtId = sku.PlatformId,
                                    ProductSkuPtId = sku.SkuId,
                                    ProductCode = sku.ProductCode,
                                    ProductSkuCode = sku.SkuCode,
                                    ProductShopId = sku.ShopId,
                                    ProductFxUserId = sku.SourceUserId ?? 0,
                                    ProductPlatformType = sku.PlatformType,
                                    ProductDbName = sku.DbName,
                                    ProductCloudPlatform = sku.CloudPlatform ?? CommUtls.GetPlatformCloudByType(sku.PlatformType),
                                    CreateTime = DateTime.Now,
                                    UpdateTime = DateTime.Now
                                };

                                msgList.Add(new MessageRecord
                                {
                                    CreateFxUserId = fxUserId,
                                    CreateTime = DateTime.Now,
                                    BusinessId = abnormal.ProductSkuCode,
                                    TargetCloud = CloudPlatformType.Alibaba.ToString(),
                                    ProductPlatformType = abnormal.ProductPlatformType,
                                    MsgType = BaseProductMsgType.AddBaseProductAbnormal,
                                    FxUserId = abnormal.FxUserId,
                                    DataJson = abnormal.ToJson()
                                });

                                itemsToRemove.Add(sku);
                                Interlocked.Increment(ref _abnormalNum);
                            }

                            // 5. 移除标记的项
                            foreach (var item in itemsToRemove)
                                productSkuFx.Remove(item);

                            if (!productSkuFx.Any()) return;

                            // 6. 判断是否存在于仓库系统
                            var useWareHouse = existWarehouseSkuCodeSet.Contains(skuCode);

                            // 7. 设置bindModel属性
                            autoBindModel.SkuCode = skuCode;
                            autoBindModel.BaseProductSkuUid = skuEnt.Uid;
                            autoBindModel.BaseProductUid = skuEnt.BaseProductUid;
                            autoBindModel.ShortTitle = skuEnt.ShortTitle;
                            autoBindModel.SettlePrice = skuEnt.SettlePrice;
                            autoBindModel.CostPrice = skuEnt.CostPrice;
                            autoBindModel.DistributePrice = skuEnt.DistributePrice;
                            autoBindModel.BaseProductSkuSupplierConfigs = modelConfigs;

                            // 8. 批量创建绑定SKU
                            autoBindModel.SkuList = productSkuFx.Select(proSku => new BindSku
                            {
                                IsUseWarehouse = useWareHouse,
                                ProductSkuCode = proSku.SkuCode,
                                ProductCode = proSku.ProductCode
                            }).ToList();

                            // 执行绑定操作
                            var result = baseOfPtSkuRelationService.BaseProductSkuRelationBindNew(autoBindModel, request, fxUserId, productSkuFx);

                            if (result.Item1) Interlocked.Add(ref _successNum, productSkuFx.Count);
                            else Log.WriteError($"新自动关联失败：{skuCode}，{result.Item2}", logfileName);

                            resultDic.TryAdd(skuCode, result);
                        });
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"新自动关联异常：{e.Message}", logfileName);

                msgList.Add(new MessageRecord
                {
                    CreateFxUserId = fxUserId,
                    CreateTime = DateTime.Now,
                    BusinessId = fxUserId.ToString(),
                    TargetCloud = CloudPlatformType.Alibaba.ToString(),
                    MsgType = BaseProductMsgType.AutoRelationBindComplete,
                    FxUserId = fxUserId,
                    DataJson = new AsyncTask
                    {
                        SuccessCount = _successNum,
                        FailCount = _abnormalNum,
                        TaskCode = taskCode,
                        ExceptionDesc = e.Message,
                        Status = -1,
                        FxUserId = fxUserId,
                        CreateTime = DateTime.Now,
                        UpdateTime = DateTime.Now
                    }.ToJson()
                });

                // 发送消息
                if (msgList.Any()) new MessageRecordService().SendBusinessMessage(msgList.ToList());
                return;
            }

            msgList.Add(new MessageRecord
            {
                CreateFxUserId = fxUserId,
                CreateTime = DateTime.Now,
                BusinessId = fxUserId.ToString(),
                TargetCloud = CloudPlatformType.Alibaba.ToString(),
                MsgType = BaseProductMsgType.AutoRelationBindComplete,
                FxUserId = fxUserId,
                DataJson = new AsyncTask
                {
                    SuccessCount = _successNum,
                    FailCount = _abnormalNum,
                    TaskCode = taskCode,
                    Status = 5,
                    FxUserId = fxUserId,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                }.ToJson()
            });

            // 发送消息
            if (msgList.Any()) new MessageRecordService().SendBusinessMessage(msgList.ToList());
        }

        // 用于控制任务进度更新
        private static readonly object _taskLock = new object();

        /// <summary>
        /// 自动关联完成，用于更新任务状态
        /// </summary>
        /// <param name="models"></param>
        private void AutoRelationBindComplete(List<MessageRecord> models)
        {
            // 只会在精选云
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                return;
            var task = models.First().DataJson.ToObject<AsyncTask>();
            if (task == null) return;

            // 获取当前任务实体
            var fxUserId = task.FxUserId;
            var service = new AsyncTaskService();
            var ent = service.GetTaskByTaskCode(task.TaskCode);

            // 更新任务状态
            ent.SuccessCount = task.SuccessCount;
            ent.FailCount = task.FailCount;
            ent.ExceptionDesc = task.ExceptionDesc;
            ent.Status = task.Status;

            service.Update(ent);

            // 更新进度到Redis
            var redisKey = CacheKeys.BaseProductAutoRelationTaskProgressKey.Replace("{FxUserId}", fxUserId.ToString());
            lock (_taskLock)
            {
                try
                {
                    var taskViewModel = RedisHelper.Get<AutoRelationTaskViewModel>(redisKey) ?? new AutoRelationTaskViewModel();
                    taskViewModel.SuccessCount += task.SuccessCount;
                    taskViewModel.FailCount += task.FailCount;
                    taskViewModel.ExceptionDesc += $"|{ent.CloudPlatformType}已完成 " + task.ExceptionDesc;
                    taskViewModel.Process += 25;
                    taskViewModel.UpdateTime = DateTime.Now;

                    RedisHelper.Set(redisKey, taskViewModel.ToJson(), TimeSpan.FromHours(2));
                }
                catch (Exception ex)
                {
                    // 处理异常，如记录日志等
                    Log.WriteError($"更新任务进度时发生错误：{ex.Message}", $"AutoRelationBindComplete-{DateTime.Now:yyyy-MM-dd}.log");
                }
            }
        }
    }
}