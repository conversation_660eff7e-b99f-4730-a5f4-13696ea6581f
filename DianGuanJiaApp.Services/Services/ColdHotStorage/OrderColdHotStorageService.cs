using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.Base;

namespace DianGuanJiaApp.Services
{
    public class OrderColdHotStorageService : BaseColdHotStorageService
    {
        //待发货状态
        private readonly List<string> _waitSellerSendStatus = new List<string> { "waitsellersend", "waitbuyerpay" };


        public OrderColdHotStorageService()
        {

        }

        public OrderColdHotStorageService(DbConfigModel dbConfig) : base(dbConfig)
        {

        }

        /// <summary>
        /// 冷热存储处理器（不处理新的冷数据，新的冷数据统一在逻辑单维度进行冷热分离）
        /// </summary>
        /// <param name="orders"></param>
        /// <param name="hotStorageFunc"></param>
        /// <param name="coldStorageFunc"></param>
        /// <returns></returns>
        private void ColdHotStorageHandler(List<Order> orders,
            Action<List<Order>, List<Order>> hotStorageFunc,
            Action<List<Order>> coldStorageFunc)
        {
            //没有开启写入到冷库，则是热库
            if (!IsWriteToColdDb)
            {
                hotStorageFunc(orders, null);
                return;
            }
            //克隆（收件人处理）
            //var ordersByClone = orders.ToJson().ToObject<List<Order>>();
            //平台订单ID
            var platformOrderIds = orders.Select(m => m.PlatformOrderId).ToList();
            //检测冷订单迁移状态，是否已迁移
            var coldLogicOrderStates = GetColdLogicOrderStatesByPlatformOrderIds(platformOrderIds);
            //已迁移冷数据订单ID
            var existPlatformOrderIds = coldLogicOrderStates.Select(m => m.PlatformOrderId).ToList();
            //迁移过冷数据
            var coldOrders = orders.Where(m => existPlatformOrderIds.Contains(m.PlatformOrderId)).ToList();
            //冷数据保存
            if (coldOrders.Any())
            {
                coldStorageFunc(coldOrders);
            }
            //测试期间，灰度冷热数据处理
            if (ColdDbStatus == 1 || ColdDbStatus == 2)
            {
                //所有订单保存一份到热库
                hotStorageFunc(orders, null);
            }
            else
            {
                //正式期间，热库存储
                var hotOrders =
                    orders.Where(m => !existPlatformOrderIds.Contains(m.PlatformOrderId)).ToList();
                    //用于解决已经发货，热库没有更新状态，导致订单列表待发货页签出现数据
                    //只要更新热库有冷数据，但是还没删除的
                    var coldPlatformOrderIds = coldLogicOrderStates.Where(s => s.State == 1).ToList()
                        .Select(s => s.PlatformOrderId);
                    var hotUpdateColdOrders =
                        orders.Where(o => coldPlatformOrderIds.Contains(o.PlatformOrderId)).ToList();
                    hotStorageFunc(hotOrders, hotUpdateColdOrders);
            }
        }

        /// <summary>
        /// 批量合并保存订单
        /// </summary>
        /// <param name="orders"></param>
        /// <param name="orderMergerParameter"></param>
        /// <param name="isOfflineOrder"></param>
        /// <param name="tag"></param>
        public List<Order> BulkMergerFx(List<Order> orders, OrderMergerParameterModel orderMergerParameter,
            bool isOfflineOrder = false, string tag = "")
        {
            //排空处理
            if (orders == null || !orders.Any())
            {
                return new List<Order>();
            }
            //冷热分离处理
            var hotOrdersByResult = new List<Order>();
            var coldOrdersByResult = new List<Order>();
            ColdHotStorageHandler(orders, (hotOrders, hotUpdateColdOrders) =>
            {
                hotOrdersByResult = new ColdOrderService(CurrentDbConfig.ConnectionString).BulkMergerFx(hotOrders,
                    orderMergerParameter, isOfflineOrder, tag);
                if (hotUpdateColdOrders != null && hotUpdateColdOrders.Any())
                {
                    //更新热库的冷数据
                    hotUpdateColdOrders.ForEach(o =>
                    {
                        var orderItemsIds = o.OrderItems.Select(oi => oi.SubItemID).Distinct().ToList();
                        new OrderService(CurrentDbConfig.ConnectionString).UpdateOrderStatus(o.ShopId,
                            o.PlatformOrderId, orderItemsIds, o.PlatformStatus);
                    });
                }
            }, coldOrders =>
            {
                coldOrdersByResult = new ColdOrderService(CurrentDbConfig.ColdDbConnectionString, 1).BulkMergerFx(
                    coldOrders,
                    orderMergerParameter,
                    isOfflineOrder, tag, isSaveReceiver: IsFormalColdHotStorage);
            });
            //非正式的冷热分离处理
            if (!IsFormalColdHotStorage)
            {
                return hotOrdersByResult;
            }
            //正式冷热分离，返回结果需要拼装
            var ordersByResult = new List<Order>();
            if (hotOrdersByResult != null && hotOrdersByResult.Any())
            {
                ordersByResult.AddRange(hotOrdersByResult);
            }

            if (coldOrdersByResult != null && coldOrdersByResult.Any())
            {
                ordersByResult.AddRange(coldOrdersByResult);
            }
            return ordersByResult;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="selectKeyModel"></param>
        /// <param name="flag"></param>
        /// <param name="remark"></param>
        /// <param name="isColdData"></param>
        /// <returns></returns>
        public bool UpdateSellerRemark(OrderSelectKeyModel selectKeyModel, string flag, string remark,
            bool isColdData = false)
        {
            //判空处理
            if (selectKeyModel.PlatformOrderId == string.Empty || selectKeyModel.PlatformOrderId == 0.ToString() ||
                selectKeyModel.ShopId == 0)
            {
                return false;
            }

            //热数据更新（热数据更新）
            new ColdOrderService(CurrentDbConfig.ConnectionString).UpdateOrderSellerRemark(selectKeyModel, flag, remark);

            //冷数据
            if (isColdData)
            {
                new ColdOrderService(CurrentDbConfig.ColdDbConnectionString, 1).UpdateOrderSellerRemark(selectKeyModel, flag, remark);
            }

            return true;
        }

        /// <summary>
        /// 订单查询 按订单号查询，只用在订单生命周期工具（其他勿用）
        /// </summary>
        /// <param name="platformOrderId"></param>
        /// <param name="selectField"></param>
        /// <returns></returns>
        public Order GetOrder(string platformOrderId, string selectField = "*")
        {
            var order = new ColdOrderService(CurrentDbConfig.ConnectionString).GetOrder(platformOrderId, selectField);
            if (IsWriteToColdDb && order == null)
            {
                order = new ColdOrderService(CurrentDbConfig.ColdDbConnectionString, 1).GetOrder(platformOrderId,
                    selectField);
            }
            return order;
        }
    }
}