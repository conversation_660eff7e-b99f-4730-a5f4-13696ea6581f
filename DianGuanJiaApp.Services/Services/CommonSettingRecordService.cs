using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using System.Collections.Generic;
using System;
using System.Linq;
using System.Threading;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using System.Data.Common;

namespace DianGuanJiaApp.Services.Services
{
    public class CommonSettingRecordService : BaseService<CommonSettingRecord>
    {
        private readonly CommonSettingRecordRepository _repository;
        public CommonSettingRecordService()
        {
            _repository = new CommonSettingRecordRepository();
            this._baseRepository = _repository;
        }

        /// <summary>
        /// 配置内容变更日志
        /// </summary>
        /// <param name="createFxUserId">创建人用户Id</param>
        /// <param name="keyNames">Key名称</param>
        /// <returns></returns>
        public List<CommonSettingRecord> GetList(int createFxUserId, List<string> keyNames)
        {
            if (createFxUserId <= 0 || keyNames.IsNullOrEmptyList())
                return new List<CommonSettingRecord>();
            var list = _repository.GetList(createFxUserId, keyNames);
            //转换显示的文案
            list?.ForEach(p => {
                p.NewShowText = ShowText(p.NewValue, p.KeyName);
                p.OldShowText = ShowText(p.OldValue, p.KeyName);
            });
            return list;
        }

        private string ShowText(string v, string keyName)
        {
            if (v.IsNullOrEmpty())
                return "";

            if (keyName == BusinessSettingKeys.SupplyBy1688.DefaultSettlementPriceSwitch)
            {
                if (v == "1" || v == "true")
                    return "开启";
                else if (v == "0" || v == "false")
                    return "关闭";
            }
            else if (keyName == BusinessSettingKeys.SupplyBy1688.IsUpdateSettlementPriceSwitch)
            {
                if (v == "1" || v == "true")
                    return "开启了价格更新设置";
                else if (v == "0" || v == "false")
                    return "关闭了价格更新设置";
            }
            else if (keyName == BusinessSettingKeys.SupplyBy1688.IsUpdateHistorySettlementPrice)
            {
                if (v == "1" || v == "true")
                    return "切换设置为同时覆盖单人单品价";
                else
                    return "切换设置为仅更新统一采购价";
            }
            if (keyName == BusinessSettingKeys.SupplyBy1688.DefaultSettlementPriceSwitch || keyName == BusinessSettingKeys.SupplyBy1688.DefaultSettlementPriceFields)
            {
                var priceDict = new Dictionary<string, string>();
                priceDict["ext_consign_price"] = "分销基准价";
                priceDict["consignPrice"] = "代发包邮价";
                priceDict["price"] = "商品价格";
                priceDict["jxhyPrice"] = "精选货源—件起批价";
                priceDict["jxhyPfPrice"] = "精品货源批发价";
                priceDict["pftPrice"] = "批发团商品价";

                var arrV = v.Split(',');
                var lastResult = string.Empty;
                var x = 0;
                foreach ( var item in arrV )
                {
                    x++;
                    if (priceDict.ContainsKey(item))
                        lastResult += $"{x}、{priceDict[item]}<br/>";
                    else
                        lastResult += $"{x}、{item}<br/>";
                }
                return lastResult;
            }
            return v;
        }
    }
}