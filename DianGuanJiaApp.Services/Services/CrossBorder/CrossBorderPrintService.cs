using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Net;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace DianGuanJiaApp.Services.Services
{
    /// <summary>
    /// 跨境打单相关服务
    /// </summary>
    public class CrossBorderPrintService
    {
        private WaybillCodeService _waybillCodeService = new WaybillCodeService();
        private readonly OrderManualRecordRepository _orderManualRecordRepository = new OrderManualRecordRepository();
        private LogicOrderService _logicOrderService = new LogicOrderService();
        private LogicOrderItemService _logicOrderItemService = new LogicOrderItemService();
        private static TkPrintProcessStateRecordService _tkPrintProcessStateRecordService = new TkPrintProcessStateRecordService();
        private ShopService _shopService = new ShopService();
        private TkOrderPrintService _tkOrderPrintService = new TkOrderPrintService();
        private TkPrintProcessStateRecordRepository _recordRepository = new TkPrintProcessStateRecordRepository();

        #region 打印/获取面单
        /// <summary>
        /// 执行打印
        /// </summary>
        /// <param name="model"></param>
        /// <param name="request"></param>
        /// <param name="pageToken"></param>
        /// <returns></returns>
        public async Task<TkOrderPrintResponseModel> ExecutePrint(TkOrderPrintRequestModel model, HttpRequestBase request, string pageToken)
        {
            var rst = new TkOrderPrintResponseModel()
            {
                Success = true,
                PrinterName = model.PrinterName,
                RequestBatchNumber = model.RequestBatchNumber,
                TemplateType = model.TemplateType,
                TemplateSize = model.TemplateSize,
                TemplateName = model.TemplateName
            };

            var sw = Stopwatch.StartNew();
            try
            {
                #region 前置校验
                //检查模板
                if (string.IsNullOrWhiteSpace(model.TemplateName) || string.IsNullOrWhiteSpace(model.TemplateType) || string.IsNullOrWhiteSpace(model.TemplateSize))
                    throw new LogicException("打印模板信息为空，请检查！");
                if (model.Orders == null || !model.Orders.Any())
                    throw new LogicException("打印订单数据为空，请检查！");

                //获取逻辑订单
                var logicOrderIds = model.Orders.Select(f => f.LogicOrderId).Distinct().ToList();
                int currentFxUserId = SiteContext.Current.CurrentFxUserId;
                model.FxUserId = currentFxUserId;
                var logicOrders = _logicOrderService.GetOrders(logicOrderIds, false, null, new QueryReceiverModel() { IsOnlyGetMask = true });
                if (logicOrders == null || !logicOrders.Any())
                    throw new LogicException("打印的订单不存在，请刷新页面后重试打印");
                if (!logicOrders.Any())
                {
                    if (logicOrders.Where(x => x.FxUserId != currentFxUserId).Count() > 0)
                        throw new LogicException("无权操作该订单，请刷新页面后重试打印");
                }
                #endregion
                List<LogicOrder> los = new List<LogicOrder>();
                var logicOrderItems = logicOrders.SelectMany(x => x.LogicOrderItems).ToList();
                var printedCount = logicOrderItems.Where(x => x.PrintState == 0).Count();
                var shopids = logicOrders.Select(f => f.ShopId).ToList();
                var shops = _shopService.GetShopByIds(shopids); //获取店铺信息
                model.ShopDict = shops.ToDictionary(f => f.Id, f => f); //临时存储店铺信息，方便后续调用接口使用
                if (printedCount == 0)//传入的订单项不存在未打印订单 不进行拆单/标记发货直接获取面单
                    goto getWaybill;

                los = await SplitPackage(logicOrders, model, rst, currentFxUserId);

                //标记发货
                await BatchShipPackageExecute(los, model, rst);

            getWaybill://goto 直接进行获取面单数据
                if (!los.Any())
                    los = logicOrders;
                //是否加发货单
                if (model.IsPreparePrint)
                    model.SendGoodTemplatePageModel = GenerateprepareTemplateDataResult(model, request, pageToken, model.ShopDict, los);

                //获取面单
                await GetWaybillDocSync(los, model, rst);
                rst.Orders = model.Orders;

                //下载面单OR面单+发货单
                await DownloadPdfAndConvertToBase64(los, model, rst);

                //保存打单
                new TkOrderPrintService().SaveData(rst);

            }
            catch (Exception ex)
            {
                //现在是分开调用的所以走这里
                if (ex is LogicException)
                    rst.SetError(ex.Message);
                else if (CustomerConfig.IsDebug)
                    rst.SetError($"程序异常:{ex.Message}");
                else
                    rst.SetError("程序异常，请稍后重试");
                Log.WriteError(ex.ToString());
            }
            finally
            {
                sw.Stop();
                rst.ElapsedMs = sw.ElapsedMilliseconds;
            }
            return rst;
        }

        #region 安排发货/购买面单

        /// <summary>
        /// 标记发货
        /// </summary>
        /// <param name="logicOrders"></param>
        /// <param name="model"></param>
        /// <param name="rst"></param>
        /// <returns></returns>
        public async Task BatchShipPackageExecute(List<LogicOrder> logicOrders, TkOrderPrintRequestModel model, TkOrderPrintResponseModel rst)
        {
            //在美国市场，（标记为已发货/ 创建包裹 /安排包裹交接）
            //按用户分组，分别调用接口
            var logicOrderGroups = logicOrders.GroupBy(f => f.ShopId);

            //获取订单店铺信息
            var shopDict = model.ShopDict;
            if (shopDict == null || !shopDict.Any())
            {
                //重新获取店铺
                var shops = _shopService.GetShopByIds(logicOrderGroups.Select(f => f.Key).ToList()); //获取店铺信息
                shopDict = shops.ToDictionary(f => f.Id, f => f); //临时存储店铺信息，方便后续调用接口使用
            }
            var loDict = logicOrders.ToDictionary(f => f.LogicOrderId, f => f);
            //打单发货设置 满足部分自发货商家面单打单支持上门揽收的诉求
            var tiktokShipSettingValue = GetTikTokShipSetting();

            ///绑定待发货数据
            var waitShipPackageList = BuildWaitShipPackageList(logicOrders, model, shopDict);
            ///待发货数据标记发货
            await WaitShipPackagesSync(waitShipPackageList, shopDict, tiktokShipSettingValue);

        }

        /// <summary>
        /// 待发货数据标记发货
        /// 内部区分美区本土/非美区本土
        /// </summary>
        /// <param name="waitShipPackageList"></param>
        /// <param name="shopDict"></param>
        /// <param name="tiktokShipSettingValue"></param>
        /// <returns></returns>
        public async Task WaitShipPackagesSync(List<OrderShipPackageContext> waitShipPackageList, Dictionary<int, Shop> shopDict, string tiktokShipSettingValue)
        {
            var groupedByShops = waitShipPackageList.GroupBy(f => f.ShopId);
            var options = new ParallelOptions { MaxDegreeOfParallelism = 10 };

            Parallel.ForEach(groupedByShops, options, async group =>
            {
                //未找到相关订单
                if (!shopDict.TryGetValue(group.Key, out var shop))
                    return;
                var platformService = PlatformFactory.GetPlatformService(shop) as TikTokPlatformService;
                var otherChunList = new List<OrderShipPackageContext>();
                //美区 TK平台物流 接口不一致（标记为已发货/ 创建包裹 /安排包裹交接）
                var usChunList = new List<OrderShipPackageContext>();
                //初始化平台类
                var tkPlatformService = PlatformFactory.GetPlatformService(shop) as TikTokPlatformService;
                foreach (var item in group)
                {
                    //包裹ID为空
                    if (string.IsNullOrEmpty(item.PackageId))
                        continue;
                    string subPlatformType = shop.SubPlatformType;
                    string roleType = shop.RoleType; //店铺类型角色 全球/本土
                    if (!string.IsNullOrEmpty(shop.SubPlatformType))
                    {
                        if ((subPlatformType != "US" || (subPlatformType == "US" && roleType != "LOCAL")))
                            otherChunList.Add(item);
                        else if (subPlatformType == "US" && roleType == "LOCAL")
                            usChunList.Add(item);
                    }
                }
                var otherChunks = otherChunList.ChunkList(50);
                var usChunks = usChunList.ChunkList(50);
                //注：按店铺分组 不存在两个都有的情况
                if (otherChunks.Any())
                    ///非美区本土执行
                    await BatchShipPackageAsync(platformService, otherChunks, tiktokShipSettingValue);
                if (usChunks.Any())
                    await CreatePackagesFromUSLocalAsync(platformService, usChunks);//创建发货标签
            });
        }

        /// <summary>
        /// 标记发货
        /// </summary>
        /// <param name="service"></param>
        /// <param name="chunks"></param>
        /// <param name="tiktokShipSettingValue"></param>
        /// <returns></returns>
        public async Task BatchShipPackageAsync(TikTokPlatformService service, List<List<OrderShipPackageContext>> chunks, string settingValue)
        {
            var parallelOptions = new ParallelOptions { MaxDegreeOfParallelism = 10 };
            Parallel.ForEach(chunks, parallelOptions, chunk =>
            {
                var packageIds = chunk.Select(c => c.PackageId).Distinct().ToList();
                try
                {
                    //注：service 按店铺分组进来的
                    var spRst = service.BatchShipPackage(packageIds, settingValue);
                    foreach (var item in chunk)
                    {
                        try
                        {
                            var packageId = item.PackageId;
                            var logicOrder = item.LogicOrder;
                            var requestOrder = item.PrintOrder;
                            // 查找发货结果
                            var packageResult = spRst.FirstOrDefault(f => f.Item1 == packageId);

                            if (packageResult != null && !packageResult.Item2)
                            {
                                throw new LogicException($"包裹【{packageId}】发货失败: {packageResult.Item3}");
                            }
                            item.PrintOrder.Logs.Add($"包裹【{string.Join(",", packageIds)}】发货成功");
                        }
                        catch (Exception ex)
                        {
                            item.PrintOrder.SetError(ex.Message);
                            if (item.IsPreviewPrint)
                                _recordRepository.UpdatePrintRecordError(ex.Message, item.RequestBatchNumber, item.LogicOrder.LogicOrderId, item.FxUserId);
                        };
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"BatchShipPackage:包裹ID【{packageIds.ToJson()}】发货异常{ex}", LogModuleTypeEnum.OrderManage);
                }
            });
        }

        /// <summary>
        /// 创建包裹
        /// </summary>
        /// <param name="service"></param>
        /// <param name="chunks"></param>
        /// <returns></returns>
        private async Task CreatePackagesFromUSLocalAsync(TikTokPlatformService service, List<List<OrderShipPackageContext>> usChunks)
        {
            var parallelOptions = new ParallelOptions { MaxDegreeOfParallelism = 10 };
            foreach (var list in usChunks)
            {
                Parallel.ForEach(list, parallelOptions, item =>
                {
                    decimal height = 0;
                    decimal length = 0;
                    decimal width = 0;
                    decimal weight = 0;
                    var lo = item.LogicOrder;
                    var requestOrder = item.PrintOrder;
                    try
                    {
                        if (!lo.LogicOrderItems.IsNotNullAndAny())
                            throw new LogicException($"订单{lo.PlatformOrderId}未找到相关订单信息");

                        #region 商品同步/获取包裹信息
                        ///商品ID
                        var itemProductIds = lo.LogicOrderItems.Select(x => x.ItemProductId).Distinct().ToList();
                        if (!itemProductIds.IsNotNullAndAny())
                            return;
                        foreach (var platProductID in itemProductIds)
                        {
                            try
                            {
                                var product = service.SyncProduct(platProductID);
                                if (product != null)
                                {
                                    if (product.Weight != null && product.Weight != 0)
                                        weight += (decimal)product.Weight;
                                    if (!string.IsNullOrEmpty(product.Length) && product.Length.ToDecimal() > length)
                                        length = product.Length.ToDecimal();
                                    if (!string.IsNullOrEmpty(product.Width) && product.Width.ToDecimal() > width)
                                        width = product.Width.ToDecimal();
                                    if (!string.IsNullOrEmpty(product.Height) && product.Height.ToDecimal() != 0)
                                        height += product.Height.ToDecimal();
                                }
                            }
                            catch (Exception ex)
                            {

                            }
                        }
                        #endregion

                        var packagesDimension = new CreatePackagesDimension() { height = height.ToString(), length = length.ToString(), unit = "CM", width = width.ToString() };
                        var packagesWeight = new CreatePackagesWeight() { value = weight.ToString(), unit = "GRAM" };
                        var gss = service.GetEligibleShippingService(lo.PlatformOrderId, item.SubItemIds, packagesDimension, packagesWeight);
                        if (!gss.Item1 || gss == null || gss.Item3 == null)
                        {
                            throw new LogicException($"订单【{lo.PlatformOrderId}】，获取符合条件配送服务失败,错误信息：{gss.Item2}");
                        }
                        var shipping_services = gss.Item3.shipping_services;
                        if (!shipping_services.IsNotNullAndAny())
                        {
                            throw new LogicException($"订单【{lo.PlatformOrderId}】,获取符合条件配送服务失败，运输服务信息错误");
                        }
                        var shippingService = shipping_services.Where(ss => ss.is_default).First();
                        if (shippingService == null || string.IsNullOrEmpty(shippingService.id))
                        {
                            throw new LogicException($"订单【{lo.PlatformOrderId}】，获取符合条件配送服务失败,配送服务为空");
                        }
                        var spRst = service.CreatePackages(lo.PlatformOrderId, item.SubItemIds, shippingService.id);
                        if (spRst == null || !spRst.Item1)
                            throw new LogicException($"订单【{lo.PlatformOrderId}】发货失败: {spRst.Item2}");
                        if (string.IsNullOrEmpty(spRst.Item3.package_id))
                        {
                            throw new LogicException($"订单【{lo.PlatformOrderId}】发货失败: {spRst.Item2}");
                        }
                        lo.LogicOrderItems.ForEach(x =>
                        {
                            x.PackageId = spRst.Item3.package_id;
                        });
                        requestOrder.LogicOrder = lo;
                    }
                    catch (Exception ex)
                    {
                        string errMsg = ex.Message;
                        item.PrintOrder.SetError(errMsg);
                        if (item.IsPreviewPrint)
                            _recordRepository.UpdatePrintRecordError(errMsg, item.RequestBatchNumber, lo.LogicOrderId, item.FxUserId);
                    }

                });
            }
        }

        /// <summary>
        /// 绑定需要安排发货数据
        /// </summary>
        /// <param name="logicOrders"></param>
        /// <param name="model"></param>
        /// <param name="shopDict"></param>
        /// <returns></returns>
        public List<OrderShipPackageContext> BuildWaitShipPackageList(List<LogicOrder> logicOrders, TkOrderPrintRequestModel model, Dictionary<int, Shop> shopDict)
        {
            var waitShipPackageList = new List<OrderShipPackageContext>();
            var loDict = logicOrders.ToDictionary(f => f.LogicOrderId, f => f);

            foreach (var item in model.Orders.Where(o => o.IsSuccess))
            {
                if (!loDict.TryGetValue(item.LogicOrderId, out var lo))
                    continue;

                var selectedItems = lo.LogicOrderItems.Where(f => item.SelectedOrderItemIds.Contains(f.Id)).ToList();
                if (selectedItems.GroupBy(f => f.PackageId).Count() > 1)
                {
                    string errMsg = "获取面单失败，勾选的订单项拆包后存在多个包裹";
                    item.SetError(errMsg);

                    if (model.IsPreviewPrint)
                        _recordRepository.UpdatePrintRecordError(errMsg, model.RequestBatchNumber, lo.LogicOrderId, model.FxUserId);
                    continue;
                }
                var printOis = lo.LogicOrderItems.Where(f => item.SelectedOrderItemIds.Contains(f.Id)).ToList(); //勾选打印的订单项
                waitShipPackageList.Add(new OrderShipPackageContext
                {
                    ShopId = lo.ShopId,
                    PackageId = selectedItems.FirstOrDefault()?.PackageId,
                    LogicOrder = lo,
                    PrintOrder = item,
                    SubItemIds = GetSubItemIds(printOis),
                    FxUserId = model.FxUserId,
                    RequestBatchNumber = model.RequestBatchNumber,
                    IsPreviewPrint = model.IsPreviewPrint,
                    PlatformOrderId = lo.PlatformOrderId
                });
            }
            return waitShipPackageList;
        }

        #endregion

        #region 获取面单

        /// <summary>
        /// 获取面单
        /// </summary>
        /// <param name="logicOrders"></param>
        /// <param name="model"></param>
        /// <param name="rst"></param>
        /// <returns></returns>
        public async Task GetWaybillDocSync(List<LogicOrder> logicOrders, TkOrderPrintRequestModel model, TkOrderPrintResponseModel rst)
        {
            var succesList = model.Orders.Where(f => f.IsSuccess).ToList();
            if (!succesList.Any())
                return;
            //按用户分组，分别调用接口
            var logicOrderGroups = logicOrders.GroupBy(f => f.ShopId);

            //获取订单店铺信息
            var shopDict = model.ShopDict;
            if (shopDict == null || !shopDict.Any())
            {
                //重新获取店铺
                var shops = _shopService.GetShopByIds(logicOrderGroups.Select(f => f.Key).ToList()); //获取店铺信息
                shopDict = shops.ToDictionary(f => f.Id, f => f); //临时存储店铺信息，方便后续调用接口使用
            }
            ///绑定待发货数据
            var waitShipPackageList = BuildWaitShipPackageList(logicOrders, model, model.ShopDict);
            //按店铺分组=》
            foreach (var group in waitShipPackageList.GroupBy(f => f.ShopId))
            {
                var shopId = group.Key;
                if (!shopDict.ContainsKey(shopId))
                {
                    continue;
                }
                var shop = shopDict[group.Key];

                //初始化平台类
                var tkPlatformService = PlatformFactory.GetPlatformService(shop) as TikTokPlatformService;
                //按包裹ID分组
                foreach (var item in group)
                {
                    var packageId = item.PackageId;
                    var lo = item.LogicOrder;
                    var requestOrder = item.PrintOrder;

                    try
                    {
                        // 获取面单文档
                        var swGetDoc = Stopwatch.StartNew();
                        var wayBillDocRst = tkPlatformService.GetPackageShippingDoc(packageId, model.TemplateType, model.TemplateSize);
                        swGetDoc.Stop();
                        rst.Logs.Add($"包裹 {packageId} 获取面单文档耗时: {swGetDoc.ElapsedMilliseconds}ms");

                        if (!wayBillDocRst.Item1)
                            throw new LogicException($"包裹【{packageId}】获取面单文档: {wayBillDocRst.Item2}");

                        // 获取面单详情
                        var swGetDetail = Stopwatch.StartNew();
                        var packageDetailRst = tkPlatformService.GetPackageDetail(packageId);
                        swGetDetail.Stop();
                        rst.Logs.Add($"包裹 {packageId} 获取包裹详情耗时: {swGetDetail.ElapsedMilliseconds}ms");

                        if (!packageDetailRst.Item1)
                            throw new LogicException($"包裹【{packageId}】获取包裹: {packageDetailRst.Item2}");

                        requestOrder.PrintPdfUrl = wayBillDocRst.Item2;
                        requestOrder.PackageDetail = packageDetailRst.Item3;
                        if (string.IsNullOrEmpty(requestOrder.PrintPdfUrl))
                            throw new LogicException($"获取TikTok打印面单失败，请稍后重试");
                        requestOrder.LogicOrder = lo;
                        requestOrder.paperHeight = item.PrintOrder?.paperHeight;
                        requestOrder.paperWidth = item.PrintOrder?.paperWidth;
                        rst.paperHeight = item.PrintOrder?.paperHeight;
                        rst.paperWidth = item.PrintOrder?.paperWidth;
                        string outputFilePath = string.Empty;
                        if (model.IsPreparePrint && model.SendGoodTemplatePageModel.IsNotNullAndAny())
                        {                // 获取面单详情
                            var swSend = Stopwatch.StartNew();
                            var reparePrintData = model.SendGoodTemplatePageModel.Find(x => !string.IsNullOrEmpty(x.LogicOrderId) && x.LogicOrderId.Contains(item.LogicOrder.PlatformOrderId));
                            //加发货单
                            var tupleModel = new Tuple<int, string, LogicOrder, TkPrintOrderModel, SendGoodTemplatePageModel>(item.ShopId, item.PackageId, item.LogicOrder, item.PrintOrder, reparePrintData);
                            outputFilePath = await _tkPrintProcessStateRecordService.GeneratePdfOutputFilePath(tupleModel, model.IsPreparePrint, null, wayBillDocRst, packageDetailRst);
                            requestOrder.Logs.Add($"生成发货单PDF耗时：{swSend.ElapsedMilliseconds} ms");
                            swSend.Stop();
                            requestOrder.PrintData = outputFilePath;

                        }

                        //预览打印
                        if (item.IsPreviewPrint)
                        {
                            TkPrintProcessStateRecord record = new TkPrintProcessStateRecord()
                            {
                                PrintPdfAddress = outputFilePath,
                                State = "Success",
                                ErrorMessage = null,
                                PdfUrl = wayBillDocRst.Item2,
                                ExpressName = packageDetailRst.Item3?.shipping_provider_name,
                                ExpressNo = packageDetailRst.Item3?.tracking_number,
                                FxUserId = item.FxUserId,
                                BatchNo = item.RequestBatchNumber,
                                LogicOrderId = item.LogicOrder.LogicOrderId
                            };
                            _recordRepository.UpPrintProcessStateRecord(record);
                        }
                    }
                    catch (Exception ex)
                    {
                        requestOrder.SetError($"{ex.Message}");
                        if (item.IsPreviewPrint)
                            _recordRepository.UpdatePrintRecordError(ex.Message, item.RequestBatchNumber, lo.LogicOrderId, item.FxUserId);
                    }
                }

            }
        }




        /// <summary>
        /// 下载PDF 转Base64
        /// </summary>
        /// <param name="logicOrders"></param>
        /// <param name="model"></param>
        /// <param name="rst"></param>
        /// <returns></returns>
        public async Task DownloadPdfAndConvertToBase64(List<LogicOrder> logicOrders, TkOrderPrintRequestModel model, TkOrderPrintResponseModel rst)
        {
            ///加发货单
            if (model.IsPreparePrint)
            {
                var sw_download_pdf = Stopwatch.StartNew();
                await _tkOrderPrintService.GeneratePdfResult(model, rst);
                sw_download_pdf.Stop();
                rst.Logs.Add($"GeneratePdfResult ：耗时：{sw_download_pdf.ElapsedMilliseconds} ms");
            }

            else
            {
                //循环获取pdf的base64位数据
                var successList = model.Orders.Where(f => f.IsSuccess).ToList();
                var pdfDict = new ConcurrentDictionary<string, string>();
                foreach (var item in successList)
                {
                    pdfDict.TryAdd(item.PrintPdfUrl, string.Empty);
                }
                if (!model.IsPreparePrint)
                {
                    var sw_download_pdf = Stopwatch.StartNew();
                    await DownloadPdfAndConvertToBase64Async(pdfDict);
                    //Utility.Net.HttpMethods.DownloadPdfAndConvertToBase64(pdfDict);
                    sw_download_pdf.Stop();
                    rst.Logs.Add($"DownloadPdfAndConvertToBase64 下载pdf 耗时：{sw_download_pdf.ElapsedMilliseconds} ms");
                    //赋值pdf base64 到响应模型
                    foreach (var item in successList)
                    {
                        var pdfUrl = item.PrintPdfUrl;
                        var pdfBase64 = string.Empty;
                        if (pdfDict.TryGetValue(pdfUrl, out pdfBase64))
                            item.PrintData = pdfBase64;
                        if (string.IsNullOrEmpty(item.PrintData))
                            item.SetError("获取平台面单失败，请重试！");
                        if (model.TemplateType.Equals("SHIPPING_LABEL_PICTURE") && !item.PrintData.Contains("data:image"))
                            item.PrintData = $"data:image/jpeg;base64,{item.PrintData}";
                    }
                }
            }
        }

        /// <summary>
        /// 组装发货单数据
        /// </summary>
        /// <param name="model"></param>
        /// <param name="request"></param>
        /// <param name="pageToken"></param>
        /// <param name="shopDict"></param>
        /// <param name="newLogicOrders"></param>
        /// <returns></returns>
        public List<SendGoodTemplatePageModel> GenerateprepareTemplateDataResult(TkOrderPrintRequestModel model, HttpRequestBase request, string pageToken, Dictionary<int, Shop> shopDict, List<LogicOrder> newLogicOrders)
        {
            List<SendGoodTemplatePageModel> prepareTemplateDataResult = new List<SendGoodTemplatePageModel>();
            //用户未勾选加发货单直接返回
            if (!model.IsPreparePrint)
                return prepareTemplateDataResult;
            try
            {
                var loDict = newLogicOrders.ToDictionary(f => f.LogicOrderId, f => f);
                List<OrderRequestModel> orderRequestModelList = _tkPrintProcessStateRecordService.GetOrderRequestModelList(model, loDict, shopDict);
                prepareTemplateDataResult = _tkPrintProcessStateRecordService.GetPrepareTemplateDataResult(orderRequestModelList, model, request, pageToken);
            }
            catch (Exception ex)
            {
                try
                {
                    model.Orders = model.Orders.Select(o =>
                    {
                        o.SetError("勾选发货单数据有误,请稍后重试");
                        return o;
                    }).ToList();
                }
                catch (Exception updateEx)
                {
                    throw updateEx;
                }
                throw ex;
            }
            return prepareTemplateDataResult;
        }

        #endregion

        #region 逻辑单拆包
        /// <summary>
        /// 逻辑单拆包
        /// </summary>
        /// <param name="logicOrders">逻辑单</param>
        /// <param name="model">打印请求模型</param>
        /// <param name="rst">Tk订单快递单打印返回结果模型</param>
        /// <param name="fxUserId">用户ID</param>
        /// <param name="IsPreviewPrint">是否批量打印预览</param>
        /// <returns></returns>
        public async Task<List<LogicOrder>> SplitPackage(List<LogicOrder> logicOrders, TkOrderPrintRequestModel model, TkOrderPrintResponseModel rst, int fxUserId)
        {
            #region 拆包说明
            //拆包逻辑
            //1.检查逻辑单是否包含整单原始单（LogicOrder.OrderItem.Count==LogicOrder.POItemCount）

            //2.如果不是完整单，则需要拆包
            //  2.1.勾选的订单项打印过，判断勾选的订单项的packageId是否相同？
            //      不相同：直接提示错误（已经打印过的订单，必须勾选与上次打印一样的商品）
            //        相同：按勾选订单项拆包
            //  2.2.勾选的订单项未打印过，按勾选的订单项拆包

            //3.当前逻辑单是完整单
            //  3.1.完整勾选，完整勾选无需拆包。
            //  3.2.部分勾选
            //      3.2.1.勾选的订单项打印过，判断勾选的订单项的packageId是否相同？
            //            不相同：直接提示错误（已经打印过的订单，必须勾选与上次打印一样的商品）
            //              相同：按勾选订单项拆包
            //      3.2.2.勾选的订单项未打印过，按勾选的订单项拆包

            //4.拆包接口如果返回空值，则重新同步订单，判断当前打印勾选的订单项的包裹id是否一致，不一致判断是否有有包裹已经发货了，发货了，提示已发货的包裹不能重新组合拆包。
            //5.合单订单需完整打印，系统内未打印合单只是系统内部合单 非平台合包，需调用接口进行合包，合包后不可拆单（退审、换绑、拆单）

            //补充
            //tk非东南亚站点若所选item均为未打印的item，则打印面单时检查item数量是否=原平台订单item数量，若相等则按订单的package id进行发货获取面单，若不相等则检查当前订单是否存在item 已经存在物流单号（不局限于当前厂家逻辑单）或打印状态为已打印，
            //若不存在，则按所选item项先调平台拆单再进行拆包获取面单，并将拆单后的另外一个package id赋值给其余item项；
            //若存在，则不支持未发货的item项继续拆单，按所选package id直接ship package并获取面单


            #endregion

            var splitPackageSucces = new List<LogicOrder>(); //无需拆包||成功拆包的订单
            //循环拆包
            var waitSplitPackageList = new List<Tuple<int, string, List<string>, LogicOrder, TkPrintOrderModel>>(); //待拆包的订单
            List<OrderShipPackageContext> orderSplitContexts = new List<OrderShipPackageContext>();//待拆包的订单上下文
            #region  预处理：更新缺失包裹信息的订单
            await UpdatePackageInfosParallelAsync(logicOrders);
            #endregion

            #region 前置校验
            var logicOrderDict = logicOrders.ToDictionary(f => f.LogicOrderId, f => f);
            //按打印的订单循环拆包
            foreach (var item in model.Orders)
            {
                // 订单有效性检查
                if (!ValidateOrder(item, logicOrderDict, rst)) continue;
                var lo = logicOrderDict[item.LogicOrderId];
                var printOis = lo.LogicOrderItems.Where(f => item.SelectedOrderItemIds.Contains(f.Id)).ToList(); //勾选打印的订单项

                #region  合单处理
                if (IsMergedOrder(lo) && lo.LogicOrderItems.Count > 1)
                {
                    var sw = Stopwatch.StartNew();
                    await HandleMergedOrder(lo, printOis, item, model, fxUserId);
                    sw.Stop();
                    rst.Logs.Add($"[{lo.LogicOrderId}]合单处理信息耗时：{sw.ElapsedMilliseconds} ms");

                    ///合单无须拆包
                    splitPackageSucces.Add(lo);
                    continue;
                }
                #endregion

                var packageGroup = printOis.GroupBy(f => f.PackageId);
                if (packageGroup.Count() > 1)
                {
                    var errmsg = $"拆包失败，勾选的订单项拆包后有多个包裹";
                    item.SetError(errmsg);
                    if (model.IsPreviewPrint)
                        _recordRepository.UpdatePrintRecordError(errmsg, model.RequestBatchNumber, lo.LogicOrderId, fxUserId);
                    continue;
                }



                // 判断是否需要拆包
                if (CanSkipSplitting(lo, printOis, model.ShopDict))
                {
                    item.Logs.Add($"无需拆包"); //无需拆包记录日志
                    splitPackageSucces.Add(lo);
                    continue;
                }

                ///选中的订单项
                var subItemIds = GetSubItemIds(printOis);

                // 准备拆包数据
                orderSplitContexts.Add(new OrderShipPackageContext
                {
                    ShopId = lo.ShopId,
                    FxUserId = fxUserId,
                    PlatformOrderId = lo.PlatformOrderId,
                    SubItemIds = subItemIds,
                    LogicOrder = lo,
                    RequestBatchNumber = model.RequestBatchNumber,
                    IsPreviewPrint = model.IsPreviewPrint,
                    PrintOrder = item
                });
            }
            //无订单需要拆包
            if (!orderSplitContexts.Any())
                return splitPackageSucces;
            #endregion

            #region 拆包部分

            var waitUpdatePackageInfo = new List<Order>(); //待更新包裹信息的订单
            // 执行拆包处理
            if (orderSplitContexts.Any())
            {
                var sw = Stopwatch.StartNew();
                splitPackageSucces = await SplitOrderssAsyncProces(orderSplitContexts, model, rst, fxUserId);
                sw.Stop();
                rst.Logs.Add($"需要拆包处理数量：{splitPackageSucces.Count}，拆包信息耗时：{sw.ElapsedMilliseconds} ms");
            }
            #endregion
            return splitPackageSucces;
        }

        /// <summary>
        /// 订单拆包异步处理
        /// </summary>
        /// <param name="contexts"></param>
        /// <param name="splitPackageSucces"></param>
        /// <param name="model"></param>
        /// <param name="rst"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        private async Task<List<LogicOrder>> SplitOrderssAsyncProces(List<OrderShipPackageContext> contexts, TkOrderPrintRequestModel model, TkOrderPrintResponseModel rst, int fxUserId)
        {
            string errMsg = string.Empty;//用于记录错误信息
            var semaphore = new SemaphoreSlim(10); // 最大并发数 10 避免资源耗尽
            var groupedByShop = contexts.GroupBy(c => c.ShopId);

            if (model.ShopDict == null)
            {
                var shops = _shopService.GetShopByIds(groupedByShop.Select(g => g.Key).ToList()); //获取店铺信息
                model.ShopDict = shops.ToDictionary(f => f.Id, f => f); //临时存储店铺信息，方便后续调用接口使用
            }


            // 用于收集所有结果的线程安全集合
            var result = new ConcurrentBag<LogicOrder>();
            var tasks = groupedByShop.Select(async group =>
            {
                await semaphore.WaitAsync();
                try
                {
                    Shop shop = null;
                    if (!model.ShopDict.TryGetValue(group.Key, out shop))
                    {
                        errMsg = $"拆包失败，勾选的订单项店铺不存在";
                        foreach (var ctx in group)
                        {
                            ctx.PrintOrder.SetError(errMsg);
                            if (model.IsPreviewPrint)
                                _recordRepository.UpdatePrintRecordError(errMsg, model.RequestBatchNumber, ctx.LogicOrder.LogicOrderId, fxUserId);
                        }
                        return;
                    }
                    //var shop = shops.FirstOrDefault(s => s.Id == group.Key);
                    //if (shop == null)
                    //{
                    //    errMsg = $"拆包失败，勾选的订单项店铺不存在";
                    //    foreach (var ctx in group)
                    //    {
                    //        ctx.PrintOrder.SetError(errMsg);
                    //        if (model.IsPreviewPrint)
                    //            _recordRepository.UpdatePrintRecordError(errMsg, model.RequestBatchNumber, ctx.LogicOrder.LogicOrderId, fxUserId);
                    //    }
                    //    return;
                    //}
                    var service = PlatformFactory.GetPlatformService(shop) as TikTokPlatformService;
                    var los = await SplitOrdersPackage(service, group.ToList(), fxUserId);
                    // 将结果添加到线程安全的集合中
                    foreach (var logicOrder in los)
                    {
                        result.Add(logicOrder);
                    }
                }
                catch (Exception ex)
                {
                    errMsg = $"拆包失败,{ex.Message}";
                    foreach (var ctx in group)
                    {
                        ctx.PrintOrder.SetError(errMsg);
                        if (model.IsPreviewPrint)
                            _recordRepository.UpdatePrintRecordError(errMsg, model.RequestBatchNumber, ctx.LogicOrder.LogicOrderId, fxUserId);
                    }
                }
                finally { semaphore.Release(); }

            });
            await Task.WhenAll(tasks); // 等待所有任务完成
            return result.ToList(); // 转换为List返回
        }

        /// <summary>
        /// 拆包处理
        /// </summary>
        /// <param name="service">用户维度Service</param>
        /// <param name="orderSplitContexts"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        async Task<List<LogicOrder>> SplitOrdersPackage(TikTokPlatformService service, List<OrderShipPackageContext> orderSplitContexts, int fxUserId)
        {
            List<LogicOrder> splitPackageSucces = new List<LogicOrder>();
            var waitUpdatePackageInfo = new List<Order>(); //待更新包裹信息的订单           
            foreach (var item in orderSplitContexts)//按订单
            {
                var platformOrderId = item.LogicOrder.PlatformOrderId;
                List<List<string>> splitSubItemIdGroups = new List<List<string>>() { item.SubItemIds };
                Order order = null;
                //1.更新最新的包裹id
                Dictionary<string, OrderItem> oiDict = null;
                #region 拆包同步接口
                try
                {
                    var splitPackageRst = service.OrderSplitPackage(platformOrderId, splitSubItemIdGroups);
                    if (splitPackageRst == null || !splitPackageRst.Any())
                    {
                        //拆包接口返回空，说明拆包失败了
                        //throw new Exception("拆包失败,返回结果是空");
                    }
                    order = service.SyncOrder(platformOrderId);

                    //更新最新的包裹id
                    oiDict = order.OrderItems.ToDictionary(f => f.SubItemID, f => f);
                }
                catch (Exception ex)
                {
                    foreach (var splitModel in orderSplitContexts)
                    {
                        splitModel.PrintOrder.SetError($"拆包失败，{ex.Message}");
                    }
                    //拆单失败，继续下一个
                    continue;
                }
                #endregion

                var packageInfoIsUpdate = false; //包裹信息是否有变化
                ///2.赋值更新订单项包裹ID
                foreach (var loi in item.LogicOrder.LogicOrderItems)
                {
                    //找出原始单的订单项
                    if (oiDict.TryGetValue(loi.SubItemId, out var oi))
                    {
                        if (loi.PackageId != oi.PackageId
                            || loi.PackageStatus != oi.PackageStatus
                            || loi.TracakingNumber != oi.TracakingNumber
                            || loi.MergeItemJSON != oi.ExtAttr1)
                        {
                            packageInfoIsUpdate = true;
                            //用于更新的时，只更新包裹信息有变化的订单项
                            loi.PackageInfIsChanged = true;
                            //赋值新的包裹信息
                            loi.PackageId = oi.PackageId;
                            loi.PackageStatus = oi.PackageStatus;
                            loi.TracakingNumber = oi.TracakingNumber;
                            loi.MergeItemJSON = oi.ExtAttr1;
                        }
                    }
                }
                ///3.考虑分单系统存在拆单的情况，直接针对相关原始订单、逻辑单进行同步包裹信息
                if (packageInfoIsUpdate && !waitUpdatePackageInfo.Contains(order))
                    waitUpdatePackageInfo.Add(order);//包裹信息有更新则加入待更新列表
                //判断订单是否是待发货状态
                if (order.PlatformStatus != OrderStatusType.waitsellersend.ToString())
                {
                    item.PrintOrder.SetError($"拆包失败，订单不是待发货状态[{order.PlatformStatus}]");
                    continue;
                }
                //4. 校验包裹一致性（勾选项必须属于同一包裹）
                var packageIds = item.SubItemIds.SelectMany(s => order.OrderItems.Where(oi => s.Contains(oi.SubItemID)).Select(oi => oi.PackageId)).Distinct().ToList();
                if (packageIds.Count() > 1)
                {
                    var errMsg = $"拆包失败，勾选的订单项有多个包裹，且包裹状态不一致";
                    item.PrintOrder.SetError($"拆包失败，勾选的订单项有多个包裹，且包裹状态不一致");
                    if (item.LogicOrder.LogicOrderItems.Any(f => f.PrintState == 1))
                    {
                        //已经打印过的订单如果拆包失败，一般是 这次打印和上次打印勾选的商品不一致，导致重新拆包。（已打印的订单，上次打印拆包包裹掉了ship package 接口，不支持再次拆包了）
                        errMsg = "拆包失败，已经打印过的订单，必须勾选和上次打印一样的商品打印（不支持重新拆包）";
                    }
                    //拆包失败
                    item.PrintOrder.SetError(errMsg);
                    continue;
                }

                splitPackageSucces.Add(item.LogicOrder);

            }
            //更新包裹订单信息
            #region 更新包裹
            if (waitUpdatePackageInfo.Any())
            {
                var sw = Stopwatch.StartNew();
                //基于拆包同步的原始订单，进行更新包裹信息
                new ColdOrderService().UpdatePackageInfoByOrders(waitUpdatePackageInfo);
                sw.Stop();
            }
            #endregion
            return splitPackageSucces;
        }


        /// <summary>
        ///针对包裹为nuLL更新包裹信息
        /// </summary>
        /// <param name="logicOrders"></param>
        /// <returns></returns>
        private async Task UpdatePackageInfosParallelAsync(List<LogicOrder> logicOrders)
        {
            var groups = logicOrders.GroupBy(x => x.ShopId).ToList();
            var tasks = groups.Select(async group =>
            {
                var shop = new ShopService().GetShopById(group.Key);
                var tkPlatformService = PlatformFactory.GetPlatformService(shop) as TikTokPlatformService;
                var ordersToSync = group.Where(o => o.LogicOrderItems.Any(i => string.IsNullOrEmpty(i.PackageId))).ToList();
                ///包裹ID为空或者为null需要同步的地方
                if (ordersToSync.Any())
                {
                    var orders = tkPlatformService.SyncOrders(ordersToSync.Select(o => o.PlatformOrderId).ToList());
                    var orderDict = orders.ToDictionary(o => o.PlatformOrderId);
                    // 构建订单项字典
                    var orderItemsDict = orders
                        .SelectMany(o => o.OrderItems)
                        .GroupBy(oi => oi.SubItemID)
                        .ToDictionary(g => g.Key, g => g.First());
                    var updatedLogicOrders = new List<LogicOrder>();
                    foreach (var logicOrder in ordersToSync)
                    {
                        if (orderDict.TryGetValue(logicOrder.PlatformOrderId, out var syncedOrder))
                        {
                            foreach (var loi in logicOrder.LogicOrderItems)
                            {
                                if (orderItemsDict.TryGetValue(loi.SubItemId, out var oi))
                                {
                                    loi.PackageId = oi.PackageId;
                                    loi.PackageStatus = oi.PackageStatus;
                                    loi.TracakingNumber = oi.TracakingNumber;
                                    loi.MergeItemJSON = oi.ExtAttr1;
                                    loi.PackageInfIsChanged = true;
                                }
                            }
                            updatedLogicOrders.Add(logicOrder);
                        }
                    }

                    if (updatedLogicOrders.Any())
                    {
                        new LogicOrderService().UpdatePackageInfo(updatedLogicOrders);
                    }
                }
            });
            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// 合单处理
        /// </summary>
        /// <param name="order"></param>
        /// <param name="selectedItems"></param>
        /// <param name="model"></param>
        /// <param name="result"></param>
        /// <returns></returns>
        private async Task HandleMergedOrder(LogicOrder lo, List<LogicOrderItem> selectedItems, TkPrintOrderModel item, TkOrderPrintRequestModel model, int fxUserId)
        {
            string errMsg = string.Empty;
            try
            {
                if (lo.IsMainOrder && (lo.MergeredType == 4 || lo.MergeredType == 5))
                {
                    var platformOrderIds = lo.LogicOrderItems.Select(x => x.PlatformOrderId).Distinct().ToList();
                    var printOrderItemCount = lo.LogicOrderItems.Count();
                    if (printOrderItemCount != selectedItems.Count)//合单的总项！=勾选打印的订单项
                    {
                        errMsg = $"订单[{string.Join(",", platformOrderIds)}]:订单项不完整，请勾选所有项进行打印。";
                        item.SetError(errMsg);
                        if (model.IsPreviewPrint)
                            _recordRepository.UpdatePrintRecordError(errMsg, model.RequestBatchNumber, lo.LogicOrderId, fxUserId);
                        return;
                    }
                    //同订单不需要进行合包
                    if (string.IsNullOrEmpty(lo.LastWaybillCode) && platformOrderIds.Count() > 1)
                    {
                        var shop = _shopService.Get(lo.ShopId);
                        //2.通过参与合单平台订单，待人SearchCombinablePackages 获取是否可以合包 并且获取最新的包裹ID  
                        var tuple = SearchTikCombinablePackages(shop, platformOrderIds);
                        if (!tuple.Item1)
                        {
                            item.SetError($"{string.Join(",", platformOrderIds)}平台不可合单，请自行进行拆单后在尝试打单");
                        }

                        var mergeredPackagesId = tuple.Item2;//可合包的包裹ID

                        var tkPlatformService = PlatformFactory.GetPlatformService(shop) as TikTokPlatformService;

                        TKCombinablePackageModel tKCombinable = new TKCombinablePackageModel()
                        {
                            id = mergeredPackagesId,
                            order_ids = platformOrderIds
                        };
                        //3.调用平台进行合包接口
                        var tupleMerg = tkPlatformService.CombinePackage(tKCombinable);
                        if (!tupleMerg.Item1)
                        {
                            errMsg = $"{string.Join(",", platformOrderIds)},平台合包失败,请自行进行拆单：{tupleMerg.Item2}";
                            item.SetError(errMsg);
                            if (model.IsPreviewPrint)
                                _recordRepository.UpdatePrintRecordError(errMsg, model.RequestBatchNumber, lo.LogicOrderId, fxUserId);
                        }
                        var needUpdateLogicOrders = new List<LogicOrder>(); //待更新包裹信息的订单
                                                                            //4.更新包裹ID 含合单、原始订单
                        var combinablePackages = tupleMerg.Item3;
                        if (combinablePackages != null && combinablePackages.Any())
                        {
                            foreach (var packageModel in combinablePackages)
                            {
                                if (packageModel.order_ids.Any(x => x == lo.PlatformOrderId))
                                {
                                    lo.LogicOrderItems.ForEach(x =>
                                    {
                                        x.PackageId = packageModel.id;
                                        x.PackageInfIsChanged = true;
                                    });
                                    needUpdateLogicOrders.Add(lo);
                                }
                            }
                            //更新逻辑单项包裹ID,更新平台订单项包裹ID 会更新所有的
                            if (needUpdateLogicOrders.Any())
                                new LogicOrderService().UpdatePackageInfo(needUpdateLogicOrders);
                        }
                        //5.防止商品换绑将合单成功的 添加至手工推单记录
                        if (lo.LogicOrderItems.Any())
                        {
                            var records = new List<OrderManualRecordModel>();
                            var batch = DateTime.Now.ToString("yyyyMMddhhssmmfff");
                            //合单取原始订单
                            var logicOrderIds = lo.LogicOrderItems.Select(x => x.OrignalOrderId).Distinct().ToList();

                            List<string> pathFlowNodes = new List<string>() { lo.PathFlowCode };
                            var pathFlowNodeList = new PathFlowService().GetPathFlowNodeListByFxUserId(pathFlowNodes, fxUserId);
                            var upFxuserId = fxUserId;
                            if (pathFlowNodeList != null && pathFlowNodeList.Any())
                            {
                                var pathFlowNode = pathFlowNodeList.Find(x => x.DownFxUserId == 0);
                                upFxuserId = pathFlowNode.UpFxUserId > 0 ? pathFlowNode.UpFxUserId : upFxuserId;
                            }
                            logicOrderIds.ForEach(x =>
                            {
                                var record = new OrderManualRecordModel()
                                {
                                    Batch = batch,
                                    UserFrom = upFxuserId,//这里考虑一二级 取逻辑单路径流上级
                                    UserTo = fxUserId,//已经是最后一级了
                                    LogicOrderId = x,
                                    CreateTime = DateTime.Now,
                                    Remark = $"跨境合单{lo.LogicOrderId}"
                                };
                                records.Add(record);
                            });
                            _orderManualRecordRepository.BulkWrite(records, "OrderManualRecord", new List<string> { "ID" });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errMsg = $"合单异常:订单{lo.LogicOrderId} 异常信息{ex.Message}";
                item.SetError(errMsg);
            }
        }


        #endregion

        #endregion

        #region 自行寄出状态修改物流信息
        /// <summary>
        /// tk订单平台物流的自行寄出状态修改物流信息
        /// </summary>
        /// <param name="model"></param>
        public Tuple<bool, string> UpdateSelfShippingInfo(UpdateSelfShippingInfo model)
        {
            try
            {
                var logicOrderService = new LogicOrderService();
                var sendHistoryService = new SendHistoryService();
                var logicOrder = logicOrderService.GetOrder(model.LogicOrderId, new QueryReceiverModel() { IsOnlyGetMask = true });
                if (logicOrder == null || !logicOrder.LogicOrderItems.IsNotNullAndAny())
                {
                    return new Tuple<bool, string>(false, "订单不存在");
                }
                var groups = logicOrder.LogicOrderItems.Where(f => !string.IsNullOrEmpty(f.PackageId)).GroupBy(f => f.PackageId).ToList();
                var sendHistorys = sendHistoryService.GetSendHistoriesByOrderIds(new List<string>() { logicOrder.LogicOrderId });
                var now = DateTime.Now;
                var platformWaitInsertOrderSelfDelivery = new OrderSelfDelivery()
                {
                    BatchNo = (model.ExpressCode + model.ExpressName + model.WaybillCode + now.ToString("yyyyMMddHHmmssfff")).ToShortMd5(),
                    CreateTime = now,
                    ExpressCpCode = model.ExpressCode,
                    ExpressName = model.ExpressName,
                    WaybillCode = model.WaybillCode,
                    Status = 1,
                    UpdateTime = now
                };
                logicOrder.OrderSelfDeliveryBatchNo = platformWaitInsertOrderSelfDelivery?.BatchNo; //赋值已交运
                //保存自发货记录
                new OrderSelfDeliveryService().Add(platformWaitInsertOrderSelfDelivery);

                if (groups.IsNotNullAndAny())
                {
                    foreach (var group in groups)
                    {
                        var packageId = group.Key;
                        SendHistory sendHistory = sendHistorys.Where(s => !string.IsNullOrEmpty(s.PackageId) && s.PackageId.Equals(packageId)).First();
                        if (sendHistory != null && sendHistory.CollectionType.Equals("SELF_DELIVERY"))
                        {
                            sendHistoryService.UpdateOrderSelfDeliveryBatchNo(platformWaitInsertOrderSelfDelivery.BatchNo, sendHistory.ID);
                        }
                    }
                }

                //更新订单的交运状态
                new LogicOrderService().UpdateLogisticStatus(new List<LogicOrder>() { logicOrder });

            }
            catch (Exception e)
            {
                return new Tuple<bool, string>(false, "更新失败");
            }

            return new Tuple<bool, string>(true, string.Empty);
        }
        #endregion

        #region 修改已发货第三方物流信息
        /// <summary>
        /// 修改已发货第三方物流信息
        /// </summary>
        /// <param name="LogicOrderInfos"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public TkUpdateShippedLogisticsResponseModel TkUpdateShippedLogistics(List<TkUpdateShippedLogisticsLogicOrderInfo> LogicOrderInfos)
        {
            var rst = new TkUpdateShippedLogisticsResponseModel()
            {
                UpdateShippedLogisticsResultList = new List<TkUpdateShippedLogisticsResultModel>()
            };

            try
            {
                var logicOrderService = new LogicOrderService();
                var logicOrderItemService = new LogicOrderItemService();
                var sendHistoryService = new SendHistoryService();
                var orderSelfDeliveryService = new OrderSelfDeliveryService();
                //获取逻辑订单
                var logicOrderIds = LogicOrderInfos.Select(f => f.LogicOrderId).Distinct().ToList();
                var logicOrders = logicOrderService.GetOrders(logicOrderIds, false, null, new QueryReceiverModel() { IsOnlyGetMask = true });
                if (logicOrders == null || !logicOrders.Any())
                {
                    throw new LogicException("订单都不存在，请刷新页面后重新勾选");
                }
                var logicOrderDict = logicOrders.ToLookup(f => f.LogicOrderId, f => f).ToDictionary(f => f.Key, f => f.FirstOrDefault());
                //获取订单店铺信息
                var shopDict = new Dictionary<int, Shop>();
                //获取店铺
                var shops = new ShopService().GetShopByIds(logicOrders.Select(l => l.ShopId).ToList());
                shopDict = shops.ToDictionary(f => f.Id, f => f);
                var sendHistorys = sendHistoryService.GetSendHistoriesByOrderIds(logicOrders.Select(l => l.LogicOrderId).ToList());
                var waitUpdateStatusLogicOrders = new List<LogicOrder>();
                foreach (var LogicOrderInfo in LogicOrderInfos)
                {
                    if (logicOrderDict.TryGetValue(LogicOrderInfo.LogicOrderId, out LogicOrder lo))
                    {
                        if (shopDict.TryGetValue(lo.ShopId, out var shop))
                        {
                            if (string.IsNullOrEmpty(LogicOrderInfo.ExpressName))
                            {
                                rst.UpdateShippedLogisticsResultList.Add(new TkUpdateShippedLogisticsResultModel()
                                {
                                    LogicOrderId = lo.LogicOrderId,
                                    PlatformOrderId = lo.PlatformOrderId,
                                    ErrMsg = "物流公司为空",
                                    Success = false
                                });
                                continue;
                            }
                            if (string.IsNullOrEmpty(LogicOrderInfo.WaybillCode))
                            {
                                rst.UpdateShippedLogisticsResultList.Add(new TkUpdateShippedLogisticsResultModel()
                                {
                                    LogicOrderId = lo.LogicOrderId,
                                    PlatformOrderId = lo.PlatformOrderId,
                                    ErrMsg = "物流单号为空",
                                    Success = false
                                });
                                continue;
                            }

                            //先调接口,成功了本地才同步保存
                            var server = PlatformFactory.GetPlatformService(shop) as TikTokPlatformService;
                            var us = server.UpdateShippingInfo(lo.PlatformOrderId, LogicOrderInfo.WaybillCode, LogicOrderInfo.ShippingProviderId);

                            if (us != null && us.Item1)
                            {
                                DateTime now = DateTime.Now;
                                OrderSelfDelivery waitInsertOrderSelfDelivery = new OrderSelfDelivery()
                                {
                                    BatchNo = (LogicOrderInfo.ExpressName + LogicOrderInfo.WaybillCode + now.ToString("yyyyMMddHHmmssfff")).ToShortMd5(),
                                    CreateTime = now,
                                    ExpressCpCode = null,
                                    ExpressName = LogicOrderInfo.ExpressName,
                                    WaybillCode = LogicOrderInfo.WaybillCode,
                                    Status = 1,
                                    UpdateTime = now
                                };
                                //保存自发货记录
                                orderSelfDeliveryService.Add(waitInsertOrderSelfDelivery);
                                //修改发货记录
                                var groups = lo.LogicOrderItems.Where(f => !string.IsNullOrEmpty(f.PackageId)).GroupBy(f => f.PackageId).ToList();
                                if (groups.IsNotNullAndAny())
                                {
                                    foreach (var group in groups)
                                    {
                                        var packageId = group.Key;
                                        if (sendHistorys.IsNotNullAndAny())
                                        {
                                            SendHistory sendHistory = sendHistorys.Where(s => !string.IsNullOrEmpty(s.PackageId) && s.PackageId.Equals(packageId)).First();
                                            if (sendHistory != null && sendHistory.CollectionType.Equals("SELF_DELIVERY"))
                                            {
                                                sendHistoryService.UpdateOrderSelfDeliveryBatchNo(waitInsertOrderSelfDelivery.BatchNo, sendHistory.ID);
                                            }
                                        }
                                    }
                                }
                                //统一保存，修改逻辑单信息
                                lo.OrderSelfDeliveryBatchNo = waitInsertOrderSelfDelivery?.BatchNo;
                                waitUpdateStatusLogicOrders.Add(lo);
                            }
                            else
                            {
                                var logicOrderItems = logicOrderItemService.GetListForDuplication(new List<string>() { lo.LogicOrderId });
                                bool successFlag = true;
                                string error = "";
                                foreach (var li in logicOrderItems)
                                {
                                    var ups = server.UpdatePackageShippingInfo(li.PackageId, LogicOrderInfo.WaybillCode, LogicOrderInfo.ShippingProviderId);
                                    if (ups == null || !ups.Item1)
                                    {
                                        successFlag = false;
                                        error = ups.Item2;
                                    }
                                }
                                if (successFlag)
                                {
                                    DateTime now = DateTime.Now;
                                    OrderSelfDelivery waitInsertOrderSelfDelivery = new OrderSelfDelivery()
                                    {
                                        BatchNo = (LogicOrderInfo.ExpressName + LogicOrderInfo.WaybillCode + now.ToString("yyyyMMddHHmmssfff")).ToShortMd5(),
                                        CreateTime = now,
                                        ExpressCpCode = null,
                                        ExpressName = LogicOrderInfo.ExpressName,
                                        WaybillCode = LogicOrderInfo.WaybillCode,
                                        Status = 1,
                                        UpdateTime = now
                                    };
                                    //保存自发货记录
                                    orderSelfDeliveryService.Add(waitInsertOrderSelfDelivery);
                                    //修改发货记录
                                    var groups = lo.LogicOrderItems.Where(f => !string.IsNullOrEmpty(f.PackageId)).GroupBy(f => f.PackageId).ToList();
                                    if (groups.IsNotNullAndAny())
                                    {
                                        foreach (var group in groups)
                                        {
                                            var packageId = group.Key;
                                            if (sendHistorys.IsNotNullAndAny())
                                            {
                                                SendHistory sendHistory = sendHistorys.Where(s => !string.IsNullOrEmpty(s.PackageId) && s.PackageId.Equals(packageId)).First();
                                                if (sendHistory != null && sendHistory.CollectionType.Equals("SELF_DELIVERY"))
                                                {
                                                    sendHistoryService.UpdateOrderSelfDeliveryBatchNo(waitInsertOrderSelfDelivery.BatchNo, sendHistory.ID);
                                                }
                                            }

                                        }
                                    }
                                    //统一保存，修改逻辑单信息
                                    lo.OrderSelfDeliveryBatchNo = waitInsertOrderSelfDelivery?.BatchNo;
                                    waitUpdateStatusLogicOrders.Add(lo);
                                }
                                else
                                {
                                    rst.UpdateShippedLogisticsResultList.Add(new TkUpdateShippedLogisticsResultModel()
                                    {
                                        LogicOrderId = lo.LogicOrderId,
                                        PlatformOrderId = lo.PlatformOrderId,
                                        ErrMsg = error,
                                        Success = false
                                    });
                                }

                            }
                        }
                        else
                        {
                            rst.UpdateShippedLogisticsResultList.Add(new TkUpdateShippedLogisticsResultModel()
                            {
                                LogicOrderId = lo.LogicOrderId,
                                PlatformOrderId = lo.PlatformOrderId,
                                ErrMsg = "订单对应的店铺不存在",
                                Success = false
                            });
                        }
                    }
                    else
                    {
                        rst.UpdateShippedLogisticsResultList.Add(new TkUpdateShippedLogisticsResultModel()
                        {
                            LogicOrderId = lo.LogicOrderId,
                            PlatformOrderId = lo.PlatformOrderId,
                            ErrMsg = "订单不存在",
                            Success = false
                        });
                    }
                }
                //成功的统一保存
                if (waitUpdateStatusLogicOrders.IsNotNullAndAny())
                {
                    logicOrderService.UpdateLogisticStatus(waitUpdateStatusLogicOrders);
                    rst.UpdateShippedLogisticsResultList.AddRange(waitUpdateStatusLogicOrders.Select(l => new TkUpdateShippedLogisticsResultModel()
                    {
                        LogicOrderId = l.LogicOrderId,
                        PlatformOrderId = l.PlatformOrderId,
                        ErrMsg = string.Empty,
                        Success = true
                    }).ToList());
                }
                return rst;
            }
            catch (Exception e)
            {
                throw new Exception("遇到错误，更新失败");
            }
        }

        public async Task<TkUpdateShippedLogisticsResponseModel> TkUpdateShippedLogisticsAsync(List<TkUpdateShippedLogisticsLogicOrderInfo> LogicOrderInfos)
        {
            var rst = new TkUpdateShippedLogisticsResponseModel()
            {
                UpdateShippedLogisticsResultList = new List<TkUpdateShippedLogisticsResultModel>()
            };

            try
            {
                var logicOrderService = new LogicOrderService();
                var logicOrderItemService = new LogicOrderItemService();
                var sendHistoryService = new SendHistoryService();
                var orderSelfDeliveryService = new OrderSelfDeliveryService();
                var shopSercice = new ShopService();

                // 获取逻辑订单
                var logicOrderIds = LogicOrderInfos.Select(f => f.LogicOrderId).Distinct().ToList();
                var logicOrders = logicOrderService.GetOrders(logicOrderIds, false, null, new QueryReceiverModel() { IsOnlyGetMask = true });

                if (logicOrders == null || !logicOrders.Any())
                {
                    throw new LogicException("订单都不存在，请刷新页面后重新勾选");
                }

                var logicOrderDict = logicOrders.ToDictionary(f => f.LogicOrderId);
                var shopDict = (shopSercice.GetShopByIds(logicOrders.Select(l => l.ShopId).ToList())).ToDictionary(f => f.Id, f => f);

                // 预查询所有发货记录，避免重复查询
                var sendHistorys = sendHistoryService.GetSendHistoriesByOrderIds(logicOrderIds);
                var sendHistoryDict = sendHistorys
                    .Where(s => !string.IsNullOrEmpty(s.PackageId))
                    .ToDictionary(s => s.PackageId, s => s);

                var waitUpdateStatusLogicOrders = new ConcurrentBag<LogicOrder>();

                // 收集每个订单处理的 Task
                var tasks = new List<Task>();

                foreach (var LogicOrderInfo in LogicOrderInfos)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        if (!logicOrderDict.TryGetValue(LogicOrderInfo.LogicOrderId, out var lo))
                        {
                            lock (rst.UpdateShippedLogisticsResultList)
                            {
                                rst.UpdateShippedLogisticsResultList.Add(new TkUpdateShippedLogisticsResultModel()
                                {
                                    LogicOrderId = LogicOrderInfo.LogicOrderId,
                                    PlatformOrderId = lo.PlatformOrderId,
                                    ErrMsg = "订单不存在",
                                    Success = false
                                });
                            }
                            return;
                        }

                        if (!shopDict.TryGetValue(lo.ShopId, out var shop))
                        {
                            lock (rst.UpdateShippedLogisticsResultList)
                            {
                                rst.UpdateShippedLogisticsResultList.Add(new TkUpdateShippedLogisticsResultModel()
                                {
                                    LogicOrderId = lo.LogicOrderId,
                                    PlatformOrderId = lo.PlatformOrderId,
                                    ErrMsg = "订单对应的店铺不存在",
                                    Success = false
                                });
                            }
                            return;
                        }

                        if (string.IsNullOrEmpty(LogicOrderInfo.ExpressName) || string.IsNullOrEmpty(LogicOrderInfo.WaybillCode))
                        {
                            lock (rst.UpdateShippedLogisticsResultList)
                            {
                                rst.UpdateShippedLogisticsResultList.Add(new TkUpdateShippedLogisticsResultModel()
                                {
                                    LogicOrderId = lo.LogicOrderId,
                                    PlatformOrderId = lo.PlatformOrderId,
                                    ErrMsg = "物流公司或单号为空",
                                    Success = false
                                });
                            }
                            return;
                        }

                        var server = PlatformFactory.GetPlatformService(shop) as TikTokPlatformService;
                        // 异步调用更新物流信息
                        var us = server.UpdateShippingInfo(lo.PlatformOrderId, LogicOrderInfo.WaybillCode, LogicOrderInfo.ShippingProviderId);

                        if (us != null && us.Item1)
                        {
                            DateTime now = DateTime.Now;
                            var batchNo = (LogicOrderInfo.ExpressName + LogicOrderInfo.WaybillCode + now.ToString("yyyyMMddHHmmssfff")).ToShortMd5();
                            var waitInsertOrderSelfDelivery = new OrderSelfDelivery()
                            {
                                BatchNo = batchNo,
                                CreateTime = now,
                                ExpressName = LogicOrderInfo.ExpressName,
                                WaybillCode = LogicOrderInfo.WaybillCode,
                                Status = 1,
                                UpdateTime = now
                            };

                            orderSelfDeliveryService.Add(waitInsertOrderSelfDelivery);

                            // 收集内部异步任务：更新每个包裹对应的发货记录
                            var packageIds = lo.LogicOrderItems
                                .Where(f => !string.IsNullOrEmpty(f.PackageId))
                                .Select(f => f.PackageId)
                                .Distinct();

                            var innerTasks = new List<Task>();
                            foreach (var packageId in packageIds)
                            {
                                if (sendHistoryDict.TryGetValue(packageId, out var sendHistory) && sendHistory.CollectionType.Equals("SELF_DELIVERY"))
                                {
                                    innerTasks.Add(Task.Run(() => sendHistoryService.UpdateOrderSelfDeliveryBatchNo(batchNo, sendHistory.ID)));
                                }
                            }
                            await Task.WhenAll(innerTasks);

                            lo.OrderSelfDeliveryBatchNo = batchNo;
                            waitUpdateStatusLogicOrders.Add(lo);
                        }
                        else
                        {
                            // 如果更新整体物流信息失败，则尝试逐包更新
                            var logicOrderItems = logicOrderItemService.GetListForDuplication(new List<string>() { lo.LogicOrderId });
                            var errors = new ConcurrentBag<string>();
                            Parallel.ForEach(logicOrderItems, li =>
                            {
                                var ups = server.UpdatePackageShippingInfo(li.PackageId, LogicOrderInfo.WaybillCode, LogicOrderInfo.ShippingProviderId);
                                if (ups == null || !ups.Item1)
                                {
                                    errors.Add(ups.Item2);
                                }
                            });

                            if (!errors.IsNotNullAndAny())
                            {
                                DateTime now = DateTime.Now;
                                var batchNo = (LogicOrderInfo.ExpressName + LogicOrderInfo.WaybillCode + now.ToString("yyyyMMddHHmmssfff")).ToShortMd5();
                                var waitInsertOrderSelfDelivery = new OrderSelfDelivery()
                                {
                                    BatchNo = batchNo,
                                    CreateTime = now,
                                    ExpressName = LogicOrderInfo.ExpressName,
                                    WaybillCode = LogicOrderInfo.WaybillCode,
                                    Status = 1,
                                    UpdateTime = now
                                };

                                orderSelfDeliveryService.Add(waitInsertOrderSelfDelivery);

                                var packageIds = lo.LogicOrderItems
                                    .Where(f => !string.IsNullOrEmpty(f.PackageId))
                                    .Select(f => f.PackageId)
                                    .Distinct();

                                var innerTasks = new ConcurrentBag<Task<int>>();
                                Parallel.ForEach(packageIds, packageId =>
                                {
                                    if (sendHistoryDict.TryGetValue(packageId, out var sendHistory) &&
                                        sendHistory.CollectionType.Equals("SELF_DELIVERY"))
                                    {
                                        innerTasks.Add(Task.Run(() => sendHistoryService.UpdateOrderSelfDeliveryBatchNo(batchNo, sendHistory.ID)));
                                    }
                                });
                                await Task.WhenAll(innerTasks.ToArray());


                                lo.OrderSelfDeliveryBatchNo = batchNo;
                                waitUpdateStatusLogicOrders.Add(lo);
                            }
                            else
                            {
                                lock (rst.UpdateShippedLogisticsResultList)
                                {
                                    rst.UpdateShippedLogisticsResultList.Add(new TkUpdateShippedLogisticsResultModel()
                                    {
                                        LogicOrderId = lo.LogicOrderId,
                                        PlatformOrderId = lo.PlatformOrderId,
                                        ErrMsg = errors.FirstOrDefault(),
                                        Success = false
                                    });
                                }
                            }
                        }
                    }));
                }

                await Task.WhenAll(tasks);
                if (waitUpdateStatusLogicOrders.IsNotNullAndAny())
                {
                    logicOrderService.UpdateLogisticStatus(waitUpdateStatusLogicOrders.ToList());
                }
                return rst;
            }
            catch (Exception)
            {
                throw new Exception("遇到错误，更新失败");
            }
        }

        #endregion

        #region 移入已交运老版本代码
        /// <summary>
        /// TK订单移入已交运老版本代码
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        public TkOrderSendResponseModel TkOrderSendOld(TkOrderSendRequestModel model)
        {
            return new TkOrderSendResponseModel();
            /*var rst = new TkOrderSendResponseModel()
            {
                ExpressCode = model.ExpressCode,
                ExpressName = model.ExpressName,
                WaybillCode = model.WaybillCode,
                SendType = model.SendType,
                OrderSendResultList = new List<TkOrderSendResultModel>()
            };


            try
            {
                //获取逻辑订单
                var logicOrderIds = model.LogicOrderInfos.Select(f => f.LogicOrderId).Distinct().ToList();
                var logicOrderService = new LogicOrderService();
                var logicOrders = logicOrderService.GetOrders(logicOrderIds, false, null, new QueryReceiverModel() { IsOnlyGetMask = true });
                var logicOrderDict = logicOrders.ToLookup(f => f.LogicOrderId, f => f).ToDictionary(f => f.Key, f => f.FirstOrDefault());

                if (logicOrders == null || !logicOrders.Any())
                    throw new LogicException("订单不存在，请刷新页面后重新勾选");

                ///获取SKU相关信息
                var skuCodes = logicOrders
                               .SelectMany(x => x.LogicOrderItems)
                               .Select(y => y.SkuCode)
                               .ToList();
                var productFx = new ProductFxService().GetBySkuCodes(skuCodes);
                var dicSku = productFx
                          .SelectMany(x => x.Skus)
                          .ToDictionary(sk => sk.SkuCode, sk => sk);

                //循环处理
                var waitUpdateLogisticStatus = new List<LogicOrder>(); //待更新logisticStatus 的逻辑单
                var waitInsertSendHistory = new List<SendHistory>(); //待插入的发货记录
                OrderSelfDelivery waitInsertOrderSelfDelivery = null; //待保存的自寄物流信息
                if (model.SendType == 2)
                {
                    var now = DateTime.Now;
                    //自寄物流信息
                    waitInsertOrderSelfDelivery = new OrderSelfDelivery()
                    {
                        //LogicOrderId=lo.LogicOrderId,
                        //PlatformOrderId =lo.PlatformOrderId,
                        BatchNo = (model.ExpressCode + model.ExpressName + model.WaybillCode + now.ToString("yyyyMMddHHmmssfff")).ToShortMd5(),
                        CreateTime = now,
                        ExpressCpCode = model.ExpressCode,
                        ExpressName = model.ExpressName,
                        WaybillCode = model.WaybillCode,
                        Status = 1,
                        UpdateTime = now
                    };
                }
                foreach (var logicOrderInfo in model.LogicOrderInfos)
                {
                    var logicOrderId = logicOrderInfo.LogicOrderId;

                    LogicOrder lo = null;
                    logicOrderDict.TryGetValue(logicOrderId, out lo);

                    if (lo == null)
                    {
                        rst.OrderSendResultList.Add(new TkOrderSendResultModel()
                        {
                            LogicOrderId = logicOrderId,
                            ErrMsg = "订单不存在，请刷新后重新勾选",
                            Success = false,
                        });
                        continue;
                    }

                    //ID1033344 允许未打印订单移入已交运

                    //if (lo.PrintState == 0)
                    //{
                    //    rst.OrderSendResultList.Add(new TkOrderSendResultModel()
                    //    {
                    //        LogicOrderId = logicOrderId,
                    //        ErrMsg = "订单订单未打印快递面单，不能移入已交运",
                    //        Success = false,
                    //    });
                    //    continue;
                    //}

                    if (lo.LogisticStatus == 2)
                    {
                        rst.OrderSendResultList.Add(new TkOrderSendResultModel()
                        {
                            LogicOrderId = logicOrderId,
                            ErrMsg = "订单已交运，不能重复移入已交运",
                            Success = false,
                        });
                        continue;
                    }

                    //生成发货记录
                    var shs = CreateSendHistory(lo, logicOrderInfo, waitInsertOrderSelfDelivery?.BatchNo, model.SendType, dicSku);
                    if (!shs.Any())
                    {
                        rst.OrderSendResultList.Add(new TkOrderSendResultModel()
                        {
                            LogicOrderId = logicOrderId,
                            ErrMsg = "订单包裹信息解析失败",
                            Success = false,
                        });
                        continue;
                    }
                    waitInsertSendHistory.AddRange(shs);

                    lo.LogisticStatus = 2; //赋值已交运

                    lo.OrderSelfDeliveryBatchNo = waitInsertOrderSelfDelivery?.BatchNo; //赋值已交运

                    var sh = shs.Where(s => (!string.IsNullOrEmpty(s.OrderId)) && s.OrderId.Equals(lo.LogicOrderId)).FirstOrDefault();
                    if (sh != null)
                    {
                        lo.ShipmentDate = sh.SendDate;
                    }
                    //加入待更新的逻辑单列表
                    waitUpdateLogisticStatus.Add(lo);


                    //合单需更新子逻辑单相关数据
                    if (lo.IsMainOrder)
                    {
                        var selectedLogicOrderIds = lo.LogicOrderItems.Select(oi => string.IsNullOrEmpty(oi.OrignalOrderId) ? oi.LogicOrderId : oi.OrignalOrderId).Distinct().ToList();
                        var logicsCombineds = logicOrderService.GetOrders(selectedLogicOrderIds, false, null, new QueryReceiverModel() { IsOnlyGetMask = true });
                        foreach (var item in logicsCombineds)
                        {
                            item.OrderSelfDeliveryBatchNo = waitInsertOrderSelfDelivery?.BatchNo; //赋值已交运
                            item.LogisticStatus = 2;
                            item.ShipmentDate = sh.SendDate;
                            waitUpdateLogisticStatus.Add(item);
                        }
                    }
                    rst.OrderSendResultList.Add(new TkOrderSendResultModel()
                    {
                        LogicOrderId = logicOrderId,
                        Success = true,
                    });
                }

                if (waitInsertOrderSelfDelivery != null)
                {
                    //保存自发货记录
                    new OrderSelfDeliveryService().Add(waitInsertOrderSelfDelivery);
                }
                //保存发货记录
                new SendHistoryService().Add(waitInsertSendHistory);

                ///更新底单发货时间
                new WaybillCodeService().UpdateWaybillCodeSendDateByOrderIds(logicOrderIds, DateTime.Now);
                #region 绑定结算价商品关系
                ///绑定结算价商品关系
                var dictSettlementProductSku = new Dictionary<string, SettlementProductSku>();
                foreach (var sh in waitInsertSendHistory)
                {
                    foreach (var sop in sh.Orders.SelectMany(order => order.Products))
                    {
                        var key = sop.SkuCode + sh.PathFlowCode + sop.OrderItemCode;
                        if (!dictSettlementProductSku.TryGetValue(key, out var existingSku))
                        {
                            existingSku = new SettlementProductSku
                            {
                                ProductCode = sop.ProductCode,
                                SkuCode = sop.SkuCode,
                                PathFlowCode = sh.PathFlowCode,
                                SourceShopId = sh.SourceShopId,
                                SourceUserId = sh.SourceUserId,
                                FxUserId = SiteContext.Current.CurrentFxUserId,
                                LastFxUserId = sh.UpFxUserId,
                                CreateTime = DateTime.Now,
                                Status = 0,
                                OrderItemCode = sop.OrderItemCode
                            };
                            dictSettlementProductSku[key] = existingSku;
                        }
                    }
                }

                ///保存结算价关系
                new SettlementProductSkuService().AddSettlementProductSku(dictSettlementProductSku.Values.ToList());

                #endregion
                //更新订单数据
                if (waitUpdateLogisticStatus.Any())
                {
                    var loId = waitUpdateLogisticStatus.SelectMany(x => x.LogicOrderItems).Select(x => x.LogicOrderId);

                    //更新订单的交运状态
                    new LogicOrderService().UpdateLogisticStatus(waitUpdateLogisticStatus);
                }
            }
            catch (Exception ex)
            {
                Log.WriteError(ex.ToString());
                throw new LogicException("程序异常，请稍后重试");
                //if (ex is LogicException)
                //    return FalidResult(ex.Message);
                //else if (CustomerConfig.IsDebug)
                //    return FalidResult(ex.Message);
                //else
                //    return FalidResult("程序异常，请稍后重试");
            }

            return rst;*/
        }
        #endregion

        /// <summary>
        /// TK 合单校验
        /// </summary>
        /// <param name="orders"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        public Tuple<bool, string> TkMergerOrderVerify(List<LogicOrder> orders)
        {
            //1.校验 orders店铺是否是同一个店铺站点
            bool hasIdenticalShop = orders.GroupBy(x => x.ShopId).Any(g => g.Count() > 1);
            if (!hasIdenticalShop)
                return Tuple.Create(false, "勾选的订单店铺或平台不一致，无法通过平台进行校验");

            //2.校验 订单状态、打印状态
            if (orders.Any(x => x.LastWaybillCode.IsNotNullOrEmpty()))
                return Tuple.Create(false, "存在订单已生成快递单号，不支持合并订单");
            //3.需校验是否完整订单
            var groupedOrders = orders.GroupBy(o => o.PlatformOrderId).ToList();
            foreach (var group in groupedOrders)
            {
                // 获取该组的第一个订单，用来对比POrderItemCount
                var firstOrder = group.First();

                // 汇总该组所有子订单中的LogicOrderItems的数量
                int totalLogicOrderItemsCount = group.Sum(order => order.LogicOrderItems.Count);

                //逻辑单是整单拆包，且勾选的全部商品打印，无需拆包
                // 检查POrderItemCount是否等于汇总后的LogicOrderItems的数量
                if (!firstOrder.IsMainOrder && firstOrder.POrderItemCount != totalLogicOrderItemsCount)
                {
                    return Tuple.Create(false, $"订单{group.Key}，已进行内部订单拆单，不支持不同订单子项合并");
                }
            }
            //4.需校验是否存在其他合单
            var mainOrders = orders.Where(x => x.IsMainOrder);
            //自动合并的主订单只能有一个，多于一个则有问题
            if (mainOrders?.Count() > 1)
                throw new LogicException("您勾选的订单中有多个合并订单，合并订单不能互相合并，您可以先对其中一个合并订单进行拆单，然后再进行合并操作。");

            //5.如果没有重复项，取列表中的第一个元素
            var firstShopId = orders.FirstOrDefault().ShopId;
            var shop = new ShopService().Get(firstShopId);
            if (shop == null)
                return Tuple.Create(false, string.Empty);

            var orderIds = orders.Select(x => x.PlatformOrderId).Distinct().ToList();
            //相同订单不需要搜索可合包
            if (orderIds.Count == 1)
                return Tuple.Create(true, "");
            //6.调平台搜搜可合包
            var tuple = SearchTikCombinablePackages(shop, orderIds);
            return Tuple.Create(tuple.Item1, tuple.Item2);

        }

        /// <summary>
        /// 根据传入的订单搜索可合包数据
        /// </summary>
        /// <param name="orderIds"></param>
        /// <returns></returns>
        public Tuple<bool, string> SearchTikCombinablePackages(Shop shop, List<string> orders)
        {
            if (orders == null || !orders.Any())
                Tuple.Create<bool, string>(true, null);
            var tkPlatformService = PlatformFactory.GetPlatformService(shop) as TikTokPlatformService;
            string nextPageToken = null;
            List<TKCombinablePackageModel> list = new List<TKCombinablePackageModel>();
            do
            {
                try
                {
                    //循环获取当前店铺可组合的包
                    var tuple = tkPlatformService.SearchCombinablePackages(nextPageToken, 30);
                    //
                    if (!tuple.Item1)
                        break;
                    if (tuple.Item3 != null && tuple.Item3.combinable_packages == null)
                    {
                        ///数据取完了
                        break;
                    }
                    if (tuple.Item3 != null && tuple.Item3.combinable_packages.IsNotNullAndAny())
                    {
                        list.AddRange(tuple.Item3.combinable_packages);
                    }
                    // 更新 next_page_token，用于下一次调用
                    nextPageToken = tuple.Item3.next_page_token;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"店铺{shop.Id}:SearchCombinablePackages异常{ex}");
                    break;
                }

            } while (!string.IsNullOrEmpty(nextPageToken));
            //该店铺不存在可组合的包
            if (!list.Any())
                return Tuple.Create(false, "平台校验，勾选的订单未找到可组合的包裹");

            // 判断 orders 是否全部存在于同一个 TKCombinablePackageModel 的 order_ids 中
            foreach (var package in list)
            {
                if (package.order_ids != null && orders.All(orderId => package.order_ids.Contains(orderId)))
                {
                    // 如果所有订单ID都在这个包模型中找到，则返回成功
                    return Tuple.Create(true, package.id);
                }
            }
            return Tuple.Create(false, "平台校验，勾选的订单未找到可组合的包裹");

        }

        /// <summary>
        /// 三方物流获取运输供应商接口
        /// </summary>
        /// <param name="logicOrderIds"></param>
        /// <returns></returns>
        public Tuple<bool, string, List<TkShippingProviderResModel>> GetShippingProviders(List<string> logicOrderIds)
        {
            try
            {
                List<TkShippingProviderResModel> result = new List<TkShippingProviderResModel>();
                //获取订单相关
                OrderService orderService = new OrderService();
                LogicOrderService _orderService = new LogicOrderService();
                var logicOrders = _orderService.GetOrders(logicOrderIds);
                if (!logicOrders.IsNotNullAndAny())
                {
                    return new Tuple<bool, string, List<TkShippingProviderResModel>>(false, "传入的逻辑单号搜索不到逻辑单", null);
                }
                if (logicOrders.Count != logicOrderIds.Count)
                {
                    return new Tuple<bool, string, List<TkShippingProviderResModel>>(false, "传入的逻辑单号有的不存在逻辑单", null);
                }

                // 获取所有相关ShopId
                var shopIds = logicOrders.Select(l => l.ShopId).Distinct().ToList();
                var shops = new ShopService().GetShopByIds(shopIds);
                var shopMap = shops.ToDictionary(s => s.Id);
                var group = logicOrders.GroupBy(l => l.DeliveryOptionId);
                //目前发现这个条件下的DeliveryOptionId都一样
                foreach (var g in group)
                {
                    List<TkShippingProviders> tksp = null;
                    foreach (var logicOrder in g)
                    {
                        try
                        {
                            if (shopMap.TryGetValue(logicOrder.ShopId, out var shop))
                            {
                                var server = PlatformFactory.GetPlatformService(shop) as TikTokPlatformService;
                                //只有第三方物流走这
                                if (string.IsNullOrEmpty(logicOrder.ShippingType))
                                {
                                    List<OrderSelectKeyModel> models = new List<OrderSelectKeyModel>() {
                                    new OrderSelectKeyModel{
                                        PlatformOrderId = logicOrder.PlatformOrderId,
                                        ShopId = logicOrder.ShopId
                                        }
                                    };

                                    var originOrder = orderService.GetOrders(models).First();
                                    if (originOrder != null)
                                    {
                                        logicOrder.ShippingType = originOrder.ShippingType;
                                        logicOrder.DeliveryOptionId = originOrder.DeliveryOptionId;
                                    }
                                }
                                if (!logicOrder.ShippingType.Equals("SELLER"))
                                {
                                    continue;
                                }
                                var DeliveryOptionId = logicOrder.DeliveryOptionId;
                                //必须保证有此值，没值不管
                                if (DeliveryOptionId == null)
                                {
                                    continue;
                                }
                                if (tksp.IsNotNullAndAny())
                                {
                                    TkShippingProviderResModel tkShippingProviderResModel = new TkShippingProviderResModel();
                                    tkShippingProviderResModel.logicOrderId = logicOrder.LogicOrderId;
                                    tkShippingProviderResModel.shipping_providers = tksp;
                                    result.Add(tkShippingProviderResModel);
                                }
                                else
                                {
                                    var sps = server.GetShippingProviders(DeliveryOptionId);
                                    if (sps.Item1 && sps.Item3 != null)
                                    {
                                        var shipping_providers = sps.Item3.shipping_providers;
                                        if (shipping_providers.IsNotNullAndAny())
                                        {
                                            TkShippingProviderResModel tkShippingProviderResModel = new TkShippingProviderResModel();
                                            tkShippingProviderResModel.logicOrderId = logicOrder.LogicOrderId;
                                            tkShippingProviderResModel.shipping_providers = shipping_providers;
                                            result.Add(tkShippingProviderResModel);
                                            if (!tksp.IsNotNullAndAny())
                                            {
                                                tksp = shipping_providers;
                                            }
                                        }
                                    }
                                }

                            }
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"逻辑单号【{logicOrder.LogicOrderId}】获取运输供应商失败");
                            continue;
                        }

                    }
                }
                return new Tuple<bool, string, List<TkShippingProviderResModel>>(true, string.Empty, result);
            }
            catch (Exception ex)
            {
                return new Tuple<bool, string, List<TkShippingProviderResModel>>(false, "获取运输供应商失败", null);
            }

        }

        #region 更新打印状态（支持冷，热分离）
        /// <summary>
        /// 更新打印状态（支持冷，热分离）
        /// </summary>
        /// <param name="updateOrderModels"></param>
        /// <returns></returns>
        private List<dynamic> UpdatePrintStatusByColdHotStorage(PrintHisotoryCallbackRequestModel updateOrderModels)
        {
            #region 更新打印状态
            //更新打印状态
            var updatePrintStatusResults = new List<dynamic>();
            var updateOrdersByDataFlag = updateOrderModels.Orders.GroupBy(m => m.DataFlag).ToList();
            updateOrdersByDataFlag.ForEach(grouping =>
            {
                //分组订单信息
                var ordersByGroup = grouping.ToList();
                //获取订单信息
                var orderRequests = ordersByGroup.Select(p => new OrderRequestModel
                { Id = p.Id, PlatformOrderId = p.PlatformOrderId, ShopId = p.ShopId }).ToList();
                //已选订单项
                var selectItems = new List<OrderItemRequest>();
                if (updateOrderModels.PrintType == 1)
                {
                    ordersByGroup.ForEach(o => { selectItems.AddRange(o.Items); });
                }

                //冷数据
                var isColdData = grouping.Key == 1;
                //订单信息
                var orderModels = new ColdOrderService(isColdData).GetOrders(orderRequests, updateOrderModels.IsCustomerOrder, isBaseProduceCombine: SiteContext.Current.BaseProductSetting.OrderCombine);
                //更新状态
                var updatePrintStatusResult =
                    new LogicOrderService().UpdatePrintStateByColdHotStorage(updateOrderModels, orderModels,
                        selectItems,
                        isColdData);
                if (updatePrintStatusResult != null && updatePrintStatusResult.Any())
                {
                    updatePrintStatusResults.AddRange(updatePrintStatusResult);
                }
            });
            #endregion

            return updatePrintStatusResults;
        }
        #endregion

        #region 公共辅助方法
        /// <summary>
        /// 检验打印订单合法性
        /// </summary>
        /// <param name="requestOrder"></param>
        /// <param name="orderDict"></param>
        /// <param name="rst"></param>
        /// <returns></returns>
        private bool ValidateOrder(TkPrintOrderModel requestOrder, IDictionary<string, LogicOrder> orderDict, TkOrderPrintResponseModel rst)
        {
            if (!orderDict.TryGetValue(requestOrder.LogicOrderId, out var logicOrder))
            {
                requestOrder.SetError("订单不存在");
                rst.SetError($"无效订单ID: {requestOrder.LogicOrderId}");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 解析出合并的订单项的的所有SubItemId
        /// </summary>
        /// <param name="printOis"></param>
        /// <returns></returns>
        List<string> GetSubItemIds(List<LogicOrderItem> printOis)
        {
            //解析出合并的订单项的的所有SubItemId
            var subItemIds = printOis.Select(f => f.SubItemId).ToList();
            if (printOis.Any(f => f.MergeItemJSON.IsNotNullOrEmpty()))
            {
                subItemIds = new List<string>();
                foreach (var oi in printOis)
                {
                    if (oi.ItemCount > 0 && oi.MergeItemJSON.IsNotNullOrEmpty())
                    {
                        //解析出所有合并的所有订单项
                        var ois = OrderItem.MergeOiJsonToOis(oi.MergeItemJSON);
                        subItemIds.AddRange(ois.Select(f => f.SubItemID));
                    }
                    else
                        subItemIds.Add(oi.SubItemId);
                }
                subItemIds = subItemIds.Distinct().ToList();
            }
            return subItemIds;
        }

        /// <summary>
        /// 是否是合单
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        private bool IsMergedOrder(LogicOrder order) => order.IsMainOrder && (order.MergeredType == 4 || order.MergeredType == 5);

        /// <summary>
        /// 打单发货设置
        /// </summary>
        /// <returns></returns>
        private string GetTikTokShipSetting()
        {
            return new CommonSettingRepository().Get(
                "/FenDan/TikTok/TikTokShippingSetting",
                BaseSiteContext.Current.CurrentShopId
            )?.Value ?? "2";
        }

        /// <summary>
        /// TK东南亚站点店铺类型
        /// </summary>
        private List<string> _SoutheastAsiaStoreCache = new List<string> { "MY", "VN", "ID", "TH", "SG", "PH" };
        /// <summary>
        /// 判断是否需要拆包
        /// </summary>
        /// <param name="logicOrder"></param>
        /// <param name="selectedItems"></param>
        /// <returns></returns>
        private bool CanSkipSplitting(LogicOrder logicOrder, List<LogicOrderItem> selectedItems, Dictionary<int, Shop> shopDict)
        {

            //通过平台订单直接获取
            LogicOrderItemQueryModel queryModel = new LogicOrderItemQueryModel()
            {
                ShopId = logicOrder.ShopId,
                Pid = new List<string> { logicOrder.PlatformOrderId }
            };
            var allOrderItems = _logicOrderService.GetLogicOrderItemList(queryModel);
            var allItemDic = allOrderItems
                .GroupBy(item => item.PlatformOrderId)
                .ToDictionary(x => x.Key, y => y.ToList());
            bool isUnpack = false;
            //美国本土不拆包
            Shop shop = null;
            if (shopDict.TryGetValue(logicOrder.ShopId, out shop))
            {
                //美国本土不拆包
                if (!string.IsNullOrEmpty(shop.SubPlatformType) && shop.SubPlatformType.Equals("US") && !string.IsNullOrEmpty(shop.RoleType) && shop.RoleType.Equals("LOCAL"))
                {
                    isUnpack = true;
                    return isUnpack;
                }
                bool isSoutheastAsiaStore = _SoutheastAsiaStoreCache.Contains(shop.SubPlatformType);
                //非东南亚 存在已拆包并且已打印 不需要在进行拆包
                if (!isSoutheastAsiaStore && allOrderItems.Any(x => x.PrintState == 1 && x.TracakingNumber.IsNotNullOrEmpty()))
                {
                    isUnpack = true;
                    return isUnpack;
                }
            }
            //逻辑单是整单拆包，且勾选的全部商品打印，无需拆包
            bool isComplete = logicOrder.POrderItemCount == logicOrder.LogicOrderItems.Count//完整订单
                             && selectedItems.Count == logicOrder.LogicOrderItems.Count///全部勾选
                             && selectedItems.GroupBy(x => logicOrder.LogicOrderItems.First(i => i.SubItemId == x.SubItemId)?.PackageId).Count() <= 1;// 并且没有拆过包

            //// 合单不拆分
            //bool isMerged = logicOrder.IsMainOrder && (logicOrder.MergeredType == 4 || logicOrder.MergeredType == 5);

            return isComplete || isUnpack /*|| isMerged*/;
        }

        /// <summary>
        /// TikTok下载打印PDF链接转Base64字符串
        /// </summary>
        /// <param name="keyValuePairs">包含PDF URL和对应Base64字符串的并发字典</param>
        /// <param name="maxRetries">最大重试次数，默认3次</param>
        /// <param name="retryDelay">重试延迟时间，默认1毫秒</param>
        private static async Task DownloadPdfAndConvertToBase64Async(ConcurrentDictionary<string, string> keyValuePairs, int maxRetries = 2, TimeSpan retryDelay = default)
        {
            if (keyValuePairs == null || !keyValuePairs.Any())
                return;
            if (retryDelay == default)
                retryDelay = TimeSpan.FromSeconds(1);
            var semaphore = new SemaphoreSlim(5);
            var tasks = new List<Task>();
            foreach (var pair in keyValuePairs)
            {
                await semaphore.WaitAsync();
                tasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        string key = pair.Key;
                        string base64String = null;
                        for (int attempt = 0; attempt < maxRetries; attempt++)
                        {
                            try
                            {
                                var fileBytes = await WebRequestHelper.GetPdfAsBytesWithRetryAsync(key, attempt, 3);
                                if (fileBytes != null && fileBytes.Length > 0)
                                {
                                    base64String = Convert.ToBase64String(fileBytes);
                                    if (!string.IsNullOrEmpty(base64String))
                                    {
                                        keyValuePairs[key] = base64String;
                                        break;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                keyValuePairs[key] = string.Empty;
                            }
                            if (attempt < maxRetries - 1)
                                await Task.Delay(retryDelay);
                        }
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }));
            }
            await Task.WhenAll(tasks);
        }




        /// <summary>
        /// 获取打印批次
        /// </summary>
        /// <param name="printBatchNumber"></param>
        /// <returns></returns>
        private string GetPrintBatchNumber(string printBatchNumber)
        {
            var array = printBatchNumber?.Split("/".ToArray());
            var first = array?.First();
            var last = array?.Last() ?? printBatchNumber;
            if (first == "ScanPrint" || first == "FreeSingle")
            {
                var dayPrintBatchNumber = new CommonSettingService().GetDayPrintBatchNumber(SiteContext.Current.CurrentShopId);
                return DateTime.Now.ToString("yyyyMMddHHmmss") + dayPrintBatchNumber;
            }
            return last;
        }

        /// <summary>
        /// 获取打印次序
        /// </summary>
        /// <param name="printBatchNumber"></param>
        /// <returns></returns>
        private int GetPrintBatchIndex(string printBatchNumber)
        {

            var array = printBatchNumber?.Split("/".ToArray());
            int first = array.First().ToInt();
            return first;
        }

        #endregion

    }
}
