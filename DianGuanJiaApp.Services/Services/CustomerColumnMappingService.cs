using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services
{
    public partial class CustomerColumnMappingService : BaseService<Data.Entity.CustomerColumnMapping>
    {
        private CustomerColumnMappingRepository _repository = new CustomerColumnMappingRepository();

        /// <summary>
        /// 获取店铺导入字段映射
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public CustomerColumnMapping GetColumnMapping(int shopId)
        {
            return _repository.GetColumnMapping(shopId);
        }

        /// <summary>
        /// 更新字段映射配置信息
        /// </summary>
        /// <param name="shopId"></param>
        /// <param name="mappingSet"></param>
        /// <returns></returns>
        public int UpdateColumnMapping(int shopId, string mappingSet)
        {
            return _repository.UpdateColumnMapping(shopId, mappingSet);
        }
    }
}
