using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Services.Services.DataArchive
{
    public class DataArchiveBaseService : ExceptionHandler
    {
        private readonly CommonSettingService _commonSettingService;
        public DataArchiveBaseService()
        {
            _commonSettingService = new CommonSettingService();
        }
        /// <summary>
        /// 获取配置库数据库链接
        /// </summary>
        /// <returns></returns>
        protected IDbConnection GetConfigureDbConnection()
        {
            return DbUtility.GetConnection(CustomerConfig.ConfigureDbConnectionString);
        }

        /// <summary>
        /// 获取配置库归档数据库链接
        /// </summary>
        /// <returns></returns>
        protected IDbConnection GetConfigureArchiveDbConnection()
        {
            return DbUtility.GetConnection(_commonSettingService.ConfigureArchiveDbConnectionString);
        }
        
        /// <summary>
        /// 是否能够执行归档
        /// </summary>
        /// <param name="beginTimeHour"></param>
        /// <param name="endTimeHour"></param>
        /// <returns></returns>
        public bool IsExecuteArchive(string beginTimeHour, string endTimeHour)
        {
            beginTimeHour = string.IsNullOrWhiteSpace(beginTimeHour) ? "00:00" : beginTimeHour;
            endTimeHour = string.IsNullOrWhiteSpace(endTimeHour) ? "05:00" : endTimeHour;

            var beginTimeConfig = $"2000-01-01 {beginTimeHour}".toDateTime();
            var endTimeConfig = $"2000-01-01 {endTimeHour}".toDateTime();

            var baseDateTime = new DateTime(2000, 1, 1, DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second);
            var startTime = new DateTime(2000, 1, 1, beginTimeConfig.Hour, beginTimeConfig.Minute, 0);
            var endTime = new DateTime(2000, 1, 1, endTimeConfig.Hour, endTimeConfig.Minute, 59);

            return baseDateTime >= startTime && baseDateTime <= endTime;
        }

        /// <summary>
        /// 批量插入（SqlBulkCopy）
        /// </summary>
        /// <param name="db"></param>
        /// <param name="dataReader"></param>
        /// <param name="tableName"></param>
        /// <param name="options"></param>
        /// <param name="batchSize"></param>
        protected void SqlBulkCopy(IDbConnection db, IDataReader dataReader, string tableName,
            SqlBulkCopyOptions options = SqlBulkCopyOptions.TableLock,
            int batchSize = 1000)
        {
            //判空处理
            if (dataReader == null)
            {
                return;
            }
            
            //集合
            using (dataReader)
            {
                if (dataReader.Read() == false)
                {
                    return;
                }

                using (var sqlBulkCopy = new SqlBulkCopy(db.ConnectionString, options))
                {
                    //批次行数
                    sqlBulkCopy.BatchSize = batchSize;
                    //开启流
                    sqlBulkCopy.EnableStreaming = true;
                    //超时时间
                    sqlBulkCopy.BulkCopyTimeout = 60;
                    //表名称
                    sqlBulkCopy.DestinationTableName = tableName;
                    //字段对应关系
                    for (var i = 0; i < dataReader.FieldCount; i++)
                    {
                        var fieldName = dataReader.GetName(i);
                        sqlBulkCopy.ColumnMappings.Add(fieldName, fieldName);
                    }

                    //写入
                    sqlBulkCopy.WriteToServer(dataReader);
                    //关闭
                    sqlBulkCopy.Close();
                }
            }
        }

        /// <summary>
        /// 批量插入（SqlBulkCopy）
        /// </summary>
        /// <param name="db"></param>
        /// <param name="dataTable"></param>
        /// <param name="tableName"></param>
        /// <param name="options"></param>
        /// <param name="batchSize"></param>
        protected void SqlBulkCopy(IDbConnection db, DataTable dataTable, string tableName,
            SqlBulkCopyOptions options = SqlBulkCopyOptions.TableLock, int batchSize = 1000)
        {
            //表信息
            if (dataTable == null || dataTable.Rows.Count == 0)
            {
                return;
            }

            //SQL批量插入
            using (var sqlBulkCopy = new SqlBulkCopy(db.ConnectionString, options))
            {
                //批次行数
                sqlBulkCopy.BatchSize = batchSize;
                //开启流
                sqlBulkCopy.EnableStreaming = true;
                //超时时间
                sqlBulkCopy.BulkCopyTimeout = 60;
                //表名称
                sqlBulkCopy.DestinationTableName = tableName;
                //字段对应关系
                foreach (DataColumn dc in dataTable.Columns)
                {
                    sqlBulkCopy.ColumnMappings.Add(dc.ColumnName, dc.ColumnName);
                }

                //写入
                sqlBulkCopy.WriteToServer(dataTable);
                //关闭
                sqlBulkCopy.Close();
            }
        }

        /// <summary>
        /// 备份归档数据
        /// </summary>
        /// <param name="models"></param>
        /// <param name="tableName"></param>
        /// <typeparam name="TEntity"></typeparam>
        /// <exception cref="Exception"></exception>
        protected void BackupArchiveData<TEntity>(List<TEntity> models, string tableName)
        {
            //判空处理
            if (models == null || models.Any() == false)
            {
                return;
            }
            //备份归档数据
            var dataReadyByTaskParamJson = models.GetDataReader();
            using (var dba = GetConfigureArchiveDbConnection())
            {
                try
                {
                    //归档备份数据
                    SqlBulkCopy(dba, dataReadyByTaskParamJson, tableName);
                }
                catch (Exception e)
                {
                    Log.WriteError($"对{tableName}执行批量插入操作异常，异常原因：{e.Message}",
                        $"SqlBulkCopy_{DateTime.Now:yyyy-MM-dd}.log");
                    #region 强制释放DataReader

                    try
                    {
                        using (dataReadyByTaskParamJson)
                        {

                        }
                    }
                    catch (Exception exception)
                    {

                    }

                    #endregion

                    models.ForEach(model =>
                    {
                        try
                        {
                            dba.InsertWithLongId(model, isUseExtension: false, isIncludeIdProperty: true,
                                isIgnoreReturnId: true);
                        }
                        catch (Exception ex)
                        {
                            var errMsg = ex.Message.ToLower();
                            if (errMsg.Contains("duplicate key") || errMsg.Contains("pk_") ||
                                errMsg.Contains("primary key") || errMsg.Contains("重复键"))
                            {
                                //忽略
                            }
                            else
                            {
                                throw ex;
                            }
                        }
                    });
                }
            }
        }
        
        /// <summary>
        /// 遍历支持并发
        /// </summary>
        /// <param name="models"></param>
        /// <param name="action"></param>
        /// <param name="isParallel"></param>
        /// <param name="maxDegreeOfParallelism"></param>
        /// <typeparam name="T"></typeparam>
        protected void DoForEach<T>(List<T> models, Action<T> action, bool isParallel = false,
            int maxDegreeOfParallelism = 1)
        {
            //判空处理
            if (models == null || !models.Any())
            {
                return;
            }

            //遍历
            if (isParallel == false)
            {
                models.ForEach(action);
                return;
            }

            //并发遍历执行
            Parallel.ForEach(models, new ParallelOptions { MaxDegreeOfParallelism = maxDegreeOfParallelism }, action);
        }
    }
}
