using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace DianGuanJiaApp.Services.Services.DataDuplication
{
    public class ProductDuplicationService : BaseDuplicationService
    {
        public ProductDuplicationService()
        {
            
        }
        public ProductDuplicationService(string serviceNo, string cpt = "") : base(serviceNo, cpt)
        {
        }


        /// <summary>
        /// 复制副本
        /// </summary>
        /// <param name="message"></param>
        /// <returns></returns>
        public override ReturnedModel Duplication(DuplicationMessageModel message)
        {
            //锁和复制副本处理
            return LockWithDuplicationHandler(CacheKeys.ProductDuplicationLockKey,
                CacheKeys.ProductDuplicationQueueKey, message, DuplicationFunc);
        }

        /// <summary>
        /// 复制副本回调函数
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private ReturnedModel DuplicationFunc(Data.Entity.DataSyncStatus.DataSyncStatus model)
        {
            if (model.Mode == SyncMode.Full)
            {
                return DuplicationFull(model);
            }
            return DuplicationIncremental(model);
        }

        /// <summary>
        /// 复制副本(全量)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private ReturnedModel DuplicationFull(Data.Entity.DataSyncStatus.DataSyncStatus model)
        {  //测量耗时
            var stopwatch = Stopwatch.StartNew();
            //商家数据库配置信息
            var dbConfigModel = GetFxDbConfigByFxUserId(model.FxUserId);
            if (dbConfigModel == null)
            {
                return new ReturnedModel
                {
                    Status = ReturnedStatus.Warning,
                    Message = "获取所属商家数据库信息为空."
                };
            }
            //非新分库用户，不复制
            if (dbConfigModel.FromFxDbConfig == 0)
            {
                return new ReturnedModel
                {
                    Status = ReturnedStatus.Warning,
                    Message = $"用户：{model.FxUserId} 非新分库用户，不复制副本."
                };
            }
            //实例路径流服务类
            var dbConnectionString = dbConfigModel.ConnectionString;
            var productService = new ProductFxService(dbConnectionString);
            //var productInfoService = new ProductInfoFxService(dbConnectionString);
            var productSkuService = new ProductSkuFxService(dbConnectionString);
            //var productSkuInfoService = new ProductSkuInfoFxService(dbConnectionString);
            var pathFlowNodeService = new PathFlowNodeService(dbConnectionString);
            //复制副本条件
            var condition = new DuplicationConditionModel
            {
                FxUserId = model.FxUserId,
                ShopId = model.ShopId
            };
            //获取当时
            var nowTime = pathFlowNodeService.GetNowTime();
            //默认同源
            var isSameDataSource = true;
            //分页查询路径流信息
            const int pageSize = 100;
            int currentCount;
            //分别同步到各厂家库
            do
            {
                //商品信息
                var mainModels = productService.GetListForDuplication(condition, pageSize);
                //商品代码列表
                var productCodes = mainModels.Select(m => m.ProductCode).ToList();
                //商品简称信息
                //var productInfos = productInfoService.GetListForDuplication(productCodes);
                //商品规格信息
                var productSkus = productSkuService.GetListForDuplication(productCodes);
                //商品规格简称信息
                //var productSkuInfos = productSkuInfoService.GetListForDuplication(productCodes);
                //获取路径流节点上用户信息
                var pathFlowFxUserIdNodes = new List<PathFlowFxUserIdNodeModel>();
                //路径节点所有用户数据库配置信息
                var factoryDbConfigs = GetNeedDuplicationDbConfigs(dbConfigModel, true, productCodes,
                    ref pathFlowFxUserIdNodes, isUseInstanceCache: true, isInitFxDbConfig: true);
                //当前页长度
                currentCount = mainModels.Count;
                //设置同步情况相关字段
                model.LastTotals += currentCount;
                model.LastTotalPages += 1;
                //更新下一页条件
                condition.MaxId = currentCount == 0 ? 0 : mainModels.Last().Id;
                //是否需要复制副本
                var isDuplication = factoryDbConfigs.All(m => string.Equals(m.ConnectionString, dbConnectionString, StringComparison.OrdinalIgnoreCase));
                if (isDuplication)
                {
                    continue;
                }
                //不同源
                isSameDataSource = false;
                //需要复制副本厂家数据库
                var needDuplicationDbConfigs = GetNeedDuplicationDbConfigsByGroup(dbConfigModel, factoryDbConfigs);
                //返回值
                var returned = new ReturnedModel
                {
                    Status = ReturnedStatus.Success
                };
                //遍历复制副本
                needDuplicationDbConfigs.ForEach(dbConfig =>
                {
                    //厂家数据库连接字符串
                    var factoryConnectionString = dbConfig.Key;
                    //当前需要复制副本的用户ID
                    var fxUserIds = dbConfig.Select(m => m.DbConfig.UserId).Distinct().ToList();
                    //需要复制到厂家副本商品代码列表
                    var factoryProductCodes = pathFlowFxUserIdNodes.Where(m => fxUserIds.Contains(m.FxUserId))
                        .Select(m => m.ProductCode).Distinct().ToList();
                    //复制副本商品信息
                    var factoryProducts = mainModels.Where(m => factoryProductCodes.Contains(m.ProductCode)).ToList();
                    //复制副本商品简称信息
                    //var factoryProductInfos = productInfos.Where(m => factoryProductCodes.Contains(m.ProductCode)).ToList();
                    //复制副本商品规格信息
                    var factoryProductSkus = productSkus.Where(m => factoryProductCodes.Contains(m.ProductCode)).ToList();
                    //复制副本商品规格简称信息
                    //var factoryProductSkuInfos = productSkuInfos.Where(m => factoryProductCodes.Contains(m.ProductCode)).ToList();
                    //复制商品信息副本
                    ExceptionRetryHandler(() =>
                    {
                        var service = new ProductFxService(factoryConnectionString);
                        service.InsertsForDuplication(factoryProducts);
                        return returned;
                    });
                    //复制商品简称信息副本
                    //ExceptionRetryHandler(() =>
                    //{
                    //    var service = new ProductInfoFxService(factoryConnectionString);
                    //    service.InsertsForDuplication(factoryProductInfos);
                    //    return returned;
                    //});
                    //复制商品规格信息副本
                    ExceptionRetryHandler(() =>
                    {
                        var service = new ProductSkuFxService(factoryConnectionString);
                        service.InsertsForDuplication(factoryProductSkus);
                        return returned;
                    });
                    //复制商品规格简称信息副本
                    //ExceptionRetryHandler(() =>
                    //{
                    //    var service = new ProductSkuInfoFxService(factoryConnectionString);
                    //    service.InsertsForDuplication(factoryProductSkuInfos);
                    //    return returned;
                    //});
                    //辅助日志
                    WriteDataDuplicationLog(dbConfigModel, dbConfig.First(),
                        nameof(DuplicationFull), factoryProductCodes, model.Id, null, nowTime);
                });

            } while (currentCount == pageSize);
            //结束测量耗时
            stopwatch.Stop();
            model.LastDurations = stopwatch.ElapsedMilliseconds;
            //下一次最大ID
            model.NextMaxId = Convert.ToInt64(condition.MaxId);
            //是否同源
            model.IsSameDataSource = isSameDataSource;
            //同步模式是全量，全量完成后变增量
            if (model.Mode == SyncMode.Full)
            {
                model.Mode = SyncMode.Incremental;
            }
            //最后同步时间记录
            model.LastSyncBeginTime = condition.BeginTime;
            model.LastSyncTime = nowTime;
            return new ReturnedModel();
        }

        /// <summary>
        /// 复制副本(全量)
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private ReturnedModel DuplicationIncremental(Data.Entity.DataSyncStatus.DataSyncStatus model)
        {  //测量耗时
            var stopwatch = Stopwatch.StartNew();
            //商家数据库配置信息
            var dbConfigModel = GetFxDbConfigByFxUserId(model.FxUserId);
            if (dbConfigModel == null)
            {
                return new ReturnedModel
                {
                    Status = ReturnedStatus.Warning,
                    Message = "获取所属商家数据库信息为空."
                };
            }
            //非新分库用户，不复制
            if (dbConfigModel.FromFxDbConfig == 0)
            {
                return new ReturnedModel
                {
                    Status = ReturnedStatus.Warning,
                    Message = $"用户：{model.FxUserId} 非新分库用户，不复制副本."
                };
            }
            //实例路径流服务类
            var dbConnectionString = dbConfigModel.ConnectionString;
            var productService = new ProductFxService(dbConnectionString);
            //var productInfoService = new ProductInfoFxService(dbConnectionString);
            var productSkuService = new ProductSkuFxService(dbConnectionString);
            //var productSkuInfoService = new ProductSkuInfoFxService(dbConnectionString);
            //复制副本条件
            var condition = new DuplicationConditionModel
            {
                FxUserId = model.FxUserId,
                ShopId = model.ShopId,
                BeginTime = model.LastSyncTime,
                EndTime = model.EndTime
            };
            //默认同源
            var isSameDataSource = true;
            //分页查询路径流信息
            const int pageSize = 1000;
            int currentCount;
            //分别同步到各厂家库
            do
            {
                //查询变更日志
                var mainModels = ExceptionRetryHandler(() => DataChangeLog.Query(condition.FxUserId, condition.ShopId,
                    DataChangeTableTypeName.Product,
                    condition.BeginTime.Value, condition.EndTime.Value, pageSize, condition.MaxId ?? 0));
                //按状态分组
                var changeLogsGroup = mainModels.Select(m => new
                {
                    Group = m.DataChangeType == DataChangeTypeEnum.INSERT ? 0 : 1,
                    Item = m
                }).GroupBy(m => m.Group).ToList();
                //分组复制副本
                changeLogsGroup.ForEach(group =>
                {
                    //代码信息
                    var codes = group.Select(m => m.Item.RelationKey).Distinct().ToList();
                    //业务主信息
                    var groupMainModels = new List<ProductFx>();
                    switch (group.Key)
                    {
                        //复制所有字段
                        case 0:
                            groupMainModels = productService.GetListForDuplication(codes);
                            break;
                        //更新部分指定字段
                        case 1:
                            groupMainModels =
                                productService.GetListForDuplication(codes, SelectFieldName.ProductStatusNew);
                            break;
                    }
                    //商品代码列表
                    codes = groupMainModels.Select(m => m.ProductCode).Distinct().ToList();
                    //获取路径流节点上用户信息
                    var pathFlowFxUserIdNodes = new List<PathFlowFxUserIdNodeModel>();
                    //路径节点所有用户数据库配置信息
                    var factoryDbConfigs = GetNeedDuplicationDbConfigs(dbConfigModel, true, codes, ref pathFlowFxUserIdNodes, isUseInstanceCache: true, isInitFxDbConfig: true);
                    //是否需要复制副本
                    var isDuplication = factoryDbConfigs.All(m => string.Equals(m.ConnectionString, dbConnectionString, StringComparison.OrdinalIgnoreCase));
                    if (isDuplication)
                    {
                        return;
                    }
                    //不同源
                    isSameDataSource = false;
                    //需要复制副本厂家数据库
                    var needDuplicationDbConfigs = GetNeedDuplicationDbConfigsByGroup(dbConfigModel, factoryDbConfigs);
                    //返回值
                    var returned = new ReturnedModel
                    {
                        Status = ReturnedStatus.Success
                    };

                    //商品简称信息
                    //var productInfos = new List<ProductInfoFx>();
                    //商品规格信息
                    var productSkus = new List<ProductSkuFx>();
                    //商品规格简称信息
                    //var productSkuInfos = new List<ProductSkuInfoFx>();
                    if (group.Key == 0)
                    {
                        //商品简称信息
                        //productInfos = productInfoService.GetListForDuplication(codes);
                        //商品规格信息
                        productSkus = productSkuService.GetListForDuplication(codes);
                        //商品规格简称信息
                        //productSkuInfos = productSkuInfoService.GetListForDuplication(codes);
                    }
                    //遍历复制副本
                    needDuplicationDbConfigs.ForEach(dbConfig =>
                    {
                        //厂家数据库连接字符串
                        var factoryConnectionString = dbConfig.Key;
                        //当前需要复制副本的用户ID
                        var fxUserIds = dbConfig.Select(m => m.DbConfig.UserId).Distinct().ToList();
                        //需要复制到厂家副本商品代码列表
                        var factoryProductCodes = pathFlowFxUserIdNodes.Where(m => fxUserIds.Contains(m.FxUserId))
                            .Select(m => m.ProductCode).Distinct().ToList();
                        //复制副本商品信息
                        var factoryProducts = groupMainModels.Where(m => factoryProductCodes.Contains(m.ProductCode)).ToList();
                        switch (group.Key)
                        {
                            case 0:
                                //复制商品信息副本
                                ExceptionRetryHandler(() =>
                                {
                                    var service = new ProductFxService(factoryConnectionString);
                                    service.InsertsForDuplication(factoryProducts);
                                    return returned;
                                });
                                //复制副本商品简称信息
                                //var factoryProductInfos = productInfos.Where(m => factoryProductCodes.Contains(m.ProductCode)).ToList();
                                //复制商品简称信息副本
                                //ExceptionRetryHandler(() =>
                                //{
                                //    var service = new ProductInfoFxService(factoryConnectionString);
                                //    service.InsertsForDuplication(factoryProductInfos);
                                //    return returned;
                                //});
                                //复制副本商品规格信息
                                var factoryProductSkus = productSkus.Where(m => factoryProductCodes.Contains(m.ProductCode)).ToList();
                                //复制商品规格信息副本
                                ExceptionRetryHandler(() =>
                                {
                                    var service = new ProductSkuFxService(factoryConnectionString);
                                    service.InsertsForDuplication(factoryProductSkus);
                                    return returned;
                                });
                                //复制副本商品规格简称信息
                                //var factoryProductSkuInfos = productSkuInfos.Where(m => factoryProductCodes.Contains(m.ProductCode)).ToList();
                                //复制商品规格简称信息副本
                                //ExceptionRetryHandler(() =>
                                //{
                                //    var service = new ProductSkuInfoFxService(factoryConnectionString);
                                //    service.InsertsForDuplication(factoryProductSkuInfos);
                                //    return returned;
                                //});
                                break;
                            case 1:
                                //复制商品状态信息副本
                                ExceptionRetryHandler(() =>
                                {
                                    var service = new ProductFxService(factoryConnectionString);
                                    service.UpdatesForDuplication(factoryProducts);
                                    return returned;
                                });
                                break;
                        }
                        //辅助日志
                        WriteDataDuplicationLog(dbConfigModel, dbConfig.First(),
                            nameof(DuplicationIncremental), factoryProductCodes, model.Id, condition.BeginTime, condition.EndTime.Value);
                    });
                });
                //当前页长度
                currentCount = mainModels.Count;
                //设置同步情况相关字段
                model.LastTotals += currentCount;
                model.LastTotalPages += 1;
                //更新下一页条件
                condition.MaxId = currentCount == 0 ? 0 : mainModels.Last().Id;

            } while (currentCount == pageSize);
            //结束测量耗时
            stopwatch.Stop();
            model.LastDurations = stopwatch.ElapsedMilliseconds;
            //下一次最大ID
            model.NextMaxId = Convert.ToInt64(condition.MaxId);
            //是否同源
            model.IsSameDataSource = isSameDataSource;

            return new ReturnedModel();
        }
    }
}