using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Helpers;

namespace DianGuanJiaApp.Services.Services.DataEventTracking
{
    public abstract class BaseDataEventTrackingService
    {
        private readonly LogHelper _logHelper;
        private readonly CommonSettingService _commonSettingService;
        protected BaseDataEventTrackingService()
        {
            _logHelper = new LogHelper(Config.SlsProject, Config.SlsLogStoreByDataEventTracking);
            _commonSettingService = new CommonSettingService();
        }

        /// <summary>
        /// 添加埋点
        /// </summary>
        /// <param name="operationType"></param>
        /// <param name="trackingType"></param>
        /// <param name="baseModel"></param>
        /// <param name="collectionData"></param>
        /// <returns></returns>
        public virtual bool AddTracking<TCollectionData>(EventTrackingOperationType operationType,
            EventTrackingType trackingType,
            DataEventTrackingBaseModel baseModel, TCollectionData collectionData)
        {
            //是否开启数据埋点
            if (!IsEnabledDataEventTracking(baseModel.ShopId))
            {
                return true;
            }
            //最终数据结构
            var finalModel = BuildDataEventTrackingModel(operationType, trackingType, baseModel, collectionData);
            //推送埋点日志
            return _logHelper.WriteLog(finalModel);
        }

        /// <summary>
        /// 构建最终数据结构
        /// </summary>
        /// <typeparam name="TCollectionData"></typeparam>
        /// <param name="operationType"></param>
        /// <param name="trackingType"></param>
        /// <param name="baseModel"></param>
        /// <param name="collectionData"></param>
        /// <returns></returns>
        protected DataEventTrackingModel BuildDataEventTrackingModel<TCollectionData>(
            EventTrackingOperationType operationType,
            EventTrackingType trackingType,
            DataEventTrackingBaseModel baseModel, TCollectionData collectionData)
        {
            //最终数据结构
            var finalModel = new DataEventTrackingModel
            {
                HostName = Environment.MachineName,
                TrackingOperationType = operationType.ToString(),
                TrackingType = trackingType.ToString(),
                CloudPlatformType = CustomerConfig.CloudPlatformType,
                CollectionData = collectionData.ToJson(),
                CurrentFxUserId = baseModel.CurrentFxUserId,
                FxUserId = baseModel.FxUserId,
                ShopId = baseModel.ShopId,
                SellerId = baseModel.SellerId,
                PlatformType = baseModel.PlatformType,
                SyncStartTime = baseModel.SyncStartTime,
                SyncEndTime = baseModel.SyncEndTime
            };
            return finalModel;
        }

        /// <summary>
        /// 是否启动数据埋点
        /// </summary>
        /// <returns></returns>
        public bool IsEnabledDataEventTracking(int shopId)
        {
            //是否开启
            if (!CustomerConfig.IsEnabledGlobalDataEventTracking)
            {
                return false;
            }
            //是否需要指定店铺ID数据埋点
            if (!CustomerConfig.IsAssignShopEnabledDataEventTracking)
            {
                return true;
            }
            //当前店铺ID为0，则不检查
            if (shopId == 0)
            {
                return true;
            }

            try
            {
                //配置值
                var value = GetConfigShopIdsWithCache();
                //店铺ID列表
                var shopIds = new List<int>();
                if (!string.IsNullOrWhiteSpace(value))
                {
                    shopIds = value.Split(',').Select(m => Convert.ToInt32(m)).ToList();
                }
                if (!shopIds.Any())
                {
                    return false;
                }
                //包含
                return shopIds.Contains(shopId);
            }
            catch (Exception e)
            {

                return false;
            }
        }
        /// <summary>
        /// 获取
        /// </summary>
        /// <returns></returns>
        private string GetConfigShopIdsWithCache()
        {
            //指定需要数据埋点的店铺ID列表，多个以逗号分隔(键名：DianGuanJia:FenXiao:DataEventTracking:Alibaba:ShopIds)。
            var key = $"DianGuanJia:FenXiao:DataEventTracking:{CustomerConfig.CloudPlatformType}:ShopIds";
            var value = MemoryCacheHelper.GetCacheItem<string>(key);
            if (value == null)
            {
                //配置
                var commonSetting = _commonSettingService.Get(key, 0);
                //配置值
                value = commonSetting?.Value;
                //未设置，直接返回
                if (value == null)
                {
                    return null;
                }
                //设置缓存
                MemoryCacheHelper.Set(key, value);
            }
            return value;
        }
    }
}