using System;
using System.Diagnostics;
using System.Threading;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Services.Services.DataEventTracking
{
    public class DataSyncDuplicationLogDataEventTrackingService : BaseDataTrackingLogService
    {
        /// <summary>
        /// 单例实列
        /// </summary>
        private static readonly DataSyncDuplicationLogDataEventTrackingService instance = new DataSyncDuplicationLogDataEventTrackingService();

        /// <summary>
        /// 单例
        /// </summary>
        public static DataSyncDuplicationLogDataEventTrackingService Instance
        {
            get
            {
                return instance;
            }
        }

        /// <summary>
        /// 写日志
        /// </summary>
        /// <param name="model"></param>
        public void WriteLog(DataSyncInterfaceDuplicationLogModel model)
        {
            try
            {
                if (!IsEnabledCollect())
                {
                    return;
                }
                if (model == null)
                {
                    return;
                }
                model.HostName = Environment.MachineName;
                model.ProcessId = Process.GetCurrentProcess().Id;
                model.AppFilePath = AppDomain.CurrentDomain.BaseDirectory;
                if (CustomerConfig.CloudPlatformType != CloudPlatformType.Pinduoduo.ToString())
                {
                    ThreadPool.QueueUserWorkItem(state =>
                    {
                        if (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString())
                        {
                            DataSyncDuplicationLogVolcanoService.Instance.WriteLog(model);
                        }
                        else
                        {
                            DataSyncDuplicationLogService.Instance.WriteLog(model);
                        }
                        
                    });
                    return;
                }
                DataSyncDuplicationLogService.Instance.WriteLog(model);
            }
            catch
            {
                //ignore
            }
        }

        /// <summary>
        /// 开关
        /// </summary>
        /// <returns></returns>
        private bool IsEnabledCollect()
        {
            const string key = CacheKeys.DataSyncDuplicationLogSwitchKey;
            return GetSwitchValue(key);
        }
    }
}