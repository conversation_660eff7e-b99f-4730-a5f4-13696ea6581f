using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Entity.DataMigrate;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility.Extension;
using System.Diagnostics;
using DianGuanJiaApp.Services.Services.ManualOrder;
using DianGuanJiaApp.Data.Repository;
using Dapper;
using vipapis.delivery;

namespace DianGuanJiaApp.Services.Services.DataMigrateSameCloud
{
    public class LogicOrderItemDataMigrateSameCloudService : BaseDataMigrateSameCloudService
    {
        //源业务
        private readonly OrderItemService _orderItemService;

        private readonly FxDataMigrateSubQueryService _subQueryService;
        //目标业务
        private readonly OrderItemService _targetOrderItemService;
        public LogicOrderItemDataMigrateSameCloudService(DbConfigModel sourceDbConfig, DbConfigModel targetDbConfig) : base(
            sourceDbConfig, targetDbConfig)
        {
            //主业务
            _orderItemService = new OrderItemService(SourceDbConnection);

            _subQueryService = new FxDataMigrateSubQueryService();

            //目标库主业务
            _targetOrderItemService = new OrderItemService(TargetDbConnection);
        }

        public override ReturnedModel FullMigrateHandler(FxDataMigrateSubTask model)
        {
            return new ReturnedModel
            {
                Message = "逻辑单项全量迁移与逻辑单全量迁移一起，此逻辑只是保证数据一致。"
            };
        }

        /// <summary>
        /// 增量迁移
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public override ReturnedModel IncrementalMigrateHandler(FxDataMigrateSubTask model)
        {
            //判空处理
            if (model == null)
            {
                return new ReturnedModel
                {
                    Status = ReturnedStatus.Warning,
                    Message = "迁移子任务信息为空。"
                };
            }
            if (model.IsNotUpdate == 1)
            {
                return new ReturnedModel
                {
                    Status = ReturnedStatus.Warning,
                    Message = "补迁不处理。"
                };
            }

            //获取当时
            var dtNow = DateTime.Now;
            //时间片大小（分钟）
            var timeSizeMinutes = 60;
            //重试次数
            var retryTimes = 5;
            //复制副本条件
            var conditionAdd = new DuplicationConditionModel
            {
                FxUserId = model.SourceFxUserId,
                ShopId = model.SourceShopId,
                BeginTime = model.StartMigrateTime,
                EndTime = model.StartMigrateTime.AddMinutes(timeSizeMinutes),
                TimeSize = timeSizeMinutes,
            };

            //分页查询信息
            int pageSize = 200;

            var queryList = new List<FxDataMigrateSubQuery>();
            queryList.Add(new FxDataMigrateSubQuery
            {
                SubTaskCode = model.SubTaskCode,
                QueryJson = conditionAdd.ToJson(),
                SupplierFxUserId = model.FxUserId,
                BeginTime = model.StartMigrateTime,
                EndTime = model.StartMigrateTime.AddMinutes(timeSizeMinutes),
                CreateTime = dtNow,
                TaskCode = model.TaskCode,
                TaskName = model.TaskName + "-Incremental",
                PathFlowCode = $"{model.TaskName}-Incremental-{model.PathFlowCode}",
                Flag = 1
            });

            var query = _subQueryService.TryCreateAndGetList(queryList).FirstOrDefault();
            if (query == null)
                throw new Exception($"{model.TaskName}-Incremental-{model.PathFlowCode}写入子查询任务失败");

            if (query.IsCompleted)
                return new ReturnedModel();

            var condition = query.QueryJson.ToObject<DuplicationConditionModel>();
            condition.MaxId = query.LastId;//从上次最后Id开始
            condition.BeginTime = query.BeginTime;
            condition.EndTime = query.EndTime;

            if (condition.TimeSize > 0)
                timeSizeMinutes = condition.TimeSize;

            condition.TimeSize = timeSizeMinutes;

            if (!condition.BeginTime.HasValue)
            {
                condition.BeginTime = model.StartMigrateTime;
                condition.EndTime = model.StartMigrateTime.AddMinutes(timeSizeMinutes);
            }

            while (condition.BeginTime < dtNow)
            {
                int currentCount;

                _subQueryService.Update(query);
                //分别同步到各厂家库
                do
                {
                    try
                    {
                        Stopwatch sw = new Stopwatch();
                        sw.Start();
                        Stopwatch sw2 = new Stopwatch();
                        sw2.Start();
                        //主信息
                        var changeLogModels = new List<DataChangeLog>();
                        //查询重试{retryTimes}次
                        for (var i = 0; i < retryTimes; i++)
                        {
                            try
                            {
                                changeLogModels = DataChangeLog.Query(condition.FxUserId, condition.ShopId,
                                    DataChangeTableTypeName.LogicOrderItem, condition.BeginTime.Value, condition.EndTime.Value,
                                    pageSize, condition.MaxId ?? 0);
                                break;
                            }
                            catch (Exception ex)
                            {
                                if (i >= retryTimes - 1)
                                    throw ex;
                                Thread.Sleep(100 * i);

                                //修改时间片大小
                                timeSizeMinutes = condition.TimeSize;
                                timeSizeMinutes = timeSizeMinutes / 2;
                                if (timeSizeMinutes <= 9)
                                    timeSizeMinutes = 10;
                                condition.EndTime = query.BeginTime.Value.AddMinutes(timeSizeMinutes);
                                condition.TimeSize = timeSizeMinutes;
                                query.BeginTime = condition.BeginTime;
                                query.EndTime = condition.EndTime;
                                query.ErrorMessage = "异常重试中...";
                                query.QueryJson = condition.ToJson();
                                _subQueryService.Update(query);
                            }
                        }
                        sw2.Stop();
                        var readSpendTimes = sw2.Elapsed.TotalMilliseconds.ToInt();
                        //变更日志ID信息
                        var dataChangeLogIds = changeLogModels.Select(m => m.Id).ToList();
                        //复制副本
                        Duplication(changeLogModels, condition, dataChangeLogIds);
                        //当前页长度
                        currentCount = changeLogModels.Count;
                        //更新下一页条件
                        condition.MaxId = currentCount == 0 ? 0 : changeLogModels.Last().Id;
                        //下一次最大ID
                        model.LastId = condition.MaxId.ToString();

                        //更新查询信息
                        query.LastId = condition.MaxId ?? 0;

                        sw.Stop();
                        var spendTimes = sw.Elapsed.TotalMilliseconds.ToInt();
                        var mainCount = currentCount;
                        //更新查询信息
                        _subQueryService.SetLastId(query.QueryCode, query.LastId, mainCount, spendTimes, mainCount, readSpendTimes, spendTimes - readSpendTimes);
                    }
                    catch (Exception ex)
                    {
                        query.ErrorMessage = ex.ToJson().ToCutString(2048);
                        _subQueryService.SetErrorMessage(query.QueryCode, query.ErrorMessage);
                        throw ex;
                    }
                } while (currentCount == pageSize);

                //时间往下走
                condition.BeginTime = condition.EndTime;
                condition.EndTime = condition.BeginTime.Value.AddMinutes(timeSizeMinutes);
                condition.MaxId = 0;

                query.BeginTime = condition.BeginTime;
                query.EndTime = condition.EndTime;
                query.QueryJson = condition.ToJson();
                query.LastId = 0;
                query.ErrorMessage = "";
            }
            //设为完成
            _subQueryService.SetCompleted(query.QueryCode, query.LastId);


            //最后迁移时间
            model.LastMigrateTime = dtNow;
            //返回
            return new ReturnedModel();

        }

        /// <summary>
        /// 复制副本
        /// </summary>
        /// <param name="aggregationModel"></param>
        /// <param name="condition"></param>
        /// <param name="isNotUpdate"></param>
        /// <param name="dataChangeLogIds"></param>
        private void Duplication(List<DataChangeLog> changeLogModels, DuplicationConditionModel condition,
            List<long> dataChangeLogIds = null)
        {
            if (changeLogModels == null || changeLogModels.Any() == false)
                return;

            //只取EXE_SQL类型
            changeLogModels = changeLogModels.Where(a => a.DataChangeType == DataChangeTypeEnum.EXE_SQL && string.IsNullOrEmpty(a.ExtField2) == false && string.IsNullOrEmpty(a.RelationKey) == false).ToList();

            //路径流代码列表
            var pathFlowCodes = changeLogModels.Select(m => m.RelationKey).Distinct().ToList();

            if (pathFlowCodes == null || pathFlowCodes.Any() == false)
                return;

            //厂家数据库连接字符串
            var factoryConnectionString = TargetDbConfig.ConnectionString;
            //返回值
            var returned = new ReturnedModel
            {
                Status = ReturnedStatus.Success
            };
            //复制逻辑订单副本
            ExceptionRetryHandler(() =>
            {
                var service = new LogicOrderItemService(factoryConnectionString);
                service.UpdateForMigrate(changeLogModels);
                return returned;
            });
            //辅助日志
            WriteDataDuplicationLog(SourceDbConfig, TargetDbConfig, nameof(LogicOrderItemDataMigrateSameCloudService),
                pathFlowCodes, condition: condition, dataChangeLogIds: dataChangeLogIds);
        }
    }
}