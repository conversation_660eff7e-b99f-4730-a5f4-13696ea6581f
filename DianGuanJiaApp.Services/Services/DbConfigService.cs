using DianGuanJiaApp.Data.Repository;
using System.Collections.Generic;
using DianGuanJiaApp.Data.Model;

namespace DianGuanJiaApp.Services.Services
{
    public class DbConfigService
    {
        private readonly DbConfigRepository _repository;
        public DbConfigService()
        {
            _repository = new DbConfigRepository();
        }


        /// <summary>
        /// 获取用户的fxdbconfig配置
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <param name="cloudPlatformTypes"></param>
        /// <returns></returns>
        public List<DbConfigModel> GetFxDbConfigFxUserIds(List<int> fxUserIds, List<string> cloudPlatformTypes)
        {
            return _repository.GetFxDbConfigFxUserIds(fxUserIds, cloudPlatformTypes);
        }

		/// <summary>
		/// 获取分单所有库
		/// </summary>
		/// <param name="dbNameConfigId"></param>
		/// <returns></returns>
		public List<DbConfigModel> GetAllDbConfigModelFx(int dbNameConfigId)
		{
			return _repository.GetAllDbConfigModelFx(dbNameConfigId);
		}

		/// <summary>
		/// 获取分单所有库
		/// </summary>
		/// <param name="dbNameConfigId"></param>
		/// <returns></returns>
		public List<DbConfigModel> GetAllDbConfigModelFx(List<int> dbNameConfigIdList)
		{
			return _repository.GetAllDbConfigModelFx(dbNameConfigIdList);
		}

	}
}