using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Services.Services.ManualOrder;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Services.Services.DuplicationCompensate
{
    public class LogicOrderDuplicationCompensateService : BaseDuplicationCompensateService
    {
        //源业务
        private readonly LogicOrderService _mainService;
        private readonly LogicOrderItemService _logicOrderItemService;
        private readonly OrderCheckService _orderCheckService;
        private readonly OrderService _orderService;
        private readonly OrderItemService _orderItemService;
        private readonly OrderItemExtService _orderItemExtService;
        private readonly OrderTagService _orderTagService;
        private readonly OrderPromiseService _orderPromiseService;
        private readonly LogicOrderPathFlowRecordService _logicOrderPathFlowRecordService;
        private readonly ProductFxService _productService;
        private readonly ProductSkuFxService _productSkuService;
        private readonly OrderManualRecordRepository _orderManualRecordRepository;
        private readonly PurchaseOrderRelationService _purchaseOrderRelationService;
        private readonly PurchaseOrderItemRelationService _purchaseOrderItemRelationService;
        private readonly SendHistoryReturnRecordService _sendHistoryReturnRecordService;
        private readonly PathFlowService _pathFlowService;
        private readonly PathFlowNodeService _pathFlowNodeService;
        public LogicOrderDuplicationCompensateService(DbConfigModel sourceDbConfig) : base(
            sourceDbConfig)
        {
            _mainService = new LogicOrderService(SourceDbConnection);
            _logicOrderItemService = new LogicOrderItemService(SourceDbConnection);
            _orderCheckService = new OrderCheckService(SourceDbConnection);
            _orderService = new OrderService(SourceDbConnection);
            _orderItemService = new OrderItemService(SourceDbConnection);
            _orderItemExtService = new OrderItemExtService(SourceDbConnection);
            _orderTagService = new OrderTagService(SourceDbConnection);
            _orderPromiseService = new OrderPromiseService(SourceDbConnection);
            _logicOrderPathFlowRecordService = new LogicOrderPathFlowRecordService();
            _productService = new ProductFxService(SourceDbConnection);
            _productSkuService = new ProductSkuFxService(SourceDbConnection);
            _orderManualRecordRepository = new OrderManualRecordRepository(SourceDbConnection);
            _purchaseOrderRelationService = new PurchaseOrderRelationService(SourceDbConnection);
            _purchaseOrderItemRelationService = new PurchaseOrderItemRelationService(SourceDbConnection);
            _sendHistoryReturnRecordService = new SendHistoryReturnRecordService(SourceDbConnection);

            _pathFlowService = new PathFlowService(SourceDbConnection);
            _pathFlowNodeService = new PathFlowNodeService(SourceDbConnection);
        }

        /// <summary>
        /// 补偿
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public override ReturnedModel CompensateHandler(DuplicationCompensateModel model)
        {
            //判空处理
            if (model == null)
            {
                return new ReturnedModel
                {
                    Status = ReturnedStatus.Warning,
                    Message = "任务信息为空。"
                };
            }
            if (model.Codes == null || model.Codes.Any() == false)
            {
                if (model.StartTime.HasValue == false || model.EndTime.HasValue == false)
                {
                    return new ReturnedModel
                    {
                        Status = ReturnedStatus.Warning,
                        Message = "查询日期不能为空"
                    };
                }
            }
            //查询条件
            var condition = new DuplicationConditionModel
            {
                FxUserId = model.FxUserId,
                ShopId = model.ShopId,
                BeginTime = model.StartTime,
                EndTime = model.EndTime,
            };

            //分页查询信息
            int pageSize = 100;
            int currentCount;

            var queryFlag = 0;
            //只用LogicOrderId查询
            if (model.Codes != null && model.Codes.Any())
                queryFlag = 1;

            //分别同步到各厂家库
            if (queryFlag == 1)
            {
                //主信息，带子表信息
                var mainModels = _mainService.GetListForDuplication(model.Codes);
                currentCount = mainModels.Count;
                Utility.Log.WriteLine($"查到{currentCount}条LogicOrder数据，开始复制副本，查询条件：{model.Codes.ToJson()}");
                //复制副本
                Duplication(model, mainModels, condition, model.IsOpenedPrePay);
            }
            else
            {
                var timeSize = model.TimeSize;//时间片大小（分钟）
                if (timeSize <= 0)
                    timeSize = 30;
                var count = Math.Ceiling((model.EndTime.Value - model.StartTime.Value).TotalMinutes / timeSize);
                for (int i = 0; i < count; i++)
                {
                    var tmpStartTime = model.StartTime.Value.AddMinutes(i * timeSize);
                    var tmpEndTime = tmpStartTime.AddMinutes(timeSize);
                    condition = new DuplicationConditionModel
                    {
                        FxUserId = model.FxUserId,
                        ShopId = model.ShopId,
                        BeginTime = tmpStartTime,
                        EndTime = tmpEndTime,
                    };
                    do
                    {
                        //主信息，带子表信息
                        var mainModels = _mainService.GetListForDuplicationCompensate(condition, pageSize);
                        //当前页长度
                        currentCount = mainModels.Count;
                        //更新下一页条件
                        condition.MaxId = currentCount == 0 ? 0 : mainModels.Last().Id;
                        //下一次最大ID
                        model.LastId = condition.MaxId ?? 0;

                        Utility.Log.WriteLine($"查到{currentCount}条LogicOrder数据，开始复制副本，查询条件：{condition.ToJson()}");
                        //复制副本
                        Duplication(model, mainModels, condition, model.IsOpenedPrePay);

                    } while (currentCount == pageSize);
                }
            }

            //返回
            return new ReturnedModel();
        }

        /// <summary>
        /// 复制副本
        /// </summary>
        /// <param name="model"></param>
        /// <param name="mainModels"></param>
        /// <param name="condition"></param>
        /// <param name="isOpenedPrePay">是否被开启了预付</param>
        private void Duplication(DuplicationCompensateModel model, List<LogicOrder> mainModels, DuplicationConditionModel condition, bool isOpenedPrePay = false)
        {
            if (mainModels == null || mainModels.Any() == false)
                return;
            //按路径流获取目标库
            var pathFlowCodes = mainModels.Select(a => a.PathFlowCode).Distinct().ToList();

            var logicOrderIds = mainModels.Select(a => a.LogicOrderId).Distinct().ToList();
            //路径流历史记录，目前仅抖店云有配置这个环境，其他云需要排除 --by 吴自飞 20240218
            List<LogicOrderPathFlowRecord> records = null;
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString())
            {
                records = _logicOrderPathFlowRecordService.GetList(logicOrderIds);
            }
            
            if (records != null && records.Any())
            {
                pathFlowCodes.AddRange(records.Select(a => a.PathFlowCode).ToList());
                pathFlowCodes = pathFlowCodes.Distinct().ToList();
            }
            else
                records = new List<LogicOrderPathFlowRecord>();

            //获取路径流节点上用户信息
            var pathFlowFxUserIdNodes = new List<PathFlowFxUserIdNodeModel>();
            //路径节点所有用户数据库配置信息
            var factoryDbConfigs = GetNeedDuplicationDbConfigs(SourceDbConfig, false, pathFlowCodes,
                ref pathFlowFxUserIdNodes, isInitFxDbConfig: true);

            //需要复制副本厂家数据库
            var needDuplicationDbConfigs = GetNeedDuplicationDbConfigsByGroup(SourceDbConfig, factoryDbConfigs);

            if (needDuplicationDbConfigs.Any() == false)
                return;

            var logicOrderItems = _logicOrderItemService.GetListForDuplication(logicOrderIds);
            var orderChecks = _orderCheckService.GetListForDuplication(logicOrderIds, "*", "LogicOrderId");
            var ptOrderIds = mainModels.Select(m => m.PlatformOrderId).Distinct().ToList();
            //与原始订单相关信息
            var orders = _orderService.GetListForDuplication(ptOrderIds, "PlatformOrderId");
            var orderItems = _orderItemService.GetListForDuplication(ptOrderIds, "PlatformOrderId");
            var orderItemCodes = orderItems.Select(m => m.OrderItemCode).Distinct().ToList();
            var orderItemExts = _orderItemExtService.GetListForDuplication(orderItemCodes);
            var sids = orders.Select(m => m.ShopId).Distinct().ToList();
            var platformOrderIds = orders.Select(m => m.PlatformOrderId).Distinct().ToList();
            //标签信息
            var orderTags = _orderTagService.GetListForDuplication(platformOrderIds, sids, TagType.Order.ToString());
            orderTags.AddRange(_orderTagService.GetListForDuplication(orderItemCodes, sids, TagType.OrderItem.ToString()));
            orderTags.AddRange(_orderTagService.GetListForDuplication(logicOrderIds, sids, TagType.LogicOrder.ToString()));
            var orderPromises = _orderPromiseService.GetListForDuplication(platformOrderIds, sids);
            //路径流信息
            var pathFlows = _pathFlowService.GetListForDuplication(pathFlowCodes);
            var pathFlowNodes = _pathFlowNodeService.GetListForDuplication(pathFlowCodes);
            //针对手工推单的逻辑单，还要对商品进行同步
            var manualLogicOrderIds = _orderManualRecordRepository.GetWaitCheckOrders(logicOrderIds, model.FxUserId);
            var productCodes = new List<string>();
            var skuCodes = new List<string>();
            var products = new List<ProductFx>();
            var skus = new List<ProductSkuFx>();
            if (manualLogicOrderIds.Any())
            {
                productCodes = logicOrderItems.Where(a => manualLogicOrderIds.Contains(a.LogicOrderId)).Select(a => a.ProductCode).Distinct().ToList();
                skuCodes = logicOrderItems.Where(a => manualLogicOrderIds.Contains(a.LogicOrderId)).Select(a => a.SkuCode).Distinct().ToList();
                products = _productService.GetListForDuplication(productCodes);
                skus = _productSkuService.GetListForDuplication(skuCodes);
            }

            //1688代销相关
            var purchaseOrderRelations = new List<PurchaseOrderRelation>();
            var purchaseOrderItemRelations = new List<PurchaseOrderItemRelation>();
            var sendHistoryReturnRecords = new List<SendHistoryReturnRecord>();
            if (isOpenedPrePay)
            {
                purchaseOrderRelations = _purchaseOrderRelationService.GetByLogicOrderIds(logicOrderIds);
                var purchaseRelationCodes = purchaseOrderRelations.Select(a => a.PurchaseRelationCode).ToList();
                List<string> fields = null;
                purchaseOrderItemRelations = _purchaseOrderItemRelationService.GetListForDuplication(purchaseRelationCodes, fields);
                sendHistoryReturnRecords = _sendHistoryReturnRecordService.GetListForDuplication(logicOrderIds);
            }

            //逐个目标库处理
            needDuplicationDbConfigs.ForEach(dbConfig =>
            {
                //目标库连接字符串
                var factoryConnectionString = dbConfig.Key;
                //当前需要复制副本的用户ID
                var fxUserIds = dbConfig.Select(m => m.DbConfig.UserId).Distinct().ToList();
                //需要复制到厂家副本路径流代码列表
                var factoryPathFlowCodes = pathFlowFxUserIdNodes.Where(m => fxUserIds.Contains(m.FxUserId))
                    .Select(m => m.PathFlowCode).Distinct().ToList();

                //历史路径流相关的逻辑单
                var historyLogicOrderIds = records.Where(a => factoryPathFlowCodes.Contains(a.PathFlowCode)).Select(a => a.LogicOrderId).Distinct().ToList();
                //路径流相关信息
                var factoryPathFlows = pathFlows.Where(m => factoryPathFlowCodes.Contains(m.PathFlowCode)).ToList();
                var factoryPathFlowNodes =
                    pathFlowNodes.Where(m => factoryPathFlowCodes.Contains(m.PathFlowCode)).ToList();
                var factoryModels = mainModels.Where(a => factoryPathFlowCodes.Contains(a.PathFlowCode) || historyLogicOrderIds.Contains(a.LogicOrderId)).ToList();
                var factoryLogicOrderIds = factoryModels.Select(a => a.LogicOrderId).Distinct().ToList();
                var factoryLogicOrderItems = logicOrderItems.Where(m => factoryLogicOrderIds.Contains(m.LogicOrderId)).ToList();
                var factoryOrderChecks = orderChecks.Where(m => factoryLogicOrderIds.Contains(m.LogicOrderId)).ToList();
                var orderCodes = factoryModels.Select(m => m.OrderCode).Distinct().ToList();
                var factoryOrders = orders.Where(m => orderCodes.Contains(m.OrderCode)).ToList();
                var itemCodes = factoryLogicOrderItems.Select(m => m.OrderItemCode).Distinct().ToList();
                var factoryOrderItems = orderItems.Where(m => orderCodes.Contains(m.OrderCode)).ToList();
                var factoryOrderItemExts = orderItemExts.Where(m => itemCodes.Contains(m.OrderItemCode)).ToList();
                var factoryOrderTags = orderTags.Where(m => factoryLogicOrderIds.Contains(m.OrderCode)).ToList();
                factoryOrderTags.AddRange(orderTags.Where(m => itemCodes.Contains(m.OrderCode)).ToList());
                factoryOrderTags.AddRange(orderTags.Where(m => orderCodes.Contains(m.OrderCode)).ToList());
                var factoryPlatformOrderIds = orderItems.Select(m => m.PlatformOrderId).Distinct().ToList();
                var targetOrderPromises = orderPromises.Where(m => factoryPlatformOrderIds.Contains(m.PlatformOrderId)).ToList();

                var factoryPurchaseOrderRelations = purchaseOrderRelations.Where(m => factoryLogicOrderIds.Contains(m.SourceLogicOrderId)).ToList();
                var factoryPurchaseRelationCodes = factoryPurchaseOrderRelations.Select(a => a.PurchaseRelationCode).ToList();
                var factoryPurchaseOrderItemRelations = purchaseOrderItemRelations.Where(m => factoryPurchaseRelationCodes.Contains(m.PurchaseRelationCode)).ToList();
                var factorySendHistoryReturnRecords = sendHistoryReturnRecords.Where(m => factoryLogicOrderIds.Contains(m.SourceLogicOrderId)).ToList();

                var targetProducts = new List<ProductFx>();
                var targetSkus = new List<ProductSkuFx>();
                if (manualLogicOrderIds.Any())
                {
                    var factoryProductCodes = factoryLogicOrderItems.Where(a => manualLogicOrderIds.Contains(a.LogicOrderId)).Select(a => a.ProductCode).Distinct().ToList();
                    var factorySkuCodes = factoryLogicOrderItems.Where(a => manualLogicOrderIds.Contains(a.LogicOrderId)).Select(a => a.SkuCode).Distinct().ToList();
                    targetProducts = products.Where(m => factoryProductCodes.Contains(m.ProductCode)).ToList();
                    targetSkus = skus.Where(m => factorySkuCodes.Contains(m.SkuCode)).ToList();
                }

                ExceptionRetryHandler(() =>
                {
                    new LogicOrderService(factoryConnectionString).InsertsForDuplication(factoryModels);
                    return true;
                });
                ExceptionRetryHandler(() =>
                {
                    new LogicOrderItemService(factoryConnectionString).InsertsForDuplication(factoryLogicOrderItems);
                    return true;
                });
                //ExceptionRetryHandler(() =>
                //{
                //    new PathFlowService(factoryConnectionString).InsertsForDuplication(factoryPathFlows);
                //    return true;
                //});
                //ExceptionRetryHandler(() =>
                //{
                //    new PathFlowNodeService(factoryConnectionString).InsertsForDuplication(factoryPathFlowNodes);
                //    return true;
                //});
                ExceptionRetryHandler(() =>
                {
                    new OrderCheckService(factoryConnectionString).InsertsForDuplication(factoryOrderChecks);
                    return true;
                });
                ExceptionRetryHandler(() =>
                {
                    new OrderService(factoryConnectionString).InsertsForDuplication(factoryOrders);
                    return true;
                });
                ExceptionRetryHandler(() =>
                {
                    new OrderItemService(factoryConnectionString).InsertsForDuplication(factoryOrderItems);
                    return true;
                });
                ExceptionRetryHandler(() =>
                {
                    new OrderItemExtService(factoryConnectionString).InsertsForDuplication(factoryOrderItemExts);
                    return true;
                });
                ExceptionRetryHandler(() =>
                {
                    new OrderTagService(factoryConnectionString).InsertsForDuplication(factoryOrderTags);
                    return true;
                });
                ExceptionRetryHandler(() =>
                {
                    new OrderPromiseService(factoryConnectionString).InsertsForDuplication(targetOrderPromises);
                    return true;
                });

                if (targetProducts.Any())
                {
                    ExceptionRetryHandler(() =>
                    {
                        new ProductFxService(factoryConnectionString).InsertsForDuplication(targetProducts);
                        return true;
                    });
                }

                if (targetSkus.Any())
                {
                    ExceptionRetryHandler(() =>
                    {
                        new ProductSkuFxService(factoryConnectionString).InsertsForDuplication(targetSkus);
                        return true;
                    });
                }
                if (factoryPurchaseOrderRelations.Any())
                {
                    ExceptionRetryHandler(() =>
                    {
                        new PurchaseOrderRelationService(factoryConnectionString).InsertsForDuplication(factoryPurchaseOrderRelations);
                        return true;
                    });
                }
                if (factoryPurchaseOrderItemRelations.Any())
                {
                    ExceptionRetryHandler(() =>
                    {
                        new PurchaseOrderItemRelationService(factoryConnectionString).InsertsForDuplication(factoryPurchaseOrderItemRelations);
                        return true;
                    });
                }
                if (factorySendHistoryReturnRecords.Any())
                {
                    ExceptionRetryHandler(() =>
                    {
                        new SendHistoryReturnRecordService(factoryConnectionString).InsertsForDuplication(factorySendHistoryReturnRecords);
                        return true;
                    });
                }

                Utility.Log.WriteLine($"从{SourceDbConfig.DbNameConfig?.DbName} ==> {dbConfig.First().DbNameConfig?.DbName} 复制副本数据，本次处理{factoryLogicOrderIds.Count}条，LogicOrderId明细如下：{factoryLogicOrderIds.ToJson()}");

                WriteDuplicationCompensateLog(dbConfig.First(), nameof(LogicOrderDuplicationCompensateService),
                    factoryLogicOrderIds, condition: condition);
            });
        }

    }
}