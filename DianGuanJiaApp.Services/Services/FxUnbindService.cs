using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Dapper;
using Dapper;
using DianGuanJiaApp.Warehouse.Sdk;
using DianGuanJiaApp.Warehouse.Model.Request;
using Nest;
using System.Diagnostics;

namespace DianGuanJiaApp.Services
{

    public partial class FxUnbindService
    {
        private IWarehouseClient _warehouseclient;
        public FxUnbindService()
        {
            _repository = new FxUnbindRepository();
            _warehouseclient = new WarehouseClient(CustomerConfig.WarehouseApiUrl, CustomerConfig.WarehouseAppkey, CustomerConfig.WarehouseAppsecret);
        }

        /// <summary>
        /// 指定云平台的解绑（用于迁移、解绑厂家所在业务库）
        /// </summary>
        /// <param name="connectionString">业务库的连接字符串</param>
        /// <param name="printHistoryConnectionString">打印记录的连接字符串</param>
        /// <param name="isBuckUp">是否开启解绑前备份</param>
        public FxUnbindService(string connectionString, string printHistoryConnectionString = "", bool isBuckUp = false)
        {
            _repository = new FxUnbindRepository(connectionString, printHistoryConnectionString, isBuckUp);
            _warehouseclient = new WarehouseClient(CustomerConfig.WarehouseApiUrl, CustomerConfig.WarehouseAppkey, CustomerConfig.WarehouseAppsecret);
        }

        FxUnbindRepository _repository;

        public void DelOrderDatas(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action,
            bool isFromColdDelete = false, bool isDeleteOldDbData = false)
        {
            _repository.DelOrderDatas(fxUserId, shopIds, isDelFxUser, action, isFromColdDelete: isFromColdDelete,
                isDeleteOldDbData: isDeleteOldDbData);
        }

        public void InitTableFields() 
        {
            _repository.InitTableFields();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopIds"></param>
        /// <param name="isDelFxUser"></param>
        /// <param name="action"></param>
        /// <param name="isSupplier">是否厂家库</param>
        public void DelProductDatas(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action, bool isSupplier = false)
        {
            // 解绑店铺商品与基础商品关联数据
            DelBaseProductRelation(fxUserId, shopIds, action);
            
            _repository.DelProductDatas(fxUserId, shopIds, isDelFxUser, action);

            //同时清理1688代销商品关联数据
            DelDistributorProductMapping(fxUserId, shopIds, isDelFxUser, action, isSupplier);
            
            //解绑库存货品关联关系
            UnbindWareHouseSkuBindRelationData(fxUserId, shopIds, action);
        }

        /// <summary>
        /// 解除店铺商品与基础商品关联数据
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopIds"></param>
        /// <param name="action"></param>
        public void DelBaseProductRelation(int fxUserId, List<int> shopIds, Action<string> action)
        {
            var sw0 = new Stopwatch();
            sw0.Start();
           
            var status = new List<AgentBingSupplierStatus>
            {
                AgentBingSupplierStatus.Binded, AgentBingSupplierStatus.UnBindFail,
                AgentBingSupplierStatus.UnBind, AgentBingSupplierStatus.UnBinding
            };
            // 获取合作中的厂家Id
            var supplierFxUserIds = new SupplierUserService().GetByFxUserId(fxUserId, true)
                .Where(x => status.Contains(x.Status))
                .Select(x => x.SupplierFxUserId).Distinct()
                .ToList();
 
            var productCodes = _repository.DelBaseProductRelation(fxUserId, shopIds, supplierFxUserIds, action);
            
             // 跨云执行
             if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
             {
                 // 大于500分批处理
                 var count = productCodes.Count;
                 const int pageSize = 500;
                 var pageCount = count / pageSize + (count % pageSize > 0 ? 1 : 0);
                 for (var i = 0; i < pageCount; i++)
                 {
                     var tempCodes = productCodes.Skip(i * pageSize).Take(pageSize).ToList();
                     var model = new DelBaseProductRelationModel
                     {
                         ProductCodes = tempCodes,
                         FxUserIds = supplierFxUserIds
                     };
                     try
                     {
                         const string apiUrl = "/BaseProductApi/DelBaseProductRelation";
                         var targetSiteUrl = CommUtls.GetTargetSiteUrl(apiUrl);
                         Log.Debug($"跨云删除基础商品关联数据，targetSiteUrl={targetSiteUrl}，lastModel={model.ToJson()}");

                         var result = WebCommon.PostFxSiteApi<DelBaseProductRelationModel, long>(targetSiteUrl,
                             fxUserId, model, "跨云查询基础商品Sku信息", isEncrypt: true);
                         Log.WriteLine($"店铺【{shopIds.ToJson()}】解除商品关联关系——删除商品库数据， 成功数量: {result}",
                             "DelBaseProductRelation.txt");
                     }
                     catch (Exception ex)
                     {
                         Log.WriteError($"跨云删除基础商品关联数据：{ex.Message}");
                     }
                 }
             }
 
            sw0.Stop();
            action.Invoke($"店铺【{shopIds.ToJson()}】清理商品相关关联数据记录总耗时：{sw0.Elapsed.TotalSeconds}s");
        }

        /// <summary>
        /// 清理库存货品关系
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopIds"></param>
        /// <param name="action"></param>
        public void UnbindWareHouseSkuBindRelationData(int fxUserId, List<int> shopIds, Action<string> action)
        {
            if (shopIds == null || shopIds.Any() == false)
                throw new LogicException("店铺Id不能为空");
            if (fxUserId <= 0)
                throw new LogicException("用户Id不能为空");
            //按店铺ID解绑
            shopIds.ForEach(shopId =>
            {
                //清理
                var sw0 = new Stopwatch();
                sw0.Start();
                var wareHouseSkuBindRelations = _repository.GetWareHouseSkuBindRelations(fxUserId, shopId);
                if (wareHouseSkuBindRelations != null && wareHouseSkuBindRelations.Any())
                {
                    wareHouseSkuBindRelations.ForEach(relation =>
                    {
                        var request = new WarehouseSkuUnbindRequest
                        {
                            SkuBindRelationCode = relation.SkuBindRelationCode,
                            OwnerCode = relation.OwnerCode
                        };
                        _warehouseclient.Execute(request);
                    });
                }
                sw0.Stop();
                action.Invoke($"店铺【{shopId}|{fxUserId}】调用库存接口清理库存货品关系相关记录总耗时：{sw0.Elapsed.TotalMilliseconds} ms，");

                _repository.UnbindWareHouseSkuBindRelationData(fxUserId, new List<int> { shopId }, action);
            });
        }

        /// <summary>
        /// 1688代销商品关联数据状态设为0
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopIds"></param>
        /// <param name="isDelFxUser"></param>
        /// <param name="isSupplier"></param>
        /// <param name="action"></param>
        public void DelDistributorProductMapping(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action, bool isSupplier = false)
        {
            _repository.DelDistributorProductMapping(fxUserId, shopIds, isDelFxUser, action, isSupplier);
        }

        public void DelWaybillCodeDatas(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action)
        {
            _repository.DelWaybillCodeDatas(fxUserId, shopIds, isDelFxUser, action);
        }

        public void DelPrintHistoryDatas(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action)
        {
            _repository.DelPrintHistoryDatas(fxUserId, shopIds, isDelFxUser, action);
        }

        public void DelSendHistoryDatas(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action)
        {
            _repository.DelSendHistoryDatas(fxUserId, shopIds, isDelFxUser, action);
        }

        public void DelBindShopDatas(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action)
        {
            //增加处理：检查并关闭预付
            //存在待发货的预付单时抛异常
            CheckAndClosePrepay(fxUserId, shopIds, isDelFxUser, action);

            _repository.DelBindShopDatas(fxUserId, shopIds, isDelFxUser, action);

            //删除店铺绑定厂家代发关系
            _repository.DelFxUserShopDefaultSupplierByShopId(fxUserId, shopIds, action);
        }

        public void DelUserDatas(int fxUserId, bool isDelFxUser, Action<string> action)
        {
            if (isDelFxUser == false)
                return;
            _repository.DelUserDatas(fxUserId, isDelFxUser, action);
        }

        public void UpdateUnbindHistory(int fxUserId, string mobile, int shopId, UnbindType unbindType, bool isDeleteUser, string exception)
        {
            _repository.UpdateUnbindHistory(fxUserId, mobile, shopId, unbindType, isDeleteUser, exception);
        }



        /// <summary>
        /// 检查新旧账号 --> 切换账号
        /// </summary>
        /// <param name="oldMobile">旧手机号</param>
        /// <param name="newMobile">新手机号</param>
        public CheckResult CheckAndSwitchFxUser(string oldMobile, string newMobile)
        {
            var result = new CheckResult();

            // 1、加载用户信息
            var userFxService = new UserFxService();
            var oldUserFx = userFxService.GetUserFxByMobile(oldMobile);
            if (oldUserFx == null)
            {
                result.Message = $"旧账号【{oldMobile}】信息未找到，不能进行切换";
                return result;
            }
            var newUserFx = userFxService.GetUserFxByMobile(newMobile);
            if (newUserFx == null)
            {
                result.Message = $"新账号【{newMobile}】不存在，不能进行切换";
                return result;
            }

            // 2、检测新账号是否有操作数据
            var fxUserShopService = new FxUserShopService();
            // 2.1 检测是否绑定店铺信息
            var newUserShops = fxUserShopService.GetList(newUserFx.Id);
            if (newUserShops != null && newUserShops.Any(x => x.PlatformType != PlatformType.System.ToString()))
            {
                result.Message = $"新账号【{newMobile}】有绑定店铺，不能进行切换";
                return result;
            }
            // 2.2 检测是否绑定商家信息
            var supplierUserService = new SupplierUserService();
            var newUserAgents = supplierUserService.GetAgentList(newUserFx.Id);
            if (newUserAgents != null && newUserAgents.Any())
            {
                result.Message = $"新账号【{newMobile}】有绑定商家，不能进行切换";
                return result;
            }
            // 2.3 检测是否绑定厂家信息
            var newUserSuppliers = supplierUserService.GetSupplierList(newUserFx.Id);
            if (newUserSuppliers != null && newUserSuppliers.Any())
            {
                result.Message = $"新账号【{newMobile}】有绑定厂家，不能进行切换";
                return result;
            }
            // 2.4 检查是否有路径关系
            var newUserPathFlows = new PathFlowService().GetPathFlowByFxUserId(newUserFx.Id);
            if (newUserPathFlows.Any())
            {
                result.Message = $"新账号【{newMobile}】存在PathFlow路径绑定关系，不能进行切换";
                return result;
            }

            // 3、切换账号
            SwitchFxUser(oldUserFx, newUserFx);

            // 4、日志
            try
            {

                var _logRepository = new LogForOperatorRepository("LogForFendan");
                var log = new LogForOperator
                {
                    OperatorType = "切换账号",
                    ShopId = SiteContext.GetCurrentShopId(),
                    UserId = SiteContext.GetCurrentFxUserId(),
                    PlatformType = SiteContext.CurrentNoThrow?.CurrentLoginShop?.PlatformType,
                    DbNameConfigId = SiteContext.CurrentNoThrow?.CurrentDbConfig?.DbConfig?.DbNameConfigId ?? 0,
                    ServerIP = DianGuanJiaApp.Utility.Net.HttpUtility.GetServerIP(),

                    Detail = new { OldMobile = oldMobile, NewMobile = newMobile }
                };
                _logRepository.BulkInsertLogs(log);
            }
            catch (Exception ex)
            {
                Log.WriteError($"切换账号，写入日志失败：{ex}");
            }

            result.Success = true;
            return result;

        }

        public void SwitchFxUser(UserFx oldUserFx, UserFx newUserFx, bool isSwitchBack = false, Action<string> action = null)
        {
            _repository.SwitchFxUser(oldUserFx, newUserFx, isSwitchBack, action);
        }

        public void ClearNewFxUserAgentAndSupplier(UserFx newUserFx, Action<string> action = null)
        {
            _repository.ClearNewFxUserAgentAndSupplier(newUserFx, action);
        }

        /// <summary>
        /// 直接修改手机号
        /// </summary>
        /// <param name="oldMobile">原手机号</param>
        /// <param name="newMobile">新手机号</param>
        public void BindMobile(string oldMobile, string newMobile, string newPassword)
        {
            _repository.BindMobile(oldMobile, newMobile, newPassword);
        }

        public void UpdateBindMobile(int oldFxUserId, int newFxUserId)
        {
            if(oldFxUserId != newFxUserId)
                _repository.UpdateBindMobile(oldFxUserId, newFxUserId);
        }

        /// <summary>
        /// 检查新旧账号 --> 修改手机号、更新旧数据 --> 初始同步相关数据
        /// </summary>
        /// <param name="oldMobile">旧手机号</param>
        /// <param name="newMobile">新手机号</param>
        /// <param name="platformType">平台类型</param>
        /// <param name="dbname">加密后的库名</param>
        public CheckResult CheckAndBindMobile(string oldMobile, string newMobile, PlatformType platformType, string dbname = "")
        {
            var result = new CheckResult();

            // 1、加载用户信息
            var userFxService = new UserFxService();
            var oldUserFx = userFxService.GetUserFxByMobile(oldMobile);
            if (oldUserFx == null)
            {
                result.Message = $"旧账号【{oldMobile}】信息未找到，不能进行切换";
                return result;
            }

            var newUserFx = userFxService.GetUserFxByMobile(newMobile);
            var _userfxService = new UserFxService();

            var oldFxUserId = oldUserFx.Id;
            var newFxUserId = oldFxUserId;
            var opt = "注册并绑定手机号";

            if (newUserFx == null)
            {
                // 2、直接修改手机号
                var pwd = CommUtls.GetRndPassword();
                var newPassword = DES.EncryptUrl(pwd, CustomerConfig.LoginCookieEncryptKey); 
                BindMobile(oldMobile, newMobile, newPassword);

                //发送短信
                var sendResult = new SendAlidayu().Send(newMobile, pwd, "pwd");
                if (!sendResult.Item1)
                {
                    Log.WriteError($"密码短信发送失败{newMobile}，错误：{sendResult.Item2}");
                }
                result.Message = $"短信发送失败：{sendResult.Item2}";
            }
            else
            {
                #region 检查旧账号数据

                // 2、检测旧账号是否有操作数据
                var fxUserShopService = new FxUserShopService();
                // 2.1 检测是否绑定商家信息
                var supplierUserService = new SupplierUserService();
                var oldUserAgents = supplierUserService.GetAgentList(oldFxUserId);
                if (oldUserAgents != null && oldUserAgents.Any())
                {
                    result.Message = $"旧账号【{oldMobile}】有绑定商家，只能用未注册的手机号绑定";
                    return result;
                }
                // 2.2 检测是否绑定厂家信息
                var oldUserSuppliers = supplierUserService.GetSupplierList(oldFxUserId);
                if (oldUserSuppliers != null && oldUserSuppliers.Any())
                {
                    result.Message = $"旧账号【{oldMobile}】有绑定厂家，只能用未注册的手机号绑定";
                    return result;
                }
                // 2.3 检查是否有路径关系
                var oldUserPathFlows = new PathFlowService().GetPathFlowByFxUserId(oldFxUserId);
                if (oldUserPathFlows.Any())
                {
                    result.Message = $"旧账号【{oldMobile}】存在PathFlow路径绑定关系，只能用未注册的手机号绑定";
                    return result;
                }
                #endregion

                newFxUserId = newUserFx.Id;
                //3、修改关联关系
                UpdateBindMobile(oldFxUserId, newFxUserId);
                opt = "登录并绑定手机号";

                //更新上下文
                if (!string.IsNullOrEmpty(dbname))
                {
                    dbname = DES.DecryptDES(dbname, CustomerConfig.LoginCookieEncryptKey);
                }
                var sc = new SiteContext(newUserFx, dbname, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
                result.Message = "login";
            }

            //3、初始化数据（同步状态、同步任务）
            _userfxService.InitFxUser(newFxUserId, platformType);

            // 4、日志
            try
            {

                var _logRepository = new LogForOperatorRepository("LogForFendan");
                var log = new LogForOperator
                {
                    OperatorType = opt,
                    ShopId = SiteContext.GetCurrentShopId(),
                    UserId = SiteContext.GetCurrentFxUserId(),
                    PlatformType = SiteContext.CurrentNoThrow?.CurrentLoginShop?.PlatformType,
                    DbNameConfigId = SiteContext.CurrentNoThrow?.CurrentDbConfig?.DbConfig?.DbNameConfigId ?? 0,
                    ServerIP = DianGuanJiaApp.Utility.Net.HttpUtility.GetServerIP(),

                    Detail = new { OldMobile = oldMobile, NewMobile = newMobile }
                };
                _logRepository.BulkInsertLogs(log);
            }
            catch (Exception ex)
            {
                Log.WriteError($"{opt}，写入日志失败：{ex}");
            }

            result.Success = true;
            return result;

        }

        public void Resume(int fxUserId, int sid, Action<string> action)
        {
            _repository.Resume(fxUserId, sid, action);
        }

        public void ResumeOrderNew(int fxUserId, int sid, Action<string> action)
        {
            _repository.ResumeOrderNew(fxUserId, sid, action);
        }

        public void ResumeProductNew(int fxUserId, int sid, Action<string> action)
        {
            _repository.ResumeProductNew(fxUserId, sid, action);
        }


        public void ResumeShopNew(int fxUserId, int sid, Action<string> action)
        {
            _repository.ResumeShopNew(fxUserId, sid, action);
        }

        public void ResumeUserNew(int fxUserId, int sysid, Action<string> action) 
        {
            _repository.ResumeUserNew(fxUserId, sysid, action);
        }

        public void ResumeAll(int fxUserId, int sid, DateTime? deleteTime, Action<string> action, bool isAliDb = false)
        {
            _repository.ResumeAll(fxUserId, sid, deleteTime, action, isAliDb);
        }

        /// <summary>
        /// 用于冷库数据还原
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="sid"></param>
        /// <param name="deleteTime"></param>
        /// <param name="action"></param>
        public void ResumeOrderNew2(int fxUserId, int sid, DateTime? deleteTime, Action<string> action)
        {
            _repository.ResumeOrderNew2(fxUserId, sid, deleteTime, action);
        }

        /// <summary>
        /// 对相关商家关闭预付（直接设置状态）
        /// </summary>
        /// <param name="supplierFxUserId"></param>
        public void ClosePrepay(int supplierFxUserId)
        {
            var supplierUserRepository = new SupplierUserRepository();
            var prepayStatusChangeRecordService = new PrepayStatusChangeRecordService();
            //已开启预付的商家
            var openedPrepayAgents = supplierUserRepository.GetAgentListV2(new List<int> { supplierFxUserId }, null, true, true, "", 1);
            //逐个关闭
            if (openedPrepayAgents != null && openedPrepayAgents.Any())
            {
                var isPrePay = 0;
                openedPrepayAgents.ForEach(agent => {
                    var curResult = supplierUserRepository.SetPrePay(supplierFxUserId, agent.FxUserId, isPrePay);
                    if (curResult > 0)
                    {
                        //预付日志
                        var model = new PrepayStatusChangeRecord()
                        {
                            FxUserId = agent.FxUserId,
                            SupplierFxUserId = supplierFxUserId,
                            PrepayStatus = isPrePay,
                            CreateFxUserId = supplierFxUserId,
                            CreateTime = DateTime.Now,
                        };
                        prepayStatusChangeRecordService.Add(model);
                    }
                });
            }
        }

        /// <summary>
        /// 对相关商家关闭预付并变更相关预付订单
        /// </summary>
        /// <param name="supplierFxUserId"></param>
        /// <param name="systemShopId"></param>
        public void ClosePrepayAndChangePrepayOrder(int supplierFxUserId, int systemShopId)
        {
            var supplierUserService = new SupplierUserService();
            var supplierUserRepository = new SupplierUserRepository();
            var prepayStatusChangeRecordService = new PrepayStatusChangeRecordService();
            //已开启预付的商家
            var openedPrepayAgents = supplierUserRepository.GetAgentListV2(new List<int> { supplierFxUserId }, null, true, true, "", 1);
            //逐个关闭
            if (openedPrepayAgents != null && openedPrepayAgents.Any())
            {
                var isPrePay = 0;
                openedPrepayAgents.ForEach(agent => {
                    var curResult = supplierUserService.SetPrePay(supplierFxUserId, agent.FxUserId, isPrePay, systemShopId);
                    if (curResult.Success)
                    {

                        var changeModel = (ChangePrePayForCheckModel)curResult.ReturnData;

                        #region 成功后还要保存到京东和拼多多平台、抖店云平台
                        var apiUrl = "/ChangePrePayApi/SetPrePay";
                        var aliCloudHost = CustomerConfig.DefaultFenFaSystemUrl.TrimEnd("/") + apiUrl;
                        var pddCloudHost = CustomerConfig.PinduoduoFenFaSystemUrl.TrimEnd("/") + apiUrl;
                        var jdCloudHost = CustomerConfig.JingdongFenFaSystemUrl.TrimEnd("/") + apiUrl;
                        var ttCloudHost = CustomerConfig.ToutiaoFenFaSystemUrl.TrimEnd("/") + apiUrl;

                        //支持1688代销的平台
                        var fx1688SupportPlatformTypes = CustomerConfig.Fx1688SupportPlatformTypes;

                        //分单系统主站点在阿里云，所有分发到朵朵云和京东云、抖店云平台
                        if (!string.IsNullOrEmpty(CustomerConfig.PinduoduoFenFaSystemUrl) && aliCloudHost != pddCloudHost && (fx1688SupportPlatformTypes.Contains(PlatformType.Pinduoduo.ToString()) || fx1688SupportPlatformTypes.Contains(PlatformType.KuaiTuanTuan.ToString())))
                            WebCommon.PostFxSiteApi<ChangePrePayForCheckModel, bool>(pddCloudHost, supplierFxUserId, changeModel, "跨云平台解绑1688分销店铺-订单审核处理");

                        if (!string.IsNullOrEmpty(CustomerConfig.JingdongFenFaSystemUrl) && aliCloudHost != jdCloudHost && fx1688SupportPlatformTypes.Contains(PlatformType.Jingdong.ToString()))
                            WebCommon.PostFxSiteApi<ChangePrePayForCheckModel, bool>(jdCloudHost, supplierFxUserId, changeModel, "跨云平台解绑1688分销店铺-订单审核处理");

                        if (!string.IsNullOrEmpty(CustomerConfig.ToutiaoFenFaSystemUrl) && aliCloudHost != ttCloudHost && fx1688SupportPlatformTypes.Contains(PlatformType.TouTiao.ToString()))
                            WebCommon.PostFxSiteApi<ChangePrePayForCheckModel, bool>(ttCloudHost, supplierFxUserId, changeModel, "跨云平台解绑1688分销店铺-订单审核处理");
                        #endregion
                    }
                });
            }
        }

        /// <summary>
        /// 检查并关闭预付
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopIds"></param>
        /// <param name="isDelFxUser"></param>
        /// <param name="action"></param>
        public void CheckAndClosePrepay(int fxUserId, List<int> shopIds, bool isDelFxUser, Action<string> action)
        {
            if (CustomerConfig.CloudPlatformType != CloudPlatformType.Alibaba.ToString())
                return;

            try
            {
                if (isDelFxUser)
                {
                    //前置检查
                    var hasWaitSendOrder = new PurchaseOrderRelationService().IsHasWaitSendBySupplierId(fxUserId);
                    if (hasWaitSendOrder)
                        throw new LogicException($"用户（FxUserId={fxUserId}）存在待发货的预付单");

                    //1.直接设置预付状态
                    ClosePrepay(fxUserId);
                }
                else
                {
                    //2.1检查是否是1688分销店铺
                    var key = BusinessSettingKeys.SupplyBy1688.ShopBy1688;
                    var systemShopId = new FxUserShopService().GetFxUserIdMapping(new List<int> { fxUserId })?.FirstOrDefault()?.ShopId ?? 0;
                    var csService = new CommonSettingService();
                    var aliShopId = csService.Get(key, systemShopId, false)?.Value.ToInt() ?? 0;
                    if (shopIds != null && aliShopId > 0 && shopIds.Contains(aliShopId))
                    {
                        //前置检查
                        var hasWaitSendOrder = new PurchaseOrderRelationService().IsHasWaitSendByPurchaseOrderShopId(aliShopId);
                        if (hasWaitSendOrder)
                            throw new LogicException($"店铺（ShopId={aliShopId}）存在待发货的预付单");

                        //2.2逐个商家关闭预付（相关订单按规则变化）
                        ClosePrepayAndChangePrepayOrder(fxUserId, systemShopId);

                        //删除1688下单店铺配置
                        var sql = $"UPDATE P_CommonSetting SET ShopId=ABS(ShopId) * -1,[Value]='-'+[Value]  WHERE [Key] = '{key}' AND ShopId={systemShopId} AND [Value]='{aliShopId}'";
                        var db = csService.baseRepository.DbConnection;
                        db.Execute(sql);

                        //清除Redis
                        var redisKey = $"{key}/{systemShopId}";
                        try
                        {
                            RedisHelper.Del(redisKey);
                        }
                        catch(Exception ex)
                        {
                            Log.WriteError($"清除Redis时异常，redisKey={redisKey}时异常：{ex}");
                        }

                        try
                        {
                            //其他云配置库更新
                            var otherCloudConfig = DbApiAccessUtility.GetTouTiaoConfigureDb();
                            otherCloudConfig.ExecuteNonQuery(sql);

                            otherCloudConfig = DbApiAccessUtility.GetPddConfigureDb();
                            otherCloudConfig.ExecuteNonQuery(sql);
                        }
                        catch(Exception ex)
                        {
                            Log.WriteError($"更新其他云P_CommonSetting，Key='{key}'，systemShopId={systemShopId}，Value={aliShopId}时异常：{ex}");
                        }
                    }

                }

            }
            catch (LogicException ex)
            {
                Log.WriteError($"检查并关闭预付时LogicException：{ex}");
                throw ex;
            }
            catch (Exception ex)
            {
                Log.WriteError($"检查并关闭预付时异常：{ex}");
            }
        }
    }
}
