using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services.Services
{
	public class FxUserShopDefaultSupplierService : BaseService<Data.Entity.FxUserShopDefaultSupplier>
	{
		private FxUserShopDefaultSupplierRepository _repository = null;


        public FxUserShopDefaultSupplierService()
        {
            _repository = new FxUserShopDefaultSupplierRepository();
        }

        /// <summary>
        /// 获取厂家信息
        /// </summary>
        /// <param name="shopIds"></param>
        /// <returns></returns>
        public List<FxUserShopDefaultSupplier> GetDefaultSupplierListByShopIds(List<int> shopIds,bool needEncryptAccount = false)
        {
            return _repository.GetDefaultSupplierListByShopIds(shopIds, needEncryptAccount);
        }

        public FxUserShopDefaultSupplier GetInfoByShopId(int shopId)
        {
            return _repository.GetInfoByShopId(shopId);
        }

        public FxUserShopDefaultSupplier GetInfoById(int id)
        {
            return _repository.GetInfoById(id);
        }

        public FxUserShopDefaultSupplier GetInfoBySupplierUserId(int supplierUserId)
        {
            return _repository.GetInfoBySupplierUserId(supplierUserId);
        }

        public int AddModel(FxUserShopDefaultSupplier model)
        {
            return _repository.AddModel(model);
        }


        public bool UpdateSupplierById(int id,int supplierFxUserId)
        {
            return _repository.UpdateSupplierById(id, supplierFxUserId);
        }

        /// <summary>
        /// 按主键id删除
        /// </summary>
        /// <param name="id"></param>
        public bool UpdateDelById(int id)
        {
            return _repository.UpdateDelById(id);
        }

        /// <summary>
        /// 按厂家删除店铺绑定厂家数据
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="supplierFxUserId"></param>
        public bool UpdateDelBySupplier(int fxUserId, int supplierFxUserId)
        {
            return _repository.UpdateDelBySupplier(fxUserId, supplierFxUserId);
        }
    }
}
