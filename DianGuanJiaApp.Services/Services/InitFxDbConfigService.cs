using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Enum;
using System.Threading;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.Model;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Data.Model;
using vipapis.puma;

namespace DianGuanJiaApp.Services
{
    public class InitFxDbConfigService : BaseService<FxDbConfig>
    {
        /// <summary>
        /// 统计用户抖店店铺的订单数量、路径流节点数
        /// </summary>
        public void StatOrder()
        {
            var _shopService = new ShopService();
            var _statOrderService = new StatOrderService();
            var _statPathFlowService = new StatPathFlowService();
            var _dbConfigRepository = new DbConfigRepository();
            var _csService = new CommonSettingService();
            var pageSize = 200;
            var pageIndex = 1;
            //倍数
            var BASIC_TIMES = 35;
            var dtNow = DateTime.Now;
            var statOrderStatus = CustomerConfig.StatOrderStatus;
            var key = $"FxSystem/StatOrder/{statOrderStatus}/MaxId";
            var maxId = _csService.Get(key, -1, false)?.Value.ToInt() ?? 0;

            while (true)
            {
                //1、查出分单抖店用户Id+店铺Id
                var list = _shopService.GetFxUserIdAndShopId(pageSize, pageIndex, "TouTiao", maxId);
                pageIndex++;

                if (list == null || !list.Any())
                    break;

                //2、查出用户所在业务库
                var curFxUserIds = list.Select(a => a.FxUserId).ToList();
                var dbList = _dbConfigRepository.GetListByFxUserIds(curFxUserIds, CustomerConfig.CloudPlatformType);

                var curStatOrders = new List<StatOrder>();
                var curStatPathFlows = new List<StatPathFlow>();

                //3、统计出店铺订单待发货数据\统计出路径流数据
                list.ForEach(item =>
                {
                    //用户所在的业务库
                    var curBusinessDb = dbList.FirstOrDefault(a => a.DbConfig.UserId == item.FxUserId);
                    //订单数量
                    var orderCount = 0;
                    //查询重试3次
                    for (var i = 0; i < 3; i++)
                    {
                        try
                        {
                            orderCount = new OrderService(curBusinessDb?.ConnectionString).GetStatOrder(item.ShopId, statOrderStatus);
                            break;
                        }
                        catch (Exception ex)
                        {
                            if (i > 2)
                            {
                                throw ex;
                            }
                            Thread.Sleep(100 * i);
                        }
                    }
                    var sumNum = orderCount * BASIC_TIMES;

                    if (sumNum < 100)
                        sumNum = 100;

                    //路径流节点数
                    var statPathFlows = new List<PathFlowCodeAndTotalCount>();
                    if (!string.IsNullOrEmpty(curBusinessDb?.ConnectionString))
                        statPathFlows = new PathFlowService(curBusinessDb.ConnectionString).GetStatPathFlowNodeNum(new List<int> { item.FxUserId });
                    curStatOrders.Add(new Data.Entity.StatOrder
                    {
                        ShopId = item.ShopId,
                        FxUserId = item.FxUserId,
                        DbName = curBusinessDb?.DbNameConfig.DbName,
                        RealNum = orderCount,
                        SumNum = sumNum,
                        StatType = statOrderStatus,
                        CreateTime = dtNow,
                    });
                    curStatPathFlows.AddRange(statPathFlows.Select(stat => new StatPathFlow
                    {
                        ShopId = item.ShopId,
                        FxUserId = item.FxUserId,
                        DbName = curBusinessDb?.DbNameConfig.DbName,
                        PathFlowCode = stat.PathFlowCode,
                        NodeNum = stat.TotalCount,
                        CreateTime = dtNow,
                    }));
                });

                _statOrderService.BulkMerger(curStatOrders);
                _statPathFlowService.BulkMerger(curStatPathFlows);

                _csService.Set(key, list.Max(a => a.ShopId).ToString2(), -1);

                Thread.Sleep(100);

                if (list.Count() < pageSize)
                    break;
            }

        }

        /// <summary>
        /// 统计用户路径流订单数
        /// </summary>
        public void StatOrderByPathFlow()
        {
            var _shopService = new ShopService();
            var _statOrderService = new StatOrderService();
            var _statPathFlowService = new StatPathFlowService();
            var _dbConfigRepository = new DbConfigRepository();
            var _csService = new CommonSettingService();
            var pageSize = 200;
            var pageIndex = 1;
            var dtNow = DateTime.Now;
            var statOrderStatus = CustomerConfig.StatOrderStatus;
            var _fxDbConfigService = new FxDbConfigService();
            //指定的源业务库Id
            var sourceDbNameConfigIds = CustomerConfig.SameCloudSourceDbNameConfigIds;
            var key = $"FxSystem/StatLogicOrderByPathFlow/FxDbConfig/MaxId";
            var maxId = _csService.Get(key, -1, false)?.Value.ToInt() ?? 0;

            while (true)
            {
                //1、查出分单抖店用户Id
                var list = _fxDbConfigService.GetList(pageIndex, pageSize, sourceDbNameConfigIds, 1, maxId);
                pageIndex++;

                if (list == null || !list.Any())
                    break;

                //2、查出用户所在业务库
                var curFxUserIds = list.Select(a => a.FxUserId).ToList();
                var dbList = _dbConfigRepository.GetListByFxUserIds(curFxUserIds, CustomerConfig.CloudPlatformType);

                var curStatOrders = new List<StatOrder>();

                //3、统计出店铺订单待发货数据\统计出路径流数据
                list.ForEach(item =>
                {
                    //用户所在的业务库
                    var curBusinessDb = dbList.FirstOrDefault(a => a.DbConfig.UserId == item.FxUserId);
                    //所有路径流
                    var pathFlows = new PathFlowService(curBusinessDb?.ConnectionString).GetPathFlowWithFxUserId(item.FxUserId);

                    pathFlows.ForEach(p =>
                    {

                        //订单数量
                        var orderCount = 0;
                        //查询重试3次
                        for (var i = 0; i < 3; i++)
                        {
                            try
                            {
                                orderCount = new OrderService(curBusinessDb?.ConnectionString).GetStatLogicOrderByPathFlow(p.PathFlowCode);
                                break;
                            }
                            catch (Exception ex)
                            {
                                if (i > 2)
                                {
                                    throw ex;
                                }
                                Thread.Sleep(100 * i);
                            }
                        }

                        curStatOrders.Add(new Data.Entity.StatOrder
                        {
                            ShopId = p.SourceShopId,
                            FxUserId = p.SourceFxUserId,
                            DbName = curBusinessDb?.DbNameConfig.DbName,
                            RealNum = orderCount,
                            SumNum = 0,
                            StatType = p.PathFlowCode,
                            CreateTime = dtNow,
                        });

                    });
                });

                _statOrderService.BulkMerger(curStatOrders, true);

                _csService.Set(key, list.Max(a => a.Id).ToString2(), -1);

                Thread.Sleep(100);

                if (list.Count() < pageSize)
                    break;
            }

        }

        /// <summary>
        /// 统计用户厂家信息
        /// </summary>
        public void StatSupplier()
        {
            var _shopService = new ShopService();
            var _statRelationSupplierService = new StatRelationSupplierService();
            var _dbConfigRepository = new DbConfigRepository();
            //var _fxDbConfigRepository = new FxDbConfigRepository();
            var _csService = new CommonSettingService();
            var pageSize = 200;
            var pageIndex = 1;
            var dtNow = DateTime.Now;
            //var key = $"FxSystem/StatSupplier/MaxFxUserId";
            //var maxId = _csService.Get(key, -1, false)?.Value.ToInt() ?? 0;
            var maxId = 0;

            while (true)
            {
                //1、查出分单抖店用户Id+所在源库Id
                var list = _shopService.GetFxUserIdInWaitMigrateTouTiaoShop(pageSize, pageIndex, maxId);
                pageIndex++;

                if (list == null || !list.Any())
                    break;

                //2、查出用户所在业务库
                var curFxUserIds = list.Select(a => a.FxUserId).ToList();
                var dbList = _dbConfigRepository.GetListByFxUserIds(curFxUserIds, CustomerConfig.CloudPlatformType);

                var curStatRelationSuppliers = new List<StatRelationSupplier>();
                var groupDb = dbList.GroupBy(a => a.DbConfig.DbNameConfigId);
                groupDb.ToList().ForEach(db => {
                    if (string.IsNullOrEmpty(db.First().ConnectionString))
                        return;
                    var curDbNameConfigId = db.Key;
                    var tempFxUserIds = list.Where(a => curDbNameConfigId == a.ShopId).Select(a => a.FxUserId).Distinct().ToList();
                    //查出厂家数据
                    var tempModels = new PathFlowNodeService(db.First().ConnectionString).GetSupplierFxUserIds(tempFxUserIds);
                    var supplierFxUserIds = tempModels.Select(a => a.ShopId).Distinct().ToList();

                    //获取厂家已分配的业务库数据
                    var supplierDbList = _dbConfigRepository.GetFxDbConfigModels(supplierFxUserIds);

                    tempModels.ForEach(m => {
                        var existDb = supplierDbList.FirstOrDefault(a => a.DbConfig.UserId == m.ShopId);

                        curStatRelationSuppliers.Add(new StatRelationSupplier {
                            FxUserId = m.FxUserId,
                            SupplierFxUserId = m.ShopId,
                            SourceDbNameConfigId = curDbNameConfigId,
                            TargetDbServerConfigId = existDb?.DbNameConfig?.DbServerConfigId,
                            TargetDbNameConfigId = existDb?.DbNameConfig?.Id,
                            TargetDbName = existDb?.DbNameConfig?.DbName,
                            CreateTime = dtNow
                        });
                    });

                });

                _statRelationSupplierService.BulkMerger(curStatRelationSuppliers);

                //_csService.Set(key, list.Max(a => a.FxUserId).ToString2(), -1);

                Thread.Sleep(50);

                if (list.Count() < pageSize)
                    break;
            }

        }

        /// <summary>
        /// 初始化分配用户的抖店云业务库
        /// </summary>
        public void InitTouTiaoFxDbConfig()
        {
            var pageSize = 200;
            var pageIndex = 1;
            var dbIds = new List<int>() { 12264, 12265, 12266, 12267, 12268, 12269, 12270, 12271, 12272, 12273, 12274, 12275, 12276, 12277, 12278, 12279, 12280, 12281, 12282, 12283, 12284, 12285, 12286, 12287, 12288, 12289, 12290, 12291, 12292, 12293, 12294, 12295, 12296, 12297, 12298, 12299, 12300, 12301, 12302, 12303, 12304, 12305, 12306, 12307, 12308, 12309, 12310, 12311, 12312, 12313, 12314, 12315, 12316, 12317, 12318, 12319, 12320, 12321, 12322, 12323 };

            var cloudPlatform = "TouTiao";
            var _service = new FxDbConfigService();
            var _tempService = new TempFxDbConfigService();
            //var _statPathFlowService = new StatPathFlowService();
            var _supplierUserService = new SupplierUserService();
            var _statOrderService = new StatOrderService();
            var _tempRepository = new TempFxDbConfigRepository();
            var _dbConfigRepository = new DbConfigRepository();

            //每个库最大订单量（待发货状态），默认20W
            var MAX_ORDER_COUNT = CustomerConfig.DbMaxOrderCount;
            //已分配订单数量
            var existList = _tempService.GetDbIdAndTotalCount();
            //只取参与分配的库
            existList = existList.Where(a => dbIds.Contains(a.Id)).ToList();
            var existDbIds = existList.Select(a => a.Id).ToList();

            var notExistDbIds = dbIds.Where(a => !existDbIds.Contains(a)).ToList();
            if (notExistDbIds.Any())
            {
                existList.AddRange(notExistDbIds.Select(dbId => new Data.Model.IdAndTotalCount
                {
                    Id = dbId,
                    TotalCount = 0
                }).ToList());
            }

            //已分配用户
            var dicFxUserIds = new Dictionary<int, int>();

            while (true)
            {
                //按订单总数倒序取数据
                var list = _statOrderService.GetFxUserIdAndTotalCount(pageSize, pageIndex);
                pageIndex++;

                if (list == null || !list.Any())
                    break;

                var curFxUserIds = list.Select(a => a.Id).ToList();
                var dbList = _dbConfigRepository.GetListByFxUserIds(curFxUserIds);

                foreach (var fxUserId in curFxUserIds)
                {
                    //已分配跳过
                    if (dicFxUserIds.ContainsKey(fxUserId))
                        continue;

                    //用户所在的业务库
                    var curBusinessDb = dbList.FirstOrDefault(a => a.DbConfig.UserId == fxUserId);

                    if (curBusinessDb == null)
                    {
                        Log.WriteLine($"FxUserId={fxUserId}找不到业务库，跳过");
                        continue;
                    }

                    //所有路径流相关联的用户Id
                    var relFxUserIds = new PathFlowService(curBusinessDb?.ConnectionString).GetAllRelationFxUserIds(fxUserId);

                    //获取相关联的商家和厂家
                    var relList = _supplierUserService.GetAgentOrSuplierIds(new List<int> { fxUserId });
                    relFxUserIds.AddRange(relList);

                    //再次查询相关联的商家和厂家
                    relFxUserIds.AddRange(_supplierUserService.GetAgentOrSuplierIds(relList));
                    relFxUserIds = relFxUserIds.Distinct().ToList();

                    //已分配FxDbConfig的用户
                    var existFxUserIds = _service.GetFxDbConfigs(relFxUserIds, cloudPlatform).Select(a => a.FxUserId).ToList();
                    if (existFxUserIds.Any())
                    {
                        existFxUserIds.ForEach(fxId =>
                        {
                            if (!dicFxUserIds.ContainsKey(fxId))
                                dicFxUserIds.Add(fxId, 1);
                        });
                        //排除已分配
                        relFxUserIds = relFxUserIds.Where(a => !existFxUserIds.Contains(a)).ToList();
                    }

                    //已分配TempFxDbConfig的用户
                    var existTempFxUserIds = _tempService.GetByFxUserIds(relFxUserIds, cloudPlatform).Select(a => a.FxUserId).ToList();
                    if (existTempFxUserIds.Any())
                    {
                        existTempFxUserIds.ForEach(fxId =>
                        {
                            if (!dicFxUserIds.ContainsKey(fxId))
                                dicFxUserIds.Add(fxId, 1);
                        });
                        //排除已分配
                        relFxUserIds = relFxUserIds.Where(a => !existTempFxUserIds.Contains(a)).ToList();
                    }

                    //获取预估的订单总数
                    var userOrderTotalCount = _statOrderService.GetFxUserIdAndTotalCount(relFxUserIds);

                    //拿第一个未满数量的库
                    var curDb = existList.Where(a => a.TotalCount < MAX_ORDER_COUNT).OrderBy(a => a.TotalCount).FirstOrDefault();
                    if (curDb == null)
                        throw new Exception("已无可分配的库");

                    //累计预估订单数量
                    curDb.TotalCount += userOrderTotalCount.Sum(a => a.TotalCount);

                    var addList = new List<TempFxDbConfig>();
                    addList.AddRange(relFxUserIds.Select(fxId => new TempFxDbConfig
                    {
                        FxUserId = fxId,
                        DbCloudPlatform = cloudPlatform,
                        FromFxDbConfig = -1,
                        DbNameConfigId = curDb.Id,
                        OrderCount = userOrderTotalCount.FirstOrDefault(a => a.Id == fxId)?.TotalCount ?? 100,
                        SourceDbNameConfigId = curBusinessDb?.DbConfig.DbNameConfigId,
                    }).ToList());

                    _tempRepository.BulkWrite(addList, "TempFxDbConfig");

                };

                Thread.Sleep(100);

                if (list.Count() < pageSize)
                    break;
            }
        }

        /// <summary>
        /// 针对无抖店店铺的用户，初始化分配抖店云业务库
        /// </summary>
        public void InitNotHasTouTiaoShopUserFxDbConfig()
        {
            var topNum = 500;
            //var dbIds = CustomerConfig.TargetDbNameConfigIdList;
            var _csService = new CommonSettingService();
            var key = $"/System/Fendan/FxDbConfig/DbNameConfigIdList/TouTiaoCloud";
            var dbIds = _csService.Get(key, 0)?.Value ?? "";
            var arrDbNameId = dbIds.Split(',');

            var cloudPlatform = "TouTiao";
            var _service = new FxDbConfigService();

            while (true)
            {
                //查询
                var list = _service.GetNotTouTiaoShopUserList(topNum);

                if (list == null || !list.Any())
                    break;

                var dtNow = DateTime.Now;

                var existFxUserIds = new List<int>();
                var addList = new List<FxDbConfig>();
                foreach (var fxDbConfig in list)
                {
                    var fxUserId = fxDbConfig.FxUserId;
                    if (fxUserId <= 0)
                        continue;

                    if (fxDbConfig.Id == 0 || fxDbConfig.DbNameConfigId == 0)
                    {
                        var index = fxUserId % arrDbNameId.Count();
                        var curDbNameConfigId = arrDbNameId[index].ToInt();
                        addList.Add(new FxDbConfig
                        {
                            FromFxDbConfig = 1,
                            FxUserId = fxUserId,
                            SystemShopId = fxDbConfig.SystemShopId,
                            DbNameConfigId = curDbNameConfigId,
                            DbCloudPlatform = cloudPlatform,
                            CreateTime = dtNow,
                            Status = "NotHasTouTiaoShop.Init"
                        });
                    }
                    else
                        existFxUserIds.Add(fxUserId);

                };

                //存在更新
                if (existFxUserIds.Any())
                    _service.SetFxDbConfigByFxUserId(existFxUserIds);

                //不存在添加
                if (addList.Any())
                    _service.TryToCreateCloudFxDbConfig(addList, new List<string> { cloudPlatform }, false);

                Log.WriteLine($"存在的更新：{existFxUserIds.ToJson()}，不存在添加：{addList.Select(a => new { a.FxUserId,a.SystemShopId,a.DbCloudPlatform }).ToJson()}");

                Thread.Sleep(100);

                if (list.Count() < topNum || CustomerConfig.IsDebug)
                    break;
            }
        }
    }
}