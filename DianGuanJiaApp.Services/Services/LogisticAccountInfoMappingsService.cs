using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;

namespace DianGuanJiaApp.Services
{

    public partial class LogisticAccountInfoMappingsService : BaseService<Data.Entity.LogisticAccountInfoMappings>
    {

        public LogisticAccountInfoMappingsService()
        {
            _repository = new LogisticAccountInfoMappingsRepository();
            base._baseRepository = _repository;
        }

        #region 私有变量

        private LogisticAccountInfoMappingsRepository _repository;

        #endregion
    }
}
