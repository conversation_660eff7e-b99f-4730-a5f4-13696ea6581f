using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;

namespace DianGuanJiaApp.Services
{

    public partial class LogisticAddServiceService : BaseService<Data.Entity.LogisticAddService>
    {

        public LogisticAddServiceService()
        {
            _repository = new LogisticAddServiceRepository();
            base._baseRepository = _repository;
        }

        #region 私有变量

        private LogisticAddServiceRepository _repository;

        #endregion

        public List<Data.Entity.LogisticAddService> GetList(string code)
        {
            return _repository.GetList(code);
        }
    }
}
