using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;

namespace DianGuanJiaApp.Services
{

    public partial class LogisticPayTypesService : BaseService<Data.Entity.LogisticPayTypes>
    {

        public LogisticPayTypesService()
        {
            _repository = new LogisticPayTypesRepository();
            base._baseRepository = _repository;
        }

        #region 私有变量

        private LogisticPayTypesRepository _repository;

        #endregion

        public List<Data.Entity.LogisticPayTypes> GetList(string code)
        {
            return _repository.GetList(code);
        }
    }
}
