using DianGuanJiaApp.Data.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services
{
    /// <summary>
    /// 对逻辑订单指定路径流
    /// </summary>
    public interface IAssignLogicOrder
    {
        /// <summary>
        /// 对逻辑订单下一个节点进行路径流指派
        /// 如果下一个节点和现有路径流重合则不做修改,并返回null        
        /// 否则得到新的路径流并返回
        /// 新路径为操作者后面追加下一节点
        /// </summary>
        /// <param name="logicorder">逻辑订单</param>
        /// <param name="oepratorUserid">操作者id,当前节点</param>
        /// <param name="downUserid">下一节点</param>
        /// <returns></returns>
        PathFlow AssignPathFlow(LogicOrder logicorder, PathFlow pf, List<PathFlowNode> pathnodes, int oepratorUserid, int downUserid);
    }
}
