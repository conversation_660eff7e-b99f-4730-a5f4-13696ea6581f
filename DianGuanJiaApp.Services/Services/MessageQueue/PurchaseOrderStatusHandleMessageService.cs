using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.RabbitMQ;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Services.Services.MessageQueue
{
    public static class PurchaseOrderStatusHandleMessageService
    {
        /// <summary>
        /// 1688采购单延迟消息描述
        /// </summary>
        public static MessageDescription AlibabaQingOrderPurchaseOrderDelayMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fendan.alibaba.qingorder.purchaseorder.delay.[23h/46h]",
                    QueueName = "fendan.alibaba.qingorder.purchaseorder.delay.[23h/46h]",
                    IsDelayQueue = true,
                    XMessageTTL = 1000 * (CustomerConfig.AutoCancelPurchaseOrderMinutes * 60),
                    XDeadLetterExchange = "fendan.alibaba.qingorder.purchaseorder",
                    XDeadLetterRoutingKey = "fendan.alibaba.qingorder.purchaseorder"
                };
            }
        }

        /// <summary>
        /// 1688采购单消息描述
        /// </summary>
        public static MessageDescription AlibabaQingOrderPurchaseOrderMessageDescription
        {
            get
            {
                return new MessageDescription
                {
                    ExchangeName = "fendan.alibaba.qingorder.purchaseorder",
                    QueueName = "fendan.alibaba.qingorder.purchaseorder"
                };
            }
        }

        /// <summary>
        /// 推送消息
        /// </summary>
        /// <param name="messageModel"></param>
        /// <returns></returns>
        public static bool SendMessage(PurchaseOrderStatusHandleMessageModel messageModel)
        {
            return RabbitMQService.SendMessage(messageModel, AlibabaQingOrderPurchaseOrderDelayMessageDescription);
        }

        /// <summary>
        /// 消费者处理消息
        /// </summary>
        public static void ConsumerListenMessage()
        {
            RabbitMQService.ConsumerListenMessage<PurchaseOrderStatusHandleMessageModel>(
                AlibabaQingOrderPurchaseOrderMessageDescription, MessagesHandle);
        }
        /// <summary>
        /// 消息处理
        /// </summary>
        /// <param name="messages"></param>
        /// <param name="messageCount"></param>
        /// <returns></returns>
        public static bool MessagesHandle(List<PurchaseOrderStatusHandleMessageModel> messages, int messageCount)
        {
            //排空处理
            if (messages == null || !messages.Any())
            {
                return true;
            }
            //消息处理
            messages.ForEach(message =>
            {
                //初始站点上下文
                var fxUser = new UserFxService().Get(message.FxUserId);
                if (fxUser == null)
                {
                    return;
                }
                var siteContext = new SiteContext(fxUser,
                    new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });
                //不同类型，处理逻辑可能不同
                switch (message.ActionType)
                {
                    case "create":
                        var service = new PurchaseOrderRelationService();
                        service.CheckTimeOutUnPaidCancelPurchaseOrder(message.FxUserId, message.LogicOrderId,
                            message.PurchaseOrderPlatformId);
                        break;
                }
            });
            //处理
            return true;
        }
    }
}