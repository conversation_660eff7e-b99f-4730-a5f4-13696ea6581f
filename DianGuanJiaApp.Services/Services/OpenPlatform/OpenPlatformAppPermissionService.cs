using DianGuanJiaApp.Data.Entity.OpenPlatform;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Repository.OpenPlatform;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System.Collections.Generic;

namespace DianGuanJiaApp.Services.OpenPlatform
{
    public class OpenPlatformAppPermissionService
    {
        private OpenPlatformAppPermissionRepository _repository;

        public OpenPlatformAppPermissionService()
        {
            _repository = new OpenPlatformAppPermissionRepository();
        }

        public OpenPlatformAppPermission GetCache(long appId, string method)
        {
            var uniqueCode = (appId + method).ToShortMd5();
            var key = CacheKeys.OpenPlatformPermissionCacheKey.Replace("{uniqueCode}", uniqueCode);
            var model = RedisHelper.Get<OpenPlatformAppPermission>(key);
            if (model == null)
            {
                model = _repository.GetByAppIdAndMethod(appId, method);
                if (model == null)
                {
                    //设置一个空数据，防止缓存穿透
                    model = new OpenPlatformAppPermission();
                    RedisHelper.Set(key, model, 60 * 5);
                }
                else
                    RedisHelper.Set(key, model);
            }
            if (model.Id == 0)
                throw new LogicException("应用暂无权限", OpenApiErrCode.NoPermission.ToString());
            return model;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="appId"></param>
        /// <returns></returns>
        public List<OpenPlatformAppPermission> GetListByAppId(long appId)
        {
            return _repository.GetListByAppId(appId);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="appId"></param>
        public void DeleteByAppId(long appId)
        {
            _repository.DeleteByAppId(appId);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="list"></param>
        public void AddList(List<OpenPlatformAppPermission> list)
        {
            _repository.BulkInsert(list);
        }
    }
}