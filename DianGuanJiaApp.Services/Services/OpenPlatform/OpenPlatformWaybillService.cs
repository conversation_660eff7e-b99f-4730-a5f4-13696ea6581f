using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.OpenPlatform;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Services.Services.OpenPlatform
{
    public class OpenPlatformWaybillService
    {
        private readonly WaybillCodeService service;
        private readonly ShopService shopService;
        private readonly SupplierUserService supplierUserService;

        public OpenPlatformWaybillService()
        {
            service = new WaybillCodeService();
            shopService = new ShopService();
            supplierUserService = new SupplierUserService();
        }

        #region 私有方法

        /// <summary>
        /// 转换为底单查询参数
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private WaybillCodeRequestModel ToWaybillSearchModel(QueryWaybillListRequest model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var queryModel = new WaybillCodeRequestModel();
            queryModel.PageIndex = model.Page;
            queryModel.PageSize = model.PageSize;
            queryModel.StartDate = model.StartDate;
            queryModel.EndDate = model.EndDate;
            queryModel.AgentIdStr = model.AgentIds == null ? "" : string.Join(",", model.AgentIds);
            queryModel.ToProvince = model.ToProvinces == null ? "" : string.Join(",", model.ToProvinces);
            queryModel.Status = model.Status == null ? "" : string.Join(",", model.Status);
            queryModel.ExpressWaybillCode = model.ExpressNumbers == null ? "" : string.Join(",", model.ExpressNumbers);
            queryModel.CustomerOrderId = model.PlatformOrderIds == null ? "" : string.Join(",", model.PlatformOrderIds);
            queryModel.OrderId = model.LogicOrderIds == null ? "" : string.Join(",", model.LogicOrderIds);
            queryModel.SourceShopIds = model.ShopIds;
            queryModel.FxUserId = fxUserId;
            return queryModel;
        }

        /// <summary>
        /// 转换为厂家底单查询参数
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        private WaybillCodeRequestModel ToWaybillSearchModel(QuerySupplierWaybillListRequest model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var queryModel = new WaybillCodeRequestModel();
            queryModel.PageIndex = model.Page;
            queryModel.PageSize = model.PageSize;
            queryModel.StartDate = model.StartDate;
            queryModel.EndDate = model.EndDate;
            queryModel.SupplierIdStr = model.SupplierIds == null ? "" : string.Join(",", model.SupplierIds);
            queryModel.ToProvince = model.ToProvinces == null ? "" : string.Join(",", model.ToProvinces);
            queryModel.ExpressWaybillCode = model.ExpressNumbers == null ? "" : string.Join(",", model.ExpressNumbers);
            queryModel.CustomerOrderId = model.PlatformOrderIds == null ? "" : string.Join(",", model.PlatformOrderIds);
            queryModel.OrderId = model.LogicOrderIds == null ? "" : string.Join(",", model.LogicOrderIds);
            queryModel.SourceShopIds = model.ShopIds;
            queryModel.FxUserId = fxUserId;
            queryModel.Status = "3";//厂家底单只获取已发货的数据
            return queryModel;
        }

        /// <summary>
        /// 转换为底单数据
        /// </summary>
        /// <param name="waybill"></param>
        /// <param name="shops"></param>
        /// <returns></returns>
        public WaybillListRespone ParsWaybillList(WaybillCode waybill, List<Shop> shops, List<SupplierUser> agents, List<KeyValuePair<string, WaybillCodeOrder>> wOrderKvs)
        {
            var agent = agents.FirstOrDefault(t => t.FxUserId == waybill.UpFxUserId);
            var shop = shops.FirstOrDefault(t => t.Id == waybill.SourceShopId);
            var model = new WaybillListRespone();
            model.PlatformOrderId = waybill.CustomerOrderId;
            model.LogicOrderId = waybill.OrderId;
            model.Source = shop?.PlatformType;
            model.ToProvince = waybill.ToProvince;
            model.SendType = waybill.SendType;
            model.ExpressCode = waybill.ExpressNo;
            model.ExpressNumber = waybill.ExpressWayBillCode;
            model.TemplateName = waybill.TemplateName;
            model.ProductCount = waybill.ProductCount;
            model.TotalWeight = waybill.TotalWeight;
            model.CreateTime = waybill.GetDate.Format();
            model.Status = waybill.Status;
            model.SendContent = waybill.SendContent;
            model.TotalPayAomount = waybill.TotalPayAomount == null ? 0 : (waybill.TotalPayAomount * 100).ToLong();
            model.ShopId = waybill.UpFxUserId == 0 ? waybill.SourceShopId : 0;
            model.ShopName = waybill.UpFxUserId == 0 ? shop?.NickName : "";//自营订单才返回店铺名称
            model.SendDate = waybill.SendDate == null ? null : waybill.SendDate.Value.Format();
            model.DistributorName = agents.FirstOrDefault(t => t.FxUserId == waybill.UpFxUserId)?.AgentMobileAndRemark;
            if (!string.IsNullOrWhiteSpace(waybill.OrderIdJoin) && waybill.OrderId.StartsWith("C"))
            {
                model.ChildLogicOrderIds = waybill.OrderIdJoin.Split(',').ToList();
                var pids = wOrderKvs.Where(x => model.ChildLogicOrderIds.Contains(x.Key)).Select(x => x.Value.CustomerOrderId).Distinct().ToList();
                model.ChildPlatformOrderIds = pids;
            }
            return model;
        }

        /// <summary>
        /// 转换为厂家底单数据
        /// </summary>
        /// <param name="waybill"></param>
        /// <param name="shops"></param>
        /// <param name="suppliers"></param>
        /// <returns></returns>
        public SupplierWaybillListRespone ParsSupplierWaybillList(WaybillCode waybill, List<Shop> shops, List<SupplierUser> suppliers, List<SupplierUser> agents, List<PathFlowFxUserIdNodeModel> pathFlowNodes, List<KeyValuePair<string, WaybillCodeOrder>> wOrderKvs)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var shop = shops.FirstOrDefault(t => t.Id == waybill.SourceShopId);
            var model = new SupplierWaybillListRespone();
            model.PlatformOrderId = waybill.CustomerOrderId;
            model.LogicOrderId = waybill.OrderId;
            model.Source = shop?.PlatformType;
            model.ToProvince = waybill.ToProvince;
            model.SendType = waybill.SendType;
            model.ExpressCode = waybill.ExpressNo;
            model.ExpressNumber = waybill.ExpressWayBillCode;
            model.TemplateName = waybill.TemplateName;
            model.ProductCount = waybill.ProductCount;
            model.TotalWeight = waybill.TotalWeight;
            model.CreateTime = waybill.GetDate.Format();
            model.Status = waybill.Status;
            model.SendContent = waybill.SendContent;
            model.TotalPayAomount = waybill.TotalPayAomount == null ? 0 : (waybill.TotalPayAomount * 100).ToLong();
            model.ShopId = waybill.UserId == fxUserId ? waybill.SourceShopId : 0;
            model.ShopName = waybill.UserId == fxUserId ? shop?.NickName : "";//自营订单才返回店铺名称
            model.SendDate = waybill.SendDate == null ? null : waybill.SendDate.Value.Format();
            model.SupplierName = suppliers.FirstOrDefault(t => t.SupplierFxUserId == waybill.DownFxUserId)?.SupplierMobileAndRemark;
            //获取上一级的分销商名称
            if (waybill.UserId != fxUserId)
            {
                //通过路径流来处理
                var pathNode = pathFlowNodes.FirstOrDefault(t => t.PathFlowCode == waybill.PathFlowCode && t.FxUserId == fxUserId);
                if (pathNode != null)
                {
                    var upFxUserId = pathNode.UpFxUserId;
                    model.DistributorName = agents.FirstOrDefault(t => t.FxUserId == upFxUserId)?.AgentMobileAndRemark;
                }
            }
            if (!string.IsNullOrWhiteSpace(waybill.OrderIdJoin) && waybill.OrderId.StartsWith("C"))
            {
                model.ChildLogicOrderIds = waybill.OrderIdJoin.Split(',').ToList();
                var pids = wOrderKvs.Where(x => model.ChildLogicOrderIds.Contains(x.Key)).Select(x => x.Value.CustomerOrderId).Distinct().ToList();
                model.ChildPlatformOrderIds = pids;
            }
            return model;
        }

        #endregion 私有方法

        /// <summary>
        /// 底单查询
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public PagedRespone<WaybillListRespone> List(QueryWaybillListRequest model)
        {
            var queryModel = ToWaybillSearchModel(model);
            var isPdd = CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString();
            var result = service.LoadListV2(queryModel, new WaybillCodeLoadListModel { needPagging = true, isPdd = isPdd, QueryType = WaybillQueryType.DataAndCount });

            var page = new PagedRespone<WaybillListRespone>();
            page.Total = result.Total;
            if (page.Total == 0)
                return page;
            //获取店铺信息
            var shopIds = result.Rows.Select(t => t.SourceShopId).Distinct().ToList();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var agents = supplierUserService.GetAgentList(fxUserId, onlyGetCurDb: true, needEncryptAccount: true);
            var shops = shopService.GetListByShopIds(shopIds);
            //获取平台单号
            var wOrderKvs = GetPlatformOrderIds(result.Rows);
            page.Rows = new List<WaybillListRespone>();
            result.Rows.ForEach(t =>
            {
                page.Rows.Add(ParsWaybillList(t, shops, agents, wOrderKvs));
            });
            return page;
        }

        /// <summary>
        /// 厂家底单
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public PagedRespone<SupplierWaybillListRespone> SupplierList(QuerySupplierWaybillListRequest model)
        {
            var queryModel = ToWaybillSearchModel(model);
            var isPdd = CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString();
            var result = service.LoadListV2(queryModel, new WaybillCodeLoadListModel { needPagging = true, isPdd = isPdd, QueryType = WaybillQueryType.DataAndCount });

            var page = new PagedRespone<SupplierWaybillListRespone>();
            page.Total = result.Total;
            if (page.Total == 0)
                return page;
            //获取店铺信息
            var shopIds = result.Rows.Select(t => t.SourceShopId).Distinct().ToList();
            var shops = shopService.GetListByShopIds(shopIds);
            //获取厂家信息
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var suppliers = supplierUserService.GetSupplierList(fxUserId, onlyGetCurDb: true, needEncryptAccount: true);
            //获取商家信息
            var agents = supplierUserService.GetAgentList(fxUserId, onlyGetCurDb: true, needEncryptAccount: true);
            //获取流程节点
            var pathFlowNodeService = new PathFlowNodeService();
            var pathFlowCodes = result.Rows.Select(t => t.PathFlowCode).Distinct().ToList();
            var pathFlowNodes = pathFlowNodeService.GetFxUserByPathFlowCodes(pathFlowCodes);
            //获取平台单号
            var wOrderKvs = GetPlatformOrderIds(result.Rows);
            page.Rows = new List<SupplierWaybillListRespone>();
            result.Rows.ForEach(t =>
            {
                page.Rows.Add(ParsSupplierWaybillList(t, shops, suppliers, agents, pathFlowNodes, wOrderKvs));
            });
            return page;
        }

        /// <summary>
        /// 获取底单合单的平台单单号
        /// </summary>
        /// <param name="waybillCodes"></param>
        /// <returns></returns>
        private List<KeyValuePair<string, WaybillCodeOrder>> GetPlatformOrderIds(List<WaybillCode> waybillCodes)
        {
            //获取平台单号
            var oids = waybillCodes.Where(x => x.CustomerOrderId.IsNullOrEmpty() && x.OrderId.StartsWith("C") == false).Select(x => x.OrderId).ToList();
            var moids = waybillCodes.Where(x => x.OrderId.ToString2().StartsWith("C")).SelectMany(x => x.OrderIdJoin.ToString2().SplitToList(",")).ToList();
            if (moids.Any())
                oids.AddRange(moids);
            oids = oids.Distinct().ToList();
            var wOrderKvs = new List<KeyValuePair<string, WaybillCodeOrder>>();
            if (oids.Any())
            {
                var waybillCodeOrders = new WaybillCodeOrderService().GetWaybillCodeOrders(oids, new List<string> { "OrderId", "CustomerOrderId" });
                var orderDic = waybillCodeOrders.GroupBy(x => x.OrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault());
                wOrderKvs = orderDic.ToList();
            }
            return wOrderKvs;
        }
    }
}