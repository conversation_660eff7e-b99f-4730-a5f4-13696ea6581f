using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Aliyun.OSS;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.OpenTelemetry;
using DianGuanJiaApp.Data.Repository.ClickHouse;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Helpers;
using DianGuanJiaApp.Utility.NPOI;

namespace DianGuanJiaApp.Services.Services.OpenTelemetry
{
	public class OtlpClickHouseService
	{
		private readonly OtlpRepository _otelRepository;

		public OtlpClickHouseService()
		{
			_otelRepository = new OtlpRepository();
		}

		/// <summary>
		/// excel文件路径
		/// </summary>
		/// <param name="excelPath">C:\\Users\\<USER>\\Desktop\\分单功能模块.xlsx</param>
		public void AddApiModule(string excelPath,out string errMsg)
		{
			try
			{
				DataTable dt = ExcelHelper.GetExcelDataTable(out errMsg, out string exMsg, filePath: excelPath, fileExt: ".xlsx");

				if (dt != null && dt.Rows != null && dt.Rows.Count > 0)
				{
					//先清空数据
					_otelRepository.DelApiModule();

					for (var i = 0; i < dt.Rows.Count; i++)
					{
						if (!string.IsNullOrWhiteSpace(dt.Rows[i][1].ToString()))
						{
							_otelRepository.AddApiModule(dt.Rows[i][0].ToString(), dt.Rows[i][1].ToString(), dt.Rows[i][2].ToString());
						}
					}
				}
				else
				{
					errMsg = "未读取到数据";
				}
			}
			catch(Exception ex)
			{
				errMsg = ex.Message;
			}
		}

		/// <summary>
		/// 后端概要数据
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public SummaryExcelDataResponseModel GetBackSummaryExcelData(QueryOtlpRequestModel request)
		{
			return _otelRepository.GetBackSummaryExcelData(request);
		}

		/// <summary>
		/// 后端详细数据
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public List<DetailExcelDataResponseModel> GetBackDetailExcelDataList(QueryOtlpRequestModel request)
		{
			return _otelRepository.GetBackDetailExcelDataList(request);
		}

		/// <summary>
		/// 查询api基础数据
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public List<ApiBasicDataResponseModel> GetApiBasicDataList(QueryOtlpRequestModel request)
		{
			return _otelRepository.GetApiBasicDataList(request);
		}
	}
}
