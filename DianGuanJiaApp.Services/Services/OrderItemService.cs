using System.Collections.Generic;
using System;
using System.Linq;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Services
{

    public partial class OrderItemService : BaseService<Data.Entity.OrderItem>
    {
        private OrderItemRepository _repository;
        public OrderItemService()
        {
            _repository = new OrderItemRepository();
        }
        public OrderItemService(string connectionString) : base(connectionString)
        {
            _repository = new OrderItemRepository(connectionString);
        }

        /// <summary>
        /// 更新打印状态
        /// </summary>
        /// <param name="printType"></param>
        /// <param name="orderItemIds"></param>
        public void UpdatePrintStatus(int printType, List<int> orderItemIds)
        {
            _repository.UpdatePrintStatus(printType, orderItemIds);
        }

        /// <summary>
        /// 查询订单项列表
        /// </summary>
        /// <param name="query"></param>
        /// <param name="fields"></param>
        /// <returns></returns>
        public List<OrderItem> GetOrderItemList(OrderItemQueryModel query, List<string> fields = null)
        {
            return _repository.GetOrderItemList(query, fields);
        }

        public int BulkUpdateOrderItemFields(List<OrderItemChangeModel> models)
        {
            var result = _repository.BulkUpdateOrderItemFields(models);

            //#region 数据变更日志
            //var dcLogs = new List<DataChangeLog>();
            //models.Where(x => x.Fields.Any()).ToList().ForEach(o =>
            //{
            //    if (!dcLogs.Any(a => a.RelationKey == o.Item.OrderItemCode))
            //    {
            //        dcLogs.Add(new DataChangeLog
            //        {
            //            DataChangeType = DataChangeTypeEnum.UPDATE,
            //            TableTypeName = DataChangeTableTypeName.OrderItem,
            //            SourceShopId = o.Item.ShopId,
            //            SourceFxUserId = o.Item.UserId,
            //            RelationKey = o.Item.OrderItemCode,
            //            ExtField1 = "BulkUpdateOrderItemFields"
            //        });
            //    }
            //});
            //new DataChangeLogRepository().Add(dcLogs);
            //#endregion

            return result;
        }

        /// <summary>
        /// 获取订单项信息，为复制副本
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="whereFieldName"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public List<OrderItem> GetListForDuplication(List<string> codes, string whereFieldName = "OrderItemCode",
            string selectFields = "*")
        {
            if (codes == null || !codes.Any())
            {
                return new List<OrderItem>();
            }
            //返回数据
            var results = new List<OrderItem>();
            //分片查询
            var chunks = codes.ChunkList(500);
            chunks.ForEach(chunk =>
            {
                var models = _repository.GetListForDuplication(chunk, whereFieldName, selectFields);
                if (models != null && models.Any())
                {
                    results.AddRange(models);
                }
            });
            //返回
            return results;
        }
        /// <summary>
        /// 批量保存
        /// </summary>
        /// <param name="models"></param>
        public void BulkWrite(List<OrderItem> models)
        {
            _repository.BulkWrite(models, "P_OrderItem", maxSingleNum: 1);
        }

        /// <summary>
        /// 批量插入数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        /// <param name="isNotUpdate"></param>
        public void InsertsForDuplication(List<OrderItem> models, int isNotUpdate = 0)
        {
            //清理源库ID
            models.ForEach(m => { m.Id = 0; });

            var batchSize = 500;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();

                // 代码
                var codes = batchModels.Select(m => m.OrderItemCode).Distinct().ToList();
                // 可能要更新的字段
                var needUpdateFields = new List<string>()
                {
                    "OrderItemCode", "ShopId", "PlatformOrderId", "SubItemID", "IsSended", "Price", "ItemAmount",
                    "ProductSubject", "productCargoNumber",
                    "CargoNumber", "ProductImgUrl", "RefundStatus", "Status", "RefundAmount", "Weight",
                    "LogisticsStatus", "EntryDiscount", "Color", "Size", "Count",
                    "ExtAttr1", "ExtAttr2", "ExtAttr3", "ExtAttr4", "ExtAttr5", "ProductID", "SendedCount", "SpecId",
                    "SkuID", "SkuCode"
                };
                
                //存在的OrderItem列表
                var idAndCodes = _repository.GetExistIdAndCodes(codes);
                //全部不存在
                if (idAndCodes.IsNullOrEmptyList())
                {
                    try
                    {
                        _repository.BulkInsert(batchModels);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"OrderItemService.InsertsForDuplication时异常：{ex}，将用单条方式重试一次");

                        var db = _repository.DbConnection;
                        if (db.State == System.Data.ConnectionState.Closed)
                            db.Open();
                        using (db)
                        {
                            //单条
                            batchModels.ForEach(item =>
                            {
                                try
                                {
                                    db.Insert(item);
                                }
                                catch (Exception ex2)
                                {
                                    var errMsg = ex2.Message.ToLower();
                                    if (errMsg.Contains("duplicate key") || errMsg.Contains("pk_") || errMsg.Contains("primary key"))
                                    {
                                        //忽略
                                    }
                                    else
                                    {
                                        throw ex2;
                                    }
                                }
                            });
                        }
                    }
                    
                    continue;
                }

                //存在
                var existCodes = idAndCodes.Select(m => m.Code).ToList();
                if (isNotUpdate == 0)
                {
                    var selectFields = new List<string>(needUpdateFields) { "Id" };
                    var existList = _repository.GetListForDuplication(existCodes, selectFields: string.Join(",", selectFields));

                    var needUpdates = batchModels
                        .Where(m => existCodes.Contains(m.OrderItemCode)).ToList();
                    var keyFields = new List<string>() { "OrderItemCode" };
                    if (needUpdates.Any())
                    {
                        needUpdates.ForEach(o =>
                        {
                            var model = existList.FirstOrDefault(
                                m => m.OrderItemCode == o.OrderItemCode);
                            if (model == null)
                            {
                                return;
                            }

                            o.Id = model.Id;
                        });

                        try
                        {
                            var tuple = EntityUtils.CompareFields<OrderItem>(existList, needUpdates, needUpdateFields, keyFields);
                            needUpdates = tuple.Item2;
                            needUpdateFields = tuple.Item1;
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"OrderItem更新，与数据库旧数据比较失败：{ex}，将使用旧逻辑进行全量更新");
                        }
                        _repository.BatchUpdate(needUpdates, needUpdateFields, keyFields);
                        Log.Debug(() => $"LogicOrderItem 副本更新数据{needUpdates.Count}条，更新字段{needUpdateFields.ToJson()}",
                            "wm_order2.txt");
                    }
                }

                //不存在
                var inserts = batchModels
                    .Where(m => !existCodes.Contains(m.OrderItemCode)).ToList();
                if (inserts.Any())
                {
                    try
                    {
                        _repository.BulkInsert(inserts);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"OrderItemService.InsertsForDuplication时异常：{ex}，将用单条方式重试一次");

                        var db = _repository.DbConnection;
                        if (db.State == System.Data.ConnectionState.Closed)
                            db.Open();
                        using (db)
                        {
                            //单条
                            inserts.ForEach(item =>
                            {
                                try
                                {
                                    db.Insert(item);
                                }
                                catch (Exception ex2)
                                {
                                    var errMsg = ex2.Message.ToLower();
                                    if (errMsg.Contains("duplicate key") || errMsg.Contains("pk_") || errMsg.Contains("primary key"))
                                    {
                                        //忽略
                                    }
                                    else
                                    {
                                        throw ex2;
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }
    }
}
