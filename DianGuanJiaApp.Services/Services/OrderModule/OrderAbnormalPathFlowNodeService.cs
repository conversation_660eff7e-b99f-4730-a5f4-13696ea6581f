using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Entity.OrderModule;
using DianGuanJiaApp.Data.Repository.OrderModule;
using DianGuanJiaApp.Utility.Extension;
using Log = DianGuanJiaApp.Utility.Log;

namespace DianGuanJiaApp.Services.Services.OrderModule
{
    public class OrderAbnormalPathFlowNodeService : BaseService<OrderAbnormalPathFlowNode>
    {
        private readonly OrderAbnormalPathFlowNodeRepository _repository;

        public OrderAbnormalPathFlowNodeService()
        {
            _repository = new OrderAbnormalPathFlowNodeRepository();
            _baseRepository = _repository;
        }

        public bool InsertOrUpdates(List<OrderAbnormalPathFlowNode> models)
        {
            //判断处理
            if (models == null || !models.Any())
            {
                return true;
            }
            //保存
            return _repository.InsertDuplicateUpdate(models, (items, sql, e) =>
            {
                Log.WriteError(
                    $"保存异常订单路径流节点信息，相关信息：{items.ToJson(true)}，执行SQL：{sql}，异常信息：{e.Message}，堆栈信息：{e.StackTrace}",
                    $"OrderAbnormalPathFlowNodeError_{DateTime.Now:yyyy-MM-dd}.log");
            });
        }
    }
}