extern alias snsdk;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Enum;
using System.Threading;
using System.Diagnostics;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Services.PlatformService.Track.Base;
using DianGuanJiaApp.Services.Services.SyncDataInterface;
using System.Data.Common;

namespace DianGuanJiaApp.Services
{

    public partial class OrderSelfDeliveryService : BaseService<Data.Entity.OrderSelfDelivery>
    {

        private OrderSelfDeliveryRepository _repository = null;

        public OrderSelfDeliveryService()
        {
            _repository = new OrderSelfDeliveryRepository();
        }

        public OrderSelfDeliveryService(string connectionString) : base(connectionString)
        {
            _repository = new OrderSelfDeliveryRepository(connectionString);
        }

        /// <summary>
        /// 根据批次号获取自寄物流信息
        /// </summary>
        /// <param name="batchNoList"></param>
        /// <returns></returns>
        public List<OrderSelfDelivery> GetList(List<string> batchNoList)
        {
            return _repository.GetList(batchNoList);
        }
    }

}
