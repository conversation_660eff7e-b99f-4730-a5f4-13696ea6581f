extern alias snsdk;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System.IO;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services.Services.ManualOrder;
using System.Diagnostics;
using DianGuanJiaApp.Services.Services.SyncDataInterface;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using Nest;
using DianGuanJiaApp.Data.Dapper;
using System.Data.Common;
using DianGuanJiaApp.Services.Services.SettingsService;
using Jd.Api.Domain;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Services.Services.OrderModule;
using System.Net.NetworkInformation;
using DianGuanJiaApp.Data.Entity.OrderModule;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using DianGuanJiaApp.Services.Services;
using MongoDB.Driver;
using System.Threading;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.RabbitMQ;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility.Helpers;
using RabbitMQ.Client.Framing.Impl;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Utility.Web;
using DianGuanJiaApp.Data.Model.BaseProduct;
using Elasticsearch.Net;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Services.Model;
using snsdk::suning_api_sdk.Models.CustomShopModel;
using DianGuanJiaApp.Services.Services.MessageQueue;
using DianGuanJiaApp.SiteMessage.Model.Request;
using DianGuanJiaApp.Data.MessageModel;
using DianGuanJiaApp.Data.Model.LogModel;

namespace DianGuanJiaApp.Services
{

    public partial class ProductFxService : BaseService<Data.Entity.ProductFx>, IProductFxService
    {
        private ProductFxRepository _repository = null;
        private ProductSkuFxRepository _productSkuFxRepository = null;
        private FinancialSettlementService _financialSettlementService = null;

        private string _connectionString = string.Empty;
        private static LogHelper _logHelper;

        public ProductFxService()
        {
            _repository = new ProductFxRepository();
            _productSkuFxRepository = new ProductSkuFxRepository();
            _financialSettlementService = new FinancialSettlementService();
            _logHelper = new LogHelper(Config.SlsProject, LogStoreNames.PurchaseOrderRelationLog);
        }
        public ProductFxService(string connectionString) : base(connectionString)
        {
            _repository = new ProductFxRepository(connectionString);
            _financialSettlementService = new FinancialSettlementService(connectionString);
            _connectionString = connectionString;
            _logHelper = new LogHelper(Config.SlsProject, LogStoreNames.PurchaseOrderRelationLog);
        }

        public static LogHelper Instance
        {
            get
            {
                return _logHelper;
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="products"></param>
        /// <param name="shopId"></param>
        /// <param name="isSyncProductType"></param>
        /// <param name="isBindSupplier">是否绑定厂家代发</param>
        /// <param name="onlyCheckCurProductCodes">只检查指定商品，true=只检查当前商品路径流，默认false</param>
        public void BulkMerger(List<ProductFx> products, int shopId, bool isSyncProductType = false, 
            bool isBindSupplier = false, bool onlyCheckCurProductCodes = false, bool isPush1688ProcessPriceMq = true)
        {
            //校验平台
            if (products != null && products.Any())
            {
                //new ShopService().CheckTouTiaoShopCloudPlatformType(products.First().SourceUserId);
                new ShopService().CheckShopCloudPlatformType(products.First().PlatformType, products.First().ShopId);

                //暂不支持tk同步商品后绑定代发
                if (products.First().PlatformType == PlatformType.TikTok.ToString())
                {
                    isBindSupplier = false;
                }
            }
            //保存前校验
            SaveBeforeCheckProductSkuName(SiteContext.Current.CurrentFxUserId, products);
            var _commonSettingService = new CommonSettingService();
            var op = OptimisticLockOperationType.FxProductBulkMerger;
            var opId = $"FX{shopId}";
            var r = _commonSettingService.ExecuteWithOptimisticLock(() =>
            {
                //新添加的product和sku,绑定代发有用
                List<ProductFx> newProductList = null;
                List<ProductSkuFx> newSkuList = null;

                try
                {
                    if (products != null && products.Any())
                    {
                        if (products.Any(x => x.SourceUserId == 0))
                            Log.WriteError($"保存商品时，有产品的SourceUserId为0，SiteContext.Current.CurrentFxUserId:{SiteContext.CurrentNoThrow?.CurrentFxUserId},调用堆栈信息：{Environment.StackTrace}");

                        //商品绑定厂家代发，需要保存新增的商品

                        if (isBindSupplier)
                        {
                            newProductList = new List<ProductFx>();
                            newSkuList = new List<ProductSkuFx>();
                        }

                        _repository.BulkMergerV2(products, isSyncProductType, newProductList, newSkuList);
                        
                        #region 1688代销品：采购结算价处理，只发MQ消息到各云
                        try
                        {
                            if (isPush1688ProcessPriceMq)
                            {
                                ProcessPriceSendRabbitMQMessage(products, "BulkMerger");
                            }
                        }
                        catch (Exception purEx)
                        {
                            Log.WriteError($"ProcessPriceSendRabbitMQMessage异常：{purEx}", "ProcessPriceSendRabbitMQMessageError.txt");
                        }
                        #endregion

                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"保存商家品时发生错误：{ex}");
                }

                try
                {
                    if (onlyCheckCurProductCodes)
                    {
                        var group = products.GroupBy(x => new { x.SourceUserId, x.ShopId });
                        foreach (var item in group)
                        {
                            //只检查当前商品
                            var curProductCodes = item.Select(a => a.ProductCode).Distinct().ToList();
                            //添加路径流和引用
                            new PathFlowReferenceService(_connectionString).SetPathFlowReferenceNew(curProductCodes, item.Key.SourceUserId, item.Key.ShopId);
                        }
                    }
                    else
                    {
                        //原方法
                        var group = products.GroupBy(x => new { x.SourceUserId, x.ShopId });
                        foreach (var item in group)
                        {
                            //添加路径流和引用
                            new PathFlowReferenceService(_connectionString).SetPathFlowReferenceNew(item.Key.SourceUserId, item.Key.ShopId);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"设置商品路径流时发生错误：{ex}");
                }

                try
                {
                    #region  商品绑定厂家代发
                    if (isBindSupplier && (newProductList?.Count > 0 || newSkuList?.Count > 0))
                    {
                        int fxUserId = SiteContext.Current.CurrentFxUserId;
                        //校验该店铺有没有绑定厂家代发
                        var fxUserShopDefaultSupplierModel = new FxUserShopDefaultSupplierService().GetInfoByShopId(shopId);
                        if (fxUserShopDefaultSupplierModel != null && fxUserShopDefaultSupplierModel.FxUserId == fxUserId)
                        {
                            //校验厂家是否绑定成功
                            var supplierInfo = new SupplierUserService().GetSupplierUserById(fxUserShopDefaultSupplierModel.SupplierFxUserId, fxUserId);
                            if (supplierInfo != null && supplierInfo.Status == AgentBingSupplierStatus.Binded)
                            {
                                //绑定厂家
                                var bindSupplierRequestModel = new BindSupplierRequestModel();
                                bindSupplierRequestModel.Configs = new List<BindConfigModel>() { new BindConfigModel() { SupplierId = fxUserShopDefaultSupplierModel.SupplierFxUserId } };
                                bindSupplierRequestModel.ShopId = shopId;

                                int pageSize = 500;

                                //新增商品
                                if (newProductList.Count > 0)
                                {
                                    bindSupplierRequestModel.IsFollowProduct = true;
                                    bindSupplierRequestModel.IsBindSku = false;
                                    //分页绑定
                                    List<string> pCodes = newProductList.Select(s => s.ProductCode).Distinct().ToList();
                                    var pageCount = Math.Ceiling(pCodes.Count * 1.0 / pageSize);
                                    for (int pageIndex = 0; pageIndex < pageCount; pageIndex++)
                                    {
                                        bindSupplierRequestModel.productCodes = pCodes.Skip(pageIndex * pageSize).Take(pageSize).ToList();
                                        BindSupplierBySynchProduct(bindSupplierRequestModel, fxUserId);
                                    }
                                }

                                //新增sku
                                if (newSkuList.Count > 0)
                                {
                                    bindSupplierRequestModel.productCodes = new List<string>();
                                    bindSupplierRequestModel.IsFollowProduct = false;
                                    bindSupplierRequestModel.IsBindSku = true;

                                    //分页绑定
                                    List<string> pCodes = newSkuList.Select(s => s.ProductCode).Distinct().ToList();

                                    var pageCount = Math.Ceiling(pCodes.Count * 1.0 / pageSize);
                                    for (int pageIndex = 0; pageIndex < pageCount; pageIndex++)
                                    {
                                        bindSupplierRequestModel.productCodes = pCodes.Skip(pageIndex * pageSize).Take(pageSize).ToList();

                                        bindSupplierRequestModel.skuCodes = new Dictionary<string, string>();
                                        foreach (var item in bindSupplierRequestModel.productCodes)
                                        {
                                            bindSupplierRequestModel.skuCodes.Add(item, string.Join(",", newSkuList.Where(w => w.ProductCode == item).Select(s => s.SkuCode).Distinct().ToList()));
                                        }

                                        BindSupplierBySynchProduct(bindSupplierRequestModel, fxUserId);
                                    }
                                }
                            }
                        }
                    }
					#endregion
				}
				catch (Exception e)
                {
                    Log.WriteError($"同步商品绑定厂家代发出现异常：{SiteContext.CurrentNoThrow?.CurrentFxUserId},{shopId},{e}", "ShopBindSupplierError.txt");
                }

                return true;
            }, () =>
            {
                Log.Debug($"店铺【{shopId}】同步完成后，同步商品动作未获取到锁");
            }, op, opId);

            if (r.IsExcuted == false)
                Log.WriteWarning("【BulkMerger】其他人正在操作商品数据，请稍会儿重试");

            #region 数据变更日志
            List<DataChangeLog> dcLogs = products.Select(o => new DataChangeLog
            {
                DataChangeType = DataChangeTypeEnum.INSERT,
                TableTypeName = DataChangeTableTypeName.Product,
                SourceShopId = o.ShopId,
                SourceFxUserId = o.SourceUserId,
                RelationKey = o.ProductCode,
                ExtField1 = "ProductFxService.BulkMerger"
            }
            ).ToList();
            new DataChangeLogRepository().Add(dcLogs);
            #endregion

            //#region 调用同步数据接口服务
            //var fxUserId = SiteContext.Current.CurrentFxUserId;
            //var productCodes = products.Select(a => a.ProductCode).ToList();
            //new SyncDataInterfaceService(fxUserId).ProductFxOpt(productCodes, (string targetConnectionString, List<string> targetProductCodes) =>
            //{
            //    if (targetProductCodes != null && targetProductCodes.Any())
            //    {
            //        var targetProducts = products.Where(a => targetProductCodes.Contains(a.ProductCode)).ToList();
            //        new ProductFxRepository(targetConnectionString).BulkMergerV2(targetProducts, isSyncProductType);
            //        var group = targetProducts.GroupBy(x => new { x.SourceUserId, x.ShopId });
            //        var _pathFlowReferenceService = new PathFlowReferenceService(targetConnectionString);
            //        foreach (var item in group)
            //        {
            //            //添加路径流和引用
            //            _pathFlowReferenceService.SetPathFlowReferenceNew(item.Key.SourceUserId, item.Key.ShopId);
            //        }
            //    }
            //});
            //#endregion
        }

        /// <summary>
        /// 保存消息过来的商品
        /// </summary>
        /// <param name="product"></param>
        /// <param name="shopId"></param>
        public void BulkMergerFromMessage(List<ProductFx> products, int shopId)
        {
            var op = OptimisticLockOperationType.FxProductBulkMerger;
            var opId = $"FX{shopId}";
            var r = new CommonSettingService().ExecuteWithOptimisticLock(() =>
            {
                try
                {
                    if (products != null && products.Any())
                    {
                        products.ForEach(p =>
                        {
                            p.From = "Message";
                            p.Skus.ForEach(s =>
                            {
                                s.From = "Message";
                            });
                        });
                        var result = _repository.BulkMergerV2(products);

                        #region 1688代销品：商品价格处理，只发MQ消息到各云
                        try
                        {
                            ProcessPriceSendRabbitMQMessage(products, "BulkMergerFromMessage");
                        }
                        catch (Exception purEx)
                        {
                            Log.WriteError($"ProcessPriceSendRabbitMQMessage异常：{purEx}", "ProcessPriceSendRabbitMQMessageError.txt");
                        }
                        #endregion

                        if (result != null && result.Any())
                        {
                            var pathflowService = new PathFlowService(_connectionString);
                            var pathflowReferenceService = new PathFlowReferenceService(_connectionString);
                            var pathflowReferenceList = new List<PathFlowReference>();
                            foreach (var item in result)
                            {
                                var defaultPathFlowCode = pathflowService.CreateDefaultPathFlow(item.ShopId, item.SourceUserId);
                                pathflowReferenceList.Add(new PathFlowReference
                                {
                                    PathFlowCode = defaultPathFlowCode,
                                    PathFlowRefType = "Product",
                                    PathFlowRefCode = item.ProductCode,
                                    Status = 0,
                                    ProductCode = item.ProductCode,
                                    CreateTime = DateTime.Now,
                                    UpdateTime = DateTime.Now
                                });
                            }
                            pathflowReferenceService.BulkInsert(pathflowReferenceList);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.WriteError($"保存商家品时发生错误：{ex}");
                }
                return true;
            }, null, op, opId);

            if (r.IsExcuted == false)
                Log.WriteWarning("【BulkMergerFromMessage】其他人正在操作商品数据，请稍会儿重试");

            #region 数据变更日志
            List<DataChangeLog> dcLogs = products.Select(o => new DataChangeLog
            {
                DataChangeType = DataChangeTypeEnum.INSERT,
                TableTypeName = DataChangeTableTypeName.Product,
                SourceShopId = o.ShopId,
                SourceFxUserId = o.SourceUserId,
                RelationKey = o.ProductCode,
                ExtField1 = "ProductFxService.BulkMergerFromMessage"
            }
            ).ToList();
            new DataChangeLogRepository().Add(dcLogs);
            #endregion
        }

        public List<string> QueryTop1000NotSetDefaultPathFlowProductCodes(int sourceFxUserId, int sourceShopId)
        {
            return _repository.QueryTop1000NotSetDefaultPathFlowProductCodes(sourceFxUserId, sourceShopId);
        }

        /// <summary>
        /// 根据指定ProductCode查询未设置路径流的商品
        /// </summary>
        /// <param name="productCodes"></param>
        /// <param name="sourceFxUserId"></param>
        /// <param name="sourceShopId"></param>
        /// <returns></returns>
        public List<string> QueryNotSetDefaultPathFlowProductCodes(List<string> productCodes, int sourceFxUserId, int sourceShopId)
        {
            return _repository.QueryNotSetDefaultPathFlowProductCodes(productCodes, sourceFxUserId, sourceShopId);
        }


        /// <summary>
        /// 指定列查询产品
        /// </summary>
        /// <param name="productId">平台Id</param>
        /// <param name="shopId">店铺Id</param>
        /// <param name="fields">字段</param>
        /// <returns></returns>
        [Obsolete("2022.8.29已弃用，请使用GetProductListV2，注意传参已不同")]
        public List<ProductFx> GetProductList(List<string> productIds, List<int> shopIds, string fields = null)
        {
            return _repository.GetProductList(productIds, shopIds, fields);
        }

        /// <summary>
        /// 指定列查询产品
        /// </summary>
        /// <param name="queryModels">查询条件</param>
        /// <param name="fields">字段</param>
        /// <returns></returns>
        public List<ProductFx> GetProductListV2(List<ProductFxQueryModel> queryModels, string fields = null)
        {
            return _repository.GetProductListV2(queryModels, fields);
        }

        /// <summary>
        /// 指定列查询产品
        /// </summary>
        /// <param name="queryModels">查询条件</param>
        /// <param name="fields">字段</param>
        /// <param name="queryAll">true时表示包含已逻辑删除的数据</param>
        /// <returns></returns>
        public List<ProductSkuFx> GetProductSkuList(List<ProductFxQueryModel> queryModels, string fields = null)
        {
            return _repository.GetProductSkuList(queryModels, fields);
        }
        /// <summary>
        /// 指定列查询产品(带简称)
        /// </summary>
        /// <param name="queryModels">查询条件</param>
        /// <param name="fields">字段</param>
        /// <param name="queryAll">true时表示包含已逻辑删除的数据</param>
        /// <returns></returns>
        public Dictionary<string, ProductFx> GetProductListV3(List<SimpleProduct> products, string fields = null)
        {
            var listDic = _repository.GetProductListV3(products, fields);
            if (listDic == null)
            {
                return new Dictionary<string, ProductFx>();
            }
            //简称
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var pCodes = listDic.Select(a => a.Key).Distinct().ToList();
            var productInfoFxService = new ProductInfoFxService(_connectionString);
            var infos = productInfoFxService.GetProductInfoList(curFxUserId, pCodes);

            foreach (var dic in listDic)
            {
                var exist = infos.FirstOrDefault(a => a.ProductCode == dic.Key);
                if (exist != null)
                {
                    if(dic.Value != null)
                    {
                        dic.Value.ShortTitle = exist.ShortTitle;
                        dic.Value.Skus?.ForEach(sku =>
                        {
                            var existSku = exist.Skus.FirstOrDefault(a => a.SkuCode == sku.SkuCode);
                            if (existSku != null)
                            {
                                sku.ShortTitle = existSku.ShortTitle;
                            }
                        });
                    }
                }
            }
            return listDic;
        }


        /// <summary>
        /// （结果带Sku）
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="fields"></param>
        /// <returns></returns>
        public List<ProductFx> GetProductListByCodes(List<string> codes, string fields = null)
        {
            if (codes == null || codes.Any() == false)
                return new List<ProductFx>();
            return _repository.GetProductListByCodes(codes, fields);
        }

        public List<ProductFx> GetProductfxByPathFlowReferenceIsNull(int fxUserId)
        {
            return _repository.GetProductfxByPathFlowReferenceIsNull(fxUserId);
        }

        public PagedResultModel<ProductFx> GetProductFxList(int fxUserId, ProductFxRequertModule model)
        {
            return GetProductFxList(fxUserId, model, displaySkuCount: 0);
        }
        /// <summary>
        /// 商品列表专用
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="model"></param>
        /// <param name="displaySkuCount"></param>
        /// <returns></returns>
        public PagedResultModel<ProductFx> GetProductFxListV2(int fxUserId, ProductFxRequertModule model, int displaySkuCount = 0)
        {
            var notFilterWitSku = string.IsNullOrEmpty(model.ProductSkuShortTitle)
                     && string.IsNullOrEmpty(model.ProductSkuName)
                     && string.IsNullOrEmpty(model.ProductSkuWeight)
                     && string.IsNullOrEmpty(model.IsSetSkuShortTitle)
                     && string.IsNullOrEmpty(model.IsSkuSetWeight)
                     && string.IsNullOrEmpty(model.QueryFlag)
                     && model.IsFilterDPSkuMapping == null;

            var tuple = GetListByFxUserIdV2(fxUserId, model);

            //获取商品额外的数据
            var products = tuple.Item2;
            var count = products.Count;
            var listData = GetProductByDownFxUserV2(fxUserId, products, model.ProductType, tuple.Item3);
            // 处理多厂家
            var pathflowInofs = tuple.Item3;
            if (pathflowInofs != null && pathflowInofs.Any())
            {
                foreach (var pathflow in pathflowInofs)
                {
                    var product = products.Find(p => p.ProductCode == pathflow.ProductCode);
                    if (product != null)
                    {
                        product.SupplierCount = pathflow.DownFxUserIdCount;
                    }
                }
            }
            if (displaySkuCount > 0)
            {
                //如果有规格的搜索则不设置3个sku的限制
                if (notFilterWitSku)
                {
                    //只取3个sku,同时设置sku的未完属性
                    products.ForEach(f =>
                    {
                        f.Skus = f.Skus.Take(3).ToList();
                    });
                }
                else
                {
                    //设置HaveMore
                    products.ForEach(f =>
                    {
                        f.HaveMore = f.Skus != null && f.Skus.Count == 3;
                    });
                }
            }

            HandleProductSkuHistory(listData, fxUserId);
            // 重新计算商品总数（下游厂家重新绑定后，页面不显示数量应该重算）
            var newCount = listData.Count;
            var totalCount = tuple.Item1 - (count - newCount);
            return new PagedResultModel<ProductFx>()
            {
                PageIndex = 1,
                PageSize = 10,
                Rows = listData,
                Total = totalCount
            };
        }
        
        public List<BaseOfPtSkuRelation> GetBaseProductSkuRelations(List<string> productSkuPtIds, int fxUserId)
        {
            var baseProductSkuRelations = _repository.GetBaseProductSkuRelations(productSkuPtIds, fxUserId);
            return baseProductSkuRelations;
        }
        
           
        public PagedResultModel<ProductFx> GetProductFxList(int fxUserId, ProductFxRequertModule model, int displaySkuCount = 0)
        {
            // 查询前更新历史数据ProductCode和RelationCode
            //new PathFlowReferenceService(_connectionString).UpdateOldDataForMutiBindSupplier();

            Stopwatch sw = new Stopwatch();
            Stopwatch sw2 = new Stopwatch();
            if (CustomerConfig.IsDebug)
            {
                sw.Start();
                sw2.Start();
            }
            var tuple = GetListByFxUserId(fxUserId, model);

            if (CustomerConfig.IsDebug)
            {
                sw.Stop();
                Log.Debug(() => $"Step1:GetListByFxUserId耗时：{sw.Elapsed.TotalSeconds}s，model={model.ToJson(true)}", "GetProductFxList.txt");
                sw.Restart();
            }
            //获取商品额外的数据
            var products = tuple.Item2;
            var count = products.Count;
            var listData = GetProductByDownFxUser(fxUserId, products, model.ProductType);

            if (CustomerConfig.IsDebug)
            {
                sw.Stop();
                Log.Debug(() => $"Step2:GetProductByDownFxUser耗时：{sw.Elapsed.TotalSeconds.ToString("F4")}s，model={model.ToJson(true)}", "GetProductFxList.txt");
                sw.Restart();
            }

            #region 商品归一数据替换
            var skuIdList = listData.SelectMany(a => a.Skus)
                                    .Where(c => c.IsRelationBaseProduct)
                                    .Select(b => b.BaseProductSkuUid)
                                    .Distinct().ToList();
            Dictionary<long, BaseProductSkuSimpleRes> baseProductSkuDic = new BaseProductSkuCommonService().GetSkuListBySkuIds(skuIdList);

            foreach (var productFx in listData)
            {
                foreach (var productSkuFx in productFx.Skus)
                {
                    ReplaceProductSkuSample(listData, baseProductSkuDic, productSkuFx, listData[0].PlatformId);
                }
            }
            #endregion


            if (CustomerConfig.IsDebug)
            {
                sw.Stop();
                Log.Debug(() => $"Step3:商品归一数据替换 耗时：{sw.Elapsed.TotalSeconds.ToString("F4")}s", "GetProductFxList.txt");
                sw.Restart();
            }

            if (displaySkuCount > 0)
            {
                //如果有规格的搜索则不设置3个sku的限制
                if (string.IsNullOrEmpty(model.ProductSkuShortTitle)
                    && string.IsNullOrEmpty(model.ProductSkuName)
                    && string.IsNullOrEmpty(model.ProductSkuWeight)
                    && string.IsNullOrEmpty(model.IsSetSkuShortTitle)
                    && string.IsNullOrEmpty(model.IsSkuSetWeight)
                    && string.IsNullOrEmpty(model.QueryFlag)
                    && model.IsFilterDPSkuMapping == null
                    )
                {
                    //只取3个sku,同时设置sku的未完属性
                    products.ForEach(f =>
                    {
                        f.SkuCount = f.Skus.Count();
                        f.IsDefaultSku = IsDefaultSku(f.Skus);
                        f.Skus = f.Skus.Take(3).ToList();
                    });
                }
                else
                {
                    //设置HaveMore
                    products.ForEach(f =>
                    {
                        f.HaveMore = true;
                        f.IsDefaultSku = IsDefaultSku(f.Skus);
                    });
                }
            }
            HandleProductSkuHistory(listData, fxUserId);


            if (CustomerConfig.IsDebug)
            {
                sw.Stop();
                sw2.Stop();
                Log.Debug(() => $"Step4:HandleProductSkuHistory耗时：{sw.Elapsed.TotalSeconds.ToString("F4")}s，总耗时：{sw2.Elapsed.TotalSeconds.ToString("F4")}s", "GetProductFxList.txt");
            }

            // 重新计算商品总数（下游厂家重新绑定后，页面不显示数量应该重算）
            var newCount = listData.Count;
            var totalCount = tuple.Item1 - (count - newCount);
            return new PagedResultModel<ProductFx>()
            {
                PageIndex = 1,
                PageSize = 10,
                Rows = listData,
                Total = totalCount
            };
        }

        /// <summary>
        /// 查询商品列表 包含成本价
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="model"></param>
        /// <param name="isSetCost">查询设置成本价的商品 0-全部 1-已设置 2-未设置</param>
        /// <returns></returns>
        public PagedResultModel<ProductFx> GetProductFxListWithCostPrice(int fxUserId, ProductFxRequertModule model, int isSetCost = 0)
        {
            var tuple = GetListWithCostPriceByFxUserId(fxUserId, model, isSetCost);

            //获取商品额外的数据
            var count = tuple.Item2.Count;
            var listData = GetProductByDownFxUser(fxUserId, tuple.Item2, model.ProductType);
            // 重新计算商品总数（下游厂家重新绑定后，页面不显示数量应该重算）
            var newCount = listData.Count;
            var totalCount = tuple.Item1 - (count - newCount);
            return new PagedResultModel<ProductFx>()
            {
                PageIndex = 1,
                PageSize = 10,
                Rows = listData,
                Total = totalCount
            };
        }

        public PagedResultModel<ProductFx> GetProductFxListThreeSku(int fxUserId, ProductFxRequertModule model)
        {
            // 查询前更新历史数据ProductCode和RelationCode
            //new PathFlowReferenceService(_connectionString).UpdateOldDataForMutiBindSupplier();                        
            //var tuple = GetListByFxUserId(fxUserId, model);
            var tuple = GetListByFxUserId(fxUserId, model);

            //获取商品额外的数据
            var count = tuple.Item2.Count;
            var listData = GetProductByDownFxUser(fxUserId, tuple.Item2, model.ProductType);
            if (model.FxPageType != FxPageType.Offline.ToInt())
            {
                //如果有规格的搜索则不设置3个sku的限制
                if (string.IsNullOrEmpty(model.ProductSkuShortTitle)
                    && string.IsNullOrEmpty(model.ProductSkuName)
                    && string.IsNullOrEmpty(model.ProductSkuWeight)
                    && string.IsNullOrEmpty(model.IsSetSkuShortTitle)
                    && string.IsNullOrEmpty(model.IsSkuSetWeight))
                {
                    //只取3个sku,同时设置sku的未完属性
                    listData.ForEach(f =>
                    {
                        f.Skus = f.Skus.Take(3).ToList();
                    });
                }
                else
                {
                    //设置HaveMore
                    listData.ForEach(f =>
                    {
                        f.HaveMore = true;
                    });
                }
            }
            // 重新计算商品总数（下游厂家重新绑定后，页面不显示数量应该重算）
            var newCount = listData.Count;
            var totalCount = tuple.Item1 - (count - newCount);
            return new PagedResultModel<ProductFx>()
            {
                PageIndex = 1,
                PageSize = 10,
                Rows = listData,
                Total = totalCount
            };
        }

        private Tuple<int, List<ProductFx>> GetListByFxUserId(int fxUserId, ProductFxRequertModule model)
        {
            //互斥查询查询直接返回
            if ((model.IsSetShortTitle == "0" && model.ProductShortTitle.IsNotNullOrEmpty()) ||
                (model.IsSetSkuShortTitle == "0" && model.ProductSkuShortTitle.IsNotNullOrEmpty()) ||
                (model.IsSetWeight == "0" && model.ProductWeight.IsNotNullOrEmpty()) ||
                (model.IsSkuSetWeight == "0" && model.ProductSkuWeight.IsNotNullOrEmpty())
                )
            {
                return new Tuple<int, List<ProductFx>>(0, new List<ProductFx>());
            }

            //厂家身份：只取开启了预付的商家
            if (model.QueryFlag.ToString2() == "supplier")
            {
                var openedPrePayFxUserIds = new SupplierUserService().GetOpenedPrePayAgentFxUserIds(fxUserId);
                if (openedPrePayFxUserIds == null || openedPrePayFxUserIds.Any() == false)
                {
                    return new Tuple<int, List<ProductFx>>(0, new List<ProductFx>());
                }
                if (string.IsNullOrEmpty(model.SelectAgentId) == false)
                {
                    var agentFxUserIds = model.SelectAgentId.SplitToList(",").ConvertAll(x => x.ToInt()).Where(sid => sid > 0).Distinct().ToList();
                    openedPrePayFxUserIds = agentFxUserIds.Where(id => openedPrePayFxUserIds.Contains(id)).ToList();
                }
                model.SelectAgentId = string.Join(",", openedPrePayFxUserIds);
            }

            var isWhiteUser = SiteContext.Current.IsWhiteUser;
            var tuple = _repository.GetListByFxUserId(fxUserId, model);
            var productInfos = new List<ProductInfoFx>();
            var productSkuRelations = new List<BaseOfPtSkuRelation>();
            var list = tuple.Item2;
            //如果有简称相关查询，第一步已查询，则不需要再次查简称信息
            if (list != null && list.Any())
            {
                var productInfoService = new ProductInfoFxService(_connectionString);
                var codes = list.Select(x => x.ProductCode).Distinct().ToList();
                productInfos = productInfoService.GetProductInfoList(fxUserId, codes);
                // 开启归一 && 无基础商品条件查询
                if(model.IsBaseProduceCombine && model.IsRelationBaseProduct.IsNullOrEmpty())
                {
                    var skuCodes = list.SelectMany(a => a.Skus).ToList().Select(a => a.SkuCode).Distinct().ToList();
                    productSkuRelations = productInfoService.GetProductSkuRelationList(fxUserId, skuCodes);
                }
            }

            list?.ForEach(p =>
            {
                var pinfo = productInfos.FirstOrDefault(x => x.ProductCode == p.ProductCode);
                p.ShortTitle = pinfo?.ShortTitle;
                p.Weight = pinfo?.Weight.ConvertGToKg() ?? 0;
                p.IsSelfShop = p.SourceUserId == fxUserId;
                var skuCount = p.Skus.Count();
                for (var i = skuCount - 1; i >= 0; i--)
                {
                    var s = p.Skus[i];
                    //if (!isShowDelSku && s.SystemStatus < 0)
                    //{
                    //    p.Skus.Remove(s);
                    //    continue;
                    //}
                    var sku = pinfo?.Skus?.FirstOrDefault(x => x.SkuCode == s.SkuCode);
                    s.ShortTitle = sku?.ShortTitle;
                    s.Weight = sku?.Weight.ConvertGToKg() ?? 0;
                    s.ImgUrl = String.IsNullOrEmpty(s.ImgUrl) ? p.ImageUrl : s.ImgUrl;
                }
                p.Skus.ForEach(sku =>
                {
                    // 开启归一 && 无基础商品条件查询
                    if (model.IsBaseProduceCombine && model.IsRelationBaseProduct.IsNullOrEmpty())
                    {
                        var relation = productSkuRelations.FirstOrDefault(a => a.ProductCode == sku.ProductCode && a.ProductSkuCode == sku.SkuCode && a.Status == 1);
                        // 非白用户允许编辑sku简称
                        if (relation != null && isWhiteUser)
                        {
                            sku.ShortTitleDisabled = true;
                            sku.IsRelationBaseProduct = true;
                            sku.BaseProductSkuUid = relation.BaseProductSkuUid;
                        }
                    }
                    sku.ShortTitleDisabled = sku.IsRelationBaseProduct && isWhiteUser;
                });
            });

            //Stopwatch stopwatch4 = new Stopwatch();
            //stopwatch4.Start();
            //只有【已关联列表】才更新结算价相关数据
            if (model.NeedSupplierMapping == true)
            {
                new DistributorProductRepository(_connectionString).SetSettlementPriceProductFx(list, fxUserId, 2, model);
            }

            //Utility.CommUtls.WriteToLog($"tuple.Item2：{tuple.Item2?.ToJson()}，model={model.ToJson()}", "FxProduct.txt", "FxProduct");
            //Sku过滤
            FilterSku(tuple.Item2, model);

            return tuple;
        }
        /// <summary>
        /// 商品列表专用
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        private Tuple<int, List<ProductFx>, List<ProductPathFlowRefType>> GetListByFxUserIdV2(int fxUserId, ProductFxRequertModule model)
        {
            //互斥查询查询直接返回
            if ((model.IsSetShortTitle == "0" && model.ProductShortTitle.IsNotNullOrEmpty()) ||
                (model.IsSetSkuShortTitle == "0" && model.ProductSkuShortTitle.IsNotNullOrEmpty()) ||
                (model.IsSetWeight == "0" && model.ProductWeight.IsNotNullOrEmpty()) ||
                (model.IsSkuSetWeight == "0" && model.ProductSkuWeight.IsNotNullOrEmpty())
                )
            {
                return new Tuple<int, List<ProductFx>, List<ProductPathFlowRefType>>(0, new List<ProductFx>(), new List<ProductPathFlowRefType>());
            }

            //厂家身份：只取开启了预付的商家
            if (model.QueryFlag.ToString2() == "supplier")
            {
                var openedPrePayFxUserIds = new SupplierUserService().GetOpenedPrePayAgentFxUserIds(fxUserId);
                if (openedPrePayFxUserIds == null || openedPrePayFxUserIds.Any() == false)
                {
                    return new Tuple<int, List<ProductFx>, List<ProductPathFlowRefType>>(0, new List<ProductFx>(), new List<ProductPathFlowRefType>());
                }
                if (string.IsNullOrEmpty(model.SelectAgentId) == false)
                {
                    var agentFxUserIds = model.SelectAgentId.SplitToList(",").ConvertAll(x => x.ToInt()).Where(sid => sid > 0).Distinct().ToList();
                    openedPrePayFxUserIds = agentFxUserIds.Where(id => openedPrePayFxUserIds.Contains(id)).ToList();
                }
                model.SelectAgentId = string.Join(",", openedPrePayFxUserIds);
            }

            var tuple = _repository.GetListByFxUserIdV2(fxUserId, model);
            var productInfos = new List<ProductInfoFx>();
            var list = tuple.Item2;
            //如果有简称相关查询，第一步已查询，则不需要再次查简称信息
            if (list != null && list.Any())
            {
                var productInfoService = new ProductInfoFxService(_connectionString);
                var codes = list.Select(x => x.ProductCode).Distinct().ToList();
                productInfos = productInfoService.GetProductInfoList(fxUserId, codes);
            }

            list?.ForEach(p =>
            {
                var pinfo = productInfos.FirstOrDefault(x => x.ProductCode == p.ProductCode);
                p.ShortTitle = pinfo?.ShortTitle;
                p.Weight = pinfo?.Weight ?? 0;
                p.IsSelfShop = p.SourceUserId == fxUserId;
                var skuCount = p.Skus.Count();
                for (var i = skuCount - 1; i >= 0; i--)
                {
                    var s = p.Skus[i];
                    var sku = pinfo?.Skus?.FirstOrDefault(x => x.SkuCode == s.SkuCode);
                    s.ShortTitle = sku?.ShortTitle;
                    s.Weight = sku?.Weight ?? 0;
                    s.ImgUrl = String.IsNullOrEmpty(s.ImgUrl) ? p.ImageUrl : s.ImgUrl;
                }
            });

            //只有【已关联列表】才更新结算价相关数据
            if (model.NeedSupplierMapping == true)
            {
                new DistributorProductRepository(_connectionString).SetSettlementPriceProductFx(list, fxUserId, 2, model);
            }

            //Sku过滤
            FilterSku(tuple.Item2, model);

            return tuple;
        }

    
        private Tuple<int, List<ProductFx>> GetListWithCostPriceByFxUserId(int fxUserId, ProductFxRequertModule model, int isSetCost = 0)
        {
            //互斥查询查询直接返回
            if ((model.IsSetShortTitle == "0" && model.ProductShortTitle.IsNotNullOrEmpty()) ||
                (model.IsSetSkuShortTitle == "0" && model.ProductSkuShortTitle.IsNotNullOrEmpty()) ||
                (model.IsSetWeight == "0" && model.ProductWeight.IsNotNullOrEmpty()) ||
                (model.IsSkuSetWeight == "0" && model.ProductSkuWeight.IsNotNullOrEmpty())
                )
            {
                return new Tuple<int, List<ProductFx>>(0, new List<ProductFx>());
            }

            var tuple = _repository.GetListWithCostPriceByFxUserId(fxUserId, model, isSetCost);
            var productInfos = new List<ProductInfoFx>();
            var list = tuple.Item2;
            //如果有简称相关查询，第一步已查询，则不需要再次查简称信息
            if (list != null && list.Any())
            {
                var productInfoService = new ProductInfoFxService(_connectionString);
                var codes = list.Select(x => x.ProductCode).Distinct().ToList();
                productInfos = productInfoService.GetProductInfoList(fxUserId, codes);
            }

            list?.ForEach(p =>
            {
                var pinfo = productInfos.FirstOrDefault(x => x.ProductCode == p.ProductCode);
                p.ShortTitle = pinfo?.ShortTitle;
                p.Weight = pinfo?.Weight ?? 0;
                p.IsSelfShop = p.SourceUserId == fxUserId;
                var skuCount = p.Skus.Count();
                for (var i = skuCount - 1; i >= 0; i--)
                {
                    var s = p.Skus[i];
                    //if (!isShowDelSku && s.SystemStatus < 0)
                    //{
                    //    p.Skus.Remove(s);
                    //    continue;
                    //}
                    var sku = pinfo?.Skus?.FirstOrDefault(x => x.SkuCode == s.SkuCode);
                    s.ShortTitle = sku?.ShortTitle;
                    s.Weight = sku?.Weight ?? 0;
                    s.ImgUrl = String.IsNullOrEmpty(s.ImgUrl) ? p.ImageUrl : s.ImgUrl;
                }
            });

            FilterSku(tuple.Item2, model);

            return tuple;
        }


        public List<ProductSkuFx> GetProductFxListMoreSku(int fxUserId, ProductFxMoreSkuRequestModule model)
        {

            var product = _repository.ProductSkuGetListByCodes(model.ProductCode, model.IsHideDelSku, fxUserId);
            var listData = GetProductByDownFxUser(fxUserId, product, model.ProductType);
            if (listData != null && listData.Any())
            {
                var productInfos = new ProductInfoFxService(_connectionString).GetProductInfoList(fxUserId, new List<string> { model.ProductCode });
            HandleProductSkuHistory(listData, fxUserId);
                var productInfoService = new ProductInfoFxService(_connectionString);
                var skuCodes = listData.SelectMany(a => a.Skus).ToList().Select(a => a.SkuCode).Distinct().ToList();
                var productSkuRelations = productInfoService.GetProductSkuRelationList(fxUserId, skuCodes);

                listData[0].Skus.ForEach(s =>
                {
                    var pinfo = productInfos.FirstOrDefault(x => x.ProductCode == s.ProductCode);
                    var sku = pinfo?.Skus?.FirstOrDefault(x => x.SkuCode == s.SkuCode);
                    s.ShortTitle = sku?.ShortTitle;
                s.Weight = sku?.Weight.ConvertGToKg() ?? 0;
                    s.ImgUrl = String.IsNullOrEmpty(s.ImgUrl) ? listData[0].ImageUrl : s.ImgUrl;
                var relation = productSkuRelations.FirstOrDefault(a => a.ProductCode == s.ProductCode && a.ProductSkuCode == s.SkuCode && a.Status == 1);
                // 非白用户允许修改sku简称
                if (relation != null && SiteContext.Current.IsWhiteUser)
                {
                        s.ShortTitleDisabled = true;

                    s.IsRelationBaseProduct = true;
                    s.BaseProductSkuUid = relation.BaseProductSkuUid;
                }
                });

                #region 筛选条件过滤
                if (model.ProductSkuShortTitle.IsNotNullOrEmpty())
                {
                    listData.ForEach(p => { p.Skus = p.Skus?.Where(a => a.ShortTitle.ToString2().Contains(model.ProductSkuShortTitle))?.ToList(); });
                }
                if (model.ProductSkuName.IsNotNullOrEmpty())
                {
                    listData.ForEach(p => { p.Skus = p.Skus?.Where(a => a.Name.ToString2().Contains(model.ProductSkuName))?.ToList(); });
                }
                if (model.IsSetSkuShortTitle.IsNotNullOrEmpty())
                {
                    if (model.IsSetSkuShortTitle == "0")
                    {
                        listData.ForEach(p => { p.Skus = p.Skus?.Where(a => string.IsNullOrEmpty(a.ShortTitle.ToString2()))?.ToList(); });
                    }
                    else if (model.IsSetSkuShortTitle == "1")
                    {
                        listData.ForEach(p => { p.Skus = p.Skus?.Where(a => !string.IsNullOrEmpty(a.ShortTitle.ToString2()))?.ToList(); });
                    }
                }
                if (model.IsSkuSetWeight.IsNotNullOrEmpty())
                {
                    if (model.IsSkuSetWeight == "0")
                    {
                        listData.ForEach(p => { p.Skus = p.Skus?.Where(a => a.Weight == 0)?.ToList(); });
                    }
                    else if (model.IsSkuSetWeight == "1")
                    {
                        listData.ForEach(p => { p.Skus = p.Skus?.Where(a => a.Weight != 0)?.ToList(); });
                    }
                }
                if (model.SkuId.IsNotNullOrEmpty())
                {
                    var skuUids = model.SkuId.SplitToList(",").Where(sid => sid.IsNotNullOrEmpty()).Distinct().ToList();
                    listData.ForEach(p => { p.Skus = p.Skus?.Where(a => skuUids.Contains(a.SkuId))?.ToList(); });
                }

                if (model.SkuCargoNumber.IsNotNullOrEmpty())
                {
                    var skuCargoNumbers = model.SkuCargoNumber.SplitToList(",").Where(skuCargoNumber => skuCargoNumber.IsNotNullOrEmpty()).Distinct().ToList();
                    listData.ForEach(p => { p.Skus = p.Skus?.Where(a => skuCargoNumbers.Contains(a.CargoNumber))?.ToList(); });
                }
                #endregion

                // 根据关联状态过滤显示
                if (model.IsBaseProduceCombine)
            {
                if (model.IsRelationBaseProduct == 0)
                {
                    listData.ForEach(p => { p.Skus = p.Skus?.Where(a => a.IsRelationBaseProduct == false)?.ToList(); });
                }
                if (model.IsRelationBaseProduct == 1)
                {
                    listData.ForEach(p => { p.Skus = p.Skus?.Where(a => a.IsRelationBaseProduct == true)?.ToList(); });
                }
            }

            #region 商品归一数据替换
            var skuIdList = listData[0].Skus.Where(c => c.IsRelationBaseProduct)
                                            .Select(b => b.BaseProductSkuUid)
                                            .Distinct().ToList();
            Dictionary<long, BaseProductSkuSimpleRes> baseProductSkuDic = new BaseProductSkuCommonService().GetSkuListBySkuIds(skuIdList);
            foreach (var productSkuFx in listData[0].Skus)
            {
                ReplaceProductSkuSample(listData, baseProductSkuDic, productSkuFx, listData[0].PlatformId);
            }
            #endregion

                return listData[0].Skus;
            }
            return new List<ProductSkuFx>();
        }

        private void ReplaceProductSkuSample(List<ProductFx> listData, Dictionary<long, BaseProductSkuSimpleRes> baseProductSkuDic, ProductSkuFx productSkuFx, string prductId)
        {
            // 店铺商品信息赋值
            productSkuFx.PtProductInfo.SetPtProduct(skuSimple =>
            {
                skuSimple.PrductId = prductId;
                skuSimple.PrductCode = productSkuFx.ProductCode;
                skuSimple.ProductSubject = productSkuFx.Subject;
                skuSimple.ProductImgUrl = productSkuFx.ImgUrl;
                skuSimple.PrductSpecs = productSkuFx.Name;
                skuSimple.SkuCode = productSkuFx.SkuCode;

                skuSimple.SpuCode = productSkuFx.ProductCargoNumber; // 平台商品编码
                skuSimple.CargoNumber = productSkuFx.CargoNumber; // 平台商品Sku编码
            });

            // 基础商品信息赋值
            if (baseProductSkuDic.TryGetValue(productSkuFx.BaseProductSkuUid, out BaseProductSkuSimpleRes baseProductSku))
            {
                productSkuFx.BaseProductInfo.SetBaseSku(baseProductSku.SpuCode, baseProductSku.ProductSkuCode, baseProductSku.ProductSpecs,
                        baseProductSku.ProductSkuImgUrl, baseProductSku.ProductSubject);
            }
        }

        public List<ProductFx> GetListByFxUserId(int fxUserId, List<string> pis)
        {
            var productInfos = new List<ProductInfoFx>();
            var list = _repository.GetListByFxUserId(fxUserId, pis);
            //如果有简称相关查询，第一步已查询，则不需要再次查简称信息
            if (list != null && list.Any())
            {
                var codes = list.Select(x => x.ProductCode).Distinct().ToList();
                productInfos = new ProductInfoFxService(_connectionString).GetProductInfoList(fxUserId, codes);
            }
            list?.ForEach(p =>
            {
                var pinfo = productInfos.FirstOrDefault(x => x.ProductCode == p.ProductCode);
                p.ShortTitle = pinfo?.ShortTitle;
                p.Weight = pinfo?.Weight ?? 0;
                p.IsSelfShop = p.SourceUserId == fxUserId;
                p?.Skus.ForEach(s =>
                {
                    var sku = pinfo?.Skus?.FirstOrDefault(x => x.SkuCode == s.SkuCode);
                    s.ShortTitle = sku?.ShortTitle;
                    s.Weight = sku?.Weight ?? 0;
                });
            });
            return list;
        }

        /// <summary>
        /// 为触发商家同步订单而查询
        /// </summary>
        /// <param name="platformIds"></param>
        /// <param name="fxUserId"></param>
        /// <param name="agentId"></param>
        /// <returns></returns>
        public List<ProductFx> GetProductsForAgentSync(List<string> platformIds, int fxUserId,int agentId)
        {
            return _repository.GetProductsForAgentSync(platformIds,fxUserId,agentId);
        }

        public List<ProductWeightModel> GetProductWeightInfo(int fxUserId, List<string> pis)
        {
            var listRes = new List<ProductWeightModel>();
            var productFxList = GetListByFxUserId(fxUserId, pis);

            productFxList.ForEach(p =>
            {
                p.Skus?.ForEach(s =>
                {
                    var model = new ProductWeightModel();
                    model.ProductWeight = p.Weight;
                    model.SkuWeight = s.Weight;
                    model.SkuId = s.SkuId;
                    model.PlatformId = p.PlatformId;
                    model.SpecId = s.SkuId;
                    listRes.Add(model);
                });
            });
            return listRes;
        }

        /// <summary>
        /// 兼容新旧数据补上Config信息
        /// </summary>
        /// <param name="pathFlows"></param>
        public void CompatibleOldDataToAddConfig(List<PathFlow> pathFlows)
        {
            if (pathFlows == null || pathFlows.Any() == false)
                return;

            //兼容新旧数据补上Config信息
            foreach (var path in pathFlows)
            {
                if (path.IsSelf)
                    continue;
                var sortPathNodes = path.SortedNodes;
                foreach (var kv in path.PathFlowReferences)
                {
                    var pathRef = kv.Value;
                    foreach (var node in sortPathNodes)
                    {
                        if (node.DownFxUserId == 0)
                            continue;
                        // 补充节点上的默认厂家(兼容旧数据无默认厂家配置情况)
                        var curNodeConfig = pathRef.ReferenceConfigs.FirstOrDefault(x => x.PathFlowCode == node.PathFlowCode && x.FxUserId == node.FxUserId);
                        if (curNodeConfig == null)
                        {
                            var defaultConfig = new PathFlowReferenceConfig
                            {
                                PathFlowCode = node.PathFlowCode,
                                PathFlowRefCode = kv.Key,
                                ConfigType = 0,
                                FxUserId = node.FxUserId,
                                DownFxUserId = node.DownFxUserId,
                                UpFxUserId = node.UpFxUserId
                            };
                            pathRef.ReferenceConfigs.Add(defaultConfig);
                        }
                        else
                        {
                            // 补充节点Config的DownFxUserId和UpFxUserId
                            curNodeConfig.PathFlowRefCode = kv.Key;//（避免Sku的Config上的RefCode存的ProductCode造成显示错误统一在这里处理）
                            curNodeConfig.DownFxUserId = node.DownFxUserId;
                            curNodeConfig.UpFxUserId = node.UpFxUserId;
                        }
                    }
                }
            }
        }

        public Tuple<int, int, bool> GetUpAndDownFxUserIdV2(string from, bool isProduct, string refCode, int fxUserId, List<PathFlow> pathFlows)
        {
            var downFxUserId = 0;
            var upFxUserId = 0;
            var pathRefConfigs = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(refCode)).SelectMany(x => x.PathFlowReferences.SelectMany(y => y.Value.ReferenceConfigs)).Where(x => x.PathFlowRefCode == refCode).ToList();
            if (from == "oneself")
            {
                // 自营-当前节点存在配置，则表示有下游厂家，需要过滤掉
                if (pathRefConfigs.Any(x => x.PathFlowRefCode == refCode && x.FxUserId == fxUserId))
                    return new Tuple<int, int, bool>(0, 0, true);

                // 1、不存在配置 2、商家分配过来的商品在厂家商品列表显示自营，需显示商家信息
                var defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.DownFxUserId == fxUserId);
                upFxUserId = defaultPathRefConfig?.FxUserId ?? 0;
            }
            else if (from == "other")
            {
                // 厂家供货-当前节点不存在配置，则表示没有下游厂家，需要过滤掉
                if (pathRefConfigs.Any(x => x.PathFlowRefCode == refCode && x.FxUserId == fxUserId) == false)
                    return new Tuple<int, int, bool>(0, 0, true);

                // 必需有默认厂家
                var defaultPathRefConfig = pathRefConfigs.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.ConfigType == 0 && x.DownFxUserId > 0 && x.FxUserId == fxUserId);
                downFxUserId = defaultPathRefConfig?.DownFxUserId ?? 0;
                upFxUserId = defaultPathRefConfig?.UpFxUserId ?? 0;
            }
            else
            {
                // 所有商品列表默认显示顺序：默认厂家（必需）=>自营
                var defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.ConfigType == 0 && x.DownFxUserId > 0 && x.FxUserId == fxUserId);
                if (defaultPathRefConfig != null)
                {
                    // 默认厂家
                    downFxUserId = defaultPathRefConfig.DownFxUserId;
                    upFxUserId = defaultPathRefConfig.UpFxUserId;
                }
                else
                {
                    // 自营
                    defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.DownFxUserId == fxUserId);
                    upFxUserId = defaultPathRefConfig?.FxUserId ?? 0;
                }
            }

            return new Tuple<int, int, bool>(upFxUserId, downFxUserId, false);
        }


        public bool GetIsShowSalePrice(int fxUserId, string pathFlowCode, Dictionary<string, List<PathFlowNode>> pathFlowNodeDic)
        {
            var isShowSalePrice = true;
            List<PathFlowNode> nodes;
            if (pathFlowNodeDic.TryGetValue(pathFlowCode, out nodes))
            {
                /*
                 * 从商家节点到当前厂家节点是否都是价格可见
                 * FxUserId,UpFxUserId,DownFxUserId
                 * 1,0,2
                 * 2,1,3
                 * 3,2,4
                 * 4,3,0
                 * 商家角色的有：1,2,3
                 * 当前节点是3，需要1->2,2->3均可见才显示
                 * 当前节点是4，需要1->2,2->3,3->4均可见才显示
                 */
                foreach (var node in nodes)
                {
                    if (node.DownFxUserId == 0)
                        continue;
                    if (node.FxUserId == fxUserId)
                        break;
                    isShowSalePrice = node.IsShowSalePrice;
                    if (isShowSalePrice == false)
                        break;
                }
            }
            return isShowSalePrice;
        }
        /// <summary>
        /// 厂家和商家
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public List<ProductFx> GetProductByDownFxUserV2(int fxUserId, List<ProductFx> list, string from, List<ProductPathFlowRefType> refTypes)
        {

            if (list == null || (list != null && list.Count == 0))
                return list;

            var agents = new SupplierUserService().GetAgentList(fxUserId);
            var agentsList = agents?.GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.AgentMobileAndRemark ?? "");

            var suppliers = new SupplierUserService().GetSupplierList(fxUserId);
            var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");

            var myShopList = new FxUserShopService().GetShopsByFxUserId(fxUserId, false);

            //获取Sku路径
            var pathflowService = new PathFlowService(_connectionString);
            var pCodes = list.Select(x => x.ProductCode).Distinct().ToList();
            var fields = "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
            var pathFlows = pathflowService.GetPathFlows(pCodes, 0, fields);

            // 上下游销售价是否可见
            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            var nodes = pathFlows.SelectMany(x => x.PathFlowNodes).ToList();
            var pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(nodes);

            //Utility.CommUtls.WriteToLog($"pCodes={pCodes.ToJson()},pathFlows={pathFlows.ToJson()}", "PathFlow.txt", "PathFlow");

            //兼容新旧数据补上Config信息
            CompatibleOldDataToAddConfig(pathFlows);

            //商家店铺数据
            var agentShopList = new ShopRepository().GetShopByIds(list.Select(s => s.ShopId).Distinct().ToList());
            
            var newList = new List<ProductFx>();
            var commonSettingRepository = new CommonSettingRepository();
            list.ForEach(p =>
            {
                var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(p.ProductCode)).ToList();
                // 商品上下游用户Id
                var tuple = GetUpAndDownFxUserIdV2(from, true, p.ProductCode, fxUserId, productPathFlows);
                //if (tuple.Item3)
                //    return;
                p.UpFxUserId = tuple.Item1;
                p.DownFxUserId = tuple.Item2;

                // 是否商品绑定厂家
                p.IsCurBindProduct = pathFlows.Any(x => x.PathFlowReferences.ContainsKey(p.ProductCode) && x.PathFlowNodes.Any(y => y.FxUserId == fxUserId));
                //var isRootProduct = pathFlows.Any(x => x.PathFlowReferences.ContainsKey(p.ProductCode) && x.RootNode != null && x.RootNode.FxUserId == fxUserId);

                var supplerName = "";
                if (p.DownFxUserId > 0)
                {
                    var supplierRes = supplierList.Where(t => t.Key == p.DownFxUserId).FirstOrDefault();
                    supplerName = supplierRes.Value;
                }

                var agentName = "";
                if (p.UpFxUserId > 0)
                {
                    var agentRes = agentsList.Where(t => t.Key == p.UpFxUserId).FirstOrDefault();
                    agentName = agentRes.Value;
                }
                else
                {
                    var myshop = myShopList.Where(w => w.ShopId == p.ShopId).FirstOrDefault();

                    if (myshop != null)
                    {
                        agentName = myshop.NickName;
                        p.IsSelfShop = true;

                        //补充没有平台类型的数据
                        if (string.IsNullOrEmpty(p.PlatformType))
                            p.PlatformType = myshop.PlatformType;
                    }
                }
                p.AgentName = agentName;
                p.SupplerName = supplerName;
                p.PathFlow = null;//清除,只是用于处理数据

                var newSkus = new List<ProductSkuFx>();
                p.Skus.ForEach(sku =>
                {
                    var skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.SkuCode)).ToList();
                    // 判断当前节点Sku路径是否继承于商品，继承至商品的这一段路径跟随商品显示
                    //var isFromProductPath = skuPathFlows?.Any(x => x.PathFlowReferences[sku.SkuCode].ReferenceConfigs.Any(y => y.FxUserId == fxUserId && y.FromType == 1)) ?? false; 
                    if (skuPathFlows == null || skuPathFlows.Any() == false)
                    {
                        sku.BindFrom = "Product";
                        sku.UpFxUserId = p.UpFxUserId;
                        sku.DownFxUserId = p.DownFxUserId;
                        sku.AgentName = p.AgentName;
                        sku.SupplerName = p.SupplerName;
                        sku.PlatformType = p.PlatformType;

                        // 绑定的是商品：无绑定厂家的SKU跟商品走，绑定的是SKU：仅显示对应的SKU+自己店铺的SKU
                        if (!p.IsCurBindProduct && !p.IsSelfShop)
                            return;
                        if (from == "other" && p.DownFxUserId == 0)
                            return;
                        else if (from == "oneself" && p.DownFxUserId > 0)
                            return;

                        // Sku来源商品，判断商品路径上游是否有设置销售价不可见
                        var isShowSalePrice = true;
                        foreach (var flow in productPathFlows)
                        {
                            isShowSalePrice = GetIsShowSalePrice(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                            if (isShowSalePrice == false)
                                break;
                        }
                        if (isShowSalePrice == false)
                            sku.SalePrice = 0;
                    }
                    else
                    {

                        // 非厂家链上的Sku不展示
                        if (skuPathFlows.Any(x => x.PathFlowNodes.Any(xx => xx.FxUserId == fxUserId)) == false)
                            return;

                        sku.BindFrom = "Sku";
                        var tuple2 = GetUpAndDownFxUserIdV2(from, false, sku.SkuCode, fxUserId, skuPathFlows);
                        if (tuple2.Item3)
                            return;

                        sku.UpFxUserId = tuple2.Item1;
                        sku.DownFxUserId = tuple2.Item2;
                        //sku.SkuDownFxUserId = sku.DownFxUserId;

                        sku.SupplierCount = skuPathFlows.SelectMany(x => x.PathFlowNodes).Where(x => x.FxUserId == fxUserId).Select(x => x.DownFxUserId).Distinct().Count();
                        // SKU厂家与商品一致，跟随商品显示
                        if (p.IsCurBindProduct && sku.DownFxUserId == p.DownFxUserId)
                        {
                            p.IsBindOtherSku = p.IsBindOtherSku == 2 ? 2 : 1;
                            sku.DownFxUserId = p.DownFxUserId;
                            sku.SupplerName = p.SupplerName;
                            sku.PlatformType = p.PlatformType;

                            //sku上游信息不一定跟商品信息一致
                            if (sku.UpFxUserId != p.UpFxUserId && sku.UpFxUserId > 0 && agentsList.ContainsKey(sku.UpFxUserId))
                            {
                                sku.AgentName = agentsList[sku.UpFxUserId];
                            }
                            else
                            {
                                sku.UpFxUserId = p.UpFxUserId;
                                sku.AgentName = p.AgentName;
                            }
                        }
                        else
                        {
                            p.IsBindOtherSku = 2;
                            supplerName = "";
                            if (sku.DownFxUserId > 0)
                            {
                                var supplierRes = supplierList.Where(t => t.Key == sku.DownFxUserId).FirstOrDefault();
                                supplerName = supplierRes.Value;
                            }

                            agentName = "";
                            if (sku.UpFxUserId > 0)
                            {
                                var agentRes = agentsList.Where(t => t.Key == sku.UpFxUserId).FirstOrDefault();
                                agentName = agentRes.Value;
                            }
                            else
                            {
                                var myshop = myShopList.Where(w => w.ShopId == sku.ShopId).FirstOrDefault();
                                if (myshop != null)
                                {
                                    agentName = myshop.NickName;
                                    //补充没有平台类型的数据
                                    if (string.IsNullOrEmpty(sku.PlatformType))
                                        sku.PlatformType = myshop.PlatformType;
                                    else
                                        sku.PlatformType = p.PlatformType;
                                }
                                else
                                {
                                    agentName = p.AgentName;
                                    sku.PlatformType = p.PlatformType;
                                }
                            }
                            sku.AgentName = agentName;
                            sku.SupplerName = supplerName;
                        }

                        // Sku路径上判断上游是否有设置销售价不可见
                        var isShowSalePrice = true;
                        foreach (var flow in skuPathFlows)
                        {
                            isShowSalePrice = GetIsShowSalePrice(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                            if (isShowSalePrice == false)
                                break;
                        }
                        if (isShowSalePrice == false)
                            sku.SalePrice = 0;
                    }

                    newSkus.Add(sku);
                });
                p.Skus = newSkus.OrderBy(x => x.Name).ToList();
                var pSkuPathFlows = pathFlows.Where(x => x.PathFlowNodes.Any(y => y.FxUserId == fxUserId) && x.PathFlowReferences.Any(y => y.Key == p.ProductCode || p.Skus.Any(z => z.SkuCode == y.Key)));
                var downFxUserIds = pSkuPathFlows.SelectMany(x => x.PathFlowNodes).Where(x => x.FxUserId == fxUserId).Select(x => x.DownFxUserId).Distinct().ToList();
                if (p.SupplierCount <= 0)
                {
                    p.SupplierCount = downFxUserIds.Count();
                }
                //Sku全部为自营
                //p.IsAllSkuSelf = p.Skus.All(x => x.DownFxUserId == 0 && x.UpFxUserId == 0) && p.IsSelfShop;
                p.IsAllSkuSelf = refTypes.Any(r => r.ProductCode == p.ProductCode && r.DownFxUserIdCount == 0);

                // 商品SKU售价区间
                var maxV = p.Skus?.OrderByDescending(s => s.SalePrice).FirstOrDefault();
                var minV = p.Skus?.OrderBy(s => s.SalePrice).FirstOrDefault();
                p.minPrice = Convert.ToDecimal(minV?.SalePrice);
                p.maxPrice = Convert.ToDecimal(maxV?.SalePrice);

                var minPurchaseV = p.Skus?.OrderBy(s => s.PurchasePrice).FirstOrDefault();
                var maxPurchaseV = p.Skus?.OrderByDescending(s => s.PurchasePrice).FirstOrDefault();
                p.minPurchasePrice = Convert.ToDecimal(minPurchaseV?.PurchasePrice);
                p.maxPurchasePrice = Convert.ToDecimal(maxPurchaseV?.PurchasePrice);
                newList.Add(p);
            });
            return newList;
        }



        /// <summary>
        /// 厂家和商家
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public List<ProductFx> GetProductByDownFxUser(int fxUserId, List<ProductFx> list, string from)
        {

            if (list == null || (list != null && list.Count == 0))
                return list;

            var agents = new SupplierUserService().GetAgentList(fxUserId, needEncryptAccount:true);
            var agentsList = agents?.GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.AgentMobileAndRemark ?? "");

            var suppliers = new SupplierUserService().GetSupplierList(fxUserId, needEncryptAccount:true);
            var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");

            var myShopList = new FxUserShopService().GetShopsByFxUserId(fxUserId, false);

            //获取Sku路径
            var pathflowService = new PathFlowService(_connectionString);
            var pCodes = list.Select(x => x.ProductCode).Distinct().ToList();
            var fields = "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
            var pathFlows = pathflowService.GetPathFlows(pCodes, 0, fields);

            // 上下游销售价是否可见
            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            var nodes = pathFlows.SelectMany(x => x.PathFlowNodes).ToList();
            var pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(nodes);

            //Utility.CommUtls.WriteToLog($"pCodes={pCodes.ToJson()},pathFlows={pathFlows.ToJson()}", "PathFlow.txt", "PathFlow");

            //兼容新旧数据补上Config信息
            CompatibleOldDataToAddConfig(pathFlows);


            //商家店铺数据
            var agentShopList = new ShopRepository().GetShopByIds(list.Select(s => s.ShopId).Distinct().ToList());

            var commonSettingRepository = new CommonSettingRepository();

            Parallel.ForEach(list, new ParallelOptions { MaxDegreeOfParallelism = Environment.ProcessorCount - 1 }, p =>
            {

                var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(p.ProductCode)).ToList();
                // 商品上下游用户Id
                var tuple = GetUpAndDownFxUserIdV2(from, true, p.ProductCode, fxUserId, productPathFlows);
                //if (tuple.Item3)
                //    return;
                p.UpFxUserId = tuple.Item1;
                p.DownFxUserId = tuple.Item2;

                // 是否商品绑定厂家
                p.IsCurBindProduct = pathFlows.Any(x => x.PathFlowReferences.ContainsKey(p.ProductCode) && x.PathFlowNodes.Any(y => y.FxUserId == fxUserId));
                //var isRootProduct = pathFlows.Any(x => x.PathFlowReferences.ContainsKey(p.ProductCode) && x.RootNode != null && x.RootNode.FxUserId == fxUserId);

                var supplerName = "";
                if (p.DownFxUserId > 0)
                {
                    var supplierRes = supplierList.Where(t => t.Key == p.DownFxUserId).FirstOrDefault();
                    supplerName = supplierRes.Value;
                }

                var agentName = "";
                if (p.UpFxUserId > 0)
                {
                    var agentRes = agentsList.Where(t => t.Key == p.UpFxUserId).FirstOrDefault();
                    agentName = agentRes.Value;

                    // 店铺名称是否可见
                    ConvertShowShopName(fxUserId, productPathFlows, pathFlowNodeDic, p, null, commonSettingRepository, agentShopList);
                }
                else
                {
                    var myshop = myShopList.Where(w => w.ShopId == p.ShopId).FirstOrDefault();

                    if (myshop != null)
                    {
                        agentName = myshop.NickName;
                        p.IsSelfShop = true;

                        //补充没有平台类型的数据
                        if (string.IsNullOrEmpty(p.PlatformType))
                            p.PlatformType = myshop.PlatformType;
                    }
                }
                p.AgentName = agentName;
                p.SupplerName = supplerName;
                p.PathFlow = null;//清除,只是用于处理数据

                // 商品标题是否可见
                ConvertShowProductTitle(fxUserId, productPathFlows, pathFlowNodeDic,p,null, commonSettingRepository);

                // 商品图片是否可见
                ConvertShowProductImg(fxUserId, productPathFlows, pathFlowNodeDic, p, null, commonSettingRepository);

                var newSkus = new List<ProductSkuFx>();
                p.Skus.ForEach(sku =>
                {
                    var skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.SkuCode)).ToList();
                    // 判断当前节点Sku路径是否继承于商品，继承至商品的这一段路径跟随商品显示
                    //var isFromProductPath = skuPathFlows?.Any(x => x.PathFlowReferences[sku.SkuCode].ReferenceConfigs.Any(y => y.FxUserId == fxUserId && y.FromType == 1)) ?? false; 
                    if (skuPathFlows == null || skuPathFlows.Any() == false)
                    {
                        sku.BindFrom = "Product";
                        sku.UpFxUserId = p.UpFxUserId;
                        sku.DownFxUserId = p.DownFxUserId;
                        sku.AgentName = p.AgentName;
                        sku.SupplerName = p.SupplerName;
                        sku.PlatformType = p.PlatformType;
                        sku.AgentShopName = p.AgentShopName;
                        sku.AgentShopPlatformType = p.AgentShopPlatformType;

                        // 商品图片是否可见
                        ConvertShowProductImg(fxUserId, productPathFlows, pathFlowNodeDic,null, sku, commonSettingRepository);

                        // 绑定的是商品：无绑定厂家的SKU跟商品走，绑定的是SKU：仅显示对应的SKU+自己店铺的SKU
                        if (!p.IsCurBindProduct && !p.IsSelfShop)
                            return;
                        if (from == "other" && p.DownFxUserId == 0)
                            return;
                        else if (from == "oneself" && p.DownFxUserId > 0)
                            return;

                        // Sku来源商品，判断商品路径上游是否有设置销售价不可见
                        sku.IsShowSalePrice = true;
                        foreach (var flow in productPathFlows)
                        {
                            sku.IsShowSalePrice = GetIsShowSalePrice(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                            if (sku.IsShowSalePrice == false)
                                break;
                        }
                        if (sku.IsShowSalePrice == false)
                            sku.SalePrice = 0;
                    }
                    else
                    {
                        //绑定的是SKU，商品信息跟随SKU权限
                        if (!p.IsCurBindProduct && !p.IsSelfShop)
                        {
                            // 商品标题是否可见
                            ConvertShowProductTitle(fxUserId, skuPathFlows, pathFlowNodeDic, p, null, commonSettingRepository);
                            ConvertShowProductImg(fxUserId, skuPathFlows, pathFlowNodeDic, p, null, commonSettingRepository);
                        }

                        // 商品图片是否可见
                        ConvertShowProductImg(fxUserId, skuPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository);

                        // 非厂家链上的Sku不展示
                        if (skuPathFlows.Any(x => x.PathFlowNodes.Any(xx => xx.FxUserId == fxUserId)) == false)
                            return;

                        sku.BindFrom = "Sku";
                        var tuple2 = GetUpAndDownFxUserIdV2(from, false, sku.SkuCode, fxUserId, skuPathFlows);
                        if (tuple2.Item3)
                            return;

                        sku.UpFxUserId = tuple2.Item1;
                        sku.DownFxUserId = tuple2.Item2;
                        //sku.SkuDownFxUserId = sku.DownFxUserId;

                        sku.SupplierCount = skuPathFlows.SelectMany(x => x.PathFlowNodes).Where(x => x.FxUserId == fxUserId).Select(x => x.DownFxUserId).Distinct().Count();
                        // SKU厂家与商品一致，跟随商品显示
                        if (p.IsCurBindProduct && sku.DownFxUserId == p.DownFxUserId)
                        {
                            p.IsBindOtherSku = p.IsBindOtherSku == 2 ? 2 : 1;
                            sku.UpFxUserId = p.UpFxUserId;
                            sku.DownFxUserId = p.DownFxUserId;
                            sku.AgentName = p.AgentName;
                            sku.SupplerName = p.SupplerName;
                            sku.PlatformType = p.PlatformType;
                            sku.AgentShopName = p.AgentShopName;
                            sku.AgentShopPlatformType = p.AgentShopPlatformType;
                        }
                        else
                        {
                            p.IsBindOtherSku = 2;
                            supplerName = "";
                            if (sku.DownFxUserId > 0)
                            {
                                var supplierRes = supplierList.Where(t => t.Key == sku.DownFxUserId).FirstOrDefault();
                                supplerName = supplierRes.Value;
                            }

                            agentName = "";
                            if (sku.UpFxUserId > 0)
                            {
                                var agentRes = agentsList.Where(t => t.Key == sku.UpFxUserId).FirstOrDefault();
                                agentName = agentRes.Value;

								// 店铺名称是否可见
								sku.ShopId = p.ShopId;
								if (string.IsNullOrWhiteSpace(p.AgentShopName))
                                {
                                    ConvertShowShopName(fxUserId, skuPathFlows, pathFlowNodeDic, p, sku, commonSettingRepository, agentShopList);
                                }
                                else
                                {
									ConvertShowShopName(fxUserId, skuPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository, agentShopList);
								}
                            }
                            else
                            {
                                var myshop = myShopList.Where(w => w.ShopId == sku.ShopId).FirstOrDefault();
                                if (myshop != null)
                                {
                                    agentName = myshop.NickName;
                                    //补充没有平台类型的数据
                                    if (string.IsNullOrEmpty(sku.PlatformType))
                                        sku.PlatformType = myshop.PlatformType;
                                    else
                                        sku.PlatformType = p.PlatformType;
                                }
                                else
                                {
                                    agentName = p.AgentName;
                                    sku.PlatformType = p.PlatformType;
                                }
                            }
                            sku.AgentName = agentName;
                            sku.SupplerName = supplerName;

                        }

                        // Sku路径上判断上游是否有设置销售价不可见
                        sku.IsShowSalePrice = true;
                        foreach (var flow in skuPathFlows)
                        {
                            sku.IsShowSalePrice = GetIsShowSalePrice(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                            if (sku.IsShowSalePrice == false)
                                break;
                        }
                        if (sku.IsShowSalePrice == false)
                            sku.SalePrice = 0;

                        
                    }
                    
                    newSkus.Add(sku);
                });
                p.Skus = newSkus.OrderBy(x => x.Name).ToList();
                var pSkuPathFlows = pathFlows.Where(x => x.PathFlowNodes.Any(y => y.FxUserId == fxUserId) && x.PathFlowReferences.Any(y => y.Key == p.ProductCode || p.Skus.Any(z => z.SkuCode == y.Key)));
                var downFxUserIds = pSkuPathFlows.SelectMany(x => x.PathFlowNodes).Where(x => x.FxUserId == fxUserId).Select(x => x.DownFxUserId).Distinct().ToList();
                p.SupplierCount = downFxUserIds.Count();
                //Sku全部为自营
                p.IsAllSkuSelf = p.Skus.All(x => x.DownFxUserId == 0 && x.UpFxUserId == 0) && p.IsSelfShop;

                // 商品SKU售价区间
                var maxV = p.Skus?.OrderByDescending(s => s.SalePrice).FirstOrDefault();
                var minV = p.Skus?.OrderBy(s => s.SalePrice).FirstOrDefault();
                p.minPrice = Convert.ToDecimal(minV?.SalePrice);
                p.maxPrice = Convert.ToDecimal(maxV?.SalePrice);

                var minPurchaseV = p.Skus?.OrderBy(s => s.PurchasePrice).FirstOrDefault();
                var maxPurchaseV = p.Skus?.OrderByDescending(s => s.PurchasePrice).FirstOrDefault();
                p.minPurchasePrice = Convert.ToDecimal(minPurchaseV?.PurchasePrice);
                p.maxPurchasePrice = Convert.ToDecimal(maxPurchaseV?.PurchasePrice);
            });
            return list;
        }

        private Tuple<int, int, bool> GetUpAndDownFxUserId(string from, bool isProduct, string refCode, int fxUserId, List<PathFlow> pathFlows)
        {
            var downFxUserId = 0;
            var upFxUserId = 0;
            var pathRefConfigs = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(refCode)).SelectMany(x => x.PathFlowReferences.SelectMany(y => y.Value.ReferenceConfigs)).ToList();
            if (from == "oneself")
            {
                // 自营-过滤绑定厂家商品
                //if (pathFlows.Where(x => x.PathFlowReferences.ContainsKey(refCode)).SelectMany(x => x.PathFlowReferences.SelectMany(y => y.Value.ReferenceConfigs)).Any(z => z.DownFxUserId > 0 && z.FxUserId == fxUserId && (isProduct || (!isProduct && z.FromType == 2))))
                //    return new Tuple<int, int, bool>(0, 0, true);

                if (pathRefConfigs.Any(x => x.PathFlowRefCode == refCode && x.FxUserId == fxUserId && (isProduct || (!isProduct && x.FromType == 2))))
                    return new Tuple<int, int, bool>(0, 0, true);

                // 默认显示默认厂家(自营downFxUserId=0,)
                var defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.ConfigType == 0 && x.FxUserId > 0 && x.DownFxUserId == fxUserId && (isProduct || (!isProduct && x.FromType == 2)));
                if (defaultPathRefConfig != null)
                    upFxUserId = defaultPathRefConfig.FxUserId;
                else
                {
                    defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.DownFxUserId == fxUserId && (isProduct || (!isProduct && x.FromType == 2)));
                    upFxUserId = defaultPathRefConfig?.FxUserId ?? 0;
                }
            }
            else if (from == "other")
            {
                // 厂家供货-过滤自营商品
                if (pathRefConfigs.Any(x => x.PathFlowRefCode == refCode && x.FxUserId == fxUserId && (isProduct || (!isProduct && x.FromType == 2))) == false)
                    return new Tuple<int, int, bool>(0, 0, true);

                var defaultPathRefConfig = pathRefConfigs.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.ConfigType == 0 && x.DownFxUserId > 0 && x.FxUserId == fxUserId && (isProduct || (!isProduct && x.FromType == 2)));
                if (defaultPathRefConfig != null)
                {
                    downFxUserId = defaultPathRefConfig.DownFxUserId;
                    upFxUserId = defaultPathRefConfig.UpFxUserId;
                }
                else
                {
                    defaultPathRefConfig = pathRefConfigs.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.DownFxUserId > 0 && x.FxUserId == fxUserId && (isProduct || (!isProduct && x.FromType == 2)));
                    downFxUserId = defaultPathRefConfig?.DownFxUserId ?? 0;
                    upFxUserId = defaultPathRefConfig?.UpFxUserId ?? 0;
                }
            }
            else
            {
                // 所有商品列表默认显示顺序：默认厂家=>配置厂家=>自营
                var defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.ConfigType == 0 && x.DownFxUserId > 0 && x.FxUserId == fxUserId && (isProduct || (!isProduct && x.FromType == 2)));
                if (defaultPathRefConfig != null)
                {
                    downFxUserId = defaultPathRefConfig.DownFxUserId;
                    upFxUserId = defaultPathRefConfig.UpFxUserId;
                }
                else
                {
                    defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.DownFxUserId > 0 && x.FxUserId == fxUserId && (isProduct || (!isProduct && x.FromType == 2)));
                    if (defaultPathRefConfig != null)
                    {
                        // 配置厂家
                        downFxUserId = defaultPathRefConfig.DownFxUserId;
                        upFxUserId = defaultPathRefConfig.UpFxUserId;
                    }
                    else
                    {
                        // 自营
                        defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.FxUserId > 0 && x.DownFxUserId == fxUserId && (isProduct || (!isProduct && x.FromType == 2)));
                        if (defaultPathRefConfig != null)
                            upFxUserId = defaultPathRefConfig.FxUserId;
                        else
                        {
                            defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.DownFxUserId == fxUserId && (isProduct || (!isProduct && x.FromType == 2)));
                            upFxUserId = defaultPathRefConfig?.FxUserId ?? 0;
                        }
                    }
                }
            }

            return new Tuple<int, int, bool>(upFxUserId, downFxUserId, false);
        }

        public List<ProductFx> TransferModelToProductFx(List<Product> list, int fxUserId, string platformType,
            int? createBy = null)
        {
            if (fxUserId == 0)
                throw new LogicException("平台产品转为分担系统的产品时，FxUserId为0");

            var fxList = new List<ProductFx>();

            if (list == null || !list.Any())
                return fxList;

            if (createBy.HasValue == false || createBy == 0)
            {
                createBy = fxUserId;
            }

            list.ForEach(p =>
            {
                var productFx = new ProductFx();
                productFx.StatusTag = p.StatusTag;
                productFx.CargoNumber = p.CargoNumber.ToString2();
                productFx.CategoryId = p.CategoryId;
                productFx.CategoryName = p.CategoryName;
                productFx.CreateBy = createBy.Value;
                productFx.CategoryId1 = p.CategoryId1;
                productFx.CategoryId2 = p.CategoryId2;
                productFx.CategoryId3 = p.CategoryId3;
                productFx.CategoryId4 = p.CategoryId4;
                productFx.CreateTime = p.CreateTime ?? DateTime.Now;
                productFx.ImageUrl = p.ImageUrl;
                productFx.ListingTime = null;
                productFx.PlatformId = p.PlatformId;
                productFx.PlatformType = p.PlatformType == null ? Convert.ToString(platformType) : p.PlatformType;

                //productFx.ProductCode = (p.PlatformId + p.ShopId).ToShortMd5();//底层自动生成
                productFx.ProductSelfCode = "";
                productFx.ShopId = p.ShopId;

                productFx.SourceUserId = fxUserId;
                productFx.Status = p.Status.ToString2();
                productFx.Subject = p.Subject;
                productFx.SystemCategoryId = "0";
                productFx.SystemStatus = 0;
                productFx.UpdateTime = DateTime.Now;
                productFx.ProductType = p.ProductType;
                productFx.Country = p.Country;
                if (!string.IsNullOrEmpty(p.From))
                {
                    productFx.From = p.From;
                }


                productFx.minPrice = p.CostPrice;

                if (platformType == PlatformType.Taobao.ToString().ToLower())
                {
                    Log.WriteError($"淘宝商品数据：{p?.ToJson()}","SynTaoBaoProductData.txt");
                }

                // 商品已删除
                var isDeleted = _repository.IsProductDelInPlatform(productFx);
                var hasDelInterfacePt = _repository.HasDelInterfacePlatform(productFx);
                if (hasDelInterfacePt)
                {
                    productFx.SystemStatus = isDeleted ? -1 : 0;
                }

                var productSkuFxList = new List<ProductSkuFx>();
                p.Skus?.ForEach(s =>
                {
                    var productSkuFx = new ProductSkuFx();

                    productSkuFx.ImgUrl = s.ProductSkuAttr?.SkuImgUrl;
                    productSkuFx.Name = s.ProductSkuAttr?.AttributeName + s.ProductSkuAttr?.AttributeValue;
                    productSkuFx.PlatformId = productFx.PlatformId;
                    productSkuFx.ProductCode = productFx.ProductCode;
                    productSkuFx.PurchasePrice = s.ProductSkuAttr?.CostPrice;
                    productSkuFx.SalePrice = s.Price;
                    productSkuFx.SkuBarCode = "";
                    productSkuFx.SkuId = string.IsNullOrEmpty(s.SkuId) ? s.SpecId : s.SkuId;
                    //productSkuFx.SkuCode = DateTime.Now.ToString("yyMMddHHmmss") + Math.Abs((s.SkuId + productFx.PlatformId + productFx.ProductCode).GetHashCode()).ToString();
                    productSkuFx.SkuCode = CustomerConfig.GetFxSkuCode(productSkuFx.PlatformId, s.SkuId,
                        productFx.ShopId, productFx.SourceUserId);
                    productSkuFx.SkuSelfCode = "";
                    productSkuFx.UpdateBy = productFx.CreateBy;

                    productSkuFx.AvailableCount = 0;
                    productSkuFx.CargoNumber = s.CargoNumber;
                    productSkuFx.CreateBy = productFx.CreateBy;
                    productSkuFx.CreateTime = DateTime.Now;
                    productSkuFx.UpdateTime = DateTime.Now;

                    productSkuFx.AttributeName1 = "";
                    productSkuFx.AttributeName2 = "";
                    productSkuFx.AttributeName3 = "";
                    productSkuFx.AttributeValue1 = s.ProductSkuAttr?.AttributeName?.ToString2();
                    productSkuFx.AttributeValue2 = s.ProductSkuAttr?.AttributeValue?.ToString2();
                    //完整的属性Json
                    productSkuFx.AttributeValue3 = s.SkuAttrs?.ToJson();

                    productSkuFx.SpecId = s.SpecId;
                    productSkuFx.PriceJson = s.PriceJson; //各类价格Json，目前仅1688代销商品有
                    productSkuFx.OuterSkuId = s.OuterSkuId;

                    // 商品已删除
                    if (hasDelInterfacePt)
                    {
                        var isDeletedSku = _repository.IsProductSkuDelInPlatform(productFx,s, isDeleted);
                        productSkuFx.SystemStatus = isDeletedSku ? -1 : 0;
                    }

                    if (string.IsNullOrEmpty(productSkuFx.ImgUrl)) //优化，sku没有图片时，使用商品主图
                        productSkuFx.ImgUrl = productFx.ImageUrl;

                    if (productSkuFx.SkuId.IsNotNullOrEmpty())
                        productSkuFxList.Add(productSkuFx);
                });

                productFx.Skus = productSkuFxList;

                fxList.Add(productFx);

            });

            return fxList;
        }

        public List<ProductFx> GetProducts(List<string> productCodes, int fxUserId)
        {
            return _repository.GetProducts(productCodes, fxUserId);
        }

        private Tuple<List<ProductFx>, CheckResult> CheckBindSupplier(BindSupplierRequestModel model)
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //var nextSupplierId = model.supplierId;
            var checkResult = new CheckResult { Success = true };
            //参数验证
            if (model == null)
                throw new LogicException("请求参数不能为空");
            //if (model.isSelf == false)
            //{
            //    if (model.supplierId <= 0)
            //        throw new LogicException("请选择厂家");
            //}
            //else
            //    nextSupplierId = fxUserId;
            if (model.productCodes == null || model.productCodes.Any() == false)
                throw new LogicException("请选择要设置厂家的产品");
            if (model.isSelf == false && model.Configs?.Count <= 0)
                throw new LogicException("请选择厂家");
            if (model.Configs != null && model.Configs.Any())
            {
                if (model.Configs.Any(x => x.ConfigType == 0) == false)
                    throw new LogicException("请设置一个默认厂家");
                else if (model.Configs.Count(x => x.ConfigType == 0) != 1)
                    throw new LogicException("只能设置一个默认厂家");
                if (model.Configs.Any(x => x.SupplierId == fxUserId))
                    throw new LogicException("下级厂家不能设置为自己");
                if (model.Configs.Any(x => x.SupplierId <= 0))
                    throw new LogicException("请选择厂家");
                //if (model.Configs.Any(x => x.RefCode.IsNullOrEmpty()))
                //    throw new LogicException("设置厂家的商品编码不能为空");
                else if (model.Configs.Where(x => x.ConfigType > 0).GroupBy(x => x.ConfigType).Count() > 1)
                    throw new LogicException("选定商品或规格只能设置一种匹配规则");
                else if (!model.isSelf && model.Configs.Any(x => x.ConfigType > 0 && x.Config.IsNullOrEmpty()))
                    throw new LogicException("设置商品或规格的匹配规则不能为空");

                // 检验配置规则是否有交集
                model.Configs.Where(x => x.Config.IsNotNullOrEmpty()).GroupBy(x => new { x.ConfigType }).ToList().ForEach(g =>
                {
                    if (g.Key.ConfigType == 1)
                    {
                        var provinceModels = g.ToList().SelectMany(x => x.Config.ToList<ThreeAreaFisrtModel>()).ToList();
                        provinceModels.GroupBy(x => x.ProvinceInfo.Name).ToList().ForEach(p =>
                        {
                            var cityList = p.SelectMany(x => x.CityLst).ToList();
                            if (cityList.GroupBy(x => x.CityInfo.Id).Any(x => x.Count() > 1))
                                throw new LogicException("地址设置有交集，请重新设置");
                        });
                    }
                    else if (g.Key.ConfigType == 2)
                    {
                        var starts = new List<DateTime>();
                        var ends = new List<DateTime>();
                        g.ToList().ForEach(x =>
                        {
                            var datePreFix = "2020-01-01";
                            var timeArr = x.Config.SplitToList(",");
                            if (timeArr.Count == 2)
                            {
                                var startTime = (datePreFix + " " + timeArr[0]).toDateTime();
                                var endTime = (datePreFix + " " + timeArr[1]).toDateTime();
                                if (startTime > endTime)
                                    throw new LogicException("开始日期不能大于结束日期");
                                starts.Add(startTime);
                                ends.Add(endTime);
                            }
                        });
                        if (!CompareDate(starts, ends))
                            throw new LogicException("时间配置存在交集，请重新设置");
                    }
                });
            }

            // 解绑店铺后再解除合作关系查不到商品信息，不再验证商品
            //var productFxService = new ProductFxService(_connectionString);
            //var products = productFxService.GetProducts(model.productCodes, fxUserId);
            //if (products == null || products.Any() == false)
            //    throw new LogicException($"未能查询到商品信息，请刷新重试");
            var products = new List<ProductFx>();
            //检查下级厂家是否和当前用户建立了合作关系，且合作关系必须是正常的            
            if (model.isSelf == false)
            {
                var supplierUserService = new SupplierUserService();

                #region 查询商品路径流信息
                var codes = model.productCodes.ToJson().ToList<string>().Distinct().ToList();
                //codes.AddRange(model.skuCodes?.Values.ToList());
                codes.AddRange(model.skuCodes?.SelectMany(c => c.Value.Split(',')));
                var pathFlowNodes = new PathFlowReferenceService(_connectionString).GetPathFlowNodeByProductCode(codes);
                if (pathFlowNodes == null || !pathFlowNodes.Any())
                    throw new LogicException($"订单不属于当前厂家，请跟商家确认是否取消了绑定");
                #endregion

                #region 检查合作关系，所有上游绑定关系都要检查
                var failProductCodes = new List<string>();
                var agentList = supplierUserService.GetAgentListV2(new List<int> { fxUserId }, getAll: true);
                if (agentList != null && agentList.Any())
                {
                    //只取非绑定成功
                    var agentFxUserIds = agentList.Where(a => a.Status != AgentBingSupplierStatus.Binded)?.Select(a => a.FxUserId).ToList();
                    if (agentFxUserIds != null && agentFxUserIds.Any())
                        failProductCodes.AddRange(pathFlowNodes.Where(a => a.FxUserId == fxUserId && agentFxUserIds.Contains(a.UpFxUserId))?.Select(a => a.ProductCode).ToList());
                }
                if (failProductCodes != null && failProductCodes.Any())
                {
                    checkResult.Success = false;
                    checkResult.Message = "系统检测到您勾选的部分商品正处于合作申请解绑状态中不满足代发商品绑定条件，避免绑定失败，是否暂时忽略该类商品绑定操作？如有疑问，您可私联商家确认解绑详情";
                    checkResult.Data = string.Join(",", failProductCodes);
                }
                else
                {
                    var targetSupplierIds = model.Configs.Select(x => x.SupplierId).Distinct().ToList();
                    var ids = supplierUserService.GetAvaliableSupplierId(fxUserId, targetSupplierIds);
                    if (ids.Count() < targetSupplierIds.Count())
                        throw new LogicException("您设置的厂家已经取消绑定了，请更换其他厂家，或前往【绑定厂家】菜单重新绑定厂家。");
                }
                #endregion

                #region 开通1688预付的厂家，不能再绑定下级厂家，暂时只支持抖店平台
                var needCheckProductCodes =
                    products.Where(m => m.PlatformType == PlatformType.TouTiao.ToString()).Select(m => m.ProductCode)
                        .ToList();
                var pathFlowNodesByGroup = pathFlowNodes.Where(m => needCheckProductCodes.Contains(m.ProductCode))
                    .GroupBy(m => m.PathFlowCode).ToList();
                var needCheckPathFlowNodesByGroup = pathFlowNodesByGroup.Where(m => m.Count() >= 2).ToList();
                if (agentList != null && agentList.Any())
                {
                    var checkResults = needCheckPathFlowNodesByGroup.Select(group =>
                    {
                        var currentPathFlowNode = group.FirstOrDefault(m => m.FxUserId == fxUserId);
                        var isPrePay = agentList.FirstOrDefault(m => m.FxUserId == currentPathFlowNode?.UpFxUserId)
                            ?.IsPrePay;
                        return new
                        {
                            PathFlowCode = group.Key,
                            currentPathFlowNode?.ProductCode,
                            currentPathFlowNode?.UpFxUserId,
                            FxUserId = fxUserId,
                            IsPrePay = isPrePay
                        };
                    }).ToList();
                    if (checkResults.Any(m => m.IsPrePay == true))
                    {
                        Log.WriteLine($"商品绑定厂家时，检查到您已经开启1688预付，暂时不支持绑定其他厂家，确认信息：{checkResults.ToJson(true)}",
                            "BindSupplierCheck1688PrePay.log");
                        throw new LogicException("您已经开启1688预付，暂时不支持绑定其他厂家。");
                    }
                }
                #endregion

            }
            return new Tuple<List<ProductFx>, CheckResult>(products, checkResult);
        }

        /// <summary>
        /// 日期也可以当成字符串进行比较，把开始日期，结束日期分别存进两个数组，并用sort排序，循环遍历数组，从开始时间的第二个元素去比较结束时间的第一个元素，
        /// 如果小于，就代表时间段有交叉，直接跳出，不然就继续遍历，遍历结束，说明时间没有重复
        /// </summary>
        /// <param name="starts"></param>
        /// <param name="ends"></param>
        /// <returns></returns>
        private bool CompareDate(List<DateTime> starts, List<DateTime> ends)
        {
            starts = starts.OrderBy(x => x).ToList();
            ends = ends.OrderBy(x => x).ToList();
            var count = starts.Count;
            for (int i = 1; i < count; i++)
            {
                if (starts[i] < ends[i - 1])
                    return false;
            }
            return true;
        }
        [Obsolete("已经有新版本")]
        public void SaveBindSupplierChangesOld(BindSupplierRequestModel model, PathFlowService pathFlowService, List<PathFlowChangeLog> changeLogs, LogForOperatorContext logContext, List<LogicOrder> waitSendOrders = null, AsyncTask asyncTask = null, bool isUnbind = false)
        {
            var guidCode = CommUtls.GetOnlyCode();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            try
            {
                var isReBindProductOrder = asyncTask != null && asyncTask.Flag == "ReBindProductOrder";
                WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-请求参数：{model.ToJson()}", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                var mergeredOrderLogicIds = new List<string>();
                var updateModels = new List<LogicOrderSplitUpdateModel>();
                var logicOrderService = new LogicOrderService(_connectionString);
                try
                {
                    if (changeLogs != null && changeLogs.Any())
                    {
                        var changeLogService = new PathFlowChangeLogService(_connectionString);
                        changeLogService.BulkInsert(changeLogs);
                    }
                }
                catch (Exception ex)
                {
                    var msg = $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】保存绑定厂家日志时发生错误,changeLogs:{changeLogs?.ToJson()}，错误详情：{ex}";
                    WriteSqlToLog(msg, null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                }
                var newPathFlows = pathFlowService.GetPathFlows(model.productCodes, 0);
                //兼容新旧数据补上Config信息
                CompatibleOldDataToAddConfig(newPathFlows);

                //更新历史订单
                if (model.isSaveHistoryData == false)
                {
                    var subLog1 = new LogForOperator { OperatorType = "查询待发货订单" };
                    logContext.StartStep(subLog1);

                    var tempProductCodes = model.productCodes;
                    var tempPathFlowCodes = new List<string>();
                    var orders = waitSendOrders != null && waitSendOrders.Any() ? waitSendOrders : logicOrderService.MatchWaitSellerSendLogicOrders(tempPathFlowCodes, tempProductCodes, true);

                    WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-待发货订单，来源：{(waitSendOrders != null ? "订单更新" : "绑定厂家")}，待发货订单【{orders.Count}】：{orders.GroupBy(x => x.PathFlowCode).ToDictionary(x => x.Key, x => x.Select(y => new { y.LogicOrderId })).ToJson()}", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                    subLog1.Detail = new { ProductCount = tempProductCodes, OrderCount = orders.Count };
                    logContext.EndStep();

                    #region 排除掉当前用户已经手工推的订单，以及待审核订单 （商家厂家解除合作关系，手工单也需要退回商家）   
                    if (isReBindProductOrder == false && isUnbind == false && orders != null && orders.Any())
                    {
                        TemplateCheckOrder checkorder = new TemplateCheckOrder(new AssignLogicOrderFactory(), new SplistLogicOrderFactory(), new CheckOrderFactory());
                        var logicOrderIds = orders.Select(x => x.LogicOrderId).ToList();
                        var allExceptIds = new List<string>();
                        var manualIds = checkorder.GetWaitCheckOrders(logicOrderIds, fxUserId);
                        if (manualIds != null && manualIds.Any())
                        {
                            allExceptIds.AddRange(manualIds);
                            //logicOrderIds = logicOrderIds.Where(x => manualIds.Contains(x) == false).ToList();
                        }
                        //移除该逻辑，自动待审核的单不能被排除，会有问题，应只排除手工退审的单，目前无法判断是否手工退审，先去掉该逻辑
                        //var orderCheckIds = new List<string>(); 
                        //if(logicOrderIds != null && logicOrderIds.Any())
                        //    orderCheckIds = new OrderCheckRepository().GetWaitCheckOrders(logicOrderIds,fxUserId);
                        //if (orderCheckIds != null && orderCheckIds.Any())
                        //    allExceptIds.AddRange(orderCheckIds);
                        if (allExceptIds.Any())
                        {
                            var newOrders = new List<LogicOrder>();
                            var manualLogicOrderIds = new List<string>();
                            //var manualDic = manuallist.GroupBy(x => x.LogicOrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault());
                            foreach (var o in orders)
                            {
                                //OrderManualRecordModel od;
                                if (allExceptIds.Contains(o.LogicOrderId))
                                    manualLogicOrderIds.Add(o.LogicOrderId);
                                else
                                    newOrders.Add(o);
                            }
                            if (manualLogicOrderIds.Any())
                            {
                                WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-手工单过滤：{string.Join(",", manualLogicOrderIds)}", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                                orders = newOrders;
                            }
                        }
                    }

                    #endregion

                    if (orders != null && orders.Any())
                    {
                        #region 订单匹配厂家前处理

                        var orderChangeDic = new Dictionary<string, List<int>>();
                        var subLog2 = new LogForOperator { OperatorType = "订单匹配新路径" };
                        logContext.StartStep(subLog2);

                        // 提前解析省市区Model
                        foreach (var item in newPathFlows)
                        {
                            foreach (var kv in item.PathFlowReferences)
                            {
                                var configs = kv.Value.ReferenceConfigs;
                                foreach (var config in configs)
                                {
                                    if (config.ConfigType == 1)
                                    {
                                        config.ProvinceInfos = config.Config.ToList<ThreeAreaFisrtModel>();
                                    }
                                }
                            }
                        }
                        #endregion

                        var tuple2 = GetWaitUpdateOrders(orders, newPathFlows);
                        mergeredOrderLogicIds = tuple2.Item1;
                        updateModels = tuple2.Item2;
                        orderChangeDic = tuple2.Item3;

                        //subLog2.Remark = newPathFlows.ToJson();
                        subLog2.Detail = orderChangeDic;
                        logContext.EndStep();
                    }
                }

                var subLog3 = new LogForOperator { OperatorType = "更新相关数据" };
                logContext.StartStep(subLog3);

                subLog3.Detail = new
                {
                    RestoreMergerOrders = mergeredOrderLogicIds,
                    OriginalLogicOrder = updateModels.Where(x => x.OriginalLogicOrder != null).GroupBy(x => x.OriginalLogicOrder.PathFlowCode).Select(x => new { PathFlowCode = x.Key, LogicOrderIds = x.Select(y => y.OriginalLogicOrder.LogicOrderId).Distinct().ToList() }),
                    OnlyUpdateLogicOrderPathFlow = updateModels.Where(x => x.OnlyUpdateLogicOrderPathFlow != null).GroupBy(x => x.OnlyUpdateLogicOrderPathFlow.PathFlowCode).Select(x => new { PathFlowCode = x.Key, LogicOrderIds = x.Select(y => y.OnlyUpdateLogicOrderPathFlow.LogicOrderId).Distinct().ToList() }),
                    UpdateLogicOrderCaculateFields = updateModels.Where(x => x.UpdateLogicOrderCaculateFields != null).Select(x => new { x.UpdateLogicOrderCaculateFields.LogicOrderId, x.UpdateLogicOrderCaculateFields.PathFlowCode, x.UpdateLogicOrderCaculateFields.ErpState, x.UpdateLogicOrderCaculateFields.ErpRefundState, x.UpdateLogicOrderCaculateFields.ProductCount, x.UpdateLogicOrderCaculateFields.ProductItemCount, x.UpdateLogicOrderCaculateFields.ProductKindCount, x.UpdateLogicOrderCaculateFields.TotalAmount, x.UpdateLogicOrderCaculateFields.TotalWeight }),
                    InsertOrders = updateModels.Where(x => x.InsertOrders != null).SelectMany(x => x.InsertOrders).GroupBy(x => x.PathFlowCode).Select(x => new { PathFlowCode = x.Key, LogicOrderIds = x.Select(y => y.LogicOrderId).Distinct().ToList() }),
                    NeedHandleOrderTags = updateModels.Where(x => x.NeedHandleOrderTags != null).SelectMany(x => x.NeedHandleOrderTags).GroupBy(x => x.PathFlowCode).Select(x => new { PathFlowCode = x.Key, OiCode = x.Select(y => y.OiCode).Distinct().ToList() })
                    //ExecutUpdateModel1 = updateModels.Select(x => new
                    //{
                    //    OriginalLogicOrder = x == null || x.OriginalLogicOrder == null ? null : new { x.OriginalLogicOrder.LogicOrderId, x.OriginalLogicOrder.PathFlowCode },
                    //    OnlyUpdateLogicOrderPathFlow = x == null || x.OnlyUpdateLogicOrderPathFlow == null ? null : new { x.OnlyUpdateLogicOrderPathFlow.LogicOrderId, x.OnlyUpdateLogicOrderPathFlow.PathFlowCode },
                    //    UpdateLogicOrderCaculateFields = x == null || x.UpdateLogicOrderCaculateFields == null ? null : new { x.UpdateLogicOrderCaculateFields.LogicOrderId, x.UpdateLogicOrderCaculateFields.PathFlowCode, x.UpdateLogicOrderCaculateFields.ErpState, x.UpdateLogicOrderCaculateFields.ErpRefundState, x.UpdateLogicOrderCaculateFields.ProductCount, x.UpdateLogicOrderCaculateFields.ProductItemCount, x.UpdateLogicOrderCaculateFields.ProductKindCount, x.UpdateLogicOrderCaculateFields.TotalAmount, x.UpdateLogicOrderCaculateFields.TotalWeight },
                    //    InsertOrders = x == null || x.InsertOrders == null ? null : x.InsertOrders.Select(y => new { y.LogicOrderId, y.PathFlowCode })
                    //})
                };

                var op = OptimisticLockOperationType.FxMergerOrder;
                var opId = $"FX{fxUserId}";
                var result = new CommonSettingService().ExecuteWithOptimisticLock(() =>
                {
                    Stopwatch sw = new Stopwatch();
                    sw.Start();
                    var paramJson = string.Empty;
                    try
                    {
                        //触发拆单动作
                        var ssubLog1 = new LogForOperator { OperatorType = "重置拆单", Remark = $"订单数量：{mergeredOrderLogicIds.Count}" };
                        paramJson = mergeredOrderLogicIds.ToJson();
                        WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-拆单工作，重置订单数：{mergeredOrderLogicIds.Count}，\n参数：{paramJson}", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                        new ServicesExtension.MergerFxOrderService(_connectionString).RestoreMergerOrders(mergeredOrderLogicIds);

                        ssubLog1.End();
                        subLog3.SubLogs.Add(ssubLog1);

                        sw.Stop();
                        WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-拆单工作，耗时：{sw.Elapsed.TotalMilliseconds}ms", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                    }
                    catch (Exception ex)
                    {
                        sw.Stop();
                        WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-拆单工作，重置订单数：{mergeredOrderLogicIds.Count}，耗时：{sw.Elapsed.TotalMilliseconds}ms，\n参数：{paramJson} \n异常消息：{ex}", null, $"BindSupplier-{fxUserId}-Error.txt", "BindSupplier");
                        throw ex;
                    }

                    try
                    {
                        sw.Restart();

                        var ssubLog2 = new LogForOperator { OperatorType = "更新订单" };
                        paramJson = new
                        {
                            OriginalLogicOrder = updateModels.Where(x => x.OriginalLogicOrder != null).GroupBy(x => x.OriginalLogicOrder.PathFlowCode).Select(x => new { PathFlowCode = x.Key, LogicOrderIds = x.Select(y => y.OriginalLogicOrder.LogicOrderId).Distinct().ToList() }),
                            OnlyUpdateLogicOrderPathFlow = updateModels.Where(x => x.OnlyUpdateLogicOrderPathFlow != null).GroupBy(x => x.OnlyUpdateLogicOrderPathFlow.PathFlowCode).Select(x => new { PathFlowCode = x.Key, LogicOrderIds = x.Select(y => y.OnlyUpdateLogicOrderPathFlow.LogicOrderId).Distinct().ToList() }),
                            UpdateLogicOrderCaculateFields = updateModels.Where(x => x.UpdateLogicOrderCaculateFields != null).Select(x => new { x.UpdateLogicOrderCaculateFields.LogicOrderId, x.UpdateLogicOrderCaculateFields.PathFlowCode, x.UpdateLogicOrderCaculateFields.ErpState, x.UpdateLogicOrderCaculateFields.ErpRefundState, x.UpdateLogicOrderCaculateFields.ProductCount, x.UpdateLogicOrderCaculateFields.ProductItemCount, x.UpdateLogicOrderCaculateFields.ProductKindCount, x.UpdateLogicOrderCaculateFields.TotalAmount, x.UpdateLogicOrderCaculateFields.TotalWeight }),
                            InsertOrders = updateModels.Where(x => x.InsertOrders != null).SelectMany(x => x.InsertOrders).GroupBy(x => x.PathFlowCode).Select(x => new { PathFlowCode = x.Key, LogicOrderIds = x.Select(y => y.LogicOrderId).Distinct().ToList() })
                        }.ToJson();
                        WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-更新订单，参数：{paramJson}", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                        //使用事务更新
                        logicOrderService.ExecutUpdateModel(updateModels, ssubLog2, asyncTask);

                        ssubLog2.End();
                        subLog3.SubLogs.Add(ssubLog2);

                        sw.Stop();
                        WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-更新订单，耗时：{sw.Elapsed.TotalMilliseconds}ms", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                    }
                    catch (Exception ex)
                    {
                        sw.Stop();
                        WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-更新订单，耗时：{sw.Elapsed.TotalMilliseconds}ms，\n参数：{paramJson} \n异常消息：{ex}", null, $"BindSupplier-{fxUserId}-Error.txt", "BindSupplier");
                        throw ex;
                    }


                    #region 修改的订单也需要重新走一手动审核流程

                    sw.Restart();
                    var ssubLog3 = new LogForOperator { OperatorType = "手动审核流程" };

                    try
                    {
                        IObserveCheckOrder objserve = new UpdateCheckOrderProduct();
                        List<LogicOrder> logicorders = new List<LogicOrder>();
                        updateModels.ForEach(x =>
                        {
                            if (x.InsertOrders != null && x.InsertOrders.Any())
                                logicorders.AddRange(x.InsertOrders);
                            if (x.OnlyUpdateLogicOrderPathFlow != null)
                                logicorders.Add(x.OnlyUpdateLogicOrderPathFlow);
                        });
                        objserve.Process(SiteContext.Current.CurrentFxUserId, logicorders);

                        sw.Stop();
                        WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-手工单审核流程，耗时：{sw.Elapsed.TotalMilliseconds}ms", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                        ssubLog3.End();
                        subLog3.SubLogs.Add(ssubLog3);
                    }
                    catch (Exception ex)
                    {
                        sw.Stop();
                        WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-手工单审核流程，耗时：{sw.Elapsed.TotalMilliseconds}ms \n异常消息：{ex}", null, $"BindSupplier-{fxUserId}-Error.txt", "BindSupplier");
                        throw ex;
                    }


                    #endregion

                    return true;
                }, null, op, opId);

                logContext.EndStep();

                if (result.IsExcuted == false)
                {
                    WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-当前还有任务未完成", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                    throw new LogicException("历史待发货订单正在变更新厂家或正在进行合单拆单操作，任务还未完成，请耐心等待...");
                }
            }
            catch (Exception ex)
            {
                var msg = $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家时发生错误，错误详情：{ex}";
                WriteSqlToLog(msg, null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                throw ex;
            }
        }

        /// <summary>
        /// 保存绑定供应商时变更信息（内部按需要加锁）
        /// </summary>
        /// <param name="model"></param>
        /// <param name="pathFlowService"></param>
        /// <param name="changeLogs"></param>
        /// <param name="logContext"></param>
        /// <param name="waitSendOrders"></param>
        /// <param name="asyncTask">异步任务</param>
        /// <param name="isUnbind">是否为解绑合作关系（true时手推单也要处理）</param>
        /// <param name="isNeedLock">是否需要锁</param>
        /// <param name="oldPathFlows"></param>
        public void SaveBindSupplierChanges(BindSupplierRequestModel model, PathFlowService pathFlowService,
            List<PathFlowChangeLog> changeLogs,
            LogForOperatorContext logContext, List<LogicOrder> waitSendOrders = null, AsyncTask asyncTask = null,
            bool isUnbind = false, bool isNeedLock = true, List<PathFlow> oldPathFlows = null)
        {
            //是否需要锁
            if (isNeedLock)
            {
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                //加入乐观锁处理
                var opId = $"FX{fxUserId}";
                var commonSettingService = new CommonSettingService();
                var result = commonSettingService.ExecuteWithOptimisticLock(() =>
                {
                    SaveBindSupplierChangesByNoLockV2(model, pathFlowService, changeLogs, logContext, waitSendOrders,
                        asyncTask, isUnbind, oldPathFlows);
                    return true;
                }, null, OptimisticLockOperationType.FxMergerOrder, opId);
                //没有被锁住，则返回
                if (result.IsExcuted)
                {
                    return;
                }
                //锁住，记录日志，抛出异常
                WriteSqlToLog($"用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家时，当前还有任务未完成", null,
                    $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                throw new LogicException("历史待发货订单正在变更新厂家或正在进行合单拆单操作，任务还未完成，请耐心等待...");
            }
            //可能外部方法有锁，内部就不加乐观锁
            SaveBindSupplierChangesByNoLockV2(model, pathFlowService, changeLogs, logContext, waitSendOrders,
                asyncTask, isUnbind, oldPathFlows);
        }

        /// <summary>
        /// 保存绑定供应商时变更信息（内部未加锁）
        /// </summary>
        /// <param name="model"></param>
        /// <param name="pathFlowService"></param>
        /// <param name="changeLogs"></param>
        /// <param name="logContext"></param>
        /// <param name="waitSendOrders"></param>
        /// <param name="asyncTask">异步任务</param>
        /// <param name="isUnbind">是否为解绑合作关系（true时手推单也要处理）</param>
        /// <param name="oldPathFlows"></param>
        [Obsolete("已经有新版本")]
        public void SaveBindSupplierChangesByNoLock(BindSupplierRequestModel model, PathFlowService pathFlowService,
            List<PathFlowChangeLog> changeLogs,
            LogForOperatorContext logContext, List<LogicOrder> waitSendOrders = null, AsyncTask asyncTask = null,
            bool isUnbind = false, List<PathFlow> oldPathFlows = null)
        {
            var guidCode = CommUtls.GetOnlyCode();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            try
            {
                var isReBindProductOrder = asyncTask != null && asyncTask.Flag == "ReBindProductOrder";
                WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-请求参数：{model.ToJson()}", null,
                    $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                #region 批量保存路径变更日志

                var logicOrderService = new LogicOrderService(_connectionString);
                try
                {
                    if (changeLogs != null && changeLogs.Any())
                    {
                        var changeLogService = new PathFlowChangeLogService(_connectionString);
                        changeLogService.BulkInsert(changeLogs);
                    }
                }
                catch (Exception ex)
                {
                    var msg =
                        $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】保存绑定厂家日志时发生错误,changeLogs:{changeLogs?.ToJson()}，错误详情：{ex}";
                    WriteSqlToLog(msg, null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                }

                #endregion

                var newPathFlows = pathFlowService.GetPathFlows(model.productCodes, 0);
                //兼容新旧数据补上Config信息
                CompatibleOldDataToAddConfig(newPathFlows);

                WriteSqlToLog(
                    $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】二、绑定厂家-循环切换商品绑定,model.IsFromUpdateLogicOrder={model.IsFromUpdateLogicOrder}\n newPathFlows==>{newPathFlows.ToJson()}",
                    null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                //日志批次号
                var batchNo = guidCode;

                model.productCodes = model.productCodes.Distinct().ToList();

                #region IsFromUpdateLogicOrder=true时，PathFlowReferenceConfig有设置省份才处理

                if (model.IsFromUpdateLogicOrder == true)
                {
                    var needProductCodes = new List<string>();
                    model.productCodes.ForEach(pCode =>
                    {
                        var pathFlowList = newPathFlows
                            .Where(x => x.PathFlowReferences.Values.Any(y =>
                                y.ProductCode == pCode && y.PathFlowRefType == "Product")).OrderByDescending(x => x.Id)
                            .ToList();
                        if (pathFlowList.Any())
                        {
                            var configs = new List<PathFlowReferenceConfig>();
                            pathFlowList.ForEach(pathFlow =>
                            {
                                configs.AddRange(pathFlow.PathFlowReferences.Values.ToList()
                                    .Where(a => a.ReferenceConfigs != null).SelectMany(a => a.ReferenceConfigs)
                                    .ToList());
                            });
                            if (configs.Any(a => a.ConfigType == 1 && a.Status == 0))
                            {
                                needProductCodes.Add(pCode);
                            }
                        }
                    });

                    WriteSqlToLog(
                        $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】二、1、绑定厂家-循环切换商品绑定,model.IsFromUpdateLogicOrder={model.IsFromUpdateLogicOrder}\n needProductCodes={needProductCodes.ToJson()}",
                        null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                    if (needProductCodes.Any() == false)
                        return;

                    model.productCodes = needProductCodes;
                }

                #endregion

                #region 批量绑定厂家分成每个商品分开进行绑定 ***** 核心处理逻辑 ******

                foreach (var pCode in model.productCodes)
                {
                    WriteSqlToLog(
                        $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】*************************** {pCode} ****************************",
                        null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                    var updateModels = new List<LogicOrderSplitUpdateModel>();
                    //重置逻辑单号
                    var mergerOrderLogicIds = new List<string>();
                    //更新历史订单
                    if (model.isSaveHistoryData == false)
                    {
                        var subLog1 = new LogForOperator { OperatorType = "查询待发货订单" };
                        logContext.StartStep(subLog1);

                        var tempProductCodes = new List<string> { pCode };
                        var tempPathFlowCodes = new List<string>();

                        var orders = new List<LogicOrder>();
                        if (waitSendOrders != null && waitSendOrders.Any())
                            orders = waitSendOrders?.Where(x => x.LogicOrderItems.Any(y => y.ProductCode == pCode))
                                .ToList();
                        else
                            orders = logicOrderService.MatchWaitSellerSendLogicOrders(tempPathFlowCodes,
                                tempProductCodes, true); //true排除采购已付款订单

                        #region 异常订单识别，待发货，已打印订单

                        //待发，已打印订单，识别未异常订单
                        orders.ForEach(logicOrder =>
                        {
                            if (logicOrder.PrintState >= 1 && logicOrder.ErpState == "waitsellersend" &&
                                oldPathFlows != null)
                            {
                                var pathFlowNodes = oldPathFlows
                                    .FirstOrDefault(y => y.PathFlowCode == logicOrder.PathFlowCode)
                                    ?.PathFlowNodes;

                                OrderAbnormalMessageService.SendMessage(new OrderAbnormalMessageModel
                                {
                                    FxUserId = logicOrder.FxUserId,
                                    ShopId = logicOrder.ShopId,
                                    AbnormalSource = AbnormalOrderSources.UnbindSupplier,
                                    AbnormalType = AbnormalOrderTypes.UnBindSupplier,
                                    LogicOrderId = logicOrder.LogicOrderId,
                                    PlatformOrderId = logicOrder.PlatformOrderId,
                                    PathFlowCode = logicOrder.PathFlowCode,
                                    PlatformType = logicOrder.PlatformType,
                                    AbnormalTime = DateTime.Now,
                                    PathFlowNodes = pathFlowNodes?.Select(m => new OrderAbnormalPathFlowNode
                                    {
                                        UniqueKey = m.UniqueKey,
                                        PathFlowCode = m.PathFlowCode,
                                        PathFlowNodeCode = m.PathFlowNodeCode,
                                        FxUserId = m.FxUserId,
                                        UpFxUserId = m.UpFxUserId,
                                        DownFxUserId = m.DownFxUserId,
                                        Status = m.Status,
                                        CreateTime = DateTime.Now
                                    }).ToList()
                                });
                            }
                        });

                        #endregion

                        //查询订单标签/只查询逻辑单对应的标签
                        logicOrderService.GetOrderTags(false, orders, CustomerConfig.ExceptionOrderManageTagNames);

                        WriteSqlToLog(
                            $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】1、绑定厂家-【{pCode}】待发货订单，来源：{(waitSendOrders != null ? "订单更新" : "绑定厂家")}，待发货订单【{orders.Count}】：{orders.GroupBy(x => x.PathFlowCode).ToDictionary(x => x.Key, x => x.Select(y => new { y.LogicOrderId })).ToJson()}",
                            null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                        subLog1.Detail = new { ProductCode = pCode, OrderCount = orders.Count };
                        logContext.EndStep();

                        #region 排除掉当前用户已经手工推的订单，以及待审核订单 （商家厂家解除合作关系，手工单也需要退回商家）

                        if (isReBindProductOrder == false && isUnbind == false && orders != null && orders.Any())
                        {
                            TemplateCheckOrder checkorder = new TemplateCheckOrder(new AssignLogicOrderFactory(),
                                new SplistLogicOrderFactory(), new CheckOrderFactory());
                            var logicOrderIds = orders.Select(x => x.LogicOrderId).ToList();
                            var allExceptIds = new List<string>();
                            var manualIds = checkorder.GetWaitCheckOrders(logicOrderIds, fxUserId);
                            if (manualIds != null && manualIds.Any())
                            {
                                allExceptIds.AddRange(manualIds);
                                //logicOrderIds = logicOrderIds.Where(x => manualIds.Contains(x) == false).ToList();
                            }

                            //移除该逻辑，自动待审核的单不能被排除，会有问题，应只排除手工退审的单，目前无法判断是否手工退审，先去掉该逻辑
                            //var orderCheckIds = new List<string>(); 
                            //if(logicOrderIds != null && logicOrderIds.Any())
                            //    orderCheckIds = new OrderCheckRepository().GetWaitCheckOrders(logicOrderIds,fxUserId);
                            //if (orderCheckIds != null && orderCheckIds.Any())
                            //    allExceptIds.AddRange(orderCheckIds);
                            if (allExceptIds.Any())
                            {
                                var newOrders = new List<LogicOrder>();
                                var manualLogicOrderIds = new List<string>();
                                //var manualDic = manuallist.GroupBy(x => x.LogicOrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault());
                                foreach (var o in orders)
                                {
                                    //OrderManualRecordModel od;
                                    if (allExceptIds.Contains(o.LogicOrderId))
                                        manualLogicOrderIds.Add(o.LogicOrderId);
                                    else
                                        newOrders.Add(o);
                                }

                                if (manualLogicOrderIds.Any())
                                {
                                    WriteSqlToLog(
                                        $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-1.1【{pCode}】手工单过滤：{string.Join(",", manualLogicOrderIds)}",
                                        null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                                    orders = newOrders;
                                }
                            }
                        }

                        #endregion

                        if (orders != null && orders.Any())
                        {

                            #region 逻辑单写入跟踪日志

                            const int batchSize = 500;
                            var count = Math.Ceiling(orders.Count * 1.0 / batchSize);
                            for (var i = 0; i < count; i++)
                            {
                                var tmpOrders = orders.Skip(i * batchSize).Take(batchSize).ToList();

                                var relatedLogicOrderIds = new List<string>();
                                relatedLogicOrderIds.AddRange(tmpOrders.Select(a => a.LogicOrderId));
                                var logModel = new LogicOrderInsertLogModel
                                {
                                    BatchNo = $"{batchNo}-{pCode}",
                                    CloudPlatformType = CustomerConfig.CloudPlatformType,
                                    CurFxUserId = SiteContext.Current.CurrentFxUserId,
                                    CollectionData = tmpOrders.Select(o => new SimpleLogicOrderModel
                                    {
                                        Id = o.Id,
                                        ShopId = o.ShopId,
                                        FxUserId = o.FxUserId,
                                        LogicOrderId = o.LogicOrderId,
                                        PathFlowCode = o.PathFlowCode,
                                        OldPathFlowCode = o.OldPathFlowCode,
                                        PlatformOrderId = o.PlatformOrderId,
                                        ErpState = o.ErpState,
                                        PlatformType = o.PlatformType,
                                        MergeredOrderId = o.MergeredOrderId,
                                        MergeredType = o.MergeredType,
                                        ChildOrderId = o.ChildOrderId,
                                        ApprovalStatus = o.ApprovalStatus,
                                        PrintState = o.PrintState,
                                        Items = o.LogicOrderItems
                                    }).ToList().ToJson(true),
                                    RelatedLogicOrderId = string.Join(",", relatedLogicOrderIds),
                                    OperationType = EventLogicOrderInsertOperationType.ChangeSupplierStep1.ToString(),
                                    MethodName = "ProductFxService.SaveBindSupplierChanges",
                                    Ext1 = newPathFlows.ToJson(),
                                    Ext2 = i.ToString(),
                                    StackTrace = Environment.StackTrace
                                };
                                LogicOrderInsertLogService.Instance.WriteLog(logModel);
                            }

                            #endregion

                            #region 订单匹配厂家前处理

                            var orderChangeDic = new Dictionary<string, List<int>>();
                            var subLog2 = new LogForOperator { OperatorType = "订单匹配新路径" };
                            logContext.StartStep(subLog2);

                            //提前解析省市区Model
                            newPathFlows.ForEach(item =>
                            {
                                foreach (var kv in item.PathFlowReferences)
                                {
                                    kv.Value.ReferenceConfigs?.ForEach(config =>
                                    {
                                        if (config.ConfigType != 1)
                                        {
                                            return;
                                        }

                                        config.ProvinceInfos = config.Config.ToList<ThreeAreaFisrtModel>();
                                    });
                                }
                            });

                            #endregion

                            #region 获取需要等待更新信息 *********** 重要逻辑 ***********

                            var tuple2 = GetWaitUpdateOrders(orders, newPathFlows);
                            mergerOrderLogicIds = tuple2.Item1;
                            updateModels = tuple2.Item2;
                            orderChangeDic = tuple2.Item3;

                            WriteSqlToLog(
                                $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】1.1、绑定厂家-【{pCode}】待发货订单，来源：{(waitSendOrders != null ? "订单更新" : "绑定厂家")}，过滤完手工单和待审待发货订单【{updateModels.Count}】：{orders.GroupBy(x => x.PathFlowCode).ToDictionary(x => x.Key, x => x.Select(y => new { y.LogicOrderId })).ToJson()}",
                                null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");


                            //subLog2.Remark = newPathFlows.ToJson();
                            subLog2.Detail = orderChangeDic;
                            logContext.EndStep();

                            #endregion

                            #region 逻辑单写入跟踪日志

                            var batchSize2 = 500;
                            var count2 = Math.Ceiling(updateModels.Count * 1.0 / batchSize);
                            for (int i = 0; i < count2; i++)
                            {
                                var tmpUpdateModels = updateModels.Skip(i * batchSize2).Take(batchSize2).ToList();
                                var logModel2 = new LogicOrderInsertLogModel
                                {
                                    BatchNo = $"{batchNo}-{pCode}",
                                    CloudPlatformType = CustomerConfig.CloudPlatformType,
                                    CurFxUserId = SiteContext.Current.CurrentFxUserId,
                                    CollectionData = tmpUpdateModels.ToJson(),
                                    OperationType = EventLogicOrderInsertOperationType.ChangeSupplierStep2.ToString(),
                                    MethodName = "ProductFxService.GetWaitUpdateOrders",
                                    Ext2 = i.ToString(),
                                    StackTrace = Environment.StackTrace,
                                };
                                LogicOrderInsertLogService.Instance.WriteLog(logModel2);
                            }

                            #endregion
                        }
                        else
                        {
                            WriteSqlToLog(
                                $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】1、绑定厂家-【{pCode}】待发货订单，来源：{(waitSendOrders != null ? "订单更新" : "绑定厂家")}，过滤完手工单和待审待发货订单【{orders.Count}】：{orders.GroupBy(x => x.PathFlowCode).ToDictionary(x => x.Key, x => x.Select(y => new { y.LogicOrderId })).ToJson()}",
                                null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                        }
                    }

                    #region 保存绑定供应商结果到逻辑单 *********** 重要逻辑 ***********

                    if (updateModels.Any() == false)
                        continue;
                    var subLog3 = new LogForOperator { OperatorType = "更新相关数据" };
                    logContext.StartStep(subLog3);

                    subLog3.Detail = new
                    {
                        RestoreMergerOrders = mergerOrderLogicIds,
                        OriginalLogicOrder = updateModels.Where(x => x.OriginalLogicOrder != null)
                            .GroupBy(x => x.OriginalLogicOrder.PathFlowCode).Select(x => new
                            {
                                PathFlowCode = x.Key,
                                LogicOrderIds = x.Select(y => y.OriginalLogicOrder.LogicOrderId).Distinct().ToList()
                            }),
                        OnlyUpdateLogicOrderPathFlow = updateModels.Where(x => x.OnlyUpdateLogicOrderPathFlow != null)
                            .GroupBy(x => x.OnlyUpdateLogicOrderPathFlow.PathFlowCode).Select(x =>
                                new
                                {
                                    PathFlowCode = x.Key,
                                    LogicOrderIds = x.Select(y => y.OnlyUpdateLogicOrderPathFlow.LogicOrderId)
                                        .Distinct().ToList()
                                }),
                        UpdateLogicOrderCaculateFields = updateModels
                            .Where(x => x.UpdateLogicOrderCaculateFields != null).Select(x => new
                            {
                                x.UpdateLogicOrderCaculateFields.LogicOrderId,
                                x.UpdateLogicOrderCaculateFields.PathFlowCode,
                                x.UpdateLogicOrderCaculateFields.ErpState,
                                x.UpdateLogicOrderCaculateFields.ErpRefundState,
                                x.UpdateLogicOrderCaculateFields.ProductCount,
                                x.UpdateLogicOrderCaculateFields.ProductItemCount,
                                x.UpdateLogicOrderCaculateFields.ProductKindCount,
                                x.UpdateLogicOrderCaculateFields.TotalAmount,
                                x.UpdateLogicOrderCaculateFields.TotalWeight
                            }),
                        InsertOrders = updateModels.Where(x => x.InsertOrders != null).SelectMany(x => x.InsertOrders)
                            .GroupBy(x => x.PathFlowCode).Select(x => new
                            {
                                PathFlowCode = x.Key, LogicOrderIds = x.Select(y => y.LogicOrderId).Distinct().ToList()
                            })
                    };
                    //辅助解绑合作关系标识
                    if (asyncTask != null)
                    {
                        asyncTask.IsUnbind = isUnbind;
                    }

                    //保存绑定供应商结果到逻辑单
                    SaveBindSupplierResultToLogicOrder(guidCode, batchNo, pCode, asyncTask, mergerOrderLogicIds,
                        updateModels, model.BatchId);

                    logContext.EndStep();

                    #endregion
                }

                #endregion

            }
            catch (Exception ex)
            {
                var msg = $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家时发生错误，错误详情：{ex}";
                WriteSqlToLog(msg, null, $"BindSupplier-{fxUserId}.txt", "BindSupplier", isWrite: true);
                ExceptionLogDataEventTrackingService.Instance.WriteLog("SaveBindSupplierChangesByNoLock", ex,
                    (asyncTask != null ? asyncTask.ToJson(true) : ""));
                throw ex;
            }
        }

        /// <summary>
        /// 保存绑定供应商时变更信息（内部未加锁）
        /// </summary>
        /// <param name="model"></param>
        /// <param name="pathFlowService"></param>
        /// <param name="changeLogs"></param>
        /// <param name="logContext"></param>
        /// <param name="waitSendOrders"></param>
        /// <param name="asyncTask">异步任务</param>
        /// <param name="isUnbind">是否为解绑合作关系（true时手推单也要处理）</param>
        /// <param name="oldPathFlows"></param>
        public void SaveBindSupplierChangesByNoLockV2(BindSupplierRequestModel model, PathFlowService pathFlowService,
            List<PathFlowChangeLog> changeLogs,
            LogForOperatorContext logContext, List<LogicOrder> waitSendOrders = null, AsyncTask asyncTask = null,
            bool isUnbind = false, List<PathFlow> oldPathFlows = null)
        {
            var guidCode = CommUtls.GetOnlyCode();
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            try
            {
                WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-请求参数：{model.ToJson()}", null,
                    $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                #region 批量保存路径变更日志

                try
                {
                    if (changeLogs != null && changeLogs.Any())
                    {
                        var changeLogService = new PathFlowChangeLogService(_connectionString);
                        changeLogService.BulkInsert(changeLogs);
                    }
                }
                catch (Exception ex)
                {
                    var msg =
                        $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】保存绑定厂家日志时发生错误,changeLogs:{changeLogs?.ToJson()}，错误详情：{ex}";
                    WriteSqlToLog(msg, null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                }

                #endregion

                var newPathFlows = pathFlowService.GetPathFlows(model.productCodes, 0);
                //兼容新旧数据补上Config信息
                CompatibleOldDataToAddConfig(newPathFlows);

                WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】二、绑定厂家-循环切换商品绑定", null,
                    $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                model.productCodes = model.productCodes.Distinct().ToList();

                #region IsFromUpdateLogicOrder=true时，PathFlowReferenceConfig有设置省份才处理

                if (model.IsFromUpdateLogicOrder)
                {
                    var needProductCodes = new List<string>();
                    model.productCodes.ForEach(pCode =>
                    {
                        var pathFlowList = newPathFlows
                            .Where(x => x.PathFlowReferences.Values.Any(y =>
                                y.ProductCode == pCode && y.PathFlowRefType == "Product")).OrderByDescending(x => x.Id)
                            .ToList();
                        if (pathFlowList.Any() == false)
                        {
                            return;
                        }

                        var configs = new List<PathFlowReferenceConfig>();
                        pathFlowList.ForEach(pathFlow =>
                        {
                            configs.AddRange(pathFlow.PathFlowReferences.Values.ToList()
                                .Where(a => a.ReferenceConfigs != null).SelectMany(a => a.ReferenceConfigs)
                                .ToList());
                        });
                        if (configs.Any(a => a.ConfigType == 1 && a.Status == 0))
                        {
                            needProductCodes.Add(pCode);
                        }
                    });

                    if (needProductCodes.Any() == false)
                        return;
                    model.productCodes = needProductCodes;
                }

                #endregion

                #region 批量绑定厂家分成每个商品分开进行绑定 ***** 核心处理逻辑 ******

                model.productCodes.ForEach(productCode =>
                {
                    ExceptionHandler.TryOneTime(() =>
                    {
                        SaveBindSupplierResultToLogicOrderByProduct(model, guidCode, productCode, newPathFlows,
                            logContext, waitSendOrders, asyncTask, isUnbind, oldPathFlows);
                        return true;
                    });
                });
                // Parallel.ForEach(model.productCodes, new ParallelOptions { MaxDegreeOfParallelism = 6 }, productCode =>
                // {
                //     ExceptionHandler.TryOneTime(() =>
                //     {
                //         SaveBindSupplierResultToLogicOrderByProduct(model, guidCode, productCode, newPathFlows,
                //             logContext, waitSendOrders, asyncTask, isUnbind, oldPathFlows);
                //         return true;
                //     });
                // });

                #endregion

            }
            catch (Exception ex)
            {
                var msg = $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家时发生错误，错误详情：{ex}";
                WriteSqlToLog(msg, null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                ExceptionLogDataEventTrackingService.Instance.WriteLog("SaveBindSupplierChangesByNoLock", ex,
                    (asyncTask != null ? asyncTask.ToJson(true) : ""));
                throw;
            }
        }

        /// <summary>
        /// 保存绑定供应商结果至逻辑单 按 商品
        /// </summary>
        /// <param name="model"></param>
        /// <param name="guidCode"></param>
        /// <param name="pCode"></param>
        /// <param name="newPathFlows"></param>
        /// <param name="logContext"></param>
        /// <param name="waitSendOrders"></param>
        /// <param name="asyncTask"></param>
        /// <param name="isUnbind"></param>
        /// <param name="oldPathFlows"></param>
        private void SaveBindSupplierResultToLogicOrderByProduct(BindSupplierRequestModel model, string guidCode,
            string pCode, List<PathFlow> newPathFlows,
            LogForOperatorContext logContext, List<LogicOrder> waitSendOrders = null, AsyncTask asyncTask = null,
            bool isUnbind = false, List<PathFlow> oldPathFlows = null)
        {

            var fxUserId = SiteContext.Current.CurrentFxUserId;
            WriteSqlToLog(
                $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】*************************** {pCode} ****************************",
                null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
            //逻辑单服务
            var logicOrderService = new LogicOrderService(_connectionString);
            //更新模型
            var updateModels = new List<LogicOrderSplitUpdateModel>();
            //重置逻辑单号
            var mergerOrderLogicIds = new List<string>();
            //日志批次号
            var batchNo = guidCode;
            //更新历史订单
            if (model.isSaveHistoryData == false)
            {
                var subLog1 = new LogForOperator { OperatorType = "查询待发货订单" };
                logContext.StartStep(subLog1);

                var tempProductCodes = new List<string> { pCode };
                var tempPathFlowCodes = new List<string>();

                List<LogicOrder> orders;
                if (waitSendOrders != null && waitSendOrders.Any())
                    orders = waitSendOrders?.Where(x => x.LogicOrderItems.Any(y => y.ProductCode == pCode)).ToList();
                else
                    orders = logicOrderService.MatchWaitSellerSendLogicOrders(tempPathFlowCodes, tempProductCodes,
                        true); //true排除采购已付款订单
                //为空处理
                if (orders == null)
                {
                    orders = new List<LogicOrder>();
                }
                #region 异常订单识别，待发货，已打印订单

                //待发，已打印订单，识别未异常订单
                orders.ForEach(logicOrder =>
                {
                    if (logicOrder.PrintState >= 1 && logicOrder.ErpState == "waitsellersend" &&
                        oldPathFlows != null)
                    {
                        var pathFlowNodes = oldPathFlows
                            .FirstOrDefault(y => y.PathFlowCode == logicOrder.PathFlowCode)
                            ?.PathFlowNodes;

                        OrderAbnormalMessageService.SendMessage(new OrderAbnormalMessageModel
                        {
                            FxUserId = logicOrder.FxUserId,
                            ShopId = logicOrder.ShopId,
                            AbnormalSource = AbnormalOrderSources.UnbindSupplier,
                            AbnormalType = AbnormalOrderTypes.UnBindSupplier,
                            LogicOrderId = logicOrder.LogicOrderId,
                            PlatformOrderId = logicOrder.PlatformOrderId,
                            PathFlowCode = logicOrder.PathFlowCode,
                            PlatformType = logicOrder.PlatformType,
                            AbnormalTime = DateTime.Now,
                            PathFlowNodes = pathFlowNodes?.Select(m => new OrderAbnormalPathFlowNode
                            {
                                UniqueKey = m.UniqueKey,
                                PathFlowCode = m.PathFlowCode,
                                PathFlowNodeCode = m.PathFlowNodeCode,
                                FxUserId = m.FxUserId,
                                UpFxUserId = m.UpFxUserId,
                                DownFxUserId = m.DownFxUserId,
                                Status = m.Status,
                                CreateTime = DateTime.Now
                            }).ToList()
                        });
                    }
                });

                #endregion

                //查询订单标签/只查询逻辑单对应的标签
                logicOrderService.GetOrderTags(false, orders, CustomerConfig.ExceptionOrderManageTagNames);

                WriteSqlToLog(
                    $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】1、绑定厂家-【{pCode}】待发货订单，来源：{(waitSendOrders != null ? "订单更新" : "绑定厂家")}，待发货订单【{orders.Count}】：{orders.GroupBy(x => x.PathFlowCode).ToDictionary(x => x.Key, x => x.Select(y => new { y.LogicOrderId })).ToJson()}",
                    null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                subLog1.Detail = new { ProductCode = pCode, OrderCount = orders.Count };
                logContext.EndStep();

                #region 排除掉当前用户已经手工推的订单，以及待审核订单 （商家厂家解除合作关系，手工单也需要退回商家）
                //是否解绑厂家操作
                var isReBindProductOrder = asyncTask != null && asyncTask.Flag == "ReBindProductOrder";
                if (isReBindProductOrder == false && isUnbind == false && orders.Any())
                {
                    var checkOrder = new TemplateCheckOrder(new AssignLogicOrderFactory(),
                        new SplistLogicOrderFactory(), new CheckOrderFactory());
                    var logicOrderIds = orders.Select(x => x.LogicOrderId).Distinct().ToList();
                    var allExceptIds = new List<string>();
                    var manualIds = checkOrder.GetWaitCheckOrders(logicOrderIds, fxUserId);
                    if (manualIds != null && manualIds.Any())
                    {
                        allExceptIds.AddRange(manualIds);
                        //logicOrderIds = logicOrderIds.Where(x => manualIds.Contains(x) == false).ToList();
                    }

                    //移除该逻辑，自动待审核的单不能被排除，会有问题，应只排除手工退审的单，目前无法判断是否手工退审，先去掉该逻辑
                    //var orderCheckIds = new List<string>(); 
                    //if(logicOrderIds != null && logicOrderIds.Any())
                    //    orderCheckIds = new OrderCheckRepository().GetWaitCheckOrders(logicOrderIds,fxUserId);
                    //if (orderCheckIds != null && orderCheckIds.Any())
                    //    allExceptIds.AddRange(orderCheckIds);
                    if (allExceptIds.Any())
                    {
                        var newOrders = new List<LogicOrder>();
                        var manualLogicOrderIds = new List<string>();
                        //var manualDic = manuallist.GroupBy(x => x.LogicOrderId).ToDictionary(x => x.Key, x => x.FirstOrDefault());
                        foreach (var o in orders)
                        {
                            //OrderManualRecordModel od;
                            if (allExceptIds.Contains(o.LogicOrderId))
                                manualLogicOrderIds.Add(o.LogicOrderId);
                            else
                                newOrders.Add(o);
                        }

                        if (manualLogicOrderIds.Any())
                        {
                            WriteSqlToLog(
                                $"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-1.1【{pCode}】手工单过滤：{string.Join(",", manualLogicOrderIds)}",
                                null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                            orders = newOrders;
                        }
                    }
                }
                #endregion

                if (orders.Any())
                {
                    #region 逻辑单写入跟踪日志

                    var chunksByLog1 = orders.ChunkList(500);
                    chunksByLog1.ForEach(chunk =>
                    {
                        var relatedLogicOrderIds = new List<string>();
                        relatedLogicOrderIds.AddRange(chunk.Select(a => a.LogicOrderId).Distinct());
                        var logModel = new LogicOrderInsertLogModel
                        {
                            BatchNo = $"{batchNo}-{pCode}",
                            CloudPlatformType = CustomerConfig.CloudPlatformType,
                            CurFxUserId = SiteContext.Current.CurrentFxUserId,
                            CollectionData = chunk.Select(o => new SimpleLogicOrderModel
                            {
                                Id = o.Id,
                                ShopId = o.ShopId,
                                FxUserId = o.FxUserId,
                                LogicOrderId = o.LogicOrderId,
                                PathFlowCode = o.PathFlowCode,
                                OldPathFlowCode = o.OldPathFlowCode,
                                PlatformOrderId = o.PlatformOrderId,
                                ErpState = o.ErpState,
                                PlatformType = o.PlatformType,
                                MergeredOrderId = o.MergeredOrderId,
                                MergeredType = o.MergeredType,
                                ChildOrderId = o.ChildOrderId,
                                ApprovalStatus = o.ApprovalStatus,
                                PrintState = o.PrintState,
                                Items = o.LogicOrderItems
                            }).ToList().ToJson(true),
                            RelatedLogicOrderId = string.Join(",", relatedLogicOrderIds),
                            OperationType = EventLogicOrderInsertOperationType.ChangeSupplierStep1.ToString(),
                            MethodName = "ProductFxService.SaveBindSupplierChanges",
                            Ext1 = newPathFlows.ToJson(),
                            StackTrace = Environment.StackTrace
                        };
                        LogicOrderInsertLogService.Instance.WriteLog(logModel);
                    });
                    #endregion

                    #region 订单匹配厂家前处理
                    var subLog2 = new LogForOperator { OperatorType = "订单匹配新路径" };
                    logContext.StartStep(subLog2);

                    //提前解析省市区Model
                    newPathFlows.ForEach(item =>
                    {
                        foreach (var kv in item.PathFlowReferences)
                        {
                            kv.Value.ReferenceConfigs?.ForEach(config =>
                            {
                                if (config.ConfigType != 1)
                                {
                                    return;
                                }
                                config.ProvinceInfos = config.Config.ToList<ThreeAreaFisrtModel>();
                            });
                        }
                    });

                    #endregion

                    #region 获取需要等待更新信息 *********** 重要逻辑 ***********

                    var tuple2 = GetWaitUpdateOrders(orders, newPathFlows);
                    mergerOrderLogicIds = tuple2.Item1;
                    updateModels = tuple2.Item2;
                    var orderChangeDic = tuple2.Item3;

                    //subLog2.Remark = newPathFlows.ToJson();
                    subLog2.Detail = orderChangeDic;
                    logContext.EndStep();

                    #endregion

                    #region 逻辑单写入跟踪日志

                    var chunksByLog2 = updateModels.ChunkList(500);
                    chunksByLog2.ForEach(chunk =>
                    {
                        var logModel2 = new LogicOrderInsertLogModel
                        {
                            BatchNo = $"{batchNo}-{pCode}",
                            CloudPlatformType = CustomerConfig.CloudPlatformType,
                            CurFxUserId = SiteContext.Current.CurrentFxUserId,
                            CollectionData = chunk.ToJson(),
                            OperationType = EventLogicOrderInsertOperationType.ChangeSupplierStep2.ToString(),
                            MethodName = "ProductFxService.GetWaitUpdateOrders",
                            StackTrace = Environment.StackTrace
                        };
                        LogicOrderInsertLogService.Instance.WriteLog(logModel2);
                    });

                    #endregion
                }
            }

            #region 保存绑定供应商结果到逻辑单 *********** 重要逻辑 ***********

            if (updateModels.Any() == false)
                return;

            var subLog3 = new LogForOperator { OperatorType = "更新相关数据" };
            logContext.StartStep(subLog3);

            subLog3.Detail = new
            {
                RestoreMergerOrders = mergerOrderLogicIds,
                OriginalLogicOrder = updateModels.Where(x => x.OriginalLogicOrder != null)
                    .GroupBy(x => x.OriginalLogicOrder.PathFlowCode).Select(x => new
                    {
                        PathFlowCode = x.Key,
                        LogicOrderIds = x.Select(y => y.OriginalLogicOrder.LogicOrderId).Distinct().ToList()
                    }),
                OnlyUpdateLogicOrderPathFlow = updateModels.Where(x => x.OnlyUpdateLogicOrderPathFlow != null)
                    .GroupBy(x => x.OnlyUpdateLogicOrderPathFlow.PathFlowCode).Select(x => new
                    {
                        PathFlowCode = x.Key,
                        LogicOrderIds = x.Select(y => y.OnlyUpdateLogicOrderPathFlow.LogicOrderId).Distinct().ToList()
                    }),
                UpdateLogicOrderCaculateFields = updateModels.Where(x => x.UpdateLogicOrderCaculateFields != null)
                    .Select(x => new
                    {
                        x.UpdateLogicOrderCaculateFields.LogicOrderId,
                        x.UpdateLogicOrderCaculateFields.PathFlowCode,
                        x.UpdateLogicOrderCaculateFields.ErpState,
                        x.UpdateLogicOrderCaculateFields.ErpRefundState,
                        x.UpdateLogicOrderCaculateFields.ProductCount,
                        x.UpdateLogicOrderCaculateFields.ProductItemCount,
                        x.UpdateLogicOrderCaculateFields.ProductKindCount,
                        x.UpdateLogicOrderCaculateFields.TotalAmount,
                        x.UpdateLogicOrderCaculateFields.TotalWeight
                    }),
                InsertOrders = updateModels.Where(x => x.InsertOrders != null).SelectMany(x => x.InsertOrders)
                    .GroupBy(x => x.PathFlowCode).Select(x => new
                    { PathFlowCode = x.Key, LogicOrderIds = x.Select(y => y.LogicOrderId).Distinct().ToList() })
            };
            //辅助解绑合作关系标识
            if (asyncTask != null)
            {
                asyncTask.IsUnbind = isUnbind;
            }

            //保存绑定供应商结果到逻辑单
            SaveBindSupplierResultToLogicOrder(guidCode, batchNo, pCode, asyncTask, mergerOrderLogicIds,
                updateModels, model.BatchId);

            logContext.EndStep();

            #endregion
        }

        /// <summary>
        /// 保存绑定供应商结果到逻辑单
        /// </summary>
        /// <param name="guidCode"></param>
        /// <param name="batchNo"></param>
        /// <param name="pCode"></param>
        /// <param name="asyncTask"></param>
        /// <param name="mergerOrderLogicIds"></param>
        /// <param name="updateModels"></param>
        /// <param name="batchId"></param>
        public void SaveBindSupplierResultToLogicOrder(string guidCode, string batchNo, string pCode,
            AsyncTask asyncTask, List<string> mergerOrderLogicIds, List<LogicOrderSplitUpdateModel> updateModels, string batchId)
        {
            //当前用户
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //时效监控
            var sw = new Stopwatch();
            sw.Start();
            //逻辑单版本号处理
            var versionNo = DateTime.Now.ToStamp(isToLocal: true);
            updateModels?.ForEach(u =>
            {
                if (u.OnlyUpdateLogicOrderPathFlow != null)
                {
                    u.OnlyUpdateLogicOrderPathFlow.VersionNo = versionNo;
                }
                if (u.UpdateLogicOrderCaculateFields != null)
                {
                    u.UpdateLogicOrderCaculateFields.VersionNo = versionNo;
                }
                u.InsertOrders?.ForEach(o =>
                {
                    o.VersionNo = versionNo;
                });
            });
            //参数
            var paramJson = string.Empty;

            if (!CustomerConfig.IsCrossBorderSite)
            {
                #region 重置拆单
                try
                {
                    //触发拆单动作
                    paramJson = mergerOrderLogicIds?.ToJson();
                    WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-2、【{pCode}】拆单工作，重置订单数：{mergerOrderLogicIds?.Count}，\n参数：{paramJson}", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                new ServicesExtension.MergerFxOrderService(_connectionString).RestoreMergerOrders(mergerOrderLogicIds,
                    batchId, versionNo);

                    sw.Stop();
                    WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-2.1、拆单工作，耗时：{sw.Elapsed.TotalMilliseconds}ms", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
                }
                catch (Exception ex)
                {
                    sw.Stop();
                    WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-2.1、【{pCode}】拆单工作，重置订单数：{mergerOrderLogicIds?.Count}，耗时：{sw.Elapsed.TotalMilliseconds}ms，\n参数：{paramJson} \n异常消息：{ex}", null, $"BindSupplier-{fxUserId}-Error.txt", "BindSupplier");
                    throw;
                }
                #endregion
            }

            #region 更新订单
            try
            {
                sw.Restart();

                var ssubLog2 = new LogForOperator { OperatorType = "更新订单" };

                paramJson = new
                {
                    OriginalLogicOrder = updateModels.Where(x => x.OriginalLogicOrder != null)
                        .GroupBy(x => x.OriginalLogicOrder.PathFlowCode).Select(x => new
                        {
                            PathFlowCode = x.Key,
                            LogicOrderIds = x.Select(y => y.OriginalLogicOrder.LogicOrderId).Distinct().ToList()
                        }),
                    OnlyUpdateLogicOrderPathFlow = updateModels.Where(x => x.OnlyUpdateLogicOrderPathFlow != null)
                        .GroupBy(x => x.OnlyUpdateLogicOrderPathFlow.PathFlowCode).Select(x =>
                            new
                            {
                                PathFlowCode = x.Key,
                                LogicOrderIds = x.Select(y => y.OnlyUpdateLogicOrderPathFlow.LogicOrderId).Distinct()
                                    .ToList()
                            }),
                    UpdateLogicOrderCaculateFields = updateModels.Where(x => x.UpdateLogicOrderCaculateFields != null)
                        .Select(x => new
                        {
                            x.UpdateLogicOrderCaculateFields.LogicOrderId,
                            x.UpdateLogicOrderCaculateFields.PathFlowCode,
                            x.UpdateLogicOrderCaculateFields.ErpState,
                            x.UpdateLogicOrderCaculateFields.ErpRefundState,
                            x.UpdateLogicOrderCaculateFields.ProductCount,
                            x.UpdateLogicOrderCaculateFields.ProductItemCount,
                            x.UpdateLogicOrderCaculateFields.ProductKindCount,
                            x.UpdateLogicOrderCaculateFields.TotalAmount,
                            x.UpdateLogicOrderCaculateFields.TotalWeight
                        }),
                    InsertOrders = updateModels.Where(x => x.InsertOrders != null).SelectMany(x => x.InsertOrders)
                        .GroupBy(x => x.PathFlowCode).Select(x => new
                        { PathFlowCode = x.Key, LogicOrderIds = x.Select(y => y.LogicOrderId).Distinct().ToList() })
                }.ToJson();

                WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-3、【{pCode}】更新订单，参数：{paramJson}", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                //使用事务更新
                var logicOrderService = new LogicOrderService(_connectionString);
                logicOrderService.ExecutUpdateModel(updateModels, ssubLog2, asyncTask, $"{batchNo}-{pCode}", batchId);

                ssubLog2.End();
                sw.Stop();
                WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-3.1、【{pCode}】更新订单，耗时：{sw.Elapsed.TotalMilliseconds}ms", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");
            }
            catch (Exception ex)
            {
                sw.Stop();
                WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-3.1、【{pCode}】更新订单，耗时：{sw.Elapsed.TotalMilliseconds}ms，\n参数：{paramJson} \n异常消息：{ex}", null, $"BindSupplier-{fxUserId}-Error.txt", "BindSupplier");
                throw;
            }
            #endregion

            #region 修改的订单也需要重新走一手动审核流程

            sw.Restart();
            var ssubLog3 = new LogForOperator { OperatorType = "手动审核流程" };

            try
            {
                var objserve = new UpdateCheckOrderProduct();
                var logicorders = new List<LogicOrder>();
                updateModels.ForEach(x =>
                {
                    if (x.InsertOrders != null && x.InsertOrders.Any())
                        logicorders.AddRange(x.InsertOrders);
                    if (x.OnlyUpdateLogicOrderPathFlow != null)
                        logicorders.Add(x.OnlyUpdateLogicOrderPathFlow);
                });
                objserve.Process(fxUserId, logicorders);

                sw.Stop();
                WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-4、【{pCode}】手工单审核流程，耗时：{sw.Elapsed.TotalMilliseconds}ms", null, $"BindSupplier-{fxUserId}.txt", "BindSupplier");

                ssubLog3.End();
            }
            catch (Exception ex)
            {
                sw.Stop();
                WriteSqlToLog($"{guidCode}==>用户【{fxUserId}-{asyncTask?.Flag ?? ""}】绑定厂家-4.1、【{pCode}】手工单审核流程，耗时：{sw.Elapsed.TotalMilliseconds}ms \n异常消息：{ex}", null, $"BindSupplier-{fxUserId}-Error.txt", "BindSupplier");
                throw;
            }
            #endregion


            #region 处理1688关联的逻辑单的发货方式
            var logicOrders = updateModels.Where(x => x.InsertOrders != null).SelectMany(x => x.InsertOrders).ToList();
            try
            {
                new PurchaseOrderDeliveryModeService().ProcessLogicOrderDeliveryMode(logicOrders);
            }
            catch (Exception ex)
            {
                CommUtls.WriteToLog($"SaveBindSupplierResultToLogicOrder：处理1688的发货方式时异常，logicOrders.PlatformOrderId={logicOrders?.Select(a => a.PlatformOrderId).ToJson()}，异常信息：{ex.ToJson()}", $"1688-ProcessLogicOrderDeliveryMode.txt", "ProcessLogicOrderDeliveryMode");
            }
            #endregion
        }

        public Tuple<List<string>, List<LogicOrderSplitUpdateModel>, Dictionary<string, List<int>>> GetWaitUpdateOrders(List<LogicOrder> orders, List<PathFlow> toMathPathFlows)
        {
            var orderChangeDic = new Dictionary<string, List<int>>();
            var updateModels = new List<LogicOrderSplitUpdateModel>();
            var logicOrderService = new LogicOrderService(_connectionString);

            // 订单匹配新厂家
            foreach (var order in orders)
            {
                //判断是否需要拆单
                var tempOis = new List<LogicOrderItemChangeModel>();
                foreach (var oi in order.LogicOrderItems)
                {
                    //TODO:此处的PathFlowCode应该是商品对应的
                    var newPathFlowCode = string.Empty;
                    var temp = new LogicOrderItemChangeModel { LogicOrderItem = oi, PathFlowCode = order.PathFlowCode };
                    if (toMathPathFlows != null && toMathPathFlows.Any())
                    {
                        // 根据规则匹配订单厂家
                        newPathFlowCode = IsMatchConfig(order, oi, toMathPathFlows);
                        if (newPathFlowCode.IsNotNullOrEmpty() && temp.PathFlowCode != newPathFlowCode)
                        {
                            var key = $"{temp.PathFlowCode}=>{newPathFlowCode}";
                            if (orderChangeDic.ContainsKey(key) == false)
                                orderChangeDic.Add(key, new List<int> { oi.Id });
                            else if (orderChangeDic[key].Contains(oi.Id) == false)
                                orderChangeDic[key].Add(oi.Id);
                            temp.PathFlowCode = newPathFlowCode;
                        }
                    }
                    tempOis.Add(temp);
                }

                var updateModel = logicOrderService.SplitOrder(order, tempOis);
                if (updateModel != null)
                    updateModels.Add(updateModel);
            }
            //合并订单全部还原处理
            var mergeredOrderLogicIds = orders.Where(x => x.IsChildOrder)
                .Where(x => !string.IsNullOrEmpty(x.MergeredOrderId)).Select(x => x.MergeredOrderId).ToList();
            return new Tuple<List<string>, List<LogicOrderSplitUpdateModel>, Dictionary<string, List<int>>>(mergeredOrderLogicIds, updateModels, orderChangeDic);
        }

        #region 匹配厂家获取PathFlowCode

        public string IsMatchConfig(LogicOrder o, LogicOrderItem oi, List<PathFlow> pathFlows)
        {
            var model = new GetPathflowCodeModel() { PlatformOrderId = o.PlatformOrderId, ToProvince = o.ToProvince, ToCity = o.ToCity, CreateTime = o.CreateTime, ProductCode = oi.ProductCode, SkuCode = oi.SkuCode };
            return IsMatchConfigNew(model, pathFlows);

            #region 旧逻辑
            //var newPathFlowCode = string.Empty;
            //if (pathFlows == null || pathFlows.Any() == false)
            //    return newPathFlowCode;

            //var maxLen = pathFlows.Max(x => x.PathFlowNodes.Count);
            //// 补全路径排序长度
            //pathFlows.ForEach(x =>
            //{
            //    var pathIndex = x.PathConfigIndex;
            //    if (pathIndex.Length < maxLen)
            //    {
            //        var str = "";
            //        var count = maxLen - pathIndex.Length;
            //        for (int i = 0; i < count; i++)
            //        {
            //            str += "9";
            //        }
            //        x.PathConfigIndex = pathIndex + str;
            //    }
            //});

            //var prePathFlowCode = string.Empty;
            //var skuRefPaths = pathFlows.Where(x => x.PathFlowReferences.Values.Any(y => y.PathFlowRefCode == oi.SkuCode && y.PathFlowRefType == "Sku")).ToList();
            //skuRefPaths = skuRefPaths.OrderBy(x => x.PathConfigIndex).ToList();

            ////1. 先匹配Sku规则
            //foreach (var skuPathFlow in skuRefPaths)
            //{
            //    if (skuPathFlow.PathFlowReferences.ContainsKey(oi.SkuCode) == false)
            //        continue;

            //    var configs = skuPathFlow.PathFlowReferences[oi.SkuCode].ReferenceConfigs;
            //    var sortNodes = skuPathFlow.SortedNodes;
            //    foreach (var node in sortNodes)
            //    {
            //        if (!skuPathFlow.IsSelf && node.DownFxUserId == 0)
            //            continue;

            //        var config = configs.FirstOrDefault(x => x.PathFlowRefCode == oi.SkuCode && x.FxUserId == node.FxUserId && x.PathFlowCode == node.PathFlowCode);
            //        if (config != null)
            //        {
            //            if (config.ConfigType == 1)
            //            {
            //                //省市区规则匹配
            //                var isMatched = IsAddressMatched(o, config);
            //                if (isMatched)
            //                    newPathFlowCode = config.PathFlowCode;
            //                else
            //                {
            //                    // 路径上一个节点未匹配，则继续匹配下一条
            //                    newPathFlowCode = string.Empty;
            //                    break;
            //                }
            //            }
            //            else if (config.ConfigType == 2)
            //            {
            //                //时间规则匹配
            //                var isMatched = IsTimeMatched(o, config);
            //                if (isMatched)
            //                    newPathFlowCode = config.PathFlowCode;
            //                else
            //                {
            //                    newPathFlowCode = string.Empty;
            //                    break;
            //                }
            //            }
            //            else if (config.ConfigType == 0)
            //            {
            //                //默认厂家
            //                newPathFlowCode = config.PathFlowCode;
            //            }
            //        }
            //        else
            //        {
            //            //默认厂家（兼容旧数据）
            //            newPathFlowCode = node.PathFlowCode;
            //        }
            //    }

            //    if (newPathFlowCode.IsNotNullOrEmpty())
            //        return newPathFlowCode;
            //}

            //if (newPathFlowCode.IsNotNullOrEmpty())
            //    return newPathFlowCode;

            //// 2. Sku无满足条件匹配，再查商品规则
            //var productRefPaths = pathFlows.Where(x => x.PathFlowReferences.Values.Any(y => y.ProductCode == oi.ProductCode && y.PathFlowRefType == "Product")).OrderByDescending(x => x.Id).ToList();
            //productRefPaths = productRefPaths.OrderBy(x => x.PathConfigIndex).ToList(); // 地址或时间推单优先

            //foreach (var productPathFlow in productRefPaths)
            //{
            //    if (productPathFlow.PathFlowReferences.ContainsKey(oi.ProductCode) == false)
            //        continue;

            //    var configs = productPathFlow.PathFlowReferences[oi.ProductCode].ReferenceConfigs.OrderByDescending(x => x.ConfigType).ToList();
            //    var sortNodes = productPathFlow.SortedNodes;

            //    foreach (var node in sortNodes)
            //    {
            //        if (!productPathFlow.IsSelf && node.DownFxUserId == 0)
            //            continue;

            //        var config = configs.FirstOrDefault(x => x.PathFlowRefCode == oi.ProductCode && x.FxUserId == node.FxUserId && x.PathFlowCode == node.PathFlowCode);
            //        if (config != null)
            //        {
            //            // 配置厂家
            //            if (config.ConfigType == 1)
            //            {
            //                //省市区规则匹配
            //                var isMatched = IsAddressMatched(o, config);
            //                if (isMatched)
            //                    newPathFlowCode = config.PathFlowCode;
            //                else
            //                    break;
            //            }
            //            else if (config.ConfigType == 2)
            //            {
            //                //时间规则匹配
            //                var isMatched = IsTimeMatched(o, config);
            //                if (isMatched)
            //                    newPathFlowCode = config.PathFlowCode;
            //                else
            //                    break;
            //            }
            //            else if (config.ConfigType == 0)
            //            {
            //                //默认厂家
            //                newPathFlowCode = config.PathFlowCode;
            //            }
            //        }
            //        else
            //        {
            //            //默认厂家（兼容旧数据）
            //            newPathFlowCode = node.PathFlowCode;
            //        }
            //    }

            //    if (newPathFlowCode.IsNotNullOrEmpty())
            //        return newPathFlowCode;
            //}
            //return newPathFlowCode;
            #endregion
        }

        public string IsMatchConfigNew(GetPathflowCodeModel model, List<PathFlow> pathFlows)
        {
            var newPathFlowCode = string.Empty;
            if (pathFlows == null || pathFlows.Any() == false)
                return newPathFlowCode;

            var curFxUserId = SiteContext.GetCurrentFxUserId();
            var guids = Guid.NewGuid().ToString();
            // 1个商品或者1个Sku最多有2条路径：条件推送厂家和默认厂家
            //1. 先匹配Sku规则
            var kvPathFlows = pathFlows.SelectMany(x => x.PathFlowReferences);
            var skuRefPaths = pathFlows.Where(x => x.PathFlowReferences.Values.Any(y => y.PathFlowRefCode == model.SkuCode && y.PathFlowRefType == "Sku")).ToList();
            newPathFlowCode = GetOrderPathFlowCode(model, true, guids, kvPathFlows, skuRefPaths);


            // 2. Sku无满足条件匹配，再查商品规则
            if (newPathFlowCode.IsNullOrEmpty())
            {
                var productRefPaths = pathFlows.Where(x => x.PathFlowReferences.Values.Any(y => y.ProductCode == model.ProductCode && y.PathFlowRefType == "Product")).OrderByDescending(x => x.Id).ToList();
                newPathFlowCode = GetOrderPathFlowCode(model, false, guids, kvPathFlows, productRefPaths);
            }

            // 多商品订单没有查出路径信息，不能判断PathflowCode为空
            //if (newPathFlowCode.IsNullOrEmpty())
            //{
            //    Log.WriteError($"{guids} IsMatchConfigNew ====> {model.ToJson()} 没有找到相应的路径信息\npathFlows:{pathFlows.ToJson()}", $"BindSupplier-{curFxUserId}.txt");
            //    throw new LogicException($"订单[{model.PlatformOrderId}]没有找到相应的路径信息");
            //}
            return newPathFlowCode;
        }

        private string GetOrderPathFlowCode(GetPathflowCodeModel o, bool isSku, string guids,
            IEnumerable<KeyValuePair<string, PathFlowReference>> kvPathFlows, List<PathFlow> refPathFlows)
        {
            var curFxUserId = SiteContext.GetCurrentFxUserId();
            // 兼容存在脏数据情况，只取经过当前账号的路径
            refPathFlows = refPathFlows.Where(x => x.SortedNodes.Any(y => y.FxUserId == curFxUserId)).ToList();
            var newPathFlowCode = string.Empty;
            if (refPathFlows.Any() == false)
                return newPathFlowCode;

            var code = isSku ? o.SkuCode : o.ProductCode;
            var refType = isSku ? "Sku" : "Product";
            var sortNodes = refPathFlows
                .Where(x => x.PathFlowReferences.Values.Any(y =>
                    y.PathFlowRefCode == code && y.PathFlowRefType == refType)).SelectMany(x => x.SortedNodes).ToList();
            var configs = kvPathFlows
                .Where(x => x.Key == code && x.Value != null && x.Value.ReferenceConfigs.Any() &&
                            x.Value.PathFlowRefType == refType).SelectMany(x => x.Value.ReferenceConfigs).ToList();

            Log.Debug(
                () =>
                    $"1、{guids} ==> [{refType}]curFxUserId={curFxUserId}， sortNodes={sortNodes.ToJson()}\nconfigs={configs.ToJson()}\nrefPathFlows={refPathFlows.ToJson()}",
                $"BindSupplier-{curFxUserId}.txt");
            // 自营
            var rootNodes = refPathFlows.Select(x => x.RootNode).ToList();
            if (rootNodes.All(x => x.DownFxUserId == 0))
                return sortNodes.FirstOrDefault().PathFlowCode;

            var rootFxUserId = rootNodes.FirstOrDefault().FxUserId;
            var downNodes = sortNodes.Where(x => rootFxUserId == x.FxUserId && x.DownFxUserId > 0).ToList();
            Log.Debug(() => $"2、{guids} ==> [{refType}] rootFxUserId={rootFxUserId}， downNodes={downNodes.ToJson()}",
                $"BindSupplier-{curFxUserId}.txt");

            while (downNodes?.Any() == true)
            {
                // 最多只有2类厂家：默认或者配置条件厂家
                var fxUserId = downNodes.FirstOrDefault().FxUserId; // 上游商家Id固定的
                var downFxUserIds = downNodes.Select(x => x.DownFxUserId).Distinct().ToList();
                if (downFxUserIds.Count == 1)
                {
                    // 只有默认厂家
                    newPathFlowCode =
                        downNodes.FirstOrDefault().PathFlowCode; // 先临时取当前节点下任意一个节点PathFlowCode，如果有下游厂家会重新取
                    var defaultDownFxUserId = downFxUserIds.FirstOrDefault();
                    downNodes = sortNodes.Where(x =>
                        defaultDownFxUserId == x.FxUserId && x.UpFxUserId == fxUserId && x.DownFxUserId > 0).ToList();
                    Log.Debug(
                        () =>
                            $"3、{guids} ==>[{refType}] fxUserId={fxUserId}， downFxUserIds={downFxUserIds.ToJson()}, downNodes={downNodes.ToJson()}",
                        $"BindSupplier-{curFxUserId}.txt");
                }
                else if (downFxUserIds.Count > 1)
                {
                    // 默认厂家+配置厂家：优先判断配置条件，不满足则取默认厂家
                    var setConfigs = configs.Where(x =>
                        x.FxUserId == fxUserId && downFxUserIds.Contains(x.DownFxUserId) && x.ConfigType > 0).ToList();
                    if (setConfigs == null || setConfigs.Any() == false)
                    {
                        //日志
                        var log = new SplitLogicOrderPathFlowExLogModel
                        {
                            FxUserId = fxUserId,
                            RefCode = code,
                            RefType = refType,
                            DownFxUserIds = downFxUserIds.ToJson(),
                            PathFlowCodes = downNodes.Select(m => m.PathFlowCode).Distinct().ToJson()
                        };
                        
                        //写错误日志
                        Log.WriteError(
                            $"3.1、{guids}未找到配置条件的厂家 ==>[{refType}] fxUserId={fxUserId}，downFxUserIds={log.DownFxUserIds}, downNodes={downNodes.ToJson()}",
                            $"BindSupplier-{curFxUserId}.txt");
                        //写日志服务
                        SplitLogicOrderPathFlowExLogDataTrackingService.Instance.WriteLog(log);
                        throw new LogicException($"未找到配置条件的厂家");
                    }

                    Log.Debug(
                        () =>
                            $"3.2、{guids} ==>[{refType}] fxUserId={fxUserId}，downFxUserIds={downFxUserIds.ToJson()}, downNodes={downNodes.ToJson()}，setConfigs={setConfigs.ToJson()}",
                        $"BindSupplier-{curFxUserId}.txt");

                    // 同类型配置的多个厂家条件应该是互斥，只要其中一个厂家满足条件就可终止循环
                    var isMatched = false;
                    foreach (var config in setConfigs)
                    {
                        var configDownFxUserId = config.DownFxUserId;
                        if (config.ConfigType == 1)
                        {
                            //省市区规则匹配
                            isMatched = IsAddressMatched(o, config);
                            //Log.Debug($"{guids} 2、IsAddressMatched({refType}) ==> {o.ToJson()} \nisMatched={isMatched},config = {config.ToJson()}", $"BindSupplier-{curFxUserId}.txt");
                            if (isMatched)
                            {
                                newPathFlowCode = config.PathFlowCode;
                                downNodes = sortNodes.Where(x =>
                                        fxUserId == x.UpFxUserId && configDownFxUserId == x.FxUserId &&
                                        x.DownFxUserId > 0)
                                    .ToList();
                                break;
                            }
                        }
                        else if (config.ConfigType == 2)
                        {
                            //时间规则匹配
                            isMatched = IsTimeMatched(o, config);
                            //Log.Debug($"{guids} 2、IsTimeMatched({refType}) ==> {o.ToJson()} \nisMatched={isMatched},config = {config.ToJson()}", $"BindSupplier-{curFxUserId}.txt");
                            if (isMatched)
                            {
                                newPathFlowCode = config.PathFlowCode;
                                downNodes = sortNodes.Where(x =>
                                        fxUserId == x.UpFxUserId && configDownFxUserId == x.FxUserId &&
                                        x.DownFxUserId > 0)
                                    .ToList();
                                break;
                            }
                        }
                    }

                    // 配置条件不满足则取默认厂家
                    if (isMatched == false)
                    {
                        //var defaultDownFxUserId = downFxUserIds.Where(x => x != configDownFxUserId).FirstOrDefault();
                        //newPathFlowCode = downNodes.FirstOrDefault(x => x.FxUserId == fxUserId && x.DownFxUserId == defaultDownFxUserId).PathFlowCode;

                        var defaultConfig = configs.OrderByDescending(x => x.Id).FirstOrDefault(x => x.FxUserId == fxUserId && downFxUserIds.Contains(x.DownFxUserId) && x.ConfigType == 0);
                        var defaultDownFxUserId = defaultConfig.DownFxUserId;
                        newPathFlowCode = defaultConfig?.PathFlowCode;
                        Log.Debug(
                            () =>
                                $"3.3、{guids} 使用默认厂家 ==> fxUserId={fxUserId}，{o.ToJson()} \ndefaultDownFxUserId={defaultDownFxUserId},newPathFlowCode = {newPathFlowCode}",
                            $"BindSupplier-{curFxUserId}.txt");

                        downNodes = sortNodes.Where(x =>
                                fxUserId == x.UpFxUserId && defaultDownFxUserId == x.FxUserId && x.DownFxUserId > 0)
                            .ToList();
                    }
                }
                else
                {
                    Log.WriteError(
                        $"3.4、{guids} ==>[{refType}] 没有找到厂家信息， fxUserId={fxUserId}，downNodes={downNodes?.ToJson() ?? "null"}",
                        $"BindSupplier-{curFxUserId}.txt");
                    throw new LogicException($"没有找到厂家信息"); // 没有厂家，前面应该当成自营处理
                }
            }

            return newPathFlowCode;
        }

        private bool IsAddressMatched(GetPathflowCodeModel o, PathFlowReferenceConfig config)
        {
            var isMatched = false;
            config.ProvinceInfos?.ForEach(pc =>
            {
                var province = pc.ProvinceInfo;
                if (province != null && o.ToProvince.ToString2().Contains(province.Name.ToString2()))
                {
                    if (province.isCheckAll)
                    {
                        isMatched = true;
                        return;
                    }
                    else
                    {
                        var cityLst = pc.CityLst;
                        if (cityLst != null && cityLst.Any())
                        {
                            isMatched = cityLst.Any(x => o.ToCity.ToString2().Contains(x.CityInfo.Name.ToString2()));
                            if (isMatched)
                                return;
                        }
                    }
                }
            });
            return isMatched;
        }

        private bool IsTimeMatched(GetPathflowCodeModel o, PathFlowReferenceConfig config)
        {
            var isMatched = false;
            var timeArr = config.Config.SplitToList(",");
            if (timeArr.Count == 2)
            {
                var startTime = (o.CreateTime.Value.ToString("yyyy-MM-dd") + " " + timeArr[0]).toDateTime();
                var endTime = (o.CreateTime.Value.ToString("yyyy-MM-dd") + " " + timeArr[1]).toDateTime();
                isMatched = o.CreateTime.Value >= startTime && o.CreateTime.Value <= endTime;
            }
            return isMatched;
        }

        #endregion

        public bool CheckSupplierHasProduct(int supplierId, int fxUserId)
        {
            return _repository.CheckSupplierHasProduct(supplierId, fxUserId);
        }

        public List<string> GetSupplierProducts(int supplierId, int fxUserId)
        {
            return _repository.GetSupplierProducts(supplierId, fxUserId);
        }

        /// <summary>
        /// 商家店铺产品消息处理
        /// </summary>
        /// <param name="order"></param>
        public void FxProductMessageProcess(List<Product> products)
        {
            var sids = products.Select(f => f.ShopId); //汇总所有的订单店铺id
            //1.查询出 商家，平台店铺，及系统店铺
            var fxUser_PlatformShop_SystemShop = (new UserFxService()).GetUserFxAndShopsByShopIds(null, sids);
            if (fxUser_PlatformShop_SystemShop == null || fxUser_PlatformShop_SystemShop.Any() == false) return;
            //按店铺分组
            products.GroupBy(f => f.ShopId).ToList().ForEach(g =>
            {
                try
                {
                    var sid = g.Key;
                    //店鋪所属的商家及系统店铺
                    var fxUserSystemShop = fxUser_PlatformShop_SystemShop.FirstOrDefault(f => f.Item2?.Id == sid);
                    if (fxUserSystemShop == null) return;
                    //实例化sitecontext
                    var sc = new SiteContext(fxUserSystemShop.Item1, new SiteContextConfig() { NeedRelationShops = false, NeedShopExpireTime = false });
                    //商品推入分销用户的库
                    var agentProductService = new ProductFxService(_connectionString);
                    var shop = fxUserSystemShop.Item2;
                    var fxProducts = agentProductService.TransferModelToProductFx(g.ToList(), fxUserSystemShop.Item1.Id, shop.PlatformType);
                    agentProductService.BulkMergerFromMessage(fxProducts, shop.Id);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"处理订单消息，商品推送到商家报错：" + ex.ToString());
                    Log.WriteError($@"参数->【order.shopid】:{g.Key},
                                        【order】:{g.ToList()?.ToJson()},
                                        【fxUser_PlatformShop_SystemShop】:{fxUser_PlatformShop_SystemShop?.FirstOrDefault(f => f.Item2.Id == g.Key)?.ToJson()}");
                }
            });
        }
        public void UpdateProductStatus(string status, List<string> pcodes)
        {
            _repository.UpdateProductStatus(status, pcodes);

            var list = _repository.GetProductsByCodes(pcodes, new List<string> { "PlatformId", "ShopId", "SourceUserId" });
            #region 数据变更日志
            List<DataChangeLog> dcLogs = list?.Select(o => new DataChangeLog
            {
                DataChangeType = DataChangeTypeEnum.UPDATE_STATUS,
                TableTypeName = DataChangeTableTypeName.Product,
                SourceShopId = o.ShopId,
                SourceFxUserId = o.SourceUserId,
                RelationKey = o.ProductCode,
                ExtField1 = "ProductFx.UpdateProductStatus"
            }
            ).ToList();
            new DataChangeLogRepository().Add(dcLogs);
            #endregion
        }

        /// <summary>
        /// 结果不带Sku
        /// </summary>
        /// <param name="productCodes"></param>
        /// <param name="fields"></param>
        /// <returns></returns>
        public List<ProductFx> GetProductsByCodesNoSku(List<string> productCodes, List<string> fields = null)
        {
            return _repository.GetProductsByCodes(productCodes, fields);
        }

        public List<ProductFx> GetFxProductList(List<int> shopIds, List<string> pids, bool pidIsItemId = false, List<string> fields = null)
        {
            return _repository.GetFxProductList(shopIds, pids, pidIsItemId, fields);
        }

        public List<ProductFx> GetFxProductListBySkuIds(List<int> shopIds, List<string> skuIds, List<string> fields = null)
        {
            return _repository.GetFxProductListBySkuIds(shopIds, skuIds, fields);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="platformType"></param>
        /// <param name="productSubject"></param>
        /// <param name="productShortTitle"></param>
        /// <param name="skuName"></param>
        /// <param name="skuShortTitle"></param>
        /// <param name="skuCargoNumber"></param>
        /// <param name="skuCode"></param>
        /// <param name="total"></param>
        /// <param name="queryFlag">查询标记，supplier:以厂家身份查询，只查商家数据，不查自己的</param>
        /// <returns></returns>
        public List<ProductSkuFx> ProductSkuGetList(int pageIndex, int pageSize, string platformType, string productSubject, string productShortTitle, string skuName, string skuShortTitle, string skuCargoNumber, string skuCode, out int total, string queryFlag = "")
        {
            var fxUserId = SiteContext.Current.CurrentFxUserId;

            var myShopList = new FxUserShopService().GetShopsByFxUserId(fxUserId, false);

            var result = _repository.ProductSkuGetList(pageIndex, pageSize, fxUserId, platformType, productSubject, productShortTitle, skuName, skuShortTitle, skuCargoNumber, out total);

            if (result != null && result.Count > 0)
            {
                if (myShopList != null && myShopList.Count > 0)
                {
                    var agents = new SupplierUserService().GetAgentList(fxUserId, null, null, 1, 10000,needEncryptAccount:true);
                    var agentsList = agents?.Item2?.Select(x => new { x.UserName, x.FxUserId }).Distinct().ToDictionary(x => x.FxUserId, x => x.UserName);
                    var productSkuPtIds = result.Select(a => a.SkuId).ToList();
                    var baseProductSkuRelations = _repository.GetBaseProductSkuRelations(productSkuPtIds);

                    foreach (var item in result)
                    {
                        if (item.UpFxUserId > 0)
                        {
                            var agentRes = agentsList?.Where(t => t.Key == item.UpFxUserId).FirstOrDefault();
                            item.ShopName = agentRes?.Value;
                        }
                        else
                        {
                            item.ShopName = myShopList?.Where(w => w.ShopId == item.ShopId).Select(s => s.NickName).FirstOrDefault();
                        }

                        // 列表补充是否关联
                        var baseProductSkuRelation = baseProductSkuRelations.FirstOrDefault(a =>
                            a.ProductSkuCode.Trim() == item.SkuCode &&
                            a.ProductSkuPtId.Trim() == item.SkuId &&
                            a.ProductShopId == item.ShopId &&
                            a.FxUserId == fxUserId);
                        item.IsBindbaseProductSku = baseProductSkuRelation != null;

                    }
                }

                #region 上下游商品信息展示
                var pathflowService = new PathFlowService(_connectionString);
                var pCodes = result.Select(x => x.ProductCode).Distinct().ToList();
                var fields = "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
                var pathFlows = pathflowService.GetPathFlows(pCodes, 0, fields);

                var logicOrderRepository = new LogicOrderRepository(_connectionString);
                var nodes = pathFlows.SelectMany(x => x.PathFlowNodes).ToList();
                var pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(nodes);
                var commonSettingRepository = new CommonSettingRepository();
                #endregion

                foreach (var item in result)
                {
                    var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(item.SkuCode)).ToList();
                    if (productPathFlows == null || productPathFlows.Count == 0)
                    {
                        productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(item.ProductCode)).ToList();
                    }

                    // 商品标题是否可见
                    ConvertShowProductTitle(fxUserId, productPathFlows, pathFlowNodeDic, null, item, commonSettingRepository);

                    // 商品图片是否可见
                    ConvertShowProductImg(fxUserId, productPathFlows, pathFlowNodeDic, null, item, commonSettingRepository);

                }
                
            }

            return result;
        }

        /// <summary>
        /// 基础商品绑定查询
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Tuple<int, List<ProductSkuFx>> GetProductSkuBindList(BaseProductSkuBindSearchModel model)
        {
            var result = _repository.GetProductSkuBindList(model);
            var fxUserId = model.FxUserId.Value;
            var list = result.Item2;
            var count = result.Item1;
            var newList = new List<ProductSkuFx>();
            var newCount = 0;
            var from = "all";

            // 分页问题
            if (list.Count > 0)
            {
                //Log.WriteLine($"GetProductSkuBindList 前1数据：{list.Count}", "GetProductSkuBindList.txt");
                // 列表补充是否关联  // 列表补充是否使用库存 // 列表补充简称重量
                var productSkuPtIds = list.Select(a => a.SkuId).ToList();
                var skuCodes = list.Select(a => a.SkuCode).ToList();
                var productCodes = list.Select(a => a.ProductCode).Distinct().ToList();
                //var productSkuDic = list.ToDictionary(x => x.ProductCode, x => x.SkuCode);
                var baseProductSkuRelations = _repository.GetBaseProductSkuRelations(productSkuPtIds);

                // 列表补充供货类型
                var agents = new SupplierUserService().GetAgentList(fxUserId,needEncryptAccount:true);
                var agentsList = agents?.GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.AgentMobileAndRemark ?? "");

                var suppliers = new SupplierUserService().GetSupplierList(fxUserId,needEncryptAccount:true);
                var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");
                var myShopList = new FxUserShopService().GetShopsByFxUserId(fxUserId, false);

                // 获取Sku路径
                var pathflowService = new PathFlowService(_connectionString);
                var pCodes = list.Select(x => x.ProductCode).Distinct().ToList();
                var fields = "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
                var pathFlows = pathflowService.GetPathFlows(pCodes, 0, fields);

                // 上下游销售价是否可见
                var logicOrderRepository = new LogicOrderRepository(_connectionString);
                var nodes = pathFlows.SelectMany(x => x.PathFlowNodes).ToList();
                var pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(nodes);

                // 兼容新旧数据补上Config信息
                CompatibleOldDataToAddConfig(pathFlows);

                //店铺信息
                var agentShopList = new ShopRepository().GetShopByIds(list.Select(s => s.ShopId).Distinct().ToList());

                // 成本价格
                var costPrices = _repository.GetProductSettlementCostPrice(skuCodes, fxUserId);

                // 结算价格
                var settlement = HandleSettlementPrice(productCodes, skuCodes, list, fxUserId);
                var oppositeSettlementPrices = settlement.Item1;
                var mySettlementPrice = settlement.Item2;
                var SupplierAgentModels = settlement.Item3;

                var commonSettingRepository = new CommonSettingRepository();
                foreach (var sku in list)
                {
                    //Log.WriteLine($"GetProductSkuBindList 当前规格数据：{list.ToJson()}", "GetProductSkuBindList.txt");
                    var product = new ProductFx();

                    //// 基础商品绑定关系
                    //var baseProductSkuRelation = baseProductSkuRelations.FirstOrDefault(a => 
                    //    a.ProductSkuCode.Trim() == sku.SkuCode &&
                    //    a.ProductSkuPtId.Trim() == sku.SkuId &&
                    //    a.ProductShopId == sku.ShopId &&
                    //    a.FxUserId == fxUserId);
                    //sku.IsBindbaseProductSku = baseProductSkuRelation != null;
                    //sku.IsUseWarehouse = baseProductSkuRelation?.IsUseWarehouse;

                    // 成本价格
                    var currentCostPrice = costPrices.FirstOrDefault(a => a.ProductSkuCode == sku.SkuCode);

                    var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.ProductCode)).ToList();
                    // 商品上下游用户Id
                    var tuple = GetUpAndDownFxUserIdV2(from, true, sku.ProductCode, fxUserId, productPathFlows);

                    product.UpFxUserId = tuple.Item1;
                    product.DownFxUserId = tuple.Item2;

                    // 是否商品绑定厂家
                    product.IsCurBindProduct =
                        pathFlows.Any(x => x.PathFlowReferences.ContainsKey(sku.ProductCode) && x.PathFlowNodes.Any(y => y.FxUserId == fxUserId));

                    #region 商品商家厂家数据
                    var supplerName = "";
                    if (product.DownFxUserId > 0)
                    {
                        var supplierRes = supplierList.Where(t => t.Key == product.DownFxUserId).FirstOrDefault();
                        supplerName = supplierRes.Value;
                    }

                    var agentName = "";
                    if (product.UpFxUserId > 0)
                    {
                        var agentRes = agentsList.Where(t => t.Key == product.UpFxUserId).FirstOrDefault();
                        agentName = agentRes.Value;
                    }
                    else
                    {
                        var myshop = myShopList.Where(w => w.ShopId == sku.ShopId).FirstOrDefault();

                        if (myshop != null)
                        {
                            agentName = myshop.NickName;
                            product.IsSelfShop = true;

                            //补充没有平台类型的数据
                            if (string.IsNullOrEmpty(sku.PlatformType))
                                product.PlatformType = myshop.PlatformType;
                        }
                    }
                    product.AgentName = agentName;
                    product.SupplerName = supplerName;
                    #endregion

                    var skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.SkuCode)).ToList();
                    if (skuPathFlows == null || skuPathFlows.Any() == false)
                    {
                        //Log.WriteLine($"GetProductSkuBindList 采用商品数据", "GetProductSkuBindList.txt");
                        #region 采用商品数据
                        sku.BindFrom = "Product";
                        sku.UpFxUserId = product.UpFxUserId;
                        sku.DownFxUserId = product.DownFxUserId;
                        sku.AgentName = product.AgentName;
                        sku.SupplerName = product.SupplerName;
                        sku.PlatformType = product.PlatformType;

                        // 绑定的是商品：无绑定厂家的SKU跟商品走，绑定的是SKU：仅显示对应的SKU+自己店铺的SKU
                        if (!product.IsCurBindProduct && !product.IsSelfShop)
                        {
                            Log.WriteLine($"GetProductSkuBindList 过滤1数据 skucode:{sku.SkuCode}", "GetProductSkuBindList.txt");
                            //continue;
                        }

                        // Sku来源商品，判断商品路径上游是否有设置销售价不可见
                        var isShowSalePrice = true;
                        foreach (var flow in productPathFlows)
                        {
                            isShowSalePrice = GetIsShowSalePrice(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                            if (isShowSalePrice == false)
                                break;
                        }
                        if (isShowSalePrice == false)
                            sku.SalePrice = 0;

                        //商品标题是否可见
                        ConvertShowProductTitle(fxUserId, productPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository);

                        // 商品图片是否可见
                        ConvertShowProductImg(fxUserId, productPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository);

                        if (product.UpFxUserId > 0)
                        {
                            // 店铺名称是否可见
                            ConvertShowShopName(fxUserId, productPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository,agentShopList);
                        }

                        #endregion
                    }
                    else
                    {
                        //Log.WriteLine($"GetProductSkuBindList 采用SKU数据", "GetProductSkuBindList.txt");
                        #region 采用SKU 数据
                        // 非厂家链上的Sku不展示
                        if (skuPathFlows.Any(x => x.PathFlowNodes.Any(xx => xx.FxUserId == fxUserId)) == false)
                        {
                            Log.WriteLine($"GetProductSkuBindList 过滤2数据 skucode:{sku.SkuCode}", "GetProductSkuBindList.txt");
                            //continue;
                        }


                        sku.BindFrom = "Sku";
                        var tuple2 = GetUpAndDownFxUserIdV2(from, false, sku.SkuCode, fxUserId, skuPathFlows);
                        if (tuple2.Item3)
                        {
                            Log.WriteLine($"GetProductSkuBindList 过滤3数据 skucode:{sku.SkuCode}", "GetProductSkuBindList.txt");
                            //continue;
                        }


                        sku.UpFxUserId = tuple2.Item1;
                        sku.DownFxUserId = tuple2.Item2;

                        sku.SupplierCount =
                            skuPathFlows.SelectMany(x => x.PathFlowNodes).Where(x => x.FxUserId == fxUserId).Select(x => x.DownFxUserId).Distinct().Count();
                        // SKU厂家与商品一致，跟随商品显示
                        if (product.IsCurBindProduct && sku.DownFxUserId == product.DownFxUserId)
                        {
                            sku.UpFxUserId = product.UpFxUserId;
                            sku.DownFxUserId = product.DownFxUserId;
                            sku.AgentName = product.AgentName;
                            sku.SupplerName = product.SupplerName;
                            sku.PlatformType = product.PlatformType;
                        }
                        else
                        {
                            product.IsBindOtherSku = 2;
                            supplerName = "";
                            if (sku.DownFxUserId > 0)
                            {
                                var supplierRes = supplierList.Where(t => t.Key == sku.DownFxUserId).FirstOrDefault();
                                supplerName = supplierRes.Value;
                            }

                            agentName = "";
                            if (sku.UpFxUserId > 0)
                            {
                                var agentRes = agentsList.Where(t => t.Key == sku.UpFxUserId).FirstOrDefault();
                                agentName = agentRes.Value;

                                // 店铺名称是否可见
                                ConvertShowShopName(fxUserId, skuPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository, agentShopList);
                            }
                            else
                            {
                                var myshop = myShopList.Where(w => w.ShopId == sku.ShopId).FirstOrDefault();
                                if (myshop != null)
                                {
                                    agentName = myshop.NickName;
                                    //补充没有平台类型的数据
                                    if (string.IsNullOrEmpty(sku.PlatformType))
                                        sku.PlatformType = myshop.PlatformType;
                                    else
                                        sku.PlatformType = sku.PlatformType;
                                }
                                else
                                {
                                    agentName = product.AgentName;
                                    sku.PlatformType = product.PlatformType;
                                }
                            }
                            sku.AgentName = agentName;
                            sku.SupplerName = supplerName;
                        }

                        // Sku路径上判断上游是否有设置销售价不可见
                        var isShowSalePrice = true;
                        foreach (var flow in skuPathFlows)
                        {
                            isShowSalePrice = GetIsShowSalePrice(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                            if (isShowSalePrice == false)
                                break;
                        }
                        if (isShowSalePrice == false)
                            sku.SalePrice = 0;

                        //商品标题是否可见
                        ConvertShowProductTitle(fxUserId, skuPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository);

                        // 商品图片是否可见
                        ConvertShowProductImg(fxUserId, skuPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository);

                        #endregion
                    }
                    // SKU信息处理
                    HandleProductSkuInfos(sku, oppositeSettlementPrices, mySettlementPrice, SupplierAgentModels, agentsList, supplierList);

                    newList.Add(sku);
                }
            }
            //newCount = count - (list.Count - newList.Count);
            newCount = count;
            //Log.WriteLine($"GetProductSkuBindList 后数据：{newList.ToJson()}", "GetProductSkuBindList.txt");
            return Tuple.Create(newCount, newList);
        }

        /// <summary>
        /// 基础商品绑定查询
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Tuple<int, List<ProductSkuFx>,int> GetProductSkuBindDetailList(BaseProductSkuBindSearchModel model)
        {
            var result = _repository.GetProductSkuBindDetailList(model);
            var fxUserId = model.FxUserId.HasValue ? model.FxUserId.Value : SiteContext.GetCurrentFxUserId();
            var list = result.Item2;
            var from = "all";
            if (list.Count > 0)
            {
                var skuCodes = list.Select(a => a.SkuCode).Distinct().ToList();
                var productCodes = list.Select(a => a.ProductCode).Distinct().ToList();
                //var productSkuDic = list.ToDictionary(x => x.ProductCode, x => x.SkuCode);

                var agents = new SupplierUserService().GetAgentList(fxUserId,needEncryptAccount:true);
                var agentsList =
                    agents?.GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.AgentMobileAndRemark ?? "");
                var suppliers = new SupplierUserService().GetSupplierList(fxUserId,needEncryptAccount:true);
                var supplierList =
                    suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");
                var myShopList = new FxUserShopService().GetShopsByFxUserId(fxUserId, false);

                //商家店铺数据
                var agentShopList = new ShopRepository().GetShopByIds(list.Select(s => s.ShopId).Distinct().ToList());

                // SKU路径流
                var pathflowService = new PathFlowService(_connectionString);
                var fields = "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
                var pathFlows = pathflowService.GetPathFlows(productCodes, 0, fields);

                // 兼容新旧数据补上Config信息
                CompatibleOldDataToAddConfig(pathFlows);

                // 上下游销售价是否可见
                var logicOrderRepository = new LogicOrderRepository(_connectionString);
                var nodes = pathFlows.SelectMany(x => x.PathFlowNodes).ToList();
                var pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(nodes);

                // 成本价格
                var costPrices = _repository.GetProductSettlementCostPrice(skuCodes, fxUserId);

                var settlement = new Tuple<List<ProductSettlementPrice>, List<ProductSettlementPrice>, List<SupplierAgentModel>>
                    (
                        new List<ProductSettlementPrice>(), new List<ProductSettlementPrice>(), new List<SupplierAgentModel>()
                    );
                var defaultPrices = new List<ProductSettlementPrice>();

                // 子账号需要判断结算价权限 
                if (!SiteContext.IsSubAccount() || SiteContext.Current.PermissionTags.Contains(FxPermission.ShowSettlePrice))
                {
                    // 默认采购价格
                    defaultPrices = _repository.GetProductSettlementDefaultSupplierSettlementPrice(skuCodes, fxUserId, true);
                    // 结算价格
                    settlement = HandleSettlementPrice(productCodes, skuCodes, list, fxUserId);
                }
                
                var oppositeSettlementPrices = settlement.Item1;
                var mySettlementPrice = settlement.Item2;
                var SupplierAgentModels = settlement.Item3;
                Log.Debug($"关联明数据源：{list.ToJson()}");

                var commonSettingRepository = new CommonSettingRepository();
                foreach (var sku in list)
                {
                    if (sku.SkuCode.IsNullOrEmpty() || sku.ProductCode.IsNullOrEmpty())
                    {
                        Log.Debug($"关联明数据源商品信息未找到");
                        continue;
                    }
                    var product = new ProductFx();
                    // 列表补充是否关联
                    // 列表补充简称重量
                    sku.IsBindbaseProductSku = true;
                    sku.IsUseWarehouse = sku.IsUseWarehouse;

                    // 列表补充成本价格
                    var currentCostPrice = costPrices.Where(a => a.ProductSkuCode == sku.SkuCode).FirstOrDefault();
                    sku.CostPrice = currentCostPrice?.Price.ToString();
                    sku.CostPriceId = currentCostPrice?.Id ?? 0;

                    // 默认采购价格
                    var defaultPrice = defaultPrices.Where(a => a.ProductSkuCode == sku.SkuCode).FirstOrDefault();
                    sku.DefaultSupplierSettlementPrice = defaultPrice?.Price;
                    sku.DefaultSupplierSettlementPriceId = defaultPrice?.Id ?? 0;

                    // 商品上下游用户Id
                    var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.ProductCode)).ToList();
                    var tuple = GetUpAndDownFxUserIdV2(from, true, sku.ProductCode, fxUserId, productPathFlows);
                    product.UpFxUserId = tuple.Item1;
                    product.DownFxUserId = tuple.Item2;

                    // 商品是否绑定厂家
                    product.IsCurBindProduct =
                        pathFlows.Any(x => x.PathFlowReferences.ContainsKey(sku.ProductCode) && x.PathFlowNodes.Any(y => y.FxUserId == fxUserId));

                    #region 商品厂家、商家名
                    var supplerName = "";
                    if (product.DownFxUserId > 0)
                    {
                        var supplierRes = supplierList.Where(t => t.Key == product.DownFxUserId).FirstOrDefault();
                        supplerName = supplierRes.Value;
                    }

                    var agentName = "";
                    if (product.UpFxUserId > 0)
                    {
                        var agentRes = agentsList.Where(t => t.Key == product.UpFxUserId).FirstOrDefault();
                        agentName = agentRes.Value;

                        // 店铺名称是否可见
                        product.ShopId = sku.ShopId;
                        ConvertShowShopName(fxUserId, productPathFlows, pathFlowNodeDic, product, null, commonSettingRepository, agentShopList);
                    }
                    else
                    {
                        var myshop = myShopList.Where(w => w.ShopId == sku.ShopId).FirstOrDefault();

                        if (myshop != null)
                        {
                            agentName = myshop.NickName;
                            product.IsSelfShop = true;

                            //补充没有平台类型的数据
                            if (string.IsNullOrEmpty(sku.PlatformType))
                                product.PlatformType = myshop.PlatformType;
                        }
                    }
                    product.AgentName = agentName;
                    product.SupplerName = supplerName;
                    #endregion

                    var skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.SkuCode)).ToList();

                    if (skuPathFlows == null || skuPathFlows.Any() == false)
                    {
                        #region 采用商品数据
                        sku.BindFrom = "Product";
                        sku.UpFxUserId = product.UpFxUserId;
                        sku.DownFxUserId = product.DownFxUserId;
                        sku.AgentName = product.AgentName;
                        sku.SupplerName = product.SupplerName;
                        sku.PlatformType = product.PlatformType;
                        sku.AgentShopName = product.AgentShopName;
                        sku.AgentShopPlatformType = product.AgentShopPlatformType;

                        var isShowSalePrice = true;
                        sku.SupplierCount =
                           productPathFlows.SelectMany(x => x.PathFlowNodes).Where(x => x.FxUserId == fxUserId).Select(x => x.DownFxUserId).Distinct().Count();
                        foreach (var flow in productPathFlows)
                        {
                            isShowSalePrice = GetIsShowSalePrice(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                            if (isShowSalePrice == false)
                                break;
                        }
                        if (isShowSalePrice == false)
                            sku.SalePrice = 0;

                        //商品标题是否可见
                        ConvertShowProductTitle(fxUserId, productPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository);


                        // 商品图片是否可见
                        ConvertShowProductImg(fxUserId, productPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository);


                        #endregion
                    }

                    else
                    {
                        #region 采用SKU 数据
                        // 非厂家链上的Sku不展示
                        if (skuPathFlows.Any(x => x.PathFlowNodes.Any(xx => xx.FxUserId == fxUserId)) == false)
                            continue;

                        sku.BindFrom = "Sku";
                        var tuple2 = GetUpAndDownFxUserIdV2(from, false, sku.SkuCode, fxUserId, skuPathFlows);
                        sku.UpFxUserId = tuple2.Item1;
                        sku.DownFxUserId = tuple2.Item2;

                        sku.SupplierCount =
                            skuPathFlows.SelectMany(x => x.PathFlowNodes).Where(x => x.FxUserId == fxUserId).Select(x => x.DownFxUserId).Distinct().Count();
                        // SKU厂家与商品一致，跟随商品显示
                        if (product.IsCurBindProduct && sku.DownFxUserId == product.DownFxUserId)
                        {
                            sku.UpFxUserId = product.UpFxUserId;
                            sku.DownFxUserId = product.DownFxUserId;
                            sku.AgentName = product.AgentName;
                            sku.SupplerName = product.SupplerName;
                            sku.PlatformType = product.PlatformType;
                            sku.AgentShopName = product.AgentShopName;
                            sku.AgentShopPlatformType = product.AgentShopPlatformType;
                        }
                        else
                        {
                            product.IsBindOtherSku = 2;
                            supplerName = "";
                            if (sku.DownFxUserId > 0)
                            {
                                var supplierRes = supplierList.Where(t => t.Key == sku.DownFxUserId).FirstOrDefault();
                                supplerName = supplierRes.Value;
                            }

                            agentName = "";
                            if (sku.UpFxUserId > 0)
                            {
                                var agentRes = agentsList.Where(t => t.Key == sku.UpFxUserId).FirstOrDefault();
                                agentName = agentRes.Value;

                                // 店铺名称是否可见
                                ConvertShowShopName(fxUserId, skuPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository, agentShopList);
                            }
                            else
                            {
                                var myshop = myShopList.Where(w => w.ShopId == sku.ShopId).FirstOrDefault();
                                if (myshop != null)
                                {
                                    agentName = myshop.NickName;
                                    //补充没有平台类型的数据
                                    if (string.IsNullOrEmpty(sku.PlatformType))
                                        sku.PlatformType = myshop.PlatformType;
                                    else
                                        sku.PlatformType = sku.PlatformType;
                                }
                                else
                                {
                                    agentName = product.AgentName;
                                    sku.PlatformType = product.PlatformType;
                                }
                            }
                            sku.AgentName = agentName;
                            sku.SupplerName = supplerName;
                        }

                        //判断上游是否有设置销售价不可见
                        var isShowSalePrice = true;
                        foreach (var flow in skuPathFlows)
                        {
                            isShowSalePrice = GetIsShowSalePrice(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                            if (isShowSalePrice == false)
                                break;
                        }
                        if (isShowSalePrice == false)
                            sku.SalePrice = 0;

                        //商品标题是否可见
                        ConvertShowProductTitle(fxUserId, skuPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository);

                        // 商品图片是否可见
                        ConvertShowProductImg(fxUserId, skuPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository);

                        #endregion
                    }
                    // SKU信息处理
                    HandleProductSkuInfos(sku, oppositeSettlementPrices, mySettlementPrice, SupplierAgentModels, agentsList, supplierList);
                }
            }
            return Tuple.Create(result.Item1, list,result.Item3);
        }

        /// <summary>
        /// 结算价格
        /// </summary>
        /// <param name="productCodes"></param>
        /// <param name="skuCodes"></param>
        /// <param name="productSkuDic"></param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public Tuple<List<ProductSettlementPrice>, List<ProductSettlementPrice>, List<SupplierAgentModel>> HandleSettlementPrice(
            List<string> productCodes,
            List<string> skuCodes,
            List<ProductSkuFx> productSkuDic,
            int fxUserId)
        {
            // 有的商品有上下游 但sku没上下游的 要设置sku的上下游为商品的上下游
            // 有上下游的sku和商品
            var SupplierAgentModels = _repository.GetSupplierAndAgentByAllCodes(productCodes, skuCodes, fxUserId, true);
            var resultCodes =
                new HashSet<string>(SupplierAgentModels.Select(s => s.ProductRefCode));
            var existingProductRefCodes =
                SupplierAgentModels.ToLookup(r => r.ProductRefCode, r => new { r.DownFxUserId, r.UpFxUserId });
            // 过滤出没有上下游的sku
            skuCodes.Where(scode => !resultCodes.Contains(scode)).ToList().ForEach(scode =>
            {
                var productSku = productSkuDic.FirstOrDefault(a => a.SkuCode == scode);
                var productCode = productSku?.ProductCode;
                if (productCode != null && existingProductRefCodes[productCode].Any())
                {
                    // 优先选有上下游的
                    var productResult = existingProductRefCodes[productCode]
                        .Where(e => e.DownFxUserId > 0 && e.UpFxUserId > 0)
                        .FirstOrDefault() ?? existingProductRefCodes[productCode]
                        .FirstOrDefault();
                    SupplierAgentModels.Add(new SupplierAgentModel()
                    {
                        ProductRefCode = scode,
                        DownFxUserId = productResult.DownFxUserId,
                        UpFxUserId = productResult.UpFxUserId
                    });
                }
            });
            var upFxUserId = SupplierAgentModels.Select(s => s.UpFxUserId).Where(i => i > 0).Distinct().ToList();
            var downFxUserId = SupplierAgentModels.Select(s => s.DownFxUserId).Where(i => i > 0).Distinct().ToList();
            var toFxUserIds = upFxUserId.Concat(downFxUserId).ToList();

            // 获取价格
            var settlementTuple = _financialSettlementService.GetSettlementPriceAll(skuCodes, toFxUserIds, fxUserId);
            // 对方的价格
            var oppositeSettlementPrices = settlementTuple.Item1;
            // 我方的价格
            var mySettlementPrice = settlementTuple.Item2;
            return Tuple.Create(oppositeSettlementPrices, mySettlementPrice, SupplierAgentModels);
        }


        /// <summary>
        /// 处理SKU结算价格
        /// </summary>
        /// <param name="sku"></param>
        /// <param name="oppositeSettlements"></param>
        /// <param name="mySettlements"></param>
        /// <param name="supplierAgents"></param>
        /// <param name="toAgentNames"></param>
        /// <param name="toSupplierNames"></param>
        private void HandleProductSkuInfos(ProductSkuFx sku
            , List<ProductSettlementPrice> oppositeSettlements
            , List<ProductSettlementPrice> mySettlements
            , List<SupplierAgentModel> supplierAgents = null
            , Dictionary<int, string> toAgentNames = null
            , Dictionary<int, string> toSupplierNames = null)
        {
            if (supplierAgents == null)
                supplierAgents = new List<SupplierAgentModel>();
            if (toAgentNames == null)
                toAgentNames = new Dictionary<int, string>();
            if (toSupplierNames == null)
                toSupplierNames = new Dictionary<int, string>();

            var oppositePrices = new List<ProductSettlementPrice>();
            var myPrices = new List<ProductSettlementPrice>();

            #region 对方是商家
            // 对方的结算价
            oppositePrices = oppositeSettlements.Where(se => se.ProductSkuCode.Equals(sku.SkuCode) && se.SettlementType == 1).ToList();
            // 我的结算价
            myPrices = mySettlements.Where(ms => ms.ProductSkuCode.Equals(sku.SkuCode) && ms.SettlementType == 2).ToList();

            var upPrice = oppositePrices?.FirstOrDefault();
            var myPrice = myPrices?.FirstOrDefault();
            var reverseUser = upPrice?.CreateUser ?? sku.UpFxUserId;
            // 对方结算价
            sku.ReverseFxUserId = reverseUser;
            sku.ReverseSettlementId = upPrice?.Id ?? 0;
            sku.ReverseSettlementPrice = upPrice != null ? $"{upPrice.Price}" : "未设置";
            // 我的结算价
            sku.ToReverseSettlementId = myPrice?.Id ?? 0;
            sku.ToReverseSettlementPrice = myPrice != null ? $"{myPrice.Price}" : "";
            sku.SupplierSettlementPriceModels = new List<SupplierSettlementPriceModels>();
            #endregion

            #region 对方是厂家
            var bindSuppliers =
                supplierAgents.Where(sa => sa.ProductRefCode.Equals(sku.SkuCode)).Select(sa => sa.DownFxUserId).Distinct().ToList();

            oppositePrices = oppositeSettlements.Where(se => se.ProductSkuCode.Equals(sku.SkuCode)
                                    && se.SettlementType == 2
                                    && bindSuppliers.Contains(se.CreateUser)).ToList();

            myPrices = mySettlements.Where(ms => ms.ProductSkuCode.Equals(sku.SkuCode)
                                    && ms.SettlementType == 1
                                    && bindSuppliers.Contains(ms.FxUserId)).ToList();

            // 给厂家赋值结算价 厂家可能有多个 所以用集合存
            // 对方的结算价
            oppositePrices?.ForEach(op =>
            {
                var model = sku.SupplierSettlementPriceModels?.Where(m => m.OppositeSettlementId == op.Id).FirstOrDefault();
                if (model == null)
                {
                    toSupplierNames.TryGetValue(op.CreateUser, out var name);
                    model = new SupplierSettlementPriceModels()
                    {
                        OppositeSettlementId = op.Id,
                        FxUserId = op.CreateUser,
                        SupplierName = name ?? "",
                        OppositeSettlementPrice = op.Price != 0 ? op.Price.ToString() : "未设置",
                    };
                    sku.SupplierSettlementPriceModels.Add(model);
                }
            });
            // 我的结算价
            myPrices?.ForEach(mp =>
            {
                var model = sku.SupplierSettlementPriceModels?.Where(m => m.FxUserId == mp.FxUserId).FirstOrDefault();
                if (model == null)
                {
                    toSupplierNames.TryGetValue(mp.FxUserId, out var name);
                    model = new SupplierSettlementPriceModels()
                    {
                        SettlementId = mp.Id,
                        FxUserId = mp.FxUserId,
                        SupplierName = name ?? "",
                        SettlementPrice = mp.Price.ToString(),
                    };
                    sku.SupplierSettlementPriceModels.Add(model);
                }
                else
                {
                    var index = sku.SupplierSettlementPriceModels.IndexOf(model);
                    if (index != -1)
                    {
                        sku.SupplierSettlementPriceModels[index].SettlementId = mp.Id;
                        sku.SupplierSettlementPriceModels[index].SettlementPrice = mp.Price.ToString();
                    }
                }
            });
            #endregion

            #region 没设置结算价 但是有下游厂家的sku，也要设置下游的用户id
            var setDownUserIds =
                sku.SupplierSettlementPriceModels.Select(m => m.FxUserId).Distinct().ToList();
            var noSetDownUserIds = new List<int>();
            // sku自己绑定有下游
            if (supplierAgents != null && supplierAgents.Any())
            {
                noSetDownUserIds = supplierAgents.Where(sa => sku.SkuCode.Equals(sa.ProductRefCode)
                                                 && !setDownUserIds.Contains(sa.DownFxUserId)
                                                 && sa.DownFxUserId > 0)
                                                 ?.Select(n => n.DownFxUserId).ToList();
            }
            // sku未绑定下游，使用未设置结算价的默认下游 （来自product）
            if (sku.DownFxUserId > 0 && !setDownUserIds.Any() && !noSetDownUserIds.Any())
            {
                noSetDownUserIds.Add(sku.DownFxUserId);
            }

            noSetDownUserIds = noSetDownUserIds.Distinct().ToList();

            noSetDownUserIds?.ForEach(nosetuser =>
            {
                toSupplierNames.TryGetValue(nosetuser, out var name);
                sku.SupplierSettlementPriceModels.Add(new SupplierSettlementPriceModels()
                {
                    SupplierName = name ?? "",
                    FxUserId = nosetuser,
                    OppositeSettlementPrice = "未设置"
                });
            });
            #endregion
            // sku.IsDisabledUseWarehouse = sku.DownFxUserId > 0 ? '厂家供货' : '自营供货';
            sku.IsDisabledUseWarehouse = sku.DownFxUserId > 0 ? true : false;
            sku.IsSelf = sku.DownFxUserId > 0 ? false : true;

            // 价格编辑权限
            sku.CostPriceEnable = true;
            sku.SupplierSettlementPriceEnable = true;
            sku.ToReverseSettlementPriceEnable = sku.UpFxUserId > 0;
            //if (sku.IsSelf == true && sku.SupplierSettlementPriceModels.Count == 0)
            //{
            //    // 补充默认采购价格
            //    sku.SupplierSettlementPriceModels.Add(new SupplierSettlementPriceModels()
            //    {
            //        SettlementId =  sku.DefaultSupplierSettlementPriceId??0,
            //        SettlementPrice = sku.DefaultSupplierSettlementPrice.ToString(),
            //        SupplierName = "",
            //    });
            //}
        }

        public ProductFx GetOne(string productCode)
        {
            return _repository.ProductGetOne(productCode);
        }

        public List<ProductFx> GetOneDetail(List<string> productCodes, int fxUserId, bool isAll = false)
        {
            return _repository.GetOneDetail(productCodes, fxUserId,isAll);
        }

        public PagedResultModel<ProductFx> LoadProductByFxUserId(int fxUserId, ProductFxRequertModule model, int type, List<int> settlementUsers)
        {
            var tuple = GetListByFxUserId(fxUserId, model);

            //获取商品额外的数据
            var listData = GetProductByDownFxUser(fxUserId, tuple.Item2, model.ProductType);
            //查询对应SKU设置的价格
            listData = _repository.GetProductSkuSettlementPrice(fxUserId, tuple.Item2, type, settlementUsers);

            return new PagedResultModel<ProductFx>()
            {
                PageIndex = 1,
                PageSize = 10,
                Rows = listData,
                Total = tuple.Item1
            };
        }

        public List<ProductFx> GetProductsByFxUserId(int fxUserId)
        {
            return _repository.GetProductsByFxUserId(fxUserId);
        }

        /// <summary>
        /// 根据ProductCode获取商品列表
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="fields"></param>
        /// <returns></returns>
        public List<ProductFx> GetFxProductListByCodes(List<string> codes, List<string> fields = null)
        {
            return _repository.GetFxProductListByCodes(codes, fields);
        }

        /// <summary>
        /// 根据简称、重量搜索条件过滤Sku
        /// </summary>
        /// <param name="products"></param>
        /// <param name="model"></param>
        private void FilterSku(List<ProductFx> products, ProductFxRequertModule model)
        {
            if (products != null && products.Any())
            {
                if (model.ProductSkuShortTitle.IsNotNullOrEmpty())
                {
                    if (model.ProductSkuShortTitle.Contains(','))
                    {
                        var shortTitles = model.ProductSkuShortTitle.Split(',').ToList();
                        products.ForEach(p => { p.Skus = p.Skus?.Where(a => shortTitles.Contains(a.ShortTitle.ToString2()))?.ToList(); });
                    }
                    else
                    {
                        products.ForEach(p => { p.Skus = p.Skus?.Where(a => a.ShortTitle.ToString2().Contains(model.ProductSkuShortTitle.ToString2()))?.ToList(); });
                    }
                }
                else
                {

                    if (model.IsSetSkuShortTitle == "0")
                    {
                        products.ForEach(p => { p.Skus = p.Skus?.Where(a => string.IsNullOrEmpty(a.ShortTitle.ToString2()))?.ToList(); });
                    }
                    else if (model.IsSetSkuShortTitle == "1")
                    {
                        products.ForEach(p => { p.Skus = p.Skus?.Where(a => !string.IsNullOrEmpty(a.ShortTitle.ToString2()))?.ToList(); });
                    }
                }
                if (model.ProductSkuWeight.IsNotNullOrEmpty())
                {
                    products.ForEach(p => { p.Skus = p.Skus?.Where(a => a.Weight == model.ProductSkuWeight.ToInt())?.ToList(); });
                }
                else
                {
                    if (model.IsSkuSetWeight == "0")
                    {
                        products.ForEach(p => { p.Skus = p.Skus?.Where(a => a.Weight == 0)?.ToList(); });
                    }
                    else if (model.IsSkuSetWeight == "1")
                    {
                        products.ForEach(p => { p.Skus = p.Skus?.Where(a => a.Weight != 0)?.ToList(); });
                    }
                }
                if (model.IsBaseProduceCombine)
                {
                    if(model.IsRelationBaseProduct == 0)
                    {
                        products.ForEach(p => { p.Skus = p.Skus?.Where(a => a.IsRelationBaseProduct == false)?.ToList(); });
                    }
                    if (model.IsRelationBaseProduct == 1)
                    {
                        products.ForEach(p => { p.Skus = p.Skus?.Where(a => a.IsRelationBaseProduct == true)?.ToList(); });
                    }
                }
            }
        }

        /// <summary>
        /// 获取商品上下游
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="isQuerySelf">是否查询自营商品/sku</param>
        /// <returns></returns>
        public List<SupplierAgentModel> GetSupplierAndAgentByAllCodes(List<string> productCodes, List<string> skuCodes, int fxUserId, bool isQuerySelf = false)
        {
            if (productCodes == null && skuCodes == null)
                return new List<SupplierAgentModel>();
            if (productCodes == null)
                productCodes = new List<string>();
            if (skuCodes == null)
                skuCodes = new List<string>();
            return _repository.GetSupplierAndAgentByAllCodes(productCodes, skuCodes, fxUserId, isQuerySelf);
        }

        public void GetSkuSupplierAndAgentFromResult(Dictionary<string, List<SupplierAgentModel>> supplierAgentDict, ref List<int> suppliers, ref int agent, string skuCode, string productCode)
        {
            if(supplierAgentDict.IsNullOrEmptyList())
                return;
            supplierAgentDict.TryGetValue(skuCode, out var skuSupplierAgentDict);
            supplierAgentDict.TryGetValue(productCode, out var productSupplierAgentDict);

            if (!skuSupplierAgentDict.IsNullOrEmptyList())
            {
                agent = skuSupplierAgentDict?.FirstOrDefault(d => d.UpFxUserId > 0)?.UpFxUserId ?? 0;
                suppliers = skuSupplierAgentDict?.Select(d => d.DownFxUserId)?.ToList();
            }
            // sku没上下游，到商品维度找默认的
            if (agent == 0)
            {
                agent = productSupplierAgentDict?.FirstOrDefault(d => d.UpFxUserId > 0)?.UpFxUserId ?? 0;
            }
            if (suppliers.IsNullOrEmptyList())
            {
                suppliers = productSupplierAgentDict?.Where(d => d.DownFxUserId > 0)?.Select(d => d.DownFxUserId)?.ToList();
            }
            else if (suppliers.Exists(s => s <= 0))//sku自营
            {
                suppliers.Clear();
            }

        }

        /// <summary>
        /// 获取商品信息为复制副本
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<ProductFx> GetListForDuplication(DuplicationConditionModel condition,
            int pageSize)
        {
            return _repository.GetListForDuplication(condition, pageSize);
        }

        /// <summary>
        /// 获取商品信息列表，为副本补偿数据
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<ProductFx> GetListForDuplicationCompensate(DuplicationConditionModel condition,
            int pageSize)
        {
            return _repository.GetListForDuplicationCompensate(condition, pageSize);
        }

        /// <summary>
        /// 获取商品信息为云内迁移(PathFlowCode)
        /// </summary>
        /// <param name="condition">必要条件：PathFlowCode</param>
        /// <param name="pageSize"></param>
        /// <param name="currentCount">查询的原始条数</param>
        /// <returns></returns>
        public List<ProductFx> GetListForSameCloudMigrate(DuplicationConditionModel condition,
            int pageSize, out int currentCount)
        {
            var list = _repository.GetListForSameCloudMigrate(condition, pageSize);
            currentCount = list.Count;
            var dicProductFx = new Dictionary<string, ProductFx>();
            list.ForEach(item =>
            {
                if (dicProductFx.ContainsKey(item.ProductCode) == false)
                {
                    dicProductFx.Add(item.ProductCode, item);
                }
            });
            return dicProductFx.Values.ToList();
        }

        /// <summary>
        /// 获取商品信息为复制副本，按商品代码列表
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public List<ProductFx> GetListForDuplication(List<string> codes, string selectFields = "*")
        {
            if (codes == null || !codes.Any())
                return new List<ProductFx>();
            return _repository.GetListForDuplication(codes, selectFields);
        }

        /// <summary>
        /// 批量插入数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        public void InsertsForDuplication(List<ProductFx> models, bool isMigrate = false)
        {
            if (models == null || !models.Any())
                return;
            //清理源库ID
            models.ForEach(m => { m.Id = 0; });

            var batchSize = 500;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();
                //路径流代码
                var codes = batchModels.Select(m => m.ProductCode).ToList();
                //存在的唯一代码列表
                var idAndCodes = _repository.GetExistIdAndCodes(codes);
                //全部不存在
                if (idAndCodes == null || !idAndCodes.Any())
                {
                    try
                    {
                        baseRepository.BulkWrite(batchModels, "Product", maxSingleNum: 1);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"ProductFxService.InsertsForDuplication时异常：{ex}，将用单条方式重试一次");

                        var db = baseRepository.DbConnection;
                        if (db.State == System.Data.ConnectionState.Closed)
                            db.Open();
                        using (db)
                        {
                            //单条
                            batchModels.ForEach(item =>
                            {
                                try
                                {
                                    db.Insert(item);
                                }
                                catch (Exception ex2)
                                {
                                    var errMsg = ex2.Message.ToLower();
                                    if (errMsg.Contains("duplicate key") || errMsg.Contains("pk_") || errMsg.Contains("primary key"))
                                    {
                                        //忽略
                                    }
                                    else
                                    {
                                        throw ex2;
                                    }
                                }
                            });
                        }
                    }
                    continue;
                }
                //存在
                var existsCodes = idAndCodes.Select(m => m.Code).ToList();
                if (!isMigrate)
                {
                    var updates = batchModels.Where(m => existsCodes.Contains(m.ProductCode)).ToList();
                    if (updates.Any())
                    {
                        updates.ForEach(x =>
                        {
                            var model = idAndCodes.FirstOrDefault(m => m.Code == x.ProductCode);
                            if (model == null)
                            {
                                return;
                            }
                            x.Id = model.Id;
                        });
                        _repository.BulkUpdate(updates);
                    }
                }
                //不存在
                var inserts = batchModels.Where(m => !existsCodes.Contains(m.ProductCode)).ToList();
                if (inserts.Any())
                {
                    try
                    {
                        baseRepository.BulkWrite(inserts, "Product", maxSingleNum: 1);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"ProductFxService.InsertsForDuplication时异常：{ex}，将用单条方式重试一次");

                        var db = baseRepository.DbConnection;
                        if (db.State == System.Data.ConnectionState.Closed)
                            db.Open();
                        using (db)
                        {
                            //单条
                            inserts.ForEach(item =>
                            {
                                try
                                {
                                    db.Insert(item);
                                }
                                catch (Exception ex2)
                                {
                                    var errMsg = ex2.Message.ToLower();
                                    if (errMsg.Contains("duplicate key") || errMsg.Contains("pk_") || errMsg.Contains("primary key"))
                                    {
                                        //忽略
                                    }
                                    else
                                    {
                                        throw ex2;
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 批量更新状态，备注信息
        /// </summary>
        /// <param name="models"></param>
        public void UpdatesForDuplication(List<ProductFx> models)
        {
            var updateFieldNames = new List<string>
            {
                "Status",
                "CategoryName",
                "CategoryId",
                "CategoryId1",
                "CategoryId2",
                "CategoryId3",
                "CategoryId4"
            };
            _repository.BatchUpdate(models, updateFieldNames, new List<string> { "ProductCode" });
        }

        /// <summary>
        /// 逻辑删除
        /// </summary>
        /// <param name="productCodes"></param>
        /// <param name="fxUserId">当前登录的FxUserId</param>
        /// <returns></returns>
        public CheckResult SetDelete(List<string> productCodes, int fxUserId)
        {
            List<ProductFx> products = new List<ProductFx>();
            var checkResult = CheckProduct(productCodes, fxUserId, ref products);
            if (checkResult.Success)
            {
                checkResult.Success = _repository.SetDelete(productCodes);

                #region 数据变更日志
                List<DataChangeLog> dcLogs = products.Select(o => new DataChangeLog
                {
                    DataChangeType = DataChangeTypeEnum.UPDATE_STATUS,
                    TableTypeName = DataChangeTableTypeName.Product,
                    SourceShopId = o.ShopId,
                    SourceFxUserId = o.SourceUserId,
                    RelationKey = o.ProductCode,
                    ExtField1 = "ProductFxService.SetDelete"
                }
                ).ToList();
                new DataChangeLogRepository().Add(dcLogs, 1);
                #endregion

                //从副本库查到的基础商品关联数据
                var otherDbBaseOfPtSkuRelationList = new List<BaseOfPtSkuRelation>();
                #region 调用同步数据接口服务
                new SyncDataInterfaceService(fxUserId).ProductFxOpt(productCodes, (string targetConnectionString, List<string> targetProductCodes) =>
                {
                    if (targetProductCodes != null && targetProductCodes.Any())
                    {
                        new ProductFxRepository(targetConnectionString).SetDelete(targetProductCodes);

                        var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService(targetConnectionString);
                        var existList = baseOfPtSkuRelationService.GetList(targetProductCodes, whereFieldName: "ProductCode");
                        if (existList != null && existList.Any())
                            otherDbBaseOfPtSkuRelationList.AddRange(existList);
                    }
                });
                #endregion

                #region 处理基础商品异常
                ProcessBaseProductAbnormalForDelProduct(products, otherDbBaseOfPtSkuRelationList, fxUserId);
                #endregion
            }
            return checkResult;
        }

        /// <summary>
        /// 处理基础商品异常（删除商品）
        /// </summary>
        /// <param name="products"></param>
        /// <param name="otherDbBaseOfPtSkuRelationList">从副本库查询到的结果</param>
        /// <param name="fxUserId">当前登录的FxUserId</param>
        /// <returns></returns>
        public void ProcessBaseProductAbnormalForDelProduct(List<ProductFx> products, List<BaseOfPtSkuRelation> otherDbBaseOfPtSkuRelationList, int fxUserId)
        {
            if (products == null || products.Any() == false)
                return;

            try
            {
                //1.是否存在基础商品关系
                var productCodes = products.Select(a => a.ProductCode).ToList();
                var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService(false);
                var curDbName = baseOfPtSkuRelationService.baseRepository?.DbConnection?.Database ?? "";
                var existList = baseOfPtSkuRelationService.GetList(productCodes, whereFieldName: "ProductCode");
                if (existList == null)
                    existList = new List<BaseOfPtSkuRelation>();
                if (otherDbBaseOfPtSkuRelationList != null && otherDbBaseOfPtSkuRelationList.Any())
                    existList.AddRange(otherDbBaseOfPtSkuRelationList);
                if (existList.Any() == false)
                    return;

                //2.针对存在的添加异常消息
                var dicSkuCode = new Dictionary<string, int>();
                var messages = new List<MessageRecord>();
                var curCpt = CustomerConfig.CloudPlatformType;
                var dtNow = DateTime.Now;
                existList.ForEach(exist =>
                {
                    var key = $"{exist.ProductSkuCode}-{exist.BaseProductSkuUid}-{exist.FxUserId}";
                    if (dicSkuCode.ContainsKey(key))
                        return;

                    dicSkuCode.Add(key, 1);

                    //var existProduct = products.FirstOrDefault(a => a.ProductCode == exist.ProductCode);
                    var businessModel = new BaseProductAbnormal()
                    {
                        AbnormalType = BaseProductAbnormalType.DelProduct,
                        AbnormalSource = fxUserId == exist.FxUserId ? "Self" : "Agent",
                        AbnormalReason = fxUserId == exist.FxUserId ? "关联对象已被删除，请确认是否解绑关联状态。" : "下游已删除该商品，关联对象已失效，请确认是否解绑关联状态。",
                        FxUserId = exist.FxUserId,
                        BaseProductUid = exist.BaseProductUid,
                        BaseProductSkuUid = exist.BaseProductSkuUid,
                        ProductPtId = exist.ProductPtId,
                        ProductSkuPtId = exist.ProductSkuPtId,
                        ProductCode = exist.ProductCode,
                        ProductSkuCode = exist.ProductSkuCode,
                        ProductShopId = exist.ProductShopId,
                        ProductFxUserId = exist.ProductFxUserId,
                        ProductPlatformType = exist.ProductPlatformType,
                        ProductCloudPlatform = curCpt,
                        ProductDbName = curDbName,
                        CreateFxUserId = fxUserId,
                        CreateTime = dtNow,
                        UpdateTime = dtNow,
                    };
                    messages.Add(new MessageRecord()
                    {
                        CreateFxUserId = fxUserId,
                        CreateTime = dtNow,
                        BusinessId = exist.ProductSkuCode,
                        TargetCloud = CloudPlatformType.Alibaba.ToString(),//固定Alibaba
                        ProductPlatformType = exist.ProductPlatformType,
                        MsgType = BaseProductMsgType.AddBaseProductAbnormal,
                        FxUserId = exist.FxUserId,
                        DataJson = businessModel.ToJson()
                    });
                });
                if (CustomerConfig.IsLocalDbDebug)
                    new MessageRecordService().AddBaseProductAbnormal(messages);
                else
                    new MessageRecordService().SendBusinessMessage(messages);
            }
            catch (Exception ex)
            {
                Log.WriteError($"ProcessBaseProductAbnormalForDelProduct时异常：{ex}，products={products?.ToJson(true)}");
            }
        }

        /// <summary>
        /// 处理基础商品异常（商品换绑厂家）
        /// </summary>
        /// <param name="perCheck"></param>
        /// <param name="fxUserId">当前登录的FxUserId</param>
        /// <param name="codes">解绑的数据</param>
        /// <returns></returns>
        public void ProcessBaseProductAbnormalForBindSupplier(PerCheckBindSupplierResult perCheck, int fxUserId, List<KeyValuePair<string, string>> codes, List<PathFlowReferenceChangeModel> pathFlowReferenceChangeModels)
        {
            if (perCheck == null || perCheck.oldPaths == null || perCheck.oldPaths.Any() == false || codes == null || codes.Any() == false)
                return;

            var oldPathFlowReferences = perCheck.oldPaths.Where(a => a.PathFlowReferences != null && a.PathFlowReferences.Values != null).SelectMany(a => a.PathFlowReferences.Values).ToList();
            if (oldPathFlowReferences == null || oldPathFlowReferences.Any() == false)
                return;

            var newPathFlowReferences = pathFlowReferenceChangeModels.Where(a => a.NewPathFlowReferences != null && a.NewPathFlowReferences.Any()).SelectMany(a => a.NewPathFlowReferences).ToList();

            try
            {

                //1.根据解绑数据，区分出哪些是商品维度，哪些是Sku维度
                var productCodes = new List<string>();
                var skuCodes = new List<string>();
                foreach (var kv in codes)
                {
                    var pathFlowRefCode = kv.Value;
                    var productCode = kv.Key;
                    if (pathFlowRefCode == productCode)
                        productCodes.Add(pathFlowRefCode);
                    else
                        skuCodes.Add(pathFlowRefCode);
                }

                //2.是否存在基础商品关系
                var supplierFxUserIds = perCheck.oldPaths.Where(a => a.PathFlowNodes != null).SelectMany(a => a.PathFlowNodes).Where(a => a.DownFxUserId > 0).Select(a => a.DownFxUserId).Distinct().ToList();
                var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService(false);
                var curDbName = baseOfPtSkuRelationService.baseRepository?.DbConnection?.Database ?? "";
                var existList = new List<BaseOfPtSkuRelation>();
                if (productCodes != null && productCodes.Any())
                    existList.AddRange(baseOfPtSkuRelationService.GetListFromSupplierDb(productCodes, supplierFxUserIds, curDbName, whereFieldName: "ProductCode"));

                if (skuCodes != null && skuCodes.Any())
                    existList.AddRange(baseOfPtSkuRelationService.GetListFromSupplierDb(skuCodes, supplierFxUserIds, curDbName, whereFieldName: "ProductSkuCode"));

                if (existList == null || existList.Any() == false)
                    return;

                //2.针对存在的添加异常消息
                var dicSkuCode = new Dictionary<string, int>();
                var messages = new List<MessageRecord>();
                var curCpt = CustomerConfig.CloudPlatformType;
                var dtNow = DateTime.Now;

                existList.ForEach(exist =>
                {
                    var key = $"{exist.ProductSkuCode}-{exist.BaseProductSkuUid}-{exist.FxUserId}";
                    if (dicSkuCode.ContainsKey(key))
                        return;

                    dicSkuCode.Add(key, 1);

                    #region 只针对厂家处理
                    var needProcess = false;
                    var supplierFxUserId = exist.FxUserId;

                    //解绑的为Sku
                    var refCode = exist.ProductSkuCode;
                    if (skuCodes.Any(a => a == refCode) == false)
                    {
                        //解绑的为商品
                        refCode = exist.ProductCode;
                    }

                    var curPFR = oldPathFlowReferences.FirstOrDefault(a => a.PathFlowRefCode == refCode);
                    if (curPFR != null)
                    {
                        var curOldPaths = perCheck.oldPaths.Where(a => a.PathFlowCode == curPFR.PathFlowCode).ToList();
                        //Step1. 旧路径流的DownFxUserId（厂家）节点包含关联数据的用户，需要处理
                        if (curOldPaths != null && curOldPaths.Where(a => a.PathFlowNodes != null).Any(a => a.PathFlowNodes.Any(b => b.DownFxUserId == supplierFxUserId)))
                            needProcess = true;

                        //新路径包含厂家，也不需要处理
                        var curNewPFR = newPathFlowReferences?.FirstOrDefault(a => a.PathFlowRefCode == refCode);
                        if (curNewPFR != null)
                        {
                            var curNewPaths = perCheck.newPathDic.Values.Where(a => a.PathFlowCode == curNewPFR.PathFlowCode).ToList();
                            //Step2. 新路径流的DownFxUserId（厂家）节点包含关联数据的用户，不需要处理
                            if (curNewPaths != null && curNewPaths.Where(a => a.PathFlowNodes != null).Any(a => a.PathFlowNodes.Any(b => b.DownFxUserId == supplierFxUserId)))
                                needProcess = false;
                        }
                    }
                    if (needProcess == false)
                        return;
                    #endregion

                    var businessModel = new BaseProductAbnormal()
                    {
                        AbnormalType = BaseProductAbnormalType.BindSupplier,
                        AbnormalSource = "Agent",
                        AbnormalReason = "下游已撤回商品代发，关联对象已失效，请确认是否解绑关联状态。",
                        FxUserId = exist.FxUserId,
                        BaseProductUid = exist.BaseProductUid,
                        BaseProductSkuUid = exist.BaseProductSkuUid,
                        ProductPtId = exist.ProductPtId,
                        ProductSkuPtId = exist.ProductSkuPtId,
                        ProductCode = exist.ProductCode,
                        ProductSkuCode = exist.ProductSkuCode,
                        ProductShopId = exist.ProductShopId,
                        ProductFxUserId = exist.ProductFxUserId,
                        ProductPlatformType = exist.ProductPlatformType,
                        ProductCloudPlatform = curCpt,
                        ProductDbName = curDbName,
                        CreateFxUserId = fxUserId,
                        CreateTime = dtNow,
                        UpdateTime = dtNow,
                    };
                    messages.Add(new MessageRecord()
                    {
                        CreateFxUserId = fxUserId,
                        CreateTime = dtNow,
                        BusinessId = exist.ProductSkuCode,
                        TargetCloud = CloudPlatformType.Alibaba.ToString(),//固定Alibaba
                        ProductPlatformType = exist.ProductPlatformType,
                        MsgType = BaseProductMsgType.AddBaseProductAbnormal,
                        FxUserId = exist.FxUserId,
                        DataJson = businessModel.ToJson()
                    });
                });
                if (CustomerConfig.IsLocalDbDebug)
                    new MessageRecordService().AddBaseProductAbnormal(messages);
                else
                    new MessageRecordService().SendBusinessMessage(messages);
            }
            catch (Exception ex)
            {
                Log.WriteError($"ProcessBaseProductAbnormalForBindSupplier时异常：{ex}，perCheck={perCheck?.ToJson()}，codes={codes?.ToJson()}");
            }
        }


        /// <summary>
        /// 默认采购价格处理
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="codes"></param>
        /// <param name="codesType"></param>
        public void UsingDefaultSupplierSettlementPrice(int fxUserId, List<KeyValuePair<string, string>> codes, string codesType, BindSupplierRequestModel model)
        {
            try
            {
                if (!model.isSelf)
                {
                    var skuCodes = new List<string>();
                    var proCodes = new List<string>();
                    var settlementPricesAll = new List<ProductSettlementPrice>();
                    var settlementPricesOld = new List<ProductSettlementPrice>();
                    var settlementPricesNew = new List<ProductSettlementPrice>();
                    var settlementPricesDefault = new List<ProductSettlementPrice>();
                    var settlementPricesUpdate = new List<ProductSettlementPrice>();
                    var settlementPricesAdd = new List<ProductSettlementPrice>();
                    var settlementNewCode = new List<KeyValuePair<string, string>>();

                    var supplierUserIds =
                        model.Configs.Select(p => p.SupplierId).Distinct().ToList();
                    if (codesType == "Product")
                    {
                        proCodes = codes.Select(p => p.Value).Distinct().ToList();
                        settlementPricesAll = _repository.GetProductSettlementSupplierSettlementPrice(proCodes, fxUserId, false);
                    }
                    if (codesType == "Sku")
                    {
                        proCodes = codes.Select(p => p.Key).Distinct().ToList();
                        skuCodes = codes.Select(p => p.Value).Distinct().ToList();
                        settlementPricesAll = _repository.GetProductSettlementSupplierSettlementPrice(skuCodes, fxUserId, true);
                    }

                    settlementPricesDefault = settlementPricesAll.Where(p => p.SettlementType == 4).ToList();
                    settlementPricesOld = settlementPricesAll.Where(p => p.SettlementType == 1 && supplierUserIds.Contains(p.FxUserId)).ToList();

                    // 默认采购价格针对SKU
                    if (codesType == "Sku")
                    {
                        foreach (var item in codes)
                        {
                            var add = settlementPricesOld.Where(p => p.ProductCode == item.Key && p.ProductSkuCode == item.Value).FirstOrDefault();
                            if (add == null)
                            {
                                settlementNewCode.Add(item);
                            }
                        }
                    }

                    // 对应厂家有设置过采购价格更新
                    // 对应厂家无设置过采购价格添加
                    // 存在默认采购价格进行采购价格覆盖
                    foreach (var item in settlementPricesOld)
                    {

                        var d = settlementPricesDefault.FirstOrDefault(p => p.ProductSkuCode == item.ProductSkuCode);
                        if (d != null)
                        {
                            item.Price = d.Price;
                            settlementPricesUpdate.Add(item);
                            Log.WriteError($"更新设置默认采购价格日志");
                        }
                    }
                    // 存在默认采购价格进行采购价格添加
                    foreach (var item in settlementNewCode)
                    {

                        var d = settlementPricesDefault.FirstOrDefault(p => p.ProductSkuCode == item.Value);
                        if (d != null)
                        {
                            var data = supplierUserIds.Select(id => new ProductSettlementPrice
                            {
                                CreateTime = DateTime.Now,
                                UpdateTime = DateTime.Now,
                                ProductCode = item.Key,
                                ProductSkuCode = item.Value,
                                SettlementType = 1,
                                PlatformType = CustomerConfig.CloudPlatformType,
                                CreateUser = fxUserId,
                                FxUserId = id,
                                Price = d.Price
                            }).ToList();
                            Log.WriteError($"新增设置默认采购价格日志");
                            settlementPricesAdd.AddRange(data);
                        }
                    }
                    settlementPricesUpdate =
                        settlementPricesUpdate.Concat(settlementPricesAdd).ToList();
                    if (settlementPricesUpdate.Count > 0)
                    {
                        int batchSize = 500;
                        int totalCount = settlementPricesUpdate.Count;
                        var pages = Math.Ceiling(totalCount * 1.0 / batchSize);

                        for (int i = 0; i < pages; i++)
                        {
                            try
                            {
                                var data = settlementPricesUpdate.Skip(i * batchSize).Take(batchSize).ToList();
                                _financialSettlementService.SetCommonSettlementPrice(data, fxUserId, 1);
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError($"SetCommonSettlementPrice时异常：{ex}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"UsingDefaultSupplierSettlementPrice时异常：{ex}，codes={codes?.ToJson()}");
            }
            //Log.WriteError($"设置默认采购价格日志1：{model.ToJson()}");
            //Log.WriteError($"设置默认采购价格日志2：{fxUserId}");
            //Log.WriteError($"设置默认采购价格日志3：{codes.ToJson()}");
            //Log.WriteError($"设置默认采购价格日志4：{codesType.ToJson()}");
            //Log.WriteError($"设置默认采购价格日志5：{settlementPricesAll.ToJson()}");
        }


        /// <summary>
        /// 恢复商品
        /// </summary>
        /// <param name="productCodes"></param>
        /// <param name="fxUserId">当前登录的FxUserId</param>
        /// <returns></returns>
        public CheckResult Recovery(List<string> productCodes, int fxUserId)
        {
            List<ProductFx> products = new List<ProductFx>();
            var checkResult = CheckProduct(productCodes, fxUserId, ref products);
            if (checkResult.Success)
            {
                checkResult.Success = _repository.Recovery(productCodes);

                #region 数据变更日志
                List<DataChangeLog> dcLogs = products.Select(o => new DataChangeLog
                {
                    DataChangeType = DataChangeTypeEnum.UPDATE_STATUS,
                    TableTypeName = DataChangeTableTypeName.Product,
                    SourceShopId = o.ShopId,
                    SourceFxUserId = o.SourceUserId,
                    RelationKey = o.ProductCode,
                    ExtField1 = "ProductFxService.Recovery"
                }
                ).ToList();
                new DataChangeLogRepository().Add(dcLogs, 1);
                #endregion

                #region 调用同步数据接口服务
                new SyncDataInterfaceService(fxUserId).ProductFxOpt(productCodes, (string targetConnectionString, List<string> targetProductCodes) =>
                {
                    if (targetProductCodes != null && targetProductCodes.Any())
                        new ProductFxRepository(targetConnectionString).Recovery(targetProductCodes);
                });
                #endregion
            }
            return checkResult;
        }

        /// <summary>
        /// 检查归属
        /// </summary>
        /// <param name="productCodes"></param>
        /// <param name="fxUserId">当前登录的FxUserId</param>
        /// <returns></returns>
        public CheckResult CheckProduct(List<string> productCodes, int fxUserId, ref List<ProductFx> products)
        {
            var checkResult = new CheckResult { Success = true };
            if (productCodes == null || !productCodes.Any())
            {
                checkResult.Success = false;
                checkResult.Message = "请选择您要操作的商品";
                return checkResult;
            }
            //检查归属，只有自己店铺的才有权限
            products = GetFxProductListByCodes(productCodes, new List<string> { "PlatformId", "ShopId", "SourceUserId" });
            if (products != null && products.Any(a => a.SourceUserId != fxUserId))
            {
                //归属有问题的商品
                var pcodes = products.Where(a => a.SourceUserId != fxUserId).Select(a => a.ProductCode);
                checkResult.Data = string.Join(",", pcodes);
                checkResult.Success = false;
                checkResult.Message = "只有自己的店铺的商品才能进行此操作";
            }
            return checkResult;
        }

        /// <summary>
        /// 是否有1688代销商品（一件代发或一件起批）
        /// </summary>
        /// <param name="shopId"></param>
        /// <returns></returns>
        public bool IsHasDistributorProduct(int shopId)
        {
            return _repository.IsHasDistributorProduct(shopId);
        }

        /// <summary>
        /// 是否有1688代销商品（一件代发或一件起批）
        /// </summary>
        /// <param name="systemShopId"></param>
        /// <returns></returns>
        public bool IsHasDistributorProductBySystemShopId(int systemShopId)
        {
            var key = BusinessSettingKeys.SupplyBy1688.ShopBy1688;
            var shopId = new BusinessSettingsService().GetsByShopId(systemShopId, new List<string> { key }).FirstOrDefault()?.Value.ToInt() ?? 0;
            if (shopId <= 0)
                return false;
            return IsHasDistributorProduct(shopId);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="skuCodes"></param>
        /// <param name="fields">商品的字段，加上前缀p.，规格的字段，加上前缀sku.</param>
        /// <returns></returns>
        public List<ProductFx> GetBySkuCodes(List<string> skuCodes, List<string> fields = null)
        {
            return _repository.GetBySkuCodes(skuCodes, fields);
        }

        /// <summary>
        /// 查询指定库的商品数据，按SkuCode
        /// </summary>
        /// <param name="skuCodes"></param>
        /// <param name="dbNameConfigId"></param>
        /// <param name="fields">sku.ProductCode及p.PlatformId\ p.ShopId\p.SourceUserId必须有</param>
        /// <returns></returns>
        public List<ProductFx> GetSupplierProductBySkuCodes(List<string> skuCodes, int dbNameConfigId, List<string> fields = null)
        {
            //跨库查询、跨云平台查询
            var dbApi = new DbAccessUtility(new ApiDbConfigModel { DbNameConfigId = dbNameConfigId, Location = "Alibaba", PlatformType = "Alibaba" });
            //先查询Sku，再查询Product
            var fieldString = "*";
            var skuFieldString = "*";
            if (fields?.Any(x => x.StartsWith("p.")) == true)
                fieldString = string.Join(",", fields?.Where(x => x.StartsWith("p.")));
            if (fields?.Any(x => x.StartsWith("sku.")) == true)
                skuFieldString = string.Join(",", fields?.Where(x => x.StartsWith("sku.")));
            var productSkus = dbApi.Query<ProductSkuFx>($"select {skuFieldString} from ProductSku sku (NOLOCK) INNER JOIN dbo.FunStringToTable(@pSkuCodeStr,',') fstt ON sku.SkuCode = fstt.item", new { pSkuCodeStr = string.Join(",", skuCodes.Distinct()) });
            var productCodes = productSkus?.Select(x => x.ProductCode)?.Distinct()?.ToList();
            List<ProductFx> products = null;
            if (productCodes != null && productCodes.Any())
            {
                products = dbApi.Query<ProductFx>($"select {fieldString} from Product p (NOLOCK) INNER JOIN dbo.FunStringToTable(@pCodeStr,',') fstt ON p.ProductCode = fstt.item", new { pCodeStr = string.Join(",", productCodes.Distinct()) });
                products?.ForEach(x =>
                {
                    x.Skus = productSkus.Where(y => y.ProductCode == x.ProductCode).ToList();
                });
            }
            return products;
        }

        /// <summary>
        /// 在用户所在【精选云】查询
        /// </summary>
        /// <param name="skuCodes"></param>
        /// <param name="fxUserIds">用户</param>
        /// <param name="fields">sku.ProductCode及p.PlatformId\ p.ShopId\p.SourceUserId必须有</param>
        /// <returns></returns>
        public List<ProductFx> GetSupplierProductBySkuCodes(List<string> skuCodes, List<int> fxUserIds, List<string> fields = null)
        {
            if (skuCodes == null || skuCodes.Any() == false)
                return new List<ProductFx>();

            var dbConfigRp = new DbConfigRepository();
            //用户所在精选云
            var fxUserDbNameConfigs = dbConfigRp.GetFxUserDbNameConfigIds(fxUserIds);
            if (fxUserDbNameConfigs == null || fxUserDbNameConfigs.Any() == false)
                throw new LogicException("系统异常，请联系我们", "FX1688_ORDER_UPFXUSER_DBCONFIG_ERROR");
            var products = new List<ProductFx>();
            fxUserDbNameConfigs.GroupBy(x => x.DbNameConfigId).ToList().ForEach(group =>
            {
                var fxUserProducts = GetSupplierProductBySkuCodes(skuCodes, group.Key, fields);
                if (fxUserProducts != null && fxUserProducts.Any())
                    products.AddRange(fxUserProducts);
                else
                {
                    Log.WriteWarning($"未能查询到厂家对应的商品信息，DbNameConfigId:{group.Key}，skuCodes:{skuCodes?.ToJson()}");
                }
            });

            return products;
        }

        /// <summary>
        /// 查询指定库的商品数据，按ProductCode
        /// </summary>
        /// <param name="productCodes"></param>
        /// <param name="dbNameConfigId"></param>
        /// <param name="fields">sku.ProductCode及p.PlatformId\ p.ShopId\p.SourceUserId必须有</param>
        /// <returns></returns>
        public List<ProductFx> GetSupplierProductByProductCodes(List<string> productCodes, int dbNameConfigId, List<string> fields = null)
        {
            //Stopwatch stopwatch1 = new Stopwatch();
            //stopwatch1.Start();

            //跨库查询、跨云平台查询
            var dbApi = new DbAccessUtility(new ApiDbConfigModel { DbNameConfigId = dbNameConfigId, Location = "Alibaba", PlatformType = "Alibaba" });
            //先查询Product，再查询Sku
            var fieldString = "*";
            var skuFieldString = "*";
            if (fields?.Any(x => x.StartsWith("p.")) == true)
                fieldString = string.Join(",", fields?.Where(x => x.StartsWith("p.")));
            if (fields?.Any(x => x.StartsWith("sku.")) == true)
                skuFieldString = string.Join(",", fields?.Where(x => x.StartsWith("sku.")));

            //var products = dbApi.Query<ProductFx>($"select {fieldString} from Product p (NOLOCK) WHERE p.ProductCode IN @productCodes", new { productCodes });
            var proSQL = $"select {fieldString} from Product p (NOLOCK) INNER JOIN FunStringToTable(@Codes,',') ft ON ft.item=p.ProductCode ";
            var products = dbApi.Query<ProductFx>(proSQL, new { Codes = string.Join(",", productCodes) });

            if (products != null && products.Any())
            {
                //var productSkus = dbApi.Query<ProductSkuFx>($"select {skuFieldString} from ProductSku sku (NOLOCK) WHERE sku.ProductCode IN @productCodes", new { productCodes });
                var skuSQL =
                    $"select {skuFieldString} from ProductSku sku (NOLOCK) INNER JOIN FunStringToTable(@Codes,',') ft ON ft.item=sku.ProductCode ";
                var productSkus = dbApi.Query<ProductSkuFx>(skuSQL, new { Codes = string.Join(",", productCodes) });

                products.ForEach(x => { x.Skus = productSkus.Where(y => y.ProductCode == x.ProductCode).ToList(); });
            }

            //stopwatch1.Stop();
            //Log.WriteLine($"GetSupplierProductByProductCodes内部，dbNameConfigId:{dbNameConfigId}; proSQL:{proSQL}   === skuSQL:{skuSQL} ===  productCodes: {productCodes.ToJson()} fields:{dbNameConfigId} fields: {fields.ToJson()} 耗时;{stopwatch1.ElapsedMilliseconds}ms", "耗时测试.txt");
            return products;
        }


        /// <summary>
        /// 在用户所在【精选云】查询
        /// </summary>
        /// <param name="productCodes"></param>
        /// <param name="fxUserIds">用户</param>
        /// <param name="fields">sku.ProductCode及p.PlatformId\ p.ShopId\p.SourceUserId必须有</param>
        /// <returns></returns>
        public List<ProductFx> GetSupplierProductByProductCodes(List<string> productCodes, List<int> fxUserIds, List<string> fields = null)
        {
            if (productCodes == null || productCodes.Any() == false)
                return new List<ProductFx>();

            var dbConfigRp = new DbConfigRepository();
            //用户所在精选云
            var fxUserDbNameConfigs = dbConfigRp.GetFxUserDbNameConfigIds(fxUserIds);
            if (fxUserDbNameConfigs == null || fxUserDbNameConfigs.Any() == false)
                throw new LogicException("系统异常，请联系我们", "FX1688_ORDER_UPFXUSER_DBCONFIG_ERROR");

            //修改为多线程，提高性能
            ConcurrentBag<ProductFx> products = new ConcurrentBag<ProductFx>();
            var dbConfigs = fxUserDbNameConfigs.GroupBy(x => x.DbNameConfigId).ToList();
            Parallel.ForEach(dbConfigs, new ParallelOptions { MaxDegreeOfParallelism = 10 }, group =>
            {
                var fxUserProducts = GetSupplierProductByProductCodes(productCodes, group.Key, fields);
                if (fxUserProducts != null && fxUserProducts.Any())
                {
                    fxUserProducts.ForEach(product => { products.Add(product); });
                }
                else
                {
                    Log.WriteWarning($"未能查询到厂家对应的商品信息，DbNameConfigId:{group.Key}，productCodes:{productCodes?.ToJson()}");
                }

            });

            return products.ToList();

            // var products = new List<ProductFx>();
            //var products = new List<ProductFx>();
            //fxUserDbNameConfigs.GroupBy(x => x.DbNameConfigId).ToList().ForEach(group =>
            //{
            //    var fxUserProducts = GetSupplierProductByProductCodes(productCodes, group.Key, fields);
            //    if (fxUserProducts != null && fxUserProducts.Any())
            //        products.AddRange(fxUserProducts);
            //    else
            //    {
            //        Log.WriteWarning($"未能查询到厂家对应的商品信息，DbNameConfigId:{group.Key}，productCodes:{productCodes?.ToJson()}");
            //    }
            //});

            //return products;
        }

        /// <summary>
        /// 设置分销品商品，规格简称
        /// </summary>
        /// <param name="sets"></param>
        /// <returns></returns>
        public ReturnedModel Set1688ProductShortTitles(List<Set1688ProductShortTitleModel> sets)
        {
            //判空处理
            if (sets == null || !sets.Any())
            {
                return new ReturnedModel();
            }
            //当前分销用户ID
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //商品简称
            var productShortTitles = sets.Where(m => string.IsNullOrWhiteSpace(m.SkuCode)).Select(m => new ProductInfoFx
            {
                SourceUserId = m.SourceFxUserId,
                SourceShopId = m.SourceShopId,
                ProductCode = m.ProductCode,
                ShortTitle = m.ShortTitle,
                FxUserId = fxUserId,
                CreateTime = DateTime.Now
            }).ToList();
            if (productShortTitles.Any())
            {
                new ProductInfoFxService(_connectionString).InsertsForDuplication(productShortTitles, true,
                    new List<string> { "ShortTitle" });
            }
            //商品规格
            var skuShortTitles = sets.Where(m => !string.IsNullOrWhiteSpace(m.SkuCode)).Select(m => new ProductSkuInfoFx
            {
                SourceUserId = m.SourceFxUserId,
                SourceShopId = m.SourceShopId,
                ProductCode = m.ProductCode,
                SkuCode = m.SkuCode,
                ShortTitle = m.ShortTitle,
                FxUserId = fxUserId,
                CreateTime = DateTime.Now
            }).ToList();
            if (skuShortTitles.Any())
            {
                Log.WriteLine($"设置商品规格简称，相关信息：{skuShortTitles.ToJson()}",
                    $"SetProductShortTitle_{DateTime.Now:yyyy-MM-dd}.log");

                new ProductSkuInfoFxService().InsertsForDuplication(skuShortTitles, true,
                    new List<string> { "ShortTitle" });
            }
            //同步到下游商家
            SetProductShortTitlesToOtherPlatform(sets);
            //调整
            return new ReturnedModel();

        }
        /// <summary>
        /// 同步到其他平台
        /// </summary>
        /// <param name="sets"></param>
        /// <returns></returns>
        public ReturnedModel SetProductShortTitlesToOtherPlatform(List<Set1688ProductShortTitleModel> sets)
        {
            //判空处理
            if (sets == null || !sets.Any())
            {
                return new ReturnedModel();
            }
            //当前分销用户ID
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //分销品商品编号
            var upProductCodes = sets.Select(m => m.ProductCode).Distinct().ToList();
            //获取分销品关系
            var mappings =
                new DistributorProductMappingRepository(_connectionString).GetDistributorProductMappings(upProductCodes, fxUserId,
                    "pm.UpProductCode");
            //判空处理
            if (mappings == null || !mappings.Any())
            {
                return new ReturnedModel();
            }
            Log.WriteLine($"设置商品简称，分销商品关联信息：{mappings.ToJson()}",
                $"SetProductShortTitle_{DateTime.Now:yyyy-MM-dd}.log");
            //构建商品简称模型
            var productInfos = sets.Where(p => string.IsNullOrWhiteSpace(p.SkuCode)).SelectMany(p =>
            {
                //找出分销品关联的商品
                var productMappings = mappings.Where(m => m.UpProductCode == p.ProductCode).ToList();
                //构建商品简称信息
                var models = productMappings.Select(m => new ProductInfoFx
                {
                    SourceUserId = m.DownFxUserId,
                    SourceShopId = m.DownShopId,
                    ProductCode = m.DownProductCode,
                    PlatformType = m.DownPlatformType,
                    ShortTitle = p.ShortTitle,
                    FxUserId = fxUserId,
                    CreateTime = DateTime.Now
                }).Distinct().ToList();
                return models;
            }).ToList();
            new ProductInfoFxService(_connectionString).SyncToOtherPlatform(productInfos);
            //构建商品规格简称模型
            var skuInfos = sets.Where(s => !string.IsNullOrWhiteSpace(s.SkuCode)).SelectMany(s =>
            {
                var skuMappings = mappings.Where(m => m.SkuMappings != null).SelectMany(m => m.SkuMappings)
                    .Where(m => m.UpProductCode == s.ProductCode && m.UpSkuCode == s.SkuCode).ToList();
                //构建商品规格简称信息
                var models = skuMappings.Select(m => new ProductSkuInfoFx
                {
                    SourceUserId = m.DownFxUserId,
                    SourceShopId = m.DownShopId,
                    ProductCode = m.DownProductCode,
                    PlatformType = m.DownPlatformType,
                    SkuCode = m.DownSkuCode,
                    ShortTitle = s.ShortTitle,
                    FxUserId = fxUserId,
                    CreateTime = DateTime.Now
                });
                return models;
            }).ToList();
            new ProductSkuInfoFxService(_connectionString).SyncToOtherPlatform(skuInfos);
            return new ReturnedModel();
        }

        /// <summary>
        /// 向各云平台发送RabbitMQ消息
        /// </summary>
        /// <param name="models"></param>
        /// <param name="tag"></param>
        public void ProcessPriceSendRabbitMQMessage(List<ProductFx> models, string tag = "")
        {
            if (models == null || models.Any() == false)
                return;

            //过滤：只取1688平台代销品
            var needModels = models.Where(a => a.PlatformType == PlatformType.Alibaba.ToString() && (a.ProductType == 1 || a.ProductType == 2)).ToList();
            if (needModels.Any() == false)
                return;

            var curFxUserId = needModels.First().SourceUserId;
            var shopIds = needModels.Select(a => a.ShopId).Distinct().ToList();
            var systemShopId = new FxUserShopService().GetFxUserIdMapping(new List<int> { curFxUserId })?.FirstOrDefault()?.ShopId ?? 0;
            var tupleSet = GetSettlementPriceSetting(systemShopId);
            var aliShopId = tupleSet.Item1;
            var isUpdateSettlementPriceSwitch = tupleSet.Item2;
            var isUpdateHistorySettlementPrice = tupleSet.Item3;
            var priceFields = tupleSet.Item4;

            //不更新单人单品价或非厂家自己设置的下单店铺，直接返回
            if (isUpdateSettlementPriceSwitch == false || aliShopId <= 0 || shopIds.Any(a => a == aliShopId) == false)
                return;

            needModels = models.Where(a => a.ShopId == aliShopId).ToList();

            var logFileName = $"ProcessPriceSendRabbitMQMessage-{DateTime.Now.ToString("yyyyMMdd")}.txt";

            #region 组装消息内容
            var messageList = needModels.Select(p => new UpdatePurchaseSettlementPriceModel
            {
                UserId = p.SourceUserId,
                ShopId = p.ShopId,
                PlatformId = p.PlatformId,
                Skus = p.Skus?.Select(sku => new PurchaseSettlementPriceSku { SkuId = sku.SkuId, PriceJson = sku.PriceJson }).ToList()
            }).ToList();
            #endregion

            //日志
            Log.Debug($"发送1688代销品采购结算价消息到消息队列，消息列表={messageList.ToJson(true)}，tupleSet={tupleSet.ToJson()}", logFileName);

            WriteAliLog(messageList, "代销品采购结算价发送MQ消息", tag);

            var wu = new WebUtils();
            var pddMessageUrl = CustomerConfig.PddMessageUrl;

            #region 向各云平台发送消息
            ThreadPool.QueueUserWorkItem(state =>
            {
                messageList.ForEach(message =>
                {
                    try
                    {
                        //发送消息到消息队列
                        RabbitMQService.SendMessage(message, RabbitMQService.UpdatePurchaseSettlementPriceMessageDescription);
                        if (isUpdateHistorySettlementPrice)
                        {
                            //发消息到抖音云消息队列
                            RabbitMQQueueToTouTiao.SendMessage(message, RabbitMQService.UpdatePurchaseSettlementPriceMessageDescription);

                            //发消息到拼多多云
                            try
                            {
                                var curMsg = new CloudMessageModel()
                                {
                                    Msg = message.ToJson(true),
                                    SourceCloud = CustomerConfig.CloudPlatformType,
                                    TargetCloud = CloudPlatformType.Pinduoduo.ToString(),
                                    MsgType = "UpdateSettlementPrice"
                                };
                                //加密
                                var lastMsg = DES.EncryptDES(curMsg.ToJson(true), CustomerConfig.LoginCookieEncryptKey);
                                var requestBody = Encoding.UTF8.GetBytes(lastMsg);
                                string content = wu.DoPostByRequestStream(pddMessageUrl, requestBody);

                                Log.Debug($"发送1688代销品采购价消息到PDD消息队列，单条消息：{message.ToJson(true)},pddMessageUrl={pddMessageUrl},lastMsg={lastMsg},content={content}", logFileName);
                            }
                            catch (Exception ex)
                            {
                                Log.WriteError($"发送1688更新采购结算价消息到拼多多云{pddMessageUrl}时发生错误，消息内容：{message.ToJson(true)}，错误信息：{ex}", $"ProcessPurchaseStatusSendRabbitMQMessage-error-{DateTime.Now.ToString("yyyyMMdd")}.txt");
                            }
                        }
                        //日志
                        Log.WriteLine($"发送1688代销品采购价消息到消息队列，单条消息：{message.ToJson(true)}", logFileName);
                    }
                    catch (Exception e)
                    {
                        Log.WriteError($"发送1688代销品采购价消息到消息队列时异常，异常原因：{e.Message}，单条消息：{message.ToJson(true)}，堆栈信息：{e.StackTrace}", "ProcessPriceSendRabbitMQMessageError.txt");
                    }
                });
            });
            #endregion

        }

        /// <summary>
        /// 写入AliLog
        /// </summary>
        /// <param name="models"></param>
        /// <param name="tag"></param>
        /// <param name="batchNo"></param>
        private void WriteAliLog(List<UpdatePurchaseSettlementPriceModel> models, string tag, string batchNo)
        {
            if (models == null || !models.Any())
                return;
            //开关，默认为关
            var key = "/System/Fendan/PurchaseOrderRelation/WriteAliLog";
            var isOpenValue = new CommonSettingRepository().Get(key, 0)?.Value ?? "";
            if (isOpenValue == "" || isOpenValue == "0" || isOpenValue == "false")
                return;

            var dbName = _repository.DbConnection.Database;
            var currentFxUserId = BaseSiteContext.CurrentNoThrow?.CurrentFxUserId ?? 0;
            var batchSize = 500;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();
                var model = new PurchaseOrderRelationLogModel() { Models = batchModels.ToJson(true), PlatformOrderIds = batchModels.Select(a => a.PlatformId).ToJson() };
                model.FxUserId = batchModels.First().UserId;
                model.BatchNo = $"{batchNo}";
                model.BatchNum = i;
                model.HostIp = HostHelper.IpAddress();
                model.HostName = Environment.MachineName;
                model.CurFxUserId = currentFxUserId;
                model.Tag = tag;
                model.DbName = dbName;
                model.CloudPlatformType = CustomerConfig.CloudPlatformType;
                Instance.WriteLog(model);
            }
        }

        /// <summary>
        /// 更新代销品采购结算价
        /// </summary>
        /// <param name="models"></param>
        /// <param name="tag"></param>
        public void UpdatePurchaseSettlementPrice(List<UpdatePurchaseSettlementPriceModel> models, List<ProductFx> fxProducts, Tuple<int, bool, bool, string, string> tupleSet, string shopPlatformType, string tag = "")
        {
            if (models == null || models.Any() == false)
                return;

            if (fxProducts == null || fxProducts.Any() == false || tupleSet == null)
                return;

            var dbName = _repository.DbConnection.Database;
            var curFxUserId = BaseSiteContext.CurrentNoThrow?.CurrentFxUserId ?? 0;
            var systemShopId = BaseSiteContext.CurrentNoThrow?.CurrentShopId ?? 0;
            var curCloudPlatformType = CustomerConfig.CloudPlatformType;

            var dtNow = DateTime.Now;
            var aliShopId = tupleSet.Item1;
            var isUpdateSettlementPriceSwitch = tupleSet.Item2;
            var isUpdateHistorySettlementPrice = tupleSet.Item3;
            var priceFields = tupleSet.Item4;

            var batchNo = models.First().PlatformId;
            var logFileName = $"1688-UpdatePurchaseSettlementPrice-{DateTime.Now.ToString("yyyyMMdd")}.txt";

            #region 若为精选云：更新ProductSku.DefaultSettlementPrice字段
            if (curCloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                //更新ProductSku的默认结算价
                var skus = fxProducts.Where(p => p.Skus != null).SelectMany(p => p.Skus).ToList();
                skus.ForEach(s =>
                {
                    s.PriceFields = priceFields;
                    s.DefaultSettlementPrice = null;
                });
                var defautSettlementPrices = skus.Select(sku => new SetDefaultSettlementPriceModel
                {
                    SkuCode = sku.SkuCode,
                    ProductCode = sku.ProductCode,
                    DefaultSettlementPrice = sku.LastSettlementPrice,
                    CreateFxUserId = curFxUserId,
                    CreateTime = dtNow
                }).ToList();

                Log.WriteLine($"更新代销品默认结算价，defautSettlementPrices={defautSettlementPrices.ToJson()}，priceFields={priceFields}，所在库：{dbName}", logFileName);

                var curLogList = defautSettlementPrices.Select(p => new UpdatePurchaseSettlementPriceModel { PlatformId = p.SkuCode, UserId = p.CreateFxUserId, ShopId = (p.DefaultSettlementPrice * 100).ToInt() }).ToList();
                WriteAliLog(curLogList, $"更新代销品默认结算价", batchNo);

                new ProductSkuFxService().SetDefaultSettlementPrices(defautSettlementPrices);
            }
            #endregion

            if (isUpdateHistorySettlementPrice == false)
            {
                Log.Debug($"不满足更新条件，直接返回-3。tupleSet={tupleSet.ToJson()}，aliShopId={aliShopId}，所在库：{dbName}", logFileName);
                return;
            }

            var upProductCodes = fxProducts.Select(a => a.ProductCode).ToList();
            Log.Debug($"upProductCodes={upProductCodes.ToJson()}，tupleSet={tupleSet.ToJson()}，aliShopId={aliShopId}，所在库：{dbName}", logFileName);

            #region 2、查当前库的DistributorProductSkuMapping
            var dpsmService = new DistributorProductSkuMappingService();
            var queryModel = new DPSkuMappingQueryModel()
            {
                Codes = upProductCodes,
                ConditionField = "UpProductCode",
                Status = 1,
                Fields = new List<string> { "UpFxUserId", "UpProductCode", "UpSkuCode", "DownFxUserId", "DownProductCode", "DownSkuCode", "DownPlatformType" }
            };
            var dpsmList = dpsmService.GetListByCodes(queryModel);
            if (dpsmList == null || dpsmList.Any() == false)
            {
                Log.Debug($"查无相关的DistributorProductSkuMapping数据，直接返回。upProductCodes={upProductCodes.ToJson()}，所在库：{dbName}", logFileName);
                return;
            }
            #endregion

            #region 3、根据当前所在云，只取和当前云相关的数据
            if (curCloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                var notInAlibabaPlatformTypes = new List<string>()
                {
                    PlatformType.TouTiao.ToString(), PlatformType.TouTiaoSaleShop.ToString(),
                    PlatformType.Pinduoduo.ToString(), PlatformType.KuaiTuanTuan.ToString(),
                    PlatformType.Jingdong.ToString()
                };
                dpsmList = dpsmList.Where(a => notInAlibabaPlatformTypes.Contains(a.DownPlatformType) == false).ToList();
            }
            else if (curCloudPlatformType == CloudPlatformType.TouTiao.ToString())
            {
                dpsmList = dpsmList.Where(a => CustomerConfig.FxDouDianCloudPlatformTypes.Contains(a.DownPlatformType)).ToList();
            }
            else if (curCloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
            {
                dpsmList = dpsmList.Where(a => CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(a.DownPlatformType)).ToList();
            }
            else if (curCloudPlatformType == CloudPlatformType.Jingdong.ToString())
            {
                dpsmList = dpsmList.Where(a =>  CustomerConfig.FxJingDongCloudPlatformTypes.Contains(a.DownPlatformType)).ToList();
            }
            if (dpsmList == null || dpsmList.Any() == false)
            {
                Log.Debug($"过滤后查无相关的DistributorProductSkuMapping数据，直接返回。upProductCodes={upProductCodes.ToJson()}，所在库：{dbName}", logFileName);
                return;
            }
            #endregion

            #region 4、等级分销价换算

            Dictionary<int, RuleModel> memberLevelDict = null;
            var finalPriceRule = FinalDistributePriceCorrectRule.RoundToTwoDecimals;
            // 开启会员等级结算价开关
            if (tupleSet.Item5 == "true" || tupleSet.Item5 == "1")
            {
                // 获取所有的下游（商家）用户ID
                var downFxUserIds = dpsmList.Select(a => a.DownFxUserId).Distinct().ToList();
                memberLevelDict = new MemberLevelService().GetMemberLevelList(downFxUserIds, curFxUserId);
                finalPriceRule = MemberLevelService.FinalPriceRule(0, systemShopId);
            }

            #endregion
            
            #region 5、生成结算价list
            var productSettlementPrices = new List<ProductSettlementPrice>();
            dpsmList.ForEach(dpsm =>
            {
                var existSku = fxProducts.FirstOrDefault(a => a.ProductCode == dpsm.UpProductCode)?.Skus
                    .FirstOrDefault(a => a.SkuCode == dpsm.UpSkuCode);
                if (existSku == null) return;

                existSku.PriceFields = priceFields;
                var lastSettlementPrice = existSku.LastSettlementPrice ?? 0;
                if (lastSettlementPrice <= 0) return;

                // 获取下游商家的会员等级结算价
                if (memberLevelDict != null && memberLevelDict.TryGetValue(dpsm.DownFxUserId, out var value) && value != null)
                {
                    var (newPrice, isChanged) =
                        MemberLevelService.DistributePriceChange(lastSettlementPrice, value, finalPriceRule);
                    dpsm.MemberSettlementPrice = Tuple.Create(newPrice ?? 0, isChanged);
                }

                // 是否使用会员等级结算价
                var memberSettlementPrice = dpsm.MemberSettlementPrice?.Item2 == true
                    ? dpsm.MemberSettlementPrice?.Item1 ?? 0
                    : dpsm.LastSettlementPrice;

                productSettlementPrices.Add(new ProductSettlementPrice
                {
                    SettlementType = 2,
                    CreateTime = dtNow,
                    CreateUser = dpsm.UpFxUserId,
                    FxUserId = dpsm.DownFxUserId,
                    ProductCode = dpsm.DownProductCode,
                    ProductSkuCode = dpsm.DownSkuCode,
                    Price = memberSettlementPrice > 0 ? memberSettlementPrice : lastSettlementPrice,
                    PlatformType = dpsm.DownPlatformType,
                    IsMemberLevelPrice = dpsm.MemberSettlementPrice?.Item2 == true
                });
            });

            Log.Debug(() => $"productSettlementPrices={productSettlementPrices.ToJson()}。tupleSet={tupleSet.ToJson()}，aliShopId={aliShopId}，所在库：{dbName}", logFileName);

            var logList = productSettlementPrices.Select(p => new UpdatePurchaseSettlementPriceModel { PlatformId = p.ProductSkuCode, UserId = p.FxUserId, ShopId = (p.Price * 100).ToInt() }).ToList();
            WriteAliLog(logList, "更新代销品采购结算价-结算价List", batchNo);
            #endregion

            #region 6、对相关业务库处理：已存在更新，不存在新增
            if (curCloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                var agentFxUserIds = productSettlementPrices.Select(a => a.FxUserId).Distinct().ToList();
                agentFxUserIds.Add(curFxUserId);
                var dbConfigModels = new DbConfigRepository().GetListByFxUserIds(agentFxUserIds);
                //逐个库处理
                dbConfigModels.GroupBy(g => g.ConnectionString).ToList().ForEach(g =>
                {
                    var curFxUserIds = g.Select(a => a.DbConfig.UserId).Distinct().ToList();
                    var curList = productSettlementPrices.Where(a => curFxUserIds.Contains(a.FxUserId) || curFxUserIds.Contains(a.CreateUser)).ToList();
                    var uniqueKeys = curList.Select(m => m.UniqueKey).ToList();
                    var financialSettlementService = new FinancialSettlementService(g.Key);
                    financialSettlementService.SetProductSettlementPrice(curList, curFxUserId, needSetIdFromDb: true, isNeedCopyData: false);

                    var curLogList = curList.Select(p => new UpdatePurchaseSettlementPriceModel { PlatformId = p.ProductSkuCode, UserId = p.FxUserId, ShopId = (p.Price * 100).ToInt() }).ToList();
                    WriteAliLog(curLogList, $"更新代销品采购结算价-更新操作-{g.First().DbNameConfig.DbName}", batchNo);
                });
            }
            else
            {
                //非精选云只要更新当前库(内部已带副本更新逻辑)
                var financialSettlementService = new FinancialSettlementService(_connectionString);
                financialSettlementService.SetProductSettlementPrice(productSettlementPrices, curFxUserId, needSetIdFromDb: true);

                WriteAliLog(logList, $"更新代销品采购结算价-更新操作", batchNo);
            }
            #endregion
        }

        /// <summary>
        /// 获取结算价相关配置
        /// </summary>
        /// <param name="systemShopId"></param>
        /// <returns>ShopBy1688,IsUpdateSettlementPriceSwitch，IsUpdateHistorySettlementPrice，DefaultSettlementPriceFields</returns>
        public Tuple<int, bool, bool, string, string> GetSettlementPriceSetting(int systemShopId)
        {
            var keys = new List<string>
            {
                BusinessSettingKeys.SupplyBy1688.ShopBy1688,
                BusinessSettingKeys.SupplyBy1688.IsUpdateSettlementPriceSwitch,
                BusinessSettingKeys.SupplyBy1688.IsUpdateHistorySettlementPrice,
                BusinessSettingKeys.SupplyBy1688.DefaultSettlementPriceFields,
                BusinessSettingKeys.SupplyBy1688.MemberSettlementPriceSwitch
            };
            var settings = new CommonSettingService().GetSets(keys, systemShopId);
            var aliShopId = settings.FirstOrDefault(a => a.Key == BusinessSettingKeys.SupplyBy1688.ShopBy1688)?.Value.ToInt() ?? 0;
            var strIsUpdateSettlementPriceSwitch = settings.FirstOrDefault(a => a.Key == BusinessSettingKeys.SupplyBy1688.IsUpdateSettlementPriceSwitch)?.Value ?? "true";//默认为开启
            var isUpdateSettlementPriceSwitch = strIsUpdateSettlementPriceSwitch == "1" || strIsUpdateSettlementPriceSwitch == "true";
            var strIsUpdateHistorySettlementPrice = settings.FirstOrDefault(a => a.Key == BusinessSettingKeys.SupplyBy1688.IsUpdateHistorySettlementPrice)?.Value ?? "";
            var isUpdateHistorySettlementPrice = strIsUpdateHistorySettlementPrice == "1" || strIsUpdateHistorySettlementPrice == "true";
            //是否开启会员等级结算价
            var memberSettlementPriceSwitch = settings.FirstOrDefault(a => a.Key == BusinessSettingKeys.SupplyBy1688.MemberSettlementPriceSwitch)?.Value ?? "false";

            var priceFields = settings.FirstOrDefault(a => a.Key == BusinessSettingKeys.SupplyBy1688.DefaultSettlementPriceFields)?.Value ?? "";

            return Tuple.Create(aliShopId, isUpdateSettlementPriceSwitch, isUpdateHistorySettlementPrice, priceFields, memberSettlementPriceSwitch);
        }

        /// <summary>
        /// 更新代销品采购结算价：获取设置配置及商品数据
        /// </summary>
        /// <param name="models"></param>
        /// <param name="tag"></param>
        public List<ProductFx> GetProductsForPurchaseSettlementPrice(List<UpdatePurchaseSettlementPriceModel> models, out Tuple<int, bool, bool, string, string> tupleSet, out string shopPlatformType, string tag = "")
        {
            tupleSet = null;
            shopPlatformType = string.Empty;
            if (models == null || models.Any() == false)
                return null;

            var batchNo = models.First().PlatformId;
            WriteAliLog(models, "更新代销品采购结算价-初始", batchNo);
            var logFileName = $"1688-UpdatePurchaseSettlementPrice-{DateTime.Now.ToString("yyyyMMdd")}.txt";
            var dbName = _repository.DbConnection.Database;
            var curFxUserId = BaseSiteContext.CurrentNoThrow?.CurrentFxUserId ?? 0;
            var systemShopId = BaseSiteContext.CurrentNoThrow?.CurrentShopId ?? 0;
            var curCloudPlatformType = CustomerConfig.CloudPlatformType;

            if (curFxUserId <= 0 || systemShopId <= 0)
            {
                Log.Debug($"Error.当前用户Id或systemShopId<=0，所在库：{dbName}", logFileName);
                return null;
            }

            var shopIds = models.Select(a => a.ShopId).Distinct().ToList();

            tupleSet = GetSettlementPriceSetting(systemShopId);
            var aliShopId = tupleSet.Item1;
            var isUpdateSettlementPriceSwitch = tupleSet.Item2;
            var isUpdateHistorySettlementPrice = tupleSet.Item3;
            var priceFields = tupleSet.Item4;

            Log.Debug($"Step1.models={models.ToJson()}，所在库：{dbName}，tupleSet={tupleSet.ToJson()}", logFileName);

            //更新开关为未开启，直接返回
            if (isUpdateSettlementPriceSwitch == false || aliShopId <= 0 || shopIds.Any(a => a == aliShopId) == false)
            {
                Log.Debug($"不满足更新条件，直接返回-1。tupleSet={tupleSet.ToJson()}，aliShopId={aliShopId}，所在库：{dbName}", logFileName);
                return null;
            }

            if (isUpdateHistorySettlementPrice == false && curCloudPlatformType != CloudPlatformType.Alibaba.ToString())
            {
                Log.Debug($"不满足更新条件，直接返回-2。tupleSet={tupleSet.ToJson()}，aliShopId={aliShopId}，所在库：{dbName}", logFileName);
                return null;
            }

            var needModels = models.Where(a => a.ShopId == aliShopId).ToList();

            var shopService = new ShopService();
            var shop = shopService.GetShopAndShopExtension(aliShopId);
            if (shop == null)
                return null;

            shopPlatformType = shop.PlatformType;

            #region 1、通过商品接口获取最新数据
            var products = new ConcurrentBag<Product>();

            if (curCloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
            {
                var priceSkus = models.Where(a => a.Skus != null).SelectMany(a => a.Skus).ToList();
                if (priceSkus == null || priceSkus.Any() == false)
                {
                    Log.WriteLine($"不满足更新条件，直接返回-3。Pinduoduo-消息中Skus为空，aliShopId={aliShopId}，所在库：{dbName}", logFileName);
                    return null;
                }
                //构造出商品
                models.ForEach(p =>
                {
                    var product = new Product
                    {
                        PlatformId = p.PlatformId,
                        ShopId = p.ShopId
                    };
                    p.Skus?.ForEach(s =>
                    {
                        product.Skus.Add(new ProductSku
                        {
                            SkuId = s.SkuId,
                            PriceJson = s.PriceJson,
                        });
                    });
                    products.Add(product);
                });
            }
            else
            {
                var platformService = PlatformFactory.GetPlatformService(shop);
                var curModels = needModels.Where(a => a.ShopId == shop.Id).ToList();
                Parallel.ForEach(curModels, new ParallelOptions { MaxDegreeOfParallelism = 5 }, model =>
                {
                    try
                    {
                        var product = platformService.SyncProduct(model.PlatformId);
                        if (product != null)
                            products.Add(product);
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"UpdatePurchaseSettlementPrice时发生错误：ShopId：{shop.Id},商品ID：{model.PlatformId}，所在库：{dbName}，错误详情：{ex}", logFileName);
                        // 241224肖：消费程序同步商品时可能出现授权问题，如果异常，直接用model中的数据
                        var product = new Product
                        {
                            PlatformId = model.PlatformId,
                            ShopId = model.ShopId
                        };
                        model.Skus?.ForEach(s =>
                        {
                            product.Skus.Add(new ProductSku
                            {
                                SkuId = s.SkuId,
                                PriceJson = s.PriceJson,
                            });
                        });
                        products.Add(product);
                    }
                });
            }

            if (products == null || products.Any() == false)
            {
                Log.WriteLine($"查询商品数据为空，直接返回-4。aliShopId={aliShopId}，所在库：{dbName}", logFileName);
                return null;
            }
            Log.WriteLine($"Step2.products={products.Select(a => a.PlatformId).ToJson()}，所在库：{dbName}", logFileName);

            var fxProducts = TransferModelToProductFx(products.ToList(), curFxUserId, shop.PlatformType);
            #endregion

            return fxProducts;
        }

        public List<ProductFx> GetByPlatformIdList(List<string> pids)
        {
            return _repository.GetByPlatformIdList(pids);
        }

        /// <summary>
        /// 更新指定的单人单品价
        /// </summary>
        /// <param name="downProdcutCodes"></param>
        /// <param name="upProductCode"></param>
        /// <param name="tag"></param>
        public Tuple<bool, string> UpdatePurchaseSettlementPriceByManual(List<string> downProdcutCodes, string upProductCode)
        {
            if (downProdcutCodes == null || downProdcutCodes.Any() == false || upProductCode.IsNullOrEmpty())
                return Tuple.Create(false, "数据为空");

            var logFileName = $"1688-UpdatePurchaseSettlementPriceBy-{DateTime.Now.ToString("yyyyMMdd")}.txt";
            var dbName = _repository.DbConnection.Database;
            var curFxUserId = BaseSiteContext.CurrentNoThrow?.CurrentFxUserId ?? 0;
            var systemShopId = BaseSiteContext.CurrentNoThrow?.CurrentShopId ?? 0;

            if (curFxUserId <= 0 || systemShopId <= 0)
            {
                Log.Debug($"Error.当前用户Id或systemShopId<=0，所在库：{dbName}", logFileName);
                return Tuple.Create(false, "数据错误");
            }

            var tupleSet = GetSettlementPriceSetting(systemShopId);
            var aliShopId = tupleSet.Item1;
            var priceFields = tupleSet.Item4;

            Log.Debug($"Step1.所在库：{dbName}，tupleSet={tupleSet.ToJson()}", logFileName);

            var shopService = new ShopService();
            var shop = shopService.GetShopAndShopExtension(aliShopId);
            if (shop == null)
                return Tuple.Create(false, "获取下单店铺错误");

            #region 1、查当前库的DistributorProductSkuMapping
            var dpsMapService = new DistributorProductSkuMappingService();
            var queryModel = new DPSkuMappingQueryModel
            {
                UpFxUserId = curFxUserId,
                Codes = downProdcutCodes,
                ConditionField = "DownProductCode",
                Status = 1,
                Fields = new List<string> { "UpFxUserId", "UpProductCode", "UpSkuCode", "UpProductId", "DownFxUserId", "DownProductCode", "DownSkuCode", "DownPlatformType" }
            };

            var dpsmList = dpsMapService.GetListByCodes(queryModel).Where(a => a.UpProductCode == upProductCode).ToList();
            if (dpsmList == null || dpsmList.Any() == false)
                return Tuple.Create(true, "查无相关数据");
            #endregion

            #region 2、通过商品接口获取最新数据
            var products = new ConcurrentBag<Product>();
            var platformService = PlatformFactory.GetPlatformService(shop);
            var upProductIds = dpsmList.Select(a => a.UpProductId).Distinct().ToList();
            Parallel.ForEach(upProductIds, new ParallelOptions { MaxDegreeOfParallelism = 5 }, upProductId =>
            {
                try
                {
                    var product = platformService.SyncProduct(upProductId);
                    if (product != null)
                        products.Add(product);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"UpdatePurchaseSettlementPrice时发生错误：ShopId：{shop.Id},商品ID：{upProductId}，所在库：{dbName}，错误详情：{ex}", logFileName);
                }
            });
            var fxProducts = new List<ProductFx>();
            bool isFromApi = true;
            if (products == null || products.Any() == false)
            {
                fxProducts = GetSupplierProductByProductCodes(new List<string> { upProductCode }, new List<int> { curFxUserId });
                isFromApi = false;
                Log.Debug($"通过接口查询商品数据为空，改用直接查询厂家库数据。aliShopId={aliShopId}，所在库：{dbName}", logFileName);
                if (fxProducts == null || fxProducts.Any() == false)
                {
                    Log.Debug($"直接查询厂家库数据，数据依然为空。aliShopId={aliShopId}，所在库：{dbName}", logFileName);
                    return Tuple.Create(false, "查询商品数据异常");
                }
            }
            else
            {
                fxProducts = TransferModelToProductFx(products.ToList(), curFxUserId, shop.PlatformType);
            }
            Log.Debug($"Step2.fxProducts={fxProducts.Select(a => a.PlatformId).ToJson()}，所在库：{dbName}", logFileName);

            #endregion

            var dtNow = DateTime.Now;

            #region 若为精选云：更新ProductSku.DefaultSettlementPrice字段
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString() && isFromApi)
            {
                //更新ProductSku的默认结算价
                var skus = fxProducts.Where(p => p.Skus != null).SelectMany(p => p.Skus).ToList();
                skus.ForEach(s =>
                {
                    s.PriceFields = priceFields;
                    s.DefaultSettlementPrice = null;
                });
                var defautSettlementPrices = skus.Select(sku => new SetDefaultSettlementPriceModel
                {
                    SkuCode = sku.SkuCode,
                    ProductCode = sku.ProductCode,
                    DefaultSettlementPrice = sku.LastSettlementPrice,
                    CreateFxUserId = curFxUserId,
                    CreateTime = dtNow
                }).ToList();

                Log.Debug($"更新代销品默认结算价，defautSettlementPrices={defautSettlementPrices.ToJson()}，priceFields={priceFields}，所在库：{dbName}", logFileName);

                new ProductSkuFxService().SetDefaultSettlementPrices(defautSettlementPrices);
            }
            #endregion

            var upProductCodes = fxProducts.Select(a => a.ProductCode).ToList();

            #region 3、根据当前所在云，只取和当前云相关的数据
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                var notInAlibabaPlatformTypes = new List<string>()
                {
                    PlatformType.TouTiao.ToString(), PlatformType.TouTiaoSaleShop.ToString(), 
                    PlatformType.Pinduoduo.ToString(), PlatformType.KuaiTuanTuan.ToString(), 
                    PlatformType.Jingdong.ToString()
                };
                dpsmList = dpsmList.Where(a => notInAlibabaPlatformTypes.Contains(a.DownPlatformType) == false).ToList();
            }
            else if (CustomerConfig.CloudPlatformType == CloudPlatformType.TouTiao.ToString())
            {
                dpsmList = dpsmList.Where(a => CustomerConfig.FxDouDianCloudPlatformTypes.Contains(a.DownPlatformType)).ToList();
            }
            else if (CustomerConfig.CloudPlatformType == CloudPlatformType.Pinduoduo.ToString())
            {
                dpsmList = dpsmList.Where(a => CustomerConfig.FxPinduoduoCloudPlatformTypes.Contains(a.DownPlatformType)).ToList();
            }
            else if (CustomerConfig.CloudPlatformType == CloudPlatformType.Jingdong.ToString())
            {
                dpsmList = dpsmList.Where(a => CustomerConfig.FxJingDongCloudPlatformTypes.Contains(a.DownPlatformType)).ToList();
            }
            if (dpsmList == null || dpsmList.Any() == false)
            {
                Log.Debug($"过滤后查无相关的DistributorProductSkuMapping数据，直接返回。upProductCodes={upProductCodes.ToJson()}，所在库：{dbName}", logFileName);
                return Tuple.Create(true, "");
            }
            #endregion

            #region 4、等级分销价换算

            Dictionary<int, RuleModel> memberLevelDict = null;
            var finalPriceRule = FinalDistributePriceCorrectRule.RoundToTwoDecimals;
            // 开启会员等级结算价开关
            if (tupleSet.Item5 == "true" || tupleSet.Item5 == "1")
            {
                // 获取所有的下游（商家）用户ID
                var downFxUserIds = dpsmList.Select(a => a.DownFxUserId).Distinct().ToList();
                memberLevelDict = new MemberLevelService().GetMemberLevelList(downFxUserIds, curFxUserId);
                finalPriceRule = MemberLevelService.FinalPriceRule(0, systemShopId);
            }

            #endregion
            
            #region 5、生成结算价list
            var productSettlementPrices = new List<ProductSettlementPrice>();
            dpsmList.ForEach(dpsm =>
            {
                var existSku = fxProducts.FirstOrDefault(a => a.ProductCode == dpsm.UpProductCode)?.Skus
                    .FirstOrDefault(a => a.SkuCode == dpsm.UpSkuCode);
                if (existSku == null) return;

                existSku.PriceFields = priceFields;
                var lastSettlementPrice = existSku.LastSettlementPrice ?? 0;
                if (lastSettlementPrice <= 0) return;

                // 获取下游商家的会员等级结算价
                if (memberLevelDict != null && memberLevelDict.TryGetValue(dpsm.DownFxUserId, out var value) && value != null)
                {
                    var (newPrice, isChanged) =
                        MemberLevelService.DistributePriceChange(lastSettlementPrice, value, finalPriceRule);
                    dpsm.MemberSettlementPrice = Tuple.Create(newPrice ?? 0, isChanged);
                }

                // 是否使用会员等级结算价
                var memberSettlementPrice = dpsm.MemberSettlementPrice?.Item2 == true
                    ? dpsm.MemberSettlementPrice?.Item1 ?? 0
                    : dpsm.LastSettlementPrice;

                productSettlementPrices.Add(new ProductSettlementPrice
                {
                    SettlementType = 2,
                    CreateTime = dtNow,
                    CreateUser = dpsm.UpFxUserId,
                    FxUserId = dpsm.DownFxUserId,
                    ProductCode = dpsm.DownProductCode,
                    ProductSkuCode = dpsm.DownSkuCode,
                    Price = memberSettlementPrice > 0 ? memberSettlementPrice : lastSettlementPrice,
                    PlatformType = dpsm.DownPlatformType,
                    IsMemberLevelPrice = dpsm.MemberSettlementPrice?.Item2 == true
                });
            });
      
            Log.Debug(() => $"productSettlementPrices={productSettlementPrices.ToJson()}。tupleSet={tupleSet.ToJson()}，aliShopId={aliShopId}，所在库：{dbName}", logFileName);

            #endregion

            #region 6、对相关业务库处理：已存在更新，不存在新增
            if (CustomerConfig.CloudPlatformType == CloudPlatformType.Alibaba.ToString())
            {
                //厂家
                new FinancialSettlementService().SetProductSettlementPrice(productSettlementPrices, curFxUserId,
                    needSetIdFromDb: true, isNeedCopyData: false);
                //商家
                var agentFxUserIds = productSettlementPrices.Select(a => a.FxUserId).Distinct().ToList();
                var dbConfigModels = new DbConfigRepository().GetListByFxUserIds(agentFxUserIds);
                //逐个库处理
                dbConfigModels.GroupBy(g => g.ConnectionString).ToList().ForEach(g =>
                {
                    var curFxUserIds = g.Select(a => a.DbConfig.UserId).Distinct().ToList();
                    var curList = productSettlementPrices.Where(a => curFxUserIds.Contains(a.FxUserId)).ToList();
                    var uniqueKeys = curList.Select(m => m.UniqueKey).ToList();
                    var financialSettlementService = new FinancialSettlementService(g.Key);
                    financialSettlementService.SetProductSettlementPrice(curList, curFxUserId, needSetIdFromDb: true);
                });
            }
            else
            {
                //非精选云只要更新当前库(内部已带副本更新逻辑)
                var financialSettlementService = new FinancialSettlementService();
                financialSettlementService.SetProductSettlementPrice(productSettlementPrices, curFxUserId, needSetIdFromDb: true);
            }
            #endregion

            return Tuple.Create(true, "");
        }

        /// <summary>
        /// 设置库存商品名称
        /// </summary>
        /// <param name="logicOrderItemStocks"></param>
        public void SetLogicOrderItemStocksProductName(List<LogicOrderItemStock> logicOrderItemStocks)
        {
            _repository.SetLogicOrderItemStocksProductName(logicOrderItemStocks);
        }

        public void RepirProduct(Action<string> action)
        {
            var filter = $" AND dn.RunningStatus='Running' AND dn.DbPlatformType IN ('toutiao') AND dn.DbName Like 'doudian_fendan%'";

            Log.Debug($"filter==>{filter}");

            DbPolicyExtension.ParellelForeachAllDbs(db =>
            {

                #region 查询SQL

                var sql = $@"															  
SELECT * INTO #tmp3 FROM (														  
SELECT 567493 AS FxUserId,3594355 AS ShopId  ) AS t

SELECT p.* FROM #tmp3 t3
inner join Product p(nolock) on t3.shopid=p.ShopId AND t3.FxUserId!=p.SourceUserId";


                #endregion

                var pList = db.Query<ProductFx>(sql).ToList();
                if (pList.Any())
                {
                    var sids = pList.Select(x => x.ShopId).Distinct().ToList();
                    Log.WriteLine($"{db.ToJson()}\n 异常店铺：{sids.ToJson()}");

                    var cofigDb = new ShopRepository().DbConnection;
                    var groups = pList.GroupBy(x => x.ShopId).ToList();
                    foreach (var g in groups)
                    {
                        var pcodes = g.Select(x => x.ProductCode).Distinct().ToList();
                        var sid = g.Key;


                        // 更新异常商品信息
                        var updateSql = $@"UPDATE p SET p.ShopId=-ABS(p.ShopId),p.SourceUserId=-ABS(p.SourceUserId),p.PlatformId='-'+p.PlatformId,p.ProductCode='-'+p.ProductCode FROM Product p(NOLOCK) 
INNER JOIN dbo.FunStringToTableV2(@Codes,',') t ON p.ProductCode=t.item

UPDATE s SET s.PlatformId='-'+s.PlatformId,s.SkuId='-'+s.SkuId,s.ProductCode='-'+s.ProductCode,s.SkuCode='-'+s.SkuCode FROM ProductSku s(NOLOCK) 
INNER JOIN dbo.FunStringToTableV2(@Codes,',') t ON s.ProductCode=t.item";

                        Log.WriteLine($"异常店铺：{sid}\n 异常商品：{pcodes.ToJson()}，UpdateSQL==> {updateSql}");
                        action($"异常店铺：{sid}\n 异常商品：{pcodes.ToJson()}");
                        db.ExecuteScalar(updateSql, new { Codes = string.Join(",", pcodes) });
                        //break;

                        // 重置商家店铺同步商品
                        var fxUserShopSql = $"SELECT * FROM dbo.P_FxUserShop {WithNoLockSql} WHERE ShopId=@sid";
                        var fxUserShop = cofigDb.Query<FxUserShop>(fxUserShopSql, new { sid }).FirstOrDefault();
                        var fxUserId = fxUserShop?.FxUserId ?? 0;
                        if (fxUserId == 0)
                            continue;

                        var shop = cofigDb.Query<Shop>($"SELECT * FROM P_Shop {WithNoLockSql} WHERE Id=@sid", new { sid }).FirstOrDefault();
                        if (shop == null)
                            continue;

                        var updateSyncstatusSql = $"UPDATE dbo.P_SyncStatus SET LastSyncTime=NULL,StartSyncTime=NULL WHERE ShopId==@sid AND SyncType=2 AND Source='fendansystem'";
                        cofigDb.Execute(updateSyncstatusSql, new { sid });
                        new SyncFxProductService(fxUserId).SyncProduct(shop);
                    }

                }


            }, filter, cpt: null);
        }


        #region 生成货源(基础资料相关)

        /// <summary>
        /// 线上拉取基础商品合成
        /// </summary>
        /// <param name="baseProduct"></param>
        /// <param name="syncProduct"></param>
        public BaseProductSkuAddModel MergeOnline(Product syncProduct, ProductFx dataBaseProduct, string productCode, int fxUserId, List<PathFlowReferenceConfigModel> configs)
        {
            var baseProduct = new BaseProductSkuAddModel();
            baseProduct.SpuCode = Guid.NewGuid().ToString().ToShortMd5();
            baseProduct.Subject = syncProduct.Subject;
            baseProduct.ShortTitle = dataBaseProduct.ShortTitle;
            baseProduct.CreateFrom = "Product";
            baseProduct.FromProductUid = productCode;
            baseProduct.FromFxUserId = dataBaseProduct.SourceUserId;
            baseProduct.FromShopId = dataBaseProduct.ShopId;
            baseProduct.FromShopPt = dataBaseProduct.PlatformType;
            baseProduct.ProductImagesStr = new List<string>();
            baseProduct.DescriptionStr = new List<string>();
            baseProduct.ProductSkus = new List<BaseProductSkuModel>();

            if (syncProduct.ImageUrls != null && syncProduct.ImageUrls.Count > 0)
            {
                baseProduct.ProductImagesStr = syncProduct.ImageUrls
                    .Where(a => a.IsNotNullOrEmpty())
                    .ToList();
            }
            if (syncProduct.DetailImageUrls != null && syncProduct.DetailImageUrls.Count > 0)
            {
                baseProduct.DescriptionStr = syncProduct.DetailImageUrls
                    .Where(a => a.IsNotNullOrEmpty())
                    .ToList();
            }
            if (syncProduct.Skus != null && syncProduct.Skus.Count > 0)
            {
                foreach (var sku in syncProduct.Skus)
                {
                    var skuCode = CustomerConfig.GetFxSkuCode(syncProduct.PlatformId, sku.SkuId, syncProduct.ShopId, fxUserId);
                    var dataBaseSku = dataBaseProduct.Skus.FirstOrDefault(a => a.SkuCode == skuCode);
                    // 路径流中不存在的要过滤
                    if (dataBaseSku == null)
                    {
                        Log.WriteLine($"当前路径流中不存在该SkuCode：{skuCode}");
                        continue;
                    }
                    BaseProductSkuModel skuModel = new BaseProductSkuModel();
                    //skuModel.SkuCode = Guid.NewGuid().ToString().ToShortMd5();
                    //skuModel.SkuCode = dataBaseSku.CargoNumber;
                    skuModel.SkuCode = sku.CargoNumber;
                    //skuModel.ShortTitle = sku.ProductSkuAttr?.ShortTitle;
                    skuModel.ShortTitle = dataBaseSku?.ShortTitle;
                    skuModel.Subject = null;
                    skuModel.CostPrice = dataBaseSku?.DefaultSettlementPrice;
                    skuModel.SettlePrice = dataBaseSku?.PurchasePrice;
                    skuModel.DistributePrice = dataBaseSku?.SalePrice;
                    //skuModel.Weight = sku.ProductSkuAttr?.Weight == null ? 0 : Convert.ToInt32(sku.ProductSkuAttr?.Weight);
                    skuModel.Weight = dataBaseSku?.Weight.ToInt();
                    skuModel.IsCombineSku = false;
                    skuModel.ImageUrlStr = sku.ProductSkuAttr?.SkuImgUrl == null ? dataBaseSku?.ImgUrl : sku.ProductSkuAttr?.SkuImgUrl;
                    skuModel.BaseProductSkuSupplierConfig = new List<BaseProductSkuSupplierConfigModel>();

                    // 取类型的前三组
                    var attributeGroup = sku.SkuAttrs.GroupBy(a => a.k).ToList();
                    var attributeGroup1 = attributeGroup.Skip(0).Take(1).ToList().FirstOrDefault();
                    var attributeGroup2 = attributeGroup.Skip(1).Take(1).ToList().FirstOrDefault();
                    var attributeGroup3 = attributeGroup.Skip(2).Take(1).ToList().FirstOrDefault();
                    skuModel.Attribute = new BaseProductSkuAttributeModel()
                    {
                        ImgAttributeValueNo = 1,
                        AttributeName1 = attributeGroup1?.Key,
                        AttributeName2 = attributeGroup2?.Key,
                        AttributeName3 = attributeGroup3?.Key,
                        AttributeValue1 = attributeGroup1?.ToList().FirstOrDefault()?.v,
                        AttributeValue2 = attributeGroup2?.ToList().FirstOrDefault()?.v,
                        AttributeValue3 = attributeGroup3?.ToList().FirstOrDefault()?.v,
                    };
                    if (configs != null && configs.Count > 0)
                    {
                        // 默认厂家获取
                        var defaultProConfig = configs.FirstOrDefault(a => a.ConfigType == 0 && a.PathFlowRefCode == productCode);
                        var defaultSkuConfig = configs.FirstOrDefault(a => a.ConfigType == 0 && a.PathFlowRefCode == skuCode);
                        if (defaultSkuConfig != null)
                        {
                            var configModel = new BaseProductSkuSupplierConfigModel();
                            configModel.ApplyScope = 0;
                            configModel.SupplierFxUserId = defaultSkuConfig.SupplierId;
                            configModel.Config = defaultSkuConfig.Config;
                            configModel.ConfigType = defaultProConfig.ConfigType;
                            configModel.IsSelf = defaultProConfig.IsSelf;
                            skuModel.BaseProductSkuSupplierConfig.Add(configModel);
                        }
                        else
                        {
                            if (defaultProConfig != null)
                            {
                                var configModel = new BaseProductSkuSupplierConfigModel();
                                configModel.ApplyScope = 0;
                                configModel.SupplierFxUserId = defaultProConfig.SupplierId;
                                configModel.Config = defaultProConfig.Config;
                                configModel.ConfigType = defaultProConfig.ConfigType;
                                configModel.IsSelf = defaultProConfig.IsSelf;
                                skuModel.BaseProductSkuSupplierConfig.Add(configModel);
                            }
                        }
                    }
                    baseProduct.ProductSkus.Add(skuModel);
                }
            }
            else
            {
                if (dataBaseProduct != null && dataBaseProduct.Skus?.Count > 0)
                {
                    foreach (var sku in dataBaseProduct.Skus)
                    {
                        //if (string.IsNullOrEmpty(sku.AttributeValue3))
                        //{
                        //    Log.WriteError($"店铺商品SKU;{sku.SkuCode}转换基础资料未获取到规格数据！");
                        //    continue;
                        //}
                        BaseProductSkuModel skuModel = new BaseProductSkuModel();
                        //skuModel.SkuCode = Guid.NewGuid().ToString().ToShortMd5();
                        skuModel.SkuCode = sku.CargoNumber;
                        skuModel.ShortTitle = sku.ShortTitle;
                        skuModel.Subject = null;
                        skuModel.CostPrice = sku?.DefaultSettlementPrice;
                        skuModel.SettlePrice = sku?.PurchasePrice;
                        skuModel.DistributePrice = sku?.SalePrice;

                        skuModel.Weight = sku?.Weight.ToInt();
                        skuModel.IsCombineSku = false;
                        skuModel.ImageUrlStr = sku?.ImgUrl;

                        var attrs = sku.AttributeValue3?.ToObject<List<SkuAttr>>();
                        var attributeGroup = attrs?.GroupBy(a => a.k).ToList();
                        var attributeGroup1 = attributeGroup?.ElementAtOrDefault(0);
                        var attributeGroup2 = attributeGroup?.ElementAtOrDefault(1);
                        var attributeGroup3 = attributeGroup?.ElementAtOrDefault(2);

                        skuModel.Attribute = new BaseProductSkuAttributeModel()
                        {
                            ImgAttributeValueNo = 1,
                            AttributeName1 = attributeGroup1?.Key,
                            AttributeName2 = attributeGroup2?.Key,
                            AttributeName3 = attributeGroup3?.Key,
                            AttributeValue1 = attributeGroup1?.ToList().FirstOrDefault()?.v,
                            AttributeValue2 = attributeGroup2?.ToList().FirstOrDefault()?.v,
                            AttributeValue3 = attributeGroup3?.ToList().FirstOrDefault()?.v,
                        };
                        if (skuModel.Attribute.AttributeValue1.IsNullOrEmpty()
                        && skuModel.Attribute.AttributeValue2.IsNullOrEmpty()
                        && skuModel.Attribute.AttributeValue3.IsNullOrEmpty())
                        {
                            skuModel.Attribute.AttributeValue1 = dataBaseProduct.Subject;
                            skuModel.Attribute.AttributeName1 = "无规格";
                        }
                        skuModel.BaseProductSkuSupplierConfig = new List<BaseProductSkuSupplierConfigModel>();
                        if (configs != null && configs.Count > 0)
                        {
                            // 默认厂家获取
                            var defaultProConfig = configs.FirstOrDefault(a => a.ConfigType == 0 && a.PathFlowRefCode == productCode);
                            var defaultSkuConfig = configs.FirstOrDefault(a => a.ConfigType == 0 && a.PathFlowRefCode == sku.SkuCode);
                            if (defaultSkuConfig != null)
                            {
                                var configModel = new BaseProductSkuSupplierConfigModel();
                                configModel.ApplyScope = 0;
                                configModel.SupplierFxUserId = defaultSkuConfig.SupplierId;
                                configModel.Config = defaultSkuConfig.Config;
                                configModel.ConfigType = defaultProConfig.ConfigType;
                                configModel.IsSelf = defaultProConfig.IsSelf;
                                skuModel.BaseProductSkuSupplierConfig.Add(configModel);
                            }
                            else
                            {
                                if (defaultProConfig != null)
                                {
                                    var configModel = new BaseProductSkuSupplierConfigModel();
                                    configModel.ApplyScope = 0;
                                    configModel.SupplierFxUserId = defaultProConfig.SupplierId;
                                    configModel.Config = defaultProConfig.Config;
                                    configModel.ConfigType = defaultProConfig.ConfigType;
                                    configModel.IsSelf = defaultProConfig.IsSelf;
                                    skuModel.BaseProductSkuSupplierConfig.Add(configModel);
                                }
                            }
                        }
                        baseProduct.ProductSkus.Add(skuModel);
                    }
                }
                else
                {
                    // 当成是自定义规格了
                    baseProduct.SkuModeType = 1;
                    BaseProductSkuModel skuModel = new BaseProductSkuModel();
                    skuModel.SkuCode = Guid.NewGuid().ToString().ToShortMd5();
                    //skuModel.SkuCode = sku.CargoNumber;
                    skuModel.ShortTitle = syncProduct.ShortTitle;
                    skuModel.Subject = syncProduct.Subject;
                    skuModel.CostPrice = syncProduct?.CostPrice;
                    //skuModel.SettlePrice = sku?.PurchasePrice;
                    //skuModel.DistributePrice = sku?.SalePrice;

                    //skuModel.Weight = sku.Weight;
                    skuModel.IsCombineSku = false;
                    //skuModel.ImageUrlStr = sku.ImgUrl;

                    skuModel.Attribute = new BaseProductSkuAttributeModel()
                    {
                        ImgAttributeValueNo = 1,
                        AttributeName1 = "无规格",
                        AttributeValue1 = syncProduct.Subject
                    };

                    skuModel.BaseProductSkuSupplierConfig = new List<BaseProductSkuSupplierConfigModel>();
                    
                    baseProduct.ProductSkus.Add(skuModel);
                }
            }
            if (baseProduct.ProductSkus.Count == 0)
            {
                Log.WriteError($"线上店铺商品：{baseProduct.FromProductUid}转换基础资料未获取到规格数据！");
                throw new Exception($"未获取到规格数据！");
            }
            return baseProduct;
        }

        /// <summary>
        /// 线下拉取基础商品合成
        /// </summary>
        /// <param name="baseProduct"></param>
        /// <param name="syncProduct"></param>
        public BaseProductSkuAddModel Mergeoffline(ProductFx dataBaseProduct, string productCode, int fxUserId, List<PathFlowReferenceConfigModel> configs)
        {
            var baseProduct = new BaseProductSkuAddModel();
            baseProduct.SpuCode = Guid.NewGuid().ToString().ToShortMd5();
            baseProduct.Subject = dataBaseProduct.Subject;
            baseProduct.ShortTitle = dataBaseProduct.ShortTitle;
            baseProduct.CreateFrom = "Product";
            baseProduct.FromProductUid = dataBaseProduct?.ProductCode;
            baseProduct.FromFxUserId = dataBaseProduct.SourceUserId;
            baseProduct.FromShopId = dataBaseProduct.ShopId;
            baseProduct.FromShopPt = dataBaseProduct.PlatformType;

            baseProduct.ProductImagesStr = new List<string>();
            baseProduct.DescriptionStr = new List<string>();
            baseProduct.ProductSkus = new List<BaseProductSkuModel>();

            if (!string.IsNullOrEmpty(dataBaseProduct.ImageUrl))
            {
                baseProduct.ProductImagesStr = dataBaseProduct.ImageUrl
                    .Split(',')
                    .Where(a => a.IsNotNullOrEmpty())
                    .Distinct()
                    .ToList();
            }

            if (dataBaseProduct.Skus != null && dataBaseProduct.Skus.Count > 0)
            {
                // 自定义规格录入
                if (isCustomize(dataBaseProduct))
                {
                    baseProduct.SkuModeType = 1;
                }

                foreach (var sku in dataBaseProduct.Skus)
                {
                    BaseProductSkuModel skuModel = new BaseProductSkuModel();
                    //skuModel.SkuCode = Guid.NewGuid().ToString().ToShortMd5();
                    skuModel.SkuCode = sku.CargoNumber;
                    skuModel.ShortTitle = sku.ShortTitle;
                    skuModel.Subject = null;
                    skuModel.CostPrice = sku?.DefaultSettlementPrice;
                    skuModel.SettlePrice = sku?.PurchasePrice;
                    skuModel.DistributePrice = sku?.SalePrice;

                    skuModel.Weight = sku.Weight.ToInt();
                    skuModel.IsCombineSku = false;
                    skuModel.ImageUrlStr = sku.ImgUrl;
                    
                    var attrs = sku.AttributeValue3?.ToObject<List<SkuAttr>>();
                    var attributeGroup = attrs?.GroupBy(a => a.k).ToList();
                    var attributeGroup1 = attributeGroup?.ElementAtOrDefault(0);
                    var attributeGroup2 = attributeGroup?.ElementAtOrDefault(1);
                    var attributeGroup3 = attributeGroup?.ElementAtOrDefault(2);

                    skuModel.Attribute = new BaseProductSkuAttributeModel()
                    {
                        ImgAttributeValueNo = 1,
                        AttributeName1 = attributeGroup1?.Key,
                        AttributeName2 = attributeGroup2?.Key,
                        AttributeName3 = attributeGroup3?.Key,
                        AttributeValue1 = attributeGroup1?.ToList().FirstOrDefault()?.v,
                        AttributeValue2 = attributeGroup2?.ToList().FirstOrDefault()?.v,
                        AttributeValue3 = attributeGroup3?.ToList().FirstOrDefault()?.v,
                    };

                    if (skuModel.Attribute.AttributeValue1.IsNullOrEmpty()
                           && skuModel.Attribute.AttributeValue2.IsNullOrEmpty()
                           && skuModel.Attribute.AttributeValue3.IsNullOrEmpty())
                    {
                        if(sku.AttributeValue1.IsNullOrEmpty() && sku.AttributeValue2.IsNullOrEmpty())
                        {
                            skuModel.Attribute.AttributeValue1 = dataBaseProduct.Subject;
                        }
                        else
                        {
                            skuModel.Attribute.AttributeValue1 = string.IsNullOrEmpty(sku.AttributeValue1)?sku.AttributeValue2:sku.AttributeValue1;
                        }
                        skuModel.Attribute.AttributeName1 = "无规格";
                    }
                    skuModel.BaseProductSkuSupplierConfig = new List<BaseProductSkuSupplierConfigModel>();
                    if (configs != null && configs.Count > 0)
                    {
                        // 默认厂家获取
                        var defaultProConfig = configs.FirstOrDefault(a => a.ConfigType == 0 && a.PathFlowRefCode == productCode);
                        var defaultSkuConfig = configs.FirstOrDefault(a => a.ConfigType == 0 && a.PathFlowRefCode == sku.SkuCode);
                        if (defaultSkuConfig != null)
                        {
                            var configModel = new BaseProductSkuSupplierConfigModel();
                            configModel.ApplyScope = 0;
                            configModel.SupplierFxUserId = defaultSkuConfig.SupplierId;
                            configModel.Config = defaultSkuConfig.Config;
                            configModel.ConfigType = defaultProConfig.ConfigType;
                            configModel.IsSelf = defaultProConfig.IsSelf;
                            skuModel.BaseProductSkuSupplierConfig.Add(configModel);
                        }
                        else
                        {
                            if (defaultProConfig != null)
                            {
                                var configModel = new BaseProductSkuSupplierConfigModel();
                                configModel.ApplyScope = 0;
                                configModel.SupplierFxUserId = defaultProConfig.SupplierId;
                                configModel.Config = defaultProConfig.Config;
                                configModel.ConfigType = defaultProConfig.ConfigType;
                                configModel.IsSelf = defaultProConfig.IsSelf;
                                skuModel.BaseProductSkuSupplierConfig.Add(configModel);
                            }
                        }
                    }
                    baseProduct.ProductSkus.Add(skuModel);
                }
            }
            if (baseProduct.ProductSkus.Count == 0)
            {
                Log.WriteError($"店铺商品SPU;{baseProduct.FromProductUid}转换基础资料未获取到规格数据！");
                throw new Exception($"未获取到规格数据！");
            }
            return baseProduct;
        }

        /// <summary>
        /// 是否自定义规格添加
        /// </summary>
        /// <param name="dataBaseProduct"></param>
        /// <returns></returns>
        public bool isCustomize(ProductFx dataBaseProduct)
        {
            var skus = dataBaseProduct.Skus;

            var isCustomize = false;

            // 商品是线下单商品
            if (dataBaseProduct.PlatformType == PlatformType.Virtual.ToString())
            {
                isCustomize = true;
            }
            // 商品已删除
            else if (dataBaseProduct.SystemStatus == -1 )
            {
                isCustomize = true;
            }
            // 商品已下架
            else if (dataBaseProduct.Status != "published")
            {
                isCustomize = true;
            }
            // 商品存在规格已失效
            else if (dataBaseProduct.Skus.Any(s=>s.SystemStatus == -1))
            {
                isCustomize = true;
            }
            // 商品存在规格来源于订单
            else if (dataBaseProduct.From == "Order")
            {
                isCustomize = true;
            }
            // 商品存在规格来源于商品
            else if (dataBaseProduct.Skus.Any(s=>s.SkuId==dataBaseProduct.PlatformId))
            {
                isCustomize = true;
            }
            else
            {
                isCustomize = false;
            }
            return isCustomize;
        }
        #endregion

        /// <summary>
        /// 获取默认价格
        /// </summary>
        /// <param name="skuCodes"></param>
        public Tuple<List<ProductSettlementPrice>, List<ProductSettlementPrice>, List<ProductSettlementPrice>> GetDefaultPrices(List<string> skuCodes, int fxUserId)
        {
            var allPrices = _financialSettlementService.GetAllPriceListBySkuCodes(fxUserId, skuCodes);
            var settlemenPriceDict = new List<ProductSettlementPrice>();
            var distributePriceDict = new List<ProductSettlementPrice>();
            var costPriceDict = new List<ProductSettlementPrice>();
            // 采购价格
            settlemenPriceDict = allPrices
                .Where(p => p.SettlementType == FinancialSettlementRepository.SettlementType.Merchant.ToInt())
                .ToList();
            // 分销价格
            distributePriceDict = allPrices
                .Where(p => p.SettlementType == FinancialSettlementRepository.SettlementType.Manufacturer.ToInt())
                 .ToList();
            // 成本价格
            costPriceDict = allPrices
                .Where(p => p.SettlementType == FinancialSettlementRepository.SettlementType.CostPrice.ToInt())
                .ToList();
            return Tuple.Create(settlemenPriceDict, distributePriceDict, costPriceDict);
        }

        /// <summary>
        /// 是否是默认SKU：店铺商品没有sku 会自动生成一条没有规格sku
        /// </summary>
        /// <param name="Skus"></param>
        /// <returns></returns>
        public bool IsDefaultSku(List<ProductSkuFx> skus)
        {
            var isDefault = false;
            var sku = skus.FirstOrDefault();
            var skuCount = skus.Count;
            var skuValue1 = (sku?.AttributeValue1).IsNullOrEmpty();
            var skuValue2 = (sku?.AttributeValue2).IsNullOrEmpty();
            var skuValue3 = (sku?.AttributeValue3).IsNullOrEmpty();
            if (skuCount <= 1)
            {
                if (skuValue1 && skuValue2 && skuValue3)
                    isDefault = true;
                else
                    isDefault = false;
            }
            else
            {
                isDefault = false;
            }
            return isDefault;
        }

        public PageResultApiModel<PlateProductSearchRes> GetProductSpuList(PlateProductSearch model, int fxUserId,bool isAllData = false)
        {
            var tuple = _repository.GetProductSpuList(model, fxUserId, isAllData);
            var products = tuple.Item2;
            var ProductsCount = tuple.Item1;
            var ProuuctsRes = new List<PlateProductSearchRes>();
            if (products.IsNotNullAndAny())
            {
                //补充店铺名
                var shops = new FxUserShopService().GetShopsByFxUserId(fxUserId, false);
                var suppliers = new SupplierUserService().GetSupplierList(fxUserId,needEncryptAccount:true);
                var agents = new SupplierUserService().GetAgentList(fxUserId,needEncryptAccount:true);
                var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");
                var agentList = agents?.GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.AgentMobileAndRemark ?? "");
                //获取Sku路径
                var pathflowService = new PathFlowService(_connectionString);
                var pCodes = products.Select(x => x.ProductCode).Distinct().ToList();
                var fields = "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
                var pathFlows = pathflowService.GetPathFlows(pCodes, 0, fields);

                // 上下游销售价是否可见
                var logicOrderRepository = new LogicOrderRepository(_connectionString);
                var nodes = pathFlows.SelectMany(x => x.PathFlowNodes).ToList();
                var pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(nodes);
                var commonSettingRepository = new CommonSettingRepository();

                //兼容新旧数据补上Config信息
                CompatibleOldDataToAddConfig(pathFlows);
                var productSkus = _productSkuFxRepository.GetAllList(pCodes);
                // 只检查可添加的商品编码
                var needCheckCodes = products.Where(x => x.HasGenerateBaseProduct == false).Select(x => x.ProductCode).Distinct().ToList();
                var allowCodes = CheckIsEnableQuote(fxUserId, needCheckCodes);

                foreach (var product in products)
                {
                    var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(product.ProductCode)).ToList();
                    var skus = productSkus.Where(p => p.ProductCode == product.ProductCode).ToList();
                    var shop = shops.FirstOrDefault(p => p.ShopId == product.ShopId);
                    var refCode = product.ProductCode;
                    var pathRefConfigs =
                        pathFlows.Where(x => x.PathFlowReferences.ContainsKey(refCode))
                        .SelectMany(x => x.PathFlowReferences
                        .SelectMany(y => y.Value.ReferenceConfigs))
                        .Where(x => x.PathFlowRefCode == refCode).ToList();
                    var isNotAllow = allowCodes.Contains(product.ProductCode) == false;

                    var defaultPathRefConfig =
                        pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.ConfigType == 0 && x.DownFxUserId > 0 && x.FxUserId == fxUserId);

                    // 是否商品绑定厂家
                    product.IsCurBindProduct = pathFlows.Any(x => x.PathFlowReferences.ContainsKey(product.ProductCode) && x.PathFlowNodes.Any(y => y.FxUserId == fxUserId));
                    if (product.IsCurBindProduct)
                    {
                        // 商品标题是否可见
                        ConvertShowProductTitle(fxUserId, productPathFlows, pathFlowNodeDic, product, null, commonSettingRepository);

                        // 商品图片是否可见
                        ConvertShowProductImg(fxUserId, productPathFlows, pathFlowNodeDic, product, null, commonSettingRepository);
                    }
                    else
                    {
                        // 不是自营根据规格信息权限（其中一个有满足）确定商品权限
                        if (shop == null)
                        {
                            foreach (var item in skus)
                            {
                                var skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(item.SkuCode)).ToList();
                                //绑定的是SKU，商品信息跟随SKU权限
                                if (skuPathFlows.Any())
                                {
                                    // 商品标题是否可见
                                    ConvertShowProductTitle(fxUserId, skuPathFlows, pathFlowNodeDic, product, null, commonSettingRepository);
                                    ConvertShowProductImg(fxUserId, skuPathFlows, pathFlowNodeDic, product, null, commonSettingRepository);
                                }
                            }
                        }
                    }

                    var data = new PlateProductSearchRes
                    {
                        ShopId = product.ShopId,
                        ShopName = shop?.NickName,
                        Id = product.Id,
                        ImageUrl = product.ImageUrl,
                        PlatformType = product.PlatformType,
                        Subject = product.Subject,
                        CargoNumber = product.CargoNumber,
                        ProductCode = product.ProductCode,
                        HasGenerateBaseProduct = product.HasGenerateBaseProduct,
                        Status = product.Status
                    };
                    string supplerName;
                    if (defaultPathRefConfig != null)
                    {
                        // 默认厂家
                        product.DownFxUserId = defaultPathRefConfig.DownFxUserId;
                        var supplierRes = supplierList.FirstOrDefault(t => t.Key == product.DownFxUserId);
                        supplerName = supplierRes.Value;
                    }
                    else
                    {
                        // 自营
                        product.DownFxUserId = defaultPathRefConfig?.DownFxUserId ?? 0;
                        supplerName = "自营";
                        // 可能是商家的商品
                        var agentRefConfig = pathRefConfigs.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.ConfigType == 0 && x.DownFxUserId == fxUserId && x.FxUserId != fxUserId);
                        if (agentRefConfig != null)
                        {
                            var agentRes = agentList.FirstOrDefault(t => t.Key == agentRefConfig.FxUserId);
                            data.ShopName = agentRes.Value ?? string.Empty;
                        }
                    }
                    data.SupplierUserId = product.DownFxUserId;
                    var sku = skus.FirstOrDefault();
                    var skuCount = skus.Count;
                    // 仅当SKU=0的情况下不能勾选，假如店铺商品没有规格，但是同步到本地会生成默认的规格，但是规格值1/2/3都是没有值，产品说也能勾选
                    var skuValue1 = (sku?.AttributeValue1).IsNullOrEmpty();
                    var skuValue2 = (sku?.AttributeValue2).IsNullOrEmpty();
                    var skuValue3 = (sku?.AttributeValue3).IsNullOrEmpty();
                    if (skuCount <= 1)
                    {
                        data.IsDefault = true;
                        if (skuValue1 && skuValue2 && skuValue3)
                            data.IsDefault = true;
                        else
                            data.IsDefault = false;
                    }
                    else
                    {
                        data.IsDefault = false;
                    }

                    // 存在路径流中的商家不允许引用商品信息
                    if (isNotAllow && data.HasGenerateBaseProduct == false) data.IsAllow = false;
                    ProuuctsRes.Add(data);
                }
            }

            var res = new PageResultApiModel<PlateProductSearchRes>()
            {
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                Rows = ProuuctsRes,
                Total = ProductsCount
            };
            return res;
        }

        /// <summary>
        /// 设置变更状态为false
        /// </summary>
        /// <param name="skuCodes"></param>
        public void SetSkuNameChangedForFalse(List<string> skuCodes)
        {
            if (skuCodes.IsNullOrEmptyList())
                return;
            _repository.SetSkuNameChangedForFalse(skuCodes);
        }

        /// <summary>
        /// 处理商品规格名变更提醒（商品换绑厂家）
        /// </summary>
        /// <param name="perCheck"></param>
        /// <param name="codes"></param>
        /// <param name="pathFlowReferenceChangeModels"></param>
        public void ProcessProductSkuHistoryForBindSupplier(PerCheckBindSupplierResult perCheck, List<KeyValuePair<string, string>> codes, List<PathFlowReferenceChangeModel> pathFlowReferenceChangeModels)
        {
            if (perCheck == null || perCheck.oldPaths == null || perCheck.oldPaths.Any() == false || codes == null || codes.Any() == false)
                return;

            var oldPathFlowReferences = perCheck.oldPaths.Where(a => a.PathFlowReferences != null && a.PathFlowReferences.Values != null).SelectMany(a => a.PathFlowReferences.Values).ToList();
            if (oldPathFlowReferences == null || oldPathFlowReferences.Any() == false)
                return;

            try
            {
                //1.根据解绑数据，区分出哪些是商品维度，哪些是Sku维度
                var productCodes = new List<string>();
                var skuCodes = new List<string>();
                foreach (var kv in codes)
                {
                    var pathFlowRefCode = kv.Value;
                    var productCode = kv.Key;
                    if (pathFlowRefCode == productCode)
                        productCodes.Add(pathFlowRefCode);
                    else
                        skuCodes.Add(pathFlowRefCode);
                }
                //1.1商品维度 转换成Sku维度
                if (productCodes.Any())
                {
                    var skus = _productSkuFxRepository.GetAllList(productCodes);
                    if (skus.IsNullOrEmptyList())
                    {
                        var tempSkuCodes = skus.Select(a => a.SkuCode).ToList();
                        skuCodes.AddRange(tempSkuCodes);
                    }
                }
                var supplierFxUserIds = perCheck.oldPaths.Where(a => a.PathFlowNodes != null).SelectMany(a => a.PathFlowNodes).Where(a => a.DownFxUserId > 0).Select(a => a.DownFxUserId).Distinct().ToList();
                var _messageService = new SiteMessageService();
                supplierFxUserIds.ForEach(t =>
                {
                    //2.是否存在变更提醒
                    var dbConfig = new DbConfigRepository().GetFxDbConfigModel(t, CustomerConfig.CloudPlatformType);
                    var _productSkuHistoryRepository = new ProductSkuHistoryRepository(dbConfig.ConnectionString);
                    var fields = "Id,CreateTime";
                    var existList = _productSkuHistoryRepository.GetListBySkuCodes(skuCodes, t, fields);
                    if (existList.IsNullOrEmptyList())
                        return;
                    //3.针对存在的变更提醒，进行删除
                    var ids = existList.Select(a => a.Id).ToList();
                    _productSkuHistoryRepository.DelteProductSkuHistory(ids);
                    //删除通知
                    var startTime = existList.Min(a => a.CreateTime);
                    var endTime = existList.Max(a => a.CreateTime);
                    //获取站内消息
                    var request = new MessageListRequest()
                    {
                        FirstType = SiteMessageTypeEnum.PrimaryType.SkuChanged.ToString(),
                        PageIndex = 1,
                        PageSize = skuCodes.Count,
                        ReadStatus = 2,
                        CreateTime = $"{startTime},{endTime}",
                        UserId = t,
                    };
                    var response = _messageService.GetMessageList(request);
                    if (response == null || response.Messages.IsNullOrEmptyList())
                    {
                        return;
                    }
                    var readRequest = new MessageSetReadRequest();
                    readRequest.MessagesCodes = response.Messages.Select(x => x.MessageCode).ToList();
                    readRequest.IsAll = false;
                    readRequest.UserId = t;
                    readRequest.CreateTime = DateTime.Now;
                    _messageService.SetMessageReaded(readRequest);
                });
            }
            catch (Exception ex)
            {
                Log.WriteError($"商品换厂家处理商品规格名变更提醒是异常：{ex}，perCheck={perCheck?.ToJson()}，codes={codes?.ToJson()}");
            }
        }

        /// <summary>
        /// 处理商品规格名变更提醒
        /// </summary>
        /// <param name="products"></param>
        /// <param name="fxUserId"></param>
        public void HandleProductSkuHistory(List<ProductFx> products,int fxUserId)
        {
            var skuCodes = products.SelectMany(t => t.Skus.Select(x => x.SkuCode)).ToList();
            var _productSkuHistoryRepository = new ProductSkuHistoryRepository();

            Stopwatch sw = new Stopwatch();

            if (CustomerConfig.IsDebug)
            {
                sw.Start();
            }
            var idAndCodeModelsDic = _productSkuHistoryRepository.GetNewProductSkuHistory(skuCodes, fxUserId).ToDictionary(t => t.Code, t => t.Id);

            if (CustomerConfig.IsDebug)
            {
                sw.Stop();
                Log.Debug(() => $"Step1:HandleProductSkuHistory查询sql耗时：{sw.Elapsed.TotalSeconds}s，skuCodes数量={skuCodes.Count}", "HandleProductSkuHistory.txt");
                sw.Restart();
            }

            Parallel.ForEach(products, new ParallelOptions { MaxDegreeOfParallelism = Environment.ProcessorCount - 1 }, product =>
            {
                product.Skus.ForEach(sku =>
                {
                    if (idAndCodeModelsDic.TryGetValue(sku.SkuCode, out var productSkuHistoryId))
                    {
                        sku.IsSkuNameChanged = true;
                        sku.ProductSkuHistoryId = productSkuHistoryId;
                        product.IsSkuNameChanged = true;
                    }
                });
            });
            if (CustomerConfig.IsDebug)
            {
                sw.Stop();
                Log.Debug(() => $"Step2:HandleProductSkuHistory转换Skus耗时：{sw.Elapsed.TotalSeconds}s，skuCodes数量={skuCodes.Count}", "HandleProductSkuHistory.txt");
                sw.Restart();
            }
        }


        /// <summary>
        /// 获取店铺绑定厂家列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public Tuple<int, List<ShopDefaultSupplierListResponseModel>> GetShopDefaultSupplierList(ShopDefaultSupplierListRequestModel request)
        {
            int total = 0;
            List<ShopDefaultSupplierListResponseModel> dataList = new List<ShopDefaultSupplierListResponseModel>();
			
			var fxUserId = SiteContext.Current.CurrentFxUserId;
            var _supplierUserService = new SupplierUserService();
            var fxUserShopService = new FxUserShopService();

            //店铺
            var _reqModel = new FxUserShopQueryModel
            {
                FxUserId = fxUserId,
                PageIndex = request.PageIndex,
                PageSize = request.PageSize,
                ShopIds = request.ShopIds,
                Status = FxUserShopStatus.Binded,
                PlatformTypes = request.PlatformTypes
            };
            //商品列表打开
            if (request.FromType == 1)
            {
                var platformTypes = new List<string>();
                if (CustomerConfig.CloudPlatformType == PlatformType.Pinduoduo.ToString())
                {
                    platformTypes = CustomerConfig.FxPinduoduoCloudPlatformTypes.ToList();
                    platformTypes.Add(PlatformType.Virtual.ToString());
                }
                else if (CustomerConfig.CloudPlatformType == PlatformType.Jingdong.ToString())
                {
                    platformTypes = CustomerConfig.FxJingDongCloudPlatformTypes.ToList();
                    platformTypes.Add(PlatformType.Virtual.ToString());
                }
                else if (CustomerConfig.CloudPlatformType == PlatformType.TouTiao.ToString())
                {
                    platformTypes = CustomerConfig.FxDouDianCloudPlatformTypes.ToList();
                    platformTypes.Add(PlatformType.Virtual.ToString());
                }
                else
                {
                    _reqModel.IsPlatformTypesNotIn = true;
                    platformTypes = CustomerConfig.FxPinduoduoCloudPlatformTypes.Concat(CustomerConfig.FxJingDongCloudPlatformTypes)
                        .Concat(CustomerConfig.FxDouDianCloudPlatformTypes).ToList();
                }

                _reqModel.PlatformTypes = string.Join(",", platformTypes);
            }

            var fxUserShopTuple = fxUserShopService.GetList(_reqModel);

            total = fxUserShopTuple.Item1;
            if (total <= 0 || fxUserShopTuple.Item2 == null || fxUserShopTuple.Item2.Count <= 0)
            {
                return Tuple.Create(total, dataList);
            }

            List<FxUserShop> fxUserShops = fxUserShopTuple.Item2;
        

            //查询绑定厂家表
            var shopDefaultSupplierList = new FxUserShopDefaultSupplierService().GetDefaultSupplierListByShopIds(fxUserShops.Select(s => s.ShopId).ToList(),needEncryptAccount:true);

            //解析数据

            foreach (var item in fxUserShops)
            {
                ShopDefaultSupplierListResponseModel model = new ShopDefaultSupplierListResponseModel();
                model.ShopId = item.ShopId;
                model.ShopName = item.NickName;
                model.PlatformTypeName = item.PlatformTypeName;
                //厂家信息
                var shopDefaultSupplierModel = shopDefaultSupplierList.FirstOrDefault(f => f.ShopId == item.ShopId && f.FxUserId == fxUserId);
                if (shopDefaultSupplierModel != null)
                {
                    model.SupplierType = 1;
                    model.ShopDefaultSupplierId = shopDefaultSupplierModel.Id;
                    model.SupplierName = shopDefaultSupplierModel.Mobile;
                    model.SupplierFxUserId = shopDefaultSupplierModel.SupplierFxUserId;
                    if (!string.IsNullOrWhiteSpace(shopDefaultSupplierModel.NickName))
                    {
                        model.SupplierName += $"({shopDefaultSupplierModel.NickName})";
                    }
                }
                dataList.Add(model);
            }

            return Tuple.Create(total, dataList);
        }

        /// <summary>
        /// 店铺绑定厂家
        /// </summary>
        /// <param name="request"></param>
        /// <param name="dbname"></param>
        /// <returns></returns>
        public AjaxResultModel TriggerShopBindSupplier(BindSupplierRequestModel request, string dbname = "")
        {
            AjaxResultModel _res = new AjaxResultModel();

            if (request.BindShopId < 1)
            {
                _res.Message = "请选择需要绑定的店铺";
                return _res;
            }
            if (request.BindShopType < 1 || request.BindShopType > 3)
            {
                _res.Message = "参数有误";
                return _res;
            }

            if ((request.BindShopType == 2 || request.BindShopType == 3) && request.FxUserShopDefaultSupplierId < 1)
            {
                _res.Message = "请选择需要解绑的厂家";
                return _res;
            }

            int fxUserId = SiteContext.Current.CurrentFxUserId;

            //校验店铺
            var fxUserShopService = new FxUserShopService();
            var _reqModel = new FxUserShopQueryModel { FxUserId = fxUserId, ShopId = request.BindShopId };
            var fxUserShop = (fxUserShopService.GetList(_reqModel)?.Item2 ?? new List<FxUserShop>()).FirstOrDefault();

            if (fxUserShop == null)
            {
                _res.Message = "无店铺信息";
                return _res;
            }

            if (_reqModel.Status == FxUserShopStatus.AuthExpired)
            {
                _res.Message = "该店铺授权已过期，请重新授权后再操作";
                return _res;
            }

            if (_reqModel.Status == FxUserShopStatus.UnBind)
            {
                _res.Message = "该店铺已解绑，请重新绑定后再操作";
                return _res;
            }

            //校验厂家
            var supplierInfo = new SupplierUserService().GetSupplierUserById(request.Configs.FirstOrDefault().SupplierId, fxUserId);
            if (supplierInfo == null)
            {
                _res.Message = "厂家不存在";
                return _res;
            }
            if (supplierInfo.Status != AgentBingSupplierStatus.Binded)
            {
                _res.Message = "该厂家未处于绑定成功状态";
                return _res;
            }

            var fxUserShopDefaultSupplierService = new FxUserShopDefaultSupplierService();
            if (request.BindShopType == 1 || request.BindShopType == 2)
            {
                //校验店铺有没有绑定其它厂家
                var fxUserDefaultSupplierInfo = fxUserShopDefaultSupplierService.GetInfoByShopId(request.BindShopId);
                if (fxUserDefaultSupplierInfo != null)
                {
                    if (request.BindShopType == 2 && request.Configs.FirstOrDefault().SupplierId == fxUserDefaultSupplierInfo.SupplierFxUserId)
                    {
                        _res.Message = "该店铺已经有绑定厂家";
                        return _res;
                    }

                    if (request.BindShopType == 1)
                    {
                        //二次校验，查询绑定厂家表
                        var shopDefaultSupplierList = new FxUserShopDefaultSupplierService().GetDefaultSupplierListByShopIds(new List<int>() { request.BindShopId }, needEncryptAccount: true);
                        if (shopDefaultSupplierList != null && shopDefaultSupplierList.Count > 0)
                        {
                            _res.Message = "该店铺已经有绑定厂家";
                            return _res;
                        }
                        else
                        {
                            //解绑店铺代发
                            if (!fxUserShopDefaultSupplierService.UpdateDelBySupplier(fxUserId, fxUserDefaultSupplierInfo.SupplierFxUserId))
                            {
                                _res.Message = "解绑旧厂家失败，请重试";
                                return _res;
                            }
                        }
                    }
                }
            }

            if (request.BindShopType == 2 || request.BindShopType == 3)
            {
                //校验解绑数据是否属于当前用户
                var fxUserDefaultSupplierInfo = fxUserShopDefaultSupplierService.GetInfoById(request.FxUserShopDefaultSupplierId);
                if (fxUserDefaultSupplierInfo == null || fxUserDefaultSupplierInfo.FxUserId != fxUserId)
                {
                    _res.Message = "该店铺未绑定厂家";
                    return _res;
                }
            }

            //任务状态
            var asyncTaskService = new AsyncTaskService();

            var query = new AsyncTaskQueryModel
            {
                ShopId = SiteContext.Current.CurrentShopId,
                FxUserId = fxUserId,
                StatusList = new List<int>() { 0, 1 },
                Flag = "BindSupplier",
                IsSetExpiress = true,
                ExpiressTime = 30
            };
            AsyncTask task = asyncTaskService.GetAsyncTask(query);
            if (task != null)
            {
                _res.Message = "当前有商品更换厂家任务进行，请等待任务结束后操作！";
                return _res;
            }

            //添加任务
            task = new AsyncTask();
            task.Flag = "BindSupplier";
            task.CData = request.ToJson();
            if (asyncTaskService.AddAsyncTask(task) == false)
            {
                _res.Message = "创建任务失败，请重试";
                return _res;
            }

            //fxUserShopDefaultSupplier表 主键ID
            int fxUserShopDefaultSupplierId = 0;
            
            try
            {
                //绑定、换绑、解绑
                if (request.BindShopType == 1)
                {
                    //绑定
                    FxUserShopDefaultSupplier addModel = new FxUserShopDefaultSupplier();
                    addModel.CreateTime = DateTime.Now;
                    addModel.FxUserId = fxUserId;
                    addModel.IsDeleted = false;
                    addModel.UpdateTime = addModel.CreateTime;
                    addModel.SupplierFxUserId = request.Configs.FirstOrDefault().SupplierId;
                    addModel.ShopId = request.BindShopId;
                    fxUserShopDefaultSupplierId = fxUserShopDefaultSupplierService.AddModel(addModel);
                    if (fxUserShopDefaultSupplierId < 1)
                    {
                        _res.Message = "绑定厂家失败";
                    }
                }
                else if (request.BindShopType == 2)
                {
                    //换绑
                    var fxUserDefaultSupplierInfo = fxUserShopDefaultSupplierService.GetInfoById(request.FxUserShopDefaultSupplierId);
                    if (fxUserDefaultSupplierInfo != null && fxUserDefaultSupplierInfo.FxUserId == fxUserId)
                    {
                        //记录旧厂家userid
                        request.Configs.FirstOrDefault().OldSupplierId = fxUserDefaultSupplierInfo.SupplierFxUserId;


                        if (fxUserShopDefaultSupplierService.UpdateSupplierById(fxUserDefaultSupplierInfo.Id, request.Configs.FirstOrDefault().SupplierId) == false)
                        {
                            _res.Message = "更换厂家失败";
                        }
                    }
                    else
                    {
                        _res.Message = "更换厂家数据不存在";
                    }
                }
                else if (request.BindShopType == 3)
                {
                    //解绑
                    var fxUserDefaultSupplierInfo = fxUserShopDefaultSupplierService.GetInfoById(request.FxUserShopDefaultSupplierId);
                    if (fxUserDefaultSupplierInfo != null && fxUserDefaultSupplierInfo.FxUserId == fxUserId)
                    {
                        //记录旧厂家userid
                        request.Configs.FirstOrDefault().OldSupplierId = fxUserDefaultSupplierInfo.SupplierFxUserId;

                        if (fxUserShopDefaultSupplierService.UpdateDelById(fxUserDefaultSupplierInfo.Id) == false)
                        {
                            _res.Message = "解绑厂家失败";
                        }

                    }
                    else
                    {
                        _res.Message = "解绑厂家数据不存在";
                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"店铺绑定厂家出现异常：参数：{request.ToJson()}，ex：{ex}", "ShopBindSupplierError.txt");
                _res.Message = "店铺绑定厂家出现异常，请稍后重试";
            }

            if (!string.IsNullOrWhiteSpace(_res.Message))
            {
                task.Status = 5;
                task.UpdateTime = DateTime.Now;
                asyncTaskService.UpdatePendingStatus(task);
                return _res;
            }


            //开启线程
            ThreadPool.QueueUserWorkItem(state =>
            {
                try
                {
                    //Thread.Sleep(30*1000);//调试
                    if (!string.IsNullOrEmpty(dbname))
                    {
                        dbname = DES.DecryptDES(dbname, CustomerConfig.LoginCookieEncryptKey);
                    }

                    var userFx = new UserFxService().Get(fxUserId);
                    SiteContext siteContext = new SiteContext(userFx, dbname, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false }); //实例化商家的SiteContext   

                    task.Status = 1;//进行中
                    task.UpdateTime = DateTime.Now;
                    asyncTaskService.UpdatePendingStatus(task);


                    //查询商品请求参数
                    ProductFxRequertModule productFxRequertModule = new ProductFxRequertModule();
                    productFxRequertModule.SelectShopId = request.BindShopId.ToString();
                    productFxRequertModule.PageIndex = 1;
                    productFxRequertModule.PageSize = 200;
                    if (request.BindShopType == 1)
                    {
                        //查询店铺所有自营商品
                        productFxRequertModule.SelectSupplierId = "";
                    }
                    else if (request.BindShopType == 2 || request.BindShopType == 3)
                    {
                        //换绑，解绑查询绑定该厂家代发商品
                        productFxRequertModule.SelectSupplierId = request.Configs.FirstOrDefault().OldSupplierId.ToString();

                        if (request.BindShopType == 3)
                        {
                            //解绑把商品改为自营
                            request.isSelf = true;
                        }
                    }

                    var data = GetBindProductFxList(fxUserId, productFxRequertModule);
                    Log.Debug($"商品绑定厂家的商品数据：{data.ToJson()}，{productFxRequertModule.ToJson()}", "ShopBindSupplierError.txt");
                    int total = data.Total;
                    if (total < 1 || data.Rows == null || data.Rows.Count < 1)
                    {
                        task.Status = 5;//执行完成
                        task.UpdateTime = DateTime.Now;
                        asyncTaskService.UpdateStatus(task);
                    }

                    //总页数
                    int pageCount = Math.Ceiling(total * 1.0 / productFxRequertModule.PageSize).ToInt();

                    do
                    {
                        try
                        {
                            //拷贝一份数据
                            var dataRows = data.Rows.ToList();

                            //分批执行product、sku参数,绑定商品和规格不能同时进行，会有差异

                            //按商品绑定
                            request.IsBindSku = false;
                            request.productCodes = new List<string>();
                            request.skuCodes = new Dictionary<string, string>();
                            request.IsFollowProduct = false;

                            if (request.BindShopType == 1)
                            {
                                //绑定厂家，把该店铺下的自营商品/规格绑定为该厂家代发

                                //如果商品和sku都是自营，直接根据商品绑定，不用单独sku绑定
                                var newDataRows = dataRows.Where(w => w.DownFxUserId == 0 && (w.Skus?.Count == 0 || !w.Skus.Any(a => a.DownFxUserId > 0))).ToList();
                                if (newDataRows.Count > 0)
                                {
                                    request.IsFollowProduct = true;
                                    request.productCodes = newDataRows.Select(s=>s.ProductCode).ToList();
                                    ShopProductBindSupplier(request, fxUserId, task);
                                    request.IsFollowProduct = false;

                                    //绑定完清掉数据
                                    newDataRows.ForEach(item => {
                                        dataRows.Remove(item);
                                    });
                                }

                                //有sku绑定了厂家，商品和sku分开绑
                                request.productCodes = dataRows.Where(w => w.DownFxUserId == 0 && (w.Skus?.Count == 0 || w.Skus.Any(a => a.DownFxUserId > 0))).Select(s => s.ProductCode).ToList();
                                
                            }
                            else
                            {
                                //换绑或解绑，获取厂家代发的商品

                                //如果商品和sku都是厂家代发，直接根据商品绑定，不用单独sku绑定
                                var newDataRows = dataRows.Where(w => w.DownFxUserId == request.Configs.FirstOrDefault().OldSupplierId && (w.Skus?.Count == 0 || !w.Skus.Any(a => a.DownFxUserId != request.Configs.FirstOrDefault().OldSupplierId))).ToList();
                                if (newDataRows.Count > 0)
                                {
                                    request.IsFollowProduct = true;
                                    request.productCodes = newDataRows.Select(s => s.ProductCode).ToList();
                                    ShopProductBindSupplier(request, fxUserId, task);
                                    request.IsFollowProduct = false;

                                    //绑定完清掉数据
                                    newDataRows.ForEach(item => {
                                        dataRows.Remove(item);
                                    });
                                }

                                //有sku绑定了其他厂家，商品和sku分开绑
                                request.productCodes = dataRows.Where(w => w.DownFxUserId == request.Configs.FirstOrDefault().OldSupplierId && (w.Skus?.Count == 0 || w.Skus.Any(a => a.DownFxUserId != request.Configs.FirstOrDefault().OldSupplierId))).Select(s => s.ProductCode).ToList();
                            }

                            if (request.productCodes.Count > 0)
                            {
                                ShopProductBindSupplier(request, fxUserId, task);
                            }

                            //按规格绑定
                            request.IsBindSku = true;
                            request.productCodes = new List<string>();

                            foreach (var item in dataRows.Select(s => s.ProductCode))
                            {
                                var productInfo = dataRows.FirstOrDefault(f => f.ProductCode == item);
                                if (productInfo != null && productInfo.Skus != null && productInfo.Skus.Count > 0)
                                {
                                    //sku
                                    var skuList = new List<ProductSkuFx>();
                                    if (request.BindShopType == 1)
                                    {
                                        skuList = productInfo.Skus.Where(w => w.DownFxUserId == 0).ToList();
                                    }
                                    else
                                    {
                                        skuList = productInfo.Skus.Where(w => w.DownFxUserId == request.Configs.FirstOrDefault().OldSupplierId).ToList();
                                    }
                                    if (skuList != null && skuList.Count > 0)
                                    {
                                        request.productCodes.Add(item);
                                        request.skuCodes.Add(item, string.Join(",", skuList.Select(s => s.SkuCode)));
                                    }
                                }
                            }

                            if (request.productCodes.Count > 0)
                            {
                                ShopProductBindSupplier(request, fxUserId, task);
                            }
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"店铺绑定厂家>分页商品绑定出现异常：参数：{fxUserId}，{request.ToJson()}，ex：{ex}", "ShopBindSupplierError.txt");
                        }


                        if (data.Rows.Count >= productFxRequertModule.PageSize)
                        {
                            //下一页
                            productFxRequertModule.PageIndex += 1;
                            data = GetBindProductFxList(fxUserId, productFxRequertModule);
                            Log.Debug($"商品绑定厂家的商品数据：{data.ToJson()}，{productFxRequertModule.ToJson()}", "ShopBindSupplierError.txt");
                        }
                        else
                        {
                            break;
                        }
                    } while (productFxRequertModule.PageIndex <= pageCount && data.Rows != null && data.Rows.Count > 0);

                    task.Status = 5;//执行完成
                    task.UpdateTime = DateTime.Now;
                    asyncTaskService.UpdateStatus(task);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"店铺绑定厂家执行任务出现异常：参数：：{fxUserId}，{request.ToJson()}，ex：{ex}", "ShopBindSupplierError.txt");
                    task.Status = -1;//
                    task.ExceptionDesc = $"异常信息:{ex.Message}";
                    task.UpdateTime = DateTime.Now;
                    asyncTaskService.UpdateStatus(task);
                }
            });

            _res.Success = true;
            _res.Data = new { FxUserShopDefaultSupplierId = fxUserShopDefaultSupplierId };
            return _res;
        }

        /// <summary>
        /// 查询按店铺绑定商品的数据
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public PagedResultModel<ProductFx> GetBindProductFxList(int fxUserId, ProductFxRequertModule model)
        {
            try
            {
                var tuple = _repository.GetShopBindProductList(fxUserId, model);

                //获取Sku路径
                var pathflowService = new PathFlowService(_connectionString);
                var pCodes = tuple.Item2.Select(x => x.ProductCode).Distinct().ToList();
                var fields = "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
                var pathFlows = pathflowService.GetPathFlows(pCodes, 0, fields);

                //兼容新旧数据补上Config信息
                CompatibleOldDataToAddConfig(pathFlows);

                tuple.Item2.ForEach(p =>
                {
                    var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(p.ProductCode)).ToList();

                    // 商品上下游用户Id
                    var tupleFxUserId = GetBindUpAndDownFxUserId(p.ProductCode, fxUserId, productPathFlows);

                    p.UpFxUserId = tupleFxUserId.Item1;
                    p.DownFxUserId = tupleFxUserId.Item2;

                    var newSkus = new List<ProductSkuFx>();
                    p.Skus.ForEach(sku =>
                    {
                        var skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.SkuCode)).ToList();
                        // 判断当前节点Sku路径是否继承于商品，继承至商品的这一段路径跟随商品显示
                        if (skuPathFlows == null || skuPathFlows.Any() == false)
                        {
                            sku.UpFxUserId = p.UpFxUserId;
                            sku.DownFxUserId = p.DownFxUserId;
                        }
                        else
                        {
                            var tupleFxUserId2 = GetBindUpAndDownFxUserId(sku.SkuCode, fxUserId, skuPathFlows);
                            sku.UpFxUserId = tupleFxUserId2.Item1;
                            sku.DownFxUserId = tupleFxUserId2.Item2;
                        }
                    });
                });


                return new PagedResultModel<ProductFx>()
                {
                    Rows = tuple.Item2,
                    Total = tuple.Item1
                };
            }
            catch (Exception e)
            {
                Log.WriteError($"店铺绑定厂家>查询店铺商品出现异常：参数：{fxUserId},{model.ToJson()}，ex：{e}", "ShopBindSupplierError.txt");
            }

            return new PagedResultModel<ProductFx>();
        }

        /// <summary>
        /// 按店铺绑定厂家用， 获取上下游fxuserid，
        /// </summary>
        /// <param name="isProduct"></param>
        /// <param name="refCode"></param>
        /// <param name="fxUserId"></param>
        /// <param name="pathFlows"></param>
        /// <returns></returns>
        private Tuple<int, int, bool> GetBindUpAndDownFxUserId(string refCode, int fxUserId, List<PathFlow> pathFlows)
        {
            var downFxUserId = 0;
            var upFxUserId = 0;
            var pathRefConfigs = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(refCode)).SelectMany(x => x.PathFlowReferences.SelectMany(y => y.Value.ReferenceConfigs)).Where(x => x.PathFlowRefCode == refCode).ToList();

            var defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.ConfigType == 0 && x.DownFxUserId > 0 && x.FxUserId == fxUserId);
            if (defaultPathRefConfig != null)
            {
                // 默认厂家
                downFxUserId = defaultPathRefConfig.DownFxUserId;
                upFxUserId = defaultPathRefConfig.UpFxUserId;
            }
            else
            {
                // 自营
                defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.DownFxUserId == fxUserId);
                upFxUserId = defaultPathRefConfig?.FxUserId ?? 0;
            }

            return new Tuple<int, int, bool>(upFxUserId, downFxUserId, false);
        }

        /// <summary>
        /// 按店铺绑定厂家用，店铺商品绑定厂家
        /// </summary>
        /// <param name="request"></param>
        /// <param name="fxUserId"></param>
        private void ShopProductBindSupplier(BindSupplierRequestModel request,int fxUserId,AsyncTask task)
        {
            var perCheck = PerCheckBindSupplierRetry(request,fxUserId,5,30);

            if (perCheck.CheckResult != null && perCheck.CheckResult.Success)
            {
                //检查通过，开始绑定
                try
                {
                    BindSupplier(request, perCheck, false, task, batchId: request.BatchId);
                }
                catch (LogicException lex)
                {
                    Log.WriteError($"店铺商品绑定厂家-更新订单发生异常：参数：{fxUserId}，{request.ToJson()}，ex：{lex}", "ShopBindSupplierError.txt");
                }
                catch (Exception ex)
                {
                    Log.WriteError($"店铺商品绑定厂家-更新订单发生异常：参数：{fxUserId}，{request.ToJson()}，ex：{ex}", "ShopBindSupplierError.txt");
                }
                finally
                {
                    //释放店铺锁
                    new ShopLockService().UnLock(perCheck.LockSids, (int)ShopLockType.ProductBind, request.BatchId);
                }
            }
            else
            {
                //释放店铺锁
                new ShopLockService().UnLock(perCheck.LockSids, (int)ShopLockType.ProductBind, request.BatchId);
            }
        }

        /// <summary>
        /// 前置检查等待重试
        /// </summary>
        /// <param name="request"></param>
        /// <param name="fxUserId"></param>
        /// <param name="rCount">重试次数</param>
        /// <param name="rSecond">重试失败后多少秒再重试 </param>
        /// <returns></returns>
        private PerCheckBindSupplierResult PerCheckBindSupplierRetry(BindSupplierRequestModel request, int fxUserId, int rCount = 0,int rSecond=0)
        {
            
            int rNum = 0;
        retryLogic:

            Thread.Sleep(rSecond * rNum * 1000);

            var perCheck = new PerCheckBindSupplierResult();
            var batchId = Guid.NewGuid().ToReplaceHyphen();
            request.BatchId = batchId;
            try
            {
                //前置检查
                perCheck = PerCheckBindSupplier(request, new PathFlowService(), new PathFlowReferenceService(), fxUserId, batchId, "ShopBindSupplierExCode");
            }
            catch (LogicException lex)
            {
                Log.WriteError($"店铺绑定厂家>前置检查出现异常：参数：{fxUserId}，{request.ToJson()}，ex：{lex}", "ShopBindSupplierError.txt");
                if (lex.ErrorCode == "ShopBindSupplierExCode" && ++rNum <= rCount)
                {
                    //需要等待后重试
                    //释放锁
                    new ShopLockService().UnLock(perCheck.LockSids, (int)ShopLockType.ProductBind, batchId);
                    goto retryLogic;
                }

            }
            catch (Exception e)
            {
                perCheck.CheckResult = null;
                Log.WriteError($"店铺绑定厂家>前置检查出现异常：参数：{fxUserId}，{request.ToJson()}，ex：{e}", "ShopBindSupplierError.txt");
                //释放锁
                new ShopLockService().UnLock(perCheck.LockSids, (int)ShopLockType.ProductBind, batchId);
            }
            return perCheck;
        }

        /// <summary>
        /// 商品选择器数据是否展示标题
        /// </summary>
        /// <param name="simpleProductList"></param>
        public void ProductSearchIsShowTitle(List<SimpleProduct> simpleProductList, List<SimpleProductModel> sourseProductList)
        {
            int curFxUserId = SiteContext.Current.CurrentFxUserId;
            // 获取订单上下游关系
            var pathFlowRepository = new PathFlowRepository(_connectionString);
            var pathFlowNodes = new List<PathFlowNode>();

            var pathFlowCodes = sourseProductList.Select(s => s.PathFlowCode).Distinct().ToList() ;

            var pathFlowNodeDic = new Dictionary<string, List<PathFlowNode>>();
            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            if (pathFlowCodes.Any())
            {
                pathFlowNodes = pathFlowRepository.GetPathFlowNodeList(pathFlowCodes);
                pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(pathFlowNodes);
            }

            var commonSettingRepository = new CommonSettingRepository();

            var dictSorseProductList = new Dictionary<string, SimpleProductModel>();
            sourseProductList?.ForEach(x => { 
                if(dictSorseProductList.ContainsKey(x.ProductCode) == false)
                    dictSorseProductList.Add(x.ProductCode, x); 
            });
            simpleProductList.ForEach(item => {
                //校验商品展示权限
                dictSorseProductList.TryGetValue(item.ProductCode, out SimpleProductModel sourseProduct);
                if (sourseProduct != null)
                {
                    bool isShowProductTitle = commonSettingRepository.SetIsShowProductTitle(curFxUserId, sourseProduct.PathFlowCode, pathFlowNodeDic);
                    if (isShowProductTitle == false)
                    {
                        item.ProductSubject = item.ProductSubject.GetShowProductStr(false);
                    }
                }
            });
        }


        /// <summary>
        /// 转换显示商品标题
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="pathFlows"></param>
        /// <param name="pathFlowNodeDic"></param>
        /// <param name="product">可为null</param>
        /// <param name="sku">可为null</param>
        /// <param name="repository"></param>
        public void ConvertShowProductTitle(int fxUserId, List<PathFlow> pathFlows, Dictionary<string, List<PathFlowNode>> pathFlowNodeDic,ProductFx product,ProductSkuFx sku,CommonSettingRepository repository)
        {
            var isShowProductTitle = true;
            foreach (var flow in pathFlows)
            {
                isShowProductTitle = repository.SetIsShowProductTitle(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                if (isShowProductTitle == false)
                {
                    if (product != null)
                    {
                        product.Subject = product.Subject.GetShowProductStr(isShowProductTitle);
                    }

                    if (sku != null)
                    {
                        sku.Subject = sku.Subject.GetShowProductStr(isShowProductTitle);
                    }

                    break;
                }
            }
        }

        /// <summary>
        /// 转换显示商品图片
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="pathFlows"></param>
        /// <param name="pathFlowNodeDic"></param>
        /// <param name="product">可为null</param>
        /// <param name="sku">可为null</param>
        /// <param name="repository"></param>
        public void ConvertShowProductImg(int fxUserId, List<PathFlow> pathFlows, Dictionary<string, List<PathFlowNode>> pathFlowNodeDic, ProductFx product, ProductSkuFx sku, CommonSettingRepository repository)
        {
            var isShowProductImg = true;
            foreach (var flow in pathFlows)
            {
                isShowProductImg = repository.SetIsShowProductImg(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                if (isShowProductImg == false)
                {
                    if (product != null)
                    {
                        product.ImageUrl = product.Subject.GetShowProductStr(isShowProductImg);
                    }

                    if (sku != null)
                    {
                        sku.ImgUrl = sku.ImgUrl.GetShowProductStr(isShowProductImg);
                    }

                    break;
                }
            }
        }

        /// <summary>
        /// 转换显示店铺名称
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="pathFlows"></param>
        /// <param name="pathFlowNodeDic"></param>
        /// <param name="product">可为null</param>
        /// <param name="sku">可为null</param>
        /// <param name="repository"></param>
        /// <param name="agentShopList"></param>
        public void ConvertShowShopName(int fxUserId, List<PathFlow> pathFlows, Dictionary<string, List<PathFlowNode>> pathFlowNodeDic, ProductFx product, ProductSkuFx sku, CommonSettingRepository repository,List<Shop> agentShopList)
        {
            var isShowShopName = false;
            foreach (var flow in pathFlows)
            {
                isShowShopName = repository.SetIsShowShopName(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                if (isShowShopName == false)
                {
                    break;
                }
            }

            if (isShowShopName)
            {
                if (product != null)
                {
                    var agentShopModel = agentShopList.FirstOrDefault(f => f.Id == product.ShopId);

                    if (agentShopModel != null)
                    {
                        product.AgentShopName = agentShopModel.NickName;
                        product.AgentShopPlatformType = agentShopModel.PlatformType;
                    }
                }

                if (sku != null)
                {
                    var agentShopModel = agentShopList.FirstOrDefault(f => f.Id == sku.ShopId);

                    if (agentShopModel != null)
                    {
                        sku.AgentShopName = agentShopModel.NickName;
                        sku.AgentShopPlatformType = agentShopModel.PlatformType;
                    }
                }
            }
        }

        /// <summary>
        /// 检查商品是否启用引用功能
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="productCodes"></param>
        /// <returns></returns>
        public List<string> CheckIsEnableQuote(int fxUserId, List<string> productCodes)
        {
            // 参数验证
            if (fxUserId <= 0 || productCodes.IsNullOrEmptyList())
                return new List<string>();

            // 获取相关路径流
            var flows = _repository.GetFxUserIdsByProductCodes(fxUserId, productCodes);
            if (flows.IsNullOrEmptyList())
                return productCodes;

            // 按PathFlowCode分组
            var flowsByCode = flows.GroupBy(x => x.PathFlowCode)
                .ToDictionary(x => x.Key, x => x.ToList());

            // 找到下游为当前用户的路径流
            var downFlows = flowsByCode.Where(x => x.Value.Any(y => y.DownFxUserId == fxUserId)).ToList();
            if (downFlows.IsNullOrEmptyList())
                return productCodes;

            // 获取相关的FxUserId（包括FxUserId和UpFxUserId）
            var fxUserIds = downFlows.SelectMany(x => x.Value)
                .SelectMany(y => new[] { y.FxUserId, y.UpFxUserId })
                .Where(id => id > 0)
                .Distinct()
                .ToList();

            // 获取用户对应的系统店铺ID
            var fxUserShops = new FxUserShopRepository().GetFxUserShopIds(fxUserIds);
            if (fxUserShops.IsNullOrEmptyList())
                return productCodes; 

            var shopIds = fxUserShops.Select(x => x.ShopId).ToList();

            // 获取开关设置信息
            var commonSettingRepository = new CommonSettingRepository();
            var quoteSettings = commonSettingRepository.Get("/FenFa/System/Config/IsEnableQuote", shopIds);
            Log.Debug(() => $"获取引用功能开关设置：{quoteSettings.ToJson()}");

            // 获取已关闭引用功能的店铺ID
            var disabledShopIds = quoteSettings
                .Where(x => !string.IsNullOrWhiteSpace(x.Value) &&
                            x.Value.Equals("false", StringComparison.OrdinalIgnoreCase))
                .Select(x => x.ShopId)
                .ToList(); 
            Log.Debug(() => $"已关闭引用功能的店铺ID：{disabledShopIds.ToJson()}");

            // 如果没有禁用的店铺，返回所有商品
            if (disabledShopIds.Count == 0)
                return productCodes;

            // 获取关闭引用的用户ID
            var disabledUserIds = fxUserShops
                .Where(x => disabledShopIds.Contains(x.ShopId))
                .Select(x => x.FxUserId)
                .Distinct()
                .ToList(); 
            Log.Debug(() => $"已禁用引用功能的用户ID：{disabledUserIds.ToJson()}");

            // 获取已禁用商品代码的集合
            var disabledProductCodes = flowsByCode
                .Where(x => x.Value.Any(y => disabledUserIds.Contains(y.FxUserId)))
                .SelectMany(x => x.Value)
                .Select(y => y.ProductCode)
                .Distinct()
                .ToList(); 

            // 获取通过检查的商品Code
            return productCodes.Where(x => !disabledProductCodes.Contains(x)).ToList();
        }
        
        #region 校验商品规格名变更

        /// <summary>
        /// 保存前检查产品sku名称是否变更
        /// </summary>
        /// <param name="productFxs"></param>
        public void SaveBeforeCheckProductSkuName(int userId,List<ProductFx> productFxs)
        {
            if (productFxs.IsNullOrEmptyList())
                return;
            var _productSkuFxRepository = new ProductSkuFxRepository();
            try
            {
                var logFileName = $"CheckProductSkuName-{DateTime.Now.ToString("yyyyMMdd")}.txt";
                Log.Debug(() => $"商品同步==商品数=》{productFxs.Count}", logFileName);
                //获取当前存在的SKU
                var codes = productFxs.Select(x => x.ProductCode).Distinct().ToList();
                var exisProductSkus = _productSkuFxRepository.GetAllList(codes);
                if (exisProductSkus.IsNullOrEmptyList())
                    return;
                //同步进来的sku
                var skus = productFxs.Where(t => !t.Skus.IsNullOrEmptyList())
                    .SelectMany(x => x.Skus)
                    .Distinct().ToList();
                Log.Debug(() => $"商品同步==进来SkuName=》{skus.Select(t => t.Name).ToJson()}", logFileName);
                //查询存在skuid相同但是name不相同的数据
                var oldProductSkus = (from o in exisProductSkus
                                      join n in skus on o.SkuCode equals n.SkuCode
                                      where o.Name != n.Name
                                      select o).ToList();
                if (oldProductSkus.IsNullOrEmptyList())
                    return;
                Log.Debug(() => $"商品同步==差异的SkuName=》{oldProductSkus.Select(t => t.Name).ToJson()}", logFileName);
                ProductSkuNameChanged(userId, productFxs, oldProductSkus);
            }
            catch (Exception ex)
            {
                Log.WriteError($"校验商品规格名是否变更错误，商品ID：{productFxs.Select(t => t.PlatformId).ToJson()},错误信息：{ex}");
            }
        }

        /// <summary>
        /// 商品规格名发生变更
        /// </summary>
        /// <param name="productFxs"></param>
        /// <param name="oldProductSkus"></param>
        public void ProductSkuNameChanged(int userId, List<ProductFx> productFxs, List<ProductSkuFx> oldProductSkus)
        {
            var logFileName = $"CheckProductSkuName-{DateTime.Now.ToString("yyyyMMdd")}.txt";
            var pathflowService = new PathFlowService();
            var productFxService = new ProductFxService();
            var _shopService = new ShopService();
            var _messageService = new SiteMessageService();
            var commonSettingService = new CommonSettingService();
            //获取Sku路径
            var productCodes = oldProductSkus.Select(x => x.ProductCode).Distinct().ToList();
            var fields =
                "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
            var pathFlows = pathflowService.GetPathFlows(productCodes, 0, fields);
            //兼容新旧数据补上Config信息
            productFxService.CompatibleOldDataToAddConfig(pathFlows);
            //商家
            var dic = new Dictionary<int, List<ProductSkuFx>>();
            oldProductSkus.ForEach(sku =>
            {
                var skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.SkuCode)).ToList();
                //判断当前节点Sku路径是否继承于商品，继承至商品的这一段路径跟随商品
                if (skuPathFlows.IsNullOrEmptyList())
                    skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.ProductCode)).ToList();
                //排除自营的数据
                if (skuPathFlows.Any(t => t.IsSelf))
                    return;
                var pathRefConfigs = skuPathFlows
                    .SelectMany(t => t.PathFlowReferences.SelectMany(x => x.Value.ReferenceConfigs)).ToList();
                var curFxUserIds = pathRefConfigs.Select(t => t.DownFxUserId).Where(t => t > 0).Distinct().ToList();
                var sourceFxUserIds = skuPathFlows.Select(t => t.SourceFxUserId).Distinct();
                curFxUserIds.AddRange(sourceFxUserIds);
                curFxUserIds.ForEach(fxUserId =>
                {
                    if (dic.ContainsKey(fxUserId))
                    {
                        dic[fxUserId].Add(sku);
                    }
                    else
                    {
                        dic.Add(fxUserId, new List<ProductSkuFx>() { sku });
                    }
                });
            });
            if (dic.IsNullOrEmptyList())
                return;
            //过滤出开启了提醒设置的用户
            var fxUserIds = dic.Select(t => t.Key).ToList();
            //var sysShops = _shopService.GetFxSystemShopByFxId(fxUserIds, "s.id,fus.FxUserId AS FxUserIds");
            //var sysShops = _shopService.GetFxSystemShopByFxIdV1(fxUserIds, "s.id,fus.FxUserId AS FxUserIds");
            var sysShops = new UserFxService().GetSystemShopsByFxUserIdsWithCache(fxUserIds);
            var enableFxUserIds = new List<int>();
            fxUserIds.ForEach(t =>
            {
                var shop = sysShops.FirstOrDefault(s => s.FxUserIds == t);
                if (shop == null)
                    return;
                var shopId = shop.Id;
                var setting = commonSettingService.GetReminderSetting(shopId);
                if (setting.IsSkuChangedRem)
                {
                    enableFxUserIds.Add(t);
                }
            });
            if (enableFxUserIds.Count == 0)
                return;
            Log.Debug(() => $"开启提醒用户：{enableFxUserIds.Count}条,fxUserIds:{enableFxUserIds.ToJson()}", logFileName);
            var now = DateTime.Now;
            //基础商品异常信息消息
            var baseProductMessages = new List<MessageRecord>();
            //已写入数据库的变更记录
            var addDbProductSkuHistoryList = new List<ProductSkuHistory>();
            //查询出启用提醒设置的用户数据库连接串
            var dbConfigs = new DbConfigRepository().GetListByFxUserIds(enableFxUserIds,
                new List<string> { CustomerConfig.CloudPlatformType });
            var dbConfigGroup = dbConfigs.GroupBy(t => t.ConnectionString).ToList();
            Log.Debug(() => $"查询到：{dbConfigGroup.Count}个数据库,连接串:{dbConfigGroup.Select(t => t.Key).ToJson()}",
                logFileName);
            dbConfigGroup.ForEach(g =>
            {
                var connectionString = g.Key;
                Log.Debug(() => $"开始处理,连接串:{connectionString}", logFileName);
                if (string.IsNullOrWhiteSpace(connectionString)) return;
                //当前库的用户id
                var curFxUserIds = g.Select(t => t.DbConfig.UserId).Distinct().ToList();
                Log.Debug(() => $"当前库处理用户：{curFxUserIds.ToJson()}", logFileName);
                if (curFxUserIds.IsNullOrEmptyList()) return;
                var _productFxRepository = new ProductFxRepository(connectionString);
                var _productSkuFxRepository = new ProductSkuFxRepository(connectionString);
                var _productSkuHistoryRepository = new ProductSkuHistoryRepository(connectionString);
                var baseOfPtSkuRelationService = new BaseOfPtSkuRelationService(connectionString);
                //需要更新的商品
                var updateProductModels = new ConcurrentDictionary<string, string>();
                //需要更新的商品规格
                var updateProductSkuModels = new ConcurrentDictionary<string, string>();
                //规格变更记录
                var skuHistorys = new ConcurrentBag<ProductSkuHistory>();
                curFxUserIds.ForEach(curFxUserId =>
                {
                    if (dic.ContainsKey(curFxUserId) == false)
                    {
                        return;
                    }
                    var productSkus = dic[curFxUserId];
                    if (productSkus == null || productSkus.Any() == false)
                    {
                        return;
                    }
                    var skuCodes = productSkus.Select(t => t.SkuCode).ToList();
                    //通过业务库的基础商品关联关系表查询是否有关联
                    var baseOfPtSkuRelationList = baseOfPtSkuRelationService.GetListBySkuCode(skuCodes, curFxUserId);
                    productSkus.ForEach(sku =>
                    {
                        var productFx = productFxs.FirstOrDefault(t => t.ProductCode == sku.ProductCode);
                        // 2.修改 Product 和 ProductSku 的 IsSkuNameChanged字段值
                        updateProductModels.TryAdd(sku.ProductCode, sku.ProductCode);
                        updateProductSkuModels.TryAdd(sku.SkuCode, sku.SkuCode);
                        // 3.根据路径流 记录 ProductSkuHistory（店铺商品规格变更历史数据）
                        var curSkuHistory = new ProductSkuHistory(productFx, sku, curFxUserId);
                        curSkuHistory.CreateTime = now;
                        skuHistorys.Add(curSkuHistory);
                        // 4.修改店铺基础商品异常信息表：BaseProductAbnormal（店铺基础商品异常信息表）
                        var baseProductSku =
                            baseOfPtSkuRelationList.FirstOrDefault(t => t.ProductSkuCode == sku.SkuCode);
                        if (baseProductSku == null)
                        {
                            return;
                        }
                        var abnormal = new BaseProductAbnormal
                        {
                            AbnormalType = BaseProductAbnormalType.ChangedSkuName,
                            AbnormalReason = "该分销品已变更规格属性信息，请确认是否继续关联。",
                            FxUserId = curFxUserId,
                            BaseProductUid = baseProductSku.BaseProductUid,
                            BaseProductSkuUid = baseProductSku.BaseProductSkuUid,
                            ProductPtId = sku.PlatformId,
                            ProductSkuPtId = sku.SkuId,
                            ProductCode = productFx.ProductCode,
                            ProductSkuCode = sku.SkuCode,
                            ProductShopId = productFx.ShopId,
                            ProductFxUserId = productFx.SourceUserId,
                            ProductPlatformType = productFx.PlatformType,
                            ProductCloudPlatform = CommUtls.GetPlatformCloudByType(productFx.PlatformType),
                            CreateTime = now,
                            UpdateTime = now
                        };
                        baseProductMessages.Add(new MessageRecord
                        {
                            CreateFxUserId = userId,
                            CreateTime = DateTime.Now,
                            BusinessId = abnormal.ProductSkuCode,
                            TargetCloud = CloudPlatformType.Alibaba.ToString(),
                            ProductPlatformType = abnormal.ProductPlatformType,
                            MsgType = BaseProductMsgType.AddBaseProductAbnormal,
                            FxUserId = abnormal.FxUserId,
                            DataJson = abnormal.ToJson()
                        });
                    });
                });
                //3.根据路径流 记录 ProductSkuHistory（店铺商品规格变更历史数据）
                Log.Debug(() => $"商品待处理变更记录：{skuHistorys.Count}条", logFileName);
                var allAddList = _productSkuHistoryRepository.BatchAdd(skuHistorys.ToList());
                if (allAddList.IsNotNullAndAny())
                {
                    Log.Debug(
                        () =>
                            $"商品变更记录新增：{allAddList.Count}条,fxUserIds:{curFxUserIds.ToJson()},dbName:{connectionString}",
                        logFileName);
                    //记录需要发站内信的数据
                    addDbProductSkuHistoryList.AddRange(allAddList);
                }

                //2.修改 Product 和 ProductSku 的 IsSkuNameChanged字段值
                var updateProductModel = new BatchUpdater("Product");
                updateProductModels.ToList().ForEach(t =>
                {
                    updateProductModel.TryAdd("ProductCode", t.Value, "IsSkuNameChanged", "1");
                });
                var updateProductSkuModel = new BatchUpdater("ProductSku");
                updateProductSkuModels.ToList().ForEach(t =>
                {
                    updateProductSkuModel.TryAdd("SkuCode", t.Value.ToString(), "IsSkuNameChanged", "1");
                });
                Log.Debug(() => $"更新商品=》userids：{curFxUserIds.ToJson()}{updateProductModel.GetUpdateSql().ToJson()}",
                    logFileName);
                _productFxRepository.BatchUpdateBySql(updateProductModel);
                Log.Debug(() => $"更新规格=》userids：{curFxUserIds.ToJson()}=》{updateProductModel.GetUpdateSql().ToJson()}",
                    logFileName);
                _productSkuFxRepository.BatchUpdateBySql(updateProductSkuModel);
                Log.Debug(() => $"结束处理,连接串:{connectionString}", logFileName);
            });
            //1.发送站内信
            //商家不需要发站内信
            addDbProductSkuHistoryList = addDbProductSkuHistoryList.Where(t => t.FxUserId != t.SourceFxUserId).ToList();
            if (addDbProductSkuHistoryList.Any())
            {
                var messages = addDbProductSkuHistoryList.GroupBy(t => new { t.FxUserId, t.SkuCode }).Select(t =>
                {
                    return new SiteMessageContentModel()
                    {
                        FirstType = SiteMessageTypeEnum.PrimaryType.SkuChanged.ToString(),
                        SecondType = SiteMessageTypeEnum.PrimaryType.SkuChanged.ToString(),
                        FromUserId = userId,
                        ToUserIds = new List<int> { t.Key.FxUserId },
                        JumpUrl = "/Common/Page/Product-Index",
                        Content = "上级分销商已修改商品规格，请及时确认。",
                        CreateTime = now,
                    };
                }).ToList();
                Log.Debug(() => $"发送站内信:{messages.Count}条", logFileName);
                Parallel.ForEach(messages, new ParallelOptions { MaxDegreeOfParallelism = 5 },
                    message => { _messageService.ConvertToMessage(message, checkUserSubscribed: false); });
            }

            //4.修改店铺基础商品异常信息表：BaseProductAbnormal（店铺基础商品异常信息表）
            if (baseProductMessages.IsNotNullAndAny())
            {
                Log.Debug(() => $"处理基础商品异常消息:{baseProductMessages.Count}条", logFileName);
                new MessageRecordService().SendBusinessMessage(baseProductMessages);
            }

            Log.Debug(() => $"结束处理", logFileName);
        }

        #endregion


        /// <summary>
        /// 保存前检查产品sku名称是否变更
        /// </summary>
        /// <param name="productFxs"></param>
        public Tuple<int, List<ProductFx>> GetShopBindProductList(int fxUserId, ProductFxRequertModule model)
        {
            var _productSkuFxRepository = new ProductFxRepository();
            var data = _productSkuFxRepository.GetShopBindProductList(fxUserId, model);

            return data;
        }
    }
}