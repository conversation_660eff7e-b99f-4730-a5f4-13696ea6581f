using DianGuanJiaApp.Data.Entity.ProfitStatistics;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model.LogModel;
using DianGuanJiaApp.Data.Model.ProfitStatistics;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Repository.ProfitStatistics;
using DianGuanJiaApp.RabbitMQ;
using DianGuanJiaApp.Services.Services.DataEventTracking;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services.Services.ProfitStatistics
{
    /// <summary>
    /// 利润统计 利润订单同步任务 服务
    /// </summary>
    public partial class ProfitOrderSyncTaskService
    {
        private ProfitOrderSyncTaskRepository _repository;
        public ProfitOrderSyncTaskService()
        {
            _repository = new ProfitOrderSyncTaskRepository();
        }

        public List<ProfitOrderSyncTask> GetListByUsers(List<int> fxUserIds,List<int> status=null)
        {
            return _repository.GetListByUsers(fxUserIds, status);
        }

        public List<ProfitOrderSyncTask> GetListByUserAndType(int fxUserId,int type = 0,int status = 0)
        {
            return _repository.GetListByUserAndType(fxUserId, type,status);
        }

        public List<ProfitOrderSyncTask> GetPageByType(int type,long preLastId,int pageSize,int syncStatus = 1, int status = 0)
        {
            return _repository.GetPageByType(type, preLastId, pageSize,syncStatus,status);
        }

        public long Add(ProfitOrderSyncTask task)
        {
            return _repository.Add(task);
        }

        public int Update(ProfitOrderSyncTask task)
        {
            return _repository.Update(task);
        }

        public int BatchUpdate(List<ProfitOrderSyncTask> tasks)
        {
            if (tasks.IsNullOrEmptyList())
                return 0;
            return _repository.BatchUpdate(tasks);
        }

        public int UpdateSyncStatus(List<long> ids, int syncStatus, DateTime? endTime = null, DateTime? startTime = null)
        {
            if (ids.IsNullOrEmptyList())
                return 0;
            return _repository.UpdateSyncStatus(ids, syncStatus, endTime, startTime);
        }
        public int UpdateDbStatus(List<long> ids , int status)
        {
            if (ids.IsNullOrEmptyList())
                return 0;
            return _repository.UpdateDbStatus(ids, status);
        }

        public void UpdateCount(long id,int? total,int? success,int? failed)
        {
            _repository.UpdateCount(id, total, success, failed);
            return;
        }

        /// <summary>
        /// 删除用户所有的同步任务
        /// </summary>
        /// <returns></returns>
        public int DeleteUserAllTask(int fxUserId)
        {
            return _repository.DeleteUserAllTask(fxUserId);
        }

       
    }

}
