using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Respository;
using static Dapper.SqlMapper;
using DianGuanJiaApp.Utility.Extension;
using System.IO;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Services
{

    public partial class PurchaseService : BaseService<Data.Entity.PurchaseHistory>
    {
        private PurchaseRepository _repository = new PurchaseRepository();
        private OrderRepository _orderRepository = new OrderRepository();

        /// <summary>
        /// 获取备货单订单商品数量
        /// </summary>
        /// <param name="whereSql">查询条件</param>
        /// <param name="parameters">参数集合</param>
        /// <returns></returns>
        public int GetOrderProductItemsCount(ProductSearchModel model)
        {
            return _orderRepository.GetOrderProductItemsCount(model);
        }

        public Tuple<List<PurchaseFullItemModel>, int> GetFullPurchaseList(ProductSearchModel model)
        {
            return _orderRepository.GetFullPurchaseList(model);
        }

        public PurchasesReturnModel GetPurchaseReturnModel(ProductSearchModel model, List<PurchaseFullItemModel> list)
        {
            return _orderRepository.GetPurchaseReturnModel(model, list);
        }

        /// <summary>
        /// 获取备货单数据
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public PurchasesReturnModel GetPurchaseList(ProductSearchModel model)
        {
            var returnModel = _orderRepository.GetPurchaseList(model);

            try
            {
                // 商品归一数据替换
                returnModel = RebuildOrder(returnModel);
            }
            catch (Exception e)
            {
                Log.WriteError($"备货单替换基础商品数据异常：{e.Message}");
            }

            return returnModel;
        }

        /// <summary>
        /// 重新构建订单（基础商品信息替换合并）
        /// </summary>
        /// <param name="returnModel"></param>
        /// <returns></returns>
        public PurchasesReturnModel RebuildOrder(PurchasesReturnModel returnModel)
        {
            // 是否需要替换为基础商品数据
            var resInfos = new Dictionary<long, BaseProductSkuSimpleRes>();
            if (SiteContext.Current.BaseProductSetting.OrderCombine)
            {
                var uids = returnModel.PurchaseItems
                    .SelectMany(x => x.SubItems)
                    .Select(x => x.BaseProductSkuUid)
                    .Distinct().ToList();

                resInfos = new BaseProductSkuCommonService().GetSkuListBySkuIds(uids);
            }

            if (!resInfos.Any()) return returnModel;
            var dic = resInfos;

            var model = returnModel.PurchaseItems;
            var tempResult = new List<PurchaseItemsModel>();
            var newDicResult = new Dictionary<string, PurchaseItemsModel>();

            model.SelectMany(pro => pro.SubItems.Select(sku => new { pro, sku }))
                .ToList().ForEach(item =>
                {
                    // 拷贝对象
                    var newPro = CommUtls.DeepClone(item.pro);

                    newPro.IsRelationBaseProduct = item.sku.IsRelationBaseProduct;
                    if (newPro.IsRelationBaseProduct)
                    {
                        // 找到对应的基础商品数据
                        dic.TryGetValue(item.sku.BaseProductSkuUid, out var baseProductSku);
                        if (baseProductSku != null)
                        {
                            // 替换数据
                            // 商品图片
                            (string skuimgurl, string producturl) = (string.Empty, string.Empty);
                            if (!baseProductSku.ProductSkuImgUrl.Contains("http"))
                            {
                                skuimgurl = CustomerConfig.AlibabaFenFaSystemUrl + baseProductSku.ProductSkuImgUrl.Replace("Common/GetImageFile", "ImageFile/Get");
                                producturl = CustomerConfig.AlibabaFenFaSystemUrl + baseProductSku.ProductImgUrl.Replace("Common/GetImageFile", "ImageFile/Get");
                            }
                            else
                            {
                                skuimgurl = baseProductSku.ProductSkuImgUrl;
                                producturl = baseProductSku.ProductImgUrl;
                            }

                            item.sku.SkuImgUrl = skuimgurl;
                            newPro.ProductImgUrl = producturl;

                            // 规格属性
                            item.sku.Color = baseProductSku.ProductSpecs;
                            item.sku.Size = string.Empty;
                            // 商品名称
                            newPro.ProductSubject = baseProductSku.ProductSubject;
                            // 商品简称
                            newPro.ShortTitle = baseProductSku.ShortTitle;
                            // SkuCode商家编码
                            item.sku.CargoNumber = baseProductSku.ProductSkuCode;
                            // SpuCode商家编码
                            newPro.ProductCargoNumber = baseProductSku.SpuCode;
                            // 基础商品Uid
                            newPro.BaseProductUid = baseProductSku.SpuUid;
                            // 价格为0
                            item.sku.CostPrice = 0;
                            newPro.SubItems = new List<PurchaseSubItemModel> { item.sku };
                        }
                    }
                    tempResult.Add(newPro);
                });

            // 已关联基础商品的订单
            var relationBaseProduct = tempResult.Where(x => x.IsRelationBaseProduct).ToList();
            // 未关联基础商品的订单
            var noRelationBaseProduct = tempResult.Where(x => !x.IsRelationBaseProduct).ToList();

            if (relationBaseProduct.Any())
                // 合并订单
                foreach (var models in relationBaseProduct.GroupBy(pro => pro.BaseProductUid))
                {
                    var newPro = models.First();

                    // 合并订单
                    models.SelectMany(x => x.SubItems)
                        .GroupBy(x => x.BaseProductSkuUid)
                        .ToList()
                        .ForEach(group =>
                        {
                            var sku = group.First();
                            sku.Count = group.Sum(x => x.Count);
                            sku.ItemAmount = 0;
                            sku.Price = 0;
                            sku.SkuIds = group.Select(x => x.SkuId).ToList();
                            if (newPro.SubItems.Contains(sku)) return;
                            newPro.SubItems.Add(sku);
                        });
                    newPro.ProductId = newPro.BaseProductUid.ToString();
                    newPro.HasMsgOrRemark = models.Any(x => x.HasMsgOrRemark);
                    newPro.TotalAmount = 0;
                    newPro.TotalItemCount = newPro.SubItems.Sum(x => x.Count);
                    if (!newDicResult.ContainsKey(newPro.ProductId)) newDicResult.Add(newPro.ProductId, newPro);
                }

            if (noRelationBaseProduct.Any())
                // 遍历子项去除存在基础商品的订单
                noRelationBaseProduct.ForEach(pro =>
                {
                    pro.SubItems = pro.SubItems.Where(x => !x.IsRelationBaseProduct).ToList();
                    pro.TotalItemCount = pro.SubItems.Sum(x => x.Count);
                    pro.TotalAmount = pro.SubItems.Sum(x => x.ItemAmount);
                    if (!newDicResult.ContainsKey(pro.ProductId))
                        newDicResult.Add(pro.ProductId, pro);
                });
            
            var newResult = newDicResult.Values.ToList();

            returnModel.PurchaseItems = newResult;
            returnModel.TotalItemCount = newResult.Sum(x => x.TotalItemCount);
            returnModel.TotalAmount = newResult.Sum(x => x.TotalAmount);

            return returnModel;
        }

        public void DecryptPddToName(PurchasesReturnModel prm)
        {
            var curShop = SiteContext.Current.CurrentLoginShop;
            // 拼多多买家留言展开买家昵称解密
            if (curShop.PlatformType == PlatformType.Pinduoduo.ToString())
            {
                var oDic = new Dictionary<string, PurchaseOrderModel>();
                prm.Orders.GroupBy(x => x.ShopId).ToList().ForEach(g =>
                {
                    g.ToList().ForEach(o =>
                    {
                        if (!oDic.ContainsKey(o.PlatformOrderId + o.ShopId))
                            oDic.Add(o.PlatformOrderId + o.ShopId, o);
                    });
                    var temp = SiteContext.Current.AllShops.FirstOrDefault(y => y.Id == g.Key);
                    if (temp != null)
                    {
                        var service = new PinduoduoPlatformService(temp);
                        var orders = service.DecryptBatch(oDic.Values.Select(o => new Order
                        {
                            PlatformOrderId = o.PlatformOrderId,
                            ShopId = o.ShopId,
                            ToName = o.BuyerWangWang
                        }).ToList(), true, field: "ToName");
                        orders.ForEach(o =>
                        {
                            PurchaseOrderModel order;
                            if (oDic.TryGetValue(o.PlatformOrderId + o.ShopId, out order))
                                order.BuyerWangWang = o.ToName;
                        });
                    }
                });
                prm.Orders = oDic.Values.ToList();
            }
        }

        /// <summary>
        /// 获取历史备货单
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public PagedResultModel<PurchaseHistory> GetHistoryList(int shopId, int pageIndex, int pageSize, List<string> fields)
        {
            return _repository.GetHistoryList(shopId, pageIndex, pageSize, fields);
        }

        /// <summary>
        /// 获取规格简称
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="skuCode"></param>
        /// <returns></returns>
        public List<ProductSkuInfoFx> GetSkuShortTitle(int fxUserId, List<string> skuCode)
        {
            return _repository.GetSkuShortTitle(fxUserId, skuCode);
        }

        public bool GetPurchaseDbData(int currShopId)
        {
            return _repository.GetPurchaseDbData(currShopId);
        }
    }
}
