using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services.Services
{
    /// <summary>
    /// 快捷查询
    /// </summary>
    public class QuickSearchService: BaseService<QuickSearch>
    {
        private const string setting_key_waitorder = "/ErpWeb/WaitOrder/QuickSearch";
        private const string setting_key_allorder = "/ErpWeb/AllOrder/QuickSearch";
        private QuickSearchRepository _repository;
        CommonSettingService _commonSettingService = new CommonSettingService();

        public QuickSearchService()
        {
            _repository = new QuickSearchRepository();
            this._baseRepository = _repository;
        }



        #region 查询

        /// <summary>
        /// 获取用户QuickSearch表中所有的数据
        /// </summary>
        /// <param name="fxuserid"></param>
        /// <returns></returns>
        private List<QuickSearch> getUserQuickSearch(int fxuserid, QuickSearchMethod method)
        {
            List<QuickSearch> person = new List<QuickSearch>();
            switch (method)
            {
                case QuickSearchMethod.AllOrder:
                case QuickSearchMethod.WaitOrder:
                {
                    var system = _repository.getSystem(method.ToString());
                    var user = _repository.getUsers(fxuserid, method.ToString());
                    if (system != null && system.Count > 0)
                    {
                        person.AddRange(system);
                    }
                    if (user != null && user.Count > 0)
                    {
                        person.AddRange(user);
                    }
                    person = FilterPolicy(person);
                    return person;
                }
                //case QuickSearchMethod.AllOrder:return _repository.getUsers(fxuserid, "AllOrder");
                default: return null;
            }
        }

        /// <summary>
        /// 获取用户的快捷查询设置
        /// </summary>
        /// <param name="systemshopid"></param>
        /// <param name="method"></param>
        /// <returns></returns>
        private string getUserSetting(int systemshopid, QuickSearchMethod method)
        {
            CommonSetting setting = null;
            switch (method)
            {
                case QuickSearchMethod.WaitOrder:
                    setting = _commonSettingService.Get(setting_key_waitorder, systemshopid);
                    break;
                case QuickSearchMethod.AllOrder:
                    setting = _commonSettingService.Get(setting_key_allorder, systemshopid);
                    break;
                default: break;
            }
            //还未在Setting表里面记录则只读取系统快捷查询
            if (setting == null)
            {
                string result = "";
                var tempqs = _repository.getSystem(method.ToString());
                if (tempqs != null && tempqs.Count > 0)
                {
                    for (int i = 0; i < tempqs.Count - 1; i++)
                    {
                        result += tempqs[i].Id.ToString() + ",";
                    }
                    result += tempqs[tempqs.Count - 1].Id.ToString();
                }
                return result;
            }
            if (string.IsNullOrEmpty(setting.Value))
                return "";
            return setting.Value;
        }

        /// <summary>
        /// 打单页面过滤平台因素        
        /// </summary>
        /// <param name="input">需要过滤的快捷查询</param>
        /// <returns></returns>
        private List<QuickSearch> FilterPolicy(List<QuickSearch> input)
        {
            if (input == null || input.Count <= 0) return input;
            //平台过滤,过滤掉系统快捷中只在特定平台使用的快捷
            input.RemoveAll(r => r.FxUserId == 0 && r.PlatformType.ToLower() != "all" && r.PlatformType != CustomerConfig.CloudPlatformType);
            return input;
        }

        /// <summary>
        /// 查询用户可见的快捷查询
        /// 设置了自定义快捷但全都不可见则返回null
        /// </summary>
        /// <param name="systemshopid">用户系统店铺id</param>
        /// <returns></returns>
        public List<QuickSearch> getUsersView(int systemshopid, QuickSearchMethod method,int fxuserid)
        {
            List<QuickSearch> result = new List<QuickSearch>();
            string settingvalue = getUserSetting(systemshopid, method);
            var values = settingvalue.Split(',');
            List<long> ids = new List<long>();
            for (int i = 0; i < values.Length; i++)
            {
                long id;
                if(long.TryParse(values[i], out id))
                {
                    ids.Add(id);
                }                
            }
            if (ids.Any())
            {
                var qslist = _repository.getByIds(ids, fxuserid); 
                //排序
                if(qslist != null && qslist.Count > 0)
                {
                    for (int i = 0; i < ids.Count; i++)
                    {
                        if(qslist.Exists(f => f.Id == ids[i]))                        
                        {
                            var qs = qslist.Find(f => f.Id == ids[i]);
                            qs.Display = "block";
                            result.Add(qs);
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 获取用户当前页面所有存在的快捷:
        /// </summary>
        /// <param name="fxuserid"></param>
        /// <returns></returns>
        public List<QuickSearch> getUsersAll(int fxuserid, QuickSearchMethod method)
        {            
            List<QuickSearch> result = new List<QuickSearch>();
            result = getUserQuickSearch(fxuserid,method);
            return result;
        }
        
        /// <summary>
        /// 展示用户带Display的的快捷查询
        /// </summary>
        /// <param name="userview">用户可见的快捷查询</param>
        /// <param name="userall">用户所有的快捷查询</param>
        /// <returns></returns>
        public List<QuickSearch> getContainDisplay(List<QuickSearch> userview,List<QuickSearch> userall, string orderstatus, QuickSearchMethod method)
        {
            List<QuickSearch> result = new List<QuickSearch>();

            if (userall == null) throw new LogicException("用户无快捷查询");
            userall.ForEach(f => {
                if((userview != null && userview.Exists(e=>e.Id == f.Id)))
                {
                    f.Display = "block";
                }
                else
                {
                    f.Display = "none";
                }
            });
            //排序
            if (userview == null || userview.Count <= 0)
            {
                result = userall;
            }
            else
            {
                for (int i = 0; i < userview.Count; i++)
                {
                    userview[i].Display = "block";
                    result.Add(userview[i]);
                }
                for (int i = 0; i < userall.Count; i++)
                {
                    if(!result.Exists(e=>e.Id == userall[i].Id))
                    {
                        result.Add(userall[i]);
                    }
                }
            }

            if (method == QuickSearchMethod.AllOrder)
            {
                //开通1688采购单，快捷菜单强制显示出来（忽略用户配置）,当前用户基本上都有配置，当新的系统快捷菜单上线后，就会导致不显示
                var isShowQingMemu = false;
                if (orderstatus.ToLower() == "waitaudit" && QingService.Instance.IsOpenedPrePay == true)
                {
                    //追加两个快捷菜单
                    //TODO:正式上线后，添加数据库数据后，可移除
                    var curPlatformType = CustomerConfig.CloudPlatformType;
                    if ((curPlatformType == CloudPlatformType.Alibaba.ToString() || curPlatformType == CloudPlatformType.TouTiao.ToString() || curPlatformType == CloudPlatformType.Pinduoduo.ToString()) && result.Any(a => a.Name == "UnordeAndUnpaidBtn") == false)
                    {
                        result.Add(new QuickSearch { Id = 10, PlatformType = curPlatformType, Name = "UnordeAndUnpaidBtn", Title = "未下单未支付", ClassName = "unordeAndUnpaidBtn", Click = "UnordeAndUnpaidBtnClick", Method = "AllOrder" });
                    }
                    if ((curPlatformType == CloudPlatformType.Alibaba.ToString() || curPlatformType == CloudPlatformType.TouTiao.ToString() || curPlatformType == CloudPlatformType.Pinduoduo.ToString()) && result.Any(a => a.Name == "OrderedAndUnpaidBtn") == false)
                    {
                        result.Add(new QuickSearch { Id = 11, PlatformType = curPlatformType, Name = "OrderedAndUnpaidBtn", Title = "已下单未支付", ClassName = "orderedAndUnpaid", Click = "OrderedAndUnpaidBtnClick", Method = "AllOrder" });
                    }

                    isShowQingMemu = true;
                }
                var qingQuickSearchNames = new List<string>() { "OrderedAndUnpaidBtn", "UnordeAndUnpaidBtn" };

                foreach (QuickSearch search in result)
                {
                    if (qingQuickSearchNames.Contains(search.Name))
                    {
                        if (isShowQingMemu)
                        {
                            search.Display = "block";
                        }
                        else
                        {
                            search.Display = "none";
                        }
                    }
                }
            }
            
            return result;
        }





        public QuickSearch GetById(long id)
        {
            return _repository.getById(id);
        }

        #endregion

        #region 编辑

        /// <summary>
        /// 保存快捷查询的Id到Setting表中
        /// </summary>
        /// <param name="systemshopid"></param>
        /// <param name="id"></param>
        /// <param name="method"></param>
        /// <returns></returns>
        private int SetSetting(int systemshopid,long id, QuickSearchMethod method)
        {
            switch(method)
            {
                case QuickSearchMethod.WaitOrder: {
                        var setting = _commonSettingService.Get(setting_key_waitorder, systemshopid);
                        //无设置则读取系统设置做初始化
                        if (setting == null)
                        {
                            string values = "";
                            var systems = _repository.getSystem(method.ToString());
                            if (systems != null && systems.Any())
                            {
                                for (int i = 0; i < systems.Count; i++)
                                {
                                    values += systems[i].Id + ",";
                                }
                            }
                            values += id.ToString();
                            _commonSettingService.Set(setting_key_waitorder, values, systemshopid);
                        }
                        else
                        {
                            if (string.IsNullOrEmpty(setting.Value))
                            {
                                _commonSettingService.Set(setting_key_waitorder, id.ToString(), systemshopid);
                            }
                            else
                            {
                                string values = setting.Value + "," + id.ToString();
                                _commonSettingService.Set(setting_key_waitorder, values, systemshopid);
                            }
                        }
                    } break;
                case QuickSearchMethod.AllOrder:
                    {
                        var setting = _commonSettingService.Get(setting_key_allorder, systemshopid);
                        if(setting == null || string.IsNullOrEmpty(setting.Value))
                        {
                            _commonSettingService.Set(setting_key_allorder, id.ToString(), systemshopid);
                        }
                        else
                        {
                            string values = setting.Value + "," + id.ToString();
                            _commonSettingService.Set(setting_key_allorder, values, systemshopid);
                        }
                    }
                    break;
            }
            return 1;
        }

        /// <summary>
        /// 插入自定义快捷查询
        /// </summary>
        /// <param name="fxuserid">用户Id</param>
        /// <param name="title">名称</param>
        /// <param name="condition">条件</param>
        /// <returns>实际插入库的id</returns>
        public QuickSearch AddCustomer(int fxuserid,int systemshopid, string title,string condition, QuickSearchMethod method)
        {
            var existscount = _repository.getCountByUserTitle(fxuserid, title,method.ToString());
            if (existscount > 0) throw new LogicException("名称已存在");
            existscount = _repository.getCountByUserMethod(fxuserid, method.ToString());
            if (existscount > 100) throw new LogicException("自定义快捷查询最多100个");
            QuickSearch qs = new QuickSearch();
            qs.FxUserId = fxuserid;
            qs.Id = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds();
            qs.Title = title;
            qs.Condition = condition;
            qs.ClassName = "";
            qs.Click = "search";
            qs.Name = "Customer" + qs.Id.ToString();
            qs.PlatformType = "All";
            qs.Render = "";
            qs.CreateTime = DateTime.Now;
            qs.Method = method.ToString();
            qs.SystemShopId = systemshopid;
            try
            {
                _repository.Add(qs);
            }catch(Exception ex)
            {
                if (ex.Message.Contains("primary"))
                {
                    qs.Id = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds();
                    qs.Name = "Customer" + qs.Id.ToString();
                    _repository.Add(qs);
                }
                else
                {
                    throw ex;
                }
            }
            //保存setting
            SetSetting(systemshopid, qs.Id, method);
            return qs;
        }

        /// <summary>
        /// 使用已知id插入自定义快捷查询
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fxuserid"></param>
        /// <param name="systemshopid"></param>
        /// <param name="title"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public long AddCustomerUseId(QuickSearch qs)
        {
            var existscount = _repository.getCountByUserTitle(qs.FxUserId, qs.Title,qs.Method);
            if (existscount > 0) throw new LogicException("名称已存在");
            _repository.Add(qs);
            //设置Setting
            QuickSearchMethod md = QuickSearchMethod.WaitOrder;
            switch (qs.Method.ToLower())
            {
                case "waitorder":md = QuickSearchMethod.WaitOrder;break;
                case "allorder":md = QuickSearchMethod.AllOrder;break;
                default:break;
            }
            SetSetting(qs.SystemShopId, qs.Id, md);
            return qs.Id;
        }


        /// <summary>
        /// 更新快捷查询的排序和可见度
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="systemshopid"></param>
        /// <returns></returns>
        public int UpdateViewAndSort(List<long> ids,int systemshopid,string method)
        {
            string setting_key = setting_key_waitorder;
            switch (method.ToLower())
            {
                case "waitorder":setting_key = setting_key_waitorder;break;
                case "allorder":setting_key = setting_key_allorder;break;
                default:break;
            }
            if (ids == null || ids.Count <= 0)
            {
                return _commonSettingService.Set(setting_key, "", systemshopid);
            }            
            string values = "";            
            for(int i =0;i<ids.Count - 1; i++)
            {
                values += ids[i] + ",";
            }
            values += ids[ids.Count - 1];
            return _commonSettingService.Set(setting_key, values, systemshopid);
        }

        /// <summary>
        /// 删除自定义快捷查询
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fxuserid"></param>
        /// <returns></returns>
        public int DeleteById(long id,int fxuserid,int systemshopid)
        {
            var qs = _repository.getById(id);
            if (qs == null) return 0;
            if (qs.FxUserId != fxuserid) return 0;
            //删除Setting中的值
            string setting_key = setting_key_waitorder;
            switch (qs.Method.ToLower())
            {
                case "waitorder": setting_key = setting_key_waitorder; break;
                case "allorder": setting_key = setting_key_allorder; break;
                default: break;
            }
            var oldsetting = _commonSettingService.Get(setting_key, systemshopid);
            if(oldsetting != null && !string.IsNullOrEmpty(oldsetting.Value))
            {
                //只有一个id直接为空字符,直接判等,多个id则替换
                string newvalues = "";
                if (oldsetting.Value.Trim() != id.ToString())
                {
                    newvalues = oldsetting.Value.Replace("," + id.ToString(), "");
                    newvalues = newvalues.Replace(id.ToString() + ",", "");
                }

                _commonSettingService.Set(setting_key, newvalues, systemshopid);
            }                        
            return _repository.DeleteById(id,fxuserid);
        }

        #endregion

    }
}
