using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Services.PlatformService;

namespace DianGuanJiaApp.Services
{

    public partial class ReceiverMaskDataService
    {
        private ReceiverMaskDataRepository _repository = new ReceiverMaskDataRepository();

        /// <summary>
        /// 查询订单打码信息
        /// </summary>
        /// <param name="relateCodes">格式：ShopId-BuyerHashCode</param>
        /// <returns></returns>
        public List<ReceiverMaskData> GetList(List<string> relateCodes)
        {
            return _repository.GetList(relateCodes);
        }

        public void TryCreateMaskData(ZhiDianNewPlatformService ptservice, Order order)
        {
            if (ptservice == null || order == null || order.ToName.IsDyEncryptData() == false || order.PlatformStatus != "waitsellersend")
                return;
            var relationCode = order.ShopId + "-" + order.BuyerHashCode;
            var mask = new ReceiverMaskData()
            {
                RelationCode = relationCode,
                PlatformOrderId = order.PlatformOrderId,
                ShopId = order.ShopId,
                CreateTime = DateTime.Now,
                //EncryptedString = $"{order.ToName},{order.ToMobile},{order.ToAddress}",
            };
            try
            {
                if (_repository.IsExist(mask.RelationCode))
                    return;

                if (order.Receiver == null)
                {
                    ptservice.DecryptBatch(new List<Order> { order }, true, isNoPing: true);
                    if (string.IsNullOrEmpty(order.ToName) == false && order.ToName.IsDyEncryptData() == false)
                    {
                        mask.ToName = order.ToName;
                        mask.ToMoblie = order.ToMobile;
                        mask.ToAddress = order.ToAddress;
                        _repository.Add(mask);
                    }
                }
                else
                {
                    mask.ToName = order.Receiver.ToNameMask;
                    mask.ToMoblie = order.Receiver.ToPhoneMask;
                    mask.ToAddress = order.Receiver.ToAddressMask;
                    _repository.Add(mask);
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("unique index") == false)
                    Utility.Log.WriteError($"创建订单的收件人脱敏信息时发生错误，{_repository.DbConnection?.ConnectionString}，订单ID:{order.PlatformOrderId},店铺ID:{order.ShopId}，错误详情：{ex}");
            }

        }

        public List<string> TryReplaceEncryptToMaskData(List<Order> orders)
        {
            var existMaskOrders = new List<string>();
            if (orders == null || orders.Any() == false)
                return existMaskOrders;
            var relateCodes = orders.Select(x => $"{x.ShopId}-{x.BuyerHashCode}").Distinct().ToList();
            var masks = GetList(relateCodes);
            if (masks == null || masks.Any() == false)
                return existMaskOrders;
            var maskDict = new Dictionary<string, ReceiverMaskData>();
            foreach (var mask in masks)
            {
                var buyerHashCodes = mask.RelationCode.Split('-').LastOrDefault();
                if (maskDict.ContainsKey(buyerHashCodes) == false)
                    maskDict.Add(buyerHashCodes, mask);
            }

            foreach (var order in orders)
            {
                var key = order.BuyerHashCode;
                if (maskDict.ContainsKey(key))
                {
                    var mask = maskDict[key];
                    order.ToName = mask.ToName;
                    order.ToMobile = mask.ToMoblie;
                    order.ToPhone = mask.ToMoblie;
                    order.BuyerMemberId = mask.ToName;
                    order.BuyerMemberName = mask.ToName;
                    order.BuyerWangWang = mask.ToName;
                    //全地址替换密文
                    order.ToFullAddress = order.ToFullAddress.TrimEnd(order.ToAddress);
                    order.ToAddress = mask.ToAddress;
                    //全地址密文替换为明文详情地址（包含街道地址）
                    order.ToFullAddress = order.ToFullAddress + order.ToAddress;
                    existMaskOrders.Add(order.PlatformOrderId);
                }
            }
            return existMaskOrders;
        }
    }
}