using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using iTextSharp.text;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Services.Services
{
    /// <summary>
    /// 修复子结算价
    /// 近期反馈的用户太多，需要写个工具处理SubProductSettlementPrice历史数据
    ///
    ///   针对存在多条相同的ProductSettlementPriceUniqueCode且IsDelete=0的数据做处理
    ///
    ///   与当前ProductSku表的Name做对比
    ///
    ///   1）SubProductSettlementPrice.SkuName与与ProductSku.Name完全相同的保留
    ///
    ///   2）SubProductSettlementPrice.SkuName与【ProductSku.Name过滤特殊符号】后相同的，IsDelete设为1
    ///
    ///   外围：读取当前云所有业务库，轮询处理
    ///
    ///   工具做成可配置指定库Id，未配置查所有业务库
    /// </summary>
    public class RepairSubProductSettlementPrice : BaseService<SubProductSettlementPrice>
    {
        private const string LogName = "RepairSubProductSettlementPrice.txt";
        private SubProductSettlementPriceRepository _repository;
        private string _dbName = string.Empty;

        public RepairSubProductSettlementPrice()
        {
            _repository = new SubProductSettlementPriceRepository();
        }

        public RepairSubProductSettlementPrice(string connectionString, string dbName)
        {
            _repository = new SubProductSettlementPriceRepository(connectionString);
            _dbName = dbName;
        }

        /*
        public void Start4()
        {
            int pageNumber = 1;
            int pageSize = 500;
            List<string> uniqueCodes = new List<string>();
            do
            {
                uniqueCodes.AddRange(GetDuplicateProductSettlementPriceUniqueCodes(pageNumber, pageSize));
                pageNumber++;
            }
            while (uniqueCodes.Count == pageSize);

            var SettlementPriceProduceSkuSql = @"select sub.* from SubProductSettlementPrice sub WITH(NOLOCK)
                            INNER JOIN FunStringToTableV2 (@Codes, ',') t1 ON t1.item = sub.ProductSettlementPriceUniqueCode;
 
                            SELECT ps.Id, ps.Name, ps.SkuCode FROM ProductSku ps WITH(NOLOCK)
                            INNER JOIN P_ProductSettlementPrice psp WITH(NOLOCK) ON ps.SkuCode = psp.ProductSkuCode
                            INNER JOIN FunStringToTableV2 (@Codes, ',') t1 ON t1.item = psp.UniqueKey;";

            var parameters = new DynamicParameters();
            parameters.Add("Codes", string.Join(",", uniqueCodes));

            // realSql：获取实际执行的 SQL 语句
            var realSql = _repository.GetRealSql(SettlementPriceProduceSkuSql, parameters);

            Log.WriteLine($"SettlementPriceProduceSkuSql={realSql}", LogName);

            using (var multi = _repository.DbConnection.QueryMultiple(SettlementPriceProduceSkuSql, parameters))
            {
                var updateData = new List<SubProductSettlementPrice>();
                var subSettlementPrices = multi.Read<SubProductSettlementPrice>().ToList();
                var productSkus = multi.Read<ProductSkuFx>().ToList();
                foreach (var subSettlementPrice in subSettlementPrices)
                {
                    var productSku = productSkus.FirstOrDefault(ps => ps.SkuCode == subSettlementPrice.SkuCode);
                    if (productSku != null)
                    {
                        if (!string.Equals(productSku.Name, subSettlementPrice.SkuName))
                        {
                            // ProductSku.Name过滤特殊符号
                            var filterSkuName = FilterStr(productSku.Name);
                            if (string.Equals(filterSkuName, subSettlementPrice.SkuName))
                            {
                                // 过滤之后的 skuName 与 subSettlementPrice.SkuName 相同
                                subSettlementPrice.IsDelete = true; // 设置为删除状态
                                updateData.Add(subSettlementPrice); // 添加到更新列表
                            }
                        }
                    }
                }

                if (updateData.Count > 0)
                    _repository.BatchUpdateFields(updateData, new List<string>() { "IsDelete" }); // 更新指定的字段
            }
        }
        */

        public void Start5()
        {
            int pageNumber = 1;
            int pageSize = 500;
            List<string> uniqueCodes = new List<string>();
            do
            {
                uniqueCodes.AddRange(GetDuplicateProductSettlementPriceUniqueCodes(pageNumber, pageSize));
                pageNumber++;
            }
            while (uniqueCodes.Count == pageSize);

            int batch = 1;
            // 分批处理 uniqueCodes，避免一次性查询太多数据
            const int batchProcessSize = 500; // 每批处理500个code
            for (int i = 0; i < uniqueCodes.Count; i += batchProcessSize)
            {
                var batchCodes = uniqueCodes.Skip(i).Take(batchProcessSize).ToList();
                ProcessBatchCodes(batchCodes, batch);
                batch++;
            }
        }

        private void ProcessBatchCodes(List<string> batchCodes, int batch)
        {
            var settlementPriceProduceSkuSql = @"select sub.* from SubProductSettlementPrice sub WITH(NOLOCK)
                    INNER JOIN FunStringToTableV2 (@Codes, ',') t1 ON t1.item = sub.ProductSettlementPriceUniqueCode;

                    SELECT ps.Id, ps.Name, ps.SkuCode FROM ProductSku ps WITH(NOLOCK)
                    INNER JOIN P_ProductSettlementPrice psp WITH(NOLOCK) ON ps.SkuCode = psp.ProductSkuCode
                    INNER JOIN FunStringToTableV2 (@Codes, ',') t1 ON t1.item = psp.UniqueKey;";

            var parameters = new DynamicParameters();
            parameters.Add("Codes", string.Join(",", batchCodes));

            // realSql：获取实际执行的 SQL 语句
            var realSql = _repository.GetRealSql(settlementPriceProduceSkuSql, parameters);

            // 只记录第一批次, 才记录SQL
            if (batch == 1)
            {
                Log.WriteLine($"SettlementPriceProduceSkuSql={realSql}", LogName);
            }

            using (var multi = _repository.DbConnection.QueryMultiple(settlementPriceProduceSkuSql, parameters))
            {
                var updateData = new List<SubProductSettlementPrice>();
                var subSettlementPrices = multi.Read<SubProductSettlementPrice>().ToList();
                var productSkus = multi.Read<ProductSkuFx>().ToList();
                foreach (var subSettlementPrice in subSettlementPrices)
                {
                    var productSku = productSkus.FirstOrDefault(ps => ps.SkuCode == subSettlementPrice.SkuCode);
                    if (productSku != null)
                    {
                        if (!string.Equals(productSku.Name, subSettlementPrice.SkuName))
                        {
                            // ProductSku.Name过滤特殊符号
                            var filterSkuName = FilterStr(productSku.Name);
                            if (string.Equals(filterSkuName, subSettlementPrice.SkuName) && subSettlementPrice.IsDelete == false) // 本身是删除的状态，移除掉
                            {
                                // 过滤之后的 skuName 与 subSettlementPrice.SkuName 相同
                                subSettlementPrice.IsDelete = true; // 设置为删除状态
                                updateData.Add(subSettlementPrice); // 添加到更新列表
                            }
                        }
                    }
                }

                if (updateData.Count > 0)
                    _repository.BatchUpdateFields(updateData, new List<string>() { "IsDelete" }); // 更新指定的字段
            }
        }

        private List<string> GetDuplicateProductSettlementPriceUniqueCodes(int pageNumber, int pageSize)
        {
            if(pageNumber == 1)
            {
                var sql = $@"
UPDATE SubProductSettlementPrice SET [IsDelete] =1 WHERE Id IN(
SELECT MIN(Id)
FROM SubProductSettlementPrice WITH(NOLOCK) 
WHERE IsDelete = 0 
GROUP BY UniqueCode,ProductSettlementPriceUniqueCode 
HAVING COUNT(1) > 1
  )";
                _repository.DbConnection.Execute(sql);
            }

            var sqlstr = @"SELECT ProductSettlementPriceUniqueCode
             FROM SubProductSettlementPrice WITH(NOLOCK) 
             WHERE IsDelete = 0 
             GROUP BY ProductSettlementPriceUniqueCode
             HAVING COUNT(1) > 1
             ORDER BY ProductSettlementPriceUniqueCode
             OFFSET @Offset ROWS
             FETCH NEXT @PageSize ROWS ONLY";

            var parameters = new DynamicParameters();
            parameters.Add("Offset", (pageNumber - 1) * pageSize);
            parameters.Add("PageSize", pageSize);

            var realSql = _repository.GetRealSql(sqlstr, parameters);

            if (pageNumber == 1)
            {
                Log.WriteLine($"GetDuplicateProductSettlementPriceUniqueCodes=\n{realSql}\n_dbName={_dbName}", LogName);
            }

            return _repository.DbConnection.Query<string>(sqlstr, parameters).ToList();
        }

        /// <summary>
        /// 过滤掉特殊符号。比如 +, -, *, / 等
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        private string FilterStr(string name)
        {
            // 过滤字符串中的特殊符号，返回过滤后的字符串
            if (string.IsNullOrEmpty(name))
                return name;

            // 这里可以根据实际需要添加更多的特殊符号
            var newStr = name
                .Replace("!", "")
                .Replace("?", "")
                .Replace("&", "")
                .Replace(">", "")
                .Replace("<", "")
                .Replace("+", " ")
                .Replace("/", ""); 
            return newStr;
        }

        public void Start6()
        {
            var sql = "select * from SubProductSettlementPrice(NOLOCK) where SkuName like '%[%]E%';";
            var subList = _repository.DbConnection.Query<SubProductSettlementPrice>(sql).ToList();
            foreach (var item in subList)
            {
                item.SkuName = System.Web.HttpUtility.UrlDecode(item.SkuName);
            }

            // var updateModel = new BatchUpdater("SubProductSettlementPrice");
            // subList.ForEach(t =>
            // {
            //     var keyDict = new Dictionary<string, string>();
            //     keyDict.Add("Id", t.Id.ToString2());
            //     updateModel.TryAdd(keyDict, "SkuName", t.SkuName.ToString());
            // });

            Log.Debug(() => $"subList总量={subList.Count};", "btn7_Click.txt");
            if (subList.Any())
                _repository.BatchUpdateFields(subList, new List<string>() { "SkuName" });
        }
    }
}
