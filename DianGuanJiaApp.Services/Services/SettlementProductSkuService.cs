using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Reflection;
using System.Text;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.Services.MessageQueue;
using DianGuanJiaApp.Services.Services.SyncDataInterface;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using Elasticsearch.Net;
using MongoDB.Driver.Core.Configuration;
using NPOI.SS.UserModel;
using RabbitMQ.Client.Framing.Impl;
using YUNDA;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Services.Services;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Utility;
using System.Diagnostics;
using NPOI.OpenXmlFormats.Vml;

namespace DianGuanJiaApp.Services
{
    using SettlementType = FinancialSettlementRepository.SettlementType;
    using SettlementRecordChangeType = FinancialSettlementRepository.ChangeType;
    public class SettlementProductSkuService : BaseService<SettlementProductSku>
    {
        private SettlementProductSkuRepository _repository;
        private ProductFxRepository _productRepository;
        private ProductInfoFxRepository _productInfoFxRepository;
        private UserFxRepository _userRepository = new UserFxRepository();
        private FinancialSettlementService _financialSettlementService;
        private string _connectionString = string.Empty;
        private ProductFxService _productFxService;
        private PathFlowReferenceService _pathFlowRefService;
        private SubProductSettlementPriceRepository subProductSettlementPriceRepository;
        private ProductSkuHistoryService productSkuHistoryService;
        private readonly OrderItemRepository _orderItemRepository;
        private readonly SettlementProductSkuNameRepository _settlementProductSkuNameRepository;

        public SettlementProductSkuService()
        {
            _repository = new SettlementProductSkuRepository();
            _productRepository = new ProductFxRepository();
            _financialSettlementService = new FinancialSettlementService();
            _productInfoFxRepository = new ProductInfoFxRepository();
            _productFxService = new ProductFxService();
            _pathFlowRefService = new PathFlowReferenceService();
            subProductSettlementPriceRepository = new SubProductSettlementPriceRepository();
            productSkuHistoryService = new ProductSkuHistoryService();
            _orderItemRepository = new OrderItemRepository();
            _settlementProductSkuNameRepository = new SettlementProductSkuNameRepository();
        }

        public SettlementProductSkuService(string connectionString) : base(connectionString)
        {
            _connectionString = connectionString;
            _repository = new SettlementProductSkuRepository(connectionString);
            subProductSettlementPriceRepository = new SubProductSettlementPriceRepository(connectionString);
            productSkuHistoryService = new ProductSkuHistoryService(connectionString);
            _orderItemRepository = new OrderItemRepository(connectionString);
            _settlementProductSkuNameRepository = new SettlementProductSkuNameRepository(connectionString);
        }

        /// <summary>
        /// 添加结算商品
        /// </summary>
        /// <param name="list"></param>
        /// <param name="isAddDcLog"></param>
        public void AddSettlementProductSku(List<SettlementProductSku> list, bool isAddDcLog = true)
        {
            if (list == null || list.Any() == false)
            {
                return;
            }
            //拷贝一份在对变更记录打标时使用
            var copyList = isAddDcLog ? list.ToJson().ToObject<List<SettlementProductSku>>() : list;
            //去重
            list = list.GroupBy(p => new { p.SkuCode, p.PathFlowCode})
                .Select(g => g.First())
                .ToList();
            //确认已经存在的
            var skuCodes = list.Select(x => x.SkuCode).Distinct().ToList();
            var pathFlowCodes = list.Select(x => x.PathFlowCode).Distinct().ToList();
            var fxUserIds = list.Select(m => m.FxUserId).Distinct().ToList();
            //数据库查询异常，增加重试
            var exceptionHandler = new ExceptionHandler("AddSettlementProductSku");
            var checkRepeatList = exceptionHandler.ExceptionRetryHandler(() =>
                _repository.GetSettlementProductSku(skuCodes, pathFlowCodes, fxUserIds));
            if (checkRepeatList.Any())
            {
                list = list.Where(x => !checkRepeatList.Any(m => m.SkuCode == x.SkuCode && m.PathFlowCode == x.PathFlowCode && m.FxUserId == x.FxUserId)).ToList();
            }

            //_repository.BulkWrite(list, "SettlementProductSku", BulkInsertPKExceptionAction);
            foreach (var item in list)
            {
                try
                {
                    Add(item);
                }
                catch (Exception ex)
                {
                    if (ex.Message?.Contains("PRIMARY KEY") == true || ex.Message?.Contains("UNIQUE KEY") == true)
                        continue;
                    try
                    {
                        Add(item);
                    }
                    catch (Exception ex1)
                    {
                        if (ex1.Message?.Contains("PRIMARY KEY") == true || ex1.Message?.Contains("UNIQUE KEY") == true)
                            continue;
                        throw ex1;
                    }
                }
            }

            ////已删除的恢复
            //var recoveryIds = checkRepeatList.Where(x => x.Status == -1).Select(x => x.Id).ToList();
            //if (recoveryIds.Count > 0)
            //{
            //    _repository.RecoveryStatus(recoveryIds);
            //}

            var recoveryStatusSettlement = checkRepeatList.Where(x => x.Status == -1).ToList();
            var recoveryKeys = recoveryStatusSettlement.Select(x => x.UniqueKey).ToList();
            if (recoveryKeys.Count > 0)
            {
                _repository.RecoveryStatus(recoveryKeys);
            }

            // 调用同步数据接口服务  检查对方(对方可能是商家/厂家)，是否有删除结算价，如果有删除，则需要恢复
            new SyncDataInterfaceService(SiteContext.Current.CurrentFxUserId).RecoveryStatusSettlementProductSkuMerchant(checkRepeatList);

            if (isAddDcLog)
            {
                #region 数据变更日志
                ////当前用户的系统店铺Id
                //var systemShopId = SiteContext.Current?.CurrentFxUserId ?? 0;
                //以商品维度记录 2023.05.06
                List<DataChangeLog> dcLogs = list?.GroupBy(g => new { g.SourceShopId, g.SourceUserId, g.ProductCode }).Select(o => new DataChangeLog
                {
                    DataChangeType = DataChangeTypeEnum.UPDATE,
                    TableTypeName = DataChangeTableTypeName.Product,
                    SourceShopId = o.Key.SourceShopId,//用户维度的存当前用户的系统店铺Id；改回源店铺2023.04.13
                    SourceFxUserId = o.Key.SourceUserId,
                    RelationKey = o.Key.ProductCode,
                    ExtField1 = "AddSettlementProductSku",
                }).ToList();
                new DataChangeLogRepository().Add(dcLogs, 1);
                #endregion

                #region 调用同步数据接口服务
                var fxUserId = SiteContext.Current.CurrentFxUserId;
                new SyncDataInterfaceService(fxUserId).AddSettlementProductSku(list);
                #endregion
            }
            try
            {
                //添加商品规格发货记录
                AddSettlementProductSkuName(copyList);
            }
            catch (Exception ex)
            {
                Log.WriteError($"标记商品规格已发货失败：{ex}", "SettlementProductSkuName.txt");
            }
        }

        /// <summary>
        /// 添加商品规格发货记录
        /// </summary>
        /// <param name="settlementProductSkus"></param>
        /// <param name="isSync"></param>
        public void AddSettlementProductSkuName(List<SettlementProductSku> settlementProductSkus)
        {
            if (settlementProductSkus.IsNullOrEmptyList())
                return;
            settlementProductSkus = settlementProductSkus.Where(t => !string.IsNullOrWhiteSpace(t.OrderItemCode)).ToList();
            if (settlementProductSkus.IsNullOrEmptyList())
                return;
            var orderItemCodes = settlementProductSkus.Select(t => t.OrderItemCode).Distinct().ToList();
            var fields = new List<string>() { "Id", "OrderItemCode", "Color", "Size", "ProductId", "SkuID", "ProductCode", "SkuCode", "CargoNumber" };
            var orderItems = _orderItemRepository.Get(orderItemCodes, fields);
            var list = new List<SettlementProductSkuName>();
            settlementProductSkus.ForEach(t =>
            {
                var orderItem = orderItems.FirstOrDefault(o => o.OrderItemCode == t.OrderItemCode);
                if (orderItem == null)
                    return;
                var skuName = orderItem.Color + orderItem.Size;
                list.Add(new SettlementProductSkuName()
                {
                    ProductId = orderItem.ProductID,
                    ProductCode = orderItem.ProductCode,
                    SkuId = orderItem.SkuID,
                    SkuCode = orderItem.SkuCode,
                    SkuName = skuName,
                    CargoNumber = orderItem.CargoNumber,
                    PathFlowCode = t.PathFlowCode
                });
            });
            //去重
            list = list.GroupBy(p=>p.UniqueCode)
                .Select(g => g.First())
                .ToList();
            _settlementProductSkuNameRepository.BulkAdd(list);
            #region 调用同步数据接口服务
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            new SyncDataInterfaceService(fxUserId).AddSettlementProductSkuName(list);  
            #endregion 调用同步数据接口服务
        }

        public void BulkInsertPKExceptionAction(List<SettlementProductSku> list)
        {
            list.GroupBy(x => x.FxUserId).ToList().ForEach(g =>
            {
                var sql = $"DELETE FROM dbo.SettlementProductSku WHERE  SkuCode IN@SkuCode AND PathFlowCode IN@PathFlowCode AND FxUserId={g.Key};";

                //分页防止in后面的条件超出2.1k
                var pathFlowCodeList = list.Select(f => f.PathFlowCode).Distinct().ToList();
                var skuCodeList = list.Select(f => f.SkuCode).Distinct().ToList();
                var pageSize = 1000;
                var pageCount = (int)decimal.Ceiling(pathFlowCodeList.Count / (decimal)pageSize);
                for (int i = 0; i < pageCount; i++)
                {
                    var pathflowCodes = pathFlowCodeList.Skip(i * pageSize).Take(pageSize);
                    var skuCodes = skuCodeList.Skip(i * pageSize).Take(pageSize);
                    _repository.DbConnection.Execute(sql, new { PathFlowCode = pathflowCodes, SkuCode = skuCodes });
                }
            });
        }

        /// <summary>
        /// 恢复
        /// </summary>
        /// <param name="ids">SettlementProductSku.Id</param>
        /// <returns></returns>
        public bool RecoveryStatus(List<int> ids)
        {
            return _repository.RecoveryStatus(ids);
        }

        /// <summary>
        /// 恢复：使用UniqueKey
        /// </summary>
        /// <param name="keys">SettlementProductSku.UniqueKey</param>
        /// <returns></returns>
        public bool RecoveryStatus(List<string> keys)
        {
            if (keys == null || !keys.Any())
                return false;
            return _repository.RecoveryStatus(keys);
        }

        public PagedResultModel<ProductFx> GetSettlementProductList(SettlementProductQuery query)
        {
            var tuple = _repository.GetSettlementProductList(query);

            //查询对应SKU设置的价格
            //_repository.GetProductSkuSettlementPrice(tuple.Item2, toFxUserId, loginFxUserId);

            //查询对应SKU设置的价格（含对方结算价 & 包含反向对方结算价）
            Stopwatch sw = new Stopwatch();
            if (CustomerConfig.IsDebug)
            {
                sw.Start();
            }
            if(query.IsDeleted != 1)
                _productRepository.GetProductSkuSettlementPrice(query.LoginFxUserId, tuple.Item2, query.UserType, query.ToFxUserIds, true, isAddOldSku: true);
            if (CustomerConfig.IsDebug)
            {
                sw.Stop();
                query.QuerySecond += $"  查询结算价：{sw.Elapsed.TotalSeconds.ToString()}s";
            }
            // 查询商家或厂家名称
            var _supplierUserService = new SupplierUserService();
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            //厂家数据源
            var suppliers = _supplierUserService.GetSupplierList(curFxUserId,needEncryptAccount:true);
            //商家数据源
            var agents = _supplierUserService.GetAgentList(curFxUserId,needEncryptAccount:true);
            if (query.UserType == 1)
            {
                tuple.Item2.ForEach(p =>
                {
                    p.Skus.ForEach(s =>
                    {
                        var supplier = suppliers.FirstOrDefault(x => x.SupplierFxUserId == s.ToFxUserId);
                        if (supplier != null)
                            s.ToFxUserName = supplier.SupplierMobileAndRemark;

                        var reverse = agents.FirstOrDefault(x => x.FxUserId == s.ReverseFxUserId);
                        if (reverse != null)
                            s.ReverseFxUserName = $"下游：{reverse.AgentMobileAndRemark}";
                    });
                });
            }
            else
            {
               
                tuple.Item2.ForEach(p =>
                {
                    p.Skus.ForEach(s =>
                    {
                        var agent = agents.FirstOrDefault(x => x.FxUserId == s.ToFxUserId);
                        if (agent != null)
                            s.ToFxUserName = agent.AgentMobileAndRemark;

                        var reverse = suppliers.FirstOrDefault(x => x.SupplierFxUserId == s.ReverseFxUserId);
                        if (reverse != null)
                            s.ReverseFxUserName = $"上游：{reverse.SupplierMobileAndRemark}";

                    });
                });

            }

            if (CustomerConfig.IsDebug)
            {
                sw.Restart();
            }
            #region 上下游商品信息展示


            var pathflowService = new PathFlowService(_connectionString);
            var pCodes = tuple.Item2.Select(x => x.ProductCode).Distinct().ToList();
            var fields = "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
            var pathFlows = pathflowService.GetPathFlows(pCodes, 0, fields);
            

            if (CustomerConfig.IsDebug)
            {
                sw.Stop();
                query.QuerySecond += $"  查询商品路径：{sw.Elapsed.TotalSeconds.ToString()}s";
            }

            if (CustomerConfig.IsDebug)
            {
                sw.Restart();
            }

            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            var nodes = pathFlows.SelectMany(x => x.PathFlowNodes).ToList();
            var pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(nodes);
            var commonSettingRepository = new CommonSettingRepository();

            if (CustomerConfig.IsDebug)
            {
                sw.Stop();
                query.QuerySecond += $"  处理pathFlowNodeDic：{sw.Elapsed.TotalSeconds.ToString()}s";
            }

            if (CustomerConfig.IsDebug)
            {
                sw.Restart();
            }

            //商家店铺数据
            var agentShopList = new ShopRepository().GetShopByIds(tuple.Item2.Select(s => s.SourceShopId).Distinct().ToList());


            if (CustomerConfig.IsDebug)
            {
                sw.Stop();
                query.QuerySecond += $"  查询商家店铺：{sw.Elapsed.TotalSeconds.ToString()}s";
            }

            if (CustomerConfig.IsDebug)
            {
                sw.Restart();
            }

            tuple.Item2.ForEach(p =>
            {
                //商品已经解绑就不受数据隐藏权限配置影响
                bool isShowProductAuth = pathFlows.Any(x => x.PathFlowReferences.ContainsKey(p.ProductCode) && x.PathFlowNodes.Any(y => y.DownFxUserId == curFxUserId));

                //未解绑
                var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(p.ProductCode)).ToList();

                if (isShowProductAuth)
                {
                    // 是否商品绑定厂家
                    p.IsCurBindProduct = pathFlows.Any(x => x.PathFlowReferences.ContainsKey(p.ProductCode) && x.PathFlowNodes.Any(y => y.FxUserId == curFxUserId));

                    // 商品标题是否可见
                    _productFxService.ConvertShowProductTitle(curFxUserId, productPathFlows, pathFlowNodeDic, p, null, commonSettingRepository);

                    // 商品图片是否可见
                    _productFxService.ConvertShowProductImg(curFxUserId, productPathFlows, pathFlowNodeDic, p, null, commonSettingRepository);

                    // 店铺名称是否可见
                    _productFxService.ConvertShowShopName(curFxUserId, productPathFlows, pathFlowNodeDic, p, null, commonSettingRepository, agentShopList);
                }

                p.Skus.ForEach(s =>
                {
                    var skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(s.SkuCode)).ToList();
                    if (skuPathFlows == null || skuPathFlows.Any() == false)
                        skuPathFlows = productPathFlows;

                    var isShowSkuAuth = pathFlows.Any(x => x.PathFlowReferences.ContainsKey(s.SkuCode) && x.PathFlowNodes.Any(y => y.DownFxUserId == curFxUserId));
                    if (isShowSkuAuth)
                    {
                        // 商品标题是否可见
                        //商品跟随sku权限
                        if (!p.IsCurBindProduct)
                        {
                            _productFxService.ConvertShowProductTitle(curFxUserId, skuPathFlows, pathFlowNodeDic, p, null, commonSettingRepository);
                            _productFxService.ConvertShowProductImg(curFxUserId, skuPathFlows, pathFlowNodeDic, p, null, commonSettingRepository);
                        }

                        // 商品图片是否可见
                        _productFxService.ConvertShowProductImg(curFxUserId, skuPathFlows, pathFlowNodeDic, null, s, commonSettingRepository);

                        // 店铺名称是否可见
                        if (string.IsNullOrWhiteSpace(p.AgentShopName))
                        {
                            _productFxService.ConvertShowShopName(curFxUserId, skuPathFlows, pathFlowNodeDic, p, null, commonSettingRepository, agentShopList);
                        }
                    }
                    else if (p.IsCurBindProduct)
                    {
                        //规格跟随商品权限
                        _productFxService.ConvertShowProductImg(curFxUserId, productPathFlows, pathFlowNodeDic, null, s, commonSettingRepository);
                    }

                    // 2025-07-07：Sku来源商品，判断商品路径上游是否有设置销售价不可见
                    s.IsShowSalePrice = true;
                    foreach (var flow in skuPathFlows)
                    {
                        s.IsShowSalePrice = _productFxService.GetIsShowSalePrice(curFxUserId, flow.PathFlowCode, pathFlowNodeDic);
                        if (s.IsShowSalePrice == false)
                            break;
                    }
                    if (s.IsShowSalePrice == false) s.SalePrice = 0;
                });
            });

            if (CustomerConfig.IsDebug)
            {
                sw.Stop();
                query.QuerySecond += $"  处理上下游商品数据：{sw.Elapsed.TotalSeconds.ToString()}s";
            }

            #endregion

            //if (CustomerConfig.IsDebug)
            //{
            //    sw.Stop();
            //    query.QuerySecond += $"  上下游商品信息展示：{sw.Elapsed.TotalSeconds.ToString()}s";
            //}
            if (query.IsUnSetPrice != 0)
            {
                //重新过滤是否设置结算价
                //因为会附加旧规格所以进行二次过滤是否设置结算价条件
                //tuple.Item2.ForEach(t =>
                //{
                //    t.Skus = t.Skus.Where(s => query.IsUnSetPrice == 1 ? s.SettlementPrice == "未设置": s.SettlementPrice != "未设置").ToList();
                //});
            }
            if (CustomerConfig.IsDebug)
            {
                sw.Restart();
            }
            #region 商品归一数据替换
            List<ProductFx> newResult = new List<ProductFx>();
           
            if (query.IsBaseProcuctCombine && query.Source == 1)
            {
                List<long> baseProductSkuIds = tuple.Item2.SelectMany(p => p.Skus).Where(sku => sku.IsRelationBaseProduct)
                                      .Select(sku => sku.BaseProductSkuUid)
                                      .ToList();

                Dictionary<long, BaseProductSkuSimpleRes> baseProductSkuDic = new BaseProductSkuCommonService().GetSkuListBySkuIds(baseProductSkuIds);
                if (baseProductSkuDic.Any())
                {
                    foreach (ProductFx product in tuple.Item2)
                        foreach (ProductSkuFx productSkuFx in product.Skus)
                        {
                            // 基础商品信息替换赋值
                            if (baseProductSkuDic.TryGetValue(productSkuFx.BaseProductSkuUid, out BaseProductSkuSimpleRes baseProductSku))
                            {
                                // 商品维度数据替换
                                product.ImageUrl = baseProductSku.ProductImgUrl;
                                product.Subject = baseProductSku.ProductSubject;
                                product.BaseProductUid = baseProductSku.SpuUid;

                                // sku 维度数据替换
                                productSkuFx.Name = baseProductSku.ProductSubject; // 商品标题
                                productSkuFx.ImgUrl = baseProductSku.ProductSkuImgUrl; // 商品图片
                                productSkuFx.ProductCargoNumber = baseProductSku.SpuCode; // 商家编码-> 商品编码
                                productSkuFx.CargoNumber = baseProductSku.ProductSkuCode; // 商家编码-> 规格编码
                                productSkuFx.AttributeValue1 = baseProductSku.ProductSpecs; // 商家规格
                                productSkuFx.AttributeValue2 = "";
                                productSkuFx.ShortTitle = baseProductSku.ShortTitle; // 商品简称
                                productSkuFx.SkuId = baseProductSku.ProductSkuId.ToString();
                            }
                        }

                    var relationBaseProduct = tuple.Item2.Where(x => x.Skus.Any(p => p.IsRelationBaseProduct)).ToList(); // 已关联基础商品的订单
                    var not_relationBaseProduct = tuple.Item2.Where(x => !x.Skus.Any(p => p.IsRelationBaseProduct)).ToList(); // 未关联基础商品的订单

                    // 已关联 按基础商品 BaseProductSkuUid 分组, 组装给 newResult
                    foreach (var baseProductGroup in relationBaseProduct.GroupBy(j => j.BaseProductUid))
                    {
                        var item = baseProductGroup.FirstOrDefault();
                        ProductFx newmodel = CommUtls.DeepClone(item);
                        item.Skus.GroupBy(k => k.BaseProductSkuUid).ToList().ForEach(group =>
                        {
                            var sku = group.First();
                            if (newmodel.Skus.Contains(sku)) return;
                            newmodel.Skus.Add(sku);
                        });
                        newResult.Add(newmodel);
                    }

                    // 未关联 按店铺商品 ProductCode 分组, 组装给 newResult
                    foreach (var item in not_relationBaseProduct)
                    {
                        ProductFx newmodel = CommUtls.DeepClone(item);
                        item.Skus.GroupBy(k => k.ProductCode).ToList().ForEach(group =>
                        {
                            var sku = group.First();
                            if (newmodel.Skus.Contains(sku)) return;
                            newmodel.Skus.Add(sku);
                        });
                        newResult.Add(newmodel);
                    }
                }
                else newResult = tuple.Item2;
            }
            else  newResult = tuple.Item2;

            #endregion
            if (CustomerConfig.IsDebug)
            {
                sw.Stop();
                query.QuerySecond += $"  商品归一数据替换：{sw.Elapsed.TotalSeconds.ToString()}s";
            }
            return new PagedResultModel<ProductFx>()
            {
                PageIndex = query.PageIndex,
                PageSize = query.PageSize,
                Rows = newResult,
                Total = tuple.Item1,
                QuerySecond = query.QuerySecond
            };
        }

        /// <summary>
        /// 财务结算 获取店铺的成本价商品列表
        /// </summary>
        /// <param name="query"></param>
        /// <param name="isSetCost">查询设置成本价的商品 0-全部 1-已设置 2-未设置</param>
        /// <returns></returns>
        public PagedResultModel<ProductFx> GetCostPriceProductList(int fxUserId,ProductFxRequertModule query,int isSetCost=0)
        {
            // 查询商品信息 已包含上下游，sku是跟着product的默认上下游
            var resultModel = _productFxService.GetProductFxListWithCostPrice(fxUserId, query,isSetCost);
            var products = resultModel.Rows;
            var skuCodes = products.SelectMany(p => p.Skus).Select(s => s.SkuCode).ToList();
            
            var downFxUserIds = products.Select(p => p.DownFxUserId).Where(id=>id>0).Distinct().ToList();


            // 获取sku额外绑定的下游厂家Id
            var SupplierAgentModels = _productRepository.GetSupplierAndAgentByAllCodes(null,skuCodes, fxUserId);
            downFxUserIds.AddRange(SupplierAgentModels.Select(r=>r.DownFxUserId));
            downFxUserIds = downFxUserIds.Distinct().ToList();

            var suppliers = new SupplierUserService().GetSupplierList(fxUserId,needEncryptAccount:true);//厂家
            var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");

            // 对方结算价和我的结算价
            var settlementTuple = GetSettlementPrice(skuCodes, downFxUserIds, fxUserId);
            var oppositeSettlementPrices = settlementTuple.Item1;
            var mySettlementPrice = settlementTuple.Item2;

            var defaultSettlementPrices = _productRepository.GetProductSettlementDefaultSupplierSettlementPrice(skuCodes, fxUserId, true);
            // 结算价并入商品列表
            ProductsIntoMoreInfo(products,oppositeSettlementPrices, mySettlementPrice,null, SupplierAgentModels, null, supplierList, defaultSettlementPrices);
            // 获取sku成本价 
            //_repository.GetSkuCostPrice(fxUserId, products);
            //resultModel.Total = products.Count;
            return resultModel;
        }

        /// <summary>
        /// 商品列表页面 获取结算价商品数据
        /// </summary>
        /// <param name="productCodes">选中的product和选中sku对应的product</param>
        /// <param name="skuCodes">选中的sku</param>
        /// <param name="noCheckedProductCodes">未选中的product</param>
        /// <param name="noCheckedSkuCodes">未选中的sku</param>
        /// <param name="fxUserId"></param>
        /// <param name="queryType">查询类型，对应前端 type = product,单个商品下所有sku，type = sku，单个sku，type = checked，勾选批量编辑</param>
        /// <returns></returns>
        public object GetSettlementProductList(List<string> productCodes,List<string> skuCodes, List<string> noCheckedSkuCodes, List<string> noCheckedProductCodes, int fxUserId,string queryType = "checked")
        {
            if (skuCodes == null)
            {
                skuCodes = new List<string>();
            }
            if (noCheckedSkuCodes==null)
            {
                noCheckedSkuCodes = new List<string>();
            }
            if (noCheckedProductCodes == null)
            {
                noCheckedProductCodes = new List<string>();
            }
            // 选中的product
            if (productCodes.IsNullOrEmptyList() && skuCodes.IsNullOrEmptyList())
            {
                return new List<ProductFx>();
            }
            bool isGetSkuSettlement = true;
            

            #region 获取商品列表
            // 获取选中product的商品列表
            var products = _productRepository.GetProductListByCodesV2(productCodes);
            if (products.IsNullOrEmptyList())
            {
                return new List<ProductFx>();
            }

            var pathflowService = new PathFlowService(_connectionString);
            var fields = "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
            var pathFlows = new List<PathFlow>();

            // 未选中product的skuCodes -- 查询其对应商品
            var skus = products.SelectMany(p => p.Skus).ToList();
            var productDic = new Dictionary<string, ProductFx>();
            if (queryType.Equals("sku"))
            {
                if (products.Count != 1)
                {
                    throw new Exception("单个sku编辑查询到了多个或零个商品数据");
                }
                products = products.Take(1).ToList();
                //var c = skus.FindAll(s => skuCodes.Contains(s.SkuCode));
                skus = skus.FindAll(s => skuCodes.Contains(s.SkuCode)).ToList();
                products.First().Skus = skus;

                productDic = products.ToDictionary(p => p.ProductCode);
                productCodes = productDic.Keys.ToList();
            }
            else if (queryType.Equals("checked"))
            {
                productDic = products.ToDictionary(p => p.ProductCode);
                //var addSkuCodes = skuCodes.Except(skus.Select(s => s.SkuCode)).ToList();
                //var addProducts = _productRepository.GetBySkuCodes(addSkuCodes);
                /*addProducts.ForEach(p =>
                {
                    if (!productDic.ContainsKey(p.ProductCode))
                    {
                        productDic[p.ProductCode] = p;
                        //products.Add(p);
                    }
                });*/
                skus = productDic.Select(d => d.Value).SelectMany(p => p.Skus).ToList();

                // 排除未勾选的sku
                var removeSkuDic = new Dictionary<string, List<string>>();
                skus.ForEach(sku =>
                {
                    if (noCheckedProductCodes.Contains(sku.ProductCode)) // 未勾选商品的sku，只保留勾选的sku
                    {
                        if (!skuCodes.Contains(sku.SkuCode))
                        {
                            if (removeSkuDic.ContainsKey(sku.ProductCode))
                            {
                                removeSkuDic[sku.ProductCode].Add(sku.SkuCode);
                            }
                            else
                            {
                                removeSkuDic[sku.ProductCode] = new List<string>() { sku.SkuCode };
                            }
                        }
                    }
                    else if (noCheckedSkuCodes.Contains(sku.SkuCode)) // 勾选了商品，排除反选的sku
                    {
                        if (removeSkuDic.ContainsKey(sku.ProductCode))
                        {
                            removeSkuDic[sku.ProductCode].Add(sku.SkuCode);
                        }
                        else
                        {
                            removeSkuDic[sku.ProductCode] = new List<string>() { sku.SkuCode };
                        }
                    }
                });
                foreach (var pcode in removeSkuDic.Keys)
                {
                    var p = productDic[pcode];
                    var removeScodes = removeSkuDic[pcode];
                    p.Skus.RemoveAll(s => removeScodes.Contains(s.SkuCode));
                }
                products = productDic.Select(d => d.Value).ToList();
                productCodes = productDic.Keys.ToList();
            }
            else if (queryType.Equals("product"))
            {
                var productCode = products.First().ProductCode;
                pathFlows = pathflowService.GetPathFlows(new List<string>() { productCode }, 0, fields);
                //兼容新旧数据补上Config信息
                _productFxService.CompatibleOldDataToAddConfig(pathFlows);

                //商品维度需要排除其它的厂家规格
                var newSkus = new List<ProductSkuFx>();
                products.First().Skus.ForEach(sku =>
                {
                    var IsSelfShop = sku.SourceUserId == fxUserId;
                    var skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.SkuCode)).ToList();
                    if (skuPathFlows.IsNotNullAndAny())
                    {
                        // 非厂家链上的Sku不展示
                        if (skuPathFlows.Any(x => x.PathFlowNodes.Any(t => t.FxUserId == fxUserId)) == false)
                            return;
                    }
                    else
                    {
                        //是否商品绑定厂家
                        var IsCurBindProduct = pathFlows.Any(x => x.PathFlowReferences.ContainsKey(productCode) && x.PathFlowNodes.Any(y => y.FxUserId == fxUserId));
                        if (!IsCurBindProduct && !IsSelfShop)
                            return;
                    }
                    newSkus.Add(sku);
                });
                products.First().Skus = newSkus;
            }
            #endregion
            
            skuCodes = products.SelectMany(p => p.Skus).Select(s => s.SkuCode).ToList();

            #region 构建商品-sku关联集合
            var skuCodeSet = new HashSet<string>(skuCodes);
            var productSkuDic = new Dictionary<string, string>();

            foreach (var product in products)
            {
                foreach (var sku in product.Skus)
                {
                    if (skuCodeSet.Contains(sku.SkuCode))
                    {
                        productSkuDic[sku.SkuCode] = product.ProductCode;
                    }
                }
            }
            #endregion
            #region
            //var products = new List<ProductFx>();
            //var productSkuDic = new Dictionary<string, string>();
            /*
            if (skuCodes.Any())
            {
                products = _productRepository.GetProductListBySkuCodes(skuCodes);
                var skuCodeSet = new HashSet<string>(skuCodes);
                productSkuDic = new Dictionary<string, string>();

                foreach (var product in products)
                {
                    foreach (var sku in product.Skus)
                    {
                        if (skuCodeSet.Contains(sku.SkuCode))
                        {
                            productSkuDic[sku.SkuCode] = product.ProductCode;
                        }
                    }
                }
            }
            else
            {
                products = _productRepository.GetProducts(productCodes, fxUserId);
            }*/

            //var allCodes = productCodes.Concat(skuCodes).ToList();
            #endregion

            #region 说明
            // 获取设置了结算价的商品和sku列表 包括同步对象toFxUserId
            // settlementProducts中默认toFxUserId=DownFxUserId，reverseFxUserId=UpFxUserId，需要进行处理
            // 不传入skuCodes，isGetSkuSettlement = false，则不查询sku的结算价
            #endregion
            if (isGetSkuSettlement)
            {
                // 获取上下游Id
                var SupplierAgentModels = _productRepository.GetSupplierAndAgentByAllCodes(productCodes,skuCodes, fxUserId,true);
                // 有的商品有上下游 但sku没上下游的 要设置sku的上下游为商品的上下游
                var resultCodes = new HashSet<string>(SupplierAgentModels.Select(s => s.ProductRefCode)); // 有上下游的sku和商品 去重
                var existingProductRefCodes = SupplierAgentModels.ToLookup(r => r.ProductRefCode, r => new { r.DownFxUserId, r.UpFxUserId });

                skuCodes.Where(scode => !resultCodes.Contains(scode)).ToList() // 过滤出没有上下游的sku
                        .ForEach(scode =>
                        {
                            if (productSkuDic.TryGetValue(scode, out string productCode) && existingProductRefCodes[productCode].Any())
                            {
                                // 优先选有上下游的
                                var productResult = existingProductRefCodes[productCode].Where(e=>e.DownFxUserId>0 && e.UpFxUserId>0).FirstOrDefault() ?? existingProductRefCodes[productCode].FirstOrDefault();
                                SupplierAgentModels.Add(new SupplierAgentModel()
                                {
                                    ProductRefCode = scode,
                                    DownFxUserId = productResult.DownFxUserId,
                                    UpFxUserId = productResult.UpFxUserId
                                });
                            }
                        });

                var upFxUserId = SupplierAgentModels.Select(s=>s.UpFxUserId).Where(i=>i > 0).Distinct().ToList();
                var downFxUserId = SupplierAgentModels.Select(s => s.DownFxUserId).Where(i => i > 0).Distinct().ToList();
                var toFxUserIds = upFxUserId.Concat(downFxUserId).ToList();

                // 获取上下游名称
                var agents = new SupplierUserService().GetAgentList(fxUserId,needEncryptAccount:true); //商家
                var agentsList = agents?.GroupBy(x => x.FxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.AgentMobileAndRemark ?? "");

                var suppliers = new SupplierUserService().GetSupplierList(fxUserId,needEncryptAccount:true);//厂家
                var supplierList = suppliers?.GroupBy(x => x.SupplierFxUserId).ToDictionary(x => x.Key, x => x.FirstOrDefault()?.SupplierMobileAndRemark ?? "");

                var settlementTuple = GetSettlementPrice(skuCodes,toFxUserIds,fxUserId,true);
                var oppositeSettlementPrices = settlementTuple.Item1;
                var mySettlementPrice = settlementTuple.Item2;
                var costPrice = settlementTuple.Item3;
                var defaultSettlementPrices = _productRepository.GetProductSettlementDefaultSupplierSettlementPrice(skuCodes, fxUserId, true);

                // 结算价商品列表数据和上下游对象信息并入所有商品列表
                ProductsIntoMoreInfo(products, oppositeSettlementPrices, mySettlementPrice,costPrice, SupplierAgentModels, agentsList,supplierList, defaultSettlementPrices);

                // 查询商品成本价
                //_repository.GetSkuCostPrice(fxUserId, products, skuCodes);
            }

            #region 查询商品和sku简称信息
            var productInfos = new List<ProductInfoFx>();
            if (products != null && products.Any())
            {
                productInfos = _productInfoFxRepository.GetProductInfoList(fxUserId, productCodes);
            }

            // 禁用标题编辑
            var codes = products.SelectMany(a => a.Skus).ToList().Select(a => a.SkuCode).Distinct().ToList();
            var productSkuRelations = _productInfoFxRepository.GetProductSkuRelationList(fxUserId, skuCodes);
            products?.ForEach(p =>
            {
                var pinfo = productInfos.FirstOrDefault(x => x.ProductCode == p.ProductCode);
                //if (pinfo != null)
                //{
                //    p.ShortTitle = pinfo?.ShortTitle;
                //    p.Weight = pinfo?.Weight ?? 0;
                //    p.IsSelfShop = p.SourceUserId == fxUserId;
                //    var skuCount = p.Skus.Count();
                //    for (var i = skuCount - 1; i >= 0; i--)
                //    {
                //        var s = p.Skus[i];
                //        var sku = pinfo?.Skus?.FirstOrDefault(x => x.SkuCode == s.SkuCode);

                //        var relation = productSkuRelations.Any(a => a.ProductCode == s.ProductCode && a.ProductSkuCode == s.SkuCode && a.Status==1);
                //        if (relation)
                //            s.ShortTitleDisabled = true;
                //        s.ShortTitle = sku?.ShortTitle;
                //        s.Weight = sku?.Weight ?? 0;
                //        s.ImgUrl = String.IsNullOrEmpty(s.ImgUrl) ? p.ImageUrl : s.ImgUrl;
                //    }
                //}
                var skuCount = p.Skus.Count();
                if (pinfo != null)
                {
                    p.ShortTitle = pinfo?.ShortTitle;
                    p.Weight = pinfo?.Weight.ConvertGToKg() ?? 0;
                    p.IsSelfShop = p.SourceUserId == fxUserId;
                }
                for (var i = skuCount - 1; i >= 0; i--)
                {
                    var s = p.Skus[i];
                    if(pinfo != null)
                    {
                        var sku = pinfo?.Skus?.FirstOrDefault(x => x.SkuCode == s.SkuCode);
                        s.ShortTitle = sku?.ShortTitle;
                        s.Weight = sku?.Weight.ConvertGToKg() ?? 0;
                        s.ImgUrl = String.IsNullOrEmpty(s.ImgUrl) ? p.ImageUrl : s.ImgUrl;
                    }
                    var relation = productSkuRelations.Any(a => a.ProductCode == s.ProductCode && a.ProductSkuCode == s.SkuCode && a.Status == 1);
                    if (relation && SiteContext.Current.IsWhiteUser)
                        s.ShortTitleDisabled = true;
                }
            });
            #endregion
            #region  上下游商品信息是否可见

            //获取Sku路径
            var pCodes = products.Select(x => x.ProductCode).Distinct().ToList();
            pathFlows = pathflowService.GetPathFlows(pCodes, 0, fields);

            
            var logicOrderRepository = new LogicOrderRepository(_connectionString);
            var nodes = pathFlows.SelectMany(x => x.PathFlowNodes).ToList();
            var pathFlowNodeDic = logicOrderRepository.GetSortPathFlowNodes(nodes);
            var commonSettingRepository = new CommonSettingRepository();

            products.ForEach(p =>
            {
                //替换规格名中的单引号，规格名中的单引号，会导致前端异常
                if (p.Skus.Any(t => t.Name != null && (t.Name.Contains("'") || t.Name.Contains("\""))))
                {
                    //将存在的英文单引号替换成中文单引号
                    p.Skus.ForEach(sku =>
                    {
                        if (sku.Name != null)
                        {
                            sku.Name = sku.Name.Replace("'", "’").Replace("\"", "”");
                        }
                    });
                }
            
                var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(p.ProductCode)).ToList();
                p.IsCurBindProduct = pathFlows.Any(x => x.PathFlowReferences.ContainsKey(p.ProductCode) && x.PathFlowNodes.Any(y => y.FxUserId == fxUserId));

                // 商品标题是否可见
                _productFxService.ConvertShowProductTitle(fxUserId, productPathFlows, pathFlowNodeDic, p, null, commonSettingRepository);

                // 商品图片是否可见
                _productFxService.ConvertShowProductImg(fxUserId, productPathFlows, pathFlowNodeDic, p, null, commonSettingRepository);

                p.Skus.ForEach(sku =>
                {
                    var skuPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(sku.SkuCode)).ToList();
                    // 判断当前节点Sku路径是否继承于商品，继承至商品的这一段路径跟随商品显示
                    //var isFromProductPath = skuPathFlows?.Any(x => x.PathFlowReferences[sku.SkuCode].ReferenceConfigs.Any(y => y.FxUserId == fxUserId && y.FromType == 1)) ?? false; 
                    if (skuPathFlows == null || skuPathFlows.Any() == false)
                    {
                        skuPathFlows = productPathFlows;
                    }

                    //绑定的是SKU，商品信息跟随SKU权限
                    if (!p.IsCurBindProduct)
                    {
                        _productFxService.ConvertShowProductTitle(fxUserId, skuPathFlows, pathFlowNodeDic, p, null, commonSettingRepository);
                        _productFxService.ConvertShowProductImg(fxUserId, skuPathFlows, pathFlowNodeDic, p, null, commonSettingRepository);
                    }

                    // 商品图片是否可见
                    _productFxService.ConvertShowProductImg(fxUserId, skuPathFlows, pathFlowNodeDic, null, sku, commonSettingRepository);

                    // 2025-07-07：Sku来源商品，判断商品路径上游是否有设置销售价不可见
                    sku.IsShowSalePrice = true;
                    foreach (var flow in /*productPathFlows*/ skuPathFlows)
                    {
                        sku.IsShowSalePrice = _productFxService.GetIsShowSalePrice(fxUserId, flow.PathFlowCode, pathFlowNodeDic);
                        if (sku.IsShowSalePrice == false)
                            break;
                    }
                    if (sku.IsShowSalePrice == false) sku.SalePrice = 0;
                });
            });

            #endregion
            return new
            {
                products = products,
                skuCodes = skuCodes,
                productCodes = productCodes
            };
        }

        /// <summary>
        /// 商品列表批量同步结算价
        /// </summary>
        /// <param name="productCodes"></param>
        /// <param name="noSyncSkuCodes">前端反选的sku，从同步列表中排除掉</param>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public void SyncSettlementPrice(List<string> productCodes,List<string> skuCodes, List<string> noSyncSkuCodes,int fxUserId)
        {
            
            // 通过productCodes获取商品和sku的信息 包括同步对象toFxUserId
            var products = _productRepository.GetProductListByCodesV2(productCodes);

            var allSkuCodes = products.SelectMany(p => p.Skus.Select(s => s.SkuCode))
                                      .Concat(skuCodes) //skuCode中包含前端页面勾选了sku但没勾选对应product的sku
                                      .Where(scode => !noSyncSkuCodes.Contains(scode))
                                      .Distinct()
                                      .ToList();

            SyncSettlementPriceBySkuCodes(allSkuCodes, fxUserId);
        }

        /// <summary>
        /// 通过skucode同步结算价
        /// </summary>
        /// <param name="skuCodes"></param>
        /// <returns></returns>
        public void SyncSettlementPriceBySkuCodes(List<string> skuCodes,int fxUserId)
        {
            // 获取对方结算价
            var oppositePrices = _financialSettlementService.GetSettlementPriceListBySkuCodes(fxUserId,skuCodes);

            // 上游结算价
            var agentPrices = oppositePrices.Where(p => p.SettlementType == (int)SettlementType.Merchant).ToList();
            // 下游结算价
            var supplierPrice = oppositePrices.Where(p => p.SettlementType == (int)SettlementType.Manufacturer).ToList();

            // 构建产品结算价格模型
            var priceModels = new List<ProductSettlementPrice>();

            oppositePrices.ForEach(o =>
            {
                priceModels.Add(new ProductSettlementPrice()
                {
                    ProductCode = o.ProductCode,
                    ProductSkuCode = o.ProductSkuCode,
                    FxUserId = o.CreateUser,
                    CreateUser = fxUserId,
                    ShopId = o.ShopId,
                    PlatformType = o.PlatformType,
                    Price = o.Price,

                    SettlementType = o.SettlementType == (int)SettlementType.Manufacturer
                                                       ? (int)SettlementType.Merchant
                                                       : (int)SettlementType.Manufacturer,
                });
            });
            
            _financialSettlementService.SetProductSettlementPrice(priceModels, fxUserId, false, (int)SettlementRecordChangeType.Sync,true);
        }

        #region 暂时弃置 -- GetSettlementPriceSku() 获取走了订单路径的sku的结算价商品信息
        /*private Tuple<List<ProductFx>,List<ProductFx>> GetSettlementPriceSku(List<ProductFx> products,int fxUserId,List<string> noSyncSkuCodes = null)
        {
            var upProducts = new List<ProductFx>();
            var downProducts = new List<ProductFx>();
            

            if (products == null || !products.Any())
            {
                return new Tuple<List<ProductFx>, List<ProductFx>>(upProducts, downProducts);
            }
            if (noSyncSkuCodes == null)
            {
                noSyncSkuCodes = new List<string>();
            }

            var typeDict = new List<string>();

            #region 
            products.ForEach(p =>
            {
                var s = p.Skus?.FirstOrDefault();
                if (s != null && !noSyncSkuCodes.Contains(s.SkuCode))
                {
                    if (s.ReverseFxUserId > 0  && s.ReverseFxUserId != fxUserId) // 同步对象为上游厂家且不是当前用户
                    {
                        upProducts.Add(p);
                    }
                    else if (s.ToFxUserId > 0 && s.ToFxUserId != fxUserId)// 同步对象为下游商家且不是当前用户
                    {
                        downProducts.Add(p);
                    }
                }
            });
            #endregion

            var upSkuCodes = upProducts.SelectMany(p => p.Skus).Select(s => s.SkuCode).Distinct().ToList();
            var downSkuCodes = downProducts.SelectMany(p => p.Skus).Select(s => s.SkuCode).Distinct().ToList();


            // 同步对象Id 先查上游
            var toFxUserIds = upProducts.SelectMany(p => p.Skus)
                .Select(s => s.ReverseFxUserId)
                .Where(x => x > 0 && x != fxUserId)
                .Distinct()
                .ToList();

            // 查询上游的结算价 
            
            if (toFxUserIds.Any())
            {
                _productRepository.GetProductSkuSettlementPrice(fxUserId, upProducts, (int)SettlementType.Manufacturer, toFxUserIds, false, upSkuCodes);
            }

            toFxUserIds = downProducts.SelectMany(p => p.Skus)
                .Select(s => s.ToFxUserId)
                .Where(x => x > 0 && x != fxUserId)
                .Distinct()
                .ToList();

            if (toFxUserIds.Any())
            {
                _productRepository.GetProductSkuSettlementPrice(fxUserId, downProducts, (int)SettlementType.Merchant, toFxUserIds, false, downSkuCodes);
            }

            return new Tuple<List<ProductFx>, List<ProductFx>>(upProducts, downProducts);
        }*/
        #endregion

        /// <summary>
        /// 获取结算价模型
        /// </summary>
        /// <param name="skuCodes"></param>
        /// <param name="toFxUserId"></param>
        /// <param name="fxUserId"></param>
        /// <param name="isQueryCost">是否查询结算价</param>
        /// <param name="onlyQueryMy">仅查询我设置的结算价</param>
        /// <returns>Item1：对方设置的结算价；Item2：我设置的结算价；Item3：我设置的成本价</returns>
        public Tuple<List<ProductSettlementPrice>, List<ProductSettlementPrice>, List<ProductSettlementPrice>> GetSettlementPrice(List<string> skuCodes,List<int> toFxUserId,int fxUserId
            ,bool isQueryCost = false,bool onlyQueryMy = false)
        {

            var _financialSettlementRepository = new FinancialSettlementRepository();
            
            var allSettlementPrices = new List<ProductSettlementPrice>();
            if (!isQueryCost) // 不查询成本价
            {
                var allUserIds = new List<int>(toFxUserId) { fxUserId };
                allSettlementPrices = _financialSettlementRepository.GetSettlementPriceListBySkuCodes(allUserIds, allUserIds, skuCodes);
            }
            else
            {
                var createUserIds = new List<int>(toFxUserId) { fxUserId };
                var fxUserIds = new List<int>(createUserIds) { 0 };
                allSettlementPrices = _financialSettlementRepository.GetSettlementPriceListBySkuCodes(fxUserIds, createUserIds, skuCodes);
            }


            // 对方的结算价
            // 仅查询我的 ，不查询对方
            var oppositeSettlementPrices = new List<ProductSettlementPrice>();
            if (!onlyQueryMy)
                oppositeSettlementPrices = allSettlementPrices.Where(a => a.FxUserId == fxUserId && a.SettlementType != SettlementType.CostPrice.ToInt()).ToList();
            
            // 我的结算价
            var mySettlementPrices = allSettlementPrices.Where(a => a.CreateUser == fxUserId && a.SettlementType != SettlementType.CostPrice.ToInt()).ToList();

            // 成本价
            var costPrices = new List<ProductSettlementPrice>();
            if (isQueryCost)
            {
                costPrices = allSettlementPrices.Where(a => a.CreateUser == fxUserId && a.SettlementType == SettlementType.CostPrice.ToInt()).ToList();
            }
            return new Tuple<List<ProductSettlementPrice>, List<ProductSettlementPrice>, List<ProductSettlementPrice>>(oppositeSettlementPrices, mySettlementPrices,costPrices);
        }

        /// <summary>
        /// 结算价商品列表数据和上下游对象信息并入所有商品列表
        /// </summary>
        /// <param name="products"></param>
        /// <param name="oppositeSettlements">对方结算价</param>
        /// <param name="mySettlements">我的结算价</param>
        /// <param name="supplierAgents">上下游用户Id</param>
        /// <param name="toAgentNames">商家名称</param>
        /// <param name="toSupplierNames">厂家名称</param>
        private void ProductsIntoMoreInfo(List<ProductFx> products, List<ProductSettlementPrice> oppositeSettlements
            , List<ProductSettlementPrice> mySettlements, List<ProductSettlementPrice> costPrice = null
            , List<SupplierAgentModel> supplierAgents = null, Dictionary<int, string> toAgentNames = null
            , Dictionary<int, string> toSupplierNames = null
            , List<ProductSettlementPrice> defaultSettlementPrices = null)
        {
            if (supplierAgents == null)
                supplierAgents = new List<SupplierAgentModel>();
            if (toAgentNames == null)
                toAgentNames = new Dictionary<int, string>();
            if (toSupplierNames == null)
                toSupplierNames = new Dictionary<int, string>();

            var dic = new Dictionary<string, List<ProductSettlementPrice>>();
            products?.ForEach(p =>
            {
                var productAgentId = supplierAgents?.Where(sa => sa.ProductRefCode.Equals(p.ProductCode)).Select(sa => sa.UpFxUserId).FirstOrDefault() ?? p.UpFxUserId;
                toAgentNames.TryGetValue(productAgentId, out var pAgentName);
                p.UpFxUserId = p.UpFxUserId < 1 ? productAgentId : p.UpFxUserId;
                p.AgentName = p.AgentName ?? pAgentName;

                p.Skus.ForEach(s =>
                {
                    var exisProductSettlementPrices = new List<ProductSettlementPrice>();
                    var skuUpFxUserId = supplierAgents?.Where(sa => sa.ProductRefCode.Equals(s.SkuCode)).Select(sa => sa.UpFxUserId).FirstOrDefault() ?? 0;
                    if (s.UpFxUserId < 1 ) 
                    {
                        s.UpFxUserId = skuUpFxUserId;
                    }
                    
                    var oppositePrices = new List<ProductSettlementPrice>();
                    var myPrices = new List<ProductSettlementPrice>();
                    #region 对方是商家
                    // 对方的结算价
                    oppositePrices = oppositeSettlements.Where(se => se.ProductSkuCode.Equals(s.SkuCode) && se.SettlementType == (int)SettlementType.Merchant).ToList();

                    // 我的结算价
                    myPrices = mySettlements.Where(ms => ms.ProductSkuCode.Equals(s.SkuCode) && ms.SettlementType == (int)SettlementType.Manufacturer).ToList();

                    var upPrice = oppositePrices?.FirstOrDefault();
                    var myPrice = myPrices?.FirstOrDefault();
                    var reverseUser = upPrice?.CreateUser ?? s.UpFxUserId;

                    // 对方结算价
                    s.ReverseFxUserId = reverseUser;
                    s.ReverseSettlementId = upPrice?.Id ?? 0;
                    s.ReverseSettlementPrice = upPrice != null ? $"{upPrice.Price}" : "未设置";
                    s.OppositeSettlementUniqueKey = upPrice?.DbUniqueKey;

                    if (upPrice?.DbUniqueKey != null)
                        exisProductSettlementPrices.Add(upPrice);
                    // 我的结算价
                    s.ToReverseSettlementId = myPrice?.Id ?? 0;
                    s.ToReverseSettlementPrice = myPrice != null ? $"{myPrice.Price}" : "";
                    s.SettlementUniqueKey = myPrice?.DbUniqueKey;

                    if (myPrice?.DbUniqueKey != null)
                        exisProductSettlementPrices.Add(myPrice);
                    if (reverseUser != productAgentId) // 如果sku的商家和product的商家不同，则另外设置
                    {
                        toAgentNames.TryGetValue(reverseUser, out var name);
                        s.AgentName = name ?? "";
                    }
                    else
                    {
                        s.AgentName = p.AgentName ?? pAgentName;
                    }
                    #endregion

                    #region 对方是厂家
                    var bindSuppliers = supplierAgents.Where(sa => sa.ProductRefCode.Equals(s.SkuCode)).Select(sa => sa.DownFxUserId).Distinct().ToList();
                    // sku没绑定下游，使用商品的默认下游
                    if (bindSuppliers.Count() < 1)
                    {
                        bindSuppliers.Add(p.DownFxUserId);
                    }

                    oppositePrices = oppositeSettlements.Where(se => se.ProductSkuCode.Equals(s.SkuCode) 
                                                        && se.SettlementType == (int)SettlementType.Manufacturer 
                                                        && bindSuppliers.Contains(se.CreateUser)).ToList(); // 过滤sku解绑了厂家，但和厂家还有结算价的记录

                    myPrices = mySettlements.Where(ms => ms.ProductSkuCode.Equals(s.SkuCode) 
                                            && ms.SettlementType == (int)SettlementType.Merchant 
                                            && bindSuppliers.Contains(ms.FxUserId)).ToList(); 

                    // 给厂家赋值结算价 厂家可能有多个 所以用集合存
                    // 对方的结算价
                    oppositePrices?.ForEach(op =>
                    {
                        var model = s.SupplierSettlementModels?.Where(m => m.OppositeSettlementId == op.Id).FirstOrDefault();
                        if (model == null)
                        {
                            toSupplierNames.TryGetValue(op.CreateUser, out var name);
                            model = new SupplierSettlementModel()
                            {
                                OppositeSettlementId = op.Id,
                                fxUserId = op.CreateUser,
                                SupplierName = name ?? "",
                                OppositeSettlementPrice = op.Price != 0 ? op.Price.ToString() : "未设置",
                            };
                            s.SupplierSettlementModels.Add(model);
                            if (op?.DbUniqueKey != null)
                                exisProductSettlementPrices.Add(op);
                        }
                    });
                    // 我的结算价
                    myPrices?.ForEach(mp =>
                    {
                        var model = s.SupplierSettlementModels?.Where(m => m.fxUserId == mp.FxUserId).FirstOrDefault();
                        if (model == null)
                        {
                            toSupplierNames.TryGetValue(mp.FxUserId, out var name);
                            model = new SupplierSettlementModel()
                            {
                                SettlementId = mp.Id,
                                fxUserId = mp.FxUserId,
                                SupplierName = name ?? "",
                                SettlementPrice = mp.Price.ToString(),
                            };
                            s.SupplierSettlementModels.Add(model);
                            if (mp?.DbUniqueKey != null)
                                exisProductSettlementPrices.Add(mp);
                        }
                        else
                        {
                            var index = s.SupplierSettlementModels.IndexOf(model);
                            if (index != -1)
                            {
                                s.SupplierSettlementModels[index].SettlementId = mp.Id;
                                s.SupplierSettlementModels[index].SettlementPrice =mp.Price.ToString();
                                if (mp?.DbUniqueKey != null)
                                    exisProductSettlementPrices.Add(mp);
                            }
                        }
                    });

                    #region 没设置结算价 但是有下游厂家的sku，也要设置下游的用户id
                    var setDownUserIds = s.SupplierSettlementModels.Select(m => m.fxUserId).Distinct().ToList();
                    
                    var noSetDownUserIds = new List<int>();

                    // sku自己绑定有下游
                    if (supplierAgents!=null && supplierAgents.Any())
                    {
                        noSetDownUserIds = supplierAgents.Where(sa => s.SkuCode.Equals(sa.ProductRefCode)
                                                         && !setDownUserIds.Contains(sa.DownFxUserId)
                                                         && sa.DownFxUserId > 0)
                                                         ?.Select(n => n.DownFxUserId).ToList();
                    }
                    if (s.DownFxUserId > 0 && !setDownUserIds.Any() && !noSetDownUserIds.Any())// sku未绑定下游，使用未设置结算价的默认下游 （来自product）
                    {
                        noSetDownUserIds.Add(s.DownFxUserId);
                    }

                    noSetDownUserIds = noSetDownUserIds.Distinct().ToList();

                    noSetDownUserIds?.ForEach(nosetuser =>
                    {
                        toSupplierNames.TryGetValue(nosetuser, out var name);
                        s.SupplierSettlementModels.Add(new SupplierSettlementModel()
                        {
                            SupplierName = name ?? "",
                            fxUserId = nosetuser,
                            OppositeSettlementPrice = "未设置"
                        });
                    });
                    #endregion

                    #endregion

                    // sku成本价
                    if (costPrice.IsNotNullOrEmpty())
                    {
                        var costItem = costPrice.FirstOrDefault(cp => cp.ProductSkuCode == s.SkuCode);
                        s.CostPrice = (costItem == null || costItem.Id < 1) ? "" : costItem.Price.ToString();
                        s.CostPriceId = costItem?.Id ?? 0;
                    }
                    var defaultSettlementPrice = defaultSettlementPrices?.FirstOrDefault(item => item.ProductSkuCode == s.SkuCode);
                    s.DefaultSupplierSettlementPrice = defaultSettlementPrice?.Price;
                    s.DefaultSupplierSettlementPriceId = defaultSettlementPrice?.Id ?? 0;
                    if (defaultSettlementPrice?.DbUniqueKey != null)
                        exisProductSettlementPrices.Add(defaultSettlementPrice);
                    var productSkuHistoryCode = (s.Name + s.SkuCode).ToShortMd5();
                    dic.Add(productSkuHistoryCode, exisProductSettlementPrices);
                });
            });

            //所有的新结算价
            var uniqueCodes = dic.SelectMany(t => t.Value)?.Select(t => t.DbUniqueKey).Distinct().ToList();
            var subPrices = subProductSettlementPriceRepository.GetListBySettlementPriceCodes(uniqueCodes);
            var addSubPrices = new List<SubProductSettlementPrice>();
            products.SelectMany(t => t.Skus).ToList().ForEach(sku =>
            {
                var productSkuHistoryCode = (sku.Name + sku.SkuCode).ToShortMd5();
                var exisProductSettlementPrices = dic[productSkuHistoryCode];
                if (!exisProductSettlementPrices.Any())
                    return;
                exisProductSettlementPrices.ForEach(x =>
                {
                    if (!subPrices.Any(t => t.ProductSkuHistoryCode == productSkuHistoryCode && t.ProductSettlementPriceUniqueCode == x.DbUniqueKey))
                    {
                        addSubPrices.Add(new SubProductSettlementPrice(sku)
                        {
                            Price = x.Price,
                            ProductSettlementPriceUniqueCode = x.DbUniqueKey,
                            SettlementSkuUniqueCode = string.Empty
                        });
                    }
                });
            });
            if (addSubPrices.Any())
            {
                subProductSettlementPriceRepository.BatchAddOrUpdate(addSubPrices);
            }
        }

        public List<UnSetPriceSettlementModel> GetUnSetSkuPriceCount(int userType, List<int> toFxUserIds, int loginFxUserId)
        {
            return _repository.GetUnSetSkuPriceCount(userType, toFxUserIds, loginFxUserId);
        }

        public bool DelSettlementProductSku(List<int> settlementSkuIdS, int createUser)
        {
            return _repository.DelSettlementProductSku(settlementSkuIdS, createUser);
        }
        /// <summary>
        /// 使用UniqueKey
        /// </summary>
        /// <param name="keys"></param>
        /// <param name="createUser"></param>
        /// <returns></returns>
        public bool DelSettlementProductSku(List<string> keys, int createUser)
        {
            return _repository.DelSettlementProductSku(keys, createUser);
        }

        public List<ProductSettlementRecord> GetSettlementPriceRecord(int priceId)
        {
            var list = _repository.GetSettlementPriceRecord(priceId);
            if (list.Any())
            {
                var fxUserIds = list.Select(x => x.FxUserId).Distinct().ToList();
                var userFxs = _userRepository.GetUsersByFxUserId(fxUserIds);
                if (userFxs.Any())
                {
                    list.ForEach(record =>
                    {
                        var user = userFxs.FirstOrDefault(u => u.Id == record.FxUserId);
                        if (user != null)
                        {
                            record.NickName = user.NickName;
                            record.Mobile = user.Mobile;
                        }
                    });
                }
            }
            return list;
        }

        public List<ProductSettlementRecord> GetSettlementPriceRecord(List<int> priceIds,int fxUserId = 0)
        {
            var list = _repository.GetSettlementPriceRecord(priceIds,fxUserId);
            if (list.Any())
            {
                //var fxUserIds = list.Select(x => x.FxUserId).Distinct().ToList();
                //var userFxs = _userRepository.GetUsersByFxUserId(fxUserIds);
                //if (userFxs.Any())
                //{
                //    list.ForEach(record =>
                //    {
                //        var user = userFxs.FirstOrDefault(u => u.Id == record.FxUserId);
                //        if (user != null)
                //        {
                //            record.NickName = user.NickName;
                //            record.Mobile = user.Mobile;
                //        }
                //    });
                //}
                SetShowOperateFxUser(list);
            }
            return list;
        }

        /// <summary>
        /// 使用UniqueyKey
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public List<ProductSettlementRecord> GetSettlementPriceRecord(string key)
        {
            if (string.IsNullOrEmpty(key))
                return new List<ProductSettlementRecord>();
            var list = _repository.GetSettlementPriceRecord(key);
            if (list.Any())
            {
                //var fxUserIds = list.Select(x => x.FxUserId).Distinct().ToList();
                //var userFxs = _userRepository.GetUsersByFxUserId(fxUserIds);
                //if (userFxs.Any())
                //{
                //    list.ForEach(record =>
                //    {
                //        var user = userFxs.FirstOrDefault(u => u.Id == record.FxUserId);
                //        if (user != null)
                //        {
                //            record.NickName = user.NickName;
                //            record.Mobile = user.Mobile;
                //        }
                //    });
                //}
                SetShowOperateFxUser(list);
            }
            return list;
        }
        private void SetShowOperateFxUser(List<ProductSettlementRecord> list)
        {
            var fxUserIds = list.Select(x => x.FxUserId).Distinct().ToList();
            var userFxs = _userRepository.GetUsersByFxUserId(fxUserIds);
            if (userFxs.Any())
            {
                //list.ForEach(record =>
                //{
                //    var user = userFxs.FirstOrDefault(u => u.Id == record.FxUserId);
                //    if (user != null)
                //    {
                //        record.NickName = user.NickName;
                //        record.Mobile = user.Mobile;
                //    }
                //});
                var subFxUserIds = list.Where(s => s.SubFxUserId.HasValue && s.SubFxUserId > 0).Select(s => s.SubFxUserId.Value).ToList();
                var subUserFxs = new List<SubUserFx>();
                if (subFxUserIds.Count > 0)
                {
                    subUserFxs = new SubUserFxRepository().GetSubUserFxs(subFxUserIds).ToList();
                }
                var wcService = new FxWeChatUserService();
                list.ForEach(re =>
                {
                    if (re.SubFxUserId.HasValue && re.SubFxUserId > 0)
                    {
                        var subFxUser = subUserFxs.FirstOrDefault(u => u.Id == re.SubFxUserId);
                        if (subFxUser != null)
                        {
                            string str1 = subFxUser.NickName;
                            string str2 = subFxUser.Mobile;
                            // 优先取手机号码，手机号码没有的话取微信昵称
                            if (str2.IsEmpty())
                            {
                                str2 = wcService.GetWeChatUserByOpenId(subFxUser.WxOpenId)?.NickName;
                            }
                            re.ShowOperateUserFx = $"{str1}（{str2}）";
                        }
                    }
                    else
                    {
                        var user = userFxs.FirstOrDefault(u => u.Id == re.FxUserId);
                        if (user != null)
                        {
                            re.ShowOperateUserFx = $"{user.Mobile}（{user.NickName}）";
                        }
                    }
                });
            }
        }

        #region 暂时弃置 结算商品信息
        /// <summary>
        /// 结算商品信息
        /// </summary>
        /// <param name="fxUserId">当前账号</param>
        /// <param name="toFxUserIds">结算对象账号</param>
        /// <param name="type">结算对象类型=》 1：厂家，2：商家</param>
        /// <returns></returns>
        /*public List<ProductFx> GetSettlementProducts(int fxUserId, List<int> toFxUserIds, int type = 0)
        {
            return _repository.GetSettlementProducts(fxUserId, toFxUserIds, type);
        }*/
        #endregion

        public List<ProductFx> GetSettlementProductSkus(List<int> sopIds, int fxUserId)
        {
            return _repository.GetSettlementProductSkus(sopIds, fxUserId);
        }

        /// <summary>
        /// 获取信息列表，为迁移数据
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<SettlementProductSku> GetListForDuplication(DuplicationConditionModel condition,
            int pageSize)
        {
            return _repository.GetListForDuplication(condition, pageSize);
        }

        /// <summary>
        /// 获取信息列表，为副本补偿数据
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public List<SettlementProductSku> GetListForDuplicationCompensate(DuplicationConditionModel condition,
            int pageSize)
        {
            return _repository.GetListForDuplicationCompensate(condition, pageSize);
        }

        /// <summary>
        /// 获取信息为复制副本，按SendHistoryCode
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFieldNames"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<SettlementProductSku> GetListForDuplication(List<string> codes, string selectFieldNames = "*",
            string whereFieldName = "UniqueKey")
        {
            if (codes == null || !codes.Any())
                return new List<SettlementProductSku>();

            var list = new List<SettlementProductSku>();
            var batchSize = 500;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetListForDuplication(batchCodes, selectFieldNames, whereFieldName);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 批量插入数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        public void InsertsForDuplication(List<SettlementProductSku> models)
        {
            if (models == null || !models.Any())
                return;
            //清理源库ID
            models.ForEach(m => { m.Id = 0; });

            var batchSize = 500;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();

                //代码
                var codes = batchModels.Select(m => m.UniqueKey).ToList();
                //存在的代码列表
                var idAndCodes = _repository.GetExistIdAndCodes(codes);
                //全部不存在
                if (idAndCodes == null || !idAndCodes.Any())
                {
                    //_repository.BulkInsert(batchModels);
                    baseRepository.BulkWrite(batchModels, "SettlementProductSku", maxSingleNum: 1);
                    continue;
                }
                //存在
                var existsCodes = idAndCodes.Select(m => m.Code).ToList();
                //var updates = batchModels.Where(m => existsCodes.Contains(m.UniqueKey)).ToList();
                //if (updates.Any())
                //{
                //    updates.ForEach(o =>
                //    {
                //        var model = idAndCodes.FirstOrDefault(m => m.Code == o.UniqueKey);
                //        if (model == null)
                //        {
                //            return;
                //        }
                //        o.Id = model.Id;
                //    });
                //    _repository.BulkUpdate(updates);
                //}
                //不存在
                var inserts = batchModels.Where(m => !existsCodes.Contains(m.UniqueKey)).ToList();
                if (inserts.Any())
                {
                    //_repository.BulkInsert(inserts);
                    baseRepository.BulkWrite(inserts, "SettlementProductSku", maxSingleNum: 1);
                }
            }
        }
        #region ObsoleteSettlementSku 迁移相关

        /// <summary>
        /// 获取ObsoleteSettlementSku信息为复制副本，按ParentUniqueKey
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFieldNames"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<ObsoleteSettlementSku> GetObsListForDuplication(List<string> codes, string selectFieldNames = "*",
            string whereFieldName = "ParentUniqueKey")
        {
            if (codes == null || !codes.Any())
                return new List<ObsoleteSettlementSku>();

            var list = new List<ObsoleteSettlementSku>();
            var batchSize = 500;
            var count = Math.Ceiling(codes.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchCodes = codes.Skip(i * batchSize).Take(batchSize).ToList();
                var batchList = _repository.GetObsListForDuplication(batchCodes, selectFieldNames, whereFieldName);
                if (batchList != null)
                    list.AddRange(batchList);
            }
            return list;
        }

        /// <summary>
        /// 批量插入ObsoleteSettlementSku数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        public void InsertsObsForDuplication(List<ObsoleteSettlementSku> models)
        {
            if (models == null || !models.Any())
                return;
            //清理源库ID
            models.ForEach(m => { m.Id = 0; });

            var batchSize = 500;
            var count = Math.Ceiling(models.Count * 1.0 / batchSize);
            for (var i = 0; i < count; i++)
            {
                var batchModels = models.Skip(i * batchSize).Take(batchSize).ToList();

                //代码
                var codes = batchModels.Select(m => m.ParentUniqueKey).ToList();
                //存在的代码列表
                var idAndCodes = _repository.GetObsExistIdAndCodes(codes);
                //全部不存在
                if (idAndCodes == null || !idAndCodes.Any())
                {
                    baseRepository.BulkWrite(batchModels, "ObsoleteSettlementSku", maxSingleNum: 1);
                    continue;
                }
                //存在
                //不需要更新
                //var updates = batchModels.Where(x => idAndCodes.Any(m => m.Code == x.ParentUniqueKey && m.FxUserId == x.CreateUser)).ToList();
                //if (updates.Any())
                //{
                //    updates.ForEach(o =>
                //    {
                //        var model = idAndCodes.FirstOrDefault(m => m.Code == o.ParentUniqueKey && m.FxUserId == o.CreateUser);
                //        if (model == null)
                //        {
                //            return;
                //        }
                //        o.Id = model.Id;
                //    });
                //   // _repository.BulkUpdate(updates);
                //}
                //不存在
                var inserts = batchModels.Where(x => !idAndCodes.Any(m => m.Code == x.ParentUniqueKey && m.FxUserId == x.CreateUser)).ToList();
                if (inserts.Any())
                {
                    baseRepository.BulkWrite(inserts, "ObsoleteSettlementSku", maxSingleNum: 1);
                }
            }
        }

        #endregion
        
        private List<ProductSettlementPrice> BuildSettlementPriceModel(List<ProductFx> products,int settlementType,int fxUserId)
        {
            decimal outPrice = 0;
            return products.SelectMany(p => p.Skus)
                .Select(s =>
                    new
                    {
                        Sku = s,
                        IsParsed = decimal.TryParse(s.OppositeSettlementPrice, out outPrice)
                    })
                .Where(x => x.IsParsed)  // 过滤settlementPrice转型失败的数据
                .Select(x =>
                {
                    return new ProductSettlementPrice
                    {
                        Id = x.Sku?.SettlementId ?? 0,
                        ProductCode = x.Sku.ProductCode,
                        ProductSkuCode = x.Sku.SkuCode,
                        SettlementType = settlementType,
                        PlatformType = x.Sku.PlatformType,
                        CreateUser = fxUserId,
                        FxUserId = x.Sku.ToFxUserId,
                        Price = outPrice
                    };
                }).ToList();
        }

        #region 批量导入相关信息
        /// <summary>
        /// 获取导入商品数据
        /// </summary>
        /// <param name="query"></param>
        /// <param name="importList"></param>
        /// <returns></returns>
        public ExcelDatasModel GetImportSettlementtList(ImportSettlementQueryModel query, List<ImportSetSettlementPriceModel> importList)
        {
            ExcelDatasModel excelDatas = new ExcelDatasModel();
            var fxUserId = query.LoginFxUserId;
            try
            {

                #region 获取商品数据以及商品路径
                ///根据导入的数据获取商品信息
                var tuplelist = _repository.GetImprotProductListV2(query);
                var products = tuplelist.Item2;
                var pCodes = products.Select(x => x.ProductCode).Distinct().ToList();
                var fields = "pf.*,pfn.*,pfr.*,pfc.Id,pfc.RelationCode,pfc.PathFlowCode,pfc.PathFlowRefCode,pfc.ConfigType,pfc.FxUserId,pfc.FromType";
                //获取商品路径
                var pathflowService = new PathFlowService(_connectionString);
                var pathFlows = pathflowService.GetPathFlows(pCodes, 0, fields);
                //兼容新旧数据补上Config信息
                new ProductFxService().CompatibleOldDataToAddConfig(pathFlows);
                #endregion

                #region 数据匹配

                ///根据商品路径流获取商品关系
                List<ProductSkuFx> needImSkuList = new List<ProductSkuFx>();
                products.ForEach(p =>
                {
                    var productPathFlows = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(p.ProductCode)).ToList();
                    // 商品上下游用户Id
                    var tuple = GetUpAndDownFxUserIdV2(p.ProductCode, fxUserId, productPathFlows);
                    p.UpFxUserId = tuple.Item1;
                    p.DownFxUserId = tuple.Item2;
                    // 是否商品绑定厂家
                    p.IsCurBindProduct = pathFlows.Any(x => x.PathFlowReferences.ContainsKey(p.ProductCode) && x.PathFlowNodes.Any(y => y.FxUserId == fxUserId));
                    p.Skus.ForEach(sku =>
                    {
                        ////查询商品SKU信息
                        var skuPathFlows = pathFlows.Where(pf => pf.PathFlowReferences.ContainsKey(sku.SkuCode)).ToList();
                        if (!skuPathFlows.Any())
                        {
                            var dbSku = new ProductSkuFx
                            {
                                PlatformId = sku.PlatformId,
                                ProductCode = p.ProductCode,
                                ImgUrl = sku.ImgUrl,
                                Name = sku.Name,
                                SkuCode = sku.SkuCode,
                                SkuId = sku.SkuId,
                                Subject = sku.Subject,
                                OppositeSettlementPrice = sku.OppositeSettlementPrice,
                                AgentName = p.AgentName,
                                DownFxUserId = tuple.Item2,
                                UpFxUserId = tuple.Item1,
                                SupplerName = p.SupplerName,
                                PlatformType = p.PlatformType,
                                ToFxUserId = query.UserType == 1 ? p.DownFxUserId : p.UpFxUserId,
                                BindFrom = "Product",
                                ShopId = sku.ShopId,
                                CargoNumber = sku.CargoNumber,
                                PathFlowCode = tuple.Item3
                            };
                            if (!needImSkuList.Any(n => n.SkuCode == dbSku.SkuCode && n.ToFxUserId == dbSku.ToFxUserId))
                                needImSkuList.Add(dbSku);
                        }
                        else
                        {
                            skuPathFlows.ForEach(x =>
                            {  //注：同个SKUID、SKUCODE 存在绑定多厂家情况
                                var pathflownode = x.PathFlowNodes?.FirstOrDefault(f => f.FxUserId == query.LoginFxUserId && x.PathFlowCode == f.PathFlowCode);
                                if (pathflownode != null)
                                {
                                    ProductSkuFx dbSku = new ProductSkuFx
                                    {
                                        PlatformId = sku.PlatformId,
                                        ProductCode = sku.ProductCode,
                                        ImgUrl = sku.ImgUrl,
                                        Name = sku.Name,
                                        SkuCode = sku.SkuCode,
                                        SkuId = sku.SkuId,
                                        Subject = sku.Subject,
                                        OppositeSettlementPrice = sku.OppositeSettlementPrice,
                                        UpFxUserId = pathflownode.UpFxUserId,
                                        DownFxUserId = pathflownode.DownFxUserId,
                                        PlatformType = p.PlatformType,
                                        ShopId = sku.ShopId,
                                        ToFxUserId = query.UserType == 1 ? pathflownode.DownFxUserId : pathflownode.UpFxUserId,
                                        PathFlowCode = pathflownode.PathFlowCode,
                                        CargoNumber = sku.CargoNumber,
                                        BindFrom = "SKU",
                                    };
                                    if (!needImSkuList.Any(n => n.SkuCode == dbSku.SkuCode && n.ToFxUserId == dbSku.ToFxUserId))
                                        needImSkuList.Add(dbSku);

                                }
                            });
                        }
                    });
                });
                products.ForEach(x =>
                {
                    x.Skus = needImSkuList.Where(n => n.ProductCode == x.ProductCode).ToList();
                });
                #endregion

                #region 获取结算信息
                // 查询商家或厂家名称
                var _supplierUserService = new SupplierUserService();
                //厂家数据源
                var suppliers = _supplierUserService.GetSupplierList(fxUserId);
                //商家数据源
                var agents = _supplierUserService.GetAgentList(fxUserId);
                if (query.ToFxUserIds == null)
                    query.ToFxUserIds = query.UserType == 1 ? suppliers.Select(x => x.SupplierFxUserId).ToList() : agents.Select(x => x.FxUserId).ToList();
                //查询对应SKU设置的价格（含对方结算价 & 包含反向对方结算价）
                _productRepository.GetProductSkuSettlementPrice(query.LoginFxUserId, products, query.UserType, query.ToFxUserIds, true);
                if (query.UserType == 1)
                {
                    tuplelist.Item2.ForEach(p =>
                    {
                        p.Skus.ForEach(s =>
                        {
                            var supplier = suppliers.FirstOrDefault(x => x.SupplierFxUserId == s.ToFxUserId);
                            if (supplier != null)
                                s.ToFxUserName = supplier.SupplierMobileAndRemark;

                            var reverse = agents.FirstOrDefault(x => x.FxUserId == s.ReverseFxUserId);
                            if (reverse != null)
                                s.ReverseFxUserName = $"下游：{reverse.AgentMobileAndRemark}";
                        });
                    });
                }
                else
                {
                    tuplelist.Item2.ForEach(p =>
                    {
                        p.Skus.ForEach(s =>
                        {
                            var agent = agents.FirstOrDefault(x => x.FxUserId == s.ToFxUserId);
                            if (agent != null)
                                s.ToFxUserName = agent.AgentMobileAndRemark;

                            var reverse = suppliers.FirstOrDefault(x => x.SupplierFxUserId == s.ReverseFxUserId);
                            if (reverse != null)
                                s.ReverseFxUserName = $"上游：{reverse.SupplierMobileAndRemark}";
                        });
                    });
                }
                needImSkuList = products.SelectMany(product => product.Skus).ToList();
                #endregion

                #region 数据匹配
                List<ImportSetSettlementPriceDetail> importSetSettlementPriceList = new List<ImportSetSettlementPriceDetail>();
                foreach (var model in importList)
                {

                    if (!model.IsException)
                    {   //前置校验数据已异常不在添加明细
                        ImportSetSettlementPriceDetail importDetails = new ImportSetSettlementPriceDetail
                        {
                            PlatformId = model.PlatformId,
                            SkuId = model.SkuId,
                            SkuCode = model.SkuCode,
                            IsException = false,
                            ExceptionMessage = model.ExceptionMessage
                        };
                        importSetSettlementPriceList.Add(importDetails);
                    }
                    else
                    {
                        ///根据商品ID 以及SKUID或SKUCODE进行匹配
                        var list = needImSkuList.Where(n => n.PlatformId == model.PlatformId && (query.ImportType == "SkuCode" ? n.CargoNumber == model.SkuCode : n.SkuId == model.SkuId)).ToList();
                        if (list.Any())
                        {
                            foreach (var sku in list)
                            {
                                var product = products.FirstOrDefault(x => x.ProductCode == sku.ProductCode);
                                ///商品是否自营
                                bool isSelfProductCodeOrSku = (sku.DownFxUserId == 0 && query.UserType == 1) || (sku.UpFxUserId == 0 && query.UserType == 2);
                                var importDetails = new ImportSetSettlementPriceDetail
                                {
                                    Subject = sku.Subject,
                                    PlatformId = sku.PlatformId,
                                    SkuId = sku.SkuId,
                                    SkuCode = sku.CargoNumber,//注：取商品界面的编码仅前端界面展示合并
                                    ImgUrl = sku.ImgUrl,
                                    Name = sku.Name,
                                    SettlementPrice = sku.SettlementPrice,
                                    OppositeSettlementPrice = sku.OppositeSettlementPrice,
                                    ImpSetSettlementPrice = model.ImpSetSettlementPrice,
                                    ToFxUserId = sku.ToFxUserId,
                                    ToFxUserName = sku.ToFxUserName,
                                    ProductCode = sku.ProductCode,
                                    PlatformType = sku.PlatformType,
                                    ShopId = sku.ShopId,
                                    SourceUserId = product.SourceUserId,
                                    PathFlowCode = sku.PathFlowCode,
                                    DownFxUserId = sku.DownFxUserId,
                                    UpFxUserId = sku.UpFxUserId,
                                    IsSyncMerchandise = false,
                                    IsException = true,
                                    ExceptionMessage = string.Empty,
                                    ImportSkuCode=sku.SkuCode,
                                    
                                };
                                // 如果是自营商品，无需设置其他属性，直接添加
                                if (isSelfProductCodeOrSku)
                                {
                                    importDetails.IsException = false;
                                    importDetails.ExceptionMessage = "异常提示:当前规格是自营商品暂不支持设置结算价，请绑定结算对象后再进行操作";
                                    importSetSettlementPriceList.Add(importDetails);
                                    continue;
                                }
                                // 非自营商品，进一步处理
                                if (model.PlatformId == sku.PlatformId && (query.ImportType == "SkuCode" ? model.SkuCode == sku.CargoNumber : model.SkuId == sku.SkuId))
                                {
                                    importDetails.ExceptionMessage = string.Empty;
                                    importDetails.IsException = true;
                                }
                                else
                                {
                                    // 如果未找到对应商品，重置异常信息
                                    importDetails.IsException = false;
                                    importDetails.ExceptionMessage = "异常提示:当前规格未匹配到对应规格属性，若该商品为新品，请前往店管家-商品列表进行同步更新商品操作，同步后重新导入表格匹配";
                                }
                                importSetSettlementPriceList.Add(importDetails);
                            }
                        }
                        else
                        {
                            // 未找到匹配的sku，直接添加异常信息
                            importSetSettlementPriceList.Add(new ImportSetSettlementPriceDetail
                            {
                                PlatformId = model.PlatformId,
                                SkuId = model.SkuId,
                                SkuCode = model.SkuCode,
                                IsException = false,
                                IsSyncMerchandise = true,
                                ExceptionMessage = "异常提示:当前规格未匹配到对应规格属性，若该商品为新品，请前往店管家-商品列表进行同步更新商品操作，同步后重新导入表格匹配"
                            });
                        }
                    }
                };
                #endregion
                return DataTableToCustomOrder(importSetSettlementPriceList, columnTitleMap, true); ;
            }
            catch (Exception ex)
            {
                Log.WriteError("批量导入结算价数据】匹配出错啦：" + ex.Message);
                throw;
            }

        }

        private Tuple<int, int, string, bool> GetUpAndDownFxUserIdV2(string refCode, int fxUserId, List<PathFlow> pathFlows)
        {
            var downFxUserId = 0;
            var upFxUserId = 0;
            var PathFlowCode = "";
            if (pathFlows.Any())
            {
                var pathRefConfigs = pathFlows.Where(x => x.PathFlowReferences.ContainsKey(refCode)).SelectMany(x => x.PathFlowReferences.SelectMany(y => y.Value.ReferenceConfigs)).Where(x => x.PathFlowRefCode == refCode).ToList();
                // 所有商品列表默认显示顺序：默认厂家（必需）=>自营
                var defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.ConfigType == 0 && x.DownFxUserId > 0 && x.FxUserId == fxUserId);
                if (defaultPathRefConfig != null)
                {
                    // 默认厂家
                    downFxUserId = defaultPathRefConfig.DownFxUserId;
                    upFxUserId = defaultPathRefConfig.UpFxUserId;
                    PathFlowCode = defaultPathRefConfig.PathFlowCode;
                }
                else
                {
                    // 自营
                    defaultPathRefConfig = pathRefConfigs?.FirstOrDefault(x => x.PathFlowRefCode == refCode && x.DownFxUserId == fxUserId);
                    upFxUserId = defaultPathRefConfig?.FxUserId ?? 0;
                    PathFlowCode = defaultPathRefConfig?.PathFlowCode ?? "";
                }
            }
            return new Tuple<int, int, string, bool>(upFxUserId, downFxUserId, PathFlowCode, false);
        }
        private ExcelDatasModel DataTableToCustomOrder(List<ImportSetSettlementPriceDetail> list, Dictionary<string, string> columnTitleMap, bool isNewColumnName = false)
        {
            #region List转DataTable
            DataTable dt = new DataTable();
            // 定义列并设置标题
            foreach (KeyValuePair<string, string> column in columnTitleMap)
            {
                dt.Columns.Add(column.Key, typeof(object)).Caption = column.Value;
            }
            // 添加数据行
            foreach (var item in list)
            {
                DataRow row = dt.NewRow();

                foreach (KeyValuePair<string, string> column in columnTitleMap)
                {
                    string propertyName = column.Key;
                    PropertyInfo property = typeof(ImportSetSettlementPriceDetail).GetProperty(propertyName);
                    if (property != null)
                    {
                        row[propertyName] = property.GetValue(item, null);
                    }
                }
                dt.Rows.Add(row);
            }
            if (isNewColumnName)
            {
                foreach (var mapping in columnTitleMap)
                {
                    string oldColumnName = mapping.Key;
                    string newColumnName = mapping.Value;
                    if (dt.Columns.Contains(oldColumnName))
                    {
                        dt.Columns[oldColumnName].ColumnName = newColumnName;
                    }
                }
            }
            #endregion

            #region ExcelDatasModel
            ExcelDatasModel model = new ExcelDatasModel();
            model.ColumnNames = ExcelHelper.GetColumnsByDataTable(dt).ToList();
            var rows = dt.Rows;
            for (var i = 0; i < rows.Count; i++)
            {
                List<string> row = new List<string>();
                foreach (var col in model.ColumnNames)
                {
                    row.Add(rows[i][col].ToString2());
                }
                model.Rows.Add(row);
            }
            return model;
            #endregion
        }

        /// <summary>
        /// 定义返回表头
        /// </summary>
        Dictionary<string, string> columnTitleMap = new Dictionary<string, string>
            {
                { "Subject", "商品名称" },
                { "PlatformId", "导入商品ID" },
                { "SkuId", "导入SKUID" },
                { "SkuCode", "导入SkuCode" },
                { "ImpSetSettlementPrice", "确认导入SKU结算价" },
                { "SettlementPrice", "原结算价" },
                { "ImgUrl", "商品图片" },
                { "Name", "商品规格属性" },
                { "OppositeSettlementPrice", "对方结算价" },
                { "ToFxUserName", "对方结算价账号" },
                { "IsException", "是否异常" },
                { "ExceptionMessage", "异常消息" },
                { "ToFxUserId", "对方结算价用户" },
                { "ProductCode", "商品编码" },
                { "ShopId", "店铺" },
                { "SourceUserId", "商品来源" },
                { "PathFlowCode", "路径流" },
                {"IsSyncMerchandise","未对应是否同步" },
                {"PlatformType","平台" },
                {"ImportSkuCode","系统SKUCODE" }
            };
        #endregion

    }
    
}
