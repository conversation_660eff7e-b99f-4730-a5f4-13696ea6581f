using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility.Extension;
using System.Data;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Dapper;
using Dapper;
using System.Collections.Concurrent;
using System.Text.RegularExpressions;
using DianGuanJiaApp.Utility.NPOI;
using NPOI.SS.UserModel;
using System.IO;
using System.Web;
using System.Web.Razor.Parser.SyntaxTree;

namespace DianGuanJiaApp.Services
{
    public partial class Stat1688V2Service : BaseService<Data.Entity.Stat1688V2>
    {
        private Stat1688V2Repository _repository;
        public Stat1688V2Service()
        {
            _repository = new Stat1688V2Repository();
            this._baseRepository = _repository;
        }

        /// <summary>
        /// 1688分销日统计V2
        /// </summary>
        /// <returns></returns>
        public void Stat1688V2ByDate(DateTime? dtStart, DateTime? dtEnd)
        {
            if (dtStart == null)
            {
                dtStart = DateTime.Now.AddDays(-7);
                if (dtStart < Convert.ToDateTime("2023-12-07"))
                    dtStart = Convert.ToDateTime("2023-12-07");
            }
            if (dtEnd == null || dtEnd.Value > DateTime.Now.AddDays(1))
                dtEnd = DateTime.Now.ToString("yyyy-MM-dd").ToDateTime();

            dtStart = dtStart.Value.ToString("yyyy-MM-dd").ToDateTime();

            var taskCode = Guid.NewGuid().ToString().ToShortMd5();

            // 定义
            // 总结果
            var statList = new List<Stat1688V2>();
            // 店铺列表：是否老店铺、是否老用户
            var stat1688Shops = new List<Stat1688Shop>();
            // 老店铺
            var oldShopIds = new List<int>();
            var userFxs = new List<UserFx>();
            // 老店铺厂家
            var oldShopSupplierFxUserIds = new List<int>();
            // 新店铺厂家
            var newShopSupplierFxUserIds = new List<int>();
            // 已设置了下单店铺
            var hasSet1688ShopSupplierFxUserIds = new List<int>();
            //厂家商家关系
            var supplierAgents = new List<SupplierAgent>();
            // 老店铺厂家所关联的商家
            var oldShopAgentFxUserIds = new List<int>();
            // 新店铺厂家所关联的商家
            var newShopAgentFxUserIds = new List<int>();
            // 已开启预付+已绑定买家账号+未推送商品给厂家的商家
            var binedBuyerButNotPostProductAgentFxUserIds = new List<int>();
            // 已开启预付+已绑定买家账号+未推送商品给厂家的相关厂家
            var binedBuyerButNotPostProductSupplierFxUserIds = new List<int>();
            // 新店铺+已开启预付+已绑定买家账号+已推送商品+未关联商品的相关厂家
            var newShopNotMappingProductSupplierFxUserIds = new List<int>();
            // 老店铺+已开启预付+已绑定买家账号+已推送商品+未关联商品的相关厂家
            var oldShopNotMappingProductSupplierFxUserIds = new List<int>();
            // 下采购单失败明细
            var purchaseOrderFailDetailFxUserIds = new List<int>();
            // 回流失败明细
            var returnFailDetailFxUserIds = new List<int>();

            var csService = new CommonSettingService();
            //// 精选云打印库连接字符串
            //var alibabaPrintConnectionString = csService.Get($"/System/Fendan/PrintHistory/ConnectiongString", 0)?.Value ?? "";
            //if (string.IsNullOrEmpty(alibabaPrintConnectionString))
            //    alibabaPrintConnectionString = CustomerConfig.PrintHistoryDefaultMysqlConnectionString;
            //var alibabaPrintDb = new PrintHistoryRepository(alibabaPrintConnectionString).DbConnection;

            //// 抖店云打印库连接字符串
            //var toutiaoPrintConnectionString = csService.Get($"/System/Fendan/PrintHistory/TouTiao/ConnectiongString", 0)?.Value ?? "";
            //var toutiaoPrintDb = new PrintHistoryRepository(toutiaoPrintConnectionString).DbConnection;

            //var aliStrDate = "CONVERT(NVARCHAR(50),MIN(PrintDate),23)";
            //var ttStrDate = "CONVERT(NVARCHAR(50),MIN(PrintDate),23)";
            //if (alibabaPrintDb is MySqlConnection)
            //    aliStrDate = "DATE_FORMAT(MIN(PrintDate), '%Y-%m-%d')";
            //if (toutiaoPrintDb is MySqlConnection)
            //    ttStrDate = "DATE_FORMAT(MIN(PrintDate), '%Y-%m-%d')";

            var db = _baseRepository.DbConnection;

            var sql = $@"SELECT CONVERT(NVARCHAR(50),se.CreateTime,23) AS ShopCreateTime,fus.FxUserId,fus.ShopId  FROM dbo.P_ShopExtension se WITH(NOLOCK)
INNER JOIN dbo.P_Shop s WITH(NOLOCK) ON se.ShopId = s.Id 
INNER JOIN P_FxUserShop fus WITH(NOLOCK) ON fus.ShopId=s.Id
  WHERE se.AppKey='5382198' 
 AND se.CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND se.CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}' ";

            stat1688Shops = db.Query<Stat1688Shop>(sql).ToList();

            var supplierFxUserIds = stat1688Shops.Select(a => a.FxUserId).Distinct().ToList();
            var shopIds = stat1688Shops.Select(a => a.ShopId).Distinct().ToList();

            var batchSize = 200;
            var dtNow = DateTime.Now;
            var timeSize = 120;//时间片大小（分钟）

            //查询是否老店铺
            if (shopIds.Any())
            {
                var chunks = shopIds.ChunkList(batchSize);
                chunks.ForEach(chunk =>
                {
                    var curSql = $@"SELECT se.ShopId FROM dbo.P_ShopExtension se WITH(NOLOCK)
  WHERE se.AppKey<>'5382198' AND se.ShopId IN  ({string.Join(",", chunk)})  ";
                    var curList = db.Query<int>(curSql).ToList();
                    if (curList != null && curList.Any())
                        oldShopIds.AddRange(curList);
                });
            }
            if (oldShopIds.Any())
            {
                stat1688Shops.Where(a => oldShopIds.Contains(a.ShopId)).ToList().ForEach(stat => stat.IsOldShop = true);
            }

            var firstPrints = new List<StatModelV2>();
            var firstOpenPrepaySuppliers = new List<StatModelV2>();
            var firstOpenPrepayAgents = new List<StatModelV2>();
            if (supplierFxUserIds.Any())
            {
                var chunks = supplierFxUserIds.ChunkList(batchSize);
                chunks.ForEach(chunk =>
                {

                    //用户相关的商家
                    var curSql = $@"SELECT DISTINCT SupplierFxUserId,FxUserId AS AgentFxUserId FROM P_SupplierUser WITH(NOLOCK) WHERE SupplierFxUserId IN ({string.Join(",", chunk)}) AND [Status] =1 ";
                    var curList = db.Query<SupplierAgent>(curSql).ToList();
                    if (curList != null && curList.Any())
                        supplierAgents.AddRange(curList);

                    //用户注册时间
                    curSql = $@"SELECT Id,CONVERT(NVARCHAR(50),CreateTime,23) AS CreateTime FROM P_UserFx WITH(NOLOCK) WHERE [Id] IN ({string.Join(",", chunk)})";

                    var curUserList = db.Query<UserFx>(curSql).ToList();
                    if (curUserList != null && curUserList.Any())
                        userFxs.AddRange(curUserList);

                    //设置了下单店铺的厂家
                    curSql = $@"SELECT fus.FxUserId FROM P_CommonSetting cs WITH(NOLOCK)
INNER JOIN P_FxUserShop fus WITH(NOLOCK) ON fus.ShopId =cs.ShopId 
WHERE cs.[Key] ='/ErpWeb/1688Supply/Supplier/1688Shop' AND fus.PlatformType ='System' AND fus.FxUserId IN ({string.Join(",", chunk)})";
                    var curFxUserIds = db.Query<int>(curSql).ToList();
                    if (curFxUserIds != null && curFxUserIds.Any())
                        hasSet1688ShopSupplierFxUserIds.AddRange(curFxUserIds);


                    //首次开通预付的厂家
                    curSql = $@"SELECT SupplierFxUserId AS FxUserId,CONVERT(NVARCHAR(50),MIN(OpenPrePayTime),23) AS FirstDate FROM P_SupplierUser WITH(NOLOCK) WHERE OpenPrePayTime IS NOT NULL 
AND SupplierFxUserId IN ({string.Join(",", chunk)})
GROUP BY SupplierFxUserId";
                    var curOpenPrepaySuppliers = db.Query<StatModelV2>(curSql).ToList();
                    if (curOpenPrepaySuppliers != null && curOpenPrepaySuppliers.Any())
                        firstOpenPrepaySuppliers.AddRange(curOpenPrepaySuppliers);


                    //首次开通预付的商家
                    curSql = $@"SELECT FxUserId,CONVERT(NVARCHAR(50),MIN(OpenPrePayTime),23) AS FirstDate FROM P_SupplierUser WITH(NOLOCK) WHERE OpenPrePayTime IS NOT NULL 
AND SupplierFxUserId IN ({string.Join(",", chunk)})
GROUP BY FxUserId";
                    var curOpenPrepayAgents = db.Query<StatModelV2>(curSql).ToList();
                    if (curOpenPrepayAgents != null && curOpenPrepayAgents.Any())
                        firstOpenPrepayAgents.AddRange(curOpenPrepayAgents);

                });
            }


            //区分出新老店铺的厂家
            if (userFxs.Any())
            {
                stat1688Shops.Where(a => userFxs.Select(user => user.Id).Contains(a.FxUserId)).ToList().ForEach(stat =>
                {
                    var existUser = userFxs.First(a => a.Id == stat.FxUserId);
                    stat.IsOldUser = existUser.CreateTime.ToString("yyyy-MM-dd") != stat.ShopCreateTime.ToString("yyyy-MM-dd");
                    stat.UserRegisterTime = existUser.CreateTime;
                });
            }
            oldShopSupplierFxUserIds = stat1688Shops.Where(a => a.IsOldShop).Select(a => a.FxUserId).Distinct().ToList();
            newShopSupplierFxUserIds = stat1688Shops.Where(a => a.IsOldShop == false).Select(a => a.FxUserId).Distinct().ToList();

            //区分出新老店铺的商家用户
            if (supplierAgents.Any())
            {
                supplierAgents.ForEach(sa =>
                {
                    var existUser = stat1688Shops.FirstOrDefault(a => a.FxUserId == sa.SupplierFxUserId && a.IsOldShop);
                    if (existUser != null)
                    {
                        sa.IsOldSupplierShop = true;
                    }
                });
            }
            oldShopAgentFxUserIds = supplierAgents.Where(a => a.IsOldSupplierShop).Select(a => a.AgentFxUserId).Distinct().ToList();
            newShopAgentFxUserIds = supplierAgents.Where(a => a.IsOldSupplierShop == false).Select(a => a.AgentFxUserId).Distinct().ToList();

            var agentFxUserIds = supplierAgents.Select(a => a.AgentFxUserId).Distinct().ToList();
            var hasBindedBuyerAgents = new List<StatModelV2>();
            if (agentFxUserIds.Any())
            {
                var chunks = agentFxUserIds.ChunkList(batchSize);
                chunks.ForEach(chunk =>
                {

                    //已绑定买家账号的用户
                    var curSql = $@"SELECT FxUserId,CONVERT(NVARCHAR(50),CreateTime,23) AS FirstDate FROM dbo.FxAlibabaBuyerShopRelation WITH(NOLOCK) WHERE [Status]=1  
AND FxUserId IN ({string.Join(",", chunk)})";
                    var curHasBindedBuyerAgents = db.Query<StatModelV2>(curSql).ToList();
                    if (curHasBindedBuyerAgents != null && curHasBindedBuyerAgents.Any())
                        hasBindedBuyerAgents.AddRange(curHasBindedBuyerAgents);
                });
            }

            //授权已过期数量
            sql = $@"SELECT COUNT(1) StatNum FROM dbo.P_ShopExtension se WITH(NOLOCK)
INNER JOIN dbo.P_Shop s (NOLOCK) ON se.ShopId = s.Id 
  WHERE se.AppKey='5382198' AND se.ExpireTime < GETDATE()";
            var authExpiredNum = db.QueryFirstOrDefault<int>(sql);

            //已开通预付商家
            var openedPrepayAgentFxUserIds = firstOpenPrepayAgents.Select(a => a.FxUserId).Distinct().ToList();
            //未开通预付商家
            var notOpenPrepayAgentFxUserIds = agentFxUserIds.Where(a => openedPrepayAgentFxUserIds.Contains(a) == false).Distinct().ToList();

            //精选业务库
            var dbList = new DbConfigRepository().GetFxAllCloudPlatformBusinessDbConfigsByAlibaba();
            var alibabaDbs = dbList.ToList();

            //抖店业务库
            var allFxUserIds = agentFxUserIds;
            allFxUserIds.AddRange(supplierFxUserIds);
            allFxUserIds = allFxUserIds.Distinct().ToList();
            var toutiaoDbs = new DbConfigRepository().GetFxDbConfigModels(allFxUserIds, CloudPlatformType.TouTiao.ToString());

            var mappingProductUsers = new List<StatModelV2>();
            var firstPurchaseSuppliers = new List<StatModelV2>();
            var firstReturnSuppliers = new List<StatModelV2>();

            var firstPostProductAgents = new List<StatModelV2>();
            var notOpenPrepayFirstPostOrderAgents = new List<StatModelV2>();

            //采购单总数、已付款数、已发货数、回流成功、回流失败

            //近七天的用户
            var purchaseSevenResult = new List<StatModel>();
            var purchaseSuccessSevenResult = new List<StatModel>();
            var payedSevenResult = new List<StatModel>();
            var sendedSevenResult = new List<StatModel>();
            var returnSuccessSevenResult = new List<StatModel>();
            var returnFailSevenResult = new List<StatModel>();

            //不限用户
            var purchaseResult = new List<StatModel>();
            var purchaseSuccessResult = new List<StatModel>();
            var payedResult = new List<StatModel>();
            var sendedResult = new List<StatModel>();
            var returnSuccessResult = new List<StatModel>();
            var returnFailResult = new List<StatModel>();

            var purchaseOrderFailList = new List<PurchaseOrderRelation>();
            var returnFailList = new List<SendHistoryReturnRecord>();

            alibabaDbs.ForEach(oDb =>
            {
                var curDb = new PurchaseOrderRelationRepository(oDb.ConnectionString).DbConnection;

                Log.WriteLine($"开始查询精选{oDb.DbNameConfig.DbName}库");

                //厂家维度分批查询
                var chunks = supplierFxUserIds.ChunkList(batchSize);
                chunks.ForEach(chunk =>
                {
                    //首次下采购单
                    var curSql = $@"SELECT PurchaseOrderFxUserId AS FxUserId,CONVERT(NVARCHAR(50),MIN( CreateTime), 23) AS FirstDate FROM PurchaseOrderRelation WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND PurchaseOrderFxUserId IN ({string.Join(",", chunk)}) 
GROUP BY PurchaseOrderFxUserId";
                    var curFirstPurchaseSuppliers = curDb.Query<StatModelV2>(curSql).ToList();
                    if (curFirstPurchaseSuppliers != null && curFirstPurchaseSuppliers.Any())
                        firstPurchaseSuppliers.AddRange(firstPurchaseSuppliers);

                    //首次回传
                    curSql = $@"SELECT SendFxUserId AS FxUserId,CONVERT(NVARCHAR(50),MIN( CreateTime), 23) AS FirstDate FROM SendHistoryReturnRecord WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND SendFxUserId IN ({string.Join(",", chunk)}) 
GROUP BY SendFxUserId";
                    var curFirstReturnSuppliers = curDb.Query<StatModelV2>(curSql).ToList();
                    if (curFirstReturnSuppliers != null && curFirstReturnSuppliers.Any())
                        firstReturnSuppliers.AddRange(curFirstReturnSuppliers);

                    //商品关联
                    curSql = $@"SELECT DISTINCT DownFxUserId,UpFxUserId FROM DistributorProductSkuMapping WITH(NOLOCK) WHERE UpFxUserId IN ({string.Join(",", chunk)}) ";
                    var curMappingProductUsers = curDb.Query<StatModelV2>(curSql).ToList();
                    if (curMappingProductUsers != null && curMappingProductUsers.Any())
                        mappingProductUsers.AddRange(curMappingProductUsers);


                    //底单记录
                    curSql = $@"SELECT FxUserId ,CONVERT(NVARCHAR(50),MIN([GetDate]),23) AS FirstDate FROM P_WaybillCode 
WHERE FxUserId  IN ({string.Join(",", chunk)}) 
AND [GetDate]>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND [GetDate]<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
GROUP BY FxUserId";
                    var curPrints = curDb.Query<StatModelV2>(curSql).ToList();
                    if (curPrints != null && curPrints.Any())
                        firstPrints.AddRange(curPrints);

                    #region 近七天订购的厂家的采购单、回流统计

                    //采购单数量统计（按日期维度）
                    curSql = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,COUNT(1) AS StatNum FROM dbo.PurchaseOrderRelation WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND PurchaseOrderFxUserId IN ({string.Join(",", chunk)})
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23) ";
                    var curStatResult = curDb.Query<StatModel>(curSql).ToList();
                    if (curStatResult != null && curStatResult.Any())
                    {
                        curStatResult.ForEach(stat =>
                        {
                            var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                            if (exit == null)
                            {
                                exit = new Stat1688V2();
                                exit.StatDate = stat.StatDate;
                                exit.TaskCode = taskCode;

                                statList.Add(exit);
                            }
                            //累计
                            exit.SevenDaysUserPurchaseOrderNum += stat.StatNum;

                        });
                    }

                    //成功采购单数量统计（按日期维度）
                    curSql = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,COUNT(1) AS StatNum FROM dbo.PurchaseOrderRelation WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND PurchaseOrderFxUserId IN ({string.Join(",", chunk)})
AND Status=1
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23) ";
                    curStatResult = curDb.Query<StatModel>(curSql).ToList();
                    if (curStatResult != null && curStatResult.Any())
                    {
                        curStatResult.ForEach(stat =>
                        {
                            var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                            if (exit == null)
                            {
                                exit = new Stat1688V2();
                                exit.StatDate = stat.StatDate;
                                exit.TaskCode = taskCode;

                                statList.Add(exit);
                            }
                            //累计
                            exit.SevenDaysUserPurchaseOrderSuccessNum += stat.StatNum;

                        });
                    }

                    //已付款采购单数量统计（按日期维度）
                    curSql = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,COUNT(1) AS StatNum FROM dbo.PurchaseOrderRelation WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND PurchaseOrderFxUserId IN ({string.Join(",", chunk)})
AND PurchaseOrderStatus IN('waitsellersend','waitbuyerreceiver','waitbuyerreceive','success')
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23) ";
                    curStatResult = curDb.Query<StatModel>(curSql).ToList();
                    if (curStatResult != null && curStatResult.Any())
                    {
                        curStatResult.ForEach(stat =>
                        {
                            var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                            if (exit == null)
                            {
                                exit = new Stat1688V2();
                                exit.StatDate = stat.StatDate;
                                exit.TaskCode = taskCode;

                                statList.Add(exit);
                            }
                            //累计
                            exit.SevenDaysUserPurchaseOrderPayedNum += stat.StatNum;

                        });
                    }

                    //已发货采购单数量统计（按日期维度）
                    curSql = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,COUNT(1) AS StatNum FROM dbo.PurchaseOrderRelation WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND PurchaseOrderFxUserId IN ({string.Join(",", chunk)})
AND PurchaseOrderStatus IN('waitbuyerreceiver','waitbuyerreceive','success')
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23) ";
                    curStatResult = curDb.Query<StatModel>(curSql).ToList();
                    if (curStatResult != null && curStatResult.Any())
                    {
                        curStatResult.ForEach(stat =>
                        {
                            var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                            if (exit == null)
                            {
                                exit = new Stat1688V2();
                                exit.StatDate = stat.StatDate;
                                exit.TaskCode = taskCode;

                                statList.Add(exit);
                            }
                            //累计
                            exit.SevenDaysUserPurchaseOrderSendedNum += stat.StatNum;

                        });
                    }

                    //回流成功（按日期维度）
                    curSql = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,COUNT(1) AS StatNum FROM dbo.SendHistoryReturnRecord WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND PurchaseFxUserId IN ({string.Join(",", chunk)})
AND Status=1 
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23) ";
                    curStatResult = curDb.Query<StatModel>(curSql).ToList();
                    if (curStatResult != null && curStatResult.Any())
                    {
                        curStatResult.ForEach(stat =>
                        {
                            var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                            if (exit == null)
                            {
                                exit = new Stat1688V2();
                                exit.StatDate = stat.StatDate;
                                exit.TaskCode = taskCode;

                                statList.Add(exit);
                            }
                            //累计
                            exit.SevenDaysUserReturnSuccessNum += stat.StatNum;

                        });
                    }

                    //回流失败（按日期维度）
                    curSql = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,COUNT(1) AS StatNum FROM dbo.SendHistoryReturnRecord WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND PurchaseFxUserId IN ({string.Join(",", chunk)})
AND Status=0 AND ErrorType<>'' 
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23) ";
                    curStatResult = curDb.Query<StatModel>(curSql).ToList();
                    if (curStatResult != null && curStatResult.Any())
                    {
                        curStatResult.ForEach(stat =>
                        {
                            var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                            if (exit == null)
                            {
                                exit = new Stat1688V2();
                                exit.StatDate = stat.StatDate;
                                exit.TaskCode = taskCode;

                                statList.Add(exit);
                            }
                            //累计
                            exit.SevenDaysUserReturnFailNum += stat.StatNum;

                        });
                    }
                    #endregion
                });


                #region 不限用户的采购单、回流统计、失败的采购单、失败的回流记录
                //采购单数量统计（按日期维度）
                var curSql2 = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,COUNT(1) AS StatNum FROM dbo.PurchaseOrderRelation WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23) ";
                var curStatResult2 = curDb.Query<StatModel>(curSql2).ToList();
                if (curStatResult2 != null && curStatResult2.Any())
                {
                    curStatResult2.ForEach(stat =>
                    {
                        var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                        if (exit == null)
                        {
                            exit = new Stat1688V2();
                            exit.StatDate = stat.StatDate;
                            exit.TaskCode = taskCode;

                            statList.Add(exit);
                        }
                        //累计
                        exit.PurchaseOrderNum += stat.StatNum;

                    });
                }

                //成功采购单数量统计（按日期维度）
                curSql2 = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,COUNT(1) AS StatNum FROM dbo.PurchaseOrderRelation WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND Status=1
AND PurchaseOrderFxUserId>0
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23) ";
                curStatResult2 = curDb.Query<StatModel>(curSql2).ToList();
                if (curStatResult2 != null && curStatResult2.Any())
                {
                    curStatResult2.ForEach(stat =>
                    {
                        var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                        if (exit == null)
                        {
                            exit = new Stat1688V2();
                            exit.StatDate = stat.StatDate;
                            exit.TaskCode = taskCode;

                            statList.Add(exit);
                        }
                        //累计
                        exit.PurchaseOrderSuccessNum += stat.StatNum;

                    });
                }

                //已付款采购单数量统计（按日期维度）
                curSql2 = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,COUNT(1) AS StatNum FROM dbo.PurchaseOrderRelation WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND PurchaseOrderFxUserId>0
AND PurchaseOrderStatus IN('waitsellersend','waitbuyerreceiver','waitbuyerreceive','success')
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23) ";
                curStatResult2 = curDb.Query<StatModel>(curSql2).ToList();
                if (curStatResult2 != null && curStatResult2.Any())
                {
                    curStatResult2.ForEach(stat =>
                    {
                        var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                        if (exit == null)
                        {
                            exit = new Stat1688V2();
                            exit.StatDate = stat.StatDate;
                            exit.TaskCode = taskCode;

                            statList.Add(exit);
                        }
                        //累计
                        exit.PurchaseOrderPayedNum += stat.StatNum;

                    });
                }

                //已发货采购单数量统计（按日期维度）
                curSql2 = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,COUNT(1) AS StatNum FROM dbo.PurchaseOrderRelation WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND PurchaseOrderFxUserId>0
AND PurchaseOrderStatus IN('waitbuyerreceiver','waitbuyerreceive','success')
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23) ";
                curStatResult2 = curDb.Query<StatModel>(curSql2).ToList();
                if (curStatResult2 != null && curStatResult2.Any())
                {
                    curStatResult2.ForEach(stat =>
                    {
                        var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                        if (exit == null)
                        {
                            exit = new Stat1688V2();
                            exit.StatDate = stat.StatDate;
                            exit.TaskCode = taskCode;

                            statList.Add(exit);
                        }
                        //累计
                        exit.PurchaseOrderSendedNum += stat.StatNum;

                    });
                }

                //回流成功（按日期维度）
                curSql2 = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,COUNT(1) AS StatNum FROM dbo.SendHistoryReturnRecord WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND Status=1 
AND PurchaseFxUserId>0
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23) ";
                curStatResult2 = curDb.Query<StatModel>(curSql2).ToList();
                if (curStatResult2 != null && curStatResult2.Any())
                {
                    curStatResult2.ForEach(stat =>
                    {
                        var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                        if (exit == null)
                        {
                            exit = new Stat1688V2();
                            exit.StatDate = stat.StatDate;
                            exit.TaskCode = taskCode;

                            statList.Add(exit);
                        }
                        //累计
                        exit.ReturnSuccessNum += stat.StatNum;

                    });
                }

                //回流失败（按日期维度）
                curSql2 = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,COUNT(1) AS StatNum FROM dbo.SendHistoryReturnRecord WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND Status=0 AND ErrorType<>'' 
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23) ";
                curStatResult2 = curDb.Query<StatModel>(curSql2).ToList();
                if (curStatResult2 != null && curStatResult2.Any())
                {
                    curStatResult2.ForEach(stat =>
                    {
                        var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                        if (exit == null)
                        {
                            exit = new Stat1688V2();
                            exit.StatDate = stat.StatDate;
                            exit.TaskCode = taskCode;

                            statList.Add(exit);
                        }
                        //累计
                        exit.ReturnFailNum += stat.StatNum;

                    });
                }

                //回流成功（按类型+日期维度）
                curSql2 = $@"SELECT CONVERT(NVARCHAR(50),CreateTime,23) AS StatDate,DeliveryMode,COUNT(1) AS StatNum FROM dbo.SendHistoryReturnRecord WITH(NOLOCK) 
WHERE CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND Status=1 
AND PurchaseFxUserId>0
GROUP BY CONVERT(NVARCHAR(50),CreateTime,23), DeliveryMode";
                curStatResult2 = curDb.Query<StatModel>(curSql2).ToList();
                if (curStatResult2 != null && curStatResult2.Any())
                {
                    curStatResult2.ForEach(stat =>
                    {
                        var exit = statList.FirstOrDefault(a => a.StatDate == stat.StatDate);
                        if (exit == null)
                        {
                            exit = new Stat1688V2();
                            exit.StatDate = stat.StatDate;
                            exit.TaskCode = taskCode;

                            statList.Add(exit);
                        }
                        //按类型累计
                        if (stat.DeliveryMode == 0)
                            exit.DeliveryModel0SuccessNum += stat.StatNum;
                        else if (stat.DeliveryMode == 1)
                            exit.DeliveryModel1SuccessNum += stat.StatNum;
                        else if (stat.DeliveryMode == 10)
                            exit.DeliveryModel10SuccessNum += stat.StatNum;
                        else if (stat.DeliveryMode == 11)
                            exit.DeliveryModel11SuccessNum += stat.StatNum;

                    });
                }

                //下单失败明细
                curSql2 = $@"SELECT CreateTime,CreateFxUserId,PurchaseOrderFxUserId,PurchaseErrorMessage FROM PurchaseOrderRelation WITH(NOLOCK) 
WHERE [Status] =-1 
AND CreateTime>= '{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND PurchaseErrorMessage NOT LIKE '%已存在正在下单%'";

                var curPors = curDb.Query<PurchaseOrderRelation>(curSql2).ToList();
                if (curPors != null && curPors.Any())
                    purchaseOrderFailList.AddRange(curPors);

                //回流失败明细
                curSql2 = $@"SELECT CreateTime,PurchaseFxUserId,SourceFxUserId,DeliveryMode,ErrorMessage FROM SendHistoryReturnRecord WITH(NOLOCK) 
WHERE Status=0 AND ErrorType<>'' AND PurchaseFxUserId>0
AND CreateTime>= '{dtStart.Value.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND ErrorMessage NOT LIKE '%不是待发货状态%'";

                var curShrrs = curDb.Query<SendHistoryReturnRecord>(curSql2).ToList();
                if (curShrrs != null && curShrrs.Any())
                    returnFailList.AddRange(curShrrs);

                #endregion

                var fx1688SupportPlatformTypes = CustomerConfig.Fx1688SupportPlatformTypes;
                var notAlibabaPts = new List<string> { PlatformType.TouTiao.ToString(), PlatformType.Jingdong.ToString(), PlatformType.Pinduoduo.ToString(), PlatformType.KuaiTuanTuan.ToString() };
                fx1688SupportPlatformTypes = fx1688SupportPlatformTypes.Where(a => notAlibabaPts.Contains(a) == false).ToList();
                if (fx1688SupportPlatformTypes.Any())
                {
                    //商家维度分批查询
                    var chunkAgents = agentFxUserIds.ChunkList(batchSize);
                    chunkAgents.ForEach(chunk =>
                    {
                        //首次推品
                        var curSupplierFxUserIds = supplierAgents.Where(a => chunk.Contains(a.AgentFxUserId)).Select(a => a.SupplierFxUserId).Distinct().ToList();
                        if (curSupplierFxUserIds.Any())
                        {
                            var curSql = $@"SELECT pfn.FxUserId,CONVERT(NVARCHAR(50),MIN( pfr.CreateTime), 23) AS FirstDate FROM PathFlowReference pfr WITH(NOLOCK) 
INNER JOIN Product p WITH(NOLOCK) ON p.ProductCode =pfr.ProductCode 
INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode =pfr.PathFlowCode
WHERE p.PlatformType IN('{string.Join("','", fx1688SupportPlatformTypes)}')
AND pfr.[Status] =0 AND pfr.CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND pfr.CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND pfn.FxUserId IN ({string.Join(",", chunk)})  AND pfn.DownFxUserId IN ({string.Join(",", curSupplierFxUserIds)}) 
GROUP BY pfn.FxUserId
";
                            var curFirstPostProductAgents = curDb.Query<StatModelV2>(curSql).ToList();
                            if (curFirstPostProductAgents != null && curFirstPostProductAgents.Any())
                                firstPostProductAgents.AddRange(curFirstPostProductAgents);
                        }
                    });
                }

                //非预付商家维度分批查询
                if (notOpenPrepayAgentFxUserIds.Any())
                {
                    var chunkAgents = notOpenPrepayAgentFxUserIds.ChunkList(batchSize);
                    chunkAgents.ForEach(chunk =>
                    {
                        //非预付商家首次推单
                        var curSupplierFxUserIds = supplierAgents.Where(a => chunk.Contains(a.AgentFxUserId)).Select(a => a.SupplierFxUserId).Distinct().ToList();
                        if (curSupplierFxUserIds.Any())
                        {
                            //按日期分批查询
                            var count = Math.Ceiling((dtEnd.Value - dtStart.Value).TotalMinutes / timeSize);
                            for (int i = 0; i < count; i++)
                            {
                                var tmpStartTime = dtStart.Value.AddMinutes(i * timeSize);
                                var tmpEndTime = tmpStartTime.AddMinutes(timeSize);

                                var curSql = $@"SELECT pfn.FxUserId,CONVERT(NVARCHAR(50),MIN( lo.CreateTime), 23) AS FirstDate FROM LogicOrder lo WITH(NOLOCK)
INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode =lo.PathFlowCode 
WHERE lo.CreateTime>='{tmpStartTime.ToString("yyyy-MM-dd HH:mm:ss")}'
AND lo.CreateTime<'{tmpEndTime.ToString("yyyy-MM-dd HH:mm:ss")}'
AND pfn.FxUserId IN ({string.Join(",", chunk)}) AND pfn.DownFxUserId IN ({string.Join(",", curSupplierFxUserIds)})
GROUP BY pfn.FxUserId";
                                var curFirstPostOrderAgents = curDb.Query<StatModelV2>(curSql).ToList();
                                if (curFirstPostOrderAgents != null && curFirstPostOrderAgents.Any())
                                    notOpenPrepayFirstPostOrderAgents.AddRange(curFirstPostOrderAgents);
                            }
                        }
                    });
                }
            });

            //抖店云
            var dbGroup = toutiaoDbs.GroupBy(g => g.ConnectionString).ToList();
            Log.WriteLine($"相关抖店库：{dbGroup.Count()}个，开始并发查询");
            var seqNumber = 1;

            //多线程并发
            Parallel.ForEach(dbGroup, new ParallelOptions { MaxDegreeOfParallelism = 5 }, dc =>
            {
                var firstDc = dc.First();
                Log.WriteLine($"开始查询抖店第{seqNumber}个库：{firstDc.DbNameConfig.DbName}");
                seqNumber++;
                var targetFxUserIds = dc.Select(m => m.DbConfig.UserId).Distinct().ToList();

                //初始化同步业务数据服务
                var apiDbConfig = new ApiDbConfigModel(firstDc.DbServer.Location, firstDc.DbServer.Location, firstDc.DbNameConfig.Id);

                var _apiAccessUtility = new DbApiAccessUtility(apiDbConfig);

                //商家维度分批查询
                var targetAgentFxUserIds = agentFxUserIds.Where(a => targetFxUserIds.Contains(a)).ToList();
                if (targetAgentFxUserIds.Any())
                {
                    var chunks = targetAgentFxUserIds.ChunkList(batchSize);
                    chunks.ForEach(chunk =>
                    {
                        //首次推品
                        var curSupplierFxUserIds = supplierAgents.Where(a => chunk.Contains(a.AgentFxUserId)).Select(a => a.SupplierFxUserId).Distinct().ToList();
                        if (curSupplierFxUserIds.Any())
                        {
                            var curSql = $@"SELECT pfn.FxUserId,CONVERT(NVARCHAR(50),MIN( pfr.CreateTime), 23) AS FirstDate FROM PathFlowReference pfr WITH(NOLOCK) 
INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode =pfr.PathFlowCode
WHERE pfr.[Status] =0 AND pfr.CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND pfr.CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND pfn.FxUserId IN ({string.Join(",", chunk)})  AND pfn.DownFxUserId IN ({string.Join(",", curSupplierFxUserIds)}) 
GROUP BY pfn.FxUserId
";
                            var curFirstPostProductAgents = _apiAccessUtility.Query<StatModelV2>(curSql).ToList();
                            if (curFirstPostProductAgents != null && curFirstPostProductAgents.Any())
                                firstPostProductAgents.AddRange(curFirstPostProductAgents);
                        }

                    });
                }


                //非预付商家维度分批查询
                if (notOpenPrepayAgentFxUserIds.Any())
                {
                    var chunkAgents = notOpenPrepayAgentFxUserIds.ChunkList(batchSize);
                    chunkAgents.ForEach(chunk =>
                    {
                        //非预付商家首次推单
                        var curSupplierFxUserIds = supplierAgents.Where(a => chunk.Contains(a.AgentFxUserId)).Select(a => a.SupplierFxUserId).Distinct().ToList();
                        if (curSupplierFxUserIds.Any())
                        {
                            var curSql = $@"SELECT pfn.FxUserId,CONVERT(NVARCHAR(50),MIN( lo.CreateTime), 23) AS FirstDate FROM LogicOrder lo WITH(NOLOCK)
INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode =lo.PathFlowCode 
WHERE lo.CreateTime>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND lo.CreateTime<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
AND pfn.FxUserId IN ({string.Join(",", chunk)}) AND pfn.DownFxUserId IN ({string.Join(",", curSupplierFxUserIds)})
GROUP BY pfn.FxUserId";
                            var curFirstPostOrderAgents = _apiAccessUtility.Query<StatModelV2>(curSql).ToList();
                            if (curFirstPostOrderAgents != null && curFirstPostOrderAgents.Any())
                                notOpenPrepayFirstPostOrderAgents.AddRange(curFirstPostOrderAgents);
                        }
                    });
                }

                //厂家维度分批查询
                var targetSupplierFxUserIds = supplierFxUserIds.Where(a => targetFxUserIds.Contains(a)).ToList();
                if (targetSupplierFxUserIds.Any())
                {
                    var chunkSuppliers = targetSupplierFxUserIds.ChunkList(batchSize);
                    chunkSuppliers.ForEach(chunk =>
                    {
                        //底单记录
                        var curSql = $@"SELECT FxUserId ,CONVERT(NVARCHAR(50),MIN([GetDate]),23) AS FirstDate FROM P_WaybillCode 
WHERE FxUserId  IN ({string.Join(",", chunk)}) 
AND [GetDate]>='{dtStart.Value.ToString("yyyy-MM-dd")}'
AND [GetDate]<'{dtEnd.Value.ToString("yyyy-MM-dd")}'
GROUP BY FxUserId";
                        var curPrints = _apiAccessUtility.Query<StatModelV2>(curSql).ToList();
                        if (curPrints != null && curPrints.Any())
                            firstPrints.AddRange(curPrints);
                    });
                }

            });

            //同一用户取采购时间小的
            firstPurchaseSuppliers = firstPurchaseSuppliers.GroupBy(g => g.FxUserId).Select(x => new StatModelV2 { FxUserId = x.Key, FirstDate = x.Min(a => a.FirstDate) }).ToList();

            //同一用户取订单时间小的
            notOpenPrepayFirstPostOrderAgents = notOpenPrepayFirstPostOrderAgents.GroupBy(g => g.FxUserId).Select(x => new StatModelV2 { FxUserId = x.Key, FirstDate = x.Min(a => a.FirstDate) }).ToList();

            //同一用户取回传时间小的
            firstReturnSuppliers = firstReturnSuppliers.GroupBy(g => g.FxUserId).Select(x => new StatModelV2 { FxUserId = x.Key, FirstDate = x.Min(a => a.FirstDate) }).ToList();

            //同一用户取打印时间小的
            firstPrints = firstPrints.GroupBy(g => g.FxUserId).Select(x => new StatModelV2 { FxUserId = x.Key, FirstDate = x.Min(a => a.FirstDate) }).ToList();

            if (stat1688Shops.Any())
            {
                stat1688Shops.GroupBy(g => g.ShopCreateTime).ToList().ForEach(g =>
                {
                    var exit = statList.FirstOrDefault(a => a.StatDate == g.Key);
                    if (exit == null)
                    {
                        exit = new Stat1688V2();
                        exit.StatDate = g.Key;
                        exit.TaskCode = taskCode;

                        statList.Add(exit);
                    }

                    exit.CreateTime = dtNow;
                    exit.AuthExpiredNum = authExpiredNum;

                    exit.AddQingAppShopNum = g.Count();
                    exit.OldShopAddQingAppShopNum = g.Where(a => a.IsOldShop).Count();
                    exit.NewShopAddQingAppShopNum = g.Where(a => a.IsOldShop == false).Count();
                    exit.AddQingAppOldUserSupplierNum = g.Where(a => a.IsOldUser).Select(a => a.FxUserId).Distinct().Count();
                    exit.AddQingAppNewUserSupplierNum = g.Where(a => a.IsOldUser == false).Select(a => a.FxUserId).Distinct().Count();

                    exit.OldShopHasSet1688ShopSupplierNum = g.Where(a => a.IsOldShop && hasSet1688ShopSupplierFxUserIds.Contains(a.FxUserId)).Select(a => a.FxUserId).Distinct().Count();
                    exit.NewShopHasSet1688ShopSupplierNum = g.Where(a => a.IsOldShop == false && hasSet1688ShopSupplierFxUserIds.Contains(a.FxUserId)).Select(a => a.FxUserId).Distinct().Count();


                    //当天为首次回传日期的厂家数
                    var curDayFirstReturnSupplierFxUserIds = firstReturnSuppliers.Where(a => a.FirstDate == g.Key).Select(a => a.FxUserId).ToList();
                    exit.OldShopFirstReturnSupplierNum = g.Where(a => a.IsOldShop && curDayFirstReturnSupplierFxUserIds.Contains(a.FxUserId)).Select(a => a.FxUserId).Distinct().Count();
                    exit.NewShopFirstReturnSupplierNum = g.Where(a => a.IsOldShop == false && curDayFirstReturnSupplierFxUserIds.Contains(a.FxUserId)).Select(a => a.FxUserId).Distinct().Count();

                    //当天首次打印的用户
                    var curDayFirstPrintFxUserIds = firstPrints.Where(a => a.FirstDate == g.Key).Select(a => a.FxUserId).ToList();
                    exit.NewShopFirstPrintOrderSupplierNum = g.Where(a => a.IsOldShop == false && curDayFirstPrintFxUserIds.Contains(a.FxUserId)).Select(a => a.FxUserId).Distinct().Count();

                    //已绑定了商家的厂家数
                    var curSupplierFxUserIds = g.Select(a => a.FxUserId).Distinct().ToList();
                    exit.OldShopHasBindedAgentSupplierNum = supplierAgents.Where(a => a.IsOldSupplierShop && curSupplierFxUserIds.Contains(a.SupplierFxUserId)).Select(a => a.SupplierFxUserId).Distinct().Count();
                    exit.NewShopHasBindedAgentSupplierNum = supplierAgents.Where(a => a.IsOldSupplierShop == false && curSupplierFxUserIds.Contains(a.SupplierFxUserId)).Select(a => a.SupplierFxUserId).Distinct().Count();

                    //当天为首次开通预付日期的厂家数
                    var curFirstOpenPrepaySupplierFxUserIds = firstOpenPrepaySuppliers.Where(a => a.FirstDate == g.Key).Select(a => a.FxUserId).ToList();
                    exit.OldShopFirstOpenPrepaySupplierNum = g.Where(a => a.IsOldShop && curFirstOpenPrepaySupplierFxUserIds.Contains(a.FxUserId)).Select(a => a.FxUserId).Distinct().Count();
                    exit.NewShopFirstOpenPrepaySupplierNum = g.Where(a => a.IsOldShop == false && curFirstOpenPrepaySupplierFxUserIds.Contains(a.FxUserId)).Select(a => a.FxUserId).Distinct().Count();

                    //绑定了买家账号的商家数                    
                    var curHasBindedBuyerAgentFxUserIds = hasBindedBuyerAgents.Where(a => a.FirstDate == g.Key).Select(a => a.FxUserId).ToList();
                    exit.OldShopHasBindedBuyerAgentNum = supplierAgents.Where(a => a.IsOldSupplierShop && curHasBindedBuyerAgentFxUserIds.Contains(a.AgentFxUserId)).Select(a => a.AgentFxUserId).Distinct().Count();
                    exit.NewShopHasBindedBuyerAgentNum = supplierAgents.Where(a => a.IsOldSupplierShop == false && curHasBindedBuyerAgentFxUserIds.Contains(a.AgentFxUserId)).Select(a => a.AgentFxUserId).Distinct().Count();


                });
            }


            var stat1688FxUserIdList = new List<Stat1688FxUserId>();
            //hasBindedBuyerAgents 已绑定买家账号
            //firstPostProductAgents 已推品给厂家的商家
            //下游商家已授权买家账号，但未使用（未推商品给厂家）
            var hasBindedBuyerButNotPostProductAgentFxUserIds = hasBindedBuyerAgents.Select(a => a.FxUserId).Distinct().ToList();
            var firstPostProductAgentFxUserIds = firstPostProductAgents.Select(a => a.FxUserId).Distinct().ToList();
            hasBindedBuyerButNotPostProductAgentFxUserIds = hasBindedBuyerButNotPostProductAgentFxUserIds.Where(a => firstPostProductAgentFxUserIds.Contains(a) == false).ToList();
            if (hasBindedBuyerButNotPostProductAgentFxUserIds.Any())
            {
                hasBindedBuyerButNotPostProductAgentFxUserIds.ForEach(fxUserId =>
                {
                    stat1688FxUserIdList.Add(new Stat1688FxUserId
                    {
                        Flag = Stat1688FxUserIdFlag.BinedBuyerButNotPostProductAgent.ToInt(),
                        FxUserId = fxUserId,
                        CreateTime = dtNow,
                        TaskCode = taskCode
                    });
                });
            }

            //hasBindedBuyerAgents 已绑定买家账号
            //mappingProductUsers 已关联商品的商家&厂家
            //下游商家已推品给厂家，但厂家未关联商品（商家）
            var mappingProductAgentFxUserIds = mappingProductUsers.Select(a => a.DownFxUserId).Distinct().ToList();
            var hasPostedProductButNotMappingProductAgentFxUserIds = hasBindedBuyerAgents.Select(a => a.FxUserId).Where(a => mappingProductAgentFxUserIds.Contains(a) == false).ToList();
            if (hasPostedProductButNotMappingProductAgentFxUserIds.Any())
            {
                hasPostedProductButNotMappingProductAgentFxUserIds.ForEach(fxUserId =>
                {
                    stat1688FxUserIdList.Add(new Stat1688FxUserId
                    {
                        Flag = Stat1688FxUserIdFlag.PostedProductButNotMappingProductAgent.ToInt(),
                        FxUserId = fxUserId,
                        CreateTime = dtNow,
                        TaskCode = taskCode
                    });
                });
            }
            //hasBindedBuyerAgents 已绑定买家账号
            //下游商家已推品给厂家，但厂家未关联商品（厂家）
            var mappingProductSupplierFxUserIds = mappingProductUsers.Select(a => a.UpFxUserId).Distinct().ToList();

            var hasPostedProductButNotMappingProductSupplierFxUserIds = supplierAgents.Where(a => hasPostedProductButNotMappingProductAgentFxUserIds.Contains(a.AgentFxUserId)).Select(a => a.SupplierFxUserId).Where(a => mappingProductSupplierFxUserIds.Contains(a) == false).ToList();
            if (hasPostedProductButNotMappingProductSupplierFxUserIds.Any())
            {
                hasPostedProductButNotMappingProductSupplierFxUserIds.ForEach(fxUserId =>
                {
                    stat1688FxUserIdList.Add(new Stat1688FxUserId
                    {
                        Flag = Stat1688FxUserIdFlag.PostedProductButNotMappingProductSupplier.ToInt(),
                        FxUserId = fxUserId,
                        CreateTime = dtNow,
                        TaskCode = taskCode
                    });
                });
            }

            if (statList.Any())
            {
                statList.ForEach(stat => { stat.TaskCode = taskCode; stat.CreateTime = dtNow; });
                _baseRepository.BulkWrite(statList, "Stat1688V2");
            }

            if (stat1688Shops.Any())
            {
                stat1688Shops.ForEach(stat => { stat.TaskCode = taskCode; stat.CreateTime = dtNow; });
                _baseRepository.BulkWrite(stat1688Shops, "Stat1688Shop");
            }

            if (stat1688FxUserIdList.Any())
            {
                stat1688FxUserIdList.ForEach(stat => { stat.TaskCode = taskCode; stat.CreateTime = dtNow; });
                _baseRepository.BulkWrite(stat1688FxUserIdList, "Stat1688FxUserId");
            }

            Log.WriteLine($"开始保存Excel文件");
            //保存成Excel文件
            SaveToExcelFile(statList, stat1688FxUserIdList, purchaseOrderFailList, returnFailList);
        }


        /// <summary>
        /// 获取七天内
        /// </summary>
        /// <returns></returns>
        public List<StatCountModel> StatMidSupplierSendedNum(int days = 7)
        {
            if (days < 0)
                days = 7;

            //精选业务库
            var dbList = new DbConfigRepository().GetFxAllCloudPlatformBusinessDbConfigs();
            var alibabaDbs = dbList.Where(a => a.DbServer.Location == CloudPlatformType.Alibaba.ToString()).ToList();

            //所有抖店云库
            var db = _baseRepository.DbConnection;
            var sql = $@"
                        SELECT s.*,n.*
                        FROM dbo.P_DbServerConfig s WITH(NOLOCK) 
                        INNER JOIN dbo.P_DbNameConfig n WITH(NOLOCK) ON s.Id = n.DbServerConfigId
                        WHERE n.ApplicationName='fx_new' AND n.RunningStatus ='Running' ";
            var toutiaoDbs = db.Query<DbServerConfig, DbNameConfig, DbConfigModel>(sql, (server, dbname) =>
            {
                var temp = new DbConfigModel { DbServer = server, DbNameConfig = dbname };
                return temp;
            }, splitOn: "Id,Id").ToList();



            var result = new List<StatCountModel>();

            alibabaDbs.ForEach(oDb =>
            {
                var curDb = new PurchaseOrderRelationRepository(oDb.ConnectionString).DbConnection;

                Log.WriteLine($"开始查询精选{oDb.DbNameConfig.DbName}库");

                sql = $@"SELECT pfn.[FxUserId] ,COUNT(*) AS StatNum,'Alibaba' AS DbCloud FROM P_SendHistory sh WITH(NOLOCK) 
INNER JOIN [PathFlowNode] pfn WITH(NOLOCK) ON pfn.[PathFlowCode] =sh.[PathFlowCode] 
WHERE sh.[SendDate] >=CONVERT(NVARCHAR(50),GETDATE()-{days},23)
AND pfn.[UpFxUserId] >0 AND pfn.[DownFxUserId] >0
GROUP BY pfn.[FxUserId] ";
                var curList = curDb.Query<StatCountModel>(sql).ToList();
                if (curList != null && curList.Any())
                    result.AddRange(curList);

            });


            //同一用户累计结果
            result = result.GroupBy(g => g.FxUserId).Select(x => new StatCountModel { FxUserId = x.Key, StatNum = x.Sum(a => a.StatNum) }).ToList();

            var ttResult = new List<StatCountModel>();
            //抖店云
            Log.WriteLine($"相关抖店库：{toutiaoDbs.Count()}个，开始并发查询");
            var seqNumber = 1;
            //多线程并发
            Parallel.ForEach(toutiaoDbs, new ParallelOptions { MaxDegreeOfParallelism = 5 }, dc =>
            {
                Log.WriteLine($"开始查询抖店第{seqNumber}个库：{dc.DbNameConfig.DbName}");
                seqNumber++;

                //初始化同步业务数据服务
                var apiDbConfig = new ApiDbConfigModel(dc.DbServer.Location, dc.DbServer.Location, dc.DbNameConfig.Id);

                var _apiAccessUtility = new DbApiAccessUtility(apiDbConfig);


                sql = $@"SELECT pfn.[FxUserId] ,COUNT(*) AS StatNum,'TouTiao' AS DbCloud FROM P_SendHistory sh WITH(NOLOCK) 
INNER JOIN [PathFlowNode] pfn WITH(NOLOCK) ON pfn.[PathFlowCode] =sh.[PathFlowCode] 
WHERE sh.[SendDate] >=CONVERT(NVARCHAR(50),GETDATE()-{days},23)
AND pfn.[UpFxUserId] >0 AND pfn.[DownFxUserId] >0
GROUP BY pfn.[FxUserId] ";

                var curList = _apiAccessUtility.Query<StatCountModel>(sql).ToList();
                if (curList != null && curList.Any())
                    ttResult.AddRange(curList);
            });

            //同一用户取最大值
            var ttLastResult = ttResult.Where(a => a.DbCloud == "TouTiao").GroupBy(g => g.FxUserId).Select(x => new StatCountModel { FxUserId = x.Key, StatNum = x.Max(a => a.StatNum) }).ToList();

            //数据合在一起
            result.AddRange(ttLastResult);
            //同一用户累计结果、排序
            result = result.GroupBy(g => g.FxUserId).Select(x => new StatCountModel { FxUserId = x.Key, StatNum = x.Sum(a => a.StatNum) }).OrderByDescending(a => a.StatNum).ToList();

            Log.WriteLine($"开始查询用户基础数据");
            //查询用户手机号
            var fxUserIds = result.Select(a => a.FxUserId).ToList();
            var fxMobiles = new List<StatCountModel>();
            if (fxUserIds.Any())
            {
                var chunks = fxUserIds.ChunkList(200);
                chunks.ForEach(chunk =>
                {
                    //手机号
                    sql = $@"SELECT Id AS FxUserId ,Mobile FROM P_UserFx WITH(NOLOCK) 
WHERE Id  IN ({string.Join(",", chunk)}) ";
                    var curList = db.Query<StatCountModel>(sql).ToList();
                    if (curList != null && curList.Any())
                        fxMobiles.AddRange(curList);
                });
            }

            Parallel.ForEach(result, new ParallelOptions { MaxDegreeOfParallelism = 5 }, o =>
            {
                var exist = fxMobiles.FirstOrDefault(a => a.FxUserId == o.FxUserId);
                if (exist != null)
                {
                    o.Mobile = exist.Mobile;
                    //此IP为服务器IP，无意义
                    //o.LastLoginIP = exist.LastLoginIP;
                    //if (string.IsNullOrEmpty(exist.LastLoginIP) == false)
                    //{
                    //    var ipModel = GetIpArea(exist.LastLoginIP);
                    //    if (ipModel != null)
                    //    {
                    //        o.IPProvince = ipModel.pro;
                    //        o.IPCity = ipModel.city;
                    //    }
                    //}
                }
            });

            return result;
        }

        public IpModel GetIpArea(string ip)
        {
            var url = $"https://whois.pconline.com.cn/ipJson.jsp?ip={ip}&json=true";
            try
            {
                var str = DianGuanJiaApp.Utility.Net.HttpUtility.Post(url, "");
                if (string.IsNullOrEmpty(str) == false)
                {
                    var ipModel = str.ToObject<IpModel>();
                    if (ipModel != null)
                        return ipModel;
                }
            }
            catch
            {
            }
            return null;
        }


        public void ProcessFromTaskCode(string taskCode)
        {
            if (string.IsNullOrEmpty(taskCode))
            {
                Log.WriteLine($"taskCode为空");
                return;
            }
            var db = _baseRepository.DbConnection;
            var sql = $@"SELECT * FROM Stat1688V2 WITH(NOLOCK) WHERE TaskCode=@taskCode";
            var statList = db.Query<Stat1688V2>(sql, new { taskCode }).ToList();


            sql = $@"SELECT MIN(StatDate) AS StartDate,MAX(StatDate) AS EndDate FROM Stat1688V2 WITH(NOLOCK) WHERE TaskCode=@taskCode";
            var taskDate = db.QueryFirstOrDefault<TaskDate>(sql, new { taskCode });
            var dtStart = DateTime.Now.AddDays(-7);
            var dtEnd = DateTime.Now;
            if (taskDate != null)
            {
                if (taskDate.StartDate.HasValue && taskDate.EndDate.HasValue)
                {
                    dtStart = taskDate.StartDate.Value;
                    dtEnd = taskDate.EndDate.Value.AddDays(1);
                }
            }

            sql = $@"SELECT * FROM Stat1688FxUserId WITH(NOLOCK) WHERE TaskCode=@taskCode";
            var stat1688FxUserIdList = db.Query<Stat1688FxUserId>(sql, new { taskCode }).ToList();


            //精选业务库
            var dbList = new DbConfigRepository().GetFxAllCloudPlatformBusinessDbConfigsByAlibaba();
            var alibabaDbs = dbList.ToList();

            var purchaseOrderFailList = new List<PurchaseOrderRelation>();
            var returnFailList = new List<SendHistoryReturnRecord>();

            alibabaDbs.ForEach(oDb =>
            {
                var curDb = new PurchaseOrderRelationRepository(oDb.ConnectionString).DbConnection;

                Log.WriteLine($"开始查询精选{oDb.DbNameConfig.DbName}库");


                #region 不限用户的失败的采购单、失败的回流记录                

                //下单失败明细
                var curSql2 = $@"SELECT CreateTime,CreateFxUserId,PurchaseOrderFxUserId,PurchaseErrorMessage FROM PurchaseOrderRelation WITH(NOLOCK) 
WHERE [Status] =-1 
AND CreateTime>= '{dtStart.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.ToString("yyyy-MM-dd")}'
AND PurchaseErrorMessage NOT LIKE '%已存在正在下单%'";

                var curPors = curDb.Query<PurchaseOrderRelation>(curSql2).ToList();
                if (curPors != null && curPors.Any())
                    purchaseOrderFailList.AddRange(curPors);

                //回流失败明细
                curSql2 = $@"SELECT CreateTime,PurchaseFxUserId,SourceFxUserId,DeliveryMode,ErrorMessage FROM SendHistoryReturnRecord WITH(NOLOCK) 
WHERE Status=0 AND ErrorType<>'' AND PurchaseFxUserId>0
AND CreateTime>= '{dtStart.ToString("yyyy-MM-dd")}'
AND CreateTime<'{dtEnd.ToString("yyyy-MM-dd")}'
AND ErrorMessage NOT LIKE '%不是待发货状态%'";

                var curShrrs = curDb.Query<SendHistoryReturnRecord>(curSql2).ToList();
                if (curShrrs != null && curShrrs.Any())
                    returnFailList.AddRange(curShrrs);

                #endregion
            });

            Log.WriteLine($"开始保存Excel文件");
            //保存成Excel文件
            SaveToExcelFile(statList, stat1688FxUserIdList, purchaseOrderFailList, returnFailList);
        }

        /// <summary>
        /// 生成Excel文件
        /// </summary>
        /// <param name="statList"></param>
        /// <param name="stat1688FxUserIdList"></param>
        /// <param name="poRelationList"></param>
        /// <param name="returnList"></param>
        public void SaveToExcelFile(List<Stat1688V2> statList, List<Stat1688FxUserId> stat1688FxUserIdList, List<PurchaseOrderRelation> poRelationList, List<SendHistoryReturnRecord> returnList)
        {

            if (statList == null || statList.Any() == false || stat1688FxUserIdList == null)
                return;

            statList = statList.OrderBy(a => a.StatDate).ToList();

            var db = _baseRepository.DbConnection;
            //查询用户信息
            var fxUserIds = stat1688FxUserIdList.Select(a => a.FxUserId).Distinct().ToList();
            if (poRelationList != null && poRelationList.Any())
            {
                fxUserIds.AddRange(poRelationList.Select(a => a.PurchaseOrderFxUserId));
                fxUserIds.AddRange(poRelationList.Select(a => a.CreateFxUserId));

                fxUserIds = fxUserIds.Distinct().ToList();
            }
            if (returnList != null && returnList.Any())
            {
                fxUserIds.AddRange(returnList.Select(a => a.PurchaseFxUserId));
                fxUserIds.AddRange(returnList.Select(a => a.SourceFxUserId));

                fxUserIds = fxUserIds.Distinct().ToList();
            }

            var fxUsers = new List<SimpleFxUser>();
            if (fxUserIds.Any())
            {
                var chunks = fxUserIds.ChunkList(200);
                chunks.ForEach(chunk =>
                {
                    //手机号
                    var sql = $@"SELECT Id AS FxUserId ,Mobile,CreateTime FROM P_UserFx WITH(NOLOCK) 
WHERE Id  IN ({string.Join(",", chunk)}) ";
                    var curList = db.Query<SimpleFxUser>(sql).ToList();
                    if (curList != null && curList.Any())
                        fxUsers.AddRange(curList);
                });
            }

            //用户数据赋值
            stat1688FxUserIdList.ForEach(o =>
            {
                var exist = fxUsers.FirstOrDefault(a => a.FxUserId == o.FxUserId);
                if (exist != null)
                {
                    o.Mobile = exist.Mobile;
                    o.UserCreateTime = exist.CreateTime;
                }
            });

            if (poRelationList != null && poRelationList.Any())
            {
                poRelationList.ForEach(o =>
                {
                    o.SupplierMobile = fxUsers.FirstOrDefault(a => a.FxUserId == o.PurchaseOrderFxUserId)?.Mobile;
                    o.AgentMobile = fxUsers.FirstOrDefault(a => a.FxUserId == o.CreateFxUserId)?.Mobile;
                });
            }

            if (returnList != null && returnList.Any())
            {
                returnList.ForEach(o =>
                {
                    o.SupplierMobile = fxUsers.FirstOrDefault(a => a.FxUserId == o.PurchaseFxUserId)?.Mobile;
                    o.AgentMobile = fxUsers.FirstOrDefault(a => a.FxUserId == o.SourceFxUserId)?.Mobile;
                });
            }

            var fxUserId = 42;
            var rp = new DianGuanJiaApp.Data.Repository.UserFxRepository();
            var userFx = rp.Get(" where Id=@id", new { id = fxUserId })?.FirstOrDefault();
            if (userFx == null)
            {
                Log.WriteLine($"查不到用户数据{fxUserId}");
                return;
            }
            new SiteContext(userFx);

            var fileName = $"1688数据统计-{DateTime.Now.ToString("yyyyMMdd-HHmm")}.xls";
            IWorkbook workbook = FxBuildExccelService.Stat1688V2BuildExcel(statList, stat1688FxUserIdList, poRelationList, returnList, fileName);

            FileStream fs = null;
            try
            {
                if (string.IsNullOrEmpty(fileName))
                    fileName = $"{Guid.NewGuid().ToString()}.xls";
                string filePath = $@"{ExportExcelDirectory + fileName}";

                fs = new FileStream(filePath, FileMode.OpenOrCreate, FileAccess.ReadWrite);
                workbook.Write(fs);
            }
            catch (Exception ex)
            {
                Log.WriteError($"保存Excel文件异常：{ex}");
            }
            finally
            {
                if (fs != null)
                {
                    fs.Dispose();
                    fs.Close();
                }
                if (workbook != null)
                    workbook = null;
            }

        }
        /// <summary>
        /// 已生成EXCEL的文件目录
        /// </summary>
        private string ExportExcelDirectory
        {
            get
            {
                if (string.IsNullOrEmpty(AppDomain.CurrentDomain.SetupInformation.PrivateBinPath))
                {
                    var path = AppDomain.CurrentDomain.BaseDirectory + "ExportFiles\\";
                    if (!Directory.Exists(path))
                        Directory.CreateDirectory(path);
                    return path;
                }
                else
                {
                    var path = AppDomain.CurrentDomain.BaseDirectory + "ExportFiles\\";
                    if (!Directory.Exists(path))
                        Directory.CreateDirectory(path);
                    return path;
                }
            }
        }



        /// <summary>
        /// 统计1688预付单使用用户数量（默认7天内）
        /// </summary>
        /// <param name="model"></param>
        public void Stat1688UserCount(QueryStatCategoryModel model)
        {
            //开始统计
            if (model == null)
                return;

            if (model.StartDate == null)
            {
                model.StartDate = DateTime.Now.AddDays(-7);
            }


            //当前云所有业务库
            var curCloudPlatformType = CustomerConfig.CloudPlatformType;
            var dbs = new DbConfigRepository().GetAllDbConfigsByCloudPlatform();
            var applicationNames = new List<string> { "fx", "fx_new" };
            //京东云
            if (curCloudPlatformType == CloudPlatformType.Jingdong.ToString())
                dbs = dbs.Where(a => a.DbNameConfig != null && string.IsNullOrEmpty(a.DbNameConfig.ApplicationName) == false && a.DbServer.Location == CloudPlatformType.Jingdong.ToString()).ToList();
            else
                //只取分单库
                dbs = dbs.Where(a => a.DbNameConfig != null && applicationNames.Contains(a.DbNameConfig.ApplicationName)).ToList();

            var dbGroup = dbs.Where(a => string.IsNullOrEmpty(a.ConnectionString) == false).GroupBy(g => g.ConnectionString).ToList();
            Log.WriteLine($"相关库：{dbGroup.Count()}个，TaskCode={model.TaskCode}，model={model.ToJson()}开始并发查询");
            var seqNumber = 0;

            var maxThreadNumForDb = model.MaxThreadNumForDb;
            if (maxThreadNumForDb <= 0)
                maxThreadNumForDb = 5;

            //有下过采购单的用户数量
            var supplierFxUserIds = new ConcurrentBag<int>();
            var agentFxUserIds = new ConcurrentBag<int>();
            var sourceLogicOrderIds = new ConcurrentBag<string>();
            //源商家店铺数
            var sourceShopIds = new ConcurrentBag<int>();
            //收单店铺数
            var supplierShopIds = new ConcurrentBag<int>();

            //有下过采购单且付款的用户数量
            var supplierPayedFxUserIds = new ConcurrentBag<int>();
            var agentPayedFxUserIds = new ConcurrentBag<int>();
            var sourcePayedLogicOrderIds = new ConcurrentBag<string>();

            //下单失败
            var sourceFailLogicOrderIds = new ConcurrentBag<string>();
            //取消
            var sourceCancelLogicOrderIds = new ConcurrentBag<string>();
            //未付款
            var sourceWaitpayLogicOrderIds = new ConcurrentBag<string>();

            //分库多线程并发
            Parallel.ForEach(dbGroup, new ParallelOptions { MaxDegreeOfParallelism = maxThreadNumForDb }, dc =>
            {
                var curConnectionString = dc.First().ConnectionString;
                var curDb = DbUtility.GetConnection(curConnectionString);

                using (curDb)
                {
                    if (curDb.State == ConnectionState.Closed)
                    {
                        curDb.ConnectionString = curConnectionString;
                        curDb.Open();
                    }

                    seqNumber += 1;
                    var curSeqNumber = seqNumber;

                    Log.WriteLine($"第{curSeqNumber}个库【{curDb.Database}】，开始查询");

                    var sql = $"SELECT CreateFxUserId FROM [PurchaseOrderRelation] WITH(NOLOCK) WHERE [CreateTime] >@startDate AND [Status] =1 GROUP BY [CreateFxUserId]  ";
                    var curFxUserIds = curDb.Query<int>(sql, new { startDate = model.StartDate }).ToList();
                    curFxUserIds.ForEach(fxUserId =>
                    {
                        agentFxUserIds.Add(fxUserId);
                    });

                    sql = $"SELECT PurchaseOrderFxUserId FROM [PurchaseOrderRelation] WITH(NOLOCK) WHERE [CreateTime] >@startDate AND [Status] =1 GROUP BY [PurchaseOrderFxUserId]  ";
                    var curFxUserIds2 = curDb.Query<int>(sql, new { startDate = model.StartDate }).ToList();
                    curFxUserIds2.ForEach(fxUserId =>
                    {
                        supplierFxUserIds.Add(fxUserId);
                    });

                    //商家源店铺
                    sql = $"SELECT SourceShopId FROM [PurchaseOrderRelation] WITH(NOLOCK) WHERE [CreateTime] >@startDate AND [Status] =1 GROUP BY [SourceShopId]  ";
                    var curSourceShopIds = curDb.Query<int>(sql, new { startDate = model.StartDate }).ToList();
                    curSourceShopIds.ForEach(sid =>
                    {
                        sourceShopIds.Add(sid);
                    });


                    //厂家店铺
                    sql = $"SELECT PurchaseOrderShopId FROM [PurchaseOrderRelation] WITH(NOLOCK) WHERE [CreateTime] >@startDate AND [Status] =1 GROUP BY [PurchaseOrderShopId]  ";
                    var curPurchaseShopIds = curDb.Query<int>(sql, new { startDate = model.StartDate }).ToList();
                    curPurchaseShopIds.ForEach(sid =>
                    {
                        supplierShopIds.Add(sid);
                    });

                    //付款成功的采购单-商家数
                    sql = $"SELECT CreateFxUserId FROM [PurchaseOrderRelation] WITH(NOLOCK) WHERE [CreateTime] >@startDate AND [Status] =1 AND PurchaseOrderStatus IN('waitsellersend','waitbuyerreceiver','waitbuyerreceive','success')  GROUP BY [CreateFxUserId]  ";
                    var curFxUserIds3 = curDb.Query<int>(sql, new { startDate = model.StartDate }).ToList();
                    curFxUserIds3.ForEach(fxUserId =>
                    {
                        agentPayedFxUserIds.Add(fxUserId);
                    });



                    //付款成功的采购单-厂家数
                    sql = $"SELECT PurchaseOrderFxUserId FROM [PurchaseOrderRelation] WITH(NOLOCK) WHERE [CreateTime] >@startDate AND [Status] =1 AND PurchaseOrderStatus IN('waitsellersend','waitbuyerreceiver','waitbuyerreceive','success')  GROUP BY [PurchaseOrderFxUserId]  ";
                    var curFxUserIds4 = curDb.Query<int>(sql, new { startDate = model.StartDate }).ToList();
                    curFxUserIds4.ForEach(fxUserId =>
                    {
                        supplierPayedFxUserIds.Add(fxUserId);
                    });


                    sql = $"SELECT SourceLogicOrderId FROM [PurchaseOrderRelation] WITH(NOLOCK) WHERE [CreateTime] >@startDate AND [Status] =1  ";
                    var curLogicOrderIds = curDb.Query<string>(sql, new { startDate = model.StartDate }).ToList();
                    curLogicOrderIds.ForEach(loId =>
                    {
                        sourceLogicOrderIds.Add(loId);
                    });

                    sql = $"SELECT SourceLogicOrderId FROM [PurchaseOrderRelation] WITH(NOLOCK) WHERE [CreateTime] >@startDate AND [Status] =1 AND PurchaseOrderStatus IN('waitsellersend','waitbuyerreceiver','waitbuyerreceive','success')  ";
                    var curLogicOrderIds2 = curDb.Query<string>(sql, new { startDate = model.StartDate }).ToList();
                    curLogicOrderIds2.ForEach(loId =>
                    {
                        sourcePayedLogicOrderIds.Add(loId);
                    });

                    //下单失败
                    sql = $"SELECT SourceLogicOrderId FROM [PurchaseOrderRelation] WITH(NOLOCK) WHERE [CreateTime] >@startDate AND [Status] =-1  ";
                    var curLogicOrderIds3 = curDb.Query<string>(sql, new { startDate = model.StartDate }).ToList();
                    curLogicOrderIds3.ForEach(loId =>
                    {
                        sourceFailLogicOrderIds.Add(loId);
                    });

                    //取消
                    sql = $"SELECT SourceLogicOrderId FROM [PurchaseOrderRelation] WITH(NOLOCK) WHERE [CreateTime] >@startDate AND [Status] =1 AND PurchaseOrderStatus='cancel'  ";
                    var curLogicOrderIds4 = curDb.Query<string>(sql, new { startDate = model.StartDate }).ToList();
                    curLogicOrderIds4.ForEach(loId =>
                    {
                        sourceCancelLogicOrderIds.Add(loId);
                    });

                    //未付款
                    sql = $"SELECT SourceLogicOrderId FROM [PurchaseOrderRelation] WITH(NOLOCK) WHERE [CreateTime] >@startDate AND [Status] =1 AND PurchaseOrderStatus='waitpay'  ";
                    var curLogicOrderIds5 = curDb.Query<string>(sql, new { startDate = model.StartDate }).ToList();
                    curLogicOrderIds5.ForEach(loId =>
                    {
                        sourceWaitpayLogicOrderIds.Add(loId);
                    });

                    Log.WriteLine($"第{curSeqNumber}个库【{curDb.Database}】，完成查询");
                }

            });


            Log.WriteLine($"查询结果【{model.StartDate}】，去重后结果，厂家数：{supplierFxUserIds.Distinct().Count()}，商家数：{agentFxUserIds.Distinct().Count()}，订单数：{sourceLogicOrderIds.Distinct().Count()}；已付款厂家数：{supplierFxUserIds.Distinct().Count()}，已付款商家数：{agentFxUserIds.Distinct().Count()}，已付款订单数：{sourcePayedLogicOrderIds.Distinct().Count()}，商家店铺数：{sourceShopIds.Distinct().Count()}，厂家店铺数：{supplierShopIds.Distinct().Count()}，下单失败订单数：{sourceFailLogicOrderIds.Distinct().Count()}，下单后取消：{sourceCancelLogicOrderIds.Distinct().Count()}，未付款订单数：{sourceWaitpayLogicOrderIds.Distinct().Count()}");

        }

    }
}
