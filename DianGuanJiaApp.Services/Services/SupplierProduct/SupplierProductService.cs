using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Entity.BaseProduct;
using DianGuanJiaApp.Data.Entity.SupplierProduct;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.FxModel.Listing;
using DianGuanJiaApp.Data.FxModel.SupplierProduct;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Model.BaseProduct;
using DianGuanJiaApp.Data.Model.SupplierProduct;
using DianGuanJiaApp.Data.Model.Tools;
using DianGuanJiaApp.Data.Repository.BaseProduct;
using DianGuanJiaApp.Data.Repository.SupplierProduct;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Other;
using DianGuanJiaApp.Utility.SubAccount;
using OpenPlatformModel = DianGuanJiaApp.Data.Model.OpenPlatform;

namespace DianGuanJiaApp.Services.Services.SupplierProduct
{
    /// <summary>
    /// 货盘商品服务层
    /// </summary>
    public class SupplierProductService : SupplierProductBaseService<SupplierProductEntity>
    {
        private BaseProductRepository _baseproductRepository;
        private BaseProductSkuRepository _baseproductSkuRepository;
        private BaseProductImageRepository _baseProductImageRepository;
        private BaseOfSupplierSkuRelationRepository _baseOfSupplierSkuRelationRepository;
        private BaseProductSkuAttributeRepository _baseProductSkuAttributeRepository;
        private PtProductInfoService _ptProductInfoService;
        private readonly SupplierProductRepository _repository;
        private readonly SupplierProductSkuRepository _supplierProductSkuRepository;
        private readonly SharePathFlowRepository _sharePathFlowRepository;
        private readonly SharePathFlowNodeRepository _sharePathFlowNodeRepository;
        private readonly BusinessCardService _businessCardService;
        private readonly SupplierProductCateRelationRepository _supplierProductCateRelationRepository;
        private readonly UserBrandRepository _userBrandRepository;
        private readonly UserSupplierStatusRepository _userSupplierStatusRepository;
        private readonly SupProductWaybillRelationRepository _supProductWaybillRelationRepository;
        private readonly UserFxService _userFxService = new UserFxService();
        public const int BATCH_SIZE = 500;//每批处理数量 

        /// <summary>
        /// 获取默认的数据库连接
        /// </summary>
        public SupplierProductService()
        {
            _repository = new SupplierProductRepository();
            _baseRepository = _repository;
            _sharePathFlowRepository = new SharePathFlowRepository();
            _sharePathFlowNodeRepository = new SharePathFlowNodeRepository();
            _supplierProductSkuRepository = new SupplierProductSkuRepository();
            _businessCardService = new BusinessCardService();
            _supplierProductCateRelationRepository = new SupplierProductCateRelationRepository();
            _userBrandRepository = new UserBrandRepository();
            _userSupplierStatusRepository = new UserSupplierStatusRepository();
            _supProductWaybillRelationRepository = new SupProductWaybillRelationRepository();
        }

        /// <summary>
        /// 根据连接字符串和数据库类型获取数据库连接
        /// </summary>
        /// <param name="connectionString"></param>
        /// <param name="isUseMySql"></param>
        public SupplierProductService(string connectionString, bool isUseMySql)
        {
            _repository = new SupplierProductRepository(connectionString, isUseMySql);
            _sharePathFlowRepository = new SharePathFlowRepository(connectionString,isUseMySql);
            _sharePathFlowNodeRepository = new SharePathFlowNodeRepository(connectionString,isUseMySql);
            _supplierProductSkuRepository = new SupplierProductSkuRepository(connectionString, isUseMySql);
            _supplierProductCateRelationRepository = new SupplierProductCateRelationRepository(connectionString, isUseMySql);
            _businessCardService = new BusinessCardService();
            _userBrandRepository = new UserBrandRepository(connectionString, isUseMySql);
            _userSupplierStatusRepository = new UserSupplierStatusRepository(connectionString, isUseMySql);
            _supProductWaybillRelationRepository = new SupProductWaybillRelationRepository(connectionString, isUseMySql);
            _baseRepository = _repository;
        }

        /// <summary>
        /// 生成货盘商品
        /// </summary>
        /// <param name="uidList"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public int GenerateSupplierProduct(List<long> uidList, int curFxUserId)
        {
            _baseproductRepository = new BaseProductRepository();
            _baseproductSkuRepository = new BaseProductSkuRepository();
            _baseProductImageRepository = new BaseProductImageRepository();
            _baseOfSupplierSkuRelationRepository = new BaseOfSupplierSkuRelationRepository();
            _baseProductSkuAttributeRepository = new BaseProductSkuAttributeRepository();

            // 根据基础商品uid获取基础商品信息并过滤掉已发布的商品
            var baseProductList = _baseproductRepository.GetByUidsAndFxUserId(uidList, curFxUserId)
                .Where(x => x.SupplierProductCount == 0 && x.Status == 1).ToList();
            if (baseProductList.Count == 0) throw new LogicException("该商品已上架小站");

            // 生成货盘商品
            var isSuccess = ConvertToSupplierProduct(baseProductList);

            return isSuccess;
        }


        /// <summary>
        /// 手工创建货盘商品
        /// </summary>
        /// <param name="model"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public int AddSupplierProduct(SupplierProductAddModel model, int curFxUserId)
        {
            _baseproductRepository = new BaseProductRepository();
            _baseproductSkuRepository = new BaseProductSkuRepository();
            _baseProductImageRepository = new BaseProductImageRepository();
            _baseOfSupplierSkuRelationRepository = new BaseOfSupplierSkuRelationRepository();
            _baseProductSkuAttributeRepository = new BaseProductSkuAttributeRepository();

            var baseProductSkuAddModel = ModelMap(model);
            // 先生成基础商品
            var baseProductEntity = new BaseProductSkuService().CreateBaseProductSku(baseProductSkuAddModel, curFxUserId);
            var isSuccess = baseProductEntity != null;
            if (!isSuccess)
            {
                Log.WriteError("手工创建货盘商品失败: 生成基础商品失败");
                throw new LogicException("创建货盘商品失败，请稍会儿再试");
            }

            // 根据SpuCode获取基础商品
            var baseProduct = _baseproductRepository.GetList(new List<string> { baseProductSkuAddModel.SpuCode })
                .FirstOrDefault(x => x.FxUserId == curFxUserId || x.Status == 1);
            if (baseProduct == null)
            {
                Log.WriteError("手工创建货盘商品失败: 生成基础商品失败");
                throw new LogicException("创建货盘商品失败，请稍会儿再试");
            }

            // 生成货盘商品
            var result = ConvertToSupplierProduct(new List<BaseProductEntity> { baseProduct }, model);

            return result;
        }

        /// <summary>
        /// 将基础商品转换为货盘商品Step1
        /// </summary>
        /// <param name="baseProducts"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public int ConvertToSupplierProduct(List<BaseProductEntity> baseProducts, SupplierProductAddModel model = null)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;

            // 获取基础商品uid
            var uidList = baseProducts.Select(x => x.Uid).ToList();
            // 根据基础商品uid获取基础商品sku信息
            var baseProductSkuList = _baseproductSkuRepository.GetSkuByProductUids(uidList, curFxUserId);
            var baseProductImageList = _baseProductImageRepository.GetListByProductUid(uidList);
            var baseProductSkuAttributes = _baseProductSkuAttributeRepository.GetListBySkuUids(uidList);
            // 过滤掉已删除的图片
            baseProductImageList = baseProductImageList.Where(x => x.Status == 1).ToList();

            // 生成UidList
            var allCount = baseProducts.Count + baseProductSkuList.Count;
            var uniqueIdList = new ProductDbConfigRepository().BaseProductSystemUniqueId("", curFxUserId, allCount);

            // 生成货盘商品
            var supplierProductList = MapToSupplierProductList(baseProducts, baseProductSkuList, baseProductSkuAttributes, baseProductImageList, uniqueIdList, model);

            if (supplierProductList.Any())
            {
                // 获取所有的Sku
                var newSkuList = supplierProductList.SelectMany(x => x.Skus).ToList();
                // 更新和创建实体
                _supplierProductSkuRepository.BatchAdd(newSkuList);
                _repository.BatchAdd(supplierProductList);
                _baseproductRepository.BulkUpdateByParam(baseProducts, new List<string>{ "SupplierProductCount", "UpdateTime", "SharePathCode"});
                _baseproductSkuRepository.BulkUpdateByParam(baseProductSkuList, new List<string>{ "SupplierProductCount", "UpdateTime", "SharePathCode"});

                return supplierProductList.Count;
            }

            return 0;
        }

        /// <summary>
        /// 将基础商品转换为货盘商品Step2
        /// </summary>
        /// <param name="baseProducts"></param>
        /// <param name="baseProductSkuList"></param>
        /// <param name="baseProductSkuAttributes"></param>
        /// <param name="baseProductImageList"></param>
        /// <param name="uniqueIdList"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        public List<SupplierProductEntity> MapToSupplierProductList(List<BaseProductEntity> baseProducts, List<BaseProductSku> baseProductSkuList,
            List<BaseProductSkuAttribute> baseProductSkuAttributes, List<BaseProductImage> baseProductImageList,
            List<string> uniqueIdList, SupplierProductAddModel model = null)
        {
            // 定义变量
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            var supplierProductList = new List<SupplierProductEntity>();
            var baseProductOfSupSkuRelationList = new List<BaseOfSupplierProductSkuRelation>();
            var cateRelationList = new List<SupplierProductCateRelation>();
            var expressRelationList = new List<SupProductWaybillRelation>();
            ShipmentsInfo shipmentsInfo;
            AfterSalesInfo afterSalesInfo;

            // 基础库
            var baseSharePathFlowRepository = new SharePathFlowRepository(fxUserId);
            var baseSharePathFlowNodeRepository = new SharePathFlowNodeRepository(fxUserId);

            // 获取当前用户的货盘已存在的SpuCode
            var spuCodeList = _repository.GetExistSpuCodeList(fxUserId);

            // 获取SourceFxUserId为自己，无上下游的路径流，若为空，则生成一个
            var sharePathFlow = _sharePathFlowRepository.GetOneBySourceFxUserId(fxUserId);
            if (sharePathFlow == null)
            {
                sharePathFlow = new SharePathFlow
                {
                    SourceFxUserId = fxUserId,
                    CreateBy = fxUserId,
                    UpdateBy = fxUserId,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    Status = 1
                };

                var sharePathFlowNode = new SharePathFlowNode
                {
                    FxUserId = fxUserId,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now,
                    Status = 1
                };
                sharePathFlow.PathFlowNodes = new List<SharePathFlowNode> { sharePathFlowNode };
                sharePathFlow.SharePathCode = SharePathFlowService.CreateSharePathCode(sharePathFlow);
                sharePathFlowNode.SharePathCode = sharePathFlow.SharePathCode;
                sharePathFlowNode.SharePathNodeCode = (sharePathFlowNode.SharePathCode + fxUserId).ToShortMd5();

                _sharePathFlowRepository.Add(sharePathFlow);
                _sharePathFlowNodeRepository.Add(sharePathFlowNode);

                // 同步基础库副本
                baseSharePathFlowRepository.Add(sharePathFlow);
                baseSharePathFlowNodeRepository.Add(sharePathFlowNode);
            }

            // 判断基础库副本是否存在，若不存在则新增
            try
            {
                var sharePathDuplicate =  baseSharePathFlowRepository.GetOneBySourceFxUserId(fxUserId);
                if (sharePathDuplicate == null)
                {
                    baseSharePathFlowRepository.Add(sharePathFlow);
                    var sharePathFlowNode =
                        _sharePathFlowNodeRepository.GetBySharePathFlowCodes(new List<string>
                            { sharePathFlow.SharePathCode }).FirstOrDefault();
                    if(sharePathFlowNode != null) baseSharePathFlowNodeRepository.Add(sharePathFlowNode);
                }
            }
            catch (Exception e)
            {
                Log.WriteError($"基础库副本生成失败: {e.Message}");
            }

            if (model == null)
            {
                // 获取我的小站的发货信息和售后信息
                shipmentsInfo = _businessCardService.GetShipmentsInfo(fxUserId);
                afterSalesInfo = _businessCardService.GetAfterSalesInfo(fxUserId);
            }
            else
            {
                // 使用model的信息
                shipmentsInfo = model.ShipmentsInfo;
                afterSalesInfo = model.AfterSalesInfo;
            }

            // uid计数
            var count = 0;
            foreach (var product in baseProducts)
            {
                var supplierProductUid = (3 + uniqueIdList[count++]).ToLong();
                var supplierProductSkuList = new List<SupplierProductSku>();
                var dtNow = DateTime.Now;

                // 这里要判断该SpuCode是否已存在，如果存在则随机生成
                var spuCode = product.SpuCode;
                if (spuCodeList.Contains(product.SpuCode)) spuCode = Guid.NewGuid().ToString("N").ToShortMd5();

                // 拿到该商品的图片，并组合生成
                var imageList = baseProductImageList.Where(x => x.ProductUid == product.Uid).ToList();
                var mainImageJson = imageList.Select(x => new
                {
                    x.ImageObjectId,
                    x.FullUrl,
                    IsMain = product.MainImageObjectId == x.ImageObjectId
                }).ToJson();

                // 如果product的SharePathCode为空，则使用当前的SharePathCode
                if (string.IsNullOrEmpty(product.SharePathCode)) product.SharePathCode = sharePathFlow.SharePathCode;

                // 处理地址信息并保存到SupplierAddress表
                string addressCode = null;
                if (shipmentsInfo?.SendaddressList != null && shipmentsInfo.SendaddressList.Any())
                {
                    try
                    {
                        var addressService = new SupplierAddressService();

                        // 优先使用单个地址模式（取第一个地址作为主要发货地址）
                        var primaryAddress = shipmentsInfo.SendaddressList.First();
                        var addressResult = addressService.SaveProductAddresses(supplierProductUid, null, primaryAddress, fxUserId);

                        if (addressResult.Success && !string.IsNullOrEmpty(addressResult.Data))
                        {
                            addressCode = addressResult.Data;
                            Log.Debug(() => $"商品主要发货地址保存成功: ProductUid={supplierProductUid}, AddressCode={addressCode}", LogModuleTypeEnum.DistributionProduct);
                        }

                        // 如果有多个地址，记录日志但只使用第一个作为主地址
                        if (shipmentsInfo.SendaddressList.Count > 1)
                        {
                            Log.Debug(() => $"商品有{shipmentsInfo.SendaddressList.Count}个地址，已选择第一个作为主要发货地址: ProductUid={supplierProductUid}", LogModuleTypeEnum.DistributionProduct);
                        }
                    }
                    catch (Exception ex)
                    {
                        // 地址保存失败不影响商品创建，继续执行
                        Log.WriteError($"保存商品地址信息失败: {ex.Message}", LogModuleTypeEnum.DistributionProduct);
                    }
                }

                // 生成SupplierProduct
                var supplierProduct = new SupplierProductEntity
                {
                    Uid = supplierProductUid,
                    SpuCode = spuCode,
                    Subject = product.Subject,
                    FromProductUid = product.Uid,
                    FxUserId = product.FxUserId,
                    CreateFxUserId = product.CreateFxUserId,
                    Description = product.Description ?? string.Empty,
                    ShortTitle = product.ShortTitle,
                    MainImageJson = mainImageJson,
                    AttributeNames = product.AttributeNames,
                    Status = 1,
                    IsPublic = true,
                    ShipmentsInfo = shipmentsInfo.ToJson(),
                    AfterSalesInfo = afterSalesInfo.ToJson(),
                    PathNodeDeep = product.PathNodeDeep,
                    SharePathCode = product.SharePathCode,
                    RootNodeFxUserId = product.RootNodeFxUserId ?? fxUserId,
                    ExpressBill = ExpressBill.ConvertNameToStr(shipmentsInfo.ExpressBill),
                    PublicTime = dtNow,
                    CreateTime = dtNow,
                    UpdateTime = dtNow,
                    SkuModeType = product.SkuModeType,
                    AddressCode = addressCode, // 设置地址关联Code
                };

                foreach (var sku in baseProductSkuList)
                {
                    if (sku.BaseProductUid != product.Uid) continue;

                    // 如果sku的SharePathCode为空，则使用当前的SharePathCode
                    if (string.IsNullOrEmpty(sku.SharePathCode)) sku.SharePathCode = sharePathFlow.SharePathCode;

                    var attrList = baseProductSkuAttributes.Where(x => x.SkuUid == sku.Uid).ToList();
                    var attrJson = string.Empty;
                    attrList.ForEach(x =>
                    {
                        attrJson = new
                        {
                            k = x.AttributeName1,
                            v = x.AttributeValue1
                        }.ToJson();

                        if (!string.IsNullOrEmpty(x.AttributeName2))
                        {
                            attrJson += "," + new
                            {
                                k = x.AttributeName2,
                                v = x.AttributeValue2
                            }.ToJson();
                        }
                        if (!string.IsNullOrEmpty(x.AttributeName3))
                        {
                            attrJson += "," + new
                            {
                                k = x.AttributeName3,
                                v = x.AttributeValue3
                            }.ToJson();
                        }
                    });
                    // 将JSON组成数组
                    attrJson = $"[{attrJson}]";

                    // 生成SupplierProductSku
                    var supplierProductSku = new SupplierProductSku
                    {
                        Uid = (4 + uniqueIdList[count++]).ToLong(),
                        SupplierProductUid = supplierProductUid,
                        SkuCode = sku.SkuCode,
                        ImageUrl = sku.ImageUrl,
                        FxUserId = fxUserId,
                        Subject = supplierProduct.Subject,
                        ShortTitle = supplierProduct.ShortTitle,
                        DistributePrice = sku.DistributePrice,
                        Weight = sku.Weight,
                        IsPublic = true,
                        Attributes = attrJson,
                        FromProductUid = product.Uid,
                        FromProductSkuUid = sku.Uid,
                        PathNodeDeep = sku.PathNodeDeep,
                        SharePathCode = sku.SharePathCode,
                        RootNodeFxUserId = sku.RootNodeFxUserId ?? fxUserId,
                        CreateTime = dtNow,
                        UpdateTime = dtNow,
                        Status = 1,
                        AttributeValue = sku.AttributeValue
                    };
                    // 需要生成关联关系
                    var relation = new BaseOfSupplierProductSkuRelation()
                    {
                        BaseProductUid = product.Uid,
                        BaseProductSkuUid = sku.Uid,
                        SupplierProductUid = supplierProductUid,
                        SupplierProductSkuUid = supplierProductSku.Uid,
                        FxUserId = fxUserId,
                        SkuCode = supplierProductSku.SkuCode,
                        Attributes = supplierProductSku.Attributes,
                        ImageUrl = supplierProductSku.ImageUrl,
                        Subject = supplierProductSku.Subject,
                        ShortTitle = supplierProductSku.ShortTitle,
                        DistributePrice = supplierProductSku.DistributePrice,
                        Weight = supplierProductSku.Weight,
                        Status = 1
                    };

                    baseProductOfSupSkuRelationList.Add(relation);
                    sku.SupplierProductCount++;
                    sku.UpdateTime = dtNow;
                    supplierProductSkuList.Add(supplierProductSku);
                }

                // 找到分销价最大值和最小值
                var maxPrice = supplierProductSkuList.Where(x => x.SupplierProductUid == supplierProductUid).Max(x => x.DistributePrice);
                var minPrice = supplierProductSkuList.Where(x => x.SupplierProductUid == supplierProductUid).Min(x => x.DistributePrice);

                if (model != null)
                {
                    // 生成类目信息和属性
                    var categoryInfoList = model.CategoryInfoList;

                    foreach (var categoryInfo in categoryInfoList)
                    {
                        cateRelationList.Add(new SupplierProductCateRelation
                        {
                            CateId = categoryInfo.CateId,
                            ParentId = categoryInfo.ParentId,
                            Name = categoryInfo.Name,
                            Level = categoryInfo.Level,
                            SupplierProductUid = supplierProduct.Uid
                        });
                    }
                }
                else
                {
                    // 生成默认Cate数据
                    cateRelationList.Add(new SupplierProductCateRelation
                    {
                        SupplierProductUid = supplierProductUid,
                        CateId = "-1",
                        ParentId = "0",
                        Level = 1,
                        Name = "其他"
                    });
                }

                // 生成面单关联表
                var expressInfo = shipmentsInfo.ExpressBill;
                expressInfo?.ForEach(x =>
                {
                    expressRelationList.Add(new SupProductWaybillRelation
                    {
                        WaybillId = x.Id,
                        WaybillName = x.ExpressBillName,
                        WaybillType = x.ExpressBillType,
                        FxUserId = fxUserId,
                        SupplierProductUid = supplierProductUid,
                    });
                });

                if (model != null) supplierProduct.CategoryAttributes = model.CategoryAttribute;
                supplierProduct.MaxPrice = maxPrice;
                supplierProduct.MinPrice = minPrice;
                product.SupplierProductCount++;
                product.UpdateTime = dtNow;
                supplierProduct.Skus = supplierProductSkuList;
                supplierProductList.Add(supplierProduct);
            }

            _supplierProductCateRelationRepository.BatchAdd(cateRelationList);
            _baseOfSupplierSkuRelationRepository.BulkInsert(baseProductOfSupSkuRelationList);
            if (expressRelationList.Any()) _supProductWaybillRelationRepository.BatchAdd(expressRelationList);
            return supplierProductList;
        }

        /// <summary>
        /// 将SupplierProductAddModel转换为BaseProductSkuAddModel
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public BaseProductSkuAddModel ModelMap(SupplierProductAddModel model)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var supplierProductAddSkuModels = model.ProductSkus;
            var skuCodes = supplierProductAddSkuModels.Select(x => x.SkuCode).ToList();
            var baseProductSkuModels = new List<BaseProductSkuModel>();

            // 获取填写的sku已存在商品库中的skuCode
            var existCodeList = _baseproductSkuRepository.GetBaseProductSkuByCode(skuCodes, true, curFxUserId);

            foreach (var sku in supplierProductAddSkuModels)
            {
                var baseProductSkuModel = new BaseProductSkuModel
                {
                    // 如果存在则随机生成
                    SkuCode = existCodeList.Contains(sku.SkuCode) ? Guid.NewGuid().ToString("N").ToShortMd5() : sku.SkuCode,
                    ImageUrlStr = sku.ImageUrlStr,
                    Attribute = sku.Attribute,
                    DistributePrice = sku.DistributePrice,
                    Weight = sku.Weight,
                    AttributeValue = sku.AttributeValue,
                };

                baseProductSkuModels.Add(baseProductSkuModel);
            }

            return new BaseProductSkuAddModel
            {
                SpuCode =  Guid.NewGuid().ToString("N").ToShortMd5(),
                Subject = model.Subject,
                ShortTitle = model.ShortTitle,
                ProductImagesStr = model.ProductImagesStr,
                DescriptionStr = model.DescriptionStr,
                Description = model.Description,
                ProductImages = model.ProductImages,
                ProductSkus = baseProductSkuModels,
                SkuModeType = model.SkuModeType,
            };
        }

        /// <summary>
        /// 编辑更新货盘商品
        /// </summary>
        /// <param name="model"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public bool UpdateSupplierProduct(SupplierProductUpdateModel model, int curFxUserId)
        {
            _baseOfSupplierSkuRelationRepository = new BaseOfSupplierSkuRelationRepository();

            // 获取货盘商品
            var supplierProduct = _repository.GetByUid(model.SupplierProductUid);
            if (supplierProduct == null || supplierProduct.FxUserId != curFxUserId) throw new LogicException("货盘商品不存在");

            // 定义变量
            var updateSku = new List<SupplierProductSku>();

            // 获取旧的类目信息
            var cateRelationDict = _supplierProductCateRelationRepository.GetBySupUid(supplierProduct.Uid)
                .ToDictionary(x => x.CateId);
            var newCateRelationList = new List<SupplierProductCateRelation>();

            // 获取旧的面单关联信息
            var waybillInfoDict = _supProductWaybillRelationRepository.GetBySupUid(supplierProduct.Uid)
                .ToDictionary(x => x.WaybillId);
            var newWaybillInfoList = new List<SupProductWaybillRelation>();

            // 更新类目信息
            foreach (var categoryInfo in model.CategoryInfoList)
            {
                if (cateRelationDict.TryGetValue(categoryInfo.CateId, out var cateRelation))
                {
                    cateRelation.Name = categoryInfo.Name;
                    cateRelation.ParentId = categoryInfo.ParentId;
                    cateRelation.Level = categoryInfo.Level;
                    cateRelation.IsChange = true;
                }
                else
                {
                    newCateRelationList.Add(new SupplierProductCateRelation
                    {
                        CateId = categoryInfo.CateId,
                        ParentId = categoryInfo.ParentId,
                        Name = categoryInfo.Name,
                        Level = categoryInfo.Level,
                        SupplierProductUid = supplierProduct.Uid,
                    });
                }
            }

            // 更新面单关联
            foreach (var waybillInfo in model.ShipmentsInfo.ExpressBill)
            {
                if (waybillInfoDict.TryGetValue(waybillInfo.Id, out var waybillRelation))
                {
                    waybillRelation.WaybillName = waybillInfo.ExpressBillName;
                    waybillRelation.WaybillType = waybillInfo.ExpressBillType;
                    waybillRelation.IsChange = true;
                }
                else
                {
                    newWaybillInfoList.Add(new SupProductWaybillRelation
                    {
                        WaybillId = waybillInfo.Id,
                        WaybillName = waybillInfo.ExpressBillName,
                        WaybillType = waybillInfo.ExpressBillType,
                        FxUserId = curFxUserId,
                        SupplierProductUid = supplierProduct.Uid,
                    });
                }
            }

            // 获取需要删除的类目信息
            foreach (var cateRelation in cateRelationDict.Values)
            {
                if (!cateRelation.IsChange)
                {
                    cateRelation.Status = 0;
                }
            }
            var updateCateRelationList = cateRelationDict.Values.ToList();

            // 获取需要删除的面单信息
            foreach (var waybillRelation in waybillInfoDict.Values)
            {
                if (!waybillRelation.IsChange)
                {
                    waybillRelation.Status = 0;
                }
            }
            var updateWaybillRelationList = waybillInfoDict.Values.ToList();

            // 获取Sku以及关联信息
            var supplierProductSkuList = _supplierProductSkuRepository.GetListByProductUid(supplierProduct.Uid, curFxUserId);
            var baseOfSupplierSkuRelationList = _baseOfSupplierSkuRelationRepository.GetListBySupProductUid(supplierProduct.Uid);

            model.DescriptionStr = model.DescriptionStr?.Select(ImgHelper.GetRealPath).ToList();

            // 处理地址信息更新
            var addressCode = supplierProduct.AddressCode; // 保持原有的AddressCode
            if (model.ShipmentsInfo?.SendaddressList != null && model.ShipmentsInfo.SendaddressList.Any())
            {
                try
                {
                    var addressService = new SupplierAddressService();

                    // 使用单个地址模式更新（取第一个地址作为主要发货地址）
                    var primaryAddress = model.ShipmentsInfo.SendaddressList.First();
                    var addressResult = addressService.SaveProductAddresses(supplierProduct.Uid, supplierProduct.AddressCode, primaryAddress, curFxUserId);

                    if (addressResult.Success && !string.IsNullOrEmpty(addressResult.Data))
                    {
                        addressCode = addressResult.Data;
                        Log.Debug(() => $"商品地址更新成功: ProductUid={supplierProduct.Uid}, AddressCode={addressCode}", LogModuleTypeEnum.DistributionProduct);
                    }

                    // 如果有多个地址，记录日志但只使用第一个作为主地址
                    if (model.ShipmentsInfo.SendaddressList.Count > 1)
                    {
                        Log.Debug(() => $"商品更新时有{model.ShipmentsInfo.SendaddressList.Count}个地址，已选择第一个作为主要发货地址: ProductUid={supplierProduct.Uid}", LogModuleTypeEnum.DistributionProduct);
                    }
                }
                catch (Exception ex)
                {
                    // 地址更新失败不影响商品更新
                    Log.WriteError($"更新商品地址信息失败: {ex.Message}", LogModuleTypeEnum.DistributionProduct);
                }
            }

            // 更新货盘商品
            supplierProduct.Subject = model.Subject;
            supplierProduct.ShortTitle = model.ShortTitle;
            supplierProduct.Description = model.DescriptionStr == null ? string.Empty : string.Join(",", model.DescriptionStr);
            supplierProduct.MainImageJson = model.ProductImages.Select(x => new
            {
                x.ImageObjectId,
                FullUrl = ImgHelper.GetRealPath(x.ImageUrl),
                x.IsMain
            }).OrderByDescending(x => x.IsMain).ToJson();
            supplierProduct.ShipmentsInfo = model.ShipmentsInfo.ToJson();
            supplierProduct.AfterSalesInfo = model.AfterSalesInfo.ToJson();
            supplierProduct.MaxPrice = model.ProductSkus.Max(x => x.DistributePrice ?? 0);
            supplierProduct.MinPrice = model.ProductSkus.Min(x => x.DistributePrice ?? 0);
            supplierProduct.ExpressBill = ExpressBill.ConvertNameToStr(model.ShipmentsInfo?.ExpressBill);
            supplierProduct.CategoryAttributes = model.CategoryAttribute;
            supplierProduct.UpdateTime = DateTime.Now;
            supplierProduct.AddressCode = addressCode; // 更新地址关联Code

            supplierProduct.SkuModeType = model.SkuModeType;

            // 更新Sku
            foreach (var sku in model.ProductSkus)
            {
                var supplierProductSku = supplierProductSkuList.FirstOrDefault(x => x.Uid == sku.SkuUid);
                if (supplierProductSku == null) continue;

                var index = model.ProductSkus.IndexOf(sku) + 1;
                supplierProductSku.Subject = supplierProduct.Subject;
                supplierProductSku.ShortTitle = sku.ShortTitle;
                supplierProductSku.DistributePrice = sku.DistributePrice;
                supplierProductSku.ImageUrl = ImgHelper.GetRealPath(sku.ImageUrlStr);
                supplierProductSku.Weight = sku.Weight ?? 0;
                supplierProductSku.Attributes = BaseProductSkuAttributeModel.FromClass(sku.Attribute);
                supplierProductSku.UpdateTime = DateTime.Now.AddMilliseconds(index * 1000);
                supplierProductSku.AttributeValue = sku.AttributeValue;

                updateSku.Add(supplierProductSku);

                // 处理关联信息
                var findRelation =
                    baseOfSupplierSkuRelationList.FirstOrDefault(x =>
                        x.SupplierProductSkuUid == supplierProductSku.Uid);
                if (findRelation == null) continue;

                findRelation.SkuCode = supplierProductSku.SkuCode;
                findRelation.Attributes = supplierProductSku.Attributes;
                findRelation.DistributePrice = supplierProductSku.DistributePrice;
            }

            // 更新数据库
            try
            {
                _repository.Update(supplierProduct);
                _supplierProductSkuRepository.BulkUpdate(updateSku);
                _supplierProductCateRelationRepository.BulkUpdate(updateCateRelationList);
                _supProductWaybillRelationRepository.BulkUpdate(updateWaybillRelationList);
                if (newCateRelationList.Any()) _supplierProductCateRelationRepository.BatchAdd(newCateRelationList);
                if (newWaybillInfoList.Any()) _supProductWaybillRelationRepository.BatchAdd(newWaybillInfoList);
                _baseOfSupplierSkuRelationRepository.BulkUpdateByParam(baseOfSupplierSkuRelationList,
                    new List<string> { "SkuCode", "Attributes", "DistributePrice" });
                return true;
            }
            catch (Exception e)
            {
                Log.WriteError($"编辑更新货盘商品失败: {e.Message}", LogModuleTypeEnum.DistributionProduct);
                return false;
            }
        }

        /// <summary>
        /// 获取商品的地址信息（兼容新旧数据格式）
        /// </summary>
        /// <param name="supplierProduct"></param>
        /// <returns></returns>
        public List<BusinessCardSendaddress> GetProductAddresses(SupplierProductEntity supplierProduct)
        {
            if (supplierProduct == null)
                return new List<BusinessCardSendaddress>();

            try
            {
                var addressService = new SupplierAddressService();

                // 优先从SupplierAddress表获取地址信息
                // 且类型不为空，即小站商品保留原逻辑，仅冗余一份数据
                if (!string.IsNullOrEmpty(supplierProduct.AddressCode) && supplierProduct.ProductType != null)
                {
                    var addresses = addressService.GetByProductUid(supplierProduct.Uid);
                    if (addresses.Any())
                    {
                        return addresses.Select(addr => new BusinessCardSendaddress
                        {
                            Province = addr.ProvinceName,
                            City = addr.CityName,
                            County = addr.DistrictName,
                            Street = addr.StreetName,
                            Address = addr.DetailAddress,
                            ReceiverName = addr.ContactName,
                            ReceiverContract = addr.ContactMobile,
                            ReceiverTel = addr.ContactPhone
                        }).ToList();
                    }
                }

                // 兼容历史数据，从ShipmentsInfo获取
                if (!string.IsNullOrEmpty(supplierProduct.ShipmentsInfo))
                {
                    var shipmentsInfo = supplierProduct.ShipmentsInfo.ToObject<ShipmentsInfo>();
                    if (shipmentsInfo?.SendaddressList != null && shipmentsInfo.SendaddressList.Any())
                    {
                        return shipmentsInfo.SendaddressList;
                    }
                }

                return new List<BusinessCardSendaddress>();
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取商品地址信息时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
                return new List<BusinessCardSendaddress>();
            }
        }

        /// <summary>
        /// 获取商品的地址信息（根据商品UID）
        /// </summary>
        /// <param name="supplierProductUid"></param>
        /// <returns></returns>
        public List<BusinessCardSendaddress> GetProductAddressesByUid(long supplierProductUid)
        {
            try
            {
                var supplierProduct = _repository.GetByUid(supplierProductUid);
                return GetProductAddresses(supplierProduct);
            }
            catch (Exception ex)
            {
                Log.WriteError($"根据商品UID获取地址信息时发生错误：{ex}", LogModuleTypeEnum.DistributionProduct);
                return new List<BusinessCardSendaddress>();
            }
        }

        /// <summary>
        /// 复制货盘商品到商品库
        /// </summary>
        /// <param name="models"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public int CopySupplierProduct(List<CopySupplierProductModel> models, int curFxUserId)
        {
            // 不能超过500条
            if (models.Count > BATCH_SIZE) throw new LogicException("最多只能复制500条商品");
            _baseproductSkuRepository = new BaseProductSkuRepository(curFxUserId);
            _ptProductInfoService = new PtProductInfoService();

            // 获取货盘商品
            var uids = models.Select(x => x.SupplierProductUid).ToList();
            if (uids.Any() == false) throw new LogicException("请选择要复制的商品");
            var supProductList = _repository.GetByUids(uids);
            if (supProductList.Any() == false) throw new LogicException("没有找到要复制的商品");
            // 避免循环引用
            supProductList = supProductList.Where(x => x.RootNodeFxUserId != curFxUserId).ToList();
            if (supProductList.Any() == false) throw new LogicException("不能复制自己的商品");

            // 校验商品所属用户与当前用户的关系
            var supplierFxUserIds = supProductList.Select(x => x.FxUserId).ToList();
            // 当前用户存在合作关系的厂家Id
            var userSupplierIds = _userSupplierStatusRepository
                .GetByFxUserIdAndStatus(curFxUserId, new List<int> { 1, 5, 6 }).Select(x => x.SupplierFxUserId)
                .ToList();
            // 不存在合作关系的厂家Id
            var notExistSupplierIds = supplierFxUserIds.Except(userSupplierIds).ToList();
            // 如果存在不合作的厂家Id，去除不存在合作关系的商品
            if (notExistSupplierIds.Any())
            {
                supProductList = supProductList.Where(x => notExistSupplierIds.Contains(x.FxUserId) == false).ToList();
                // 如果没有商品了，抛出异常
                if (supProductList.Any() == false) throw new LogicException("您与该商品的厂家还不是合作关系，请检查绑定关系！");
            }

            // 过滤已复制的商品
            uids = supProductList.Select(x => x.Uid).ToList();
            var existUids = new BaseProductRepository(curFxUserId).GetByFromUids(uids, curFxUserId);
            if (existUids.Any())
            {
                uids = uids.Except(existUids.Select(x => x.ToLong())).ToList();
                if (uids.Any() == false) throw new LogicException("不能选择已复制过的商品");
                supProductList = supProductList.Where(x => uids.Contains(x.Uid)).ToList();
            }

            // 获取货盘商品的Sku
            var skuList = _supplierProductSkuRepository.GetListByProductUids(uids);
            // 获取货盘商品所有的BaseProductUid
            var baseProductUidsDic = supProductList.ToDictionary(x => x.FromProductUid, x => x.Uid);
            // 获取对应的平台资料
            var ptProductInfos = _ptProductInfoService.GetListByBaseProductUids(baseProductUidsDic);
            // 对比SkuCode
            _ptProductInfoService.CompareSkuCode(ptProductInfos, baseProductUidsDic.Keys.ToList());
            // 基础商品添加模型构建
            var baseProductSkuAddModels = ModelMap(supProductList, skuList);

            // 生成基础商品
            var results = new ConcurrentBag<BaseProductEntity>();
            Parallel.ForEach(baseProductSkuAddModels, new ParallelOptions { MaxDegreeOfParallelism = 5 }, model =>
            {
                try
                {
                    var result = new BaseProductSkuService().CreateBaseProductSku(model, curFxUserId);
                    if (result != null) results.Add(result);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"由厂家货盘商品批量创建基础资料异常：{ex.Message}，参数：{model.ToJson()}", LogModuleTypeEnum.DistributionProduct);
                }
            });

            // 平台资料添加
            if (ptProductInfos.Any())
            {
                try
                {
                    var ptProductResults = _ptProductInfoService.ModelMapAndAdd(ptProductInfos, results.ToList());
                    Log.Debug($"由厂家货盘商品批量创建平台资料成功：{ptProductResults.Count(x => x)}条，失败：{ptProductResults.Count(x => x == false)}条");
                }
                catch (Exception e)
                {
                    Log.WriteError($"由厂家货盘商品批量创建平台资料异常：{e.Message}", LogModuleTypeEnum.DistributionProduct);
                }
            }

            return results.Count;
        }

        public List<BrandModel> GetBrand(int fxUserId, string name)
        {
            return _userBrandRepository.GetBrand(fxUserId, name);
        }

        public string SaveBrand(int currentFxUserId, string brandname)
        {
            return _userBrandRepository.SaveBrand(currentFxUserId, brandname);
        }

        /// <summary>
        /// 将SupplierProduct实体转为BaseProductSkuAddModel
        /// </summary>
        /// <param name="models"></param>
        /// <param name="skuModels"></param>
        /// <returns></returns>
        public List<BaseProductSkuAddModel> ModelMap(List<SupplierProductEntity> models, List<SupplierProductSku> skuModels)
        {
            var curFxUserId = SiteContext.Current.CurrentFxUserId;
            var result = new List<BaseProductSkuAddModel>();
            var skuCodeList = skuModels.Select(x => x.SkuCode).Distinct().ToList();
            var spuCodeList = models.Select(x => x.SpuCode).Distinct().ToList();

            // 查询用户商品库中已存在的skuCode
            var existSkuCodeList = _baseproductSkuRepository.GetBaseProductSkuByCode(skuCodeList, true, curFxUserId);
            var existSpuCodeList = _baseproductSkuRepository.GetBaseProductSkuByCode(spuCodeList, false, curFxUserId);

            // 获取货盘商品及Sku的分享路径流
            var codes = models.Select(x => x.SharePathCode).ToList();
            codes.AddRange(skuModels.Select(x => x.SharePathCode).ToList());
            codes = codes.Distinct().ToList();

            // 根据SharePathCode获取分享路径流
            var sharePathEntList = _sharePathFlowRepository.GetListByCodes(codes);
            var sharePathNodeEntList = _sharePathFlowNodeRepository.GetBySharePathFlowCodes(codes);

            var userSupplierIds = models.Select(x => x.FxUserId).ToList();
            // 查询厂家换算规则
            var supplierRuleDic = new MemberLevelService().GetSupplierRuleModel(userSupplierIds, curFxUserId);
            
            // 构建模型
            models.ForEach(ent =>
            {
                var skuEnt = skuModels.FindAll(x => x.SupplierProductUid == ent.Uid);
                var baseProductSkus = new List<BaseProductSkuModel>();
                var configModels = new List<BaseProductConfigModel>();

                // 获取厂家分享路径流
                var findSharePath = sharePathEntList.Find(x => x.SharePathCode == ent.SharePathCode);
                var findNode = sharePathNodeEntList.FindAll(x => x.SharePathCode == ent.SharePathCode);
                findSharePath.PathFlowNodes = findNode;

                // 生成自己的分享路径流
                var sharePathFlow = SharePathFlowService.GenerateSharePathFlow(findSharePath, ent.FxUserId, curFxUserId);
                var sharePathCode = sharePathFlow.SharePathCode;

                skuEnt.ForEach(sku =>
                {
                    // 获取Sku维度的分享路径流
                    var findSkuSharePath = sharePathEntList.Find(x => x.SharePathCode == sku.SharePathCode);
                    var findSkuNode = sharePathNodeEntList.FindAll(x => x.SharePathCode == sku.SharePathCode);
                    findSkuSharePath.PathFlowNodes = findSkuNode;

                    // 生成自己的分享路径流
                    var sharePathFlowSku = SharePathFlowService.GenerateSharePathFlow(findSkuSharePath, sku.FxUserId, curFxUserId);
                    var sharePathCodeSku = sharePathFlowSku.SharePathCode;

                    var baseSku = new BaseProductSkuModel
                    {
                        // 如果存在则随机生成
                        SkuCode = existSkuCodeList.Contains(sku.SkuCode)
                            ? Guid.NewGuid().ToString("N").ToShortMd5()
                            : sku.SkuCode,
                        ImageUrlStr = sku.ImageUrl,
                        Attribute = BaseProductUtils.FromJson(sku.Attributes),
                        DistributePrice = sku.DistributePrice,
                        RootNodeFxUserId = sku.RootNodeFxUserId,
                        SharePathCode = sharePathCodeSku,
                        Weight = sku.Weight,
                        PathNodeDeep = sku.PathNodeDeep + 1,
                        SharePathFlow = sharePathFlowSku,
                        UpFxUserId = sku.FxUserId,
                        UpSkuUid = sku.FromProductSkuUid.ToString()//基础规格Uid
                    };
                                  
                    // 等级分销价换算
                    if (supplierRuleDic.Any())
                    {
                        supplierRuleDic.TryGetValue(sku.FxUserId, out var rule);
                        if (rule != null)
                        {
                            var (newPrice, isChanged) = MemberLevelService.DistributePriceChange(sku.DistributePrice, rule.PriceRule,
                                rule.FinalDistributePriceCorrectRule);
                            if (isChanged) baseSku.DistributePrice = newPrice;
                        }
                    }
                    
                    configModels.Add(new BaseProductConfigModel
                    {
                        RefType = "Sku",
                        SupplierFxUserId = sku.FxUserId,
                        RefCode = baseSku.SkuCode
                    });
                    baseProductSkus.Add(baseSku);
                });

                // 解析图片，获取图片地址
                var imgModel = ent.MainImageJson.ToObject<List<SupplierProductImgModel>>();
                var imgStrList = imgModel.OrderByDescending(x => x.IsMain).Select(x => x.FullUrl).ToList();

                var baseProduct = new BaseProductSkuAddModel
                {
                    SpuCode = existSpuCodeList.Contains(ent.SpuCode) ? Guid.NewGuid().ToString("N").ToShortMd5() : ent.SpuCode,
                    Subject = ent.Subject,
                    ShortTitle = ent.ShortTitle,
                    ProductImagesStr = imgStrList,
                    DescriptionStr = ent.Description?.Split(',').ToList(),
                    RootNodeFxUserId = ent.RootNodeFxUserId,
                    SharePathCode = sharePathCode,
                    ProductSkus = baseProductSkus,
                    FromFxUserId = ent.FxUserId,
                    FromProductUid = ent.Uid.ToString(),
                    CreateFrom = "Copy",
                    PathNodeDeep = ent.PathNodeDeep + 1,
                    SkuModeType = ent.SkuModeType,//规格模式
                    UpBaseProductUid = ent.FromProductUid.ToString() //基础商品Uid
                };

                configModels.Add(new BaseProductConfigModel
                {
                    RefType = "Product",
                    SupplierFxUserId = ent.FxUserId,
                    RefCode = baseProduct.SpuCode
                });
                baseProduct.ConfigModels = configModels;
                baseProduct.SharePathFlow = sharePathFlow;

                result.Add(baseProduct);
            });

            return result;
        }

        /// <summary>
        /// 返回商品详情
        /// </summary>
        /// <param name="uid"></param>
        /// <param name="curFxUserId"></param>
        /// <param name="isBaseProUid">是否基础商品的Uid</param>
        /// <returns></returns>
        public SupplierProductDetailModel GetByUid(long uid, int curFxUserId, bool isBaseProUid = false)
        {
            var result = new SupplierProductDetailModel();
            var resultSku = new List<SupplierProductUpdateSkuModel>();
            // 查找货盘商品
            if (isBaseProUid) uid = _repository.GetUidByBaseProductUid(uid);
            var ent = _repository.GetByUid(uid);
            if (ent == null) throw new LogicException("商品不存在！");
            if (ent.Status == 0) throw new LogicException("该商品已删除！");

            // 判断是否有权限查看
            var userSupplier = _userSupplierStatusRepository.GetByFxUserIdAndStatus(curFxUserId, new List<int> { 1, 5, 6 });
            var supplierFxUser = userSupplier.FirstOrDefault(x => x.SupplierFxUserId == ent.FxUserId);

            if (supplierFxUser == null && ent.FxUserId != curFxUserId) throw new LogicException("您无权查看该商品！");
            var supplierRuleDic = new Dictionary<int, SupplierRuleModel>();
            if (supplierFxUser != null)
            {
                // 未上架不允许查看
                if (ent.IsPublic == false) return result;
                var user = _userFxService.GetUserFxAddressInfo(supplierFxUser.SupplierFxUserId);

                if (SiteContext.IsSubAccount() && !SiteContext.Current.PermissionTags.Contains(FxPermission.SupplierAccount))
                    user.Mobile = EncryptUtil.EncryptAccount(user.Mobile);

                result.SupplierRemark = user.Mobile;
                if (supplierFxUser.RemarkName.IsNotNullOrEmpty()) result.SupplierRemark += $"({supplierFxUser.RemarkName})";

                // 查找是否已经复制
                var uids = new BaseProductRepository().GetByFromUids(new List<long>{ent.Uid}, curFxUserId);
                if (uids.Contains(ent.Uid.ToString())) result.IsCopy = true;

                // 获取厂家的业务联系人和公司信息和头像信息
                result.Contacts = _businessCardService.GetCardContacts(supplierFxUser.SupplierFxUserId,needEncryptAccount:true);
                result.CompanyInfo = _businessCardService.GetCompanyInfo(supplierFxUser.SupplierFxUserId);
                result.AvatarUrl = user.AvatarUrl;
                result.SupplierFxUserId = supplierFxUser.SupplierFxUserId;
                // 查询厂家换算规则
                supplierRuleDic = new MemberLevelService().GetSupplierRuleModel(new List<int> { supplierFxUser.SupplierFxUserId }, curFxUserId);
            }

            // 自己的商品
            if (ent.FxUserId == curFxUserId)
            {
                var user = _userFxService.GetUserFxAddressInfo(curFxUserId);
                result.Contacts = _businessCardService.GetCardContacts(curFxUserId);
                result.CompanyInfo = _businessCardService.GetCompanyInfo(curFxUserId);
                result.AvatarUrl = user.AvatarUrl;
                result.SupplierRemark = user.Mobile;
                result.SupplierFxUserId = curFxUserId;
            }

            // 获取类目信息
            var cateRelationList = _supplierProductCateRelationRepository.GetBySupUid(uid).Where(x => x.Status == 1)
                .ToList();
            if (cateRelationList.Any() == false)
                cateRelationList = new List<SupplierProductCateRelation>
                {
                    new SupplierProductCateRelation
                    {
                        CateId = "-1",
                        ParentId = "0",
                        Name = "其他",
                        Level = 1
                    }
                };
            var categoryInfoList = cateRelationList.Select(x => new CategoryInfo
            {
                CateId = x.CateId,
                Level = x.Level,
                Name = x.Name,
                ParentId = x.ParentId
            }).ToList();
            // 根据CateId去重
            categoryInfoList = categoryInfoList.GroupBy(x => x.CateId).Select(x => x.FirstOrDefault()).ToList();

            // 获取面单信息
            var shipmentsInfo = ent.ShipmentsInfo.ToObject<ShipmentsInfo>();
            var waybillInfoList = _supProductWaybillRelationRepository.GetBySupUid(uid);
            if (waybillInfoList.Any())
            {
                var expressInfo = waybillInfoList.Select(x => new ExpressBill
                {
                    Id = x.WaybillId,
                    ExpressBillName = x.WaybillName,
                    ExpressBillType = x.WaybillType
                }).ToList();
                if(shipmentsInfo != null) shipmentsInfo.ExpressBill = expressInfo;
            }

            var attrInfoList = new List<BaseProductSkuAttributeModel>();

            // 查找Sku
            var skuList = _supplierProductSkuRepository.GetListByProductUid(uid, ent.FxUserId, 1)
                .OrderBy(x => x.UpdateTime).ToList();
            skuList.ForEach(sku => {
                var skuModel = new SupplierProductUpdateSkuModel
                {
                    SkuUid = sku.Uid,
                    Attribute = BaseProductUtils.FromJson(sku.Attributes),
                    Attributes = sku.Attributes,
                    DistributePrice = sku.DistributePrice,
                    ImageUrlStr = ImgHelper.ChangeImgUrl(sku.ImageUrl),
                    SkuCode = sku.SkuCode,
                    Weight = sku.Weight,
                    ShortTitle = sku.ShortTitle,
                    SkuUidStr = sku.Uid.ToString()
                };
                // 等级分销价换算
                if (supplierRuleDic.Any())
                {
                    supplierRuleDic.TryGetValue(sku.FxUserId, out var rule);
                    if (rule != null)
                    {
                        var (newPrice, isChanged) = MemberLevelService.DistributePriceChange(sku.DistributePrice, rule.PriceRule,
                            rule.FinalDistributePriceCorrectRule);
                        if (isChanged) skuModel.DistributePrice = newPrice;
                    }
                }

                skuModel.Attribute.ValueUrl = skuModel.ImageUrlStr;
                skuModel.Attribute.ValueUrlKey = skuModel.ImageUrlStr;
                resultSku.Add(skuModel);
                attrInfoList.Add(skuModel.Attribute);
            });

            var random = new Random();
            var type1 = attrInfoList
                .Where(a => !string.IsNullOrEmpty(a.AttributeName1))
                .GroupBy(a => a.AttributeName1)
                .Select(a => new AttributeTypeModel
                {
                    AttributeName = a.Key,
                    AttributeValues = a.Select(b => new AttributeValueModel
                    {
                        ImgAttributeValueNo = string.IsNullOrEmpty(b.ValueUrl) ? 0 : 1,
                        Value = b.AttributeValue1,
                        ValueUrl = b.ValueUrl,
                        ValueUrlKey = b.ValueUrlKey,
                        ImageObjectId = random.Next(),
                    }).GroupBy(m => m.Value).Select(m => m.FirstOrDefault()).ToList()
                }).ToList();
            var type2 = attrInfoList
                .Where(a => !string.IsNullOrEmpty(a.AttributeName2))
                .GroupBy(a => a.AttributeName2)
                .Select(a => new AttributeTypeModel
                {
                    AttributeName = a.Key,
                    AttributeValues = a.Select(b => new AttributeValueModel
                    {
                        ImgAttributeValueNo = string.IsNullOrEmpty(b.ValueUrl) ? 0 : 1,
                        Value = b.AttributeValue2,
                        ValueUrl = b.ValueUrl,
                        ValueUrlKey = b.ValueUrlKey,
                        ImageObjectId = random.Next(),
                    }).GroupBy(m => m.Value).Select(m => m.FirstOrDefault()).ToList()
                }).ToList();
            var type3 = attrInfoList
                .Where(a => !string.IsNullOrEmpty(a.AttributeName3))
                .GroupBy(a => a.AttributeName3)
                .Select(a => new AttributeTypeModel
                {
                    AttributeName = a.Key,
                    AttributeValues = a.Select(b => new AttributeValueModel
                    {
                        ImgAttributeValueNo = string.IsNullOrEmpty(b.ValueUrl) ? 0 : 1,
                        Value = b.AttributeValue3,
                        ValueUrl = b.ValueUrl,
                        ValueUrlKey = b.ValueUrlKey,
                        ImageObjectId = random.Next(),
                    }).GroupBy(m => m.Value).Select(m => m.FirstOrDefault()).ToList()
                }).ToList();

            // 解析图片，获取图片地址
            var imgModel = ent.MainImageJson.ToObject<List<SupplierProductImgModel>>();
            var imgStrList = imgModel.OrderBy(x => x.IsMain).Select(x => new SupplierProductImageModel
            {
                IsMain = x.IsMain,
                ImageUrl = ImgHelper.ChangeImgUrl(x.FullUrl),
                ImageObjectId = x.ImageObjectId
            }).OrderByDescending(x => x.IsMain).ToList();

            if (ent.Description == "") ent.Description = null;
            result.DescriptionStr = ent.Description?.Split(',').Select(a => ImgHelper.ChangeImgUrl(a)).ToList();
            result.CategoryInfoList = categoryInfoList;
            result.CategoryAttribute = ent.CategoryAttributes;
            result.SupplierProductUid = ent.Uid;
            result.SupplierProductUidStr = ent.Uid.ToString();
            result.ShortTitle = ent.ShortTitle;
            result.Subject = ent.Subject;
            result.ShipmentsInfo = shipmentsInfo;
            result.AfterSalesInfo = ent.AfterSalesInfo.ToObject<AfterSalesInfo>();
            result.ProductImages = imgStrList;
            result.ProductSkus = resultSku;
            result.PublicTime = ent.PublicTime?.ToString("yyyy-MM-dd");
            result.MaxPrice = ent.MaxPrice;
            result.MinPrice = ent.MinPrice;
            result.IsWaitSyncBaseProduct = ent.IsWaitSyncBaseProduct;
            result.BaseProductUid = ent.FromProductUid.ToString();
            result.AttributeTypes = new List<AttributeTypeModel>();
            result.AttributeTypes.AddRange(type1);
            result.AttributeTypes.AddRange(type2);
            result.AttributeTypes.AddRange(type3);
            result.SkuModeType = ent.SkuModeType;
            // 等级分销价换算
            if (supplierRuleDic.Any())
            {
                supplierRuleDic.TryGetValue(result.SupplierFxUserId ?? 0, out var rule);
                if (rule == null) return result;
                
                // 更新 MaxPrice
                var (newMaxPrice, maxPriceChanged) = MemberLevelService.DistributePriceChange(result.MaxPrice, rule.PriceRule, rule.FinalDistributePriceCorrectRule);
                if (maxPriceChanged) result.MaxPrice = newMaxPrice;

                // 更新 MinPrice
                var (newMinPrice, minPriceChanged) = MemberLevelService.DistributePriceChange(result.MinPrice, rule.PriceRule, rule.FinalDistributePriceCorrectRule);
                if (minPriceChanged) result.MinPrice = newMinPrice;

                // 确保 MaxPrice 和 MinPrice 的顺序正确
                if (result.MaxPrice < result.MinPrice)
                {
                    (result.MaxPrice, result.MinPrice) = (result.MinPrice, result.MaxPrice);
                }
            }

            return result;
        }

        /// <summary>
        /// 上架或下架货盘商品
        /// </summary>
        /// <param name="uidList"></param>
        /// <param name="isPublic"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public int PublicOrPrivate(List<long> uidList, bool isPublic, int curFxUserId)
        {
            // 获取自己的货盘商品且状态为 !isPublic
            var productList = _repository.GetByUids(uidList)
                .Where(x => x.FxUserId == curFxUserId && x.IsPublic == !isPublic).ToList();
            if (productList.Any() == false) throw new LogicException("没有找到要操作的商品");

            // 更新状态
            foreach (var product in productList)
            {
                product.IsPublic = isPublic;
                if (isPublic) product.PublicTime = DateTime.Now;
                product.UpdateTime = DateTime.Now;
            }

            // 更新数据库
            var result = _repository.BulkUpdateByParam(productList, new List<string>{"IsPublic", "UpdateTime", "PublicTime"});

            return result;
        }

        /// <summary>
        /// 获取列表详情
        /// </summary>
        /// <param name="model"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public SupplierProductListModel GetSupplierProductPage(SupplierProductQueryModel model, int curFxUserId)
        {
            _baseproductRepository = new BaseProductRepository(curFxUserId);
            var query = new SupplierProductQuery
            {
                FxUserId = curFxUserId,
                IsOrderDesc = model.IsOrderDesc,
                OrderByField = model.OrderByField,
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                ProductName = model.ProductName,
                IsPublic = model.IsPublic,
                Tag = model.Tag,
                IsInit = model.IsInit
            };
            var userSupplier = new List<UserSupplierStatus>();
            var supplierRuleDic = new Dictionary<int, SupplierRuleModel>();

            // 如果厂家小站和选品铺货，要查询合作关系
            if (model.Tag == 2 || model.Tag == 3)
            {
                // 当前用户存在合作关系的厂家Id
                userSupplier = _userSupplierStatusRepository
                    .GetByFxUserIdAndStatus(curFxUserId, new List<int> { 1, 5, 6 });
                var userSupplierIds = userSupplier
                    .Select(x => x.SupplierFxUserId)
                    .ToList();
                // 查询厂家换算规则
                supplierRuleDic = new MemberLevelService().GetSupplierRuleModel(userSupplierIds, curFxUserId);

                if (model.Tag == 2)
                {
                    if (model.SupplierFxUserId.HasValue == false ||
                        !userSupplierIds.Contains(model.SupplierFxUserId.Value))
                    {
                        throw new LogicException("您与该厂家还不是合作关系，请确认！");
                    }

                    query.SupplierFxUserId = new List<int>{model.SupplierFxUserId.Value};
                }

                if (model.Tag == 3)
                {
                    if (userSupplierIds.Any() == false) throw new LogicException("您未与厂家建立合作关系，请检查！");

                    query.ExpressBillId = model.ExpressBillId;
                    query.CategoryId = model.CategoryId;
                    query.SupplierFxUserId = userSupplierIds;
                    // 筛选厂家
                    if (model.SupplierFxUserIds != null && model.SupplierFxUserIds.Any()) query.SupplierFxUserId = model.SupplierFxUserIds;
                }
            }

            var result = new SupplierProductListModel();
            var tuple = _repository.GetPageList(query);
            result.Total = tuple.Item1;
            result.PageList = new List<SupplierProductPageModel>();
            var uids = tuple.Item2.Select(x => x.Uid).ToList();
            // 查询基础商品是否有已复制的商品
            var byFormUids = _baseproductRepository.GetByFromUids(uids, curFxUserId);
            // 根据Uid查询快递面单关联信息
            var waybillList = _supProductWaybillRelationRepository.GeyBySupUids(uids);
            // 根据厂家Id查询厂家信息
            var fxUserNickMobileModels = new List<FxUserNickMobileModel>();
            if (model.Tag == 3)
            {
                fxUserNickMobileModels = _userFxService.GetFxUserNickMobiles(userSupplier.Select(x => x.SupplierFxUserId).ToList());
            }

            tuple.Item2.ForEach(ent =>
            {
                var product = new SupplierProductPageModel();
                var imgModel = ent.MainImageJson.ToObject<List<SupplierProductImgModel>>();
                var waybill = waybillList.FindAll(x => x.SupplierProductUid == ent.Uid).Select(x => new ExpressBill()
                {
                    ExpressBillName = x.WaybillName,
                    ExpressBillType = x.WaybillType,
                    Id = x.WaybillId
                }).ToList();

                product.MainImgUrl = ImgHelper.ChangeImgUrl(imgModel.FirstOrDefault(x => x.IsMain)?.FullUrl);
                product.Uid = ent.Uid;
                product.ShortTitle = ent.ShortTitle;
                product.Subject = ent.Subject;
                product.SupplierFxUserId = ent.FxUserId;
                product.MaxPrice = ent.MaxPrice;
                product.MinPrice = ent.MinPrice;
                product.PublicTime = ent.PublicTime?.ToString();
                product.IsCopy = byFormUids.Contains(ent.Uid.ToString());
                product.ExpressBill = waybill;
                product.IsPublic = ent.IsPublic;

                var userSupplierStatus = userSupplier.FirstOrDefault(x => x.SupplierFxUserId == ent.FxUserId);
                product.SupplierRemark = userSupplierStatus?.RemarkName;

                // 是否有厂家账号数据权限
                var hasSupplierAccountPermission = !SiteContext.IsSubAccount() || SiteContext.Current.PermissionTags.Contains(FxPermission.SupplierAccount);
                if (model.Tag == 3 && fxUserNickMobileModels.Any())
                {
                    var fxUserNickMobile = fxUserNickMobileModels.FirstOrDefault(x => x.Id == ent.FxUserId);
                    product.SupplierRemark = hasSupplierAccountPermission ? fxUserNickMobile?.Mobile : EncryptUtil.EncryptAccount(fxUserNickMobile?.Mobile);
                    product.AvatarUrl = fxUserNickMobile?.AvatarUrl;
                    if (userSupplierStatus != null && userSupplierStatus.RemarkName.IsNotNullOrEmpty())
                        product.SupplierRemark += $"({userSupplierStatus.RemarkName})";
                }

                result.PageList.Add(product);
            });

            if (model.Tag == 3)
            {
                var supCateList = _supplierProductCateRelationRepository.GetBySupUids(uids).Where(x => x.Level == 1);
                var existCatId = new List<string>();
                var cateList = new List<CategoryInfo>();
                foreach (var info in supCateList)
                {
                    if (existCatId.Contains(info.CateId)) continue;
                    var cate = new CategoryInfo
                    {
                        CateId = info.CateId,
                        Name = info.Name,
                        Level = info.Level,
                        ParentId = info.ParentId
                    };
                    existCatId.Add(info.CateId);
                    cateList.Add(cate);
                }
                result.CategoryList = cateList;
            }
            
            // 20240930新增等级分销价逻辑，要将价格根据厂家换算逻辑进行换算
            if (supplierRuleDic.Any())
            {
                result.PageList.ForEach(ent =>
                {
                    if (!supplierRuleDic.TryGetValue(ent.SupplierFxUserId, out var rule)) return;
                    
                    // 更新 MaxPrice
                    var (newMaxPrice, maxPriceChanged) = MemberLevelService.DistributePriceChange(ent.MaxPrice, rule.PriceRule, rule.FinalDistributePriceCorrectRule);
                    if (maxPriceChanged) ent.MaxPrice = newMaxPrice;

                    // 更新 MinPrice
                    var (newMinPrice, minPriceChanged) = MemberLevelService.DistributePriceChange(ent.MinPrice, rule.PriceRule, rule.FinalDistributePriceCorrectRule);
                    if (minPriceChanged) ent.MinPrice = newMinPrice;

                    // 确保 MaxPrice 和 MinPrice 的顺序正确
                    if (ent.MaxPrice < ent.MinPrice)
                    {
                        (ent.MaxPrice, ent.MinPrice) = (ent.MinPrice, ent.MaxPrice);
                    }
                });
            }
            
            return result;
        }

        /// <summary>
        /// 编辑更新货盘SKU分销价格
        /// </summary>
        /// <param name="model"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public bool UpdateDistributePrice(EditSupplierProductSkuRelation model, int curFxUserId)
        {
            var fxUserId = curFxUserId;
            var supplierModes = model.Relations;
            var productUid =
                supplierModes.Select(p => p.BaseProductUid).ToList();
            var supplierSkuUid =
                supplierModes.Select(p => p.SupplierProductSkuUid).Distinct().ToList();
            var supplierProductUid =
                supplierModes.Select(p => p.SupplierProductUid).Distinct().ToList();

            if (supplierSkuUid.Count == 0)
                throw new LogicException("货盘商品不存在!");
            var supplierProductSkuRelationR = new BaseOfSupplierSkuRelationRepository();
            var supplierProductSkuRepository = new SupplierProductSkuRepository();
            var supplierProductRepository = new SupplierProductRepository();

            var supplierProudctSkuRelations =
                supplierProductSkuRelationR.GetListBySupProductSkuUid(supplierSkuUid, curFxUserId);
            if (supplierProudctSkuRelations.Count == 0)
                throw new LogicException("货盘商品关系不存在!");
            var supplierProductSkus = supplierProductSkuRepository.GetListByUids(supplierSkuUid, curFxUserId);
            if (supplierProductSkus.Count == 0)
                throw new LogicException("货盘商品SKU不存在!");

            var upSupplierProductSkus = new List<SupplierProductSku>();
            var upSupplierProductSkusRelation = new List<BaseOfSupplierProductSkuRelation>();
            foreach (var item in supplierModes)
            {
                var sku = supplierProductSkus
                    .Where(p => p.Uid == item.SupplierProductSkuUid).FirstOrDefault();
                var skuRel = supplierProudctSkuRelations
                    .Where(p => p.SupplierProductSkuUid == item.SupplierProductSkuUid).FirstOrDefault();
                if (sku != null)
                {
                    sku.DistributePrice = item.DistributePrice;
                    upSupplierProductSkus.Add(sku);
                }
                if (skuRel != null)
                {
                    skuRel.DistributePrice = item.DistributePrice;
                    upSupplierProductSkusRelation.Add(skuRel);
                }
            }
            try
            {
                // 更新数据库
                supplierProductSkuRepository
                    .BulkUpdateByParam(upSupplierProductSkus, new List<string> {"DistributePrice" });
                supplierProductSkuRelationR
                    .BulkUpdateByParam(upSupplierProductSkusRelation,new List<string> {"DistributePrice" });

                // 更新成功处理货盘主表数据
                var currentSupplierSkus = supplierProductSkuRepository.GetListByProductUids(supplierProductUid, fxUserId);
                var currentSupplierProducts = supplierProductRepository.GetAllByUids(supplierProductUid);
                var currentUpdateProducts = new List<SupplierProductEntity>();
                foreach (var product in currentSupplierProducts)
                {
                    var skus = currentSupplierSkus.Where(p => p.SupplierProductUid == product.Uid).ToList();
                    var maxPrice = skus.Max(x => x.DistributePrice);
                    var minPrice = skus.Min(x => x.DistributePrice);
                    product.MaxPrice = maxPrice;
                    product.MinPrice = minPrice;
                    currentUpdateProducts.Add(product);
                }
                supplierProductRepository.BulkUpdateByParam(currentUpdateProducts,new List<string> { "MaxPrice", "MinPrice" });
                return true;
            }
            catch (Exception e)
            {
                Log.WriteError($"编辑更新货盘商品失败: {e.Message}");
                return false;
            }
        }


        /// <summary>
        /// 获取用户是否存在货盘商品
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<int> ExistProductByFxUserId(IEnumerable<int> fxUserIds)
        {
            return _repository.ExistProductByFxUserId(fxUserIds);
        }

        /// <summary>
        /// 软删除，目前仅用于测试
        /// </summary>
        /// <param name="uids"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        public int DeleteSupplierProduct(List<long> uids)
        {
            // 获取货盘商品
            var supplierProductList = _repository.GetByUids(uids);
            if (supplierProductList.Any() == false) throw new LogicException("没有找到要删除的商品");
            supplierProductList.ForEach(x => x.Status = 0);

            // 获取Sku
            var skuList = _supplierProductSkuRepository.GetListByProductUids(uids);
            skuList.ForEach(x => x.Status = 0);

            // 更新数据库
            var result = _repository.BulkUpdateByParam(supplierProductList, new List<string> { "Status" });
            _supplierProductSkuRepository.BulkUpdateByParam(skuList, new List<string> { "Status" });

            return result;
        }
        /// <summary>
        /// 获取货盘商品相关的的Sku（平台资料那需要拿RootNodeFxUserId这些东西）
        /// </summary>
        /// <param name="uid"></param>
        /// <param name="fxUserId"></param>
        public List<SupplierProductSku> GetSkuByUid(long uid, int fxUserId)
        {
            // 获取Sku
            return _supplierProductSkuRepository.GetListByProductUid(uid, fxUserId, 1);
        }
        /// <summary>
        /// 获取货盘商品（平台资料那需要拿RootNodeFxUserId这些东西）
        /// </summary>
        /// <param name="uid"></param>
        /// <param name="fxUserId"></param>
        public SupplierProductEntity GetSupplierProductByUid(long uid, int curFxUserId)
        {
            // 查找货盘商品
            var ent = _repository.GetByUid(uid);
            if (ent == null) throw new LogicException("商品不存在！");
            if (ent.Status == 0) throw new LogicException("该商品已删除！");

            // 判断是否有权限查看
            var userSupplier = _userSupplierStatusRepository.GetByFxUserIdAndStatus(curFxUserId, new List<int> { 1, 5, 6 });
            var supplierFxUser = userSupplier.FirstOrDefault(x => x.SupplierFxUserId == ent.FxUserId);

            if (supplierFxUser == null && ent.FxUserId != curFxUserId) throw new LogicException("您无权查看该商品！");

            return ent;
        }

        /// <summary>
        /// 删除小站SKU
        /// </summary>
        public void DelSku(long skuUid, int fxUserId)
        {
            _baseOfSupplierSkuRelationRepository = new BaseOfSupplierSkuRelationRepository();
            var supplierProudctSkuRelations = _baseOfSupplierSkuRelationRepository.GetListByProductSkuUid(new List<long>() { skuUid }, fxUserId);
            if (supplierProudctSkuRelations == null || !supplierProudctSkuRelations.Any())
            {
                return; // 货盘商品关系不存在
            }
            var supplierProudctSkuRelationfirst = supplierProudctSkuRelations.FirstOrDefault();
            var supplierProductSkuUid = supplierProudctSkuRelationfirst.SupplierProductSkuUid;

            SupplierProductSku delsku = _supplierProductSkuRepository.GetListByUids(new List<long>() { supplierProductSkuUid }, fxUserId).FirstOrDefault();
            if (delsku != null)
            {
                delsku.Status = 0;
                _supplierProductSkuRepository.Update(delsku);
            }
        }

        /// <summary>
        /// 获取列表（包含规格）
        /// </summary>
        /// <param name="model"></param>
        /// <param name="curFxUserId"></param>
        /// <returns></returns>
        public OpenPlatformModel.PagedRespone<OpenPlatformModel.SupplierProductListRespone> GetPageList(SupplierProductQueryModel model, int curFxUserId)
        {
            var query = new SupplierProductQuery()
            {
                FxUserId = curFxUserId,
                IsOrderDesc = model.IsOrderDesc,
                OrderByField = model.OrderByField,
                PageIndex = model.PageIndex,
                PageSize = model.PageSize,
                ProductName = model.ProductName,
                IsPublic = model.IsPublic,
                Tag = model.Tag,
                IsInit = model.IsInit,
            };

            var userSupplier = new List<UserSupplierStatus>();
            // 当前用户存在合作关系的厂家Id
            userSupplier = _userSupplierStatusRepository
                .GetByFxUserIdAndStatus(curFxUserId, new List<int> { 1 });
            var userSupplierIds = userSupplier
                .Select(x => x.SupplierFxUserId)
                .ToList();
            query.SupplierFxUserId = userSupplierIds;
            // 筛选厂家
            if (model.SupplierFxUserIds.IsNotNullAndAny())
            {
                query.SupplierFxUserId = model.SupplierFxUserIds;
                if (!query.SupplierFxUserId.All(userSupplierIds.Contains))
                    //校验厂家id
                    throw new LogicException($"您查询的厂家存在不是【绑定成功】状态，请重试，或更换其他厂家");
            }
      
            var result = new OpenPlatformModel.PagedRespone<OpenPlatformModel.SupplierProductListRespone>();
          
            var tuple = _repository.GetPageList(query);
            result.Total = tuple.Item1;
            if (tuple.Item2.IsNullOrEmptyList())
                return result;
            var fxUserNickMobileModels = _userFxService.GetFxUserNickMobiles(userSupplier.Select(x => x.SupplierFxUserId).ToList());
            var productUids = tuple.Item2.Select(t => t.Uid).ToList();
            var supplierProductSkus = _supplierProductSkuRepository.GetListByProductUids(productUids);
            tuple.Item2.ForEach(t =>
            {
                var item = new OpenPlatformModel.SupplierProductListRespone();
                item.Id = t.Uid;
                item.Name = t.Subject;
                item.PublicTime = t.PublicTime?.ToString("yyyy-MM-dd HH:mm:ss");
                var shipmentsInfo = t.ShipmentsInfo.ToObject<ShipmentsInfo>();
                var afterSalesInfo = t.AfterSalesInfo.ToObject<AfterSalesInfo>();
                item.ShipmentsInfo = new OpenPlatformModel.SupplierProductShipmentsInfo()
                {
                    SendaddressList = shipmentsInfo.SendaddressList
                };
                item.AfterSalesInfo = new OpenPlatformModel.SupplierProductAfterSalesInfo()
                {
                    AftersaleaddressList = afterSalesInfo?.AftersaleaddressList
                };
                item.Skus = new List<OpenPlatformModel.SupplierProductSku>();

                //厂家账号信息
                //校验厂家数据权限
                var hasSupplierAccountPermission = !SiteContext.IsSubAccount() || SiteContext.Current.PermissionTags.Contains(FxPermission.SupplierAccount);

                supplierProductSkus.Where(s => s.SupplierProductUid == t.Uid)?.ToList().ForEach(sku =>
                {
                    var distributePrice = 0l;
                    if (sku.DistributePrice!=null && sku.DistributePrice>0)
                        distributePrice= (sku.DistributePrice.Value*100).ToLong();
                    var attribute = BaseProductUtils.FromJson(sku.Attributes);
                    var skuName = attribute.AttributeValue1;
                    if (!string.IsNullOrWhiteSpace(attribute.AttributeValue2))
                        skuName += " " + attribute.AttributeValue2;
                    item.Skus.Add(new OpenPlatformModel.SupplierProductSku()
                    {
                        SkuId = sku.Uid,
                        SkuName = skuName,
                        DistributePrice = distributePrice,
                    });
                });
                var fxUserNickMobile = fxUserNickMobileModels.FirstOrDefault(x => x.Id == t.FxUserId);
                item.SupplierUser = hasSupplierAccountPermission ? fxUserNickMobile?.Mobile : EncryptUtil.EncryptAccount(fxUserNickMobile?.Mobile);

                result.Rows.Add(item);
            });
            return result;
        }
    }
}