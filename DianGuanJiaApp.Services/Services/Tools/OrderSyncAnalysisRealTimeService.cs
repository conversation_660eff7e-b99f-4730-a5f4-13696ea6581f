using System;
using DianGuanJiaApp.Data.FxModel.Tools;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Services.Services.Tools
{
    public class OrderSyncAnalysisRealTimeService
    {
        private readonly OrderSyncAnalysisWeiXinTriggerSyncMessageHandleService
            _orderSyncAnalysisWeiXinTriggerSyncMessageHandleService;
        private readonly OrderSyncAnalysisService _orderSyncAnalysisService;

        public OrderSyncAnalysisRealTimeService()
        {
            _orderSyncAnalysisWeiXinTriggerSyncMessageHandleService =
                new OrderSyncAnalysisWeiXinTriggerSyncMessageHandleService();
            _orderSyncAnalysisService = new OrderSyncAnalysisService();
        }

        /// <summary>
        /// 微信小程序触发订单同步分析
        /// </summary>
        /// <param name="message"></param>
        public void WeiXinTriggerAnalysisWithReleaseMessageLock(OrderSyncAnalysisWeiXinTriggerSyncMessageModel message)
        {
            try
            {
                //非订单同步，直接修改时间
                if (message.SyncType != 1)
                {
                    //更新最后同步时间
                    _orderSyncAnalysisService.UpdateLastSyncTime(message.SyncStatusId, message.LastSyncTime);
                }
                //订单同步分析
                var analysisMessage = new OrderSyncAnalysisMessageModel
                {
                    SyncStatusId = message.SyncStatusId,
                    Time = message.Time,
                    IsRealTimeAnalysis = true,
                    NeedUpdateOfLastSyncTime = message.LastSyncTime
                };
                var isTriggerSync = _orderSyncAnalysisService.AnalysisRealTime(analysisMessage);
                //日志
                Log.WriteLine($"同步状态ID：{message.SyncStatusId}，其他信息：{message.FxUserId}|{message.ShopId}|{message.LastSyncTime}，微信小程序触发订单同步分析完成，是否触发同步：{isTriggerSync}",
                    $"WeiXinTriggerAnalysis_{DateTime.Now:yyyy-MM-dd}.log");
                //是否触发同步
                if (isTriggerSync)
                {
                    _orderSyncAnalysisService.UpdateLastSyncTimeWithTriggerSync(message.SyncStatusId, message.FxUserId,
                        message.LastSyncTime);
                }
            }
            catch (Exception e)
            {
                //异常，执行触发同步
                _orderSyncAnalysisService.UpdateLastSyncTimeWithTriggerSync(message.SyncStatusId, message.FxUserId,
                    message.LastSyncTime);
                //日志
                Log.WriteError(
                    $"同步状态ID：{message.SyncStatusId}，其他信息：{message.FxUserId}|{message.ShopId}|{message.LastSyncTime}，微信小程序触发订单同步分析失败，失败原因：{e}",
                    $"WeiXinTriggerAnalysisWithReleaseMessageLockError_{DateTime.Now:yyyy-MM-dd}.log");
            }
            finally
            {
                _orderSyncAnalysisWeiXinTriggerSyncMessageHandleService.ReleasePushMessageLock(message);
            }
        }

    }
}