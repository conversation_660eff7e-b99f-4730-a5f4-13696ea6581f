using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Caching;
using System.Threading;
using Dapper;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.Services.DataArchive;
using DianGuanJiaApp.Services.Services.SubAccount;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;

namespace DianGuanJiaApp.Services
{
    public class UserFxService : BaseService<UserFx>
    {
        private readonly CommonSettingService _commonSettingService = new CommonSettingService();
        private readonly UserFxRepository repository = new UserFxRepository();
        private readonly SubUserFxService subUserFxService = new SubUserFxService();
        //private ShopRepository shopRepository = new ShopRepository();
        private SyncStatusService _syncStatusService = new SyncStatusService();
        private UserVerificationCodeRepository verificationCodeRepository = new UserVerificationCodeRepository();
        //private WareHouseService wareHouseService = new WareHouseService();
        private readonly CommonSettingRedisRepository _redis = new CommonSettingRedisRepository();
        private readonly ReceDbConfigRepository _receDbConfigRepository = new ReceDbConfigRepository();

        public UserFxService()
        {
            _baseRepository = new BaseRepository<UserFx>(CustomerConfig.ConfigureDbConnectionString);
        }

        public Shop GetSystemShop(int fxUserId)
        {
            if (fxUserId <= 0)
                return null;
            return repository.GetSystemShops(fxUserId);
        }

        /// <summary>
        /// fxuserId,获取system店铺数据
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<Shop> GetSystemShopList(List<int> fxUserIds)
        {
            if (fxUserIds.Count <= 0)
                return new List<Shop>();
            return repository.GetSystemShopList(fxUserIds).ToList();
        }


        public IEnumerable<Shop> GetSystemShops(IEnumerable<int> fxUserIds)
        {
            if (fxUserIds == null || fxUserIds.Any() == false)
                return new List<Shop>();
            return repository.GetSystemShops(fxUserIds);
        }

        public bool PhoneIsExist(string phone)
        {
            var user = repository.GetByMobile(phone);
            return user == null ? false : true;
        }

        /// <summary>
        ///分单微信小程序
        /// </summary>
        /// <param name="openid"></param>
        /// <returns></returns>
        public UserFx GetWxOpenId(string openid)
        {
            var user = repository.GetByWxOpenId(openid);
            if (user == null)
            {
                var now = DateTime.Now;
                var userfxNew = new UserFx
                {
                    NickName = "微信用户",
                    Mobile = "",
                    Password = "",
                    CreateTime = now,
                    Status = 1, //状态(1:正常,2:禁用,3:删除)
                };

                if (!string.IsNullOrWhiteSpace(openid))
                    userfxNew.WxOpenId = openid;
                try
                {
                    userfxNew.Id = repository.Add(userfxNew);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"【{openid}】创建UserFx异常：{ex}");
                    if (ex.Message.Contains("PRIMARY KEY"))
                        Log.WriteError($"【{openid}】订单分发账号注册失败：{ex}"); //同步到拼多多配置库可能会主键重复失败，不抛异常保证阿里这边正常运行
                    else
                        throw new LogicException("注册账号异常，请联系我们");
                }
                if (userfxNew.Id > 0)
                    user = userfxNew;
                else
                    user = repository.GetByWxOpenId(openid);
            }
            return user;
        }


        /// <summary>
        /// 登录（兼容子账号）
        /// </summary>
        /// <param name="model"></param>
        /// <param name="isSimpleLogin">是否使用短信验证码登录</param>
        /// <returns></returns>
        public Tuple<UserFx, Shop, SubUserFx> Login(UserLoginModel model, bool isSimpleLogin = false)
        {
            UserFx userfx = null;
            SubUserFx subUserFx = null;

            userfx = repository.GetByMobile(model.Mobile);
            if (userfx == null)
            {
                //子账号
                return subUserFxService.Login(model, isSimpleLogin);
            }
            else if (model.SubFxUserId > 0)
            {
                //子账号
                subUserFx = subUserFxService.GetSubUserFx(model.SubFxUserId);
                if (subUserFx != null && subUserFx.MainFxUserId != userfx.Id)
                {
                    throw new LogicException("子账号所属异常，无法登录。");
                }
                if (subUserFx != null && subUserFx.Status != 1)
                {
                    throw new LogicException("该账号尚未启用，请联系您的系统管理员开启。");
                }
            }

            if (userfx.IsBlockek)
                throw new LogicException("账号异常，无法登录。");

            var temp = (DateTime.Now - userfx.LastTryLoginTime.toDateTime()).Hours;

            if (userfx.RetryTimes != null && userfx.RetryTimes > 4 && userfx.LastTryLoginTime != null && (DateTime.Now - userfx.LastTryLoginTime.toDateTime()).Hours < 1)
            {
                throw new LogicException("由于您的异常登录，系统已将您的账号锁定1小时，请一小时后再登录。");
            }
            //isSimpleLogin为true时不校验密码
            if (!isSimpleLogin && (model.Password.IsNullOrEmpty() || userfx.Password != DES.EncryptUrl(model.Password, CustomerConfig.LoginCookieEncryptKey)))
            {
                userfx.RetryTimes = (userfx.RetryTimes ?? 0) + 1;
                if (userfx.LastTryLoginTime != null && (DateTime.Now - userfx.LastTryLoginTime.toDateTime()).Hours >= 1)
                    userfx.RetryTimes = 1;
                repository.UpdateLastTryLoginTime(userfx);
                throw new LogicException("账号或密码有误，请重新输入。");
            }
			//验证密码是否长时间未修改，超过指定时间未修改应提示密码长时间未修改，请重置密码后重新登录，修改密码后更新PasswordUpdateTime
			if (CustomerConfig.IsFxUserPasswordNeedReset 
                && userfx.PasswordUpdateTime != null 
                && userfx.PasswordUpdateTime < DateTime.Now.AddDays(-CustomerConfig.FxUserPasswordNeedResetDays))
			{
				throw new LogicException("由于您的登录异常，且账号密码长时间未修改，请点击忘记密码重设密码。");
			}

			//成功，更新登录时间
			userfx.LastLoginIP = Utility.Net.HttpUtility.GetServerIP();
            repository.UpdateLastLoginTime(userfx);

            //初始化货主
            var wareHouseService = new WareHouseService();
            wareHouseService.OwnerInit(userfx.Id, userfx.Name, "FenDanSystem");

            var shopService = new ShopService();
            var mobile = userfx.Mobile;
            var now = DateTime.Now;
            var expiredTime = now.AddDays(15);
            var shop = shopService.Get(userfx.Mobile, PlatformType.System);

            #region 补偿创建Shop 2022.9.26
            if (shop == null)
            {
                Log.WriteError($"【{mobile}】补偿创建Shop");

                shop = new Shop
                {
                    ShopId = mobile,
                    ShopName = mobile,
                    AccessToken = "",
                    RefreshToken = "",
                    AuthTime = now,
                    CreateTime = now,
                    ExpireTime = expiredTime,
                    PlatformType = PlatformType.System.ToString(),
                    FullSyncStatus = "",//默认值为空字符串，不触发全量同步逻辑。
                    FxUserIds = userfx.Id //赋值，添加FxDbConfig时要用到
                };
                shop.NickName = shop.ShopName;
                var onlyCode = CommUtls.GetOnlyCode();
                shop.OnlyCode = onlyCode;
                try
                {
                    shop.Id = shopService.Add(shop);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"【{mobile}】补偿创建Shop异常：{ex}");
                    throw new LogicException("注册信息有误，请联系我们。");
                }
            }
            else
            {
                shop.FxUserIds = userfx.Id;
            }
            #endregion
            //检查过期用户登录恢复归档数据
            new DataArchiveRestoreService().RestoreByExpiredUserArchive(userfx.Id);
            
            var fxUserShop = repository.GetFxUserShopByUserId(userfx.Id).FirstOrDefault();

            #region 补偿创建FxUserShop 2022.9.26

            if (fxUserShop == null)
            {
                Log.WriteError($"【{mobile}】补偿创建FxUserShop");

                fxUserShop = new FxUserShop
                {
                    FxUserId = userfx.Id,
                    ShopId = shop.Id,
                    CreateTime = now,
                    PlatformType = PlatformType.System.ToString(),
                    Status = FxUserShopStatus.Binded,
                    AuthTime = now,
                    NickName = shop.NickName
                };
                try
                {
                    new FxUserShopRepository().Add(fxUserShop);
                }
                catch (Exception ex)
                {
                    Log.WriteError($"【{mobile}】补偿创建FxUserShop异常：{ex}");
                    throw new LogicException("注册信息有误，请联系我们。");
                }
            }

            #endregion

            //d.创建该系统店铺的数据库关系P_DbConfig
            try
            {
                //配置数据库在哪
                new ShopService().TryToCreateCloudDbConfig(shop, "login");
            }
            catch (Exception e)
            {
            }


            return new Tuple<UserFx, Shop, SubUserFx>(userfx, shop, subUserFx);
        }

        public void UpdateLastTryLoginTime(UserFx userfx)
        {
            repository.UpdateLastTryLoginTime(userfx);
        }


        /// <summary>
        ///     用户注册（手机号码校验兼容子账号）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public Tuple<UserFx, Shop> Register(UserRegisterModel model)
        {
            //a.添加用户信息（需判断手机号是否有注册过，微信同理） P_UserFx
            UserFx userfx = null;
            if (!string.IsNullOrEmpty(model.Mobile))
            {
                userfx = repository.GetByMobile(model.Mobile);
                if (userfx != null)
                {
                    Log.WriteError($"【{model.Mobile}】该号码已注册，请返回登录");
                    throw new LogicException($"该号码已注册，请返回登录。", "Has_Registered");
                }
                else
                {
                    //子账号
                    var subUserFx = new SubUserFxService().GetByMobile(model.Mobile);
                    if (subUserFx != null)
                    {
                        Log.WriteError($"【{model.Mobile}】该号码已注册，请返回登录");
                        throw new LogicException($"该号码已注册，请返回登录。", "Has_Registered");
                    }
                }
            }

            var isThrowException = false;
            Shop shop = null;
            var op = OptimisticLockOperationType.FxRegisterUser;
            var opId = $"FXZC{model.Mobile}";
            var result = _commonSettingService.ExecuteWithOptimisticLock(() =>
            {
                //b.添加一个线下店（或叫微商店）、并与用户账号进行关联
                var configureDb = repository.GetConfigureDb();

                var now = DateTime.Now;
                userfx = new UserFx
                {
                    Mobile = model.Mobile,
                    CreateTime = now,
                    Status = 1, //状态(1:正常,2:禁用,3:删除)
                    PasswordUpdateTime = now,
                };

                if (!string.IsNullOrWhiteSpace(model.Password))
                    userfx.Password = DES.EncryptUrl(model.Password, CustomerConfig.LoginCookieEncryptKey);
                if (!string.IsNullOrEmpty(model.WxName))
                    userfx.NickName = model.WxName;
                if (!string.IsNullOrWhiteSpace(model.WxOpenId))
                    userfx.WxOpenId = model.WxOpenId;
                try
                {
                    userfx.Id = repository.Add(userfx);
                }
                catch (Exception ex)
                {
                    isThrowException = true;
                    Log.WriteError($"【{model.Mobile}】创建UserFx异常：{ex}");
                    if (ex.Message.Contains("PRIMARY KEY"))
                        Log.WriteError($"【{model.Mobile}】订单分发账号注册失败：{ex}"); //同步到拼多多配置库可能会主键重复失败，不抛异常保证阿里这边正常运行
                    else
                        throw new LogicException("注册账号异常，请联系我们");
                }


                #region b.添加店铺及其关联信息 P_Shop

                var expiredTime = now.AddDays(15);
                var platformType = PlatformType.System.ToString(); //店铺类型为System

                var shopId = userfx.Mobile;
                shop = new Shop
                {
                    ShopId = shopId,
                    ShopName = shopId,
                    AccessToken = "",
                    RefreshToken = "",
                    AuthTime = now,
                    CreateTime = now,
                    ExpireTime = expiredTime,
                    PlatformType = platformType,
                    FullSyncStatus = "", //默认值为空字符串，不触发全量同步逻辑。
                    FxUserIds = userfx.Id //赋值，添加FxDbConfig时要用到
                };
                shop.NickName = shop.ShopName;
                var onlyCode = CommUtls.GetOnlyCode();
                shop.OnlyCode = onlyCode;
                try
                {
                    shop.Id = configureDb.Insert(shop).Value;
                }
                catch (Exception)
                {
                    try
                    {
                        shop.Id = 0;
                        shop.Id = configureDb.Insert(shop).Value;
                    }
                    catch (Exception e)
                    {
                        isThrowException = true;
                        Log.WriteError($"【{model.Mobile}】创建Shop异常：{e}");

                        #region 回删数据2022.9.26

                        var errMobile = model.Mobile + "_Error_step2";
                        //回删P_UserFx数据
                        userfx.Mobile = errMobile;
                        repository.Update(userfx);

                        #endregion

                        throw new LogicException("注册账号异常，请联系我们");
                    }
                }

                #endregion

                #region c.添加关联数据P_FxUserShop

                var userShop = new FxUserShop
                {
                    FxUserId = userfx.Id,
                    ShopId = shop.Id,
                    CreateTime = now,
                    PlatformType = platformType,
                    Status = FxUserShopStatus.Binded,
                    AuthTime = now,
                    NickName = shop.NickName
                };
                try
                {
                    userShop.Id = configureDb.Insert(userShop).Value;
                }
                catch (Exception)
                {
                    try
                    {
                        userShop.Id = configureDb.Insert(userShop).Value;
                    }
                    catch (Exception e)
                    {
                        isThrowException = true;
                        Log.WriteError($"【{model.Mobile}】创建FxUserShop异常：{e}");

                        #region 回删数据2022.9.26

                        var errMobile = model.Mobile + "_Error_step3";
                        //回删P_Shop数据
                        shop.ShopName = errMobile;
                        shop.NickName = errMobile;
                        shop.ShopId = errMobile;
                        configureDb.Update(shop);

                        //回删P_UserFx数据
                        userfx.Mobile = errMobile;
                        repository.Update(userfx);

                        #endregion

                        throw new LogicException("注册账号异常，请联系我们");
                    }
                }

                #endregion

                #region d.创建该系统店铺的数据库关系P_DbConfig

                var shopService = new ShopService();
                try
                {
                    //配置数据库在哪
                    shopService.TryToCreateCloudDbConfig(shop, "register");
                }
                catch (Exception)
                {
                    try
                    {
                        //配置数据库在哪
                        shopService.TryToCreateCloudDbConfig(shop, "register");
                    }
                    catch (Exception e)
                    {
                        isThrowException = true;
                        Log.WriteError($"【{model.Mobile}】创建DbConfig异常：{e}");

                        #region 回删数据2022.9.26

                        var errMobile = model.Mobile + "_Error_step4";
                        //回删P_FxUserShop数据
                        userShop.NickName = errMobile;
                        userShop.Status = FxUserShopStatus.AuthExpired;
                        userShop.ShopId = -1 * shop.Id;
                        userShop.FxUserId = -1 * userfx.Id;
                        configureDb.Update(userShop);

                        //回删P_Shop数据
                        shop.ShopName = errMobile;
                        shop.NickName = errMobile;
                        shop.ShopId = errMobile;
                        configureDb.Update(shop);

                        //回删P_UserFx数据
                        userfx.Mobile = errMobile;
                        repository.Update(userfx);

                        #endregion

                        throw new LogicException("注册账号异常，请联系我们");
                    }
                }

                #endregion

                #region e.创建FxUserId对应的【收件人数据库关系ReceDbConfig】

                try
                {
                    TryToCreateCloudReceDbConfig(userfx.Id);
                }
                catch (Exception)
                {
                    try
                    {
                        TryToCreateCloudReceDbConfig(userfx.Id);
                    }
                    catch (Exception e)
                    {
                        isThrowException = true;
                        Log.WriteError($"【{model.Mobile}】创建ReceDbConfig异常：{e}");
                        throw new LogicException("注册账号异常，请联系我们");
                    }
                }

                #endregion

                return true;
            }, null, op, opId);

            if (isThrowException)
                throw new LogicException("注册账号异常，请联系我们");

            if (result.IsExcuted == false)
            {
                Log.WriteError($"【{model.Mobile}】该号码正在注册，请稍候刷新重新登录");
                throw new LogicException("该号码正在注册，请稍候刷新重新登。", "Has_Registered");
            }

            return new Tuple<UserFx, Shop>(userfx, shop);
        }

        /// <summary>
        ///     重置密码（兼容子账号）
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="LogicException"></exception>
        public Tuple<UserFx, Shop, SubUserFx> ResetPassword(UserRegisterModel model)
        {
            //var userfx = repository.GetByMobile(model.Mobile);
            var tuple = new UserFxService().GetUserFxByMobileV2(model.Mobile);
            var userfx = tuple.Item1;
            var subUserFx = tuple.Item2;
            if (userfx == null && subUserFx == null)
                throw new LogicException("您要重置密码的账号不存在，您可以先注册一个，再登录。");
            if (userfx.IsBlockek)
                throw new LogicException("账号异常，无法登录。");

            if (subUserFx == null)
            {
                var newPassword = DES.EncryptUrl(model.Password, CustomerConfig.LoginCookieEncryptKey);
                if (newPassword == userfx.Password)
                    throw new LogicException("新密码不能和老密码一样，请更换密码");
                userfx.Password = DES.EncryptUrl(model.Password, CustomerConfig.LoginCookieEncryptKey);
                userfx.LastUpdateTime = DateTime.Now;
                userfx.PasswordUpdateTime = DateTime.Now;
                repository.Update(userfx);
            }
            else
            {
                //子账号
                subUserFx.Password = DES.EncryptUrl(model.Password, CustomerConfig.LoginCookieEncryptKey);
                subUserFx.LastUpdateTime = DateTime.Now;
                new SubUserFxRepository().Update(subUserFx);
            }

            var shopService = new ShopService();

            Shop shop = null;
            var fxUserShop = repository.GetFxUserShopByUserId(userfx.Id).FirstOrDefault();

            if (fxUserShop != null)
                shop = shopService.Get(fxUserShop.ShopId);

            if (fxUserShop == null || shop == null)
                throw new LogicException("注册信息有误，请联系我们。");

            return Tuple.Create(userfx, shop, subUserFx);
        }


        /// <summary>
        ///     验证电话号码对应的验证码是否正确
        /// </summary>
        /// <param name="phone"></param>
        /// <param name="messageCode">短信验证码</param>
        /// <returns>1:不正确  2:已过期  3:已被使用</returns>
        public int CheckPhoneCodeIsValid(string phone, string messageCode)
        {
            var res = 0;

            //本地测试，直接返回0
            // if (CustomerConfig.IsDebug && CustomerConfig.IsLocalDbDebug)
            //     return res;

            var vCode = verificationCodeRepository.GetModel(phone, messageCode);

            if (vCode == null)
                res = 1;
            else if (DateTime.Now > vCode.ExpirationTime)
                res = 2;
            else if (vCode.IsEmploy)
                res = 3;

            return res;
        }

        /// <summary>
        ///     短信验证码，更新为已使用
        /// </summary>
        /// <returns></returns>
        public void UpdateVeriCodeIsEmploy(UserRegisterModel model)
        {
            var vCode = verificationCodeRepository.GetModel(model.Mobile, model.MobileMeessageCode);

            if (vCode != null)
            {
                vCode.IsEmploy = true;

                verificationCodeRepository.Update(vCode);
            }
        }

        /// <summary>
        ///     判断该号码是否在2分钟内重复发送
        /// </summary>
        /// <param name="phone"></param>
        /// <returns></returns>
        public bool OneMinutes(string phone)
        {
            return verificationCodeRepository.OneMinutes(phone);
        }

        /// <summary>
        ///     添加一条短信验证码
        /// </summary>
        /// <returns></returns>
        public bool AddVerificationCod(UserVerificationCode vcodeModel)
        {
            vcodeModel.Id = verificationCodeRepository.Add(vcodeModel);

            return vcodeModel.Id > 0 ? true : false;
        }


        public UserFx GetByNamebeltStatus(int fxUserId, string key, bool isMobile = true)
        {
            return repository.GetByNamebeltStatus(fxUserId, key, isMobile);
        }

        public UserFx GetByNamebeltStatusV1(int fxUserId, string key, bool isMobile = true)
        {
            return repository.GetByNamebeltStatusV1(fxUserId, key, isMobile);
        }

        /// <summary>
        /// 获取用户基本信息
        /// </summary>
        /// <param name="id"></param>
        /// <param name="isSubAccount"></param>
        /// <returns></returns>
        public UserFxModel GetUserFxModelById(int id, bool isSubAccount)
        {
            var model = repository.GetUserFxModelById(id, isSubAccount);
            if (model != null)
            {
                model.IsSubAccount = isSubAccount;
                model.FewDaysLaterChangePhone = GetFewDaysLaterChangePhone(id, isSubAccount);
            }
            return model;
        }

        /// <summary>
        ///     获取用户基本设置带地址库
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public UserFx GetUserFxAddressInfo(int fxUserId,bool needEncryptAccount = false)
        {
            var result = repository.GetUserFxAddressInfo(fxUserId);

            if (needEncryptAccount && SiteContext.IsSubAccount() && !SiteContext.HasPermission(FxPermission.SupplierAccount))
            {
                result.NickName = Utility.SubAccount.EncryptUtil.EncryptAccount(result.NickName);
                result.Mobile = Utility.SubAccount.EncryptUtil.EncryptAccount(result.Mobile);
            }
            return result;
        }

        public void UpdateFxUser(int fxUserId, string nickName, string avatarUrl)
        {
            repository.UpdateFxUser(fxUserId, nickName, avatarUrl);
        }

        public void UpdateFxUser(int fxUserId, string riskStatus, bool isSubUser)
        {
            repository.UpdateFxUser(fxUserId, riskStatus, isSubUser);
        }

        public IEnumerable<UserFx> GetUserFxs(IEnumerable<int> fxUserIds)
        {
            if (fxUserIds == null || fxUserIds.Any() == false)
                return new List<UserFx>();
            return repository.GetUserFxs(fxUserIds);
        }

        public UserFx GetUserFx(int fxUserId)
        {
            if (fxUserId == null || fxUserId == 0)
                return new UserFx();
            return repository.GetUserFx(fxUserId);
        }

        /// <summary>
        ///     获取用户相关信息
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<FxUserNickMobileModel> GetFxUserNickMobiles(List<int> fxUserIds)
        {
            if (fxUserIds == null || fxUserIds.Any() == false)
                return new List<FxUserNickMobileModel>();
            return repository.GetFxUserNickMobiles(fxUserIds);
        }

        /// <summary>
        ///     判断用户名是否已经被使用
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public bool IsExistNickName(int fxUserId, string key)
        {
            return repository.IsExistNickName(fxUserId, key);
        }

        public UserFx GetUserFxByMobile(string mobile)
        {
            return repository.GetByMobile(mobile);
        }

        /// <summary>
        ///     根据手机号获取用户数据（兼容子账号）
        /// </summary>
        /// <param name="mobile"></param>
        /// <returns></returns>
        public Tuple<UserFx, SubUserFx> GetUserFxByMobileV2(string mobile)
        {
            var userFx = repository.GetByMobile(mobile);
            SubUserFx subUserFx = null;
            if (userFx == null)
            {
                subUserFx = new SubUserFxService().GetByMobile(mobile);
                if (subUserFx != null)
                {
                    //主账号
                    userFx = repository.Get(subUserFx.MainFxUserId);
                    if (userFx == null)
                        subUserFx = null;
                }
            }

            return Tuple.Create(userFx, subUserFx);
        }

        public UserFx GetWithFxDbConfigByMobile(string mobile)
        {
            return repository.GetWithFxDbConfigByMobile(mobile);
        }

        public UserFx GetWithFxDbConfigByFxUserId(int fxUserId)
        {
            return repository.GetWithFxDbConfigByFxUserId(fxUserId);
        }

        /// <summary>
        ///     创建虚拟用户，供虚拟厂家使用
        /// </summary>
        /// <returns></returns>
        public int CreateVirtualFxUser(string name, int fxUserId)
        {
            //查找是否有重复的虚拟用户
            var isExist = repository.IsExistVirtualSupplierNickName(fxUserId, name);
            if (isExist)
                throw new LogicException("您要添加的虚拟厂家名称已存在，请更换其他名称");
            var guid = Guid.NewGuid().ToString().ToShortMd5();
            var fxUser = new UserFx
            {
                NickName = name,
                Name = name,
                Mobile = guid,
                Password = DES.EncryptUrl("123456w", CustomerConfig.LoginCookieEncryptKey),
                CreateTime = DateTime.Now,
                Status = 0,
                Remark = "虚拟用户"
            };
            var id = repository.Add(fxUser);
            return id;
        }

        /// <summary>
        ///     根据店铺的平台店铺ID来获取分销用户(消息处理使用)
        /// </summary>
        /// <param name="psid">平台店铺ID</param>
        /// <param name="sid">系统店铺ID</param>
        /// <param name="platformType">平台店铺类型</param>
        /// <returns>Item1:分销用户，Item2:平台店铺，Item3:分销用户的系统店铺</returns>
        public List<Tuple<UserFx, Shop>> GetUserFxAndShopsByShopIds(IEnumerable<string> psid, IEnumerable<int> sid,
            string platformType = "")
        {
            List<Tuple<UserFx, Shop>> result = null;

            //缓存Key
            var cacheKey = string.Empty;
            if (psid != null && psid.Any())
            {
                psid = psid.OrderBy(f => f);
                cacheKey = string.Join(",", psid);

                if (!string.IsNullOrWhiteSpace(platformType))
                    cacheKey += $"_{platformType}";
            }

            if (sid != null && sid.Any())
            {
                sid = sid.OrderBy(f => f);
                cacheKey = string.Join(",", sid);
            }

            ObjectCache cache = MemoryCache.Default;
            //取缓存
            if (cache.Contains(cacheKey)) result = cache[cacheKey] as List<Tuple<UserFx, Shop>>;
            //缓存取不到，查询数据库
            var isFromDb = false;
            if (result == null)
            {
                result = repository.GetUserFxAndShopsByShopIds(psid, sid, platformType);
                isFromDb = true;
                //将结果存储到缓存
                var policy = new CacheItemPolicy
                {
                    AbsoluteExpiration = DateTime.Now.AddMinutes(30)
                };
                cache.Set(cacheKey, result, policy);
            }

            //2.查询出店铺的同步状态
            var sids = result.Where(f => f.Item2 != null).Select(f => f.Item2.Id).Where(f => f > 0).Distinct().ToList();
            if (sids.Any())
            {
                var syncStatus = _syncStatusService.GetByShopId(sids);
                result.ForEach(item =>
                {
                    if (item.Item2 != null)
                    {
                        var syncStatusList = syncStatus.Where(f => f.ShopId == item.Item2.Id);
                        if (item.Item2.SyncStatusList == null)
                            item.Item2.SyncStatusList = new List<SyncStatus>();
                        if (syncStatusList.Any())
                            item.Item2.SyncStatusList.AddRange(syncStatusList);
                    }
                });
            }

            //从缓存中查询的店铺信息，需要将授权信息重新查询下
            if (isFromDb == false)
            {
                var shops = result.Select(x => x.Item2).ToList();
                if (shops.Any())
                {
                    var shopIds = shops.Select(x => x.Id).Distinct().ToList();
                    var shopExtensionRepository = new ShopExtensionRepository();
                    var shopExtensions = shopExtensionRepository.GetShopExtensionByShopIds(shopIds);
                    shopExtensions.ForEach(x =>
                    {
                        var shop = shops.FirstOrDefault(s => s.Id == x.ShopId);
                        if (shop != null)
                            shop.ShopExtension = x;
                    });
                }
            }

            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sid"></param>
        /// <param name="platformType">为TikTok时查P_FxUserForeignShop表，其他情况查P_FxUserShop表，本身不做为查询条件</param>
        /// <returns></returns>
        public int GetFxUserIdByShopId(int sid, string platformType = "")
        {
            return repository.GetFxUserIdByShopId(sid, platformType);
        }

        public int GetFxUserIdByShopId_TikTok(int sid)
        {
            return repository.GetFxUserIdByShopId_TikTok(sid);
        }


        public void ResetRetryTimes(int id)
        {
            repository.ResetRetryTimes(id);
        }

        public int GetSystemShopIdByFxUserId(int fxUserId)
        {
            return repository.GetSystemShopIdByFxUserId(fxUserId);
        }

        public Dictionary<string, UserFx> GetAgentByPathFlowCodes(List<string> pathFlowCodes)
        {
            return repository.GetAgentByPathFlowCodes(pathFlowCodes);
        }

        public Dictionary<int, List<UserFx>> GetAgentsByShopIds(List<int> shopIds, int fxUserId)
        {
            return repository.GetAgentsByShopIds(shopIds, fxUserId);
        }

        /// <summary>
        /// 获取用户信息 按ID列表 且缓存（缓存键：ErpWeb/UserFx/{0}）
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<UserFx> GetListByIdsWithCache(List<int> ids)
        {
            return repository.GetListByIdsWithCache(ids);
        }

        /// <summary>
        ///     针对各个平台创建不同的【收件人数据库配置信息】，不存在才添加
        /// </summary>
        /// <param name="fxUserId"></param>
        public void TryToCreateCloudReceDbConfig(int fxUserId)
        {
            var cloudPts = new List<string>
            {
                CloudPlatformType.Alibaba.ToString(), CloudPlatformType.Pinduoduo.ToString(),
                CloudPlatformType.Jingdong.ToString(), CloudPlatformType.TouTiao.ToString()
            };
            foreach (var cpt in cloudPts)
            {
                for (var i = 0; i < 3; i++)
                    try
                    {
                        _receDbConfigRepository.GetDefaultDbConfigModel(fxUserId, cpt);
                        break;
                    }
                    catch (Exception ex)
                    {
                        Log.WriteError($"TryToCreateCloudReceDbConfig创建ReceDbConfig时发生错误，会员Id:{fxUserId}，错误详情：{ex}");
                        Thread.Sleep(500);
                    }
            }
        }

        /// <summary>
        ///     初始化相关数据
        /// </summary>
        /// <param name="fxuserId"></param>
        /// <param name="platformType"></param>
        public void InitFxUser(int fxuserId, PlatformType platformType)
        {
            var fxUserShops = new FxUserShopService().GetShopsByFxUserId(fxuserId, false);
            if (fxUserShops == null || !fxUserShops.Any())
                throw new LogicException("数据异常，绑定失败，请联系客服");

            fxUserShops = fxUserShops.Where(a => a.PlatformType == platformType.ToString()).ToList();

            try
            {
                var _fxUserShopService = new FxUserShopService();
                var _syncStatusService = new SyncStatusService();
                var _syncTaskService = new SyncTaskService();

                fxUserShops.ForEach(s =>
                {
                    var shopId = s.ShopId;

                    ////1.绑定关系
                    //_fxUserShopService.AddFxUserShop(new FxUserShop()
                    //{
                    //    FxUserId = fxuserId,
                    //    ShopId = shopId,
                    //    PlatformType = shop.PlatformType,
                    //    NickName = shop.NickName,
                    //    CreateTime = DateTime.Now,
                    //    AuthTime = shop.ExpireTime ?? DateTime.Now,
                    //    Status = FxUserShopStatus.Binded
                    //});

                    //2.保存店铺同步状态数据
                    //订单同步状态
                    _syncStatusService.AddSyncStatus(new SyncStatus
                    {
                        FxUserId = fxuserId,
                        ShopId = shopId,
                        SyncType = ShopSyncType.Order,
                        CreateTime = DateTime.Now,
                        Source = OwnerSource.FenDanSystem.ToString()
                    });

                    //产品同步状态
                    _syncStatusService.AddSyncStatus(new SyncStatus
                    {
                        FxUserId = fxuserId,
                        ShopId = shopId,
                        SyncType = ShopSyncType.Product,
                        CreateTime = DateTime.Now,
                        Source = OwnerSource.FenDanSystem.ToString()
                    });

                    //3.同步任务
                    _syncTaskService.AddTask(new SyncTask
                    {
                        FxUserId = fxuserId,
                        ShopId = shopId,
                        TaskType = SyncTaskType.Order,
                        Status = SyncTaskStatus.Wait,
                        CreateTime = DateTime.Now,
                        Source = OwnerSource.FenDanSystem.ToString()
                    });


                    //4.1售后同步状态
                    _syncStatusService.AddSyncStatus(new SyncStatus
                    {
                        FxUserId = fxuserId,
                        ShopId = shopId,
                        SyncType = ShopSyncType.AfterSale,
                        CreateTime = DateTime.Now,
                        Source = OwnerSource.FenDanSystem.ToString()
                    });

                    //4.2售后同步任务
                    _syncTaskService.AddTask(new SyncTask
                    {
                        FxUserId = fxuserId,
                        ShopId = shopId,
                        TaskType = SyncTaskType.AfterSale,
                        Status = SyncTaskStatus.Wait,
                        CreateTime = DateTime.Now,
                        Source = OwnerSource.FenDanSystem.ToString()
                    });
                });
            }
            catch (Exception ex)
            {
                Log.WriteError(
                    $"ShopId={string.Join(",", fxUserShops.Select(a => a.ShopId))},和FxUserId={fxuserId},绑定关系失败,错误消息：{ex.Message}");
                throw new LogicException("绑定失败，请联系客服");
            }
        }

        public UserFx GetBkUserFx(int id)
        {
            return repository.GetBkUserFx(id);
        }

        public List<FxUserShop> GetBkFxUserShops(int fxUserId)
        {
            return repository.GetBkFxUserShops(fxUserId);
        }


        /// <summary>
        ///     获取用户所有订购app信息
        /// </summary>
        /// <param name="fxuserid"></param>
        /// <param name="platform"></param>
        /// <returns></returns>
        public List<AppPlatformModel> UserApp(int fxuserid)
        {
            List<AppPlatformModel> result = null;
            var apps = repository.getUserNotExpireApp(fxuserid);
            if (apps == null)
                return result;
            result = new List<AppPlatformModel>();
            foreach (var app in apps)
            {
                var appKey = app.ServiceAppId;
                // 新加店铺限制只能使用抖店新的分销应用
                if (app.PlatformType == PlatformType.TouTiao.ToString() &&
                    app.ServiceAppId != CustomerConfig.TouTiaoFxNewAppKey)
                    appKey = CustomerConfig.TouTiaoFxNewAppKey;
                var appmodel = AppServiceFactory.GetAppService(app.PlatformType)?.getAppModel(appKey);
                if (appmodel != null && !result.Exists(e => e.Appkey == appmodel.Appkey))
                    result.Add(appmodel);
            }

            return result;
        }

        public List<UserFx> GetUserFxIncludeFxShops(List<int> ids)
        {
            return repository.GetUserFxIncludeFxShops(ids);
        }

        public List<UserFx> GetUserFxIncludeFxShopsByTikTok(List<int> ids)
        {
            return repository.GetUserFxIncludeFxShopsByTikTok(ids);
        }

        public int CheckFxUserHasTwoApp(int fxUserId)
        {
            return repository.CheckFxUserHasTwoApp(fxUserId);
        }

        /*
            缓存性能优化:覆盖原来直通base的操作
         */
        /// <summary>
        ///     获取分单系统用户详细信息
        /// </summary>
        /// <param name="fxUserId">id</param>
        /// <returns></returns>
        public new UserFx Get(int fxUserId)
        {
            return repository.Get(fxUserId);
        }

        public List<UserFx> GetDouDianUserFxs()
        {
            return repository.GetDouDianUserFxs();
        }

        /// <summary>
        ///     根据Ids查询
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="selectFields"></param>
        /// <returns></returns>
        public List<UserFx> GetsByIds(List<int> ids, string selectFields = null)
        {
            return repository.GetsByIds(ids, selectFields);
        }

        /// <summary>
        ///     获取用户UserFlag
        /// </summary>
        /// <param name="fxUserId"></param>
        public string GetUserFlag(int fxUserId)
        {
            return repository.GetUserFlag(fxUserId);
        }

        /// <summary>
        ///     是否有旧京东应用授权还未到期的（用于前端是否显示双应用入口）
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool CheckFxUserHasOldJingdongAppUnExpired(int fxUserId)
        {
            return repository.CheckFxUserHasOldJingdongAppUnExpired(fxUserId);
        }
        
        /// <summary>
        /// 是否展示微信小店双应用
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool CheckWxVideoUser(int fxUserId)
        {
            return repository.CheckWxVideoUser(fxUserId);
        }

        public UserFx GetByMobile(string mobile)
        {
            var userfx = repository.GetByMobile(mobile);
            return userfx;
        }

        /// <summary>
        ///     通过手机号获取用户信息
        /// </summary>
        /// <param name="mobiles"></param>
        /// <returns></returns>
        public List<UserFx> GetListByMobiles(List<string> mobiles)
        {
            return repository.GetListByMobile(mobiles);
        }

        ///// <summary>
        ///// </summary>
        ///// <param name="keys"></param>
        ///// <param name="shopId"></param>
        ///// <returns></returns>
        //public List<CommonSetting> GetRedisSettings(List<string> keys, int shopId)
        //{
        //    var values = _redis.Gets(keys, shopId);
        //    return values;
        //}

        /// <summary>
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <returns></returns>
        public bool IsWhiteUser(int fxUserId)
        {
            if (fxUserId <= 0) return false;

            var flag = repository.GetUserFlag(fxUserId).ToString2();
            return flag.Contains(UserFxRepository.WhiteUserFlag);
        }

        /// <summary>
        /// 获取系统店铺，按用户列表，并缓存
        /// </summary>
        /// <param name="fxUserIds"></param>
        /// <returns></returns>
        public List<Shop> GetSystemShopsByFxUserIdsWithCache(List<int> fxUserIds)
        {
            return repository.GetSystemShopsByFxUserIdsWithCache(fxUserIds);
        }
        /// <summary>
        /// 获取几天后可以换手机号
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="isSubAccount"></param>
        /// <returns></returns>
        public int GetFewDaysLaterChangePhone(int userId, bool isSubAccount)
        {
            var redisKey = CacheKeys.ReplaceMobileDateKey.Replace("{fxUserId}", isSubAccount ? "sub_" + userId.ToString() : userId.ToString());
            var lastChangePhoneTime = RedisHelper.Get<DateTime?>(redisKey);
            if (lastChangePhoneTime.HasValue)
            {
                //30天后才可以更换手机号
                if ((DateTime.Now - lastChangePhoneTime.Value).TotalDays >= 30)
                {
                    return 0;
                }
                else
                {
                    return 30 - (int)(DateTime.Now - lastChangePhoneTime.Value).TotalDays;
                }
            } 
            return 0;
        }

        /// <summary>
        /// 设置最后一次换手机号时间
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="isSubAccount"></param>
        public void SetLastChangePhoneTime(int userId, bool isSubAccount)
        {
            var redisKey = CacheKeys.ReplaceMobileDateKey.Replace("{fxUserId}", isSubAccount ? "sub_" + userId.ToString() : userId.ToString());
            RedisHelper.Set(redisKey, DateTime.Now);
        }

        /// <summary>
        /// 校验获取验证码次数是否超过限制
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="isSubAccount"></param>
        /// <param name="phone"></param>
        /// <param name="type">类型 1 旧手机号 2 新手机号</param>
        /// <param name="maxCount">最大获取次数</param>
        /// <returns></returns>
        public bool CheckGetValidCodeExceed(int userId, bool isSubAccount, string phone, int type,int maxCount = 10)
        {
            var redisKey = string.Empty;
            if (type ==1)
            {
                //旧手机号
                redisKey = CacheKeys.ReplaceMobileOldValidCodeKey.Replace("{fxUserId}", isSubAccount ? "sub_" + userId.ToString() : userId.ToString());
            }
            else
            {
                //新手机号
                redisKey = CacheKeys.ReplaceMobileNewValidCodeKey.Replace("{fxUserId}", isSubAccount ? "sub_" + userId.ToString() : userId.ToString());
            }
            redisKey = redisKey.Replace("{mobile}", phone);
            var count = RedisHelper.IncrBy(redisKey);
            if (count> maxCount)
            {
                //超过次数
                return true;
            }
            //设置过期时间 24小时 24 * 60 * 60
            if (count == 1)
                RedisHelper.Expire(redisKey, TimeSpan.FromSeconds(24 * 60 * 60));
            return false;
        }

        /// <summary>
        /// 换绑手机号
        /// </summary>
        /// <param name="fxUserId"></param>
        /// <param name="shopId"></param>
        /// <param name="phone"></param>
        public void ChangeBandPhone(int fxUserId, int shopId,string phone)
        {
            repository.ChangeBandPhone(fxUserId, shopId, phone);
            //更新缓存
            FxCaching.RefeshCache(FxCachingType.FxUser, fxUserId);
            FxCaching.RefeshCache(FxCachingType.SystemShops, fxUserId);
            FxCaching.RefeshCache(FxCachingType.FxShopSelf, fxUserId);
        }

        /// <summary>
        /// 是否需要将子账号合并到主账号
        /// </summary>
        /// <returns></returns>
        public MergeSubUserModel IsNeedMergeSubUser()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            //获取提醒配置
            //0 需要提醒，1 已处理 2 暂不处理 3 不提醒(代码检查到不需要提醒)
            var reminderType = _commonSettingService.GetMergeSubUserReminder(shopId);
            if (reminderType > 0)
                return new MergeSubUserModel(false);
            //检查主账号
            var mainFxUserId = SiteContext.CurrentNoThrow.CurrentFxUserId;
            var user = repository.GetUserFxModelById(mainFxUserId, false); 
            if (user == null)
                throw new LogicException("数据异常");
            if (user.BandWx)
            {
                //主账号已经绑定了微信，不需要继续检查
                _commonSettingService.SetMergeSubUserReminder(shopId, 3);
                return new MergeSubUserModel(false); 
            }
            //查询子账号
            var model = new SubAccountSearchModel
            {
                MainFxUserId = mainFxUserId,
                Status = 1,//启用
                BindMethod = 2//绑定了微信
            };
            var subUsers = subUserFxService.GetsByConditions(model);
            if (subUsers.IsNullOrEmptyList() || subUsers.Count > 1)
            {
                //没有子账号，或者子账号大于1个，不需要继续检查
                _commonSettingService.SetMergeSubUserReminder(shopId, 3);
                return new MergeSubUserModel(false);
            }
            var subUser = subUsers.First();
            if (subUser.IsBindMobile)
            {
                //子账号已经绑定了手机，不需要继续检查
                _commonSettingService.SetMergeSubUserReminder(shopId, 3);
                return new MergeSubUserModel(false);
            }
            //查询子账号职位权限
            var postFx = new PostFxService().GetByPostCode(subUser.PostCode);
            if (postFx == null || postFx.PostName != "高级管理员" || postFx.PostType != 1)
            {
                //子账号不是高级管理员，不需要继续检查
                _commonSettingService.SetMergeSubUserReminder(shopId, 3);
                return new MergeSubUserModel(false);
            }
            //需要用户确认是否将子账号合并到主账号
            var mainUserMobile = SiteContext.CurrentNoThrow.CurrentFxUser.Mobile;
            var subUserWxNick = subUser.WxNickName;
            return new MergeSubUserModel(true, mainUserMobile, subUserWxNick);
        }

        /// <summary>
        /// 合并子账号
        /// </summary>
        public void MergeSubUser()
        {
            var mainFxUserId = SiteContext.CurrentNoThrow.CurrentFxUserId;
            var user = GetUserFx(mainFxUserId);
            var _fxwechatuserService = new FxWeChatUserService();
            //获取子账号
            var subUsers = subUserFxService.GetsByMainFxUserId(mainFxUserId);
            var subUserFx= subUsers.FirstOrDefault(t=>t.Status == 1);
            if (subUserFx == null)
                throw new LogicException("数据异常");
            //解绑微信
            var wxuser = _fxwechatuserService.GetWeChatUserByOpenId(subUserFx.WxOpenId);
            if (wxuser == null)
                throw new LogicException("数据异常");
            wxuser.UnBind = 1;
            _fxwechatuserService.UpdateWeChatUnBind(wxuser);
            //将微信绑定到主账号上
            wxuser.FxUserId = user.Id;
            wxuser.SubFxUserId = 0;//子账号
            wxuser.Mobile = user.Mobile;
            wxuser.UnBind = 0;
            user.WxOpenId = subUserFx.WxOpenId;
            if (!Update(user)) 
                throw new LogicException("主账号关联子账号微信失败！");
            var index = _fxwechatuserService.UpdateWeChatUserFx(wxuser);
            if (index < 1)
                throw new LogicException("主账号关联子账号微信失败！");
            //删除子账号
            subUserFx.WxOpenId = null;
            subUserFx.WxNickName = null;
            subUserFx.LastUpdateTime = DateTime.Now;
            subUserFx.IsDeleted = true;
            var updateResult = subUserFxService.Update(subUserFx);
            if (updateResult && subUserFx.IsDeleted)
            {
                //清除登录信息
                subUserFxService.ClearLoginAuth(subUserFx.Id);
                //清除子账号与职位的关联关系
                new UserFxPostRelationService().DeleteByFxUserId(subUserFx.Id);
            }
        }
    }
}