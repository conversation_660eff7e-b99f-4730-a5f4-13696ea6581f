using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Repository;
using System.Collections.Generic;
using System.Linq;

namespace DianGuanJiaApp.Services.Services
{
    public class WaybillCodeChildService : BaseService<WaybillCodeChild>
    {
        private WaybillCodeChildRepository _repository;
        public WaybillCodeChildService()
        {
            _repository = new WaybillCodeChildRepository();
            base._baseRepository = _repository;
        }

        public WaybillCodeChildService(string connectionString) : base(connectionString)
        {
            _repository = new WaybillCodeChildRepository(connectionString);
        }

        /// <summary>
        /// 获取信息为复制副本，按ParentUniqueKey
        /// </summary>
        /// <param name="codes"></param>
        /// <param name="selectFieldNames"></param>
        /// <param name="whereFieldName"></param>
        /// <returns></returns>
        public List<WaybillCodeChild> GetListForDuplication(List<string> codes, string selectFieldNames = "*",
            string whereFieldName = "ParentUniqueKey")
        {
            if (codes == null || !codes.Any())
                return new List<WaybillCodeChild>();
            return _repository.GetListForDuplication(codes, selectFieldNames, whereFieldName);
        }

        /// <summary>
        /// 批量插入数据为复制副本
        /// </summary>
        /// <param name="models"></param>
        public void InsertsForDuplication(List<WaybillCodeChild> models)
        {
            if (models == null || !models.Any())
                return;
            //清理源库ID
            models.ForEach(m => { m.Id = 0; });
            //代码
            var codes = models.Select(m => m.ParentUniqueKey).Distinct().ToList();
            //存在的代码列表
            var idAndCodes = _repository.GetExistIdAndCodes(codes);
            //全部不存在
            if (idAndCodes == null || !idAndCodes.Any())
            {
                //_repository.BulkInsert(models);
                baseRepository.BulkWrite(models, "P_WaybillCodeChild");
                return;
            }

            //存在
            var updates = models.Where(m =>
                idAndCodes.Any(o => o.Code == m.ParentUniqueKey && o.ItemCode == m.ChildWaybillCode)).ToList();
            if (updates.Any())
            {
                updates.ForEach(o =>
                {
                    var model = idAndCodes.FirstOrDefault(m =>
                        m.Code == o.ParentUniqueKey && m.ItemCode == o.ChildWaybillCode);
                    if (model == null)
                    {
                        return;
                    }

                    o.Id = model.Id;
                });
                _repository.BulkUpdate(updates);
            }

            //不存在
            var inserts = models.Where(m =>
                !idAndCodes.Any(o => o.Code == m.ParentUniqueKey && o.ItemCode == m.ChildWaybillCode)).ToList();
            if (inserts.Any())
            {
                //_repository.BulkInsert(inserts);
                baseRepository.BulkWrite(inserts, "P_WaybillCodeChild");
            }
        }
    }
}