using DianGuanJiaApp.Utility;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Services
{
    /// <summary>
    /// Redis单桶读取和保存
    /// 读取和更新Redis中的Bucket
    /// 这里对象属性硬编码了,失去了灵活性但是比反射性能要好,因为Bucket算法比较稳定所以硬编码问题不大
    /// 但是也需要了解这里对Bucket的依赖,一旦Bucket有变化这里可能也需要变化
    /// </summary>
    public class RedisSingleBucket : IBucketManager
    {
        private string _key { get; set; }
        public RedisSingleBucket()
        {
            if (!BucketState()) throw new Exception("桶为开放");                        
        }
        public RedisSingleBucket(string partition)
        {
            if (!BucketState()) throw new Exception("桶为开放");
            _key = "bucket" + partition;
        }
        /// <summary>
        /// 读取单桶信息
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public IBucket GetBucket(string key = null)
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException("key不能为空");
            HashEntry[] hashs;
            using (var conn = RedisPool.GetConnection())
            {
                if(conn != null)
                {
                    hashs = conn.GetDatabase().HashGetAll(key);
                }
                else
                    throw new Exception("Redis连接失败");
            }
            if (hashs == null || hashs.Length <= 0) hashs = Init(key);
            Bucket bucket = new Bucket();
            for (int i = 0; i < hashs.Length; i++)
            {
                switch (hashs[i].Name)
                {
                    case "Capacity": bucket.Capacity = (long)hashs[i].Value; break;
                    case "Rate": bucket.Rate = (double)hashs[i].Value; break;
                    case "LastTime": bucket.LastTime = (long)hashs[i].Value; break;
                    case "Token": bucket.Token = (long)hashs[i].Value; break;
                }
            }
            return bucket;
        }

        /// <summary>
        /// 更新单桶信息
        /// </summary>
        /// <param name="key"></param>
        /// <param name="ibucket"></param>
        /// <returns></returns>
        public bool SetBucket(IBucket ibucket, string key = null)
        {
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException("key不能为空");
            if (ibucket == null) throw new ArgumentNullException("IBucket不能为Null");
            Bucket bucket;
            try
            {
                bucket = ibucket as Bucket;
            }
            catch
            {
                throw new ArgumentException("IBucket不是一个Bucket单桶");
            }
            using (var conn = RedisPool.GetConnection())
            {
                conn?.GetDatabase().HashSet(key, "LastTime", bucket.LastTime);
                conn?.GetDatabase().HashSet(key, "Token", bucket.Token);                
            }
            return true;
        }

        /// <summary>
        /// 当Redis中不存在key对应的单桶时,进行默认初始化
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        private HashEntry[] Init(string key = null)
        {
            if (string.IsNullOrEmpty(key)) key = _key;
            if (string.IsNullOrEmpty(key)) throw new ArgumentNullException("key不能为空");
            Bucket bucket = new Bucket();
            bucket.Capacity = 15000;
            bucket.Rate = 2;
            bucket.LastTime = RedisHelp.TimeToUnix();
            bucket.Token = 5000;
            HashEntry Capacity = new HashEntry("Capacity", bucket.Capacity);
            HashEntry Rate = new HashEntry("Rate", bucket.Rate);
            HashEntry LastTime = new HashEntry("LastTime", bucket.LastTime);
            HashEntry Token = new HashEntry("Token", bucket.Token);
            HashEntry[] he = { Capacity, Rate, LastTime, Token };
            using (var conn = RedisPool.GetConnection())
            {
                conn?.GetDatabase().HashSet(key, he);
            }
            return he;
        }

        public bool BucketState()
        {
            CommonSettingService setting = new CommonSettingService();
            return setting.IsBucketToken();
        }
    }
}
