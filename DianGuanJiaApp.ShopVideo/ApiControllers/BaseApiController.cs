using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace DianGuanJiaApp.ShopVideo.ApiControllers
{
    [DefaultApiAuthorizationFilter]
    public class BaseApiController : ApiController
    {
        /// <summary>
        /// 返回结果
        /// </summary>
        /// <param name="message"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected AjaxResult<T> SuccessResult<T>(T data, string message = "成功！")
        {
            return new AjaxResult<T>()
            {
                Success = true,
                Data = data,
                Message = message
            };
        }

        /// <summary>
        /// 返回结果
        /// </summary>
        /// <param name="message"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        protected AjaxResult<T> FailedResult<T>(T data, string message)
        {
            return new AjaxResult<T>()
            {
                Success = false,
                Data = data,
                Message = message
            };
        }

        /// <summary>
        /// 返回结果
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        protected AjaxResult<T> FailedResult<T>(T data)
        {
            return new AjaxResult<T>()
            {
                Success = false,
                Data = data,
                Message = data is string ? data.ToString() : "失败!"
            };
        }

        #region 校验越权

        protected void CheckDataPermission(int shopId)
        {
            if (shopId > 0)
            {
                var userShops = SiteContext.Current.UserShops;
                if (userShops == null || userShops.FirstOrDefault(t => t.Id == shopId && t.PlatformType == PlatformType.TouTiao.ToString()) == null)
                    throw new LogicException("指定店铺不属于当前的账号！");
            }
        }

        protected void CheckDataPermission(List<int> shopIds)
        {
            if (shopIds.IsNotNullAndAny())
            {
                var userShops = SiteContext.Current.UserShops;
                if (userShops.IsNullOrEmptyList())
                    throw new LogicException("指定店铺不属于当前的账号！");
                var userShopIds = userShops
                    .Where(t => t.PlatformType == PlatformType.TouTiao.ToString())
                    .Select(t => t.Id).ToList();
                if (userShopIds == null || !shopIds.All(userShopIds.Contains))
                    throw new LogicException("指定店铺不属于当前的账号！");
            }
        }

        #endregion 校验越权
    }
}