<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{776BDADB-85E8-4C5B-9B21-3C9235A36A79}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DianGuanJiaApp.SiteMessage</RootNamespace>
    <AssemblyName>DianGuanJiaApp.SiteMessage</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="EntityExtension\Message.cs" />
    <Compile Include="Entity\ReadedMessage.cs" />
    <Compile Include="Entity\MessageTemplate.cs" />
    <Compile Include="Entity\Message.cs" />
    <Compile Include="Model\BaseSiteMessageRequest.cs" />
    <Compile Include="Model\BaseSiteMessageRespone.cs" />
    <Compile Include="Model\Request\MessageClearCecheRequest.cs" />
    <Compile Include="Model\Request\MessageClearRequest.cs" />
    <Compile Include="Model\Request\MessageDetailRequest.cs" />
    <Compile Include="Model\Request\MessageGetUnreadCountRequest.cs" />
    <Compile Include="Model\Request\MessagePublishRequest.cs" />
    <Compile Include="Model\Request\MessageListRequest.cs" />
    <Compile Include="Model\Request\MessageSetReadRequest.cs" />
    <Compile Include="Model\Request\PublicMessageListRequest.cs" />
    <Compile Include="Model\Request\PublicMessageSaveRequest.cs" />
    <Compile Include="Model\Response\MessageDetailResponse.cs" />
    <Compile Include="Model\Response\MessageGetUnreadCountResponse.cs" />
    <Compile Include="Model\Response\MessagePublishResponse.cs" />
    <Compile Include="Model\Response\MessageSetReadResponse.cs" />
    <Compile Include="Model\Response\PublicMessageListResponse.cs" />
    <Compile Include="Model\Result\MessageResult.cs" />
    <Compile Include="Model\Response\MessageListResponse.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Repository\ReadedMessageRepository.cs" />
    <Compile Include="Repository\MessageTemplateRepository.cs" />
    <Compile Include="Repository\MessageRepository.cs" />
    <Compile Include="Sdk\BaseRequest.cs" />
    <Compile Include="Sdk\Helpers\TimeStampHelper.cs" />
    <Compile Include="Sdk\ISiteMessageClient.cs" />
    <Compile Include="Sdk\ISiteMessageRequest.cs" />
    <Compile Include="Sdk\SiteMessageClient.cs" />
    <Compile Include="Services\BaseService.cs" />
    <Compile Include="Services\MessageService.cs" />
    <Compile Include="Services\PublicMessageService.cs" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="CodeProject.ObjectPool, Version=3.0.0.0, Culture=neutral, PublicKeyToken=2f204b7110a52060, processorArchitecture=MSIL">
      <HintPath>..\..\packages\CodeProject.ObjectPool.3.2.4\lib\net461\CodeProject.ObjectPool.dll</HintPath>
    </Reference>
    <ProjectReference Include="..\DianGuanJiaApp.Data\DianGuanJiaApp.Data.csproj">
      <Project>{6c8b9657-fbaa-46c8-8433-1b2f908e8398}</Project>
      <Name>DianGuanJiaApp.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Utility\DianGuanJiaApp.Utility.csproj">
      <Project>{a0ead1ef-8b94-4487-ad73-f981cb45e58f}</Project>
      <Name>DianGuanJiaApp.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="..\taobao-sdk-net-auto_1554290167233-20190926-source\TopSdk.csproj">
      <Project>{9C11CAE5-5188-4E71-825B-68FC3135728A}</Project>
      <Name>TopSdk</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Enum\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>