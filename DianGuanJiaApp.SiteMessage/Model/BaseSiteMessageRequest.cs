using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.SiteMessage.Sdk;

namespace DianGuanJiaApp.SiteMessage.Model
{
    public abstract class BaseSiteMessageRequest<T> : ISiteMessageRequest<T> where T : BaseSiteMessageRespone
    {
        /// <summary>
        /// 来源系统：0分单系统，1打单系统，2铺货系统
        /// </summary>
        public int Source { get; set; }

        public string RequestId { get; set; }

        public virtual void Check()
        {
        }
        public abstract string GetApiName();

        /// <summary>
        /// 来源系统：0分单系统，1打单系统，2铺货系统
        /// </summary>
        public string SourceStr { 
            get {
                 var sourse = this.Source == 0 ? "Fx" :
                              this.Source == 1 ? "Fd" :
                              this.Source == 2 ? "Ph" : "";
                return sourse;
            } 
        }

    }
}
