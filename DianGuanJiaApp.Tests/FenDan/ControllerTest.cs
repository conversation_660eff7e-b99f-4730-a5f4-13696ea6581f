using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using DianGuanJiaApp.Data;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Services.ServicesExtension;
using System.Threading.Tasks;
using DianGuanJiaApp.Data.Model;
using System.Threading;
using DianGuanJiaApp.Data.Repository;
using System.Diagnostics;
using Z.Dapper.Plus;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.ErpWeb.Controllers;
using System.Text;
using DianGuanJiaApp.Data.Dapper;
using RabbitMQ.Client.Framing.Impl;
using System.Web.Mvc;

namespace DianGuanJiaApp.Tests.Data
{
    [TestClass]
    public class ControllerTest
    {
        private Shop _shop;
        private SiteContext _siteContext;
        private OrderRepository _orderRepository;
        private ShopRepository _shopRepository;
        private LogicOrderService _logicOrderService;
        private LogicOrderRepository _logicOrderRepository;

        [TestInitialize]
        public void Init()
        {
            RedisConfigService.Initialization();
            var rp = new DianGuanJiaApp.Data.Repository.UserFxRepository();
            var userFx = rp.Get(" where Id=@id", new { id = 5 })?.FirstOrDefault();
            _siteContext = new SiteContext(userFx, "AlibabaFenFaDB");
            //_siteContext = new SiteContext(userFx);
            _shop = _siteContext.CurrentLoginShop;
            _orderRepository = new OrderRepository();
            _shopRepository = new ShopRepository();
            _logicOrderRepository = new LogicOrderRepository();
            new LogForOperatorContext() { logInfo = new LogForOperator { } };
        }

        [TestMethod]
        public void TestSplitOrderWithNoLog()
        {
            var service = new OrderService();
            var shop = _shopRepository.Get(259);
            service.SplitLogicOrdersWithLog(shop, false);
            Console.ReadLine();
        }

        [TestMethod]
        public void Example()
        {
            var db = SiteContext.Current.CurrentDbConfig;
            //获取当前登录用户ID，用于查询订单分发相关的数据：我的厂家、我的商家、我的店铺（需排除系统店铺）、商品、订单、等。
            var fxUserId = SiteContext.Current.CurrentFxUserId;
            //获取当前系统店铺ID：用于查询从打单系统延续过来的数据： 快递模板、发货单模板、电子面单授权、底单记录、打印记录、发货记录、等。
            var systemShopId = SiteContext.Current.CurrentShopId;
        }


        [TestMethod]
        public void TestSplitOrder()
        {
            var sid = 1687;
            var pid = "5044784902707301534";
            var result = new NewOrderController().SyncSingleOrder(pid,sid);
        }

        [TestMethod]
        public void TestGetOrders()
        {
            var db = _orderRepository.DbConnection;
            var pids = db.Query<string>("SELECT top 1000 PlatformOrderID FROM dbo.P_Order WITH(NOLOCK) WHERE ShopId=@shopId", new { shopId = _shop.Id })
                .Select(pid => new OrderSelectKeyModel { PlatformOrderId = pid, ShopId = _shop.Id }).ToList();
        }

        [TestMethod]
        public void TestCopyOrder()
        {
            var configureDb = _shopRepository.DbConnection;
            var db = _orderRepository.DbConnection;
            //排除合并订单
            var orders = configureDb.Query<Order>("SELECT * FROM dbo.P_Order WITH(NOLOCK)  WHERE ShopId=1393 AND (ChildOrderId ='' OR MergeredOrderId IS NULL)");
            var orderItems = configureDb.Query<OrderItem>("SELECT * FROM dbo.P_OrderItem WITH(NOLOCK) WHERE ShopId=1393 AND OrignalOrderId IS NULL");
            var fxUserId = _siteContext.CurrentFxUserId;
            foreach (var oi in orderItems)
            {
                oi.UserId = fxUserId;
            }
            foreach (var order in orders)
            {
                order.OrderItems = orderItems?.Where(x => x.PlatformOrderId == order.PlatformOrderId).ToList();
                order.UserId = fxUserId;
            }
            //db.BulkInsert(orders);
            //db.BulkInsert(orderItems);
            //生成逻辑单，先每个订单生成一个逻辑单，不做拆单处理
            var dbNameId = _siteContext.CurrentDbConfig.DbNameConfig.Id;
            var logicOrders = new List<LogicOrder>();
            var logicOrderItems = new List<LogicOrderItem>();
            var ids = TestProduceLogicOrderId("", fxUserId, orders.Count());
            var index = 0;
            foreach (var order in orders)
            {
                var logicOrderId = ids[index++];
                var lo = new LogicOrder()
                {
                    ErpRefundState = order.RefundStatus,
                    PlatformOrderId = order.PlatformOrderId,
                    ShopId = order.ShopId,
                    //BuyerHashCode = order.BuyerHashCode,
                    ToName = order.ToName,
                    ToPhone = order.ToMobile,
                    ToProvince = order.ToProvince,
                    ToCity = order.ToCity,
                    ToCounty = order.ToCounty,
                    ToAddress = order.ToAddress,
                    ToFullAddress = order.ToFullAddress,
                    PlatformType = order.PlatformType,
                    CreateTime = order.CreateTime,
                    OrderCode = order.OrderCode,
                    LogicOrderId = logicOrderId,
                    PathFlowCode = "",
                    ErpState = "waitsellersend"
                };
                logicOrders.Add(lo);
                lo.LogicOrderItems = order.OrderItems.Select(x => new LogicOrderItem
                {
                    LogicOrderId = lo.LogicOrderId,
                    OrderItemCode = x.OrderItemCode,
                    PlatformOrderId = x.PlatformOrderId,
                    SubItemId = x.SubItemID,
                    ShopId = x.ShopId,
                    ProductCode = x.ProductCode,
                    SkuCode = x.SkuCode
                }).ToList();
                logicOrderItems.AddRange(lo.LogicOrderItems);
            }
            db.BulkInsert(logicOrders);
            db.BulkInsert(logicOrderItems);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="platformType">云平台类型：Alibaba、Pinduoduo、Jingdong</param>
        /// <param name="fxUserId">当前登录用户ID</param>
        /// <param name="count">要生成订单号的个数</param>
        /// <returns></returns>
        [TestMethod]
        public List<string> TestProduceLogicOrderId(string platformType, int fxUserId, int count)
        {
            if (count <= 0)
                return new List<string>();
            var list = new List<string>();
            var code = Guid.NewGuid().ToString().ToShortMd5();
            var codes = new List<UniqueIdCode>();
            for (int i = 0; i < count; i++)
            {
                codes.Add(new UniqueIdCode { Code = code });
            }
            var ptPrefix = "1";
            if (string.Equals(platformType, PlatformType.Pinduoduo.ToString(), StringComparison.OrdinalIgnoreCase))
                ptPrefix = "2";
            if (string.Equals(platformType, PlatformType.Jingdong.ToString(), StringComparison.OrdinalIgnoreCase))
                ptPrefix = "3";
            var userFix = fxUserId.ToString();
            var zeroLength = 3 - fxUserId.ToString().Length;
            if (zeroLength > 0)
            {
                for (int i = 0; i < zeroLength; i++)
                {
                    userFix = "0" + userFix;
                }
            }
            ptPrefix += userFix;

            var db = _orderRepository.DbConnection;
            db.BulkInsert(codes);
            var ids = db.Query<string>("select Id from UniqueIdCode where Code=@code", new { code }).ToList();
            if (ids == null)
                throw new LogicException("订单编号生成失败");
            var randomNumber = new Random().Next(1000, 9999).ToString();

            foreach (var id in ids)
            {
                //判断ID长度，如果ID长度不够，补充随机数
                var temp = id;
                var randowLength = 8 - id.Length;
                if (randowLength > 4)
                    temp = randomNumber + id;
                else if (randowLength > 0)
                    temp = randomNumber.Substring(0, randowLength) + id;
                list.Add(ptPrefix + temp);
            }
            return list;
        }

        /// <summary>
        /// 单个订单打印测试
        /// </summary>
        [TestMethod]
        public void TestSingleOrderExpressPrint()
        {
            //生成订单请求数据，
            //需包含：
            //订单：Id,OrderCode,LogicOrderId,PlatformOrderId,ShopId,UserId
            //订单项：OrderItems:Id,LogicOrderId,PlatformOrderId,SubItemId,ShopId,UserId
            //根据逻辑单请求数据，转换为订单数据（逻辑单ID要带过去）

            //查询逻辑订单数据
            var db = _orderRepository.DbConnection;
            //var orders = GetOrdersByPaged(_orderRepository).Values.ToList();
            var orders = _logicOrderRepository.GetOrders(new List<string> { "100529710054", "100529710055" });
            //转换
            var ptOrders = _logicOrderRepository.ConvertOrders(orders);
            //var model = new ExpressPrintRequestModel()
            //{
            //    PrintMethod = ExpressPrintMethodType.Normal,
            //    PackageCount = 1,
            //    Orders = orders.Select(x => new OrderRequestModel
            //    {
            //        Id = x.Id,
            //        OrderCode = x.OrderCode,
            //        PlatformOrderId = x.PlatformOrderId,
            //        ChildOrderId = x.ChildOrderId,
            //        ShopId = x.ShopId,
            //        OrderItems = x.LogicOrderItems.Select(y => y.Id).ToList()
            //    }).ToList(),
            //    TemplateId = 5136,
            //    IsPreview = true,
            //    IsScanPrint = false,
            //    RequestBatchNumber = "",
            //    FxUserId = _siteContext.CurrentFxUserId
            //};
            //获取商家发件人信息
            var model = new ExpressPrintRequestModel()
            {
                PrintMethod = ExpressPrintMethodType.Normal,
                PackageCount = 1,
                Orders = ptOrders.Select(x => new OrderRequestModel
                {
                    Id = x.Id,
                    OrderCode = x.OrderCode,
                    PlatformOrderId = x.PlatformOrderId,
                    ChildOrderId = x.ChildOrderId,
                    ShopId = x.ShopId,
                    OrderItems = x.OrderItems.Select(y => y.Id).ToList(),
                    Sender = new OrderSenderRequestModel
                    {
                        CompanyName = "深圳步步飞升有限公司",
                        SenderAddress = "广东省深圳市龙华区白石龙一区1111号111",
                        SenderName = "发件人",
                        SenderPhone = "13013001300"
                    },
                    Receiver = new OrderReceiverRequestModel
                    {
                        toArea = "广东省深圳市龙华区白石龙一区1111号111",
                        toProvince = "省",
                        toCity = "市",
                        toCounty = "区",
                        buyerMemberId = "",
                        toFullName = "发件人",
                        toPhone = "13013001300",
                        toMobile = "13013001300"
                    },
                    PrintInfo = "打印内容*********",
                }).ToList(),
                TemplateId = 5136,
                IsPreview = false,
                IsScanPrint = false,
                RequestBatchNumber = "",
                FxUserId = _siteContext.CurrentFxUserId,
            };


            var newOrderController = new NewOrderController();
            var actionResult = newOrderController.ExpressPrint(model);
        }

        /// <summary>
        /// 合并订单打印测试
        /// </summary>
        [TestMethod]
        public void TestMergerOrderExpressPrint()
        {
            //生成订单请求数据，
            //需包含：
            //订单：Id,OrderCode,LogicOrderId,PlatformOrderId,ShopId,UserId
            //订单项：OrderItems:Id,LogicOrderId,PlatformOrderId,SubItemId,ShopId,UserId
            //根据逻辑单请求数据，转换为订单数据（逻辑单ID要带过去）

            //查询逻辑订单数据
            var orders = _logicOrderRepository.GetOrders(new List<string> { "C100529710056" });
            //转换
            var ptOrders = _logicOrderRepository.ConvertOrders(orders);
            //TODO:获取商家发件人信息
            var model = new ExpressPrintRequestModel()
            {
                PrintMethod = ExpressPrintMethodType.Normal,
                PackageCount = 1,
                Orders = ptOrders.Select(x => new OrderRequestModel
                {
                    Id = x.Id,
                    OrderCode = x.OrderCode,
                    PlatformOrderId = x.PlatformOrderId,
                    ChildOrderId = x.ChildOrderId,
                    ShopId = x.ShopId,
                    OrderItems = x.OrderItems.Select(y => y.Id).ToList(),
                    Sender = new OrderSenderRequestModel
                    {
                        CompanyName = "深圳步步飞升有限公司",
                        SenderAddress = "广东省深圳市龙华区白石龙一区1111号111",
                        SenderName = "发件人",
                        SenderPhone = "13013001300"
                    },
                    Receiver = new OrderReceiverRequestModel
                    {
                        toArea = "广东省深圳市龙华区白石龙一区1111号111",
                        toProvince = "省",
                        toCity = "市",
                        toCounty = "区",
                        buyerMemberId = "",
                        toFullName = "发件人",
                        toPhone = "13013001300",
                        toMobile = "13013001300"
                    },
                    PrintInfo = "打印内容*********"
                }).ToList(),
                TemplateId = 5135,
                IsPreview = false,
                IsScanPrint = false,
                RequestBatchNumber = "",
                FxUserId = _siteContext.CurrentFxUserId
            };


            var newOrderController = new NewOrderController();
            var actionResult = newOrderController.ExpressPrint(model);
        }

        /// <summary>
        /// 合并订单打印测试
        /// </summary>
        [TestMethod]
        public void TestMergerOrderExpressPrint2()
        {
            //查询逻辑订单数据
            var orders = _logicOrderRepository.GetOrders(new List<string> { "C100597710343" });
            //转换
            var ptOrders = _logicOrderRepository.ConvertOrders(orders);
            //TODO:获取商家发件人信息
            var model = new ExpressPrintRequestModel()
            {
                PrintMethod = ExpressPrintMethodType.Normal,
                PackageCount = 1,
                Orders = ptOrders.Select(x => new OrderRequestModel
                {
                    Id = x.Id,
                    OrderCode = x.OrderCode,
                    PlatformOrderId = x.PlatformOrderId,
                    ChildOrderId = x.ChildOrderId,
                    ShopId = x.ShopId,
                    FxUserId = x.UserId,
                    OrderItems = x.OrderItems.Select(y => y.Id).ToList(),
                    Receiver = new OrderReceiverRequestModel
                    {
                        toArea = "广东省深圳市福田区深圳国际文化大厦2401A",
                        toFullName = "13632840520",
                        toPhone = "13013001300",
                        toMobile = "13013001300"
                    },
                    PrintInfo = "(1)袖口真是您的气质之选 黄色 25;1122 [1];\r\n(2)袖口真是您的气质之选 绿色 39; 1122[1]; "
                }).ToList(),
                TemplateId = 5150,
                IsPreview = false,
                IsScanPrint = false,
                RequestBatchNumber = "1/1/202103161730080001",
                FxUserId = _siteContext.CurrentFxUserId
            };


            var newOrderController = new NewOrderController();
            var actionResult = newOrderController.ExpressPrint(model);
        }


        /// <summary>
        /// 测试单个正常订单打印回调
        /// </summary>
        [TestMethod]
        public void TestSingleOrderPrintCallback()
        {
            var orders = _logicOrderRepository.GetOrders(new List<string> { "100529710054" });
            var model = new PrintHisotoryCallbackRequestModel
            {
                PrintType = 1,
                PrintDataType = 1,
                TemplateId = 5136,
                PrintHistoryIds = new List<long> { 4 },
                Orders = new List<PrintHisotoryCallbackRequestOrderModel> {
                    new PrintHisotoryCallbackRequestOrderModel{
                        Id = 1,
                        OrderItems= new List<int>{ 1,2 },
                        PlatformOrderId = "100529710054",
                        ShopId = 1393,
                        WaybillCode="ZJS000211169490"
                    }
                },
                PrintHistoryIndexModels = new List<PrintHistoryIndexModel> {
                    new PrintHistoryIndexModel{
                        Id=4,
                        Pid = "100529710054",
                        BatchIndex = 0,
                        WaybillCode = "ZJS000211169490",
                        WaybillCodeChild = "",
                    }
                },
                IsCustomerOrder = false,
                IsPreview = false
            };
            var newOrderController = new NewOrderController();
            newOrderController.PrintCallback(model);
        }

        /// <summary>
        /// 测试合并订单打印回调
        /// </summary>
        [TestMethod]
        public void TestMergerOrderPrintCallback()
        {
            var orders = _logicOrderRepository.GetOrders(new List<string> { "C100529710056" });
            var model = new PrintHisotoryCallbackRequestModel
            {
                PrintType = 1,
                PrintDataType = 1,
                TemplateId = 5136,
                PrintHistoryIds = new List<long> { 3 },
                Orders = new List<PrintHisotoryCallbackRequestOrderModel> {
                    new PrintHisotoryCallbackRequestOrderModel{
                        Id = 54,
                        OrderItems= new List<int>{ 1,2 },
                        PlatformOrderId = "C100529710056",
                        ShopId = 1393,
                        WaybillCode="ZJS000211283682"
                    }
                },
                PrintHistoryIndexModels = new List<PrintHistoryIndexModel> {
                    new PrintHistoryIndexModel{
                        Id=3,
                        Pid = "C100529710056",
                        BatchIndex = 0,
                        WaybillCode = "ZJS000211283682",
                        WaybillCodeChild = "",
                    }
                },
                IsCustomerOrder = false,
                IsPreview = false
            };
            var newOrderController = new NewOrderController();
            newOrderController.PrintCallback(model);
        }

        public void TestOnlineSend(List<string> logicOrderIds)
        {
            var orders = _logicOrderRepository.GetOrders(logicOrderIds);
            var ptOrders = _logicOrderRepository.ConvertOrders(orders);
            var model = new OnlineSendRequestModel
            {
                //TemplateId = 5136,
                //ExpressCompanyId = 16,
                Orders = ptOrders.Select(x => new OrderRequestModel
                {
                    Id = x.Id,
                    OrderCode = x.OrderCode,
                    PlatformOrderId = x.PlatformOrderId,
                    LogicOrderId = x.PlatformOrderId,
                    ChildOrderId = x.ChildOrderId,
                    ShopId = x.ShopId,
                    FxUserId = x.UserId,
                    WaybillCode = "ZJS000336113536",
                    //WaybillCodes = new List<string> { "ZJS000336113536", "ZJS000336113535" },
                    OrderItems = x.OrderItems.Select(y => y.Id).ToList(),
                    ExpressCompanyCode = "ZJS",
                    Sender = new OrderSenderRequestModel
                    {
                        CompanyName = "深圳步步飞升有限公司",
                        SenderAddress = "广东省深圳市龙华区白石龙一区1111号111",
                        SenderName = "发件人",
                        SenderPhone = "13013001300"
                    },
                    Receiver = new OrderReceiverRequestModel
                    {
                        toArea = "广东省深圳市龙华区白石龙一区1111号111",
                        toProvince = "省",
                        toCity = "市",
                        toCounty = "区",
                        buyerMemberId = "",
                        toFullName = "发件人",
                        toPhone = "13013001300",
                        toMobile = "13013001300"
                    },
                    PrintInfo = "打印内容*********"
                }).ToList(),
                ExcludeOrders = new List<ExcludeOrderRequestModel> { },
                IsSendPreCheck = false
            };
            var newOrderController = new NewOrderController();
            try
            {
                var actionResult = newOrderController.OnlineSend(model);
            }
            catch (Exception ex)
            {

            }
        }

        /// <summary>
        /// 测试指定店铺同步商品
        /// </summary>
        [TestMethod]
        public void TestSyncProduct()
        {
            var bangShop = new ShopService().Get(3159131);
            RedisConfigService.Initialization();
            new SyncFxProductService(SiteContext.Current.CurrentFxUserId).SyncProduct(bangShop,isBindSupplier:true);
        }

        /// <summary>
        /// 测试Controller同步商品
        /// </summary>
        [TestMethod]
        public void TestCSyncProduct()
        {
            new ProductController().SyncProduct(true);
        }

        /// <summary>
        /// 测试单个订单发货
        /// </summary>
        [TestMethod]
        public void TestSingleOrderOnlineSend()
        {
            TestOnlineSend(new List<string> { "100000540120334" });
            Thread.Sleep(1000000);
        }

        /// <summary>
        /// 测试单个订单发货
        /// </summary>
        [TestMethod]
        public void TestOrderMutilWybCodeOnlineSend()
        {
            var logicOrderIds = new List<string> { "100000513146907" };
            var orders = _logicOrderRepository.GetOrders(logicOrderIds);
            var ptOrders = _logicOrderRepository.ConvertOrders(orders);
            var model = new OnlineSendRequestModel
            {
                //TemplateId = 5136,
                //ExpressCompanyId = 16,
                Orders = ptOrders.Select(x => new OrderRequestModel
                {
                    Id = x.Id,
                    OrderCode = x.OrderCode,
                    PlatformOrderId = x.PlatformOrderId,
                    LogicOrderId = x.PlatformOrderId,
                    ChildOrderId = x.ChildOrderId,
                    ShopId = x.ShopId,
                    FxUserId = x.UserId,
                    WaybillCode = "",
                    //WaybillCodes = new List<string> { "ZJS000336113536", "ZJS000336113535" },
                    OrderItems = x.OrderItems.Select(y => y.Id).ToList(),
                    ExpressCompanyCode = "",
                    Sender = new OrderSenderRequestModel
                    {
                        CompanyName = "深圳步步飞升有限公司",
                        SenderAddress = "广东省深圳市龙华区白石龙一区1111号111",
                        SenderName = "发件人",
                        SenderPhone = "13013001300"
                    },
                    Receiver = new OrderReceiverRequestModel
                    {
                        toArea = "广东省深圳市龙华区白石龙一区1111号111",
                        toProvince = "省",
                        toCity = "市",
                        toCounty = "区",
                        buyerMemberId = "",
                        toFullName = "发件人",
                        toPhone = "13013001300",
                        toMobile = "13013001300"
                    },
                    PrintInfo = "打印内容*********",
                    MultiPackSendModels = new List<MultiPackSendModel>
                    {
                        new MultiPackSendModel { LogicOrderItemId = 106716,WaybillCode="189050918885",Count=1,ExpressCompanyCode="FENGWANG" },
                        new MultiPackSendModel { LogicOrderItemId = 106717,WaybillCode="ZJS000347880912",Count=1,ExpressCompanyCode="ZJS" }
                    }
                }).ToList(),
                ExcludeOrders = new List<ExcludeOrderRequestModel> { },
                IsSendPreCheck = false
            };
            var newOrderController = new NewOrderController();
            try
            {
                var actionResult = newOrderController.OnlineSend(model);
            }
            catch (Exception ex)
            {

            }
            Thread.Sleep(1000000);
        }


        /// <summary>
        /// 测试订单发货
        /// </summary>
        [TestMethod]
        public void TestMergerOrderOnlineSend()
        {
            TestOnlineSend(new List<string> { "C100532499650" });

            Thread.Sleep(1000000);
        }

        [TestMethod]
        public void TestBindSupplier()
        {
            var productController = new ProductController();
            //var pathFlowService = new PathFlowService();
            //var oldPaths = pathFlowService.GetPathFlows(new List<string> { "23cda5fe702f8401" });
            //var sort = oldPaths?.FirstOrDefault()?.SortedNodes;
            var result = productController.BindSupllier(new BindSupplierRequestModel
            {
                isSaveHistoryData = false,
                supplierId = 32,
                isSelf = false,
                productCodes = new List<string> { "70db60a0e5b313cf" },
                from = "Product"
            });
        }

        [TestMethod]
        public void TestBindSupplierToSelf()
        {
            var productController = new ProductController();
            var result = productController.BindSupllier(new BindSupplierRequestModel
            {
                isSaveHistoryData = false,
                supplierId = 0,
                isSelf = true,
                productCodes = new List<string> { "23cda5fe702f8401" },
                from = "Product"
            });
        }

        [TestMethod]
        public void TestExceptionOrder()
        {
            var md5 = "1558504765301508772199588".ToShortMd5();
            var userFx = new UserFxService().Get(5);
            var sc = new SiteContext(userFx);
            var shop = new ShopService().Get(3159140);
            var syncOrderService = new SyncFxOrderService(5);
            syncOrderService.SyncSingleOrder("299386359219", shop);
        }

        [TestMethod]
        public void TestSetPathFlowReference()
        {
            //添加路径流和引用
            new PathFlowReferenceService().SetPathFlowReferenceNew(5, 1467);
        }

        [TestMethod]
        public void TestAddVirtualSupplier()
        {
            var partnerController = new PartnerController();
            partnerController.AddVirtualSupplier(new SupplierUser
            {
                NickName = "飞翔的小企鹅",
                SupplierType = "Virtual"
            });
        }

        [TestMethod]
        public void TestFixPathFlowReference()
        {
            //var db = _logicOrderRepository.DbConnection;
            var db = DbApiAccessUtility.GetPddFenDanDb();
            var pfs = db.Query<PathFlowReference>("select * from PathFlowReference").ToList();
            var sql = new StringBuilder();
            var groups = pfs.GroupBy(x => x.PathFlowRefCode);
            var fu1 = groups.Where(x => x.Count() == 1).Where(x => x.First().Status == -1).ToList();
            var count = 0;
            foreach (var g in groups)
            {
                var last = g.OrderByDescending(x => x.Id).First();
                if (last.Status == -1 || g.All(x => x.Status == -1))
                {
                    sql.AppendLine($"UPDATE dbo.PathFlowReference SET Status=-1 WHERE PathFlowRefCode='{g.Key}';UPDATE PathFlowReference SET Status=0 WHERE Id={last.Id};");
                    count++;
                }
            }
            Log.WriteLine(sql.ToString());
        }


        /// <summary>
        /// 合并订单打印测试
        /// </summary>
        [TestMethod]
        public void TestMergerOrderExpressPrint3()
        {
            var json = System.IO.File.ReadAllText("E:/Code/Git/Xinban/DianGuanJiaApp.Tests/bin/Debug/1.txt");
            var model = json.ToObject<ExpressPrintRequestModel>();
            var newOrderController = new NewOrderController();
            var index = 1;
            model.Orders.ForEach(x =>
            {
                x.Id = index++;
            });
            var actionResult = newOrderController.ExpressPrint(model);
        }

        [TestMethod]
        public void TestUnBindAgent()
        {
            // ID:29,45
            var partnerController = new PartnerController();
            //partnerController.UpdateSupplierBindStatus(29, AgentBingSupplierStatus.Reject);
        }

        [TestMethod]
        public void TestUnBindSupplier()
        {
            var partnerController = new PartnerController();
            var model = new BindSupplierRequestModel();
            model.from = "supplier";
            model.unbindSupplierId = 2;
            model.unbindSupplierFxUserId = 30;

            //// 1、商品改自营
            //model.isSelf = true;

            //// 2、商品绑定厂家（已存在厂家不包含新的）
            //model.isSelf = false;
            //model.Configs = new List<BindConfigModel> 
            //{
            //    new BindConfigModel{ Config="",ConfigType=0,SupplierId=28}, //默认厂家切换为28
            //    new BindConfigModel{ Config="00:00:00,12:00:00",ConfigType=2,SupplierId=29}, //时间配置厂家29
            //};

            // 3、商品绑定厂家（存在其他厂家）
            model.isSelf = false;
            model.Configs = new List<BindConfigModel>
            {
                new BindConfigModel{ Config="",ConfigType=0,SupplierId=28}, //默认厂家切换为30
                new BindConfigModel{ Config="10:00:00,12:00:00",ConfigType=2,SupplierId=29}, //时间配置厂家29
            };

            partnerController.UnbundlingEvent(model);
        }

        [TestMethod]
        public void TestSyncUnBindSupplier()
        {
            new SupplierUserService().TriggerSyncSupplierUserStatu(5, 30, 1, AgentBingSupplierStatus.UnBindFail);
        }

        [TestMethod]
        public void TestChangeFlowShopToVersion()
        {
            var pathFlowReferenceService = new PathFlowReferenceService();
            var result = pathFlowReferenceService.IsExistsPathFlowShopInVersion();
        }


        [TestMethod]
        public void TestOrderLoadList()
        {
            var orderController = new NewOrderController();
            var model = new OrderSearchModel
            {
                Filters = new List<OrderSearchFieldModel>
                {
                    new OrderSearchFieldModel{ TableAlias="o",TableName=""}
                }
            };
            orderController.List(model);
        }

        /// <summary>
        /// 测试发货-异常补偿
        /// </summary>
        [TestMethod]
        public void TestAsyncDelivery()
        {
            Init();
            TestOnlineSend(new List<string> { "***************" });
        }

        [TestMethod]
        public void TestGetAllTotal()
        {
            var result2 = new MobileApiController().GetAllTotal("1658", 7);
        }
	[TestMethod]
        public void TestSimpleLogin()
        {
            var model = new UserLoginModel();
            model.Mobile = "***********";
            model.MobileMeessageCode = "123456";
            var result = new FFAccountController().SimpleLogin(model);
        }

        [TestMethod]
        public void TestPreCheckAppendPack()
        {
            var orders = new List<OrderRequestModel>();
            orders.Add(new OrderRequestModel
            {
                CustomerOrderId = "221110164503204888458",
                PlatformOrderId = "C100000516962383",
                LogicOrderId = "C100000516962383",
                PlatformType = "Virtual",
            });
            orders.Add(new OrderRequestModel
            {
                CustomerOrderId = "2211151601071622825734",
                PlatformOrderId = "***************",
                LogicOrderId = "***************",
                PlatformType = "Virtual",
            });


            var result = new NewOrderController().PreCheckAppendPack(orders);
        }


        [TestMethod]
        public void TestAppendPack()
        {
            var model = new OnlineSendRequestModel();
            var orders = new List<OrderRequestModel>();
            orders.Add(new OrderRequestModel
            {
                CustomerOrderId = "221202-522893465171917",
                LogicOrderId = "200004658128776",
                PlatformType = "Pinduoduo",
                ExpressCompanyCode = "SF",
                MultiPackSendModels = new List<MultiPackSendModel>
                {
                    new MultiPackSendModel
                    {
                        ExpressCompanyCode = "FENGWANG",
                        WaybillCode = "990102181731015",
                        TrackType = 1,
                        OrderItemCode = "0a69ce103d482d3e",
                        LogicOrderItemId = 157066,
                        OrderItemId = 157066,
                        Count = 1
                    },new MultiPackSendModel
                    {
                        ExpressCompanyCode = "FENGWANG",
                        WaybillCode = "990102181679619",
                        TrackType = 1,
                        OrderItemCode = "0a69ce103d482d3e",
                        LogicOrderItemId = 157066,
                        OrderItemId = 157066,
                        Count = 1
                    },new MultiPackSendModel
                    {
                        ExpressCompanyCode = "FENGWANG",
                        WaybillCode = "990102181674459",
                        TrackType = 3,
                        OrderItemCode = "0a69ce103d482d3e",
                        LogicOrderItemId = 157066,
                        OrderItemId = 157066,
                        Count = 1
                    }
                }
            });

            model.Orders = orders;

            var result = new NewOrderController().AppendPack(model);
        }

        /// <summary>
        /// 测试多个订单发货，异步处理
        /// </summary>
        [TestMethod]
        public void TestOnlineSendBatch()
        {
            var logicOrderIds = new List<string> { "240607100428195244140" };
            var orders = _logicOrderRepository.GetOrders(logicOrderIds);
             var ptOrders = _logicOrderRepository.ConvertOrders(orders);
            var model = new OnlineSendRequestModel
            {
                //TemplateId = 5136,
                //ExpressCompanyId = 16,
                Orders = ptOrders.Select(x => new OrderRequestModel
                {
                    Id = x.Id,
                    OrderCode = x.OrderCode,
                    PlatformOrderId = x.PlatformOrderId,
                    LogicOrderId = x.PlatformOrderId,
                    ChildOrderId = x.ChildOrderId,
                    ShopId = x.ShopId,
                    FxUserId = x.UserId,
                    WaybillCode = "",
                    //WaybillCodes = new List<string> { "ZJS000336113536", "ZJS000336113535" },
                    OrderItems = x.OrderItems.Select(y => y.Id).ToList(),
                    ExpressCompanyCode = "",
                    Sender = new OrderSenderRequestModel
                    {
                        CompanyName = "深圳步步飞升有限公司",
                        SenderAddress = "广东省深圳市龙华区白石龙一区1111号111",
                        SenderName = "发件人",
                        SenderPhone = "13013001300"
                    },
                    Receiver = new OrderReceiverRequestModel
                    {
                        toArea = "广东省深圳市龙华区白石龙一区1111号111",
                        toProvince = "省",
                        toCity = "市",
                        toCounty = "区",
                        buyerMemberId = "",
                        toFullName = "发件人",
                        toPhone = "13013001300",
                        toMobile = "13013001300"
                    },
                    PrintInfo = "打印内容*********",
                    MultiPackSendModels = new List<MultiPackSendModel>
                    {
                        new MultiPackSendModel { LogicOrderItemId = 106716,WaybillCode="189050918885",Count=1,ExpressCompanyCode="FENGWANG" },
                        new MultiPackSendModel { LogicOrderItemId = 106717,WaybillCode="ZJS000347880912",Count=1,ExpressCompanyCode="ZJS" }
                    }
                }).ToList(),
                ExcludeOrders = new List<ExcludeOrderRequestModel> { },
                IsSendPreCheck = false
            };
            var newOrderController = new NewOrderController();
            try
            {
                var actionResult = newOrderController.OnlineSendBatch(model);
                JsonResult jsonResult = (JsonResult)actionResult;
                string jsonData = ((dynamic)jsonResult.Data).key.ToString(); // 获取 JSON 中的数据
                Timer timer = new Timer(state => 
                {
                    newOrderController.OnlineSendPolling("");

                },null,0,1000);
            }
            catch (Exception ex)
            {

            }
            Thread.Sleep(1000000);
        }
    }
}
