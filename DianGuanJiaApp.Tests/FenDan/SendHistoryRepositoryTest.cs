using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Tests.FenDan
{
    [TestClass]
    public class SendHistoryRepositoryTest
    {
        [TestInitialize]
        public void Init()
        {
            var rp = new UserFxRepository();
            var userFx = rp.Get(" where Id=@id", new { id = 5 })?.FirstOrDefault();
            var _siteContext = new SiteContext(userFx, "AlibabaFenFaDB");
        }
        [TestMethod]
        public void GetSendOrderHistorysV2Test()
        {
            var rp = new SendHistoryRepository();
            var res = rp.GetSendOrderHistorysV2(new DianGuanJiaApp.Data.Model.OrderSearchModel()
            {

            }, false);
            Assert.IsTrue(res.Item1!=null&&res.Item1.Rows!=null&&res.Item1.Rows.Count>0);
        }

    }
}
