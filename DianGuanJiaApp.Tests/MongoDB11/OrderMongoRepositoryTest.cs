using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.Mvc;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using DianGuanJiaApp;
using DianGuanJiaApp.Controllers;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Data.MongoRepository;
using MongoDB.Bson;
using MongoDB.Driver;
using System.Collections.Concurrent;
using Dapper;
using DianGuanJiaApp.Utility.Extension;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Threading;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.IO;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.Tests.MongoDB11
{
    [TestClass]
    public class OrderMongoRepositoryTest
    {
        private Shop _shop;
        private SiteContext _siteContext;
        private OrderMongoRepository _repository;
        private OrderRepository _repositoryOld;
        [TestInitialize]
        public void Init()
        {
            var rp = new DianGuanJiaApp.Data.Repository.ShopRepository();
            _shop = rp.Get("where Id=@id", new { id = 111 })?.FirstOrDefault();
            _siteContext = new SiteContext(_shop);
            _repository = new OrderMongoRepository();
            _repositoryOld = new OrderRepository();
        }

        [TestMethod]
        public void TestInsertOrder()
        {
            var order = _repositoryOld.GetOrders(new List<OrderSelectKeyModel> { new OrderSelectKeyModel { ShopId = _shop.Id, PlatformOrderId = "277499535131969811" } })?.FirstOrDefault();
            order.PlatformType = "Pinduoduo";
            _repository.Add(order);
        }

        [TestMethod]
        public void TestGetOrder()
        {
            var orders = _repository.FindAll();
            var first = orders?.FirstOrDefault();
            var id = first.ObjectId.Increment;
            var oldId = first.Id;
            var time = first.ObjectId.CreationTime;
            Assert.AreEqual(6453441, id);
        }

        [TestMethod]
        public void TestFindPage()
        {
            var orders = _repository.Find(x => x.TotalAmount >= 66, x => x.PayTime, 0, 10, true);
            Assert.AreEqual(1, orders.Count());
            orders = _repository.Find(x => x.TotalAmount >= 67, x => x.PayTime, 0, 10, true);
            Assert.AreEqual(0, orders.Count());
        }
        [TestMethod]
        public void TestUpdateOrder()
        {
            _repository.Update(new { ShopId = _shop.Id, PlatformOrderId = "C254650254839969811", PlatformType = "Alibaba", CreateTime = DateTime.Now, CategoryId = 1, ShippingFee = 999.9, IsLocked = "1", IsSvcCOD = true, ToName = "吴" });
        }

        [TestMethod]
        public void TestUpdateOrderItem()
        {
            _repository.Update(new { ShopId = _shop.Id, PlatformOrderId = "258821892500097888", PlatformType = "Alibaba", CreateTime = DateTime.Now, CategoryId = 1, ShippingFee = 999.9, IsLocked = "1", IsSvcCOD = true, ToName = "吴" });
        }

        [TestMethod]
        public void TestFindByPlatformOrderId()
        {
            var json = "{ 'PlatformOrderId': '258821892500097888' ,'OrderItems._id':2812467}";
            var bson = BsonDocument.Parse(json);
            var order0 = _repository.Find(bson)?.FirstOrDefault();
            var order = _repository.Find(json)?.FirstOrDefault();
        }

        [TestMethod]
        public void TestUpdateLog()
        {
            LogForOperatorRepository logRepository = new LogForOperatorRepository();
            var result = logRepository.Update("5d1b565e0c701c40109b3e8e", 0.125);
        }


        [TestMethod]
        public void TestQueryModelToJson()
        {
            OrderSearchModel model = new OrderSearchModel();
            model.OrderByField = "oi.ProductCargoNumber";
            model.IsOrderDesc = true;
            model.IsCustomerOrder = false;
            model.PageIndex = 1;
            model.PageSize = 100;

            var filters = new List<OrderSearchFieldModel>()
            {
                new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="ShopId",Contract="in",Value="50",FieldType="int"},
                new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="CreateTime",Contract="between",Value="2019-05-05 16:00:00",ExtValue="2019-06-06 15:59:59.999",FieldType="datetime"},
                new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="IsPreordain",Contract="=",Value="0",FieldType="bool"},
                new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="ChildOrderId",Contract="!=",Value="MergeOrder",FieldType="string",CustomQuery="MergeOrder"},
                //new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="PlatformStatus",Contract="=",Value="waitsellersend",FieldType="string"},
                new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="productCargoNumber",Contract="!=",Value="",FieldType="string"},
                //new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="PlatformStatus",Contract="=",Value="waitsellersend",FieldType="string"},
                //new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="PlatformOrderId",Contract="=",Value="C302313037165969811",FieldType="string"},
                //new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="IsPreordain",Contract="=",Value="0",FieldType="int"},
                new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="ColorAndSize",Contract="like",Value="M[100斤-110斤]",FieldType="string",CustomQuery="ColorAndSize"},
                //new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="PlatformStatus",Contract="=",Value="waitsellersend",FieldType="string"},
                //new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="TotalAmount",Contract="between",Value="0",ExtValue="10",FieldType="int"},
                new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="Color",Contract="!=",Value="M[100斤-110斤]",FieldType="string"},
                //new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="Size",Contract="like",Value="XL",FieldType="string"},
                //new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="CargoNumber",Contract="not like",Value="110111",FieldType="string"},
            };
            var multiFilters = new List<List<OrderSearchFieldModel>>();
            //multiFilters.Add(new List<OrderSearchFieldModel>()
            //{
            //    new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="Color",Contract="!=",Value="黑色",FieldType="string",Operator="OR"},
            //    new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="Size",Contract="like",Value="XL",FieldType="string",Operator="OR"},
            //    new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="CargoNumber",Contract="not like",Value="110111",FieldType="string",Operator="OR"},
            //});
            //multiFilters.Add(new List<OrderSearchFieldModel>()
            //{
            //    new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="SendedCount",CompareName="ProductCount",Contract="小于",Value="0",FieldType="int",Operator="AND"},
            //    new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="SendedCount",Contract="大于",Value="0",FieldType="int",Operator="AND"},
            //    new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="PlatformStatus",Contract="=",Value="waitsellersend",FieldType="string",Operator="AND"},
            //});

            model.Filters = filters;
            model.MultiFilters = multiFilters;

            CommonSettingService _commonSettingService = new CommonSettingService();
            var systemSetting = _commonSettingService.GetSystemSetting(SiteContext.Current.CurrentShopId);

            //var json = _repository.GetWhereSql(model, systemSetting);  
            //var set = _repository.Find(json).ToList();

            var result = _repository.GetOrders(model, systemSetting);
            var orders = result?.Rows;

        }



        #region 合并订单相关测试

        [TestMethod]
        public void TestSplitOrder()
        {
            _repository.SplitOrderWhenPlatformStatusNotSame(new List<Shop> { _shop });
        }

        [TestMethod]
        public void TestRestoreOrder()
        {
            var keys = new List<OrderSelectKeyModel> { new OrderSelectKeyModel { PlatformOrderId = "C300855308522969811", ShopId = _shop.Id } };
            var order = _repository.GetOrders(keys).FirstOrDefault();
            Assert.IsNotNull(order);
            _repository.RestoreMergeredOrder(order, null, true);
            var temp = _repository.GetOrders(keys).FirstOrDefault();
            Assert.IsNull(temp);
        }

        [TestMethod]
        public void TestBulkMergerOrder()
        {
            var order = _repository.GetOrders(_shop.Id).FirstOrDefault();
            Assert.IsNotNull(order);
            order.ToName = "77777777777777777777777777";
            order.ToPhone = "Hello";
            order.NahuoPrintTimes = 999;
            order.OrderItems.ForEach(oi =>
            {
                oi.CargoNumber = "77777777777777777777777777";
                oi.Color = "黄色";
            });
            _repository.BulkMerger(new List<Order> { order });
        }

        [TestMethod]
        public void TestGetCouldMergerOrders()
        {
            var orders = _repository.GetCouldMergerOrders(_shop);
        }
        [TestMethod]
        public void TestIsAnyOrderMergered()
        {
            var mergered = _repository.IsAnyOrderMergered(new List<string> { "301005452642969811" }, _shop.Id);
            Assert.IsTrue(mergered);
            var notMergered = _repository.IsAnyOrderMergered(new List<string> { "303708173261969811" }, _shop.Id);
            Assert.IsFalse(notMergered);
        }

        [TestMethod]
        public void TestAutoMerger()
        {
            _repository.AutoMergerOrder(new List<Shop> { _shop });
        }


        #endregion

        #region 压力测试

        /// <summary>
        /// 查询压测
        /// </summary>
        [TestMethod]
        public void TestQueryBalance()
        {
            var rs = new ConcurrentBag<KeyValuePair<double, int>>();
            Parallel.For(1000, 1050, (i) =>
            {
                var sw = new Stopwatch();
                sw.Start();
                var model = new OrderSearchModel() { PageIndex = 1, PageSize = 500 };
                model.Filters.Add(new OrderSearchFieldModel { TableName = "P_Order", TableAlias = "o", Name = "ShopId", Contract = "=", Value = i.ToString(), FieldType = "int" });
                model.Filters.Add(new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "PlatformStatus", Contract = "=", Value = "waitbuyerreceive", FieldType = "string" });
                model.Filters.Add(new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "CreateTime", Contract = "between", Value = "2019-05-05 16:00:00", ExtValue = "2019-06-16 15:59:59.999", FieldType = "datetime" });
                var orders = _repository.GetOrders(model, new SystemSetting { });
                sw.Stop();
                rs.Add(new KeyValuePair<double, int>(sw.Elapsed.TotalMilliseconds, orders.Rows.Count));
            });
            var totalCount = rs.Select(kv => kv.Value).Sum();
            var avgMillSeconds = rs.Select(kv => kv.Key).Average();
            var max = rs.Select(kv => kv.Key).Max();
            var min = rs.Select(kv => kv.Key).Min();
        }

        /// <summary>
        /// 更新压测
        /// </summary>
        [TestMethod]
        public void TestUpdateBalance()
        {
            var rs = new ConcurrentBag<KeyValuePair<double, int>>();
            Parallel.For(1000, 1050, (i) =>
            {
                var sw = new Stopwatch();
                sw.Start();
                var model = new OrderSearchModel() { PageIndex = 1, PageSize = 500 };
                var orders = _repository.GetOrders(i).ToList() ?? new List<Order>();
                orders.ForEach(o =>
                {
                    o.ToName = "飞飞飞飞飞飞";
                });
                ThreadPool.QueueUserWorkItem(state =>
                {
                    _repository.BulkMerger(orders);
                });
                _repository.BulkMerger(orders);
                sw.Stop();
                rs.Add(new KeyValuePair<double, int>(sw.Elapsed.TotalMilliseconds, orders.Count()));
            });
            var totalCount = rs.Select(kv => kv.Value).Sum();
            var avgMillSeconds = rs.Select(kv => kv.Key).Average();
            var max = rs.Select(kv => kv.Key).Max();
            var min = rs.Select(kv => kv.Key).Min();
        }

        /// <summary>
        /// 更新查询压测
        /// </summary>
        [TestMethod]
        public void TestUpdateQueryBalance()
        {
            var rs = new ConcurrentBag<KeyValuePair<double, int>>();
            Parallel.For(1000, 1001, (i) =>
            {
                var sw = new Stopwatch();
                sw.Start();
                var model = new OrderSearchModel() { PageIndex = 1, PageSize = 500 };
                var orders = _repository.GetOrders(i).ToList() ?? new List<Order>();

                ThreadPool.QueueUserWorkItem(state =>
                {
                    orders.ForEach(o =>
                    {
                        o.ToName = "飞飞飞-1-" + i;
                    });
                    _repository.BulkMerger(orders);
                });
                ThreadPool.QueueUserWorkItem(state =>
                {
                    orders.ForEach(o =>
                    {
                        o.ToName = "飞飞飞-2-" + i;
                    });
                    _repository.BulkMerger(orders);
                });
                orders = _repository.GetOrders(i).ToList() ?? new List<Order>();
                sw.Stop();
                rs.Add(new KeyValuePair<double, int>(sw.Elapsed.TotalMilliseconds, orders.Count()));
            });
            var totalCount = rs.Select(kv => kv.Value).Sum();
            var avgMillSeconds = rs.Select(kv => kv.Key).Average();
            var max = rs.Select(kv => kv.Key).Max();
            var min = rs.Select(kv => kv.Key).Min();
            Thread.Sleep(300 * 1000);
        }

        #endregion

        #region MongoDB ID 测试
        [TestMethod]
        public void TestMongoDBID()
        {
            var temp = new Order();
            Assert.IsFalse(temp.IsFromDB);
            var old = _repository.Find(x => x.ShopId == _shop.Id).FirstOrDefault();
            Assert.IsTrue(old.IsFromDB);
        }

        #endregion

        #region 订单业务字段更新测试


        /// <summary>
        /// 更新订单收件人
        /// </summary>
        [TestMethod]
        public void TestUpdateOrderReceiver()
        {
            var result = _repository.UpdateOrderReceiver(new OrderSelectKeyModel()
            {
                ShopId = _shop.Id,
                PlatformOrderId = "C464995937633969811",
            }, "邹毅008", "18575506910", "湖南省", "岳阳市", "岳阳楼区", "机电市场888号");

            Assert.IsTrue(result);
        }

        /// <summary>
        /// 更新订单发件人
        /// </summary>
        [TestMethod]
        public void TestUpdateOrderSeller()
        {
            var result = _repository.UpdateOrderSeller(new OrderSelectKeyModel()
            {
                ShopId = _shop.Id,
                PlatformOrderId = "C464995937633969811",
            }, "Test001", "发件人测试", "18575506910", "", "湖南省长沙市雨花区机电市场203号");

            Assert.IsTrue(result);
        }

        /// <summary>
        /// 更新订单备注
        /// </summary>
        [TestMethod]
        public void TestUpdateOrderSellerRemark()
        {
            var result = _repository.UpdateOrderSellerRemark(new OrderSelectKeyModel()
            {
                ShopId = _shop.Id,
                PlatformOrderId = "C464995937633969811",
            }, "1", "测试mongodb更新备注");

            Assert.IsTrue(result);
        }

        /// <summary>
        /// 更新 代收货款 和 保价金额
        /// </summary>
        [TestMethod]
        public void TestUpdateCodAmountOrInSureAmount()
        {
            var result = _repository.UpdateCodAmountOrInSureAmount(new List<OrderSelectKeyModel> { new OrderSelectKeyModel()
            {
                ShopId = _shop.Id,
                PlatformOrderId = "C464995937633969811",
            }
            }, "cod", 125, "assign_amount", 128, 50);

            Assert.IsTrue(result);
        }

        #endregion

        #region 数据库策略测试

        [TestMethod]
        public void TestIsMongoDBReady()
        {
            var rs = PolicyService.IsMongoDBReady();
        }

        [TestMethod]
        public void TestCreateOrderRepository()
        {
            var rs = PolicyService.CreateOrderRepository();
        }

        [TestMethod]
        public void TestInitMongoDBOrders()
        {
            PolicyService.InitMongoDBOrders();
        }
        #endregion


        #region 导出客户配置查询字段
        [TestMethod]
        public void ExportSearchFieldTest()
        {
            OrderFilterService filterService = new OrderFilterService();
            ShopService shopService = new ShopService();
            var filters = filterService.Get().ToList();
            Dictionary<string, List<string>> dic = new Dictionary<string, List<string>>();
            filters.ForEach(filter =>
            {
                var shop = shopService.Get(filter.ShopId);
                if (shop == null)
                    return;
                var jtoken = JsonConvert.DeserializeObject<JToken>(filter.Config);
                var fields = jtoken.Value<JArray>("Filter").ToList();
                var values = new List<string>();
                fields.ForEach(feild =>
                {
                    var fName = ConvertToFieldCName(feild.Value<string>("Name"));
                    if (fName.IsNullOrEmpty())
                        return;
                    values.Add(fName);
                });

                if (!dic.Keys.Contains(shop.ShopName))
                    dic.Add(shop.ShopName, values);
            });

            StringBuilder text = new StringBuilder();
            foreach (var key in dic.Keys)
            {
                text.AppendLine($"{key},{dic[key]}");
            }

            var path = AppDomain.CurrentDomain.BaseDirectory + "log\\";
            using (StreamWriter sr = new StreamWriter(path + @"SearchFields.log", false))
            {
                sr.WriteLine(text.ToString2());
            }
        }


        private string ConvertToFieldCName(string ename)
        {
            List<CEName> names = new List<CEName>()
            {
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "留言备注",EName="MsgOrRemark"},
                new CEName(){CName = "备注旗帜",EName="SellerRemarkFlag"},
                new CEName(){CName = "快递模版",EName="ExpressTemplate"},
                new CEName(){CName = "快捷筛单",EName="QuickScreenOrder"},
                new CEName(){CName = "打印状态",EName="PrintStatus"},
                new CEName(){CName = "订单分类",EName="CategoryId"},
                new CEName(){CName = "仓库",EName="Warehouse"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
                new CEName(){CName = "省份",EName="ToProvince"},
            };

            if (ename == "ToProvince")
                return "省份";
            else if (ename == "MsgOrRemark")
                return "留言备注";
            else if (ename == "SellerRemarkFlag")
                return "备注旗帜";
            else if (ename == "ExpressTemplate")
                return "快递模版";
            else if (ename == "QuickScreenOrder")
                return "快捷筛单";
            else if (ename == "PrintStatus")
                return "打印状态";
            else if (ename == "CategoryId")
                return "订单分类";
            else if (ename == "Warehouse")
                return "仓库";
            else if (ename == "OrderBy")
                return "订单排序";
            else if (ename == "PlatformOrderId")
                return "订单编号";
            else if (ename == "BuyerWangWang")
                return "买家旺旺";
            else if (ename == "SellerRemark")
                return "卖家备注";
            else if (ename == "ToName")
                return "收件人姓名";
            else if (ename == "ToPhone")
                return "收件人电话";
            else if (ename == "ToAddress")
                return "收件人地址";
            else if (ename == "LastWaybillCode")
                return "运单号";
            else if (ename == "PrintedSerialNumber")
                return "打印流水号";
            else if (ename == "TotalWeight")
                return "订单重量起止";
            else if (ename == "TotalAmount")
                return "订单金额起止";
            else if (ename == "ProductItemCount")
                return "商品数量起止";
            else if (ename == "ProductKindCount")
                return "商品种数起止";
            else if (ename == "ProductSubject")
                return "商品标题";
            else if (ename == "ProductSubjectNotContains")
                return "商品标题不包含";
            else if (ename == "ShortTitle")
                return "商品简称";
            else if (ename == "ShortTitleNotContains")
                return "商品简称不包含";
            else if (ename == "ProductCargoNumber")
                return "商品货号";
            else if (ename == "ProductCargoNumberNotContains")
                return "商品货号不包含";
            else if (ename == "ProductId")
                return "商品ID";
            else if (ename == "ColorAndSize")
                return "规格颜色、尺码";
            else if (ename == "ColorAndSizeNotContains")
                return "规格颜色、尺码不包含";
            else if (ename == "CargoNumber")
                return "单品货号";
            else if (ename == "CargoNumberNotContains")
                return "单品货号不包含";
            else
                return "";
        }

        #endregion


        [TestMethod]
        public void TestUpdateBalance11()
        {
            int tryCount = 10;
            try
            {
                ShopService _shopService = new ShopService();
                var shops = _shopService.Get().ToList();

                List<string> status = new List<string>() { "waitbuyerpay", "waitsellersend", "waitbuyerreceive", "success", "cancel", "confirm_goods_but_not_fund" };
                var random = new Random();
                for (int i = 1; i <= tryCount; i++)
                {
                    List<double> updateTimes = new List<double>();
                    List<double> queryTimes = new List<double>();
                    Parallel.ForEach(shops, shop =>
                    {
                        try
                        {
                            _repository = new OrderMongoRepository();
                            CommonSettingService _commonSettingService = new CommonSettingService();

                            StringBuilder log = new StringBuilder();
                            var filterDoc = new BsonDocument("ShopId", shop.Id);
                            var count = _repository.Count(filterDoc);
                            if (count == 0) return;

                            log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】订单总数：{count}");
                            int pageCount = Math.Ceiling(count * 1.0 / 500).ToInt();
                            int pageIndex = new Random().Next(1, pageCount);
                            var model = new OrderSearchModel() { PageIndex = pageIndex, PageSize = 500, NeedPagging = true };
                            model.Filters = new List<OrderSearchFieldModel>()
                            {
                                new OrderSearchFieldModel() { TableAlias="o",TableName="P_Order",Name="ShopId",Contract="in",Value=shop.Id.ToString(),FieldType="int"},
                                new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "PlatformStatus", Contract = "=", Value = "waitsellersend", FieldType = "string" },
                                new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "CreateTime", Contract = "between", Value = "2019-04-01 00:00:00", ExtValue = "2019-06-20 15:59:59.999", FieldType = "datetime" }
                            };

                            var sw1 = new Stopwatch();
                            sw1.Start();
                            var systemSetting = _commonSettingService.GetSystemSetting(shop.Id);
                            var result = _repository.GetOrders(model, systemSetting);
                            var orders = result?.Rows?.ToList() ?? new List<Order>();
                            var queryCondition = result?.QueryCondition.ToString2();
                            log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】订单数量：{orders.Count}");
                            log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】查询条件：{queryCondition}");
                            sw1.Stop();
                            var queryElapsedTime = sw1.Elapsed.TotalMilliseconds;
                            queryTimes.Add(queryElapsedTime);
                            log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】查询{(pageIndex - 1) * 500}~{pageIndex * 500}条订单数据耗时：{queryElapsedTime}(ms)");

                            if (orders == null || orders.Count == 0)
                                return;

                            var sw2 = new Stopwatch();
                            sw2.Start();

                            orders.ForEach(o =>
                            {
                                o.PlatformStatus = status[random.Next(0, status.Count - 1)];
                                o.PayTime = DateTime.Now;
                                o.ModifyTime = DateTime.Now.AddMinutes(random.Next(-10, 100));
                                o.SellerRemark = new Guid().ToString("N");
                                o.BuyerRemark = new Guid().ToString("N");
                                o.ToName = "大锤-" + new Guid().ToString("N");
                                o.ToPhone = "136" + random.Next(1000, 9999) + random.Next(1000, 9999);
                                foreach (var item in o.OrderItems)
                                {
                                    item.Color = "红色" + random.Next(1, 1000);
                                    item.Size = "XL" + random.Next(1, 1000);
                                }
                            });
                            _repository.BulkMerger(orders);
                            sw2.Stop();
                            var updateElapsedTime = sw2.Elapsed.TotalMilliseconds;
                            log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】批量更新{orders.Count}条数据，耗时：{updateElapsedTime}(ms)");
                            updateTimes.Add(updateElapsedTime);

                            Log.WriteLine(log.ToString2());
                            //ThreadPool.QueueUserWorkItem(state =>
                            //{

                            //});
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"【TestUpdateBalance】 循环内异常：{ex.Message}\n{ex.StackTrace}");
                        }
                    });
                    //Thread.Sleep(100);
                    string statistic = string.Empty;
                    if (queryTimes.Count > 0)
                        statistic += $"查询最大耗时：{queryTimes.Max()}(ms),查询最小耗时：{queryTimes.Min()}";
                    if (updateTimes.Count > 0)
                        statistic += (statistic.IsNullOrEmpty() ? "" : "|||") + $"更新最大耗时：{updateTimes.Max()}(ms),更新最小耗时：{updateTimes.Min()}";
                    Log.WriteLine($"第{i}次循环------{statistic}");
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"【TestUpdateBalance】异常：{ex.Message}\n{ex.StackTrace}");
            }
        }

        [TestMethod]
        public void TestQueryBalance11()
        {
            int tryCount = 10;
            try
            {
                ShopService _shopService = new ShopService();
                var shops = _shopService.Get().ToList();

                for (int i = 1; i <= tryCount; i++)
                {
                    List<double> times = new List<double>();
                    Parallel.ForEach(shops, shop =>
                    {
                        try
                        {
                            _repository = new OrderMongoRepository();
                            CommonSettingService _commonSettingService = new CommonSettingService();

                            StringBuilder log = new StringBuilder();
                            var filterDoc = new BsonDocument("ShopId", shop.Id);
                            var count = _repository.Count(filterDoc);
                            if (count == 0) return;


                            log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】订单总数：{count}");
                            int pageCount = Math.Ceiling(count * 1.0 / 500).ToInt();
                            int pageIndex = new Random().Next(1, pageCount);
                            var model = new OrderSearchModel() { PageIndex = pageIndex, PageSize = 500, NeedPagging = true };
                            model.Filters.Add(new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "ShopId", Contract = "in", Value = shop.Id.ToString(), FieldType = "int" });
                            model.Filters.Add(new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "CreateTime", Contract = "between", Value = "2019-06-10 00:00:00", ExtValue = "2019-06-20 15:59:59.999", FieldType = "datetime" });
                            model.Filters.Add(new OrderSearchFieldModel() { TableAlias = "o", TableName = "P_Order", Name = "PlatformStatus", Contract = "=", Value = "waitsellersend", FieldType = "string" });
                            var filters = new List<OrderSearchFieldModel>()
                            {
                                new OrderSearchFieldModel(){TableAlias="o",TableName="P_Order",Name="IsPreordain",Contract="=",Value="0",FieldType="bool"},
                                new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="productCargoNumber",Contract="!=",Value="",FieldType="string"},
                                new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="Color",Contract="!=",Value="黑色",FieldType="string"},
                                new OrderSearchFieldModel(){TableAlias="oi",TableName="P_OrderItem",Name="Size",Contract="like",Value="XL",FieldType="string"},
                            };
                            var random = new Random();
                            model.Filters.Add(filters[random.Next(0, filters.Count - 1)]);
                            model.Filters.Add(filters[random.Next(0, filters.Count - 1)]);
                            model.Filters = model.Filters.Distinct().ToList();

                            var queryFields = model.Filters.Select(m => m.Name).ToList();

                            var sw1 = new Stopwatch();
                            sw1.Start();
                            var systemSetting = _commonSettingService.GetSystemSetting(shop.Id);
                            filterDoc = _repository.GetWhereSql(model, systemSetting);
                            var result = _repository.GetOrders(model, systemSetting);
                            var orders = result?.Rows?.ToList() ?? new List<Order>();
                            var queryCondition = result?.QueryCondition.ToString2();

                            log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】订单数量：{orders.Count}");
                            log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】查询条件：{queryCondition}");
                            sw1.Stop();
                            var elapsedTime = sw1.Elapsed.TotalMilliseconds;
                            log.AppendLine($"店铺【{shop.ShopName}-{shop.Id}】查询{(pageIndex - 1) * 500}~{pageIndex * 500}条订单数据耗时：{elapsedTime}(ms)");
                            times.Add(elapsedTime);

                            #region 订单打印订单数据查询条件日志记录
                            if (CustomerConfig.IsDebug)
                            {
                                var path = AppDomain.CurrentDomain.BaseDirectory + "log\\查询Test.log";
                                var logTryCount = 5;
                                for (int ii = 1; ii <= logTryCount; ii++)
                                {
                                    try
                                    {
                                        var sb_log = new StringBuilder();
                                        var sqllog = $"\r\n-------------------{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}-----------------------\r\n{log.ToString2()}";
                                        using (StreamWriter sr = new StreamWriter(path, true))
                                        {
                                            sr.WriteLine(sqllog);
                                        }
                                        break;
                                    }
                                    catch (Exception ex)
                                    {
                                        if (ii == logTryCount)
                                            Log.WriteError("写入日志异常：" + ex.Message);
                                    }
                                    Thread.Sleep(100);
                                }
                            }
                            #endregion
                        }
                        catch (Exception ex)
                        {
                            Log.WriteError($"【TestQueryBalance】 循环内异常：{ex.Message}\n{ex.StackTrace}");
                        }
                    });
                    //Thread.Sleep(100);
                    #region 订单打印订单数据查询条件日志记录
                    if (CustomerConfig.IsDebug)
                    {
                        var path = AppDomain.CurrentDomain.BaseDirectory + "log\\查询Test.log";
                        var logTryCount = 5;
                        for (int ii = 1; ii <= logTryCount; ii++)
                        {
                            try
                            {
                                var sqllog = $"\r\n*********************************第{i}次循环*********************************************\r\n";
                                sqllog += $"最大耗时：{times.Max()}(ms),最小耗时：{times.Min()}";
                                using (StreamWriter sr = new StreamWriter(path, true))
                                {
                                    sr.WriteLine(sqllog);
                                }
                                break;
                            }
                            catch (Exception ex)
                            {
                                if (ii == logTryCount)
                                    Log.WriteError("写入日志异常：" + ex.Message);
                            }
                            Thread.Sleep(100);
                        }
                    }
                    #endregion
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"【TestQueryBalance】异常：{ex.Message}\n{ex.StackTrace}");
            }

        }

        [TestMethod]
        public void TestFind()
        {
            try
            {
                LogForOperatorRepository _logRepository = new LogForOperatorRepository();
                //var filter = Builders<LogForOperator>.Filter.Nin("OperatorType", new List<string> { "增量同步订单" });
                var filter = Builders<LogForOperator>.Filter.Gte(y => y.StartTime, Convert.ToDateTime("2020-01-13"));
                filter &= Builders<LogForOperator>.Filter.Lte(y => y.StartTime, Convert.ToDateTime("2020-01-20 23:59:59"));

                var projection = Builders<LogForOperator>.Projection
                                    .Exclude(x => x.Request)
                                    .Exclude(x => x.Detail)
                                    .Exclude(x => x.Response);
                var sort = Builders<LogForOperator>.Sort.Descending(x => x.StartTime);


                var LogForOperators = _logRepository.Collection;

                var bsons = LogForOperators.Find(filter)
                    .Project<LogForOperator>(projection)
                    .Sort(sort)
                    .Skip(0)
                    .Limit(500)
                    .As<BsonDocument>().ToList();

                var logList = bsons?.Select(s => new LogForOperator
                {
                    _Id = s["_id"].ToString2(),
                    StartTime = s["StartTime"].toDateTime().AddHours(8),
                    EndTime = s["EndTime"].toDateTime().AddHours(8),
                    SubLogs = s["SubLogs"] == BsonNull.Value ? null : (s["SubLogs"] as BsonArray)?.Select(sl => new LogForOperator
                    {
                        _Id = sl["_id"].ToString2(),
                        StartTime = sl["StartTime"].toDateTime().AddHours(8),
                        EndTime = sl["EndTime"].toDateTime().AddHours(8),
                        RenderSeconds = sl["RenderSeconds"] == BsonNull.Value ? 0 : sl["RenderSeconds"].ToDouble(),
                        SpendTimes = sl["SpendTimes"] == BsonNull.Value ? 0 : sl["SpendTimes"].ToDouble(),
                        ServerIP = sl["ServerIP"] == BsonNull.Value ? "" : sl["ServerIP"].ToString2(),
                        IP = sl["IP"] == BsonNull.Value ? "" : sl["IP"].ToString2(),
                        OperatorType = sl["OperatorType"] == BsonNull.Value ? "" : sl["OperatorType"].ToString2(),
                        ShopId = sl["ShopId"] == BsonNull.Value ? 0 : sl["ShopId"].ToInt(),
                        UserId = sl["UserId"] == BsonNull.Value ? 0 : sl["UserId"].ToInt(),
                        DBType = sl["DBType"] == BsonNull.Value ? "" : sl["DBType"].ToString2(),
                        SuccessCount = sl["SuccessCount"] == BsonNull.Value ? 0 : sl["SuccessCount"].ToInt(),
                        TotalCount = sl["TotalCount"] == BsonNull.Value ? 0 : sl["TotalCount"].ToInt(),
                        Description = sl["Description"].ToString2().IsNullOrEmpty() ? new OperationDescription() : JsonExtension.ToObject<OperationDescription>(sl["Description"].ToString2()),
                        IsError = sl["IsError"] == BsonNull.Value ? false : sl["IsError"].ToBool(),
                        ErrorCode = sl["ErrorCode"] == BsonNull.Value ? "" : sl["ErrorCode"].ToString2(),
                        Exception = sl["Exception"] == BsonNull.Value ? "" : sl["Exception"].ToString2(),
                        Remark = sl["Remark"] == BsonNull.Value ? "" : sl["Remark"].ToString2(),
                        //Request = sl["Request"] == BsonNull.Value ? "" : sl["Request"].ToString2(),
                        //Response = sl["Response"] == BsonNull.Value ? "" : sl["Response"].ToString2(),
                        //Detail = sl["Detail"] == BsonNull.Value ? "" : sl["Detail"].ToString2(),
                    }).ToList() ?? null,
                    RenderSeconds = s["RenderSeconds"] == BsonNull.Value ? 0 : s["RenderSeconds"].ToDouble(),
                    SpendTimes = s["SpendTimes"] == BsonNull.Value ? 0 : s["SpendTimes"].ToDouble(),
                    ServerIP = s["ServerIP"] == BsonNull.Value ? "" : s["ServerIP"].ToString2(),
                    IP = s["IP"] == BsonNull.Value ? "" : s["IP"].ToString2(),
                    OperatorType = s["OperatorType"] == BsonNull.Value ? "" : s["OperatorType"].ToString2(),
                    ShopId = s["ShopId"] == BsonNull.Value ? 0 : s["ShopId"].ToInt(),
                    UserId = s["UserId"] == BsonNull.Value ? 0 : s["UserId"].ToInt(),
                    DBType = s["DBType"] == BsonNull.Value ? "" : s["DBType"].ToString2(),
                    SuccessCount = s["SuccessCount"] == BsonNull.Value ? 0 : s["SuccessCount"].ToInt(),
                    TotalCount = s["TotalCount"] == BsonNull.Value ? 0 : s["TotalCount"].ToInt(),
                    Description = s["Description"].ToString2().IsNullOrEmpty() ? new OperationDescription() : JsonExtension.ToObject<OperationDescription>(s["Description"].ToString2()),
                    IsError = s["IsError"] == BsonNull.Value ? false : s["IsError"].ToBool(),
                    ErrorCode = s["ErrorCode"] == BsonNull.Value ? "" : s["ErrorCode"].ToString2(),
                    Exception = s["Exception"] == BsonNull.Value ? "" : s["Exception"].ToString2(),
                    Remark = s["Remark"] == BsonNull.Value ? "" : s["Remark"].ToString2(),
                    //Request = s["Request"] == BsonNull.Value ? "" : s["Request"].ToString2(),
                    //Response = s["Response"] == BsonNull.Value ? "" : s["Response"].ToString2(),
                    //Detail = s["Detail"] == BsonNull.Value ? "" : s["Detail"].ToString2(),
                })?.ToList();
            }
            catch (Exception ex)
            {

                throw;
            }


        }

        [TestMethod]
        public void Tesst2()
        {
            var startTime = Convert.ToDateTime("2020-01-12");   //设置时间或者统计表最大时间
            var now = DateTime.Now;
            while (startTime < now)
            {
                LogForOperatorRepository _logForOperatorRepository = new LogForOperatorRepository();
                var tmpStartTime = startTime;
                var tmpEndTime = tmpStartTime.AddDays(1).AddSeconds(-1);

                //var filter = Builders<LogForOperator>.Filter.Gte(y => y.StartTime, tmpStartTime);
                //filter &= Builders<LogForOperator>.Filter.Lte(y => y.StartTime, tmpEndTime);
                _logForOperatorRepository.UpdateLogOperatorStatistic2(startTime, tmpEndTime);

                startTime = startTime.AddDays(1);

                //for (var i = 1; i <= 24; i++) 
                //{
                //    var tmpStartTime = startTime;
                //    var tmpEndTime = tmpStartTime.AddHours(1).AddSeconds(-1);

                //    //var filter = Builders<LogForOperator>.Filter.Gte(y => y.StartTime, tmpStartTime);
                //    //filter &= Builders<LogForOperator>.Filter.Lte(y => y.StartTime, tmpEndTime);
                //    _logForOperatorRepository.UpdateLogOperatorStatistic2( startTime,tmpEndTime);

                //    startTime = startTime.AddHours(1);
                //}
            }
        }

        [TestMethod]
        public void Test22()
        {
              
            LogForOperatorRepository _logForOperatorRepository = new LogForOperatorRepository();
            _logForOperatorRepository.UpdateLastLogOperatorStatistic();
        }

        [TestMethod]
        public void Test3()
        {
            using (StreamReader sr = new StreamReader(@"D:\Workplace\NET\DianGuanJIaAppV2\Xinban\DianGuanJiaApp.LogStatistic\Config\operatorConfig.json"))
            {
                var json = sr.ReadToEnd();
                var list = json.ToList<JToken>();

                var operators = new List<LogOperatorType>();
                list.ForEach(item =>
                {
                    LogOperatorType type = new LogOperatorType();
                    type.OperatorType = item.Value<string>("OperatorType");
                    type.SubOperatorTypes = item.Value<JArray>("SubOperatorTypes").ToList().ConvertAll(m => m.Value<string>());
                    operators.Add(type);
                });
                LogOperatorTypeRepository _logOperatorTypeRepository = new LogOperatorTypeRepository();
                _logOperatorTypeRepository.Add(operators);
            }
        }
    }

    public class CEName
    {
        public string CName { get; set; }
        public string EName { get; set; }
    }
}
