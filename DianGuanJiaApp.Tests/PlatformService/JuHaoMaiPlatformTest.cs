using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.PlatformService.OtherPlatforms;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Utility.Extension;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Tab;

namespace DianGuanJiaApp.Tests.PlatformService
{
    [TestClass]
    public class JuHaoMaiPlatformTest
    {
        private Shop _shop;
        private ShopService _shopService = new ShopService();
        private IPlatformService _platformService;
        private IAfterSaleService _afterSaleService;

        [TestInitialize]
        public void Init()
        {
            RedisConfigService.Initialization();
            _shop = _shopService.GetShopById(3159204);
            _platformService = PlatformFactory.GetPlatformService(_shop);
            _afterSaleService = PlatformFactory.GetAfterSaleService(_shop);
            var userfx = new UserFxService().Get(5);
            var _siteContext = new SiteContext(userfx);
        }

        [TestMethod]
        public void PingTest()
        {
            _platformService.Ping();
        }

        [TestMethod]
        public void SyncProductTest()
        {
            _platformService.SyncProduct("720574425408");
            //_platformService.SyncProduct(0, DateTime.Now.AddDays(-100), DateTime.Now);
        }

        [TestMethod]
        public void SyncOrderTest()
        {
            _platformService.SyncOrder("7325194109800689078");
            //_platformService.SyncOrders(new SyncOrderParametersModel()
            //{
            //    StartTime = DateTime.Now.AddDays(-1),
            //    EndTime = DateTime.Now
            //});
        }

        [TestMethod]
        public void SyncAfterSaleOrderDetailTest()
        {
            _afterSaleService.SyncAfterSaleOrderDetail("7350963659899530564");
        }

        [TestMethod]
        public void OnlineSendTest()
        {
            var userfx = new UserFxService().Get(45);
            var _siteContext = new SiteContext(userfx);
            DeliverySendOrderModel model = new DeliverySendOrderModel()
            {
                Orders = new List<Order>()
                {
                    new Order()
                    {
                        Id = 1,
                        PlatformOrderId = "20241223114603471",
                        UserId = 5
                    }
                },
                OrderRequests = new List<OrderRequestModel>()
                {
                    new OrderRequestModel()
                    {
                        Id =1,
                        WaybillCode="1313132131"
                    }
                },
                ExpressCodeMapping = new ExpressCodeMapping()
                {
                    ExpressCompanyCode = "SFKY",
                    PlatformExpressName = "顺丰速运"
                }
            };
            _platformService.OnlineSend(model);
        }
    }
}