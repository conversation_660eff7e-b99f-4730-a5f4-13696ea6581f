using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using DianGuanJiaApp.Data;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Services.ServicesExtension;
using System.Threading;
using DianGuanJiaApp.Data.Repository;
using System.Diagnostics;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Utility;
using Top.Api;
using Top.Api.Request;
using DianGuanJiaApp.ErpWeb.Controllers;

namespace DianGuanJiaApp.Tests.PlatformService
{
    [TestClass]
    public class TaobaoMaiCaiPlatformServiceTest
    {
        private Shop _shop;
        private SiteContext _siteContext;
        private TaobaoMaiCaiPlatformService _service;
        private DianGuanJiaApp.Services.PlatformService.TaobaoApiClient _client;
        private SyncFxOrderService _syncService;
        private List<Shop> _shops;
        private MergerOrderService mergerOrderService;
        private OrderService orderService;
        private SyncProductService syncProductService = new SyncProductService();


        [TestInitialize]
        public void Init()
        {
            var rp = new DianGuanJiaApp.Data.Repository.ShopRepository();
            _shop = rp.Get(" where Id=@id", new { id = 3159034 })?.FirstOrDefault();
            var user = new UserFxRepository().Get(5);
            //_shops = rp.Get().ToList();
            //临时用线上AccessToken
            //if(_shop.ShopExtension != null)
            //    _shop.ShopExtension.AccessToken = "50002900d39A0PP7nqhZyFvgKQAxOj8jjsBoU0FheOizjrtFk1A11b5da96rjEHgx7Bt";
            // _shop.AccessToken = "50002900d39A0PP7nqhZyFvgKQAxOj8jjsBoU0FheOizjrtFk1A11b5da96rjEHgx7Bt";
            _siteContext = new SiteContext(_shop, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
            //_siteContext = new SiteContext(user, new SiteContextConfig { NeedRelationShops = false, NeedShopExpireTime = false });
            _service = new TaobaoMaiCaiPlatformService(_shop);
            //_syncService = new SyncFxOrderService(5);
            _client = new TaobaoApiClient(_shop);
            //orderService = new OrderService();
            //mergerOrderService = new MergerOrderService();
            //DapperMapping.SetEntityMapping();
        }

        [TestMethod]
        public void TestListen()
        {
            ITopClient client = new DefaultTopClient("http://gw.api.taobao.com/router/rest", "34704816", "5d9bc7b5562e0862de69623c73169175");
            var req = new AlibabaWdkMessageListenerRegisteRequest();
            //req.Topic = "WDKOP_TOPIC_NAME";
            //AlibabaWdkMessageListenerRegisteResponse rsp = client.Execute(req);
            //Console.WriteLine(rsp.Body);
        }

        [TestMethod]
        public void TestSyncOrder()
        {
            //var order=_service.SyncOrder("3979880931332516702");
            //_syncService.SyncSingleOrder("3979880931332516702",_shop);
            new NewOrderController().SyncSingleOrder("2237356454437673399", 3159034);
            return;
        }


        [TestMethod]
        public void TestSyncOrder2()
        {
            var order= _service.SyncOrder("2248145797010156793");

            //new OrderRepository("server=192.168.1.168;uid=sa;pwd=**********;database=AlibabaFenFaDB_New;max pool size=512;").Add(order);
            //new OrderItemRepository("server=192.168.1.168;uid=sa;pwd=**********;database=AlibabaFenFaDB_New;max pool size=512;").BulkInsert(order.OrderItems);
            
            //new NewOrderController().SyncSingleOrder("2235486253097673399", 3159034);
            return;
        }

        [TestMethod]
        public void TestSyncAfterSaleOrder()
        {
            //var order = _service.SyncAfterSaleOrderDetail("286528608586873392");
            var order = new SyncFxOrderService(5).SyncAfterSaleSingleOrder("286528608586873392",_shop);

            //new NewOrderController().SyncSingleOrder("2235486253097673399", 3159034);
            return;
        }

        [TestMethod]
        public void TestSyncProduct()
        {
            var products = _service.SyncProduct(3);
            return;
        }

        


    }
}
