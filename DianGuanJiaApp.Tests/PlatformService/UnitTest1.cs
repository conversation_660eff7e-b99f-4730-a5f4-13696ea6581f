using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using DianGuanJiaApp.Services.WaybillService;
using DianGuanJiaApp.Data.Model;
using System.Collections.Generic;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System.Dynamic;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility.Extension;
using System.Security.Cryptography;
using System.Text;
using System.Linq;
using DianGuanJiaApp.Services;
using System.IO;
using System.Text.RegularExpressions;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Models;
using System.Threading.Tasks;
using System.Threading;
using System.Collections.Concurrent;
using DianGuanJiaApp.Services.ServicesExtension;

namespace DianGuanJiaApp.Tests.PlatformService
{
    [TestClass]
    public class UnitTest1
    {
        [TestInitialize]
        public void Init()
        {
            RedisConfigService.Initialization();
        }
        
        [TestMethod]
        public void RecycleCaiNiaoWaybillCode()
        {
            var WaybillAuthConfig = new WayBillAuthConfig()
            {
                AppKey = "23172028",
                AppSecret = "d0b07cc2d59c70226c22a742585dc1ff",
                ApiUrl = "http://gw.api.taobao.com/router/rest",
                SessionKey = "6202a147c8b20c986ac090ZZbf15f33f2a55be329e2445d61519687"
            };
            var waybillCodes = new List<string> {
                //"71676810275245","71676811275339","71676812275391","71676814275248","71676813275338","71676818275393","71676812275503","71676818275246","71676812275249","71676813275461","71676817275299","71676817275336","71676815275460","71676810275392","71676816275247","71676811275650","71676819275651","71676815275337","71676810275556","71676812275602","71676813275244","71676815275422","71676815275752","71676814275700","71676816275558","71676816275756","71676818275604","71676814275559","71676817275341","71676819275750","71676811275424","71676817275505","71676818275755","71676813275606","71676818275425","71676815275605","71676817275652","71676813275654","71676819275463","71676816275704","71676812275560","71676811275754","71676819275340","71676817275421","71676813275423","71676817275609","71676810275603","71676813275753","71676812275701","71676811275396","71676817275751","71676818275703","71676815275342","71676816275394","71676819275608","71676818275557","71676810275702","71676813275395","71676813275507","71676815275506","71676810275504","71676811275508","71676815275653","71676811275462","71676811275607","71676814275465","71676816275426","71676818275656","71676816275657","71676819275509","71676815275511","71676815275610","71676817275464","71676811275706","71676817275510","71676819275707","71676810275561","71676813275705","71676810275655","71676814275658","71676814275427","71676818275623","71676813275809","71676819275811","71676812275857","71676814275781","71676818275524","71676811275810","71676818275779","71676810275716","71676819275482","71676812275782","71676810275858","71676812275579","71676816275780","71676813275625","71676818275680","71676815275525","71676817275483","71676814275861","71676813275786","71676811275787","71676818275784","71676810275580","71676814275682","71676816275860","71676814275719","71676818275717","71676816275718","71676811275626","71676813275526","71676818275859","71676816275681","71676815275785","71676817275812","71676818275581","71676812275683","71676810275684","71676810275783","71676815275813","71676812275862","71676813275814","71676818275816","71676814275818","71676812275720","71676817275628","71676817275440","71676815275484","71676815275865","71676810275815","71676816275817","71676818275864","71676819275627","71676812275819","71676810275863","71676813275866","71676810275721","71676815275441","71676811275867","71676817275685","71676811275527","71676819275868","71676817275869","71676816275723","71676819275788","71676816275582","71676818275722","71676813275442","71676815275686","71676815275870","71676812275485","71676817275789","71676811275443","71676818275821","71676811275725","71676813275871","71676817275727","71676819275444","71676815275587","71676810275900","71676812275824","71676815275629","71676819275694","71676815275827","71676815275907","71676811275952","71676813275531","71676817275906","71676819275953","71676816275799","71676816275596","71676816276001","71676819275910","71676810275882","71676816275695","71676812275635","71676813275908","71676817275534","71676812275598","71676811275532","71676811275829","71676814275597","71676816275493","71676810275599","71676813275828","71676819275533","71676818275883","71676814275955","71676811275909","71676814275880","71676814276002","71676817275831","71676810275877","71676810275698","71676818276000","71676816275879","71676818275878","71676810276004","71676812275697","71676819275830","71676817275954","71676812276003","71676815275634","71676817275633","71676812275956","71676812275881","71676814275696","71676815276054","71676810275957","71676817276053","71676817275497","71676816275884","71676811276051","71676819275496","71676812275536","71676817275911","71676815275832","71676818275699","71676810275636","71676815275912","71676813276007","71676819276052","71676810275537","71676817276005","71676818275637","71676815276006","71676813276050","71676816275959","71676811276008","71676819276009","71676814276101","71676815275498","71676816275638","71676813275890","71676817275888","71676814275540","71676814275639","71676810275542","71676817276152","71676815275969","71676817275968","71676816276063","71676819275887","71676815276153","71676819276151","71676813276154","71676816276020","71676812275541","71676812276022","71676815275889","71676817275925","71676811275971","71676814276021","71676811275886","71676810275924","71676816275841","71676813275970","71676811276150","71676819275972","71676819276066","71676811276065","71676815275894","71676811276107","71676813275927","71676810275896","71676816275544","71676818275543","71676816275643","71676810276155","71676817275893","71676817276072","71676811275891","71676812275843","71676818276156","71676819275892","71676812276159","71676815275931","71676817275973","71676818275642","71676817276067","71676813276026","71676812275895","71676814276158","71676814275842","71676813276069","71676815276068","71676813276106","71676818276024","71676814276064","71676811276070","71676812275640","71676810275641","71676815275974","71676815276025","71676817276109","71676815275926","71676811275928","71676819276108","71676819276071","71676810276023","71676817275930","71676819275929","71676813275932","71676816276157","71676815276110","71676811276112","71676813276111","71676817276114","71676817275845","71676814276115","71676819276113","71676811275933","71676810275844","71676817276034","71676811276032","71676818276161","71676817275647","71676819275934","71676813275649","71676812276116","71676816276162","71676810276080","71676816276077","71676814276078","71676815275648","71676818276076","71676810276075","71676814275899","71676815275846","71676818275897","71676812276079","71676818276081","71676812275980","71676812276164","71676814276163","71676813276031","71676814275979","71676813275847","71676819276165","71676816275898","71676811275848","71676811276254","71676811275546","71676812276201","71676818275939","71676818276203","71676816275935","71676815276167","71676810276202","71676819276250","71676817276171","71676810276117","71676818275982","71676815276087","71676815276252","71676817275548","71676814275936","71676814276035","71676813276205","71676812276300","71676818276118","71676817276086","71676812275937","71676814276200","71676819276085","71676816276204","71676810275938","71676813276088","71676811276169","71676813275545","71676816276256","71676814276120","71676814276083","71676818276255","71676817276251","71676819276170","71676817276166","71676810275981","71676810276122","71676812276036","71676813276168","71676816276082","71676812276084","71676814276257","71676813276253","71676812276121","71676816276119","71676810276301","71676819275849","71676818276123","71676819275547","71676812276258","71676816276124","71676815276172","71676816275940","71676813276173","71676811276174","71676810276037","71676814275941","71676818276302","71676811276089","71676819276212","71676818276260","71676812275942","71676816276261","71676818275944","71676814276262","71676817276213","71676812276263","71676818276477","71676817276289","71676810276235","71676811276334","71676818276335","71676817276374","71676813276333","71676812276475","71676815276474","71676811276560","71676817276473","71676813276559","71676814276653","71676819276655","71676812276531","71676814276530","71676810276377","71676819276561","71676818276236","71676810276476","71676815276558","71676812276376","71676810276532","71676812276654","71676814276375","71676812276423","71676813276535","71676816276341","71676812276611","71676810276339","71676818276383","71676814276342","71676810276612","71676813276564","71676813276427","71676816276534","71676816276609","71676812276343","71676818276378","71676812276381","71676813276291","71676810276382","71676814276479","71676814276337","71676811276292","71676818276566","71676815276563","71676815276426","71676816276336","71676812276480","71676817276425","71676810276424","71676817276656","71676813276658","71676813276615","71676816276379","71676811276536","71676817276562","71676816276614","71676816276478","71676818276533","71676810276344","71676814276238","71676812276338","71676814276610","71676814276380","71676818276613","71676812276239","71676815276290","71676815276657","71676810276481","71676810276565","71676816276237","71676819276537","71676818276340","71676817276294","71676811276616","71676816276384","71676817276430","71676819276293","71676811276428","71676815276346","71676814276295","71676819276429","71676817276345","71676819276434","71676819276349","71676816276242","71676811276541","71676810276297","71676814276243","71676819276542","71676817276618","71676812276545","71676812276244","71676817276487","71676811276701","71676811276391","71676810276754"
                "71676826369985","71676827370120","71676827370115","71676826370253","71676829370204","71676825369877","71676823369741","71676823370117","71676821369737","71676825369599"
            };

            var cp_code = "HTKY";

            waybillCodes.ForEach(item =>
            {
                var result = TopCaiNiaoApiService.CancelCainiaoWaybillCode(WaybillAuthConfig, cp_code, item);
                Console.WriteLine($"{result.Item1},msg:{result.Item2}");
            });

        }

        [TestMethod]
        public void RecyclePddWaybillCode()
        {
            var WaybillAuthConfig = new WayBillAuthConfig()
            {
                AppKey = "afabb0e68b3443b9a0dda11c1442a042",
                AppSecret = "bf3a8a74b9977cf0cc65e21a4cbfbca9f7962093",
                ApiUrl = "https://gw-api.pinduoduo.com/api/router",
                SessionKey = "c0e60cc9885d4c0a9b28d6a41cb587b25abc9212"
            };
            var waybillCodes = new List<string> {
                ""
            };
            var cp_code = "HTKY";

            var pddWaybillCodeApiService = new PddWaybillApiService(WaybillAuthConfig);
            waybillCodes.ForEach(item =>
            {
                var result = pddWaybillCodeApiService.CancelPinduoduoWaybillCode(cp_code, item);
                Console.WriteLine($"{result.Item1},msg:{result.Item2}");
            });

        }


        [TestMethod]
        public void TestJingDongBranchCodeGet()
        {
            var WaybillAuthConfig = new WayBillAuthConfig()
            {
                AppKey = "557C37C5BB73B0A55A1A01422F0705F4",
                AppSecret = "00c553795cd44e7aa2db5853001c968a",
                ApiUrl = "https://gw-api.pinduoduo.com/api/router",
                SessionKey = "44fc5888317a4bdc97a651c6249b15afmyzu"
            };

            var jdWaybillCodeApiService = new WuJieWaybillApiService(WaybillAuthConfig);
            var branchCodeList = jdWaybillCodeApiService.GetJingDongBranchAddressList();

        }


        [TestMethod]
        public void TestRetryer()
        {
            var context = new Polly.Context();
            context.Add("count", 1);
            Retryer.Retry<Polly.Context, string>(Action, context);

        }

        private string Action(Polly.Context context)
        {
            var count = context["count"].ToInt();
            if (count == 3)
            {
                Console.WriteLine("执行成功");
                return "b";
            }
            else
            {
                context["count"] = (++count);
                Console.WriteLine("abc");
                throw new RetryException("xxx");
            }
        }
        //https://openapi.jinritemai.com/product/getGoodsCategory?app_key=your_app_key_here&method=product.getGoodsCategory&param_json={"cid":"0"}&timestamp=2018-06-19 16:06:59&v=1&sign=your_sign_here

        [TestMethod]
        public void TestJinRiTeMailApi()
        {
            var key = "3353694527402736221";
            var secret = "d115d0abd483b2b9b2ea8df8edce32cd";

            //app_keyxxxmethodxxxparam_jsonxxxtimestampxxxvxxx

            var method = "product.getGoodsCategory";
            var param_json = new { cid = "0" };
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            var v = "1";

            var str = $"{secret}app_key{key}method{method}param_json{param_json.ToJson()}timestamp{timestamp}v{v}{secret}";
            var sign = DianGuanJiaApp.Utility.CommUtls.MD5(str);

            var url = $"https://openapi-fxg.jinritemai.com/product/getGoodsCategory?app_key={key}&method={method}&param_json={param_json}&timestamp={timestamp}&v={v}&sign={sign}";
            Console.WriteLine(url);

            // https://openapi-fxg.jinritemai.com/product/list?app_key=your_app_key_here&method=product.list&param_json={"page":"0","size":"20"}&timestamp=2018-06-19 16:06:59&v=1&sign=your_sign_here

            method = "product.list";
            var param_json_02 = "{\"page\":\"0\",\"size\":\"20\"}";

            var str2 = $"{secret}app_key{key}method{method}param_json{param_json_02}timestamp{timestamp}v{v}{secret}";
            var sign2 = DianGuanJiaApp.Utility.CommUtls.MD5(str2);

            var productListUrl = $"https://openapi-fxg.jinritemai.com/product/list?app_key={key}&method={method}&param_json={param_json_02}&timestamp={timestamp}&v={v}&sign={sign2}";

            Console.WriteLine(productListUrl);


            //https://openapi-fxg.jinritemai.com/order/list?app_key=your_app_key_here&method=order.list&param_json={"end_time":"2018/05/31 16:01:02","is_desc":"1","page":"0","size":"10","start_time":"2018/04/01 15:03:58"}&timestamp=2018-06-14 16:06:59&v=1&sign=your_sign_here

            method = "order.list";
            var param_json_03 = new
            {
                end_time = DateTime.Now,
                is_desc = "1",
                page = "0",
                size = "10",
                start_time = DateTime.Now.AddMonths(-1)
            };
            str = $"{secret}app_key{key}method{method}param_json{param_json_03.ToJson()}timestamp{timestamp}v{v}{secret}";
            sign = DianGuanJiaApp.Utility.CommUtls.MD5(str);

            var orderList = $"https://openapi-fxg.jinritemai.com/order/list?app_key={key}&method={method}&param_json={param_json_03.ToJson()}&timestamp={timestamp}&v={v}&sign={sign}";
        }

        [TestMethod]
        public void TestListToDataTable()
        {
            var list = new List<OrderForUpdateExpressPrintState>() {
                new OrderForUpdateExpressPrintState() {
                    Id=100,
                    ExpressPrintedCount=1,
                    ExpressPrintTimes=2,
                    LastExpressPrintTime=DateTime.Now,
                    LastExpressTemplateId=3,
                    LastWaybillCode="abc123",
                    PlatformOrderId="aabbcc112233",
                    PrintedBuyerHashCode="xxxxyyyy",
                    PrintedSerialNumber="1-1-2",
                    PrintEffectiveCount=4,
                    ShopId=200,
                },
                new OrderForUpdateExpressPrintState() {
                    Id=300,
                    ExpressPrintedCount=10,
                    ExpressPrintTimes=20,
                    LastExpressPrintTime=DateTime.Now,
                    LastExpressTemplateId=30,
                    LastWaybillCode="abc123xxx",
                    PlatformOrderId="aabbcc112233xxx",
                    PrintedBuyerHashCode="xxxxyyyyxxxx",
                    PrintedSerialNumber="1-1-2xx",
                    PrintEffectiveCount=40,
                    ShopId=400,
                },
            };

            var dt = DianGuanJiaApp.Data.DataConverter.CollectionHelper.ConvertTo(list);

            int? a = null;
            bool? b = null;
            var list02 = new List<dynamic>() {
                new {
                    Age=123,
                    Name="啊四道口附近",
                    Time=DateTime.Now,
                    IsTrue=true,
                    IsDecimal=23.34M,
                    IsNull=a,
                    IsBoolNull=b,
                }
            };
            //foreach (var data in list02)
            //{
            //    if (data is ExpandoObject)
            //    {
            //        var aa = ((IDictionary<string, object>)data).ContainsKey("Age");
            //        var bb = data.GetType().GetProperty("Age");
            //    }

            //}

            string json = JsonConvert.SerializeObject(list02[0]);

            var jtoken = JsonConvert.DeserializeObject<JToken>(json);

            foreach (var item in jtoken.Values())
            {
                var s = item.Path;
                var t = item.Type;
                var st = t.GetType();
            }

            var dt02 = DianGuanJiaApp.Data.DataConverter.CollectionHelper.ConvertTo(list02);


            List<KeyValuePair<string, dynamic>> paramList = new List<KeyValuePair<string, dynamic>>()
            {
                new KeyValuePair<string, dynamic>("a",new List<dynamic>() {
                    new {
                        Age=24,
                        Name="abc"
                    },
                    new {
                        Age=18,
                        Name="xxxx"
                    }
                }),
                new KeyValuePair<string, dynamic>("b","abvc"),
                new KeyValuePair<string, dynamic>("c",123),
            };

            foreach (var item in paramList)
            {
                var v = item.Value;
                var t = v.GetType();

            }

        }

        [TestMethod]
        public void TestGetServerIp()
        {
            var ip = DianGuanJiaApp.Utility.Net.HttpUtility.GetServerIP();
        }


        [TestMethod]
        public void TestDateTimeHavle()
        {
            var s = DateTime.Now.AddMonths(-18);
            var e = DateTime.Now;
            var timeList = CommUtls.DateTimeSplit(s, e, 30, TimeScale.dd);

        }
        [TestMethod]
        public void TestOpenApiSign()
        {
            var dict = new Dictionary<string, string>();
            dict.Add("method", "order.list.query");
            dict.Add("appKey", "204a5673ea0d95789834bb19337d886e");
            dict.Add("timestamp", "1570786352839");
            dict.Add("param", "{\"startCreated\":\"2019-09-01 00:00:00\",\"endCreated\":\"2019-09-01 00:00:00\",\"page\":1,\"pageSize\":100}");
            var sign = Sign(dict, "918dbd16b17e1c112436c00a93dbea1a");
        }
        /// <summary>
        /// 店管家开放协议签名算法
        /// </summary>
        /// <param name="paramDic">请求参数</param>
        /// <param name="appSecret">app秘钥，授权方式一时为分配给店管家的应用秘钥，授权方式二为商家店铺的秘钥</param>
        /// <returns></returns>
        public string Sign(Dictionary<string, string> paramDic, string appSecret)
        {
            //1.将请求参数按参数名进行排序
            var keys = paramDic.Select(kv => kv.Key).ToList();
            keys.Sort();
            var tmp = "";
            //2.将排序好的参数名和参数值拼装在一起
            foreach (var key in keys)
            {
                tmp += key + paramDic[key];
            }
            //3.把拼装好的字符串前后加上appSecret进行MD5加密
            tmp = appSecret + tmp + appSecret;
            //4.把拼装好的字符串前后加上appSecret进行MD5加密
            var result = Encoding.UTF8.GetBytes(tmp);
            var md5 = new MD5CryptoServiceProvider();
            var output = md5.ComputeHash(result);
            var sign = BitConverter.ToString(output).Replace("-", "").ToUpper();
            return sign;
        }

        [TestMethod]
        public void YouZanQianYiTest()
        {
            CommonSettingService _commonSettingService = new CommonSettingService();
            ShopService _shopService = new ShopService();
            var shops = _shopService.GetShopByPlatformType("YouZan");

            shops.ForEach(shop =>
            {
                var keys = new List<string> { "CustomConditionSet", "CutomAdvancedContidionSet" };
                var commSets = _commonSettingService.GetSets(keys, shop.Id);
                var cutomAdvancedContidionSet = commSets.FirstOrDefault(m => m.Key == "CutomAdvancedContidionSet");
                var customConditionSet = commSets?.FirstOrDefault(m => m.Key == "CustomConditionSet");

                try
                {
                    var conditionVal = customConditionSet?.Value.ToString2();
                    if (!conditionVal.IsNullOrEmpty())
                    {
                        var jToken = JsonConvert.DeserializeObject<JToken>(conditionVal);
                        if (jToken != null)
                        {
                            var conditions = jToken.Value<JArray>("OrderPrint")?.ToJson() ?? "[]";
                            var newCond = conditions.Replace("\"ColorAndSize\"", "\"ColorAndSize2\"").Replace("\"ColorAndSizeNotContains\"", "\"ColorAndSizeNotContains2\"");

                            var freeCond = jToken.Value<JArray>("FreePrint")?.ToJson() ?? "[]";
                            var newCommSetVal = "{\"OrderPrint\":" + newCond + ",\"FreePrint\":" + freeCond + "}";

                            customConditionSet.Value = newCommSetVal;
                            //_commonSettingService.Update(cutomAdvancedContidionSet);
                        }
                    }


                    var advanceCondVal = cutomAdvancedContidionSet?.Value.ToString2();
                    if (!advanceCondVal.IsNullOrEmpty())
                    {
                        var jToken = JsonConvert.DeserializeObject<JToken>(advanceCondVal);
                        if (jToken != null)
                        {
                            var conditions = jToken.Value<JArray>("OrderPrint")?.ToJson() ?? "[]";
                            var newCond = conditions.Replace("\"Color\"", "\"ColorAndSize\"");

                            var freeCond = jToken.Value<JArray>("FreePrint")?.ToJson() ?? "[]";
                            var newCommSetVal = "{\"OrderPrint\":" + newCond + ",\"FreePrint\":" + freeCond + "}";

                            cutomAdvancedContidionSet.Value = newCommSetVal;
                            //_commonSettingService.Update(cutomAdvancedContidionSet);
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw;
                }


            });

        }

        [TestMethod]
        public void Test1()
        {
            var obj = new List<dynamic>
                {
                    new { Name = "ToProvince",Text="所有省份" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "MsgOrRemark",Text="选择查询：留言备注" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "SellerRemarkFlag",Text="备注旗帜" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "ExpressTemplate",Text="快递模版" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "QuickScreenOrder",Text="快捷筛单" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "PrintStatus",Text="打印状态" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "CategoryId",Text="未分类+已分类订单" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "Warehouse",Text="全部仓" ,Display_OP = false,Display_FP = false, Type = "Common"},
                    new { Name = "PlatformOrderId",Text="多个订单编号(,)分隔" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "BuyerWangWang",Text="多个买家旺旺(,)分隔" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "SellerRemark",Text="卖家备注" ,Display_OP = false,Display_FP = true, Type = "Common"},
                    new { Name = "ToName",Text="收件人姓名" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "ToPhone",Text="收件人电话" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "ToAddress",Text="收件人地址" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "LastWaybillCode",Text="运单号" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "PrintedSerialNumber",Text="打印流水号" ,Display_OP = false,Display_FP = true, Type = "Common"},
                    new { Name = "MoreThanTotalWeight",Text="订单重量起" ,Display_OP = false,Display_FP = false, Type = "Common"},
                    new { Name = "LessThanTotalWeight",Text="订单重量止" ,Display_OP = false,Display_FP = false, Type = "Common"},
                    new { Name = "MoreThanTotalAmount",Text="订单金额起" ,Display_OP = false,Display_FP = false, Type = "Common"},
                    new { Name = "LessThanTotalAmount",Text="订单金额止" ,Display_OP = false,Display_FP = false, Type = "Common"},
                    new { Name = "MoreThanProductItemCount",Text="商品数量起" ,Display_OP = false,Display_FP = false, Type = "Common"},
                    new { Name = "LessThanProductItemCount",Text="商品数量止" ,Display_OP = false,Display_FP = false, Type = "Common"},
                    new { Name = "ProductSubject",Text="商品标题" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "ProductSubjectNotContains",Text="商品标题不包含" ,Display_OP = false,Display_FP = false, Type = "Common"},
                    new { Name = "ProductCargoNumber",Text="商品货号" ,Display_OP = true,Display_FP = true, Type = "Common"},
                    new { Name = "ProductCargoNumberNotContains",Text="商品货号不包含" ,Display_OP = false,Display_FP = true, Type = "Common"},
                    new { Name = "ProductId",Text="商品ID" ,Display_OP = false,Display_FP = false, Type = "Common"},

                    // 订单打印独有查询条件
                    new { Name = "ShortTitle",Text="商品简称" ,Display_OP = true,Display_FP = false, Type = "OrderPrint"},
                    new { Name = "ShortTitleNotContains",Text="商品简称不包含" ,Display_OP = false,Display_FP = false, Type = "OrderPrint"},
                    new { Name = "MoreThanProductKindCount",Text="商品种数起" ,Display_OP = false,Display_FP = false, Type = "OrderPrint"},
                    new { Name = "LessThanProductKindCount",Text="商品种数止" ,Display_OP = false,Display_FP = false, Type = "OrderPrint"},
                    new { Name = "Color",Text="规格颜色" ,Display_OP = true,Display_FP = true, Type = "OrderPrint"},
                    new { Name = "Size",Text="规格尺码" ,Display_OP = true,Display_FP = true, Type = "OrderPrint"},
                    new { Name = "NotContainsColor",Text="规格颜色不包含" ,Display_OP = false,Display_FP = false, Type = "OrderPrint"},
                    new { Name = "NotContainsSize",Text="规格尺码不包含" ,Display_OP = false,Display_FP = false, Type = "OrderPrint"},
                    new { Name = "CargoNumber",Text="单品货号" ,Display_OP = false,Display_FP = false, Type = "OrderPrint"},
                    new { Name = "CargoNumberNotContains",Text="单品货号不包含" ,Display_OP = false,Display_FP = false, Type = "OrderPrint"},
                    new { Name = "BuyerRemark",Text="买家留言" ,Display_OP = false,Display_FP = false, Type = "OrderPrint"},
                    // 自由打印独有查询条件
                    new { Name = "OrderSource",Text="全部来源" ,Display_OP = false,Display_FP = true, Type = "FreePrint"},
                    new { Name = "ImportBatchNo",Text="导入批次" ,Display_OP = false,Display_FP = true, Type = "FreePrint"},
                    new { Name = "ColorAndSize",Text="属性规格" ,Display_OP = true,Display_FP = true, Type = "FreePrint"},
                    new { Name = "ColorAndSizeNotContains",Text="属性规格不包含" ,Display_OP = true,Display_FP = true, Type = "FreePrint"},
                    // 有赞平台查询条件
                    //new { Name = "ColorAndSize2",Text="商品规格" ,Display = true},
                    //new { Name = "ColorAndSizeNotContains2",Text="商品规格不包含" ,Display = true},
                    // 拼多多平台查询条件
                    //new { Name = "LastShipTime",Text="订单剩余时间" ,Display_OP = true,Display_FP = true, Type = "Common"},
                };


            var json = obj.ToJson();

            CommonSettingService _commService = new CommonSettingService();
            var commSet = _commService.GetSets(new List<string> { "CommonDefaultSearchConfig" }, 0).FirstOrDefault();
            if (commSet == null)
                _commService.Add(new CommonSetting { ShopId = 0, Key = "CommonDefaultSearchConfig", Value = json });
            else
            {
                commSet.Value = json;
                _commService.Update(commSet);
            }
        }


        [TestMethod]
        public void Test2()
        {
            CommonSettingService _commService = new CommonSettingService();
            var value =  _commService.GetStringOnlyByShopId("/Temp/TouTiao/PushDb/7152331588175513101",1);
            var json = _commService.GetString("CommonDefaultSearchConfig", 0);
            try
            {
                var jArray = JsonConvert.DeserializeObject<JArray>(json);
                if (jArray != null && jArray.Count > 0)
                {
                    // 默认可见查询条件
                    var orderPrintItems = jArray.Where(m => m.Value<bool>("Display_OP") && (m.Value<string>("Type") == "OrderPrint" || m.Value<string>("Type") == "Common")).ToList();
                    var freePrintItems = jArray.Where(m => m.Value<bool>("Display_FP") && (m.Value<string>("Type") == "FreePrint" || m.Value<string>("Type") == "Common")).ToList();

                    var defaultSearchConfig = new
                    {
                        OrderPrint = orderPrintItems.Select(m => new { Name = m.Value<string>("Name"), Text = m.Value<string>("Text") }).ToList(),
                        FreePrint = freePrintItems.Select(m => new { Name = m.Value<string>("Name"), Text = m.Value<string>("Text") }).ToList(),
                    };

                    var defaultCustomConditionSetVal = defaultSearchConfig.ToJson();
                }

            }
            catch (Exception ex)
            {

                throw;
            }
        }

        [TestMethod]
        public void UpdateTouTiaoSubPlatformTypeTest()
        {
            ShopService _shopService = new ShopService();
            var ptLst = new List<string> { "ZhiDian", "DouYinXiaoDian", "TouTiaoXiaoDian", "LuBan" };
            StringBuilder sqls = new StringBuilder();
            var shops = _shopService.GetShopByPlatformType("toutiao");
            var ids = $"{string.Join(",", shops.Select(m => m.Id).ToList())}";
            shops.ForEach(shop =>
            {
                var newlst = new List<string>();
                var subPts = shop.SubPlatformType.ToString2();
                var lst = subPts.Split(",".ToArray(), StringSplitOptions.RemoveEmptyEntries)?.ToList();
                lst.ForEach(pt =>
                {
                    if (!newlst.Contains(pt) && ptLst.Contains(pt))
                    {
                        newlst.Add(pt);
                    }
                });

                if (!newlst.Any())
                    newlst.Add("ZhiDian");
                var sql = $"UPDATE P_Shop SET SubPlatformType='{(string.Join(",", newlst))}' WHERE Id={shop.Id} AND PlatformType='TouTiao';";
                sqls.AppendLine(sql);
            });

            Log.WriteLine(ids.ToString2());
            Log.WriteLine(sqls.ToString2());
        }

        [TestMethod]
        public void StatisticProductItemCountInfo()
        {
            try
            {
                ShopService _shopService = new ShopService();
                var json = string.Empty;
                var path = @"C:\Users\<USER>\Desktop\商品数量查询.json";
                using (StreamReader sr = new StreamReader(path))
                {
                    json = sr.ReadToEnd();
                    json = Regex.Replace(json, @"/\*\s\d+\s\*/", "");
                }
                var jarr = JsonConvert.DeserializeObject<JArray>(json)?.ToList();
                var groupResult = jarr.Select(m => new { ShopId = m.Value<int>("_id"), Count = m.Value<int>("Count") }).OrderByDescending(m => m.Count).ToList();
                var shopIds = groupResult.Select(m => m.ShopId).ToList();
                var shops = _shopService.GetShopByIds(shopIds);
                var result = shops.GroupBy(m => m.PlatformType).Select(m => new { PlatformType = m.Key, Count = m.Count() }).ToList().ToJson();

                path = @"C:\Users\<USER>\Desktop\商品数量排序.json";
                using (StreamReader sr = new StreamReader(path))
                {
                    json = sr.ReadToEnd();
                    json = Regex.Replace(json, @"/\*\s\d+\s\*/", "");
                }
                jarr = JsonConvert.DeserializeObject<JArray>(json)?.ToList();
                groupResult = jarr.Select(m => new { ShopId = m.Value<int>("_id"), Count = m.Value<int>("Count") }).OrderByDescending(m => m.Count).ToList();
                shopIds = groupResult.Select(m => m.ShopId).ToList();
                shops = _shopService.GetShopByIds(shopIds);
                result = shops.GroupBy(m => m.PlatformType).Select(m => new { PlatformType = m.Key, Count = m.Count() }).ToList().ToJson();
            }
            catch (Exception ex)
            {

                throw;
            }
        }
        
        [TestMethod]
        public void TestTokenExplain()
        {
            var token = "C7C67F0C2558BAD7";
            var str = Utility.DES.DecryptUrl(token,CustomerConfig.LoginCookieEncryptKey);
        }

        [TestMethod]
        public void TestProductItemCount()
        {
            ShopService _shopService = new ShopService();
            CommonSettingService _commService = new CommonSettingService();
            var shops = _shopService.Get().ToList();
            var lst = new List<Tmp>();

            shops.ForEach(shop =>
            {
                var keys = new List<string> { "PaggingOrderBySet", "CustomConditionSet" };
                var commSets = _commService.GetSets(keys, shop.Id);

                var paggingOrderBySet = commSets?.FirstOrDefault(m => m.Key == "PaggingOrderBySet");
                var customConditionSet = commSets?.FirstOrDefault(m => m.Key == "CustomConditionSet");
                Tmp t = new Tmp { PlatformType = shop.PlatformType };
                if (paggingOrderBySet != null && !paggingOrderBySet.Value.IsNullOrEmpty())
                {
                    var pageToken = JsonConvert.DeserializeObject<JToken>(paggingOrderBySet.Value);
                    var Field = pageToken?.Value<JToken>("OrderPrint")?.Value<string>("Field") ?? "";
                    if (Field.Contains("ProductItemCount"))
                        t.PageOrder = 1;

                    Field = pageToken?.Value<JToken>("FreePrint")?.Value<string>("Field") ?? "";
                    if (Field.Contains("ProductItemCount"))
                        t.PageCustomer = 1;
                }

                if (customConditionSet != null && !customConditionSet.Value.IsNullOrEmpty())
                {
                    var conditionToken = JsonConvert.DeserializeObject<JToken>(customConditionSet.Value);
                    var fields = conditionToken?.Value<JToken>("OrderPrint").ToString2() ?? "";
                    if (fields.Contains("ProductItemCount"))
                        t.ConditionOrder = 1;

                    fields = conditionToken?.Value<JToken>("FreePrint").ToString2() ?? "";
                    if (fields.Contains("ProductItemCount"))
                        t.ConditionCustomer = 1;
                }
                lst.Add(t);
            });

            var a1 = lst.Where(m => m.PageOrder == 1).GroupBy(m => m.PlatformType).Select(m => new { PT = m.Key, Count = m.Count() }).ToList().ToJson();
            var a2 = lst.Where(m => m.PageCustomer == 1).GroupBy(m => m.PlatformType).Select(m => new { PT = m.Key, Count = m.Count() }).ToList().ToJson();
            var a3 = lst.Where(m => m.ConditionOrder == 1).GroupBy(m => m.PlatformType).Select(m => new { PT = m.Key, Count = m.Count() }).ToList().ToJson();
            var a4 = lst.Where(m => m.ConditionCustomer == 1).GroupBy(m => m.PlatformType).Select(m => new { PT = m.Key, Count = m.Count() }).ToList().ToJson();

        }

        [TestMethod]
        public void Testss()
        {       
            var date = DateTime.Now.ToString("yyMMddHHmmssfff");
            var errLst = new List<int>();
            int breakIndex = 0;
            var logs = new ConcurrentBag<string>();
            Parallel.For(1, int.MaxValue, new ParallelOptions { MaxDegreeOfParallelism = 10 }, (i, ParallelLoopState) =>
            {
                try
                {
                    if (i > 5)
                    {
                        breakIndex = i;
                        logs.Add($"---->【{i}】,Break开始");
                        ParallelLoopState.Break();
                        //Thread.Sleep(3000);
                        logs.Add($"---->【{i}】,Break结束");
                        return;
                    }
                    else
                    {
                        logs.Add($"---->【{i}】,开始");
                        Thread.Sleep(300);
                        logs.Add($"---->【{i}】,结束");
                    }
                }
                catch (Exception ex)
                {
                    errLst.Add(i);
                }
            });

            var logStr = new StringBuilder();
            logs.ToList().ForEach(log =>
            {
                logStr.AppendLine(log);
            });
            Log.WriteLine(logStr.ToString());
        }

        [TestMethod]
        public void TestSupportTraceDataTypes()
        {
           var dd =  new CommonSettingService().SupportTraceDataTypesWithCache();
        }
    }

    public class Tmp
    {
        public string PlatformType { get; set; }
        public int PageOrder { get; set; }
        public int PageCustomer { get; set; }
        public int ConditionOrder { get; set; }
        public int ConditionCustomer { get; set; }
    }
}
