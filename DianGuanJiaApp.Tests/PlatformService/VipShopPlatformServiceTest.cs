using System;
using System.Text;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.ServicesExtension;
using System.Linq;
using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using System.Security.Cryptography;
using DianGuanJiaApp.Utility.Other;
using Osp.Sdk.Context;
using vipapis.address;
using Osp.Sdk.Exception;
using vipapis.oauth;
using Osp.Sdk.Util;
using DianGuanJiaApp.Data.Model;

namespace DianGuanJiaApp.Tests.PlatformService
{
    /// <summary>
    /// VipShopPlatformServiceTest 的摘要说明
    /// </summary>
    [TestClass]
    public class VipShopPlatformServiceTest
    {

        private Shop _shop;
        private SiteContext _siteContext;
        private VipShopPlatformService _service;
        private VipShopApiClient _client;
        private SyncOrderService _syncService;
        private List<Shop> _shops;
        private OrderService orderService;
        private MergerOrderService mergerOrderService;
        private SyncProductService syncProductService;      

        public VipShopPlatformServiceTest()
        {
            //
            //TODO:  在此处添加构造函数逻辑
            //
        }

        [TestInitialize]
        public void Init()
        {
            //Redis 初始化
            RedisConfigService.Initialization();
            var userFxService = new UserFxService();
            var userFx = userFxService.Get(45);
            _siteContext = new SiteContext(userFx, new SiteContextConfig { NeedShopExpireTime = false, NeedRelationShops = false });

            var shopService = new ShopService();
            _shop = shopService.GetShopAndShopExtension(3817741);
            _service = new VipShopPlatformService(_shop);
            _syncService = new SyncOrderService();
            _client = new VipShopApiClient(_shop);  
            DapperMapping.SetEntityMapping();
        }
            
        [TestMethod]
        public void SyncShopTest()
        {                                          
            var result = _service.SyncShopInfo();
        }


        [TestMethod]
        public void SyncRefundOrdersTest()
        {
            var result = _service.SyncRefundOrders("2020-06-01");
        }


        [TestMethod]
        public void SignTest()
        {
            var appSecret = "6EC72934A6A874B5E2348221869F5719";
            //当前时间戳
            var timestamp = TimeUtil.CurrentTimeMillis().ToString();
            //string timestamp = (currentTimeMillis / 1000).ToString();
            Dictionary<string, string> sysParams = new Dictionary<string, string>();
            sysParams.Add("service", "vipapis.marketplace.store.StoreInfoService");
            sysParams.Add("method", "getStoreInfo");
            sysParams.Add("version", "1.0.0");
            sysParams.Add("timestamp", timestamp);
            sysParams.Add("format", "json");
            sysParams.Add("appKey", "a722074a");  
            sysParams.Add("accessToken", _shop.AccessToken);  //可选，引导用户授权登录后获得，有效期为3个月

            SortedDictionary<string, string> sortDic = new SortedDictionary<string, string>(sysParams);
            var paramStr = new StringBuilder();
            foreach (var key in sortDic.Keys)
            {
                paramStr.Append($"{key}{sortDic[key]}");
            }
            //paramStr.Append(appDic == null || !appDic.Any() ? "" : appDic.ToJson());

            var sign = HmacUtil.byte2hex(HmacUtil.encryptHMAC(paramStr.ToString2(), appSecret)).ToUpper();
        }
          

        [TestMethod]
        public void GetWarehouseTest()
        {
            try
            {
                vipapis.marketplace.store.StoreInfoServiceHelper.StoreInfoServiceClient client = new vipapis.marketplace.store.StoreInfoServiceHelper.StoreInfoServiceClient();
                var result = client.getWarehouse();
            }
            catch (Osp.Sdk.Exception.OspException e)
            {
                Console.WriteLine(e.ToString());
            }
        }

        [TestMethod]
        public void RefreshTokenTest()
        {
            var result = _client.RefreshToken(true);
        }
        
        [TestMethod]
        public void GetLogisticsCompanyListTest()
        {
            var result = _service.GetLogisticList();
        }

        [TestMethod]
        public void getGoodsCategoryTest()
        {
            
        }

        [TestMethod]
        public void GetOrderListTest()
        {
            try
            {
                var orders = _service.SyncOrders(new DianGuanJiaApp.Data.Model.SyncOrderParametersModel
                {
                    IsFullSync = false,
                    StartTime = DateTime.Now.AddDays(-5),
                    EndTime = DateTime.Now//"2020-08-16 23:32:44".toDateTime(),
                                          //OrderStatus = DianGuanJiaApp.Data.Enum.OrderStatusType.waitsellersend
                });
            }
            catch (Exception ex)
            {

                throw;
            }

            //OrderService orderService = new OrderService();
            //orderService.BulkMerger(orders);
        }

        [TestMethod]
        public void GetOrderListTest1()
        {
            try
            {
                _syncService.SyncOrder(new DianGuanJiaApp.Data.Model.SyncOrderParametersModel
                {
                    IsFullSync = false,
                    StartTime = "2021-01-01 23:32:44".toDateTime(),
                    EndTime = "2021-01-06 23:32:44".toDateTime()
                                          //OrderStatus = DianGuanJiaApp.Data.Enum.OrderStatusType.waitsellersend
                });
            }
            catch (Exception ex)
            {

                throw;
            }

            //OrderService orderService = new OrderService();
            //orderService.BulkMerger(orders);
        }


        [TestMethod]
        public void GetOrderDetailTest()
        {
            var order = _service.SyncOrder("21010604143266");
        }


        [TestMethod]
        public void GetProductListTest()
        { 
            var products = _service.SyncProduct(3);
            //ProductService _productService = new ProductService();
            //_productService.BulkMerger(products, _shop);
        }

        [TestMethod]
        public void GetProductDetailTest()
        {         
            var result = _service.SyncProduct("SPU-06154850800004C7");

            //ProductService _productService = new ProductService();
            //_productService.BulkMerger(new List<Product> { result }, _shop);
        }

        [TestMethod]
        public void GetProductPreviewUrlTest()
        {
            var result = _service.GetProductPreviewUrl("SPU-06BBABF2800000A7", "SKU-06BBABF2802000A7");
        }

        [TestMethod]
        public void GetProductsTest()
        {
            //var pids = new List<string>() { "2880195908", "2845215883", "1353572" };
            //var product = _service.SyncProducts(pids);
        }

        [TestMethod]
        public void GetShopProductsTest()
        {
            var product = _service.SyncProduct(0);
        }
                
        [TestMethod]
        public void GetLogisticList()
        {
            //var expressLst = _service.GetLogisticList();
        }
           
        [TestMethod]
        public void GetCategoryList()
        {                
            var categoryList = _service.CategoryList();   
        }

        
        [TestMethod]
        public void TestRefreshShopToken()
        {
            System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            sw.Start();
            var shop = _service.RefreshShopToken();
            sw.Stop();
            var ts = sw.Elapsed;
            Console.WriteLine("运行毫秒数：" + ts);
        }

        [TestMethod]
        public void GetSkuByIdTest()
        {
            _service.GetSkuById("SKU-061548508020B4C7");
        }
    }
}
