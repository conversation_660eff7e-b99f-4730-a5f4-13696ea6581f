using System.Collections.Generic;
using DianGuanJiaApp.Services.ServicesExtension;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using vipapis.delivery;

namespace DianGuanJiaApp.Services.Services.Compensate.Tests
{
    [TestClass()]
    public class SendHistoryCompensateTaskServiceTests
    {
        [TestInitialize]
        public void Init()
        {
            RedisConfigService.Initialization();
        }
        [TestMethod()]
        public void SearchNeedAnalysisTraceBatchesTest()
        {
            var service = new SendHistoryCompensateTaskService();
            var batches = service.SearchNeedAnalysisBatches();
            Assert.IsNull(batches);
        }

        [TestMethod()]
        public void AddTasksTest()
        {
            var service = new SendHistoryCompensateTaskService();
            var returned = service.AddTasks();
            Assert.IsTrue(returned.Success);
        }
        [TestMethod]
        public void GetHistoriesTest()
        {
            var service = new SendHistoryCompensateTaskService();
            var models = service.GetHistories(new List<string>{ "b07579c6ec474a58b7322936ef41ff27", "b7565fa5b8fd4ca2815c63e56e4a5add" });
            Assert.IsNull(models);
        }
    }
}