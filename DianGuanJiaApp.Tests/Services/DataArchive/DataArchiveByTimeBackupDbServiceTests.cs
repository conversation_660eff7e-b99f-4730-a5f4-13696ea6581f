using DianGuanJiaApp.Services.Services.DataArchive;
using DianGuanJiaApp.Services.ServicesExtension;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace DianGuanJiaApp.Tests.Services.DataArchive
{
    [TestClass]
    public class DataArchiveByTimeBackupDbServiceTests
    {
        [TestInitialize]
        public void Initialize()
        {
            RedisConfigService.Initialization();
        }

        [TestMethod]
        public void ArchiveHandleTest()
        {
            // var model = new DianGuanJiaApp.Data.Entity.DataArchive
            // {
            //     ArchiveType = 3,
            //     ArchiveBizName = "AsyncTask",
            //     ArchiveTableNames = "AsyncTask",
            //     ArchiveConditionField = "CreateTime",
            //     RetentionDays = 3,
            //     ArchivePageSize = 1000,
            //     ArchiveBeginTime = "00:00",
            //     ArchiveEndTime = "23:00",
            //     IsOpenArchive = true,
            //     IsArchiveBackupDb = true
            // };
            // var model = new DianGuanJiaApp.Data.Entity.DataArchive
            // {
            //     ArchiveType = 3,
            //     ArchiveBizName = "ServiceAppOrder",
            //     ArchiveTableNames = "ServiceAppOrder",
            //     ArchiveConditionField = "ServiceEnd",
            //     RetentionDays = 31,
            //     ArchivePageSize = 1000,
            //     ArchiveBeginTime = "00:00",
            //     ArchiveEndTime = "23:00",
            //     IsOpenArchive = true,
            //     IsArchiveBackupDb = true
            // };
            var model = new DianGuanJiaApp.Data.Entity.DataArchive
            {
                ArchiveType = 3,
                ArchiveBizName = "AppOrderList",
                ArchiveTableNames = "AppOrderList",
                ArchiveConditionField = "GmtServiceEnd",
                RetentionDays = 31,
                ArchivePageSize = 1000,
                ArchiveBeginTime = "00:00",
                ArchiveEndTime = "23:00",
                IsOpenArchive = true,
                IsArchiveBackupDb = true
            };
            var service = new DataArchiveByTimeBackupDbService(model);
            service.ArchiveHandle();
        }
    }
}