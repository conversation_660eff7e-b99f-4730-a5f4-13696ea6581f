using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Services.Services.DataDuplication;
using DianGuanJiaApp.Services.ServicesExtension;

namespace DianGuanJiaApp.Services.Services.DataSyncStatus.Tests
{
    [TestClass()]
    public class DataSyncStatusServiceTests
    {
        private DataSyncStatusService _dataSyncStatusService;
        private PathFlowNodeService _pathFlowNodeService;
        [TestInitialize]
        public void Init()
        {
            RedisConfigService.Initialization();
            _dataSyncStatusService = new DataSyncStatusService();
            _pathFlowNodeService = new PathFlowNodeService(CustomerConfig.FenDanDbConnectionString);
        }

        [TestMethod()]
        public void GetSyncStatusTest()
        {
            var model = _dataSyncStatusService.GetSyncStatus("PathFlowDuplicationService", 5, 1443);
            Assert.IsNotNull(model);
        }
        [TestMethod]
        public void UpdateSyncStatusTest()
        {
            var model = _dataSyncStatusService.GetSyncStatus("PathFlowDuplicationService", 5, 1443);
            model.LastStatus = Convert.ToInt16(SyncStatusType.Success);
            model.LastSyncTime = DateTime.Now;
            model.Mode = SyncMode.Incremental;
            var result = _dataSyncStatusService.UpdateSyncStatus(model);
            Assert.IsTrue(result);
        }
        [TestMethod]
        public void GetNowTimeTest()
        {
            var result = _pathFlowNodeService.GetNowTime();
            Assert.IsNotNull(result);
        }

        [TestMethod()]
        public void InsertsTest()
        {
            var model = _dataSyncStatusService.GetSyncStatus("PathFlowDuplicationService", 5, 1443);
            model.ShopId = 1554;
            var model2 = _dataSyncStatusService.GetSyncStatus("PathFlowDuplicationService", 5, 1443);
            _dataSyncStatusService.Inserts("PathFlowDuplicationService", 5,
                new List<Data.Entity.DataSyncStatus.DataSyncStatus> { model, model2 });
            Assert.Fail();
        }

        [TestMethod()]
        public void TestCompensatePushMessageToRedis()
        {
            new PushMessageService().CompensatePushMessageToRedis();
        }

        [TestMethod()]
        public void UpdateLastStatusTest()
        {
            _dataSyncStatusService.UpdateLastStatus(1, SyncStatusType.Syncing, "01", 1, "192.168.2.1");
        }
    }
}