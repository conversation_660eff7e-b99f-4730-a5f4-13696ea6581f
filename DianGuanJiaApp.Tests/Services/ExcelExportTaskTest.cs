using DianGuanJiaApp.Services;
using DianGuanJiaApp.Winform.ExportTaskApp;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Tests.Services
{
    [TestClass]
    public class ExcelExportTaskTest
    {
        [TestMethod]
        public void TestBuildFxOrderPrintExcelWithTempXML()
        {
            var exportTaskService = new ExportTaskService();
            var task = exportTaskService.Get(289754);

            //(new ExportExcelTasks(new Form1())).BuildFxOrderPrintExcelWithTempXML(task, DianGuanJiaApp.Data.Enum.FxPageType.AllOrder);
        }

        [TestMethod]
        public void TestGetExportTaskByBillCode()
        {
            var billCode = "B03F36665E129A009FF2DAD9E81882E0";
            var fxUserId = 5;
            var result = new ExportTaskService().GetExportTaskByBillCode(billCode, fxUserId);
        }

        [TestMethod]
        public void TestGetExportTaskByBillCodes()
        {
            var billCodes = new List<string> { "B03F36665E129A009FF2DAD9E81882E0", "33B92E764398604961D61AF7C8949607", "7F22265310F097248E246E8E692CEF3D", "34B058C79A3673EA57426D8FF7E618F5" };            
            var result = new ExportTaskService().GetExportTaskByBillCode(billCodes);
        }

        [TestMethod]
        public void GetExportTask()
        {
            var result1 = new ExportTaskService().GetExportTask(1443, 13);
            var result2 = new ExportTaskService().GetErpWebExportTask(1443, "Alibaba", 13);
            var result3= new ExportTaskService().GetErpWebExportTaskSharding(1443, "Alibaba",13, "67515905effb607cd0b56878");
        }
    }
}
