using DianGuanJiaApp.Services.Services.MessageQueue;
using System;
using DianGuanJiaApp.Data.FxModel;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace DianGuanJiaApp.Services.Services.MessageQueue.Tests
{
    [TestClass()]
    public class PurchaseOrderStatusHandleMessageServiceTests
    {
        [TestMethod()]
        public void SendMessageTest()
        {
            PurchaseOrderStatusHandleMessageService.SendMessage(new PurchaseOrderStatusHandleMessageModel
            {
                FxUserId = 5,
                ActionType = "create",
                LogicOrderId = "12323423q",
                PurchaseOrderPlatformId = "123124124124",
                Time = DateTime.Now
            });
            Assert.Fail();
        }

        [TestMethod()]
        public void ConsumerListenMessageTest()
        {
            PurchaseOrderStatusHandleMessageService.ConsumerListenMessage();
            Assert.Fail();
        }
    }
}