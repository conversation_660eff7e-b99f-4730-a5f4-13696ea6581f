using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.FxModel;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace DianGuanJiaApp.Services.Services.OrderModule.Tests
{
    [TestClass()]
    public class OrderAbnormalMessageServiceTests
    {
        [TestMethod()]
        public void SendMessageTest()
        {
            var result = OrderAbnormalMessageService.SendMessage(new OrderAbnormalMessageModel
            {
                FxUserId = 5,
                ShopId = 1334,
                AbnormalSource = AbnormalOrderSources.SyncOrder,
                AbnormalType = AbnormalOrderTypes.NonDgjShip,
                LogicOrderId = "100000573383848",
                PlatformOrderId = "2013277622351469454",
                PathFlowCode = "",
                PlatformType = "Alibaba",
                AbnormalReason = "该订单已在其他平台发货，请及时处理在店管家打印的订单，回收包裹和已打印的单号，避免重发"
            });
            Assert.IsTrue(result);
        }
    }
}