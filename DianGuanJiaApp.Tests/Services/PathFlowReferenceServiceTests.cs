using System;
using System.Collections.Generic;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Utility;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace DianGuanJiaApp.Services.Tests
{
    [TestClass()]
    public class PathFlowReferenceServiceTests
    {
        [TestInitialize]
        public void Init()
        {
            RedisConfigService.Initialization();
            var fxUser = new UserFxService().Get(5);
            var siteContext = new SiteContext(fxUser);

        }
        [TestMethod()]
        public void GetPathFlowNodeByProductCodeTest()
        {
            var connectionString = CustomerConfig.NewAlibabaConnectionString;
            var service = new PathFlowReferenceService(connectionString);
            var result = service.GetPathFlowNodeByProductCode(new List<string> { "f1be64f1613b1abb", "9646f24ae6d7bd54" });
            Assert.Fail();
        }

        [TestMethod]
        public void BulkInsertTest()
        {
            var service = new PathFlowReferenceService();
            var models = new List<PathFlowReference>
            {
                new PathFlowReference
                {
                    PathFlowCode = "55c2f2faecdb0d2c",
                    PathFlowRefType = "Product",
                    PathFlowRefCode = "56c31cdcd11b52ef",
                    Status = 0,
                    ProductCode = "56c31cdcd11b52ef",
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                },
                new PathFlowReference
                {
                    PathFlowCode = "2f1249a73a3ed07e",
                    PathFlowRefType = "Product",
                    PathFlowRefCode = "7a789b870909fa99",
                    Status = 0,
                    ProductCode = "7a789b870909fa99",
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                }
            };
            service.BulkInsert(models);
        }
    }
}