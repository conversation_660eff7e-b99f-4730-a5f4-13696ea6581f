using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;

namespace DianGuanJiaApp.Services.Tests
{
    [TestClass()]
    public class ProductFxServiceTests
    {
        [TestMethod()]
        public void GetFxProductListByCodesTest()
        {
            var service = new ProductFxService("server=192.168.1.168;uid=sa;pwd=**********;database=AlibabaFenFaDB;max pool size=512;");
            var dd = service.GetFxProductListByCodes(new List<string>() { "0000176dea3138ab" });
            Assert.Fail();
        }

        [TestMethod()]
        public void UpdatesForDuplicationTest()
        {
            var service = new ProductFxService("server=192.168.1.168;uid=sa;pwd=**********;database=AlibabaFenFaDB;max pool size=512;");
            var models = service.GetFxProductListByCodes(new List<string>() { "0000176dea3138ab" });
            service.UpdatesForDuplication(models);
            Assert.Fail();
        }

        [TestMethod]
        public void SyncProductTest()
        {
            var userfx = new UserFxService().Get(5);
            var shop = new ShopService().Get(1687);
            var sc = new SiteContext(userfx, new Data.Model.SiteContextConfig() { NeedRelationShops = false, NeedShopExpireTime = false });


            //new SyncFxProductService(5).SyncProductByShop(shop,0,platformId: "3707164864612007134");
        }
    }
}