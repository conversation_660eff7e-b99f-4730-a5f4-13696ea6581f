using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.ServicesExtension;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace DianGuanJiaApp.Services.Services.Tests
{
    [TestClass()]
    public class SendHistoryReturnRecordServiceTests
    {

        [TestInitialize]
        public void Init()
        {
            RedisConfigService.Initialization();
            var rp = new Data.Repository.UserFxRepository();
            var userFx = rp.Get(" where Id=@id", new { id = 45 })?.FirstOrDefault();
            new SiteContext(userFx);
        }

        [TestMethod()]
        public void SyncDataToAgentWith1688SupplierTest()
        {
            var service = new SendHistoryReturnRecordService();
            service.SyncDataToAgentWith1688Supplier(new List<SendHistoryReturnRecord>
            {
                new SendHistoryReturnRecord { ReturnRecordCode = "cc0ac53cdfd0e6bb",PurchaseFxUserId = 5, Status = 1 }
            });
        }

        [TestMethod()]
        public void BatchUpdateByNotUpdateFieldsTest()
        {
            var models = new List<SendHistoryReturnRecord>();
            models.Add(new SendHistoryReturnRecord { ReturnRecordCode = "cc0ac53cdfd0e6bb", PurchaseFxUserId = 5, Status = 1 });
            var service = new SendHistoryReturnRecordService();
            service.baseRepository.BatchUpdateByNotUpdateFields(models, new List<string> { "ReturnRecordCode" });
        }
        

        [TestMethod()]
        public void GetPageListTest()
        {
            var service = new SendHistoryReturnRecordService();
            var query = new SendHistoryReturnRecordQuery
            {
                //PurchaseOrderShopId = 1835,
                //SupplierSystemShopId = 1492,
                SendFxUserId = 5,
                PageSize = 5,
                PlatformOrderIds = "3507626809271326211,3507866463303326211"
            };

            var result = service.GetPageList(query);
        }

        [TestMethod()]
        public void GetMessageForMonitorTest()
        {
            //var result = SendHistoryReturnRecordService.GetMessageForMonitor();
        }

        [TestMethod()]
        public void SyncUpdateDataToAgentWith1688SupplierTest()
        {
            var service = new SendHistoryReturnRecordService();
            service.SyncUpdateDataToAgentWith1688Supplier(new SendHistoryReturnRecord{ReturnRecordCode = "cc0ac53cdfd0e6bb",Status = 1}, new List<string> { "Status" });
            Assert.Fail();
        }

        [TestMethod()]
        public void SyncUpdateDataToAlibabaCloudTest()
        {
            var service = new SendHistoryReturnRecordService();
            service.SyncUpdateDataToAlibabaCloud(new List<SendHistoryReturnRecord>
            {
                new SendHistoryReturnRecord { ReturnRecordCode = "cc0ac53cdfd0e6bb",PurchaseFxUserId = 5, Status = 1 }
            }, new List<string> { "Status" });
            Assert.Fail();
        }



        [TestMethod()]
        public void InsertsForDuplicationTest()
        {
            var service = new SendHistoryReturnRecordService();
            var dtNow = System.DateTime.Now;
            var models = new List<SendHistoryReturnRecord>();
            for (int i = 1; i <= 120; i++)
            {
                models.Add(new SendHistoryReturnRecord
                {
                    ReturnRecordCode = $"TCODE{i}",
                    CreateFxUserId = 5,
                    CreateTime = dtNow,
                    SendTime = dtNow,
                    SendHistoryCode = "",
                    SendHistoryOrderCode = "",
                    SendPathFlowCode = "",
                    PurchasePlatformOrderId = "",
                    PurchaseOrderCode = "",
                    SourceCloudPt = "",
                    SourceLogicOrderId = "",
                    SendHistoryData = "",
                    SourceReturnRecordCode = "",

                });
            }
            service.InsertsForDuplication(models);
        }

        [TestMethod()]
        public void CompensatePushSendReturnRecordToMessageTest()
        {
            var service = new SendHistoryReturnRecordService();
            service.CompensatePushSendReturnRecordToMessage(24*60);
        }


        [TestMethod()]
        public void SendForSendReturnRecordTest()
        {
            var service = new PurchaseOrderSendService();
            var sendModel = new LogicOrderSendMessageModel
            {
                SendUserId = 45,
                ReturnRecordCode = "7da009df945c648c",
                DbName = ""
            };
            var result = service.Send(sendModel);
        }
        
    }
}