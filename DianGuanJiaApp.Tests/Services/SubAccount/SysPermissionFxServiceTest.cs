using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using DianGuanJiaApp.Data.Dapper;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.FxModel;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Winform.ExportTaskApp;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Dapper;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services.Services.SubAccount;

namespace DianGuanJiaApp.Services.Tests
{
    [TestClass()]
    public class SysPermissionFxServiceTest
    {

        private SysPermissionFxService _service = new SysPermissionFxService();

        [TestInitialize]
        public void Init()
        {

        }
        
        [TestMethod]
        public void GetPermissionTreeTest()
        {
            _service.GetPermissionTree();
            return;
        }

        
    }
}