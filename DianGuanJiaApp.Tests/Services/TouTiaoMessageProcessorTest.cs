using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using DianGuanJiaApp.Data;
using Dapper;
using DianGuanJiaApp.Data.Entity;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using System.IO;
using System.Diagnostics;
using IWshRuntimeLibrary;
using System.Text;
using DianGuanJiaApp.Services.LogisticService;
using DianGuanJiaApp.Controllers;
using StackExchange.Redis;
using Order = DianGuanJiaApp.Data.Entity.Order;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.Web;
using Newtonsoft.Json.Linq;
using System.Threading;
using DianGuanJiaApp.Data.Enum;
using NPOI.SS.Formula.Functions;
using DianGuanJiaApp.Services.ServicesExtension;

namespace DianGuanJiaApp.Tests.Services
{
    [TestClass]
    public class TouTiaoMessageProcessorTest
    {
        [TestInitialize]
        public void Init()
        {
            RedisConfigService.Initialization();
        }
        [TestMethod]
        public void TestProcessTouTiaoMessage()
        {
            var shopService = new ShopService();
            //var shop = shopService.GetShopById(1687);
            var shops = shopService.GetShopWithDbConfig(new List<string>() { "22321509" }, "TouTiao");
            var shop = shops.FirstOrDefault();// shopService.GetShopById(1687);
            shop.AppKey = "6838756062733010445";
            var sc = new SiteContext(shop, new SiteContextConfig() { NeedRelationShops = false, NeedShopExpireTime = false });
            var s = new DianGuanJiaApp.Message.Consumer.Services.TouTiaoMessageProcessor();
            s.ProcessTouTiaoMessage(
                new List<DianGuanJiaApp.Data.MessageModel.PlatformMessageModel>() {
                "{\"PT\":\"TouTiao\",\"TID\":\"5042620035585791251\",\"CID\":\"5042620035585791251\",\"MID\":\"22321509\",\"AfterSaleId\":\"7208087464815198476\",\"AfterSaleType\":\"2\",\"ASS\":\"12\",\"State\":\"206\",\"Key\":\"TouTiao-T-5042988725350934017\",\"Content\":\"\",\"ISC\":false}".ToObject<DianGuanJiaApp.Data.MessageModel.PlatformMessageModel>()
                },
                shops);
        }
        [TestMethod]
        public void TestFxProcessTouTiaoMessage()
        {
            var shopService = new ShopService();
            var user = new UserFxService().Get(5);
            var shops = shopService.GetShopWithDbConfig(new List<string>() { "22321509" }, "TouTiao");
            var shop = shops.FirstOrDefault();// shopService.GetShopById(1687);
            shop.AppKey = "6838756062733010445";
            var sc = new SiteContext(shop, new SiteContextConfig() { NeedRelationShops = false, NeedShopExpireTime = false });
            var s = new Message.Consumer.Services.FxTouTiaoMessageProcessor();
            s.FxProcessTouTiaoMessage(
                new List<DianGuanJiaApp.Data.MessageModel.PlatformMessageModel>() {
                    "{\"PT\":\"TouTiao\",\"TID\":\"5042620035585791251\",\"CID\":\"5042620035585791251\",\"MID\":\"22321509\",\"AfterSaleId\":\"7208087464815198476\",\"AfterSaleType\":\"2\",\"ASS\":\"12\",\"State\":\"206\",\"Key\":\"TouTiao-T-5042988725350934017\",\"Content\":\"\",\"ISC\":false}".ToObject<DianGuanJiaApp.Data.MessageModel.PlatformMessageModel>()
                },
                new List<Tuple<UserFx, Shop>>
                {
                    new Tuple<UserFx, Shop>(user,shop)
                });
        }
    }
}
