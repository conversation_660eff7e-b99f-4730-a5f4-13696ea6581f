using CSRedis;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;

namespace DianGuanJiaApp.Trace.ServicesTests.Services
{
    /// <summary>
    /// 售后单操作，单元测试
    /// </summary>
    [TestClass]
    public class AfterSaleActionTest
    {
        private Shop _shop;
        private SiteContext _siteContext;
        //private ZhiDianPlatformService _service;
        private ZhiDianNewPlatformService _service;
        //private ZhiDianApiClient _client;
        private ZhiDianNewApiClient _client;
        private SyncOrderService _syncService;
        private List<Shop> _shops;
        private OrderService orderService;
        private MergerOrderService mergerOrderService;
        private SyncProductService syncProductService;

        [TestInitialize]
        public void Init()
        {
            // RedisHelper.Initialization(new CSRedisClient(ConfigurationManager.ConnectionStrings["TraceRedis"].ConnectionString));

            RedisHelper.Initialization(new CSRedisClient("192.168.1.168,password=Wyd4484309,connectTimeout=2000"));

            var rp = new DianGuanJiaApp.Data.Repository.ShopRepository();
            _shop = rp.Get(" where Id=@id", new { id = 1687 })?.FirstOrDefault();
            var _userFx = new UserFxService().Get(5);
            _siteContext = new SiteContext(_userFx, new SiteContextConfig() { NeedRelationShops = false, NeedShopExpireTime = false });
            _service = new ZhiDianNewPlatformService(_shop);
            //_service = PlatformFactory.GetPlatformService(_shop) as ZhiDianNewPlatformService;
            //_syncService = new SyncOrderService();
            _client = new ZhiDianNewApiClient(_shop);
            //DapperMapping.SetEntityMapping();
            //RedisConfigService.Initialization();
            //_service.SyncOrder("6935744385527846295");
        }

        private ZhiDianNewPlatformService BuildSvc()
        {
            var targetShops = new ShopService().GetShopByIds(new List<int>() { 1687 });
            var shop = targetShops.FirstOrDefault();
            shop.AppKey = "7152331588175513101"; // 店管家_分销代发
          

            _service = new ZhiDianNewPlatformService(shop);
            PlatformFactory.GetPlatformService(shop);

            string ak = shop.AppKey;

            /*
              8010546（粉尘创意屋）// 这个店铺本地调接口报：token过期，实际没有过期
              -- 【抖店配置库】抖店最新的token, 【分销代发】
              select AccessToken from P_ShopExtension t2 WITH(NOLOCK) where ShopId in (8010546) and appkey = '7152331588175513101';  


             6850062（恰噶百货）
             -- 【抖店配置库】抖店最新的token, 【分销代发】
             select AccessToken from P_ShopExtension t2 WITH(NOLOCK) where ShopId in (6850062) and appkey = '7152331588175513101';  
           */

            shop.ShopExtension.AccessToken = shop.AccessToken = "q52e5su53r1ic8py3i6jcwd000392gih-11";

            var service = new ZhiDianNewPlatformService(shop);
            return service;
        }

        /// <summary>
        /// 根据省获取全量四级地址
        /// </summary>
        [TestMethod]
        public void TestGetAreasByProvince()
        {
            BuildSvc().GetAreasByProvince();
        }

        /// <summary>
        /// 拒绝原因码列表接口
        /// </summary>
        [TestMethod]
        public void Test_GetRejectReasonCodeList()
        {
            List<AfterSaleRejectReason> saleAddressList = BuildSvc().GetRejectReasonList(null);
        }

        /// <summary>
        /// 获取——平台售后地址
        /// </summary>
        [TestMethod]
        public void Test_GetAddressList2()
        {
            List<SellerAddressModel> sellerAddressesLsit = BuildSvc().GetAfterSaleAllAddres();
        }

        /// <summary>
        /// 批量查询地址库列表接口
        /// 创建——平台售后地址
        /// </summary>
        [TestMethod]
        public void Test_CreateAddress()
        {
            try
            {
                // 宇航的小店 48471462
                var address_id = BuildSvc().CreateAddress(new CreateAddressModel()
                {
                    address = new CreateAddressDetail()
                    {
                        province_id = 44,
                        city_id = 440300,
                        town_id = 440304,
                        street_id = 440304004,
                        detail = "国际文化大厦2407",
                        fixed_phone = "15799071811",
                        link_type = 0,
                        mobile = "15799071811",
                        user_name = "胡宇航",
                        remark = "测试用途"
                    }
                });
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        /// <summary>
        /// 售后接口——聚合操作
        /// </summary>
        [TestMethod]
        public void TestAfterSaleOperate()
        {
            // 【未调试】
            BuildSvc().AfterSaleOperate(new AfterSaleOperateModel()
            {
                type = AfterSaleOperateEnum.AgreeOnlyRefund,
                items = new List<AfterSaleOperateItem>(),
                shopid = 1687
            });
        }

        /// <summary>
        /// 查询物流公司
        /// </summary>
        [TestMethod]
        public void TestGetLogisticList()
        {
            //var paramDic = new Dictionary<string, string>();
            //var result = _client.Execute("order.logisticsCompanyList", paramDic);

            var result = BuildSvc().GetLogisticList();
        }

        /// <summary>
        /// 查询平台地址，Code
        /// </summary>
        [TestMethod]
        public void TestGetPtAddressCode()
        {
            // Dictionary<名称，平台Id>
            var dic = new Dictionary<string, string>();
            new PlatformAreaCodeInfoRepository().GetPtAddressCode(dic, "广东省", "深圳市", "福田区", "福田街道", PlatformType.TouTiao.ToString());
        }


        /// <summary>
        /// 同步单个售后单详情【抖店】
        /// </summary>
        [TestMethod]
        public void TestSyncOrderJson()
        {
            // var result0 = BuildSvc().SyncOrderJson("6943013810369140697"); // 订单编号：6943013810369140697  售后单号：146978282048608795
            // var result1 = BuildSvc().SyncAfterSaleOrderDetail("146978282048608795");
            // var result2 = BuildSvc().SyncAfterSaleOrderDetail("146978452302464459");
            // var result3 = BuildSvc().SyncAfterSaleOrderDetail("147014514828465070");
         
            // var result4 = BuildSvc().SyncAfterSaleOrderDetail("147039306772445932"); // 6920256411432484756 粉尘创意屋-shopid:8010546
            // var result5 = BuildSvc().SyncAfterSaleOrderDetail("147045150123454123"); // 6920303462698352532 粉尘创意屋-shopid:8010546  商家规格: 银杏树
            var result6 = BuildSvc().SyncAfterSaleOrderDetail("147046620110028821"); // 6920303462698352532 粉尘创意屋-shopid:8010546  商家规格: 银杏树
        }

        /// <summary>
        /// 同步单个售后单详情
        /// 考拉好品
        /// </summary>
        [TestMethod]
        public void TestSyncOrderJson2()
        {
            var targetShops = new ShopService().GetShopByIds(new List<int>() { 1687 });
            var shop = targetShops.FirstOrDefault();
            shop.AppKey = "7152331588175513101"; // 店管家_分销代发

            _service = new ZhiDianNewPlatformService(shop);
            PlatformFactory.GetPlatformService(shop);

            shop.ShopExtension.AppSecret = CustomerConfig.TouTiaoAppSecret;
            shop.ShopExtension.AccessToken = shop.AccessToken = "mc6cbf666p1ic8py3i6jcwd0001n7684-11"; // 考拉好品

            var service = new ZhiDianNewPlatformService(shop);

            service.SyncAfterSaleOrderDetail("147014780546203246");
        }

        /// <summary>
        /// 查找厂家
        /// </summary>
        [TestMethod]
        public void TestSSearchForManufacturers()
        {
            List<int> fxuserId = new List<int>() { 5 };
            List<string> afterSaleCodeList = "09575b0cea1fceca,9bb438329e59c29d,1d6cb9c525d570ae".Split(',').ToList();

            // 本地找出模拟数据
            /*
             select top 10 asoi.AfterSaleCode FROM AfterSaleOrder ab  WITH(NOLOCK) 
                inner join AfterSaleOrderItem  asoi WITH(NOLOCK)
                on ab.afterSaleCode = asoi.afterSaleCode
                inner join LogicOrderItem  loi WITH(NOLOCK)
                on loi.OrderItemCode = asoi.OrderItemCode
                where ab.shopid = 1687
                order by ab.CreateTime desc;
             
            select OrderItemCode from AfterSaleOrderItem loi
             INNER JOIN FunStringToTable('09575b0cea1fceca,9bb438329e59c29d,1d6cb9c525d570ae',',') t
             on t.item = loi.AfterSaleCode;

            select loi.OrderItemCode as OrderCode, pfn.DownFxUserId as FxUserId ,lo.shopid FROM LogicOrder lo WITH(NOLOCK)
            INNER JOIN LogicOrderItem loi WITH(NOLOCK) ON lo.LogicOrderId = loi.LogicOrderId
            INNER JOIN PathFlowNode pfn WITH(NOLOCK) ON pfn.PathFlowCode = lo.PathFlowCode
            INNER JOIN FunStringToTable('030f03d899594dd3,be0712db8ace2584,9c7713f9a8062bb9',',') t on t.item = loi.OrderItemCode
            WHERE lo.ShopId = 1687 and pfn.FxUserId in (5)
             */

            // Dictionary《AfterSaleCode, 厂家Id》  如果厂家用户Id为0，则表示这个单，自己就是厂家
            var dic = new AfterSaleOrderRepository().SearchForManufacturers(fxuserId, afterSaleCodeList, 1687);
        }

        /// <summary>
        /// 【副本】扩展表数据查询 ✅
        /// </summary>
        [TestMethod]
        public void TestGetListForDuplication()
        {
            var service = new AfterSaleOrderService();
            var models = service.GetExtListForDuplication(new List<string> { "d11e691108d82282", "b20fab25890b2324" });
            service.InsertsForDuplicationExt(models);
        }

        /// <summary>
        /// 【副本】扩展表数据查询 ✅
        /// </summary>
        [TestMethod]
        public void TestGetListForDuplication2()
        {
            var service = new AfterSaleOrderService("server=192.168.1.168;uid=sa;pwd=**********;database=AlibabaFenFaDB;max pool size=512;");
            var models = service.GetExtListForDuplication(new List<string> { "d11e691108d82282", "b20fab25890b2324" });
            service.InsertsForDuplicationExt(models);
        }

        /// <summary>
        /// 【副本】扩展表数据写入
        /// </summary>
        [TestMethod]
        public void InsertForDuplication()
        {
            var service = new AfterSaleOrderService();

            List<AfterSaleOrderExt> factoryAfterSaleOrderExt = new List<AfterSaleOrderExt>();
            AfterSaleOrderExt ext = new AfterSaleOrderExt()
            {
                AfterSaleCode = "123456789A",
                AfterSaleId = "abcabcabc",
                Code = "1234567",
                ExchangeGoods = "",
                Evidence = "https",
                PlatformType = "TouTiao",
                CreateTime = DateTime.Now,
                PlatformOrderId = "",
                ShopId = 1687
            };
            factoryAfterSaleOrderExt.Add(ext);
            service.InsertsForDuplicationExt(factoryAfterSaleOrderExt); // 单元测试
        }

        /// <summary>
        /// 批量插入和更新
        /// </summary>
        [TestMethod]
        public void BatchAfterOrder()
        {
            List<AfterSaleOrder> list = new List<AfterSaleOrder>();

            AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
            AfterSaleOrder afterSaleOrder2 = new AfterSaleOrder();
            list.Add(afterSaleOrder);
            list.Add(afterSaleOrder2);

            AfterSaleOrderExt ext = new AfterSaleOrderExt() 
            {
              AfterSaleCode = "d11e691108d82282",
              AfterSaleId = "TQ340320024304492085",
              Code = "8985dfsf",
              ExchangeGoods = "123",
              Evidence = "https",
            };
            afterSaleOrder.Ext = ext;

            AfterSaleOrderExt ext2 = new AfterSaleOrderExt()
            {
                AfterSaleCode = "c71e3a4d89831dd0",
                AfterSaleId = "147039306772445932",
                Code = "C21D83FE2425E41F6A98697C70728613",
                ExchangeGoods = "456",
                Evidence = "pppp",
            };
            afterSaleOrder2.Ext = ext2;

            new AfterSaleOrderService().BatchAfterOrderExt(list);
        }

        /// <summary>
        /// 对应同步更新操作成功的售后单对应信息【抖店】
        /// </summary>
        [TestMethod]
        public void TestUpdateStatusAfterOperate()
        {
            ConcurrentBag<AfterSaleOperateModel> list = new ConcurrentBag<AfterSaleOperateModel>();
            AfterSaleOperateModel afterSaleOperateModel = new AfterSaleOperateModel()
            {
                items = new List<AfterSaleOperateItem>(),
                shopid = 1687,
                type = AfterSaleOperateEnum.AgreeOnlyRefund,
            };

            afterSaleOperateModel.items.Add(new AfterSaleOperateItem()
            {
                aftersale_id = "146736582913099590", // 售后单号
                reason = "不要了", // 订单项号
                reject_reason_code  = 12,
            });
            
            list.Add(afterSaleOperateModel);
            BuildSvc().UpdateStatusAfterOperate(list);
        }

        [TestMethod]
        public void Md5Test()
        {
            AfterSaleAddress afterSaleAddress1 = new AfterSaleAddress()
            {
                FxuserId = 74,
                TargetFxuserId = 1161723,
                PlatformType = "TouTiao",
            };

            AfterSaleAddress afterSaleAddress2 = new AfterSaleAddress()
            {
                FxuserId = 1161723,
                TargetFxuserId = 74,
                PlatformType = "TouTiao",
            };
            bool mm = afterSaleAddress1.Code == afterSaleAddress2.Code; // true
        }
    }
}
