namespace DianGuanJiaApp.Trace.ViewModels.Models
{
    public struct RoutingKeys
    {
        /// <summary>
        /// 链路批次
        /// </summary>
        public const string TraceBatch = "trace.batch.routing";
        /// <summary>
        /// 链路监控
        /// </summary>
        public const string TraceData = "trace.data.routing";
        /// <summary>
        /// 批次
        /// </summary>
        public const string Batch = "batch.routing";
        /// <summary>
        /// 补偿批次
        /// </summary>
        public const string CompensateBatch = "compensate.batch.routing";
        /// <summary>
        /// 调用API数据
        /// </summary>
        public const string InvokeApiData = "invoke.api.data.routing";
    }
}