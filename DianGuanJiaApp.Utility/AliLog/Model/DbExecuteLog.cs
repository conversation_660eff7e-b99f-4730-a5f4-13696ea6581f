using OpenTelemetry.Trace;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Utility.Model.AliLog
{
    /// <summary>
    /// 数据库执行日志 AliLog模型
    /// </summary>
    public class DbExecuteLogModel : DbExecuteLog, IAliPutLogs
    {
    }

    /// <summary>
    /// 数据库执行日志 消息模型
    /// </summary>
    public class DbExecuteLogMsgModel
    {
        public string Project { get; set; }
        public string Logstore { get; set; }
        public string Topic { get; set; }
        public List<DbExecuteLogModel> Data { get; set; }
    }

    /// <summary>
    /// 数据库执行日志模型
    /// </summary>
    public class DbExecuteLog
    {
        /// <summary>
        /// 触发时间(当前时间转成时间戳(秒))
        /// </summary>
        public uint ExecuteTime { get; set; }
        /// <summary>
        /// 服务器IP
        /// </summary>
        public string ServerIP { get; set; }
        /// <summary>
        /// 执行方法
        /// </summary>
        public string Method { get; set; }
        /// <summary>
        /// 数据库名
        /// </summary>
        public string DbName { get; set; }
        /// <summary>
        /// 表名
        /// </summary>
        public string TableName { get; set; }
        /// <summary>
        /// 行数
        /// </summary>
        public int RowsCount { get; set; }
        /// <summary>
        /// 花费时间，单位：毫秒
        /// </summary>
        public int SpendTimes { get; set; }
        /// <summary>
        /// 是否错误
        /// </summary>
        public bool IsError { get; set; }
        /// <summary>
        /// 错误原因
        /// </summary>
        public string ErrorMessage { get; set; }
        /// <summary>
        /// 分单系统店铺Id即SiteContext.CurrentNoThrow?.CurrentShopId
        /// </summary>
        public int ShopId { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public string Tag { get; set; }

        /// <summary>
        /// OpenTelemetry TraceId
        /// </summary>
        public string TraceId
        {
            get
            {
                var span = Tracer.CurrentSpan;
                if (span != null && span.Context != null && span.Context.TraceId != null)
                {
                    //traceId
                    return span.Context.TraceId.ToString();
                }
                return "";
            }
        }

        /// <summary>
        /// OpenTelemetry SpanID
        /// </summary>
        public string SpanId
        {
            get
            {
                var span = Tracer.CurrentSpan;
                if (span != null && span.Context != null && span.Context.SpanId != null)
                {
                    //spanId
                    return span.Context.SpanId.ToString();
                }
                return "";
            }
        }
    }
}
