using Newtonsoft.Json;

namespace DianGuanJiaApp.Utility.DingTalk.RequestModel
{
    public class MessageRequestModel
    {
        [JsonProperty("msgtype")]
        public string MsgType { get; set; }
        [JsonProperty("text")]
        public TextMessageRequestModel Text { get; set; }
        [JsonProperty("link")]
        public LinkMessageRequestModel Link { get; set; }
        [JsonProperty("markdown")]
        public MarkdownMessageRequestModel Markdown { get; set; }
        [JsonProperty("actionCard")]
        public ActionCardMessageRequestModel ActionCard { get; set; }
        [JsonProperty("at")]
        public AtRequestModel At { get; set; }
    }

    public struct MessageType
    {
        public const string Text = "text";
    }
}