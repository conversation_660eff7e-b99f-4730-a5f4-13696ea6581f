using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Security.Cryptography;
using System.Text;

namespace YTO
{
    /// <summary>
    ///YTOClass 的摘要说明
    /// </summary>
    public class YTOClass
    {
        public YTOClass()
        {
            //
            //TODO: 在此处添加构造函数逻辑
            //
        }

        //MD5加密
        private string GetMD5Hash(String input)
        {
            MD5 md5Hasher = MD5.Create();
            byte[] data = md5Hasher.ComputeHash(Encoding.UTF8.GetBytes(input));
            StringBuilder sBuilder = new StringBuilder();
            for (int i = 0; i < data.Length; i++)
            {
                sBuilder.Append(data[i].ToString("X2"));
            }
            return sBuilder.ToString();
        }

        public static String GetApiUrl()
        {
            //测试环境
            //return "http://58.32.246.71:8000/CommonOrderModeBServlet.action";

            //正式环境
            //return "http://service.yto56.net.cn/CommonOrderModeBServlet.action";

            //新接口正式环境
            return "http://customerewms.yto.net.cn/CommonOrderModeBPlusServlet.action";
        }

    }
}