using System;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Es.Log.Client;
using DianGuanJiaApp.Utility.Extension;
using Nest;

namespace DianGuanJiaApp.Utility.Helpers
{
    public class EsLogHelper
    {
        private readonly EsLogClient _logClient;
        /// <summary>
        /// 私有构造函数（内部需要）
        /// </summary>
        private EsLogHelper()
        {
            _logClient = new EsLogClient($".{Config.SlsLogStore}");
        }
        /// <summary>
        /// 静态构造函数
        /// </summary>
        static EsLogHelper()
        {
            _instance = new EsLogHelper();
        }

        private static EsLogHelper _instance;
        //单例
        public static EsLogHelper Instance
        {
            get
            {
                return _instance;
            }
        }

        /// <summary>
        /// 写日志
        /// </summary>
        /// <typeparam name="TLogView"></typeparam>
        /// <param name="logStoreName"></param>
        /// <param name="logView"></param>
        /// <param name="isLogStoreNameAppendDate"></param>
        /// <returns></returns>
        public bool WriteLog<TLogView>(string logStoreName, TLogView logView,
            bool isLogStoreNameAppendDate = false) where TLogView : class
        {
            return WriteLog(logStoreName, new List<TLogView> { logView }, isLogStoreNameAppendDate);
        }

        /// <summary>
        /// 写日志
        /// </summary>
        /// <typeparam name="TLogView"></typeparam>
        /// <param name="logStoreName"></param>
        /// <param name="logViews"></param>
        /// <param name="isLogStoreNameAppendDate">是否存储名称附加日期</param>
        /// <returns></returns>
        public bool WriteLog<TLogView>(string logStoreName, List<TLogView> logViews,
            bool isLogStoreNameAppendDate = false) where TLogView : class
        {
            //判空处理
            if (logViews == null || !logViews.Any())
            {
                return true;
            }
            //日志存储名称有值
            if (!string.IsNullOrWhiteSpace(logStoreName))
            {
                //确认是否ES规范，索引前面是否有 "."，没有则加上
                if (!logStoreName.StartsWith("."))
                {
                    logStoreName = $".{logStoreName}";
                }
                //是否存储名称附加日期，方便管理ES索引
                if (isLogStoreNameAppendDate)
                {
                    logStoreName = $"{logStoreName}-{DateTime.Now:yyyy-MM-dd}-000001";
                }
            }
            //是否成功
            var isSuccess = true;
            //分块，每块控制500
            var chuckLogs = logViews.ChunkList(500);
            //分块上传
            chuckLogs.ForEach(chuckLog =>
            {
                //异步请求
                try
                {
                    //推送日志
                    var response = _logClient.PutLogs(logStoreName, chuckLog);
                    //错误情况
                    if (response != null && response.Errors)
                    {
                        isSuccess = false;
                        Log.WriteLine($"写ES日志无效，{logStoreName}，DEBUG信息：{response?.DebugInformation}", LogModuleTypeEnum.SLS);
                    }

                    Log.Debug(() => $"索引名称：{logStoreName}，响应信息：{response.ToJson(true)}", LogModuleTypeEnum.SLS);
                }
                catch (Exception ex)
                {
                    Log.WriteError(new { LogViews = chuckLog.Take(1), ex.Message }.ToJson(), LogModuleTypeEnum.SLS);
                    isSuccess = false;
                }
            });
            return isSuccess;
        }

        /// <summary>
        /// 写异常日志（默认索引）
        /// </summary>
        /// <param name="exception"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public bool WriteLog(Exception exception, string message)
        {
            var logErrorView = exception.BuildLogErrorView(message);
            try
            {
                var response = _logClient.PutLog(logErrorView);
                return true;
            }
            catch (Exception ex)
            {
                Log.WriteError(new { LogViews = logErrorView, ex.Message }.ToJson(),LogModuleTypeEnum.SLS);
                return false;
            }
        }
        
        /// <summary>
        /// ES日志查询
        /// </summary>
        /// <param name="searchDescriptor"></param>
        /// <typeparam name="TLog"></typeparam>
        /// <returns></returns>
        public ISearchResponse<TLog> Search<TLog>(Func<SearchDescriptor<TLog>, SearchDescriptor<TLog>> searchDescriptor)
            where TLog : class
        {
            return _logClient.Search(searchDescriptor);
        }

        /// <summary>
        /// 游标查询
        /// </summary>
        /// <param name="scroll"></param>
        /// <param name="scrollId"></param>
        /// <typeparam name="TLog"></typeparam>
        /// <returns></returns>
        public ISearchResponse<TLog> Scroll<TLog>(string scroll, string scrollId) where TLog : class
        {
            return _logClient.Scroll<TLog>(scroll, scrollId);
        }

        /// <summary>
        /// 清理游标
        /// </summary>
        /// <param name="scrollId"></param>
        /// <returns></returns>
        public ClearScrollResponse ClearScroll(string scrollId)
        {
            return _logClient.ClearScroll(scrollId);
        }
    }
}