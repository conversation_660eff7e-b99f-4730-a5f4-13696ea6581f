using OpenTelemetry.Trace;

namespace DianGuanJiaApp.Utility.Helpers.Model
{
    public class LogView
    {
        /// <summary>
        /// 等级
        /// </summary>
        public string Level { get; set; }
        /// <summary>
        /// 机器名称
        /// </summary>
        public string MachineName { get; set; }
        /// <summary>
        /// 机器IP
        /// </summary>
        public string MachineIp { get; set; }
        /// <summary>
        /// 类型名称
        /// </summary>
        public string TypeName { get; set; }
        /// <summary>
        /// 函数名称
        /// </summary>
        public string FunctionName { get; set; }
        /// <summary>
        /// 自定义信息
        /// </summary>
        public string Message { get; set; }
        /// <summary>
        /// 附加信息
        /// </summary>
        public string AdditionalMessage { get; set; }
        /// <summary>
        /// 云平台类型
        /// </summary>
        public string CloudPlatformType { get; set; }
        /// <summary>
        /// 程序路径
        /// </summary>
        public string AppFilePath { get; set; }

        /// <summary>
        /// OpenTelemetry TraceId
        /// </summary>
        public string TraceId
        {
            get
            {
                var span = Tracer.CurrentSpan;
                if (span != null && span.Context != null && span.Context.TraceId != null)
                {
                    //traceId
                    return span.Context.TraceId.ToString();
                }
                return "";
            }
        }

        /// <summary>
        /// OpenTelemetry SpanID
        /// </summary>
        public string SpanId
        {
            get
            {
                var span = Tracer.CurrentSpan;
                if (span != null && span.Context != null && span.Context.SpanId != null)
                {
                    //spanId
                    return span.Context.SpanId.ToString();
                }
                return "";
            }
        }

        /// <summary>
        /// 系统环境版本
        /// </summary>
		public string SystemEnvironmentVersion
		{
			get
			{
				return CustomerConfig.SystemEnvironmentVersion;
			}
		}

	}
}