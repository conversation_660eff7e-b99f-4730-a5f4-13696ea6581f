using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Utility
{
    public static class Mapping
    {
        public static T map<S,T>(S s)
        {
            var stype = s.GetType();
            var ttype = typeof(T);
            var tprops = ttype.GetProperties();
            var tobj = (T)Activator.CreateInstance(ttype);
            foreach(var prop in tprops)
            {
                if(prop.CanWrite && stype.GetProperty(prop.Name) != null)
                {
                    prop.SetValue(tobj, stype.GetProperty(prop.Name).GetValue(s));
                }
            }
            return tobj;
        }
    }
}
