using DianGuanJiaApp.Utility.Helpers;
using NLog;
using OpenTelemetry.Trace;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Utility
{
	public static class NLogHelper
	{
		private static readonly Logger logger = LogManager.GetCurrentClassLogger();

		private static readonly object fileLocker = new object();

		/// <summary>
		/// 主要记录系统关键信息，旨在保留系统正常工作期间关键运行指标，开发人员可以将初始化系统配置业务状态变化信息，或者用户业务流程中的核心处理记录到 INFO 日志中，方便日常运维工作以及错误回溯时上下文场景复现。
		/// </summary>
		/// <param name="message"></param>
		/// <param name="logModuleTypeEnum"></param>
		public static void WriteInfo(string message, LogModuleTypeEnum logModuleTypeEnum)
		{
			LogMessage(message, LogLevelEnum.INFO, logModuleTypeEnum);
		}

		/// <summary>
		/// 主要输出警告性质的内容，这些内容是可以预知且是有规划的，比如，某个方法入参为空或者该参数的值不满足运行该方法的条件时。在 WARN 级别的时应输出较为详尽的信息，以便于事后对日志进行分析。
		/// </summary>
		/// <param name="message"></param>
		/// <param name="logModuleTypeEnum"></param>
		public static void WriteWarn(string message, LogModuleTypeEnum logModuleTypeEnum)
		{
			LogMessage(message, LogLevelEnum.WARN, logModuleTypeEnum);
		}

		/// <summary>
		/// 主要输出调试性质的内容，该级别日志主要用于在开发、测试阶段输出
		/// </summary>
		/// <param name="messageFunc"></param>
		/// <param name="logModuleTypeEnum"></param>
		public static void WriteDebug(Func<string> messageFunc, LogModuleTypeEnum logModuleTypeEnum)
		{
			if (CustomerConfig.IsDebug)
			{
				var str = messageFunc.Invoke();
				LogMessage(str, LogLevelEnum.DEBUG, logModuleTypeEnum);
			}

		}

		/// <summary>
		/// 主要输出调试性质的内容，该级别日志主要用于在开发、测试阶段输出
		/// </summary>
		/// <param name="message"></param>
		/// <param name="logModuleTypeEnum"></param>
		public static void WriteDebug(string message, LogModuleTypeEnum logModuleTypeEnum)
		{
			if (CustomerConfig.IsDebug)
				LogMessage(message, LogLevelEnum.DEBUG, logModuleTypeEnum);
		}

		/// <summary>
		/// 主要针对于一些不可预知的信息，诸如:错误、异常等，比如，在 catch 块中抓获的网络通信、数据库连接等异常，若异常对系统的整个流程影响不大，可以使用 WARN 级别日志输出。
		/// 不要滥用 ERROR 级别日志。一般来说在配置了告警的系统中，WARN 级别一般不会告警，ERROR 级别则会设置监控告警甚至电话报警，ERROR 级别日志的出现意味着系统中发生了非常严重的问题，必须有人立即处理。
		/// </summary>
		/// <param name="message"></param>
		/// <param name="logModuleTypeEnum"></param>
		public static void WriteError(string message, LogModuleTypeEnum logModuleTypeEnum)
		{
			LogMessage(message, LogLevelEnum.ERROR, logModuleTypeEnum);
		}

		private static void LogMessage(string message, LogLevelEnum levelEnum, LogModuleTypeEnum logModuleTypeEnum)
		{
			//SLS日志
			if (CustomerConfig.IsDebug == false && logModuleTypeEnum != LogModuleTypeEnum.SLS)
			{
				WriteExceptionLog(message, levelEnum, logModuleTypeEnum);
			}

			//文件日志
			string traceId = "";
			string spanId = "";

			//OpenTelemetry链路id
			var span = Tracer.CurrentSpan;
			if (span != null && span.Context != null && span.Context.TraceId != null)
			{
				traceId = span.Context.TraceId.ToString();
				if (span.Context.SpanId != null)
				{
					spanId = span.Context.SpanId.ToString();
				}
			}
			message = string.Format("\r\nTraceId：{0}\r\nSpanId：{1}\r\nLogDetail：{2}\r\n", traceId, spanId, message);
			LogMessageToFile(message, levelEnum, logModuleTypeEnum);
		}

		/// <summary>
		/// 写阿里云异常日志
		/// </summary>
		/// <param name="message"></param>
		/// <param name="level"></param>
		private static void WriteExceptionLog(string message, LogLevelEnum levelEnum, LogModuleTypeEnum logModuleTypeEnum)
		{
			if (levelEnum != LogLevelEnum.ERROR && levelEnum != LogLevelEnum.WARN)
			{
				return;
			}
			if (string.IsNullOrWhiteSpace(message))
			{
				return;
			}
			//京东暂时不开放
			if (CustomerConfig.CloudPlatformType == "Jingdong")
			{
				return;
			}
			if (CustomerConfig.CloudPlatformType == "Pinduoduo")
			{
				return;
			}
			ThreadPool.QueueUserWorkItem(state =>
			{
				try
				{
					if (message.Length > 8192)
					{
						message = message.Substring(0, 8192);
					}
					//异常日志
					var logModel = new Exception(message).BuildLogErrorView("错误日志", levelEnum, logModuleTypeEnum);
					LogHelper.Instance.WriteLog(logModel);

				}
				catch
				{
					// ignored
				}
			});
		}

		public static void LogMessageToFile(string message, LogLevelEnum levelEnum, LogModuleTypeEnum logModuleTypeEnum)
		{
			var fileName = logModuleTypeEnum.ToString() + ".log";
			if (CustomerConfig.IsDebug)
			{
				fileName = logModuleTypeEnum.ToString() + ".txt";
			}

			//完整路径
			string path = $"{levelEnum.ToString()}/{fileName}";


			for (var i = 0; i < 5; i++)
			{
				try
				{
					lock (fileLocker)
					{
						LogManager.Configuration.Variables["FileName"] = path;
						switch (levelEnum)
						{
							case LogLevelEnum.ERROR:
								logger.Error(message);
								break;
							case LogLevelEnum.WARN:
								logger.Warn(message);
								break;
							case LogLevelEnum.DEBUG:
								logger.Debug(message);
								break;
							case LogLevelEnum.INFO:
								logger.Info(message);
								break;
						}
					}

					break;
				}
				catch (Exception err)
				{
					Thread.Sleep(50);
				}
			}

		}

	}
}
