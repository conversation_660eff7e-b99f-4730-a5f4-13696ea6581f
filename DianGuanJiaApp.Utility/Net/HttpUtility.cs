using DianGuanJiaApp.Utility.Extension;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using DianGuanJiaApp.Utility.FeiShu;

namespace DianGuanJiaApp.Utility.Net
{
    public class HttpUtility
    {
        /// <summary>
        /// 上传文件
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="webapiUrl"></param>
        /// <param name="httpContent"></param>
        /// <returns></returns>
        private static async Task<T> PostAsync<T>(string webapiUrl, HttpContent httpContent)
        {
            HttpClient httpClient = null;
            HttpResponseMessage response = null;
            try
            {
                httpClient = new HttpClient()
                {
                    MaxResponseContentBufferSize = 1024 * 1024 * 2,
                    BaseAddress = new Uri(webapiUrl)
                };
                
                response = await httpClient.PostAsync(webapiUrl, httpContent);

                if (response.Content != null && response.StatusCode == HttpStatusCode.OK)
                {
                    var resutlt = await response.Content.ReadAsStringAsync();
                    return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(resutlt);

                }
                return default(T);
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                if (httpClient != null) response.Dispose();
                if (response != null) response.Dispose();
            }
        }

        public static T Post<T>(string webapiUrl, string jsonString)
        {
            HttpContent httpContent = new StringContent(jsonString);

            httpContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            var task = Task.Run(() => PostAsync<T>(webapiUrl, httpContent));

            Task.WaitAll(task);

            return task.Result;
        }

        public static T Post<T>(string webapiUrl, object data)
        {
            var json = Newtonsoft.Json.JsonConvert.SerializeObject(data);

            return Post<T>(webapiUrl, json);
        }
        /// <summary>
        /// 上传文件
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="webapiUrl"></param>
        /// <param name="httpContent"></param>
        /// <returns></returns>
        private static string Post(string webapiUrl, HttpContent httpContent)
        {
            HttpClient httpClient = null;
            HttpResponseMessage response = null;
            try
            {
                httpClient = new HttpClient()
                {
                    MaxResponseContentBufferSize = 1024 * 1024 * 2,
                    BaseAddress = new Uri(webapiUrl)
                };

                response = httpClient.PostAsync(webapiUrl, httpContent).Result;

                if (response.Content != null && response.StatusCode == HttpStatusCode.OK)
                {
                    var resutlt = response.Content.ReadAsStringAsync().Result;
                    return resutlt;

                }
                return "";
            }
            catch (Exception ex)
            {
                return "";
            }
            finally
            {
                if (httpClient != null) httpClient.Dispose();
                if (response != null) response.Dispose();
            }
        }
        
        /// <summary>
        /// POST 请求视频号
        /// </summary>
        /// <param name="requestUrl"></param>
        /// <param name="jsonString"></param>
        /// <returns></returns>
        public static string PostByWxVideo(string requestUrl, string jsonString)
        {
            HttpContent httpContent = new StringContent(jsonString);

            httpContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            var str = PostByWxVideo(requestUrl, httpContent);
            return str;
        }
        /// <summary>
        /// POST 请求视频号
        /// </summary>
        /// <param name="requestUrl"></param>
        /// <param name="httpContent"></param>
        /// <returns></returns>
        private static string PostByWxVideo(string requestUrl, HttpContent httpContent)
        {
            HttpResponseMessage response = null;
            try
            {
                response = WxVideoHttpClient.Instance.PostAsync(requestUrl, httpContent).Result;
                if (response.Content == null || response.StatusCode != HttpStatusCode.OK)
                {
                    return string.Empty;
                }
                var result = response.Content.ReadAsStringAsync().Result;
                return result;
            }
            catch (Exception ex)
            {
                Log.WriteWarning($"视频号请求接口异常，异常原因：{ex}", $"WxVideoRequestError_{DateTime.Now:yyyy-MM-dd}.log");
                return string.Empty;
            }
            finally
            {
                response?.Dispose();
            }
        }

        public static string Post(string webapiUrl, string jsonString)
        {
            HttpContent httpContent = new StringContent(jsonString);

            httpContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");

            var str = Post(webapiUrl, httpContent);
            return str;
        }

        public static string PostToDingDing(string msg, List<string> atMobiles = null)
        {
            //if (atMobiles == null)
            //    atMobiles = new List<string>();
            //var url = "https://oapi.dingtalk.com/robot/send?access_token=79ecc8cc7fdb9c73b76e02220d2b26bdfaf9f676c832605661ecfbd095c16a79";
            //var rq = new
            //{
            //    msgtype = "text",
            //    text = new
            //    {
            //        content = $"服务器：{GetServerIP()}{Environment.NewLine}{msg}"
            //    },
            //    at = new
            //    {
            //        atMobiles = atMobiles
            //    }
            //};
            //var json = JsonConvert.SerializeObject(rq);
            ////return json;
            //System.Threading.ThreadPool.QueueUserWorkItem(state =>
            //{
            //    try
            //    {
            //        var str = Post(url, json);
            //    }
            //    catch
            //    {
            //    }
            //});
            return "";
        }
        
        /// <summary>
        /// 发送飞书通知
        /// </summary>
        /// <param name="msg"></param>
        /// <param name="isAtAll"></param>
        public static void NoticeMsgToFeiShu(string msg, bool isAtAll = false)
        {
            try
            {
                var url = "https://open.feishu.cn/open-apis/bot/v2/hook/6c4e0efb-8b26-482c-b872-1a719972bc92";
                if (CustomerConfig.CloudPlatformType == "Pinduoduo")
                    url = "http://open.feishu.cn:29943/open-apis/bot/v2/hook/6c4e0efb-8b26-482c-b872-1a719972bc92";

                var rst = new FeiShuRobotClient(url).Send($"【{CustomerConfig.CloudPlatformType}云】{msg}", isAtAll);
            }
            catch (Exception ex)
            {
                Log.WriteError($"消息发送飞书失败:{ex.Message}", $"HeartBeat-{DateTime.Now.ToString("yyyy-MM-dd")}.txt");
            }
        }

        public static string PostToDingDing(string msg, string url, List<string> atMobiles = null)
        {
            //if (atMobiles == null)
            //    atMobiles = new List<string>();
            ////var url = "https://oapi.dingtalk.com/robot/send?access_token=aaca758aa81ec2860157c3a8d33a10155da9a6f95093cf0f1b2b79bc7d79fd2f";
            //var rq = new
            //{
            //    msgtype = "text",
            //    text = new
            //    {
            //        content = $"{msg}"
            //    },
            //    at = new
            //    {
            //        atMobiles = atMobiles
            //    }
            //};
            //var json = JsonConvert.SerializeObject(rq);
            ////return json;
            //System.Threading.ThreadPool.QueueUserWorkItem(state =>
            //{
            //    try
            //    {
            //        var str = Post(url, json);
            //    }
            //    catch (Exception ex)
            //    {
            //        Log.WriteError($"PostToDingDing==> msg={msg},url={url},发送请求异常：{ex}");
            //    }
            //});
            return "";
        }

        public static void PostToRemind(string content, Exception ex = null)
        {
            try
            {
                var stringBuilder = new StringBuilder();
                stringBuilder.AppendLine($"** 开发环境：{CustomerConfig.IsDebug} **");
                stringBuilder.AppendLine($"FN消息：{content}");

                if (ex != null)
                {
                    stringBuilder.AppendLine($"异常：{ex.Message}");
                    stringBuilder.AppendLine($"堆栈：{ex.StackTrace} ");
                }

                var pubtext = stringBuilder.ToString();
                string url = "https://open.feishu.cn/open-apis/bot/v2/hook/7c17fb61-abd1-499f-a309-6503c65aeca0";

                var data = new
                {
                    msg_type = "interactive",
                    card = new
                    {
                        elements = new List<object> {
                            new {
                                tag = "markdown",
                                content = pubtext
                            }
                        },
                        header = new
                        {
                            title = new
                            {
                                content = "类目工具",
                                tag = "plain_text"
                            }
                        }
                    }
                };

                var message = JsonConvert.SerializeObject(data, Formatting.Indented);
                System.Threading.ThreadPool.QueueUserWorkItem(state =>
                {
                    var str = Post(url, message);
                    Debug.WriteLine(str);
                });
            }
            catch (Exception exce)
            {
                Log.WriteError($"机器人通知失败：{exce.Message} 堆栈：{exce.StackTrace}");
            }
        }

        #region 获取MAC地址

        [DllImport("Iphlpapi.dll")]
        private static extern int SendARP(Int32 dest, Int32 host, ref Int64 mac, ref Int32 length);
        [DllImport("Ws2_32.dll")]
        private static extern Int32 inet_addr(string ip);

        public static string GetClientMAC(string hostAddress)
        {
            string mac_dest = string.Empty;
            // 在此处放置用户代码以初始化页面
            try
            {
                string userip = hostAddress;
                string strClientIP = hostAddress.ToString().Trim();
                Int32 ldest = inet_addr(strClientIP); //目的地的ip 
                Int32 lhost = inet_addr("");   //本地服务器的ip 
                Int64 macinfo = new Int64();
                Int32 len = 6;
                int res = SendARP(ldest, 0, ref macinfo, ref len);
                string mac_src = macinfo.ToString("X");
                while (mac_src.Length < 12)
                {
                    mac_src = mac_src.Insert(0, "0");
                }
                for (int i = 0; i < 11; i++)
                {
                    if (0 == (i % 2))
                    {
                        if (i == 10)
                        {
                            mac_dest = mac_dest.Insert(0, mac_src.Substring(i, 2));
                        }
                        else
                        {
                            mac_dest = "-" + mac_dest.Insert(0, mac_src.Substring(i, 2));
                        }

                    }
                }
            }
            catch (Exception ex)
            {
            }
            return mac_dest;
        }
        #endregion

        #region 获取服务端IP

        private static string _serverIp;
        /// <summary>
        /// 获取服务器IP
        /// </summary>
        /// <returns></returns>
        public static string GetServerIP()
        {
            try
            {
                if (string.IsNullOrEmpty(_serverIp))
                {
                    ///获取服务端
                    var AddressIP = string.Empty;
                    foreach (IPAddress _IPAddress in Dns.GetHostEntry(Dns.GetHostName()).AddressList)
                    {
                        var ip = _IPAddress.ToString();
                        if (_IPAddress.AddressFamily.ToString() == "InterNetwork" && !ip.StartsWith("10."))
                        {
                            AddressIP = _IPAddress.ToString();
                            break;
                        }
                    }
                    _serverIp = AddressIP;
                }
            }
            catch (Exception ex)
            {
                _serverIp = "::1";
            }
            return _serverIp;

        }

        #endregion

        /// <summary>
        /// 客户端IP获取
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static string GetClientIP()
        {
            var request = HttpContext.Current?.Request;
            var IP = string.Empty;
            try
            {
                IP = request?.Headers["X-Forwarded-For"]?.ToString()?.Split(',')?.FirstOrDefault()?.Trim(); ;

                if (IP.IsNullOrEmpty() || IP == "0.0.0.0" || IP == "127.0.0.1")
                {
                    IP = request?.Headers["X_REAL_IP"]?.ToString()?.Split(',')?.FirstOrDefault()?.Trim();
                }
                if (IP.IsNullOrEmpty() || IP == "0.0.0.0" || IP == "127.0.0.1")
                {
                    IP = request?.ServerVariables["REMOTE_ADDR"]; ;
                }
                if (IP.IsNullOrEmpty() || IP == "0.0.0.0" || IP == "127.0.0.1")
                {
                    IP = request.UserHostAddress;
                }
                if (IP.IndexOf(",") > 0)
                {
                    string[] arIPs = IP.Split(',');

                    IP = arIPs[0];
                }
                if (IP.IsNullOrEmpty())
                {
                    IP = "127.0.0.1";
                }
            }
            catch (Exception)
            {
                IP = "127.0.0.1";
            }
            return IP;
        }
    }
}
