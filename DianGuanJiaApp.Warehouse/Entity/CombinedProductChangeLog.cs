using System;
using Dapper;
using DianGuanJiaApp.Data.Enum;

namespace DianGuanJiaApp.Warehouse.Entity
{
    /// <summary>
    /// 组合品变更日志
    /// </summary>
    [Table("CombinedProductChangeLog")]
    public class CombinedProductChangeLog
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 唯一Code，Guid.ToShortMD5()；
        /// </summary>
        public string ChangeCode { get; set; }

        /// <summary>
        /// 变更类型，0创建，1增加子货品，2移除子货品，3子货品出库数量变更
        /// </summary>
        public CombinedProductChangeType ChangeType { get; set; }

        /// <summary>
        /// 变更明细
        /// </summary>
        public string ChangeDetail { get; set; }

        /// <summary>
        /// 变更后子货品内容，来源ChildSkuModel.ToJson()
        /// </summary>
        public string AfterData { get; set; }

        /// <summary>
        /// 组合货品的唯一Code
        /// </summary>
        public string WareHouseProductCode { get; set; }

        /// <summary>
        /// 所属用户Code
        /// </summary>
        public string OwnerCode { get; set; }

        /// <summary>
        /// 所属用户Id
        /// </summary>
        public int FxUserId { get; set; }

        /// <summary>
        /// 创建时间，变更时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }
}