using System;
using Dapper;
using System.Data;

namespace DianGuanJiaApp.Warehouse.Entity
{
    /// <summary>
    /// 组合货品的子货品
    /// </summary>
    [Table("WareHouseChildSku")]
    public partial class WareHouseChildSku
    {
        /// <summary>
        /// 自增主键
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 货主编码，仓库所属人的Code
        /// </summary>
        public string OwnerCode { get; set; }

        /// <summary>
        /// 父库存SKU编码
        /// </summary>
        public string ParentWareHouseSkuCode { get; set; }

        /// <summary>
        /// 子库存SKU编码
        /// </summary>
        public string ChildWareHouseSkuCode { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 状态： 0 正常， 1被移除
        /// </summary>
        public int Status { get; set; }

    }
}
