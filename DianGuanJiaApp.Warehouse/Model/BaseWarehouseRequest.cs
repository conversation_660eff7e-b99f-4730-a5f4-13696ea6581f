using DianGuanJiaApp.Warehouse.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Warehouse.Model
{
    public abstract class BaseWarehouseRequest<T> : IWarehouseRequest<T> where T : BaseWarehouseRespone
    {
        public string OwnerCode { get; set; }
        public string RequestId { get; set; }

        public virtual void Check()
        {
            if (string.IsNullOrWhiteSpace(OwnerCode))
                throw new ArgumentException("货主编码不能为空");
        }

        public abstract string GetApiName();

    }
}
