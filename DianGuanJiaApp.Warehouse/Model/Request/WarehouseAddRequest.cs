using DianGuanJiaApp.Warehouse.Model.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Warehouse.Model.Request
{
    /// <summary>
    /// 添加仓库请求
    /// </summary>
    public class WarehouseAddRequest : BaseWarehouseRequest<WarehouseAddResponse>
    {
        public string WarehouseName { get; set; }
        public string Province { get; set; }
        public string City { get; set; }
        public string County { get; set; }
        public string Address { get; set; }
        public string Telphone { get; set; }
        public string Mobile { get; set; }
        public string Contact { get; set; }

        public override void Check()
        {
            base.Check();

            if (string.IsNullOrWhiteSpace(WarehouseName))
                throw new ArgumentException("WarehouseName不能为空");
            if (string.IsNullOrWhiteSpace(Province))
                throw new ArgumentException("Province不能为空");
            if (string.IsNullOrWhiteSpace(City))
                throw new ArgumentException("City不能为空");
            //if (string.IsNullOrWhiteSpace(County))
            //    throw new ArgumentException("County不能为空");
            if (string.IsNullOrWhiteSpace(Address))
                throw new ArgumentException("Address不能为空");
            if (string.IsNullOrWhiteSpace(Telphone) && string.IsNullOrWhiteSpace(Mobile))
                throw new ArgumentException("Telphone与Mobile必须有一个不为空");

        }

        public override string GetApiName()
        {
            return "warehouse.add";
        }
    }
}
