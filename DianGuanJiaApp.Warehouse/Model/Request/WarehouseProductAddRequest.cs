using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Other;
using DianGuanJiaApp.Warehouse.Entity;
using DianGuanJiaApp.Warehouse.Model.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Utility.Other;

namespace DianGuanJiaApp.Warehouse.Model.Request
{
    /// <summary>
    /// 仓库货品添加请求
    /// </summary>
    public class WarehouseProductAddRequest : BaseWarehouseRequest<WarehouseProductAddResponse>
    {
        public string Name { get; set; }
        public string CargoNumber { get; set; }
        public string ShortName { get; set; }
        public string ImageUrl { get; set; }

        public List<WareHouseSku> Skus { get; set; }

        public override void Check()
        {
            base.Check();
            //库存商品编码，不能为空至少要填写一个，最多100个
            if (string.IsNullOrWhiteSpace(Name))
                throw new LogicException("货品名称不能为空");
            if (Name.Length > 512)
                throw new LogicException("货品名称长度不能超过512个字符");
            if (Skus == null)
                throw new LogicException("货品至少包含一个Sku");
            foreach (var item in Skus)
            {
                if (string.IsNullOrWhiteSpace(item.SkuCargoNumber))
                    throw new LogicException($"{item.SkuName}的SkuCargoNumber不能为空");
                if (item.SkuCargoNumber.Length > 128)
                    throw new LogicException($"{item.SkuName}的SkuCargoNumber长度不能超过128个字符");
            }
        }

        public override string GetApiName()
        {
            return "warehouse.product.add";
        }

        /// <summary>
        /// 获取解密后的图片链接
        /// </summary>
        /// <returns></returns>
        public string GetDecryptUrl()
        {
            return ImgHelper.GetRealPath(ImageUrl);
        }
    }
}
