using DianGuanJiaApp.Warehouse.Model.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Warehouse.Model.Request
{
    /// <summary>
    /// 删除Sku请求
    /// WareHouseSkuCode和WareHouseSkuCodes不能都为空
    /// WareHouseSkuCode不为空单个删除；
    /// WareHouseSkuCode为空批量删除
    /// </summary>
    public class WareHouseSkuDeleteRequest : BaseWarehouseRequest<WareHouseSkuDeleteResponse>
    {
        /// <summary>
        /// Sku唯一编码
        /// </summary>
        public string WareHouseSkuCode { get; set; }
        /// <summary>
        /// Sku唯一编码（批量）
        /// </summary>
        public List<string> WareHouseSkuCodes { get; set; }

        /// <summary>
        /// 是否来自基础商品
        /// </summary>
        public bool isFormBaseProduct { get; set; } = false;

        /// <summary>
        /// 基础商品SkuCodeList
        /// </summary>
        public List<string> SkuCodes { get; set; }

        /// <summary>
        /// 货品编码
        /// </summary>
        public string SkuCargoNumber { get; set; }

        public override void Check()
        {
            base.Check();
            if (string.IsNullOrEmpty(WareHouseSkuCode) && (WareHouseSkuCodes == null || !WareHouseSkuCodes.Any()) && (SkuCodes == null || !SkuCodes.Any())) throw new ArgumentException("Sku唯一编码不能为空");
        }

        public override string GetApiName()
        {
            return "warehouse.sku.delete";
        }
    }
}
