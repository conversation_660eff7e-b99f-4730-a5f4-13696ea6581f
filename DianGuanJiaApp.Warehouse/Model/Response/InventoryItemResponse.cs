using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Warehouse.Model.Response
{
    public class InventoryItemResponse
    {
        /// <summary>
        /// 订单项唯一码
        /// </summary>
        public string OrderItemCode { get; set; }
        /// <summary>
        /// 扣减数量
        /// </summary>
        public int DeductionCount { get; set; }
        /// <summary>
        /// 库存SKU编码
        /// </summary>
        public string WarehouseSkuCode { get; set; }
        /// <summary>
        /// 扩展字段1
        /// </summary>
        public string ExtProperty1 { get; set; }
        /// <summary>
        /// 扣减后的可用库存数量
        /// </summary>
        public int AfterCount { get; set; }
        /// <summary>
        /// 库存变更记录唯一码
        /// </summary>
        public string StockChangeCode { get; set; }
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }
        /// <summary>
        /// 错误码，发生错误时必填
        /// </summary>
        public string ErrorCode { get; set; }
        /// <summary>
        /// 错误消息，发生错误时必填
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}
