using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Warehouse.Model.Response
{
    public class InventoryStockOutItemResponse
    {
        /// <summary>
        /// 出库数量
        /// </summary>
        public int ChangeCount { get; set; }
        /// <summary>
        /// 库存SKU编码
        /// </summary>
        public string WarehouseSkuCode { get; set; }
        /// <summary>
        /// 货号
        /// </summary>
        public string WareHouseShelfCode { get; set; }
        /// <summary>
        /// 出库后的可用库存数量
        /// </summary>
        public int AfterCount { get; set; }
        /// <summary>
        /// 库存变更记录唯一码
        /// </summary>
        public string StockChangeCode { get; set; }
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }
        /// <summary>
        /// 错误码
        /// </summary>
        public string ErrorCode { get; set; }
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 关联的原出入库记录
        /// </summary>
        public string OriginalStockChangeCode { get; set; }
    }
}
