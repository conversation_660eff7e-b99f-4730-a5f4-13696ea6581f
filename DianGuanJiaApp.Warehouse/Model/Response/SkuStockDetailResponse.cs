using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Warehouse.Model.Response
{
    public class SkuStockDetailResponse
    {
        /// <summary>
        /// WareHouseSku.Id
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// 仓库唯一码
        /// </summary>
        public string WareHouseCode { get; set; }
        /// <summary>
        /// 仓库名
        /// </summary>
        public string WareHouseName { get; set; }
        /// <summary>
        /// 库存商品唯一码
        /// </summary>
        public string WareHouseProductCode { get; set; }
        /// <summary>
        /// 库存商品名称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 库存商品编码
        /// </summary>
        public string ProductCargoNumber { get; set; }
        /// <summary>
        /// 库存商品简称
        /// </summary>
        public string ProductShortName { get; set; }
        /// <summary>
        /// 库存商品主图链接
        /// </summary>
        public string ProductImageUrl { get; set; }
        /// <summary>
        /// SKU编码
        /// </summary>
        public string SkuCargoNumber { get; set; }
        /// <summary>
        /// 货品唯一码
        /// </summary>
        public string WareHouseSkuCode { get; set; }
        /// <summary>
        /// SKU货品名称
        /// </summary>
        public string SkuName { get; set; }
        /// <summary>
        /// SKU货品规格信息
        /// </summary>
        public string SkuProperty { get; set; }
        /// <summary>
        /// SKU货品条码
        /// </summary>
        public string SkuBarCode { get; set; }
        /// <summary>
        /// 规格图链接
        /// </summary>
        public string ImageUrl { get; set; }
        /// <summary>
        /// 货品类型
        /// </summary>
        public string ItemType { get; set; }
        /// <summary>
        /// 货品简称
        /// </summary>
        public string ShortName { get; set; }
        /// <summary>
        /// 成本价
        /// </summary>
        public string CostPrice { get; set; }
        /// <summary>
        /// 安全数量
        /// </summary>
        public int SafetyCount { get; set; }
        /// <summary>
        /// 预警数量
        /// </summary>
        public int PreAlertCount { get; set; }
        /// <summary>
        /// 总库存数量=自身库存数+子货品可组合数量
        /// </summary>
        public int TotalStockCount { get; set; }
        /// <summary>
        /// 预占数量
        /// </summary>
        public int LockStockCount { get; set; }

        /// <summary>
        /// 关联商品数量
        /// </summary>
        public int TotalBindSkuCount { get; set; }

        /// <summary>
        /// 货品自身库存
        /// </summary>
        public int SelfStockCount { get; set; }

        /// <summary>
        /// 组合货品子商品可组合数量
        /// </summary>
        public int ComposeChildStockCount { get; set; }

        /// <summary>
        /// 历史出库数量
        /// </summary>
        public int? SumStockOutCount { get; set; }
        /// <summary>
        /// 历史入库数量
        /// </summary>
        public int? SumStockInCount { get; set; }
    }
}
