using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DianGuanJiaApp.Warehouse.Entity;

namespace DianGuanJiaApp.Warehouse.Model.Response
{
    public class WarehouseOrderStockInResponse : BaseWarehouseRespone
    {
        /// <summary>
        /// 仓库唯一码
        /// </summary>
        public string WareHouseCode { get; set; }

        /// <summary>
        /// 检查结果
        /// </summary>
        public bool CheckStatus { get; set; }

        /// <summary>
        /// 结果提示信息
        /// </summary>
        public string CheckMessage { get; set; }

        /// <summary>
        /// 处理结果集
        /// </summary>
        public List<OrderStockIn> Items { get; set; }
    }

    
}
