using Dapper;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Warehouse.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Warehouse.Repository
{
    public partial class WareHouseRepository : BaseRepository<WareHouse>
    {

        public WareHouseRepository() : base(CustomerConfig.WareHouseDB)
        { }

        public int AddWarehouse(WareHouse warehouse)
        {
            var id = Add(warehouse);
            return id;
        }

        public WareHouse GetByWarehouseCode(string warehouseCode)
        {
            var sql = "SELECT * FROM dbo.WareHouse WITH(NOLOCK) WHERE WarehouseCode = @code;";
            return DbConnection.QueryFirstOrDefault<WareHouse>(sql, new { code = warehouseCode });
        }

        public WareHouse GetByWarehouseCode(string warehouseCode, string ownerCode)
        {
            var sql = "SELECT * FROM dbo.WareHouse WITH(NOLOCK) WHERE WarehouseCode = @code AND OwnerCode=@ownerCode;";
            return DbConnection.QueryFirstOrDefault<WareHouse>(sql, new { code = warehouseCode, ownerCode = ownerCode });
        }

        public int GetWarehouseCount(string ownerCode)
        {
            var sql = "SELECT COUNT(1) FROM dbo.WareHouse WITH(NOLOCK) WHERE OwnerCode=@ownerCode;";
            return DbConnection.QueryFirstOrDefault<int>(sql, new { ownerCode = ownerCode });
        }

        public int UpdateWarehouseStatus(string status, int id)
        {
            var sql = "UPDATE dbo.WareHouse SET [Status]=@status WHERE Id=@id;";
            return DbConnection.Execute(sql, new { status = status, id = id });
        }

        internal List<WareHouse> GetWarehouseList(int pageIndex, int pageSize, string warehouseName, string address, string ownerCode, out int total)
        {
            total = 0;
            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("ownerCode", $"{ownerCode}");
            string sql_condition = "";

            if (!string.IsNullOrEmpty(warehouseName))
            {
                sql_condition += " AND  wh.WarehouseName like @warehouseName ";
                parameters.Add("warehouseName", $"%{warehouseName}%");
            }

            if (!string.IsNullOrEmpty(address))
            {
                sql_condition += " AND  wh.Address like @address ";
                parameters.Add("address", $"%{address}%");
            }

            string countsql = $" SELECT COUNT(1) FROM dbo.WareHouse wh WITH(NOLOCK) WHERE OwnerCode=@ownerCode {sql_condition} ";

            var paggingSql = $" OFFSET {((pageIndex - 1) * pageSize)} ROWS FETCH NEXT {pageSize} ROWS ONLY ";

            var sql = $@"
SELECT wh.*,
(
	SELECT COUNT(1) FROM SkuStockDetail sd WITH(NOLOCK) 
	INNER JOIN dbo.WareHouseSku ws WITH(NOLOCK) ON ws.WareHouseSkuCode = sd.WareHouseSkuCode 
	WHERE sd.WarehouseCode = wh.WarehouseCode AND ws.IsDeleted=0
 ) AS SkuCount FROM WareHouse wh 
                         WHERE wh.OwnerCode=@ownerCode {sql_condition} ORDER BY wh.CreateTime DESC {paggingSql} ";

            var grid = DbConnection.QueryMultiple(countsql + ";" + sql, parameters);

            total = grid.ReadFirstOrDefault<int>();
            var list = grid.Read<WareHouse>().ToList();
            return list;

        }

        public WareHouse GetWarehouseByOwnerCode(string ownerCode)
        {
            var sql = "SELECT * FROM dbo.WareHouse WITH(NOLOCK) WHERE OwnerCode=@ownerCode AND Status=N'Enabled';";
            return DbConnection.QueryFirstOrDefault<WareHouse>(sql, new { ownerCode = ownerCode });
        }

        public WareHouse GetDefaultWareHouse(string ownerCode, bool isEnabled = true)
        {
            var sql = "SELECT * FROM dbo.WareHouse WITH(NOLOCK) WHERE OwnerCode=@ownerCode ";
            if (isEnabled)
            {
                sql += " AND Status=N'Enabled'";
            }
            return DbConnection.QueryFirstOrDefault<WareHouse>(sql, new { ownerCode });
        }

        /// <summary>
        /// 生成唯一货品编码
        /// </summary>
        /// <param name="prefix">前缀</param>
        /// <param name="count">数量</param>
        /// <param name="type">1:生成商品编码，2：生成Sku编码</param>
        /// <returns></returns>
        public List<string> GenerateCargoNumber(string ownerCode, GenerateCargoNumberType type, string prefix, int count)
        {
            List<string> list = new List<string>();
            if (count == 0)
                return list;
            try
            {
                StringBuilder strsql = new StringBuilder();
                strsql.AppendLine("");
                for (int i = 0; i < count; i++)
                {
                    strsql.AppendLine($"SELECT '{prefix}-'+(CAST((NEXT VALUE FOR SEQ_ZHHP_INDEX) AS NVARCHAR)) AS VAL");
                }
                var sql = strsql.ToString();
                var value = DbConnection.QueryMultiple(sql);
                for (int i = 0; i < count; i++)
                {
                    var a = value.Read<string>();
                    list.Add(a.ElementAt(0));
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"获取自增字段异常：{ex}");
                for (int i = 0; i < count; i++)
                {
                    list.Add((Guid.NewGuid().ToString() + DateTime.Now.Ticks).ToShortMd5());
                }
            }
            // 检测生成的编码是否存在
            var existList = new List<string>();
            if (type == GenerateCargoNumberType.Product)
            {
                WareHouseProductRepository productRepository = new WareHouseProductRepository();
                existList = productRepository.CheckExistCargoNumber(ownerCode, list);
            }
            else if (type == GenerateCargoNumberType.Sku)
            {
                WareHouseSkuRepository skuRepository = new WareHouseSkuRepository();
                existList = skuRepository.CheckExistCargoNumber(ownerCode, list);
            }

            if (existList.Any())
            {
                list = list.Except(existList).Distinct().ToList();
                var newList = GenerateCargoNumber(ownerCode, type, prefix, existList.Count);
                list.AddRange(newList);
            }
            return list;
        }
    }
}
