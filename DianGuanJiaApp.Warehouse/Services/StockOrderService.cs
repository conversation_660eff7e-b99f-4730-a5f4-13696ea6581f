using DianGuanJiaApp.Warehouse.Entity;
using DianGuanJiaApp.Warehouse.Repository;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DianGuanJiaApp.Warehouse.Services
{
    public partial class StockOrderService : BaseService<StockOrder>
    {
        private StockOrderRepository _repository;

        public StockOrderService()
        {
            _repository = new StockOrderRepository();
            this._baseRepository = _repository;
        }

        /// <summary>
        /// 创建入库单
        /// </summary>
        /// <param name="_stockorder"></param>
        /// <returns></returns>
        public StockOrder AddStock(StockOrder _stockorder)
        {
            var id = 0;
            try
            {
                //保存入库单
                id = _repository.Add(_stockorder);
                _stockorder.Id = id;
                //保存入库单详细
                (new StockChangeDetailRecordService()).AddList(_stockorder.StockChangeDetails);
            }
            catch (Exception ex)
            {
                if (id > 0)
                    _repository.Delete(id);
                throw ex;
            }
            return _stockorder;
        }

        /// <summary>
        /// 根据货主编码+入库单唯一编码获取出入库单信息
        /// </summary>
        /// <param name="ownerCode">货主编码</param>
        /// <param name="stockOrderCode">唯一编码</param>
        /// <returns></returns>
        public StockOrder GetStockOrderByCode(string ownerCode, string stockOrderCode)
        {
            return _repository.GetStockOrderByCode(ownerCode, stockOrderCode);
        }

        /// <summary>
        /// 存储过程执行确认入库单
        /// </summary>
        /// <param name="ownerCode"></param>
        /// <param name="stockOrderCode"></param>
        /// <returns></returns>
        public StockOrder ConfirmEntryOrderPro(string ownerCode, string stockOrderCode)
        {
            return _repository.ConfirmEntryOrderPro(ownerCode, stockOrderCode);
        }

        /// <summary>
        /// 创建出库单
        /// </summary>
        /// <param name="_stockorder"></param>
        /// <returns></returns>
        public StockOrder StockOut(StockOrder _stockorder)
        {
            var id = 0;
            try
            {
                //保存出库单
                id = _repository.Add(_stockorder);
                _stockorder.Id = id;
                //保存出库单详细
                (new StockChangeDetailRecordService()).AddList(_stockorder.StockChangeDetails);
            }
            catch (Exception ex)
            {
                if (id > 0)
                    _repository.Delete(id);
                throw ex;
            }
            return _stockorder;
        }

        /// <summary>
        /// 确认出库单
        /// </summary>
        /// <param name="ownerCode"></param>
        /// <param name="stockOrderCode"></param>
        /// <returns></returns>
        public StockOrder StockoutConfirmPro(string ownerCode, string stockOrderCode)
        {
            return _repository.StockoutConfirmPro(ownerCode, stockOrderCode);
        }
    }
}
