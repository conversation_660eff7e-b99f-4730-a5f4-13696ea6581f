using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Warehouse.Entity;
using DianGuanJiaApp.Warehouse.Repository;
using System.Collections.Generic;

namespace DianGuanJiaApp.Warehouse.Services
{
    public partial class WareHouseSkuBindRelationService : BaseService<WareHouseSkuBindRelation>
    {
        private WareHouseSkuBindRelationRepository _repository;

        public WareHouseSkuBindRelationService()
        {
            _repository = new WareHouseSkuBindRelationRepository();
            this._baseRepository = _repository;
        }

        /// <summary>
        /// 查询绑定关系
        /// </summary>
        /// <param name="ownerCode">仓库所属人的Code</param>
        /// <param name="wareHouseSkuCode">货品SkuCode</param>
        /// <param name="platformSkuCode">平台商品SkuCode</param>
        /// <param name="shopId">平台商品对应的店铺ID</param>
        /// <returns></returns>
        public WareHouseSkuBindRelation GetBindRelation(string ownerCode, string wareHouseSkuCode, string platformSkuCode, int shopId, out string skuBindRelationCode)
        {
            skuBindRelationCode = (ownerCode + wareHouseSkuCode + platformSkuCode + shopId).ToShortMd5();
            return _repository.GetBindRelation(ownerCode, skuBindRelationCode);
        }

        /// <summary>
        /// 查询绑定关系
        /// </summary>
        /// <param name="ownerCode">仓库所属人的Code</param>
        /// <param name="skuBindRelationCode">绑定关系唯一编码</param>
        /// <returns></returns>
        public WareHouseSkuBindRelation GetBindRelation(string ownerCode, string skuBindRelationCode)
        {
            return _repository.GetBindRelation(ownerCode, skuBindRelationCode);
        }
        /// <summary>
        /// 批量查询绑定关系
        /// </summary>
        /// <param name="ownerCode">仓库所属人的Code</param>
        /// <param name="skuBindRelationCodes">绑定关系唯一编码</param>
        /// <returns></returns>
        public List<WareHouseSkuBindRelation> GetBindRelationList(string ownerCode, List<string> skuBindRelationCodes)
        {
            return _repository.GetBindRelationList(ownerCode, skuBindRelationCodes);
        }

        /// <summary>
        /// 查询绑定关系(用于判断同一个平台的同一个商品只能绑定一个SKU)
        /// </summary>
        /// <param name="ownerCode"></param>
        /// <param name="platformSkuCode"></param>
        /// <param name="platformType"></param>
        /// <returns></returns>
        public WareHouseSkuBindRelation GetBindRelation(string ownerCode, string platformSkuCode, string platformType)
        {
            return _repository.GetBindRelation(ownerCode, platformSkuCode, platformType);
        }

        /// <summary>
        /// 修改绑定关系状态
        /// </summary>
        /// <param name="status">状态</param>
        /// <param name="ownerCode">仓库所属人的Code</param>
        /// <param name="skuBindRelationCode">绑定关系唯一编码</param>
        /// <returns></returns>
        public bool UpdateSkuBindRelationStatus(int status, string ownerCode, string skuBindRelationCode)
        {
            return _repository.UpdateSkuBindRelationStatus(status, ownerCode, skuBindRelationCode);
        }

        /// <summary>
        /// 批量修改绑定关系状态
        /// </summary>
        /// <param name="status">状态</param>
        /// <param name="ownerCode">仓库所属人的Code</param>
        /// <param name="skuBindRelationCode">绑定关系唯一编码</param>
        /// <returns></returns>
        public bool BatchUpdateSkuBindRelationStatus(int status, string ownerCode, List<string> skuBindRelationCodes)
        {
            return _repository.BatchUpdateSkuBindRelationStatus(status, ownerCode, skuBindRelationCodes);
        }

        /// <summary>
        /// 回滚操作
        /// </summary>
        /// <param name="type">1=绑定回滚 2=取消绑定回滚</param>
        /// <param name="status"></param>
        /// <param name="ownerCode"></param>
        /// <param name="skuBindRelationCode"></param>
        /// <returns></returns>
        public bool RoolBack(int type, int status, string ownerCode, string skuBindRelationCode)
        {
            return _repository.RoolBack(type, status, ownerCode, skuBindRelationCode);
        }

        /// <summary>
        /// 批量回滚操作
        /// </summary>
        /// <param name="type">1=绑定回滚 2=取消绑定回滚</param>
        /// <param name="status"></param>
        /// <param name="ownerCode"></param>
        /// <param name="skuBindRelationCodes"></param>
        /// <returns></returns>
        public bool BatchRollBack(int type, int status, string ownerCode, List<string> skuBindRelationCodes)
        {
            return _repository.BatchRollBack(type, status, ownerCode, skuBindRelationCodes);
        }

        public List<WareHouseSkuBindRelation> GetWareHouseSkuBindRelationList(int pageIndex, int pageSize, string wareHouseSkuCode, string platformType, string skuCargoNumber, out int total, bool isAll = false)
        {
            return _repository.GetWareHouseSkuBindRelationList(pageIndex, pageSize, wareHouseSkuCode, platformType, skuCargoNumber, out total, isAll);
        }

        public bool AddWareHouseSkuBindRelation(List<WareHouseSkuBindRelation> datas)
        {
            return _repository.AddWareHouseSkuBindRelation(datas);
        }
    }
}
