using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Controllers;
using DianGuanJiaApp.Data;
using DianGuanJiaApp.Data.EntityExtension;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.Api
{
    public class ScanSendApiController : ApiBaseController
    {
        ShopService shopService = new ShopService();

        [LogForOperatorFilter("扫描订单发货查询API")]
        public ActionResult Scan()
        {
            var scanSendQueryModel = RequestModel.GetParamModel<ScanSendQueryModel>();

            ScanSendQueryResultModel result;
            try
            {
                var strConfig = CustomerConfigExt.GetConnectString(SiteContext.Current.CurrentLoginShop);
                var _scanSendGoodsService = new ScanSendGoodsService(strConfig);
                result = _scanSendGoodsService.QueryCodeV2(scanSendQueryModel);
            }
            catch (Exception ex)
            {
                Log.WriteError($"扫描订单发货查询,错误详情：{ex}");
                throw new LogicException("服务器繁忙，请稍后再试！");
            }

            return SuccessResult(result);
        }


        [LogForOperatorFilter("扫描订单发货API")]
        public ActionResult Send()
        {
            var sendRequestModel = RequestModel.GetParamModel<ScanSendRequestModel>();

            ActionResult result;
            try
            {
                result = (new ScanSendGoodsController()).SendOrder(
                    SiteContext.Current.CurrentLoginShop,
                    sendRequestModel.ExpressId,
                    sendRequestModel.OrderRequest,
                    sendRequestModel.WaybillCode,
                    sendRequestModel.Weight,
                    sendRequestModel.IsSendPreCheck);
            }
            catch (Exception ex)
            {
                Log.WriteError($"扫描发货,错误详情：{ex}");
                throw new LogicException("服务器繁忙，请稍后再试！");
            }

            return result;
        }
    }
}