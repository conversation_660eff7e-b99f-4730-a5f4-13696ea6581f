using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Web;
using System.Web.Mvc;
using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.EntityExtension;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.Net;
using DianGuanJiaApp.Utility.NPOI;

namespace DianGuanJiaApp.Controllers
{

    /// <summary>
    /// 公共控制器
    /// 获取一些公共数据的方法
    /// </summary>
    public class CommonController : BaseController
    {
        private ExpressCompanyService expressCompanyService = new ExpressCompanyService();
        private AreaCodeInfoService areaCodeInfoService = new AreaCodeInfoService();
        private CommonSettingService commonSettingService = new CommonSettingService();
        private PrinterBindService printerBindService = new PrinterBindService();
        private ShopService ShopBindService = new ShopService();
        private OrderCategoryService _orderCategory = new OrderCategoryService();
        private CommonSettingService _commonSettingService = new CommonSettingService();


        /// <summary>
        /// 获取系统快递公司
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadExpressCompany()
        {
            var expressCompanyList = expressCompanyService.GetExpressCompay();

            return SuccessResult(expressCompanyList);
        }

        /// <summary>
        /// 获取区域信息
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        //[IgnoreToken]
        public ActionResult LoadAreaCodeInfo(int parentId)
        {
            var areaCodeInfoList = areaCodeInfoService.GetAreaInfoList(parentId)?.OrderBy(a => a.Name)?.ToList();

            return SuccessResult(areaCodeInfoList);
        }

        /// <summary>
        /// 获取所有的省市区信息
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadAreas()
        {
            var provinceKey = new string[] { "特别行政区", "古自治区", "维吾尔自治区", "壮族自治区", "回族自治区", "自治区", "省省直辖", "省", "市" };
            var cityKey = new string[] { "布依族苗族自治州", "苗族侗族自治州", "自治州", "州", "市", "县" };
            var areas = areaCodeInfoService.GetAreaInfoList();
            var provinces = areas.Where(m => m.ParentId == 1);
            var result = provinces.Select(p => new
            {
                name = p.Name.TrimEnd(provinceKey),
                label = p.Name,
                p.Id,
                city = areas.Where(c => c.ParentId == p.Id).Select(c => new
                {
                    name = c.Name.TrimEnd(cityKey),
                    label = c.Name,
                    c.Id,
                    area = areas.Where(a => a.ParentId == c.Id).Select(a => a.Name)
                })
            });
            return SuccessResult(result);
        }

        public ActionResult LoadCommonSetting(string settingKey)
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var setting = commonSettingService.GetString(settingKey, shopId);
            if (settingKey == "PrintContent" && setting == null)
            {
                var val = "{\"PrintContentSet\":[{\"Name\":\"序号\",\"Value\":\"SerialNumber\",\"Checked\":true,\"Sort\":0},{\"Name\":\"简称标题\",\"Value\":\"Title\",\"Checked\":true,\"Sort\":1},{\"Name\":\"货号\",\"Value\":\"UPC\",\"Checked\":true,\"Sort\":2},{\"Name\":\"规格颜色\",\"Value\":\"Color\",\"Checked\":true,\"Sort\":3},{\"Name\":\"规格尺码\",\"Value\":\"Size\",\"Checked\":true,\"Sort\":4},{\"Name\":\"单价\",\"Value\":\"Price\",\"Checked\":false,\"Sort\":5},{\"Name\":\"数量\",\"Value\":\"Qty\",\"Checked\":true,\"Sort\":6}],\"TitleSet\":\"NoShortPrintTitle\",\"UpcSet\":\"NoProductUpcPirntGoodsUpc\",\"MergeGoodsSet\":false,\"LinePrintNum\":1,\"PrintPaging\":\"Paging\",\"PagingProductNum\":5,\"QtyStryle\":\"[{0}]\",\"QtyUnit\":\"无单位\"}";
                if (SiteContext.Current.CurrentLoginShop.PlatformType == "YouZan")
                    val = "{\"PrintContentSet\":[{\"Name\":\"序号\",\"Value\":\"SerialNumber\",\"Checked\":true,\"Sort\":0},{\"Name\":\"简称标题\",\"Value\":\"Title\",\"Checked\":true,\"Sort\":1},{\"Name\":\"货号\",\"Value\":\"UPC\",\"Checked\":true,\"Sort\":2},{\"Name\":\"商品规格\",\"Value\":\"ProductAttr\",\"Checked\":true,\"Sort\":3},{\"Name\":\"单价\",\"Value\":\"Price\",\"Checked\":false,\"Sort\":4},{\"Name\":\"数量\",\"Value\":\"Qty\",\"Checked\":true,\"Sort\":5}],\"TitleSet\":\"NoShortPrintTitle\",\"UpcSet\":\"NoProductUpcPirntGoodsUpc\",\"MergeGoodsSet\":false,\"LinePrintNum\":1,\"PrintPaging\":\"Paging\",\"PagingProductNum\":5,\"QtyStryle\":\"[{0}]\",\"QtyUnit\":\"无单位\"}";
                else if (SiteContext.Current.CurrentLoginShop.PlatformType == "KuaiShou")
                    val = "{\"PrintContentSet\":[{\"Name\":\"序号\",\"Value\":\"SerialNumber\",\"Checked\":true,\"Sort\":0},{\"Name\":\"简称标题\",\"Value\":\"Title\",\"Checked\":true,\"Sort\":1},{\"Name\":\"SKU编码\",\"Value\":\"UPC\",\"Checked\":true,\"Sort\":2},{\"Name\":\"产品ID\",\"Value\":\"ProductID\",\"Checked\":true,\"Sort\":3},{\"Name\":\"规格颜色\",\"Value\":\"Color\",\"Checked\":true,\"Sort\":4},{\"Name\":\"规格尺码\",\"Value\":\"Size\",\"Checked\":true,\"Sort\":5},{\"Name\":\"单价\",\"Value\":\"Price\",\"Checked\":false,\"Sort\":6},{\"Name\":\"数量\",\"Value\":\"Qty\",\"Checked\":true,\"Sort\":7}],\"TitleSet\":\"NoShortPrintTitle\",\"UpcSet\":\"NoProductUpcPirntGoodsUpc\",\"MergeGoodsSet\":false,\"LinePrintNum\":1,\"PrintPaging\":\"Paging\",\"PagingProductNum\":5,\"QtyStryle\":\"[{0}]\",\"QtyUnit\":\"无单位\"}";

                var result = commonSettingService.Set(settingKey, val, shopId);
                if (result > 0)
                    setting = val;
            }
            else if (settingKey == "ScanSendSelectContentSet" && setting == null)
            {
                var val = "{\"AutoSend\":false,\"WeightOpen\":false,\"UnitIndex\":false}";
                var result = commonSettingService.Set(settingKey, val, shopId);
                if (result > 0)
                    setting = val;
            }
            return SuccessResult(setting);
        }

        public ActionResult SaveCommonSetting(string settingKey, string settingValue)
        {
            //TODO:ShopId 待 授权做好之后,再修改为授权店铺
            var shopId = SiteContext.Current.CurrentShopId;
            var date = DateTime.Now;
            // 查询字段为时间格式异常日志记录
            if (settingKey.ToString2() == "CustomConditionSet" && DateTime.TryParse(settingValue.ToString2(), out date))
            {
                try
                {
                    var ip = Request?.Headers["X-Forwarded-For"];
                    if (string.IsNullOrEmpty(ip))
                        ip = Request?.UserHostAddress;
                    var log = new LogForOperator()
                    {
                        OperatorType = "查询条件异常",
                        TraceId = Request?["traceId"],
                        DBType = DatabaseTypeEnum.SQLServer.ToString(),
                        ShopId = SiteContext.GetCurrentShopId(),
                        UserId = SiteContext.GetCurrentFxUserId(),
                        IP = ip,
                        ServerIP = DianGuanJiaApp.Utility.Net.HttpUtility.GetServerIP(),
                        Description = new OperationDescription
                        {
                            Route = $"Common/SaveCommonSetting?settingKey={settingKey.ToString2()}&settingValue={settingValue.ToString2()}",
                            Referrer = Request?.UrlReferrer?.ToString(),
                            UserAgent = Request?.UserAgent,
                            Url = Request?.Url?.ToString(),
                            Name = "查询条件异常"
                        },

                    };
                    LogForOperatorContext.Current.Begin(log);
                    LogForOperatorContext.Current.End();
                }
                catch (Exception ex)
                {
                    Log.WriteError($"查询条件设置异常：{ex}");
                }
            }
            var result = commonSettingService.Set(settingKey, settingValue, shopId);
            if (result > 0)
                return SuccessResult();
            else
                return FalidResult("保存配置失败");
        }


        /// <summary>
        /// 加载打印机绑定数据
        /// </summary>
        /// <returns></returns>
        public ActionResult LoadPrinterBind()
        {
            var printerBindList = printerBindService.Get(" WHERE ShopId IN @ShopIds", new { ShopIds = SiteContext.Current.ShopIds });
            return Json(printerBindList);
        }

        /// <summary>
        /// 绑定打印机
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public ActionResult BindPrinter(PrinterBind model)
        {
            if (model == null || model.TemplateId == 0 || model.TemplateType == 0 || string.IsNullOrWhiteSpace(model.PrinterName))
            {
                return FalidResult("请求参数错误.");
            }
            else
            {
                model.ShopId = SiteContext.Current.CurrentShopId;
                printerBindService.Delete("WHERE ShopId=@ShopId AND TemplateId=@TemplateId AND TemplateType=@TemplateType", new
                {
                    ShopId = model.ShopId,
                    TemplateId = model.TemplateId,
                    TemplateType = model.TemplateType
                });
                try
                {
                    int id = printerBindService.Add(model);
                    model.Id = id;
                }
                catch (Exception ex)
                {
                    Log.WriteError($"绑定打印机异常：{ex}");
                }
            }
            return SuccessResult(model);
        }

        public ActionResult moreFunLeftNav()
        {
            return PartialView();
        }

        public ActionResult LoadShopBind()
        {
            var shops = SiteContext.Current.AllShops;
            return SuccessResult(shops);
        }

        public ActionResult LoadShopBindEqualType()
        {
            Shop _shops = SiteContext.Current.CurrentLoginShop;
            var shops = SiteContext.Current.AllShops.Where(m => m.PlatformType == _shops.PlatformType).ToList();
            return SuccessResult(shops);
        }

        public ActionResult LoadOrderCategoryBind()
        {
            var categorys = _orderCategory.Get().ToList();
            return SuccessResult(categorys);
        }

        public ActionResult SaveFile(string fileDirectory)
        {
            //Thread.Sleep(TimeSpan.FromSeconds(1));

            if (string.IsNullOrWhiteSpace(fileDirectory))
            {
                //return FalidResult("没有指定文件保存路劲");
                fileDirectory = "Files/Temp";
            }
            var path = $"/{fileDirectory.TrimStart('/').TrimEnd('/')}/";
            var directory = Server.MapPath("~" + path);
            if (System.IO.Directory.Exists(directory) == false)
            {
                System.IO.Directory.CreateDirectory(directory);
            }

            if (Request.Files.Count > 0)
            {
                var file = Request.Files[0];
                if (file.FileName.EndsWith(".exe") == true)
                {
                    //return FalidResult("不能上传.exe文件");

                    return Content((new AjaxResult()
                    {
                        Success = false,
                        Message = "不能上传.exe文件"
                    }).ToJson());
                }
                var fileName = (DateTime.Now.ToString("yyyyMMddHHmmssfff") + file.FileName);
                file.SaveAs(directory + fileName);
                //return SuccessResult("文件上传成功");                
                //return SuccessResult(path + fileName);
                return Content((new AjaxResult()
                {
                    Success = true,
                    Data = path + fileName
                }).ToJson());
            }
            else
            {
                //return FalidResult("未读取到文件");

                return Content((new AjaxResult()
                {
                    Success = false,
                    Message = "未读取到文件"
                }).ToJson());
            }
        }

        /// <summary>
        /// 图片上传
        /// </summary>
        /// <param name="fileName">文件名称，必填，需包含后缀</param>
        /// <param name="fileContent">文件内容（base64编码）</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult UploadImage(string fileName, string fileContent)
        {
            var rspJson = string.Empty;
            try
            {
                var para = new Dictionary<string, string> { { "fileName", fileName }, { "fileContent", fileContent }, { "memberId", SiteContext.Current.CurrentLoginShop.ShopId }, { "key", DES.EncryptDES(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "sda98yp3") } };
                rspJson = WebRequestHelper.PostRequest(CustomerConfig.ImageServer + "/Image.ashx?FuncName=Upload&key=debug", para, "UTF-8");
            }
            catch (Exception ex)
            {
                Log.WriteError("上传图片时发生错误：" + ex.ToString());
                return OriginalJson(new { IsOk = false, Message = "图片服务器暂不可用，请使用图片链接" });
            }

            var result = rspJson.ToObject<dynamic>();
            if (result.IsOk == true)
            {
                return OriginalJson(new { IsOk = result.IsOk, Message = result.Message, Data = CustomerConfig.ImageServerOut + result.Data });
            }
            else
            {
                return OriginalJson(new { IsOk = result.IsOk, Message = result.Message });
            }
        }


        /// <summary>
        /// 文件上传 到 文件服务器
        /// </summary>
        /// <param name="fileName">文件名称，必填，需包含后缀</param>
        /// <param name="fileContent">文件内容（base64编码）</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult UploadFile()
        {
            if (Request.Files.Count == 0 || Request.Files["upfile"] == null)
                return FalidResult("没有选择文件上传");
            try
            {
                var postedFile = Request.Files["upfile"];//获取上传的文件
                string fileName = postedFile.FileName;

                //文件格式校验
                var notSupportFileExt = new List<string> { ".exe", ".js", ".vbs" };
                foreach (var ext in notSupportFileExt)
                {
                    if (fileName.EndsWith(ext))
                    {
                        return FalidResult($"不允许上传{ext}的文件");
                    }
                }

                //文件大小校验
                if (postedFile.ContentLength == 0)
                {
                    return FalidResult("请勿导入空文件");
                }
                var maxSize = Request.Params["MaxSize"].ToInt();
                if (maxSize > 0)
                {
                    var size = postedFile.ContentLength * 1.0 / 1024 / 1024;
                    if (size > maxSize)
                    {
                        return FalidResult($"导入文件请不要超过{maxSize}M");
                    }
                }

                byte[] bs = new byte[postedFile.InputStream.Length];
                var length = Convert.ToInt32(postedFile.InputStream.Length);
                postedFile.InputStream.Position = 0;
                //var pageSize = 100000;
                //var count = (int)Math.Ceiling(postedFile.InputStream.Length / (double)pageSize);
                postedFile.InputStream.Read(bs, 0, length);

                var fileContentBase64 = Convert.ToBase64String(bs);

                var para = new Dictionary<string, string> { { "fileName", fileName }, { "fileContent", fileContentBase64 }, { "memberId", SiteContext.Current.CurrentLoginShop.ShopId }, { "key", DES.EncryptDES(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), "sda98yp3") } };

                //上传到文件服务器
                var rspJson = WebRequestHelper.PostRequest(CustomerConfig.ImageServer + "/Image.ashx?FuncName=Export&key=debug", para, "UTF-8");

                var result = rspJson.ToObject<dynamic>();
                if (result.IsOk == false)
                    return FalidResult(result.Message);
                else
                    return SuccessResult(result.Data);

            }
            catch (Exception ex)
            {
                var errorMsg = "文件上传到文件服务器失败：" + ex.Message;
                Log.WriteError(errorMsg);
                return FalidResult(errorMsg);
            }
        }

        /// <summary>
        ///  文件服务器上的文件下载
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        [LogForOperatorFilter("文件下载")]
        public string Download(string filePath, string fileName)
        {
            var logInfo = LogForOperatorContext.Current?.logInfo;
            if (logInfo != null)
            {
                logInfo.Request = fileName;
            }
            System.IO.Stream iStream = null;
            try
            {
                var taskId = Request["taskId"].ToInt();
                if (taskId == 0)
                    return "数据下载异常(0)";
                var task = new ShareWaybillAccountCheckingRecordService().Get(taskId);
                if (task == null)
                    return "数据下载异常(1)";
                else if (task.ShopId != SiteContext.Current.CurrentShopId)
                    return "数据下载异常(2)";
                if (task.MatchedDoneFile?.Trim() != filePath?.Trim())
                    return "数据下载异常(3)";

                System.Net.WebClient client = new System.Net.WebClient();
                var httpPath = CustomerConfig.ImageServer + filePath;
                byte[] data = client.DownloadData(httpPath);

                var downloadToken = Request.Form["downloadToken"].ToString2();
                Response.Cookies.Add(new HttpCookie("downloadToken", downloadToken));
                Response.ContentType = "application/octet-stream";
                Response.AddHeader("Content-Disposition", "attachment; filename=" + System.Web.HttpUtility.UrlEncode(fileName));//System.Text.UTF8Encoding.UTF8.GetBytes(FileName)
                Response.OutputStream.Write(data, 0, data.Length);
                Response.Flush();
                //return Content(downloadToken);                                                             
            }
            catch (Exception ex)
            {
                if (logInfo != null)
                {
                    logInfo.Response = ex.Message;
                }
                Response.Cookies.Add(new HttpCookie("downloadToken", "error"));
                string message = ex.Message;
                //return Content("error");
                return "<script>alert('Error : " + message + "');</script>";
            }
            finally
            {
                if (iStream != null)
                {
                    iStream.Close();
                }
            }
            if (logInfo != null)
            {
                logInfo.Response = "下载成功";
            }
            //return Content("");
            return string.Empty;
        }


        [LogForOperatorFilter("文件下载方式2")]
        public ActionResult Download2()
        {
            var fileName = Request["fileName"].ToString2();
            var filePath = Request["filePath"].ToString2();
            var taskId = Request["taskId"].ToInt();
            var fileFrom = Request["fileFrom"].ToString2();
            var httpPath = CustomerConfig.ImageServer + filePath;
            byte[] data = new byte[] { };
            var fileContent = string.Empty;

            var _taskService = new ExportTaskService();
            ExportTask task = null;

            var logInfo = LogForOperatorContext.Current?.logInfo;
            try
            {
                if (taskId == 0)
                    return Content("数据下载异常(0)");

                //下载分单对账账单文件
                if (fileFrom?.ToLower() == "settlement")
                {
                    var settlementBillModel = new SettlementBillService().Get(taskId);
                    if (settlementBillModel == null
                        || (settlementBillModel.FxUserId != SiteContext.Current.CurrentFxUserId && settlementBillModel.CreateUser != SiteContext.Current.CurrentFxUserId)
                        || (settlementBillModel.FilePath?.Trim() != filePath?.Trim() && settlementBillModel.FilePath2?.Trim() != filePath?.Trim()))
                        return Content($"账单不存在");
                }
                else
                {
                    task = _taskService.Get(taskId);

                    if (task == null)
                        return Content("数据下载异常(1)");
                    else if (task.ShopId != SiteContext.Current.CurrentShopId)
                        return Content("数据下载异常(2)");
                    if (task.FilePath?.Trim() != filePath?.Trim())
                        return Content("数据下载异常(3)");
                }

                System.Net.WebClient client = new System.Net.WebClient();
                data = client.DownloadData(httpPath);

                if (filePath.EndsWith(".json") && data.Length > 0)
                {
                    //忽略BOM头将其转string 
                    fileContent = data.ToUTF8String();
                    if (!fileContent.IsNullOrEmpty() && task != null && task.Type == ExportType.ExpressBill.ToInt())
                    {
                        var showType = Request["showType"].ToString2();
                        var returnModel = new ExpressBillReturnModel();
                        var expressBillViewModel = fileContent.ToObject<ExpressBillViewModel>();

                        switch (showType)
                        {
                            case "use_list":
                                returnModel.List = expressBillViewModel?.UseList ?? new List<ExpressWaybillCodeModel>();
                                break;
                            case "check_list":
                                returnModel.List = expressBillViewModel?.CheckList ?? new List<ExpressWaybillCodeModel>();
                                break;
                            case "found_list":
                                returnModel.List = expressBillViewModel?.FoundList ?? new List<ExpressWaybillCodeModel>();
                                break;
                            case "not_found_list":
                                returnModel.List = expressBillViewModel?.NotFoundList ?? new List<ExpressWaybillCodeModel>();
                                break;
                            case "recycle_list":
                                returnModel.List = expressBillViewModel?.RecycleList ?? new List<ExpressWaybillCodeModel>();
                                break;
                            case "repeat_list":
                                returnModel.List = expressBillViewModel?.RepeatList ?? new List<ExpressWaybillCodeModel>();
                                break;
                            default:
                                returnModel.List = expressBillViewModel?.CheckList ?? new List<ExpressWaybillCodeModel>();
                                break;
                        }
                        returnModel.CurrentShowType = showType.IsNullOrEmpty() ? "check_list" : showType;
                        returnModel.UseCount = expressBillViewModel?.UseList?.Count ?? 0;
                        returnModel.CheckCount = expressBillViewModel?.CheckList?.Count ?? 0;
                        returnModel.FoundCount = expressBillViewModel?.FoundList?.Count ?? 0;
                        returnModel.NotFoundCount = expressBillViewModel?.NotFoundList?.Count ?? 0;
                        returnModel.RecycleCount = expressBillViewModel?.RecycleList?.Count ?? 0;
                        returnModel.RepeatCount = expressBillViewModel?.RepeatList?.Count ?? 0;
                        fileContent = returnModel.ToJson();
                    }
                }
                else
                    fileName = ExcelHelper.GetFileName(fileName, Request);
            }
            catch (Exception ex)
            {
                if (logInfo != null)
                    logInfo.Response = ex.Message;
                return FalidResult("error");
            }

            if (logInfo != null)
                logInfo.Response = "下载成功";
            if (task != null)
            {
                task.Status = 5;
                _taskService.UpdateStatus(task, false);
            }

            if (filePath.IndexOf(".xlsx") >= 0)
                return File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            else if (filePath.IndexOf(".xls") >= 0)
                return File(data, "application/ms-excel", fileName);
            else if (filePath.IndexOf(".json") >= 0)
                return SuccessResult(fileContent);
            else
                return FalidResult("未知文件类型");
        }

        public ActionResult LoadPrintSerialNumber()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var setting = commonSettingService.Get(CommonSettingService.PrintSerialSet, shopId);
            return SuccessResult(setting);
        }

        public ActionResult ClearSerialNumber()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            PrintSerialSetting printSerialSetting = _commonSettingService.GetPrintSerialSetting(shopId);
            if (printSerialSetting != null)
            {
                var clearFormat = printSerialSetting.ClearSet.ToString2();
                if (!clearFormat.IsNullOrEmpty())
                {
                    var key = DateTime.Now.ToString(clearFormat);
                    var value = printSerialSetting.PrintSerialValues.FirstOrDefault(m => m.DateTime == key);
                    if (value != null)
                    {
                        value.SerialNum = 0;
                    }
                    var printSerialSettingJson = JsonExtension.ToJson(printSerialSetting);
                    _commonSettingService.UpdatePrintSerialNumber(printSerialSettingJson, shopId);
                }
            }
            return SuccessResult();
        }

        /// <summary>
        /// 地址识别日志
        /// </summary>
        /// <param name="addr"></param>
        /// <param name="discernResult"></param>
        /// <returns></returns>
        [LogForOperatorFilter("地址识别")]
        public ActionResult AddressDiscernLog(string addr, bool isDiscernName, string discernResult)
        {
            var result = discernResult.ToObject<dynamic>();

            var loginfo = LogForOperatorContext.Current.logInfo;
            try
            {
                loginfo.TotalCount = 1;
                loginfo.Remark = addr; //识别的地址
                loginfo.Detail = discernResult; //识别结果
                if (result.isSuccess == false)
                {
                    loginfo.IsError = true;
                    //loginfo.Exception = discernResult;
                }
                else if (result.needConfirm == true)
                {
                    loginfo.SuccessCount = 1; //查询 SuccessCount==1为成功识别的
                    loginfo.Detail = "识别成功需要确认：" + loginfo.Detail;
                }
                else
                {
                    loginfo.SuccessCount = 1;//查询 SuccessCount==1为成功识别的
                }
            }
            catch (Exception ex)
            {
                loginfo.Exception = ex.ToString();
            }

            return SuccessResult();
        }

        /// <summary>
        /// 订单打印，前端错误日志
        /// </summary>
        /// <param name="addr"></param>
        /// <param name="isDiscernName"></param>
        /// <param name="discernResult"></param>
        /// <returns></returns>
        [LogForOperatorFilter]
        public ActionResult JavasScriptExcptionLog(string operatorType, string exception)
        {
            var loginfo = LogForOperatorContext.Current.logInfo;
            loginfo.OperatorType = operatorType;
            loginfo.Exception = exception;

            return SuccessResult();
        }

        /// <summary>
        /// 前端日志
        /// </summary>
        /// <param name="addr"></param>
        /// <param name="isDiscernName"></param>
        /// <param name="discernResult"></param>
        /// <returns></returns>
        [LogForOperatorFilter]
        public ActionResult JavasScriptLog(string operatorType, string logJson)
        {
            var loginfo = LogForOperatorContext.Current.logInfo;
            loginfo.OperatorType = operatorType;
            loginfo.Remark = logJson;

            return SuccessResult();
        }


        public ActionResult TestPage()
        {
            return View();
        }

        public ActionResult TestBatchAjax(int requestNumber, List<int> datas)
        {

            if ((new int[] { 25, 18, 38 }).Contains(requestNumber))
            {
                throw new Exception("后台发生错误...");
            }

            if (requestNumber < 10)
            {
                Thread.Sleep(TimeSpan.FromMinutes(3));
                return SuccessResult(new { Sleep = 0, RequestNumber = requestNumber, Data = datas });
            }

            if (datas.Last() > 50)
            {
                int x = (new Random().Next(1, 6)) * 1000;

                Thread.Sleep(x);
                return SuccessResult(new { Sleep = x, RequestNumber = requestNumber, Data = datas });
            }

            int s = (new Random().Next(1, 5)) * 1000;
            Thread.Sleep(s);
            return SuccessResult(new { Sleep = s, RequestNumber = requestNumber, Data = datas });


        }

        [HttpPost]
        public ActionResult ReAuthorization(int shopId, string appKey, string appSecret)
        {
            var shop = SiteContext.Current.CurrentLoginShop;
            if (shop == null)
                return FalidResult("未登陆店铺");

            if (shopId != 0)
            {
                shop = SiteContext.Current.AllShops.FirstOrDefault(m => m.Id == shopId);
                if (shop == null)
                    return FalidResult($"店铺【{shopId}】不存在");
            }

            if (appKey.IsNullOrEmpty() || appSecret.IsNullOrEmpty())
                return FalidResult("AppKey和AppSecret都为必填项");

            var oldAppKey = shop.AccessToken;
            var oldAppSecret = shop.RefreshToken;

            ShopService _shopService = new ShopService();
            shop.AccessToken = appKey;
            shop.RefreshToken = appSecret;
            shop.ShopId = shop.ShopId.IsNullOrEmpty() ? appKey : shop.ShopId;
            shop.LastSyncMessage = $"已重新授权，AppKey【{oldAppKey}->{appKey}】,AppSecret【{oldAppSecret}->{appSecret}】";
            var result = _shopService.Update(shop);
            return SuccessResult(result);
        }

    }
}
