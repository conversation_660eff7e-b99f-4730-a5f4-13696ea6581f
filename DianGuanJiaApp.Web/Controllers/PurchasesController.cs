using AngleSharp.Parser.Html;
using Dapper;
using DianGuanJiaApp.App_Start;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Models;
using DianGuanJiaApp.Models.Purchases;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.PlatformService;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NPOI.HPSF;
using NPOI.HSSF.UserModel;
using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Web;
using System.Web.Mvc;

namespace DianGuanJiaApp.Controllers
{
    public class PurchasesController : BaseController
    {
        PurchaseService _purchaseService = new PurchaseService();
        ShopService _shopService = new ShopService();
        OrderItemService _orderItemService = new OrderItemService();
        PurchaseConfigService _purchaseConfigService = new PurchaseConfigService();
        CommonSettingService _commonSettingService = new CommonSettingService();
        NaHuoLabelService _naHuoLabelService = new NaHuoLabelService();
        ExportTaskService _exportTaskService = new ExportTaskService();

        // GET: Purchaes
        public ActionResult Index()
        {
            LoadDefaultConfig();
            return View();
        }

        public ActionResult IndexFromOrderPrint()
        {
            string pids = Request["pids"].ToString2();
            pids = WebHelper.HtmlDecode(pids); //&amp->&， &ampnbsp->&nbsp;
            pids = WebHelper.HtmlDecode(pids).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果
            var pidLst = new List<string>();
            if (pids.IsNullOrEmpty() || !(pidLst = pids.ToList<string>()).Any())
                throw new LogicException("请勾选订单后，在生成备货单");

            ViewBag.PlatformOrderIds = string.Join(",", pidLst);
            ViewBag.IsFromOrderPrint = true;
            LoadDefaultConfig();
            return View("Index");
        }

        /// <summary>
        /// 备货单打印html页面测试
        /// </summary>
        /// <returns></returns>
        //[ValidateInput(false)]
        public ActionResult Test()
        {
            //var shopId = Request.Params["shopId"].ToInt();
            //if (shopId > 0)
            //{
            //    TestHtmlToDatas(shopId);
            //    return Json("OK");
            //}
            //else
            //{
            //    var html = Request.Params["Body"].ToString2();
            //    if (!html.IsNullOrEmpty())
            //    {
            //        if (CustomerConfig.IsDebug)
            //        {
            //            var paperWidth = Request.Params["PageWidth"].ToDouble();
            //            var path = Server.MapPath("~/Views/Purchases/Test.cshtml");
            //            using (StreamWriter sw = new StreamWriter(path, false, Encoding.UTF8))
            //            {
            //                html = "@{ Layout = null;}\n" + html;
            //                html += "<style>body{margin:100px 20px 50px 20px;} table{width:" + paperWidth + "mm;}</style>";
            //                sw.WriteLine(html);
            //            }
            //        }
            //        return Json("OK");
            //    }
            //}
            //return Json("OK");
            return View();
        }

        public void LoadDefaultConfig()
        {
            var currShop = SiteContext.Current.CurrentLoginShop;
            var isUseOldTheme = false;// SiteContext.Current?.IsUseOldTheme ?? false;
            var platformType = isUseOldTheme ? "Old" : SiteContext.Current.CurrentLoginShop.PlatformType;

            var sysKeys = new List<string> { "/Purchase/CreatePurchase/ExpireSeconds" };
            var sysCommSets = _commonSettingService.GetSets(sysKeys, 0);
            var defaultExpireSecondsSet = sysCommSets?.FirstOrDefault(m => m.Key == "/Purchase/CreatePurchase/ExpireSeconds");

            var keys = new List<string> { "/Purchase/DefaultLoadShop", "/Purchase/CreatePurchase/UpdateTime", "/Purchase/TemplateSetting", "OrderCategorySet", "PurchasePrintFieldsSet", "PurchaseCustomConditionSet", "PurchaseCheckboxConditionSet" };
            var commSets = _commonSettingService.GetSets(keys, currShop.Id);

            var purchaseCreateTimeSet = commSets?.FirstOrDefault(m => m.Key == "/Purchase/CreatePurchase/UpdateTime");
            var purchaseTemplateSet = commSets?.FirstOrDefault(m => m.Key == "/Purchase/TemplateSetting");
            var purchaseCustomConditionSet = commSets?.FirstOrDefault(m => m.Key == "PurchaseCustomConditionSet");
            var purchasePrintFieldsSet = commSets?.FirstOrDefault(m => m.Key == "PurchasePrintFieldsSet");
            var orderCategorysSetting = commSets?.FirstOrDefault(m => m.Key == "OrderCategorySet");
            var purchaseCheckboxConditionSet = commSets?.FirstOrDefault(m => m.Key == "PurchaseCheckboxConditionSet");
            var defaultLoadShopSet = commSets.FirstOrDefault(m => m.Key == "/Purchase/DefaultLoadShop");

            var defaultLoadShopVal = defaultLoadShopSet?.Value.ToString2() ?? "";
            if (defaultLoadShopVal.IsNullOrEmpty())
            {
                defaultLoadShopVal = "CurrShop";
            }

            var defaultOrderCategorysSetVal = orderCategorysSetting?.Value.ToString2() ?? "";
            if (defaultOrderCategorysSetVal.IsNullOrEmpty())
                defaultOrderCategorysSetVal = GetDefaultSetting(platformType, "OrderCategorySet");

            var defaultPurchasePrintFieldsSetVal = purchasePrintFieldsSet?.Value.ToString2() ?? "";
            if (defaultPurchasePrintFieldsSetVal.IsNullOrEmpty())
                defaultPurchasePrintFieldsSetVal = GetDefaultSetting(platformType, "PurchasePrintFieldsSet");

            var defaultPurchaseCustomConditionSetVal = purchaseCustomConditionSet?.Value.ToString2() ?? "";
            if (defaultPurchaseCustomConditionSetVal.IsNullOrEmpty())
                defaultPurchaseCustomConditionSetVal = GetDefaultSetting(platformType, "PurchaseCustomConditionSet");

            var defaultPurchaseCheckboxConditionSetVal = purchaseCheckboxConditionSet?.Value.ToString2() ?? "";
            if (defaultPurchaseCheckboxConditionSetVal.IsNullOrEmpty())
                defaultPurchaseCheckboxConditionSetVal = GetDefaultSetting(platformType, "PurchaseCheckboxConditionSet");


            var lastUpdateTime = purchaseCreateTimeSet?.Value.ToDateTime() ?? null;
            var lastUpdateTimeSetVal = lastUpdateTime == null ? "" : lastUpdateTime.Value.ToString("yyyy-MM-ddTHH:mm:ss");
            // 默认导出间隔时长（300s）
            var defaultExpireSeconds = defaultExpireSecondsSet?.Value.ToInt() ?? 0;
            defaultExpireSeconds = defaultExpireSeconds <= 0 ? 300 : defaultExpireSeconds;

            ViewBag.LastUpdateTimeSet = lastUpdateTimeSetVal;
            ViewBag.DefaultExpireSeconds = defaultExpireSeconds;

            //前台获取导出任务
            var exportTask = _exportTaskService.GetExportTask(SiteContext.Current.CurrentShopId, ExportType.Purchase.ToInt());
            // 显示未完成任务或已完成一天前的导出任务
            exportTask = exportTask != null && ((exportTask.Status >= 0 && exportTask.Status < 4) || (exportTask.Status >= 4 && exportTask.UploadToServerTime != null && DateTime.Now < exportTask.UploadToServerTime.Value.AddDays(1))) ? exportTask : null;
            var task = GetExportTaskToWeb(exportTask);
            ViewBag.CreatePurchaseTask = task?.ToJson() ?? "null";


            ViewBag.Shops = SiteContext.Current.AllShops?.Where(m => m.PlatformType == currShop.PlatformType).ToList().ToJson() ?? "";
            ViewBag.PurchasePrintFieldsSet = defaultPurchasePrintFieldsSetVal;
            ViewBag.OrderCategorySet = defaultOrderCategorysSetVal;
            ViewBag.PurchaseCustomConditionSet = defaultPurchaseCustomConditionSetVal;
            ViewBag.PurchaseCheckboxConditionSet = defaultPurchaseCheckboxConditionSetVal;
            ViewBag.IsNewUser = currShop.CreateTime.Value > _commonSettingService.GetUseNewPurchaseTemplateStartTime().toDateTime() ? "true" : "false";
            ViewBag.PurchaseTemplateSet = purchaseTemplateSet?.Value ?? "";
        }

        public string GetDefaultSetting(string platformType, string key)
        {
            var ptKey = $"Default_{platformType}_{key}";
            key = $"Default_{key}";
            var keys = new List<string> { key, ptKey };
            var commSets = _commonSettingService.GetSets(keys, 0);
            var commonSet = commSets.FirstOrDefault(m => m.Key == ptKey);
            if (commonSet == null)
                commonSet = commSets.FirstOrDefault(m => m.Key == key);
            return commonSet?.Value.ToString2() ?? "";
        }

        public ActionResult SaveConfig()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var config = Request.Form["ConfigData"].ToString2();
            var model = _purchaseConfigService.GetOrderFilterByShopId(shopId);
            if (model != null)
            {
                model.Config = config;
                _purchaseConfigService.Update(model);
            }
            else
            {
                _purchaseConfigService.Add(
               new PurchaseConfig()
               {
                   ShopId = shopId,
                   CreateTime = DateTime.Now,
                   Config = ""
               });
            }

            return SuccessResult();
        }

        public ActionResult GetConfig()
        {
            var shopId = SiteContext.Current.CurrentShopId;
            var model = _purchaseConfigService.GetOrderFilterByShopId(shopId);
            return Json(model?.Config);
        }


        //private void TestHtmlToDatas(int shopId)
        //{
        //    var model = _purchaseService.GetHistoryList(shopId, 1, 100, new List<string> { "Id", "Name", "CreateTime" });
        //    var setting = _commonSettingService.Get("PurchasePrintFieldsSet", shopId)?.Value ?? "";
        //    var setModel = JsonExtension.ToObject<PurchaseSettingModel>(setting);
        //    model?.Rows?.ForEach(history =>
        //    {
        //        if (!history.Data.StartsWith("{"))
        //        {
        //            var html = history.Data;
        //            var phvm = ExportExcelHtmlToData(html);
        //            if (phvm.Setting.DisplayFields != null && phvm.Setting.DisplayFields.Any())
        //                setModel.DisplayFields = phvm.Setting.DisplayFields;
        //            phvm.Setting = setModel;

        //            var json = JsonExtension.ToJson(phvm);
        //            if (history.Data != json)
        //            {
        //                history.Data = json;
        //                //_purchaseService.Update(history);
        //                var newHistory = new PurchaseHistory()
        //                {
        //                    Data = history.Data,
        //                    CreateTime = history.CreateTime,
        //                    ShopId = history.ShopId,
        //                    Name = history.Name,
        //                    QueryData = history.QueryData,
        //                    Editable = history.Editable
        //                };
        //                _purchaseService.Add(newHistory);
        //            }
        //        }
        //    });
        //}

        [LogForOperatorFilter("生成拿货小标签")]
        //[App_Start.ActionPermissionControlFilter("打印拿货小标签")]
        public ActionResult PrintNaHuoLabel()
        {
            var printer = Request.Form["Printer"].ToString2();
            if (printer.IsNullOrEmpty())
                return FalidResult("请选择打印机后，再生成拿货标签");

            var tempId = Request.Form["TempId"].ToInt();
            if (tempId == 0)
                return FalidResult("请选择打印模板后，再生成拿货标签");

            var options = Request.Form["Orders"].ToString2();
            options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
            options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果

            var orders = JsonExtension.ToList<PurchaseOrderModel>(options);
            if (orders == null || !orders.Any())
                return FalidResult("无订单数据，不能生成拿货标签");

            var shopIds = orders.Select(x => x.ShopId).Distinct().ToList();
            if (shopIds != null && shopIds.Any() && SiteContext.Current.CurrentLoginShop.PlatformType == PlatformType.Alibaba.ToString())
            {
                //阿里多版本识别与拦截
                var result = CheckAlibabaShopVersionControl(shopIds, "Purchases", "PrintNaHuoLabel");
                if (result != null)
                    return Json(result);
            }

            // 获取拿货小标签打印模板
            NaHuoLabelService _naHuoService = new NaHuoLabelService();
            var shopId = SiteContext.Current.CurrentShopId;
            var naHuoTemplate = _naHuoService.GetNaHuoTemplate(tempId, shopId);

            if (naHuoTemplate == null)
                return FalidResult("请确认当前模板是否存在");

            var log = LogForOperatorContext.Current.logInfo;
            var logDetail = new LogForCreatePrintLabel() { TempId = tempId };
            // 默认分拣码规则   Condition: 0是等于,1是大于等于,2是小于等于
            var rules = new List<SetSortingCodeRuleJsonRequestModel> {
                new SetSortingCodeRuleJsonRequestModel{ Code = "A",Numbers = 1,Condition = 0,OrderIndex = 1},
                new SetSortingCodeRuleJsonRequestModel{ Code = "B",Numbers = 2,Condition = 0,OrderIndex = 1},
                new SetSortingCodeRuleJsonRequestModel{ Code = "C",Numbers = 3,Condition = 0,OrderIndex = 1},
                new SetSortingCodeRuleJsonRequestModel{ Code = "D",Numbers = 4,Condition = 0,OrderIndex = 1},
                new SetSortingCodeRuleJsonRequestModel{ Code = "E",Numbers = 5,Condition = 1,OrderIndex = 1},
            };

            var commSets = _commonSettingService.GetSets(new List<string> { "SetSortingCodeRuleData", "SetNaHuoLabelData" }, shopId);
            var codeRuleSet = commSets.FirstOrDefault(m => m.Key == "SetSortingCodeRuleData");
            var codeRuleModel = new SetSortingCodeRuleRequestModel();
            //  分拣码规则对应订单序号orderIndex
            if (codeRuleSet != null && !codeRuleSet.Value.IsNullOrEmpty())
            {
                codeRuleModel = codeRuleSet.Value.ToObject<SetSortingCodeRuleRequestModel>();
                if (codeRuleModel != null && codeRuleModel.Lists != null && codeRuleModel.Lists.Any())
                    rules = codeRuleModel.Lists;
            }

            // 排序和店铺别名配置
            NaHuoLabelTemplateConfigModel naHuoConfigSet = null;
            var sortShopSignSet = commSets.FirstOrDefault(m => m.Key == "SetNaHuoLabelData");
            var shopSigns = SiteContext.Current.AllShops.Select(m => new NaHuoLabelShopSign { ShopId = m.Id, ShopSign = "", NickName = m.NickName }).ToList();
            if (sortShopSignSet != null && !sortShopSignSet.Value.IsNullOrEmpty())
            {
                naHuoConfigSet = sortShopSignSet.Value.ToObject<NaHuoLabelTemplateConfigModel>();
                naHuoConfigSet = naHuoConfigSet != null ? naHuoConfigSet : new NaHuoLabelTemplateConfigModel()
                {
                    LabelSort = 0,
                    IsPrintStall = false,
                    ShopSignList = shopSigns
                };
                shopSigns = naHuoConfigSet?.ShopSignList ?? SiteContext.Current.AllShops.Select(m => new NaHuoLabelShopSign { ShopId = m.Id, ShopSign = "", NickName = m.NickName }).ToList();
            }
            else
            {
                naHuoConfigSet = new NaHuoLabelTemplateConfigModel()
                {
                    LabelSort = 0,
                    IsPrintStall = false,
                    ShopSignList = shopSigns
                };
            }

            // 生成唯一批次号
            var batchNo = DateTime.Now.ToString("yyMMddHHmmssfff");
            Random random = new Random(batchNo.ToInt());
            batchNo = $"{batchNo}{random.Next(100, 999)}"; // 18位 15位时间+3位随机数

            var createTime = DateTime.Now;
            var batchModel = new NaHuoLabelBatch
            {
                CreateTime = createTime,
                BatchNo = batchNo,
                OrderCount = orders.Count,
                TempId = tempId,
                SortingRules = rules.ToJson(),
                Printer = printer,
                ShopId = shopId
            };

            // 订单中ProductItemCount=0，重新计算临时处理
            var noCountOrders = orders.Where(o => o.ProductItemCount == 0).ToList();
            if (noCountOrders.Any())
            {
                var oKeys = noCountOrders.Select(x => new OrderSelectKeyModel { PlatformOrderId = x.PlatformOrderId, ShopId = x.ShopId }).ToList();
                var dbOrders = MyOrderService.GetOrders(oKeys, false, new List<string> { "o.id", "o.PlatformOrderId", "o.shopId", "o.ProductItemCount", "oi.Id", "oi.Count" });
                dbOrders?.ForEach(o =>
                {
                    var pOrder = orders.FirstOrDefault(x => x.PlatformOrderId == o.PlatformOrderId && x.ShopId == o.ShopId);
                    if (pOrder != null)
                    {
                        pOrder.ProductItemCount = o.OrderItems.Sum(x => x.Count ?? 0);
                    }
                });
            }
            // 根据备货单订单查询是否有已屯货商品
            var keys = orders.SelectMany(m => m.Items.Select(oi => new OrderItemSelectKeyModel { PlatformOrderId = oi.PlatformOrderId, ShopId = oi.ShopId, SubItemID = oi.SubItemId })).Distinct().ToList();
            var tunNaHuoLabels = _naHuoService.GetTunNaHuoLabelByKeys(keys);
            // 订单在不同批次中生成的OrderIndex可能不一致
            var orderIndexKeyValLst = tunNaHuoLabels.GroupBy(m => m.PlatformOrderId).Select(m => new { Key = m.Key, Value = m.Select(n => n.OrderIndex).Distinct().ToList() }).ToList();
            // 根据订单商品数量进行分组
            var productItemCountGroup = orders.GroupBy(m => m.ProductItemCount).Select(m => new { ProductItemCount = m.Key, Orders = m.ToList() }).ToList();
            // 根据分拣码规则生成规格生成拿货小标签
            rules.ForEach(rule =>
            {
                // 根据规则获取当前规则的订单数量Key，以及含有Key数量的订单集合
                var groups = productItemCountGroup.Where(m =>
                {
                    if (rule.Condition == 0)
                        return m.ProductItemCount == rule.Numbers;
                    else if (rule.Condition == 1)
                        return m.ProductItemCount >= rule.Numbers;
                    else
                        return m.ProductItemCount <= rule.Numbers;
                }).ToList();
                if (groups == null || !groups.Any()) return;

                // 生成拣货码，订单数量 序号                                        
                int orderIndex = 1;// rule.OrderIndex; // 每天清零     
                groups.ForEach(group =>
                {
                    group.Orders.ForEach(o =>
                    {
                        var order = new NaHuoLabelOrder
                        {
                            PlatformOrderId = o.PlatformOrderId,
                            ShopId = o.ShopId,
                            BatchNo = batchNo,
                            BuyerHashCode = o.BuyerHashCode,
                            ShopNameLabel = shopSigns.FirstOrDefault(m => m.ShopId == o.ShopId)?.ShopSign ?? "",
                            CreateTime = createTime,
                            ProductCountCode = rule.Code,
                            OrderIndex = orderIndex,
                            ProductCount = o.ProductItemCount,
                        };
                        // 已屯货的商品需要从数据库中查询（订单和商品保留原有序号）: 分拣码/订单序号/商品序号(总商品数量的序号) C/4/5          
                        // 存在已经屯货商品，保留分拣码和订单序号                
                        var tunNaHuoOrderItems = tunNaHuoLabels.Where(m => m.PlatformOrderId == o.PlatformOrderId).OrderByDescending(m => m.CreateTime).Distinct().ToList(); // 订单已屯货商品信息
                        var productCountCode = rule.Code;
                        if (tunNaHuoOrderItems.Any())
                        {
                            var tunFirst = tunNaHuoOrderItems.OrderByDescending(m => m.CreateTime).FirstOrDefault();
                            productCountCode = tunFirst.ProductCountCode;
                            order.OrderIndex = tunFirst.OrderIndex;
                            // 跳过当前订单已屯货的订单序，保证未屯货的orderIndex与屯货orderIndex不冲突（第一批次中10个订单，排在第8；第二批次中3订单，排在第2，新的批次中已屯货的情况下OrderIndex仍要从8开始）
                            var keyVal = orderIndexKeyValLst.FirstOrDefault(m => m.Key == o.PlatformOrderId && m.Value.Contains(orderIndex));
                            while (keyVal != null)
                            {
                                orderIndex++;
                                keyVal = orderIndexKeyValLst.FirstOrDefault(m => m.Key == o.PlatformOrderId && m.Value.Contains(orderIndex));
                            }
                        }
                        else
                            orderIndex++;

                        var orderPIndexLst = tunNaHuoOrderItems.Select(m => m.ProductIndex).Distinct().ToList();  // 该订单屯货占用的ProductIndex集合        
                        var tunPIndexLst = new List<int>();   // 当前屯货+新生成商品序号集合
                        var totalProductCount = o.Items?.Sum(m => m.Count) ?? 0; // 订单商品数量         
                        var md5 = (o.PlatformOrderId + o.ShopId).ToShortMd5().ToZiMuIntIndex(); // 商品唯一条形码：订单唯一编号MD5 + 商品序号
                        o.Items?.OrderBy(oi => oi.Id).ToList().ForEach(oi =>
                        {
                            var orderItem = order.OrderItems.FirstOrDefault(m => m.SubItemID == oi.SubItemId && m.ShopId == oi.ShopId && m.PlatformOrderId == oi.PlatformOrderId);
                            if (orderItem != null) return;

                            var naHuoOrderItem = new NaHuoLabelOrderItem
                            {
                                PlatformOrderId = oi.PlatformOrderId,
                                SubItemID = oi.SubItemId,
                                ShopId = oi.ShopId,
                                BatchNo = batchNo,
                                ProductCount = oi.Count
                            };
                            order.OrderItems.Add(naHuoOrderItem);

                            // 屯货商品的序号已存在，条码和商品序号保持原有数据
                            var productIndexItems = tunNaHuoOrderItems.Where(sr => o.ShopId == sr.ShopId && sr.PlatformOrderId == o.PlatformOrderId && sr.SubItemID == oi.SubItemId).OrderByDescending(m => m.CreateTime).ToList();
                            var sRules = new NaHuoLabelSortingRules();
                            var tunProductIndexList = productIndexItems.Select(m => m.ProductIndex).Distinct().ToList();

                            // 先添加已屯货的商品小标签
                            if (tunProductIndexList.Any())
                            {
                                tunProductIndexList.ForEach(pIndex =>
                                {
                                    // 预防屯货数量大于该商品数量
                                    if (naHuoOrderItem.SortingRules.Count == oi.Count || tunPIndexLst.Contains(pIndex))
                                        return;

                                    var tunProductItem = productIndexItems.OrderByDescending(m => m.CreateTime).FirstOrDefault(m => m.ProductIndex == pIndex);
                                    sRules = new NaHuoLabelSortingRules
                                    {
                                        NaHuoPrintStatus = true,
                                        PlatformOrderId = oi.PlatformOrderId,
                                        SubItemID = oi.SubItemId,
                                        ShopId = oi.ShopId,
                                        BatchNo = batchNo,
                                        ProductCountCode = productCountCode,
                                        OrderIndex = order.OrderIndex,
                                        Barcode = tunProductItem.Barcode,
                                        ProductIndex = tunProductItem.ProductIndex,//已屯货商品序号
                                        ScannedTime = tunProductItem.ScannedTime,
                                        PrintedTime = tunProductItem.PrintedTime,
                                        ScanStatus = true,
                                        CreateTime = createTime
                                    };
                                    naHuoOrderItem.SortingRules.Add(sRules);
                                    tunPIndexLst.Add(pIndex);
                                });
                            }

                            // 生成剩下没有生成过的小标签     
                            for (var i = 1; i <= totalProductCount; i++)
                            {
                                // 全部已屯货，则不需要生成新的小标签
                                if (naHuoOrderItem.SortingRules.Count == oi.Count)
                                    break;
                                if (orderPIndexLst.Contains(i))
                                    continue;

                                sRules = new NaHuoLabelSortingRules
                                {
                                    NaHuoPrintStatus = false,
                                    PlatformOrderId = oi.PlatformOrderId,
                                    SubItemID = oi.SubItemId,
                                    ShopId = oi.ShopId,
                                    BatchNo = batchNo,
                                    ProductCountCode = productCountCode,
                                    OrderIndex = order.OrderIndex,
                                    Barcode = $"{md5}{i}",
                                    ProductIndex = i,
                                    ScanStatus = false,
                                    CreateTime = createTime
                                };
                                naHuoOrderItem.SortingRules.Add(sRules);
                                tunPIndexLst.Add(i);
                                if (!orderPIndexLst.Contains(i))
                                    orderPIndexLst.Add(i);
                            }
                        });
                        //orderIndex++;
                        //rule.OrderIndex = orderIndex;  
                        batchModel.Orders.Add(order);
                    });
                });
            });

            logDetail.BatchNo = batchNo;
            logDetail.Rules = rules;
            logDetail.ShopSigns = shopSigns;

            log.Detail = logDetail;

            // 排序            
            var tempRules = new List<NaHuoLabelSortingRules>();
            var naHuoLabels = batchModel.Orders.SelectMany(m => m.OrderItems.SelectMany(n => n.SortingRules)).ToList();
            tempRules = GetNaHuoLabelOrderBy(orders, naHuoLabels, tunNaHuoLabels, naHuoConfigSet.LabelSort);
            if (tempRules == null || !tempRules.Any())
            {
                log.Exception = "当前分拣码规则下，没有满足条件的订单";
                return FalidResult("当前分拣码规则下，没有满足条件的订单");
            }

            var barcodeGroup = tempRules.GroupBy(m => m.Barcode).Select(m => new { Barcode = m.Key, Count = m.Count() }).Where(m => m.Count > 1).ToList();
            if (barcodeGroup.Any())
            {
                log.Exception = "同批次拿货小标签生成相同的条码：" + tempRules.Where(m => barcodeGroup.Select(n => n.Barcode).ToList().Contains(m.Barcode));
                return FalidResult("生成拿货小标签异常，请联系我们");
            }

            // 将生成的拿货标签插入数据库    
            _naHuoService.BulkInsertNaHuoLabelBatch(batchModel, tempRules);

            // 保存小标签规则配置 
            codeRuleModel.Lists = rules;
            codeRuleModel.UpdateTime = DateTime.Now;
            _commonSettingService.Set("SetSortingCodeRuleData", codeRuleModel.ToJson(), shopId);

            var modelNaHuoConfig = new PurchasesNaHuoLabelConfig();
            modelNaHuoConfig.Printer = printer;
            modelNaHuoConfig.TempId = tempId;

            _commonSettingService.Set("PurchasesNaHuoLabelConfig", modelNaHuoConfig.ToJson(), SiteContext.Current.CurrentShopId);

            //return SuccessResult(new { batchNo = batchNo, ShopId = orders.FirstOrDefault().ShopId });
            //var shopIds = orders.Select(m => m.ShopId).Distinct().ToList();
            return SuccessResult(new { batchNo = batchNo, ShopIds = shopIds });
        }


        [LogForOperatorFilter("生成拿货小标签")]
        public ActionResult PrintNaHuoLabel_New()
        {
            var printer = Request.Form["Printer"].ToString2();
            if (printer.IsNullOrEmpty())
                return FalidResult("请选择打印机后，再生成拿货标签");

            var tempId = Request.Form["TempId"].ToInt();
            if (tempId == 0)
                return FalidResult("请选择打印模板后，再生成拿货标签");

            var options = Request.Form["Orders"].ToString2();
            options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
            options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果

            var orders = JsonExtension.ToList<PurchaseOrderModel>(options);
            if (orders == null || !orders.Any())
                return FalidResult("无订单数据，不能生成拿货标签");

            // 获取拿货小标签打印模板
            NaHuoLabelService _naHuoService = new NaHuoLabelService();
            var shopId = SiteContext.Current.CurrentShopId;
            var naHuoTemplate = _naHuoService.GetNaHuoTemplate(tempId, shopId);

            if (naHuoTemplate == null)
                return FalidResult("请确认当前模板是否存在");

            var log = LogForOperatorContext.Current.logInfo;
            var logDetail = new LogForCreatePrintLabel() { TempId = tempId };
            // 默认分拣码规则   Condition: 0是等于,1是大于等于,2是小于等于
            var rules = new List<SetSortingCodeRuleJsonRequestModel> {
                new SetSortingCodeRuleJsonRequestModel{ Code = "A",Numbers = 1,Condition = 0,OrderIndex = 1},
                new SetSortingCodeRuleJsonRequestModel{ Code = "B",Numbers = 2,Condition = 0,OrderIndex = 1},
                new SetSortingCodeRuleJsonRequestModel{ Code = "C",Numbers = 3,Condition = 0,OrderIndex = 1},
                new SetSortingCodeRuleJsonRequestModel{ Code = "D",Numbers = 4,Condition = 0,OrderIndex = 1},
                new SetSortingCodeRuleJsonRequestModel{ Code = "E",Numbers = 5,Condition = 1,OrderIndex = 1},
            };

            var commSets = _commonSettingService.GetSets(new List<string> { "SetSortingCodeRuleData", "SetNaHuoLabelData" }, shopId);
            var codeRuleSet = commSets.FirstOrDefault(m => m.Key == "SetSortingCodeRuleData");
            var codeRuleModel = new SetSortingCodeRuleRequestModel();
            //  分拣码规则对应订单序号orderIndex
            if (codeRuleSet != null && !codeRuleSet.Value.IsNullOrEmpty())
            {
                codeRuleModel = codeRuleSet.Value.ToObject<SetSortingCodeRuleRequestModel>();
                if (codeRuleModel != null && codeRuleModel.Lists != null && codeRuleModel.Lists.Any())
                    rules = codeRuleModel.Lists;
            }

            // 排序和店铺别名配置
            NaHuoLabelTemplateConfigModel naHuoConfigSet = null;
            var sortShopSignSet = commSets.FirstOrDefault(m => m.Key == "SetNaHuoLabelData");
            var shopSigns = SiteContext.Current.AllShops.Select(m => new NaHuoLabelShopSign { ShopId = m.Id, ShopSign = "", NickName = m.NickName }).ToList();
            if (sortShopSignSet != null && !sortShopSignSet.Value.IsNullOrEmpty())
            {
                naHuoConfigSet = sortShopSignSet.Value.ToObject<NaHuoLabelTemplateConfigModel>();
                naHuoConfigSet = naHuoConfigSet != null ? naHuoConfigSet : new NaHuoLabelTemplateConfigModel()
                {
                    LabelSort = 0,
                    IsPrintStall = false,
                    ShopSignList = shopSigns
                };
                shopSigns = naHuoConfigSet?.ShopSignList ?? SiteContext.Current.AllShops.Select(m => new NaHuoLabelShopSign { ShopId = m.Id, ShopSign = "", NickName = m.NickName }).ToList();
            }
            else
            {
                naHuoConfigSet = new NaHuoLabelTemplateConfigModel()
                {
                    LabelSort = 0,
                    IsPrintStall = false,
                    ShopSignList = shopSigns
                };
            }

            // 生成唯一批次号
            var batchNo = DateTime.Now.ToString("yyMMddHHmmssfff");
            Random random = new Random(batchNo.ToInt());
            batchNo = $"{batchNo}{random.Next(100, 999)}"; // 18位 15位时间+3位随机数

            var createTime = DateTime.Now;
            var batchModel = new NaHuoLabelBatch
            {
                CreateTime = createTime,
                BatchNo = batchNo,
                OrderCount = orders.Count,
                TempId = tempId,
                SortingRules = rules.ToJson(),
                Printer = printer,
                ShopId = shopId
            };

            // 订单中ProductItemCount=0，重新计算临时处理
            var noCountOrders = orders.Where(o => o.ProductItemCount == 0).ToList();
            if (noCountOrders.Any())
            {
                var oKeys = noCountOrders.Select(x => new OrderSelectKeyModel { PlatformOrderId = x.PlatformOrderId, ShopId = x.ShopId }).ToList();
                var dbOrders = MyOrderService.GetOrders(oKeys, false, new List<string> { "o.id", "o.PlatformOrderId", "o.shopId", "o.ProductItemCount", "oi.Id", "oi.Count" });
                dbOrders?.ForEach(o =>
                {
                    var pOrder = orders.FirstOrDefault(x => x.PlatformOrderId == o.PlatformOrderId && x.ShopId == o.ShopId);
                    if (pOrder != null)
                    {
                        pOrder.ProductItemCount = o.OrderItems.Sum(x => x.Count ?? 0);
                    }
                });
            }
            // 根据备货单订单查询是否有已屯货商品
            var keys = orders.SelectMany(m => m.Items.Select(oi => new OrderItemSelectKeyModel { PlatformOrderId = oi.PlatformOrderId, ShopId = oi.ShopId, SubItemID = oi.SubItemId })).Distinct().ToList();
            var tunNaHuoLabels = _naHuoService.GetTunNaHuoLabelByKeys(keys);
            // 订单在不同批次中生成的OrderIndex可能不一致
            var orderIndexKeyValLst = tunNaHuoLabels.GroupBy(m => m.PlatformOrderId).Select(m => new { Key = m.Key, Value = m.Select(n => n.OrderIndex).Distinct().ToList() }).ToList();
            // 根据订单商品数量进行分组
            var productItemCountGroup = orders.GroupBy(m => m.ProductItemCount).Select(m => new { ProductItemCount = m.Key, Orders = m.ToList() }).ToList();
            // 根据分拣码规则生成规格生成拿货小标签
            rules.ForEach(rule =>
            {
                // 根据规则获取当前规则的订单数量Key，以及含有Key数量的订单集合
                var groups = productItemCountGroup.Where(m =>
                {
                    if (rule.Condition == 0)
                        return m.ProductItemCount == rule.Numbers;
                    else if (rule.Condition == 1)
                        return m.ProductItemCount >= rule.Numbers;
                    else
                        return m.ProductItemCount <= rule.Numbers;
                }).ToList();
                if (groups == null || !groups.Any()) return;

                // 生成拣货码-订单数量-序号                                 
                groups.ForEach(group =>
                {
                    var orderIndexLst = new List<int>();

                    #region 1.处理已屯货的订单
                    var tunOrders = group.Orders.Where(x => tunNaHuoLabels.Any(m => m.PlatformOrderId == x.PlatformOrderId)).ToList();
                    tunOrders.ForEach(o =>
                    {
                        // 已屯货的商品需要从数据库中查询（订单和商品保留原有序号）: 分拣码/订单序号/商品序号(总商品数量的序号) C/4/5    
                        var tunNaHuoProducts = tunNaHuoLabels.Where(m => m.PlatformOrderId == o.PlatformOrderId).OrderByDescending(m => m.CreateTime).ToList(); // 订单已屯货商品信息
                        var tunOrderIndex = tunNaHuoProducts.FirstOrDefault().OrderIndex;
                        var order = new NaHuoLabelOrder
                        {
                            PlatformOrderId = o.PlatformOrderId,
                            ShopId = o.ShopId,
                            BatchNo = batchNo,
                            BuyerHashCode = o.BuyerHashCode,
                            ShopNameLabel = shopSigns.FirstOrDefault(m => m.ShopId == o.ShopId)?.ShopSign ?? "",
                            CreateTime = createTime,
                            ProductCountCode = rule.Code,
                            OrderIndex = tunOrderIndex,// 取最近一次生成小标签订单序号作为本次小标签订单序号
                            ProductCount = o.ProductItemCount,
                        };
                        orderIndexLst.Add(tunOrderIndex);

                        // 已经屯货商品，保留分拣码、订单序号、唯一条形码
                        var productIndexLst = new List<int>();
                        o.Items.ForEach(oi =>
                        {
                            //添加所有的OrderItems
                            var tunProducts = tunNaHuoProducts.Where(x => x.PlatformOrderId == oi.PlatformOrderId && x.SubItemID == oi.SubItemId).OrderByDescending(x => x.CreateTime).ToList();
                            var naHuoOrderItem = new NaHuoLabelOrderItem
                            {
                                PlatformOrderId = oi.PlatformOrderId,
                                SubItemID = oi.SubItemId,
                                ShopId = oi.ShopId,
                                BatchNo = batchNo,
                                ProductCount = oi.Count
                            };
                            order.OrderItems.Add(naHuoOrderItem);

                            //添加已屯货的商品分拣码、订单序号、唯一条形码信息
                            tunProducts.GroupBy(g => g.ProductIndex).Take(oi.Count).ToList().ForEach(g =>
                            {
                                var tunProduct = g.FirstOrDefault();
                                var sRules = new NaHuoLabelSortingRules
                                {
                                    NaHuoPrintStatus = true,
                                    PlatformOrderId = oi.PlatformOrderId,
                                    SubItemID = oi.SubItemId,
                                    ShopId = oi.ShopId,
                                    BatchNo = batchNo,
                                    OrderIndex = order.OrderIndex,
                                    ScanStatus = true,
                                    CreateTime = createTime,
                                    ProductCountCode = rule.Code,
                                    Barcode = tunProduct.Barcode,
                                    ProductIndex = tunProduct.ProductIndex,
                                    PrintedTime = tunProduct.PrintedTime,
                                    ScannedTime = tunProduct.ScannedTime,
                                };
                                naHuoOrderItem.SortingRules.Add(sRules);
                                productIndexLst.Add(tunProduct.ProductIndex);
                            });
                        });

                        // 未屯货的新进商品，重新生成唯一条码，商品唯一条形码：订单唯一编号MD5 + 商品序号
                        var md5 = (o.PlatformOrderId + o.ShopId).ToShortMd5().ToZiMuIntIndex();
                        o.Items.ForEach(oi =>
                        {
                            //新进商品数量
                            var naHuoOrderItem = order.OrderItems?.FirstOrDefault(x => x.PlatformOrderId == oi.PlatformOrderId && x.SubItemID == oi.SubItemId);
                            if (naHuoOrderItem == null) return;

                            var newItemCount = oi.Count - naHuoOrderItem.SortingRules.Count();
                            for (int i = 0; i < newItemCount; i++)
                            {
                                // 未使用的商品序号赋值给新进商品
                                for (int j = 1; j <= o.ProductItemCount; j++)
                                {
                                    if (!productIndexLst.Contains(j))
                                    {
                                        var sRules = new NaHuoLabelSortingRules
                                        {
                                            NaHuoPrintStatus = false,
                                            PlatformOrderId = oi.PlatformOrderId,
                                            SubItemID = oi.SubItemId,
                                            ShopId = oi.ShopId,
                                            BatchNo = batchNo,
                                            ProductCountCode = rule.Code,
                                            OrderIndex = order.OrderIndex,
                                            Barcode = $"{md5}{j}",
                                            ProductIndex = j,
                                            ScanStatus = false,
                                            CreateTime = createTime
                                        };
                                        productIndexLst.Add(i);
                                        naHuoOrderItem.SortingRules.Add(sRules);
                                        break;
                                    }
                                }
                            }
                        });

                        batchModel.Orders.Add(order);
                    });
                    #endregion

                    #region 2.处理未屯货的订单
                    var orderIndex = 1;
                    var newOrders = group.Orders.Where(x => !tunNaHuoLabels.Any(m => m.PlatformOrderId == x.PlatformOrderId)).ToList();
                    newOrders.ForEach(o =>
                    {
                        var order = new NaHuoLabelOrder
                        {
                            PlatformOrderId = o.PlatformOrderId,
                            ShopId = o.ShopId,
                            BatchNo = batchNo,
                            BuyerHashCode = o.BuyerHashCode,
                            ShopNameLabel = shopSigns.FirstOrDefault(m => m.ShopId == o.ShopId)?.ShopSign ?? "",
                            CreateTime = createTime,
                            ProductCountCode = rule.Code,
                            OrderIndex = orderIndex,
                            ProductCount = o.ProductItemCount,
                        };

                        var productIndex = 1;
                        o.Items.ForEach(oi =>
                        {
                            var naHuoOrderItem = new NaHuoLabelOrderItem
                            {
                                PlatformOrderId = oi.PlatformOrderId,
                                SubItemID = oi.SubItemId,
                                ShopId = oi.ShopId,
                                BatchNo = batchNo,
                                ProductCount = oi.Count
                            };
                            order.OrderItems.Add(naHuoOrderItem);

                            var md5 = (o.PlatformOrderId + o.ShopId).ToShortMd5().ToZiMuIntIndex(); // 商品唯一条形码：订单唯一编号MD5 + 商品序号
                            for (int i = 1; i <= oi.Count; i++)
                            {
                                var sRules = new NaHuoLabelSortingRules
                                {
                                    NaHuoPrintStatus = false,
                                    PlatformOrderId = oi.PlatformOrderId,
                                    SubItemID = oi.SubItemId,
                                    ShopId = oi.ShopId,
                                    BatchNo = batchNo,
                                    ProductCountCode = rule.Code,
                                    OrderIndex = order.OrderIndex,
                                    Barcode = $"{md5}{productIndex}",
                                    ProductIndex = productIndex,
                                    ScanStatus = false,
                                    CreateTime = createTime
                                };
                                naHuoOrderItem.SortingRules.Add(sRules);
                                productIndex++;
                            }
                        });
                        orderIndex++;

                        batchModel.Orders.Add(order);
                    });
                    #endregion

                });
            });

            logDetail.BatchNo = batchNo;
            logDetail.Rules = rules;
            logDetail.ShopSigns = shopSigns;

            log.Detail = logDetail;

            // 排序            
            var tempRules = new List<NaHuoLabelSortingRules>();
            var naHuoLabels = batchModel.Orders.SelectMany(m => m.OrderItems.SelectMany(n => n.SortingRules)).ToList();
            tempRules = GetNaHuoLabelOrderBy(orders, naHuoLabels, tunNaHuoLabels, naHuoConfigSet.LabelSort);
            if (tempRules == null || !tempRules.Any())
            {
                log.Exception = "当前分拣码规则下，没有满足条件的订单";
                return FalidResult("当前分拣码规则下，没有满足条件的订单");
            }

            var barcodeGroup = tempRules.GroupBy(m => m.Barcode).Select(m => new { Barcode = m.Key, Count = m.Count() }).Where(m => m.Count > 1).ToList();
            if (barcodeGroup.Any())
            {
                log.Exception = "同批次拿货小标签生成相同的条码：" + tempRules.Where(m => barcodeGroup.Select(n => n.Barcode).ToList().Contains(m.Barcode));
                return FalidResult("生成拿货小标签异常，请联系我们");
            }

            // 将生成的拿货标签插入数据库    
            _naHuoService.BulkInsertNaHuoLabelBatch(batchModel, tempRules);

            // 保存小标签规则配置 
            codeRuleModel.Lists = rules;
            codeRuleModel.UpdateTime = DateTime.Now;
            _commonSettingService.Set("SetSortingCodeRuleData", codeRuleModel.ToJson(), shopId);

            var modelNaHuoConfig = new PurchasesNaHuoLabelConfig();
            modelNaHuoConfig.Printer = printer;
            modelNaHuoConfig.TempId = tempId;

            _commonSettingService.Set("PurchasesNaHuoLabelConfig", modelNaHuoConfig.ToJson(), SiteContext.Current.CurrentShopId);

            //return SuccessResult(new { batchNo = batchNo, ShopId = orders.FirstOrDefault().ShopId });
            var shopIds = orders.Select(m => m.ShopId).Distinct().ToList();
            return SuccessResult(new { batchNo = batchNo, ShopIds = shopIds });
        }

        /// <summary>
        /// 拿货小标签根据配置进行排序
        /// </summary>
        /// <param name="orders"></param>
        /// <param name="naHuoLabels"></param>
        /// <param name="labelSort"></param>
        /// <returns></returns>
        private List<NaHuoLabelSortingRules> GetNaHuoLabelOrderBy(List<PurchaseOrderModel> orders, List<NaHuoLabelSortingRules> naHuoLabels, List<NaHuoLabelSortingRules> tunNaHuoLabels, int labelSort)
        {
            if (!orders.Any() || !naHuoLabels.Any()) return null;
            // 组装排序数据源对象
            var items = new List<PurchaseFullItemModel>();
            orders.ForEach(o =>
            {
                o.Items.ForEach(oi =>
                {
                    items.Add(new PurchaseFullItemModel
                    {
                        OrderItemId = oi.Id,
                        SubItemId = oi.SubItemId,
                        PlatformOrderId = o.PlatformOrderId,
                        Stall = oi.Stall,
                        Bazaar = oi.Bazaar,
                        ProductItemCount = o.ProductCount,
                        ProductCargoNumber = oi.ProductCargoNumber,
                        CargoNumber = oi.CargoNumber,
                    });
                });
            });

            //var items1 = orders.SelectMany(m => m.Items).Select(m => m.Bazaar.ToString2() + "->" + m.Stall.ToString2() + "->" + m.SubItemId).ToList();
            //Log.WriteLine(string.Join(",", items1));

            // 开始排序             
            items = labelSort == 0 ? items.OrderBy(m => m.Bazaar.ToString2()).ThenBy(m => m.Stall.ToString2()).ThenBy(m => m.OrderItemId).ToList() : //按市场档口排序
                labelSort == 1 ? items = items.OrderBy(m => m.Bazaar.ToString2()).ThenBy(m => m.Stall.ToString2()).ThenBy(m => m.ProductCargoNumber.ToString2()).ThenBy(m => m.OrderItemId).ToList() : //按市场档口排序，同一商家编码排在一起
                labelSort == 2 ? items = items.OrderBy(m => m.Bazaar.ToString2()).ThenBy(m => m.Stall.ToString2()).ThenBy(m => m.ProductItemCount).ThenBy(m => m.OrderItemId).ToList() : //按市场档口排序，同一档口先排单件再排多件
                labelSort == 3 ? items = items.OrderBy(m => m.Bazaar.ToString2()).ThenBy(m => m.Stall.ToString2()).ThenByDescending(m => m.ProductItemCount).ThenBy(m => m.OrderItemId).ToList()  //按市场档口排序，同一档口先排多件再排单件
                : items;
            if (labelSort == 4) //按市场档口排序，同一档口先排屯货再排其他商品
            {
                var tempOrders = new List<PurchaseOrderModel>();
                var pids = tunNaHuoLabels.Select(m => m.PlatformOrderId).ToList();
                // 临时自定义屯货订单Count=1,否则为0
                pids.ForEach(pid =>
                {
                    var item = items.FirstOrDefault(m => m.PlatformOrderId == pid);
                    if (item != null)
                        item.Count = 1;
                });
                items = items.OrderBy(m => m.Bazaar.ToString2()).ThenBy(m => m.Stall.ToString2()).ThenByDescending(m => m.Count).ThenBy(m => m.OrderItemId).ToList();
            }

            //var items2 = items.Select(m => m.Bazaar.ToString2() + "->" + m.Stall.ToString2() + "->" + m.SubItemId).ToList();
            //Log.WriteLine(string.Join(",", items2));

            // 根据已排序的数据源对象（items）对拿货小标签进行排序
            var tempRules = new List<NaHuoLabelSortingRules>();
            items.ForEach(item =>
            {
                var tmpList = naHuoLabels.Where(m => m.PlatformOrderId == item.PlatformOrderId && m.SubItemID == item.SubItemId).OrderBy(m => m.ProductCountCode + m.OrderIndex + m.ProductIndex).ToList();
                if (tmpList.Any())
                {
                    tempRules.AddRange(tmpList);
                }
            });
            //var items3 = tempRules.Select(m => m.SubItemID.ToString2() + "->" + m.Barcode).ToList();
            //Log.WriteLine(string.Join(",", items3));
            return tempRules;
        }

        [LogForOperatorFilter("分多次获取拿货小标签并打印")]
        public ActionResult PrintNaHuoLabelPiCi(string piCi, int tempId, int pageIndex, string ShopIdList, string bazaarStall = "")
        {
            var shopIds = SiteContext.Current.CurrentShopId;
            var shopIdList = ShopIdList.ToList<int>();
            //目前没有指定批次，所以取好一次后，前端循环就用该次的
            if (string.IsNullOrEmpty(piCi))
            {
                var naHuoLabelBatch = _naHuoLabelService.GetNewBatch(shopIds);
                if (naHuoLabelBatch != null)
                    piCi = naHuoLabelBatch.BatchNo;//默认是最后一次的批次号
            }

            if (string.IsNullOrEmpty(piCi))
                return FalidResult("获取不到批次号，请重试！");


            var naHuoTemplate = _naHuoLabelService.GetNaHuoTemplate(tempId, shopIds);
            if (naHuoTemplate == null)
                return FalidResult("请确认当前模板是否存在(来自打印中)!");

            var json = JsonConvert.DeserializeObject<JToken>(naHuoTemplate.Config);
            bool isDanPai = json?.Value<bool>("IsDanPai") ?? false;  //true是单排，false是双排

            int pageSize = 10;
            if (!isDanPai)
                pageSize = 20;//双排时，2个一起作为一行,所以可以双倍查询打印信息


            var tuple = _naHuoLabelService.GetListSortingRules(shopIdList, piCi, pageIndex, pageSize);
            var tunPids = tuple.Item1;
            var totalNumber = tuple.Item2;
            var listss = tuple.Item3;
            if (!listss.Any())
                return FalidResult("无数据打印");

            // 通过扫描状态区分哪些订单是已屯货的，这些订单所有小标签标记为屯
            listss.Where(o => tunPids.Contains(o.PlatformOrderId)).ToList().ForEach(o =>
            {
                o.IsTun = true;
            });

            var lastBazaarStall = "";
            bool isPrintOneBazaarStall = false;//是否单独打印市场档口信息
            var setting = _commonSettingService.GetString("SetNaHuoLabelData", shopIds);
            if (setting != null)
            {
                var tmpModel = setting.ToObject<NaHuoLabelTemplateConfigModel>();
                isPrintOneBazaarStall = tmpModel.IsPrintStall;
            }

            List<object> templateList = new List<object>();
            if (isDanPai)
            {
                templateList = _naHuoLabelService.SinglePrintLabelByPurchases(json, listss, bazaarStall, isPrintOneBazaarStall, out lastBazaarStall);
            }
            else
            {
                templateList = _naHuoLabelService.DoublePrintLabelByPurchases(json, listss, bazaarStall, isPrintOneBazaarStall, out lastBazaarStall);
            }

            var PrintNums = 0;
            if (totalNumber > 0)
                PrintNums = (int)Math.Ceiling(totalNumber / (pageSize * 1.0));

            var pageWidth = json?.Value<int>("Width") ?? 50;

            if (!isDanPai)
                pageWidth = pageWidth * 2;

            var currLabelCount = pageIndex * pageSize > totalNumber ? totalNumber : pageIndex * pageSize;
            var StrResult = new
            {
                IsOK = true,
                ImageUrl = "",
                PageX = json?.Value<int>("Left") ?? 0,
                PageY = json?.Value<int>("Top") ?? 0,
                PageWidth = pageWidth,
                PageHeight = json?.Value<int>("Height") ?? 30,
                PrintNums = PrintNums,
                CurrLabelCount = currLabelCount,
                TotalLabelCount = totalNumber,
                TemplateList = templateList,
                lastBazaarStall = lastBazaarStall
            };


            return SuccessResult(StrResult);
        }


        [LogForOperatorFilter("拿货小标签打印确认")]
        public ActionResult PrintNaHuoLabelPrintCallback(string piCi, int tempId, int pageIndex, string ShopIdList)
        {
            var shopIds = SiteContext.Current.CurrentShopId;
            var shopIdList = ShopIdList.ToList<int>();

            var naHuoTemplate = _naHuoLabelService.GetNaHuoTemplate(tempId, shopIds);
            if (naHuoTemplate == null)
                return FalidResult("请确认当前模板是否存在(来自打印中)!");

            var json = JsonConvert.DeserializeObject<JToken>(naHuoTemplate.Config);
            bool isDanPai = json?.Value<bool>("IsDanPai") ?? false;  //true是单排，false是双排

            int pageSize = 10;
            if (!isDanPai)
                pageSize = 20;//双排时，2个一起作为一行,所以可以双倍查询打印信息


            _naHuoLabelService.SortingRulesPrintCallback(shopIdList, piCi, pageIndex, pageSize);


            return SuccessResult();
        }

        /// <summary>
        /// 导出Excel
        /// </summary>        
        [LogForOperatorFilter("导出备货单")]
        public ActionResult ExportExcel(string settingOptions)
        {
            var log = LogForOperatorContext.Current.logInfo;
            try
            {
                string options = Request.Form["options"].ToString2();
                options = WebHelper.HtmlDecode(options); //&amp->&， &ampnbsp->&nbsp;
                options = WebHelper.HtmlDecode(options).Replace("</br>", "\n"); //&nbsp->' '，2次解码得到最后结果

                var exportModel = JsonExtension.ToObject<ExportPurchaseModel>(options);
                var setting = JsonExtension.ToObject<ExportPurchaseSettingModel>(settingOptions);
                exportModel.Setting = setting;

                if (exportModel == null)
                {
                    log.Exception = "导出设置不能为空";
                    return FalidResult("Excel导出配置解析失败，请联系我们.");
                }
                var fields = setting?.DisplayFields;
                if (fields == null || fields.Count == 0)
                {
                    log.Exception = "列表设置-至少勾选1列显示，再导出Excel";
                    return FalidResult("列表设置-至少勾选1列显示，再导出Excel");
                }
                var items = exportModel?.PurchaseItemsModel?.PurchaseItems;
                if (items == null || items.Count == 0)
                {
                    log.Exception = "无商品数据，请不要导出Excel";
                    return FalidResult("无商品数据，请不要导出Excel");
                }

                log.Detail = new LogForExportDetailModel()
                {
                    TotalCount = exportModel.PurchaseItemsModel.PurchaseItems.Count,
                    ExportSetting = settingOptions,
                    IsCustomerOrder = false,
                    IsExportChecked = false
                };

                var fileName = ExcelHelper.GetFileName("备货单.xls", Request);
                IWorkbook workbook = BuildStockUpCodeExcel(exportModel, fileName);
                Response.Cookies.Add(new HttpCookie("downloadToken", Request.Form["downloadToken"].ToString2()));


                //var rootPath = Server.MapPath("../Files") + $"\\备货单-{SiteContext.Current.CurrentShopId}-{DateTime.Now.ToString("yyyyMMddHHmmss")}.xls";
                //using (var fs = new FileStream(rootPath, FileMode.Create, FileAccess.Write))
                //{
                //    workbook.Write(fs);
                //}
                //var memoryStream = new MemoryStream();
                //using (var fileStream = new FileStream(rootPath, FileMode.Open))
                //{
                //    fileStream.CopyTo(memoryStream);
                //}
                //memoryStream.Position = 0;
                //if (System.IO.File.Exists(rootPath))
                //    System.IO.File.Delete(rootPath);
                //return File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);


                using (MemoryStream ms = new MemoryStream())
                {
                    workbook.Write(ms);
                    var buffer = ms.GetBuffer();
                    //ms.Close();                                                                                            
                    return File(buffer, "application/ms-excel", fileName);
                }
            }
            catch (Exception ex)
            {
                log.Exception = ex.Message;
                throw;
            }
        }

        private PurchaseHistoryViewModel ExportExcelHtmlToData(string html)
        {
            PurchaseHistoryViewModel phvm = new PurchaseHistoryViewModel();
            List<FieldCheckedItem> fields = null;
            List<PurchaseItemsModel> items = null;
            float imgWidth = 80; //默认 80x80 px
            float attrImgWidth = 30;
            int dpi = 72;
            if (!html.IsNullOrEmpty())
            {
                //创建一个html的解析器
                var parser = new HtmlParser();
                var document = parser.Parse(html);
                PurchasesReturnModel pvm = new PurchasesReturnModel()
                {
                    TotalItemCount = document?.QuerySelector(".sum-totalitemcount span")?.TextContent?.ToInt() ?? 0,
                    TotalAmount = document?.QuerySelector(".sum-totalamount span")?.TextContent?.ToDouble() ?? 0,
                    TotalPurchasePrice = document?.QuerySelector(".sum-totalpurchaseprice")?.TextContent?.ToDouble() ?? 0,
                    TotalProfit = document?.QuerySelector(".sum-totalprofit")?.TextContent?.ToDouble() ?? 0,
                };

                //配置信息
                var settingElemt = document?.QuerySelector(".display_setting");
                var displaySet = settingElemt?.TextContent;
                phvm.Setting.DisplayFields = displaySet.IsNullOrEmpty() ? null : JsonExtension.ToList<FieldCheckedItem>(displaySet);
                //dpi = (settingElemt.Attributes?.FirstOrDefault(e => e.Name == "data-dpi")?.Value ?? "72").ToInt();
                //var displayImgSize = settingElemt.Attributes?.FirstOrDefault(e => e.Name == "data-imgsize")?.Value ?? "80x80";
                //var imgSize = displayImgSize.SplitWithString("x").ToList();
                //var displayAttrImgSize = settingElemt.Attributes?.FirstOrDefault(e => e.Name == "data-attr-imgsize")?.Value ?? "30x30";
                //var attrImgSize = displayAttrImgSize.SplitWithString("x").ToList();
                //if (imgSize.Any() && imgSize.Count > 0)
                //    imgWidth = imgSize[0].ToInt() > 0 ? imgSize[0].ToInt() : 80;
                //if (attrImgSize.Any() && attrImgSize.Count > 0)
                //    attrImgWidth = attrImgSize[0].ToInt() > 0 ? attrImgSize[0].ToInt() : 30;


                var ths = document?.QuerySelectorAll("th")?.ToList();
                var trs = document?.QuerySelectorAll("tbody>tr")?.ToList();
                if (trs != null && trs.Count > 0 && ths != null && ths.Count > 0)
                {
                    items = new List<PurchaseItemsModel>();
                    // 所有商品行循环
                    trs.ForEach(tr =>
                    {
                        var productId = tr.GetAttribute("data-productId").ToString2();
                        if (!items.Any(i => i.ProductId == productId))
                        {
                            // 同一商品分组后的数据
                            var groupTrs = trs.Where(gtr => gtr.GetAttribute("data-productId") == productId)?.ToList();
                            if (groupTrs != null && groupTrs.Count > 0)
                            {
                                PurchaseItemsModel item = new PurchaseItemsModel();
                                item.ProductId = productId;
                                groupTrs.ForEach(gtr =>
                                {
                                    var tds = gtr?.QuerySelectorAll("td")?.Where(td => td.ClassName != "td_remark_icon").ToList();
                                    if (tds != null && tds.Count > 0)
                                    {
                                        // 汇总数据
                                        if (tds.Count == ths.Count)
                                        {
                                            if (tds.Count == 15)
                                            {
                                                item.ProductImgUrl = tds[0]?.QuerySelector("img")?.GetAttribute("src").ToString2() ?? "";
                                                item.ShortTitle = tds[1]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                item.ProductSubject = tds[2]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                item.ProductCargoNumber = tds[3]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                item.TotalItemCount = tds[12]?.QuerySelector("span")?.TextContent.ToInt() ?? 0;
                                                item.TotalAmount = tds[13]?.QuerySelector("span")?.TextContent.ToDouble() ?? 0;
                                                item.TotalPurchasePrice = tds[14]?.QuerySelector("span")?.TextContent.ToDouble() ?? 0;

                                                var subItem = new PurchaseSubItemModel();
                                                //subItem.SkuImgUrl = tds[4]?.QuerySelector("img")?.GetAttribute("src").ToString2() ?? "";
                                                subItem.CargoNumber = tds[4]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Color = tds[5]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Size = tds[6]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Count = tds[7]?.QuerySelector("span")?.TextContent.ToInt() ?? 0;
                                                subItem.ItemAmount = tds[8]?.QuerySelector("span")?.TextContent.ToDouble() ?? 0;
                                                subItem.Bazaar = tds[9]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Stall = tds[10]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.CostPrice = tds[11]?.QuerySelector("span")?.TextContent.ToDouble() ?? 0;
                                                item.SubItems.Add(subItem);
                                            }
                                            else
                                            {
                                                item.ProductImgUrl = tds[0]?.QuerySelector("img")?.GetAttribute("src").ToString2() ?? "";
                                                item.ShortTitle = tds[1]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                item.ProductSubject = tds[2]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                item.ProductCargoNumber = tds[3]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                item.TotalItemCount = tds[13]?.QuerySelector("span")?.TextContent.ToInt() ?? 0;
                                                item.TotalAmount = tds[14]?.QuerySelector("span")?.TextContent.ToDouble() ?? 0;
                                                item.TotalPurchasePrice = tds[15]?.QuerySelector("span")?.TextContent.ToDouble() ?? 0;

                                                var subItem = new PurchaseSubItemModel();
                                                subItem.SkuImgUrl = tds[4]?.QuerySelector("img")?.GetAttribute("src").ToString2() ?? "";
                                                subItem.CargoNumber = tds[5]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Color = tds[6]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Size = tds[7]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Count = tds[8]?.QuerySelector("span")?.TextContent.ToInt() ?? 0;
                                                subItem.ItemAmount = tds[9]?.QuerySelector("span")?.TextContent.ToDouble() ?? 0;
                                                subItem.Bazaar = tds[10]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Stall = tds[11]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.CostPrice = tds[12]?.QuerySelector("span")?.TextContent.ToDouble() ?? 0;
                                                item.SubItems.Add(subItem);
                                            }
                                        }
                                        else
                                        {
                                            if (tds.Count == 8)
                                            {
                                                var subItem = new PurchaseSubItemModel();
                                                //subItem.SkuImgUrl = tds[0]?.QuerySelector("img")?.GetAttribute("src").ToString2() ?? "";
                                                subItem.CargoNumber = tds[0]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Color = tds[1]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Size = tds[2]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Count = tds[3]?.QuerySelector("span")?.TextContent.ToInt() ?? 0;
                                                subItem.ItemAmount = tds[4]?.QuerySelector("span")?.TextContent.ToDouble() ?? 0;
                                                subItem.Bazaar = tds[5]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Stall = tds[6]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.CostPrice = tds[7]?.QuerySelector("span")?.TextContent.ToDouble() ?? 0;
                                                item.SubItems.Add(subItem);
                                            }
                                            else
                                            {
                                                var subItem = new PurchaseSubItemModel();
                                                subItem.SkuImgUrl = tds[0]?.QuerySelector("img")?.GetAttribute("src").ToString2() ?? "";
                                                subItem.CargoNumber = tds[1]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Color = tds[2]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Size = tds[3]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Count = tds[4]?.QuerySelector("span")?.TextContent.ToInt() ?? 0;
                                                subItem.ItemAmount = tds[5]?.QuerySelector("span")?.TextContent.ToDouble() ?? 0;
                                                subItem.Bazaar = tds[6]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.Stall = tds[7]?.QuerySelector("span")?.TextContent.ToString2() ?? "";
                                                subItem.CostPrice = tds[8]?.QuerySelector("span")?.TextContent.ToDouble() ?? 0;
                                                item.SubItems.Add(subItem);
                                            }
                                        }
                                    }
                                });
                                items.Add(item);
                            }
                        }
                    });

                    pvm.PurchaseItems = items;
                }
                phvm.PurchaseItemsModel = pvm;
                return phvm;
            }

            return phvm;
        }

        /// <summary>
        /// 生成备货单-直接保存备货单列表的html
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public ActionResult Add(PurchaseHistory model)
        {
            model.ShopId = model.ShopId == 0 ? SiteContext.Current.CurrentShopId : model.ShopId;
            model.CreateTime = DateTime.Now;
            var result = _purchaseService.Add(model);
            return SuccessResult(result);
        }

        public ActionResult Delete(int id)
        {
            var isSuccess = _purchaseService.Delete(id).ToBoolean();
            return SuccessResult(isSuccess);
        }

        public ActionResult UpdatePurchaseHistory(int id)
        {
            if (id <= 0) return FalidResult("保存失败");

            var history = _purchaseService.Get(id);
            history.Data = Request.Form["Data"].ToString2();
            _purchaseService.Update(history);
            return SuccessResult();
        }

        /// <summary>
        /// 加载选择的备货单数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public ActionResult Load(int id)
        {
            var history = _purchaseService.Get(id);
            return SuccessResult(history);
        }

        ///// <summary>
        ///// 渲染备货单列表页面
        ///// </summary>
        ///// <returns></returns>
        //public ActionResult RenderPurchaseList(ProductSearchModel model)
        //{
        //    PurchasesViewModel pvm = new PurchasesViewModel()
        //    {
        //        PurchaseItems = _purchaseService.GetPurchaseList(model)?.PurchaseItems ?? new List<PurchaseItemsModel>()
        //    };

        //    var html = RenderPartialViewToString("RenderPurchaseList", pvm);
        //    return Json(html);
        //}


        [LogForOperatorFilter("生成备货单")]
        public ActionResult GetPurchaseList(ProductSearchModel model)
        {
            var log = LogForOperatorContext.Current.logInfo;
            var logId = log._Id;
            log.Request = model.ToJson().ToObject<ProductSearchModel>();

            var shopIdFilter = model.Filters.FirstOrDefault(m => m.Name == "ShopId");
            if (shopIdFilter == null)
                model.Filters.Add(new OrderSearchFieldModel { TableAlias = "o", TableName = "P_Order", Name = "ShopId", Value = string.Join(",", SiteContext.Current.SamePlatformShopIds), Contract = "in" });

            var commSets = _commonSettingService.GetSets(new List<string> { "/Purchase/TaskForOrderCountLimit", "/Purchase/DefaultPageSize" }, 0);
            var limitCountSet = commSets?.FirstOrDefault(m => m.Key == "/Purchase/TaskForOrderCountLimit");
            var pageSizeSet = commSets?.FirstOrDefault(m => m.Key == "/Purchase/DefaultPageSize");
            var limitCount = limitCountSet?.Value.ToInt() ?? CustomerConfig.ExportByTaskOrderCountLimit;
            limitCount = limitCount > 0 ? limitCount : CustomerConfig.ExportByTaskOrderCountLimit;
            var pageSize = pageSizeSet?.Value.ToInt() ?? 5000;
            pageSize = pageSize > 0 ? pageSize : 5000;
            model.PageSize = limitCount;

            var curShop = SiteContext.Current.CurrentLoginShop;
            var json = model.ToJson();
            var countSearchModel = json.ToObject<ProductSearchModel>();


            Stopwatch sw = new Stopwatch();
            sw.Start();

            var count = _purchaseService.GetOrderProductItemsCount(countSearchModel);

            sw.Stop();
            Log.Debug($"************店铺【{curShop.Id}】备货单查询订单商品数量耗时：{sw.Elapsed.TotalSeconds}s");


            if (count >= limitCount)
            {
                model.NeedPagging = true;
                //使用任务方式导出
                var newTask = new ExportTask
                {
                    IP = Request.UserHostAddress,
                    CreateTime = DateTime.Now,
                    PlatformType = curShop.PlatformType,
                    ShopId = curShop.Id,
                    UserId = SiteContext.Current.CurrentLoginSubUser?.Id.ToString(),
                    Status = 0,
                    Type = ExportType.Purchase.ToInt(),
                    PageIndex = 1,
                    PageSize = pageSize,
                    TotalCount = count,
                    ParamJson = model.ToJson(),
                    FromModule = "生成备货单",
                    ExtField1 = logId,
                };
                newTask.Id = _exportTaskService.Add(newTask);
                return SuccessResult("任务创建成功", GetExportTaskToWeb(newTask));
            }

            var searchModel = json.ToObject<ProductSearchModel>();
            searchModel.NeedPagging = false;
            var prm = _purchaseService.GetPurchaseList(searchModel);
            prm.LogId = logId;

            // 拼多多买家留言展开买家昵称解密
            new PurchaseService().DecryptPddToName(prm);

            log.Detail = new LogForCreatePurchaseModel() { ProductCount = prm.PurchaseItems.Count, ProductItemCount = prm.PurchaseItems.Sum(m => m.SubItems.Count) };
            return Json(prm);
        }

        /// <summary>
        /// 渲染备货单历史数据列表
        /// </summary>
        /// <returns></returns>
        public ActionResult RenderHistoryList()
        {
            int pageNum = Request.Form["PageIndex"].ToInt();
            int rowNum = Request.Form["PageSize"].ToInt();
            var list = _purchaseService.GetPage(pageNum, rowNum, "", "CreateTime Desc", null);

            return SuccessResult(list);
        }

        public ActionResult HistoryList()
        {
            int pageIndex = Request.Form["PageIndex"].ToInt();
            int pageSize = Request.Form["PageSize"].ToInt();
            var list = _purchaseService.GetHistoryList(SiteContext.Current.CurrentShopId, pageIndex, pageSize, new List<string> { "Id", "Name", "CreateTime" });

            return SuccessResult(list);
        }

        public ActionResult GetHistoryById(int id)
        {
            if (id <= 0)
                return FalidResult("查询的历史备货单不存在");

            var history = _purchaseService.Get(id);
            if (history == null)
                return FalidResult("查询的历史备货单不存在");

            return SuccessResult(history);
        }

        public ActionResult PurchasesSet()
        {


            return View();
        }

        public ActionResult newPurchases()
        {


            return View();
        }

        #region Excel导出
        private IWorkbook BuildStockUpCodeExcel(ExportPurchaseModel exportModel, string fileName)
        {
            //List<FieldCheckedItem> displayFields = new List<FieldCheckedItem>();
            //PurchasePrintFieldsSetModel fieldSetModel = new PurchasePrintFieldsSetModel();
            //var fieldSet = _commonSettingService.Get("PurchasePrintFieldsSet", SiteContext.Current.CurrentShopId);
            //if (fieldSet != null && !fieldSet.Value.IsNullOrEmpty())
            //{
            //    fieldSetModel = JsonExtension.ToObject<PurchasePrintFieldsSetModel>(fieldSet.Value);
            //    displayFields = fieldSetModel.DisplayFields ?? new List<FieldCheckedItem>();
            //}
            var purchaseList = exportModel?.PurchaseItemsModel?.PurchaseItems ?? new List<PurchaseItemsModel>();// tuple.Item1;
            var displayFields = exportModel?.Setting?.DisplayFields ?? new List<CheckedItem>();//tuple.Item2;
            var imgWidth = exportModel?.Setting?.ImgWidth ?? 80f;// displayFields.Any(m=>m.Value == "ProductImgUrl")? tuple.Item3 : 80f;
            var attrImgWidth = exportModel?.Setting?.AttrImgWidth ?? 30f;//displayFields.Any(m => m.Value == "SkuImgUrl") ? tuple.Item4 : 30f;
            var dpi = exportModel?.Setting?.DPI ?? 72;//tuple.Item5;

            var imgHeightInPoints = (float)ExcelHelper.PixelToRowHeightPoints(imgWidth, dpi);
            var imgWidthInPoints = imgWidth / 7;// NPOI px->colwidth   //(float)ExcelHelper.PixelToColWidthPoints(imgWidth);

            var attrImgHeightInPoints = (float)ExcelHelper.PixelToRowHeightPoints(attrImgWidth, dpi);
            var attrImgWidthInPoints = attrImgWidth / 7;

            // 默认行高
            var rowHeightInPoints = imgHeightInPoints > attrImgHeightInPoints ? imgHeightInPoints : attrImgHeightInPoints;
            // 移除合计行的配置
            var sumFields = displayFields.Where(field => field.Value == "Sum-TotalItemCount" || field.Value == "Sum-TotalAmount" || field.Value == "Sum-TotalPurchasePrice" || field.Value == "Sum-TotalProfit").ToList();
            displayFields.RemoveAll(field => field.Value == "Sum-TotalItemCount" || field.Value == "Sum-TotalAmount" || field.Value == "Sum-TotalPurchasePrice" || field.Value == "Sum-TotalProfit");
            // 表头名称集合
            var headNames = displayFields.Select(m => m.Text).ToList();
            // 合并列的集合
            var regionFieldLst = displayFields.Where(m => m.Value == "ProductImgUrl" || m.Value == "ShortTitle" || m.Value == "ProductSubject" || m.Value == "ProductCargoNumber" || m.Value == "TotalItemCount" || m.Value == "TotalAmount" || m.Value == "TotalPurchasePrice" || m.Value == "TotalProfit").ToList();
            // 非合并列集合
            var normalFieldLst = displayFields.Except(regionFieldLst).ToList();

            //DocumentSummaryInformation docInfo = PropertySetFactory.CreateDocumentSummaryInformation();
            //docInfo.Company = "NPOI Team";
            //SummaryInformation summaryInfo = PropertySetFactory.CreateSummaryInformation();
            //summaryInfo.Subject = "NPOI SDK Example";

            //IWorkbook workbook = new IWorkbook();
            //workbook.DocumentSummaryInformation = docInfo;
            //workbook.SummaryInformation = summaryInfo;

            IWorkbook workbook = ExcelHelper.GetNewWorkbook(fileName);
            ICellStyle headerStyle = GetHeaderCellStyle(workbook);
            ICellStyle contentStyle = GetContentCellStyle(workbook);
            ICellStyle contentMergeStyle = GetMergeContentCellStyle(workbook);
            ICellStyle productSubjectContentStyle = GetProductSubjectContentCellStyle(workbook);

            ISheet sheet = workbook.CreateSheet("备货单");
            IRow header = sheet.CreateRow(0);
            header.HeightInPoints = 30;

            // 第一行填充表头信息和样式
            int colIndex = 0;
            displayFields.ForEach(head =>
            {
                //设置列宽度
                if (head.Text == "图片" || head.Text == "商品主图")
                    sheet.SetColumnWidth(colIndex, Convert.ToInt32(imgWidthInPoints * 256));
                else if (head.Text == "规格图")
                {
                    // 规格图最小宽度60px<->7.86
                    var cellWidthInPoints = attrImgWidthInPoints > 7.86 ? attrImgWidthInPoints : 7.86f;
                    sheet.SetColumnWidth(colIndex, Convert.ToInt32(cellWidthInPoints * 256));
                }
                else
                    SetColumnWidth(sheet, head.Text, colIndex);

                //var pixels = sheet.GetColumnWidthInPixels(index);
                // 设置列名和样式
                if (false)
                {
                    var headName = head.Text == "商品主图" ? "图片" : head.Text;
                    headName = headName == "单品成本" ? "成本价" : headName;
                    header.CreateCell(colIndex).SetCellValue(headName);
                }
                else
                    header.CreateCell(colIndex).SetCellValue(head.Text);
                header.GetCell(colIndex).CellStyle = headerStyle;
                colIndex++;
            });

            // 第二行开始填充Excel内容
            var index = 1;
            purchaseList.ForEach(item =>
            {
                var dic = item.ToDictionary();
                IRow row = sheet.CreateRow(index);
                List<IRow> tmpSLRowLst = new List<IRow>();
                row.HeightInPoints = rowHeightInPoints;
                var itemRowIndex = index;

                // 合并列设置
                var mergeCount = item.SubItems.Count;
                regionFieldLst.ForEach(field =>
                {
                    var cellIndex = headNames.IndexOf(field.Text);
                    if (cellIndex != -1 && mergeCount > 1)
                    {
                        int startRowIndex = index;
                        int endRowNumIndex = index + mergeCount - 1;
                        var productRegion = new CellRangeAddress(startRowIndex, endRowNumIndex, cellIndex, cellIndex);
                        sheet.AddMergedRegion(productRegion);
                    }
                });

                // 非合并列赋值
                var skuImgUrlLst = new List<string>();
                var attrImgSizeCellIndex = -1;
                item.SubItems.ForEach(subItem =>
                {
                    var tmpRow = mergeCount == 1 ? row : sheet.CreateRow(itemRowIndex);
                    // 新建子行设置默认行高为规格图高度
                    if (mergeCount > 1 && attrImgHeightInPoints > 0)
                        tmpRow.HeightInPoints = attrImgHeightInPoints;

                    var subItemDic = subItem.ToDictionary();
                    normalFieldLst.ForEach(field =>
                    {
                        var cellIndex = displayFields.IndexOf(field);
                        ICell cell = tmpRow.CreateCell(cellIndex);
                        cell.CellStyle = contentStyle;

                        if (field.Value == "SkuImgUrl")
                            attrImgSizeCellIndex = cellIndex;
                        else if (field.Value == "ColorAndSize" || field.Value == "ColorAndSize2")
                            cell.SetCellValue(subItemDic["Color"].ToString2());
                        else
                            cell.SetCellValue(subItemDic[field.Value].ToString2());
                    });

                    // 合并列及其子行设置样式(这里设置边框才有效果)
                    regionFieldLst.ForEach(field =>
                    {
                        var cellIndex = displayFields.IndexOf(field);
                        ICell cell = tmpRow.CreateCell(cellIndex);
                        cell.CellStyle = field.Value == "ProductSubject" ? productSubjectContentStyle : contentStyle;
                    });

                    if (mergeCount > 1)
                        tmpSLRowLst.Add(tmpRow);

                    skuImgUrlLst.Add(subItem.SkuImgUrl.ToString2());
                    itemRowIndex++;
                });

                // 合并列赋值和样式设置
                regionFieldLst.ForEach(field =>
                {
                    var cellIndex = displayFields.IndexOf(field);
                    ICell cell = row.GetCell(cellIndex) ?? row.CreateCell(cellIndex);
                    if (field.Value != "ProductImgUrl")
                    {
                        cell.SetCellValue(dic[field.Value].ToString2());
                        cell.CellStyle = field.Value == "ProductSubject" ? productSubjectContentStyle : contentStyle;
                    }
                });

                AutoSizeCellHeight(item, row, tmpSLRowLst, sheet, workbook);
                // 在Excel中画规格图 
                var skuImgUrl = string.Empty;
                if (normalFieldLst.Any(field => field.Value == "SkuImgUrl"))
                {
                    if (mergeCount == 1)
                    {
                        var url = skuImgUrlLst?[0]?.ToString2();
                        skuImgUrl = skuImgUrl.IsNullOrEmpty() ? url : skuImgUrl;
                        if (attrImgSizeCellIndex > 0)
                            DrawPicIntoExcel(workbook, sheet, new List<IRow> { row }, attrImgHeightInPoints, attrImgWidthInPoints, url, itemRowIndex - 1, attrImgSizeCellIndex);
                    }
                    else
                    {
                        int tmpIndex = 0;
                        int tmpRowIndex = index;
                        tmpSLRowLst.ForEach(tmpRow =>
                        {
                            var url = skuImgUrlLst[tmpIndex].ToString2();
                            skuImgUrl = skuImgUrl.IsNullOrEmpty() ? url : skuImgUrl;
                            if (attrImgSizeCellIndex > 0)
                                DrawPicIntoExcel(workbook, sheet, new List<IRow> { tmpRow }, attrImgHeightInPoints, attrImgWidthInPoints, url, tmpRowIndex, attrImgSizeCellIndex);
                            tmpRowIndex++;
                            tmpIndex++;
                        });
                    }
                }
                // 在 Excel中画商品主图 
                if (regionFieldLst.Any(field => field.Value == "ProductImgUrl"))
                {
                    var defaultImgUrl = "https://cbu01.alicdn.com/images/cn/market/myalibaba/newmyali/nopic.gif";
                    var url = item.ProductImgUrl.IsNullOrEmpty() || item.ProductImgUrl == defaultImgUrl ? skuImgUrl : item.ProductImgUrl;
                    DrawPicIntoExcel(workbook, sheet, tmpSLRowLst, imgHeightInPoints, imgWidthInPoints, url, itemRowIndex - 1, 0);
                }

                if (itemRowIndex > index)
                    index = itemRowIndex;
                else
                    index++;
            });

            // 合并项
            if (sumFields != null && sumFields.Any())
            {
                IRow row = sheet.CreateRow(index);
                row.HeightInPoints = 40f;
                var sumRegion = new CellRangeAddress(index, index, 0, colIndex - 1);
                sheet.AddMergedRegion(sumRegion);

                var contentCell = row.CreateCell(0);
                contentCell.CellStyle = GetRightContentCellStyle(workbook);
                for (var i = 1; i < colIndex; i++)
                {
                    ICell cell = row.CreateCell(i);
                    cell.CellStyle = GetRightContentCellStyle(workbook);
                }

                var sumContent = string.Empty;
                sumFields.ForEach(f =>
                {
                    if (f.Value == "Sum-TotalItemCount")
                        sumContent += $"  总件数：{exportModel.PurchaseItemsModel.TotalItemCount}件";
                    else if (f.Value == "Sum-TotalAmount")
                        sumContent += $"  总金额：{exportModel.PurchaseItemsModel.TotalAmount}元";
                    else if (f.Value == "Sum-TotalPurchasePrice")
                        sumContent += $"  总成本：{exportModel.PurchaseItemsModel.TotalPurchasePrice}元";
                    else if (f.Value == "Sum-TotalProfit")
                        sumContent += $"  总商品差价：{exportModel.PurchaseItemsModel.TotalProfit}元";
                });
                contentCell.SetCellValue(sumContent);
            }
            return workbook;
        }

        private void SetColumnWidth(ISheet sheet, string headName, int index)
        {
            int width = 20 * 256;

            if (headName == "图片" || headName == "数量" || headName == "金额" || headName == "市场" || headName == "档口"
                || headName == "成本价" || headName == "单品成本" || headName == "单品总成本" || headName == "单价" || headName == "单品差价" || headName == "总量" || headName == "总金额" || headName == "总成本" || headName == "总商品差价")
                width = 10 * 256;
            else if (headName == "简称" || headName == "商品货号" || headName == "单品货号" || headName == "颜色" || headName == "尺码")
                width = 15 * 256;
            else if (headName == "商品名称")
                width = 50 * 256;

            sheet.SetColumnWidth(index, width);
        }

        private ICellStyle GetHeaderCellStyle(IWorkbook excel)
        {
            IFont font = excel.CreateFont();
            font.FontName = "Times New Roman";
            font.Boldweight = short.MaxValue;
            font.FontHeightInPoints = 11;

            ICellStyle headerStyle = excel.CreateCellStyle();
            headerStyle.SetFont(font);
            headerStyle.Alignment = HorizontalAlignment.Center;//内容居中显示
            headerStyle.VerticalAlignment = VerticalAlignment.Center;
            headerStyle.WrapText = true;
            headerStyle.BorderRight = BorderStyle.Hair;
            headerStyle.BorderBottom = BorderStyle.Hair;

            return headerStyle;
        }

        private ICellStyle GetMergeContentCellStyle(IWorkbook excel)
        {
            IFont font = excel.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = excel.CreateCellStyle();
            contentStyle.Alignment = HorizontalAlignment.Center;//内容居中显示;
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.WrapText = true;

            contentStyle.BorderBottom = BorderStyle.Hair;
            contentStyle.BorderRight = BorderStyle.Hair;
            contentStyle.BorderLeft = BorderStyle.Hair;
            contentStyle.BorderTop = BorderStyle.Hair;

            return contentStyle;
        }

        private ICellStyle GetContentCellStyle(IWorkbook excel)
        {
            IFont font = excel.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = excel.CreateCellStyle();
            contentStyle.SetFont(font);
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.Alignment = HorizontalAlignment.Left;
            contentStyle.WrapText = true;

            contentStyle.BorderBottom = BorderStyle.Hair;
            contentStyle.BorderRight = BorderStyle.Hair;
            contentStyle.BorderLeft = BorderStyle.Hair;
            contentStyle.BorderTop = BorderStyle.Hair;

            return contentStyle;
        }

        private ICellStyle GetProductSubjectContentCellStyle(IWorkbook excel)
        {
            ICellStyle contentStyle = excel.CreateCellStyle();
            IFont font = excel.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";
            //font.Underline = FontUnderlineType.Single;
            font.Color = HSSFColor.LightBlue.Index;

            contentStyle.SetFont(font);
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.Alignment = HorizontalAlignment.Left;
            contentStyle.WrapText = true;

            contentStyle.BorderBottom = BorderStyle.Hair;
            contentStyle.BorderRight = BorderStyle.Hair;
            contentStyle.BorderLeft = BorderStyle.Hair;

            return contentStyle;
        }

        private ICellStyle GetRightContentCellStyle(IWorkbook excel)
        {
            IFont font = excel.CreateFont();
            font.FontHeightInPoints = 10;
            font.FontName = "Times New Roman";

            ICellStyle contentStyle = excel.CreateCellStyle();
            contentStyle.SetFont(font);
            contentStyle.VerticalAlignment = VerticalAlignment.Center;
            contentStyle.Alignment = HorizontalAlignment.Right;
            contentStyle.WrapText = true;

            contentStyle.BorderBottom = BorderStyle.Hair;
            contentStyle.BorderRight = BorderStyle.Hair;
            contentStyle.BorderLeft = BorderStyle.Hair;
            contentStyle.BorderTop = BorderStyle.Hair;

            return contentStyle;
        }

        /// <summary>
        /// 正则获取html标签的text内容
        /// </summary>
        /// <param name="tag"></param>
        /// <returns></returns>
        private string GetHtmlText(string tag)
        {
            // <a\s*[^>]*>([\s\S]+?)</a>
            Regex reg = new Regex(@"<\s*[^>]*>([\s\S]+?)</\s*[^>]*>", RegexOptions.IgnoreCase);
            tag = reg.Replace(tag, "$1");
            return tag;
        }

        /// <summary>
        /// 验证是否是URL链接
        /// </summary>
        /// <param name="str">指定字符串</param>
        /// <returns></returns>
        public static bool IsURL(string str)
        {
            string pattern = @"^((http|https|ftp|ws|file)://)?(www.)?[\w\.]+(\.(com|net|cn|com\.cn|com\.net|net\.cn))(/[^\s\n]*)?";
            Regex reg = new Regex(pattern);
            if (string.IsNullOrEmpty(str))
                return false;
            return reg.IsMatch(str);
        }

        /// <summary>
        /// 向sheet插入图片
        /// </summary>
        /// <param name="workbook"></param>
        /// <param name="sheet"></param>
        /// <param name="tmpSLRowLst"></param>
        /// <param name="imgHeightInPoints"></param>
        /// <param name="picUrl"></param>
        /// <param name="rowIndex"></param>
        /// <param name="colIndex"></param>
        private void DrawPicIntoExcel(IWorkbook workbook, ISheet sheet, List<IRow> tmpSLRowLst, float imgHeightInPoints, float imgWidthInPoints, string picUrl, int rowIndex, int colIndex)
        {
            if (picUrl.ToString2().EndsWith("cbu01.alicdn.com/images/cn/market/myalibaba/newmyali/nopic.gif"))
                return;

            var bytes = GetPicBytes(picUrl);
            int dtTmpSICount = tmpSLRowLst.Count;
            var sumRowHeightInPoints = tmpSLRowLst.Sum(row => row.HeightInPoints);
            int dx1 = 10, // 距离左侧宽度 (y:0~255,x:0~1024)
                dy1 = 5,
                dx2 = 1013,
                dy2 = 250;

            int startRowIndex = 0;// 起点行号
            int endRowIndex = 0; // 终点行号

            if (dtTmpSICount <= 1)
            {
                startRowIndex = rowIndex;// 起点行号
                endRowIndex = rowIndex; // 终点行号

                if (sumRowHeightInPoints > imgHeightInPoints)
                {
                    var diff = (sumRowHeightInPoints - imgHeightInPoints) / 2;
                    dy1 = Convert.ToInt32((diff / sumRowHeightInPoints) * 255);
                    dy2 = 255 - dy1;
                }

                var colWidthInPoints = sheet.GetColumnWidth(colIndex) * 1.0 / 256;
                if (colWidthInPoints > imgWidthInPoints)
                {
                    var widthDiff = (colWidthInPoints - imgWidthInPoints) / 2;
                    dx1 = Convert.ToInt32((widthDiff / colWidthInPoints) * 1023);
                    dx2 = 1023 - dx1;
                }
            }
            else
            {
                var diff = (sumRowHeightInPoints - imgHeightInPoints) / 2;
                var isCalcDX1_DY1 = false;
                var isCalcDX2_DY2 = false;

                var index = 0;
                var rowHeighLst = new List<float>();
                var rowCount = tmpSLRowLst.Count;
                tmpSLRowLst.ForEach(trow =>
                {
                    // 设置列宽
                    var colWidthInPoints = sheet.GetColumnWidth(colIndex) * 1.0 / 256;
                    if (colWidthInPoints > imgWidthInPoints)
                    {
                        var widthDiff = (colWidthInPoints - imgWidthInPoints) / 2;
                        dx1 = Convert.ToInt32((widthDiff / colWidthInPoints) * 1023);
                        dx2 = 1023 - dx1;
                    }

                    // 设置行高
                    var tmpRowHeightInPoints = trow.HeightInPoints;
                    rowHeighLst.Add(tmpRowHeightInPoints);
                    var sumHeighInPoints = rowHeighLst.Sum();
                    if (sumHeighInPoints >= diff && !isCalcDX1_DY1)
                    {
                        var dy1InPoints = diff - (sumHeighInPoints - tmpRowHeightInPoints);
                        dy1 = dy1InPoints <= 5 ? 5 : Convert.ToInt32((dy1InPoints / tmpRowHeightInPoints) * 255);
                        startRowIndex = rowIndex - rowCount + index + 1;
                        isCalcDX1_DY1 = true;
                    }

                    if (sumHeighInPoints >= (diff + imgHeightInPoints) && !isCalcDX2_DY2)
                    {
                        var dy2InPoints = (diff + imgHeightInPoints) - (sumHeighInPoints - tmpRowHeightInPoints);
                        dy2 = dy2InPoints >= 250 ? 250 : Convert.ToInt32((dy2InPoints / tmpRowHeightInPoints) * 255);
                        endRowIndex = rowIndex - rowCount + index + 1;
                        isCalcDX2_DY2 = true;
                        return;
                    }

                    index++;
                });

                if (startRowIndex > endRowIndex)
                    endRowIndex = startRowIndex;
            }

            // 上下间隔5，左右间隔10
            dy1 = dy1 <= 5 ? 5 : (dy1 >= 250 ? 250 : dy1);
            dy2 = dy2 <= 5 ? 5 : (dy2 >= 250 ? 250 : dy2);
            dx1 = dx1 <= 10 ? 10 : (dx1 >= 1013 ? 1013 : dx1);
            dx2 = dx2 <= 10 ? 10 : (dx2 >= 1013 ? 1013 : dx2);

            int pictureIdx = workbook.AddPicture(bytes, NPOI.SS.UserModel.PictureType.JPEG);
            var patriarch = sheet.CreateDrawingPatriarch();
            // HSSFClientAnchor(int dx1,int dy1,int dx2,int dy2,short col1,int row1,short col2, int row2)
            // dx1,dy1:起点x,y坐标（起点单元格为参照物），dx2,dy2:终点x,y坐标（终点单元格为参照物）. x,y值范围：（x:0~1023,y:0~255）
            // col1,row1:起点单元格位置，col2,row2:终点单元格位置
            // 插图片的位置  HSSFClientAnchor（dx1,dy1,dx2,dy2,col1,row1,col2,row2) 
            // 后面四个参数定位行列的时候，参数（，，，，0,14,11,14）在xls文档中可以正确定位插入图片，
            // 但是在xlsx文档中图片找不到了，参数改成（，，，，0,14,12,15）才能正确定位画图

            //var anchor = new XSSFClientAnchor(dx1, dy1, dx2, dy2, (short)colIndex, startRowIndex, (short)colIndex+1, endRowIndex+1);
            IClientAnchor anchor = null;
            if (workbook is XSSFWorkbook)
                anchor = new XSSFClientAnchor(dx1, dy1, dx2, dy2, (short)colIndex, startRowIndex, (short)colIndex + 1, endRowIndex + 1);
            else
                anchor = new HSSFClientAnchor(dx1, dy1, dx2, dy2, (short)colIndex, startRowIndex, (short)colIndex, endRowIndex);
            anchor.AnchorType = AnchorType.MoveDontResize;
            var pict = patriarch.CreatePicture(anchor, pictureIdx);
        }

        /// <summary>
        /// 获取图片byte数组
        /// </summary>
        /// <param name="picUrl">图片url地址</param>
        /// <returns></returns>
        private byte[] GetPicBytes(string picUrl)
        {
            try
            {
                var request = (HttpWebRequest)WebRequest.Create(picUrl);
                var response = (HttpWebResponse)request.GetResponse();

                using (var stream = response.GetResponseStream())
                {
                    byte[] bytes = new byte[1024];
                    using (MemoryStream ms = new MemoryStream())
                    {
                        int length = 0;
                        while ((length = stream.Read(bytes, 0, bytes.Length)) > 0)
                        {
                            ms.Write(bytes, 0, length);
                        }
                        ms.Seek(0, SeekOrigin.Begin);

                        return ms.ToArray();
                        //Image img = Image.FromStream(ms);
                        //using (MemoryStream ms1 = new MemoryStream())
                        //{
                        //    img = resizeImage(img, new Size(80, 80));
                        //    img.Save(ms1, ImageFormat.Jpeg);
                        //    byte[] buffer = new byte[ms1.Length];
                        //    //Image.Save()会改变MemoryStream的Position，需要重新Seek到Begin
                        //    ms1.Seek(0, SeekOrigin.Begin);
                        //    ms1.Read(buffer, 0, buffer.Length);


                        //}

                    }
                }
            }
            catch (Exception ex)
            {
                Log.WriteError($"{picUrl} 不存在，异常详情：{ex.Message}");
                return GetPicBytes("https://cbu01.alicdn.com/images/cn/market/myalibaba/newmyali/nopic.gif");
            }

        }

        /// <summary>
        /// 自动设置行最大高度（图片列作为最小默认高度），总行高超出图片高度，则平摊到被合并的每一行
        /// </summary>
        /// <param name="dRow"></param>
        /// <param name="row">合并行</param>
        /// <param name="tmpSLRowLst">合并子行</param>
        /// <param name="ffSheet"></param>
        /// <param name="columnNum"></param>
        private void AutoSizeCellHeight(PurchaseItemsModel item, IRow row, List<IRow> tmpSLRowLst, ISheet ffSheet, IWorkbook workbook)
        {
            float mergerRowHeightInPoints = row.HeightInPoints;
            int tmpSLRowCount = tmpSLRowLst.Count;
            if (tmpSLRowCount == 0)
            {
                var maxHeightInPoint = ExcelHelper.AutoSizeRowHeight(workbook, ffSheet, row);
                row.HeightInPoints = maxHeightInPoint > mergerRowHeightInPoints ? maxHeightInPoint : mergerRowHeightInPoints;
            }
            else
            {
                // 合并行总行高
                float maxMergeCellHeightInPoint = mergerRowHeightInPoints;
                float sumHeightInPoint = 0;
                int rowIndex = 0;
                List<float> maxHeightInPointLst = new List<float>();
                tmpSLRowLst.ForEach(tmpRow =>
                {
                    int cellIndex = 0;
                    float maxCellHeightInPoint = 0;
                    var rowHeightInPoints = tmpRow.HeightInPoints;
                    if (rowHeightInPoints > mergerRowHeightInPoints)
                        maxMergeCellHeightInPoint = rowHeightInPoints;

                    tmpRow.Cells.ForEach(cell =>
                    {
                        float heightInPoints = ExcelHelper.GetCellHeightInPoints(workbook, ffSheet, cell, cellIndex);
                        heightInPoints = heightInPoints > rowHeightInPoints ? heightInPoints : rowHeightInPoints;
                        if (cell.IsMergedCell)
                            maxMergeCellHeightInPoint = maxMergeCellHeightInPoint > heightInPoints ? maxMergeCellHeightInPoint : heightInPoints;
                        else
                            maxCellHeightInPoint = maxCellHeightInPoint > heightInPoints ? maxCellHeightInPoint : heightInPoints;
                        cellIndex++;
                    });

                    maxHeightInPointLst.Add(maxCellHeightInPoint);
                    sumHeightInPoint += maxCellHeightInPoint;
                    rowIndex++;
                });

                // 如果合并列的高度大于合并行的总高度时，平均平摊到合并行的高度,否则每行取其合并行最大高度即可
                var diffHeightInPoint = maxMergeCellHeightInPoint - sumHeightInPoint;
                rowIndex = 0;
                tmpSLRowLst.ForEach(tmpRow =>
                {
                    if (diffHeightInPoint > 0)
                        tmpRow.HeightInPoints = maxHeightInPointLst[rowIndex] + diffHeightInPoint / tmpSLRowCount;
                    else
                        tmpRow.HeightInPoints = maxHeightInPointLst[rowIndex];
                    rowIndex++;
                });
            }
        }

        private void ExportToBrowser(IWorkbook excel)
        {
            MemoryStream excelStream = new MemoryStream();
            excel.Write(excelStream);

            string fileName = GetFileName("备货单.xls");
            Response.AddHeader("Content-Disposition", "attachment;fileName=" + fileName);
            Response.ContentType = "application/vnd.ms-excel";

            Response.Charset = "utf-8";
            Response.BinaryWrite(excelStream.ToArray());
            Response.Flush();
            Response.End();

            excelStream.Close();
        }


        private bool isMSBrowser()
        {
            string[] IEBrowserSignals = { "MSIE", "Trident", "Edge" };
            string userAgent = Request.UserAgent.ToUpper();
            foreach (var signal in IEBrowserSignals)
            {
                if (userAgent.Contains(signal))
                    return true;
            }
            return false;
        }

        /// <summary>
        /// 处理中文文件名乱码问题
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private string GetFileName(string fileName)
        {
            Encoding encoding;
            string outputFileName = null;
            string browser = Request.UserAgent.ToUpper();
            if (isMSBrowser())
            {
                outputFileName = HttpUtility.UrlEncode(fileName);
                encoding = System.Text.Encoding.Default;
            }
            else if (browser.Contains("FIREFOX") == true)
            {
                outputFileName = fileName;
                encoding = System.Text.Encoding.GetEncoding("GB2312");
            }
            else
            {
                outputFileName = HttpUtility.UrlEncode(fileName);
                encoding = System.Text.Encoding.Default;
            }
            return outputFileName;
        }
        #endregion
    }
}