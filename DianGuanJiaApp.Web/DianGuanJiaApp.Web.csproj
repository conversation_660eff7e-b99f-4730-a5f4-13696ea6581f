<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.Default.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{9EFF6008-AB13-4524-97E5-EFD416E72CE9}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DianGuanJiaApp</RootNamespace>
    <AssemblyName>DianGuanJiaApp</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TypeScriptToolsVersion>1.8</TypeScriptToolsVersion>
    <Use64BitIISExpress />
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="aliyun-net-sdk-core">
      <HintPath>..\packages\Build\aliyun-net-sdk-core.dll</HintPath>
    </Reference>
    <Reference Include="AngleSharp, Version=*******, Culture=neutral, PublicKeyToken=e83494dcdc6d31ea, processorArchitecture=MSIL">
      <HintPath>..\packages\AngleSharp.0.9.10\lib\net45\AngleSharp.dll</HintPath>
    </Reference>
    <Reference Include="CSRedisCore, Version=3.8.670.0, Culture=neutral, PublicKeyToken=9aa6a3079358d437, processorArchitecture=MSIL">
      <HintPath>..\packages\CSRedisCore.3.8.670\lib\net45\CSRedisCore.dll</HintPath>
    </Reference>
    <Reference Include="DnsClient, Version=*******, Culture=neutral, PublicKeyToken=4574bb5573c51424, processorArchitecture=MSIL">
      <HintPath>..\packages\DnsClient.1.0.7\lib\net45\DnsClient.dll</HintPath>
    </Reference>
    <Reference Include="EbillSdk">
      <HintPath>..\packages\Build\EbillSdk.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=0.86.0.518, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.0.86.0\lib\20\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AI.Agent.Intercept, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Agent.Intercept.1.2.1\lib\net45\Microsoft.AI.Agent.Intercept.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.DependencyCollector, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.DependencyCollector.2.0.0\lib\net45\Microsoft.AI.DependencyCollector.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.PerfCounterCollector, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.PerfCounterCollector.2.0.0\lib\net45\Microsoft.AI.PerfCounterCollector.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.ServerTelemetryChannel, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.2.0.0\lib\net45\Microsoft.AI.ServerTelemetryChannel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.Web.2.0.0\lib\net45\Microsoft.AI.Web.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AI.WindowsServer, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.WindowsServer.2.0.0\lib\net45\Microsoft.AI.WindowsServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.0.0\lib\net45\Microsoft.ApplicationInsights.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=3.6.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="MongoDB.Bson, Version=2.7.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Bson.2.7.3\lib\net45\MongoDB.Bson.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver, Version=2.7.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Driver.2.7.2\lib\net45\MongoDB.Driver.dll</HintPath>
    </Reference>
    <Reference Include="MongoDB.Driver.Core, Version=2.7.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\MongoDB.Driver.Core.2.7.3\lib\net45\MongoDB.Driver.Core.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=2.4.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=2.4.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=2.4.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=2.4.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.0\lib\net40\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=9.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.0\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.0.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="TopSdk, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Build\TopSdk.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.5.2\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.4.1.9004\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Z.Dapper.Plus">
      <HintPath>..\packages\Build\Z.Dapper.Plus.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Net.Http.Formatting">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.3\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.1\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.1\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.EntityFramework">
      <HintPath>..\packages\Microsoft.AspNet.Identity.EntityFramework.2.2.1\lib\net45\Microsoft.AspNet.Identity.EntityFramework.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Api\ApiBaseController.cs" />
    <Compile Include="Api\OrderApiController.cs" />
    <Compile Include="Api\ScanProductPrintApiController.cs" />
    <Compile Include="Api\ScanSendApiController.cs" />
    <Compile Include="Api\ScanPrintApiController.cs" />
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\DapperMapping.cs" />
    <Compile Include="App_Start\ActionPermissionControlFilter.cs" />
    <Compile Include="App_Start\LogForOperatorFilter.cs" />
    <Compile Include="App_Start\ExceptionFilter.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Controllers\CommonPortalController.cs" />
    <Compile Include="Controllers\GeneralizeIndexController.cs" />
    <Compile Include="Controllers\ExpressApiController.cs" />
    <Compile Include="Controllers\OrderFdsController.cs" />
    <Compile Include="Controllers\EvaluationController.cs" />
    <Compile Include="Controllers\IncludeQRCodeController.cs" />
    <Compile Include="Controllers\SubUserController.cs" />
    <Compile Include="Controllers\ApiController.cs" />
    <Compile Include="Controllers\AutoCommentController.cs" />
    <Compile Include="Controllers\BuyerInfoController.cs" />
    <Compile Include="Controllers\AuthoritycenterController.cs" />
    <Compile Include="Controllers\WDYYController.cs" />
    <Compile Include="Controllers\SharedWaybillAccountCheckController.cs" />
    <Compile Include="Controllers\ShareWayBillAccountController.cs" />
    <Compile Include="Controllers\LogisticQueryController.cs" />
    <Compile Include="Controllers\ParthersController.cs" />
    <Compile Include="Controllers\ScanProductPrintController.cs" />
    <Compile Include="Controllers\SetProductFlagController.cs" />
    <Compile Include="Controllers\SetSortingCodeRuleController.cs" />
    <Compile Include="Controllers\ToolsBaseController.cs" />
    <Compile Include="Controllers\ToolsController.cs" />
    <Compile Include="Controllers\UnAuthController.cs" />
    <Compile Include="Controllers\UpdatePriceController.cs" />
    <Compile Include="Controllers\UserFeedbackController.cs" />
    <Compile Include="Controllers\WsXcxPrintController.cs" />
    <Compile Include="Controllers\WsXcxShowController.cs" />
    <Compile Include="Controllers\XcxBaseController.cs" />
    <Compile Include="Controllers\CustomerAreaTemplateController.cs" />
    <Compile Include="Controllers\XcxCommentController.cs" />
    <Compile Include="Controllers\WxAuthController.cs" />
    <Compile Include="Controllers\XcxManagementController.cs" />
    <Compile Include="Controllers\LogisticSubscribeMsgController.cs" />
    <Compile Include="Controllers\ScanPrintController.cs" />
    <Compile Include="Controllers\ScanSendGoodsController.cs" />
    <Compile Include="Controllers\NaHuoLabelController.cs" />
    <Compile Include="Controllers\ReciverInfoController.cs" />
    <Compile Include="Controllers\MessageController.cs" />
    <Compile Include="Controllers\ShopController.cs" />
    <Compile Include="Controllers\AuthController.cs" />
    <Compile Include="Controllers\CorrelationController.cs" />
    <Compile Include="Controllers\AgentSetController.cs" />
    <Compile Include="Controllers\ExpressBillController.cs" />
    <Compile Include="Controllers\SendGoodTemplateController.cs" />
    <Compile Include="Controllers\FreePrintExpressController.cs" />
    <Compile Include="Controllers\SetMarketStallsController.cs" />
    <Compile Include="Controllers\ImportDeliverController.cs" />
    <Compile Include="Controllers\PreordainController.cs" />
    <Compile Include="Controllers\PrintHistoryController.cs" />
    <Compile Include="Controllers\SellerInfoController.cs" />
    <Compile Include="Controllers\SendHistoryController.cs" />
    <Compile Include="Controllers\SetInfoController.cs" />
    <Compile Include="Controllers\SetOfferTitleController.cs" />
    <Compile Include="Controllers\AccountListController.cs" />
    <Compile Include="Controllers\BaseController.cs" />
    <Compile Include="Controllers\PurchasesController.cs" />
    <Compile Include="Controllers\CommonController.cs" />
    <Compile Include="Controllers\OrderController.cs" />
    <Compile Include="Controllers\TaobaoAuthController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\TemplateSetController.cs" />
    <Compile Include="Controllers\WaybillCodeListController.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Models\IpWhiteModel.cs" />
    <Compile Include="Models\Common.cs" />
    <Compile Include="Models\ConfigModel.cs" />
    <Compile Include="Models\FreePrintExpress\CustomerOrderJsonModel.cs" />
    <Compile Include="Models\NeedTokenAttribute.cs" />
    <Compile Include="Models\IgnoreTokenAttribute.cs" />
    <Compile Include="Models\PhoneLoginModel.cs" />
    <Compile Include="Models\Purchases\PurchaseExportModel.cs" />
    <Compile Include="Models\Purchases\SaveDIYNaHuoLabelModel.cs" />
    <Compile Include="Models\SetNaHuoLabelViewModel.cs" />
    <Compile Include="Models\SetOfferTitle\SetOfferTitleViewModel.cs" />
    <Compile Include="Models\AccountList\AccountViewModel.cs" />
    <Compile Include="Models\AjaxResult.cs" />
    <Compile Include="Models\Purchases\PurchasesViewModel.cs" />
    <Compile Include="Models\CustomerJsonResult.cs" />
    <Compile Include="Models\WaybillCodeListPageViewModel.cs" />
    <Compile Include="Models\WaybillCodeRecycleViewModel.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Content Include="Content\css\artdialog\skins\aero.css" />
    <Content Include="Content\css\artdialog\skins\aero\aero_s.png" />
    <Content Include="Content\css\artdialog\skins\aero\aero_s2.png" />
    <Content Include="Content\css\artdialog\skins\aero\ie6\aui_close.hover.png" />
    <Content Include="Content\css\artdialog\skins\aero\ie6\aui_close.png" />
    <Content Include="Content\css\artdialog\skins\aero\ie6\aui_e.png" />
    <Content Include="Content\css\artdialog\skins\aero\ie6\aui_n.png" />
    <Content Include="Content\css\artdialog\skins\aero\ie6\aui_ne.png" />
    <Content Include="Content\css\artdialog\skins\aero\ie6\aui_nw.png" />
    <Content Include="Content\css\artdialog\skins\aero\ie6\aui_s.png" />
    <Content Include="Content\css\artdialog\skins\aero\ie6\aui_se.png" />
    <Content Include="Content\css\artdialog\skins\aero\ie6\aui_sw.png" />
    <Content Include="Content\css\artdialog\skins\aero\ie6\aui_title_icon.png" />
    <Content Include="Content\css\artdialog\skins\aero\ie6\aui_w.png" />
    <Content Include="Content\css\artdialog\skins\black.css" />
    <Content Include="Content\css\artdialog\skins\black\bg.png" />
    <Content Include="Content\css\artdialog\skins\black\bg2.png" />
    <Content Include="Content\css\artdialog\skins\black\bg_css3.png" />
    <Content Include="Content\css\artdialog\skins\black\bg_css3_2.png" />
    <Content Include="Content\css\artdialog\skins\black\ie6\close.hover.png" />
    <Content Include="Content\css\artdialog\skins\black\ie6\close.png" />
    <Content Include="Content\css\artdialog\skins\black\ie6\e.png" />
    <Content Include="Content\css\artdialog\skins\black\ie6\n.png" />
    <Content Include="Content\css\artdialog\skins\black\ie6\ne.png" />
    <Content Include="Content\css\artdialog\skins\black\ie6\nw.png" />
    <Content Include="Content\css\artdialog\skins\black\ie6\s.png" />
    <Content Include="Content\css\artdialog\skins\black\ie6\se.png" />
    <Content Include="Content\css\artdialog\skins\black\ie6\sw.png" />
    <Content Include="Content\css\artdialog\skins\black\ie6\w.png" />
    <Content Include="Content\css\artdialog\skins\blue.css" />
    <Content Include="Content\css\artdialog\skins\blue\bg.png" />
    <Content Include="Content\css\artdialog\skins\blue\bg2.png" />
    <Content Include="Content\css\artdialog\skins\blue\bg_css3.png" />
    <Content Include="Content\css\artdialog\skins\blue\bg_css3_2.png" />
    <Content Include="Content\css\artdialog\skins\blue\ie6\close.hover.png" />
    <Content Include="Content\css\artdialog\skins\blue\ie6\close.png" />
    <Content Include="Content\css\artdialog\skins\blue\ie6\e.png" />
    <Content Include="Content\css\artdialog\skins\blue\ie6\n.png" />
    <Content Include="Content\css\artdialog\skins\blue\ie6\ne.png" />
    <Content Include="Content\css\artdialog\skins\blue\ie6\nw.png" />
    <Content Include="Content\css\artdialog\skins\blue\ie6\s.png" />
    <Content Include="Content\css\artdialog\skins\blue\ie6\se.png" />
    <Content Include="Content\css\artdialog\skins\blue\ie6\sw.png" />
    <Content Include="Content\css\artdialog\skins\blue\ie6\w.png" />
    <Content Include="Content\css\artdialog\skins\chrome.css" />
    <Content Include="Content\css\artdialog\skins\chrome\border.png" />
    <Content Include="Content\css\artdialog\skins\chrome\chrome_s.png" />
    <Content Include="Content\css\artdialog\skins\default.css" />
    <Content Include="Content\css\artdialog\skins\green.css" />
    <Content Include="Content\css\artdialog\skins\green\bg.png" />
    <Content Include="Content\css\artdialog\skins\green\bg2.png" />
    <Content Include="Content\css\artdialog\skins\green\bg_css3.png" />
    <Content Include="Content\css\artdialog\skins\green\bg_css3_2.png" />
    <Content Include="Content\css\artdialog\skins\green\color_bg.png" />
    <Content Include="Content\css\artdialog\skins\green\ie6\close.hover.png" />
    <Content Include="Content\css\artdialog\skins\green\ie6\close.png" />
    <Content Include="Content\css\artdialog\skins\green\ie6\e.png" />
    <Content Include="Content\css\artdialog\skins\green\ie6\n.png" />
    <Content Include="Content\css\artdialog\skins\green\ie6\ne.png" />
    <Content Include="Content\css\artdialog\skins\green\ie6\nw.png" />
    <Content Include="Content\css\artdialog\skins\green\ie6\s.png" />
    <Content Include="Content\css\artdialog\skins\green\ie6\se.png" />
    <Content Include="Content\css\artdialog\skins\green\ie6\sw.png" />
    <Content Include="Content\css\artdialog\skins\green\ie6\w.png" />
    <Content Include="Content\css\artdialog\skins\icons\error.png" />
    <Content Include="Content\css\artdialog\skins\icons\face-sad.png" />
    <Content Include="Content\css\artdialog\skins\icons\face-smile.png" />
    <Content Include="Content\css\artdialog\skins\icons\loading.gif" />
    <Content Include="Content\css\artdialog\skins\icons\question.png" />
    <Content Include="Content\css\artdialog\skins\icons\succeed.png" />
    <Content Include="Content\css\artdialog\skins\icons\warning.png" />
    <Content Include="Content\css\artdialog\skins\idialog.css" />
    <Content Include="Content\css\artdialog\skins\idialog\idialog_s.png" />
    <Content Include="Content\css\artdialog\skins\idialog\idialog_s2.png" />
    <Content Include="Content\css\artdialog\skins\idialog\ie6\aui_close.hover.png" />
    <Content Include="Content\css\artdialog\skins\idialog\ie6\aui_close.png" />
    <Content Include="Content\css\artdialog\skins\idialog\ie6\aui_e.png" />
    <Content Include="Content\css\artdialog\skins\idialog\ie6\aui_n.png" />
    <Content Include="Content\css\artdialog\skins\idialog\ie6\aui_ne.png" />
    <Content Include="Content\css\artdialog\skins\idialog\ie6\aui_nw.png" />
    <Content Include="Content\css\artdialog\skins\idialog\ie6\aui_s.png" />
    <Content Include="Content\css\artdialog\skins\idialog\ie6\aui_se.png" />
    <Content Include="Content\css\artdialog\skins\idialog\ie6\aui_sw.png" />
    <Content Include="Content\css\artdialog\skins\idialog\ie6\aui_w.png" />
    <Content Include="Content\css\artdialog\skins\loading.gif" />
    <Content Include="Content\css\artdialog\skins\opera.css" />
    <Content Include="Content\css\artdialog\skins\opera\ie6\aui_close.hover.png" />
    <Content Include="Content\css\artdialog\skins\opera\ie6\aui_close.png" />
    <Content Include="Content\css\artdialog\skins\opera\ie6\aui_e.png" />
    <Content Include="Content\css\artdialog\skins\opera\ie6\aui_n.png" />
    <Content Include="Content\css\artdialog\skins\opera\ie6\aui_ne.png" />
    <Content Include="Content\css\artdialog\skins\opera\ie6\aui_nw.png" />
    <Content Include="Content\css\artdialog\skins\opera\ie6\aui_s.png" />
    <Content Include="Content\css\artdialog\skins\opera\ie6\aui_se.png" />
    <Content Include="Content\css\artdialog\skins\opera\ie6\aui_sw.png" />
    <Content Include="Content\css\artdialog\skins\opera\ie6\aui_w.png" />
    <Content Include="Content\css\artdialog\skins\opera\s1.png" />
    <Content Include="Content\css\artdialog\skins\opera\s2.png" />
    <Content Include="Content\css\artdialog\skins\simple.css" />
    <Content Include="Content\css\artdialog\skins\twitter.css" />
    <Content Include="Content\css\correlation\correlation.css" />
    <Content Include="Content\css\daterangepicker\calenderTime.css" />
    <Content Include="Content\css\daterangepicker\daterangepicker.css" />
    <Content Include="Content\css\editemplatecom.css" />
    <Content Include="Content\css\FreePrintList.css" />
    <Content Include="Content\css\printsend.css" />
    <Content Include="Content\css\purchases\purchasePrint.css" />
    <Content Include="Content\css\selectbox2.css" />
    <Content Include="Content\css\selectbox.css" />
    <Content Include="Content\css\SetInfo\SetInfo.css" />
    <Content Include="Content\files\fail.wav" />
    <Content Include="Content\files\finishfile.wav" />
    <Content Include="Content\Images\001.jpg" />
    <Content Include="Content\Images\01.jpg" />
    <Content Include="Content\Images\1.jpg" />
    <Content Include="Content\Images\1.png" />
    <Content Include="Content\Images\13.png" />
    <Content Include="Content\Images\1688login.jpg" />
    <Content Include="Content\Images\2.jpg" />
    <Content Include="Content\Images\222.jpg" />
    <Content Include="Content\Images\3.jpg" />
    <Content Include="Content\Images\333.png" />
    <Content Include="Content\Images\360-1.JPG" />
    <Content Include="Content\Images\360-2.JPG" />
    <Content Include="Content\Images\360-3.JPG" />
    <Content Include="Content\Images\4.jpg" />
    <Content Include="Content\Images\aa20180926174948.png" />
    <Content Include="Content\Images\addong.gif" />
    <Content Include="Content\Images\addWebcat.png" />
    <Content Include="Content\Images\ads-01.png" />
    <Content Include="Content\Images\agent_shilitu.jpg" />
    <Content Include="Content\Images\alibaba.png" />
    <Content Include="Content\Images\allicons2.png" />
    <Content Include="Content\Images\all_parther.png" />
    <Content Include="Content\Images\an1.gif" />
    <Content Include="Content\Images\an2.gif" />
    <Content Include="Content\Images\an56.jpg" />
    <Content Include="Content\Images\an56.png" />
    <Content Include="Content\Images\anli.png" />
    <Content Include="Content\Images\announcement.png" />
    <Content Include="Content\Images\auth001.png" />
    <Content Include="Content\Images\authinfo01.png" />
    <Content Include="Content\Images\authinfo02.png" />
    <Content Include="Content\Images\backgound_icon.png" />
    <Content Include="Content\Images\baidu-1.JPG" />
    <Content Include="Content\Images\baidu-2.JPG" />
    <Content Include="Content\Images\baidu-3.JPG" />
    <Content Include="Content\Images\banner-ad-conterIcon.png" />
    <Content Include="Content\Images\banner-ad-leftIcon.png" />
    <Content Include="Content\Images\Barcode.png" />
    <Content Include="Content\Images\bg_btn_getorder.gif" />
    <Content Include="Content\Images\bg_btn_tempsave.gif" />
    <Content Include="Content\Images\bg_deliveryname.gif" />
    <Content Include="Content\Images\bg_h2.gif" />
    <Content Include="Content\Images\bg_h2_sblock.gif" />
    <Content Include="Content\Images\bg_li_normal.gif" />
    <Content Include="Content\Images\bg_li_star.gif" />
    <Content Include="Content\Images\bg_operation_buttons.gif" />
    <Content Include="Content\Images\bg_root.gif" />
    <Content Include="Content\Images\bg_sprite_count_tab.gif" />
    <Content Include="Content\Images\border.png" />
    <Content Include="Content\Images\border_shawBottom.png" />
    <Content Include="Content\Images\btns.png" />
    <Content Include="Content\Images\btns927.png" />
    <Content Include="Content\Images\btn_operation.png" />
    <Content Include="Content\Images\btn_tempmenu_close.gif" />
    <Content Include="Content\Images\btn_tempmenu_open.gif" />
    <Content Include="Content\css\SendGoodTemplateList\index.css" />
    <Content Include="Content\css\SendGoodTemplateList\setSendList.css" />
    <Content Include="Content\css\SendGoodTemplateList\setSendTemplate.css" />
    <Content Include="Content\Images\bt_gb.gif" />
    <Content Include="Content\Images\bt_save.gif" />
    <Content Include="Content\Images\button.gif" />
    <Content Include="Content\Images\calendar_arrows.png" />
    <Content Include="Content\Images\chrome_s.png" />
    <Content Include="Content\Images\combo_arrow.png" />
    <Content Include="Content\Images\contact.png" />
    <Content Include="Content\Images\contact_me_qr_02.png" />
    <Content Include="Content\Images\container03_tabale_setBtn.png" />
    <Content Include="Content\Images\controlOffset.png" />
    <Content Include="Content\Images\danpai1.jpg" />
    <Content Include="Content\Images\datebox_arrow.png" />
    <Content Include="Content\Images\deledialogTwo.png" />
    <Content Include="Content\Images\deles-icon.png" />
    <Content Include="Content\Images\dgjalogo.png" />
    <Content Include="Content\Images\dgjlogo.png" />
    <Content Include="Content\Images\dialog00.png" />
    <Content Include="Content\Images\dialog01.png" />
    <Content Include="Content\Images\dialog02.png" />
    <Content Include="Content\Images\dialog03.png" />
    <Content Include="Content\Images\dialog_windows_dele.png" />
    <Content Include="Content\Images\dianhau-icon.png" />
    <Content Include="Content\Images\email_icon.png" />
    <Content Include="Content\Images\erliandanTemplate.png" />
    <Content Include="Content\Images\erweima.png" />
    <Content Include="Content\Images\ganthao.gif" />
    <Content Include="Content\Images\greenhand.png" />
    <Content Include="Content\Images\guidance-set.png" />
    <Content Include="Content\Images\guidance-set02.png" />
    <Content Include="Content\Images\guidance-set03.png" />
    <Content Include="Content\Images\headerActiveBackground.png" />
    <Content Include="Content\Images\headerbg.png" />
    <Content Include="Content\Images\icon-hot.png" />
    <Content Include="Content\Images\icon.png" />
    <Content Include="Content\Images\icon_close.png" />
    <Content Include="Content\Images\image001.png" />
    <Content Include="Content\Images\index_btn.png" />
    <Content Include="Content\Images\index_btn2.png" />
    <Content Include="Content\Images\kdexload.gif" />
    <Content Include="Content\Images\lbbeihuodan.png" />
    <Content Include="Content\Images\leftandright.png" />
    <Content Include="Content\Images\leftAndRight02.png" />
    <Content Include="Content\Images\leftAndRights.png" />
    <Content Include="Content\Images\leftNavSmall.png" />
    <Content Include="Content\Images\liPics01.png" />
    <Content Include="Content\Images\loading_small.gif" />
    <Content Include="Content\Images\lock.png" />
    <Content Include="Content\Images\logo - 副本.jpg" />
    <Content Include="Content\Images\logo.jpg" />
    <Content Include="Content\Images\makeShop.jpg" />
    <Content Include="Content\Images\morePic2.png" />
    <Content Include="Content\Images\moreShow01.gif" />
    <Content Include="Content\Images\moreShow02.gif" />
    <Content Include="Content\Images\mx-1.JPG" />
    <Content Include="Content\Images\mx-2.JPG" />
    <Content Include="Content\Images\mx-3.JPG" />
    <Content Include="Content\Images\nahuo-icon.png" />
    <Content Include="Content\Images\nahuo-lie-01_03.png" />
    <Content Include="Content\Images\nahuolabelBackground.png" />
    <Content Include="Content\Images\new01.gif" />
    <Content Include="Content\Images\new02.gif" />
    <Content Include="Content\Images\new03.gif" />
    <Content Include="Content\Images\new04.gif" />
    <Content Include="Content\Images\newConcatIcon.png" />
    <Content Include="Content\Images\newNoviceIntro\purchases\newPurchases-1.png" />
    <Content Include="Content\Images\newNoviceIntro\purchases\newPurchases-2.png" />
    <Content Include="Content\Images\newNoviceIntro\purchases\newPurchases-3.png" />
    <Content Include="Content\Images\newNoviceIntro\purchases\newPurchases-4.png" />
    <Content Include="Content\Images\newNoviceIntro\purchases\newPurchases-5.png" />
    <Content Include="Content\Images\newNoviceIntro\purchases\setPurchasesSet-1.png" />
    <Content Include="Content\Images\newNoviceIntro\purchases\setPurchasesSet-2.png" />
    <Content Include="Content\Images\newNoviceIntro\purchases\setPurchasesSet-3.png" />
    <Content Include="Content\Images\newNoviceIntro\purchases\setPurchasesSet-4.png" />
    <Content Include="Content\Images\newNoviceIntro\purchases\setPurchasesSet-5.png" />
    <Content Include="Content\Images\new_msg.gif" />
    <Content Include="Content\Images\nopic.gif" />
    <Content Include="Content\Images\noviceIntroPic\wuliuyujin-1.png" />
    <Content Include="Content\Images\noviceIntroPic\wuliuyujin-2-1.png" />
    <Content Include="Content\Images\noviceIntroPic\wuliuyujin-2-2.png" />
    <Content Include="Content\Images\noviceIntroPic\wuliuyujin-2-3.png" />
    <Content Include="Content\Images\noviceIntroPic\wuliuyujin-2-4.png" />
    <Content Include="Content\Images\noviceIntroPic\wuliuyujin-2.png" />
    <Content Include="Content\Images\noviceIntroPic\wuliuyujin-3.png" />
    <Content Include="Content\Images\noviceIntroPic\yufahuo-1.png" />
    <Content Include="Content\Images\noviceIntroPic\yufahuo-2.png" />
    <Content Include="Content\Images\Number-1.png" />
    <Content Include="Content\Images\Number-2.png" />
    <Content Include="Content\Images\Number-3.png" />
    <Content Include="Content\Images\onecode.png" />
    <Content Include="Content\Images\order-bg.png" />
    <Content Include="Content\Images\ordergoods_noimage.jpg" />
    <Content Include="Content\Images\orderNumber.png" />
    <Content Include="Content\Images\other.png" />
    <Content Include="Content\Images\pay_02.png" />
    <Content Include="Content\Images\pdd.jpg" />
    <Content Include="Content\Images\pdd_logo_v2.png" />
    <Content Include="Content\Images\phoneLogo.png" />
    <Content Include="Content\Images\piccshow.png" />
    <Content Include="Content\Images\picShow925.gif" />
    <Content Include="Content\Images\picShowonload.gif" />
    <Content Include="Content\Images\pinduoduo.png" />
    <Content Include="Content\Images\pinduoduodj.png" />
    <Content Include="Content\Images\pinduoduo_ys.png" />
    <Content Include="Content\Images\pl.jpg" />
    <Content Include="Content\Images\platform.png" />
    <Content Include="Content\Images\pur-table-operate.png" />
    <Content Include="Content\Images\QQ-1.JPG" />
    <Content Include="Content\Images\QQ-2.JPG" />
    <Content Include="Content\Images\QQ-3.JPG" />
    <Content Include="Content\Images\qqKefu-cion.png" />
    <Content Include="Content\Images\qqkehu.gif" />
    <Content Include="Content\Images\qqkehu02.gif" />
    <Content Include="Content\Images\QRcode.png" />
    <Content Include="Content\Images\qrkmmexport.png" />
    <Content Include="Content\Images\quyeweixiner01.png" />
    <Content Include="Content\Images\quyeweixiner02.png" />
    <Content Include="Content\Images\set-icon02.png" />
    <Content Include="Content\Images\SetSendList_container02_btn.png" />
    <Content Include="Content\Images\SetSendList_title.png" />
    <Content Include="Content\Images\shop-type.png" />
    <Content Include="Content\Images\showmore.png" />
    <Content Include="Content\Images\shuangpai1.jpg" />
    <Content Include="Content\Images\smallPic.png" />
    <Content Include="Content\Images\sogou-1.JPG" />
    <Content Include="Content\Images\sogou-2.JPG" />
    <Content Include="Content\Images\sogou-3.JPG" />
    <Content Include="Content\Images\spinner_arrows.png" />
    <Content Include="Content\Images\sxh001.png" />
    <Content Include="Content\Images\sxs001.png" />
    <Content Include="Content\Images\syncOrder.png" />
    <Content Include="Content\Images\tabale_content_dele.png" />
    <Content Include="Content\Images\tabale_content_mo.png" />
    <Content Include="Content\Images\tabale_content_print.png" />
    <Content Include="Content\Images\table_style_1.png" />
    <Content Include="Content\Images\table_style_2.png" />
    <Content Include="Content\Images\table_style_3.png" />
    <Content Include="Content\Images\table_style_4.png" />
    <Content Include="Content\Images\table_style_5.png" />
    <Content Include="Content\Images\taobao.png" />
    <Content Include="Content\Images\tbauthinfo.jpg" />
    <Content Include="Content\Images\tbauthinfo2.jpg" />
    <Content Include="Content\Images\template_img_icon_01.jpg" />
    <Content Include="Content\Images\template_img_icon_02.jpg" />
    <Content Include="Content\Images\tianmao.png" />
    <Content Include="Content\Images\tiaoxin60043.png" />
    <Content Include="Content\Images\timeLeftRight.png" />
    <Content Include="Content\Images\topAd.png" />
    <Content Include="Content\Images\toubackground27.png" />
    <Content Include="Content\Images\toubackground28.png" />
    <Content Include="Content\Images\trBackground.png" />
    <Content Include="Content\Images\trBackground2.png" />
    <Content Include="Content\Images\UC-1.JPG" />
    <Content Include="Content\Images\UC-2.JPG" />
    <Content Include="Content\Images\UC-3.JPG" />
    <Content Include="Content\Images\upgrade-background01.png" />
    <Content Include="Content\Images\upgrade-background01_03.png" />
    <Content Include="Content\Images\upgrade-background01_04.png" />
    <Content Include="Content\Images\upgrade-background02.png" />
    <Content Include="Content\Images\upgrade-background03_03.png" />
    <Content Include="Content\Images\wanwan-01.png" />
    <Content Include="Content\Images\wanzhan_error.png" />
    <Content Include="Content\Images\waybillcode.png" />
    <Content Include="Content\Images\webOperationSystem01.png" />
    <Content Include="Content\Images\webOperationSystem02.png" />
    <Content Include="Content\Images\welcome-icon.png" />
    <Content Include="Content\Images\wenhao.png" />
    <Content Include="Content\Images\whilebg.png" />
    <Content Include="Content\Images\wt.jpg" />
    <Content Include="Content\Images\wuliuyujin-icon02 .png" />
    <Content Include="Content\Images\xxh001.png" />
    <Content Include="Content\Images\xxs001.png" />
    <Content Include="Content\Images\yantuqrcode.png" />
    <Content Include="Content\Images\yiliandanTemplate.png" />
    <Content Include="Content\Images\yinleftNav.png" />
    <Content Include="Content\Images\yufahuo-2.png" />
    <Content Include="Content\oldCss\AccountList.css" />
    <Content Include="Content\oldCss\AgentSet.css" />
    <Content Include="Content\oldCss\FreePrintExpress.css" />
    <Content Include="Content\oldCss\oldVersion.css" />
    <Content Include="Content\oldCss\Preordain.css" />
    <Content Include="Content\oldCss\Purchases.css" />
    <Content Include="Content\oldCss\SetMarkeStalls.css" />
    <Content Include="Content\oldCss\SetOfferTitle.css" />
    <Content Include="Content\oldCss\Settings.css" />
    <Content Include="Content\oldCss\TemplateSet.css" />
    <Content Include="Content\oldCss\WaybillCodeList.css" />
    <Content Include="Content\oldImages\001.jpg" />
    <Content Include="Content\oldImages\01.jpg" />
    <Content Include="Content\oldImages\1.jpg" />
    <Content Include="Content\oldImages\1.png" />
    <Content Include="Content\oldImages\13.png" />
    <Content Include="Content\oldImages\1688login.jpg" />
    <Content Include="Content\oldImages\2.jpg" />
    <Content Include="Content\oldImages\222.jpg" />
    <Content Include="Content\oldImages\3.jpg" />
    <Content Include="Content\oldImages\333.png" />
    <Content Include="Content\oldImages\360-1.JPG" />
    <Content Include="Content\oldImages\360-2.JPG" />
    <Content Include="Content\oldImages\360-3.JPG" />
    <Content Include="Content\oldImages\4.jpg" />
    <Content Include="Content\oldImages\aa20180926174948.png" />
    <Content Include="Content\oldImages\addong.gif" />
    <Content Include="Content\oldImages\ads-01.png" />
    <Content Include="Content\oldImages\agent_shilitu.jpg" />
    <Content Include="Content\oldImages\alibaba.png" />
    <Content Include="Content\oldImages\allicons.png" />
    <Content Include="Content\oldImages\allicons2.png" />
    <Content Include="Content\oldImages\an1.gif" />
    <Content Include="Content\oldImages\an2.gif" />
    <Content Include="Content\oldImages\an56.jpg" />
    <Content Include="Content\oldImages\an56.png" />
    <Content Include="Content\oldImages\anli.png" />
    <Content Include="Content\oldImages\announcement.png" />
    <Content Include="Content\oldImages\auth001.png" />
    <Content Include="Content\oldImages\authinfo01.png" />
    <Content Include="Content\oldImages\authinfo02.png" />
    <Content Include="Content\oldImages\backgound_icon.png" />
    <Content Include="Content\oldImages\baidu-1.JPG" />
    <Content Include="Content\oldImages\baidu-2.JPG" />
    <Content Include="Content\oldImages\baidu-3.JPG" />
    <Content Include="Content\oldImages\Barcode.png" />
    <Content Include="Content\oldImages\bg_btn_getorder.gif" />
    <Content Include="Content\oldImages\bg_btn_tempsave.gif" />
    <Content Include="Content\oldImages\bg_deliveryname.gif" />
    <Content Include="Content\oldImages\bg_h2.gif" />
    <Content Include="Content\oldImages\bg_h2_sblock.gif" />
    <Content Include="Content\oldImages\bg_li_normal.gif" />
    <Content Include="Content\oldImages\bg_li_star.gif" />
    <Content Include="Content\oldImages\bg_operation_buttons.gif" />
    <Content Include="Content\oldImages\bg_root.gif" />
    <Content Include="Content\oldImages\bg_sprite_count_tab.gif" />
    <Content Include="Content\oldImages\border.png" />
    <Content Include="Content\oldImages\border_shawBottom.png" />
    <Content Include="Content\oldImages\btns.png" />
    <Content Include="Content\oldImages\btns927.png" />
    <Content Include="Content\oldImages\btn_close_s.gif" />
    <Content Include="Content\oldImages\btn_operation.png" />
    <Content Include="Content\oldImages\btn_resize.gif" />
    <Content Include="Content\oldImages\btn_tempmenu_close.gif" />
    <Content Include="Content\oldImages\btn_tempmenu_open.gif" />
    <Content Include="Content\oldImages\bt_close.png" />
    <Content Include="Content\oldImages\bt_gb.gif" />
    <Content Include="Content\oldImages\bt_save.gif" />
    <Content Include="Content\oldImages\button.gif" />
    <Content Include="Content\oldImages\calendar_arrows.png" />
    <Content Include="Content\oldImages\chrome_s.png" />
    <Content Include="Content\oldImages\combo_arrow.png" />
    <Content Include="Content\oldImages\comment.png" />
    <Content Include="Content\oldImages\contact.png" />
    <Content Include="Content\oldImages\container03_tabale_setBtn.png" />
    <Content Include="Content\oldImages\danpai1.jpg" />
    <Content Include="Content\oldImages\datebox_arrow.png" />
    <Content Include="Content\oldImages\deledialogTwo.png" />
    <Content Include="Content\oldImages\dgjalogo.png" />
    <Content Include="Content\oldImages\dgjlogo.png" />
    <Content Include="Content\oldImages\dialog00.png" />
    <Content Include="Content\oldImages\dialog01.png" />
    <Content Include="Content\oldImages\dialog02.png" />
    <Content Include="Content\oldImages\dialog03.png" />
    <Content Include="Content\oldImages\dialog_windows_dele.png" />
    <Content Include="Content\oldImages\dianhau-icon.png" />
    <Content Include="Content\oldImages\dindinerwema2.jpg" />
    <Content Include="Content\oldImages\d_fh.gif" />
    <Content Include="Content\oldImages\d_qx.gif" />
    <Content Include="Content\oldImages\d_xg.gif" />
    <Content Include="Content\oldImages\email_icon.png" />
    <Content Include="Content\oldImages\ganthao.gif" />
    <Content Include="Content\oldImages\genavigation.png" />
    <Content Include="Content\oldImages\greenhand.png" />
    <Content Include="Content\oldImages\headerActiveBackground.png" />
    <Content Include="Content\oldImages\headerbg.png" />
    <Content Include="Content\oldImages\icon_close.png" />
    <Content Include="Content\oldImages\image001.png" />
    <Content Include="Content\oldImages\index_btn.png" />
    <Content Include="Content\oldImages\index_btn2.png" />
    <Content Include="Content\oldImages\kdexload.gif" />
    <Content Include="Content\oldImages\leftandright.png" />
    <Content Include="Content\oldImages\leftAndRight02.png" />
    <Content Include="Content\oldImages\leftAndRights.png" />
    <Content Include="Content\oldImages\leftNavSmall.png" />
    <Content Include="Content\oldImages\liPics01.png" />
    <Content Include="Content\oldImages\loading.gif" />
    <Content Include="Content\oldImages\loading_small.gif" />
    <Content Include="Content\oldImages\logo - 副本.jpg" />
    <Content Include="Content\oldImages\logo.jpg" />
    <Content Include="Content\oldImages\makeShop.jpg" />
    <Content Include="Content\oldImages\morePic2.png" />
    <Content Include="Content\oldImages\moreShow01.gif" />
    <Content Include="Content\oldImages\moreShow02.gif" />
    <Content Include="Content\oldImages\mx-1.JPG" />
    <Content Include="Content\oldImages\mx-2.JPG" />
    <Content Include="Content\oldImages\mx-3.JPG" />
    <Content Include="Content\oldImages\new01.gif" />
    <Content Include="Content\oldImages\new02.gif" />
    <Content Include="Content\oldImages\new03.gif" />
    <Content Include="Content\oldImages\new04.gif" />
    <Content Include="Content\oldImages\new_msg.gif" />
    <Content Include="Content\oldImages\nopic.gif" />
    <Content Include="Content\oldImages\Number-1.png" />
    <Content Include="Content\oldImages\Number-2.png" />
    <Content Include="Content\oldImages\Number-3.png" />
    <Content Include="Content\oldImages\onecode.png" />
    <Content Include="Content\oldImages\order-bg.png" />
    <Content Include="Content\oldImages\ordergoods_noimage.jpg" />
    <Content Include="Content\oldImages\orderNumber.png" />
    <Content Include="Content\oldImages\other.png" />
    <Content Include="Content\oldImages\pay_02.png" />
    <Content Include="Content\oldImages\pdd.jpg" />
    <Content Include="Content\oldImages\pdd_logo_v2.png" />
    <Content Include="Content\oldImages\piccshow.png" />
    <Content Include="Content\oldImages\picShow925.gif" />
    <Content Include="Content\oldImages\picShowonload.gif" />
    <Content Include="Content\oldImages\pinduoduo.png" />
    <Content Include="Content\oldImages\pinduoduodj.png" />
    <Content Include="Content\oldImages\pinduoduo_ys.png" />
    <Content Include="Content\oldImages\pl.jpg" />
    <Content Include="Content\oldImages\platform.png" />
    <Content Include="Content\oldImages\print_loading.gif" />
    <Content Include="Content\oldImages\produst01.png" />
    <Content Include="Content\oldImages\produst02.png" />
    <Content Include="Content\oldImages\produst03.png" />
    <Content Include="Content\oldImages\produst04.png" />
    <Content Include="Content\oldImages\QQ-1.JPG" />
    <Content Include="Content\oldImages\QQ-2.JPG" />
    <Content Include="Content\oldImages\QQ-3.JPG" />
    <Content Include="Content\oldImages\qqkehu.gif" />
    <Content Include="Content\oldImages\QRcode.png" />
    <Content Include="Content\oldImages\remark_icon.png" />
    <Content Include="Content\oldImages\SetSendList_container02_btn.png" />
    <Content Include="Content\oldImages\SetSendList_title.png" />
    <Content Include="Content\oldImages\showmore.png" />
    <Content Include="Content\oldImages\shuangpai1.jpg" />
    <Content Include="Content\oldImages\smallPic.png" />
    <Content Include="Content\oldImages\sogou-1.JPG" />
    <Content Include="Content\oldImages\sogou-2.JPG" />
    <Content Include="Content\oldImages\sogou-3.JPG" />
    <Content Include="Content\oldImages\spinner_arrows.png" />
    <Content Include="Content\oldImages\suotou.png" />
    <Content Include="Content\oldImages\sxh001.png" />
    <Content Include="Content\oldImages\sxs001.png" />
    <Content Include="Content\oldImages\tabale_content_dele.png" />
    <Content Include="Content\oldImages\tabale_content_mo.png" />
    <Content Include="Content\oldImages\tabale_content_print.png" />
    <Content Include="Content\oldImages\taobao.png" />
    <Content Include="Content\oldImages\tbauthinfo.jpg" />
    <Content Include="Content\oldImages\tbauthinfo2.jpg" />
    <Content Include="Content\oldImages\tbg.gif" />
    <Content Include="Content\oldImages\template_img_icon_01.jpg" />
    <Content Include="Content\oldImages\template_img_icon_02.jpg" />
    <Content Include="Content\oldImages\tfans.jpg" />
    <Content Include="Content\oldImages\tianmao.png" />
    <Content Include="Content\oldImages\tiaoxin60043.png" />
    <Content Include="Content\oldImages\timeLeftRight.png" />
    <Content Include="Content\oldImages\topAd.png" />
    <Content Include="Content\oldImages\toubackground27.png" />
    <Content Include="Content\oldImages\toubackground28.png" />
    <Content Include="Content\oldImages\trBackground.png" />
    <Content Include="Content\oldImages\trBackground2.png" />
    <Content Include="Content\oldImages\UC-1.JPG" />
    <Content Include="Content\oldImages\UC-2.JPG" />
    <Content Include="Content\oldImages\UC-3.JPG" />
    <Content Include="Content\oldImages\wanwanicon.png" />
    <Content Include="Content\oldImages\waybillcode.png" />
    <Content Include="Content\oldImages\whilebg.png" />
    <Content Include="Content\oldImages\wt.jpg" />
    <Content Include="Content\oldImages\xxh001.png" />
    <Content Include="Content\oldImages\xxs001.png" />
    <Content Include="Content\oldImages\yantuqrcode.png" />
    <Content Include="Content\oldImages\yinleftNav.png" />
    <Content Include="Content\oldJs\FreePrintExpress.js" />
    <Content Include="Content\oldJs\oldLayout.js" />
    <Content Include="Content\oldJs\Preordain.js" />
    <Content Include="Content\oldJs\Purchases.js" />
    <Content Include="Content\oldJs\WaybillCodeList.js" />
    <Content Include="Files\CustomerTemplates\CaiNiao_CustomerTemplate_100x180.xml" />
    <Content Include="Files\CustomerTemplates\CaiNiao_CustomerTemplate_76x130.xml" />
    <Content Include="Files\CustomerTemplates\Toutiao_CustomerTemplate_100x180.xml" />
    <Content Include="Files\CustomerTemplates\Pinduoduo_CustomerTemplate_100x180.xml" />
    <Content Include="Files\CustomerTemplates\Pinduoduo_CustomerTemplate_76x130.xml" />
    <Content Include="Files\CustomerTemplates\Toutiao_CustomerTemplate_76x130.xml" />
    <Content Include="Files\Templates\快递对账模板.xlsx" />
    <Content Include="Files\Temp\testCustomerArea.xml" />
    <Content Include="ILLink\ILLink.Descriptors.LibraryBuild.xml" />
    <Content Include="loadimages\imgs\danpai1.jpg" />
    <Content Include="loadimages\imgs\shuangpai1.jpg" />
    <Content Include="robots.txt" />
    <Content Include="Scripts\AddressSpliter.js" />
    <Content Include="Scripts\arealist.js" />
    <Content Include="Scripts\FreePrintExpress\SinglePageJs.js" />
    <Content Include="Scripts\GeneralizeIndex\GeneralizeIndexModule.js" />
    <Content Include="Scripts\ImportDeliver\ImportDeliverModule.js" />
    <Content Include="Scripts\Layout.js" />
    <Content Include="Scripts\ajaxfileupload.js" />
    <Content Include="Scripts\artDialog.js" />
    <Content Include="Scripts\BuyerInfoSet\BuyerInfoManagerModule.js" />
    <Content Include="Scripts\FreePrintExpress\BatchImportModule.js" />
    <Content Include="Scripts\headerCommon.js" />
    <Content Include="Scripts\layui\css\layui.css" />
    <Content Include="Scripts\layui\css\layui.mobile.css" />
    <Content Include="Scripts\layui\css\modules\code.css" />
    <Content Include="Scripts\layui\css\modules\laydate\default\laydate.css" />
    <Content Include="Scripts\layui\css\modules\layer\default\icon-ext.png" />
    <Content Include="Scripts\layui\css\modules\layer\default\icon.png" />
    <Content Include="Scripts\layui\css\modules\layer\default\layer.css" />
    <Content Include="Scripts\layui\css\modules\layer\default\loading-0.gif" />
    <Content Include="Scripts\layui\css\modules\layer\default\loading-1.gif" />
    <Content Include="Scripts\layui\css\modules\layer\default\loading-2.gif" />
    <Content Include="Scripts\layui\font\iconfont.svg" />
    <Content Include="Scripts\layui\images\face\0.gif" />
    <Content Include="Scripts\layui\images\face\1.gif" />
    <Content Include="Scripts\layui\images\face\10.gif" />
    <Content Include="Scripts\layui\images\face\11.gif" />
    <Content Include="Scripts\layui\images\face\12.gif" />
    <Content Include="Scripts\layui\images\face\13.gif" />
    <Content Include="Scripts\layui\images\face\14.gif" />
    <Content Include="Scripts\layui\images\face\15.gif" />
    <Content Include="Scripts\layui\images\face\16.gif" />
    <Content Include="Scripts\layui\images\face\17.gif" />
    <Content Include="Scripts\layui\images\face\18.gif" />
    <Content Include="Scripts\layui\images\face\19.gif" />
    <Content Include="Scripts\layui\images\face\2.gif" />
    <Content Include="Scripts\layui\images\face\20.gif" />
    <Content Include="Scripts\layui\images\face\21.gif" />
    <Content Include="Scripts\layui\images\face\22.gif" />
    <Content Include="Scripts\layui\images\face\23.gif" />
    <Content Include="Scripts\layui\images\face\24.gif" />
    <Content Include="Scripts\layui\images\face\25.gif" />
    <Content Include="Scripts\layui\images\face\26.gif" />
    <Content Include="Scripts\layui\images\face\27.gif" />
    <Content Include="Scripts\layui\images\face\28.gif" />
    <Content Include="Scripts\layui\images\face\29.gif" />
    <Content Include="Scripts\layui\images\face\3.gif" />
    <Content Include="Scripts\layui\images\face\30.gif" />
    <Content Include="Scripts\layui\images\face\31.gif" />
    <Content Include="Scripts\layui\images\face\32.gif" />
    <Content Include="Scripts\layui\images\face\33.gif" />
    <Content Include="Scripts\layui\images\face\34.gif" />
    <Content Include="Scripts\layui\images\face\35.gif" />
    <Content Include="Scripts\layui\images\face\36.gif" />
    <Content Include="Scripts\layui\images\face\37.gif" />
    <Content Include="Scripts\layui\images\face\38.gif" />
    <Content Include="Scripts\layui\images\face\39.gif" />
    <Content Include="Scripts\layui\images\face\4.gif" />
    <Content Include="Scripts\layui\images\face\40.gif" />
    <Content Include="Scripts\layui\images\face\41.gif" />
    <Content Include="Scripts\layui\images\face\42.gif" />
    <Content Include="Scripts\layui\images\face\43.gif" />
    <Content Include="Scripts\layui\images\face\44.gif" />
    <Content Include="Scripts\layui\images\face\45.gif" />
    <Content Include="Scripts\layui\images\face\46.gif" />
    <Content Include="Scripts\layui\images\face\47.gif" />
    <Content Include="Scripts\layui\images\face\48.gif" />
    <Content Include="Scripts\layui\images\face\49.gif" />
    <Content Include="Scripts\layui\images\face\5.gif" />
    <Content Include="Scripts\layui\images\face\50.gif" />
    <Content Include="Scripts\layui\images\face\51.gif" />
    <Content Include="Scripts\layui\images\face\52.gif" />
    <Content Include="Scripts\layui\images\face\53.gif" />
    <Content Include="Scripts\layui\images\face\54.gif" />
    <Content Include="Scripts\layui\images\face\55.gif" />
    <Content Include="Scripts\layui\images\face\56.gif" />
    <Content Include="Scripts\layui\images\face\57.gif" />
    <Content Include="Scripts\layui\images\face\58.gif" />
    <Content Include="Scripts\layui\images\face\59.gif" />
    <Content Include="Scripts\layui\images\face\6.gif" />
    <Content Include="Scripts\layui\images\face\60.gif" />
    <Content Include="Scripts\layui\images\face\61.gif" />
    <Content Include="Scripts\layui\images\face\62.gif" />
    <Content Include="Scripts\layui\images\face\63.gif" />
    <Content Include="Scripts\layui\images\face\64.gif" />
    <Content Include="Scripts\layui\images\face\65.gif" />
    <Content Include="Scripts\layui\images\face\66.gif" />
    <Content Include="Scripts\layui\images\face\67.gif" />
    <Content Include="Scripts\layui\images\face\68.gif" />
    <Content Include="Scripts\layui\images\face\69.gif" />
    <Content Include="Scripts\layui\images\face\7.gif" />
    <Content Include="Scripts\layui\images\face\70.gif" />
    <Content Include="Scripts\layui\images\face\71.gif" />
    <Content Include="Scripts\layui\images\face\8.gif" />
    <Content Include="Scripts\layui\images\face\9.gif" />
    <Content Include="Scripts\layui\layui.all.js" />
    <Content Include="Scripts\layui\layui.js" />
    <Content Include="Scripts\layui\lay\modules\carousel.js" />
    <Content Include="Scripts\layui\lay\modules\code.js" />
    <Content Include="Scripts\layui\lay\modules\colorpicker.js" />
    <Content Include="Scripts\layui\lay\modules\element.js" />
    <Content Include="Scripts\layui\lay\modules\flow.js" />
    <Content Include="Scripts\layui\lay\modules\form.js" />
    <Content Include="Scripts\layui\lay\modules\jquery.js" />
    <Content Include="Scripts\layui\lay\modules\laydate.js" />
    <Content Include="Scripts\layui\lay\modules\layedit.js" />
    <Content Include="Scripts\layui\lay\modules\layer.js" />
    <Content Include="Scripts\layui\lay\modules\laypage.js" />
    <Content Include="Scripts\layui\lay\modules\laytpl.js" />
    <Content Include="Scripts\layui\lay\modules\mobile.js" />
    <Content Include="Scripts\layui\lay\modules\rate.js" />
    <Content Include="Scripts\layui\lay\modules\slider.js" />
    <Content Include="Scripts\layui\lay\modules\table.js" />
    <Content Include="Scripts\layui\lay\modules\tree.js" />
    <Content Include="Scripts\layui\lay\modules\upload.js" />
    <Content Include="Scripts\layui\lay\modules\util.js" />
    <Content Include="Scripts\noviceIntro\noviceIntro.css" />
    <Content Include="Scripts\noviceIntro\noviceIntro.js" />
    <Content Include="Scripts\noviceIntro\noviceIntro_step.png" />
    <Content Include="Scripts\noviceIntro\noviceIntro_step_right.png" />
    <Content Include="Scripts\orderlist\AdvanceConditionModule.js" />
    <Content Include="Scripts\LogisticQuery\LogisticQueryModule.js" />
    <Content Include="Scripts\orderlist\OrderPrintModule.js" />
    <Content Include="Scripts\orderlist\setMoreFunSetMoudle.js" />
    <Content Include="Scripts\echo.js" />
    <Content Include="Scripts\jquery.lazyload.min.js" />
    <Content Include="Scripts\jquery.slimscroll.min.js" />
    <Content Include="Scripts\PinduoduoPrinter.js" />
    <Content Include="Scripts\daterangepicker\calenderTime.js" />
    <Content Include="Scripts\daterangepicker\daterangepicker.js" />
    <Content Include="Scripts\daterangepicker\jquery.min.js" />
    <Content Include="Scripts\daterangepicker\moment.js" />
    <Content Include="Scripts\daterangepicker\moment.min.js" />
    <Content Include="Scripts\ExpressBill\WaybillCodeCheckModule.js" />
    <Content Include="Scripts\FreePrintExpress\FreePrintSingleModule.js" />
    <Content Include="Scripts\FreePrintExpress\SelectProductModule.js" />
    <Content Include="Scripts\FreePrintExpress\SenderOrReciverChoseModule.js" />
    <Content Include="Scripts\jquery-1.12.4.js" />
    <Content Include="Scripts\jquery-1.12.4.min.js" />
    <Content Include="Scripts\jquery-3.3.1.min.js" />
    <Content Include="Scripts\jquery.cookie.js" />
    <Content Include="Scripts\jquery.form.js" />
    <Content Include="Scripts\NaHuoLabel.js" />
    <Content Include="Scripts\orderlist\CustomerValidModule.js" />
    <Content Include="Scripts\orderlist\ScanPrints.js" />
    <Content Include="Scripts\orderlist\setMoreFunSetMoudle.js" />
    <Content Include="Scripts\orderlist\ShopCheckBoxModule.js" />
    <Content Include="Scripts\orderlist\ExportOrderModule.js" />
    <Content Include="Scripts\orderlist\mergerOrderModule.js" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\css\common.css" />
    <Content Include="Content\css\orderlist\orderList.css" />
    <Content Include="Content\css\setBase.css" />
    <Content Include="Content\html\changemodepage.html" />
    <Content Include="Content\Images\allicons.png" />
    <Content Include="Content\Images\btn_close_s.gif" />
    <Content Include="Content\Images\btn_resize.gif" />
    <Content Include="Content\Images\genavigation.png" />
    <Content Include="Content\Images\loading.gif" />
    <Content Include="Content\Images\produst01.png" />
    <Content Include="Content\Images\produst02.png" />
    <Content Include="Content\Images\produst03.png" />
    <Content Include="Content\Images\produst04.png" />
    <Content Include="Content\Images\remark_icon.png" />
    <Content Include="Content\Images\suotou.png" />
    <Content Include="Content\Images\tfans.jpg" />
    <Content Include="Content\Images\wanwanicon.png" />
    <Content Include="Content\old\AllPagesStyle.css" />
    <Content Include="Content\old\main.css" />
    <Content Include="Content\css\purchases\purchases.css" />
    <Content Include="Content\setBase.css" />
    <Content Include="favicon.ico" />
    <Content Include="Global.asax" />
    <None Include="Config\AppSettings.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </None>
    <None Include="Config\ConnectionStrings.config">
      <SubType>Designer</SubType>
    </None>
    <Content Include="Files\SellerInfo\发件人模板.xls" />
    <Content Include="Files\FreePrint\自由打印-批量导入模板.xls" />
    <Content Include="Files\BuyerInfo\收件人模板.xls" />
    <Content Include="Files\FreePrint\ORDER201610.xls" />
    <Content Include="Files\FreePrint\KS_自由打印-批量导入模板.xls" />
    <Content Include="Files\ImportDeliver\导入发货模板.xls" />
    <Content Include="Files\FreePrint\批量导入模板-精简版.xls" />
    <Content Include="Files\FreePrint\批量导入模板-详细版.xls" />
    <Content Include="Files\ImportSend\批量导入发货模板.xls" />
    <None Include="Properties\PublishProfiles\dev.pubxml" />
    <None Include="Properties\PublishProfiles\KuaiDiNiao_Publish.pubxml" />
    <None Include="Properties\PublishProfiles\Taobao_Publish.pubxml" />
    <None Include="Scripts\jquery-1.10.2.intellisense.js" />
    <Content Include="Content\Images\allicons.png" />
    <Content Include="Content\Images\genavigation.png" />
    <Content Include="Content\Images\produst01.png" />
    <Content Include="Content\Images\produst02.png" />
    <Content Include="Content\Images\produst03.png" />
    <Content Include="Content\Images\produst04.png" />
    <Content Include="Content\Images\remark_icon.png" />
    <Content Include="Scripts\alalog.js" />
    <Content Include="loadimages\bncsm\201305071638348161.jpg" />
    <Content Include="loadimages\bncsm\201305071647296427.jpg" />
    <Content Include="loadimages\bncsm\201305071656413853.jpg" />
    <Content Include="loadimages\bncsm\201305071659422966.jpg" />
    <Content Include="loadimages\bncsm\201305071705373049.jpg" />
    <Content Include="loadimages\bncsm\201305071710146158.jpg" />
    <Content Include="loadimages\bncsm\201305071714575930.jpg" />
    <Content Include="loadimages\bncsm\201305071719562351.jpg" />
    <Content Include="loadimages\bncsm\201305071736397615.jpg" />
    <Content Include="loadimages\bncsm\201305071746078050.jpg" />
    <Content Include="loadimages\bncsm\201305091817095706.jpg" />
    <Content Include="loadimages\bncsm\201305201111067327.jpg" />
    <Content Include="loadimages\bncsm\201305201519032293.jpg" />
    <Content Include="loadimages\bncsm\201305201519154690.jpg" />
    <Content Include="loadimages\bncsm\201305201523544469.jpg" />
    <Content Include="loadimages\bncsm\201305201529174584.jpg" />
    <Content Include="loadimages\bncsm\201305201533370682.jpg" />
    <Content Include="loadimages\bncsm\201305201537193479.jpg" />
    <Content Include="loadimages\bncsm\201305221124054435.jpg" />
    <Content Include="loadimages\bncsm\201305221124260597.jpg" />
    <Content Include="loadimages\bncsm\201305301735067991.jpg" />
    <Content Include="loadimages\bncsm\201402261404262985.jpg" />
    <Content Include="loadimages\bncsm\201402261404471867.jpg" />
    <Content Include="loadimages\bncsm\201402281936449871.jpg" />
    <Content Include="loadimages\bncsm\201402282132170808.jpg" />
    <Content Include="loadimages\bncsm\201402282235339715.jpg" />
    <Content Include="loadimages\bncsm\201402282240086277.jpg" />
    <Content Include="loadimages\bncsm\201402282307581079.jpg" />
    <Content Include="loadimages\bncsm\201405202207226345.jpg" />
    <Content Include="loadimages\bncsm\201407271455085562.jpg" />
    <Content Include="loadimages\kh_image_505.jpg" />
    <Content Include="loadimages\kh_image_505_conew1.jpg" />
    <Content Include="loadimages\ZH_image_501.jpg" />
    <Content Include="loadimages\ZH_image_501_conew1.jpg" />
    <Content Include="loadimages\ZH_image_502.jpg" />
    <Content Include="loadimages\ZH_image_502_conew1.jpg" />
    <Content Include="Scripts\dragModule.js" />
    <Content Include="Scripts\CaiNiaoPrinter.js" />
    <Content Include="Scripts\laydate\laydate.js" />
    <Content Include="Scripts\laydate\theme\default\font\iconfont.svg" />
    <Content Include="Scripts\laydate\theme\default\laydate.css" />
    <Content Include="Scripts\laypage\laypage.css" />
    <Content Include="Scripts\laypage\laypage.js" />
    <Content Include="Scripts\orderlist\AddTemplateInOrderListModule.js" />
    <Content Include="Scripts\orderlist\KeywordFilterRepalceModule.js" />
    <Content Include="Scripts\orderlist\ModifyOrderInfoModule.js" />
    <Content Include="Scripts\orderlist\orderlist.js" />
    <Content Include="Scripts\orderlist\ScanSendGood.js" />
    <Content Include="Scripts\Preordain\PreordainModule.js" />
    <Content Include="Scripts\PrintHistory\PrintHistoryModule.js" />
    <Content Include="Scripts\PrintPreviewModel.js" />
    <Content Include="Scripts\progress\ProgressModule.js" />
    <Content Include="Scripts\Purchase\NahuoLabelModule.js" />
    <Content Include="Scripts\Purchase\newpurchases.js" />
    <Content Include="Scripts\Purchase\PurchaseModule.js" />
    <Content Include="Scripts\Purchase\purchasesSet.js" />
    <Content Include="Scripts\qrcode.js" />
    <Content Include="Scripts\raphael.min.js" />
    <Content Include="Scripts\ScanProductPrint\ScanProductPrint.js" />
    <Content Include="Scripts\select2\select2-spinner.gif" />
    <Content Include="Scripts\select2\select2.css" />
    <Content Include="Scripts\select2\select2.js" />
    <Content Include="Scripts\select2\select2.png" />
    <Content Include="Scripts\select2\select2_locale_zh-CN.js" />
    <Content Include="Scripts\selectbox\selectbox2.js" />
    <Content Include="Scripts\selectbox\selectbox.js" />
    <Content Include="Scripts\SellerInfoSet\SellerInfoManagerModule.js" />
    <Content Include="Scripts\SendGoodTemplate.js" />
    <Content Include="Scripts\FreePrintOrderOperation.js" />
    <Content Include="Scripts\ScanSendLogistic.js" />
    <Content Include="Scripts\SendLogistic.js" />
    <Content Include="Scripts\SendHistory\SendHistoryModule.js" />
    <Content Include="Scripts\sendTemplate\artDialog.js" />
    <Content Include="Scripts\sendTemplate\artDialog.plugins.min.js" />
    <Content Include="Scripts\sendTemplate\json2.js" />
    <Content Include="Scripts\sendTemplate\setSendList.js" />
    <Content Include="Scripts\sendTemplate\setSendTemplate.js" />
    <Content Include="Scripts\sendTemplate\setSendTemplate2.js" />
    <Content Include="Scripts\sendTemplate\Uploader.js" />
    <Content Include="Scripts\SharedWaybillAccountCheck\sharedWaybillAccountCheckModule.js" />
    <Content Include="Scripts\ShareWayBillAccount\shareWayBillAccountModule.js" />
    <Content Include="Scripts\SubUser\SubUserModule.js" />
    <Content Include="Scripts\TemplateSet\AddTemplateModule.js" />
    <Content Include="Scripts\ExpressPrinter.js" />
    <Content Include="Scripts\jquery-1.10.2.js" />
    <Content Include="Scripts\jquery-1.10.2.min.js" />
    <Content Include="Scripts\jquery.min.js" />
    <Content Include="Scripts\jsrender\jsrender.js" />
    <Content Include="Scripts\jsrender\jsrender.min.js" />
    <Content Include="Scripts\layer\layer.js" />
    <Content Include="Scripts\layer\mobile\layer.js" />
    <Content Include="Scripts\layer\mobile\need\layer.css" />
    <Content Include="Scripts\layer\skin\of\style.css" />
    <Content Include="Scripts\layer\theme\default\icon-ext.png" />
    <Content Include="Scripts\layer\theme\default\icon.png" />
    <Content Include="Scripts\layer\theme\default\layer.css" />
    <Content Include="Scripts\layer\theme\default\loading-0.gif" />
    <Content Include="Scripts\layer\theme\default\loading-1.gif" />
    <Content Include="Scripts\layer\theme\default\loading-2.gif" />
    <Content Include="Scripts\LodopPrinter.js" />
    <Content Include="Scripts\orderlist\OrderTableBuilder.js" />
    <Content Include="Scripts\print\LodopFuncs.js" />
    <Content Include="Scripts\TemplateSet\EditTemplateModule.js" />
    <Content Include="Scripts\TemplateSet\EditSiteTemplateModule.js" />
    <Content Include="Scripts\TemplateSet\EditWaybillTemplateModule.js" />
    <Content Include="Scripts\TemplateSet\EditTraditionTemplateModule.js" />
    <Content Include="Scripts\TemplateSet\TemplateListModule.js" />
    <Content Include="Scripts\TemplateSet\TemplateSetCommonModule.js" />
    <Content Include="Scripts\Tools\ExecSqlModule.js" />
    <Content Include="Scripts\UpdatePrice\UpdatePrice.js" />
    <Content Include="Scripts\WaybillCodeList\WaybillCodeListModule.js" />
    <Content Include="Scripts\WxXcx\AddXcxAcount.js" />
    <Content Include="Scripts\qrcode.min.js" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Shared\500.cshtml" />
    <Content Include="Views\TaobaoAuth\Index.cshtml" />
    <Content Include="Views\TemplateSet\Index.cshtml" />
    <Content Include="Views\TemplateSet\AddTemplate.cshtml" />
    <Content Include="Views\Purchases\Index.cshtml" />
    <Content Include="Views\AccountList\Index.cshtml" />
    <Content Include="Views\TemplateSet\EditTraditionTemplate.cshtml" />
    <Content Include="Views\TemplateSet\EditSiteTemplate.cshtml" />
    <Content Include="Views\TemplateSet\EditWaybillTemplate.cshtml" />
    <Content Include="Views\Order\AddTemplateInOrderList.cshtml" />
    <Content Include="Views\Common\PartialPrintTemplate.cshtml" />
    <Content Include="Views\SetOfferTitle\Index.cshtml" />
    <Content Include="Views\SellerInfo\Index.cshtml" />
    <Content Include="Views\Common\leftNav.cshtml" />
    <Content Include="Views\SellerInfo\AddSellerInfo.cshtml" />
    <Content Include="Views\Order\OrderCategoryList.cshtml" />
    <Content Include="Views\SetInfo\Index.cshtml" />
    <Content Include="Views\Correlation\Index.cshtml" />
    <Content Include="Views\Order\Index.cshtml" />
    <Content Include="Views\AgentSet\Index.cshtml" />
    <Content Include="Views\PrintHistory\Index.cshtml" />
    <Content Include="Views\SetOfferTitle\Qurey.cshtml" />
    <Content Include="Views\SendHistory\Index.cshtml" />
    <Content Include="Views\Preordain\Index.cshtml" />
    <Content Include="Views\Common\moreFunLeftNav.cshtml" />
    <Content Include="Scripts\laydate\theme\default\font\iconfont.eot" />
    <Content Include="Scripts\laydate\theme\default\font\iconfont.ttf" />
    <Content Include="Scripts\laydate\theme\default\font\iconfont.woff" />
    <Content Include="Views\Purchases\RenderPurchaseList.cshtml" />
    <Content Include="Views\ExpressBill\Index.cshtml" />
    <Content Include="Views\ImportDeliver\Index.cshtml" />
    <Content Include="Views\SetMarketStalls\Index.cshtml" />
    <Content Include="Views\Common\ChoseProvincePartialView.cshtml" />
    <Content Include="Views\FreePrintExpress\Index.cshtml" />
    <Content Include="Views\Order\FreePrintList.cshtml" />
    <Content Include="Views\Common\ImportFreePrint.cshtml" />
    <Content Include="Views\SetMarketStalls\Qurey.cshtml" />
    <Content Include="Views\FreePrintExpress\Header.cshtml" />
    <Content Include="Views\FreePrintExpress\PrintPartialView.cshtml" />
    <Content Include="Views\Order\OrderFooter.cshtml" />
    <Content Include="Views\SendGoodTemplate\Edit.cshtml" />
    <Content Include="Views\SendGoodTemplate\Index.cshtml" />
    <Content Include="Views\Order\SearchCondition.cshtml" />
    <Content Include="Views\Order\FreePrintSearchCondition.cshtml" />
    <Content Include="Views\Auth\Empty.cshtml" />
    <Content Include="Views\Purchases\SearchCondition.cshtml" />
    <Content Include="Views\FreePrintExpress\_SelectProductView.cshtml" />
    <Content Include="Views\SendGoodTemplate\OldVersion.cshtml" />
    <Content Include="Views\WaybillCodeList\Index.cshtml" />
    <Content Include="Views\Common\PrintPreViewPartialView.cshtml" />
    <Content Include="Views\AutoComment\Index.cshtml" />
    <Content Include="Views\Common\PrintCommonDiv.cshtml" />
    <Content Include="Views\XcxManagement\Index.cshtml" />
    <Content Include="Views\Order\PrintContentSetting.cshtml" />
    <Content Include="Views\Purchases\Test.cshtml" />
    <Content Include="Views\Auth\Tool.cshtml" />
    <Content Include="Views\Shared\_EmptyLayout.cshtml" />
    <Content Include="Scripts\layui\font\iconfont.eot" />
    <Content Include="Scripts\layui\font\iconfont.ttf" />
    <Content Include="Scripts\layui\font\iconfont.woff" />
    <Content Include="Views\Common\TestPage.cshtml" />
    <Content Include="Views\UserFeedback\Index.cshtml" />
    <Content Include="Views\BuyerInfo\Index.cshtml" />
    <Content Include="Views\Shared\_OldVersionLayout.cshtml" />
    <Content Include="Views\Order\SearchCondition_OldVersion.cshtml" />
    <Content Include="Views\Order\FreePrintSearchCondition_OldVersion.cshtml" />
    <Content Include="Views\Purchases\SearchCondition_OldVersion.cshtml" />
    <Content Include="Views\Common\ImportFreePrint_OldVersion.cshtml" />
    <Content Include="Views\BuyerInfo\AddBuyerInfo.cshtml" />
    <Content Include="Views\Parthers\Index.cshtml" />
    <Content Include="Views\UpdatePrice\Index.cshtml" />
    <Content Include="Views\Preordain\UpdateLogisticInfoAlert.cshtml" />
    <Content Include="Views\LogisticQuery\Index.cshtml" />
    <Content Include="Views\Tools\Login.cshtml" />
    <Content Include="Views\Tools\AddUserOrResetPwd.cshtml" />
    <Content Include="Views\Tools\VisitSystem.cshtml" />
    <Content Include="Views\Order\KS_SearchCondition.cshtml" />
    <Content Include="Views\Order\KS_FreePrintSearchCondition.cshtml" />
    <Content Include="Views\Purchases\KS_SearchCondition.cshtml" />
    <Content Include="Views\ScanProductPrint\Index.cshtml" />
    <Content Include="Views\Common\KS_ImportFreePrint.cshtml" />
    <Content Include="Views\SetProductFlag\Index.cshtml" />
    <Content Include="Views\Home\kuaiShouVideo.cshtml" />
    <Content Include="Views\SetSortingCodeRule\Index.cshtml" />
    <Content Include="Views\ShareWayBillAccount\Index.cshtml" />
    <Content Include="Views\FreePrintExpress\_SinglePrintPage.cshtml" />
    <Content Include="Views\XcxManagement\YouZanQRCodeBindShop.cshtml" />
    <Content Include="Views\Order\PrintContentInfoPartialView.cshtml" />
    <Content Include="Views\Authoritycenter\Index.cshtml" />
    <Content Include="Views\Authoritycenter\loginBypassAccount.cshtml" />
    <Content Include="Views\SharedWaybillAccountCheck\Index.cshtml" />
    <Content Include="Views\SubUser\Index.cshtml" />
    <Content Include="Views\WDYY\WaitApprove.cshtml" />
    <Content Include="Views\WDYY\ApproveList.cshtml" />
    <Content Include="Views\Shared\_wd_Layout.cshtml" />
    <Content Include="Views\WDYY\Index.cshtml" />
    <Content Include="Views\WDYY\_IndexPartialView.cshtml" />
    <Content Include="Views\Tools\ExecSql.cshtml" />
    <Content Include="Views\OrderFds\IndexFds.cshtml" />
    <Content Include="Views\Purchases\PurchasesSet.cshtml" />
    <Content Include="Views\GeneralizeIndex\Index.cshtml" />
    <Content Include="Views\Purchases\newPurchases.cshtml" />
    <Content Include="Scripts\noviceIntro\说明.md" />
    <Content Include="Views\Auth\AuthSuccess.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Files\WaybillCodeCheck\" />
    <Folder Include="Views\ApiBase\" />
    <Folder Include="Views\CustomerAreaTemplate\" />
    <Folder Include="Views\LoadPrintSet\" />
    <Folder Include="Views\LogisticSubscribeMsg\" />
    <Folder Include="Views\ReciverInfo\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Scripts\jquery-1.10.2.min.map" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AutoComment\DianGuanJiaApp.RabbitMQ\DianGuanJiaApp.RabbitMQ.csproj">
      <Project>{90dc55be-abba-4686-8119-a18e012f540e}</Project>
      <Name>DianGuanJiaApp.RabbitMQ</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Data\DianGuanJiaApp.Data.csproj">
      <Project>{6c8b9657-fbaa-46c8-8433-1b2f908e8398}</Project>
      <Name>DianGuanJiaApp.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Services\DianGuanJiaApp.Services.csproj">
      <Project>{abee0a3a-3b86-4631-add0-3bdd17a0b1fc}</Project>
      <Name>DianGuanJiaApp.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\DianGuanJiaApp.Utility\DianGuanJiaApp.Utility.csproj">
      <Project>{a0ead1ef-8b94-4487-ad73-f981cb45e58f}</Project>
      <Name>DianGuanJiaApp.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="..\jos-net-open-api-sdk-2.0\jos-sdk-net.csproj">
      <Project>{cf7757d6-4f03-4bca-948d-d1e0b81b491c}</Project>
      <Name>jos-sdk-net</Name>
    </ProjectReference>
    <ProjectReference Include="..\kdapi\KDAPI.csproj">
      <Project>{8fcc5126-bda6-4141-983c-cfb7f3720fe3}</Project>
      <Name>KDAPI</Name>
    </ProjectReference>
    <ProjectReference Include="..\sf-fengqiao-sdk\sf-fengqiao-sdk.csproj">
      <Project>{24C1F4F0-D3DD-4BE1-BEBA-EFF47FEB7F3D}</Project>
      <Name>sf-fengqiao-sdk</Name>
    </ProjectReference>
    <ProjectReference Include="..\ych-sdk\ych-sdk.csproj">
      <Project>{aaf61c3f-5729-4e3c-bc8c-eea8067e0305}</Project>
      <Name>ych-sdk</Name>
    </ProjectReference>
    <ProjectReference Include="..\yz-sdk\yz-sdk.csproj">
      <Project>{2a0e43ad-8078-45b9-8c19-9f8115f2337f}</Project>
      <Name>yz-sdk</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Scripts\CommonModule.js" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{4A0DDDB5-7A95-4FBF-97CC-616D07737A77}" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Scripts\orderlist\PrintContentFormatSetModule.js" />
  </ItemGroup>
  <ItemGroup>
    <COMReference Include="IWshRuntimeLibrary">
      <Guid>{F935DC20-1CF0-11D0-ADB9-00C04FD58A0B}</Guid>
      <VersionMajor>1</VersionMajor>
      <VersionMinor>0</VersionMinor>
      <Lcid>0</Lcid>
      <WrapperTool>tlbimp</WrapperTool>
      <Isolated>False</Isolated>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </COMReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\oldCss\common.css" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\oldCss\oderList.css" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\oldJs\setMarkeStalls.js" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets" Condition="Exists('$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\TypeScript\Microsoft.TypeScript.targets')" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>36038</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:36038/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.3.6.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets'))" />
  </Target>
</Project>