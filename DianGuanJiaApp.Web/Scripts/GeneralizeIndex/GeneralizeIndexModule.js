var generalizeIndexModule = (function (module, common, $, layer) {
    $(function () {
        statisticFunc();
    });

    var platformType = commonModule.CurrShop.PlatformType.toLocaleLowerCase() || "";
    var statisticFunc = function () {

        // 今日打印快递单数量
        commonModule.Ajax({
            type: "POST",
            url: "/WaybillCodeList/GetTodayPrintedCount",
            success: function (rsp) {
                if (rsp.Success) {
                    $("#TodayPrintedCount").html(rsp.Data);
                }
            }
        });
        // 今日发货订单数量
        commonModule.Ajax({
            type: "POST",
            url: "/SendHistory/GetTodaySendedCount",
            success: function (rsp) {
                if (rsp.Success) {
                    $("#TodaySendedCount").html(rsp.Data);
                }
            }
        });

        if (platformType == "toutiao") {
            // 预发货统计
            commonModule.Ajax({
                type: "POST",
                url: "/Preordain/GetSoonTimeOutOrderCount",
                success: function (rsp) {
                    if (rsp.Success) {
                        $("#SoonTimeOutOrderCount").html(rsp.Data);
                    }
                }
            });
            // 物流轨迹预警件统计
            commonModule.Ajax({
                type: "POST",
                url: "/LogisticQuery/GetWarningCount",
                success: function (rsp) {
                    if (rsp.Success) {
                        $("#LogisticWarningCount").html(rsp.Data);
                    }
                }
            });
        }
        //if (commonModule.IsDefaultEnableLogistic) {
           
        //}
        //else {
        //    $(".stockup_table_content tr>th:eq(2),.stockup_table_content tr>th:eq(3)").hide();
        //    $(".stockup_table_content tr>td:eq(2),.stockup_table_content tr>td:eq(3)").hide();
        //}
      
    }

    return module;
}(generalizeIndexModule || {}, commonModule, jQuery, layer));