/// <reference path="../jquery-1.10.2.min.js" />
/// <reference path="../layer/layer.js" />
/// <reference path="../CommonModule.js" />

var purchaseModule = (function (module, common, lp, $, layer) {
    var _pageIndex = 1, _pageSize = 10;
    var editable = false;
    var needReload = false;
    var queryData = {};
    var isPrinting = false;

    var printFieldsConfig = {};
    var defaultSetting = []; // 系统设置的显示列（区别于历史记录）
    var _historyPurchaseSetting = {};
    var _defaultPurchaseSetting = {};
    var _defaultImgSize = { "DisplayImgSize": "80x80", "PrintImgSize": "80x80", "DisplayAttrImgSize": "30x30", "PrintAttrImgSize": "30x30" };
    var platformType = (commonModule.PlatformType || "").toLowerCase();
    var _fromStr = "";

    var defaultChoiceShop = "CurrShop";
    var customRemarkFlags = [];
    var customConditions = [];
    var customFilterConditions = [];
    var orderCategorys = [];
    var purchasePrintFieldsSet = {};
    var purchaseTemplateSet = {};
    var purchaseCustomConditionSet = {};
    var shops = [];
    var configSearchCondition = [];
    var defaultChoiceShop = "CurrShop";

    var purchasesNaHuoLabelConfig = {};//小标签配置信息（打印机和模板）

    $(function () {

        if (platformType == "kuaishou") {
            $("#onSaveRecord").hide();
            $("#stockupRecord").hide();
        }

        //禁用F5刷新
        document.onkeydown = function () {
            if (event.keyCode == 116 && isPrinting == true) {
                event.keyCode = 0;
                event.cancelBubble = true;
                layer.msg("正在打印禁止刷新!", { icon: 2 });
                return false;
            }
        }


        //common.LoadCommonSetting("PurchaseCheckboxConditionSet", true, function (rsp) {
        //    if (rsp.Success) {
        //        var checkVals = JSON.parse(rsp.Data) || [];
        //        if (checkVals.length > 0) {
        //            for (var i = 0; i < checkVals.length; i++) {
        //                if (checkVals[i] != "")
        //                    $(".search_condition .checkboxline input[name=" + checkVals[i] + "]").prop("checked", true);
        //            }
        //        }
        //    }
        //});

        // 表单序列化为对象
        $.fn.serializeObject = function () {
            var o = {};
            var a = this.serializeArray();
            $.each(a, function () {
                if (o[this.name] !== undefined) {
                    if (!o[this.name].push) {
                        o[this.name] = [o[this.name]];
                    }
                    o[this.name].push(this.value || '');
                } else {
                    o[this.name] = this.value || '';
                }
            });
            return o;
        };

        // 初始化时间控件
        //common.InitDateRangeSelect("#QueryDateVal", 30);
        //common.InitDateRangePicker($("#QueryDateVal"));
        commonModule.InitCalenderTime($("#QueryDateVal"), { days: 15 });
        // 展开或收缩全部备注信息
        $("#collapse-all-msg,#allRemarkCheckBox").on("change", function () {

            if (_isUseOldTemplate) {
                var checked = $(this).prop("checked");
                expandAllDetail(checked);
            }
            else {
                NewpurchasesSetModule.showAllRemark(this);
            }

        });

        // 查询条件全选操作
        $(".search_checkall").change(function () {
            configSearchCondition = [];
            var $checkboxes = $("#addSearchCondition :checkbox:visible");
            var isChecked = $(".search_checkall").prop("checked");
            $checkboxes.prop("checked", isChecked);
            if (isChecked) {
                $checkboxes.each(function (i, item) {
                    //var name = $(item).attr("name");
                    //if (platformType == "youzan"){
                    //    if (name == "ColorAndSize" || name == "ColorAndSizeNotContains")
                    //        return;
                    //}
                    //else {
                    //    if (name == "ColorAndSize2" || name == "ColorAndSizeNotContains2")
                    //        return;
                    //}
                    configSearchCondition.push($(item));
                });
            }
        });

        // 更多查询条件-> 勾选框change事件
        $(".aialog_wrapper_main .addOrderList_ul :checkbox:visible").each(function () {
            $(this).change(function () {
                var $checkboxes = $(".aialog_wrapper_main .addOrderList_ul :checkbox:visible");
                var $checkedboxes = $(".aialog_wrapper_main .addOrderList_ul :checkbox:checked:visible");
                $(".search_checkall").prop("checked", $checkboxes.length == $checkedboxes.length);
            });
        });

        // 预发货、包含锁定订单、排除退款订单、精确查询
        $(".search_condition input[type=checkbox]").each(function () {
            $(this).change(function () {
                var checked = $(this).prop("checked");
                $(this).val(checked);
                var name = $(this).attr("name");
                if (checked && customFilterConditions.indexOf(name) == -1) {
                    customFilterConditions.push(name);
                }
                else {
                    var index = -1;
                    $(customFilterConditions).each(function (i, item) {
                        index = item == name ? i : -1;
                    });
                    if (index != -1)
                        customFilterConditions.splice(index, 1);
                }
            });
        });
        // 订单分类修改别名
        $("select.classifyOrder").change(function () {
            if ($(this).val() === "UpdateCategoryName") {
                renameAlias();
                $(this).val("");
            }
        });

        // 是否显示简称
        $("select.ShowShortTitle").change(function () {
            var val = $(this).val();
            $(".tableColumnSet [value=ShortTitle]:checkbox").prop("checked", val == "1");
            module.SaveColSetting();
        });

        // 过滤
        $("select.FilterSearch").change(function () {
            var val = $(this).val();
            if (val == "1") { //排除预发货
                $(".search_condition [name=yufahuo]:checkbox").prop("checked", false);
                $("select#printState option[value='']").prop("selected", true);
            }
            else if (val == "2") { // 排除快递单已打印
                $(".search_condition [name=yufahuo]:checkbox").prop("checked", true);
                $("select#printState option[value=Un_ExpressPrintTimes]").prop("selected", true);
            }
            else if (val == "3") { // 排除发货单已打印   
                $(".search_condition [name=yufahuo]:checkbox").prop("checked", true);
                $("select#printState option[value=Un_SendPrintTimes]").prop("selected", true);
            }
            else { //不过滤
                $(".search_condition [name=yufahuo]:checkbox").prop("checked", true);
                $("select#printState option[value='']").prop("selected", true);
            }
        });

        $(".index_btn1").on("mouseover", function () {
            $(".index_btn1 ul").show();
            $(".index_btn1 i").addClass("show-inverse");

        })
        $(".index_btn1").on("mouseout", function () {
            $(".index_btn1 ul").hide();
            $(".index_btn1 i").removeClass("show-inverse");
        })

        $(".index_btn1 ul li").each(function () {
            $(this).on("mouseover", function () {
                $(this).css({ backgroundColor: "#feb07c", color: "#fff" })
            })
            $(this).on("mouseout", function () {
                $(this).css({ backgroundColor: "#fff", color: "#666" })
            })
        })

        $(".showMoreSelectIcon").each(function () {
            $(this).on("mouseover", function () {
                if ($(this).hasClass("rightList")) {
                    $(this).children().css({ top: "20px", left: "-52px" })
                } else {
                    $(this).children().css({ top: "20px", left: "0" })
                }
                $(this).children("ul").show();

            })
            $(this).on("mouseout", function () {
                $(this).children("ul").hide();
            })
        })

        // 商品信息查询下拉框change事件
        $("#productAttribute").change(function () {
            var opt = $("#productAttribute").val();
            var options = ["选择查询", "商品标题", "简称", "商品货号", "单品货号", "商品ID", "规格颜色", "尺码"]
            if (opt == "") {
                $("#productAttributeDiv").addClass("productAttributeHide");
                $("#productAttribute").css({ width: "469px" });
                $("#inclubeOrNoinclube").hide();
            } else {
                $("#productAttributeDiv").removeClass("productAttributeHide");
                $("#productAttribute").css({ width: "135px" });
                //$("#productAttributeDiv>label").html("请输入" + options[opt]);
                //$("#productAttributeDiv #productAttribute_input").attr("placeholder", "请输入" + options[opt]);
                $("#productAttributeDiv .productAttributeLabel_span").html("请输入" + options[opt]);
                $("#inclubeOrNoinclube").show();
            }
        });
        $("#productAttributeDiv .productAttributeInput").on("input", function () {
            if ($(this).val() != "") {  //如果当前值不为空，隐藏placeholder
                $("#productAttributeDiv .productAttributeLabel_span").hide();
            }
            else {
                $("#productAttributeDiv .productAttributeLabel_span").show();
            }
        })
        $("#productAttributeDiv .productAttributeInput").on("blur", function () {
            if ($(this).val() != "") {  //如果当前值不为空，隐藏placeholder
                $("#productAttributeDiv .productAttributeLabel_span").hide();
            }
            else {
                $("#productAttributeDiv .productAttributeLabel_span").show();
            }
        })

        $("#otherSelect").change(function () {
            var opt = $("#otherSelect").val();
            var option2s = { "all": "旺旺/订单编号/买家留言/卖家备注", "wangwang": "旺旺，多个以逗号(\",\")分隔", "orderno": "订单编号，多个以逗号(\",\")分隔", "buyerremark": "买家留言", "sellerremark": "卖家备注" };
            if (opt == "") {
                $("#otherSelectLabel").hide();
                $("#otherSelect").css({ width: "326px" });
                $("#otherSelectInput").hide();

            } else {
                $("#otherSelectLabel").show();
                $("#otherSelect").css({ width: "80px" });
                $("#otherSelectLabel").html("请输入" + option2s[opt]);
                $("#otherSelectInput").show();
            }
        });

        $("#closeAialog").on("click", function () {
            $(".aialog").hide();
            $("#remarkFlag option:first").prop("selected", 'selected');

        })
        $(".main-title-icon").on("click", function () {
            $(".aialogHistory").hide();

        })

        // 卖家备注弹出框，添加按钮事件
        $("#aicustomFlag_btn").on("click", function () {
            var $flagCheckbox = $(".main-content-flag>li>input");
            var $flagArray = [];  //"红","黄","绿","蓝","紫";
            var $numArray = [];     //对应0 1 2 3 4
            for (var i = 0; i < $flagCheckbox.length; i++) {
                if ($($flagCheckbox[i]).is(':checked')) {
                    $flagArray.push($($flagCheckbox[i]).val());
                    $numArray.push($($flagCheckbox[i]).data("id"));
                }
            }
            if ($flagArray.length == 0) {  //$flagArray为多选框数据，如果为空，点提交直接返回
                layer.msg("请选择旗帜");
                return;
            }

            var chooseName = $('input:radio[name="flagChoos"]:checked').val();  //值为包含或不包含
            var item = { "Operator": chooseName, "Value": $numArray.join(","), "Text": $flagArray.join("") };

            var isExist = false;
            for (var i = 0; i < customRemarkFlags.length; i++) {
                if (customRemarkFlags[i].Value == item.Value &&
                    customRemarkFlags[i].Operator == item.Operator &&
                    customRemarkFlags[i].Text == item.Text) {
                    isExist = true;
                    break;
                }
            }

            if (!isExist)
                customRemarkFlags.unshift(item);

            _loadRemarkFlagName();
            $(".main-content-flag>li input[type=checkbox]").prop("checked", false);
            $(".flag-choose input[type=radio]:first").prop("checked", true);
        });

        // 卖家备注下拉框change事件
        bindSearchRemarkFlagChange();
        // 平台单号查询互斥事件
        //bindPlatformOrderIdChange();


        $(".aialog-main-footer-up>span").on("click", function () {   //确定按钮，保存数据
            $(".main-title-icon").click();
            //$(".aialog").hide();

        })

        $("#otherSelectInput").on("focus", function () {
            $("#otherSelectLabel").html("");
        })

        // 列表显示字段全选反选
        $(".checkall_setting").change(function () {
            var $checkboxes = $(".setting_content :checkbox");
            $checkboxes.prop("checked", $(this).prop("checked"));
        });

        $(".setting_content :checkbox").each(function () {
            $(this).change(function () {
                var $checkboxes = $(".setting_content :checkbox");
                var checkedboxes = $(".setting_content :checkbox:checked");
                $(".checkall_setting").prop("checked", $checkboxes.length == checkedboxes.length);
            });
        });
        // 打印字段全选反选
        $(".print_checkall_setting").change(function () {
            var $checkboxes = $(".print_setting_content :checkbox");
            $checkboxes.prop("checked", $(this).prop("checked"));
        });

        $(".print_setting_content :checkbox").each(function () {
            $(this).change(function () {
                var $checkboxes = $(".print_setting_content :checkbox");
                var checkedboxes = $(".print_setting_content :checkbox:checked");
                $(".print_checkall_setting").prop("checked", $checkboxes.length == checkedboxes.length);
            });
        });

        // 回车查询
        $(".search_condition input[name]").each(function () {
            $(this).bind("keyup", function (event) {
                if (event.keyCode == "13") {
                    //回车执行查询
                    $(".btn_create").click();
                }
            });
        });

        $("select#SortBy_ProductAttr").change(function () {
            var val = $(this).val();
            if (val == "") {
                $(this).css({ "width": "160px", "margin-right": "5px" });
                $("select#OrderBy_ProductAttr").hide();
            }
            else {
                $(this).css({ "width": "109px", "margin-right": "0px" });
                $("select#OrderBy_ProductAttr").show();
                $("select#OrderBy_ProductAttr").removeAttr("disabled");
            }
        });

        $("select#SortBy_Product_Sku").change(function () {
            var val = $(this).val();
            if (val == "") {
                $(this).css({ "width": "160px", "margin-right": "5px" });
                $("select#OrderBy_Product_Sku").hide();
            }
            else {
                $(this).css({ "width": "109px", "margin-right": "0px" });
                $("select#OrderBy_Product_Sku").show();
                $("select#OrderBy_Product_Sku").removeAttr("disabled");
            }
        });

        if (platformType == "youzan" || platformType == "kuaishou") {
            $(".addOrderList_ul_ul [data-group='ColorAndSize']").parent().hide();
            $(".addOrderList_ul_ul [data-group='ColorAndSizeNotContains']").parent().hide();
            $(".addOrderList_ul_ul .yz_ColorAndSize").parent().show();
            $(".addOrderList_ul_ul .yz_ColorAndSizeNotContains").parent().show();

            $(".print_setting_content .yz_ColorAndSize").show();
            $(".print_setting_content .print_color").hide();
            $(".print_setting_content .print_size").hide();

            $(".tableColumnSet_content .yz_ColorAndSize").show();
            $(".tableColumnSet_content .tb_color").hide();
            $(".tableColumnSet_content .tb_size").hide();

            $("#SortBy_Product_Sku option[value='Size']").hide();
            $("#SortBy_Product_Sku option[value='Color']").hide();
            $("#SortBy_Product_Sku option[value='Size,Color']").hide();
            $("#SortBy_Product_Sku option[value='Size']").hide();
        }
        else {
            $(".addOrderList_ul_ul [data-group='ColorAndSize']").parent().show();
            $(".addOrderList_ul_ul [data-group='ColorAndSizeNotContains']").parent().show();
            $(".addOrderList_ul_ul .yz_ColorAndSize").parent().hide();
            $(".addOrderList_ul_ul .yz_ColorAndSizeNotContains").parent().hide();

            $(".print_setting_content .yz_ColorAndSize").hide();
            $(".print_setting_content .print_color").show();
            $(".print_setting_content .print_size").show();

            $(".tableColumnSet_content .yz_ColorAndSize").hide();
            $(".tableColumnSet_content .tb_color").show();
            $(".tableColumnSet_content .tb_size").show();
        }


        if (_isFromOrderPrint) {
            $(".btn_create").click();
            $(".search_condition [name='PlatformOrderId']").blur();
        }
    });

    // 使用新旧模板页面初始化
    var displayTemplateStyle = function (isUseOldTemplate) {

        if (isUseOldTemplate) {
            $('#TableColumnSet').show();
            $(".print-setting").show();

            //$(".print-setting .print-fields,.print-setting .print-img-size").show();
            $('#isShowPrice').parent().hide();
            //$(".print-attr").css("border-bottom", "1px solid #ccc");
            $("#SortBy_ProductAttr").removeAttr("disabled");
            $("#SortBy_Product_Sku").removeAttr("disabled");
        }
        else {
            $('#TableColumnSet').hide();
            $(".print-setting").hide();

            //$(".print-setting .print-fields,.print-setting .print-img-size").hide();
            $('#isShowPrice').parent().show();
            //$(".print-attr").css("border-bottom", "none");
            $("#SortBy_ProductAttr").attr("disabled", "disabled");
            $("#SortBy_Product_Sku").attr("disabled", "disabled");
        }
    }

    // 获取模板类型
    var getTemplateType = function () {
        if (purchaseTemplateSet && purchaseTemplateSet.tableName != "老版模板")
            _isUseOldTemplate = false;
        else
            _isUseOldTemplate = true;
        return _isUseOldTemplate;
    }

    var bindPlatformOrderIdChange = function () {
        $(".search_condition input[name=PlatformOrderId]").on("keyup change", function () {
            var inputs = $(".search_condition [name],#orderList_tableSetSort").not("[name=ShopId],[name=PlatformOrderId],[name=QueryDate],[name=StartTime],[name=EndTime]");
            if ($(this).val()) {
                inputs.attr("disabled", "disabled");
            }
            else {
                inputs.removeAttr("disabled");
            }
        });
    }

    var bindSearchRemarkFlagChange = function () {
        $(".stockup_search select[name=SellerRemarkFlag]").on("change", function () {
            $val = $(this).val()
            if ($val == 6) {
                customRemarkFlags = commonModule.SystemoConfig.PurchaseCustomConditionSet.RemarkFlags;
                _loadRemarkFlagName();

                layer.open({
                    type: 1,
                    title: "设置备注旗帜", //不显示标题
                    content: $('.flag-main'),
                    area: ['800', '700'], //宽高
                    btn: ['保存', '取消'],
                    yes: function () {
                        if ($(".main-content-flag>li input[type=checkbox]:checked").length > 0) {
                            $("#aicustomFlag_btn").click();
                        }

                        module.SaveCustomConditionsSet();
                    },
                    cancel: function () {

                    }
                });
                $(this).val("");
            }
        });
    }

    module.SaveCustomConditionsSet = function (callback) {

        var conditions = [];
        for (var i = 0; i < configSearchCondition.length; i++) {
            var name = configSearchCondition[i].attr("name");
            conditions.push(name);
        }
        purchaseCustomConditionSet.SearchConditions = conditions || [];
        purchaseCustomConditionSet.RemarkFlags = customRemarkFlags || [];
        purchaseCustomConditionSet.DefaultChoiceShop = defaultChoiceShop || "CurrShop";

        commonModule.SystemoConfig.PurchaseCustomConditionSet = purchaseCustomConditionSet;

        if (typeof callback == "function")
            commonModule.SaveCommonSetting("PurchaseCustomConditionSet", JSON.stringify(commonModule.SystemoConfig.PurchaseCustomConditionSet), callback);
        else
            commonModule.SaveCommonSetting("PurchaseCustomConditionSet", JSON.stringify(commonModule.SystemoConfig.PurchaseCustomConditionSet), function (rsp) {
                if (rsp.Success) {
                    renderSearchConditions();
                }
                layer.closeAll();
            });
    }


    module.Initialize = function () {
        console.log("Initialize");
    }

    module.From = function () {
        _fromStr = $(".display_setting").attr("data-from");
        return _fromStr;
    }

    module.SetPurchaseSettingFrom = function (from) {
        _fromStr = from;
    }

    module.DefaultImgSize = function () {
        return _defaultImgSize;
    }

    module.HistoryPurchaseSetting = function () {
        return _historyPurchaseSetting;
    }

    module.SetHistoryPurchaseSetting = function (obj) {
        _historyPurchaseSetting = obj;
    }

    module.DefaultPurchaseSetting = function () {
        return _defaultPurchaseSetting;
    }

    module.SetDefaultPurchaseSetting = function (obj) {
        _defaultPurchaseSetting = obj;
    }

    module.PurchaseSetting = function () {
        return module.From() == "history" ? module.HistoryPurchaseSetting() : module.DefaultPurchaseSetting();
    }

    module.SetPurchaseSetting = function (obj) {
        if (module.From() == "history")
            module.SetHistoryPurchaseSetting(obj);
        else
            module.SetDefaultPurchaseSetting(obj);
    }

    module.LoadDefaultConfig = function (config) {
        if (!config) {
            layer.msg("加载配置信息失败，请刷新重试");
            return;
        }

        commonModule.SystemoConfig = config || {};
        orderCategorys = commonModule.SystemoConfig.OrderCategorys || [];
        shops = commonModule.SystemoConfig.Shops || [];

        purchasePrintFieldsSet = commonModule.SystemoConfig.PurchasePrintFieldsSet || {};
        purchaseCustomConditionSet = commonModule.SystemoConfig.PurchaseCustomConditionSet || {};
        purchaseTemplateSet = config.PurchaseTemplateSet;

        defaultChoiceShop = purchaseCustomConditionSet.DefaultChoiceShop || "CurrShop";
        customRemarkFlags = purchaseCustomConditionSet.RemarkFlags || [];
        customConditions = purchaseCustomConditionSet.SearchConditions || [];
        customFilterConditions = commonModule.SystemoConfig.PurchaseCheckboxConditionSet || {};
        defaultChoiceShop = config.DefaultLoadShop || "CurrShop";

        initPrintFieldsConfig(purchasePrintFieldsSet, true);

        // 加载店铺信息
        if (!commonModule.isCustomerOrder() && shops && shops.length > 0) {
            loadShopBind(shops);
        }

        // 加载订单分类信息
        if (orderCategorys && orderCategorys.length > 0) {
            updateCategoryAliasToHtml(orderCategorys);
        }

        // 加载查询条件
        renderSearchConditions(customConditions);
        SearchCheckOrderEvt();


        //console.log("isUseOldTemplate:" + _isUseOldTemplate);
        // 使用新旧模板页面初始化
        displayTemplateStyle(_isUseOldTemplate);

    }

    var initPrintFieldsConfig = function (purchasePrintFieldsSet, isLoadConfig) {
        if (purchasePrintFieldsSet == undefined) return;

        // 加载列表显示字段、打印字段配置
        if (typeof purchasePrintFieldsSet == "string")
            printFieldsConfig = JSON.parse(purchasePrintFieldsSet) || {};
        else
            printFieldsConfig = purchasePrintFieldsSet || {};

        printFieldsConfig.PrintFields = printFieldsConfig.PrintFields || [];
        printFieldsConfig.DisplayFields = printFieldsConfig.DisplayFields || [];

        printFieldsConfig.PrintParams = printFieldsConfig.PrintParams || { "Paper": "A4", "Direction": "1", "ImgSize": _defaultImgSize.PrintImgSize, "AttrImgSize": _defaultImgSize.PrintAttrImgSize };
        printFieldsConfig.SettingParams = printFieldsConfig.SettingParams || { "ImgSize": _defaultImgSize.DisplayImgSize, "AttrImgSize": _defaultImgSize.DisplayAttrImgSize };

        printFieldsConfig.SettingParams.ImgSize = printFieldsConfig.SettingParams.ImgSize || _defaultImgSize.DisplayImgSize;
        printFieldsConfig.SettingParams.AttrImgSize = printFieldsConfig.SettingParams.AttrImgSize || _defaultImgSize.DisplayAttrImgSize;
        printFieldsConfig.PrintParams.ImgSize = printFieldsConfig.PrintParams.ImgSize || _defaultImgSize.PrintImgSize;
        printFieldsConfig.PrintParams.AttrImgSize = printFieldsConfig.PrintParams.AttrImgSize || _defaultImgSize.PrintAttrImgSize;

        module.PurchaseSetting().Setting = printFieldsConfig;

        loadDefaultPrintSet();
    }

    var loadShopBind = function (shops) {
        //var html = "";
        //var shopIds = "";
        //var currentShopId = $(".currentShopId").val();
        //for (var i = 0; i < shops.length; i++) {
        //    shopIds += shops[i].Id + ",";
        //    html += "<option value=\"" + shops[i].Id + "\" " + (currentShopId == shops[i].Id ? "selected" : "") + ">" + shops[i].NickName + "</option>"
        //}
        //shopIds = shopIds.trimEndDgj(",");
        //html = "<option value=\"" + shopIds + "\">全部店铺</option>" + html;
        //$(".stockup_search select[name=ShopId]").html(html);

        var selectboxArr = [];
        for (var i = 0; i < shops.length; i++) {
            var obj = {};
            obj.Value = shops[i].Id;
            obj.Text = shops[i].NickName;
            selectboxArr.push(obj);
        }

        var selectInit = {
            eles: '#selectShops',
            emptyTitle: '全部店铺', //设置没有选择属性时，出现的标题
            data: selectboxArr,
            searchType: 1, //1出现搜索框，不设置不出现搜索框
            showWidth: '250px', //显示下拉的宽
            isRadio: false, //有设置，下拉框改为单选
            allSelect: true,
            //selectData: [selectboxArr[0]]  //初始化数据
        };

        var selectShops = new selectBoxModule2();
        selectShops.initData(selectInit);
        // 默认进入选择全部店铺或当前店铺设置
        var label = '<label style="float: right;"><i class="shop_iconSet" onclick="orderPrintModule.SetDefaultShop();" title="默认选择店铺设置"></i></label>';
        $(label).insertAfter($("#selectShops .electWrap-allSelect label"));

        // 选择店铺加载默认设置
        if (defaultChoiceShop == "AllShop")
            $("#selectShops .allSelect-checkbox").prop("checked", true).change();
        else
            $("#selectShops .selectMore-items-input[value='" + commonModule.CurrShop.Id + "']").prop("checked", true).change();
        $(".ChoiceShopDefaultSetting input[name='ChoiceShopDefaultSetting'][value='" + defaultChoiceShop + "']").prop("checked", true);
    }

    module.SetDefaultShop = function () {
        var setDefaultChoiceShopLayer = layer.open({
            type: 1,
            title: "店铺查询默认设置",
            content: $('.ChoiceShopDefaultSetting'),
            area: ['300', '120'], //宽高
            btn: ['保存', '取消'],
            yes: function () {
                // 选择默认设置
                var val = $(".ChoiceShopDefaultSetting input[type=radio]:checked").val();
                defaultChoiceShop = val || "CurrShop";
                commonModule.SaveCommonSetting("/OrderPrint/DefaultLoadShop", defaultChoiceShop, function (rsp) {
                    if (rsp.Success) {
                        if (defaultChoiceShop == "CurrShop")
                            $("#selectShops .selectMore-items-input[data-id='" + commonModule.CurrShop.Id + "']").prop("checked", true).change();
                        else
                            $("#selectShops .allSelect-checkbox").prop("checked", true).change();
                    }
                });

                layer.close(setDefaultChoiceShopLayer);
            },
            cancel: function () {

            }
        });
    }


    // 监听contenteditable文本改变事件
    var ListenContenteditableEvt = function () {
        // contenteditable修改内容
        $.fn.ContenteditableEvt = function () {
            return this.each(function () {
                var $this = $(this);
                var htmlold = $this.html();
                $this.unbind('blur paste copy cut')
                    .bind('blur paste copy cut', function (e) {
                        var htmlnew = $this.html();
                        if (htmlold !== htmlnew) {
                            $this.trigger('change');
                            //console.log(htmlnew);
                            editable = true;
                            if (e.type == "paste") {
                                // 粘贴不能实时获取到粘贴板中的图片等值
                                // e.originalEvent.clipboardData.getData('Text');
                                setTimeout(function () {
                                    htmlnew = $this.html();
                                    updatePurchaseItemsModel($this, htmlnew, htmlold);
                                }, 100);
                            }
                            else
                                updatePurchaseItemsModel($this, htmlnew, htmlold);
                        } else {
                            editable = false;
                        }
                    })
            })
        }

        var $contenteditables = $("*[contenteditable]");
        $contenteditables.ContenteditableEvt();
        // 绑定contenteditable控件点击和失去焦点事件(选择行进行赋值需要)
        $contenteditables.each(function () {
            $(this).click(function () {
                $(this).attr("contenteditable", true);
                $(this).focus();
            });

            $(this).blur(function () {
                $(this).attr("contenteditable", false);
            });
        });
    }

    var updatePurchaseItemsModel = function ($elemt, newVal, oldVal) {
        var $td = $elemt.is("td") ? $elemt : $elemt.parents("td");
        if ($td.length == 0)
            return;

        var $tr = $td.parent();
        if ($tr.length == 0)
            return;

        var purchaseItemsModel = module.PurchaseSetting().PurchaseItemsModel || {};
        // 合计行修改
        var text = $elemt.text();
        var thisClass = $elemt.parent().attr("class") || "";
        if (thisClass == "sum-totalitemcount")
            purchaseItemsModel.TotalItemCount = parseInt(text) || 0;
        else if (thisClass == "sum-totalpurchaseprice")
            purchaseItemsModel.TotalPurchasePrice = parseFloat(text) || 0;
        else if (thisClass == "sum-totalamount")
            purchaseItemsModel.TotalAmount = parseFloat(text) || 0;
        else if (thisClass == "sum-totalprofit")
            purchaseItemsModel.TotalProfit = parseFloat(text) || 0;
        else {
            // 备货单普通行列修改
            var items = purchaseItemsModel.PurchaseItems || [];
            var colIndex = $td.attr("data-index") || 0;
            //var rowIndex = $tr.attr("data-subitemrownum") || 0;
            var productId = $tr.attr("data-pid") || 0;
            var skuId = $tr.attr("data-sku") || "";
            var fieldName = $(".stockup_table_content th[data-index='" + colIndex + "']").attr("class");
            if (fieldName == "ProductImgUrl" || fieldName == "SkuImgUrl") {
                var $img = $td.find("img");
                newVal = $img.length == 0 ? "" : $img.eq(0).attr("src");
            }
            else if (fieldName == "Count" || fieldName == "Price" || fieldName == "Profit" || fieldName == "TotalProfit" || fieldName == "TotalItemCount" || fieldName == "ItemAmount" || fieldName == "TotalAmount" || fieldName == "SingleCostPrice" || fieldName == "CostPrice" || fieldName == "TotalPurchasePrice") {
                // int类型字段
                newVal = fieldName == "Count" || fieldName == "TotalItemCount" ? parseInt(newVal) : parseFloat(newVal) || 0;
                if (isNaN(newVal) || (newVal == 0 && parseFloat(oldVal) > 0)) {
                    $elemt.text(oldVal);
                    return;
                }
            }

            var isBreak = false;
            for (var i = 0; i < items.length; i++) {
                var item = items[i];
                if (item.ProductId == productId) {
                    if (colIndex < 4 || colIndex > 15) {
                        // 合并行修改
                        item[fieldName] = newVal;
                        isBreak = true;
                    }
                    else {
                        // 普通行修改
                        var subItems = item.SubItems;
                        for (var ii = 0; ii < subItems.length; ii++) {
                            var subItem = subItems[ii];
                            if (subItem.SkuId == skuId) {
                                subItem[fieldName] = newVal;
                                isBreak = true;
                            }
                            if (isBreak)
                                break;
                        }
                    }
                }
                if (isBreak)
                    break;
            }
        }

        render();
    }

    // 字段名称对应后台Json名称简称
    var fieldNameToShortAlias = function (fieldName) {
        if (fieldName == "") { }
    }

    var SearchCheckOrderEvt = function () {
        var $checkboxes = $("#addSearchCondition :checkbox");
        $checkboxes.each(function () {
            $(this).change(function () {
                var name = $(this).attr("name");
                // 移除查询条件或将最后勾选条件移至最后
                for (var i = 0; i < configSearchCondition.length; i++) {
                    var tmpName = configSearchCondition[i].attr("name");
                    if (name == tmpName) {
                        configSearchCondition.splice(i, 1);
                    }
                }

                var isChecked = $(this).prop("checked");
                if (isChecked) {
                    configSearchCondition.push($(this));
                }
                isCheckAllSearchCondition();
            });
        });
    }

    // 判断勾选条件是否全部选中
    var isCheckAllSearchCondition = function () {
        var $checkboxes = $("#addSearchCondition :checkbox");
        if (platformType == "youzan" || platformType == "kuaishou") {
            $checkboxes = $checkboxes.filter("[data-group!='ColorAndSize']");
            $checkboxes = $checkboxes.filter("[data-group!='ColorAndSizeNotContains']");
        }
        else {
            $checkboxes = $checkboxes.filter("[name!='ColorAndSize2']");
            $checkboxes = $checkboxes.filter("[name!='ColorAndSizeNotContains2']");
        }
        var $checkedCheckbox = $checkboxes.filter(":checked");
        $(".search_checkall").prop("checked", $checkedCheckbox.length == $checkboxes.length);
    }


    // 生成备货单
    module.createPurchase = function (isCreatePurches) {
        if (isQuerying) {
            commonModule.preventDefault();
            return;
        }

        var $txtDate = $("#QueryDateVal");
        var startDate = $txtDate.attr('start-date');
        var endDate = $txtDate.attr('end-date');
        if (!!startDate == false || !!endDate == false) {
            layer.msg('时间格式不正确');
            return;
        }

        isFromCreatePurches = isCreatePurches == undefined ? true : isCreatePurches;
        queryData["QueryDateVal"] = { startDate: startDate, endDate: endDate };
        var options = getOptions();
        // 获取生成备货单的查询条件
        var $elmts = $("#form1 label:not(.hide)").find("input[name],select[name]");
        $elmts.each(function () {
            var name = $(this).attr("name");
            var val = $(this).val();
            if (val != "")
                queryData[name] = val;
        });

        // 保存勾选框条件
        var purchaseCheckboxConditionSet = [];
        var $checkboxes = $(".search_condition .checkboxline :checkbox:checked");
        $checkboxes.each(function () {
            var name = $(this).attr("name");
            if (name && name != "")
                purchaseCheckboxConditionSet.push(name);
        });
        // 保存勾选框条件配置
        commonModule.SaveCommonSetting("PurchaseCheckboxConditionSet", JSON.stringify(purchaseCheckboxConditionSet), function (rsp) {
            customFilterConditions = purchaseCheckboxConditionSet;
            //console.log(JSON.stringify(purchaseCheckboxConditionSet));
        });

        var tipMessage = (isFromCreatePurches ? "正在生成备货单" : "正在生成拿货小标签") + "，请稍等……";
        // 生成备货单使用当前配置
        printFieldsConfig = module.DefaultPurchaseSetting().Setting;
        options.IsNewTemplate = !_isUseOldTemplate;
        isQuerying = true;
        commonModule.Ajax({
            type: 'post',
            url: '/Purchases/GetPurchaseList',
            data: options,
            loadingMessage: tipMessage,
            success: function (result) {
                if (result.Success && result.Data) {

                    if (result.Data.FromModule == "生成备货单") {
                        layer.msg("后台正在生成备货单，请稍候…");

                        commonModule.CreatePurchaseTask = result.Data || null;
                        //$("#ExportProgress").show();
                        //disbleCreatePurchaseBtn();
                        //$("#CreatePurchase").attr("style", "background: linear-gradient(#ddd, #ddd 50%, #ddd);");
                        //$("#CreatePurchase").removeAttr("onclick");
                        //$("#newpurchases_content_main").html('<div style="text-align:center;margin-top:50px;font-size: 14px;"><span class="tip-content"> 请设置筛选条件后，生成备货单</span></div>');
                        //module.CheckExportTaskStatus(true);
                        return;
                    }

                    _isUseOldTemplate = getTemplateType();
                    $(".tip-content").hide();
                    //console.log(JSON.stringify(result.Data));
                    $(".display_setting").attr("data-historyId", "");
                    $(".display_setting").attr("data-from", "create");
                    // 生成备货单使用系统显示列的配置
                    var setting = module.PurchaseSetting() || {};
                    setting.PurchaseItemsModel = result.Data || {};
                    var logId = result.Data.LogId;
                    var date1 = new Date();  //开始时间
                    packageOrderData();
                    render();
                    var date2 = new Date();    //结束时间
                    var ms = date2.getTime() - date1.getTime()  //时间差的毫秒数
                    upadteRenderTime(logId, ms);
                }
                else {
                    layer.msg(result.Message, { icon: 2, time: 4000 });
                }
                isQuerying = false;
                editable = false;
            }
        });
    }

    var loadRemarkOrdersInfo = function (pid) {
        var $trs = $(".stockup_table_content tr[class*='tr-pid-" + pid + "']");
        if ($trs.length > 0) return;

        var setting = module.PurchaseSetting() || {};
        var items = (setting.PurchaseItemsModel || {}).PurchaseItems;

        colSpan = printFieldsConfig.DisplayFields.length;
        $(printFieldsConfig.DisplayFields).each(function (i, item) {
            if (item.Value.indexOf("Sum-") != -1)
                colSpan--;
        });
        var html = "";
        var defaultNoImgUrl = "http://img.dgjapp.com/nopicurl.jpg";
        $(items).each(function (i, item) {
            if (item.ProductId != pid) return;

            var orders = item.Orders || [];
            $(orders).each(function (ii, order) {
                html += '<tr class="remarkParticulars tr-detail-' + order.Id + ' tr-pid-' + item.ProductId + '">';
                html += '   <td colspan="' + colSpan + '">';
                html += '       <div class="clearfix" style="padding-left: 10px">';
                html += '           <div class="remarkParticulars_left">';
                if (platformType == "taobao") {
                    html += '               <div>淘宝昵称：' + (order.BuyerWangWang || "") + '(' + (order.PlatformOrderId || "") + ')</div>';
                }
                else {
                    html += '               <div>买家昵称：' + (order.BuyerWangWang || "") + '(' + (order.PlatformOrderId || "") + ')</div>';
                }
                
                html += '               <div><p>';

                var flag = order.SellerRemarkFlag || "";
                if (flag == "1")
                    html += '                   <span style="background-position:-0px -20px;background-image: url(\'../Content/Images/allicons.png\')" class="remark_flag remark_flag_' + flag + '">&nbsp;</span>';
                else if (flag == "2")
                    html += '                   <span style="background-position:-42px -20px;background-image: url(\'../Content/Images/allicons.png\')" class="remark_flag_' + flag + '">&nbsp;</span>';
                else if (flag == "3")
                    html += '                   <span style="background-position:-28px -20px;background-image: url(\'../Content/Images/allicons.png\')" class="remark_flag_' + flag + '">&nbsp;</span>';
                else if (flag == "4")
                    html += '                   <span style="background-position:-14px -20px;background-image: url(\'../Content/Images/allicons.png\')" class="remark_flag_' + flag + '">&nbsp;</span>';
                else if (flag == "5")
                    html += '                   <span style="background-position:-56px -20px;background-image: url(\'../Content/Images/allicons.png\')" class="remark_flag_' + flag + '">&nbsp;</span>';
                html += '                   备注: <span>' + (order.SellerRemark || "") + '</span>&nbsp;&nbsp;留言: <span>' + (order.BuyerRemark || "") + '</span></p>';
                html += '               </div></div>';

                html += '               <div class="remarkParticulars_right" onclick="purchaseModule.showOrderItemDetail(this)">';
                html += '                   <span class="collapse">展开</span><span class="icon_show showRemarkMore"></span>';
                html += '               </div>';
                html += '           </div>';
                html += '       <div class="remarkParticulars_content showRemarkMoreShow"><ul>';

                for (var n = 0; n < order.Items.length; n++) {
                    var orderItem = order.Items[n];
                    var imgUrl = orderItem.SkuImgUrl || "";
                    var color = orderItem.Color || "";
                    var size = (orderItem.Size || "").trimStartDgj(";");
                    var productId = orderItem.ProductId || "";
                    var productSubject = orderItem.ProductSubject || "";

                    html += '           <li>';
                    html += '           <img src="' + (imgUrl == "" ? defaultNoImgUrl : imgUrl) + '" alt="" style="width: 45px; height: 45px; display: inline-block"  onclick="commonModule.openProductUrl(\'' + productId + '\')">';
                    html += '           <div><p>' + productSubject + '</p > <p>颜色：<span>' + color + '</span>&nbsp;&nbsp;尺码：<span>' + size + '</span></p></div>';
                    html += '           </li>';
                }
                html += '       </ul></div>';
            });
        });

        $("tr[class='remarkHide'][data-pid='" + pid + "']").prop("outerHTML", html)
    }

    // 生成拿货小标签
    module.createNaHuoLabel = function (isLoaded) {
        var setting = module.PurchaseSetting() || {};
        if (!isLoaded)
            module.createPurchase(true);
        else {

        }
    }

    // 组装留言订单数据
    var packageOrderData = function () {
        var setting = module.PurchaseSetting() || {};
        var orders = (setting.PurchaseItemsModel || {}).Orders;
        var items = (setting.PurchaseItemsModel || {}).PurchaseItems;

        $(items).each(function (i, item) {
            if (item.HasMsgOrRemark) {
                $(orders).each(function (j, o) {
                    var isExists = false;
                    $(o.Items).each(function (k, oi) {
                        if (oi.ProductId == item.ProductId && (o.SellerRemark || o.BuyerRemark || o.SellerRemarkFlag)) {
                            isExists = true;
                            return;
                        }
                    });
                    if (isExists) {
                        item.Orders.push(o);
                    }
                });
            }
        });
    }


    module.PurchasesNaHuoLabelConfig = function () {
        common.LoadCommonSetting("PurchasesNaHuoLabelConfig", false, function (rsp) {
            if (common.IsError(rsp)) return;

            purchasesNaHuoLabelConfig = JSON.parse(rsp.Data) || {};

        });
    }

    // 打印拿货小标签
    module.printNaHuoLabel = function (tmpId) {
        var setting = module.PurchaseSetting() || {};
        var purchaseItemsModel = setting.PurchaseItemsModel || {};
        if (purchaseItemsModel.Orders == undefined || purchaseItemsModel.Orders.length == 0) {
            layer.msg("无商品数据，无需统计拿货标签");
            return;
        }
        if (lp.check(true)) {
            loadNaHuoPrinter();
            loadNaHuoTemplateList();


            //默认打印机  --- 显示 //模板是在加载时处理
            if (purchasesNaHuoLabelConfig.Printer && $("#nahuos-printer-select").find("option[value='" + purchasesNaHuoLabelConfig.Printer + "']").length > 0)
                $("#nahuos-printer-select").val(purchasesNaHuoLabelConfig.Printer);
            else
                $("#nahuos-printer-select").val("");



            layer.open({
                type: 1,
                title: "打印拿货标签", //不显示标题
                content: $('.naHuoLabelPrintClasee'),
                area: ['580', '160'], //宽高
                btn: ["直接打印", "关闭"],
                yes: function () {
                    var templateId = $("#nahuos-template-select").val();
                    if (templateId == "" || templateId == 0) {
                        layer.msg("请选择模板!", { icon: 2 });
                        return;
                    }
                    var printerId = $("#nahuos-printer-select").val();
                    //var printer = $("#nahuos-printer-select option:selected").text();
                    var printer = $("#nahuos-printer-select").val();

                    if (printerId == "") {
                        layer.msg("请选择打印机!", { icon: 2 });
                        return;
                    }

                    var loding = layer.load(2, { shade: false });
                    //module.piciPrintNaHuoLable("", templateId, false, printerId,1, 1264,"");

                    var options = { "TempId": templateId, "Printer": printer, "Orders": JSON.stringify(purchaseItemsModel.Orders) };
                    common.Ajax({
                        url: '/Purchases/PrintNaHuoLabel',
                        data: options,
                        type: 'POST',
                        success: function (rsp) {
                            module.PurchasesNaHuoLabelConfig();

                            layer.close(loding);
                            if (rsp.Success) {
                                var batchNo = rsp.Data.batchNo;
                                var shopId = rsp.Data.ShopIds;
                                module.piciPrintNaHuoLable(batchNo, templateId, false, printerId, 1, shopId, "");
                            }
                            else
                                layer.msg("生成拿货小标签失败：" + rsp.Message);
                        }
                    });


                },
                cancel: function () {
                    layer.closeAll();
                }
            });
        }// lp.check(true)   end 

    }


    module.piciPrintNaHuoLable = function (batchNo, templateId, isLoad, printerId, indexNumber, shopId, lastBazaarStallCanShu) {
        common.Ajax({
            url: '/Purchases/PrintNaHuoLabelPiCi',
            data: { piCi: batchNo, tempId: templateId, pageIndex: indexNumber, ShopIdList: JSON.stringify(shopId), bazaarStall: lastBazaarStallCanShu },
            type: 'POST',
            success: function (rsp) {
                if (!isLoad)
                    layer.closeAll();

                if (rsp.Success) {
                    var data = rsp.Data
                    isPrinting = true;

                    $(".progress-pici-byHuoLabelPrint").show();

                    var baifenbi = parseInt((parseInt(indexNumber) / parseInt(data.PrintNums)) * 100);

                    $(".dgj-progress-bar").css({ width: baifenbi + '%' })
                    $(".dgj-progress-percent").html(data.CurrLabelCount + "/" + data.TotalLabelCount)
                    //$(".dgj-progress-percent").html(indexNumber + "/" + data.PrintNums)


                    postToLodop(data, batchNo, templateId, printerId, indexNumber, data.PrintNums, shopId, data.lastBazaarStall);

                    //注释掉，循环改到postToLodop 的打印中，打印成功后才接着打印
                    //if (indexNumber <= data.PrintNums) {
                    //    indexNumber++;
                    //    module.piciPrintNaHuoLable(batchNo, templateId, true, printerId, indexNumber)
                    //}
                    //else {
                    //    $(".progress-pici-byHuoLabelPrint").hide();
                    //    isPrinting = false;
                    //}

                }
                else {
                    layer.msg("失败：" + rsp.Message);
                    isPrinting = false;
                }


            }
        });


    }

    function postToLodop(data, batchNo, templateId, printerIndex, indexNumber, allNumber, shopId, lastBazaarStall) {

        try {
            var w = data.PageWidth;
            var h = data.PageHeight;
            var ox = parseFloat(data.PageX);
            var oy = parseFloat(data.PageY);
            var result;
            var isPrintTypeGlobal = 0;
            var list = data.TemplateList;
            var listDocSize = list.length;
            LODOP.PRINT_INIT("店管家拿货小标签打印");
            var sepid = 1;
            for (var i = 0; i < list.length; i++) {
                LODOP.SET_PRINT_PAGESIZE(1, w + "mm", h + "mm", "");
                var item = list[i];
                if (!item.InputText)
                    item.InputText = "";
                var els = item.TemplateInputList;
                for (var j = 0; j < els.length; j++) {
                    var el = els[j];
                    var top = parseFloat(el.Y) + oy;
                    var left = parseFloat(el.X) + ox;
                    var iw = el.Width;
                    var ih = el.Height;
                    var txt = unescape(el.InputText);
                    if (el.controlId == 'WaybillCodeSerialCode') {
                        txt = (i + 1) + "/" + listDocSize;
                    }
                    if (txt == "")
                        continue;

                    if (el.IsCode != "" && el.IsCode != undefined) {
                        LODOP.ADD_PRINT_BARCODE(top, left, iw, ih, el.IsCode, txt);
                        LODOP.SET_PRINT_STYLEA(0, "ShowBarText", 0);
                    } else if (txt == "sxh001" || txt == "sxs001" || txt == "xxh001" || txt == "xxs001") {
                        if (txt == "sxh001")  //横线（实体）
                            LODOP.ADD_PRINT_LINE(top, left, top, left + iw, 0, el.FontSize);

                        if (txt == "sxs001") //竖线（实体）
                            LODOP.ADD_PRINT_LINE(top, left, top + ih, left, 0, el.FontSize);

                        if (txt == "xxh001") //横线（虚线）
                            LODOP.ADD_PRINT_LINE(top, left, top, left + iw, 2, el.FontSize);

                        if (txt == "xxs001") //竖线（虚线）
                            LODOP.ADD_PRINT_LINE(top, left, top + ih, left, 2, el.FontSize);
                    } else if (el.controlType == "8" && el.InputText != '') { //图片

                        LODOP.ADD_PRINT_IMAGE(top, left, iw, ih, "<img border='0' style='" + "width:" + iw + ";height:" + ih + ";" + "' src='" + el.InputText + "' />");
                        LODOP.SET_PRINT_STYLEA(0, "Stretch", 2);
                    }
                    else {
                        if (el.IsText) {
                            if (el.highlimit == "1" || isPrintTypeGlobal == 3)
                                LODOP.ADD_PRINT_TEXT(top, left, iw, ih, txt);
                            else
                                LODOP.ADD_PRINT_TEXT(top, left, iw, ih, txt);

                            if (isPrintTypeGlobal == 3)
                                LODOP.SET_PRINT_STYLEA(sepid, "ReadOnly", 0);

                            LODOP.SET_PRINT_STYLEA(sepid, "LineSpacing", -4); //设置行间距
                            LODOP.SET_PRINT_STYLEA(sepid, "FontName", el.FontFamily); //el.FontFamily设置字体大小
                            LODOP.SET_PRINT_STYLEA(sepid, "FontSize", el.FontSize); //设置字体大小
                            LODOP.SET_PRINT_STYLEA(sepid, "Bold", el.FontWeight); //设置加粗
                            if (el.controlId == "ShortAddress") {
                                LODOP.SET_PRINT_STYLEA(sepid, "Alignment", 2); //设置行间距
                            }
                        } else {
                            var strHtml = txt.replace(eval("/<br>/gi"), "\n");
                            if (strHtml == "null")
                                continue;
                            if (el.highlimit == "1" || isPrintTypeGlobal == 3)
                                LODOP.ADD_PRINT_TEXT(top, left, iw, ih, strHtml);
                            else
                                LODOP.ADD_PRINT_TEXT(top, left, iw, ih, strHtml);

                            if (isPrintTypeGlobal == 3)
                                LODOP.SET_PRINT_STYLEA(sepid, "ReadOnly", 0);

                            LODOP.SET_PRINT_STYLEA(sepid, "LineSpacing", -4); //设置行间距
                            LODOP.SET_PRINT_STYLEA(sepid, "FontName", el.FontFamily); //设置字体大小
                            LODOP.SET_PRINT_STYLEA(sepid, "FontSize", el.FontSize); //设置字体大小
                            LODOP.SET_PRINT_STYLEA(sepid, "Bold", el.FontWeight); //设置加粗
                            if (el.controlId == "ShortAddress") {
                                LODOP.SET_PRINT_STYLEA(sepid, "Alignment", 2); //设置行间距
                            }
                        }
                    }

                    sepid++;
                }

                if (i + 1 < list.length) {
                    LODOP.NewPageA();
                }
            }
            if (list.length <= 0)
                return;
            LODOP.SET_PRINT_COPIES(1);
            lp.setPrinter(printerIndex);

            //当前这个方法，只要把任务发送给打印机，即可进行下一个
            //LODOP.On_Return = function (TaskID, Value) {
            //    if (Value) {
            //        indexNumber++;
            //        if (indexNumber <= allNumber) {
            //            module.piciPrintNaHuoLable(batchNo, templateId, true, printerIndex, indexNumber, shopId,lastBazaarStall);
            //        } else {
            //            $(".progress-pici-byHuoLabelPrint").hide();
            //                isPrinting = false;
            //        }
            //    }
            //};
            // result = LODOP.PRINT();

            //当前这个方法，只要当前打印任务完成后，才会接着。
            indexNumber++;
            if (indexNumber <= allNumber) {
                PrintWaitLoop(batchNo, templateId, printerIndex, indexNumber, shopId, lastBazaarStall, false);
            } else {
                PrintWaitLoop(batchNo, templateId, printerIndex, indexNumber, shopId, lastBazaarStall, true);
            }


        } catch (e) {
            layer.alert("打印出现异常,尝试调整快递模版再打印!");
        }
    }

    var P_ID = "", TaskID1, TaskID2, waiting = false
    var PrintWaitLoop = function (batchNo, templateId, printerIndex, indexNumber, shopId, lastBazaarStall, isEnd) {
        if (!waiting) {
            LODOP.SET_PRINT_MODE("CATCH_PRINT_STATUS", true);
            if (LODOP.CVERSION) {
                LODOP.On_Return_Remain = true;
                LODOP.On_Return = function (TaskID, Value) {
                    if (TaskID == TaskID1) {
                        P_ID = Value;
                        if ((P_ID != "")) {
                            waiting = true;
                            setTimeout(PrintWaitLoop(batchNo, templateId, printerIndex, indexNumber, shopId, lastBazaarStall, isEnd), 200);
                        } else {
                            layer.msg("请检查打印机是否已停止打印！", { icon: 2 });
                        };
                    } else
                        if (TaskID == TaskID2 && Value == 0) {
                            nahuoPrintCallback(batchNo, templateId, indexNumber, shopId);
                            waiting = false;
                            if (!isEnd)
                                module.piciPrintNaHuoLable(batchNo, templateId, true, printerIndex, indexNumber, shopId, lastBazaarStall);
                            else {
                                $(".progress-pici-byHuoLabelPrint").hide();
                                isPrinting = false;
                            }
                        } else
                            setTimeout(PrintWaitLoop(batchNo, templateId, printerIndex, indexNumber, shopId, lastBazaarStall, isEnd), 200); //else 默认还是 waiting==true

                };
                TaskID1 = LODOP.PRINT();
            } else {
                P_ID = LODOP.PRINT();
                if ((P_ID != "")) {
                    waiting = true;
                    setTimeout(PrintWaitLoop(batchNo, templateId, printerIndex, indexNumber, shopId, lastBazaarStall, isEnd), 200);
                } else {
                    layer.msg("请检查打印机是否已停止打印！", { icon: 2 });
                }
            };
        } else {
            if (LODOP.CVERSION) {
                TaskID2 = LODOP.GET_VALUE("PRINT_STATUS_EXIST", P_ID);
            } else {
                if (!LODOP.GET_VALUE("PRINT_STATUS_EXIST", P_ID)) {
                    waiting = false;//以是否还在队列为准
                    nahuoPrintCallback(batchNo, templateId, indexNumber, shopId);
                    if (!isEnd)
                        module.piciPrintNaHuoLable(batchNo, templateId, true, printerIndex, indexNumber, shopId, lastBazaarStall);
                    else {
                        $(".progress-pici-byHuoLabelPrint").hide();
                        isPrinting = false;
                    }
                } else
                    setTimeout(PrintWaitLoop(), 200);
            };
        }
    }



    var nahuoPrintCallback = function (batchNo, templateId, indexNumber, shopId) {
        common.Ajax({
            url: '/Purchases/PrintNaHuoLabelPrintCallback',
            data: { piCi: batchNo, tempId: templateId, pageIndex: indexNumber - 1, ShopIdList: JSON.stringify(shopId) },
            type: 'POST',
            success: function (rsp) {

            }
        });
    }






    var loadNaHuoPrinter = function loadNaHuoPrinter() {
        var printers = lp.printers();

        var html = "<option value='' >请选择打印机</option>";
        for (var i = 0; i < printers.length; i++) {
            html += '<option value="' + printers[i].name + '" >' + printers[i].name + '</option>';
        }
        $("#nahuos-printer-select").html(html);
    }

    var loadNaHuoTemplateList = function loadNaHuoTemplateList() {
        common.Ajax({
            url: '/SetProductFlag/GetNaHuoTemplates',
            type: 'POST',
            success: function (rsp) {
                var html = "<option value='0' >请选择模板</option>";
                if (rsp.Success) {
                    var data = rsp.Data;

                    for (var i = 0; i < data.length; i++) {
                        if (data[i].IsDefault == true)
                            html += '<option value="' + data[i].Id + '" selected>' + data[i].TemplateName + '</option>';
                        else
                            html += '<option value="' + data[i].Id + '" >' + data[i].TemplateName + '</option>';
                    }

                }
                $("#nahuos-template-select").html(html);

                if (purchasesNaHuoLabelConfig.TempId && $("#nahuos-template-select").find("option[value='" + purchasesNaHuoLabelConfig.TempId + "']").length > 0)
                    $("#nahuos-template-select").val(purchasesNaHuoLabelConfig.TempId);
                else
                    $("#nahuos-template-select").val(0);
            }
        });
    }



    var upadteRenderTime = function (logId, ms) {
        var options = { logId: logId, ms: ms };
        common.Ajax({
            url: '/Order/UpdateRenderTime',
            data: options,
            type: 'POST',
            success: function (rsp) { }
        });
    }

    module.setStalls = function () {
        if (isQuerying) {
            commonModule.preventDefault();
            return;
        }
        //window.location.href = commonModule.rewriteUrl('/SetMarketStalls/');
        window.open(commonModule.rewriteUrl('/SetMarketStalls/'), '_blank');
    }
    //$table区别于显示的table和打印table
    var displayColumns = function ($table, type) {
        // $table不为空表示列表设置，否则打印设置
        var fields = (type != "print" ? printFieldsConfig.DisplayFields : printFieldsConfig.PrintFields) || [];

        var $ths = $table.find(">thead>tr>th");
        var $tds = $table.find(">tbody>tr:not('.remarkParticulars'):not('.table_footer')>td:not('.td_remark_icon')");
        //隐藏所以的单元格
        $ths.hide();
        $tds.hide();
        $table.find(".sum-totalitemcount").hide();
        $table.find(".sum-totalamount").hide();
        $table.find(".sum-totalpurchaseprice").hide();
        $table.find(".sum-totalprofit").hide();
        $table.find(".table_footer").hide();

        // 显示设置的单元格
        var colSpan = 0;
        // 显示设置的单元格
        for (var j = 0; j < fields.length; j++) {
            if (fields[j].Value == "Sum-TotalItemCount" || fields[j].Value == "Sum-TotalAmount" || fields[j].Value == "Sum-TotalPurchasePrice" || fields[j].Value == "Sum-TotalProfit")
                $table.find(".table_footer,." + fields[j].Value.toLowerCase()).show();
            else {
                $ths.each(function (i, th) {
                    var index = $(th).attr("data-index");
                    var clas = $(th).attr("class");
                    if (clas == fields[j].Value) {
                        colSpan++;
                        $(th).show();
                        $table.find("tbody>tr:not('.remarkParticulars'):not('.table_footer')>td[data-index=" + index + "]").show();
                        return false;
                    }
                });
            }
        }
        $table.find(".table_footer>td[colspan],tr.remarkParticulars>td[colspan]").attr("colspan", colSpan);
    }

    var render = function () {
        $(".stockup_table_title").show();
        displayTemplateStyle(_isUseOldTemplate);

        if (!_isUseOldTemplate) {
            //渲染新模板
            render_New();
            return;
        }

        console.time("生成html内容");
        var setting = module.PurchaseSetting() || {};
        var dataSource = (setting.PurchaseItems || setting.PurchaseItemsModel) || {};
        var items = dataSource.PurchaseItems || [];

        var colSpan = 0;
        for (var i = 0; i < printFieldsConfig.DisplayFields.length; i++) {
            var item = printFieldsConfig.DisplayFields[i];
            if (item.Value.indexOf("Sum-") == -1)
                colSpan++;
        }

        var ProductImgUrlStyle = 'min-width:45px;max-width:180px;';
        var ShortTitleStyle = 'min-width:30px;max-width:90px;';
        var ProductSubjectStyle = 'min-width:100px;max-width:300px;';
        var ProductCargoNumberStyle = 'min-width:45px;max-width:90px;';
        var SkuImgUrlStyle = 'min-width:45px;max-width:180px;';
        var CargoNumberStyle = 'min-width:45px;max-width:90px;';
        var ColorAndSizeStyle = 'min-width:60px;max-width:100px;';
        var ColorStyle = 'min-width:40px;max-width:80px;';
        var SizeStyle = 'min-width:40px;max-width:80px;';
        var CountStyle = 'min-width:30px;max-width:60px;';
        var ItemAmountStyle = 'min-width:40px;max-width:90px;';
        var BazaarStyle = 'min-width:40px;max-width:90px;';
        var StallStyle = 'min-width:40px;max-width:90px;';
        var SingleCostPriceStyle = 'min-width:45px;max-width:90px;';
        var CostPriceStyle = 'min-width:45px;max-width:90px;';
        var PriceStyle = 'min-width:40px;max-width:90px;';
        var ProfitStyle = 'min-width:45px;max-width:90px;';
        var TotalItemCountStyle = 'min-width:30px;max-width:60px;';
        var TotalAmountStyle = 'min-width:45px;max-width:90px;';
        var TotalPurchasePriceStyle = 'min-width:45px;max-width:90px;';
        var TotalProfitStyle = 'min-width:45px;max-width:90px;';

        var html = "";
        html += '<table class="stockup_table_content"><thead><tr>';
        if (commonModule.isUseOldTheme)
            html += '<th data-index="0" class="ProductImgUrl" style="' + ProductImgUrlStyle + '">图片</th>';
        else
            html += '<th data-index="0" class="ProductImgUrl" style="' + ProductImgUrlStyle + '">商品主图</th>';

        html += '<th data-index="1" class="ShortTitle" style="' + ShortTitleStyle + '">简称</th>';
        html += '<th data-index="2" class="ProductSubject" style="' + ProductSubjectStyle + '">商品名称</th>';
        html += '<th data-index="3" class="ProductCargoNumber" style="' + ProductCargoNumberStyle + '">商品货号</th>';
        html += '<th data-index="4" class="SkuImgUrl" style="' + SkuImgUrlStyle + '">规格图</th>';
        if (platformType == "kuaishou") {
            html += '<th data-index="5" class="CargoNumber" style="' + CargoNumberStyle + '">SKU编码</th>';
        }
        else {
            html += '<th data-index="5" class="CargoNumber" style="' + CargoNumberStyle + '">单品货号</th>';
        }
        if (platformType == "youzan" || platformType == "kuaishou") {
            html += '<th data-index="6" class="ColorAndSize" style="' + ColorAndSizeStyle + '">商品规格</th>';
        }
        else {
            html += '<th data-index="6" class="Color" style="' + ColorStyle + '">颜色</th>';
            html += '<th data-index="7" class="Size" style="' + SizeStyle + '">尺码</th>';
        }

        html += '<th data-index="8" class="Count" style="' + CountStyle + '">数量</th>';
        html += '<th data-index="9" class="ItemAmount" style="' + ItemAmountStyle + '">金额</th>';
        //if (platformType != "kuaishou") {
        html += '<th data-index="10" class="Bazaar" style="' + BazaarStyle + '">市场</th>';
        html += '<th data-index="11" class="Stall" style="' + StallStyle + '">档口</th>';
        //}
        if (commonModule.isUseOldTheme)
            html += '<th data-index="12" class="SingleCostPrice" style="' + SingleCostPriceStyle + '">成本价</th>';
        else
            html += '<th data-index="12" class="SingleCostPrice" style="' + SingleCostPriceStyle + '">单品成本</th>';
        html += '<th data-index="13" class="CostPrice" style="' + CostPriceStyle + '">单品总成本</th>';
        html += '<th data-index="14" class="Price" style="' + PriceStyle + '">单价</th>';
        html += '<th data-index="15" class="Profit" style="' + ProfitStyle + '">单品差价</th>';
        html += '<th data-index="16" class="TotalItemCount" style="' + TotalItemCountStyle + '">总量</th>';
        html += '<th data-index="17" class="TotalAmount" style="' + TotalAmountStyle + '">总金额</th>';
        html += '<th data-index="18" class="TotalPurchasePrice" style="' + TotalPurchasePriceStyle + '">总成本</th>';
        html += '<th data-index="19" class="TotalProfit" style="' + TotalProfitStyle + '">总商品差价</th>';
        html += '</tr></thead>';
        html += '<tbody>';

        if (items.length > 0) {
            var itemRowNum = 0;
            var subItemRowNum = 0;
            var imgSize = printFieldsConfig.SettingParams.ImgSize.split("x");
            var imgWidth = imgSize[0];
            var imgHeight = imgWidth;
            var defaultNoImgUrl = "http://img.dgjapp.com/nopicurl.jpg";
            for (var i = 0; i < items.length; i++) {
                var item = items[i];
                var subItems = item.SubItems || [];
                var rowCount = subItems.length;
                var rowSpan = rowCount <= 1 ? "" : "rowspan =" + rowCount;
                var rowIndex = 0;
                var productImgUrl = item.ProductImgUrl;
                // 商品不同规格属性行数据
                for (var ii = 0; ii < rowCount; ii++) {
                    var subItem = subItems[ii];
                    var productAttrImgUrl = subItem.SkuImgUrl || defaultNoImgUrl;
                    if (productImgUrl && productImgUrl.indexOf("nopic.gif") != -1)
                        productImgUrl = productAttrImgUrl;
                    var attrImgSize = printFieldsConfig.SettingParams.AttrImgSize.split("x");
                    var attrImgWidth = attrImgSize[0];
                    var attrImgHeight = attrImgWidth;

                    if (platformType == "alibaba") {
                        // 剔除尺寸取大图
                        $("#setting-img-size option,#setting-img-size option").each(function () {
                            var text = $(this).val() + ".jpg";
                            if (productImgUrl.indexOf(text) != -1)
                                productImgUrl = productImgUrl.replaceAll(text, "jpg");
                            if (productAttrImgUrl.indexOf(text) != -1)
                                productAttrImgUrl = productAttrImgUrl.replaceAll(text, "jpg");
                        });
                    }

                    var pid = platformType == "jingdong" ? subItem.SkuId : item.ProductId;
                    if (rowCount == 1) {
                        html += '<tr data-pid="' + item.ProductId + '" data-sku="' + subItem.SkuId + '" data-rowcount="' + rowCount + '">';
                        if (commonModule.isUseOldTheme) {
                            item.ProductImgUrl = productImgUrl == defaultNoImgUrl ? productAttrImgUrl : productImgUrl;
                            html += '   <td class="productImg" data-index="0"  style="' + ProductImgUrlStyle + '" contenteditable="false"><div class="imgbox"><img src="' + item.ProductImgUrl + '" alt="" width="' + imgWidth + '" height="' + imgHeight + '" style="width: ' + imgWidth + 'px; height:' + imgHeight + 'px; cursor: pointer; " onclick="commonModule.openProductUrl(\'' + pid + '\')"></div></td>';
                        }
                        else
                            html += '   <td class="productImg" data-index="0"  style="' + ProductImgUrlStyle + '" contenteditable="false"><div class="imgbox"><img src="' + productImgUrl + '" alt="" width="' + imgWidth + '" height="' + imgHeight + '" style="width: ' + imgWidth + 'px; height:' + imgHeight + 'px; cursor: pointer; " onclick="commonModule.openProductUrl(\'' + pid + '\')"></div></td>';

                        html += '   <td data-index="1"  style="' + ShortTitleStyle + '"><span contenteditable="false">' + (item.ShortTitle || "&nbsp;") + '</span></td>';
                        if (commonModule.isUseOldTheme)
                            html += '   <td class="productSubject" data-index="2"  style="' + ProductSubjectStyle + '"><span onclick="commonModule.openProductUrl(\'' + pid + '\')" style="text-decoration: underline;color: blue;">' + (item.ProductSubject || "&nbsp;") + '</span></td>';
                        else
                            html += '   <td class="productSubject" data-index="2"  style="' + ProductSubjectStyle + '"><span contenteditable="false">' + (item.ProductSubject || "&nbsp;") + '</span></td>';

                        html += '   <td data-index="3" style="' + ProductCargoNumberStyle + '"><span contenteditable="false">' + (item.ProductCargoNumber || "&nbsp;") + '</span></td>';
                        html += '   <td class="productAttrImg" data-index="4" style="' + SkuImgUrlStyle + '" contenteditable="false"><div class="attrimgbox"><img src="' + productAttrImgUrl + '" alt="" width="' + attrImgWidth + '" height="' + attrImgHeight + '" style="width: ' + attrImgWidth + 'px; height:' + attrImgHeight + 'px; cursor: pointer; " onclick="commonModule.openProductUrl(\'' + pid + '\')"></div></td>';
                        html += '   <td data-index="5" style="' + CargoNumberStyle + '"><span contenteditable="false">' + (subItem.CargoNumber || "&nbsp;") + '</span></td>';
                        if (platformType == "youzan" || platformType == "kuaishou") {
                            html += '   <td data-index="6" style="' + ColorAndSizeStyle + '"><span contenteditable="false">' + (subItem.Color || "&nbsp;") + '</span></td>';
                        }
                        else {
                            html += '   <td data-index="6" style="' + ColorStyle + '"><span contenteditable="false">' + (subItem.Color || "&nbsp;") + '</span></td>';
                            html += '   <td data-index="7" style="' + SizeStyle + '"><span contenteditable="false">' + (subItem.Size || "&nbsp;").trimStartDgj(";") + '</span></td>';
                        }

                        subItem.Count = subItem.Count || 0;
                        subItem.ItemAmount = commonModule.ToFixed((subItem.ItemAmount || 0), 2);
                        subItem.SingleCostPrice = commonModule.ToFixed((subItem.SingleCostPrice || 0), 2);
                        subItem.CostPrice = commonModule.ToFixed((subItem.CostPrice || 0), 2);
                        subItem.Price = commonModule.ToFixed((subItem.Price || 0), 2);
                        subItem.Profit = commonModule.ToFixed((subItem.Profit || 0), 2);
                        item.TotalItemCount = (item.TotalItemCount || 0);
                        item.TotalAmount = commonModule.ToFixed((item.TotalAmount || 0), 2);
                        item.TotalPurchasePrice = commonModule.ToFixed((item.TotalPurchasePrice || 0), 2);
                        item.TotalProfit = commonModule.ToFixed((item.TotalProfit || 0), 2);
                        html += '   <td data-index="8" style="' + CountStyle + '"><span contenteditable="false">' + subItem.Count + '</span></td>';
                        html += '   <td data-index="9" style="' + ItemAmountStyle + '"><span contenteditable="false">' + subItem.ItemAmount + '</span></td>';
                        //if (platformType != "kuaishou") {
                        html += '   <td data-index="10" style="' + BazaarStyle + '"><span contenteditable="false">' + (subItem.Bazaar || "&nbsp;") + '</span></td>';
                        html += '   <td data-index="11" style="' + StallStyle + '"><span contenteditable="false">' + (subItem.Stall || "&nbsp;") + '</span></td>';
                        //}
                        //html += '   <td data-index="12" style="min-width:50px;"><span contenteditable="false">' + (subItem.CostPrice || 0).toFixed(2) + '</span></td>';                                                                                                                                                                     
                        html += '   <td data-index="12" style="' + SingleCostPriceStyle + '"><span contenteditable="false">' + subItem.SingleCostPrice + '</span></td>';
                        html += '   <td data-index="13" style="' + CostPriceStyle + '"><span contenteditable="false">' + subItem.CostPrice + '</span></td>';
                        html += '   <td data-index="14" style="' + PriceStyle + '"><span contenteditable="false">' + subItem.Price + '</span></td>';
                        html += '   <td data-index="15" style="' + ProfitStyle + '"><span contenteditable="false">' + subItem.Profit + '</span></td>';
                        html += '   <td data-index="16" style="' + TotalItemCountStyle + '"><span contenteditable="false">' + item.TotalItemCount + '</span></td>';
                        html += '   <td data-index="17" style="' + TotalAmountStyle + '"><span contenteditable="false">' + item.TotalAmount + '</span></td>';
                        html += '   <td data-index="18" style="' + TotalPurchasePriceStyle + '"><span contenteditable="false">' + item.TotalPurchasePrice + '</span></td>';
                        html += '   <td data-index="19" ' + rowSpan + '  style="' + TotalProfitStyle + '"><span contenteditable="false">' + item.TotalProfit + '</span></td>';
                        html += '   <td class="td_remark_icon" data-index="20" style="border:none;">';
                        if (item.HasMsgOrRemark) {
                            html += '   <span class="remark_icon showRemark collapse" onclick="purchaseModule.showPurchaseDetail(\'' + pid + '\');"></span>';
                        }
                        html += '</td></tr>';
                    }
                    else {
                        html += '<tr data-pid="' + item.ProductId + '" data-sku="' + subItem.SkuId + '" data-rowcount="' + rowCount + '">';
                        if (rowIndex == 0) {
                            if (commonModule.isUseOldTheme) {
                                item.ProductImgUrl = productImgUrl == defaultNoImgUrl ? productAttrImgUrl : productImgUrl;
                                html += '   <td class="productImg" data-index="0"' + rowSpan + ' style="' + ProductImgUrlStyle + '" contenteditable="false"><div class="imgbox"><img src="' + item.ProductImgUrl + '" alt="" width="' + imgWidth + '" height="' + imgHeight + '" style="width: ' + imgWidth + 'px; height:' + imgHeight + 'px; cursor: pointer; " onclick="commonModule.openProductUrl(\'' + pid + '\')"></div></td>';
                            }
                            else
                                html += '   <td class="productImg" data-index="0"' + rowSpan + ' style="' + ProductImgUrlStyle + '" contenteditable="false"><div class="imgbox"><img src="' + productImgUrl + '" alt="" width="' + imgWidth + '" height="' + imgHeight + '" style="width: ' + imgWidth + 'px; height:' + imgHeight + 'px; cursor: pointer; " onclick="commonModule.openProductUrl(\'' + pid + '\')"></div></td>';

                            html += '   <td data-index="1" ' + rowSpan + ' style="' + ShortTitleStyle + '"><span contenteditable="false">' + (item.ShortTitle || "&nbsp;") + '</span></td>';
                            if (commonModule.isUseOldTheme)
                                html += '   <td class="productSubject" data-index="2" style="' + ProductSubjectStyle + '" ' + rowSpan + ' ><span onclick="commonModule.openProductUrl(\'' + pid + '\')" style="text-decoration: underline;color: blue;">' + (item.ProductSubject || "&nbsp;") + '</span></td>';
                            else
                                html += '   <td class="productSubject" data-index="2" style="' + ProductSubjectStyle + '" ' + rowSpan + ' style="color:#1295c1"><span contenteditable="false">' + (item.ProductSubject || "&nbsp;") + '</span></td>';

                            html += '   <td data-index="3" style="' + ProductCargoNumberStyle + '" ' + rowSpan + '><span contenteditable="false">' + (item.ProductCargoNumber || "&nbsp;") + '</span></td>';
                        }

                        html += '   <td class="productAttrImg" data-index="4" style="' + SkuImgUrlStyle + '" contenteditable="false"><div class="attrimgbox"><img src="' + productAttrImgUrl + '" alt="" width="' + attrImgWidth + '" height="' + attrImgHeight + '" style="width: ' + attrImgWidth + 'px; height:' + attrImgHeight + 'px; cursor: pointer; " onclick="commonModule.openProductUrl(\'' + pid + '\')"></div></td>';
                        html += '   <td data-index="5" style="' + CargoNumberStyle + '"><span contenteditable="false">' + (subItem.CargoNumber || "&nbsp;") + '</span></td>';
                        if (platformType == "youzan" || platformType == "kuaishou") {
                            html += '   <td data-index="6" style="' + ColorAndSizeStyle + '"><span contenteditable="false">' + (subItem.Color || "&nbsp;") + '</span></td>';
                        }
                        else {
                            html += '   <td data-index="6" style="' + ColorStyle + '"><span contenteditable="false">' + (subItem.Color || "&nbsp;") + '</span></td>';
                            html += '   <td data-index="7" style="' + SizeStyle + '"><span contenteditable="false">' + (subItem.Size || "&nbsp;").trimStartDgj(";") + '</span></td>';
                        }
                        subItem.Count = (subItem.Count || 0);
                        subItem.ItemAmount = commonModule.ToFixed((subItem.ItemAmount || 0), 2);
                        subItem.SingleCostPrice = commonModule.ToFixed((subItem.SingleCostPrice || 0), 2);
                        subItem.CostPrice = commonModule.ToFixed((subItem.CostPrice || 0), 2);
                        subItem.Price = commonModule.ToFixed((subItem.Price || 0), 2);
                        subItem.Profit = commonModule.ToFixed((subItem.Profit || 0), 2);
                        item.TotalItemCount = (item.TotalItemCount || 0);
                        item.TotalAmount = commonModule.ToFixed((item.TotalAmount || 0), 2);
                        item.TotalPurchasePrice = commonModule.ToFixed((item.TotalPurchasePrice || 0), 2);
                        item.TotalProfit = commonModule.ToFixed((item.TotalProfit || 0), 2);
                        html += '   <td data-index="8" style="' + CountStyle + '"><span contenteditable="false">' + subItem.Count + '</span></td>';
                        html += '   <td data-index="9" style="' + ItemAmountStyle + '"><span contenteditable="false">' + subItem.ItemAmount + '</span></td>';
                        //if (platformType != "kuaishou") {
                        html += '   <td data-index="10" style="' + BazaarStyle + '"><span contenteditable="false">' + (subItem.Bazaar || "&nbsp;") + '</span></td>';
                        html += '   <td data-index="11" style="' + StallStyle + '"><span contenteditable="false">' + (subItem.Stall || "&nbsp;") + '</span></td>';
                        //}
                        //html += '   <td data-index="12" style="min-width:50px;"><span contenteditable="false">' + (subItem.CostPrice || 0).toFixed(2) + '</span></td>';
                        html += '   <td data-index="12" style="' + SingleCostPriceStyle + '"><span contenteditable="false">' + subItem.SingleCostPrice + '</span></td>';
                        html += '   <td data-index="13" style="' + CostPriceStyle + '"><span contenteditable="false">' + subItem.CostPrice + '</span></td>';
                        html += '   <td data-index="14" style="' + PriceStyle + '"><span contenteditable="false">' + subItem.Price + '</span></td>';
                        html += '   <td data-index="15" style="' + ProfitStyle + '"><span contenteditable="false">' + subItem.Profit + '</span></td>';

                        if (rowIndex == 0) {
                            html += '   <td data-index="16" style="' + TotalItemCountStyle + '"  ' + rowSpan + '><span contenteditable="false">' + item.TotalItemCount + '</span></td>';
                            html += '   <td data-index="17" style="' + TotalAmountStyle + '"  ' + rowSpan + '><span contenteditable="false">' + item.TotalAmount + '</span></td>';
                            html += '   <td data-index="18" style="' + TotalPurchasePriceStyle + '" ' + rowSpan + '><span contenteditable="false">' + item.TotalPurchasePrice + '</span></td>';
                            html += '   <td data-index="19" style="' + TotalProfitStyle + '" ' + rowSpan + '><span contenteditable="false">' + item.TotalProfit + '</span></td>';
                            html += '   <td class="td_remark_icon" data-index="20" style="border:none;">';
                            if (item.HasMsgOrRemark) {
                                html += '   <span class="remark_icon showRemark collapse" onclick="purchaseModule.showPurchaseDetail(\'' + pid + '\');"></span>';
                            }
                            html += '</td>';
                        }
                        html += "</tr>";
                    }
                    rowIndex++;
                    subItemRowNum++;
                }
                // 加载有该商品且有留言备注的订单基本信息
                if (item.HasMsgOrRemark) {
                    html += '<tr style="display:none;" class="remarkHide" data-pid="' + item.ProductId + '"></tr>';

                    //var orders = item.Orders || [];
                    //for (var iii = 0; iii < orders.length; iii++) {
                    //    var order = orders[iii];
                    //    html += '<tr class="remarkParticulars tr-detail-' + order.Id + ' tr-pid-' + item.ProductId + '">';
                    //    html += '   <td colspan="' + colSpan + '">';
                    //    html += '       <div class="clearfix" style="padding-left: 10px">';
                    //    html += '           <div class="remarkParticulars_left">';
                    //    html += '               <div>淘宝昵称：' + (order.BuyerWangWang || "") + '(' + (order.PlatformOrderId || "") + ')</div>';
                    //    html += '               <div><p>';

                    //    var flag = order.SellerRemarkFlag || "";
                    //    if (flag == "1")
                    //        html += '                   <span style="background-position:-0px -20px;background-image: url(\'../Content/Images/allicons.png\')" class="remark_flag remark_flag_' + flag + '">&nbsp;</span>';
                    //    else if (flag == "2")
                    //        html += '                   <span style="background-position:-42px -20px;background-image: url(\'../Content/Images/allicons.png\')" class="remark_flag_' + flag + '">&nbsp;</span>';
                    //    else if (flag == "3")
                    //        html += '                   <span style="background-position:-28px -20px;background-image: url(\'../Content/Images/allicons.png\')" class="remark_flag_' + flag + '">&nbsp;</span>';
                    //    else if (flag == "4")
                    //        html += '                   <span style="background-position:-14px -20px;background-image: url(\'../Content/Images/allicons.png\')" class="remark_flag_' + flag + '">&nbsp;</span>';
                    //    else if (flag == "5")
                    //        html += '                   <span style="background-position:-56px -20px;background-image: url(\'../Content/Images/allicons.png\')" class="remark_flag_' + flag + '">&nbsp;</span>';
                    //    html += '                   备注: <span>' + (order.SellerRemark || "") + '</span>&nbsp;&nbsp;留言: <span>' + (order.BuyerRemark || "") + '</span></p>';
                    //    html += '               </div></div>';

                    //    html += '               <div class="remarkParticulars_right" onclick="purchaseModule.showOrderItemDetail(this)">';
                    //    html += '                   <span class="collapse">展开</span><span class="icon_show showRemarkMore"></span>';
                    //    html += '               </div>';
                    //    html += '           </div>';
                    //    html += '       <div class="remarkParticulars_content showRemarkMoreShow"><ul>';

                    //    for (var n = 0; n < order.Items.length; n++) {
                    //        var orderItem = order.Items[n];
                    //        var imgUrl = orderItem.SkuImgUrl || "";
                    //        var color = orderItem.Color || "";
                    //        var size = (orderItem.Size || "").trimStartDgj(";");
                    //        var productId = orderItem.ProductId || "";
                    //        var productSubject = orderItem.ProductSubject || "";

                    //        html += '           <li>';
                    //        html += '           <img src="' + (imgUrl == "" ? defaultNoImgUrl : imgUrl) + '" alt="" style="width: 45px; height: 45px; display: inline-block"  onclick="commonModule.openProductUrl(\'' + productId + '\')">';
                    //        html += '           <div><p>' + productSubject + '</p > <p>颜色：<span>' + color + '</span>&nbsp;&nbsp;尺码：<span>' + size + '</span></p></div>';
                    //        html += '           </li>';
                    //    }
                    //    html += '       </ul></div>';
                    //}

                }
            }
            itemRowNum++;
        }

        // 合计
        dataSource.TotalItemCount = dataSource.TotalItemCount || 0;
        dataSource.TotalPurchasePrice = commonModule.ToFixed((dataSource.TotalPurchasePrice || 0), 2);
        dataSource.TotalAmount = commonModule.ToFixed((dataSource.TotalAmount || 0), 2);
        dataSource.TotalProfit = commonModule.ToFixed((dataSource.TotalProfit || 0), 2);

        html += '<tr class="table_footer">';
        html += '   <td colspan="' + colSpan + '">';
        html += '       <span>合计</span>';
        html += '           <span style="float: right;font-weight: bold">';
        html += '           <span class="sum-totalitemcount"> 共：<span contenteditable="false">' + dataSource.TotalItemCount + '</span>件 &nbsp;&nbsp;</span>';
        html += '           <span class="sum-totalpurchaseprice"> 总成本：<span contenteditable="false">' + dataSource.TotalPurchasePrice + '</span>元 &nbsp;&nbsp;</span>';
        html += '           <span class="sum-totalamount"> 总金额: <span contenteditable="false">' + dataSource.TotalAmount + '</span>元</span>';
        html += '           <span class="sum-totalprofit"> 总商品差价: <span contenteditable="false">' + dataSource.TotalProfit + '</span>元</span>';
        html += '       </span>';
        html += '</td></tr></tbody></table>';

        console.timeEnd("生成html内容");


        console.time("方式一、html内容渲染到页面");
        $(".stockup_content").html(html);
        console.timeEnd("方式一、html内容渲染到页面");

        console.time("方式二、html内容渲染到页面");
        $(".stockup_content")[0].innerHTML = html;
        console.timeEnd("方式二、html内容渲染到页面");

        if (commonModule.isUseOldTheme)
            $(".stockup_content [contenteditable]").removeAttr("contenteditable");
        else
            ListenContenteditableEvt();

        loadDefaultPrintSet();

        console.time("显示或隐藏列");
        displayColumns($(".purchase_content table.stockup_table_content"), "display");
        console.timeEnd("显示或隐藏列");

        expandAllDetail($("#collapse-all-msg").prop("checked"));
    }

    // 新模板样式渲染
    var render_New = function () {

        var $table = purchaseTemplateSet;
        var $tds = $table.tdsData;
        var type = 2;

        // 模板资料
        for (var i = 0; i < $tds.length; i++) {
            $tds[i].type = 2;
            for (var j = 0; j < $tds[i].tdContent.length; j++) {
                if ($tds[i].tdContent[j].type == 0) {
                    for (var k = $tds[i].tdContent.length - 1; k > -1; k--) {
                        if ($tds[i].tdContent[k].type == 1) {
                            $tds[i].tdContent.splice(k, 1);
                        }
                    }
                    type = 0;
                    break;
                } else {
                    if ($tds[i].tdContent[j].type == 1) {
                        type = 1;
                    } else {
                        type = 2;
                    }
                }
            }
            $tds[i].type = type;
        }

        // 列表渲染
        var setting = module.PurchaseSetting() || {};
        var dataSource = (setting.PurchaseItems || setting.PurchaseItemsModel) || {};
        //var $PurchaseItem = JSON.parse(JSON.stringify(setting.PurchaseItemsModel));
        var $PurchaseItem = dataSource;
        var $PurchaseModule = {};
        var $Orders = $PurchaseItem.Orders;



        $PurchaseModule.TotalItemCount = commonModule.ToFixed($PurchaseItem.TotalItemCount, 2);
        $PurchaseModule.TotalPurchasePrice = commonModule.ToFixed($PurchaseItem.TotalPurchasePrice, 2);
        $PurchaseModule.TotalAmount = commonModule.ToFixed($PurchaseItem.TotalAmount, 2);
        $PurchaseModule.TotalProfit = commonModule.ToFixed($PurchaseItem.TotalProfit, 2);

        var $PurchaseItems = $PurchaseItem.PurchaseItems;

        //排序  spsl商品总数量排序
        if ($table.firstSortField == "spsl") {
            $PurchaseItems = $PurchaseItems.sort(function (a, b) { return b.TotalItemCount - a.TotalItemCount; })
        }
        if ($table.firstSortField == "sphh") {
            $PurchaseItems.sort(function (x, y) {
                return x.ProductCargoNumber.localeCompare(y.ProductCargoNumber);
            });
        }
        if ($table.firstSortField == "spjc") {
            $PurchaseItems.sort(function (x, y) {
                return x.ShortTitle.localeCompare(y.ShortTitle);
            });
        }
        if ($table.firstSortField == "spbt") {
            $PurchaseItems.sort(function (x, y) {
                return x.ProductSubject.localeCompare(y.ProductSubject);
            });
        }

        // 新版样式备货单-默认商品属性根据颜色尺寸排序
        $($PurchaseItems).each(function (i, item) {
            if (item.SubItems) {
                item.SubItems.sort(function (x, y) {
                    return (x.Color || "" + x.Size || "").localeCompare((y.Color || "" + y.Size || ""));
                });
            }
        });

        $PurchaseModule.PurchaseItems = $PurchaseItems;

        for (var i = 0; i < $PurchaseModule.PurchaseItems.length; i++) {
            if ($PurchaseModule.PurchaseItems[i].SubItems.length > 0) {
                for (var j = 0; j < $PurchaseModule.PurchaseItems[i].SubItems.length; j++) {
                    $PurchaseModule.PurchaseItems[i].SubItems[j].ProductId = $PurchaseModule.PurchaseItems[i].ProductId;
                }
            }
        }

        var $trWidth = 0;
        var $ProductSubject = [];
        for (var i = 0; i < $PurchaseModule.PurchaseItems.length; i++) {
            var obj = {};

            if ($PurchaseModule.PurchaseItems[i].Stall || $PurchaseModule.PurchaseItems[i].Bazaar) {
                obj.index = $PurchaseModule.PurchaseItems.length + i;
            } else {
                obj.index = i;
            }


            obj.Stall = $PurchaseModule.PurchaseItems[i].Stall ? $PurchaseModule.PurchaseItems[i].Stall : '';
            obj.Bazaar = $PurchaseModule.PurchaseItems[i].Bazaar ? $PurchaseModule.PurchaseItems[i].Bazaar : '';
            obj.ShortTitle = $PurchaseModule.PurchaseItems[i].ShortTitle;
            obj.TotalAmount = commonModule.ToFixed($PurchaseModule.PurchaseItems[i].TotalAmount, 2);
            obj.TotalKindCount = $PurchaseModule.PurchaseItems[i].TotalKindCount;
            obj.TotalItemCount = commonModule.ToFixed($PurchaseModule.PurchaseItems[i].TotalItemCount, 2);
            obj.TotalPurchasePrice = commonModule.ToFixed($PurchaseModule.PurchaseItems[i].TotalPurchasePrice, 2);
            obj.TotalProfit = commonModule.ToFixed($PurchaseModule.PurchaseItems[i].TotalProfit, 2);
            obj.ProductId = $PurchaseModule.PurchaseItems[i].ProductId;

            var $array = [];
            for (var j = 0; j < $tds.length; j++) {
                var tdObj = {};
                tdObj.id = $tds[j].id;
                tdObj.title = $tds[j].id;
                tdObj.textId = $tds[j].title;
                tdObj.tdWidth = $tds[j].tdWidth;
                if (i == 0)
                    $trWidth += tdObj.tdWidth;
                tdObj.fontFamily = $tds[j].fontFamily;
                tdObj.fontSize = $tds[j].fontSize;
                tdObj.color = $tds[j].color;
                tdObj.fontWeight = $tds[j].fontWeight;
                tdObj.textAlign = $tds[j].textAlign;
                tdObj.tdContent = [];
                tdObj.type = $tds[j].type;
                if ($tds[j].type == 0 || $tds[j].type == 2) {
                    var tdContent = {};
                    for (var m = 0; m < $tds[j].tdContent.length; m++) {
                        for (var k in $PurchaseModule.PurchaseItems[i]) {
                            if ($tds[j].tdContent[m].textId == k) {
                                tdContent[k] = $PurchaseModule.PurchaseItems[i][k]
                            } else if ($tds[j].tdContent[m].textId == "br") {
                                tdContent["br" + m] = "<br>";
                            } else if ($tds[j].tdContent[m].textId == "CustomText") {
                                tdContent["CustomText" + m] = $tds[j].tdContent[m].title;
                            } else if ($tds[j].tdContent[m].textId == "index") {
                                tdContent.index = i + 1
                            } else if ($tds[j].tdContent[m].textId == "TotalAmount") {
                                tdContent["TotalAmount"] = commonModule.ToFixed($PurchaseModule.PurchaseItems[i]["TotalAmount"], 2);
                            } else if ($tds[j].tdContent[m].textId == "TotalProfit") {
                                tdContent["TotalProfit"] = commonModule.ToFixed($PurchaseModule.PurchaseItems[i]["TotalProfit"], 2);
                            }
                        }
                    }
                    tdObj.tdContent.push(tdContent)
                } else if ($tds[j].type == 1) {

                    for (var n = 0; n < $PurchaseModule.PurchaseItems[i].SubItems.length; n++) {
                        var tdContent = {};
                        for (var m = 0; m < $tds[j].tdContent.length; m++) {
                            for (var k in $PurchaseModule.PurchaseItems[i].SubItems[n]) {
                                var $color = $PurchaseModule.PurchaseItems[i].SubItems[n].Color;
                                var $Size = $PurchaseModule.PurchaseItems[i].SubItems[n].Size ? $PurchaseModule.PurchaseItems[i].SubItems[n].Size : "";

                                if ($tds[j].tdContent[m].textId == k) {
                                    tdContent[k] = $PurchaseModule.PurchaseItems[i].SubItems[n][k]
                                } else if ($tds[j].tdContent[m].textId == "br") {
                                    tdContent["br" + m] = "<br>";
                                } else if ($tds[j].tdContent[m].textId == "CustomText") {
                                    tdContent["CustomText" + m] = $tds[j].tdContent[m].title;
                                } else if ($tds[j].tdContent[m].textId == "index") {
                                    tdContent.index = i + 1
                                } else if ($tds[j].tdContent[m].textId == "ColorAndSize") {
                                    tdContent["ColorAndSize"] = $color != "" ? ($color + ";") + $Size : "" + $Size;
                                } else if ($tds[j].tdContent[m].textId == "SingleCostPrice") {
                                    tdContent["SingleCostPrice"] = commonModule.ToFixed($PurchaseModule.PurchaseItems[i].SubItems[n]["SingleCostPrice"], 2);
                                } else if ($tds[j].tdContent[m].textId == "CostPrice") {
                                    tdContent["CostPrice"] = commonModule.ToFixed($PurchaseModule.PurchaseItems[i].SubItems[n]["CostPrice"], 2);
                                } else if ($tds[j].tdContent[m].textId == "Profit") {
                                    tdContent["Profit"] = commonModule.ToFixed($PurchaseModule.PurchaseItems[i].SubItems[n]["Profit"], 2);
                                } else if ($tds[j].tdContent[m].textId == "Price") {
                                    tdContent["Price"] = commonModule.ToFixed($PurchaseModule.PurchaseItems[i].SubItems[n]["Price"], 2);
                                } else if ($tds[j].tdContent[m].textId == "ItemAmount") {
                                    tdContent["ItemAmount"] = commonModule.ToFixed($PurchaseModule.PurchaseItems[i].SubItems[n]["ItemAmount"], 2);
                                }

                            }
                        }

                        tdObj.tdContent.push(tdContent)

                    }

                    //以下处理合并的逻辑
                    //skuPropsShortMerge: "1"时，规格简称相同时合并    
                    //cargoNumberNotContains:"1"时 单品货号相同时合并
                    if ($table.skuPropsShortMerge == 1) {
                        combineOrder("ShortTitle")
                    }
                    if ($table.cargoNumberNotContains == 1) {
                        combineOrder("CargoNumber")
                    }

                    function combineOrder(field) {
                        var $$tdContent = [];
                        var $tdContent = JSON.parse(JSON.stringify(tdObj.tdContent));
                        for (var ii = 0; ii < $tdContent.length; ii++) {
                            var isTrue = false;
                            for (var jj = 0; jj < $$tdContent.length; jj++) {
                                if ($tdContent[ii][field] != "" && $tdContent[ii][field] == $$tdContent[jj][field]) {
                                    if ($$tdContent[jj].Count) {
                                        $$tdContent[jj].Count = parseInt($$tdContent[jj].Count) + parseInt($tdContent[ii].Count);
                                    }
                                    if ($$tdContent[jj].SingleCostPrice) {
                                        $$tdContent[jj].SingleCostPrice = parseInt($$tdContent[jj].SingleCostPrice) + parseInt($tdContent[ii].SingleCostPrice);
                                    }
                                    isTrue = true;
                                }
                            }

                            if (!isTrue) {
                                $$tdContent.push(tdObj.tdContent[ii])
                            }
                        }
                        tdObj.tdContent = $$tdContent;
                    }
                }

                $array.push(tdObj);
            }
            var $tr = {}, trs = [];
            $tr.tds = $array;
            $tr.index = i;
            $tr.isBuyerRemark = false;
            $tr.ShortTitle = $PurchaseModule.PurchaseItems[i].ShortTitle;
            $tr.buyerRemark = "";
            $tr.SellerRemark = "";
            $tr.orders = [];
            $tr.ProductId = $PurchaseModule.PurchaseItems[i].ProductId;
            trs.push($tr);
            obj.trs = trs;
            obj.tds = $array;

            $ProductSubject.push(obj);

        }

        console.log("$ProductSubject", $ProductSubject)


        $ProductSubject = $ProductSubject.sort(function (a, b) { return a.index - b.index })

        //查询订单列表，把订单列表商品，合并到备货单里
        for (var i = 0; i < $ProductSubject.length; i++) {
            var orders = [];
            var isBuyerRemark = false;//是否有买家备注或卖家留言
            for (var j = 0; j < $Orders.length; j++) {
                var obj = {};
                for (var k = 0; k < $Orders[j].Items.length; k++) {
                    if ($ProductSubject[i].ProductId == $Orders[j].Items[k].ProductId) {
                        obj = $Orders[j];
                        orders.push(obj)
                    }

                    if ($ProductSubject[i].ProductId == $Orders[j].Items[k].ProductId) {
                        if ($Orders[j].BuyerRemark != "" && $Orders[j].BuyerRemark || $Orders[j].SellerRemark != "" && $Orders[j].SellerRemark) {
                            isBuyerRemark = true;
                            for (var n = 0; n < $ProductSubject[i].trs.length; n++) {
                                $ProductSubject[i].trs[n].isBuyerRemark = true;
                                $ProductSubject[i].trs[n].orders = orders;
                                $ProductSubject[i].trs[n].buyerRemark = $Orders[j].BuyerRemark;
                                $ProductSubject[i].trs[n].SellerRemark = $Orders[j].SellerRemark;
                                $ProductSubject[i].trs[n].SellerRemarkFlag = $Orders[j].SellerRemarkFlag;

                            }
                            break;
                        }
                    }

                }
            }
            //$ProductSubject[i].orders = orders;
            $ProductSubject[i].isBuyerRemark = isBuyerRemark;

        }


        //相同市场档口进行合并
        var PurchaseModule2 = [];
        for (var i = 0; i < $ProductSubject.length; i++) { //数组去重
            var isE = false;
            for (var j = 0; j < PurchaseModule2.length; j++) {
                if ($ProductSubject[i].Stall == PurchaseModule2[j].Stall && $ProductSubject[i].Bazaar == PurchaseModule2[j].Bazaar && $ProductSubject[i].Stall != "" && $ProductSubject[i].Bazaar != "") {
                    PurchaseModule2[j].TotalAmount = parseInt(PurchaseModule2[j].TotalAmount) + parseInt($ProductSubject[i].TotalAmount);
                    PurchaseModule2[j].TotalKindCount = parseInt(PurchaseModule2[j].TotalKindCount) + parseInt($ProductSubject[i].TotalKindCount);
                    PurchaseModule2[j].TotalItemCount = parseInt(PurchaseModule2[j].TotalItemCount) + parseInt($ProductSubject[i].TotalItemCount);
                    PurchaseModule2[j].TotalPurchasePrice = parseInt(PurchaseModule2[j].TotalPurchasePrice) + parseInt($ProductSubject[i].TotalPurchasePrice);
                    PurchaseModule2[j].TotalProfit = parseInt(PurchaseModule2[j].TotalProfit) + parseInt($ProductSubject[i].TotalProfit);
                    PurchaseModule2[j].ProductId = PurchaseModule2[j].ProductId + "," + $ProductSubject[i].ProductId;
                    PurchaseModule2[j].trs = PurchaseModule2[j].trs.concat($ProductSubject[i].trs);
                    isE = true;
                }
            }
            if (!isE)
                PurchaseModule2.push($ProductSubject[i]);
        }


        //商品简称相同时
        //skuPropsShortMerge: "1"时，规格简称相同时合并    
        if ($table.ShortTitle == 1) {

            var $ShortTitleEqualPurchaseModule = [];
            for (var i = 0; i < PurchaseModule2.length; i++) {
                if (PurchaseModule2[i].Stall == "" && PurchaseModule2[i].Bazaar == "") {
                    var isE = false;
                    for (var j = 0; j < $ShortTitleEqualPurchaseModule.length; j++) {
                        if (PurchaseModule2[i].ShortTitle == $ShortTitleEqualPurchaseModule[j].ShortTitle && PurchaseModule2[i].ShortTitle != "") {
                            $ShortTitleEqualPurchaseModule[j].trs = $ShortTitleEqualPurchaseModule[j].trs.concat(PurchaseModule2[i].trs)
                            isE = true;
                        }

                    }

                    if (!isE) {
                        $ShortTitleEqualPurchaseModule.push(PurchaseModule2[i]);
                    }

                } else {

                }
            }
        }

        console.table(PurchaseModule2)
        var tplt = $.templates("#newPurchasesTable_tmpl")
        var html = tplt.render({ tableType: $table, trWidth: $trWidth, tdsLength: $tds.length, PurchaseModule: PurchaseModule2, TotalItemCount: $PurchaseModule.TotalItemCount, TotalPurchasePrice: $PurchaseModule.TotalPurchasePrice, TotalAmount: $PurchaseModule.TotalAmount, TotalProfit: $PurchaseModule.TotalProfit });
        $('#newpurchases_content_main').html(html);
        $("#newpurchasesTbody .tdIndex").each(function (index, item) {
            var $index = $(item).attr("data-index")
            $(item).text($index);
        });
    }

    var getOptions = function () {
        var options = {};
        options.Filters = [];
        options.MultiFilters = [];
        options.IsOrderDesc = true;
        //options.OrderByField = "o.PayTime";
        options.OrderByField = "";
        options.ProductOrderByField = "";

        var container = $(".stockup_search>ul>li label:not('.hide')").find("[name]");

        var orderTbName = commonModule.isCustomerOrder() ? "P_CustomerOrder" : "P_Order";

        //// 店铺
        //var val = checkboxModule.getCheckedShopIdStr();
        //if (val !== "") {
        //    options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "ShopId", Value: val, Contract: "in" });
        //}

        // 店铺
        var val = $("#selectShops").attr("data-values") || "";
        if (val !== "") {
            options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: orderTbName, Name: "ShopId", Value: val, Contract: "in" });
        }

        if (_isUseOldTemplate) {
            // 按商品标题/简称/货号/市场档口排序
            var attrVal = container.filter("[name=SortBy_ProductAttr]").val() || "";
            if (attrVal != "") {
                var isOrderDesc = container.filter("[name=OrderBy_ProductAttr]").val() || "DESC";
                //var attrVals = attrVal.split(',');
                //for (var i = 0; i < attrVals.length; i++) {
                //    options.ProductOrderByField += attrVals[i] + " " + isOrderDesc + ",";
                //}
                options.ProductOrderByField = attrVal;
                options.IsProductDesc = isOrderDesc == "DESC";
            }
            //按销售属性/货号/颜色/尺寸排序
            var skuVal = container.filter("[name=SortBy_Product_Sku]").val() || "";
            if (skuVal != "") {
                var isOrderDesc = container.filter("[name=OrderBy_Product_Sku]").val() || "DESC";
                //skuVal = skuVal + " " + isOrderDesc;
                options.OrderByField = skuVal;
                options.IsOrderDesc = isOrderDesc == "DESC";
            }
        }
        else {
            //前端进行排序
            //if (purchaseTemplateSet.firstSortField == "spjc")
            //    options.ProductOrderByField = "p.ShortTitle";
            //else if (purchaseTemplateSet.firstSortField == "spbt")
            //    options.ProductOrderByField = "oi.ProductSubject";
            //else if (purchaseTemplateSet.firstSortField == "sphh")
            //    options.ProductOrderByField = "oi.ProductCargoNumber";
            //else if (purchaseTemplateSet.firstSortField == "spsl")
            //    options.ProductOrderByField = "oi.ProductCount";
            //options.IsProductDesc = true;
        }

        //// 退款成功和已取消的订单首先排除
        //options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "RefundStatus", Value: "REFUND_SUCCESS", Contract: "!=" });
        //options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "Status", Value: "cancel", Contract: "!=" });

        // 订单状态
        var val = container.filter("[name=PlatformStatus]").val() || "";
        if (val !== "") {
            options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: "P_Order", Name: "PlatformStatus", Value: val, Contract: "=", CustomQuery: "PurchasePlatformStatus" });
        }

        // 订单编号(只查询店铺和订单号)   
        var val = _isFromOrderPrint ? $(".stockup_search>ul>li label [name=PlatformOrderId]").val() || "" :
            container.filter("[name=PlatformOrderId]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: "P_Order", Name: "PlatformOrderId", Value: val, Contract: "in", CustomQuery: "PlatformOrderIdSearch" });
            return options;
        }

        // 查询订单时间
        var key = container.filter("[name=QueryDate]").val();
        if (key !== "") {
            //// 查询时间作为优先排序字段
            //options.OrderByField = "o." + key;
            //    options.OrderByField = "o." + key;

            var startTime = $("#QueryDateVal").attr("start-date") || "";
            var endTime = $("#QueryDateVal").attr("end-date") || "";
            if (startTime != "" && endTime != "") {
                options.Filters.push({ TableAlias: "o", FieldType: "DateTime", TableName: "P_Order", Name: key, Value: startTime, ExtValue: endTime, Contract: "between" });
            }
            else if (startTime === "" && endTime !== "") {
                options.Filters.push({ TableAlias: "o", FieldType: "DateTime", TableName: "P_Order", Name: key, Value: endTime, Contract: "小于等于" });
            }
            else if (startTime !== "" && endTime === "") {
                options.Filters.push({ TableAlias: "o", FieldType: "DateTime", TableName: "P_Order", Name: key, Value: startTime, Contract: "大于等于" });
            }
        }

        // 打印状态                               
        var val = (commonModule.isUseOldTheme ? $("select#printState").val() : container.filter("[name=PrintStatus]").val()) || "";
        if (val !== "") {
            var arr = val;
            if (val === "none") {
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "ExpressPrintTimes", Value: "0", Contract: "=" });
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "SendPrintTimes", Value: "0", Contract: "=" });
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "NahuoPrintTimes", Value: "0", Contract: "=" });
            }
            else if (val === "ExpressPrintTimes_SendPrintTimes") {
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "ExpressPrintTimes", Value: "0", Contract: "大于" });
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "SendPrintTimes", Value: "0", Contract: "大于" });
            }
            else if (val === "ExpressPrintTimes_SendPrintTimes_NahuoPrintTimes") {
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "ExpressPrintTimes", Value: "0", Contract: "大于" });
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "SendPrintTimes", Value: "0", Contract: "大于" });
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "NahuoPrintTimes", Value: "0", Contract: "大于" });
            }
            else {
                var isUnPrint = val.indexOf("Un_") == 0;
                val = val.replace("Un_", "");
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: val, Value: "0", Contract: isUnPrint ? "小于等于" : "大于" });
            }
        }

        //留言备注
        var val = container.filter("[name=MsgOrRemark]").val() || "";
        if (val !== "") {
            var filters = [];
            if (val == "msg_or_remark") {
                options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: "P_Order", CustomQuery: "msg_or_remark" });
            }
            else if (val == "msg_and_remark") {
                options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: "P_Order", CustomQuery: "msg_and_remark" });
            }
            else if (val == "nomsg_and_noremark") {
                options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: "P_Order", CustomQuery: "nomsg_and_noremark" });
            }
            else if (val == "msg_noremark") {
                options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: "P_Order", CustomQuery: "msg_noremark" });
            }
            else if (val == "nomsg_remark") {
                options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: "P_Order", CustomQuery: "nomsg_remark" });
            }
        }

        //备注旗帜
        var val = container.filter("[name=SellerRemarkFlag]").val() || "";
        if (val !== "") {
            var $option = container.filter("[name=SellerRemarkFlag]").find("option:selected");
            var opt = $option.attr("data-operator") || 1;
            var flagVal = opt == 1 && (commonModule.IsNumber(val) || parseInt(val)) == val ? parseInt(val) : -1;
            if (flagVal < 6 && flagVal > 0) {
                options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: orderTbName, Name: "SellerRemarkFlag", Value: val, Contract: "like" });
            }
            else {
                var contract = opt == '1' ? "like" : "not like";
                var value = "[" + val + "]";
                options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: orderTbName, Name: "SellerRemarkFlag", Value: value, Contract: contract, CustomQuery: "CustomerSellerRemarkFlag" });
            }
        }

        // 分类订单
        var val = container.filter("[name=OrderCategory]").val() || "";
        if (val !== "") {
            if (val == "NoCategoryOrder") {
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "CategoryId", Value: "", Contract: "=" });
            }
            else if (val == "CategoryOrder") {
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "CategoryId", Value: "", Contract: "!=" });
            }
            else {
                options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "CategoryId", Value: val, Contract: "=" });
            }
        }

        // 旺旺
        var val = container.filter("[name=BuyerWangWang]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: "P_Order", Name: "BuyerWangWang", Value: val, Contract: "in" });
        }

        //买家留言
        var checked = container.filter("[name=exact_match]").prop("checked");
        var contract = checked ? "=" : "like";
        var val = container.filter("[name=BuyerRemark]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: "P_Order", Name: "BuyerRemark", Value: val, Contract: contract });
        }
        //卖家备注
        var val = container.filter("[name=SellerRemark]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "o", FieldType: "string", TableName: "P_Order", Name: "SellerRemark", Value: val, Contract: contract });
        }

        //商品ID
        var val = container.filter("[name=ProductId]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "ProductId", Value: val, Contract: "=" });
        }

        //商品标题
        var val = container.filter("[name=ProductSubject]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "ProductSubject", Value: val, Contract: "like" });
        }
        //商品标题不包含
        var val = container.filter("[name=ProductSubjectNotContains]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "ProductSubject", Value: val, Contract: "not like" });
        }

        //商品简称
        var val = container.filter("[name=ShortTitle]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "p", FieldType: "string", TableName: "P_Product", Name: "ShortTitle", Value: val, Contract: "like" });
        }
        //商品简称不包含
        var val = container.filter("[name=ShortTitleNotContains]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "p", FieldType: "string", TableName: "P_Product", Name: "ShortTitle", Value: val, Contract: "not like" });
        }

        //商品货号
        var val = container.filter("[name=ProductCargoNumber]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "ProductCargoNumber", Value: val, Contract: "like" });
            //var filters = [];
            //filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "ProductCargoNumber", Value: val, Contract: "like", Operator: "OR" });
            //filters.push({ TableAlias: "p", FieldType: "string", TableName: "P_Product", Name: "CargoNumber", Value: val, Contract: "like", Operator: "OR" });
            //options.MultiFilters.push(filters);

        }
        //商品货号不包含
        var val = container.filter("[name=ProductCargoNumberNotContains]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "ProductCargoNumber", Value: val, Contract: "not like" });
            //var filters = [];
            //filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "ProductCargoNumber", Value: val, Contract: "not like", Operator: "AND" });
            //filters.push({ TableAlias: "p", FieldType: "string", TableName: "P_Product", Name: "CargoNumber", Value: val, Contract: "not like", Operator: "AND" });
            //options.MultiFilters.push(filters);
        }

        //商品规格包含
        var val = container.filter("[name=ColorAndSize]").val() || container.filter("[name=ColorAndSize2]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "Color", Value: val, Contract: "like" });
        }
        //商品规格不包含
        var val = container.filter("[name=ColorAndSizeNotContains]").val() || container.filter("[name=ColorAndSizeNotContains2]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "Color", Value: val, Contract: "not like" });
        }

        //规格颜色
        var val = container.filter("[name=Color]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "Color", Value: val, Contract: "like" });
        }
        //规格颜色不包含
        var val = container.filter("[name=NotContainsColor]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "Color", Value: val, Contract: "not like" });
        }

        //规格尺码
        var val = container.filter("[name=Size]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "Size", Value: val, Contract: "like" });
        }
        //规格尺码不包含
        var val = container.filter("[name=NotContainsSize]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "Size", Value: val, Contract: "not like" });
        }

        //单品货号
        var val = container.filter("[name=CargoNumber]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "CargoNumber", Value: val, Contract: "like" });
            //var filters = [];
            //filters.push({ TableAlias: "sku", FieldType: "string", TableName: "P_ProductSku", Name: "CargoNumber", Value: val, Contract: "like", Operator: "OR" });
            //filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "CargoNumber", Value: val, Contract: "like", Operator: "OR" });
            //options.MultiFilters.push(filters);
        }
        //单品货号不包含
        var val = container.filter("[name=CargoNumberNotContains]").val() || "";
        if (val != "") {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "CargoNumber", Value: val, Contract: "not like" });

            //var filters = [];
            //filters.push({ TableAlias: "sku", FieldType: "string", TableName: "P_ProductSku", Name: "CargoNumber", Value: val, Contract: "not like", Operator: "AND" });
            //filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "CargoNumber", Value: val, Contract: "not like", Operator: "AND" });
            //options.MultiFilters.push(filters);
        }

        // 市场、档口
        var val = container.filter("[name=Bazaar]").val() || "";
        if (val != "") {
            var filters = [];
            filters.push({ TableAlias: "skuAttr", FieldType: "string", TableName: "P_ProductSkuAttribute", Name: "Bazaar", Value: val, Contract: "=", Operator: "OR" });
            filters.push({ TableAlias: "p", FieldType: "string", TableName: "P_Product", Name: "Bazaar", Value: val, Contract: "=", Operator: "OR" });
            options.MultiFilters.push(filters);
        }
        var val = container.filter("[name=Stall]").val() || "";
        if (val != "") {
            var filters = [];
            filters.push({ TableAlias: "skuAttr", FieldType: "string", TableName: "P_ProductSkuAttribute", Name: "Stall", Value: val, Contract: "=", Operator: "OR" });
            filters.push({ TableAlias: "p", FieldType: "string", TableName: "P_Product", Name: "Stall", Value: val, Contract: "=", Operator: "OR" });
            options.MultiFilters.push(filters);
        }

        // 包含预发货
        var checked = container.filter("[name=yufahuo]").prop("checked") || false;
        if (!checked) {
            options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "IsPreordain", Value: "0", Contract: "=" });
        }

        // 包含锁定订单
        var checked = container.filter("[name=suoding]").prop("checked") || false;
        if (!checked) {
            options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "IsLocked", Value: "0", Contract: "=" });
        }

        // 排除退款中订单
        var checked = container.filter("[name=tuikuan]").prop("checked") || false;
        if (checked) {
            options.Filters.push({ TableAlias: "oi", FieldType: "string", TableName: "P_OrderItem", Name: "RefundStatus", Value: "", Contract: "=", CustomQuery: "NoReturnOrder" });
        }

        // 排除快递单已打印
        var checked = container.filter("[name=FilterExpressPrinted]").prop("checked") || false;
        if (checked) {
            options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "ExpressPrintTimes", Value: "0", Contract: "小于等于" });
        }

        // 排除发货单已打印
        var checked = container.filter("[name=FilterSendPrinted]").prop("checked") || false;
        if (checked) {
            options.Filters.push({ TableAlias: "o", FieldType: "int", TableName: "P_Order", Name: "SendPrintTimes", Value: "0", Contract: "小于等于" });
        }

        // 排除屯货商品 (仅统计拿货小标签可用)
        if (!isFromCreatePurches) {
            var checked = container.filter("[name=FilterTunProducts]").prop("checked") || false;
            if (checked) {
                options.Filters.push({ TableAlias: "sr", FieldType: "bool", TableName: "P_NaHuoLabelSortingRules", Name: "ScanStatus", Value: "", Contract: "!=", CustomQuery: "FilterTunProducts" });
            }
        }

        return options;
    }

    // 加载备货单历史记录
    module.loadPurchaseHistory = function (options, isPaging) {
        var $trs = $("#aialogHistory table.unify_table>tbody>tr");
        if ($trs.length > 0 && !needReload) {
            showHistoryDialog();
            return;
        }

        if (options === undefined) {
            options = { PageSize: _pageSize, PageIndex: _pageIndex, IsOrderDesc: true, OrderByField: "CreateTime" };
        }

        common.Ajax({
            url: '../Purchases/HistoryList',
            data: options,
            type: 'POST',
            loading: true,
            //loadingMessage: "正在加载备货单记录……",
            success: function (rsp) {
                if (rsp.Success) {
                    needReload = false;
                    var list = rsp.Data;

                    if (list && list.Rows.length === 0) {
                        layer.msg("无备货单历史数据");
                        return;
                    }

                    renderPurchaseHistory(list.Rows);

                    if (!isPaging) {
                        layPage.render({
                            elem: 'paging'
                            , count: rsp.Data.Total
                            , limit: _pageSize
                            , limits: [10, 20, 50, 100, 200]
                            , layout: ['count', 'prev', 'page', 'next', 'limit']
                            , jump: function (obj, first) {
                                if (!first) {
                                    needReload = true;
                                    _pageIndex = obj.curr;
                                    _pageSize = obj.limit;

                                    options.PageSize = _pageSize;
                                    options.PageIndex = obj.curr;
                                    options.IsOrderDesc = true;

                                    module.loadPurchaseHistory(options, true);
                                }
                            }
                        });
                    }
                }
            }
        });
    }

    // 加载指定的备货单
    module.loadHistoryById = function (obj, id) {

        commonModule.Ajax({
            url: "/Purchases/GetHistoryById",
            type: "POST",
            data: { id: id },
            success: function (rsp) {
                if (rsp.Success) {
                    try {
                        var obj = rsp.Data || {};
                        var setting = module.PurchaseSetting() || {};
                        var queryData = obj.QueryData;
                        var rows = obj.Data;
                        $(".display_setting").attr("data-historyId", id);
                        $(".display_setting").attr("data-from", "history");

                        var historyTemplate = !obj.TemplateName || obj.TemplateName == "老版模板";
                        //console.log("加载历史备货单isUseOldTemplate：" + _isUseOldTemplate + "=>" + historyTemplate);
                        _isUseOldTemplate = historyTemplate;
                        displayTemplateStyle(_isUseOldTemplate);
                        if (!_isUseOldTemplate) {
                            rows = commonModule.escape2Html(rows);
                            $("#newpurchases_content_main").html(rows);
                        }
                        else {
                            setting = JSON.parse(rows);
                            if (setting.Setting && setting.Setting != "")
                                initPrintFieldsConfig(setting.Setting, false);
                            module.SetPurchaseSetting(setting);
                            render();
                        }

                        //var queryData = $(obj).find("~div.queryData").text();
                        //var html = $(obj).find("~div.rows").html();
                        //$(".display_setting").attr("data-historyId", id);
                        //$(".display_setting").attr("data-from", "history");
                        //setting = JSON.parse(html);
                        //if (setting.Setting && setting.Setting != "")
                        //    initPrintFieldsConfig(setting.Setting, false);
                        //module.SetPurchaseSetting(setting);
                        //render();
                        loadQueryCondition(queryData);
                    } catch (e) {
                        console.error(e);
                    }
                    layer.closeAll();
                }
                else {

                }

            }
        });

        //try {
        //    var setting = module.PurchaseSetting() || {};
        //    var queryData = $(obj).find("~div.queryData").text();
        //    var html = $(obj).find("~div.rows").html();
        //    $(".display_setting").attr("data-historyId", id);
        //    $(".display_setting").attr("data-from", "history");
        //    setting = JSON.parse(html);
        //    if (setting.Setting && setting.Setting != "")
        //        initPrintFieldsConfig(setting.Setting, false);
        //    module.SetPurchaseSetting(setting);
        //    render();
        //    loadQueryCondition(queryData);
        //} catch (e) {
        //    console.error(e);
        //}

        //layer.closeAll();
    }

    // 加载查询条件
    var loadQueryCondition = function (queryJson) {
        var $container = $(".search_condition");
        var queryObj = JSON.parse(queryJson) || {};
        for (var key in queryObj) {
            if (key == "QueryDateVal") {
                commonModule.InitCalenderTime($("#QueryDateVal"), queryObj[key]);
            }
            else {
                var $elmt = $container.find("select[name=" + key + "]");
                if ($elmt.length == 0)
                    $elmt = $container.find("input[name=" + key + "]");
                $elmt.val(queryObj[key]);
            }
        }
    }

    // 保存配置信息
    module.saveSearchContion = function () {
        // 获取新的查询配置信息
        orderFilterConfig.SearchConditions = [];
        for (var i = 0; i < configSearchCondition.length; i++) {
            var name = configSearchCondition[i].attr("name");
            orderFilterConfig.SearchConditions.push(name);
        }

        commonModule.Ajax({
            type: 'post',
            url: '/Purchases/SaveConfig',
            data: { "ConfigData": JSON.stringify(orderFilterConfig) },
            success: function (result) {
                if (result.Success) {
                    renderSearchConditions(orderFilterConfig.SearchConditions);
                    layer.closeAll();
                }
                else {
                    layer.closeAll();
                }
            }
        });
    }

    // 获取配置信息
    // 获取配置信息
    var renderSearchConditions = function () {
        var conditions = commonModule.SystemoConfig.PurchaseCustomConditionSet.SearchConditions;
        configSearchCondition = [];
        $(".productNameSort,.productSizeSort").parent().addClass("hide");

        var $container = $(".search_condition li.search_one:first");
        $container.find("label.search_one_labelOne").addClass("hide");
        var $addConditions = $("#addSearchCondition input:checkbox");
        // 更多条件弹出框
        commonModule.Foreach(conditions, function (i, name) {
            if (platformType == "youzan" || platformType == "kuaishou") {
                if (name == "ColorAndSize")
                    name = "ColorAndSize2";
                else if (name == "ColorAndSizeNotContains")
                    name = "ColorAndSizeNotContains2";
            }
            // 页面显示查询条件
            if (name == "SortBy_Product_Sku" || name == "SortBy_ProductAttr")
                $container.find("[name=" + name + "]").parent().removeClass("hide");

            var $input = $container.find("[name=" + name + "]");
            if ($input.length == 0)
                $input = $container.find("[data-group=" + name + "]");
            $input.parent().removeClass("hide");

            if (name == "SellerRemarkFlag") {
                _renderCustomRemarkFlag();
            }
            // 更多条件勾选框   
            var $checkbox = $addConditions.filter("[name=" + name + "]");
            if ($checkbox.length > 0) {
                configSearchCondition.push($checkbox);
                $checkbox.prop("checked", true);
            }
        });
        // 判断勾选条件是否全部选中
        isCheckAllSearchCondition();

        // 页面查询条件按勾选顺序显示
        var beforeName = "PlatformStatus";
        for (var i = 0; i < configSearchCondition.length; i++) {
            var name = $(configSearchCondition[i]).attr("name") || "";
            var group = $(configSearchCondition[i]).attr("data-group") || "";
            var checked = $(configSearchCondition[i]).prop("checked");

            var $input = $container.find("[name=" + name + "]");
            if (group !== "" && $input.length == 0)
                $input = $container.find("[data-group=" + group + "]");

            var $beforeElmt = $container.find("[name=" + beforeName + "]").parent();
            var $target = $input.parent();
            $target.removeClass("hide");
            $target.insertAfter($beforeElmt);
            beforeName = name;
        }
    }

    var _renderCustomRemarkFlag = function () {
        var selectVal = $(".stockup_search>ul>li select[name=SellerRemarkFlag]").val();
        var str = "";
        str += '<option value="">备注旗帜</option>'
        str += '<option value="0" ' + (selectVal == "0" ? "selected" : "") + '>无旗帜</option>'
        str += '<option value="1" ' + (selectVal == "1" ? "selected" : "") + '>红旗</option>'
        str += '<option value="2" ' + (selectVal == "2" ? "selected" : "") + '>蓝旗</option>'
        str += '<option value="3" ' + (selectVal == "3" ? "selected" : "") + '>绿旗</option>'
        str += '<option value="4" ' + (selectVal == "4" ? "selected" : "") + '>黄旗</option>'
        str += '<option value="5" ' + (selectVal == "5" ? "selected" : "") + '>紫旗</option>'
        str += '<option value="6">自定义旗帜</option>'
        for (var i = 0; i < customRemarkFlags.length; i++) {
            var text = (customRemarkFlags[i].Operator == "1" ? "包含：" : "不包含：") + customRemarkFlags[i].Text;
            str += '<option data-operator="' + customRemarkFlags[i].Operator + '" value=' + customRemarkFlags[i].Value + ' ' + (selectVal == customRemarkFlags[i].Value ? "selected" : "") + '>' + text + '</option>';
        }
        $(".stockup_search>ul>li select[name=SellerRemarkFlag]").html(str);

        var str = "";
        str += '<li data-value="">备注旗帜</li>'
        str += '<li data-value="0">无旗帜</li>'
        str += '<li data-value="1">红旗</li>'
        str += '<li data-value="2">蓝旗</li>'
        str += '<li data-value="3">绿旗</li>'
        str += '<li data-value="4">黄旗</li>'
        str += '<li data-value="5">紫旗</li>'
        str += '<li data-value="6">自定义旗帜</option>'
        for (var i = 0; i < customRemarkFlags.length; i++) {
            var text = (customRemarkFlags[i].Operator == "1" ? "包含：" : "不包含：") + customRemarkFlags[i].Text;
            str += '<li data-operator="' + customRemarkFlags[i].Operator + '" data-value=' + customRemarkFlags[i].Value + '>' + text + '</li>';
        }

        $("#addOrderList_SellerRemarkFlag").html(str);
    }

    // 验证时间是否合法
    var IsDate = function (dateStr) {
        if (dateStr == undefined || dateStr == "") return false;
        return (new Date(dateStr).getSeconds() == dateStr.substring(dateStr.length - 2));
    }

    // 删除备货单
    module.delHistoryById = function (id) {
        commonModule.Ajax({
            type: 'get',
            url: '../Purchases/Delete',
            data: { "id": id },
            loadingMessage: "正在删除……",
            success: function (result) {
                //layer.closeAll();
                if (result.Success) {
                    needReload = true;
                    layer.msg("删除成功");
                    var len = $(".historyTable tr:has(td[data-id])").length;
                    if (len == 1)
                        layer.closeAll();
                    else
                        module.loadPurchaseHistory();
                    //$(".historyTable tr:has(td[data-id=" + id + "])").remove();
                }
                else {
                    layer.msg("删除失败");
                }
            }
        });
    }

    // 渲染备货单历史记录
    var renderPurchaseHistory = function (rows) {
        common.Foreach(rows, function (i, o) {
            o.CreateTime = o.CreateTime.indexOf("Date") != -1 ? eval(o.CreateTime.replace(/\/Date\((\d+)\)\//gi, "new Date($1)")).format('yyyy-MM-dd hh:mm:ss') : o.CreateTime;
        });

        var historyTmpl = $.templates("#purchaseHistoryList");
        var html = historyTmpl.render(rows);
        $("#aialogHistory .historyTable_forWrap tbody").html(html);

        showHistoryDialog();
    }

    var showHistoryDialog = function () {
        if (!$("#aialogHistory").is(":visible")) {
            layer.open({
                type: 1,
                title: "备货单历史记录",
                content: $('#aialogHistory'),
                area: ['680', '450']
            });
        }
    }

    // 保存备货单弹框
    module.showSavePurchaseDialog = function () {
        $(".stockup_table_content td.td_remark_icon span").each(function (i, span) {
            $(span).click();
        });

        var $trs = _isUseOldTemplate ? $(".purchase_content table>tbody>tr:not('.remarkParticulars'):not('.table_footer')")
            : $(".purchase_content table>tbody>tr[data-index]");
        if ($trs.length == 0) {
            layer.msg("无商品数据,请不要保存备货单");
            return;
        }

        var now = new Date().format("yyyy-MM-dd hh:mm:ss");
        $("#PurchaseName").val("备货单-" + now);

        layer.open({
            type: 1,
            title: "输入备货单名称保存",
            content: $('.savePurchase'),
            area: ['600', '800'],
            btn: ['保存', '取消'],
            yes: function () {
                savePurchase(false);
            },
            cancel: function () {

            }
        });
    }

    // 保存备货单
    var savePurchase = function (confirmed) {
        if (editable && !confirmed) {
            //alert("已经修改备货单数据，是否按照修改内容保存？");
            layer.open({
                type: 1,
                title: "保存备货单",
                content: $('.confirmSaveRecord'),
                area: ['300', '200'],
                btn: ['保存', '取消'],
                yes: function () {
                    savePurchase(true);
                },
                cancel: function () {

                }
            });
        }
        else {
            var setting = module.PurchaseSetting() || {};
            var datas = {};
            datas.QueryData = JSON.stringify(queryData);
            datas.Name = $("#PurchaseName").val();
            datas.Editable = editable;
            //datas.ShopId = $("#shops").val();
            datas.TemplateName = purchaseTemplateSet.tableName;
            if (!_isUseOldTemplate) {
                var table = $("#newpurchases_content_main").html();
                var searchOptions = commonModule.html2Escape(table); //html字符转义进行POST提交
                datas.Data = searchOptions;
            }
            else
                datas.Data = JSON.stringify(setting)

            commonModule.Ajax({
                type: 'post',
                url: '/Purchases/Add',
                data: datas,
                loadingMessage: "正在保存备货单……",
                success: function (result) {
                    layer.closeAll();
                    if (result.Success) {
                        layer.msg("添加成功");
                        editable = false;
                        needReload = true;
                    }
                    else {
                        layer.msg("添加失败");
                    }
                }
            });
        }
    }

    // 新导出方式
    module.NewExportExcel = function () {
        if (_isUseOldTemplate)
            module.exportExcel();
        else
            module.openColumnSet();
    }

    // 备货单导出Excel
    module.exportExcel = function () {
        var unitObj = new unitConversion();
        var dpi = unitObj.getDPI()[0] || 72;
        var setting = module.PurchaseSetting() || {};

        //$(".display_setting").attr("data-dpi", dpi);
        var displayFields = printFieldsConfig.DisplayFields;
        if (displayFields.length == 0) {
            layer.alert("列表设置-至少勾选1列显示，再导出Excel");
            return;
        }

        var purchaseItemsModel = setting.PurchaseItemsModel || {};
        if (purchaseItemsModel.PurchaseItems == undefined || purchaseItemsModel.PurchaseItems.length == 0) {
            layer.msg("无商品数据，请不要导出Excel");
            return;
        }

        var imgSize = printFieldsConfig.SettingParams.ImgSize.split("x");
        var imgWidth = imgSize[0];
        var attrImgSize = printFieldsConfig.SettingParams.AttrImgSize.split("x");
        var attrImgWidth = attrImgSize[0];
        var searchOptions = getOptions();

        // 组装页面显示需导出的列内容
        var displayExportModel = {};
        displayExportModel.LogId = purchaseItemsModel.LogId;
        displayExportModel.PurchaseItems = [];
        displayExportModel.TotalAmount = purchaseItemsModel.TotalAmount;
        displayExportModel.TotalItemCount = purchaseItemsModel.TotalItemCount;
        displayExportModel.TotalProfit = purchaseItemsModel.TotalProfit;
        displayExportModel.TotalPurchasePrice = purchaseItemsModel.TotalPurchasePrice;
        $(purchaseItemsModel.PurchaseItems).each(function (i, item) {
            var pitem = {};
            pitem.SubItems = [];
            $(displayFields).each(function (ii, field) {
                // 商品相关列    
                var name = field.Value;
                if (name == "ProductImgUrl" || name == "ProductSubject" || name == "ShortTitle" || name == "ProductCargoNumber" || name == "TotalItemCount" || name == "TotalAmount" || name == "TotalPurchasePrice" || name == "TotalProfit")
                    pitem[name] = item[name];
            });
            $(item.SubItems).each(function (j, si) {
                pSubItem = {};
                $(displayFields).each(function (jj, field) {
                    // Sku相关列
                    var name = field.Value;
                    if (name == "ColorAndSize")
                        pSubItem["Color"] = si["Color"];
                    else if (name == "SkuImgUrl" || name == "CargoNumber" || name == "Color" || name == "Size" || name == "Count" || name == "ItemAmount" || name == "Bazaar" || name == "Stall" || name == "SingleCostPrice" || name == "CostPrice" || name == "Price" || name == "Profit")
                        pSubItem[name] = si[name];
                });
                pitem.SubItems.push(pSubItem);
            });
            displayExportModel.PurchaseItems.push(pitem);
        });

        //console.log(displayExportModel);

        var options = { "PurchaseItemsModel": displayExportModel, "SearchOptions": searchOptions };
        var settingOptions = { "DisplayFields": displayFields, "DPI": dpi, "ImgWidth": imgWidth, "AttrImgWidth": attrImgWidth };

        exportOrderModule.ExportExcel({
            url: '/Purchases/ExportExcel',
            type: 'POST',
            data: { "options": options, "setting": settingOptions },
            loadMsg: "正在导出备货单，请稍等……",
            success: function () {
                layer.msg("导出成功");
            }
        });
    }

    // 打印备货单
    var savePrintFields = function (type, callBack) {
        var setting = module.DefaultPurchaseSetting() || {};
        var $container = $(".setting_content");
        if (type == "print")
            $container = $(".print_setting_content");

        var $checkboxes = $container.find(":checkbox:checked:visible");
        var checkedTmpArr = [];
        $checkboxes.each(function () {
            checkedTmpArr.push({ Text: $(this).parent().text(), Value: $(this).val() });
        });

        var defaultImgSize = module.DefaultImgSize();
        if (type == "print") {
            // 打印属性设置
            var printPaper = $("#print-paper-select").val();
            var printDirection = $("#print-direction-select").val();
            var printImgSize = $("#print-img-size").val() || defaultImgSize.PrintImgSize;
            var printAttrImgSize = $("#print-attr-img-size").val() || defaultImgSize.PrintAttrImgSize;
            // 打印机绑定
            var printer = $("#purchase-printer-select").find("option:selected").text();

            printFieldsConfig.PrintParams = { "DefaultPrinter": printer, "Paper": printPaper, "Direction": printDirection, "ImgSize": printImgSize, "AttrImgSize": printAttrImgSize };
            printFieldsConfig.PrintFields = checkedTmpArr;
        }
        else {
            var imgSize = $("#setting-img-size").val() || defaultImgSize.ImgSize;
            var attrImgSize = $("#setting-attr-img-size").val() || defaultImgSize.AttrImgSize;
            printFieldsConfig.SettingParams = { "ImgSize": imgSize, "AttrImgSize": attrImgSize };
            printFieldsConfig.DisplayFields = checkedTmpArr;
        }

        //if (module.From() == "history")
        //{
        //    var historyID = $(".display_setting").attr("data-historyid") || 0;
        //    if (historyID > 0) {
        //        options = { "id": historyID, "Data": JSON.stringify(setting) };
        //        commonModule.Ajax({
        //            type: 'post',
        //            url: '../Purchases/UpdatePurchaseHistory',
        //            data: options,
        //            loadingMessage: "正在更新历史备货单设置，请稍等……",
        //            success: function (result) {
        //                if (result.Success) {
        //                    module.SetPurchaseSetting(setting);
        //                    console.log("保存成功");
        //                }
        //            }
        //        });
        //    }
        //}
        //else
        commonModule.SaveCommonSetting("PurchasePrintFieldsSet", JSON.stringify(printFieldsConfig), callBack(type, checkedTmpArr));
    }

    module.printPurchase = function () {
        var $trs = _isUseOldTemplate ? $(".purchase_content table>tbody>tr:not('.remarkParticulars'):not('.table_footer')")
            : $(".purchase_content table>tbody>tr[data-index]");
        if ($trs.length == 0) {
            layer.msg("无商品数据,请不要打印");
            return;
        }

        if (lp.check(true)) {
            loadPrinter();

            // 区分新旧模板打印方式
            var layerIndex = layer.open({
                type: 1,
                title: "打印备货单", //不显示标题
                content: $('.bindprinter'),
                area: ['450', '250'], //宽高
                btn: ["直接打印", "打印预览", "关闭"],
                yes: function () {
                    if (_isUseOldTemplate) {
                        savePrintFields("print", function (type, checkedTmpArr) {
                            if (type == "print")
                                printFieldsConfig.PrintFields = checkedTmpArr;
                            else
                                printFieldsConfig.DisplayFields = checkedTmpArr;
                            //console.log(JSON.stringify(printFieldsConfig));
                            doPrint(true);
                            //return false;
                        });
                    }
                    else {
                        doNewPrint(true);
                        //return false;
                    }
                    layer.close(layerIndex);
                },
                btn2: function () {
                    if (_isUseOldTemplate) {
                        savePrintFields("print", function (type, checkedTmpArr) {
                            if (type == "print")
                                printFieldsConfig.PrintFields = checkedTmpArr;
                            else
                                printFieldsConfig.DisplayFields = checkedTmpArr;
                            //console.log(JSON.stringify(printFieldsConfig));

                            doPrint(false);
                            //return false;
                        });
                    }
                    else {
                        doNewPrint(false);
                        //return false;
                    }
                    layer.close(layerIndex);
                },
                cancel: function () {
                    layer.closeAll();
                }
            });

        }
    }

    module.defaultPrinter = "0";
    // 加载默认打印机
    var loadPrinter = function loadPrinter() {
        var printers = lp.printers();
        module.defaultPrinter = printFieldsConfig.PrintParams.DefaultPrinter || "";

        var html = "";
        for (var i = 0; i < printers.length; i++) {
            html += '<option value="' + printers[i].value + '" ' + (printers[i].name === module.defaultPrinter ? "selected" : "") + '>' + printers[i].name + '</option>';
        }
        $("#purchase-printer-select").html(html);
    }

    module.bindPrinter = function () {
        savePrintFields("print", function () {
            var $printer = $("#purchase-printer-select");
            module.defaultPrinter = $printer.find("option:selected").text();
            layer.msg("已绑定打印机【" + module.defaultPrinter + "】");
        });
    }

    var doNewPrint = function (print) {
        var printer = $("#purchase-printer-select option:selected").text();
        if (!printer) {
            layer.alert("请选择打印机");
            return;
        }

        var printPaper = purchaseTemplateSet.pageType || "210_297";//$("#print-paper-select").val();
        var printDirection = purchaseTemplateSet.orient || 1;//$("#print-direction-select").val();

        var title = "备货单";
        var top = 40;
        var left = 10;

        var unitObj = new unitConversion();
        var paperWidth = 210.0;
        var paperHeight = 297.0;

        var intOrient = printDirection; // 打印方向
        var strPageName = "LodopCreateCustomPage"; //纸张名称
        var arr = printPaper.split("_");
        if (arr.length == 2) {
            paperWidth = intOrient == 1 ? arr[0] : arr[1];
            paperHeight = intOrient == 1 ? arr[1] : arr[0];
            strPageName = strPageName + "_" + printPaper;
        }
        paperWidth = unitObj.mmToPx(paperWidth);
        paperHeight = unitObj.mmToPx(paperHeight);

        try {
            LODOP = lp.getLodop(document.getElementById('LODOP_OB'), document.getElementById('LODOP_EM'));
            LODOP.PRINT_INIT(title);
            //设置打印机
            LODOP.SET_PRINTER_INDEXA(printer);
            LODOP.SET_PRINT_PAGESIZE(intOrient, paperWidth + "px", paperHeight + "px", strPageName); //设置纸张大小


            var headHtml = '<!DOCTYPE html><html><head><link href="/Content/css/setBase.css" rel="stylesheet"><link href="/Content/css/purchases/purchases.css" rel="stylesheet"></head>';

            var $container = $(".purchases").clone();
            $container.find(".stockup_table_title").remove();
            $container.find("tr.hasRemark").remove();
            $container.find(".newpurchases-content").css("text-align", "left");
            $container.find(".purchase_content").css("display", "inline-block");
            $container.find(".purchasesSet_table_content").css("display", "table-caption");

            //var trWidth = $container.find(".purchasesSet_table_content thead tr").width();
            //var $ths = $container.find(".purchasesSet_table_content thead tr th");
            //$ths.each(function (i, th) {
            //    var thWidth = $(th).width();
            //    var elmtWidth = Math.floor(thWidth * 1.0 / trWidth * 100);
            //    $(th).css("width", elmtWidth + "%");
            //    $container.find(".purchasesSet_table_content tbody tr td:eq(" + i + ")").css("width", elmtWidth + "%");
            //});

            var stockup_main = $container.find(".stockup_main").html();

            var bodyHtml = '<body>';
            bodyHtml += '<div class="wrapper purchases">';
            bodyHtml += '   <div class="content">';
            bodyHtml += '       <div class="stockup_main doPrint">';
            bodyHtml += stockup_main;
            bodyHtml += '       </div>';
            bodyHtml += '   </div>';
            bodyHtml += '</div>';
            bodyHtml += '</body></html>';

            // Lodop打印html内容
            //console.log(bodyHtml);

            var printHtml = headHtml + bodyHtml;
            // 设置打印内容和纸张边距 ADD_PRINT_HTM(int Top,int Left,int Width,int Height,strHtml)
            LODOP.ADD_PRINT_HTM(top + "px", left + "px", (paperWidth - left) + "px", (paperHeight - top) + "px", printHtml);
            //如果内容很多需要加延迟等待动态内容加载完毕,延迟lodop加载速度，等待网页图片或动态生成内容加载完成
            var trs = $("#newpurchasesTbody tr[data-index]").length;
            var waitMilSecs = Math.ceil(trs / 10) * 50;
            console.log("打印延迟加载图片：" + waitMilSecs + "ms")
            //设置上一项延迟超文本下载N毫秒
            LODOP.SET_PRINT_STYLEA(0, "HtmWaitMilSecs", waitMilSecs);
            if (print) {
                LODOP.PRINT();
            }
            else {
                //LODOP.PRINT_DESIGN(); //查看设计属性，获取打印的html，用于调试
                LODOP.PREVIEW();
            }
        } catch (e) {
            console.log("备货单打印异常：" + e);
        }
    }

    // 打印预览或直接打印
    var doPrint = function (print) {
        var printer = $("#purchase-printer-select option:selected").text();
        if (!printer) {
            layer.alert("请选择打印机");
            return;
        }

        if (printFieldsConfig.PrintFields.length == 0) {
            layer.alert("至少需要选择1列进行打印");
            return;
        }

        var printPaper = $("#print-paper-select").val();
        var printDirection = $("#print-direction-select").val();

        var title = "备货单";
        var top = 0;
        var left = 0;

        var paperWidth = "210";
        var paperHeight = "297";

        //var font_size = 14;
        //var font_family = "宋体";

        var intOrient = printDirection; // 打印方向
        var strPageName = "LodopCreateCustomPage"; //纸张名称

        var arr = printPaper.split("_");
        if (arr.length == 2) {
            paperWidth = intOrient == 1 ? arr[0] : arr[1];
            paperHeight = intOrient == 1 ? arr[1] : arr[0];
            strPageName = strPageName + "_" + printPaper;
        }
        try {
            LODOP = lp.getLodop(document.getElementById('LODOP_OB'), document.getElementById('LODOP_EM'));
            LODOP.PRINT_INIT(title);
            //设置打印机
            LODOP.SET_PRINTER_INDEXA(printer);
            //LODOP.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "Auto-Width"); // 内容适应纸张宽度 
            LODOP.SET_PRINT_PAGESIZE(intOrient, paperWidth + "mm", paperHeight + "mm", strPageName); //设置纸张大小
            //LODOP.SET_PRINT_MODE("CREATE_CUSTOM_PAGE_NAME", strPageName); //设置自定义纸张别名,适应某些不能动态设置纸张大小的打印机
            //LODOP.SET_PRINT_STYLE("FontSize", font_size); //设置字体
            //LODOP.SET_PRINT_STYLE("FontName", font_family); //设置字体
            //LODOP.SET_PRINT_STYLEA("LineSpacing", -4); //设置行间距

            var $container = $(".stockup_content").clone();
            $container.find(".remarkParticulars,td.td_remark_icon").remove();

            var unitObj = new unitConversion();
            var imgSize = printFieldsConfig.PrintParams.ImgSize.split("x");
            var attrImgSize = printFieldsConfig.PrintParams.AttrImgSize.split("x");

            var imgWidth_Px = imgSize[0] || 80;
            var attrImgWidth_Px = attrImgSize[0] || 30;
            var imgWidth_Mm = 0; //px->mm
            var attrImgWidth_Mm = 0; //px->mm

            // 设置表格宽度
            var count = 0;
            var $displayThs = [];
            for (var i = 0; i < printFieldsConfig.PrintFields.length; i++) {
                var item = printFieldsConfig.PrintFields[i];
                var fieldName = item.Value;
                if (fieldName != "ProductImgUrl" && fieldName != "SkuImgUrl") {
                    if (fieldName == "ShortTitle" || fieldName == "ProductCargoNumber" || fieldName == "CargoNumber" || fieldName == "Color" || fieldName == "Size")
                        count += 1.5;
                    else if (fieldName == "ProductSubject")
                        count += 3;
                    else
                        count += 1;
                }
                else {
                    if (fieldName == "ProductImgUrl")
                        imgWidth_Mm = unitObj.pxToMm(imgWidth_Px); //px->mm
                    else if (fieldName == "SkuImgUrl")
                        attrImgWidth_Mm = unitObj.pxToMm(attrImgWidth_Px); //px->mm
                }
                var $th = $container.find("th[class=" + item.Value + "]");
                if ($th.length > 0)
                    $displayThs.push($th);
            }

            paperWidth = intOrient == 1 ? paperWidth : paperHeight;
            paperWidth = paperWidth - left * 2;
            var imgTdWidth = 100.0 * imgWidth_Mm / paperWidth;
            var attrImgTdWidth = 100.0 * attrImgWidth_Mm / paperWidth;
            var avgWidth = (100 - imgTdWidth - attrImgTdWidth) / count; //除图片外其他列平均百分比

            var width = avgWidth;
            var $ths = $container.find("thead>tr>th");
            var $tds = $container.find("tbody>tr:not('.table_footer')>td");
            $tds.removeAttr("width").removeAttr("height");
            $ths.removeAttr("width").removeAttr("height");
            for (var i = 0; i < $displayThs.length; i++) {
                var $th = $($displayThs[i]);
                var index = $ths.index($th);
                var $displayTds = $tds.filter("[data-index=" + index + "]");
                var fieldName = $th.attr("class");

                if (fieldName == "ProductImgUrl") {
                    width = imgTdWidth
                    var $imgs = $displayTds.find("img");
                    $imgs.attr({ "width": imgWidth_Px, "height": imgWidth_Px });
                    $imgs.css({ "max-width": imgWidth_Px, "width": imgWidth_Px, "height": imgWidth_Px });
                }
                else if (fieldName == "SkuImgUrl") {
                    width = attrImgTdWidth
                    var $imgs = $displayTds.find("img");
                    $imgs.attr({ "width": attrImgWidth_Px, "height": attrImgWidth_Px });
                    $imgs.css({ "max-width": attrImgWidth_Px, "width": attrImgWidth_Px, "height": attrImgWidth_Px });
                }
                else {
                    if (fieldName == "ShortTitle" || fieldName == "ProductCargoNumber" || fieldName == "CargoNumber" || fieldName == "Color" || fieldName == "Size")
                        width = 1.5 * avgWidth;
                    else if (fieldName == "ProductSubject") {
                        width = 3 * avgWidth;
                        $displayTds.css("color", "black");
                    }
                    else
                        width = avgWidth;
                }

                var elmtWidth = Math.floor(width * 100) / 100;
                $th.attr("width", elmtWidth + "%");
                $displayTds.attr("width", elmtWidth + "%");
            }

            var strStyle = "<!DOCTYPE html><html><head><link href='../../Content/css/purchases/purchasePrint.css' type='text/css' rel='stylesheet' \/></head>";
            displayColumns($container.find("table.stockup_table_content"), "print");
            var strbody = strStyle + "<body>" + $container.html() + "</body></html>";
            //console.log(strbody);
            //if (window.location.href.indexOf("localhost") != -1 || window.location.href.indexOf("test") != -1) {
            //    var htmlObj = { Body: strbody, PageWidth: paperWidth, isFilterDangerChar: false };
            //    commonModule.Ajax({
            //        url: "/Purchases/Test",
            //        data: htmlObj,
            //        success: function (result) {
            //            if (result.Success) {
            //                console.log(strbody);
            //            }
            //            else {
            //                layer.msg(result.Message);
            //            }
            //        }
            //    });
            //}

            // 设置打印内容和纸张边距
            LODOP.ADD_PRINT_HTM(top + "mm", left + "mm", "RightMargin:" + left + "mm", "BottomMargin:" + top + "mm", strbody); //"BottomMargin:100px"

            //如果内容很多需要加延迟等待动态内容加载完毕,延迟lodop加载速度，等待网页图片或动态生成内容加载完成
            var trs = $(".stockup_table_content tr[data-pid]").length;
            var waitMilSecs = Math.ceil(trs / 10) * 50;
            console.log("打印延迟加载图片：" + waitMilSecs + "ms")
            //设置上一项延迟超文本下载N毫秒
            LODOP.SET_PRINT_STYLEA(0, "HtmWaitMilSecs", waitMilSecs);

            if (print) {
                LODOP.PRINT();
            }
            else {
                //LODOP.PRINT_DESIGN(); //查看设计属性，获取打印的html，用于调试
                LODOP.PREVIEW();
            }
        } catch (e) {
            console.log("备货单打印异常：" + e);
        }
    }

    // 像素单位转换
    var unitConversion = function unitConversion() {
        /**
         * 获取DPI
         * @returns {Array}
         */
        this.getDPI = function () {
            var arrDPI = new Array;
            if (window.screen.deviceXDPI) {
                arrDPI[0] = window.screen.deviceXDPI;
                arrDPI[1] = window.screen.deviceYDPI;
            } else {
                var tmpNode = document.createElement("DIV");
                tmpNode.style.cssText = "width:1in;height:1in;position:absolute;left:0px;top:0px;z-index:99;visibility:hidden";
                document.body.appendChild(tmpNode);
                arrDPI[0] = parseInt(tmpNode.offsetWidth);
                arrDPI[1] = parseInt(tmpNode.offsetHeight);
                tmpNode.parentNode.removeChild(tmpNode);
            }
            return arrDPI;
        };
        /**
         * px转换为mm
         * @param value
         * @returns {number}
         */
        this.pxToMm = function (value) {
            var inch = value / this.getDPI()[0];
            var c_value = inch * 25.4;
            //      console.log(c_value);
            return c_value;
        };
        /**
         * mm转换为px
         * @param value
         * @returns {number}
         */
        this.mmToPx = function (value) {
            var inch = value / 25.4;
            var c_value = inch * this.getDPI()[0];
            //      console.log(c_value);
            return c_value;
        }
    }


    // 展开所有备注详情
    var expandAllDetail = function (checked) {
        var $itemDetails = $(".remarkParticulars_right");
        if (checked) {
            $(".stockup_table_content td.td_remark_icon span").each(function (i, span) {
                $(span).click();
            });

            $(".stockup_table_content .remarkParticulars").show();
            $itemDetails.filter(":contains('展开')").each(function () {
                $(this).find(".collapse").text("收缩");
                $(this).find(".icon_show").css({ backgroundPosition: "-164px -20px" })
                $(this).parent().parent().find(".remarkParticulars_content").show();
            });
        }
        else {
            $(".stockup_table_content .remarkParticulars").hide();
            $itemDetails.filter(":contains('收缩')").each(function () {
                $(this).find(".collapse").text("展开");
                $(this).find(".icon_show").css({ backgroundPosition: "-154px -20px" })
                $(this).parent().parent().find(".remarkParticulars_content").hide();
            });
        }
    }

    // 显示备注详情
    module.showPurchaseDetail = function (pid) {
        loadRemarkOrdersInfo(pid);

        var $detailTrs = $(".stockup_table_content tr[class*=tr-pid-" + pid + "]");
        // 已展开
        if ($detailTrs.is(":visible")) {
            $detailTrs.hide();
        }
        else {
            $detailTrs.show();
        }
    }

    // 显示所在订单的订单项详情
    module.showOrderItemDetail = function (obj) {
        var text = $(obj).find(".collapse").text();
        if (text === "展开") {
            $(obj).find(".collapse").text("收缩");
            $(obj).find(".icon_show").css({ backgroundPosition: "-164px -20px" })
            $(obj).parent().parent().find(".remarkParticulars_content").show();
        }
        else {
            $(obj).find(".collapse").text("展开");
            $(obj).find(".icon_show").css({ backgroundPosition: "-154px -20px" })
            $(obj).parent().parent().find(".remarkParticulars_content").hide();

        }
    }

    // form表单参数转对象
    var serializeFormObject = function (selector) {
        var a, o, h, i, e;
        a = $(selector).serializeArray();
        o = {};
        h = o.hasOwnProperty;
        for (i = 0; i < a.length; i++) {
            e = a[i];
            if (!h.call(o, e.name)) {
                o[e.name] = e.value;
            }
        }
        return o;
    }

    // 订单分类--修改别名弹框
    var renameAlias = function () {
        layer.open({
            type: 1,
            title: "设置分类别名", //不显示标题
            content: $('.orderClassify_main'),
            area: ['500', '600'], //宽高
            btn: ['保存', '取消'],
            yes: function () {
                updateCategoryAlias();
            },
            cancel: function () {

            }
        });
    }

    // 修改分类别名
    var updateCategoryAlias = function () {
        var models = [];
        $(".orderClassify_main>ul>li input").each(function () {
            var name = $(this).parent().find("span").eq(0).text();
            var color = $(this).parent().find("span").eq(1).css("color");
            models.push({ "Id": $(this).attr("data-id"), "Alias": $(this).val(), "Name": name, "Color": color });
        });

        commonModule.Ajax({
            url: "../Order/UpdateOrderCategoryAlias",
            data: { "Categorys": JSON.stringify(models) },
            success: function (result) {
                if (result.Success) {
                    updateCategoryAliasToHtml(models);
                    layer.closeAll();
                }
                else {
                    layer.msg(result.Message);
                }
            }
        });
    }

    // 订单分类更新页面显示数据
    var updateCategoryAliasToHtml = function (categorys) {
        // 更新查询中的分类别名
        var html = "";
        html = "<option value=\"\">未分类+已分类订单</option>";
        html += "<option value=\"NoCategoryOrder\">未分类订单</option>";
        html += "<option value=\"CategoryOrder\">已分类订单</option>";
        for (var i = 0; i < categorys.length; i++) {
            html += '<option value="' + categorys[i].Id + '" style="color: ' + categorys[i].Color + '">★' + categorys[i].Alias + '</option>';
        }
        html += "<option value=\"UpdateCategoryName\">修改分类别名</option>";
        $(".classifyOrder").html(html);

        // 更新更多查询的分类别名
        var html = "";
        html += "<li data-value=\"\">未分类+已分类订单</li>";
        html += "<li data-value=\"NoCategoryOrder\">未分类订单</li>";
        html += "<li data-value=\"CategoryOrder\">已分类订单</li>";
        for (var i = 0; i < categorys.length; i++) {
            html += "<li data-value=\"" + categorys[i].Id + "\" style=\"color:" + categorys[i].Color + ";\">" + categorys[i].Alias + "</li>"
        }
        //html += "<li data-value=\"UpdateCategoryName\" onclick=\"orderPrintModule.OpenUpdateOrderCategoryPanel()\">修改分类别名</li>";
        $("#addSearchCondition ul[data-name=CategoryId]").html(html);

        // select 订单分类修改别名
        html = "<ul>";
        for (var i = 0; i < categorys.length; i++) {
            html += '<li>';
            html += '<span>' + categorys[i].Name + '</span>的别名：';
            html += '<span style="color: ' + categorys[i].Color + '">★</span>';
            html += '<input type="text" data-id="' + categorys[i].Id + '" value="' + categorys[i].Alias + '">';
            html += '</li>';
        }
        html += "</ul>";
        $(".orderClassify_main").html(html);
    }


    var _loadRemarkFlagName = function () {
        var str = "";
        for (var i = 0; i < customRemarkFlags.length; i++) {
            var text = (customRemarkFlags[i].Operator == "1" ? "包含：" : "不包含：") + customRemarkFlags[i].Text;
            str += '<li><span class="flag-show-contentNav">' + text + '</span><span  class="flag-show-dev" onclick=\'purchaseModule.DeleFlagShow("' + customRemarkFlags[i].Operator + '","' + customRemarkFlags[i].Value.replaceAll("\"", "\\\"") + '")\'>删除</span></li>'
        }
        $("#flagShowContent").html(str);
    }

    module.DeleFlagShow = function (opt, value) {  //删除自定义旗帜数据
        for (var i = 0; i < customRemarkFlags.length; i++) {
            if (customRemarkFlags[i].Operator == opt && customRemarkFlags[i].Value == value) {
                customRemarkFlags.splice(i, 1);
                $(".stockup_search select[name=SellerRemarkFlag]>option").filter("[value='" + value + "'][data-operator='" + opt + "']").remove();
                //$(".addOrderList_ul ul[name=SellerRemarkFlag]>li").filter("[data-value=" + customRemarkFlags.Value + "]").remove();
            }
        }
        _loadRemarkFlagName();
    }

    module.openCloseAialogs = function (name) {
        layer.open({
            type: 1,
            title: "查询条件管理",
            content: $('.addSearch'),
            scrollbar: false,
            area: '600',
            yes: function () {

            },
            cancel: function () {

            }
        });

    }

    module.Closes = function () {
        layer.closeAll();
    }

    function exportSet() {
        layer.open({
            type: 1,
            title: "导出设置",
            content: $('.exportSet'),
            area: ['600', '600'], //宽高
            btn: ['添加', '取消'],
            yes: function () {

            },
            cancel: function () {

            }
        });
    }

    function showContentSet() {
        layer.open({
            type: 1,
            title: "商品信息显示内容设置",
            content: $('.contentInfernationSet'),
            area: ['450', '600'], //宽高
            btn: ['添加', '取消'],
            yes: function () {

            },
            cancel: function () {

            }
        });
    }

    module.getCheckedSetting = function () {
        return checkedArr;
    }

    var loadDefaultPrintSet = function () {
        var $checkboxes = $(".setting_content :checkbox").prop("checked", false);
        var $printCheckboxes = $(".print_setting_content :checkbox").prop("checked", false);

        // 加载列表需要显示字段勾选框
        if (printFieldsConfig.DisplayFields && printFieldsConfig.DisplayFields.length > 0) {
            for (var i = 0; i < printFieldsConfig.DisplayFields.length; i++) {
                $checkboxes.filter(":checkbox[value=" + printFieldsConfig.DisplayFields[i].Value + "]").prop("checked", true);
            }
        }
        $(".checkall_setting").prop("checked", printFieldsConfig.DisplayFields && printFieldsConfig.DisplayFields.length == $checkboxes.length);
        // 基础设置
        $("#setting-img-size option[value='" + (printFieldsConfig.SettingParams.ImgSize || _defaultImgSize.DisplayImgSize) + "']").prop("selected", true);
        $("#setting-attr-img-size option[value='" + (printFieldsConfig.SettingParams.AttrImgSize || _defaultImgSize.DisplayAttrImgSize) + "']").prop("selected", true);

        // 查询条件排除勾选框                        
        if (customFilterConditions.length > 0) {
            for (var i = 0; i < customFilterConditions.length; i++) {
                if (customFilterConditions[i] != "")
                    $(".search_condition .checkboxline input[name=" + customFilterConditions[i] + "]").prop("checked", true);
            }
        }

        // 加载需要打印字段勾选框
        if (printFieldsConfig.PrintFields && printFieldsConfig.PrintFields.length > 0) {
            for (var i = 0; i < printFieldsConfig.PrintFields.length; i++) {
                $printCheckboxes.filter(":checkbox[value=" + printFieldsConfig.PrintFields[i].Value + "]").prop("checked", true);
            }
        }
        $(".print_checkall_setting").prop("checked", printFieldsConfig.PrintFields && printFieldsConfig.PrintFields.length == $checkboxes.length);
        // 打印属性设置
        $("#print-paper-select option[value=" + (printFieldsConfig.PrintParams.Paper || "A4") + "]").prop("selected", true);
        $("#print-direction-select option[value=" + (printFieldsConfig.PrintParams.Direction || "1") + "]").prop("selected", true);
        // 基础设置
        $("#print-img-size option[value='" + (printFieldsConfig.PrintParams.ImgSize || _defaultImgSize.PrintImgSize) + "']").prop("selected", true);
        $("#print-attr-img-size option[value='" + (printFieldsConfig.PrintParams.AttrImgSize || _defaultImgSize.PrintAttrImgSize) + "']").prop("selected", true);
    }

    module.openColumnSet = function () {
        // 查询中，禁用列表选择
        if (isQuerying) {
            commonModule.preventDefault();
            return;
        }

        layer.open({
            type: 1,
            title: "列表设置",
            content: $('.tableColumnSet'),
            area: ['450', '600'], //宽高
            btn: ['保存', '取消'],
            yes: function () {
                module.SaveColSetting(function () {
                    if (!_isUseOldTemplate)
                        module.exportExcel();
                });
            },
            cancel: function () {

            }
        });
    }

    module.SaveColSetting = function (callback) {
        savePrintFields("display", function (type, checkedTmpArr) {
            if (type == "print")
                printFieldsConfig.PrintFields = checkedTmpArr;
            else {
                printFieldsConfig.DisplayFields = checkedTmpArr;
                // 更新主图尺寸
                if (printFieldsConfig.SettingParams.ImgSize) {
                    var imgSize = printFieldsConfig.SettingParams.ImgSize.split("x");
                    var imgWidth = imgSize[0];
                    var imgHeight = imgWidth;

                    var $imgs = $(".stockup_table_content td.productImg img");
                    $imgs.css({ "width": imgWidth + "px", "height": imgHeight + "px" }).attr({ "width": imgWidth, "height": imgHeight });
                }

                // 更新规格图尺寸
                if (printFieldsConfig.SettingParams.AttrImgSize) {
                    var imgSize = printFieldsConfig.SettingParams.AttrImgSize.split("x");
                    var imgWidth = imgSize[0];
                    var imgHeight = imgWidth;

                    var $imgs = $(".stockup_table_content td.productAttrImg img");
                    $imgs.css({ "width": imgWidth + "px", "height": imgHeight + "px" }).attr({ "width": imgWidth, "height": imgHeight });
                }
            }

            displayColumns($(".purchase_content table.stockup_table_content"), "display");
            layer.closeAll();
            if (typeof callback == "function")
                callback();
        });
    }


    //扫描打印------------------------------------------------------------------------------------------------

    module.openScanPrintSet = function () {

        layer.open({
            type: 1,
            title: "扫描设置",
            content: $('.setScanPrint'),
            area: ['650', '600'], //宽高
            btn: ['保存', '取消'],
            yes: function () {
            },
            cancel: function () {

            }
        });
    }

    module.inputBarCode = function () {

        var event = arguments.callee.caller.arguments[0] || window.event;
        if (event.keyCode == 13) {

            module.searchByScanPrint();

        }

    }

    module.searchByScanPrint = function () {

        $("#111111").addClass("scanPrint-status11").find(".status-icon").text("1 - ").nextAll('s').text('已备齐');
        $("#111111").find(".scanPrint-reminder").show();//显示+1的图标
        setTimeout(function () {//5秒显示，关闭+1图标
            $("#111111").find(".scanPrint-reminder").hide();

        }, 5000)

    }

    $('.search_one_labelOne input[name="PlatformOrderId"]').on("blur", function () {
        if ($(this).val().replace(/(^\s+)|(\s+$)/g, "") != "") {
            $(".search_one_labelOne input[type='text']").attr("disabled", "disabled");
            $(this).removeAttr("disabled");
            $(this).css({ border: "1px solid #8aace3", boxShadow: "0 0 0 1px #a5c7fe", borderRadius: "1px" });
            $(".search_one select:not('#SortBy_Product_Sku'):not('#OrderBy_Product_Sku'):not('#SortBy_ProductAttr'):not('#OrderBy_ProductAttr')").attr("disabled", "disabled");
        } else {
            $(".search_one_labelOne input[type='text']").removeAttr("disabled");
            $(this).css({ border: "1px solid #d2d2d2", boxShadow: "none", borderRadius: "none" });
            $(".search_one select").removeAttr("disabled");
            if (!_isUseOldTemplate) {
                $("#SortBy_Product_Sku,#SortBy_ProductAttr").attr("disabled", "disabled");
            }
        }

    })

    /*------------------任务生成备货单-----------------------*/
    //轮询检测后台导出任务状态(增加导出任务去除)
    //module.CheckExportTaskStatus = function (isNewTask) {
    //    if (commonModule.CreatePurchaseTask == null)
    //        return;
    //    if (isNewTask)
    //        progressModule.SetProgress(commonModule.CreatePurchaseTask);

    //    disbleCreatePurchaseBtn();
    //    var checkExportTaskInterval = setInterval(function () {
    //        var task = commonModule.CreatePurchaseTask;
    //        if (!task || task.Id == "" || task.Status == -1 || task.Status == -10) {
    //            clearInterval(checkExportTaskInterval);
    //            $("#ExportProgress").hide();
    //            return;
    //        }
    //        commonModule.Ajax({
    //            type: "POST",
    //            url: "/Purchases/CheckExportTaskStatus",
    //            data: { "id": task.Id },
    //            success: function (rsp) {
    //                //console.log(rsp);
    //                if (rsp.Success) {
    //                    commonModule.CreatePurchaseTask = rsp.Data || null;
    //                    task = commonModule.CreatePurchaseTask;
    //                    if (task.Status == -1 || task.Status == -10) {
    //                        showCreatePurchaseBtn();
    //                        clearInterval(checkExportTaskInterval);
    //                        $("#ExportProgress").hide();
    //                        layer.confirm("生成备货单异常，请联系我们");
    //                        return;
    //                    }
    //                    progressModule.SetProgress(task, function () {
    //                        //导出完成，关闭轮询           
    //                        clearInterval(checkExportTaskInterval);
    //                        //更新最近导出时间
    //                        var key = "/Purchase/CreatePurchase/UpdateTime";
    //                        var val = (new Date()).Format("yyyy-MM-ddThh:mm:ss");
    //                        commonModule.SaveCommonSetting(key, val, function () {
    //                            lastUpdateTimeSet = val;
    //                            module.UpdateExportShow();
    //                            module.CreatePurchaseByTask();
    //                        });
    //                    });
    //                }
    //                else {
    //                    showCreatePurchaseBtn();
    //                    clearInterval(checkExportTaskInterval);
    //                    $("#ExportProgress").hide();
    //                    layer.confirm("生成备货单失败，请重试。如果问题依然存在，请联系我们");
    //                }
    //            }
    //        });
    //    }, 5000);
    //}

    //更新导出按钮显示状态
    module.UpdateExportShow = function () {
        if (!lastUpdateTimeSet)
            return;

        var interval = setInterval(function () {
            var lastUpdateTime = new Date(lastUpdateTimeSet);
            var diff = lastUpdateTime.DateDiff("s", new Date());
            var $index_btn1 = $("#CreatePurchase");
            if (diff < defaultExpireSeconds) {
                //隐藏遮罩
                var tip = (defaultExpireSeconds - diff) + "s";
                $index_btn1.text(tip);
                $index_btn1.attr("style", "background: linear-gradient(#ddd, #ddd 50%, #ddd);");
                $index_btn1.removeAttr("onclick");
            }
            else {
                //显示遮罩
                showCreatePurchaseBtn();
                clearInterval(interval);
            }
        }, 1000);
    }

    var disbleCreatePurchaseBtn = function () {
        var $index_btn1 = $("#CreatePurchase");
        $index_btn1.attr("style", "background: linear-gradient(#ddd, #ddd 50%, #ddd);");
        $index_btn1.removeAttr("onclick");
    }

    var showCreatePurchaseBtn = function () {
        var $index_btn1 = $("#CreatePurchase");
        $index_btn1.text("生成备货单");
        isQuerying = false;
        $index_btn1.attr("style", "background: linear-gradient(#5dbbff, #49b2ff 50%, #3aadff);");
        $index_btn1.attr("onclick", "purchaseModule.createPurchase(true)");
    }



    module.CreatePurchaseByTask = function () {

        var url = $("#task-progress-url").attr("data-url");
        //// 获取生成备货单数据json
        var layerIndex = commonModule.LoadingMsg("加载备货单数据中…", 150);
        commonModule.Ajax({
            url: url,
            type: "GET",
            success: function (rsp) {
                //console.log(rsp);

                if (rsp) {
                    var data = JSON.parse(rsp.Data) || {};
                    _isUseOldTemplate = getTemplateType();
                    $(".tip-content").hide();
                    //console.log(JSON.stringify(result.Data));
                    $(".display_setting").attr("data-historyId", "");
                    $(".display_setting").attr("data-from", "create");
                    // 生成备货单使用系统显示列的配置
                    var setting = module.PurchaseSetting() || {};
                    setting.PurchaseItemsModel = data || {};
                    //var logId = result.Data.LogId;
                    //var date1 = new Date();  //开始时间
                    packageOrderData();
                    render();
                    //var date2 = new Date();    //结束时间
                    //var ms = date2.getTime() - date1.getTime()  //时间差的毫秒数

                    //upadteRenderTime(logId, ms);
                }

                isQuerying = false;
                editable = false;
                layer.close(layerIndex);
            }
        });

    }

    //新手指引教程
    module.purchasesShowHelps = function () {

        var noviceIntroObj = {

            backgroundColor: "#000", 	//遮罩背景色
            opacity: 0.8, 				//遮罩透明度
            isStartButton: false, 		//是否开启 跳过使用步骤 显示直接去使用按钮
            startButtonTitle: '', 		//直接去使用按钮 名称   不设置默认为跳过:开始使用
            isStartButton: false, 		//是否开启 跳过使用步骤 显示直接去使用按钮
            isStartButton02: true,      //是否开启 跳过使用步骤 显示直接去使用按钮  这个按钮是显示在步骤旁边
            callBack: function () {     //最后关闭触发回调函数
                $(".purchases-tab-ul").css({ zIndex: 100 });
                $(".purchases-tab-ul").css({ backgroundColor: "transparent" });

            },
            steps: [
                {                   //步骤
                    elem: "#gotoPurchasesSet", 	//定位到某个元素上			
                    isStopOperate: true,	// 是否可操作指定区 默认为true
                    top: 0,
                    left: 0,
                    width: 120,
                    height: 45,
                    control: { 			//控制区
                        top: 35,
                        left: 0,
                        align: "left", 	//控制区文字 对齐
                        arrowsIcon: true,//是否出现箭头图标
                        arrowsIconDirection: "right", //箭头指向    不设置 箭头指向左
                        imgSrc: "../Content/Images/newNoviceIntro/purchases/newPurchases-1.png", //说明步骤  图片
                        stepCallBack: function () {//步骤回调函数
                            $(".purchases-tab-ul").css({ zIndex: 10000000 });
                            $(".stopNoviceIntroOperate").css({ backgroundColor: "#fff" })
                        }
                    }
                },
                {                   //步骤
                    elem: "#CreatePurchase", 	//定位到某个元素上			
                    isStopOperate: true,	// 是否可操作指定区 默认为true
                    top: 0,
                    left: 0,
                    width: 95,
                    height: 35,
                    control: { 			//控制区
                        align: "left", 	//控制区文字 对齐
                        arrowsIcon: true,//是否出现箭头图标
                        arrowsIconDirection: "right", //箭头指向    不设置 箭头指向左
                        imgSrc: "../Content/Images/newNoviceIntro/purchases/newPurchases-2.png", //说明步骤  图片
                        stepCallBack: function () {//步骤回调函数
                            $(".purchases-tab-ul").css({ zIndex: 100 });
                        }
                    }
                }
                ,
                {                   //步骤
                    elem: ".tip-content,.newpurchases-content-title", 	//定位到某个元素上			
                    isStopOperate: false,	// 是否可操作指定区 默认为true
                    top: 0,
                    left: 0,
                    width: 1,
                    height: 1,
                    control: { 			//控制区
                        align: "left", 	//控制区文字 对齐
                        arrowsIcon: false,//是否出现箭头图标
                        arrowsIconDirection: "right", //箭头指向    不设置 箭头指向左
                        imgSrc: "../Content/Images/newNoviceIntro/purchases/newPurchases-5.png", //说明步骤  图片
                        stepCallBack: function () {//步骤回调函数

                        }
                    }
                }
                ,
                {                   //步骤
                    elem: ".footer", 	//定位到某个元素上			
                    isStopOperate: true,	// 是否可操作指定区 默认为true
                    top: 0,
                    left: 0,
                    control: { 			//控制区
                        top: 0,
                        align: "left", 	//控制区文字 对齐
                        arrowsIcon: false,//是否出现箭头图标
                        arrowsIconDirection: "right", //箭头指向    不设置 箭头指向左
                        buttonTitle: "开始使用", //下一步  步骤按钮标题  默认为下一步，可以不设置
                        imgSrc: "../Content/Images/newNoviceIntro/purchases/newPurchases-4.png", //说明步骤  图片
                        stepCallBack: function () {//步骤回调函数

                        }
                    }
                }
            ]
        };

        var newFun = new noviceIntro();
        newFun.initData(noviceIntroObj);

    }

    return module;
}(purchaseModule || {}, commonModule, lodopPrinter, jQuery, layer));