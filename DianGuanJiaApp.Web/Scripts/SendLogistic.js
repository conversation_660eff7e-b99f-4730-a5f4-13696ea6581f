/// <reference path="CommonModule.js" />
/// <reference path="/Scripts/orderlist/OrderTableBuilder.js" />
/// <reference path="/Scripts/orderlist/AddTemplateInOrderListModule.js" />
var sendLogistic = (function (sl, common, tmpModule, otb, $) {

    sl.check = function (orders, template, isDontNeedLogistic, confirmPrinted) {
        if (!orders || orders.length == 0) {
            layer.alert("请选择您要发货的订单");
            return false;
        }
        //检查是否是已经发货了的订单
        var errorCount = 0;
        for (var i = 0; i < orders.length; i++) {
            var index = orders[i].Index;
            var o = otb.rows[index];
            if (o.PlatformStatus != "waitsellersend")
                errorCount++;
        }
        if (errorCount == orders.length && commonModule.PlatformType != "Pinduoduo") {
            layer.alert("您选择的订单不是待发货订单，请选择待发货订单进行发货。", { btn: ["知道了"] });
            return false;
        }
        if (!isDontNeedLogistic && $("input[name='rdo_print_template']").length == 0) {
            layer.alert('您还没有添加快递模板，请点击<span id="sp_add_template" class="orderList_expressTemplate_addIcon"></span>添加快递模板');
            return false;
        }
        if (!isDontNeedLogistic) {
            if (template == null) {
                layer.alert("请选择您要发货的快递模板");
                return false;
            }
        }
        //return true; 
        var riskCtrlOrder = []; //拼多多风控订单
        var sendSFOrder = []; //拼多多顺丰加价订单
        var unPrintOrder = []; //获取了单号，但是没有打印标记的订单，发货提示 用户
        for (var i = 0; i < orders.length; i++) {
            var order = orders[i];
            var buyerName = order.Buyer.BuyerWangWang;
            if (!buyerName || buyerName == "")
                buyerName = order.Receiver.ToName;
            if (order.PlatformType == "Pinduoduo" && order.IsWeiGong == true) {
                riskCtrlOrder.push(order.PlatformOrderId);
            }

            if (order.PlatformType == "Pinduoduo" && order.ExtField1 == "1") {
                sendSFOrder.push(order.PlatformOrderId);
            }

            if (!order.LastExpressPrintTime) {
                unPrintOrder.push(order.PlatformOrderId);
            }

            if (order.OrderItems == null || order.OrderItems.length <= 0) {
                layer.alert("第【" + (order.Index + 1) + "】行买家【" + buyerName + "】的订单未选择任何商品，请展开订单勾选需要发货的商品。");
                return false;
            }
            if (!isDontNeedLogistic && (!order.WaybillCode || order.WaybillCode.trim() == "")) {
                layer.alert("第【" + (order.Index + 1) + "】行买家【" + buyerName + "】的快递单号不能为空，请先打印快递单，再进行发货");
                return false;
            }
        }

        if (unPrintOrder.length > 0 && !confirmPrinted) {
            var html = "";
            if (unPrintOrder.length < 10) {
                html = "订单【" + unPrintOrder.join(",") + "】未检测到打印标记，请确认是否有打印面单？"
            }
            else {
                html = "检测到有<label style='color:red;font-size:16px;font-weigth:bold;' title='" + unPrintOrder.join(",") + "'>" + unPrintOrder.length + "</label>";
                html += "个订单没有打印标记，请确认是否有打印面单？";
            }
            var dialogSendTips = layer.open({
                type: 1,
                title: "请确认是否有打印面单",
                content: "<div style='font-size:14px;line-height:20px;margin:25px;'><div><p style='margin:5px;'>" + html + "</p></div></div>",
                btn: ["确认已打印，继续发货", "取消"],
                shadeClose: true,
                area: ['550px', '250px'],
                btn1: function () {
                    layer.close(dialogSendTips);
                    sl.realSend(isDontNeedLogistic, orders, true); //已确认打印，继续发货
                    return true;
                },
                btn2: function () {
                    return true;
                }
            });
            return false;
        }

        if (riskCtrlOrder.length > 0) {
            layer.alert("订单【" + riskCtrlOrder.join(",") + "】风控中，不允许发货！<label style='color:red'>当风控解除后，请及时发货。</albel>");
            return false;
        }

        if (sendSFOrder.length > 0 && template.ExpressCompanyCode != "SF") {
            if (sendSFOrder.length == orders.length) {
                layer.alert("所选订单为“加价发顺丰”订单，请使用顺丰快递发货。");
                return false;
            }
            else {
                layer.alert("订单【" + sendSFOrder.join(",") + "】为“加价发顺丰”订单，请使用顺丰快递发货。");
                return false;
            }
        }


        return true;
    }

    sl.checkNoLogisticInfo = function (orders) {
        //检查必填项是否填写
        var noLogisticsCondition = $("#nologistics-condition-select").val();
        var noLogisticsBillNo = $("#nologistics-noLogisticsBillNo-input").val();
        var noLogisticsName = $("#nologistics-noLogisticsName-input").val();
        var noLogisticsTel = $("#nologistics-noLogisticsTel-input").val();
        var remarks = $("#nologistics-remarks-input").val();
        if (noLogisticsCondition == 1 || noLogisticsCondition == 3) {
            if (
                !noLogisticsName || noLogisticsName.trim() == "" ||
                !noLogisticsTel || noLogisticsTel.trim() == ""
            ) {
                var msg = commonModule.PlatformType == "WeiDian" ? "请填写快递名称和快递单号" : "请填写物流名称和物流联系方式";
                layer.msg(msg);
                return false;
            }
        }
        else if (noLogisticsCondition == 2) {
            if (orders.length == 1) {
                if (!noLogisticsBillNo || noLogisticsBillNo.trim() == "") {
                    layer.msg("请填写运单号");
                    return false;
                } else {
                    orders[0].WaybillCode = noLogisticsBillNo;
                }
            } else {
                //验证单号是否填写
                var count = 0;
                for (var i = 0; i < orders.length; i++) {
                    var order = orders[i];
                    if (!order.WaybillCode || order.WaybillCode.trim() == "") {
                        count++;
                    }
                }
                if (count > 0) {
                    layer.alert("您有" + count + "个订单没有填写运单号，发货原因不能为【补运费、差价】<br/>您可以先返回订单列表填写或获取运单号，或选择其他发货原因",
                        { area: ["550px"], icon: 2 });
                    return false;
                }
            }

        }
        return { noLogisticsCondition: noLogisticsCondition, noLogisticsBillNo: noLogisticsBillNo, noLogisticsName: noLogisticsName, noLogisticsTel: noLogisticsTel, remarks: remarks };
    }

    var _IgnoreOrdersByWaybillCodeIsEmpty = false; //忽略面单号为空的订单，打印后发货，如果有失败的，只发货成功的。
    sl.SetIgnoreOrderFlag = function (val) {
        _IgnoreOrdersByWaybillCodeIsEmpty = val;
    }

    sl.IsNotConfirm = false; // 是否不需要弹窗确认发货

    ///发货
    ///参数 orderId 若是针对单个订单时必填
    ///参数 isDontNeedLogistic 是否为不需要物流：true 不需要物流发货  ; false 需要物流发货 默认为false
    sl.send = function (isDontNeedLogistic, orderId, isNotConfirm) {
        sl.IsNotConfirm = isNotConfirm || false; // 是否不需要弹窗确认发货
        var orders = [];
        if (orderId)
            orders = otb.getSelections(orderId)
        else
            orders = otb.getSelections();

        //是否忽略运单号为空的订单
        if (_IgnoreOrdersByWaybillCodeIsEmpty) {
            var os = [];
            for (var i = 0; i < orders.length; i++) {
                var o = orders[i];
                if (o.WaybillCode != "") {
                    os.push(o);
                }
            }
            orders = os;
        }

        // 检查后端是否有退款订单
        otb.checkReturnOrders(orders);

        //// 头条平台部分发货检测
        //if (common.PlatformType == "TouTiao") {
        //    var isCheckAll = otb.checkTouTiaoOrders();
        //    if (!isCheckAll) {
        //        layer.confirm("当前平台不支持部分商品发货");
        //        return;
        //    }
        //}

        if (isDontNeedLogistic == null || isDontNeedLogistic == undefined) {
            //若是拼多多订单，且使用的非拼多多电子面单，给与提示
            var isPinduoduo = common.PlatformType == "Pinduoduo" || (orders && orders[0].PlatformType == "Pinduoduo");
            var template = tmpModule.GetCurrentTemplate();
            if (!template) { layer.alert("请选择发货模板"); }
            var ck = $.cookie("pdd-send-tips-no-show");
            if (isPinduoduo && !common.IsPddTemplate(template.TemplateType) && !ck) {
                var dialogIndex = layer.open({
                    type: 1,
                    title: "请确认",
                    content: "<div style='font-size:14px;line-height:20px;margin:25px;'><div><p style='margin:5px;'>您当前使用的不是拼多多电子面单，发货时可能会出现发货失败或网络超时。</p><p style='margin:5px;'>为了提升您的发货效率，根据拼多多官方建议，请使用拼多多电子面单。</p><p style='margin:5px;'>使用教程：<a style='color:#2ebae9;' target='_blank' href='https://www.dgjapp.com/newHelpContentPc.html?id=5cbe6ccb33088732d493806c'>前往查看</a></p></div></div>",
                    btn: ["继续发货", "不再提示，继续发货", "取消"],
                    shadeClose: true,
                    area: ['550px', '250px'],
                    btn1: function () {
                        layer.close(dialogIndex);
                        sl.realSend(isDontNeedLogistic, orders);
                        return true;
                    },
                    btn2: function () {
                        layer.close(dialogIndex);
                        $.cookie("pdd-send-tips-no-show", 1, { expires: 30 });
                        sl.realSend(isDontNeedLogistic, orders);
                        return true;
                    },
                    btn3: function () {
                        return true;
                    }
                });
            }
            else {
                sl.realSend(isDontNeedLogistic, orders);
            }
            return;
        }
        var isNot1688Orders = [];
        var alibabaOrders = [];
        for (var i = 0; i < orders.length; i++) {
            var o = orders[i];
            if (o.PlatformType == "Alibaba" || o.PlatformType == "1688" || o.PlatformType == "Taobao" || o.PlatformType == "YouZan" || o.PlatformType == "WeiDian" || o.PlatformType == "YunJi" || o.PlatformType == "WeiMeng")
                alibabaOrders.push(o);
            else
                isNot1688Orders.push(o);
        }
        if (!isDontNeedLogistic)
            sl.realSend(isDontNeedLogistic, orders);
        else if (isNot1688Orders.length == 0)
            sl.realSend(isDontNeedLogistic, orders);
        else {
            if (isNot1688Orders.length == orders.length) {
                layer.alert("您选择订单不支持【无物流发货】，请使用【批量发货】");
            } else {
                layer.open({
                    type: 1,
                    title: "请确认",
                    content: "<div style='font-size:14px;line-height:20px;margin:25px;'><div>您选择的订单中，有<span style='color:red;font-size:18px;'>" + isNot1688Orders.length + "</span>个不是1688或淘宝的订单，不能使用【无物流发货】</div><div>是否仅对1688和淘宝的订单进行【无物流发货】？</div></div>",
                    btn: ["仅对1688和淘宝订单进行【无物流发货】", "取消"],
                    shadeClose: true,
                    area: ['550px', '200px'],
                    btn1: function () {
                        for (var i = 0; i < isNot1688Orders.length; i++) {
                            var cur = isNot1688Orders[i];
                            $("#order-" + cur.Id).removeClass("onClickColor");
                            $(".order-chx[data-id='" + cur.Id + "']")[0].checked = false;//取消勾选
                        }
                        sl.realSend(isDontNeedLogistic, alibabaOrders);
                        return true;
                    },
                    btn2: function () {
                        return true;
                    }
                });
            }
        }
    }

    ///发货
    ///参数 orderId 若是针对单个订单时必填
    ///参数 isDontNeedLogistic 是否为不需要物流：true 不需要物流发货  ; false 需要物流发货 默认为false
    ///参数 confirmPrinted 确认已打印
    sl.realSend = function (isDontNeedLogistic, orders, confirmPrinted) {
        var isSingleOrder = orders.length == 1;
        var template = tmpModule.GetCurrentTemplate();
        if (!sl.check(orders, template, isDontNeedLogistic, confirmPrinted))
            return false;
        if (template == null)
            template = {};
        var ext = otb.getSelectionsExt(orders);
        var pt = otb.rows[0].PlatformType;
        var dialog = $.templates("#send-logistic-dialog-tmpl");
        var html = dialog.render({ Orders: orders, ExtInfo: ext, Template: template, isDontNeedLogistic: isDontNeedLogistic, isSingleOrder: isSingleOrder, platformType: pt, IsPddFds: common.IsPddFds() });
        var title = "发货";
        var height = "250px";
        if (isDontNeedLogistic) {
            title = "无需物流发货";
            height = "380px";
            if (pt == "YouZan")
                height = "250px";
        }

        var btnText = "确定发货";
        if (common.IsPddFds()) {
            btnText = "确定回传";
            title = "回传单号";
        }
        if (sl.IsNotConfirm == true) {
            //开始发货
            try {
                doSend(orders, ext, template, null);
            } catch (e) {
                console.error(e);
                alert("发货异常，请联系我们：" + e.message + "\r\n异常信息：" + e.stack);
            }
        }
        else {
            layer.open({
                type: 1,
                title: title,
                //closeBtn: 1,
                btn: [btnText, "取消"],
                shadeClose: true,
                area: ['550px', height],
                content: html,
                btn1: function () {
                    var nologisticModel = null;
                    if (isDontNeedLogistic) {
                        var temp = sl.checkNoLogisticInfo(orders)
                        if (temp == false)
                            return false;
                        else
                            nologisticModel = temp;
                    }
                    //开始发货
                    try {
                        doSend(orders, ext, template, nologisticModel);
                    } catch (e) {
                        console.error(e);
                        alert("发货异常，请联系我们：" + e.message + "\r\n异常信息：" + e.stack);
                    }
                    return false;
                },
                btn2: function () {
                    return true;
                }
            });
        }
        $("p[id^='nologistics-']").hide();
    }

    function doSend(orders, refundOrders, template, nologisticModel, isSendPreCheck) {
        //排除已退款的订单
        var request = {
            TemplateId: template.Id,
            ExpressCompanyId: template.ExpressCompanyId,
            Orders: orders,
            ExcludeOrders: refundOrders,
            NoLogisticsInfoModel: nologisticModel,
            IsSendPreCheck: (isSendPreCheck == undefined ? true : isSendPreCheck)
        };
        common.Ajax({
            url: "/Order/OnlineSend",
            data: { model: request },
            loadingMessage: "发货中...",
            success: function (data) {
                layer.closeAll();
                if (data.Success) {
                    try {
                        var model = data.Data;
                        model.Template = template;
                        var hideOrderCount = 0;
                        if (model.SuccessCount > 0) {

                            //保存普通模板 单号
                            common.SaveTraditionWaybillCodeConfig(template);

                            for (var i = 0; i < model.Orders.length; i++) {
                                var cur = model.Orders[i];
                                if (!cur.IsSuccess)
                                    continue;
                                var rowIndex = $("#order-" + cur.OrderRequest.Id).attr("data-index");
                                var row = otb.rows[rowIndex];
                                if (row.Id != cur.OrderRequest.Id) {
                                    common.JsExcptionLog("发货后前端查找到的订单不正确", "" + row.Id);
                                }
                                var sendedCount = 0;
                                var totalItemCount = 0;
                                $(row.SubOrders).each(function (a, sb) {
                                    $(sb.OrderItems).each(function (b, oi) {
                                        if (cur.OrderRequest.OrderItems.indexOf(oi.Id) != -1) {
                                            oi.Status = 'waitbuyerreceive';
                                        }
                                        if (oi.Status == "waitbuyerreceive")
                                            sendedCount++;
                                        totalItemCount++;
                                    });
                                });
                                row.checked = false;
                                if (cur.IsAllDelivered || totalItemCount == sendedCount || (cur.IsSuccess && totalItemCount == cur.OrderRequest.OrderItems.length)) {
                                    var id = cur.OrderRequest.Id;
                                    $(".order-chx[data-id='" + id + "']").remove();//移除已经发货的订单行的复选框，防止全选时被勾选导致重复打单发货
                                    $("#order-detail-" + id).next().remove();
                                    $("#order-" + id + " ,#order-detail-" + id).remove();//隐藏已发货订单
                                    //$("#order-" + id).removeClass("onClickColor");
                                    //$(".order-chx[data-id='" + id + "']")[0].checked = false;//取消勾选
                                    hideOrderCount += cur.RelatedPlatformOrderIds.split(',').length;
                                    otb.rows[rowIndex] = {};
                                } else {
                                    otb.refreshRow(row);
                                }
                            }
                            var leftOrders = $(".order-chx").length;
                            if (leftOrders == 0) {
                                //当前页面所有订单都已经发货了，则刷新页面
                                layer.msg("当前页订单已全部发货，重新加载订单数据...");
                                setTimeout(function () {
                                    $("#search-btn").trigger("click");
                                }, 1500);
                            }
                            else {
                                var numberSpan = $("#orderList_orderState_choose .active .i-order-count");
                                if (numberSpan.length > 0) {
                                    var text = numberSpan.text();
                                    if (text.length > 2) {
                                        var number = text.substring(1, text.length - 1);
                                        number -= hideOrderCount;
                                        numberSpan.text("[" + number + ']');
                                    }
                                    otb.changeTemplate(template);
                                }
                            }
                        }

                        if (model.ErrorCount > 0) {

                            //判断是否有发货预检查的报错，预检查报错用户可以确认继续发货，继续发货不再预检查
                            var btns = ["关闭"];
                            if (model.HasSendPreCheckError)
                                btns = ["坚持发货", "关闭"];

                            //显示详细的错误消息
                            var dialog = $.templates("#send-logistic-error-dialog-tmpl");
                            var html = dialog.render(model);
                            layer.open({
                                type: 1,
                                title: "发货结果",
                                //closeBtn: 1,
                                btn: btns,
                                shadeClose: true,
                                area: ['600px', '350px'],
                                content: html,
                                yes: function () {
                                    if (model.HasSendPreCheckError) {
                                        //坚持发货，将错误订单找出，继续发货
                                        var reSendOrders = [];
                                        for (var i = 0; i < model.Orders.length; i++) {
                                            var ro = model.Orders[i];
                                            if (ro.IsSuccess)
                                                continue;
                                            for (var j = 0; j < orders.length; j++) {
                                                var o = orders[j];
                                                if (ro.OrderRequest.PlatformOrderId == o.PlatformOrderId && ro.OrderRequest.ShopId == o.ShopId) {
                                                    reSendOrders.push(o);
                                                    break;
                                                }
                                            }
                                        }
                                        //继续发货，不预检查
                                        doSend(reSendOrders, refundOrders, template, nologisticModel, false);
                                    }
                                    else
                                        layer.closeAll();
                                },
                                btn2: function () {
                                    layer.closeAll();
                                },
                                cancel: function () {
                                    layer.closeAll();
                                }
                            });

                        }
                        else {
                            layer.msg("发货成功", { time: 500 });
                        }

                    } catch (e) {

                        var errorMsg = "发货成功后前端操作异常》" + e.stack;
                        console.log(errorMsg);
                        common.JsExcptionLog("发货前端异常日志", errorMsg);

                    }

                } else {
                    layer.alert("发货失败：" + data.Message);
                }
            }
        });
    }

    sl.changeNologisticsOption = function changeNologisticsOption($this, isSingleOrder) {
        var condition = $($this).val();
        $("p[id^='nologistics-']").hide();
        $("#nologistics-remarks-span").hide();
        $("#nologistics-remarks-p").show();

        if (condition == 1 || condition == 3) {
            $("#nologistics-noLogisticsName-p").show();
            $("#nologistics-noLogisticsTel-p").show();
        }
        else if (condition == 2 && isSingleOrder) {
            $("#nologistics-noLogisticsBillNo-p").show();
        } else if (condition == 5) {
            $("#nologistics-remarks-span").show();
        }

    }
    $(document).ready(function () {
        $(".batch-send-btn-nologistic").unbind("click").bind("click", function () {
            sl.send(true);
        });
    });

    ///拼多多厂家代打订单取消回传单号
    ///参数 orderId 若是针对单个订单时必填
    sl.cancelSend = function (orderId) {

        var template = tmpModule.GetCurrentTemplate();

        if (!template) {
            layer.msg("请选择模板");
            return;
        }

        var orders = [];
        if (orderId)
            orders = otb.getSelections(orderId)
        else
            orders = otb.getSelections();

        //排除没有单号的订单
        var notWaybillcodeOrders = [];
        var canCancelReturnOrders = [];
        common.Foreach(orders, function (i, o) {
            if (!o.WaybillCode) {
                notWaybillcodeOrders.push(o.PlatformOrderId);
            }
            else {
                canCancelReturnOrders.push(o);
            }
        });

        if (notWaybillcodeOrders.length == orders.length) {
            layer.alert("所选订单没有单号，请确认是否回收了单号，回收单号的订单回传请进入 <span style='color:blue;cursor:pointer;' onclick='commonModule.transferUrl(\"/PrintHistory/Index\",\"_blank\")'>打印记录</span> 找到相应的打印记录进行取消回传。");
            return;
        }

        var msg = "";
        if (notWaybillcodeOrders.length > 0) {
            msg = "订单【" + notWaybillcodeOrders.join(",") + "】没有单号可以取消回传，请确认是否已回收单号，回收的运单要取消回传请去 <span style='color:blue;cursor:pointer;' onclick='commonModule.transferUrl(\"/PrintHistory/Index\",\"_blank\")'>打印记录</span> 进行回传。<br/>"
        }

        var request = {
            TemplateId: template.Id,
            ExpressCompanyId: template.ExpressCompanyId,
            Orders: canCancelReturnOrders
        };

        var cancelReturnConfirm = layer.confirm(msg + '确定要取消单号回传吗?', { icon: 3, title: '取消单号回传确认' }, function (index) {
            common.Ajax({
                url: "/Order/CancelSend",
                data: { model: request },
                loadingMessage: "取消单号回传...",
                success: function (data) {
                    layer.closeAll();
                    if (data.Success) {
                        var model = data.Data;
                        model.Template = template;
                        var hideOrderCount = 0;

                        if (model.SuccessCount > 0) {
                            $("#search-btn").trigger("click");
                        }

                        if (model.ErrorCount > 0) {
                            //显示详细的错误消息
                            var dialog = $.templates("#send-logistic-error-dialog-tmpl");
                            var html = dialog.render(model);
                            layer.open({
                                type: 1,
                                title: "取消单号回传结果",
                                //closeBtn: 1,
                                btn: ["关闭"],
                                shadeClose: true,
                                area: ['600px', '350px'],
                                content: html,
                                yes: function () {
                                    layer.closeAll();
                                },
                                cancel: function () {
                                    layer.closeAll();
                                }
                            });

                        }
                        else {
                            layer.msg("取消单号回传成功", { time: 500 });
                        }

                    } else {
                        layer.alert("取消单号回传失败：" + data.Message);
                    }
                }
            });
            layer.close(cancelReturnConfirm);
        });
    }

    return sl;
}(sendLogistic || {}, commonModule, addTmplInOrderListModule, orderTableBuilder, jQuery));
