/// <reference path="../CommonModule.js" />
// 电子面单分享
var sharedWaybillAccountCheckModule = (function (module, common, $, layer) {

    $(function () {
        $("#shareWayBillAccount_navs").on("click", function (e) {
            var target = e.target;
            $("#shareWayBillAccount_navs>span").removeClass("active");
            // 1:新建单号分享,2:充值明细,3:使用明细,4:回收明细
            var type = 0;
            if (target.nodeName.toUpperCase() == "SPAN") {
                $(target).addClass("active");
                type = $(target).attr("data-type");
            } else if (target.nodeName.toUpperCase() == "I") {
                $(target).parent().addClass("active");
                type = $(target).parent().attr("data-type");
            }
            window.location.href = common.rewriteUrl("/ShareWayBillAccount/Index/" + type);
        });

        //加载列表
        module.LoadAccountCheckingList();

        //30秒自动刷新
        window.setInterval(function () { sharedWaybillAccountCheckModule.LoadAccountCheckingList(); }, 30000);
    });

    module.LoadAccountCheckingList = function () {
        commonModule.Ajax({
            url: "/SharedWaybillAccountCheck/LoadList",
            type: 'post',
            loading: true,
            success: function (rsp) {

                if (!rsp.Success) {
                    layer.msg(rsp.Message);
                    return;
                }

                var tplt = $.templates("#tmpl_account_checking");
                var html = tplt.render(rsp.Data, { rewriteUrl: common.rewriteUrl, encodeURI: encodeURI });
                var tb = $("#tb_accountchecking_list");
                if (html)
                    tb.empty().append(html);
                else
                    tb.empty().append('<tr><td colspan="99">暂无数据</td></tr>');
            }
        });
    }

    var fileName = ""; //文件名称
    var bindFileChoiceChange = function () {
        $("#input-choice-file").unbind('change').bind('change', function (e) {
            var file = e.currentTarget.files[0];
            if (file.size > (30 * 1024 * 1024)) {
                layer.msg("上传文件不能大于30M");
                return;
            }
            var name = file.name
            if (name.indexOf(".xls") == -1 && name.indexOf(".xlsx") == -1) {
                layer.msg("请导入Excel文件!");
                return;
            }
            fileName = name;
            module.ImportExcel();
        });
    }
    var importType = 0; //默认导入对账，1：导入查看轨迹
    module.ImportAccountChecking = function (type) {
        importType = type;
        bindFileChoiceChange();
        $("#input-choice-file").click();
    }

    // Excel导入
    module.ImportExcel = function () {
        common.AjaxFileUpload({
            url: '/Common/UploadFile',
            type: "POST",
            data: { "MaxSize": 30 },
            secureuri: false,
            fileElementId: 'input-choice-file',
            dataType: 'text',
            loadingMessage: "正在上传Excel…",
            success: function (result) {

                $("#input-choice-file").replaceWith('<input type="file" id="input-choice-file" name="upfile" size="1" />');
                bindFileChoiceChange();

                result = $.parseJSON(result.replace(/<.*?>/ig, ""));
                msg = result.Message;

                if (!result.Success) {
                    layer.msg(result.Message);
                    return;
                }

                var result = result.Data;

                if (importType == 0) {
                    layer.open({
                        type: 1,
                        title: "指定运单号",
                        content: $("#import_file_msg_after"),
                        area: [], //宽高
                        btn: ['确定'],
                        yes: function () {
                            var expCodeInColumnIndex = $("#txtColumnIndex").val().trim();
                            if (expCodeInColumnIndex == "") {
                                layer.msg("请输入运单号在第几列");
                                return false;
                            }
                            var columnIndex = parseInt(expCodeInColumnIndex);
                            if (isNaN(expCodeInColumnIndex) || columnIndex <= 0) {
                                layer.msg("请输入正确的数字");
                                return false;
                            }
                            layer.closeAll();
                            module.SaveImportResult(columnIndex, result, 0, '');
                        },
                        cancel: function () {

                        }
                    });
                }
                else {

                    layer.open({
                        type: 1,
                        title: "指定轨迹查询需要的信息",
                        content: $("#import_traces_file_msg_after"),
                        area: [], //宽高
                        btn: ['确定'],
                        yes: function () {
                            var expCodeInColumnIndex = $("#txtColumnIndex_2").val().trim();
                            if (expCodeInColumnIndex == "") {
                                layer.msg("请输入运单号在第几列");
                                return false;
                            }
                            var columnIndex = parseInt(expCodeInColumnIndex);
                            if (isNaN(expCodeInColumnIndex) || columnIndex <= 0) {
                                layer.msg("请输入正确的数字");
                                return false;
                            }
                            var waybillType = $("#selWaybillType").val();
                            if (waybillType == 0) {
                                layer.msg("请选择电子面单类型");
                                return false;
                            }
                            var expressCode = $("#selExpress").val();
                            if (expressCode == 0) {
                                layer.msg("请选择快递类型");
                                return false;
                            }
                            layer.closeAll();
                            module.SaveImportResult(columnIndex, result, waybillType, expressCode);
                        },
                        cancel: function () {

                        }
                    });
                }
            }
        });
    }

    module.Test = function () {
        layer.open({
            type: 1,
            title: "指定轨迹查询需要的信息",
            content: $("#import_traces_file_msg_after"),
            area: [], //宽高
            btn: ['确定'],
            yes: function () {
                var expCodeInColumnIndex = $("#txtColumnIndex").val().trim();
                if (expCodeInColumnIndex == "") {
                    layer.msg("请输入运单号在第几列");
                    return false;
                }
                var columnIndex = parseInt(expCodeInColumnIndex);
                if (isNaN(expCodeInColumnIndex) || columnIndex <= 0) {
                    layer.msg("请输入正确的数字");
                    return false;
                }
                layer.closeAll();
                module.SaveImportResult(columnIndex, result);
            },
            cancel: function () {

            }
        });
    }

    module.SaveImportResult = function (columnIndex, filePath, waybillType, expressCode) {
        commonModule.Ajax({
            url: "/SharedWaybillAccountCheck/SaveImprotResult",
            type: 'post',
            data: {
                columnIndex: columnIndex,
                fileName: fileName,
                filePath: filePath,
                importType: importType,
                waybillType: waybillType,
                expressCode: expressCode
            },
            loadingMessage: "保存对账信息...",
            success: function (rsp) {

                if (!rsp.Success) {
                    layer.msg(rsp.Message);
                    return;
                }
                module.LoadAccountCheckingList();
            }
        });
    }

    module.DeleteTask = function (id) {

        var confirm = layer.confirm("确定删除对账任务吗？", function () {
            layer.close(confirm);
            commonModule.Ajax({
                url: "/SharedWaybillAccountCheck/DeletedCheckingTask",
                type: 'post',
                data: { id: id },
                loadingMessage: "正在删除对账任务...",
                success: function (rsp) {

                    if (!rsp.Success) {
                        layer.msg(rsp.Message);
                        return;
                    }

                    module.LoadAccountCheckingList();
                }
            });

        }, function () {
        });
    }


    return module;
}(sharedWaybillAccountCheckModule || {}, commonModule, jQuery, layer));