/// <reference path="../jquery-1.10.2.min.js" />
/// <reference path="../layer/layer.js" />
/// <reference path="../dragModule.js" />
/// <reference path="../CommonModule.js" />

var editTemplateModule = (function (module, drag, common, $, layer) {

    module.SetItem = function (id, w, h, l, t, name, size, bold, txt, type) {

        var obj = document.getElementById(id);
        var objDiv = document.getElementById("div_" + id);
        var o = document.getElementById("font_" + id);
        var o1 = document.getElementById("input_" + id);
        if (w == "") {
            w = 120;
        } else {
            w = parseInt(w);
        }
        if (h == "") {
            h = 25;
        } else {
            h = parseInt(h);
        }
        l = parseInt(l);
        t = parseInt(t);
        o1.value = id + "," + name + "," + size + "," + bold;
        o.style.fontFamily = name;

        var sizeUnit = 'pt';

        //获取当前编辑的模板，如果是拼多多模板，则字体大小按px来算
        var template = (window.editWaybillTemplateModule) ? editWaybillTemplateModule.GetTemplate() : null;
        if (template && common.IsPddTemplate(template.TemplateType)) {//template.TemplateType > 20 && template.TemplateType < 30
            sizeUnit = 'px';
        }

        o.style.fontSize = size + sizeUnit;
        var sb = "normal";
        if (bold == 1) {
            sb = "bold";
        }
        o.style.fontWeight = sb;
        obj.checked = true;
        obj.value = l + "," + t + "," + w + "," + h;
        objDiv.style.display = "";
        objDiv.style.left = l + "px";
        objDiv.style.top = t + "px";
        objDiv.style.width = w + "px";
        objDiv.style.height = h + "px";
        if (type == 2 || type == 4) {
            $("#font_" + id).text(txt);
        }
    }

    module.Initialize = function (containerId) {

        var moveHandler = function (l, f, obj) {

            var divId = obj.parentNode.id;
            var chkid = divId.substr(divId.indexOf("div_") + 4, divId.length);
            var chk = document.getElementById(chkid);
            var vals = chk.value.split(",");
            chk.value = l + "," + f + "," + vals[2] + "," + vals[3];

        }
        var resizeHandler = function (w, h, obj) {
            var divId = obj.parentNode.id;
            var chkid = divId.substr(divId.indexOf("div_") + 4, divId.length);
            var chk = document.getElementById(chkid);
            var vals = chk.value.split(",");
            chk.value = vals[0] + "," + vals[1] + "," + w + "," + h;
        }
        drag.Initialize(containerId, moveHandler, resizeHandler);
    }

    module.Move = function (obj, e) {
        drag.Drag(obj, e);
    }

    module.Resize = function (obj, e) {
        drag.Resize(obj, e);
    }

    module.ShowDiv = function (obj) {
        var id = obj.id;
        var flag = obj.checked;
        var chkVal = obj.value;
        var arrVal = chkVal.split(",");
        x = parseInt(arrVal[0]);
        y = parseInt(arrVal[1]);
        var objDiv = document.getElementById("div_" + id);

        if (flag) {
            objDiv.style.display = "";
            objDiv.style.zIndex = 1000;
            objDiv.style.left = x + "px";
            objDiv.style.top = y + "px";
        } else {
            objDiv.style.display = "none";
        }
    }

    module.CloseDiv = function (id) {
        var divObj = document.getElementById(id);
        var chkid = id.substr(id.indexOf("div_") + 4, id.length);
        divObj.style.display = "none";
        var chkObj = document.getElementById(chkid);
        if (chkObj.checked) {
            chkObj.checked = false;
        }
    }

    module.SetTemplateSize = function (val) {
        if (val != "custom") {

            var arrVal = val.split(":");

            var t_w = document.getElementById("template_width");
            var t_h = document.getElementById("template_height");

            t_w.value = arrVal[0];
            t_h.value = arrVal[1];
            var w_h = common.MmToPx(t_w.value, t_h.value);

            //var pb = document.getElementById("pb");
            //pb.style.width = w_h.width + "px";
            //pb.style.height = w_h.height + "px";
            var ppic = document.getElementById("printPic");
            ppic.style.width = w_h.width + "px";
            ppic.style.height = w_h.height + "px";

            $("#wrape-printbox").css("width", w_h.width);
            $("#wrape-printbox").css("height", w_h.height);


        } else {
            document.getElementById("template_width").value = "";
            document.getElementById("template_height").value = "";
        }
    }

    module.ImageWidthHeight = function () {
        var offsetx = $.trim($("#template_offsetx").val());
        if (isNaN(offsetx) || offsetx == '') {
            $("#template_offsetx").val("0");
        }
        var offsety = $.trim($("#template_offsety").val());
        if (isNaN(offsety) || offsety == '') {
            $("#template_offsety").val("0");
        }
        var width = $.trim($("#template_width").val());
        var height = $.trim($("#template_height").val());
        if (isNaN(width) || width == '') {
            width = '230.0';
        }
        if (isNaN(height) || height == '') {
            height = '127.0';
        }
        width = parseFloat(width) < 50.0 ? '100.0' : width.toString();
        height = parseFloat(height) < 10.0 ? '10.0' : height.toString();
        width = parseFloat(width) > 270.0 ? '270.0' : width.toString();
        height = parseFloat(height) > 260.0 ? '260.0' : height.toString();
        $("#template_width").val(width);
        $("#template_height").val(height);

        var w_h = common.MmToPx(width, height);

        $("#printPic").css("width", w_h.width);
        $("#printPic").css("height", w_h.height);
        $("#wrape-printbox").css("width", w_h.width);
        $("#wrape-printbox").css("height", w_h.height);

        var t_l = common.MmToPx( $("#template_offsetx").val(), $("#template_offsety").val());
        $("#pb").css({ top: t_l.height, left: t_l.width });

        var obj = {};
        obj.offsetx = parseInt(offsetx);
        obj.offsety = parseInt(offsety);
        obj.width = width;
        obj.height = height;

        return obj;
    }

    module.Move_Left = function () {
        var arrChk = document.getElementsByName("chk");
        for (var i = 0; i < arrChk.length; i++) {
            var chk = arrChk[i];
            if (chk.checked == true) {
                var objDiv = document.getElementById("div_" + chk.id);
                objDiv.style.left = parseInt(objDiv.style.left) - 5 + "px";
                var chk = document.getElementById(chk.id);
                var vals = chk.value.split(",");
                chk.value = parseInt(objDiv.style.left) + "," + vals[1] + "," + vals[2] + "," + vals[3];
            }
        }
    }

    module.Move_Top = function () {
        var arrChk = document.getElementsByName("chk");
        for (var i = 0; i < arrChk.length; i++) {
            var chk = arrChk[i];
            if (chk.checked == true) {
                var objDiv = document.getElementById("div_" + chk.id);
                objDiv.style.top = parseInt(objDiv.style.top) - 5 + "px";
                var chk = document.getElementById(chk.id);
                var vals = chk.value.split(",");
                chk.value = vals[0] + "," + parseInt(objDiv.style.top) + "," + vals[2] + "," + vals[3];
            }
        }
    }

    module.Move_Right = function () {
        var arrChk = document.getElementsByName("chk");
        for (var i = 0; i < arrChk.length; i++) {
            var chk = arrChk[i];
            if (chk.checked == true) {
                var objDiv = document.getElementById("div_" + chk.id);
                objDiv.style.left = parseInt(objDiv.style.left) + 5 + "px";
                var chk = document.getElementById(chk.id);
                var vals = chk.value.split(",");
                chk.value = parseInt(objDiv.style.left) + "," + vals[1] + "," + vals[2] + "," + vals[3];
            }
        }
    }

    module.Move_Down = function () {
        var arrChk = document.getElementsByName("chk");
        for (var i = 0; i < arrChk.length; i++) {
            var chk = arrChk[i];
            if (chk.checked == true) {
                var objDiv = document.getElementById("div_" + chk.id);
                objDiv.style.top = parseInt(objDiv.style.top) + 5 + "px";
                var chk = document.getElementById(chk.id);
                var vals = chk.value.split(",");
                chk.value = vals[0] + "," + parseInt(objDiv.style.top) + "," + vals[2] + "," + vals[3];
            }
        }
    }

    module.ToggleMenu = function (obj, imgObj) {
        if (document.getElementById) {
            var el = document.getElementById(obj);
            var io = document.getElementById(imgObj);
            var ar = document.getElementById("masterdiv").getElementsByTagName("ul");
            var ios = document.getElementById("masterdiv").getElementsByTagName("img")
            if (el.style.display != "block") {
                for (var i = 0; i < ar.length; i++) {
                    ar[i].style.display = "none";
                    ios[i].src = "/Content/Images/moreShow01.gif";
                }
                el.style.display = "block";
                io.src = "/Content/Images/moreShow02.gif";
            } else {
                el.style.display = "none";
                io.src = "/Content/Images/moreShow01.gif";
            }
        }
    }

    module.SetFontName = function (name) {
        var propId = document.getElementById("propId").value;
        var o = document.getElementById("font_" + propId);
        var inputO = document.getElementById("input_" + propId).value;
        o.style.fontFamily = name;
        var ss = inputO.split(",");
        var tmpVal = ss[0] + "," + name + "," + ss[2] + "," + ss[3];
        document.getElementById("input_" + propId).value = tmpVal;
    }

    module.SetFontSize = function (size) {
        var propId = document.getElementById("propId").value;
        var o = document.getElementById("font_" + propId);
        var inputO = document.getElementById("input_" + propId).value;

        var sizeUnit = 'pt';

        //获取当前编辑的模板，如果是拼多多模板，则字体大小按px来算
        var template = (window.editWaybillTemplateModule) ? editWaybillTemplateModule.GetTemplate() : null;
        if (template && common.IsPddTemplate(template.TemplateType)) {//template.TemplateType > 20 && template.TemplateType < 30
            sizeUnit = 'px';
        }

        if (size != -1) {
            o.style.fontSize = size + sizeUnit;
        } else {
            o.style.fontSize = "10" + sizeUnit;
        }
        var ss = inputO.split(",");
        var tmpVal = ss[0] + "," + ss[1] + "," + size + "," + ss[3];
        document.getElementById("input_" + propId).value = tmpVal;
    }

    module.SetFontBold = function (flag) {
        var propId = document.getElementById("propId").value;
        var o = document.getElementById("font_" + propId);
        var inputO = document.getElementById("input_" + propId).value;
        var sb = "normal";
        if (flag == "true") {
            sb = "bold";
        }
        o.style.fontWeight = sb;
        var ss = inputO.split(",");
        var tmpVal = ss[0] + "," + ss[1] + "," + ss[2] + "," + flag;
        document.getElementById("input_" + propId).value = tmpVal;
    }

    module.SetId = function (id) {
        var o1 = document.getElementById("input_" + id).value;
        var o = document.getElementById("font_" + id);
        document.getElementById("propName").innerHTML = "<b>" + o.innerHTML + "</b>";
        document.getElementById("propId").value = id;
        var sss = o1.split(",");
        document.getElementById("fontName").value = sss[1];
        document.getElementById("fontsize").value = sss[2];
        document.getElementById("fontbold").value = sss[3];
    }

    module.SetContent = function (flog) {
        //JShow3("art_print_dialog", "设置备注内容", document.getElementById('setRemarks' + flog));
        layer.open({
            type: 1,
            title: '设置备注内容',
            content: $('#setRemarks' + flog),
            area: ['460px', '280px'],
            btn: ["确定"],
            yes: function () {
                $("#font_" + flog).text($("#txtSetRemark" + flog).val());
                layer.closeAll();
            }

        });
    }

    module.ShowRemarks = function (obj) {
        $("#font_" + obj).text($("#txtSetRemark" + obj).val());
        layer.closeAll();
    }

    $(".controlOffset-direction>span").each(function () {
        $(this).on("click", function (e) {
            e = e || event;
            e.preventDefault;
            var offsetTop = $("#template_offsety").val();
            var offsetLeft = $("#template_offsetx").val();
            if ($(this).hasClass("controlOffset-direction-up")) {
                offsetTop--;
            }
            if ($(this).hasClass("controlOffset-direction-down")) {
                offsetTop++;
            }
            if ($(this).hasClass("controlOffset-direction-right")) {
                offsetLeft++;
            }
            if ($(this).hasClass("controlOffset-direction-left")) {
                offsetLeft--;
            }

            var t_l = common.MmToPx(offsetLeft, offsetTop);


            $("#template_offsety").val(offsetTop);
            $("#template_offsetx").val(offsetLeft);
            $("#pb").css({ top: t_l.height, left: t_l.width });
        })
    })

    return module;
}(editTemplateModule || {}, dragModule, commonModule, jQuery, layer))