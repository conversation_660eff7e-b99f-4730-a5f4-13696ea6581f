var applyToListArray = [];          //存储选中的tabel标题字段
var cloosePrintNavArray = [];    //发货内容打印界面设置--选中打印标题
var province = ['北京市', '天津市', '上海市', '重庆市', '河北省', '山西省', '辽宁省', '吉林省', '黑龙江省', '江苏省', '浙江省', '安徽省', '福建省', '江西省', '山东省', '河南省', '湖北省', '湖南省', '广东省', '海南省', '四川省', '贵州省', '云南省', '陕西省', '甘肃省', '青海省', '台湾省']
var productDisplayCountSet = {};
$(function () {

    $(".cheshi").children("td").css({ borderBottom: "none" })

    $(".order-row:odd").addClass("oddTrColor")
    var $chooseOrderState = $("#orderList_orderState_choose>span");
    var $orderSort = $("#orderList_tableSetSort>li");

    var $checkBoxs = $(".changeTrColor>td input[type='checkbox']"); //table 表格行点击多选框 整个�着色选中
    for (var i = 0; i < $checkBoxs.length; i++) {
        $($checkBoxs[i]).on("change", function (e) {
            e.stopPropagation();
            var $allOrderList = $(this).parent().parent("tr");
            //console.log($allOrderList);

            if (!$(this).prop("checked")) {
                $allOrderList.addClass("onClickColor")
                $(this).prop("checked", true);
                //console.log($checkBox.is(':checked'));//获取checkBox的�

            } else {
                $allOrderList.removeClass("onClickColor")
                $(this).prop("checked", false);
            }
        })
    }

    $("#allTableCheckbox").on("change", function () {   //table 表格行点击多选框选择全部或取消全�着�
        var $allTableTr = $(".changeTrColor");
        if ($(this).prop("checked")) {
            $allTableTr.addClass("onClickColor");
            $allTableTr.children().children("input[type='checkbox']").prop("checked", true);
            $(this).prop("checked", true);
        } else {
            $allTableTr.removeClass("onClickColor");
            $allTableTr.children().children("input[type='checkbox']").prop("checked", false);
            $(this).prop("checked", false);
        }
    });

    $(".changeTrColor input[type='checkbox']").on("change", function () {
        //console.log($("input[type='checkbox']").is(':checked'))
    })

    var tableListNames = [
        { id: 0, textName: '打印', odrderBy: 0 },
        { id: 1, textName: '买家旺旺', odrderBy: 1 },
        { id: 2, textName: '日期', odrderBy: 2 },
        { id: 3, textName: '订单', odrderBy: 3 },
        { id: 4, textName: '款/件', odrderBy: 4 },
        { id: 5, textName: '金额/运费', odrderBy: 5 },
        { id: 6, textName: '收件信息', odrderBy: 6 },
        { id: 7, textName: '快递单号', odrderBy: 7 },
        { id: 8, textName: '发件人', odrderBy: 8 },
        { id: 9, textName: '产品详情', odrderBy: 9 },
        { id: 10, textName: '总重量(g)', odrderBy: 10 },
        { id: 11, textName: '付款时间', odrderBy: 11 },
        { id: 12, textName: '下单时间', odrderBy: 12 },
        { id: 13, textName: '订单编号', odrderBy: 13 },
        { id: 14, textName: '操作', odrderBy: 14 }
    ]

    var allTableTitle = orderTableBuilder.columns

    allTableTilesShow(orderTableBuilder.columns); //表格所有标题渲染到设置排列顺序的alaig弹框�

    var $allTableTilesCheckbox = $("#wrapper_main_down_title input[type='checkbox']");
    // var applyToListArray=[];
    for (var i = 0; i < $allTableTilesCheckbox.length; i++) {
        $($allTableTilesCheckbox[i]).on("change", function () {
            var data_id = $(this).attr("data-id");
            if ($(this).prop("checked")) {
                var listObject = {};
                listObject.name = $(this).val();
                listObject.order = $(this).data("order");
                listObject.id = $(this).data("id");
                applyToListArray.splice(applyToListArray.length - 1, 0, listObject);
                //applyToListArray.push(listObject);
                chooseTableTilesShow(applyToListArray)
                //勾选产品详情，图片位置为不显示时，默认选择行中显示
                var picShowStyle = $(".wrapper_main_down_footer input[name='ProductPicShowStyle']:checked").val();
                if (data_id == 10 && picShowStyle=="NoShow") {
                    $(".wrapper_main_down_footer input[name='ProductPicShowStyle'][value='ShowInRow']").prop("checked", true);
                }
            } else {
                var textName = $(this).val()
                for (var i = 0; i < applyToListArray.length; i++) {
                    if (applyToListArray[i].name == textName) {
                        applyToListArray.splice(i, 1);
                    }
                }
                chooseTableTilesShow(applyToListArray)
                //不勾选产品详情，图片位置为不显示
                if (data_id == 10) {
                    $(".wrapper_main_down_footer input[name='ProductPicShowStyle'][value='NoShow']").prop("checked", true);
                }
            }
        })
    }


    $("#rowDownSelectCount").on("change", function () {    //控制行下方，商品图片显示数量。
        var re = /^\+?[1-9][0-9]*$/;
        if (re.test($(this).val()) === false) {
            layer.msg("请输入非零的正整数");
            return;
        }
        rowDownSelectCount = $(this).val() - 0;

    })

    var printNavTitles = [      //发货内容打标
        { id: 0, textName: '序号', orderBy: 0 },
        { id: 1, textName: '简称标题', orderBy: 1 },
        { id: 2, textName: '商品编码', orderBy: 2 },
        { id: 3, textName: '规格颜色', orderBy: 3 },
        { id: 4, textName: '规格尺码', orderBy: 4 },
        { id: 5, textName: '单价', orderBy: 5 },
        { id: 6, textName: '数量', orderBy: 6 }
    ];

    allShipmentsContentTitles(printNavTitles) //渲染打印商品内容标题字段
    // cloosePrintNavArray - 选中打印标题数组
    var $allShipmentsCheckbox = $("#shipmentsContentSet_titles input[type='checkbox']");
    for (var i = 0; i < $allShipmentsCheckbox.length; i++) {
        $($allShipmentsCheckbox[i]).on("change", function () {
            if ($(this).prop("checked")) {
                var listObject = {};
                listObject.textName = $(this).val();
                listObject.orderBy = $(this).data("id");
                listObject.id = $(this).data("order");
                cloosePrintNavArray.push(listObject);
                shipmentsPrintContentSet(cloosePrintNavArray);
            } else {
                var textName = $(this).val()
                for (var i = 0; i < cloosePrintNavArray.length; i++) {
                    if (cloosePrintNavArray[i].textName == textName) {
                        cloosePrintNavArray.splice(i, 1);
                        shipmentsPrintContentSet(cloosePrintNavArray);
                    }
                }
            }
        })
    }

    var $showMoreSelectIcon = $(".showMoreSelectIcon"); //三角
    for (var i = 0; i < $showMoreSelectIcon.length; i++) {
        $($showMoreSelectIcon[i]).on("mouseover", function () {
            $(this).children().show();
        })
        $($showMoreSelectIcon[i]).on("mouseout", function () {
            $(this).children().hide();
        })
    }
    //provinceShow(province);//渲染所有省份
    //highclassProvince(province);//高级查询所有省份

    //var $footerOperationsSpan = $("#footerMoreOperateList>span");
    //changeNavBackgroundColor($footerOperationsSpan); //footer--更多设置--着行变�
    $("#footerMoreOperateList>span").on("mouseover", function () {
        $(this).css({ backgroundColor: "#1295c1", color: '#fff' })
    }).on("mouseout", function () {
        $(this).css({ backgroundColor: "#fff", color: '#888' })
    })
    $("#footerMoreOperations").on("mouseover", function () {   //footer--更多设置--显示与隐�
        $("#footerMoreOperateList").show();
    }).on("mouseout", function () {
        $("#footerMoreOperateList").hide();
    })

    //setExpressTemplate()//设置快递模板

    EndServiceFun();//界面加载1秒后执行，店铺是否过期
    //IsExistLogisticsAddressFun();//界面加载1秒后执行，淘宝用户的地址库
})

//function getExpireTimeFromApi(needReload) {
//    commonModule.Ajax({
//        type: 'post',
//        url: '/Order/GetExpireTimeFromApi',
//        data: {},
//        success: function (rsp) {
//            if (rsp.Success) {
//                $("#curshop_expiretime").text(rsp.Data);
//                //EndServiceFun();
//                if (needReload) {
//                    layer.msg("已更新过期时间，正在刷新页面");
//                    setTimeout(function () {
//                        window.location.href = window.location.href; 
//                    }, 1500);
//                }
//            }
//        }
//    });
//}

function EndServiceFun() {
    var isNoShow = $.cookie('no-show-pay-dialog-tips');
    if (isNoShow == "1")
        return;
    //setTimeout(function () {
    
    commonModule.ajax({
        type: 'post',
        url: '/Order/EndService',
        data: {},
        success: function (json) {
            if (json.Success) {
                var dates = json.Data;
                if (dates.IsShow) {
                    var listData = dates.ListData;
                    if (listData.length == 1) {
                        var curData = listData[0];
                        var payUrl = curData.GetAppPayUrl;
                        if (payUrl.indexOf("orderApplications.html") != -1) {
                            payUrl += "&redirectUrl=" + encodeURIComponent(window.location.href);
                        }
                        var str = "<font style='font-size: 18px;'>店铺【" + curData.ShopName + "】的应用服务仅剩<span style='color:red;font-size:25px;font-weight:bold;'>" + curData.EndServiceDateNum + "</span>天就到期了，为不影响您的使用，请尽快续费。</font><a id='a_open_apppayurl' target='_blank' " + 'href="' + curData.GetAppPayUrl + '" style="display:none;">  点击 </a>';
                        if (curData.EndServiceDateNum <= 0) {
                            str = "<font style='font-size: 18px;'>店铺【" + curData.ShopName + "】的应用服务<span style='color:red;font-size:25px;font-weight:bold;'>到期了</span>，为不影响您的使用，请尽快续费。</font><a id='a_open_apppayurl' target='_blank' " + 'href="' + curData.GetAppPayUrl + '" style="display:none;">  点击 </a>';
                        }
                        //layer.alert(str);
                        //var link = listData[0].GetAppPayUrl;
                        layer.open({
                            title: "服务即将到期",
                            offset: "100px",
                            area: ["500px", "200px"],
                            shadeClose: false,
                            icon: 8,
                            content: str,
                            btn: ['现在续费', '今天不再提示', '更新过期时间'],
                            yes: function () {
                                window.location.href = payUrl;
                                //document.getElementById("a_open_apppayurl").click();
                            },
                            btn2: function () {
                                $.cookie('no-show-pay-dialog-tips', '1', { expires: 1 });
                                return true;
                            },
                            btn3: function () {
                                getExpireTimeFromApi(true);
                                return true;
                            },
                            cancel: function () {
                                $.cookie('no-show-pay-dialog-tips', '1', { expires: 1 });
                                return true;
                            },
                        });

                    } else {
                        var str = "";
                        for (var i = 0; i < listData.length; i++) {
                            var curData = listData[i];
                            var payUrl = curData.GetAppPayUrl;
                            if (payUrl.indexOf("orderApplications.html") != -1) {
                                payUrl += "&redirectUrl=" + encodeURIComponent(window.location.href);
                            }
                            if (curData.EndServiceDateNum <= 0) {
                                str += "<div><font style='font-size: 18px;'>店铺【" + curData.ShopName + "】的应用服务<span style='color:red;font-size:25px;font-weight:bold;'>到期了</span>，为不影响您的使用，请尽快续费：</font>"
                                    + "<a style='color:blue;' id='a_open_apppayurl' target='_blank' " + 'href="' + payUrl + '">  立即续费 </a></div > <br/>';
                            }
                            else {
                                str += "<div><font style='font-size: 18px;'>店铺【" + curData.ShopName + "】的应用服务仅剩<span style='color:red;font-size:25px;font-weight:bold;'>" + curData.EndServiceDateNum + "</span>天就到期了，为不影响您的使用，请尽快续费：</font>"
                                    + "<a style='color:blue;' id='a_open_apppayurl' target='_blank' " + 'href="' + payUrl + '">  立即续费 </a></div><br/>';
                            }
                        }
                        var temp = layer.open({
                            title: "服务即将到期",
                            offset: "100px",
                            area: ["500px", "600px"],
                            content: str,
                            btn: ['我知道了', '今天不再提示', '更新过期时间'],
                            yes: function () {
                                layer.close(temp);
                                return true;
                            },
                            btn2: function () {
                                $.cookie('no-show-pay-dialog-tips', '1', { expires: 1 });
                                return true;
                            },
                            btn3: function () {
                                getExpireTimeFromApi(true);
                                return true;
                            },
                            cancel: function () {
                                $.cookie('no-show-pay-dialog-tips', '1', { expires: 1 });
                                return true;
                            }
                        });
                    }

                }
            }
        }
    });
    //}, 3000);
}

function IsExistLogisticsAddressFun(callback) {
    //setTimeout(function () {
    commonModule.ajax({
        type: 'post',
        url: '/Order/IsExistLogisticsAddress',
        data: {},
        success: function (json) {
            if (json.Success) {
                var dates = json.Data;
                if (dates.IsShow) {
                    var list = dates.ListModel;
                    var str = "";
                    if (list.length > 1) {
                        for (var i = 0; i < list.length; i++) {
                            str += list[i].ShopName + " ";
                        }
                        $("#isMoveLogisticsaddressShopName").html(str);
                        $("#isMoveNotLogisticsaddressId").show();
                    } else {
                        str = "店铺（" + list[0].ShopName + "）";
                        $("#isOneLogisticsaddressId").show();
                        $("#isOneLogisticsaddressId").html(str);
                    }

                    var addLogisticsAddress = layer.open({
                        type: 1,
                        title: "添加地址库",
                        content: $('#add-logisticsaddress-div-show'),
                        area: ['1200', '300'], //宽高
                        btn: ['保存', '取消'],
                        cancel: function () {
                            //$('#add-defaultseller-div-show').hide();
                            callback(false);
                            layer.close(addLogisticsAddress);
                        },
                        yes: function () {
                            AddLogisticsAddress(addLogisticsAddress);
                            callback(true);
                        },
                        btn2: function () {
                            callback(false);
                            layer.close(addLogisticsAddress);
                            //$('#add-defaultseller-div-show').hide();
                        }
                    });
                }
            }
        }
    });
    //}, 3000);
}

function AddLogisticsAddress(addLogisticsAddress) {

    var contactName = $('#txtLogisticsaddressContactName').val().trim();
    var province = $('#txtLogisticsaddressProvince').val().trim();
    var city = $('#txtLogisticsaddressCity').val().trim();
    var country = $('#txtLogisticsaddressCountry').val().trim();
    var address = $('#txtLogisticsaddressAddress').val().trim();
    var zipcode = $('#txtLogisticsaddressZipcode').val().trim();
    var phone = $('#txtLogisticsaddressPhone').val().trim();
    var mobile = $('#txtLogisticsaddressMobile').val().trim();
    var company = $('#txtLogisticsaddressCompany').val().trim();
    var memo = $('#txtLogisticsaddressMemo').val().trim();
    var fahuoCheck = $("#checkedLogisticsaddressFa").prop("checked");
    var tuihuoCheck = $("#checkedLogisticsaddressTui").prop("checked");


    if (contactName == '') {
        layer.msg('请填写联系人.');
        $('#txtLogisticsaddressContactName').focus();
        return;
    }


    if (province == '') {
        layer.msg('请填写省份.');
        $('#txtLogisticsaddressProvince').focus();
        return;
    }

    if (city == '') {
        layer.msg('请填写 市.');
        $('#txtLogisticsaddressCity').focus();
        return;
    }
    if (country == '') {
        layer.msg('请填写 区.');
        $('#txtLogisticsaddressCountry').focus();
        return;
    }
    if (address == '') {
        layer.msg('请填写 街道地址.');
        $('#txtLogisticsaddressAddress').focus();
        return;
    }
    if (zipcode == '') {
        layer.msg('请填写 邮编.');
        $('#txtLogisticsaddressZipcode').focus();
        return;
    }

    if (phone == '' && mobile == '') {
        layer.msg('座机和手机，必填一个！');
        return;
    }


    var dates = {
        ContactName: contactName,
        Province: province,
        City: city,
        Country: country,
        Address: address,
        Zipcode: zipcode,
        Phone: phone,
        Mobile: mobile,
        Company: company,
        Memo: memo,
        FahuoCheck: fahuoCheck,
        TuihuoCheck: tuihuoCheck,
    }

    commonModule.Ajax({
        url: '/Order/SaveLogisticsAddress',
        loading: true,
        data: dates,
        type: 'POST',
        success: function (rsp) {
            var dates = rsp.Data;
            if (dates.IsShow) {
                var listData = dates.ListModel;

                var str = "<span>保存失败：</span><br/>";
                for (var i = 0; i < listData.length; i++) {
                    str += "<font style='font-size: 10px;'> 店铺(" + listData[i].ShopName + ")  ：<span style='color:red'>" + listData[i].ErrMsg + " </span></font>";
                    str += "<br/>";
                }
                layer.alert(str);

            } else {
                layer.msg('保存成功');
                layer.close(addLogisticsAddress);
            }
        }
    });
}



function changeNavBackgroundColor(spans) {       // 着行变
    for (var i = 0; i < spans.length; i++) {
        $(spans[i]).on("mouseover", function () {
            $(this).css({ backgroundColor: "#1cb3fc", color: '#fff' })
        })
        $(spans[i]).on("mouseout", function () {
            $(this).css({ backgroundColor: "#fff", color: '#888' })
        })
    }
}

function zk_sq(e) {
    if (e.stopPropagation) {
        e.stopPropagation();
        e.preventDefault();
    } else {
        window.event.returnValue = false;
        window.event.cancelBubble = true;
    };

    var rowIndex = $(this).attr('data-index');
    var row = orderTableBuilder.rows[rowIndex];
    if ($(this).hasClass('zk')) {
        //解码
        if (row.PlatformType == "Taobao" || row.PlatformType == "Jingdong") {
            orderTableBuilder.ViewEncryptInfo(rowIndex);
        }

        $(this).html("收起").removeClass("zk").parent().parent().parent('tr').addClass("showMoreColor").children().parent().next().removeClass("addMoreShowHide").css({ marginBottom: "10px" }).children().css({ borderTop: "none" });
        $(this).parent().parent().siblings(".PrintState");
        $(this).parent().parent().parent().children().css({ borderLelft: "1px solid #e2e2e2" });
        $(this).parent().parent().siblings(".Product").children().children(".productContent_moreTitle").html("收起").removeClass("zk");
        if ($('input[value="ShowDownRow"]').prop("checked")) {
            $(this).parent().parent().parent().next().next().hide();
        }
        row.zk = true; //展开状态
    } else {
        $(this).html("展开").addClass("zk").parent().parent().css({ borderRight: "none" }).parent('tr').removeClass("showMoreColor").children().removeClass("addBorderBottom").parent().removeClass('addMoreShow').next().addClass("addMoreShowHide").css({ marginBottom: "0" });
        $(this).parent().parent().siblings(".PrintState").css({ borderLeft: "none" });
        $(this).parent().parent().siblings(".Detail").css({ borderRight: "none" });
        $(this).parent().parent().siblings(".Product").children().children(".productContent_moreTitle").html("更多").addClass("zk");
        if ($('input[value="ShowDownRow"]').prop("checked")) {
            $(this).parent().parent().parent().next().next().show();
        }

        if ($(this).parent().parent().parent().siblings(".listPicTr").hasClass("zk")) {
            $(this).parent().parent().parent().siblings(".order-row").children().removeClass("addBorderBottom")
        }
        row.zk = false; //展开状态
    }

    layer.closeAll(); //关闭所有的layer层    

}

function product_zk_sq(e) {
    e.stopPropagation();
    if ($(this).hasClass('zk')) {
        $(this).html("收起").removeClass("zk").parent().parent().parent('tr').addClass("showMoreColor").children().parent().next().removeClass("addMoreShowHide").css({ marginBottom: "10px" }).children().css({ borderTop: "none" });;
        $(this).parent().parent().siblings(".PrintState");
        $(this).parent().parent().siblings(".Detail").children().children(".table_content_spread").html("收起").removeClass("zk");
        $(this).parent().parent().parent().children().css({ borderLelft: "1px solid #e2e2e2" });
        if ($('input[value="ShowDownRow"]').prop("checked")) {
            $(this).parent().parent().parent().next().next().hide();
        }
    } else {
        $(this).html("更多").addClass("zk").parent().parent().parent('tr').removeClass("showMoreColor").children().parent().removeClass('addMoreShow').next().addClass("addMoreShowHide").css({ marginBottom: "0" });
        $(this).parent().parent().siblings(".Detail").children().children(".table_content_spread").html("展开").addClass("zk");;
        $(this).parent().parent().siblings(".PrintState");
        if ($('input[value="ShowDownRow"]').prop("checked")) {
            $(this).parent().parent().parent().next().next().show();
        }
    }
    layer.closeAll(); //关闭所有的layer层    

}

function InitOrderTableEvent() {
    $(".order-row:odd").addClass("oddTrColor");
    $(".order-row ").children("td").addClass("tdNobottom");

    var productGoodsBox = $(".table_content_tbody_tdProducts");   //控制表格行内产品图片展示样式位置：4个产品排列顺序不会错乱
    productGoodsBox.each(function () {
        var $this = $(this);
        if (!$this.hasClass("tdHide")) {
            var child = $this.children().children()[0];
            var childNext = $(child).next();
            var childrenFirst = $(child).height();
            var childrenSecond = childNext.height();

            if (!childNext.hasClass("zk")) {
                if (childrenFirst > childrenSecond) {
                    childNext.height(childrenFirst);
                } else {
                    $(child).height(childrenSecond)
                }
            }

        }
    })

    $("select").on("change", function () {     //控制搜索条件：下拉框，有选择时，出现选择框
        var $this = $(this);
        var val = $this.val();
        if (val != "" && val != "PayTime") {
            //console.log($(this).val())
            $this.css({ border: "1px solid rgb(138, 172, 227)", boxShadow: "0px 0px 0px 1px rgb(165, 199, 254) " })
        } else {
            $this.css({ border: "1px solid #c7c7c7", boxShadow: "none" })
        }
    })

    $(".table_content_dagou_moreFun").each(function () { //控制‘可合并订单’方框出现时，收件地址下面便签（乡镇、微供、部分发货）不会被方框遮挡
        var $this = $(this);
        if ($this.find(".table_content_dagou_merge").length > 0) {
            $this.parent().parent().nextAll(".ReceiveAddress").children(".address_type").css({ marginBottom: "13px" })
            //console.log("aabbcc")
        }
    })

    //$("#wrapper_main_down_title input[value='收件地址']").attr("disabled", "false");
    //$("#wrapper_main_down_title input[value='买家旺旺']").attr("disabled", "false");
    $("#wrapper_main_down_title input[value='快递单号']").attr("disabled", "false");
    $("#wrapper_main_down_title input[value='勾选框']").attr("disabled", "false");

    $(".stopHref").click(function (event) {
        event.stopPropagation();
    });

    var timerTableContentImgs = null;
    $(".table_content_img,.orderMoreShow").hover(function () {
        var $this = $(this);
        var bigImg = "";
        var imgSrc = $(this).find('img').attr('src');
        if (imgSrc.substr(imgSrc.length - 10, 10) == ".80x80.jpg") {
            bigImg = imgSrc.substr(0, imgSrc.length - 9) + "jpg";

        } else {
            bigImg = imgSrc;
        }
        $(this).find('img').attr('src', bigImg);


        timerTableContentImgs = setTimeout(function () {
            $this.next().next().show();
        }, 800)
    }, function () {
        $(this).next().next().hide();
        clearTimeout(timerTableContentImgs); //清除将要在800毫秒后执行的弹出框动作
    });
    $(".product_messages_moreShow_img").hover(function () {
        var $this = $(this);
        $this.children(".table_content_tbody_productsBigPic").show();

    }, function () {
        $(this).children(".table_content_tbody_productsBigPic").hide();
    })

    $('.orderList_search input[type="text"]').each(function () {  //订单编号或运单号有值时，其它输入框禁用
        $(this).on("blur", function () {
            var val = $(this).val().replace(/(^\s+)|(\s+$)/g, "");
            var name = $(this).attr("name");
            if (val != "") {
                if (name == "LastWaybillCode" || name == "PlatformOrderId") {
                    //$('.orderList_search input[type="text"]').addClass("noneSelectInput");
                    //$(this).addClass("selectInput").removeClass("noneSelectInput");
                    $(".search_one_labelOne").children("input").attr("disabled", "disabled").css({ border: "1px solid #e2e2e2", boxShadow: "none", borderRadius: "none" });
                    $(".orderList_search_one>label").children("select").attr("disabled", "disabled").css({ border: "1px solid #e2e2e2", boxShadow: "none", borderRadius: "none" });
                    $(".selectBox .select2-choice").css({ border: "1px solid #e2e2e2", boxShadow: "none", borderRadius: "none" });
                    $(this).removeAttr("disabled");
                    $("#ProductSubjectCheck.selectWrap").addClass("selectWrapStopSelect").children().removeClass("active");
                    $("#ProductAttrCheck.selectWrap").addClass("selectWrapStopSelect").children().removeClass("active");
                    //$(".noneSelectInput").parent().attr("onclick", " noneSelectInputMsg()")
                    orderPrintModule.IsQueryAlone = true;
                }
                $(this).css({ border: "1px solid #8aace3", boxShadow: "0 0 0 1px #a5c7fe", borderRadius: "1px" });
              

            } else {
                if (name == "LastWaybillCode" || name == "PlatformOrderId") {
                    
                    $(".search_one_labelOne").children("input").each(function () {
                        if ($(this).val() != "") {
                            $(this).css({ border: "1px solid #8aace3", boxShadow: "0 0 0 1px #a5c7fe", borderRadius: "1px" });
                        }
                    });

                    $(".search_one_labelOne").children("select").each(function () {
                        if ($(this).val()) {
                            $(this).css({ border: "1px solid #8aace3", boxShadow: "0 0 0 1px #a5c7fe", borderRadius: "1px" });
                        }
                    });

                    $(".selectWrap").removeClass("selectWrapStopSelect");
                    if ($("#ProductSubjectCheck .selectMore").hasClass("selectHaveData")) {
                        $("#ProductSubjectCheck .selectMore").addClass("active");
                    }
                    if ($("#ProductAttrCheck .selectMore").hasClass("selectHaveData")) {
                        $("#ProductAttrCheck .selectMore").addClass("active");
                    }
                    orderPrintModule.IsQueryAlone = false;
                }
                //$('.orderList_search input[type="text"]').removeClass("noneSelectInput");
                $(this).css({ border: "1px solid #e2e2e2", boxShadow: "none", borderRadius: "none" });
                if ($(this).attr("name") == "LastWaybillCode" || $(this).attr("name") == "PlatformOrderId") {
                    $(".search_one_labelOne").children("input").removeAttr("disabled");
                    $(".orderList_search_one>label").children("select").removeAttr("disabled");
                }
                //$(".noneSelectInput").parent().removeClass("onclick")

            }

            //拼多多待发货才显示剩余时间查询
            if (commonModule.CurrShop.PlatformType == "Pinduoduo") {
                var status = $("#orderList_orderState_choose .active").attr("data-status");

                if (status == "waitsellersend" || $("#orderList_orderState_choose span.active").length == 0) {

                    if (name == "LastWaybillCode" || name == "PlatformOrderId") {   
                        if (val == "")
                            $("#SeachConditions .LastShipTime").removeAttr("disabled");
                        else
                            $("#SeachConditions .LastShipTime").attr("disabled", "disabled");
                    }
                    else {
                        $("#SeachConditions .LastShipTime").removeAttr("disabled");
                    }
                }                                                                
                else
                    $("#SeachConditions .LastShipTime").attr("disabled", "disabled");
            }

        })
    })



    $("#sureAgainPrintShow").unbind('click').bind('click', function () {
        if ($(this).hasClass("zk")) {
            $(this).removeClass("zk");
            $(".sureAgainPrint_main_down").show();
        } else {
            $(this).addClass("zk");
            $(".sureAgainPrint_main_down").hide();
        }
    })
    $(".sureAgainPrint_main_down_ul li").each(function () {
        $(this).on("click", function () {
            $(".sureAgainPrint_main_down_ul li").removeClass("active");
            $(this).addClass("active");
        })
    })


    //var outTableWidth = $(".orderList_table_content").width() + 60;
    //$(".header").css({ minWidth: outTableWidth });
    if ($('input[value="ShowDownRow"]').prop("checked")) {
        $(".listPicTr").prev().prev().children("td").addClass("radio")
    } else if ($('input[value="ShowInRow"]').prop("checked")) {
        $(".listPicTr").prev().prev().children("td").removeClass("radio")

    } else if ($('input[value="NoShow"]').prop("checked")) {
        $(".listPicTr").prev().prev().children("td").removeClass("radio")
    }

    var $moreOperationsSpan = $(".moreOperations_aialog>span");
    changeNavBackgroundColor($moreOperationsSpan);   //表格设置-更多选项--着行变色

    orderPrintModule.InitOrderClassifyEvent();

    $(".closeDefaultClassify").each(function () {
        $(this).on("click", function () {
            //console.log($(this).parent())
            $(this).parent().hide();
        })
    })

    $("#particularsIcon").on("click", function () {  //所有行更多展开或关闭
        if ($("#particularsIcon").hasClass("allZK")) {
            $(".allShowAndHide").removeClass("addMoreShowHide").children().css({ borderTop: "none" });
            $(this).removeClass("allZK").css({ backgroundPosition: "-164px -20px" });
            $(".order-row").addClass('showMoreColor');
            $(".table_content_spread").removeClass("zk").html("收起");
            if ($('input[value="ShowDownRow"]').prop("checked")) {
                $(".listPicTr").hide();
            }

            ////批量解码
            //if (row.PlatformType == "Taobao" || row.PlatformType == "Jingdong") {
            //    //orderTableBuilder.ViewEncryptInfo(rowIndex);
            //}

        } else {
            $(".allShowAndHide").addClass("addMoreShowHide").children().css({ borderTop: "1px solid #e2e2e2" });
            $(this).addClass("allZK").css({ backgroundPosition: "-154px -20px" });
            $(".order-row").removeClass('showMoreColor')
            $(".table_content_spread").addClass("zk").html("展开");
            $(".orderList_table_content tbody tr td").removeClass("addBorderBottom")
            if ($('input[value="ShowDownRow"]').prop("checked")) {
                $(".listPicTr").show();
            }

        }
    })


    $(".order-row>.Remark").each(function () {   //移上留言备注td 出现修改备注图标
        $(this).on("mouseover", function () {
            $(this).find(".setRemark_icon:last").css({ display: "inline-block" });
        })
        $(this).on("mouseout", function () {
            $(this).find(".setRemark_icon:last").css({ display: "none" });

        })

    })

    echo.init({
        offset: 300,
        throttle: 100,
        unload: false,
        callback: function (element, op) {
            console.log('loaded ok.');
        }
    });
    echo.render();

}

function moreProductShow(elmt) {
    $(elmt).parent().parent().parent().css({ display: "none" }).prev().removeClass("addMoreShowHide").prev().children(".table_content_tbody_tdMore").children("div").children(".table_content_spread").removeClass("zk").html("收起");
    $(elmt).parent().parent().parent().prev().prev().addClass("showMoreColor").children(".table_content_tbody_tdMore").css({ borderRight: "1px solid #e2e2e2" }).siblings(".PrintState").css({ borderLeft: "1px solid #ddd" })
    $(elmt).parent().parent().parent().prev().children().css({ borderTop: "none" });
}

function allTableTilesShow(navs) {    //表格所有标题渲染到设置排列顺序的弹框里
    var str = "";
    for (var i = 0; i < navs.length; i++) {
        //str += '<li><input type="checkbox" ' + (navs[i].display ? 'checked="checked"' : '') + ' data-order="' + navs[i].order + '" data-id="' + navs[i].id + '" value="' + navs[i].name + '"><span style="margin-left: 5px">' + navs[i].name + '</span></li>';
        var noMove = navs[i] && (navs[i].field == "PrintState" || navs[i] && navs[i].field == "Detail") ? true : false;
        if (!noMove) {
            navs[i] && (str += '<li><label><input type="checkbox" data-order="' + navs[i].order + '" data-id="' + navs[i].id + '" value="' + navs[i].name + '"><span style="margin-left: 5px">' + navs[i].name + '</span></label></li>');
        }
        //navs[i] && (str += '<li ' + (noMove ? "class=\"column-nomove\"" : "") + '><input type="checkbox" ' + (noMove ? 'disabled="disabled"' : '') + ' data-order="' + navs[i].order + '" data-id="' + navs[i].id + '" value="' + navs[i].name + '"><span style="margin-left: 5px">' + navs[i].name + '</span></li>');
    }
    $("#wrapper_main_down_title").html(str);
}

function chooseTableTilesShow(navs) {  //选中的标渲染到设置排列顺序弹框里
    var str = "";
    navs = navs.sort(function (a, b) { return a.order - b.order });
    for (var i = 0; i < navs.length; i++) {
        str += '<li><span data-id=' + (navs[i] && navs[i].id) + '>' + (navs[i] && navs[i].name) + '</span><span><i class="icon_left" onclick=\'afterList("' + (navs[i] && navs[i].id) + '","' + false + '","1")\' ></i><i onclick=\'afterList("' + (navs[i] && navs[i].id) + '","' + true + '","1")\' class="icon_right"></i></span></li>'
    }
    $("#wrapper_main_up_title").html(str)
}

function afterList(id, isRight) {   //字段互换位置
    var currentItem = "";
    var $newArray = [];
    $newArray = applyToListArray;

    //console.log($newArray);
    for (var i = 0; i < $newArray.length; i++) {
        if ($newArray[i].id == id) {
            currentItem = $newArray[i]
        }
    }
    if (isRight == "false") {
        isRight = false
    } else {
        isRight = true
    }

    $newArray = $newArray.sort(function (a, b) {
        return a.order - b.order;
    })
    var index = -1;
    for (var i = 0; i < $newArray.length; i++) {
        if ($newArray[i].id == currentItem.id)
            index = i;
    }


    if (index == 0 && !isRight) {
        return;
    }
    if (index == $newArray.length && isRight) {
        return
    }
    //console.log(index);
    var switcher = $newArray[isRight ? index + 1 : index - 1];
    var orderIndex = switcher.order;
    switcher.order = currentItem.order;
    currentItem.order = orderIndex;
    var leftIndex = -1;
    var rightIndex = -1;
    for (var i = 0; i < $newArray.length; i++) {
        var temp = $newArray[i];
        if (temp.id == switcher.id)
            leftIndex = i;
        else if (temp.id == currentItem.id)
            rightIndex = i;

    }
    $newArray[leftIndex] = currentItem;
    $newArray[rightIndex] = switcher;
    chooseTableTilesShow($newArray);
}



function allShipmentsContentTitles(navs) {
    var str = "";
    for (var i = 0; i < navs.length; i++) {
        str += '<li><input data-order="' + navs[i].orderBy + '" data-id="' + navs[i].id + '" value="' + navs[i].textName + '" type="checkbox">' + navs[i].textName + '</li>'
    }
    $("#shipmentsContentSet_titles").html(str)
}

function shipmentsPrintContentSet(navs) {
    var str = "";
    for (var i = 0; i < navs.length; i++) {
        str += '<li><span>' + navs[i].textName + '</span><span><i class="icon_left" onclick=\'afterList("' + navs[i].id + '","' + false + '","2")\' ></i><i class="icon_right" onclick=\'afterList("' + navs[i].id + '","' + true + '","2")\' ></i></span></li>'
    }
    $("#shipmentsPrintContentSet").html(str)
}

function amendAlias(id) {  // tabel 订单分类--修改别名
    // $('.orderClassify').show()
    layer.open({
        type: 1,
        title: "设置分类别名", //不显示标题
        content: $('.orderClassify_main'),
        area: ['500', '600'], //宽高
        btn: ['保存', '取消'],
        yes: function () {
            updateCategoryAlias();
        },
        cancel: function () {

        }
    });
}

// 修改分类别名
function updateCategoryAlias() {
    var models = [];
    $(".orderClassify_main>ul>li input").each(function () {
        var name = $(this).parent().find("span").eq(0).text();
        var color = $(this).parent().find("span").eq(1).css("color");
        models.push({ "Id": $(this).attr("data-id"), "Alias": $(this).val(), "Name": name, "Color": color });
    });

    var shopIds = $("#selectShops").attr("data-values") || "";
    commonModule.Ajax({
        url: "/Order/UpdateOrderCategoryAlias",
        data: { "Categorys": JSON.stringify(models), "ShopIds": shopIds },
        success: function (result) {
            if (result.Success) {
                updateCategoryAliasToHtml(models);
            }

            layer.closeAll();  //关闭弹出框
        }
    });
}

function handworkMergeOrder(id) {  //手工合并订单
    layer.open({
        type: 1,
        title: "修改备注", //不显示标题
        content: $('.handworkMergeOrder_main'),
        area: ['1000', '600'], //宽高
        btn: ['保存', '取消'],
        yes: function () {

        },
        cancel: function () {

        }
    });
}

function amendRemark() {   //修改备注
    layer.open({
        type: 1,
        title: "修改备注", //不显示标题
        content: $('.amendRemark_main'),
        area: ['500', '600'], //宽高
        btn: ['保存', '取消'],
        yes: function () {

        },
        cancel: function () {

        }
    });

}
function remarksetContent() {
    layer.open({
        type: 1,
        title: "常用备注内容", //不显示标题
        content: $('.amendRemark_setContent'),
        area: ['450', '500'], //宽高
        btn: ['关闭'],
        cancel: function () {

        }
    });
}
function overageWord() {
    layer.open({
        type: 1,
        title: "过虑词", //不显示标题
        content: $('.overageWord'),
        area: ['450', '500'], //宽高
        btn: ['关闭'],
        cancel: function () {

        }
    });
}
function substituteWord() {
    layer.open({
        type: 1,
        title: "替换词", //不显示标题
        content: $('.substituteWord'),
        area: ['500', '500'], //宽高
        btn: ['关闭'],
        cancel: function () {

        }
    });
}
function chooseAllProvince() {
    layer.open({
        type: 1,
        title: "替换词", //不显示标题
        content: $('.wrapper_chooseProvince'),
        area: ['760', '700'], //宽高
        btn: ['关闭'],
        cancel: function () {

        }
    });
}
function chooseProvince() {
    layer.open({
        type: 1,
        title: "替换词", //不显示标题
        content: $('.wrapper_chooseProvince'),
        area: ['760', '700'], //宽高
        btn: ['关闭'],
        cancel: function () {

        }
    });
}


function setAbbreviation() {
    layer.open({
        type: 1,
        title: "设置简称", //不显示标题
        content: $('.setAbbreviation'),
        area: ['760', '700'], //宽高
        btn: ['保存', '关闭'],
        yes: function () { },
        cancel: function () {

        }
    });
}

// 省份自定义设置
function setProvince() {
    layer.open({
        type: 1,
        title: "省份", //不显示标题
        content: $('.wrapper_chooseProvince'),
        area: ['760', '700'], //宽高
        btn: ['保存', '关闭'],
        yes: function () { },
        cancel: function () {

        }
    });
}

function stopM(event) {  //阻止冒泡和默认行为
    if (event) {
        try {            //兼容ie8   
            event.stopPropagation();
            event.preventDefault();
        } catch (err) {
            window.event.returnValue = false;
            window.event.cancelBubble = true;
        }

    } else {
        window.event.returnValue = false;
        window.event.cancelBubble = true;
    };
}

function closeColumnSet() {
    $("#columnSet").hide();
}

function initColumnUpTitle() {
    applyToListArray = [];
    for (var i = 0; i < orderTableBuilder.columns.length; i++) {
        var column = orderTableBuilder.columns[i];
        var $checkbox = $("#wrapper_main_down_title li>label>input[type=checkbox][data-id=" + column.id + "]");
        if ($checkbox.length > 0) {
            $checkbox.prop("checked", column.display);
            if (column.display) {
                var listObject = {};
                listObject.name = column.name;
                listObject.order = column.order;
                listObject.id = column.id;
                listObject.canMove = column.canMove;
                applyToListArray.push(listObject);
            }
        }
    }
    chooseTableTilesShow(applyToListArray);
}

function closeAialogs(idName) {
    $(idName).hide()
}

function openCloseAialogs(idName) {
    $(idName).show();
    // 判断勾选条件是否全部选中
    orderPrintModule.LoadSearchConditionCheckStatus();
    var $checkboxes = $("#addSearchCondition :checkbox:visible");
    var $checkedboxes = $("#addSearchCondition :checkbox:visible:checked"); 
    $(".search_checkall").prop("checked", $checkboxes.length == $checkedboxes.length);
}

function batchAmendSeller() {
    // $('.highclassDemand').show()
    layer.open({
        type: 1,
        title: "修改发件人", //不显示标题
        content: $('.batchAmendSeller'),
        area: ['1100', '700'], //宽高
        btn: ['保存', '关闭'],
        yes: function () {
        },
        cancel: function () {

        }
    });
}

function scanShipments() {
    $("#ScanWaybillNumber").val("");
    $("#openScanSendGoodJSdocumentId").val("1");
    $("#ScanWeightId").val("");
    $("#saomiao_show_order").html("");
    $("#scanNowSendGoodsCK").removeAttr("checked");
    $("#scan_unit_ck_id").removeAttr("checked");
    $("#unit_show_id").hide();
    setTimeout(function () {//隐藏关闭按钮和控制弹框
        $('#ScanWaybillNumber').focus();
        //document.body.pressKey(32, " ");
    }, 20);

    layer.open({
        type: 1,
        title: "扫描发货", //不显示标题
        content: $('.scanShipments'),
        area: ['1200', '700'], //宽高
        btn: ['关闭'],
        yes: function () {
            layer.closeAll();

            var snd_ie_success_f = document.getElementById('snd_ie_success_ScanFahuo');
            snd_ie_success_f.src = "";
            var snd_chrome_success_f = document.getElementById("snd_chrome_success_ScanFahuo");
            snd_chrome_success_f.data = "";
            var snd_ie_fail_f = document.getElementById('snd_ie_fail_ScanFahuo');
            snd_ie_fail_f.src = "";
            var snd_chrome_fail_f = document.getElementById("snd_chrome_fail_ScanFahuo");
            snd_chrome_fail_f.data = "";
            $("#openScanSendGoodJSdocumentId").val("0");
        },
        cancel: function () {
            layer.closeAll();

            var snd_ie_success_f = document.getElementById('snd_ie_success_ScanFahuo');
            snd_ie_success_f.src = "";
            var snd_chrome_success_f = document.getElementById("snd_chrome_success_ScanFahuo");
            snd_chrome_success_f.data = "";
            var snd_ie_fail_f = document.getElementById('snd_ie_fail_ScanFahuo');
            snd_ie_fail_f.src = "";
            var snd_chrome_fail_f = document.getElementById("snd_chrome_fail_ScanFahuo");
            snd_chrome_fail_f.data = "";
            $("#openScanSendGoodJSdocumentId").val("0");
        },
    });
    scanSendGoodJS.GetLoadSet();


}
var audioSoundId = 1;
function playSound(url) {
    audioSoundId++;
    var sys = GetBrowserType();
    if (sys == "Chrome") {
        var id = "sound-" + audioSoundId;
        $("body").append('<audio style="display:none;" id="' + id + '" src="' + url + '" controls="controls" autoplay></audio>');
        $("#" + id)[0].play();
    }
    else {
        var snd_ie_fail = document.getElementById('snd_ie_fail_ScanPrint');
        snd_ie_fail.src = url;
        $("#snd_ie_fail_ScanPrint")[0].play();
    }
}

//成功的声音
function playSuccessSound_ScanFahuo() {
    playSound("/Content/files/finishfile.wav");
}

//失败的声音
function playFailSound_ScanFahuo() {
    playSound("/Content/files/fail.wav");
}

function playSuccessSound_ScanPrint() {
    playSound("/Content/files/finishfile.wav");
}
function playFailSound_ScanPrint() {
    playSound("/Content/files/fail.wav");
    //return;
    //var sys = GetBrowserType();
    //var path = "/Content/files/fail.wav";
    //if (sys == "Chrome") {
    //    var snd_chrome_fail = document.getElementById("snd_chrome_fail_ScanPrint");
    //    snd_chrome_fail.data = path;
    //}
    //else {
    //    var snd_ie_fail = document.getElementById('snd_ie_fail_ScanPrint');
    //    snd_ie_fail.src = path;
    //}
}



//判断是什么浏览器
function GetBrowserType() {
    var userAgent = navigator.userAgent;
    var isOpera = userAgent.indexOf("Opera") > -1;
    var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera;
    var isEdge = userAgent.indexOf("Windows NT 6.1; Trident/7.0;") > -1 && !isIE;
    var isFF = userAgent.indexOf("Firefox") > -1;
    var isSafari = userAgent.indexOf("Safari") > -1 && userAgent.indexOf("Chrome") == -1;
    var isChrome = userAgent.indexOf("Chrome") > -1 && userAgent.indexOf("Safari") > -1;

    if (isIE) {
        var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
        reIE.test(userAgent);
        var fIEVersion = parseFloat(RegExp["$1"]);
        if (fIEVersion == 7) { return "IE7"; }
        else if (fIEVersion == 8) { return "IE8"; }
        else if (fIEVersion == 9) { return "IE9"; }
        else if (fIEVersion == 10) { return "IE10"; }
        else if (fIEVersion == 11) { return "IE11"; }
        else { return "0" }
    }

    if (isFF) { return "FF"; }
    if (isOpera) { return "Opera"; }
    if (isSafari) { return "Safari"; }
    if (isChrome) { return "Chrome"; }
    if (isEdge) { return "Edge"; }
}


function setOrderClassify() {  //设置订单分类
    layer.open({
        type: 1,
        title: "设置订单分类",
        content: $('.setOrderClassify'),
        btn: ['保存', '关闭'],
        area: ['600', '500'], //宽高
        yes: function () {
            var cid = $(".setOrderClassify [name=OrderClassify]:checked").val();
            if (cid === undefined) {
                layer.msg("请先勾选分类");
                return;
            }
            orderPrintModule.UpdateOrderCategoryId("", cid);
        },
        cancel: function () {

        }
    });

}

function setExpressTemplate() {  //设置快递模板

    if ($(".step_style_3>div>ul>li").length > 2) {
        //console.log("wwwccc")
        $("#choose_order_style_left").show();
        $("#choose_order_style_right").show();
    }

    var num = 0;
    $("#choose_order_style_right").on("click", function () {
        var withs = $(this).prev().prev().width();
        //console.log(withs)
        if (num <= -withs + 700) {
            return;
        }
        num -= 710
        $(this).prev().prev().get(0).style.left = num + "px";
    })

    $("#choose_order_style_left").on("click", function () {
        if (num == 0) {
            return
        } else {
            num += 710
            $(this).prev().get(0).style.left = num + "px";
        }
    })

    if ($(".choose_order_style>div>ul>li").length > 2) {
        $("#choose_order_style_left").show();
        $("#choose_order_style_right").show();
    }
}


$(".index_btn1").on("mouseover", function () {
    $(".index_btn1 ul").show();
    $(".index_btn1 i").addClass("show-inverse");

})
$(".index_btn1").on("mouseout", function () {
    $(".index_btn1 ul").hide();
    $(".index_btn1 i").removeClass("show-inverse");
})

$(".index_btn1 ul li").each(function () {
    $(this).on("mouseover", function () {
        $(this).css({ backgroundColor: "#feb07c", color: "#fff" })
    })
    $(this).on("mouseout", function () {
        $(this).css({ backgroundColor: "#fff", color: "#666" })
    })
})


$("#oyhfd").on("change", function () {
    if ($("option:selected", this).val() == 4) {
        $("#oyhshowInfo").show();
    } else {
        $("#ozkOnther").val("");
        $("#oyhshowInfo").hide();
    }
});

function platformOrderShow(isThis) {
    $(isThis).children().css({ display: 'inline-block' });
    if ($(isThis).children(".lastExpressCodeContentShow").children(".moreWaybillCode").length) {
        $(isThis).children(".lastExpressCodeContentShow").children(".moreWaybillCode").css({ display: 'block' }).children(".moreWaybillCode-show").show();
    }
   
}
function platformOrderHide(isThis) {
    $(isThis).find(".copyIcon").css({ display: 'none' });
    if ($(isThis).children(".lastExpressCodeContentShow").children(".moreWaybillCode").length) {
        $(isThis).children(".lastExpressCodeContentShow").children(".moreWaybillCode").children(".moreWaybillCode-show").hide();
    }
}


//新手操作指导----------------------------------------------------------

//$(function () {
//    var platformType = (commonModule.PlatformType || "").toLowerCase();
//    if (platformType == "kuaishou") {
//        setTimeout(function () {
//            //只在订单列表显示
//            if (window.location.pathname != "/" && window.location.pathname != "/Order/Index")
//                return;
//            commonModule.getJSON("/Order/GetUserGuideInfo", function (rsp) {
//                var greenhandObj = rsp.Data;
//                window.greenhandObj = greenhandObj;
//                if (greenhandObj.isShowGreenhand) {
//                    $(".guidance-wrape").show();  //初始化，新手入门
//                }
//            });
//        }, 1000);
//    }
//    else {
//        setTimeout(function () {
//            //只在订单列表显示
//            if (window.location.pathname != "/" && window.location.pathname != "/Order/Index")
//                return;
//            commonModule.getJSON("/Order/GetUserGuideInfo", function (rsp) {
//                var greenhandObj = rsp.Data;
//                window.greenhandObj = greenhandObj;
//                if (greenhandObj.isShowGreenhand) {
//                    initGreenhand(greenhandObj)  //初始化，新手入门
//                }
//            });
//        }, 1000);
//    }

//    //var greenhandObj = {};

//    //greenhandObj.isShowGreenhand = true;  //  是否展示新手入门引导--弹框
//    //greenhandObj.isSetAddresserComplete = true;      //是否已经完成  1) 设置发件人信息 设置  为true时为打勾符号  false打叉
//    //greenhandObj.isSetElectronicOrdeComplete = false; //是否已经完成  2) 开通/授权电子面单 设置  为true时为打勾符号  false打叉
//    //greenhandObj.isAddElectronicOrderComplete = false;//是否已经完成  3) 添加电子面单模板 设置  为true时为打勾符号  false打叉

//    function initGreenhand(greenhandObj) {  // 初始化，新手入门弹框
//        layer.open({
//            type: 1,
//            title: "新手入门，操作步骤指引<span style='color:#ff0000;font-size:12px;margin-left:10px'>1分钟设置步骤1/2/3内容，开始打单发货！</span>",
//            content: $('.greenhand'),
//            offset: ['40px', '10px'],
//            area: ['645', '330'], //宽高
//            //btn: ['保存', '关闭'],
//            yes: function () {

//            },
//            cancel: function () {


//                if ($("#isGreenhandShow").is(':checked')) {  //关闭按钮时  如果为 checkbox 已选择 调借口  isShowGreenhand值改为 false 以后不再弹出

//                    isShowGreenhand = false
//                    commonModule.getJSON("/Order/SetUserGuideInfoNeverShow", function () { });
//                }

//            }
//        });

//        //addTmplInOrderListModule.Initialize = function (templateSelectedCallback) {
//        addTmplInOrderListModule.LoadStapleTemplate(templateSelectedCallback, function () {
//            addTemplateModule.Initialize('addElectronicOrderMoudel', function () {
//                addTmplInOrderListModule.LoadStapleTemplate(templateSelectedCallback);
//                $('.addElectronicOrderIcon').css({ backgroundPosition: '0 -10px' });  //设置成功打勾符号
//                window.greenhandObj.isAddElectronicOrderComplete = true;
//                isAllStepSetted();
//            });
//        });
//        //}
//        if (greenhandObj.isSetAddresserComplete) { $('.setAddresserIcon').css({ backgroundPosition: '0 -10px' }) }
//        if (greenhandObj.isSetElectronicOrdeComplete) { $('.setElectronicOrderIcon').css({ backgroundPosition: '0 -10px' }) }
//        if (greenhandObj.isAddElectronicOrderComplete) { $('.addElectronicOrderIcon').css({ backgroundPosition: '0 -10px' }) }

//    }

//})

function guidanceSet() {

    var offsetY = $("#sp_add_template").offset().top - 5;
    var offsetX = $("#sp_add_template").offset().left - 5;
    $(".guidance-set02").css({ top: offsetY + 'px', left: offsetX })

    $(".guidance-set").hide(200);
    $(".guidance-set02").show(200);


}

function closeGuidanceSet() {
    $(".guidance-wrape").hide();
    isShowGreenhand = false
    commonModule.getJSON("/Order/SetUserGuideInfoNeverShow", function () { });
}

function isAllStepSetted() {
    var obj = window.greenhandObj;
    if (obj.isSetAddresserComplete && obj.isSetElectronicOrdeComplete && obj.isAddElectronicOrderComplete) {
        setTimeout(function () {
            layer.open({
                title: "设置完成", content: "设置完成，开始打单发货！", icon: 1, offset: ['150px'], btn: ['开始使用'],
                btn1: function () {
                    layer.closeAll();
                }
            });
        }, 500);
        //commonModule.getJSON("/Order/SetUserGuideInfoNeverShow", function () { });
    }
}

$("#setAddresserInformation").on("click", function () {
    expressPrinter.isExistSellerInfo(function (isSetSuccess) {
        if (isSetSuccess) {
            $('.setAddresserIcon').css({ backgroundPosition: '0 -10px' })  //设置成功打勾符号
            window.greenhandObj.isSetAddresserComplete = true;
            isAllStepSetted();
        }
        else
            $('.setAddresserIcon').css({ backgroundPosition: '-18px -10px' })  //设置失败打叉符号
    });
})



$("#setElectronicOrder").on("click", function () {       //  2)  开通/授权电子面单
    window.greenhandObj.isSetElectronicOrdeComplete = true;
    window.open(commonModule.rewriteUrl("/AccountList"));
    //window.location.href = commonModule.rewriteUrl("/AccountList");
})


$(".greenhand_footer_btn").on("mouseover", function () {
    $(this).children().show(100);
})
$(".greenhand_footer_btn").on("mouseleave", function () {
    $(this).children().hide();
})
$(".greenhandVideo_i").on("click", function () {
    $(".greenhandVideo").hide();
    var video = document.querySelector('video');
    video.pause();
})

$(".greenhand_fourList i").each(function (i, item) {

    $(this).on("click", function () {
        $(".greenhandVideo").css({ display: 'flex' });

        $(".greenhandVideo video").attr("src", "#");
        if (i == 0) {
            $(".greenhandVideo video").attr("src", "http://www.dgjapp.com/public/upload/8KgGSmvsnPmbGSAcutx6dG96.mp4")

        } else if (i == 1) {
            $(".greenhandVideo video").attr("src", "http://www.dgjapp.com/public/upload/U39kfx0qOJHKU2rGQukFQk7b.mp4")

        }
        else if (i == 2) {
            $(".greenhandVideo video").attr("src", "http://www.dgjapp.com/public/upload/JZPfmXZJnzVNVA655f1mXbr9.mp4")

        }
        else if (i == 3) {
            $(".greenhandVideo video").attr("src", "http://www.dgjapp.com/public/upload/6904FKNGOlzsHOpiX8m5xwPA.mp4")

        }

    })

})

//快手小店视频教程--新用户显示4天--4天后隐藏
var basicOrderlistSetCommon = (function (module, $, layer) {

    var $firstDataValue = null;

    module.kuaiShouGreenhandVideo = function () {
        layer.open({
            type: 1,
            title: "快手视频教程",
            content: $('.kuaiShouGreenhandVideoAialog'),
            area: ['800'], //宽高
            //btn: ['保存', '关闭'],
            yes: function () {
            },
            cancel: function () {
                var video = document.getElementById("kuaiShouGreenhandVideo");
                video.pause();
            }
        });
    }

    var isKSNewCustomer = isNewFirstVisitor();

    if (isKSNewCustomer) {

        var $nowDataValue = new Date().getTime();
        $firstDataValue = getCookieByArray("KSFirstVisitor");//拿到用户首次访问的时间
        var nTime = $nowDataValue - $firstDataValue;         //用户现在访问的时间减去首次访问时间 
        var days = Math.ceil(nTime / 1000 / 60 / 60 / 24);   //得到用户首次访问的时间已经过去多少天

        if (days > 4) {
            $(".banner-KuaiShou").hide();
        } else {
            $(".banner-KuaiShou").show();
        }

    } else {

        var firstDataValue = new Date().getTime();
        setcookie('KSFirstVisitor', firstDataValue);
        $(".banner-KuaiShou").show();

    }

    function isNewFirstVisitor() {
        var flg = getCookieByArray("KSFirstVisitor");
        if (!flg) {
            return false;
        } else {
            return true;
        }
    }


    function getCookieByArray(name) {   //获取cookies
        var cookies = document.cookie.split(';');
        var c;
        for (var i = 0; i < cookies.length ; i++) {
            c = cookies[i].split('=');
            if (c[0].replace(' ', '') == name) {
                return c[1];
            }
        }
    }

    function setcookie(name, value, days) {    //创建cookies
        var d = new Date();
        d.setTime(d.getTime() + (days * 24 * 60 * 60 * 1000));
        var expires = d.toGMTString();
        document.cookie = name + "=" + value + ";expires=" + expires;
    }

    return module;

})(basicOrderlistSetCommon || {}, jQuery, layer);


//新手指引通用-------------------------------------------------

var newUserGuideCommon = (function (module, $, layer) {

    var newUserGuideNum = 0;
    offsetY = $(".search_btn_up .index_btn").offset().top + 20;
    offsetX = $(".search_btn_up .index_btn").offset().left;
    $(".newUserGuide").css({ top: offsetY + 'px', left: offsetX + 'px' });

    module.newUserGuideNext = function (isThis) {

        newUserGuideNum++;
        if (newUserGuideNum == 1) {
            offsetY = $("#sp_add_template").offset().top + 20;
            offsetX = $("#sp_add_template").offset().left - 5;

            $("#newUserGuide_step").text("2.点击“+”，添加快递模板。");

            $(".newUserGuide").animate({ top: offsetY + 'px', left: offsetX + 'px' }, "1000", "linear");


        } else if (newUserGuideNum == 2) {
            offsetY = $(".orderList_orderState_title").offset().top + 100;
            offsetX = $(".orderList_orderState_title").offset().left + 100;
            $(isThis).parent(".newUserGuide").removeClass("newUserGuide01").addClass("newUserGuide02");
            //$(".newUserGuide").css({ top: offsetY + 'px', left: offsetX + 'px' });
            $("#newUserGuide_step").text("3.选择需要打印的订单");

            $(".newUserGuide").animate({ top: offsetY + 'px', left: offsetX + 'px' }, "1000", "linear");

        } else if (newUserGuideNum == 3) {
            $(isThis).parent(".newUserGuide").removeClass("newUserGuide01").removeClass("newUserGuide02").addClass("newUserGuide03").css({ position: 'fixed' });
            offsetX = $(".express-print-btn").offset().left - 80;
            $("#newUserGuide_step").text("4.打印快递单。");
            //$(".newUserGuide").css({ bottom: '60px', left: offsetX, top: "" });
            $(".newUserGuide").css({ top: 'unset' })
            $(".newUserGuide").animate({ bottom: '60px', left: offsetX }, "500", "linear");


        } else if (newUserGuideNum == 4) {
            $(isThis).parent(".newUserGuide").removeClass("newUserGuide01").removeClass("newUserGuide02").addClass("newUserGuide03").css({ position: 'fixed' });
            offsetX = $(".batch-send-btnonly-in-order-list").offset().left - 80;
            //$(".newUserGuide").css({ bottom: '60px', left: offsetX, top: "" });
            $("#newUserGuide_step").text("5.已经打印的快递单，批量发货。发货状态自动同步到店铺。");
            $(".newUserGuide").css({ top: 'unset' })

            $(".newUserGuide").animate({ bottom: '60px', left: offsetX }, "1000", "linear");


        } else if (newUserGuideNum == 5) {
            $('html , body').animate({ scrollTop: 0 });
            $(isThis).parent(".newUserGuide").addClass("newUserGuide01").removeClass("newUserGuide02").removeClass("newUserGuide03").css({ position: 'absolute' });
            offsetY = $(".telService").offset().top + 50;
            offsetX = $(".telService").offset().left;
            //$(".newUserGuide").css({ bottom: '', left: offsetX, top: offsetY });
            $("#newUserGuide_step").text("6.在使用过程中，有任何问题，联系在线客服！");
            $(".newUserGuideNext").text("开始使用");
            $(".newUserGuide").css({ bottom: 'unset' })
            $(".newUserGuide").animate({ left: offsetX, top: offsetY }, "1000", "linear");


        } else {
            module.closeNewUserGuide();

        }

    }

    module.closeNewUserGuide = function () {
        //设置配置，不再显示指引
        commonModule.SaveCommonSetting('ShowGuide', '0');
        $(".newUserGuide-wrape").hide();
        $(".newUserGuide-body").hide();
    }

    //检测是否是新用户，新用户显示指引
    module.IsShowGuide = function () {
        commonModule.Ajax({
            url: '/Order/IsShowGuide',
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                if (rsp.Data == true) {
                    $('#div_new_user_guide_layer').show();
                    $('#div_new_user_guide_step').show();
                    var offsetY = '';
                    var offsetX = '';
                    var offsetY = $(".search_btn_up>.index_btn").offset().top + 40;
                    var offsetX = $(".search_btn_up>.index_btn").offset().left;

                    $(".newUserGuide01").css({ top: offsetY + 'px', left: offsetX + 'px' })
                }
                else {
                    $('#div_new_user_guide_layer').hide();
                    $('#div_new_user_guide_step').hide();
                }
            }
        });
    }

    module.ClosePrintInitWind = function () {
        layer.close(setExpressTemplateLayer);
    }

    //检测是否有模板，没有模板弹出添加模板指引
    var setExpressTemplateLayer = null;
    module.OpenPrintInit = function () {

        if (addTmplInOrderListModule.GetAllStapleTemplates().length > 0) {
            return false;
        }

        setExpressTemplateLayer = layer.open({
            type: 1,
            title: "打单初始化操作步骤",
            content: $('#printInitStepShow'),
            area: ['730', 'auto'], //宽
            success: function () {
                $('#chk_no_set_close').prop('checked', false);
            }
        });
        return true;
    }

    //打开添加发件人
    module.setPrintInitStep = function () {

        var hideOrShowElement = function (display) {
            if (display == "none") {
                //隐藏不需要的项
                $('#li_CompanyName').hide();
                $('#li_seller_phone').hide();
                $('#li_seller_address_discern').hide();
            }
            else {
                //还原显示不需要的项
                $('#li_CompanyName').show();
                $('#li_seller_phone').show();
                $('#li_seller_address_discern').show();
            }
        }

        var initAddTemplateWind = function (preStepAction) {
            //初始化打开添加模板的按钮事件
            addTemplateModule.Initialize('btn_pre_step_help', function () {
                //添加完后，刷新常用模板
                addTmplInOrderListModule.LoadStapleTemplate(templateSelectedCallback);
                //检测组件安装情况
                //根据最后添加的模板类型，来检测哪一个组件是否安装。
                var stapleTemplates = addTmplInOrderListModule.GetAllStapleTemplates();
                var template = stapleTemplates[stapleTemplates.length - 1];
                var templateType = template.TemplateType;
                if (commonModule.IsCainiaoTemplate(templateType)
                    || commonModule.IsKuaiYunTemplate(templateType)
                    || commonModule.IsLinkTemplate(templateType)) {
                    caiNiaoPrinter.check(); //检测菜鸟组件
                }
                else if (commonModule.IsPddTemplate(templateType)) {
                    pinDuoDuoPrinter.check(); //检测拼多多组件
                }
                else {
                    lodopPrinter.check(); //检测lodop组件
                }
            }, function () {
                //上一步，返回
                if (typeof preStepAction == "function")
                    preStepAction();
            });

            //打开添加模板
            $('#btn_pre_step_help').click();
        }

        //先检测有没有设置发件人，有设置发件人，不再设置
        commonModule.Ajax({
            url: '/Order/GetCurrentShopHasSellerInfo',
            loading: true,
            type: 'POST',
            success: function (rsp) {
                if (rsp.Success == false) {
                    layer.msg(rsp.Message, { icon: 2 });
                    return;
                }
                //关闭指引窗
                module.ClosePrintInitWind();

                if (rsp.Data == true) {
                    //有设置发件人，直接弹出设置模板
                    initAddTemplateWind(newUserGuideCommon.OpenPrintInit);
                    return;
                }

                //无发件人，则弹出设置发件人
                var addSellerInfLayer = layer.open({
                    type: 1,
                    title: "添加发货地址",
                    content: $('#div_add_sellerInfo'),
                    area: ['800'], //宽
                    btn: ['上一步', '下一步'],
                    success: function () {
                        //隐藏不需要的项
                        hideOrShowElement("none");


                        function selectCallBack(control) {
                            var deep = control.attr('deep');

                            if (deep > 1) {
                                var dataValue = control.attr("data-value");
                                var isExistsVal = control.find("option[value='" + dataValue + "']").length;
                                if (isExistsVal > 0)
                                    control.val(dataValue).trigger('change');
                            }
                        }

                        //加载地址级联选择
                        commonModule.LoadAreaInfoToControl('sellerProvince-select', 1, function () {
                        }, selectCallBack, "name");

                        //$('#fromProvince-select').val('0').change();
                        //$('#txtFromDetailAddr').val('');

                    },
                    end: function () {
                        //还原显示不需要的项
                        hideOrShowElement("block");
                    },
                    yes: function () {
                        //alert("上一步")
                        //还原显示不需要的项
                        hideOrShowElement("block");
                        //关闭此窗口，显示提示窗口
                        layer.close(addSellerInfLayer);
                        //显示提示窗口
                        setExpressTemplateLayer = layer.open({
                            type: 1,
                            title: "添加快递单模板",
                            content: $('#printInitStepShow'),
                            area: ['730', 'auto'], //宽

                        });
                    },
                    btn2: function () {

                        //alert("下一步")

                        var companyName = $('#txtCompanyName').val().trim();
                        var sellerName = $('#txtSellerName').val().trim();
                        var sellerMobile = $('#txtSellerMobile').val().trim();
                        var sellerPhone = $('#txtSellerPhone').val().trim();

                        var sellerProvince = $('#sellerProvince-select').val();
                        var sellerCity = $('#sellerCity-select').val();
                        var sellerArea = $('#sellerArea-select').val();
                        var sellerDetailAddr = $('#txtSellerDetailAddr').val().trim();


                        if (sellerName == '') {
                            layer.msg('请填写发件人姓名.');
                            return false;
                        }

                        if (sellerMobile == '') {
                            layer.msg('请填写发件人手机号.');
                            return false;
                        }

                        var sellerAddr = '';

                        if (sellerProvince && sellerProvince != "0") {
                            sellerAddr += sellerProvince;
                        }
                        else {
                            layer.msg('请选择省份.');
                            return false;
                        }

                        if (sellerCity && sellerCity != "0") {
                            sellerAddr += sellerCity;
                        }
                        else {
                            layer.msg('请选择城市.');
                            return false;
                        }

                        if (sellerArea && sellerArea != "0") {
                            sellerAddr += sellerArea;
                        }
                        else {
                            layer.msg('请选择区域.');
                            return false;
                        }

                        if (sellerDetailAddr == '') {
                            layer.msg('请填写发件人地址.');
                            return false;
                        }

                        sellerAddr += sellerDetailAddr;

                        commonModule.Ajax({
                            url: '/SellerInfo/AddDefaultSeller',
                            loading: true,
                            data: {
                                companyName: companyName,
                                sellerName: sellerName,
                                sellerMobile: sellerMobile,
                                sellerPhone: sellerPhone,
                                sellerAddress: sellerAddr
                            },
                            type: 'POST',
                            success: function (rsp) {
                                if (rsp.Success == false) {
                                    layer.msg(rsp.Message, { icon: 2 });
                                    return;
                                }
                                //自动填充发件人信息到订单列表
                                if (window.orderTableBuilder && orderTableBuilder.rows && orderTableBuilder.rows.length > 0) {
                                    commonModule.Foreach(orderTableBuilder.rows, function (i, o) {
                                        //只填充当前店铺的发件人为空的订单
                                        if (o.ShopId == commonModule.CurrShop.Id && (!o.SenderName || (!o.SenderMobile && !o.SenderPhone) || !o.SenderAddress)) {
                                            //完善数据
                                            o.SenderName = sellerName;
                                            o.SenderPhone = sellerPhone;
                                            o.SenderMobile = sellerMobile;
                                            o.SenderAddress = sellerAddr;
                                            o.IsManualUpdateSeller = true;
                                            //重新渲染行
                                            orderTableBuilder.refreshRow(o);
                                        }
                                    });
                                }
                                //打开添加模板弹窗
                                initAddTemplateWind(newUserGuideCommon.OpenPrintInit);
                            }
                        });


                    }
                });
            }
        });
    }

    return module;

})(newUserGuideCommon || {}, jQuery, layer);


    ;(function (win) {


    win.footerFunc = {};


    win.footerFunc.onSpeedChooseOrderList = function () {

        $(this).addClass("active").children(".footers-select-title").css({ boxShadow: "0px 2px 4px 0px #ddd", borderTopRightRadius: "unset", borderTopLeftRadius: "unset" }).nextAll(".footers-select-list").show();

    };


    win.footerFunc.outSpeedChooseOrderList = function () {

        $(this).addClass("active").children(".footers-select-title").css({ boxShadow: "0px 0 3px 0px #ddd", borderTopRightRadius: "4px", borderTopLeftRadius: "4px" }).nextAll(".footers-select-list").hide();

    };

    win.footerFunc.allSelectOrderList = function (type) {

        var allCheck = $(".order-chx-all");

        if (type == 0) {
            if ($(".table_content_tbody>tr").length != 0) {

                if (!allCheck.is(':checked')) {
                    allCheck.click();
                }
            }

        } else if (type == 1) {

            if (allCheck.is(':checked')) {

                allCheck.click();

            } else {

                allCheck.click().click();

            }

        } else if (type == 2) {

            $(".order-chx").click();

        }

        var m = 0;
        $(".order-chx").each(function (i, item) {

            if ($(item).is(':checked')) {

                m++;
            }

        });

        if (m != 0) {

            $("#footers_chooseOrderMun").html('已选<i style="color:#3aadff;padding:0 5px;">' + m + '</i>单');

        } else {

            $("#footers_chooseOrderMun").html('快捷勾选');
        }
    }

    $("#Speed_ChooseOrder_List").on("mouseover", function (e) {

        $("#Speed_ChooseOrder_List li").css({ background: "unset", color: "#666" });
        var touchLi = e.target;
        if (touchLi.nodeName.toUpperCase() === "LI") {
            $(touchLi).css({ "background": "#3aadff", color: "#fff" })
        }
    })


    $("#Speed_ChooseOrder_List").on("mouseout", function (e) {

        var touchLi = e.target;

        if (touchLi.nodeName.toUpperCase() === "LI") {

            $("#Speed_ChooseOrder_List li").css({ background: "unset", color: "#666" });

        }

    })

})(window);
