
@{
    ViewBag.Title = "子账号登陆";

}
@section Header{
    <style type="text/css">
        .wrapper {
            background-color: #3aadff;
            margin: 0;
            background: linear-gradient(#3aadff,#3aadff 30%, #fff);
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            text-align: center;
            display: flex; 
            justify-content: center; 
            align-items: center; 
        }
        .loginBox {
            background-color: #fff;
            border-radius: 4px;
            padding: 20px;
            display: inline-block;
            position: absolute;
            border-radius: 12px;
            border: 10px solid #b9e2ff;
        }

        .loginBox-title {
            padding: 10px;
            /* border: 1px solid #e2e2e2; */
            background: linear-gradient(#def1ff,#e5f4ff 30%, #fff);
            color: #ffa037;
            border-radius: 4px;
            font-size: 18px;
            font-weight: 700;
            font-family: 微软雅黑;
            letter-spacing: 2px;
            text-align: center;
            margin-bottom: 10px;
        }
        .loginBox-content {

        }
        .loginBox-content > li {
            padding:11px 10px;
            text-align: left;            
        }
        .warn-content {
            position: absolute;
            display: block;
            color: #fa6b10;
        }
        .loginBox-content input[type=text], .loginBox-content input[type=password] {
            height:26px;
            border:1px solid #e2e2e2;
            border-radius:4px;
            margin: 0;
            font-size: 14px;
            letter-spacing: 1px;
            /* padding-left: 5px; */
            box-sizing: border-box;
            font-family: '微软雅黑';
            line-height: 26px;
            
        }
        .loginBtn {
            width: 100%;
            height: 32px;
            background-color: #4eb5ff;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: 14px;
            cursor: pointer;
        }
        .loginCode {
            display: inline-block;
            margin-left: 5px;
        }

    </style>


}

<div class="wrapper" id="wrapper">
    <div class="loginBox" id="loginBox">
        <div class="loginBox-title">店管家-子账号登录窗口</div>
        <ul class="loginBox-content">
            <li>
                <input type="text" id="loginTelePhone" name="name" value="" placeholder="请输入子账号" style="width:250px"/>
            </li>
            <li><input type="password" name="name" value="" placeholder="请输入密码" style="width:250px" /></li>
            <li class="codeWrapper"><input type="text" name="name" value="" style="width:150px" placeholder="请输入验证码"/><div class="loginCode"><img src="#" alt="验证码" /></div></li>
            <li style="padding-bottom:0"><span class="loginBtn" onclick="loginBypassAccount.login()">登录</span></li>
        </ul>
    </div>
</div>





@section scripts{
    <script type="text/javascript">

        ; (function (win) {

            win.loginBypassAccount = {};

            loginBypassAccount.login = function () {

                if (!checkPhone("#loginTelePhone", "手机号格式不正确")) {
                    return;
                }


            }





            function checkPhone(ele, str) {  //匹配手机号，正确返回true 错误返回false
                var phone = $(ele).val();
                if (!(/^1(3|4|5|7|8|9)\d{9}$/.test(phone))) {
                    var html = '<span class="warn-content">' + str + '</span>';
                    $('#loginTelePhone').after(html);
                    setTimeout(function () {
                        $(".warn-content").remove();

                    }, 3000)

                    return false;
                }
                return true;
            }



            var boxEl = document.getElementById("wrapper");
            var moveEl = document.getElementById("loginBox");
            startDrop(boxEl, moveEl);
            function startDrop(el, move) {
                el.onmousedown = function (event) {
                    var event = event || window.event;
                    var x = event.clientX - move.offsetLeft;  //记录当前盒子的x位置
                    var y = event.clientY - move.offsetTop;  //记录当前盒子的y位置
                    document.onmousemove = function (event) {
                        var event = event || window.event;
                        move.style.left = event.clientX - x + "px";
                        move.style.top = event.clientY - y + "px";
                    }
                }
                document.onmouseup = function () {  //鼠标谈起，不应该操作
                    document.onmousemove = null;
                }

            }



        })(window);





    </script>
}




