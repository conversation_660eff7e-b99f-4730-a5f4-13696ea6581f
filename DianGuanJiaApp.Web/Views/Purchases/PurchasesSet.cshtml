
@{
    ViewBag.Title = "备货单-表格样式设置";
    ViewBag.MenuId = "PurchasesSet";

}
@section Header{
    @Styles.Render("~/css/puchase")
    <style>
        label > span {
            position: unset;
        }

        #purchasesSet_table_content {
            width: unset;
        }

        .purchasesSet_table_content {
            font-size: 12px;
            text-align: center;
            font-family: 宋体;
            /* line-height: 30px; */
            border-left: solid 1px #dbdbdb;
            width: unset;
            
        }

            .purchasesSet_table_content thead tr {
                background-color: #f7f7f7;
            }

                .purchasesSet_table_content thead tr th,
                .purchasesSet_table_content tbody tr td {
                    text-align: center;
                    border-right: solid 1px #ccc;
                    border-top: solid 1px #ccc;
                    border-bottom: 1px solid #ccc;
                    font-weight: normal;
                    background-color: #f5f5f5;
                    cursor: pointer;
                }

                    .purchasesSet_table_content thead tr th.td-active,
                    .purchasesSet_table_content tbody tr td.td-active {
                        background-color: #ffefdd;
                    }

            .purchasesSet_table_content tbody tr#tabelOparete td.td-active {
                background-color: #f7941f;
            }

            .purchasesSet_table_content tbody .tabelOparete td {
                background-color: #555;
            }

            .purchasesSet_table_content tbody .tabelContent td {
                text-align: left;
            }

            .purchasesSet_table_content thead tr th {
                color: #333;
                height: 30px;
            }

            .purchasesSet_table_content tbody tr td {
                box-sizing: border-box;
            }

        .table-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
        }

            .table-btn > span {
                cursor: pointer;
                width: 30px;
            }

            .table-btn > i {
                background-image: url(/Content/Images/allicons2.png);
                width: 14px;
                height: 13px;
                background-position: 0 -87px;
                display: inline-block;
                cursor: pointer;
            }



        .purchasesSetWrap {
            min-height: 500px;
        }

            .purchasesSetWrap > .content {
                width: 1000px;
                margin: 20px auto;
                position: relative;
                min-width: unset;
            }

                .purchasesSetWrap > .content > .content-title {
                    padding: 20px;
                    font-size: 18px;
                    font-family: 微软雅黑;
                    border-bottom: 1px solid #e2e2e2;
                    font-weight: 700;
                    color: #000;
                    display:flex;
                    justify-content:space-between;
                }

                .purchasesSetWrap > .content > .content-title > .help {
                    font-size:12px;
                    font-weight:300;
                    color:#1295c1;
                    cursor:pointer;
                    font-family: 宋体;             
                }
                .purchasesSetWrap > .content > .content-left {
                    width: 100%;
                }

        .content-left-up > li {
            padding-top: 20px;
        }

            .content-left-up > li > label > span {
                width: 105px;
                text-align: right;
                display: inline-block;
            }

            .content-left-up > li > label > select,
            .content-left-up > li > label > input {
                width: 80px;
                height: 22px;
                border: 1px solid #e2e2e2;
            }

        .content-left-down {
            margin-top: 30px;
        }

        .tableWrap {
            position: relative;
            margin: 0 auto;
            position: relative;
            display: flex;
            justify-content: center;
            flex-wrap: nowrap;
        }


            .tableWrap .morelatitude > div:nth-child(2) {
                border-top: 1px solid #ccc;
            }

        .content-left-down .editCol {
            width: 100%;
            margin-top: 20px;
            border: 1px solid #3aadff;
            position: relative;
            padding: 10px 0;
            display: none;
        }

            .content-left-down .editCol .btn {
                position: absolute;
                top: 8px;
                right: 0;
            }

            .content-left-down .editCol > div {
                padding: 10px;
            }

                .content-left-down .editCol > div > label {
                    margin-right: 15px;
                }

                .content-left-down .editCol > div input,
                .content-left-down .editCol > div select {
                    height: 22px;
                    border: 1px solid #e2e2e2;
                }

            .content-left-down .editCol .table-labels {
                margin-left: 55px;
            }

            .content-left-down .editCol .warn {
                margin-left: 55px;
                color: #888;
                margin-top: 10px;
            }

            .content-left-down .editCol .table-labels > li {
                float: left;
                margin-top: 10px;
                margin-right: 10px;
                padding: 2px 10px;
                border: 1px solid #3aadff;
                border-radius: 2px;
                background-color: #daf0ff;
                color: #000;
                position: relative;
            }

        .table-labels .table-labels-delIcon {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-image: url(/Content/Images/btn_close_s.gif);
            position: absolute;
            top: 1px;
            right: 1px;
            cursor: pointer;
        }

        .purchasesSetWrap > .content > .content-right {
            width: 136px;
            border: 1px solid #d3edff;
            position: fixed;
            right: 20px;
            top: 185px;
            background-color: #fff;
            z-index: 10000000;
        }

        .editTable-module > dt {
            text-align: center;
            padding: 8px 0;
            background-color: #3aadff;
            color: #fff;
        }

        .editTable-module > dd {
            padding: 0 10px 10px 10px;
            border-bottom: 1px solid #e2e2e2;
            cursor: pointer;
        }

            .editTable-module > dd:last-child {
                border-bottom: none;
            }

            .editTable-module > dd.active {
                background-color: #b2dfff;
                color: #fff;
                position: relative;
            }

                .editTable-module > dd.active .editTable-module-img {
                    background-position: 0 -56px !important;
                }

                .editTable-module > dd.active::before {
                    position: absolute;
                    content: "";
                    width: 0;
                    height: 0;
                    top: 43px;
                    left: -9px;
                    border-top: 10px solid transparent;
                    border-right: 10px solid #b2dfff;
                    border-bottom: 10px solid transparent;
                }

            .editTable-module > dd > span {
                padding: 10px 0;
                display: block;
            }

        .content-left-down > .save {
            padding: 20px 0;
            text-align: center;
        }

            .content-left-down > .save > .index_btn {
                padding: 6px 30px;
                font-size: 14px;
            }


        .editTable-module .editTable-module-img {
            width: 118px;
            height: 56px;
            background-image: url(/Content/Images/table_style_1.png);
            background-position: 0 0;
        }

        .purchasesSetDialog {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 999;
            overflow: auto;
            display: none;
        }

        .purchasesSetDialog-wrap {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 99;
            width: 100%;
            height: 100%;
            background-color: #999;
            filter: alpha(opacity=50);
            -moz-opacity: 0.5;
            -khtml-opacity: 0.5;
            opacity: 0.6;
        }

        .purchasesSetDialog-main {
            width: 820px;
            background-color: #fff;
            position: absolute;
            left: 50%;
            top: 50%;
            z-index: 10000000;
            transform: translate(-50%, -50%);
            border-radius: 4px;
        }

        .purchasesSetDialog-main-title {
            padding: 0 80px 0 20px;
            height: 42px;
            line-height: 42px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
            color: #333;
            overflow: hidden;
            background-color: #F8F8F8;
            border-radius: 4px 4px 0 0;
            display: flex;
            justify-content: space-between;
        }

        .purchasesSetDialog-close {
            position: relative;
            display: inline-block;
            top: 11px;
            right: -68px;
            cursor: pointer;
            width: 20px;
            height: 20px;
        }

            .purchasesSetDialog-close::before,
            .purchasesSetDialog-close::after {
                position: absolute;
                width: 15px;
                height: 1px;
                background-color: #000;
                content: '';
                top: 9px;
                left: 2px;
                transform: rotate(45deg);
            }

            .purchasesSetDialog-close::after {
                transform: rotate(135deg);
            }

        .purchasesSetDialog-main-content > li > label > span {
            width: 90px;
        }

        .purchasesSetDialog-main-content > li > label > select,
        .purchasesSetDialog-main-content > li > label > input {
            width: 62px;
        }

        .purchasesSetDialog-main-content > li > label > .productCargoNumber {
            width: 96px;
        }

        .purchasesSetDialog-main-content > li {
            padding-top: 10px;
        }

        .urchasesSetDialog-main-contentTable {
            margin: 20px 10px 0 10px;
        }

            .urchasesSetDialog-main-contentTable .purchasesSet_table_content thead tr th,
            .purchasesSet_table_content tbody tr td {
                border-bottom: unset;
            }

        #urchasesSetDialog_main-contentTable > table:nth-child(2) {
            margin-left: 10px;
        }

        .purchasesSetDialog-footer {
            width: 100%;
            height: 44px;
            border-bottom-right-radius: 4px;
            border-bottom-left-radius: 4px;
            border-right: 1px solid #fff;
            border-left: 1px solid #fff;
            border-bottom: 1px solid #fff;
            box-sizing: border-box;
            position: relative;
            text-align: right;
            padding-top: 8px;
            margin-bottom: 10px;
            margin-top: 10px;
        }

            .purchasesSetDialog-footer > span {
                display: inline-block;
                padding: 6px 13px;
                border-radius: 2px;
                margin-right: 10px;
                cursor: pointer;
                background-color: #3aadff;
                color: #fff;
            }

            .purchasesSetDialog-footer > .aialog_wrapper_cancel {
                border: 1px solid #dedede;
                box-sizing: border-box;
                color: #888;
                padding: 6px 13px;
                background-color: #fff;
                margin-right: 20px;
            }

        .purchasesSetDialog-main > .zhe {
            position: relative;
        }

            .purchasesSetDialog-main > .zhe::before {
                position: absolute;
                content: "";
                width: 100%;
                height: 100%;
                display: block;
                top: 0;
                left: 0;
                z-index: 1000;
            }

        .customAialog {
            padding: 10px;
            width: 400px;
            height: 100px;
            box-sizing: border-box;
        }

            .customAialog > div {
                margin-bottom: 10px;
            }

            .customAialog > input {
                width: 300px;
                height: 30px;
            }
        .oviceIntrog-nosetBackgroundColor {
            background-color:#fff!important;
        }
    </style>
}

<div class="purchases_content_nav">
    <ul class="clearfix purchases-tab-ul">
        <li onclick="location.href=commonModule.rewriteUrl('/Purchases/Index')" data-type="purchases"><span>备货单</span></li>
        @*<li onclick="location.href = commonModule.rewriteUrl('/Purchases/newPurchases')" data-type="PurchasesSet"><span>新备货单</span></li>*@
        <li onclick="location.href = commonModule.rewriteUrl('/ScanProductPrint/Index')" data-type="scanPrint" style="position:relative;"><span>扫描打印</span></li>
        <li class="active" onclick="location.href = commonModule.rewriteUrl('/Purchases/PurchasesSet')" data-type="PurchasesSet"><span>表格样式设置</span></li>
        <li onclick="location.href = commonModule.rewriteUrl('/SetMarketStalls/Index')" data-type="setMarketStalls"><span>设置市场档口</span></li>
        <li onclick="location.href = commonModule.rewriteUrl('/SetProductFlag/Index')" data-type="setLabel"><span>设置标签</span></li>
        <li onclick="location.href = commonModule.rewriteUrl('/SetSortingCodeRule/Index')" data-type="setSortCode"><span>设置分拣码规则</span></li>
        @*<li class="target-dgjHelps">
            <a href="https://www.dgjapp.com/newHelpsClassify.html?id=1576570067746&columnId=1576570023713&type=helps" target="_blank">
                小标签、扫描打印发货教程
            </a>
        </li>*@
        <li class="target-dgjHelps"><a href="javascript:;" onclick="PurchasesSetModule.purchasesSetHelps()"><i class="search-icon" style="margin-right:3px" title="如何使用表格样式设置">?</i>表格样式设置使用教程</a></li>
    </ul>

</div>

<div class="purchasesSetWrap">
    <div class="content">
        <div class="content-title"><span>备货单表格样式设置</span></div>
        <div class="content-left">
            <ul class="content-left-up" id="content_left_up">
                <li>
                    <label>
                        <span>纸张规格:</span>
                        <select class="select_font" name="pageType">
                            <option value="210_297">A4</option>
                            <option value="148_210">A5</option>
                            <option value="297_420">A3</option>
                            <option value="241_93">三等分</option>
                            <option value="241_140">二等分</option>
                            <option value="241_280">一等分</option>
                            <option value="100_150">10*15</option>
                            <option value="100_180">10*18</option>
                            <option value="100_210">10*21</option>
                        </select>
                    </label>
                    <label>
                        <span>打印方向:</span>
                        <select class="select_font" name="orient">
                            <option value="1">纵向</option>
                            <option value="2">横向</option>
                        </select>
                    </label>
                    <label>
                        <span>图片尺寸:</span>
                        <select class="select_font" name="imageSize">
                            <option value="50x50">50x50</option>
                            <option value="60x60">60x60</option>
                            <option value="70x70">70x70</option>
                            <option value="80x80">80x80</option>
                            <option value="90x90">90x90</option>
                            <option value="100x100">100x100</option>
                            <option value="110x110">110x110</option>
                            <option value="120x120">120x120</option>
                            <option value="130x130">130x130</option>
                            <option value="140x140">140x140</option>
                            <option value="150x150">150x150</option>
                            <option value="160x160">160x160</option>
                        </select>
                    </label>
                    <label>
                        <span>行高:</span>
                        <input type="text" name="height" style="margin-right:0">
                    </label>
                    <label>
                        <span>规格简称相同时:</span>
                        <select class="select_font" name="skuPropsShortMerge">
                            <option value="1">合并</option>
                            <option value="0">不合并</option>
                        </select>
                    </label>
                </li>
                <li>
                    <label>
                        <span>默认字体:</span>
                        <select class="select_font" name="fontFamily">
                            <option value="宋体">宋体</option>
                            <option value="黑体">黑体</option>
                            <option value="微软雅黑">微软雅黑</option>
                            <option value="楷体">楷体</option>
                            <option value="仿宋">仿宋</option>
                            <option value="Arial">Arial</option>
                        </select>
                    </label>

                    <label>
                        <span>默认字号:</span>
                        <select name="fontSize">
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10</option>
                            <option value="11">11</option>
                            <option value="12">12</option>
                            <option value="13">13</option>
                            <option value="14">14</option>
                            <option value="15">15</option>
                            <option value="16">16</option>
                            <option value="17">17</option>
                            <option value="18">18</option>
                            <option value="19">19</option>
                            <option value="20">20</option>
                            <option value="21">21</option>
                            <option value="22">22</option>
                            <option value="23">23</option>
                            <option value="24">24</option>
                            <option value="25">25</option>
                            <option value="26">26</option>
                            <option value="27">27</option>
                            <option value="28">28</option>
                        </select>
                    </label>

                    <label>
                        <span>表格颜色:</span>
                        <select class="select_font" name="tableColor">
                            <option value="#000000">黑色</option>
                            <option value="#666666">深灰色</option>
                            <option value="#cccccc">浅灰色</option>
                            <option value="#ffffff">无</option>
                        </select>
                    </label>

                    <label>
                        <span>行距:</span>
                        <select class="select_font" name="lineHeight">
                            <option value="80">80%</option>
                            <option value="90">90%</option>
                            <option value="100">100%</option>
                            <option value="110">110%</option>
                            <option value="120">120%</option>
                            <option value="130">130%</option>
                            <option value="140">140%</option>
                            <option value="150">150%</option>
                            <option value="160">160%</option>
                            <option value="170">170%</option>
                            <option value="180">180%</option>
                            <option value="190">190%</option>
                            <option value="200">200%</option>
                        </select>
                    </label>
                    <label>
                        <span>单品货号相同时:</span>
                        <select class="select_font" name="cargoNumberNotContains">
                            <option value="1">合并</option>
                            <option value="0">不合并</option>
                        </select>
                    </label>
                </li>

                <li>
                    <label>
                        <span>商品排序:</span>
                        <select class="productCargoNumber" name="firstSortField">
                            <option value="">请选择</option>
                            <option value="sphh">商品货号</option>
                            <option value="spjc">商品简称</option>
                            <option value="spbt">商品标题</option>
                            <option value="spsl">商品数量</option>
                        </select>
                    </label>

                    @*<label>
                            <span>商品简称相同时:</span>
                            <select class="select_font" name="ShortTitle">
                                <option value="1">合并</option>
                                <option value="0">不合并</option>
                            </select>
                        </label>*@

                    @*<label>
                            <span>规格名称相同时:</span>
                            <select class="select_font" name="skuPropsMerge">
                                <option value="1">合并</option>
                                <option value="0">不合并</option>
                            </select>
                        </label>*@

                    @*<label>
                        <span>规格简称相同时:</span>
                        <select class="select_font" name="skuPropsShortMerge">
                            <option value="1">合并</option>
                            <option value="0">不合并</option>
                        </select>
                    </label>*@
                </li>
            </ul>
            <div class="content-left-down">
                <div class="tableWrap" id="tableWrap">
                    <table class="purchasesSet_table_content" id="purchasesSet_table_content">
                        <thead id="purchasesSet_table_thead">
                            <tr></tr>
                        </thead>
                        <tbody id="purchasesSet_table_tbody">
                            <tr class="" id="tabelContentheader" style="line-height: 30px;"></tr>
                            <tr class="tabelContent" id="tabelContent"></tr>
                            <tr class="tabelOparete" id="tabelOparete" style="line-height: 30px;"></tr>
                        </tbody>
                    </table>
                    <div style="margin-left: 20px;"><span id="addTableCol" class="index_btn" onclick="PurchasesSetModule.addTableCol()">添加列</span></div>
                </div>
                <div class="editCol" id="editCol">
                    <div>
                        <label><span>表头名称:</span><input type="text" name="title" style="width: 120px;"></label>
                        <label><span>列宽度: </span><input type="text" name="tdWidth" style="width: 50px;"><span>px</span></label>
                        <label>
                            <span>对齐方式:</span>
                            <select id="editColTextAlign">
                                <option value="left">左对齐</option>
                                <option value="center">居中</option>
                                <option value="right">右对齐</option>
                            </select>
                        </label>
                        <label>
                            <span>字体:</span>
                            <select class="select_font" name="fontFamily" id="editColFontFamily">
                                <option value="">默认字体</option>
                                <option value="宋体">宋体</option>
                                <option value="黑体">黑体</option>
                                <option value="微软雅黑">微软雅黑</option>
                                <option value="楷体">楷体</option>
                                <option value="仿宋">仿宋</option>
                                <option value="Arial">Arial</option>
                            </select>
                        </label>
                        <label>
                            <span>字号:</span>
                            <select name="fontSize" id="editColFontSize">
                                <option value="-1">默认字号</option>
                                <option value="8">8</option>
                                <option value="9">9</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                                <option value="13">13</option>
                                <option value="14">14</option>
                                <option value="15">15</option>
                                <option value="16">16</option>
                                <option value="17">17</option>
                                <option value="18">18</option>
                                <option value="19">19</option>
                                <option value="20">20</option>
                                <option value="21">21</option>
                                <option value="22">22</option>
                                <option value="23">23</option>
                                <option value="24">24</option>
                                <option value="25">25</option>
                                <option value="26">26</option>
                                <option value="27">27</option>
                                <option value="28">28</option>
                            </select>
                        </label>
                        <label>
                            <span>加粗:</span>
                            <select class="select_font" name="fontWeight" id="editColFontWeight">
                                <option value="normal" selected="">否</option>
                                <option value="bold">是</option>
                            </select>
                        </label>
                        <label>
                            <span>颜色:</span>
                            <select class="select_font" name="fontColor" id="editColFontColor">
                                <option value="#000000" selected="">黑</option>
                                <option value="#555555">深灰</option>
                                <option value="#777777">中灰</option>
                                <option value="#aaaaaa">浅灰</option>
                                <option value="#ff0000">红</option>
                                <option value="#008000">绿</option>
                                <option value="#8fdacd">蓝</option>
                                <option value="#f68b1e">橙</option>
                                <option value="#7c007a">紫</option>
                            </select>
                        </label>
                    </div>
                    <div>
                        <label>
                            <span>表格内容:</span>
                            <select class="label-select" id="tableContentSelect">
                                <option value="0">添加数据项...</option>

                            </select>
                        </label>
                        <ul class="table-labels clearfix" id="targetPurchasesCol">
                            <li>序号<i class="table-labels-delIcon"></i></li>
                        </ul>
                        <div class="warn">(同一个数据框内带*的数据（商品维度）和不带*的数据（其它维度）不能同时显示)</div>
                    </div>
                    <div class="btn"><span class="index_btn1" onclick="PurchasesSetModule.delTableDataTd()">删除列</span></div>
                </div>
                <div class="save"><span class="index_btn" onclick="PurchasesSetModule.saveSubmit()">保存</span></div>
            </div>
        </div>
        <div class="content-right">
            <dl class="editTable-module" id="editTable_module">
                <dt>参考模版</dt>
                @*<dd>
                        <span>模版(一)</span>
                        <div class="editTable-module-img"  style="background-image: url(/Content/Images/table_style_1.png)"></div>
                    </dd>*@
            </dl>
        </div>

        <div class="purchasesSetDialog">
            <div class="purchasesSetDialog-wrap">
            </div>
            <div class="purchasesSetDialog-main">
                <div class="purchasesSetDialog-main-title">
                    <span id="purchasesSetDialog_title">模板(一)</span>
                    <span class="purchasesSetDialog-close" onclick="PurchasesSetModule.purchasesSetDialogClose()"></span>
                </div>
                <div class="zhe">
                    <ul class="purchasesSetDialog-main-content content-left-up" id="purchasesSetDialog_main_content"></ul>
                    <div class="urchasesSetDialog-main-contentTable tableWrap" id="urchasesSetDialog_main-contentTable">
                        <table class="purchasesSet_table_content" id="purchasesSetDialog_table_content">
                            @*<thead id="purchasesSet_table_thead">
                                    <tr></tr>
                                </thead>
                                <tbody id="purchasesSet_table_tbody">
                                    <tr class="" id="tabelContentheader"></tr>
                                    <tr class="tabelContent" id="tabelContent"></tr>
                                    <tr class="tabelOparete" id="tabelOparete"></tr>
                                </tbody>*@
                        </table>
                    </div>
                </div>
                <div class="purchasesSetDialog-footer">
                    <span class="aialog_wrapper_sure" onclick="PurchasesSetModule.saveTableModule()">确定</span>
                    <span class="aialog_wrapper_cancel" onclick="PurchasesSetModule.purchasesSetDialogClose()">取消</span>
                </div>
            </div>
        </div>
    </div>
</div>
@section scripts{
    <script src="~/Scripts/Purchase/purchasesSet.js"></script>
}

