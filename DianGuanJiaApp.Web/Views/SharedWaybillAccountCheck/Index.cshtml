@{
    ViewBag.Title = "电子面单分享对账";
    ViewBag.MenuId = "Settings";
    ViewBag.LeftMenuId = "ShareWayBillAccount_LeftMenu";
    ViewBag.MenuId_wd = "shareWaybillAccount"; //网点系统菜单id
}
@section Header{
    <style>
        .more_right_title_nav {
            display: inline-block;
        }

            .more_right_title_nav > span {
                padding: 8px 15px 9px 15px;
                color: #1295c1;
                font-size: 12px;
                font-weight: 400;
                cursor: pointer;
                position: relative;
            }

            .more_right_title_nav > .active {
                border-bottom: 2px solid #17b3e7;
                font-weight: 700;
            }

        .more_right_title_navIcon {
            position: absolute;
            top: -4px;
            right: -10px;
            color: #fe6f4f;
            font-weight: 400 !important;
        }

        select {
            height: 22px;
        }

        .ShareWayBillAccount-explain {
            padding: 10px 15px;
            background-color: #f5f5f5;
        }

            .ShareWayBillAccount-explain > li {
                padding: 2px 0;
            }

        .ShareWayBillAccount-btn {
            padding: 10px 0;
        }

            .ShareWayBillAccount-btn > .index_btn {
                padding: 3px 15px;
            }

        .index_btn:hover {
            background: linear-gradient(#51b5fd, #2ea5fd 50%, #21a1fd);
        }

        .ShareWayBillAccount-search, .append-search {
            border: 1px solid #e2e2e2;
            padding: 10px;
            margin-bottom: 10px;
        }

        .ShareWayBillAccount-search-select, .append-search-select, .used-search-select, .recycle-search-select {
            width: 165px;
        }

        .ShareWayBillAccount-search > label, .append-search > label {
            margin-right: 10px;
        }

        .labelSearch-input > input {
            width: 90px;
        }

        .labelSearch-input > span {
            padding: 0 2px 0 5px;
        }

        .stockup_table_content {
            width: 100%;
        }

            .stockup_table_content thead tr th {
                padding: 5px;
                line-height: 16px;
            }

            .stockup_table_content tbody tr td {
                padding: 5px;
                text-align: center;
            }

        .iconEdmit, .iconAdd {
            display: inline-block;
            width: 14px;
            height: 14px;
            background-image: url(/Content/Images/allicons.png);
            background-position: 0 -102px;
            cursor: pointer;
            position: relative;
            top: 2px;
        }

        .iconAdd {
            background-position: -14px -102px;
        }

        .iconEdmit {
            display: none;
            position: absolute;
            top: unset;
            right: -14px;
            bottom: 0;
        }

        .table-operate > span {
            cursor: pointer;
            display: inline-block;
            position: relative;
            margin-right: 5px;
        }

            .table-operate > span::after {
                display: block;
                content: '';
                width: 1px;
                height: 10px;
                background-color: #e2e2e2;
                position: absolute;
                right: -5px;
                top: 5px;
            }

            .table-operate > span:last-child {
                margin-right: 0;
            }

                .table-operate > span:last-child::after {
                    background-color: #fff;
                }

        .defaultBtn {
            color: #3aadff;
        }

        .warmBtn {
            color: #ee9c33;
        }

        .dangerBtn {
            color: #fe6f4f;
        }

        .deletedRow {
            color: #ccc;
        }
        /*.defaultBtn:hover{color:#0095ff;}
        .warmBtn:hover{color:#ffd655;}
        .dangerBtn:hover{color:#ff0000;}*/

        .newBuildShare, .appendWillBill {
            width: 570px;
            padding: 10px 10px 0 10px;
        }

        .newBuildShare-ul, .addBuildShare-ul {
            border: 1px solid #e2e2e2;
            border-radius: 5px;
        }

            .newBuildShare-ul > li, .addBuildShare-ul > li {
                padding: 8px 5px;
            }

                .newBuildShare-ul > li > span, .addBuildShare-ul > li > span {
                    width: 150px;
                    text-align: right;
                    display: inline-block;
                }

                .newBuildShare-ul > li > input, .newBuildShare-ul > li > select, .addBuildShare-ul > li > input, .addBuildShare-ul > li > select {
                    width: 300px;
                    box-sizing: border-box;
                }

            .newBuildShare-ul #txtQty, .newBuildShare-ul #txtareRemark {
                position: relative;
                left: 3px;
            }

        .balance {
            color: #3aadff;
            font-weight: 700 !important;
            letter-spacing: 1px;
        }

        .setRemark {
            width: 500px;
            padding: 10px 10px 0 10px;
            box-sizing: border-box;
        }

        .setRemark-textarea {
            width: 100%;
            height: 180px;
            border: 1px solid #e2e2e2;
            box-sizing: border-box;
        }

        .setRemark-div {
            width: 100%;
        }

        .billAccountDetail {
            width: 900px;
            padding: 10px;
            box-sizing: border-box;
            max-height: 500px;
            overflow-y: auto;
        }

        .billAccountDetail-ul {
            border: 1px solid #e2e2e2;
            border-radius: 4px;
            padding: 5px 10px;
            margin-bottom: 10px;
        }

            .billAccountDetail-ul > li {
                width: 50%;
                float: left;
                padding: 5px 0;
            }

        .newBuildShare-ul-remark, .addBuildShare-ul-remark {
            display: flex;
        }

            .newBuildShare-ul-remark > textarea, .addBuildShare-ul-remark > textarea {
                width: 293px;
                border: 1px solid #e2e2e2;
                height: 50px;
            }

        .createShareAffinityMask {
            width: 520px;
            padding: 10px;
        }

            .createShareAffinityMask > ul {
                border: 1px solid #e2e2e2;
                border-radius: 4px;
                padding: 10px;
            }

                .createShareAffinityMask > ul > li {
                    padding: 5px;
                }

        .createShareAffinityMask-ul .my-btn, .createShareAffinityMask-ul .my-btn-warm {
            margin-left: 10px;
        }

        .newBuildShare-ul-shopName {
            margin-left: 150px;
            text-align: left;
            width: 400px;
            padding: 5px;
            display: inline-block;
        }

            .newBuildShare-ul-shopName > i {
                display: inline-block;
                padding: 0 5px;
                cursor: pointer;
                font-weight: 700;
                font-size: 15px;
                /* border-bottom: 1px; */
                margin-left: 5px;
                /* border: 1px solid; */
            }

                .newBuildShare-ul-shopName > i:nth-child(1) {
                    color: red;
                }

                .newBuildShare-ul-shopName > i:nth-child(2) {
                    color: green;
                }

        .remarkContent {
            word-wrap: break-word;
            word-break: break-all;
        }

        .operateLog-div {
            padding: 0 20px;
            max-height: 500px;
            overflow-y: auto;
        }

            .operateLog-div > div {
                padding: 10px;
                border-bottom: 1px dotted #e2e2e2;
                position: relative;
            }

                .operateLog-div > div::before {
                    display: block;
                    content: '';
                    width: 4px;
                    height: 4px;
                    border-radius: 50%;
                    background-color: #ccc;
                    position: absolute;
                    top: 18px;
                    left: -2px;
                }

        .operateLogs {
            width: 650px;
        }

        .logType {
            color: #000;
        }

        .logAppend, .logBalance {
            color: #3aadff;
            margin: 0 3px;
        }

        .Remark-span {
            position: relative;
        }

        .wrapperTime_input, .inputSelectTime {
            width: 160px;
        }

        .inputSelectTime {
            top: 9px;
        }

        .common-search {
            border: 1px solid #e2e2e2;
            padding: 10px 10px 20px 10px;
            margin-bottom: 15px;
        }

        .common-status {
            padding-bottom: 5px;
        }

            .common-status > span {
                color: #3aadff;
                font-weight: 700;
                padding: 0 5px;
            }

        #append_pagination, #used_pagination, #recycle_pagination {
            text-align: right;
        }

        .common_right_content05 .common-status .index_btn {
            padding: 3px 15px;
        }

        .common-status-bottom {
            position: relative;
            padding-top: 15px;
        }

        .common-status-a {
            position: absolute;
            top: 14px;
            right: 0;
            color: #3aadff;
        }

        .table-operate .stop {
            cursor: wait;
            color: #ccc;
        }

        dot {
            font-size: 14px;
            position: absolute;
            display: inline-block;
            text-indent: -1ch;
            vertical-align: bottom;
            overflow: hidden;
            animation: dot 2s infinite step-start both;
        }

        @@keyframes dot {
            33% {
                text-indent: 0;
            }

            66% {
                text-indent: -2ch;
            }
        }

        @@media screen and (min-width: 1440px) {
            .table-operate > span {
                margin-right: 10px;
            }

                .table-operate > span::after {
                    right: -8px;
                }
        }
        .moreNav-07 {
            padding: 8px 15px 9px 15px;
            color: #1295c1;
            font-size: 12px;
            font-weight: 400;
            cursor: pointer;
        }
        .more_right_title_nav > span:hover {
            color:#2ebae9
        }
    </style>
}

<div class="common_wrapper">
    @Html.Partial("~/Views/Common/leftNav.cshtml")
    <div class="common_right">

        <div class="more_right_title">
            <i class="more_right_title_icon"></i>
            <div class="more_right_title_nav" id="shareWayBillAccount_navs">
                <span data-type="1">新建单号分享</span>
                <span data-type="2">充值明细</span>
                <span data-type="3">使用明细</span>
                <span data-type="4">回收明细</span>
                <span data-type="5">单号归属查询</span>
                <span class="active" data-type="6">单号分享快递对账</span>
            </div>
            <a class="moreNav-07" href="/XcxManagement/" target="_blank">手机端分享</a>
        </div>

        @*快递对账*@
        <div class="common_right_main common_right_content06">
            <ul class="ShareWayBillAccount-explain">
                <li>1.将外部对账单数据导入店管家，系统自动匹配，确定每个运单号归属使用店铺；</li>
                <li>2.导入数据后，请耐心等待几分钟，您可以先去处理其它事情，匹配完成后，下载结果。</li>
            </ul>
            <div style="padding:10px 0">
                <span onclick="sharedWaybillAccountCheckModule.ImportAccountChecking(0)" class="index_btn" style="width:116px;">导入对账单</span>
                @if (DianGuanJiaApp.Services.SiteContext.Current.CurrentShopId == 111)
                {
                    <span onclick="sharedWaybillAccountCheckModule.ImportAccountChecking(1)" class="index_btn" style="width:116px;">导入查看轨迹</span>
                }
                <div style="display:none;"><input type="file" id="input-choice-file" name="upfile" size="1" /></div>
            </div>

            <table class="stockup_table_content">
                <thead>
                    <tr>
                        <th class="index" style="width:50px;">序号</th>
                        <th style="width:150px;">导入时间</th>
                        <th style="width:250px;">文件名</th>
                        <th style="width:130px;">导入数量</th>
                        <th style="width:130px;">匹配成功</th>
                        <th style="width:130px;">匹配不到</th>
                        <th style="width:130px;">状态</th>
                        <th style="width:150px;">操作</th>
                    </tr>
                </thead>
                <tbody id="tb_accountchecking_list">
                    <tr>
                        <td colspan="99">暂无数据</td>
                    </tr>
                    @*<tr>
                            <td>1</td>
                            <td>2019-12-26 09:42:13</td>
                            <td>EFS100026990266文件</td>
                            <td>688</td>
                            <td>-</td>
                            <td>-</td>
                            <td><span style="color:#fba721">进行中<dot>...</dot></span></td>
                            <td class="table-operate">
                                    <span class="defaultBtn">下载</span>
                                    <span class="dangerBtn">删除</span>
                            </td>
                        </tr>*@
            </table>
        </div>
    </div>
</div>

<div id="import_file_msg_after" style="display:none;padding: 20px;">
    <div style="font-size:14px;margin-bottom:10px">请指定导入的Excel中运单号在第几列？</div>
    <div>第<input type="text" style="width:40px;" id="txtColumnIndex" />列为运单号</div>
</div>

<div id="import_traces_file_msg_after" style="display:none;padding: 20px;">
    <div style="font-size:14px;margin-bottom:10px">请指定获取轨迹需要的信息。</div>
    <div style="font-size:14px;margin-bottom:10px">第<input type="text" style="width:40px;" id="txtColumnIndex_2" />列为运单号</div>
    <div style="font-size:14px;margin-bottom:10px">
        面单类型<select id="selWaybillType">
            <option value="0">===请选择电子面单类型===</option>
            <option value="2">拼多多电子面单</option>
            <option value="1">菜鸟电子面单</option>
            <option value="3">无界电子面单</option>
        </select>
    </div>
    <div style="font-size:14px;margin-bottom:10px">
        快递类型<select id="selExpress">
            <option value="0">===请选择快递类型===</option>
            @foreach (var item in (Model as List<DianGuanJiaApp.Data.Entity.ExpressCompany>))
            {
                <option value="@item.CompanyCode">@item.Names</option>
            }
        </select>
    </div>

</div>

<div class="minSpace"></div>
<script type="text/x-jsrender" id="tmpl_account_checking">
    <tr>
        <td>{{:#index+1}}</td>
        <td>{{:CreateTime}}</td>
        <td>{{:FileName}}</td>
        <td>{{:RowNumber>=0?RowNumber:'-'}}</td>
        <td>{{:MatchSucc>=0?MatchSucc:'-'}}</td>
        <td>{{:NotFound>=0?NotFound:'-'}}</td>
        {{if Status<0}}
        <td><span style="color:red;cursor:pointer;text-decoration:underline;" onclick="alertErrorMsg('{{:LogInfo}}')">对账错误</span></td>
        {{else Status ==0}}
        <!--待对账状态，显示对账中-->
        <td><span style="color:#7ef779">对账中<dot>...</dot></span></td>
        {{else Status ==1}}
        <td><span style="color:#fba721">对账中<dot>...</dot></span></td>
        {{else Status ==2}}
        <td><span style="color:green" onclick="alertErrorMsg('{{:LogInfo}}')">对账完成</span></td>
        {{else}}
        <td><span style="color:orangered">状态无法识别</span></td>
        {{/if}}
        <td class="table-operate">
            {{if MatchedDoneFile}}
            <span class="defaultBtn"><a href="{{:~rewriteUrl('https://16881.dgjapp.com/common/download')+'&filePath=' + ~encodeURI(MatchedDoneFile) + '&fileName=' + ~encodeURI('对账完成_'+FileName)}}" >下载</a></span>
            {{else}}
            <span class="defaultBtn" style="visibility:hidden;"><a href="#">下载</a></span>
            {{/if}}
            <span class="dangerBtn" onclick="sharedWaybillAccountCheckModule.DeleteTask('{{:Id}}')">删除</span>
        </td>
    </tr>
</script>
@section scripts{
    <script type="text/javascript">
        function downloadFile(path, name) {
            //onclick="downloadFile('{{:MatchedDoneFile}}','对账完成_{{:FileName}}')"
            var iframe = document.createElement("IFRAME");
            iframe.setAttribute("style", "display:none;")
            document.body.appendChild(iframe);

            var url = commonModule.rewriteUrl("https://16881.dgjapp.com/common/download");
            var urlStr = url + "&filePath=" + encodeURI(path) + "&fileName=" + encodeURI(name);

            iframe.src = urlStr;
            if (iframe.attachEvent) {
                //iframe.attachEvent("onload", function () {
                //    alert("Local iframe is now loaded.");
                //});
            } else {
                //iframe.onload = function () {
                //    alert("Local iframe is now loaded.");
                //};
            }
        }
        function alertErrorMsg(errorMsg) {
            layer.alert(errorMsg);
        }
    </script>
    @Scripts.Render("~/bundles/sharewaybillaccountcheck")

}
