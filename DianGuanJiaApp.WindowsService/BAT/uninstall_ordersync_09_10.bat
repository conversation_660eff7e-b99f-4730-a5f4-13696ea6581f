::ж�ط���
::===================================
::�������Աʡ�����У�����ɾ��
@echo off
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"
if '%errorlevel%' NEQ '0' (
    echo "�������ԱȨ��..."
    goto UACPrompt
) else ( goto gotAdmin )
:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\getadmin.vbs"
    "%temp%\getadmin.vbs"
    exit /B
:gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"
    CD /D ..
::===================================
echo %cd%           
echo ===============================================
DianGuanJiaApp.WindowsService.exe uninstall -servicename ��ܼҶ�������ͬ������_09 -displayname ��ܼҷ���1.0_��������ͬ������09 -description ��ܼҷ���1.0_��������ͬ������09
timeout /t 1
echo ===============================================
DianGuanJiaApp.WindowsService.exe uninstall -servicename ��ܼҶ�������ͬ������_10 -displayname ��ܼҷ���1.0_��������ͬ������10 -description ��ܼҷ���1.0_��������ͬ������10           
pause