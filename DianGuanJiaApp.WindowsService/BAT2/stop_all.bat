::��װ����
::===================================
::�������Ա������У�����ɾ��
@echo off
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"
if '%errorlevel%' NEQ '0' (
    echo "�������ԱȨ��..."
    goto UACPrompt
) else ( goto gotAdmin )
:UACPrompt
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\getadmin.vbs"
    "%temp%\getadmin.vbs"
    exit /B
:gotAdmin
    if exist "%temp%\getadmin.vbs" ( del "%temp%\getadmin.vbs" )
    pushd "%CD%"
    CD /D "%~dp0"
    CD /D ..
::===================================           
echo %cd%        
DianGuanJiaApp.WindowsService.exe stop -servicename ��ܼҷ�����ʷ����������ط��� -displayname ��ܼҷ���1.0_������ʷ����������ط��� -description ��ܼҷ���1.0_������ʷ����������ط���
timeout /t 1
echo ===============================================
DianGuanJiaApp.WindowsService.exe stop -servicename ��ܼҲ������δ洢����_01 -displayname ��ܼҷ���1.0_�������δ洢����01 -description ��ܼҷ���1.0_�������δ洢����01
timeout /t 1
echo ===============================================
DianGuanJiaApp.WindowsService.exe stop -servicename ��ܼҲ������δ洢����_02 -displayname ��ܼҷ���1.0_�������δ洢����02 -description ��ܼҷ���1.0_�������δ洢����02
timeout /t 1
echo ===============================================
DianGuanJiaApp.WindowsService.exe stop -servicename ��ܼҲ������δ洢����_03 -displayname ��ܼҷ���1.0_�������δ洢����03 -description ��ܼҷ���1.0_�������δ洢����03
timeout /t 1
echo ===============================================
DianGuanJiaApp.WindowsService.exe stop -servicename ��ܼҲ������δ洢����_04 -displayname ��ܼҷ���1.0_�������δ洢����04 -description ��ܼҷ���1.0_�������δ洢����04
timeout /t 1
echo ===============================================
DianGuanJiaApp.WindowsService.exe stop -servicename ��ܼҵ���API���ݴ洢����_01 -displayname ��ܼҷ���1.0_����API���ݴ洢����01 -description ��ܼҷ���1.0_����API���ݴ洢����01
timeout /t 1
echo ===============================================
DianGuanJiaApp.WindowsService.exe stop -servicename ��ܼҵ���API���ݴ洢����_02 -displayname ��ܼҷ���1.0_����API���ݴ洢����02 -description ��ܼҷ���1.0_����API���ݴ洢����02
timeout /t 1
echo ===============================================
DianGuanJiaApp.WindowsService.exe stop -servicename ��ܼҷ�����¼����������� -displayname ��ܼҷ���1.0_������¼����������� -description ��ܼҷ���1.0_������¼�����������
timeout /t 1
echo ===============================================
DianGuanJiaApp.WindowsService.exe stop -servicename ��ܼҷ�����¼��������_01 -displayname ��ܼҷ���1.0_������¼��������01 -description ��ܼҷ���1.0_������¼��������01
timeout /t 1
echo ===============================================
DianGuanJiaApp.WindowsService.exe stop -servicename ��ܼҷ�����¼��������_02 -displayname ��ܼҷ���1.0_������¼��������02 -description ��ܼҷ���1.0_������¼��������02
pause