using DianGuanJiaApp.Core.Helpers;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Data.Repository;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services.DataSyncStatus;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.WindowsService.ViewModel;
using Nest;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;

namespace DianGuanJiaApp.WindowsService.Service
{
	public class ClearDataSyncStatusWinService : BaseService
	{
		private readonly Timer _timer;
		private static bool _isRunning = false;

		public ClearDataSyncStatusWinService(string serviceName)
		{
            base.InitHeart(serviceName);
            Log.WriteLine($"ClearDataSyncStatusWinService启动");
			//简单定时执行
			_timer = new Timer(1000 * CustomerConfig.ClearDataSyncStatusElapsedSeconds)
			{
				AutoReset = true
			};
			_timer.Elapsed += Executed;
		}

		private void Executed(object sender, ElapsedEventArgs e)
		{
			try
			{
                //更新心跳
                ExceptionHandler(() =>
                {
                    SendHeartbeat(); //发送心跳
                }, "清理DataSyncStatus服务-心跳异常");

                if (_isRunning)
				{
					return;
				}

				//每天凌晨1点到5点之间执行
				if (DateTime.Now.Hour < 1 || DateTime.Now.Hour >= 5)
				{
					return;
				}

                Log.WriteLine($"开始执行");
                _isRunning = true;
				//获取要清理的店铺数据
				//先查redis有没有值，有值就增量删，否则全量
				string cacheKey = $"ClearDataSyncStatusTime:{CustomerConfig.CloudPlatformType}";

				//查询时间
				DateTime nowTime = DateTime.Now;
				string startTime = RedisHelper.Get(cacheKey);
				string endTime = nowTime.AddDays(-5).ToString("yyyy-MM-dd HH:mm:ss");
				
				//平台类型
				List<string> platformTypes = new List<string>();
				if (CustomerConfig.CloudPlatformType == PlatformType.Pinduoduo.ToString())
				{
					platformTypes.Add(PlatformType.Pinduoduo.ToString());
					platformTypes.Add(PlatformType.KuaiTuanTuan.ToString());
				}
				else if (CustomerConfig.CloudPlatformType == PlatformType.Jingdong.ToString())
				{
					platformTypes.Add(PlatformType.Jingdong.ToString());
				}
				else if (CustomerConfig.CloudPlatformType == PlatformType.TouTiao.ToString())
				{
					platformTypes.Add(PlatformType.TouTiao.ToString());
				}
				else
				{
					platformTypes.Add(PlatformType.TouTiao.ToString());
					platformTypes.Add(PlatformType.Pinduoduo.ToString());
					platformTypes.Add(PlatformType.Jingdong.ToString());
					platformTypes.Add(PlatformType.KuaiTuanTuan.ToString());
				}

				//分页
				int pageIndex = 1;
				int pageSize = 100;
				
				var dataSyncStatus = new DataSyncStatusService();

				while (true)
				{
					if (DateTime.Now.Hour < 1 || DateTime.Now.Hour >= 5)
					{
						break;
					}

					var shopList = new SyncStatusService().GetListByLastSyncTime(pageIndex, pageSize, startTime, endTime, platformTypes);
					if (shopList == null || shopList.Count == 0)
					{
						//存储当次执行的时间，下次增量删,冗余十分钟
						RedisHelper.Set(cacheKey, endTime.ToDateTime()?.AddMinutes(-10).ToString("yyyy-MM-dd HH:mm:ss"));
						break;
					}


                    Log.WriteLine($"当前清理startTime={startTime}，endTime={endTime}，pageIndex={pageIndex}，pageSize={pageSize}，platformTypes={string.Join(",", platformTypes)}，ShopIds={string.Join(",", shopList.Select(s => s.ShopId))}");

                    //删除数据
                    dataSyncStatus.DelDataSyncStatusByShopIds(shopList.Select(s=>s.ShopId).ToList());

					if (shopList.Count < pageSize)
					{
						//存储当次执行的时间，下次增量删,冗余十分钟
						RedisHelper.Set(cacheKey, endTime.ToDateTime()?.AddMinutes(-10).ToString("yyyy-MM-dd HH:mm:ss"));
						break;
					}

					pageIndex += 1;
				}

                Log.WriteLine($"本次执行完成");
				_isRunning = false;
			}
			catch (Exception ex)
			{
				Log.WriteError($"清理fendan_data_syncstatus表数据发生异常：\r\n{ex}");
				_isRunning = false;
			}
			
		}

		public void Start()
		{
			//服务运行开始日志
			var runLogView = new RunLogView
			{
				ProcessType = "Start",
				ServiceType = GetType().Name,
				ExecuteTime = DateTime.Now,
				HostIp = HostHelper.IpAddress(),
				HostName = Environment.MachineName
			};
			WriteLog(LogStoreNames.ServiceRun, runLogView);
			//开始定时时间
			_timer.Start();

			//手动执行一次
			//Executed(null,null);
		}

		public void Stop()
		{
			//服务运行停止日志
			var runLogView = new RunLogView
			{
				ProcessType = "Stop",
				ServiceType = GetType().Name,
				ExecuteTime = DateTime.Now,
				HostIp = HostHelper.IpAddress(),
				HostName = Environment.MachineName
			};
			WriteLog(LogStoreNames.ServiceRun, runLogView);
			//停止定时时间
			_timer.Stop();
		}
	}
}
