using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.WindowsService.ViewModel;
using System;
using System.Timers;
using DianGuanJiaApp.Utility.Helpers;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Services.Services;
using DianGuanJiaApp.RabbitMQ;
using DianGuanJiaApp.ViewModels.Models;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Data.Extension;
using DianGuanJiaApp.Data.Repository;
using System.Diagnostics;
using System.Collections.Concurrent;
using DianGuanJiaApp.Data.Model.LogModel;
using System.Text;

namespace DianGuanJiaApp.WindowsService.Service
{
    public class CompensatePurchaseOrderAndSkuMappingService : BaseService
    {
        private readonly Timer _timer;
        private static bool _isRunning = false;

        public CompensatePurchaseOrderAndSkuMappingService(string serviceName)
        {
            base.InitHeart(serviceName);
            Log.WriteLine($"CompensatePurchaseOrderAndSkuMappingService启动");
            var minutes = CustomerConfig.CompensatePurchaseOrderAndSkuMappingMinutes;
            //简单定时执行
            _timer = new Timer(1000 * 60 * minutes)
            {
                AutoReset = true
            };
            _timer.Elapsed += Executed;
        }

        private void Executed(object sender, ElapsedEventArgs e)
        {
            ExceptionHandler(() =>
            {
                //更新心跳
                ExceptionHandler(() =>
                {
                    SendHeartbeat(); //发送心跳
                }, "1688补偿采购单数据-心跳异常");

                //扫描所有当前云平台下的分单数据库
                if (_isRunning)
                {
                    Log.WriteLine("前一个任务还未执行完成，等待下次触发");
                    return;
                }
                _isRunning = true;
                var message = $"";
                var cpt = CustomerConfig.CloudPlatformType;
                //查询所有数据库
                var dbRp = new DbConfigRepository();
                var dbConfigs = dbRp.GetAllDbConfigsByCloudPlatform()?.Where(x => x.DbServer.RunningStatus == "Running" && x.DbNameConfig.RunningStatus == "Running" && x.DbNameConfig.DbName.Contains("fendan")).ToList();
                var totalCount = dbConfigs.Count();
                var sw = new Stopwatch();
                sw.Start();
                message = $"【开始】1688补偿采购单数据及Sku映射数据，查询到共有：【{totalCount}】个数据库";
                Log.WriteLine(message);
                var totalPorCount = 0;
                var totalDpsmCount = 0;
                for (int i = 0; i < dbConfigs.Count(); i++)
                {
                    var dbConfig = dbConfigs[i];
                    Log.WriteLine($"【{(i + 1)}/{totalCount}】1688补偿采购单数据及Sku映射数据，当前库：{dbConfig.DbNameConfig.DbName}，开始");
                    try
                    {
                        var purService = new PurchaseOrderRelationService(dbConfig.ConnectionString);
                        var batchId = Guid.NewGuid().ToString().ToShortMd5();
                        //补偿采购关系
                        var porCount = purService.CompensatePurchaseOrderRelation(batchId);
                        totalPorCount += porCount;

                        //补偿Sku映射关系
                        var dpsmCount = new DistributorProductSkuMappingService(dbConfig.ConnectionString).CompensateDistributorProductSkuMapping(batchId);
                        totalDpsmCount += dpsmCount;

                        Log.WriteLine($"【{(i + 1)}/{totalCount}】1688补偿采购单数据及Sku映射数据，当前库：{dbConfig.DbNameConfig.DbName}，采购关系：{porCount}条，Sku映射关系：{dpsmCount}条，完成");
                    }
                    catch (Exception ex)
                    {
                        Log.WriteLine($"【{(i + 1)}/{totalCount}】1688补偿采购单数据及Sku映射数据，当前库：{dbConfig.DbNameConfig.DbName}，发生异常：\r\n{ex}");
                    }
                }
                sw.Stop();
                var sbMessage = new StringBuilder($"【结束】1688补偿采购单数据及Sku映射数据，查询到共有：【{totalCount}】个数据库，采购关系共：{totalPorCount}条，Sku映射关系共：{totalDpsmCount}条，耗时：{sw.Elapsed.TotalSeconds}秒");
                message = sbMessage.ToString();
                Log.WriteLine(message);

            }, "1688补偿采购单数据及Sku映射数据");
            _isRunning = false;
        }

        public void Start()
        {
            //服务运行开始日志
            var runLogView = new RunLogView
            {
                ProcessType = "Start",
                ServiceType = GetType().Name,
                ExecuteTime = DateTime.Now,
                HostIp = HostHelper.IpAddress(),
                HostName = Environment.MachineName
            };
            WriteLog(LogStoreNames.ServiceRun, runLogView);
            //开始定时时间
            _timer.Start();
        }

        public void Stop()
        {
            //服务运行停止日志
            var runLogView = new RunLogView
            {
                ProcessType = "Stop",
                ServiceType = GetType().Name,
                ExecuteTime = DateTime.Now,
                HostIp = HostHelper.IpAddress(),
                HostName = Environment.MachineName
            };
            WriteLog(LogStoreNames.ServiceRun, runLogView);
            //停止定时时间
            _timer.Stop();
        }
    }

}
