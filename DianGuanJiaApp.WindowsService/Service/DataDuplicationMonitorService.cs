using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.WindowsService.ViewModel;
using System;
using System.Timers;
using DianGuanJiaApp.Utility.Helpers;
using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Services.Services.DataDuplication;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.WindowsService.Service
{
    public class DataDuplicationMonitorService : BaseService
    {
        private readonly Timer _timer;

        /// <summary>
        /// 默认最大剩余数
        /// </summary>
        private static readonly Dictionary<DataChangeTableTypeName, long> DefaultMaxRemainders =
            new Dictionary<DataChangeTableTypeName, long>
            {
                { DataChangeTableTypeName.Product, 500 },
                { DataChangeTableTypeName.LogicOrder, 6000 },
                { DataChangeTableTypeName.AfterSaleOrder, 500 },
                { DataChangeTableTypeName.PurchaseOrderRelation, 500 }
            };
        /// <summary>
        /// 默认最大剩余数（灰度和测试环境）
        /// </summary>
        private static readonly Dictionary<DataChangeTableTypeName, long> DefaultGrayMaxRemainders =
            new Dictionary<DataChangeTableTypeName, long>
            {
                { DataChangeTableTypeName.Product, 200 },
                { DataChangeTableTypeName.LogicOrder, 1000 },
                { DataChangeTableTypeName.AfterSaleOrder, 200 },
                { DataChangeTableTypeName.PurchaseOrderRelation, 200 }
            };

        public DataDuplicationMonitorService(string serviceName)
        {
            //简单定时执行
            _timer = new Timer(1000 * 60 * ConfigView.HealthMonitorHeartbeatTime)
            {
                AutoReset = true
            };
            _timer.Elapsed += Executed;
        }

        private void Executed(object sender, ElapsedEventArgs e)
        {
            var messagePrefix = GetMessagePrefix();

            ExceptionHandler(() =>
            {
                //复制副本分类列表
                var typeNames = new List<KeyValuePair<DataChangeTableTypeName, string>>
                {
                    new KeyValuePair<DataChangeTableTypeName, string>(DataChangeTableTypeName.Product, "商品"),
                    new KeyValuePair<DataChangeTableTypeName, string>(DataChangeTableTypeName.LogicOrder, "逻辑单"),
                    new KeyValuePair<DataChangeTableTypeName, string>(DataChangeTableTypeName.AfterSaleOrder, "售后单"),
                    //new KeyValuePair<DataChangeTableTypeName, string>(DataChangeTableTypeName.PurchaseOrderRelation,
                    //    "采购单关系")
                };
               
                //需要发送的消息
                var messages = new List<string>();
                typeNames.ForEach(m =>
                {
                    var service = DuplicationFactoryService.Instance(m.Key);
                    var maxRemainders = service.GetMaxGetRemainders();
                    if (maxRemainders <= 0)
                    {
                        maxRemainders = string.IsNullOrWhiteSpace(CustomerConfig.DataDuplicationEnvironment) ||
                                        CustomerConfig.DataDuplicationEnvironment == "production"
                            ? DefaultMaxRemainders[m.Key]
                            : DefaultGrayMaxRemainders[m.Key];
                    }

                    var remainders = service.GetRemainders();
                    if (remainders < maxRemainders)
                    {
                        return;
                    }
                    var totals = service.GetCounter(CacheFieldKeys.Totals);
                    var processings = service.GetProcessings();
                    var fails = service.GetCounter(CacheFieldKeys.Fails);
                    //消息格式
                    var message =
                        $"{m.Value}复制副本，总数：{totals}，已处理数：{totals - remainders}，正处理中数：{processings}，剩余数：{remainders}，失败数：{fails}";
                     messages.Add(message);
                });
                if (messages.Any() == false)
                {
                    return;
                }
                //消息前缀信息
                var prefixMessage = $"{messagePrefix}复制副本监控数据如下({CustomerConfig.CloudPlatformType}-{Environment.MachineName})：";
                messages.InsertRange(0, new List<string> { prefixMessage });
                //换行
                var messageBody = string.Join("\n", messages);
                SendRobotMessage(RobotUrls.DataDuplicationRobotUrl, messageBody, true);
            }, $"{messagePrefix}复制副本监控服务异常日志信息");
        }
        /// <summary>
        /// 消息前缀
        /// </summary>
        /// <returns></returns>
        private string GetMessagePrefix()
        {
            switch (CustomerConfig.DataDuplicationEnvironment)
            {
                case DataDuplicationEnvironment.Backup:
                    return "【备库】";
                case DataDuplicationEnvironment.Gray:
                    return "【灰度】";
                case DataDuplicationEnvironment.Test:
                    return "【测试】";
                default:
                    return string.Empty;
            }
        }

        public void Start()
        {
            //服务运行开始日志
            var runLogView = new RunLogView
            {
                ProcessType = "Start",
                ServiceType = GetType().Name,
                ExecuteTime = DateTime.Now,
                HostIp = HostHelper.IpAddress(),
                HostName = Environment.MachineName
            };
            WriteLog(LogStoreNames.ServiceRun, runLogView);
            //开始定时时间
            _timer.Start();
        }

        public void Stop()
        {
            //服务运行停止日志
            var runLogView = new RunLogView
            {
                ProcessType = "Stop",
                ServiceType = GetType().Name,
                ExecuteTime = DateTime.Now,
                HostIp = HostHelper.IpAddress(),
                HostName = Environment.MachineName
            };
            WriteLog(LogStoreNames.ServiceRun, runLogView);
            //停止定时时间
            _timer.Stop();
        }
    }
}