using System.Collections.Generic;
using System.Linq;
using DianGuanJiaApp.WindowsService.ViewModel;
using System.Timers;
using DianGuanJiaApp.Data.Enum;
using System;
using DianGuanJiaApp.Services.Services.UniversalModule;
using DianGuanJiaApp.Utility;

namespace DianGuanJiaApp.WindowsService.Service
{
    public class DataSyncStorageHealthMonitorWinService : BaseServiceV2
    {
        private readonly Timer _timer;
        /// <summary>
        /// 默认最大剩余数
        /// </summary>
        private static readonly Dictionary<string, long> DefaultMaxRemainders =
            new Dictionary<string, long>
            {
                { MessageSyncBusinessTypes.LogicOrderColdHotStorage, 200 }
            };
        /// <summary>
        /// 默认最大剩余数（灰度和测试环境）
        /// </summary>
        private static readonly Dictionary<string, long> DefaultGrayMaxRemainders =
            new Dictionary<string, long>
            {
                { MessageSyncBusinessTypes.LogicOrderColdHotStorage, 100 }
            };
        public DataSyncStorageHealthMonitorWinService(string serviceName)
        {
            //简单定时执行
            _timer = new Timer(1000 * 60 * ConfigView.HealthMonitorHeartbeatTime)
            {
                AutoReset = true
            };
            _timer.Elapsed += Executed;
        }
        /// <summary>
        /// 服务开始
        /// </summary>
        public void Start()
        {
            Start(ct =>
            {
                //开始定时时间
                _timer.Start();
            });
        }

        private void Executed(object sender, ElapsedEventArgs e)
        {
            ExceptionHandler(() =>
            {
                //按业务类型维度发送健康监控
                var businessTypeNames = new List<KeyValuePair<string, string>>
                {
                    new KeyValuePair<string, string>(MessageSyncBusinessTypes.LogicOrderColdHotStorage, "逻辑单冷热分离存储")
                };
                //需要发送的消息
                var messages = new List<string>();
                //消息
                businessTypeNames.ForEach(m =>
                {
                    
                    var service = new DataSyncStorageHealthTraceService(m.Key);
                    var maxRemainders = service.MaxRemainders;
                    if (maxRemainders <= 0)
                    {
                        maxRemainders = string.IsNullOrWhiteSpace(CustomerConfig.SyncServiceEnvironment)
                            ? DefaultMaxRemainders[m.Key]
                            : DefaultGrayMaxRemainders[m.Key];
                    }
                    var remainders = service.Remainders;
                    if (remainders < maxRemainders)
                    {
                        return;
                    }
                    var totals = service.Totals;
                    var processings = service.Processings;
                    var fails = service.Fails;
                    //消息格式
                    var message =
                        $"{m.Value}，总数：{totals}，已处理数：{totals - remainders}，正处理中数：{processings}，剩余数：{remainders}，失败数：{fails}";

                    messages.Add(message);
                });
                if (messages.Any() == false)
                {
                    return;
                }
                //消息前缀信息
                var prefixMessage = $"消息数据同步存储监控数据如下({CustomerConfig.CloudPlatformType}-{Environment.MachineName})：";
                messages.InsertRange(0, new List<string> { prefixMessage });
                //换行
                var messageBody = string.Join("\n", messages);
                //发送机器人消息
                SendRobotMessage(RobotUrls.DataDuplicationRobotUrl, messageBody, true);
            });
        }
    }
}