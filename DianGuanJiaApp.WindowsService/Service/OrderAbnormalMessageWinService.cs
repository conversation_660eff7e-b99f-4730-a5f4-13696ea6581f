using System.Threading.Tasks;
using System.Timers;
using DianGuanJiaApp.Services.Services.OrderModule;

namespace DianGuanJiaApp.WindowsService.Service
{
    public class OrderAbnormalMessageWinService : BaseServiceV2
    {
        private readonly Timer _timer;

        public OrderAbnormalMessageWinService(string serviceNo)
        {
            base.InitHeart(serviceNo);
            ServiceNo = serviceNo;
            _timer = new Timer(1000 * 2) { AutoReset = true };
            _timer.Elapsed += new ElapsedEventHandler((s, e) =>
            {
                ExceptionHandler(() => SendHeartbeat(), "异常订单消息处理服务 -心跳异常");
            });
        }

        /// <summary>
        /// 服务开始
        /// </summary>
        public void Start()
        {
            _timer.Start();
            Start(ct =>
            { 
                //不阻塞主线程
                Task.Run(OrderAbnormalMessageService.ConsumerListenMessage, ct);
            });
        }

        public new void Stop()
        {
            if (string.IsNullOrWhiteSpace(ServiceNo))
            {
                ServiceNo = "01";
            }
            WriteRunLog(ServiceNo, "Stop");
            _cancellationTokenSource.Cancel();
            _timer.Stop();
        }
    }
}