using System;
using System.Collections.Generic;
using System.Timers;
using CSRedis;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Services.Services.Tools;
using DianGuanJiaApp.Utility;
using Timer = System.Timers.Timer;

namespace DianGuanJiaApp.WindowsService.Service
{
    public class OrderSyncAnalysisPushTaskWinService : BaseServiceV2
    {
        private readonly System.Timers.Timer _timer;
        private readonly OrderSyncAnalysisService _orderSyncAnalysisService;
        public OrderSyncAnalysisPushTaskWinService(string serviceName)
        {
            base.InitHeart(serviceName);

            _orderSyncAnalysisService = new OrderSyncAnalysisService();
            //简单定时执行
            _timer = new System.Timers.Timer(1000 * 60 * 2)
            {
                AutoReset = true
            };
            _timer.Elapsed += Executed;
        }
        /// <summary>
        /// 服务开始
        /// </summary>
        public void Start()
        {
            Start(ct =>
            {
                //开始定时时间
                _timer.Start();
            });
        }

        private void Executed(object sender, ElapsedEventArgs e)
        {
            //更新心跳
            ExceptionHandler(() =>
            {
                SendHeartbeat(); //发送心跳
            }, "订单同步分析任务推送服务-心跳异常");

            //系统维护时间控制
            var time = new CommonSettingService().GetMaintenanceTime();
            if (time > DateTime.Now)
            {
                System.Threading.Thread.Sleep(5000);
                return;
            }
            
            ExceptionHandler(() =>
            {
                //时间范围控制
                if (IsTimeRangeByNowTime() == false)
                {
                    return;
                }
                //执行
                ExecutePushTask();
            });
        }

        /// <summary>
        /// 执行推送任务
        /// </summary>
        private void ExecutePushTask()
        {
            //每天只执行一次
            var nowDate = DateTime.Now.ToString("yyyyMMdd");
            var key = $"{CacheKeys.OrderSyncAnalysisPushTaskLock}:{nowDate}";
            if (RedisHelper.Set(key, nowDate, TimeSpan.FromHours(7), RedisExistence.Nx))
            {
                //同步类型（订单同步：1，售后单同步：10）
                var syncTypes = new List<int> { 1 };
                //推送任务
                syncTypes.ForEach(syncType => { _orderSyncAnalysisService.PushMessages(syncType); });
            }
        }

        /// <summary>
        /// 当前时间是否在指定时间范围
        /// </summary>
        /// <returns></returns>
        private static bool IsTimeRangeByNowTime()
        {
            //return true;
            var nowTime = DateTime.Now;
            var startTime = Convert.ToDateTime(nowTime.ToString("yyyy-MM-dd 00:01:00"));
            var endTime = Convert.ToDateTime(nowTime.ToString("yyyy-MM-dd 01:00:00"));
            return nowTime >= startTime && nowTime <= endTime;
        }
    }
}