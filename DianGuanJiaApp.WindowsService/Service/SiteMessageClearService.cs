using DianGuanJiaApp.Core.Helpers;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services.Services.MessageQueue;
using DianGuanJiaApp.SiteMessage.Model.Request;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.WindowsService.ViewModel;
using System;
using System.Timers;

namespace DianGuanJiaApp.WindowsService.Service
{
    class SiteMessageClearService : BaseService
    {
        private readonly Timer _timer;

        public SiteMessageClearService(string serviceName)
        {
            base.InitHeart(serviceName);
            var time = CustomerConfig.SiteMessageClearMonitorHour;
            //DateTime targetTime = DateTime.Now.AddMinutes(1);
            DateTime targetTime = new DateTime(
                DateTime.Now.Year,
                DateTime.Now.Month,
                DateTime.Now.Day, time.Item1, time.Item2, time.Item3); // 设置目标时间为每天02:00:00
            double interval = (targetTime - DateTime.Now).TotalMilliseconds;
            if (interval < 0)
            {
                targetTime = targetTime.AddDays(1);
                interval = (targetTime - DateTime.Now).TotalMilliseconds;
            }
            _timer = new Timer(interval) { AutoReset = true };
            _timer.Elapsed += Executed;
        }

        private void Executed(object sender, ElapsedEventArgs e)
        {
            ExceptionHandler(() =>
            {
                //更新心跳
                ExceptionHandler(() =>
                {
                    SendHeartbeat(); //发送心跳
                }, "清除站内信-心跳异常");

                Log.WriteLine($"每天定时清除过期消息，清除时间：{DateTime.Now}");
                var res = new SiteMessageService().ClearMessage(new MessageClearRequest());
                Log.WriteLine($"每天定时清除过期消息，成功,成功条数{res.SuccessCount}");
            }, "站内信息过期信息清除");
        }

        public void Start()
        {
            var runLogView = new RunLogView
            {
                ProcessType = "Start",
                ServiceType = GetType().Name,
                ExecuteTime = DateTime.Now,
                HostIp = HostHelper.IpAddress(),
                HostName = Environment.MachineName
            };
            WriteLog(LogStoreNames.SiteMessageClearLog, runLogView);
            _timer.Start();
        }

        public void Stop()
        {
            var runLogView = new RunLogView
            {
                ProcessType = "Stop",
                ServiceType = GetType().Name,
                ExecuteTime = DateTime.Now,
                HostIp = HostHelper.IpAddress(),
                HostName = Environment.MachineName
            };
            WriteLog(LogStoreNames.SiteMessageClearLog, runLogView);
            _timer.Stop();
        }
    }
}
