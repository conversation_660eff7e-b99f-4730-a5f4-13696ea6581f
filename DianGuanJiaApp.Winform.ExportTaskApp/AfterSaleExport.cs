using DianGuanJiaApp.Data.Entity;
using DianGuanJiaApp.Services;
using DianGuanJiaApp.Utility.Extension;
using DianGuanJiaApp.Utility.NPOI;
using DianGuanJiaApp.Utility.Other;
using NPOI.HSSF.UserModel;
using NPOI.HSSF.Util;
using NPOI.SS.UserModel;
using NPOI.SS.Util;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using DianGuanJiaApp.Utility.Net;
using DianGuanJiaApp.Utility;
using DianGuanJiaApp.Data.Model;
using DianGuanJiaApp.Data.Enum;
using DianGuanJiaApp.Services.BaseProduct;
using DianGuanJiaApp.Data;

namespace DianGuanJiaApp.Winform.ExportTaskApp
{
    public partial class ExportExcelTasks
    {
        #region 分单系统-售后订单
        public void BuildFxAfterSaleExcel(ExportTask task)
        {
            var sw = new Stopwatch();
            var _afterSaleOrderService = new AfterSaleOrderService();

            //初始化上下文
            var fxUserId = task.UserId.ToInt();
            var fxUser = new UserFxService().GetUserFxs(new List<int> { fxUserId }).FirstOrDefault();
            var subFxUserId = task.SubFxUserId.ToInt();
            SubUserFx subUserFx = null;
            if (subFxUserId > 0)
                subUserFx = new SubUserFxService().Get(subFxUserId);
            if (task.ExtField5.IsNullOrEmpty())
                new SiteContext(fxUser,subUserFx:subUserFx);
            else
                new SiteContext(fxUser, FxBuildExccelService.GetDecryptDbName(task.ExtField5),subUserFx:subUserFx);

            var isTikTokPlatform = !string.IsNullOrWhiteSpace(task.ExtField6) && task.ExtField6 == PlatformType.TikTok.ToString();
            ///线程站点上下文赋值为跨境
            if (isTikTokPlatform)
                BaseSiteContext.SetIsCrossBorder(true);

            // 是否开启基础商品归一设置
            var isQueryBaseProductInfo = SiteContext.Current.BaseProductSetting.OrderCombine;

            var list = new List<AfterSalePageResult>();//结果集


            //取消或异常，终止订单查询  
            if (IsErrorOrCancelTask(out task, task, true))
                return;

            #region 查询数据共试3次
            var model = task.ParamJson.ToObject<AfterSaleOrderQuery>();
            model.PageIndex = task.PageIndex;
            model.PageSize = task.PageSize;
            var fromModule = task.FromModule;
            if (isQueryBaseProductInfo) model.IsBaseProduceCombine = true;

            var totalCount = 0;
            var hasMoreData = true;

            while (hasMoreData)
            {
                var sw1 = new Stopwatch();
                sw1.Start();
                //查询重试3次
                for (var i = 0; i < 3; i++)
                {
                    try
                    {
                        model.IsExport = true;
                        var result = _afterSaleOrderService.GetAfterSaleOrderList(model, true);

                        var pgList = result.Item2;
                        list.AddRange(pgList);

                        // 获取总数并判断是否还有下一页
                        totalCount = result.Item1;
                        hasMoreData = model.PageIndex * model.PageSize < totalCount;

                        model.PageIndex++;
                        break;
                    }
                    catch (Exception ex)
                    {
                        if (i >= 2)
                        {
                            task.Message = $"导出售后单异常：{ex}";
                            task.Status = -1;
                            _taskService.SimpleUpdate(task);
                            throw ex;
                        }
                        task.RetryTimes++;
                        task = _taskService.UpdateRetryTimes(task);
                        Thread.Sleep(i * 500);
                    }
                    sw1.Stop();
                    _frm.WriteLine($"--------【{fromModule}】导出任务【{task.Id}-{task.ShopId}】查询第【{task.PageIndex - 1}】页耗时：{sw1.Elapsed.TotalSeconds}s");
                }

                //更新查询页码  
                task.PageIndex++;
                task.RetryTimes = 0;

                //重新获取查询条件，预防方法中修改
                model = task.ParamJson.ToObject<AfterSaleOrderQuery>();
                model.PageIndex = task.PageIndex;
                model.PageSize = task.PageSize;
                task = _taskService.UpdatePageIndex(task);

                Thread.Sleep(500);
            }
            #endregion

            // 替换基础商品信息
            if (isQueryBaseProductInfo)
            {
                try
                {
                    _afterSaleOrderService.ReplaceOrder(list);
                }
                catch (Exception e)
                {
                    Log.WriteError($"【{task.Id}】售后单替换基础商品信息异常：{e}");
                }
            }

            var guid = Guid.NewGuid().ToString().Replace("-", "").ToLower();
            var fileName = $"售后单-{guid}-{task.Id}.xlsx";
            string errorMessage = string.Empty;
            IWorkbook workbook = FxBuildExccelService.AfterSaleOrderBuildExcel(list, fileName);
            SaveAndUploadFile(task, workbook, fileName, sw, ref errorMessage);
        }

        #endregion


        /// <summary>
        /// 保存并上传文件
        /// </summary>
        /// <param name="task"></param>
        /// <param name="workbook"></param>
        /// <param name="fileName"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        public void SaveAndUploadFile(ExportTask task, IWorkbook workbook, string fileName, Stopwatch sw, ref string errorMessage)
        {
            string lastFilePath = "";
            errorMessage = "";
            FileStream fs = null;
            try
            {
                if (string.IsNullOrEmpty(fileName))
                    fileName = $"{Guid.NewGuid().ToString()}.xls";
                string filePath = $@"{ExportExcelDirectory + fileName}";

                fs = new FileStream(filePath, FileMode.OpenOrCreate, FileAccess.ReadWrite);
                workbook.Write(fs);
                lastFilePath = filePath;

                task.Status = 3;
                task = _taskService.UpdateStatus(task);
            }
            catch (Exception ex)
            {
                lastFilePath = "";
                errorMessage = ex.ToString();
            }
            finally
            {
                if (fs != null)
                {
                    fs.Dispose();
                    fs.Close();
                }
                if (workbook != null)
                    workbook = null;
            }

            #region Excel上传到服务器 并更新任务状态
            if (!string.IsNullOrEmpty(lastFilePath))
                UploadedThenUpdateTask(ref task, task.FromModule, sw, fileName, lastFilePath);
            else
                Log.WriteError($"【{task.FromModule}】生成文件错误：{errorMessage}");

            #endregion
        }

    }

}
