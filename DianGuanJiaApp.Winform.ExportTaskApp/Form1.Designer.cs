namespace DianGuanJiaApp.Winform.ExportTaskApp
{
    partial class Form1
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.button1 = new System.Windows.Forms.Button();
            this.rtb_log = new System.Windows.Forms.RichTextBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.lbl_running = new System.Windows.Forms.Label();
            this.lbl_nofinished = new System.Windows.Forms.Label();
            this.lbl_wait_count = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.ckb_purchase = new System.Windows.Forms.CheckBox();
            this.ckb_expressbill = new System.Windows.Forms.CheckBox();
            this.ckb_branchshare = new System.Windows.Forms.CheckBox();
            this.ckb_logistic = new System.Windows.Forms.CheckBox();
            this.ckb_waybillcode = new System.Windows.Forms.CheckBox();
            this.ckb_customerorder = new System.Windows.Forms.CheckBox();
            this.ckb_order = new System.Windows.Forms.CheckBox();
            this.ckb_all = new System.Windows.Forms.CheckBox();
            this.ckb_fx_expressbill = new System.Windows.Forms.CheckBox();
            this.ckb_fx_settlement = new System.Windows.Forms.CheckBox();
            this.ckb_fx_purchase = new System.Windows.Forms.CheckBox();
            this.ckb_fx_waybillcode = new System.Windows.Forms.CheckBox();
            this.ckb_fx_waitorder = new System.Windows.Forms.CheckBox();
            this.ckb_fx_allorder = new System.Windows.Forms.CheckBox();
            this.button3 = new System.Windows.Forms.Button();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.textThreadCount = new System.Windows.Forms.TextBox();
            this.ckb_fx_profitStatistics = new System.Windows.Forms.CheckBox();
            this.ckb_fx_baseProduct_import = new System.Windows.Forms.CheckBox();
            this.label1 = new System.Windows.Forms.Label();
            this.ckb_fx_settlement_exprot = new System.Windows.Forms.CheckBox();
            this.chb_fx_printhistory = new System.Windows.Forms.CheckBox();
            this.chb_fx_sendfail = new System.Windows.Forms.CheckBox();
            this.chb_fx_stockdetail = new System.Windows.Forms.CheckBox();
            this.chb_fx_aftersale = new System.Windows.Forms.CheckBox();
            this.ckb_fx_offline = new System.Windows.Forms.CheckBox();
            this.ckb_fx_sendorder = new System.Windows.Forms.CheckBox();
            this.ckb_fx_branchshare = new System.Windows.Forms.CheckBox();
            this.timer2 = new System.Windows.Forms.Timer(this.components);
            this.btnStop = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.rtb_log2 = new System.Windows.Forms.RichTextBox();
            this.rtb_log3 = new System.Windows.Forms.RichTextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.labelBySystemVersion = new System.Windows.Forms.Label();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.SuspendLayout();
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(858, 13);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(93, 29);
            this.button1.TabIndex = 0;
            this.button1.Text = "开始监听";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // rtb_log
            // 
            this.rtb_log.Location = new System.Drawing.Point(12, 352);
            this.rtb_log.Name = "rtb_log";
            this.rtb_log.Size = new System.Drawing.Size(861, 293);
            this.rtb_log.TabIndex = 1;
            this.rtb_log.Text = "";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.lbl_running);
            this.groupBox1.Controls.Add(this.lbl_nofinished);
            this.groupBox1.Controls.Add(this.lbl_wait_count);
            this.groupBox1.Location = new System.Drawing.Point(12, 10);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(274, 180);
            this.groupBox1.TabIndex = 12;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "任务监控";
            // 
            // lbl_running
            // 
            this.lbl_running.AutoSize = true;
            this.lbl_running.Font = new System.Drawing.Font("宋体", 15F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbl_running.ForeColor = System.Drawing.Color.Red;
            this.lbl_running.Location = new System.Drawing.Point(0, 101);
            this.lbl_running.Name = "lbl_running";
            this.lbl_running.Size = new System.Drawing.Size(209, 20);
            this.lbl_running.TabIndex = 1;
            this.lbl_running.Text = "此窗体执行中任务：0";
            // 
            // lbl_nofinished
            // 
            this.lbl_nofinished.AutoSize = true;
            this.lbl_nofinished.Font = new System.Drawing.Font("宋体", 15F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbl_nofinished.ForeColor = System.Drawing.Color.Red;
            this.lbl_nofinished.Location = new System.Drawing.Point(1, 64);
            this.lbl_nofinished.Name = "lbl_nofinished";
            this.lbl_nofinished.Size = new System.Drawing.Size(188, 20);
            this.lbl_nofinished.TabIndex = 0;
            this.lbl_nofinished.Text = "所有执行中任务：0";
            // 
            // lbl_wait_count
            // 
            this.lbl_wait_count.AutoSize = true;
            this.lbl_wait_count.Font = new System.Drawing.Font("宋体", 15F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbl_wait_count.ForeColor = System.Drawing.Color.Red;
            this.lbl_wait_count.Location = new System.Drawing.Point(1, 24);
            this.lbl_wait_count.Name = "lbl_wait_count";
            this.lbl_wait_count.Size = new System.Drawing.Size(167, 20);
            this.lbl_wait_count.TabIndex = 0;
            this.lbl_wait_count.Text = "等待执行任务：0";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.ckb_purchase);
            this.groupBox2.Controls.Add(this.ckb_expressbill);
            this.groupBox2.Controls.Add(this.ckb_branchshare);
            this.groupBox2.Controls.Add(this.ckb_logistic);
            this.groupBox2.Controls.Add(this.ckb_waybillcode);
            this.groupBox2.Controls.Add(this.ckb_customerorder);
            this.groupBox2.Controls.Add(this.ckb_order);
            this.groupBox2.Controls.Add(this.ckb_all);
            this.groupBox2.Location = new System.Drawing.Point(292, 10);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(202, 180);
            this.groupBox2.TabIndex = 16;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "打单导出类型";
            // 
            // ckb_purchase
            // 
            this.ckb_purchase.AutoSize = true;
            this.ckb_purchase.Enabled = false;
            this.ckb_purchase.Location = new System.Drawing.Point(105, 48);
            this.ckb_purchase.Name = "ckb_purchase";
            this.ckb_purchase.Size = new System.Drawing.Size(84, 16);
            this.ckb_purchase.TabIndex = 0;
            this.ckb_purchase.Text = "生成备货单";
            this.ckb_purchase.UseVisualStyleBackColor = true;
            this.ckb_purchase.CheckedChanged += new System.EventHandler(this.ckb_other_CheckedChanged);
            // 
            // ckb_expressbill
            // 
            this.ckb_expressbill.AutoSize = true;
            this.ckb_expressbill.Enabled = false;
            this.ckb_expressbill.Location = new System.Drawing.Point(6, 111);
            this.ckb_expressbill.Name = "ckb_expressbill";
            this.ckb_expressbill.Size = new System.Drawing.Size(72, 16);
            this.ckb_expressbill.TabIndex = 0;
            this.ckb_expressbill.Text = "快递对账";
            this.ckb_expressbill.UseVisualStyleBackColor = true;
            this.ckb_expressbill.CheckedChanged += new System.EventHandler(this.ckb_other_CheckedChanged);
            // 
            // ckb_branchshare
            // 
            this.ckb_branchshare.AutoSize = true;
            this.ckb_branchshare.Enabled = false;
            this.ckb_branchshare.Location = new System.Drawing.Point(105, 111);
            this.ckb_branchshare.Name = "ckb_branchshare";
            this.ckb_branchshare.Size = new System.Drawing.Size(72, 16);
            this.ckb_branchshare.TabIndex = 0;
            this.ckb_branchshare.Text = "明细导出";
            this.ckb_branchshare.UseVisualStyleBackColor = true;
            this.ckb_branchshare.CheckedChanged += new System.EventHandler(this.ckb_other_CheckedChanged);
            // 
            // ckb_logistic
            // 
            this.ckb_logistic.AutoSize = true;
            this.ckb_logistic.Enabled = false;
            this.ckb_logistic.Location = new System.Drawing.Point(105, 20);
            this.ckb_logistic.Name = "ckb_logistic";
            this.ckb_logistic.Size = new System.Drawing.Size(72, 16);
            this.ckb_logistic.TabIndex = 0;
            this.ckb_logistic.Text = "物流更新";
            this.ckb_logistic.UseVisualStyleBackColor = true;
            this.ckb_logistic.CheckedChanged += new System.EventHandler(this.ckb_other_CheckedChanged);
            // 
            // ckb_waybillcode
            // 
            this.ckb_waybillcode.AutoSize = true;
            this.ckb_waybillcode.Enabled = false;
            this.ckb_waybillcode.Location = new System.Drawing.Point(105, 79);
            this.ckb_waybillcode.Name = "ckb_waybillcode";
            this.ckb_waybillcode.Size = new System.Drawing.Size(72, 16);
            this.ckb_waybillcode.TabIndex = 0;
            this.ckb_waybillcode.Text = "底单导出";
            this.ckb_waybillcode.UseVisualStyleBackColor = true;
            this.ckb_waybillcode.CheckedChanged += new System.EventHandler(this.ckb_other_CheckedChanged);
            // 
            // ckb_customerorder
            // 
            this.ckb_customerorder.AutoSize = true;
            this.ckb_customerorder.Enabled = false;
            this.ckb_customerorder.Location = new System.Drawing.Point(6, 79);
            this.ckb_customerorder.Name = "ckb_customerorder";
            this.ckb_customerorder.Size = new System.Drawing.Size(96, 16);
            this.ckb_customerorder.TabIndex = 0;
            this.ckb_customerorder.Text = "自由打印导出";
            this.ckb_customerorder.UseVisualStyleBackColor = true;
            this.ckb_customerorder.CheckedChanged += new System.EventHandler(this.ckb_other_CheckedChanged);
            // 
            // ckb_order
            // 
            this.ckb_order.AutoSize = true;
            this.ckb_order.Enabled = false;
            this.ckb_order.Location = new System.Drawing.Point(6, 48);
            this.ckb_order.Name = "ckb_order";
            this.ckb_order.Size = new System.Drawing.Size(72, 16);
            this.ckb_order.TabIndex = 0;
            this.ckb_order.Text = "订单导出";
            this.ckb_order.UseVisualStyleBackColor = true;
            this.ckb_order.CheckedChanged += new System.EventHandler(this.ckb_other_CheckedChanged);
            // 
            // ckb_all
            // 
            this.ckb_all.AutoSize = true;
            this.ckb_all.Enabled = false;
            this.ckb_all.Location = new System.Drawing.Point(6, 20);
            this.ckb_all.Name = "ckb_all";
            this.ckb_all.Size = new System.Drawing.Size(48, 16);
            this.ckb_all.TabIndex = 0;
            this.ckb_all.Text = "全部";
            this.ckb_all.UseVisualStyleBackColor = true;
            this.ckb_all.CheckedChanged += new System.EventHandler(this.ckb_other_CheckedChanged);
            // 
            // ckb_fx_expressbill
            // 
            this.ckb_fx_expressbill.AutoSize = true;
            this.ckb_fx_expressbill.Checked = true;
            this.ckb_fx_expressbill.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb_fx_expressbill.Location = new System.Drawing.Point(172, 20);
            this.ckb_fx_expressbill.Name = "ckb_fx_expressbill";
            this.ckb_fx_expressbill.Size = new System.Drawing.Size(84, 16);
            this.ckb_fx_expressbill.TabIndex = 21;
            this.ckb_fx_expressbill.Text = "fx快递对账";
            this.ckb_fx_expressbill.UseVisualStyleBackColor = true;
            // 
            // ckb_fx_settlement
            // 
            this.ckb_fx_settlement.AutoSize = true;
            this.ckb_fx_settlement.Checked = true;
            this.ckb_fx_settlement.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb_fx_settlement.Location = new System.Drawing.Point(96, 73);
            this.ckb_fx_settlement.Name = "ckb_fx_settlement";
            this.ckb_fx_settlement.Size = new System.Drawing.Size(60, 16);
            this.ckb_fx_settlement.TabIndex = 20;
            this.ckb_fx_settlement.Text = "fx对账";
            this.ckb_fx_settlement.UseVisualStyleBackColor = true;
            // 
            // ckb_fx_purchase
            // 
            this.ckb_fx_purchase.AutoSize = true;
            this.ckb_fx_purchase.Checked = true;
            this.ckb_fx_purchase.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb_fx_purchase.Location = new System.Drawing.Point(96, 49);
            this.ckb_fx_purchase.Name = "ckb_fx_purchase";
            this.ckb_fx_purchase.Size = new System.Drawing.Size(72, 16);
            this.ckb_fx_purchase.TabIndex = 4;
            this.ckb_fx_purchase.Text = "fx备货单";
            this.ckb_fx_purchase.UseVisualStyleBackColor = true;
            // 
            // ckb_fx_waybillcode
            // 
            this.ckb_fx_waybillcode.AutoSize = true;
            this.ckb_fx_waybillcode.Checked = true;
            this.ckb_fx_waybillcode.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb_fx_waybillcode.Location = new System.Drawing.Point(96, 20);
            this.ckb_fx_waybillcode.Name = "ckb_fx_waybillcode";
            this.ckb_fx_waybillcode.Size = new System.Drawing.Size(60, 16);
            this.ckb_fx_waybillcode.TabIndex = 3;
            this.ckb_fx_waybillcode.Text = "fx底单";
            this.ckb_fx_waybillcode.UseVisualStyleBackColor = true;
            // 
            // ckb_fx_waitorder
            // 
            this.ckb_fx_waitorder.AutoSize = true;
            this.ckb_fx_waitorder.Checked = true;
            this.ckb_fx_waitorder.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb_fx_waitorder.Location = new System.Drawing.Point(6, 47);
            this.ckb_fx_waitorder.Name = "ckb_fx_waitorder";
            this.ckb_fx_waitorder.Size = new System.Drawing.Size(84, 16);
            this.ckb_fx_waitorder.TabIndex = 2;
            this.ckb_fx_waitorder.Text = "fx待打订单";
            this.ckb_fx_waitorder.UseVisualStyleBackColor = true;
            // 
            // ckb_fx_allorder
            // 
            this.ckb_fx_allorder.AutoSize = true;
            this.ckb_fx_allorder.Checked = true;
            this.ckb_fx_allorder.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb_fx_allorder.Location = new System.Drawing.Point(6, 20);
            this.ckb_fx_allorder.Name = "ckb_fx_allorder";
            this.ckb_fx_allorder.Size = new System.Drawing.Size(84, 16);
            this.ckb_fx_allorder.TabIndex = 1;
            this.ckb_fx_allorder.Text = "所有fx订单";
            this.ckb_fx_allorder.UseVisualStyleBackColor = true;
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(858, 86);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(93, 29);
            this.button3.TabIndex = 19;
            this.button3.Text = "查看运行任务";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // timer1
            // 
            this.timer1.Interval = 1000;
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.textThreadCount);
            this.groupBox3.Controls.Add(this.ckb_fx_profitStatistics);
            this.groupBox3.Controls.Add(this.ckb_fx_baseProduct_import);
            this.groupBox3.Controls.Add(this.label1);
            this.groupBox3.Controls.Add(this.ckb_fx_settlement_exprot);
            this.groupBox3.Controls.Add(this.chb_fx_printhistory);
            this.groupBox3.Controls.Add(this.chb_fx_sendfail);
            this.groupBox3.Controls.Add(this.chb_fx_stockdetail);
            this.groupBox3.Controls.Add(this.chb_fx_aftersale);
            this.groupBox3.Controls.Add(this.ckb_fx_offline);
            this.groupBox3.Controls.Add(this.ckb_fx_expressbill);
            this.groupBox3.Controls.Add(this.ckb_fx_sendorder);
            this.groupBox3.Controls.Add(this.ckb_fx_branchshare);
            this.groupBox3.Controls.Add(this.ckb_fx_allorder);
            this.groupBox3.Controls.Add(this.ckb_fx_waitorder);
            this.groupBox3.Controls.Add(this.ckb_fx_settlement);
            this.groupBox3.Controls.Add(this.ckb_fx_waybillcode);
            this.groupBox3.Controls.Add(this.ckb_fx_purchase);
            this.groupBox3.Location = new System.Drawing.Point(500, 10);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(262, 180);
            this.groupBox3.TabIndex = 22;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "分单系统导出";
            // 
            // textThreadCount
            // 
            this.textThreadCount.Location = new System.Drawing.Point(74, 153);
            this.textThreadCount.Name = "textThreadCount";
            this.textThreadCount.Size = new System.Drawing.Size(65, 21);
            this.textThreadCount.TabIndex = 38;
            this.textThreadCount.Text = "5";
            // 
            // ckb_fx_profitStatistics
            // 
            this.ckb_fx_profitStatistics.AutoSize = true;
            this.ckb_fx_profitStatistics.Checked = true;
            this.ckb_fx_profitStatistics.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb_fx_profitStatistics.Location = new System.Drawing.Point(172, 152);
            this.ckb_fx_profitStatistics.Name = "ckb_fx_profitStatistics";
            this.ckb_fx_profitStatistics.Size = new System.Drawing.Size(84, 16);
            this.ckb_fx_profitStatistics.TabIndex = 37;
            this.ckb_fx_profitStatistics.Text = "fx利润统计";
            this.ckb_fx_profitStatistics.UseVisualStyleBackColor = true;
            // 
            // ckb_fx_baseProduct_import
            // 
            this.ckb_fx_baseProduct_import.AutoSize = true;
            this.ckb_fx_baseProduct_import.Checked = true;
            this.ckb_fx_baseProduct_import.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb_fx_baseProduct_import.Location = new System.Drawing.Point(172, 123);
            this.ckb_fx_baseProduct_import.Name = "ckb_fx_baseProduct_import";
            this.ckb_fx_baseProduct_import.Size = new System.Drawing.Size(108, 16);
            this.ckb_fx_baseProduct_import.TabIndex = 36;
            this.ckb_fx_baseProduct_import.Text = "fx生成基础商品";
            this.ckb_fx_baseProduct_import.UseVisualStyleBackColor = true;
            this.ckb_fx_baseProduct_import.CheckedChanged += new System.EventHandler(this.ckb_fx_baseProduct_import_CheckedChanged);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(1, 156);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 37;
            this.label1.Text = "消费者数量：";
            // 
            // ckb_fx_settlement_exprot
            // 
            this.ckb_fx_settlement_exprot.AutoSize = true;
            this.ckb_fx_settlement_exprot.Checked = true;
            this.ckb_fx_settlement_exprot.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb_fx_settlement_exprot.Location = new System.Drawing.Point(96, 126);
            this.ckb_fx_settlement_exprot.Name = "ckb_fx_settlement_exprot";
            this.ckb_fx_settlement_exprot.Size = new System.Drawing.Size(120, 16);
            this.ckb_fx_settlement_exprot.TabIndex = 35;
            this.ckb_fx_settlement_exprot.Text = "fx对账单一键下载";
            this.ckb_fx_settlement_exprot.UseVisualStyleBackColor = true;
            // 
            // chb_fx_printhistory
            // 
            this.chb_fx_printhistory.AutoSize = true;
            this.chb_fx_printhistory.Checked = true;
            this.chb_fx_printhistory.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chb_fx_printhistory.Location = new System.Drawing.Point(6, 126);
            this.chb_fx_printhistory.Name = "chb_fx_printhistory";
            this.chb_fx_printhistory.Size = new System.Drawing.Size(84, 16);
            this.chb_fx_printhistory.TabIndex = 27;
            this.chb_fx_printhistory.Text = "fx打印记录";
            this.chb_fx_printhistory.UseVisualStyleBackColor = true;
            // 
            // chb_fx_sendfail
            // 
            this.chb_fx_sendfail.AutoSize = true;
            this.chb_fx_sendfail.Checked = true;
            this.chb_fx_sendfail.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chb_fx_sendfail.Location = new System.Drawing.Point(172, 100);
            this.chb_fx_sendfail.Name = "chb_fx_sendfail";
            this.chb_fx_sendfail.Size = new System.Drawing.Size(84, 16);
            this.chb_fx_sendfail.TabIndex = 26;
            this.chb_fx_sendfail.Text = "fx发货失败";
            this.chb_fx_sendfail.UseVisualStyleBackColor = true;
            // 
            // chb_fx_stockdetail
            // 
            this.chb_fx_stockdetail.AutoSize = true;
            this.chb_fx_stockdetail.Checked = true;
            this.chb_fx_stockdetail.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chb_fx_stockdetail.Location = new System.Drawing.Point(96, 100);
            this.chb_fx_stockdetail.Name = "chb_fx_stockdetail";
            this.chb_fx_stockdetail.Size = new System.Drawing.Size(84, 16);
            this.chb_fx_stockdetail.TabIndex = 25;
            this.chb_fx_stockdetail.Text = "fx实时库存";
            this.chb_fx_stockdetail.UseVisualStyleBackColor = true;
            // 
            // chb_fx_aftersale
            // 
            this.chb_fx_aftersale.AutoSize = true;
            this.chb_fx_aftersale.Checked = true;
            this.chb_fx_aftersale.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chb_fx_aftersale.Location = new System.Drawing.Point(6, 100);
            this.chb_fx_aftersale.Name = "chb_fx_aftersale";
            this.chb_fx_aftersale.Size = new System.Drawing.Size(84, 16);
            this.chb_fx_aftersale.TabIndex = 24;
            this.chb_fx_aftersale.Text = "fx售后订单";
            this.chb_fx_aftersale.UseVisualStyleBackColor = true;
            // 
            // ckb_fx_offline
            // 
            this.ckb_fx_offline.AutoSize = true;
            this.ckb_fx_offline.Checked = true;
            this.ckb_fx_offline.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb_fx_offline.Location = new System.Drawing.Point(6, 73);
            this.ckb_fx_offline.Name = "ckb_fx_offline";
            this.ckb_fx_offline.Size = new System.Drawing.Size(72, 16);
            this.ckb_fx_offline.TabIndex = 23;
            this.ckb_fx_offline.Text = "fx线下单";
            this.ckb_fx_offline.UseVisualStyleBackColor = true;
            // 
            // ckb_fx_sendorder
            // 
            this.ckb_fx_sendorder.AutoSize = true;
            this.ckb_fx_sendorder.Checked = true;
            this.ckb_fx_sendorder.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb_fx_sendorder.Location = new System.Drawing.Point(172, 73);
            this.ckb_fx_sendorder.Name = "ckb_fx_sendorder";
            this.ckb_fx_sendorder.Size = new System.Drawing.Size(84, 16);
            this.ckb_fx_sendorder.TabIndex = 0;
            this.ckb_fx_sendorder.Text = "fx发货订单";
            this.ckb_fx_sendorder.UseVisualStyleBackColor = true;
            this.ckb_fx_sendorder.CheckedChanged += new System.EventHandler(this.ckb_other_CheckedChanged);
            // 
            // ckb_fx_branchshare
            // 
            this.ckb_fx_branchshare.AutoSize = true;
            this.ckb_fx_branchshare.Checked = true;
            this.ckb_fx_branchshare.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ckb_fx_branchshare.Location = new System.Drawing.Point(172, 47);
            this.ckb_fx_branchshare.Name = "ckb_fx_branchshare";
            this.ckb_fx_branchshare.Size = new System.Drawing.Size(84, 16);
            this.ckb_fx_branchshare.TabIndex = 0;
            this.ckb_fx_branchshare.Text = "fx单号分享";
            this.ckb_fx_branchshare.UseVisualStyleBackColor = true;
            this.ckb_fx_branchshare.CheckedChanged += new System.EventHandler(this.ckb_other_CheckedChanged);
            // 
            // timer2
            // 
            this.timer2.Enabled = true;
            this.timer2.Interval = 5000;
            this.timer2.Tick += new System.EventHandler(this.timer2_Tick);
            // 
            // btnStop
            // 
            this.btnStop.Location = new System.Drawing.Point(858, 48);
            this.btnStop.Name = "btnStop";
            this.btnStop.Size = new System.Drawing.Size(93, 30);
            this.btnStop.TabIndex = 23;
            this.btnStop.Text = "停止监听";
            this.btnStop.UseVisualStyleBackColor = true;
            this.btnStop.Click += new System.EventHandler(this.btnStop_Click);
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(858, 121);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(93, 31);
            this.button2.TabIndex = 24;
            this.button2.Text = "清空任务";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click_1);
            // 
            // rtb_log2
            // 
            this.rtb_log2.Location = new System.Drawing.Point(12, 196);
            this.rtb_log2.Name = "rtb_log2";
            this.rtb_log2.Size = new System.Drawing.Size(424, 150);
            this.rtb_log2.TabIndex = 1;
            this.rtb_log2.Text = "";
            // 
            // rtb_log3
            // 
            this.rtb_log3.Location = new System.Drawing.Point(442, 196);
            this.rtb_log3.Name = "rtb_log3";
            this.rtb_log3.Size = new System.Drawing.Size(431, 150);
            this.rtb_log3.TabIndex = 1;
            this.rtb_log3.Text = "";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label2.ForeColor = System.Drawing.Color.Red;
            this.label2.Location = new System.Drawing.Point(778, 164);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(71, 14);
            this.label2.TabIndex = 25;
            this.label2.Text = "Version:";
            // 
            // labelBySystemVersion
            // 
            this.labelBySystemVersion.AutoSize = true;
            this.labelBySystemVersion.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.labelBySystemVersion.ForeColor = System.Drawing.Color.Black;
            this.labelBySystemVersion.Location = new System.Drawing.Point(855, 166);
            this.labelBySystemVersion.Name = "labelBySystemVersion";
            this.labelBySystemVersion.Size = new System.Drawing.Size(31, 12);
            this.labelBySystemVersion.TabIndex = 26;
            this.labelBySystemVersion.Text = "正式";
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(968, 657);
            this.Controls.Add(this.labelBySystemVersion);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.btnStop);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.button3);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.rtb_log3);
            this.Controls.Add(this.rtb_log2);
            this.Controls.Add(this.rtb_log);
            this.Controls.Add(this.button1);
            this.Name = "Form1";
            this.Text = "Form1";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.Form1_FormClosing);
            this.Load += new System.EventHandler(this.Form1_Load);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private System.Windows.Forms.TextBox textThreadCount;
        private System.Windows.Forms.Label label1;

        #endregion

        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.RichTextBox rtb_log;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Button button3;
        public System.Windows.Forms.CheckBox ckb_all;
        public System.Windows.Forms.CheckBox ckb_purchase;
        public System.Windows.Forms.CheckBox ckb_logistic;
        public System.Windows.Forms.CheckBox ckb_waybillcode;
        public System.Windows.Forms.CheckBox ckb_customerorder;
        public System.Windows.Forms.CheckBox ckb_order;
        public System.Windows.Forms.CheckBox ckb_branchshare;
        public System.Windows.Forms.CheckBox ckb_expressbill;
        public System.Windows.Forms.CheckBox ckb_fx_waitorder;
        public System.Windows.Forms.CheckBox ckb_fx_allorder;
        public System.Windows.Forms.CheckBox ckb_fx_waybillcode;
        public System.Windows.Forms.CheckBox ckb_fx_purchase;
        public System.Windows.Forms.CheckBox ckb_fx_settlement;
        public System.Windows.Forms.CheckBox ckb_fx_expressbill;
        private System.Windows.Forms.Timer timer1;
        private System.Windows.Forms.GroupBox groupBox3;
        public System.Windows.Forms.CheckBox ckb_fx_offline;
        private System.Windows.Forms.Label lbl_nofinished;
        private System.Windows.Forms.Label lbl_wait_count;
        public System.Windows.Forms.CheckBox ckb_fx_branchshare;
        public System.Windows.Forms.CheckBox ckb_fx_sendorder;
        public System.Windows.Forms.CheckBox chb_fx_aftersale;
        public System.Windows.Forms.CheckBox chb_fx_stockdetail;
        public System.Windows.Forms.CheckBox chb_fx_printhistory;
        public System.Windows.Forms.CheckBox chb_fx_sendfail;
        private System.Windows.Forms.Timer timer2;
        private System.Windows.Forms.Button btnStop;
        private System.Windows.Forms.Button button2;
        public System.Windows.Forms.Label lbl_running;
        public System.Windows.Forms.RichTextBox rtb_log2;
        public System.Windows.Forms.RichTextBox rtb_log3;
        private System.Windows.Forms.CheckBox ckb_fx_settlement_exprot;
        private System.Windows.Forms.CheckBox ckb_fx_baseProduct_import;
        public System.Windows.Forms.CheckBox ckb_fx_profitStatistics;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label labelBySystemVersion;
    }
}

