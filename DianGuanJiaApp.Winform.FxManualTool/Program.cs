using DianGuanJiaApp.Services.ServicesExtension;
using DianGuanJiaApp.Utility;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace DianGuanJiaApp.Winform.FxManualTool
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            RedisConfigService.Initialization();
            var noRun = false;
            using (System.Threading.Mutex m = new System.Threading.Mutex(true, Application.ProductName, out noRun))
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                //处理未捕获的异常
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                //处理UI线程异常
                Application.ThreadException += new System.Threading.ThreadExceptionEventHandler(Application_ThreadException);
                //处理非UI线程异常
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
                TaskScheduler.UnobservedTaskException += (sender, e) =>
                {
                    Log.WriteError($"捕获到线程中未解决异常,UnobservedTaskException：{e?.Exception}");
                    Restart();
                };
                try
                {
                    Application.Run(new FxManualTool());
                }
                catch (Exception ex)
                {
                    Log.WriteError($"启动窗体失败：{ex}");
                }
            }
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Log.WriteError($"捕获到未解决异常,UnhandledException：{e?.ExceptionObject}");
            Restart();
        }

        static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            Log.WriteError($"捕获到线程中未解决异常,ThreadException：{e?.Exception}");
            Restart();
        }

        static void Restart()
        {
            return;
            System.Diagnostics.ProcessStartInfo cp = new System.Diagnostics.ProcessStartInfo();
            cp.FileName = Application.ExecutablePath;
            cp.Arguments = "cmd params";
            cp.UseShellExecute = true;
            System.Diagnostics.Process.Start(cp);
        }
    }
}
