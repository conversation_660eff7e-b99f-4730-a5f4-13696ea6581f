namespace DianGuanJiaApp.Winform.SendHistoryAnalysisTool
{
    partial class Form1
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.txtDbName = new System.Windows.Forms.TextBox();
            this.label13 = new System.Windows.Forms.Label();
            this.txtDbNameConfigId = new System.Windows.Forms.TextBox();
            this.label12 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.txtSendHistoryCompleteTotal = new System.Windows.Forms.TextBox();
            this.txtSendHistoryRepetitionTotal = new System.Windows.Forms.TextBox();
            this.txtSendHistoryLossTotal = new System.Windows.Forms.TextBox();
            this.txtNotDgjSendTotal = new System.Windows.Forms.TextBox();
            this.txtNotHasSendHistoryTotal = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.txtAnalysisBatchId = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.txtLogs = new System.Windows.Forms.TextBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.lbStatus = new System.Windows.Forms.Label();
            this.btnManualAnalyse = new System.Windows.Forms.Button();
            this.dtpStartTime = new System.Windows.Forms.DateTimePicker();
            this.label2 = new System.Windows.Forms.Label();
            this.txtMobile = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.dgvAnalysisResult = new System.Windows.Forms.DataGridView();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.txtAutoLogs = new System.Windows.Forms.TextBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.txtConcurrency = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.lbAutoAnalysisStatus = new System.Windows.Forms.Label();
            this.btnSearch = new System.Windows.Forms.Button();
            this.txtSearchDbName = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.btnAutoAnalysis = new System.Windows.Forms.Button();
            this.cbxDbNameConfigs = new System.Windows.Forms.CheckedListBox();
            this.DbNameConfigId = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.DbName = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.AnalysisBatchId = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.AnalysisDate = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.NotHasSendHistoryTotal = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.NotDgjSendTotal = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.SendHistoryLossTotal = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.SendHistoryRepetitionTotal = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.SendHistoryCompleteTotal = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.dtpEndTime = new System.Windows.Forms.DateTimePicker();
            this.label14 = new System.Windows.Forms.Label();
            this.txtFxUserId = new System.Windows.Forms.TextBox();
            this.label15 = new System.Windows.Forms.Label();
            this.txtNotHasSendTotal = new System.Windows.Forms.TextBox();
            this.label16 = new System.Windows.Forms.Label();
            this.txtAnalysisTotal = new System.Windows.Forms.TextBox();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.groupBox6.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvAnalysisResult)).BeginInit();
            this.groupBox5.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1003, 601);
            this.tabControl1.TabIndex = 0;
            this.tabControl1.SelectedIndexChanged += new System.EventHandler(this.tabControl1_SelectedIndexChanged);
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.groupBox3);
            this.tabPage1.Controls.Add(this.groupBox2);
            this.tabPage1.Controls.Add(this.groupBox1);
            this.tabPage1.Location = new System.Drawing.Point(4, 22);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(995, 575);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "按用户分析";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.txtAnalysisTotal);
            this.groupBox3.Controls.Add(this.label16);
            this.groupBox3.Controls.Add(this.txtNotHasSendTotal);
            this.groupBox3.Controls.Add(this.label15);
            this.groupBox3.Controls.Add(this.txtFxUserId);
            this.groupBox3.Controls.Add(this.label14);
            this.groupBox3.Controls.Add(this.txtDbName);
            this.groupBox3.Controls.Add(this.label13);
            this.groupBox3.Controls.Add(this.txtDbNameConfigId);
            this.groupBox3.Controls.Add(this.label12);
            this.groupBox3.Controls.Add(this.label9);
            this.groupBox3.Controls.Add(this.txtSendHistoryCompleteTotal);
            this.groupBox3.Controls.Add(this.txtSendHistoryRepetitionTotal);
            this.groupBox3.Controls.Add(this.txtSendHistoryLossTotal);
            this.groupBox3.Controls.Add(this.txtNotDgjSendTotal);
            this.groupBox3.Controls.Add(this.txtNotHasSendHistoryTotal);
            this.groupBox3.Controls.Add(this.label8);
            this.groupBox3.Controls.Add(this.label7);
            this.groupBox3.Controls.Add(this.label6);
            this.groupBox3.Controls.Add(this.label5);
            this.groupBox3.Controls.Add(this.label4);
            this.groupBox3.Controls.Add(this.txtAnalysisBatchId);
            this.groupBox3.Controls.Add(this.label3);
            this.groupBox3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox3.Location = new System.Drawing.Point(3, 65);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(505, 507);
            this.groupBox3.TabIndex = 2;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "分析结果";
            this.groupBox3.Enter += new System.EventHandler(this.groupBox3_Enter);
            // 
            // txtDbName
            // 
            this.txtDbName.Location = new System.Drawing.Point(147, 133);
            this.txtDbName.Name = "txtDbName";
            this.txtDbName.ReadOnly = true;
            this.txtDbName.Size = new System.Drawing.Size(227, 21);
            this.txtDbName.TabIndex = 16;
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(29, 138);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(77, 12);
            this.label13.TabIndex = 15;
            this.label13.Text = "数据库名称：";
            // 
            // txtDbNameConfigId
            // 
            this.txtDbNameConfigId.Location = new System.Drawing.Point(147, 98);
            this.txtDbNameConfigId.Name = "txtDbNameConfigId";
            this.txtDbNameConfigId.ReadOnly = true;
            this.txtDbNameConfigId.Size = new System.Drawing.Size(227, 21);
            this.txtDbNameConfigId.TabIndex = 14;
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(29, 103);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(89, 12);
            this.label12.TabIndex = 13;
            this.label12.Text = "数据库配置ID：";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("宋体", 8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label9.ForeColor = System.Drawing.Color.Red;
            this.label9.Location = new System.Drawing.Point(381, 41);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(93, 11);
            this.label9.TabIndex = 12;
            this.label9.Text = "查询分析结果使用";
            // 
            // txtSendHistoryCompleteTotal
            // 
            this.txtSendHistoryCompleteTotal.Enabled = false;
            this.txtSendHistoryCompleteTotal.Location = new System.Drawing.Point(147, 359);
            this.txtSendHistoryCompleteTotal.Name = "txtSendHistoryCompleteTotal";
            this.txtSendHistoryCompleteTotal.ReadOnly = true;
            this.txtSendHistoryCompleteTotal.Size = new System.Drawing.Size(227, 21);
            this.txtSendHistoryCompleteTotal.TabIndex = 11;
            // 
            // txtSendHistoryRepetitionTotal
            // 
            this.txtSendHistoryRepetitionTotal.Enabled = false;
            this.txtSendHistoryRepetitionTotal.Location = new System.Drawing.Point(147, 327);
            this.txtSendHistoryRepetitionTotal.Name = "txtSendHistoryRepetitionTotal";
            this.txtSendHistoryRepetitionTotal.ReadOnly = true;
            this.txtSendHistoryRepetitionTotal.Size = new System.Drawing.Size(227, 21);
            this.txtSendHistoryRepetitionTotal.TabIndex = 10;
            // 
            // txtSendHistoryLossTotal
            // 
            this.txtSendHistoryLossTotal.Enabled = false;
            this.txtSendHistoryLossTotal.Location = new System.Drawing.Point(147, 294);
            this.txtSendHistoryLossTotal.Name = "txtSendHistoryLossTotal";
            this.txtSendHistoryLossTotal.ReadOnly = true;
            this.txtSendHistoryLossTotal.Size = new System.Drawing.Size(227, 21);
            this.txtSendHistoryLossTotal.TabIndex = 9;
            // 
            // txtNotDgjSendTotal
            // 
            this.txtNotDgjSendTotal.Enabled = false;
            this.txtNotDgjSendTotal.Location = new System.Drawing.Point(147, 259);
            this.txtNotDgjSendTotal.Name = "txtNotDgjSendTotal";
            this.txtNotDgjSendTotal.ReadOnly = true;
            this.txtNotDgjSendTotal.Size = new System.Drawing.Size(227, 21);
            this.txtNotDgjSendTotal.TabIndex = 8;
            // 
            // txtNotHasSendHistoryTotal
            // 
            this.txtNotHasSendHistoryTotal.Enabled = false;
            this.txtNotHasSendHistoryTotal.Location = new System.Drawing.Point(147, 229);
            this.txtNotHasSendHistoryTotal.Name = "txtNotHasSendHistoryTotal";
            this.txtNotHasSendHistoryTotal.ReadOnly = true;
            this.txtNotHasSendHistoryTotal.Size = new System.Drawing.Size(227, 21);
            this.txtNotHasSendHistoryTotal.TabIndex = 7;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(33, 364);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(113, 12);
            this.label8.TabIndex = 6;
            this.label8.Text = "发货记录完整情况：";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(33, 332);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(113, 12);
            this.label7.TabIndex = 5;
            this.label7.Text = "发货记录重复数量：";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(31, 299);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(113, 12);
            this.label6.TabIndex = 4;
            this.label6.Text = "发货记录缺失数量：";
            this.label6.Click += new System.EventHandler(this.label6_Click);
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(29, 264);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(113, 12);
            this.label5.TabIndex = 3;
            this.label5.Text = "非店管家发货数量：";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(27, 234);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(113, 12);
            this.label4.TabIndex = 2;
            this.label4.Text = "没有发货记录数量：";
            // 
            // txtAnalysisBatchId
            // 
            this.txtAnalysisBatchId.Location = new System.Drawing.Point(147, 36);
            this.txtAnalysisBatchId.Name = "txtAnalysisBatchId";
            this.txtAnalysisBatchId.ReadOnly = true;
            this.txtAnalysisBatchId.Size = new System.Drawing.Size(227, 21);
            this.txtAnalysisBatchId.TabIndex = 1;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(27, 41);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(89, 12);
            this.label3.TabIndex = 0;
            this.label3.Text = "分析批次编号：";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.txtLogs);
            this.groupBox2.Dock = System.Windows.Forms.DockStyle.Right;
            this.groupBox2.Location = new System.Drawing.Point(508, 65);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(484, 507);
            this.groupBox2.TabIndex = 1;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "日志信息";
            // 
            // txtLogs
            // 
            this.txtLogs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtLogs.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtLogs.Location = new System.Drawing.Point(3, 17);
            this.txtLogs.Multiline = true;
            this.txtLogs.Name = "txtLogs";
            this.txtLogs.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtLogs.Size = new System.Drawing.Size(478, 487);
            this.txtLogs.TabIndex = 0;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.dtpEndTime);
            this.groupBox1.Controls.Add(this.lbStatus);
            this.groupBox1.Controls.Add(this.btnManualAnalyse);
            this.groupBox1.Controls.Add(this.dtpStartTime);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Controls.Add(this.txtMobile);
            this.groupBox1.Controls.Add(this.label1);
            this.groupBox1.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox1.Location = new System.Drawing.Point(3, 3);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(989, 62);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "分析条件";
            // 
            // lbStatus
            // 
            this.lbStatus.AutoSize = true;
            this.lbStatus.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lbStatus.ForeColor = System.Drawing.Color.Red;
            this.lbStatus.Location = new System.Drawing.Point(695, 24);
            this.lbStatus.Name = "lbStatus";
            this.lbStatus.Size = new System.Drawing.Size(0, 12);
            this.lbStatus.TabIndex = 5;
            // 
            // btnManualAnalyse
            // 
            this.btnManualAnalyse.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnManualAnalyse.Location = new System.Drawing.Point(602, 18);
            this.btnManualAnalyse.Name = "btnManualAnalyse";
            this.btnManualAnalyse.Size = new System.Drawing.Size(75, 23);
            this.btnManualAnalyse.TabIndex = 4;
            this.btnManualAnalyse.Text = "分 析";
            this.btnManualAnalyse.UseVisualStyleBackColor = true;
            this.btnManualAnalyse.Click += new System.EventHandler(this.btnManualAnalyse_Click);
            // 
            // dtpStartTime
            // 
            this.dtpStartTime.Location = new System.Drawing.Point(328, 20);
            this.dtpStartTime.Name = "dtpStartTime";
            this.dtpStartTime.Size = new System.Drawing.Size(130, 21);
            this.dtpStartTime.TabIndex = 3;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(265, 27);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 2;
            this.label2.Text = "分析时间：";
            // 
            // txtMobile
            // 
            this.txtMobile.Location = new System.Drawing.Point(88, 21);
            this.txtMobile.Name = "txtMobile";
            this.txtMobile.Size = new System.Drawing.Size(150, 21);
            this.txtMobile.TabIndex = 1;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(25, 29);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "用户电话：";
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.groupBox6);
            this.tabPage2.Controls.Add(this.groupBox5);
            this.tabPage2.Controls.Add(this.groupBox4);
            this.tabPage2.Location = new System.Drawing.Point(4, 22);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(995, 575);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "按数据库分析";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.dgvAnalysisResult);
            this.groupBox6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.groupBox6.Location = new System.Drawing.Point(3, 160);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(670, 412);
            this.groupBox6.TabIndex = 2;
            this.groupBox6.TabStop = false;
            this.groupBox6.Text = "分析结果(按数据库)";
            // 
            // dgvAnalysisResult
            // 
            this.dgvAnalysisResult.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvAnalysisResult.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvAnalysisResult.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.DbNameConfigId,
            this.DbName,
            this.AnalysisBatchId,
            this.AnalysisDate,
            this.NotHasSendHistoryTotal,
            this.NotDgjSendTotal,
            this.SendHistoryLossTotal,
            this.SendHistoryRepetitionTotal,
            this.SendHistoryCompleteTotal});
            this.dgvAnalysisResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvAnalysisResult.Location = new System.Drawing.Point(3, 17);
            this.dgvAnalysisResult.Name = "dgvAnalysisResult";
            this.dgvAnalysisResult.RowTemplate.Height = 23;
            this.dgvAnalysisResult.Size = new System.Drawing.Size(664, 392);
            this.dgvAnalysisResult.TabIndex = 0;
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.txtAutoLogs);
            this.groupBox5.Dock = System.Windows.Forms.DockStyle.Right;
            this.groupBox5.Location = new System.Drawing.Point(673, 160);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(319, 412);
            this.groupBox5.TabIndex = 1;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "日志信息";
            // 
            // txtAutoLogs
            // 
            this.txtAutoLogs.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtAutoLogs.Location = new System.Drawing.Point(3, 17);
            this.txtAutoLogs.Multiline = true;
            this.txtAutoLogs.Name = "txtAutoLogs";
            this.txtAutoLogs.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtAutoLogs.Size = new System.Drawing.Size(313, 392);
            this.txtAutoLogs.TabIndex = 0;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.txtConcurrency);
            this.groupBox4.Controls.Add(this.label11);
            this.groupBox4.Controls.Add(this.lbAutoAnalysisStatus);
            this.groupBox4.Controls.Add(this.btnSearch);
            this.groupBox4.Controls.Add(this.txtSearchDbName);
            this.groupBox4.Controls.Add(this.label10);
            this.groupBox4.Controls.Add(this.btnAutoAnalysis);
            this.groupBox4.Controls.Add(this.cbxDbNameConfigs);
            this.groupBox4.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBox4.Location = new System.Drawing.Point(3, 3);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(989, 157);
            this.groupBox4.TabIndex = 0;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "分析数据库";
            // 
            // txtConcurrency
            // 
            this.txtConcurrency.Location = new System.Drawing.Point(414, 17);
            this.txtConcurrency.Name = "txtConcurrency";
            this.txtConcurrency.Size = new System.Drawing.Size(100, 21);
            this.txtConcurrency.TabIndex = 7;
            this.txtConcurrency.Text = "5";
            this.txtConcurrency.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(334, 22);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(77, 12);
            this.label11.TabIndex = 6;
            this.label11.Text = "分析并发数：";
            // 
            // lbAutoAnalysisStatus
            // 
            this.lbAutoAnalysisStatus.AutoSize = true;
            this.lbAutoAnalysisStatus.ForeColor = System.Drawing.Color.Red;
            this.lbAutoAnalysisStatus.Location = new System.Drawing.Point(670, 22);
            this.lbAutoAnalysisStatus.Name = "lbAutoAnalysisStatus";
            this.lbAutoAnalysisStatus.Size = new System.Drawing.Size(0, 12);
            this.lbAutoAnalysisStatus.TabIndex = 5;
            // 
            // btnSearch
            // 
            this.btnSearch.Location = new System.Drawing.Point(249, 17);
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Size = new System.Drawing.Size(75, 23);
            this.btnSearch.TabIndex = 4;
            this.btnSearch.Text = "查 询";
            this.btnSearch.UseVisualStyleBackColor = true;
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // txtSearchDbName
            // 
            this.txtSearchDbName.Location = new System.Drawing.Point(83, 19);
            this.txtSearchDbName.Name = "txtSearchDbName";
            this.txtSearchDbName.Size = new System.Drawing.Size(150, 21);
            this.txtSearchDbName.TabIndex = 3;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(7, 26);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(77, 12);
            this.label10.TabIndex = 2;
            this.label10.Text = "数据库名称：";
            // 
            // btnAutoAnalysis
            // 
            this.btnAutoAnalysis.Enabled = false;
            this.btnAutoAnalysis.Location = new System.Drawing.Point(528, 16);
            this.btnAutoAnalysis.Name = "btnAutoAnalysis";
            this.btnAutoAnalysis.Size = new System.Drawing.Size(75, 23);
            this.btnAutoAnalysis.TabIndex = 1;
            this.btnAutoAnalysis.Text = "分 析";
            this.btnAutoAnalysis.UseVisualStyleBackColor = true;
            this.btnAutoAnalysis.Click += new System.EventHandler(this.btnAutoAnalysis_Click);
            // 
            // cbxDbNameConfigs
            // 
            this.cbxDbNameConfigs.ColumnWidth = 160;
            this.cbxDbNameConfigs.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.cbxDbNameConfigs.FormattingEnabled = true;
            this.cbxDbNameConfigs.HorizontalScrollbar = true;
            this.cbxDbNameConfigs.Location = new System.Drawing.Point(3, 54);
            this.cbxDbNameConfigs.MultiColumn = true;
            this.cbxDbNameConfigs.Name = "cbxDbNameConfigs";
            this.cbxDbNameConfigs.Size = new System.Drawing.Size(983, 100);
            this.cbxDbNameConfigs.TabIndex = 0;
            // 
            // DbNameConfigId
            // 
            this.DbNameConfigId.DataPropertyName = "DbNameConfigId";
            this.DbNameConfigId.HeaderText = "数据配置ID";
            this.DbNameConfigId.Name = "DbNameConfigId";
            this.DbNameConfigId.ReadOnly = true;
            // 
            // DbName
            // 
            this.DbName.DataPropertyName = "DbName";
            this.DbName.HeaderText = "数据库名称";
            this.DbName.Name = "DbName";
            this.DbName.ReadOnly = true;
            // 
            // AnalysisBatchId
            // 
            this.AnalysisBatchId.DataPropertyName = "AnalysisBatchId";
            this.AnalysisBatchId.HeaderText = "分析批次ID";
            this.AnalysisBatchId.Name = "AnalysisBatchId";
            this.AnalysisBatchId.ReadOnly = true;
            // 
            // AnalysisDate
            // 
            this.AnalysisDate.DataPropertyName = "AnalysisDate";
            this.AnalysisDate.HeaderText = "分析时间";
            this.AnalysisDate.Name = "AnalysisDate";
            this.AnalysisDate.ReadOnly = true;
            // 
            // NotHasSendHistoryTotal
            // 
            this.NotHasSendHistoryTotal.DataPropertyName = "NotHasSendHistoryTotal";
            this.NotHasSendHistoryTotal.HeaderText = "没有发货记录数量";
            this.NotHasSendHistoryTotal.Name = "NotHasSendHistoryTotal";
            this.NotHasSendHistoryTotal.ReadOnly = true;
            // 
            // NotDgjSendTotal
            // 
            this.NotDgjSendTotal.DataPropertyName = "NotDgjSendTotal";
            this.NotDgjSendTotal.HeaderText = "非店管家发货数量";
            this.NotDgjSendTotal.Name = "NotDgjSendTotal";
            this.NotDgjSendTotal.ReadOnly = true;
            // 
            // SendHistoryLossTotal
            // 
            this.SendHistoryLossTotal.DataPropertyName = "SendHistoryLossTotal";
            this.SendHistoryLossTotal.HeaderText = "发货记录缺失数量";
            this.SendHistoryLossTotal.Name = "SendHistoryLossTotal";
            this.SendHistoryLossTotal.ReadOnly = true;
            // 
            // SendHistoryRepetitionTotal
            // 
            this.SendHistoryRepetitionTotal.DataPropertyName = "SendHistoryRepetitionTotal";
            this.SendHistoryRepetitionTotal.HeaderText = "发货记录重复数量";
            this.SendHistoryRepetitionTotal.Name = "SendHistoryRepetitionTotal";
            this.SendHistoryRepetitionTotal.ReadOnly = true;
            // 
            // SendHistoryCompleteTotal
            // 
            this.SendHistoryCompleteTotal.DataPropertyName = "SendHistoryCompleteTotal";
            this.SendHistoryCompleteTotal.HeaderText = "发货记录完整数量";
            this.SendHistoryCompleteTotal.Name = "SendHistoryCompleteTotal";
            this.SendHistoryCompleteTotal.ReadOnly = true;
            // 
            // dtpEndTime
            // 
            this.dtpEndTime.Location = new System.Drawing.Point(466, 20);
            this.dtpEndTime.Name = "dtpEndTime";
            this.dtpEndTime.Size = new System.Drawing.Size(130, 21);
            this.dtpEndTime.TabIndex = 6;
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(28, 71);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(77, 12);
            this.label14.TabIndex = 17;
            this.label14.Text = "分销用户ID：";
            // 
            // txtFxUserId
            // 
            this.txtFxUserId.Location = new System.Drawing.Point(147, 66);
            this.txtFxUserId.Name = "txtFxUserId";
            this.txtFxUserId.ReadOnly = true;
            this.txtFxUserId.Size = new System.Drawing.Size(227, 21);
            this.txtFxUserId.TabIndex = 18;
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(29, 205);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(77, 12);
            this.label15.TabIndex = 19;
            this.label15.Text = "未发货数量：";
            // 
            // txtNotHasSendTotal
            // 
            this.txtNotHasSendTotal.Enabled = false;
            this.txtNotHasSendTotal.Location = new System.Drawing.Point(147, 202);
            this.txtNotHasSendTotal.Name = "txtNotHasSendTotal";
            this.txtNotHasSendTotal.ReadOnly = true;
            this.txtNotHasSendTotal.Size = new System.Drawing.Size(227, 21);
            this.txtNotHasSendTotal.TabIndex = 20;
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(31, 173);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(77, 12);
            this.label16.TabIndex = 21;
            this.label16.Text = "总分析数量：";
            // 
            // txtAnalysisTotal
            // 
            this.txtAnalysisTotal.Enabled = false;
            this.txtAnalysisTotal.Location = new System.Drawing.Point(147, 168);
            this.txtAnalysisTotal.Name = "txtAnalysisTotal";
            this.txtAnalysisTotal.ReadOnly = true;
            this.txtAnalysisTotal.Size = new System.Drawing.Size(227, 21);
            this.txtAnalysisTotal.TabIndex = 22;
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1003, 601);
            this.Controls.Add(this.tabControl1);
            this.Name = "Form1";
            this.Text = "发货记录分析工具 v1.0";
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tabPage2.ResumeLayout(false);
            this.groupBox6.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvAnalysisResult)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.DateTimePicker dtpStartTime;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.TextBox txtMobile;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button btnManualAnalyse;
        private System.Windows.Forms.TextBox txtLogs;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.TextBox txtAnalysisBatchId;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.TextBox txtSendHistoryCompleteTotal;
        private System.Windows.Forms.TextBox txtSendHistoryRepetitionTotal;
        private System.Windows.Forms.TextBox txtSendHistoryLossTotal;
        private System.Windows.Forms.TextBox txtNotDgjSendTotal;
        private System.Windows.Forms.TextBox txtNotHasSendHistoryTotal;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label lbStatus;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.CheckedListBox cbxDbNameConfigs;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.Button btnAutoAnalysis;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.TextBox txtSearchDbName;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label lbAutoAnalysisStatus;
        private System.Windows.Forms.DataGridView dgvAnalysisResult;
        private System.Windows.Forms.TextBox txtAutoLogs;
        private System.Windows.Forms.TextBox txtConcurrency;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox txtDbNameConfigId;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.TextBox txtDbName;
        private System.Windows.Forms.DataGridViewTextBoxColumn DbNameConfigId;
        private System.Windows.Forms.DataGridViewTextBoxColumn DbName;
        private System.Windows.Forms.DataGridViewTextBoxColumn AnalysisBatchId;
        private System.Windows.Forms.DataGridViewTextBoxColumn AnalysisDate;
        private System.Windows.Forms.DataGridViewTextBoxColumn NotHasSendHistoryTotal;
        private System.Windows.Forms.DataGridViewTextBoxColumn NotDgjSendTotal;
        private System.Windows.Forms.DataGridViewTextBoxColumn SendHistoryLossTotal;
        private System.Windows.Forms.DataGridViewTextBoxColumn SendHistoryRepetitionTotal;
        private System.Windows.Forms.DataGridViewTextBoxColumn SendHistoryCompleteTotal;
        private System.Windows.Forms.DateTimePicker dtpEndTime;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox txtNotHasSendTotal;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.TextBox txtFxUserId;
        private System.Windows.Forms.TextBox txtAnalysisTotal;
        private System.Windows.Forms.Label label16;
    }
}

