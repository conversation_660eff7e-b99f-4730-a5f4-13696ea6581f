namespace DianGuanJiaApp.Winform
{
    partial class SyncOrder
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(SyncOrder));
            this.startBtn = new System.Windows.Forms.Button();
            this.stopBtn = new System.Windows.Forms.Button();
            this.DebugModeChk = new System.Windows.Forms.CheckBox();
            this.groupBoxOfSetting = new System.Windows.Forms.GroupBox();
            this.btnPddPushDb = new System.Windows.Forms.Button();
            this.btnPdd = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.txtShopIds = new System.Windows.Forms.TextBox();
            this.btnReSubscribePddMsg = new System.Windows.Forms.Button();
            this.button6 = new System.Windows.Forms.Button();
            this.button5 = new System.Windows.Forms.Button();
            this.button4 = new System.Windows.Forms.Button();
            this.button3 = new System.Windows.Forms.Button();
            this.button2 = new System.Windows.Forms.Button();
            this.button1 = new System.Windows.Forms.Button();
            this.LogList = new System.Windows.Forms.TextBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.syncStatusLabelFxAfterSale = new System.Windows.Forms.Label();
            this.allShopCountLabelFxAfterSale = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.syncingShopCountLabelFxAfterSale = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.waitSyncShopCountLabelFxAfterSale = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.syncStatusLabelFx = new System.Windows.Forms.Label();
            this.allShopCountLabelFx = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.syncingShopCountLabelFx = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.waitSyncShopCountLabelFx = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.syncStatusLabel = new System.Windows.Forms.Label();
            this.allShopCountLabel = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.syncingShopCountLabel = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.waitSyncShopCountLabel = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.退出ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.notifyIcon1 = new System.Windows.Forms.NotifyIcon(this.components);
            this.groupBoxOfSetting.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.contextMenuStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // startBtn
            // 
            this.startBtn.Location = new System.Drawing.Point(262, 11);
            this.startBtn.Name = "startBtn";
            this.startBtn.Size = new System.Drawing.Size(75, 23);
            this.startBtn.TabIndex = 2;
            this.startBtn.Text = "开始同步";
            this.startBtn.UseVisualStyleBackColor = true;
            this.startBtn.Click += new System.EventHandler(this.startBtn_Click);
            // 
            // stopBtn
            // 
            this.stopBtn.Enabled = false;
            this.stopBtn.Location = new System.Drawing.Point(346, 11);
            this.stopBtn.Name = "stopBtn";
            this.stopBtn.Size = new System.Drawing.Size(75, 23);
            this.stopBtn.TabIndex = 3;
            this.stopBtn.Text = "停止同步";
            this.stopBtn.UseVisualStyleBackColor = true;
            this.stopBtn.Click += new System.EventHandler(this.stopBtn_Click);
            // 
            // DebugModeChk
            // 
            this.DebugModeChk.AutoSize = true;
            this.DebugModeChk.Checked = true;
            this.DebugModeChk.CheckState = System.Windows.Forms.CheckState.Checked;
            this.DebugModeChk.Location = new System.Drawing.Point(430, 14);
            this.DebugModeChk.Name = "DebugModeChk";
            this.DebugModeChk.Size = new System.Drawing.Size(96, 16);
            this.DebugModeChk.TabIndex = 4;
            this.DebugModeChk.Text = "显示调试信息";
            this.DebugModeChk.UseVisualStyleBackColor = true;
            this.DebugModeChk.CheckedChanged += new System.EventHandler(this.DebugModeChk_CheckedChanged);
            // 
            // groupBoxOfSetting
            // 
            this.groupBoxOfSetting.Controls.Add(this.btnPddPushDb);
            this.groupBoxOfSetting.Controls.Add(this.btnPdd);
            this.groupBoxOfSetting.Controls.Add(this.label1);
            this.groupBoxOfSetting.Controls.Add(this.txtShopIds);
            this.groupBoxOfSetting.Controls.Add(this.btnReSubscribePddMsg);
            this.groupBoxOfSetting.Controls.Add(this.button6);
            this.groupBoxOfSetting.Controls.Add(this.button5);
            this.groupBoxOfSetting.Controls.Add(this.button4);
            this.groupBoxOfSetting.Controls.Add(this.button3);
            this.groupBoxOfSetting.Controls.Add(this.button2);
            this.groupBoxOfSetting.Controls.Add(this.button1);
            this.groupBoxOfSetting.Controls.Add(this.DebugModeChk);
            this.groupBoxOfSetting.Controls.Add(this.stopBtn);
            this.groupBoxOfSetting.Controls.Add(this.startBtn);
            this.groupBoxOfSetting.Location = new System.Drawing.Point(12, 12);
            this.groupBoxOfSetting.Name = "groupBoxOfSetting";
            this.groupBoxOfSetting.Size = new System.Drawing.Size(580, 133);
            this.groupBoxOfSetting.TabIndex = 5;
            this.groupBoxOfSetting.TabStop = false;
            this.groupBoxOfSetting.Text = "设置";
            // 
            // btnPddPushDb
            // 
            this.btnPddPushDb.Location = new System.Drawing.Point(466, 70);
            this.btnPddPushDb.Name = "btnPddPushDb";
            this.btnPddPushDb.Size = new System.Drawing.Size(96, 24);
            this.btnPddPushDb.TabIndex = 14;
            this.btnPddPushDb.Text = "订阅pdd推送库";
            this.btnPddPushDb.UseVisualStyleBackColor = true;
            this.btnPddPushDb.Click += new System.EventHandler(this.btnPddPushDb_Click);
            // 
            // btnPdd
            // 
            this.btnPdd.Location = new System.Drawing.Point(323, 69);
            this.btnPdd.Name = "btnPdd";
            this.btnPdd.Size = new System.Drawing.Size(137, 24);
            this.btnPdd.TabIndex = 13;
            this.btnPdd.Text = "订阅pdd消息和推送库";
            this.btnPdd.UseVisualStyleBackColor = true;
            this.btnPdd.Click += new System.EventHandler(this.btnPdd_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(145, 75);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(65, 12);
            this.label1.TabIndex = 12;
            this.label1.Text = "指定店铺Id";
            // 
            // txtShopIds
            // 
            this.txtShopIds.Location = new System.Drawing.Point(216, 72);
            this.txtShopIds.Name = "txtShopIds";
            this.txtShopIds.Size = new System.Drawing.Size(100, 21);
            this.txtShopIds.TabIndex = 11;
            // 
            // btnReSubscribePddMsg
            // 
            this.btnReSubscribePddMsg.Location = new System.Drawing.Point(11, 70);
            this.btnReSubscribePddMsg.Name = "btnReSubscribePddMsg";
            this.btnReSubscribePddMsg.Size = new System.Drawing.Size(128, 23);
            this.btnReSubscribePddMsg.TabIndex = 10;
            this.btnReSubscribePddMsg.Text = "重新订阅pdd消息";
            this.btnReSubscribePddMsg.UseVisualStyleBackColor = true;
            this.btnReSubscribePddMsg.Click += new System.EventHandler(this.btnReSubscribePddMsg_Click);
            // 
            // button6
            // 
            this.button6.Location = new System.Drawing.Point(14, 99);
            this.button6.Name = "button6";
            this.button6.Size = new System.Drawing.Size(183, 23);
            this.button6.TabIndex = 14;
            this.button6.Text = "新应用订阅用户及厂家跳灰度";
            this.button6.UseVisualStyleBackColor = true;
            this.button6.Click += new System.EventHandler(this.button6_Click);
            // 
            // button5
            // 
            this.button5.Enabled = false;
            this.button5.Location = new System.Drawing.Point(428, 40);
            this.button5.Name = "button5";
            this.button5.Size = new System.Drawing.Size(75, 23);
            this.button5.TabIndex = 9;
            this.button5.Text = "button5";
            this.button5.UseVisualStyleBackColor = true;
            this.button5.Click += new System.EventHandler(this.button5_Click);
            // 
            // button4
            // 
            this.button4.Location = new System.Drawing.Point(145, 40);
            this.button4.Name = "button4";
            this.button4.Size = new System.Drawing.Size(276, 23);
            this.button4.TabIndex = 8;
            this.button4.Text = "补充pdd配置库信息(改成在拼多多服务器运行)";
            this.button4.UseVisualStyleBackColor = true;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // button3
            // 
            this.button3.Location = new System.Drawing.Point(145, 11);
            this.button3.Name = "button3";
            this.button3.Size = new System.Drawing.Size(111, 23);
            this.button3.TabIndex = 7;
            this.button3.Text = "自动触发增量同步";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // button2
            // 
            this.button2.Location = new System.Drawing.Point(11, 40);
            this.button2.Name = "button2";
            this.button2.Size = new System.Drawing.Size(128, 23);
            this.button2.TabIndex = 6;
            this.button2.Text = "开启关联店铺迁移";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // button1
            // 
            this.button1.Location = new System.Drawing.Point(11, 11);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(128, 23);
            this.button1.TabIndex = 5;
            this.button1.Text = "开启刷新Token";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // LogList
            // 
            this.LogList.Location = new System.Drawing.Point(12, 269);
            this.LogList.Multiline = true;
            this.LogList.Name = "LogList";
            this.LogList.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.LogList.Size = new System.Drawing.Size(580, 235);
            this.LogList.TabIndex = 6;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.syncStatusLabelFxAfterSale);
            this.groupBox1.Controls.Add(this.allShopCountLabelFxAfterSale);
            this.groupBox1.Controls.Add(this.label7);
            this.groupBox1.Controls.Add(this.syncingShopCountLabelFxAfterSale);
            this.groupBox1.Controls.Add(this.label11);
            this.groupBox1.Controls.Add(this.waitSyncShopCountLabelFxAfterSale);
            this.groupBox1.Controls.Add(this.label13);
            this.groupBox1.Controls.Add(this.syncStatusLabelFx);
            this.groupBox1.Controls.Add(this.allShopCountLabelFx);
            this.groupBox1.Controls.Add(this.label6);
            this.groupBox1.Controls.Add(this.syncingShopCountLabelFx);
            this.groupBox1.Controls.Add(this.label8);
            this.groupBox1.Controls.Add(this.waitSyncShopCountLabelFx);
            this.groupBox1.Controls.Add(this.label10);
            this.groupBox1.Controls.Add(this.syncStatusLabel);
            this.groupBox1.Controls.Add(this.allShopCountLabel);
            this.groupBox1.Controls.Add(this.label5);
            this.groupBox1.Controls.Add(this.syncingShopCountLabel);
            this.groupBox1.Controls.Add(this.label3);
            this.groupBox1.Controls.Add(this.waitSyncShopCountLabel);
            this.groupBox1.Controls.Add(this.label2);
            this.groupBox1.Location = new System.Drawing.Point(12, 151);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(581, 112);
            this.groupBox1.TabIndex = 6;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "状态";
            // 
            // syncStatusLabelFxAfterSale
            // 
            this.syncStatusLabelFxAfterSale.AutoSize = true;
            this.syncStatusLabelFxAfterSale.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.syncStatusLabelFxAfterSale.ForeColor = System.Drawing.Color.Red;
            this.syncStatusLabelFxAfterSale.Location = new System.Drawing.Point(405, 70);
            this.syncStatusLabelFxAfterSale.Name = "syncStatusLabelFxAfterSale";
            this.syncStatusLabelFxAfterSale.Size = new System.Drawing.Size(92, 16);
            this.syncStatusLabelFxAfterSale.TabIndex = 20;
            this.syncStatusLabelFxAfterSale.Text = "未开始同步";
            // 
            // allShopCountLabelFxAfterSale
            // 
            this.allShopCountLabelFxAfterSale.AutoSize = true;
            this.allShopCountLabelFxAfterSale.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.allShopCountLabelFxAfterSale.Location = new System.Drawing.Point(111, 70);
            this.allShopCountLabelFxAfterSale.Name = "allShopCountLabelFxAfterSale";
            this.allShopCountLabelFxAfterSale.Size = new System.Drawing.Size(16, 16);
            this.allShopCountLabelFxAfterSale.TabIndex = 19;
            this.allShopCountLabelFxAfterSale.Text = "0";
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(12, 74);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(101, 12);
            this.label7.TabIndex = 18;
            this.label7.Text = "队列中售后店数：";
            // 
            // syncingShopCountLabelFxAfterSale
            // 
            this.syncingShopCountLabelFxAfterSale.AutoSize = true;
            this.syncingShopCountLabelFxAfterSale.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.syncingShopCountLabelFxAfterSale.Location = new System.Drawing.Point(353, 70);
            this.syncingShopCountLabelFxAfterSale.Name = "syncingShopCountLabelFxAfterSale";
            this.syncingShopCountLabelFxAfterSale.Size = new System.Drawing.Size(16, 16);
            this.syncingShopCountLabelFxAfterSale.TabIndex = 17;
            this.syncingShopCountLabelFxAfterSale.Text = "0";
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(280, 74);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(77, 12);
            this.label11.TabIndex = 16;
            this.label11.Text = "正在同步数：";
            // 
            // waitSyncShopCountLabelFxAfterSale
            // 
            this.waitSyncShopCountLabelFxAfterSale.AutoSize = true;
            this.waitSyncShopCountLabelFxAfterSale.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.waitSyncShopCountLabelFxAfterSale.Location = new System.Drawing.Point(234, 70);
            this.waitSyncShopCountLabelFxAfterSale.Name = "waitSyncShopCountLabelFxAfterSale";
            this.waitSyncShopCountLabelFxAfterSale.Size = new System.Drawing.Size(16, 16);
            this.waitSyncShopCountLabelFxAfterSale.TabIndex = 15;
            this.waitSyncShopCountLabelFxAfterSale.Text = "0";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Location = new System.Drawing.Point(153, 74);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(89, 12);
            this.label13.TabIndex = 14;
            this.label13.Text = "待同步店铺数：";
            // 
            // syncStatusLabelFx
            // 
            this.syncStatusLabelFx.AutoSize = true;
            this.syncStatusLabelFx.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.syncStatusLabelFx.ForeColor = System.Drawing.Color.Red;
            this.syncStatusLabelFx.Location = new System.Drawing.Point(404, 44);
            this.syncStatusLabelFx.Name = "syncStatusLabelFx";
            this.syncStatusLabelFx.Size = new System.Drawing.Size(92, 16);
            this.syncStatusLabelFx.TabIndex = 13;
            this.syncStatusLabelFx.Text = "未开始同步";
            // 
            // allShopCountLabelFx
            // 
            this.allShopCountLabelFx.AutoSize = true;
            this.allShopCountLabelFx.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.allShopCountLabelFx.Location = new System.Drawing.Point(110, 44);
            this.allShopCountLabelFx.Name = "allShopCountLabelFx";
            this.allShopCountLabelFx.Size = new System.Drawing.Size(16, 16);
            this.allShopCountLabelFx.TabIndex = 12;
            this.allShopCountLabelFx.Text = "0";
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(11, 48);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(101, 12);
            this.label6.TabIndex = 11;
            this.label6.Text = "队列中店铺总数：";
            // 
            // syncingShopCountLabelFx
            // 
            this.syncingShopCountLabelFx.AutoSize = true;
            this.syncingShopCountLabelFx.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.syncingShopCountLabelFx.Location = new System.Drawing.Point(352, 44);
            this.syncingShopCountLabelFx.Name = "syncingShopCountLabelFx";
            this.syncingShopCountLabelFx.Size = new System.Drawing.Size(16, 16);
            this.syncingShopCountLabelFx.TabIndex = 10;
            this.syncingShopCountLabelFx.Text = "0";
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(279, 48);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(77, 12);
            this.label8.TabIndex = 9;
            this.label8.Text = "正在同步数：";
            // 
            // waitSyncShopCountLabelFx
            // 
            this.waitSyncShopCountLabelFx.AutoSize = true;
            this.waitSyncShopCountLabelFx.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.waitSyncShopCountLabelFx.Location = new System.Drawing.Point(233, 44);
            this.waitSyncShopCountLabelFx.Name = "waitSyncShopCountLabelFx";
            this.waitSyncShopCountLabelFx.Size = new System.Drawing.Size(16, 16);
            this.waitSyncShopCountLabelFx.TabIndex = 8;
            this.waitSyncShopCountLabelFx.Text = "0";
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(152, 48);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(89, 12);
            this.label10.TabIndex = 7;
            this.label10.Text = "待同步店铺数：";
            // 
            // syncStatusLabel
            // 
            this.syncStatusLabel.AutoSize = true;
            this.syncStatusLabel.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.syncStatusLabel.ForeColor = System.Drawing.Color.Red;
            this.syncStatusLabel.Location = new System.Drawing.Point(404, 18);
            this.syncStatusLabel.Name = "syncStatusLabel";
            this.syncStatusLabel.Size = new System.Drawing.Size(92, 16);
            this.syncStatusLabel.TabIndex = 6;
            this.syncStatusLabel.Text = "未开始同步";
            // 
            // allShopCountLabel
            // 
            this.allShopCountLabel.AutoSize = true;
            this.allShopCountLabel.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.allShopCountLabel.Location = new System.Drawing.Point(110, 18);
            this.allShopCountLabel.Name = "allShopCountLabel";
            this.allShopCountLabel.Size = new System.Drawing.Size(16, 16);
            this.allShopCountLabel.TabIndex = 5;
            this.allShopCountLabel.Text = "0";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(11, 22);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(101, 12);
            this.label5.TabIndex = 4;
            this.label5.Text = "队列中店铺总数：";
            // 
            // syncingShopCountLabel
            // 
            this.syncingShopCountLabel.AutoSize = true;
            this.syncingShopCountLabel.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.syncingShopCountLabel.Location = new System.Drawing.Point(352, 18);
            this.syncingShopCountLabel.Name = "syncingShopCountLabel";
            this.syncingShopCountLabel.Size = new System.Drawing.Size(16, 16);
            this.syncingShopCountLabel.TabIndex = 3;
            this.syncingShopCountLabel.Text = "0";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(279, 22);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "正在同步数：";
            // 
            // waitSyncShopCountLabel
            // 
            this.waitSyncShopCountLabel.AutoSize = true;
            this.waitSyncShopCountLabel.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.waitSyncShopCountLabel.Location = new System.Drawing.Point(233, 18);
            this.waitSyncShopCountLabel.Name = "waitSyncShopCountLabel";
            this.waitSyncShopCountLabel.Size = new System.Drawing.Size(16, 16);
            this.waitSyncShopCountLabel.TabIndex = 1;
            this.waitSyncShopCountLabel.Text = "0";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(152, 22);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(89, 12);
            this.label2.TabIndex = 0;
            this.label2.Text = "待同步店铺数：";
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.退出ToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Size = new System.Drawing.Size(101, 26);
            // 
            // 退出ToolStripMenuItem
            // 
            this.退出ToolStripMenuItem.Name = "退出ToolStripMenuItem";
            this.退出ToolStripMenuItem.Size = new System.Drawing.Size(100, 22);
            this.退出ToolStripMenuItem.Text = "退出";
            this.退出ToolStripMenuItem.Click += new System.EventHandler(this.退出ToolStripMenuItem_Click);
            // 
            // notifyIcon1
            // 
            this.notifyIcon1.BalloonTipText = "店管家全量同步订单助手";
            this.notifyIcon1.BalloonTipTitle = "店管家全量同步订单助手";
            this.notifyIcon1.ContextMenuStrip = this.contextMenuStrip1;
            this.notifyIcon1.Icon = ((System.Drawing.Icon)(resources.GetObject("notifyIcon1.Icon")));
            this.notifyIcon1.Text = "店管家全量同步订单助手";
            this.notifyIcon1.Visible = true;
            this.notifyIcon1.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.notifyIcon1_MouseDoubleClick);
            // 
            // SyncOrder
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(605, 513);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.LogList);
            this.Controls.Add(this.groupBoxOfSetting);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "SyncOrder";
            this.Text = "全量同步订单";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.SyncOrder_FormClosing);
            this.Load += new System.EventHandler(this.SyncOrder_Load);
            this.groupBoxOfSetting.ResumeLayout(false);
            this.groupBoxOfSetting.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.contextMenuStrip1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.Button startBtn;
        private System.Windows.Forms.Button stopBtn;
        private System.Windows.Forms.CheckBox DebugModeChk;
        private System.Windows.Forms.GroupBox groupBoxOfSetting;
        private System.Windows.Forms.TextBox LogList;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.Label waitSyncShopCountLabel;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label syncingShopCountLabel;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label allShopCountLabel;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label syncStatusLabel;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem 退出ToolStripMenuItem;
        private System.Windows.Forms.NotifyIcon notifyIcon1;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.Button button3;
        private System.Windows.Forms.Button button4;
        private System.Windows.Forms.Label syncStatusLabelFx;
        private System.Windows.Forms.Label allShopCountLabelFx;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label syncingShopCountLabelFx;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label waitSyncShopCountLabelFx;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Button button5;
        private System.Windows.Forms.Label syncStatusLabelFxAfterSale;
        private System.Windows.Forms.Label allShopCountLabelFxAfterSale;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label syncingShopCountLabelFxAfterSale;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label waitSyncShopCountLabelFxAfterSale;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Button btnReSubscribePddMsg;
        private System.Windows.Forms.Button btnPdd;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.TextBox txtShopIds;
        private System.Windows.Forms.Button btnPddPushDb;
        private System.Windows.Forms.Button button6;
    }
}

