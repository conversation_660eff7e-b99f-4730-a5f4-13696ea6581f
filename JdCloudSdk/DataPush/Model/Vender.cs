/*
 * Copyright 2018 JDCLOUD.COM
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http:#www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * 
 * 
 *
 * 
 * Contact: 
 *
 * NOTE: This class is auto generated by the jdcloud code generator program.
 */


using JdCloudSdk.Annotation;

namespace JdCloudSdk.DataPush.Model
{

    /// <summary>
    ///  数据推送用户对象
    /// </summary>
    public class Vender
    {

        ///<summary>
        /// appkey
        ///Required:true
        ///</summary>
        [Required]
        public string Appkey{ get; set; }
        ///<summary>
        /// 云鼎数据库实例ID
        ///Required:true
        ///</summary>
        [Required]
        public string YdRdsInstanceId{ get; set; }
        ///<summary>
        /// 商家ID
        ///Required:true
        ///</summary>
        [Required]
        public string VenderId{ get; set; }
    }
}
