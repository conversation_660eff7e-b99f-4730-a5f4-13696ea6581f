import { useState, useEffect, useCallback } from 'react'
import './index.less';

function StatusTag(props: any) {
  // const [type] = useState(props.type);
  // const [title] = useState(props.title || '');

  useEffect(() => {

  }, [])
  return (
    <span className={"wu-badge " +
      (
        props.type == 0 ? "" :                //失效状态
        props.type == 1 ?"wu-processing":     //主色色状态
        props.type == 2 ?"wu-error":          //失败状态
        props.type == 3 ?"wu-warning":        //警告状态
        props.type == 4 ?"wu-success":        //成功状态
        props.type == 5 ?"wu-error":""      //失败状态 icon为感叹号的
      )
    }>
      {
        props.type == 5 ?
        <i className="wu-badge-gan">！</i>:
        <i className="wu-badge-dot"></i>
      }
      <s className="wu-badge-title">{props.title}</s>
    </span>
  )
}

export default StatusTag;