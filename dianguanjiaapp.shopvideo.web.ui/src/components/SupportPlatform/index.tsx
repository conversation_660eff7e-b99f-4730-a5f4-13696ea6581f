import { useState, useEffect, useCallback } from 'react'
import './index.less';
import { Modal,Form,Input,message,Button,Radio,Table,Select } from "antd";
import PlatformShow from '@/components/PlatformShow/platformShow';

function SupportPlatform(props: any) {
  const [form] = Form.useForm();
  useEffect(() => {
    setIsModalOpen(props.isShow)
  },[props.isShow])
 
  const [type]= useState(props.type || "");
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 父组件通信 - 关闭弹窗
  useEffect(() => {
    if (!isModalOpen) {
      props.close(isModalOpen)
    }
  },[isModalOpen])
  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
      setIsModalOpen(false);
  };
  // 自定义title
  const onTitleReactNode = (type: any) => {
    return (
      type === 'platform' ?
      <div className='modal-title'>选择平台<a className='dColor' href="https://docs.qq.com/sheet/DQ29TVldGTHpXWGRk?tab=BB08J2&token=BEB2803BB881104BC883809A5B526D07&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv" target="_blank">所有平台订购地址</a></div>
      : null
    )
  }
  // 平台列表数据
  const [platformList, setPlatformList] = useState<any>([
    {
      "IsAuthUrl": true,
      "PlatformType": "TouTiao",
      "PlatformName": "抖音小店",
      "AuthUrl": "http://auth.dgjapp.com/auth/douyinfxnew?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "-110px -48px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "Pinduoduo",
      "PlatformName": "拼多多",
      "AuthUrl": "http://testauth.dgjapp.com/fxauth/pdd?AuthType=3&SuccToUrl=http%3a%2f%2fpdd10.dgjapp.com%2fauth%2fauthsuccess",
      "Style": "-440px 0;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "KuaiShou",
      "PlatformName": "快手小店",
      "AuthUrl": "https://auth.dgjapp.com/fxauth/kuaishou?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "0 -48px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "Alibaba",
      "PlatformName": "阿里巴巴",
      "AuthUrl": "https://pr1688.dgjapp.com?CallUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fentrance%3fauthtype%3d3&SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess",
      "Style": "0 0;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "Taobao",
      "PlatformName": "淘宝",
      "AuthUrl": "https://taobao2.dgjapp.com?CallUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fentrance%3fauthtype%3d3&SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess",
      "Style": "-110px 0;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "AlibabaC2M",
      "PlatformName": "淘工厂",
      "AuthUrl": "http://auth.dgjapp.com/auth/alibabaC2M?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "0  -96px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "Jingdong",
      "PlatformName": "京东",
      "AuthUrl": "http://auth.dgjapp.com/fxauth/jingdong?state=%7b%22AType%22%3a%223%22%2c%22SuccToUrl%22%3a%22http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess%22%2c%22CallUrl%22%3a%22http%3a%2f%2fjd3.dgjapp.com%22%7d",
      "Style": "-330px 0;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "JingdongPurchase",
      "PlatformName": "京东供销平台",
      "AuthUrl": "http://auth.dgjapp.com/fxauth/jingdong?state=%7b%22AType%22%3a%224%22%2c%22SuccToUrl%22%3a%22http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess%22%2c%22CallUrl%22%3a%22http%3a%2f%2fjd3.dgjapp.com%22%7d",
      "Style": "-114px -288px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "XiaoHongShu",
      "PlatformName": "小红书",
      "AuthUrl": "http://auth.dgjapp.com/auth/xiaohongshuv2fx?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "-110px -144px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "TaoBaoMaiCai",
      "PlatformName": "淘宝买菜",
      "AuthUrl": "http://auth.dgjapp.com/auth/taobaomaicai?state=eyJTdWNjVG9VcmwiOiJodHRwOi8vYXV0aC5kZ2phcHAuY29tL2F1dGgvYXV0aHN1Y2Nlc3MiLCJBdXRoVHlwZSI6IjMiLCJScCI6IkJFQjI4MDNCQjg4MTEwNEJDODgzODA5QTVCNTI2RDA3In0=",
      "Style": "-115px -240px",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": false,
      "PlatformType": "WxVideo",
      "PlatformName": "微信小店",
      "AuthUrl": "https://channels.weixin.qq.com/",
      "Style": "-5px -289px",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": false,
      "PlatformType": "WxVideo",
      "PlatformName": "微信视频号",
      "AuthUrl": "https://channels.weixin.qq.com/",
      "Style": "-0px -240px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "KuaiTuanTuan",
      "PlatformName": "快团团",
      "AuthUrl": "http://auth.dgjapp.com/auth/pddktt?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "-440px -190px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "DuXiaoDian",
      "PlatformName": "度小店",
      "AuthUrl": "http://auth.dgjapp.com/fxauth/duxiaodian?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "-110px -96px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "YouZan",
      "PlatformName": "有赞",
      "AuthUrl": "https://auth.dgjapp.com/auth/youzan?state=%7b%22SuccToUrl%22%3a%22http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess%22%2c%22AuthType%22%3a%223%22%2c%22Rp%22%3a%22BEB2803BB881104BC883809A5B526D07%22%7d",
      "Style": "-330px -48px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "WeiMeng",
      "PlatformName": "微盟",
      "AuthUrl": "http://auth.dgjapp.com/auth/weimengfx?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "-440px -48px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "MoGuJie",
      "PlatformName": "蘑菇街",
      "AuthUrl": "http://auth.dgjapp.com/auth/mogujie?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "-330px -95px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "MeiLiShuo",
      "PlatformName": "美丽说",
      "AuthUrl": "http://auth.dgjapp.com/auth/meilishuo?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "-220px -95px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "MoKuai",
      "PlatformName": "魔筷星选",
      "AuthUrl": "http://auth.dgjapp.com/auth/mokuai?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "0 -144px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "WeiDian",
      "PlatformName": "微店",
      "AuthUrl": "http://auth.dgjapp.com/auth/weidian?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "-0px -190px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "SuNing",
      "PlatformName": "苏宁",
      "AuthUrl": "http://auth.dgjapp.com/auth/suning?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "-440px -144px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": false,
      "PlatformType": "TuanHaoHuo",
      "PlatformName": "团好货",
      "AuthUrl": "http://auth.dgjapp.com/auth/tuanhaohuo?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "-330px -144px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "VipShop",
      "PlatformName": "唯品会",
      "AuthUrl": "http://auth.dgjapp.com/auth/vipshop?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      "Style": "-220px -144px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": false,
      "PlatformType": "OtherPlatforms",
      "PlatformName": "其他平台",
      "AuthUrl": "",
      "Style": "-330px -288px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": false,
      "PlatformType": "OwnShop",
      "PlatformName": "自有商城",
      "AuthUrl": "",
      "Style": "-220px -288px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }, {
      "IsAuthUrl": true,
      "PlatformType": "BiliBili",
      "PlatformName": "B站会员购",
      "AuthUrl": "http://auth.dgjapp.com/fxauth/bilibili?state=%7b%22AType%22%3a%223%22%2c%22SuccToUrl%22%3a%22http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess%22%7d",
      "Style": "-440px -288px;",
      "PayUrl": null,
      "Flag": null,
      "AuthApi": null
    }
  ])
  // 平台列表数据（跨境）
  const [abroadPlatformList, setAbroadPlatformList] = useState<any>([
    {
      IsAuthUrl: true,
      PlatformType: "TikTok",
      PlatformName: "TikTok",
      AuthUrl: "http://396ec02725.zicp.vip/fxauth/crossborder?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess",
      Style: " - 110px - 48px; ",
      PayUrl: null,
      Flag: null,
      AuthApi: null
    }
  ])
  //头条订购双应用弹框提示
  const [HasTwoApp, setHasTwoApp] = useState(false);
  const [checkPlatform, setCheckPlatform] = useState<any>({
    PlatformType: "Alibaba",
    PlatformName: "",
  });
  const handlePlatformList = (item: any) => {
    checkPlatform.PlatformType = item.PlatformType;
    checkPlatform.PlatformName = item.PlatformName;
    //setIsModalOpen(false); // 关闭弹框
    setCheckPlatform(checkPlatform);
    
     // 头条订购双应用弹框提示
    if (item.PlatformType == "TouTiao" && HasTwoApp) {
      var showID = 'setNoTouTiaoChangePlatformWarn';
      // commonModule.LoadCommonSetting(showID, true, function (rsp) { //是否展示
      //     if (rsp.Success && rsp.Data != "1") {
      //         module.showTouTiaoChangePlatformDailog(showID);
      //     }
      // });
    }
    if (item.PlatformType == "Alibaba" || item.PlatformType == "KuaiShou") {
      //partnerModule.goToTouTiaoAuthModel(platform)
      
      setIsSubModalOpen(true);
    }
    if (item.PlatformType == "WxXiaoShangDian" || item.PlatformType == "WxVideo") {
      subModalData.title = <div className='modal-title'>输入小程序ID<a className='dColor' href="https://docs.qq.com/sheet/DQ29TVldGTHpXWGRk?tab=BB08J2&token=BEB2803BB881104BC883809A5B526D07&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv" target="_blank">使用教程</a></div>
      subModalData.width = 350,
      setIsSubModalOpen(true);
    }
    // 京东供销平台
    if (item.PlatformType == "JingdongPurchase") {
      setIsSubModalOpen(true);
      subModalData.title = '发货模式选择';
      subModalData.width = 560;
      subModalData.footer = <div className='wu-flex wu-betteen wu-yCenter wu-width100'>
        <span className='wu-color-a wu-operate' onClick={()=>onModalLevel3Open('changeLog')}>修改日志</span>
        <div>
          <Button color="default" variant="outlined" onClick={()=>setIsSubModalOpen(false)}>取消</Button>
          <Button color="primary" variant="solid" className='wu-mL12'>确认，立即授权</Button>
        </div>
      </div>
    }
    if (item.PlatformType === 'OtherPlatforms') { // 其他平台
      setIsSubModalOpen(true);
      subModalData.title = '选择平台';
      subModalData.width = 350;
      subModalData.selectValue = 'Other_Heliang';
      subModalData.footer = <div className='wu-flex wu-betteen wu-yCenter wu-width100'>
        <span className='wu-color-a wu-operate' ></span>
        <div>
          <Button color="primary" variant="solid"  onClick={()=>onModalLevel3Open('OtherPlatforms')}>添加</Button>
          <Button color="default" variant="outlined" className='wu-mL12' onClick={()=>setIsSubModalOpen(false)}>取消</Button>
        </div>
      </div>
    }
    if (item.PlatformType === 'OwnShop') { // 自营店铺
      setIsSubModalOpen(true);
      subModalData.title = '输入店铺名称';
      subModalData.width = 350;
    }
    if (item.PlatformType === 'TuanHaoHuo') { // 团好货
      setIsSubModalOpen(true);
      subModalData.title = <div><a href="https://www.yuque.com/dianguanjiadadan/cg2p0s/xze7lb" target="_blank">如何获取团好货的店铺ID和店铺名称？</a></div>;
      subModalData.width = 350;
      subModalData.okText = '添加';
    }
      // commonModule.Ajax({
      //     url: "/partner/HasShop",
      //     type: "POST",
      //     loading: true,
      //     data: { pt: platform },
      //     success: function (rsp) {
      //         if (rsp.Success) {
      //             // 如果返回true，就不需要弹窗了
      //             var resData = rsp.Data;
      //             if (!resData) {
      //                 DeliveryModeSelectDialog();
      //             }   
      //         }
      //     }
      // });
    //填id模式
    // if (!FillIdModel(platform, platformName)) {
    //   GotoAuthModel(platform);
    // } else {
      
    // }

    setSubModalData(subModalData);
  }
  // 选择平台 - 子弹窗
  const [isSubModalOpen, setIsSubModalOpen] = useState(false) ;
  const [subModalData, setSubModalData] = useState<any>({
    title: null,
    width: 648,
    okText: '确定',
    shopId: '',
    shopName: '',
    selectValue: '',
    radioValue: 1,
  });
  const [togglePlatformList, setTogglePlatformList] = useState<any>([
    {
      title: "店管家_分销管理(旧)",
      href: "https://auth.dgjapp.com/fxauth/kuaishou?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      imgPosition: "-420px 0",
      platform:"KuaiShou",
      isChecked: false, // 是否选中
    },
    {
      title: "店管家_分销代发(新)",
      href: "https://auth.dgjapp.com/fxauth/kuaishouv2?SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess&AuthType=3",
      imgPosition: "-140px 0",
      platform:"KuaiShou",
      isChecked: false, // 是否选中
    },
    {
      title: "店管家打单",
      href: "https://pr1688.dgjapp.com?CallUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fentrance%3fauthtype%3d3&SuccToUrl=http%3a%2f%2fauth.dgjapp.com%2fauth%2fauthsuccess",
      platform:"Alibaba",
      imgPosition: "-280px 0",
      isChecked: false, // 是否选中
    }
  ]);
  const handleTogglePlatform = (index: number) => {
    const newTogglePlatformList = [...togglePlatformList];
    newTogglePlatformList.map((item, i) => {
      item.isChecked = false;
      if (i === index) {
        item.isChecked = !item.isChecked;
      }
    })
    setTogglePlatformList(newTogglePlatformList);
  }
  // 选择平台 - 子弹窗内容
  const onSubModalContent = () => {
    return (
      checkPlatform.PlatformType === "Alibaba" || checkPlatform.PlatformType === "KuaiShou" ?
        <div className="support-platform">
          <div className="support-platform-title">请选择店铺已订购的应用类型</div>
          <div className="support-platform-content wu-flex">
            {
              togglePlatformList.map((item: any, index: number) => {
                return (
                  <div className={"support-platform-content-item" +(item.isChecked ? " active" : "")} style={checkPlatform.PlatformType=== item.platform ? {display: "block"} : {display: "none"}} key={index} onClick={() => handleTogglePlatform(index)}>
                    <div className="support-platform-content-item-img" style={{backgroundImage: "url(https://3tfxali.dgjapp.com/Content/images/toutiao-platform.png)",backgroundPosition: item.imgPosition}}></div>
                    <div className="support-platform-content-item-title">{item.title}</div>
                  </div>
                )
              })
            }
          </div>
        </div>
        : checkPlatform.PlatformType === "WxXiaoShangDian" || checkPlatform.PlatformType === "WxVideo" ?
        <div className="support-platform">
          <div className="support-platform-content">
            <div className="support-platform-content-label">例：wxf414f0****1845</div>
            <Input placeholder="输入小程序ID" allowClear onChange={(e)=>handleInputChange(e,'shopId')} />
          </div>
        </div>
        : checkPlatform.PlatformType === "TuanHaoHuo" ? // 团好货
        <div className="support-platform">
          <div className="support-platform-content">
            <div className="support-platform-content-label">输入店铺ID：</div>
            <Input placeholder="请输入店铺ID" allowClear onChange={(e)=>handleInputChange(e,'shopId')} />
            <div className="support-platform-content-label">店铺名称：</div>
            <Input placeholder="请输入店铺名称" allowClear onChange={(e)=>handleInputChange(e,'shopName')} />
          </div>
        </div>
        : checkPlatform.PlatformType === "OwnShop" ? // 自有商城
          <div className="support-platform-content">
            <div className="support-platform-content-label">例：XXXX店</div>
            <Input placeholder="请输入店铺名称" allowClear onChange={(e)=>handleInputChange(e,'shopId')} />
          </div>
        : checkPlatform.PlatformType === "JingdongPurchase" ? // 京喜供销
          <div className="delivery-mode-container">
            <div className="delivery-mode-container-card">
              <p>京东供销平台官方要求：</p>
              <p>①每个订单只能上传一个快递单号到店铺后台；②不允许拆单发货，只能整单发货。</p>
            </div>
            <div className="wu-pL16 wu-mT16">当您需要将订单中的商品推送给不同的厂家发货时，请选择</div>
            <div className="radio-box wu-mT16">
            <Radio.Group onChange={(e)=>handleInputChange(e,'radioValue')}>
              <div className="radio-box-item">
                <Radio name="radiogroup" value={1}>
                  <div>快递单号不自动上传到店铺后台，需手动到后台发货</div>
                </Radio>
              </div>
              <div className="radio-box-item">
                <Radio name="radiogroup" value={2}>
                  <div>
                    <p>所有商品发货后，店铺后台订单才变为已发货，并上传任一商品快递单号</p>
                    <p className='wu-color-c active mT4'>可能有发货后仅退款的风险，避免经济损失，请慎重选择</p>
                  </div>
                </Radio>
              </div>
            </Radio.Group>
            </div>
          </div>
        : checkPlatform.PlatformType === 'OtherPlatforms'? //其他平台
        <div className="support-platform-content">
          <div className="support-platform-content-select wu-flex wu-yCenter">
            <div className="">
              选择其他平台：
            </div>
            <Select
              defaultValue="Other_Heliang"
              style={{ width: 170 }}
              onChange={(value)=>handleSelectChange(value,'selectValue')}
              options={[
                { value: 'Other_Heliang', label: '禾量平台' },
                { value: 'Other_JuHaoMai', label: '聚好麦平台' },
              ]}
            />
          </div>
        </div>
        :null
    )
    
  }
  const handleInputChange = (e: any, key: string) => {
    subModalData[key] = e.target.value;
    setSubModalData(subModalData);
  };
  const handleSelectChange = (value: string, key: string) => {
    subModalData[key] = value;
    setSubModalData(subModalData);
  }
  // 选择平台 - 子弹窗 - 确认
  const handleSubOk = () => {
    let platform = checkPlatform.PlatformType;
    let reg = /^wx[0-9a-zA-Z]+$/;
    
    let shopId = subModalData.shopId;
    let shopName = subModalData.shopName;

    if (platform == "OtherPlatforms") {
        GotoOtherPlatforms();
    }
    
    else if (platform != "TuanHaoHuo") {
        reg = /^[0-9a-zA-Z]+$/;
        if (reg.test(shopId) != true && platform != "OwnShop") {
            message.error('请填写正确的信息');
            return;
        }
        // commmon.Ajax({
        //     url: "/Partner/AddShop",
        //     type: "POST",
        //     data: { pt: platform, shopId: shopId },
        //     loading: true,
        //     success: function (rsp) {
        //         if (rsp.Success == false) {
        //             if (rsp.ErrorCode) {
        //                 if (rsp.ErrorCode.indexOf("NOTAUTH") != -1) {
        //                     //或许购买了， 但没有点去使用
        //                     layer.alert('您可能没有订购应用，请前往服务市场订购。 【<a href="' + payurl + '" target="_blank" style="color:#3aadff;cursor:pointer;">订购链接</a>】<br/>如果您已订购，请先从服务市场进入【我的服务】，找到当前系统应用，点击【<b>去使用</b>】后再添加店铺。', { title: '绑定失败', area: ['200'], skin: 'wu-dailog' });

        //                 } else if (rsp.ErrorCode.indexOf("NOTPAY") != -1) {
        //                     //分多个应用，并没有购买分单的
        //                     layer.alert('您可能没有订购<b>分销代发</b>应用，请前往服务市场订购。 【<a href="' + payurl + '" target="_blank" style="color:#3aadff;cursor:pointer;">订购链接</a>】<br/>如果您已订购，请先从服务市场进入【我的服务】，找到<b>分销代发</b>应用，点击【<b>去使用</b>】后再添加店铺。', { title: '绑定失败', area: ['200'], skin: 'wu-dailog' });

        //                 } else {
        //                     layer.alert(rsp.Message, { icon: 2, skin: 'wu-dailog' });
        //                 }
        //             } else {
        //                 layer.alert(rsp.Message, { icon: 2, skin: 'wu-dailog' });
        //             }
        //             return;
        //         }
        //         layer.closeAll();
        //         module.Init(false);
        //     }
        // });
    }
    else {
        if ((!shopId || shopId == "") && (!shopName || shopName == "")) {
            message.error('店铺ID和店铺名称不能为空');
            return false;
        }
        else if (!shopId || shopId == "") {
            message.error('店铺ID不能为空');
            return false;
        }
        else if (!shopName || shopName == "") {
            message.error('店铺名称不能为空');
            return false;
        }
        // commonModule.Foreach(supportPlatforms, function (i, o) {
        //     if (o.PlatformType == "TuanHaoHuo") {
        //         var authurl = o.AuthUrl;
        //         authurl += "&shopId=" + shopId;
        //         authurl += "&shopName=" + shopName;
        //         authurl += "&rp=" + token;
        //         commmon.OpenNewTab(authurl);
        //         var isAddOk = layer.confirm('是否授权成功？', { icon: 3, title: '授权结果确认', btn: ['确定', '取消'], skin: 'wu-dailog' },
        //             function () {
        //                 module.Init(false);
        //                 layer.closeAll();
        //             }, function () {
        //                 module.Init(false);
        //                 layer.close(isAddOk);
        //             }
        //         );
        //     }
        // });
    }
    
    handleSubCancel();
  };
  // 其他平台
  const GotoOtherPlatforms = () => {
    let platform = subModalData.selectValue; // 平台类型
    if (platform == 'Other_Heliang') { // 禾量平台
      
    } else if(platform == 'Other_JuHaoMai'){ //聚好麦平台

    }
  }
  const handleSubCancel = () => {
    setIsSubModalOpen(false);
  };
  useEffect(() => {
    if (!isSubModalOpen) {
      setSubModalData({
        title: null,
        width: 648,
        okText: '确定',
        shopId: '',
        shopName: '',
        selectValue: '',
        radioValue: 1,
      })
    }
  }, [isSubModalOpen])
  // 修改日志弹窗表格数据
  const dataSource = [
        {
            "Id": 368,
            "FxUserId": 0,
            "SubFxUserId": 0,
            "Value": "",
            "CreateTime": "2025-03-12 14:46:15"
        },
        {
            "Id": 242,
            "FxUserId": 0,
            "SubFxUserId": 0,
            "Value": "1",
            "CreateTime": "2025-02-26 11:26:04"
        },
        {
            "Id": 235,
            "FxUserId": 0,
            "SubFxUserId": 0,
            "Value": "",
            "CreateTime": "2025-02-25 16:36:15"
        },
  ];

  const columns = [
      {
          title: '操作时间',
          dataIndex: 'CreateTime',
          key: 'CreateTime',
          width: 100
      },
      {
          title: '操作明细',
          dataIndex: 'Value',
          key: 'Value',
          width: 300,
          render: (text: string) => {
              if (text === '1') {
                  return <span>快递单号不自动上传到店铺后台，需手动到后台发货</span>;
              } else {
                return <span>所有商品发货后，店铺后台订单才变为已发货，并上传任一商品快递单号</span>;
              }
          }
        
      },
      
  ];

  
  // 选择平台 - 子弹窗(层级3弹窗)
  const [isModalLevel3Open, setIsModalLevel3Open] = useState(false) ;
  const [modalLevel3Data, setModalLevel3Data] = useState<any>({
    title: '发货模式修改日志',
    width: 640,
    okText: '确定',
    shopId: '',
    shopName: '',
    selectValue: '',
    radioValue: 1,
  });
  const handleInputLevel3Change = (e: any, key: string) => {
    modalLevel3Data[key] = e.target.value;
    setModalLevel3Data(modalLevel3Data);
  };
  const onModalLevel3Open = (type: string) => {
    if (type === 'changeLog') { // 发货模式修改日志
      modalLevel3Data.type = type;
      modalLevel3Data.footer = null;
      setModalLevel3Data(modalLevel3Data);
      setIsModalLevel3Open(true);
    } else if (type === 'OtherPlatforms') { // 其他平台
      delete modalLevel3Data.footer;
      modalLevel3Data.type = type;
      if (subModalData.selectValue === "Other_Heliang") {
        modalLevel3Data.title = <div className='modal-title'>禾量平台店铺授权
        <a className='dColor' href="https://docs.qq.com/sheet/DQ29TVldGTHpXWGRk?tab=BB08J2&token=BEB2803BB881104BC883809A5B526D07&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv" target="_blank">使用教程</a>
        </div>
      } else {
        modalLevel3Data.title = <div className='modal-title'>聚好麦平台店铺授权
        <a className='dColor' href="https://docs.qq.com/sheet/DQ29TVldGTHpXWGRk?tab=BB08J2&token=BEB2803BB881104BC883809A5B526D07&dbname=wdJM8OZZiF4gKKxb82bFQlfMufOua2Gv" target="_blank">使用教程</a>
        </div>
      }
      modalLevel3Data.width = 350;
      setModalLevel3Data(modalLevel3Data);
      setIsModalLevel3Open(true);
    }
  }
  const onModalLevel3lContent = () => {
    if (modalLevel3Data.type === 'changeLog') {
      return (
        <div className='wu-bg-fff wu-radius8 wu-flex-1 wu-mL16 wu-mR16'>
            <div className='wu-stickyTable-wrap' id="table_wrap">
              <Table className='changeLogOpenTable' dataSource={dataSource} columns={columns} pagination={false} />
            </div>
        </div>
      )
    } else if (modalLevel3Data.type === 'OtherPlatforms') {
      let platform = subModalData.selectValue; // 平台类型
      if (platform === 'Other_Heliang') { // 禾量平台
        return (
          <div className="support-platform">
            <div className="support-platform-content">
              <div className="support-platform-content-label"><i className='wu-color-b wu-mR2'>*</i>店铺名称：</div>
              <Input placeholder="店铺>店铺信息>店铺名称" allowClear onChange={(e)=>handleInputLevel3Change(e,'shopName')} />
              <div className="support-platform-content-label"><i className='wu-color-b wu-mR2'>*</i>AppId：</div>
              <Input placeholder="店铺>店铺信息>APPID" allowClear onChange={(e)=>handleInputLevel3Change(e,'appId')} />
              <div className="support-platform-content-label"><i className='wu-color-b wu-mR2'>*</i>AppSecret：</div>
              <Input placeholder="店铺>店铺信息>APPSECRET" allowClear onChange={(e)=>handleInputLevel3Change(e,'appSecret')} />
            </div>
          </div>
        )
      } else if(platform === 'Other_JuHaoMai'){ //聚好麦平台
        return (
          <div className="support-platform">
            <div className="support-platform-content">
              <div className="support-platform-content-label"><i className='wu-color-b wu-mR2'>*</i>AppId：</div>
              <Input placeholder="店铺>店铺信息>APPID" allowClear onChange={(e)=>handleInputLevel3Change(e,'appId')} />
              <div className="support-platform-content-label"><i className='wu-color-b wu-mR2'>*</i>AppSecret：</div>
              <Input placeholder="店铺>店铺信息>APPSECRET" allowClear onChange={(e)=>handleInputLevel3Change(e,'appSecret')} />
            </div>
          </div>
        )
      }
    }
  }
  // 选择平台 - 子弹窗(层级3弹窗) - 确认
  const handleModalLevel3Ok = () => {
    if (modalLevel3Data.type === 'OtherPlatforms') { // 其他平台
      let shopName = modalLevel3Data.shopName; // 店铺名称
      let appId = modalLevel3Data.appId; // AppId
      let appSecret = modalLevel3Data.appSecret; // AppSecret
      let platform = subModalData.selectValue; // 平台类型

      if ((!shopName || shopName == "") && platform == 'Other_Heliang') {
        message.error('请填写正确的店铺名称');
        return;
      }
      if (!appId || appId == "") {
        message.error('请填写正确的AppId');
        return;
      }
      if (!appSecret || appSecret == "") {
        message.error('请填写正确的AppSecret');
        return;
      }
      if (shopName.length > 32) {
        message.error('店铺名称不能超过32个字符');
        return;
      }
      setIsModalLevel3Open(false);
      // commmon.Ajax({
      //     url: "/Partner/AddShopByOtherPlatforms",
      //     type: "POST",
      //     data: { pt: platform, shopName: shopName, appId: appId, appSecret: appSecret, isBind: true },
      //     loading: true,
      //     success: function (rsp) {
      //         if (rsp.Success == false) {
      //             layer.alert(rsp.Message, { icon: 2, skin: 'wu-dailog' });
      //             return;
      //         }
      //         layer.msg("授权成功", {}, function () {
      //             layer.closeAll();
      //         });                          
      //         module.Init(false);
      //     }
      // });    
      
      
    }
  }
  useEffect(() => {
    if (!isModalLevel3Open) {
      setModalLevel3Data({
        title: '发货模式修改日志',
        width: 640,
        okText: '确定',
        shopId: '',
        shopName: '',
        selectValue: '',
        radioValue: 1,
      })
    }
  }, [isModalLevel3Open])
  return (
    <div>
      {/* 选择平台 */}
      <Modal 
        title={onTitleReactNode('platform')}
        wrapClassName='wu-modal'
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确认"
        width={648}
        cancelText="取消"
      >
        <div className='platform-content'>
          <div className="platform-domestic">
            <div className={"platform-domestic-title "+(type!=''?'wu-hide':'')}>国内平台</div>
            <div className={"platform-domestic-content wu-flex "+(type=='overseas'?'wu-hide':'')}>
              {
                platformList.map((item: any, index: number) => {
                  return (
                        <div className="platform-domestic-content-item" onClick={()=>handlePlatformList(item)}>
                          <PlatformShow size='wu-big' platform={item.PlatformType} showTooltip={true} />
                        </div>
                      )
                  })
              }
            </div>
          </div>
          <div className="platform-domestic">
            <div className={"platform-domestic-title "+(type!=''?'wu-hide':'')}>跨境平台</div>
            <div className={"platform-domestic-content wu-flex "+(type=='domestic'?'wu-hide':'')}>
              {
                abroadPlatformList.map((item: any, index: number) => {
                  return (
                        <div className="platform-domestic-content-item">
                          <PlatformShow size='wu-big' platform={item.PlatformType} showTooltip={true} />
                        </div>
                      )
                  })
              }
              
            </div>
          </div>
        </div>
      </Modal>
      {/* 二级弹窗 */}
      <Modal 
        title={subModalData.title}
        wrapClassName='wu-modal'
        open={isSubModalOpen}
        onOk={handleSubOk}
        onCancel={handleSubCancel}
        okText={subModalData.okText || "确认"}
        width={subModalData.width}
        cancelText="取消"
        footer={subModalData.footer}
      >
        {onSubModalContent()}
      </Modal>
      {/* 第三级弹窗 */}
      <Modal 
        title={modalLevel3Data.title}
        wrapClassName='wu-modal'
        open={isModalLevel3Open}
        onOk={handleModalLevel3Ok}
        onCancel={()=>setIsModalLevel3Open(false)}
        okText={modalLevel3Data.okText || "确认"}
        width={modalLevel3Data.width}
        cancelText="取消"
        footer={modalLevel3Data.footer}
      >
        {onModalLevel3lContent()}
      </Modal>
    </div>
  )
}



export default SupportPlatform;