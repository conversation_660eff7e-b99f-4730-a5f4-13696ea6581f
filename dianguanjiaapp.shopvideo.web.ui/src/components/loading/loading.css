#commonloading {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}
#commonloading .loadingWrap-content {
  width: 100px;
  height: 100px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
#commonloading .loadingWrap-content .iconfont {
  font-size: 40px;
  color: #0888ff;
  display: inline-block;
  animation: rotate linear 1.5s infinite;
}
#commonloading .loadingWrap-content .loadingWrap-title {
  font-size: 14px;
  color: #0888ff;
  margin-top: 8px;
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
