.ant-layout-content{
    margin-left: 176px;
    height: 100vh;
    
    /* Breadcrumb粘性定位样式 */
    .ant-breadcrumb {
        position: sticky;
        top: 48px;
        z-index: 100;
        padding: 12px 24px;
        background: #fff;
        box-shadow: 0 1px 2px 0 rgba(0,0,0,0.03);
    }
    padding-top: 48px;
    box-sizing: border-box;
}
.ant-layout-sider-collapsed +.ant-layout .ant-layout-content{
    margin-left: 48px;
}


.navHeader{
    display: none;
}
.ant-layout-sider{
    display: none;
}
.ant-layout-content{
    margin-left: 0;
    padding-top:0;
}
