import { useState, useEffect, useCallback, useRef } from 'react'
import { Space, Table, Tag, Flex, Button, Checkbox, Breadcrumb, Menu, Select, Modal, } from 'antd';
import type { TableProps } from 'antd';
import './ComponentPage.less';
import PlatformShow from '@/components/PlatformShow/platformShow';
import MyPagination from '@/components/Pagination/Pagination';
import ChangePlatformMenu from '@/components/ChangePlatformMenu/ChangePlatformMenu';
import StatusTag from '@/components/StatusTag/index';
import StatusTagText from '@/components/StatusTagText/index';
import ChooseAddress from '@/components/chooseAddress/chooseAddress'


import { AndroidOutlined, AppleOutlined } from '@ant-design/icons';


function ComponentPage(props: any) {
    const [isModalOpen, setIsModalOpen] = useState(false);

    useEffect(() => { }, []);

    const clickBtn = (e) => {
        console.log("e", e)
    }


    const showModal = () => {
        setIsModalOpen(true);
    };
    const handleOk = () => {
        setIsModalOpen(false);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };

    const changeCurrent = (current: number) => {
        console.log("current", current)
    }
    const changePageSize = (current: number, pageSize: number) => {
        console.log("current", current)
        console.log("pageSize", pageSize)

    }

    const getChooseAddress=(resutlData:any)=>{

        console.log("resutlData",resutlData)

    }

    return (
        <div className='wu-flex wu-column wu-mB32' style={{ backgroundColor: '#fff' }}>
            <div className='wu-m16'>
                <div className='wu-m16 wu-f16 wu-c06 wu-borderB wu-pB16'>按钮类型：已更变成店管家主题色：使用直接复制按钮color和variant</div>

                <Flex vertical gap="small">
                    <Flex gap="small" wrap onClick={(e) => clickBtn(e)}>
                        <Button color="default" variant="solid">color="default" variant="solid"</Button>
                        <Button color="default" variant="outlined">color="default" variant="outlined"</Button>
                        <Button color="default" variant="dashed">color="default" variant="dashed"</Button>
                        <Button color="default" variant="filled"> color="default" variant="filled"</Button>
                        <Button color="default" variant="text">color="default" variant="text"</Button>
                        <Button color="default" variant="link">color="default" variant="link"</Button>
                    </Flex>
                    <Flex gap="small" wrap>
                        <Button color="primary" variant="solid">color="primary" variant="solid"</Button>
                        <Button color="primary" variant="outlined">color="primary" variant="outlined"</Button>
                        <Button color="primary" variant="dashed">color="primary" variant="dashed"</Button>
                        <Button color="primary" variant="filled">color="primary" variant="filled"</Button>
                        <Button color="primary" variant="text">color="primary" variant="text"</Button>
                        <Button color="primary" variant="link">color="primary" variant="link"</Button>
                    </Flex>
                    <Flex gap="small" wrap>
                        <Button color="danger" variant="solid">color="danger" variant="solid"</Button>
                        <Button color="danger" variant="outlined">color="danger" variant="outlined"</Button>
                        <Button color="danger" variant="dashed">color="danger" variant="dashed"</Button>
                        <Button color="danger" variant="filled">color="danger" variant="filled"</Button>
                        <Button color="danger" variant="text">color="danger" variant="text"</Button>
                        <Button color="danger" variant="link">color="danger" variant="link"</Button>
                    </Flex>
                    <Flex gap="small" wrap>
                        <Button color="pink" variant="solid">color="pink" variant="solid"</Button>
                        <Button color="pink" variant="outlined">color="pink" variant="outlined"</Button>
                        <Button color="pink" variant="dashed">color="pink" variant="dashed"</Button>
                        <Button color="pink" variant="filled">color="pink" variant="filled"</Button>
                        <Button color="pink" variant="text">color="pink" variant="text"</Button>
                        <Button color="pink" variant="link">color="pink" variant="link"</Button>
                    </Flex>
                    <Flex gap="small" wrap>
                        <Button color="purple" variant="solid">color="purple" variant="solid"</Button>
                        <Button color="purple" variant="outlined">color="purple" variant="outlined"</Button>
                        <Button color="purple" variant="dashed">color="purple" variant="dashed"</Button>
                        <Button color="purple" variant="filled">color="purple" variant="filled"</Button>
                        <Button color="purple" variant="text">color="purple" variant="text"</Button>
                        <Button color="purple" variant="link">color="purple" variant="link"</Button>
                    </Flex>
                    <Flex gap="small" wrap>
                        <Button color="cyan" variant="solid">color="cyan" variant="solid"</Button>
                        <Button color="cyan" variant="outlined">color="cyan" variant="outlined"</Button>
                        <Button color="cyan" variant="dashed">color="cyan" variant="dashed"</Button>
                        <Button color="cyan" variant="filled">color="cyan" variant="filled"</Button>
                        <Button color="cyan" variant="text">color="cyan" variant="text"</Button>
                        <Button color="cyan" variant="link">color="cyan" variant="link"</Button>
                    </Flex>
                </Flex>

            </div>

            <div className='wu-m16'>
                <div className='wu-m16 wu-f16 wu-c09 wu-borderB wu-pB16 '>平台名称、平台logo</div>
                <div className='wu-mB16'>
                    <div className='wu-flex'>
                        <div className='wu-mR36'>
                            <div className='wu-mB8 wu-c06'>大图：(size='wu-big')</div>
                            <PlatformShow size='wu-big' platform='Taobao' />
                        </div>
                        <div className='wu-mR36'>
                            <div className='wu-mB8 wu-c06'>鼠标移上显示平台名称</div>
                            <PlatformShow size='wu-big' platform='Taobao' showTooltip={true} />
                        </div>
                        <div className='wu-mR36'>
                            <div className='wu-mB8 wu-c06'>直接显示平台名称</div>
                            <PlatformShow size='wu-big' platform='Taobao' showTitie={true} />
                        </div>
                    </div>
                </div>
                <div className='wu-mB16'>
                    <div className='wu-flex'>
                        <div className='wu-mR36'>
                            <div className='wu-mB8 wu-c06'>中图：(size='wu-mid')</div>
                            <PlatformShow size='wu-mid' platform='Taobao' />
                        </div>
                        <div className='wu-mR36'>
                            <div className='wu-mB8 wu-c06'>鼠标移上显示平台名称</div>
                            <PlatformShow size='wu-mid' platform='Taobao' showTooltip={true} />
                        </div>
                        <div className='wu-mR36'>
                            <div className='wu-mB8 wu-c06'>直接显示平台名称</div>
                            <PlatformShow size='wu-mid' platform='Taobao' showTitie={true} />
                        </div>
                    </div>
                </div>
                <div className='wu-mB16'>
                    <div className='wu-flex'>
                        <div className='wu-mR36'>
                            <div className='wu-mB8 wu-c06'>小图：(size='wu-small')</div>
                            <PlatformShow size='wu-small' platform='Taobao' />
                        </div>
                        <div className='wu-mR36'>
                            <div className='wu-mB8 wu-c06'>鼠标移上显示平台名称</div>
                            <PlatformShow size='wu-small' platform='Taobao' showTooltip={true} />
                        </div>
                        <div className='wu-mR36'>
                            <div className='wu-mB8 wu-c06'>直接显示平台名称</div>
                            <PlatformShow size='wu-small' platform='Taobao' showTitie={true} />
                        </div>
                    </div>
                </div>
            </div>

            <div className='wu-m16'>
                <div className='wu-mB24'>
                    <div className='wu-m16 wu-f16 wu-c09 wu-borderB wu-pB16 '>Modal:对话框(已更新店管家样式)</div>
                    <div className='wu-m16 wu-c06'>信息处理类弹窗</div>
                    <div className='wu-mB16'>
                        <Button type="primary" onClick={showModal}>
                            Open Modal
                        </Button>
                        <Modal title="Basic Modal"
                            wrapClassName='wu-modal'
                            open={isModalOpen}
                            onOk={handleOk}
                            onCancel={handleCancel}
                            okText="确认"
                            cancelText="取消"

                        >
                            <div>
                                aabb
                            </div>
                            <div>
                                bbcc
                            </div>
                        </Modal></div>
                </div>

                <div className='wu-mB24'>
                    <div className='wu-m16 wu-c06'>单文本提示</div>
                    <div className='wu-mB16 modalWrap'>
                        <Button onClick={() => {
                            Modal.confirm({
                                title: false,
                                content: (
                                    <div className='wu-c06'>
                                        <p>描述文案描述文案描述文案描述文</p>
                                        <p>案描述文案描述文案描述文案描述文案。</p>
                                    </div>
                                ),
                                wrapClassName: 'modalconfirm-320 wu-closeIcon',
                                onOk() { },
                                onCancel() { },
                                closable: true,
                                okText: "确认",
                                cancelText: "取消"
                            })


                        }}>base</Button>

                        <Button onClick={() => {
                            Modal.confirm({
                                title: '这里是常规信息提示内容',
                                content: (
                                    <div className='wu-c06'>
                                        <p>描述文案描述文案描述文案描述文</p>
                                        <p>案描述文案描述文案描述文案描述文案。</p>
                                    </div>
                                ),
                                wrapClassName: 'modalconfirm-320',
                                icon: <i className='iconfont confirmicon icon-a-info-circle-filled1x wu-f20 wu-color-a'></i>,
                                onOk() { },
                                onCancel() { },
                                closable: true,
                                okText: "确认",
                                cancelText: "取消"
                            })


                        }}>Info</Button>

                        <Button onClick={() => {
                            Modal.confirm({
                                title: '这里是常规信息提示内容',
                                content: (
                                    <div className='wu-c06'>
                                        <p>描述文案描述文案描述文案描述文</p>
                                        <p>案描述文案描述文案描述文案描述文案。</p>
                                    </div>
                                ),
                                wrapClassName: 'modalconfirm-320',
                                icon: <i className='iconfont confirmicon icon-a-check-circle-filled1x wu-f20 wu-color-d'></i>,
                                onOk() { },
                                onCancel() { },
                                closable: true,
                                okText: "确认",
                                cancelText: "取消"
                            })


                        }}>success</Button>


                        <Button onClick={() => {
                            Modal.confirm({
                                title: '这里是常规信息提示内容',
                                content: (
                                    <div className='wu-c06'>
                                        <p>描述文案描述文案描述文案描述文</p>
                                        <p>案描述文案描述文案描述文案描述文案。</p>
                                    </div>
                                ),
                                wrapClassName: 'modalconfirm-320 modalcerror',
                                icon: <i className='iconfont confirmicon icon-a-error-circle-filled1x wu-f20 wu-color-b'></i>,
                                onOk() { },
                                onCancel() { },
                                closable: true,
                                okText: "确认",
                                cancelText: "取消"
                            })
                        }}>error</Button>

                    </div>
                </div>
            </div>



            <div className='wu-m16'>
                <div className='wu-m16 wu-f16 wu-c09 wu-borderB wu-pB16 '>分页</div>
                <div className='wu-mB16'>
                    <div className='wu-flex'>
                        <div className='wu-mR36'>
                            <div className='wu-mB8 wu-c06'>样式一</div>
                            <MyPagination
                                current={1}
                                pageSize={10}
                                total={100}
                                changeCurrent={changeCurrent}
                                changePageSize={changePageSize}
                                pageSizeOptions={[10, 20, 50, 100, 200]}
                                skin='wu-skin'
                            />
                        </div>

                    </div>
                </div>

                <div className='wu-mB16'>
                    <div className='wu-flex'>
                        <div className='wu-mR36'>
                            <div className='wu-mB8 wu-c06'>样式二</div>
                            <MyPagination
                                current={1}
                                pageSize={10}
                                total={100}
                                changeCurrent={changeCurrent}
                                changePageSize={changePageSize}
                                pageSizeOptions={[10, 20, 50, 100, 200]}
                                skin='wu-skin wu-two'
                            />
                        </div>

                    </div>
                </div>
                <div className='wu-mB16'>
                    <div className='wu-flex'>
                        <div className='wu-mR36'>
                            <div className='wu-mB8 wu-c06'>样式三</div>
                            <MyPagination
                                current={1}
                                pageSize={10}
                                total={100}
                                changeCurrent={changeCurrent}
                                changePageSize={changePageSize}
                                pageSizeOptions={[10, 20, 50, 100, 200]}
                                skin='wu-skin wu-three'
                            />
                        </div>

                    </div>
                </div>
            </div>




            <div className='wu-m16'>
                <div className='wu-m16 wu-f16 wu-c09 wu-borderB wu-pB16 '>平台切换</div>
                <div className='wu-mB16'>
                    <ChangePlatformMenu isHideAgents={true} />
                </div>
                <div className='wu-mB16'>
                    <div className='wu-mB8 wu-c06'>展示平台商家</div>
                    <ChangePlatformMenu  />
                </div>
            </div>

            <div className='wu-m16'>
                <div className='wu-m16 wu-f16 wu-c09 wu-borderB wu-pB16 '>状态(一)</div>
                <ul className='wu-flex'>
                    <li className='wu-mR16'>
                        <StatusTag type={0} title="失效状态"/>
                    </li>
                    <li className='wu-mR16'>
                        <StatusTag type={1} title="主色色状态"/>
                    </li>
                    <li className='wu-mR16'>
                        <StatusTag type={2} title="失败状态"/>
                    </li>
                    <li className='wu-mR16'>
                        <StatusTag type={3} title="警告状态"/>
                    </li>
                    <li className='wu-mR16'>
                        <StatusTag type={4} title="成功状态"/>
                    </li>
                    <li className='wu-mR16'>
                        <StatusTag type={5} title="失败状态"/>
                    </li>
                </ul>
            </div>

            <div className='wu-m16'>
                <div className='wu-m16 wu-f16 wu-c09 wu-borderB wu-pB16 '>状态(二)</div>
                <ul className='wu-flex'>
                    <li className='wu-mR16 wu-flex wu-column'>
                        <StatusTagText type={0} color={0} title="标题说明"/><br/>
                        <StatusTagText type={1} color={0} title="标题说明"/><br/>
                        <StatusTagText type={2} color={0} title="标题说明"/><br/>
                        <StatusTagText type={3} color={0} title="标题说明"/>
                    </li>
                    <li className='wu-mR16 wu-flex wu-column'>
                        <StatusTagText type={0} color={1} title="标题说明"/><br/>
                        <StatusTagText type={1} color={1} title="标题说明"/><br/>
                        <StatusTagText type={2} color={1} title="标题说明"/><br/>
                        <StatusTagText type={3} color={1} title="标题说明"/>
                    </li>
                    <li className='wu-mR16 wu-flex wu-column'>
                        <StatusTagText type={0} color={2} title="标题说明"/><br/>
                        <StatusTagText type={1} color={2} title="标题说明"/><br/>
                        <StatusTagText type={2} color={2} title="标题说明"/><br/>
                        <StatusTagText type={3} color={2} title="标题说明"/>
                    </li>
                    <li className='wu-mR16 wu-flex wu-column'>
                        <StatusTagText type={0} color={3} title="标题说明"/><br/>
                        <StatusTagText type={1} color={3} title="标题说明"/><br/>
                        <StatusTagText type={2} color={3} title="标题说明"/><br/>
                        <StatusTagText type={3} color={3} title="标题说明"/>
                    </li>
                    <li className='wu-mR16 wu-flex wu-column'>
                        <StatusTagText type={0} color={4} title="标题说明"/><br/>
                        <StatusTagText type={1} color={4} title="标题说明"/><br/>
                        <StatusTagText type={2} color={4} title="标题说明"/><br/>
                        <StatusTagText type={3} color={4} title="标题说明"/>
                    </li>
                </ul>
            </div>



            <div className='wu-m16'>
                <div className='wu-m16 wu-f16 wu-c09 wu-borderB wu-pB16 '>选择地址</div>
                <ChooseAddress defaultAddress={['广东省', '深圳市', '福田区']} defaultWidth={500} chooseAddress={getChooseAddress}></ChooseAddress>

            </div>




        </div>
    )
}



export default ComponentPage;