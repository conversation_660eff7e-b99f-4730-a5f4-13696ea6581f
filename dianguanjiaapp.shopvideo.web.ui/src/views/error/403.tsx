import { Button, Result } from "antd";
import {useNavigate} from 'react-router-dom';
import '@/views/error/error.less';
function NotAuthorized(){
    const navigate = useNavigate()
    const handleClick = () => {
      navigate('/')
    }

    return <Result status={403} subTitle="抱歉，您无权访问页面！" extra={
        <Button type="primary"  onClick={handleClick}>返回首页</Button>
    } />
}

export default NotAuthorized;