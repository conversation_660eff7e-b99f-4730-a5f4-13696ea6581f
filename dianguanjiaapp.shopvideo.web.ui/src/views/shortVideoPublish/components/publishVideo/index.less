.changeTabsWrapper {
    display: flex;
    gap: 8px;

    &>li {
        min-width: 240px;
        border-radius: 6px;
        box-sizing: border-box;
        border: 1px solid rgba(0, 0, 0, 0.1);
        padding: 12px;
        background: #FFFFFF;
        cursor: pointer;
        overflow: hidden;

        &:hover {
            background: rgba(8, 136, 255, 0.08);
        }

        &.disabled {
            // pointer-events: none;
            cursor: not-allowed;
            background: rgba(0, 0, 0, 0.05);
        }

        &.active {
            background: rgba(8, 136, 255, 0.08);
            border: 1px solid #0888FF;
            position: relative;

            &::after {
                content: '';
                position: absolute;
                bottom: 0px;
                right: 0px;
                width: 0;
                height: 0;
                border-left: 8px solid transparent;
                border-top: 8px solid transparent;
                border-right: 8px solid #0888FF;
                border-bottom: 8px solid #0888FF;
            }

            &::before {
                content: '✔';
                position: absolute;
                bottom: -2px;
                right: 0px;
                color: #FFFFFF;
                font-size: 10px;
                z-index: 4;
            }
        }
    }
}

.disabledSelectedProductWrap {
    width: 144px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 6px 12px;
    background: #FFFFFF;
    box-sizing: border-box;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.uploadVideoWrapper {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    &>.uploadVideoItems {
        width: 72px;
        height: 96px;
        box-sizing: border-box;
        border: 0.5px solid rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        cursor: pointer;
        overflow: hidden;
        position: relative;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.9);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        &.uploadContent {
            border: 0.5px dashed rgba(0, 0, 0, 0.14);
            background: rgba(0, 0, 0, 0.04);

            &:hover {
                &>i {
                    color: #0888FF;
                }

                &>span {
                    color: #0888FF;
                }
            }
        }

        &.active {
            border: 1px solid #0888FF;

            &::after {
                content: '';
                position: absolute;
                bottom: 0px;
                right: 0px;
                width: 0;
                height: 0;
                border-left: 8px solid transparent;
                border-top: 8px solid transparent;
                border-right: 8px solid #0888FF;
                border-bottom: 8px solid #0888FF;
            }

            &::before {
                content: '✔';
                position: absolute;
                bottom: -2px;
                right: 0px;
                color: #FFFFFF;
                font-size: 10px;
                z-index: 4;
            }
        }

        &>.timeDuration {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 34px;
            height: 16px;
            border-radius: 2px;
            position: absolute;
            bottom: 8px;
            left: 19px;
            color: rgba(255, 255, 255, 0.9);
            background: rgba(0, 0, 0, 0.3);
            font-size: 12px;
            padding: 0px 2px;
            box-sizing: border-box;
        }

        &>.removeIcon {
            display: none;
            align-items: center;
            justify-content: center;
            position: absolute;
            bottom: 0px;
            left: 0px;
            right: 0px;
            width: 100%;
            height: 22px;
            background: rgba(0, 0, 0, 0.4);
            padding: 4px;
            box-sizing: border-box;
            z-index: 5;

            &>i {
                color: rgba(255, 255, 255, 0.9);
            }
        }

        &:hover {
            border-color: #0888FF !important;

            .timeDuration {
                display: none;
            }

            .removeIcon {
                display: flex;
            }
        }

        &.uploadLoading {
            background: #FFFFFF;
        }
    }
}

.uploadCoverImagesContent {
    li {
        width: 72px;
        height: 96px;
        box-sizing: border-box;
        border: 0.5px solid rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        background-size: cover;
        cursor: pointer;
        overflow: hidden;
        position: relative;

        &.uploadContent {
            border: 0.5px dashed rgba(0, 0, 0, 0.14);
            background: rgba(0, 0, 0, 0.04);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            &:hover {
                &>i {
                    color: #0888FF;
                }

                &>span {
                    color: #0888FF;
                }
            }
        }

        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0);
            /* 初始状态下没有蒙层 */
            transition: background-color 0.3s ease;
            /* 添加平滑过渡效果 */
        }

        &:hover {
            &::after {
                background-color: rgba(0, 0, 0, 0.4);
            }
        }
    }
}

.uploadCoverImagesWrapper {
    display: flex;
    gap: 8px;

    &>.uploadCoverImageItem {
        width: 56px;
        height: 76px;
        border-radius: 4px;
        position: relative;
        background-size: cover;
        cursor: pointer;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;

        &>.applicationCoverImageBtn {
            display: none;
            width: 36px;
            height: 18px;
            font-size: 12px;
            border-radius: 4px;
        }

        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0);
            /* 初始状态下没有蒙层 */
            transition: background-color 0.3s ease;
            /* 添加平滑过渡效果 */
        }

        &:hover {
            &::after {
                background-color: rgba(0, 0, 0, 0.4);
            }

            .applicationCoverImageBtn {
                display: inline-flex;
                z-index: 6;
            }
        }
    }
}

.videoDescriptionWrapper {
    width: 394px;
    min-height: 200px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0 12px 12px 12px;
    box-sizing: border-box;
    border-radius: 6px;
    position: relative;

    .ant-input-affix-wrapper {
        padding: 8px 0 !important;
    }

    .ant-input {
        padding: 0 !important;
    }

    &.descriptionHover {
        border-color: #0888FF;
    }

    &.descriptionActive {
        border-color: #0888FF;
        box-shadow: 0 0 0 2px rgba(5, 175, 255, 0.1);
    }
}

.ant-drawer-body {
    padding: 0 !important;
}

.videoPreviewContainer {
    border-radius: 40px;
    height: 609px;
    overflow: hidden;
    position: relative;
    width: 294px;

    .videoPreviewContentBg {
        background-color: #000;
        border-radius: 30px;
        left: 12px;
        position: absolute;
        top: 8px;
        width: 275px;
        height: 594px;
    }

    .videoPreviewContentMask {
        height: 609px;
        position: relative;
        width: 294px;
    }

    .videoPreviewCoverImage {
        background-color: #000;
        left: 12px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 275px;
    }

    .videoPreviewNoDataText {
        color: #9E9E9E;
        font-size: 14px;
        left: 50%;
        line-height: 20px;
        position: absolute;
        top: 50%;
        transform: translate3d(-50%, -50%, 0);
    }
}

.activePublishBtn {
    .ant-modal-footer {
        .ant-btn-primary {
            background: rgba(8, 136, 255, 0.3) !important;
            pointer-events: none !important;
        }
    }
}

.publishTableContent {
    .ant-table-cell {
        line-height: 16px;
        padding: 9px 12px !important;
        box-sizing: border-box;
        height: 40px;
    }
    .custom-row {
        &:hover {
            background-color: #e6f7ff !important;
        }
    }
    .ant-table-thead {
        &>tr {
            &>th {
                &::before {
                    width: 0px !important;
                }
                font-size: 12px !important;
            }
        }
    }
    .ant-table-tbody {
        &>.ant-table-row {
            &.ant-table-row-selected {
                &>.ant-table-cell {
                    background: transparent !important;
                }
            }
            .ant-table-cell-row-hover {
                background-color: #e6f7ff !important;
            }
        }
    }
}
