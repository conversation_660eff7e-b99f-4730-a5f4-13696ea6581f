using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Newtonsoft.Json;
using Jd.Api.Domain;
namespace Jd.Api.Domain
{

[Serializable]
public class ChargeCycleModel:JdObject{
      [JsonProperty("service_code")]
public 				string

                                                                                     serviceCode
 { get; set; }
      [JsonProperty("service_id")]
public 				long

                                                                                     serviceId
 { get; set; }
      [JsonProperty("item_id")]
public 				long

                                                                                     itemId
 { get; set; }
      [JsonProperty("item_code")]
public 				string

                                                                                     itemCode
 { get; set; }
      [JsonProperty("charge_days")]
public 				int

                                                                                     chargeDays
 { get; set; }
      [JsonProperty("price")]
public 				long

             price
 { get; set; }
      [JsonProperty("created")]
public 				DateTime

             created
 { get; set; }
      [JsonProperty("modified")]
public 					DateTime

             modified
 { get; set; }
      [JsonProperty("page_display")]
public 				int

                                                                                     pageDisplay
 { get; set; }
	}
}
