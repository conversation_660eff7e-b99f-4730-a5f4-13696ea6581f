using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Newtonsoft.Json;
using Jd.Api.Domain;
namespace Jd.Api.Domain
{

[Serializable]
public class IsvDayStockLog:JdObject{
      [JsonProperty("goodsNo")]
public 				string

             goodsNo
 { get; set; }
      [JsonProperty("goodsName")]
public 				string

             goodsName
 { get; set; }
      [JsonProperty("deptNo")]
public 				string

             deptNo
 { get; set; }
      [JsonProperty("warehouseNo")]
public 				string

             warehouseNo
 { get; set; }
      [JsonProperty("purchaseInstoreNum")]
public 				int

             purchaseInstoreNum
 { get; set; }
      [JsonProperty("orderOutstoreNum")]
public 				int

             orderOutstoreNum
 { get; set; }
      [JsonProperty("returnInstoreNum")]
public 				int

             returnInstoreNum
 { get; set; }
      [JsonProperty("returnOutstoreNum")]
public 				int

             returnOutstoreNum
 { get; set; }
      [JsonProperty("stockOverNum")]
public 				int

             stockOverNum
 { get; set; }
      [JsonProperty("stockShortNum")]
public 				int

             stockShortNum
 { get; set; }
	}
}
