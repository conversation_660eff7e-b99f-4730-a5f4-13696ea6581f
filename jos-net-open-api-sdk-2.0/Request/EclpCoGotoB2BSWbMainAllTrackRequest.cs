using System;
using System.Collections.Generic;
using Jd.Api.Response;
using Jd.Api.Util;
namespace Jd.Api.Request
{
    public class EclpCoGotoB2BSWbMainAllTrackRequest : JdRequestBase<EclpCoGotoB2BSWbMainAllTrackResponse>
    {
                                                                                  public  		string
              deptNo
 {get; set;}
                                                          
                                                          public  		string
              newWBType
 {get; set;}
                                                          
                                                          public  		string
              no
 {get; set;}
                                                          
                                             public override string ApiName
            {
                get{return "jingdong.eclp.co.gotoB2BSWbMainAllTrack";}
            }
            protected override void PrepareParam(IDictionary<String, Object> parameters)
            {
                                                                                                parameters.Add("deptNo", this.            deptNo
);
                                                                                                        parameters.Add("newWBType", this.            newWBType
);
                                                                                                        parameters.Add("no", this.            no
);
                                                                                                    }
    }
}





        
 

