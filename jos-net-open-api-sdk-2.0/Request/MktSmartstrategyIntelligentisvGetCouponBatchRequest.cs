using System;
using System.Collections.Generic;
using Jd.Api.Response;
using Jd.Api.Util;
namespace Jd.Api.Request
{
    public class MktSmartstrategyIntelligentisvGetCouponBatchRequest : JdRequestBase<MktSmartstrategyIntelligentisvGetCouponBatchResponse>
    {
                                                                                  public  		string
              putKey
 {get; set;}
                                                          
            public override string ApiName
            {
                get{return "jingdong.mkt.smartstrategy.intelligentisv.getCouponBatch";}
            }
            protected override void PrepareParam(IDictionary<String, Object> parameters)
            {
                                                                                                parameters.Add("putKey", this.            putKey
);
                                                    }
    }
}





        
 

