using System;
using System.Collections.Generic;
using Jd.Api.Response;
using Jd.Api.Util;
namespace Jd.Api.Request
{
    public class MktSmartstrategyIntelligentisvGetISVPlanEffectListRequest : JdRequestBase<MktSmartstrategyIntelligentisvGetISVPlanEffectListResponse>
    {
                                                                                  public  		Nullable<int>
              pageNo
 {get; set;}
                                                          
                                                          public  		Nullable<int>
              pageSize
 {get; set;}
                                                          
            public override string ApiName
            {
                get{return "jingdong.mkt.smartstrategy.intelligentisv.getISVPlanEffectList";}
            }
            protected override void PrepareParam(IDictionary<String, Object> parameters)
            {
                                                                                                parameters.Add("pageNo", this.            pageNo
);
                                                                                                        parameters.Add("pageSize", this.            pageSize
);
                                                    }
    }
}





        
 

