using System;
using System.Collections.Generic;
using Jd.Api.Response;
using Jd.Api.Util;
namespace Jd.Api.Request
{
    public class VenderShopcategoryFindShopCategoriesByVenderIdRequest : JdRequestBase<VenderShopcategoryFindShopCategoriesByVenderIdResponse>
    {
                                                                     public override string ApiName
            {
                get{return "jingdong.vender.shopcategory.findShopCategoriesByVenderId";}
            }
            protected override void PrepareParam(IDictionary<String, Object> parameters)
            {
                                                                                            }
    }
}





        
 

