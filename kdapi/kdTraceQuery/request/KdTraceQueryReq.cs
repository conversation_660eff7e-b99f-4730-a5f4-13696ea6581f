using System;
using Base;
using Utils;
using kdTraceQueryRsp;

namespace kdTraceQueryReq
{
    public class KdTraceQueryReq : BaseRequest
    {
		public MailNos mailNos { get; set; }
       
		public override String obtainServiceType()
        {
            return "KD_TRACE_QUERY";

        }

		public override BaseResponse makeResponse(string rsp, string format)
        {
           if ("XML".Equals(format, StringComparison.InvariantCultureIgnoreCase)) {
               return XmlUtils.xmlToObj(rsp,typeof(KdTraceQueryRsp)) as KdTraceQueryRsp;
           }
           return JsonParser.jsonToObj<KdTraceQueryRsp>(rsp);
        }


    }
}