using System.Runtime.Serialization;
using System.Collections.Generic;

namespace Dop.Api.AfterSaleDetail.Data
{
	public class RecordLogsListItem
	{
		[DataMemberAttribute(Name = "operator")]
		public string Operator { get; set; }

		[DataMemberAttribute(Name = "time")]
		public string Time { get; set; }

		[DataMemberAttribute(Name = "text")]
		public string Text { get; set; }

		[DataMemberAttribute(Name = "images")]
		public List<string> Images { get; set; }

		[DataMemberAttribute(Name = "desc_kvs")]
		public List<DescKvsItem> DescKvs { get; set; }

		[DataMemberAttribute(Name = "action")]
		public string Action { get; set; }

		[DataMemberAttribute(Name = "role")]
		public long? Role { get; set; }

		[DataMemberAttribute(Name = "all_evidence")]
		public List<AllEvidenceItem> AllEvidence { get; set; }

	}
}
