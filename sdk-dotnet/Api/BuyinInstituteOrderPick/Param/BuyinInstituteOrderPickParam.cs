using System.Runtime.Serialization;

namespace Dop.Api.BuyinInstituteOrderPick.Param
{
	public class BuyinInstituteOrderPickParam
	{
		[DataMemberAttribute(Name = "size")]
		public long? Size { get; set; }

		[DataMemberAttribute(Name = "cursor")]
		public string Cursor { get; set; }

		[DataMemberAttribute(Name = "start_time")]
		public string StartTime { get; set; }

		[DataMemberAttribute(Name = "end_time")]
		public string EndTime { get; set; }

		[DataMemberAttribute(Name = "product_id")]
		public string ProductId { get; set; }

		[DataMemberAttribute(Name = "search_type")]
		public long? SearchType { get; set; }

		[DataMemberAttribute(Name = "order_ids")]
		public string OrderIds { get; set; }

	}
}
