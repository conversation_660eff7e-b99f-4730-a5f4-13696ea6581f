using System.Runtime.Serialization;
using System.Collections.Generic;

namespace Dop.Api.FreightTemplateDetail.Data
{
	public class ColumnsItem
	{
		[DataMemberAttribute(Name = "delivery_fulfillment_mode")]
		public long? DeliveryFulfillmentMode { get; set; }

		[DataMemberAttribute(Name = "rule_type")]
		public long? RuleType { get; set; }

		[DataMemberAttribute(Name = "fixed_amount")]
		public long? FixedAmount { get; set; }

		[DataMemberAttribute(Name = "end_time")]
		public long? EndTime { get; set; }

		[DataMemberAttribute(Name = "reason")]
		public string Reason { get; set; }

		[DataMemberAttribute(Name = "first_weight")]
		public double? FirstWeight { get; set; }

		[DataMemberAttribute(Name = "first_weight_price")]
		public double? FirstWeightPrice { get; set; }

		[DataMemberAttribute(Name = "first_num")]
		public long? FirstNum { get; set; }

		[DataMemberAttribute(Name = "first_num_price")]
		public double? FirstNumPrice { get; set; }

		[DataMemberAttribute(Name = "add_weight")]
		public double? AddWeight { get; set; }

		[DataMemberAttribute(Name = "add_weight_price")]
		public double? AddWeightPrice { get; set; }

		[DataMemberAttribute(Name = "add_num")]
		public long? AddNum { get; set; }

		[DataMemberAttribute(Name = "add_num_price")]
		public double? AddNumPrice { get; set; }

		[DataMemberAttribute(Name = "is_default")]
		public long? IsDefault { get; set; }

		[DataMemberAttribute(Name = "is_limited")]
		public bool? IsLimited { get; set; }

		[DataMemberAttribute(Name = "is_over_free")]
		public bool? IsOverFree { get; set; }

		[DataMemberAttribute(Name = "over_weight")]
		public double? OverWeight { get; set; }

		[DataMemberAttribute(Name = "over_amount")]
		public long? OverAmount { get; set; }

		[DataMemberAttribute(Name = "over_num")]
		public long? OverNum { get; set; }

		[DataMemberAttribute(Name = "province_infos")]
		public List<ProvinceInfosItem> ProvinceInfos { get; set; }

	}
}
