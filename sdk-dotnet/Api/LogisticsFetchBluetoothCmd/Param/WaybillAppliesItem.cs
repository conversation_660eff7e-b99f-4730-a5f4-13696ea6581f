using System.Runtime.Serialization;

namespace Dop.Api.LogisticsFetchBluetoothCmd.Param
{
	public class WaybillAppliesItem
	{
		[DataMemberAttribute(Name = "track_no")]
		public string TrackNo { get; set; }

		[DataMemberAttribute(Name = "std_template_code")]
		public string StdTemplateCode { get; set; }

		[DataMemberAttribute(Name = "cus_template_url")]
		public string CusTemplateUrl { get; set; }

		[DataMemberAttribute(Name = "cus_template_code")]
		public string CusTemplateCode { get; set; }

		[DataMemberAttribute(Name = "cus_print_data")]
		public string CusPrintData { get; set; }

	}
}
