using System;
using Dop.Core;
using Dop.Api.LogisticsPushOnlineServiceConfig.Param;

namespace Dop.Api.LogisticsPushOnlineServiceConfig
{
	public class LogisticsPushOnlineServiceConfigRequest : DoudianOpApiRequest<LogisticsPushOnlineServiceConfigParam>
	{
		public override string GetUrlPath()
		{
			return "/logistics/pushOnlineServiceConfig";
		}

		public override Type GetResponseType()
		{
			return typeof(LogisticsPushOnlineServiceConfigResponse);
		}

		public  LogisticsPushOnlineServiceConfigParam BuildParam()
		{
			return Param ?? (Param = new LogisticsPushOnlineServiceConfigParam());
		}

	}
}
