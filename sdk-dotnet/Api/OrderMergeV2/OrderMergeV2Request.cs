using System;
using Dop.Core;
using Dop.Api.OrderMergeV2.Param;

namespace Dop.Api.OrderMergeV2
{
	public class OrderMergeV2Request : DoudianOpApiRequest<OrderMergeV2Param>
	{
		public override string GetUrlPath()
		{
			return "/order/mergeV2";
		}

		public override Type GetResponseType()
		{
			return typeof(OrderMergeV2Response);
		}

		public  OrderMergeV2Param BuildParam()
		{
			return Param ?? (Param = new OrderMergeV2Param());
		}

	}
}
