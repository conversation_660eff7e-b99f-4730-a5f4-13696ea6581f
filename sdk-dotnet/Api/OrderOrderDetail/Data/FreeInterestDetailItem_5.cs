using System.Runtime.Serialization;

namespace Dop.Api.OrderOrderDetail.Data
{
	public class FreeInterestDetailItem_5
	{
		[DataMemberAttribute(Name = "platform_clearing_amount")]
		public long? PlatformClearingAmount { get; set; }

		[DataMemberAttribute(Name = "shop_clearing_amount")]
		public long? ShopClearingAmount { get; set; }

		[DataMemberAttribute(Name = "trade_type")]
		public long? TradeType { get; set; }

	}
}
