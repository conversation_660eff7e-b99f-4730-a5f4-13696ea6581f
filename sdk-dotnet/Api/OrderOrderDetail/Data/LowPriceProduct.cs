using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Dop.Api.OrderOrderDetail.Data
{
	public class LowPriceProduct
	{
		[DataMemberAttribute(Name = "sku_specs")]
		public List<SkuSpecsItem_6> SkuSpecs { get; set; }

		[DataMemberAttribute(Name = "product_id")]
		public long? ProductId { get; set; }

		[DataMemberAttribute(Name = "sku_id")]
		public long? SkuId { get; set; }

		[DataMemberAttribute(Name = "product_name")]
		public string ProductName { get; set; }

		[DataMemberAttribute(Name = "img")]
		public string Img { get; set; }

	}
}
