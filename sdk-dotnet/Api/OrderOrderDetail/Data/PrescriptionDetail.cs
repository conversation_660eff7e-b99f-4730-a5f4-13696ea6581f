using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Dop.Api.OrderOrderDetail.Data
{
	public class PrescriptionDetail
	{
		[DataMemberAttribute(Name = "img_urls")]
		public List<string> ImgUrls { get; set; }

		[DataMemberAttribute(Name = "create_time")]
		public long? CreateTime { get; set; }

		[DataMemberAttribute(Name = "status")]
		public string Status { get; set; }

		[DataMemberAttribute(Name = "consultation_id")]
		public string ConsultationId { get; set; }

		[DataMemberAttribute(Name = "prescription_id")]
		public string PrescriptionId { get; set; }

	}
}
