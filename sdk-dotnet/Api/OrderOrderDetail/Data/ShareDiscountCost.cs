using System.Collections.Generic;
using System.Runtime.Serialization;

namespace Dop.Api.OrderOrderDetail.Data
{
	public class ShareDiscountCost
	{
		[DataMemberAttribute(Name = "extra_map")]
		public Dictionary<string,string> ExtraMap { get; set; }

		[DataMemberAttribute(Name = "third_platform_cost")]
		public long? ThirdPlatformCost { get; set; }

		[DataMemberAttribute(Name = "author_cost")]
		public long? AuthorCost { get; set; }

		[DataMemberAttribute(Name = "shop_cost")]
		public long? ShopCost { get; set; }

		[DataMemberAttribute(Name = "platform_cost")]
		public long? PlatformCost { get; set; }

	}
}
