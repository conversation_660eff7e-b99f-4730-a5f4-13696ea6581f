using System.Runtime.Serialization;

namespace Dop.Api.OrderQueryOrderLogistics.Data
{
	public class TrackInfoItem
	{
		[DataMemberAttribute(Name = "context")]
		public string Context { get; set; }

		[DataMemberAttribute(Name = "time_stamp")]
		public long? TimeStamp { get; set; }

		[DataMemberAttribute(Name = "site")]
		public string Site { get; set; }

		[DataMemberAttribute(Name = "state")]
		public string State { get; set; }

		[DataMemberAttribute(Name = "state_desc")]
		public string StateDesc { get; set; }

	}
}
