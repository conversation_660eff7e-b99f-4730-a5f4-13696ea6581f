using System;
using Dop.Core;
using Dop.Api.OrderSearchByReceiver.Param;

namespace Dop.Api.OrderSearchByReceiver
{
	public class OrderSearchByReceiverRequest : DoudianOpApiRequest<OrderSearchByReceiverParam>
	{
		public override string GetUrlPath()
		{
			return "/order/searchByReceiver";
		}

		public override Type GetResponseType()
		{
			return typeof(OrderSearchByReceiverResponse);
		}

		public  OrderSearchByReceiverParam BuildParam()
		{
			return Param ?? (Param = new OrderSearchByReceiverParam());
		}

	}
}
