using System.Runtime.Serialization;

namespace Dop.Api.ShopVideoGetHiddenWaterMarkTask.Data
{
	public class ShopVideoGetHiddenWaterMarkTaskData
	{
		[DataMemberAttribute(Name = "watermark_url")]
		public string WatermarkUrl { get; set; }

		[DataMemberAttribute(Name = "task_id")]
		public string TaskId { get; set; }

		[DataMemberAttribute(Name = "display_status")]
		public int? DisplayStatus { get; set; }

		[DataMemberAttribute(Name = "failed_reason")]
		public string FailedReason { get; set; }

	}
}
