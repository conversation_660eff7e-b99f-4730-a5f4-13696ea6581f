using System;
using Dop.Core;
using Dop.Api.ShopVideoGetHiddenWaterMarkTask.Param;

namespace Dop.Api.ShopVideoGetHiddenWaterMarkTask
{
	public class ShopVideoGetHiddenWaterMarkTaskRequest : DoudianOpApiRequest<ShopVideoGetHiddenWaterMarkTaskParam>
	{
		public override string GetUrlPath()
		{
			return "/shopVideo/getHiddenWaterMarkTask";
		}

		public override Type GetResponseType()
		{
			return typeof(ShopVideoGetHiddenWaterMarkTaskResponse);
		}

		public  ShopVideoGetHiddenWaterMarkTaskParam BuildParam()
		{
			return Param ?? (Param = new ShopVideoGetHiddenWaterMarkTaskParam());
		}

	}
}
