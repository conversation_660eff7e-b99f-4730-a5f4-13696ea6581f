using System;
using Dop.Core;
using Dop.Api.ShopVideoSubmitHiddenWaterMarkTask.Param;

namespace Dop.Api.ShopVideoSubmitHiddenWaterMarkTask
{
	public class ShopVideoSubmitHiddenWaterMarkTaskRequest : DoudianOpApiRequest<ShopVideoSubmitHiddenWaterMarkTaskParam>
	{
		public override string GetUrlPath()
		{
			return "/shopVideo/submitHiddenWaterMarkTask";
		}

		public override Type GetResponseType()
		{
			return typeof(ShopVideoSubmitHiddenWaterMarkTaskResponse);
		}

		public  ShopVideoSubmitHiddenWaterMarkTaskParam BuildParam()
		{
			return Param ?? (Param = new ShopVideoSubmitHiddenWaterMarkTaskParam());
		}

	}
}
