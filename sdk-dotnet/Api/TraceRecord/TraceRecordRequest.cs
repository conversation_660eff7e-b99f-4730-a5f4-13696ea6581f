using System;
using Dop.Core;
using Dop.Api.TraceRecord.Param;

namespace Dop.Api.TraceRecord
{
	public class TraceRecordRequest : DoudianOpApiRequest<TraceRecordParam>
	{
		public override string GetUrlPath()
		{
			return "/trace/record";
		}

		public override Type GetResponseType()
		{
			return typeof(TraceRecordResponse);
		}

		public  TraceRecordParam BuildParam()
		{
			return Param ?? (Param = new TraceRecordParam());
		}

	}
}
