using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Top.Api.Domain
{
    /// <summary>
    /// IdentifyInfo Data Structure.
    /// </summary>
    [Serializable]
    public class IdentifyInfo : TopObject
    {
        /// <summary>
        /// 三方鉴定物流相关信息
        /// </summary>
        [XmlArray("identify_logistics_infos")]
        [XmlArrayItem("identify_logistics_info")]
        public List<Top.Api.Domain.IdentifyLogisticsInfo> IdentifyLogisticsInfos { get; set; }

        /// <summary>
        /// 三方鉴定服务相关信息
        /// </summary>
        [XmlArray("identify_service_infos")]
        [XmlArrayItem("identify_service_info")]
        public List<Top.Api.Domain.IdentifyServiceInfo> IdentifyServiceInfos { get; set; }
    }
}
