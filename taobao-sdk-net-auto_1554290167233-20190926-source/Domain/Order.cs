using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Top.Api.Domain
{
    /// <summary>
    /// Order Data Structure.
    /// </summary>
    [Serializable]
    public class Order : TopObject
    {
        /// <summary>
        /// 手工调整金额.格式为:1.01;单位:元;精确到小数点后两位.
        /// </summary>
        [XmlElement("adjust_fee")]
        public string AdjustFee { get; set; }

        /// <summary>
        /// 苹果商品标映射字段
        /// </summary>
        [XmlElement("apple_cc")]
        public string AppleCc { get; set; }

        /// <summary>
        /// assemblyItem
        /// </summary>
        [XmlElement("assembly_item")]
        public string AssemblyItem { get; set; }

        /// <summary>
        /// assemblyPrice
        /// </summary>
        [XmlElement("assembly_price")]
        public string AssemblyPrice { get; set; }

        /// <summary>
        /// assemblyRela
        /// </summary>
        [XmlElement("assembly_rela")]
        public string AssemblyRela { get; set; }

        /// <summary>
        /// 优仓业务场景下 1（自动流转）/0（非自动流转）
        /// </summary>
        [XmlElement("auto_flow")]
        public string AutoFlow { get; set; }

        /// <summary>
        /// 订单是否属于b2b代销
        /// </summary>
        [XmlElement("b2b_daixiao")]
        public string B2bDaixiao { get; set; }

        /// <summary>
        /// 捆绑的子订单号，表示该子订单要和捆绑的子订单一起发货，用于卖家子订单捆绑发货
        /// </summary>
        [XmlElement("bind_oid")]
        public long BindOid { get; set; }

        /// <summary>
        /// bind_oid字段的升级，支持返回绑定的多个子订单，多个子订单以半角逗号分隔
        /// </summary>
        [XmlElement("bind_oids")]
        public string BindOids { get; set; }

        /// <summary>
        /// bind_oids字段的升级，在交易成功和交易关闭状态下也能获取到，支持返回绑定的多个子订单，多个子订单以半角逗号分隔
        /// </summary>
        [XmlElement("bind_oids_all_status")]
        public string BindOidsAllStatus { get; set; }

        /// <summary>
        /// 云店交易链路，为tmall.daogoubao.cloudstore时表示云店链路
        /// </summary>
        [XmlElement("biz_code")]
        public string BizCode { get; set; }

        /// <summary>
        /// 同城购订单来源
        /// </summary>
        [XmlElement("brand_light_shop_source")]
        public string BrandLightShopSource { get; set; }

        /// <summary>
        /// 同城购渠道店id
        /// </summary>
        [XmlElement("brand_light_shop_store_id")]
        public string BrandLightShopStoreId { get; set; }

        /// <summary>
        /// 套餐购套餐ID
        /// </summary>
        [XmlElement("bundle_id")]
        public string BundleId { get; set; }

        /// <summary>
        /// 套餐购套餐名称
        /// </summary>
        [XmlElement("bundle_out_name")]
        public string BundleOutName { get; set; }

        /// <summary>
        /// 套餐购套餐类型标识 1营销套餐-PromoBundle 2人群套餐-CrowdBundle
        /// </summary>
        [XmlElement("bundle_type")]
        public long BundleType { get; set; }

        /// <summary>
        /// 买家展示昵称
        /// </summary>
        [XmlElement("buyer_display_nick")]
        public string BuyerDisplayNick { get; set; }

        /// <summary>
        /// 买家昵称
        /// </summary>
        [XmlElement("buyer_nick")]
        public string BuyerNick { get; set; }

        /// <summary>
        /// 买家是否已评价。可选值：true(已评价)，false(未评价)
        /// </summary>
        [XmlElement("buyer_rate")]
        public bool BuyerRate { get; set; }

        /// <summary>
        /// 百补sn码校验订单标
        /// </summary>
        [XmlElement("bybt_sn_code_tag")]
        public string BybtSnCodeTag { get; set; }

        /// <summary>
        /// calPenalty
        /// </summary>
        [XmlElement("cal_penalty")]
        public string CalPenalty { get; set; }

        /// <summary>
        /// carStoreCode
        /// </summary>
        [XmlElement("car_store_code")]
        public string CarStoreCode { get; set; }

        /// <summary>
        /// carStoreName
        /// </summary>
        [XmlElement("car_store_name")]
        public string CarStoreName { get; set; }

        /// <summary>
        /// carTaker
        /// </summary>
        [XmlElement("car_taker")]
        public string CarTaker { get; set; }

        /// <summary>
        /// carTakerID
        /// </summary>
        [XmlElement("car_taker_id")]
        public string CarTakerId { get; set; }

        /// <summary>
        /// carTakerIDNum
        /// </summary>
        [XmlElement("car_taker_id_num")]
        public string CarTakerIdNum { get; set; }

        /// <summary>
        /// carTakerPhone
        /// </summary>
        [XmlElement("car_taker_phone")]
        public string CarTakerPhone { get; set; }

        /// <summary>
        /// 交易商品对应的类目ID
        /// </summary>
        [XmlElement("cid")]
        public long Cid { get; set; }

        /// <summary>
        /// clCarTaker
        /// </summary>
        [XmlElement("cl_car_taker")]
        public string ClCarTaker { get; set; }

        /// <summary>
        /// clCarTakerIDNum
        /// </summary>
        [XmlElement("cl_car_taker_i_d_num")]
        public string ClCarTakerIDNum { get; set; }

        /// <summary>
        /// clCarTakerIDNum
        /// </summary>
        [XmlElement("cl_car_taker_id_num")]
        public string ClCarTakerIdNum { get; set; }

        /// <summary>
        /// clCarTakerPhone
        /// </summary>
        [XmlElement("cl_car_taker_phone")]
        public string ClCarTakerPhone { get; set; }

        /// <summary>
        /// clDownPayment
        /// </summary>
        [XmlElement("cl_down_payment")]
        public string ClDownPayment { get; set; }

        /// <summary>
        /// clDownPaymentRatio
        /// </summary>
        [XmlElement("cl_down_payment_ratio")]
        public string ClDownPaymentRatio { get; set; }

        /// <summary>
        /// clInstallmentNum
        /// </summary>
        [XmlElement("cl_installment_num")]
        public string ClInstallmentNum { get; set; }

        /// <summary>
        /// clMonthPayment
        /// </summary>
        [XmlElement("cl_month_payment")]
        public string ClMonthPayment { get; set; }

        /// <summary>
        /// clServiceFee
        /// </summary>
        [XmlElement("cl_service_fee")]
        public string ClServiceFee { get; set; }

        /// <summary>
        /// clTailPayment
        /// </summary>
        [XmlElement("cl_tail_payment")]
        public string ClTailPayment { get; set; }

        /// <summary>
        /// 云店标记为1，且bizCode不为tmall.daogoubao.cloudstore时，为旗舰店订单
        /// </summary>
        [XmlElement("cloud_store")]
        public string CloudStore { get; set; }

        /// <summary>
        /// 云店pos单号
        /// </summary>
        [XmlElement("cloud_store_bind_pos")]
        public string CloudStoreBindPos { get; set; }

        /// <summary>
        /// 云店改价用token
        /// </summary>
        [XmlElement("cloud_store_token")]
        public string CloudStoreToken { get; set; }

        /// <summary>
        /// 最晚揽收时间
        /// </summary>
        [XmlElement("collect_time")]
        public string CollectTime { get; set; }

        /// <summary>
        /// 组合品信息
        /// </summary>
        [XmlArray("combine_item_info")]
        [XmlArrayItem("combine_sub_item_d_o")]
        public List<Top.Api.Domain.CombineSubItemDO> CombineItemInfo { get; set; }

        /// <summary>
        /// 天猫搭配宝
        /// </summary>
        [XmlElement("combo_id")]
        public string ComboId { get; set; }

        /// <summary>
        /// 子订单发货时间，当卖家对订单进行了多次发货，子订单的发货时间和主订单的发货时间可能不一样了，那么就需要以子订单的时间为准。（没有进行多次发货的订单，主订单的发货时间和子订单的发货时间都一样）
        /// </summary>
        [XmlElement("consign_time")]
        public string ConsignTime { get; set; }

        /// <summary>
        /// 有值表示信用购订单；1表示信用购一期；2表示信用购二期；3表示信用购三期
        /// </summary>
        [XmlElement("credit_buy")]
        public string CreditBuy { get; set; }

        /// <summary>
        /// 定制信息
        /// </summary>
        [XmlElement("customization")]
        public string Customization { get; set; }

        /// <summary>
        /// 物流截单时间，分钟
        /// </summary>
        [XmlElement("cutoff_minutes")]
        public string CutoffMinutes { get; set; }

        /// <summary>
        /// 最晚发货时间
        /// </summary>
        [XmlElement("delivery_time")]
        public string DeliveryTime { get; set; }

        /// <summary>
        /// 子订单级订单优惠金额。精确到2位小数;单位:元。如:200.07，表示:200元7分
        /// </summary>
        [XmlElement("discount_fee")]
        public string DiscountFee { get; set; }

        /// <summary>
        /// 最晚派送时间
        /// </summary>
        [XmlElement("dispatch_time")]
        public string DispatchTime { get; set; }

        /// <summary>
        /// 订单是有代发订单，为空表示该订单暂无代发单据，distribute-该子订单有已分配代发单据，cancel-订单的代发单据都已取消，
        /// </summary>
        [XmlElement("distribute_status")]
        public string DistributeStatus { get; set; }

        /// <summary>
        /// 分摊之后的实付金额
        /// </summary>
        [XmlElement("divide_order_fee")]
        public string DivideOrderFee { get; set; }

        /// <summary>
        /// downPayment
        /// </summary>
        [XmlElement("down_payment")]
        public string DownPayment { get; set; }

        /// <summary>
        /// downPaymentRatio
        /// </summary>
        [XmlElement("down_payment_ratio")]
        public string DownPaymentRatio { get; set; }

        /// <summary>
        /// 子订单的交易结束时间说明：子订单有单独的结束时间，与主订单的结束时间可能有所不同，在有退款发起的时候或者是主订单分阶段付款的时候，子订单的结束时间会早于主订单的结束时间，所以开放这个字段便于订单结束状态的判断
        /// </summary>
        [XmlElement("end_time")]
        public string EndTime { get; set; }

        /// <summary>
        /// 预计送达时间
        /// </summary>
        [XmlElement("es_date")]
        public string EsDate { get; set; }

        /// <summary>
        /// 预计配送时间段
        /// </summary>
        [XmlElement("es_range")]
        public string EsRange { get; set; }

        /// <summary>
        /// 物流时效，相对时间，单位是天
        /// </summary>
        [XmlElement("es_time")]
        public string EsTime { get; set; }

        /// <summary>
        /// 子订单预计发货时间
        /// </summary>
        [XmlElement("estimate_con_time")]
        public string EstimateConTime { get; set; }

        /// <summary>
        /// 车牌号码
        /// </summary>
        [XmlElement("et_plate_number")]
        public string EtPlateNumber { get; set; }

        /// <summary>
        /// 天猫汽车服务预约时间
        /// </summary>
        [XmlElement("et_ser_time")]
        public string EtSerTime { get; set; }

        /// <summary>
        /// 电子凭证预约门店地址
        /// </summary>
        [XmlElement("et_shop_name")]
        public string EtShopName { get; set; }

        /// <summary>
        /// 电子凭证核销门店地址
        /// </summary>
        [XmlElement("et_verified_shop_name")]
        public string EtVerifiedShopName { get; set; }

        /// <summary>
        /// 购物金核销子订单本金分摊金额（单位为元）
        /// </summary>
        [XmlElement("expand_card_basic_price_used_suborder")]
        public string ExpandCardBasicPriceUsedSuborder { get; set; }

        /// <summary>
        /// 购物金核销子订单权益金分摊金额（单位为元）
        /// </summary>
        [XmlElement("expand_card_expand_price_used_suborder")]
        public string ExpandCardExpandPriceUsedSuborder { get; set; }

        /// <summary>
        /// 透出的额外信息
        /// </summary>
        [XmlElement("extend_info")]
        public string ExtendInfo { get; set; }

        /// <summary>
        /// 订单履行状态，如喵鲜生极速达：分单完成
        /// </summary>
        [XmlElement("f_status")]
        public string FStatus { get; set; }

        /// <summary>
        /// 单履行内容，如喵鲜生极速达：storeId,phone
        /// </summary>
        [XmlElement("f_term")]
        public string FTerm { get; set; }

        /// <summary>
        /// 订单履行类型，如喵鲜生极速达（jsd）
        /// </summary>
        [XmlElement("f_type")]
        public string FType { get; set; }

        /// <summary>
        /// 花呗分期期数
        /// </summary>
        [XmlElement("fqg_num")]
        public long FqgNum { get; set; }

        /// <summary>
        /// 赠品关联的id，主品订单下此字段表示赠品订单id，赠品订单表示主品订单id
        /// </summary>
        [XmlElement("gift_mids")]
        public string GiftMids { get; set; }

        /// <summary>
        /// 是否含有赠品
        /// </summary>
        [XmlElement("has_gift")]
        public bool HasGift { get; set; }

        /// <summary>
        /// 云店是否扣拥
        /// </summary>
        [XmlElement("hj_settle_no_commission")]
        public string HjSettleNoCommission { get; set; }

        /// <summary>
        /// 商品的字符串编号(注意：iid近期即将废弃，请用num_iid参数)
        /// </summary>
        [XmlElement("iid")]
        public string Iid { get; set; }

        /// <summary>
        /// installmentNum
        /// </summary>
        [XmlElement("installment_num")]
        public string InstallmentNum { get; set; }

        /// <summary>
        /// 库存类型：6为在途
        /// </summary>
        [XmlElement("inv_type")]
        public string InvType { get; set; }

        /// <summary>
        /// 子订单所在包裹的运单号
        /// </summary>
        [XmlElement("invoice_no")]
        public string InvoiceNo { get; set; }

        /// <summary>
        /// 是否使用百补优惠的订单
        /// </summary>
        [XmlElement("is_bybt_order")]
        public bool IsBybtOrder { get; set; }

        /// <summary>
        /// 表示订单交易是否含有对应的代销采购单。如果该订单中存在一个对应的代销采购单，那么该值为true；反之，该值为false。
        /// </summary>
        [XmlElement("is_daixiao")]
        public bool IsDaixiao { get; set; }

        /// <summary>
        /// 子订单是否优惠贬值
        /// </summary>
        [XmlElement("is_devalue_fee")]
        public bool IsDevalueFee { get; set; }

        /// <summary>
        /// 是否自动流转到菜鸟仓发货
        /// </summary>
        [XmlElement("is_force_dc")]
        public bool IsForceDc { get; set; }

        /// <summary>
        /// 是否商家承担手续费
        /// </summary>
        [XmlElement("is_fqg_s_fee")]
        public bool IsFqgSFee { get; set; }

        /// <summary>
        /// 是否是赠品订单
        /// </summary>
        [XmlElement("is_free_gift")]
        public bool IsFreeGift { get; set; }

        /// <summary>
        /// 是否为闲鱼订单 1是0否
        /// </summary>
        [XmlElement("is_idle")]
        public string IsIdle { get; set; }

        /// <summary>
        /// 是否家装分销订单，入参fields中传入orders.is_jzfx时返回
        /// </summary>
        [XmlElement("is_jzfx")]
        public bool IsJzfx { get; set; }

        /// <summary>
        /// 是否是考拉商品订单
        /// </summary>
        [XmlElement("is_kaola")]
        public bool IsKaola { get; set; }

        /// <summary>
        /// 是否超卖
        /// </summary>
        [XmlElement("is_oversold")]
        public bool IsOversold { get; set; }

        /// <summary>
        /// 是否是服务订单，是返回true，否返回false。
        /// </summary>
        [XmlElement("is_service_order")]
        public bool IsServiceOrder { get; set; }

        /// <summary>
        /// 是否发货
        /// </summary>
        [XmlElement("is_sh_ship")]
        public bool IsShShip { get; set; }

        /// <summary>
        /// 子订单是否是www订单
        /// </summary>
        [XmlElement("is_www")]
        public bool IsWww { get; set; }

        /// <summary>
        /// 套餐ID
        /// </summary>
        [XmlElement("item_meal_id")]
        public long ItemMealId { get; set; }

        /// <summary>
        /// 套餐的值。如：M8原装电池:便携支架:M8专用座充:莫凡保护袋
        /// </summary>
        [XmlElement("item_meal_name")]
        public string ItemMealName { get; set; }

        /// <summary>
        /// 商品备注
        /// </summary>
        [XmlElement("item_memo")]
        public string ItemMemo { get; set; }

        /// <summary>
        /// 服务所属的交易订单号。如果服务为一年包换，则item_oid这笔订单享受改服务的保护
        /// </summary>
        [XmlElement("item_oid")]
        public long ItemOid { get; set; }

        /// <summary>
        /// 鉴定编码
        /// </summary>
        [XmlElement("jewcc_no")]
        public string JewccNo { get; set; }

        /// <summary>
        /// 预售订单立减金额
        /// </summary>
        [XmlElement("lijian")]
        public string Lijian { get; set; }

        /// <summary>
        /// 子订单发货的快递公司名称
        /// </summary>
        [XmlElement("logistics_company")]
        public string LogisticsCompany { get; set; }

        /// <summary>
        /// 免单金额
        /// </summary>
        [XmlElement("md_fee")]
        public string MdFee { get; set; }

        /// <summary>
        /// 免单资格
        /// </summary>
        [XmlElement("md_qualification")]
        public string MdQualification { get; set; }

        /// <summary>
        /// 订单修改时间，目前只有taobao.trade.ordersku.update会返回此字段。
        /// </summary>
        [XmlElement("modified")]
        public string Modified { get; set; }

        /// <summary>
        /// 有值表示买家修改了地址；1表示付款后改地址；2表示付款前改地址
        /// </summary>
        [XmlElement("modify_address")]
        public string ModifyAddress { get; set; }

        /// <summary>
        /// monthPayment
        /// </summary>
        [XmlElement("month_payment")]
        public string MonthPayment { get; set; }

        /// <summary>
        /// 仅对赠品子单生效，退货时赠品是否需要随主品一起退回，字段缺失/true-需要退回，false-无需退回
        /// </summary>
        [XmlElement("need_return")]
        public bool NeedReturn { get; set; }

        /// <summary>
        /// 新零售商家端商品唯一编号
        /// </summary>
        [XmlElement("nr_outer_iid")]
        public string NrOuterIid { get; set; }

        /// <summary>
        /// nrReduceInvFail
        /// </summary>
        [XmlElement("nr_reduce_inv_fail")]
        public string NrReduceInvFail { get; set; }

        /// <summary>
        /// 购买数量。取值范围:大于零的整数
        /// </summary>
        [XmlElement("num")]
        public long Num { get; set; }

        /// <summary>
        /// 商品数字ID
        /// </summary>
        [XmlElement("num_iid")]
        public long NumIid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [XmlElement("o2o_et_order_id")]
        public string O2oEtOrderId { get; set; }

        /// <summary>
        /// 导购员Id
        /// </summary>
        [XmlElement("o2o_guide_id")]
        public string O2oGuideId { get; set; }

        /// <summary>
        /// 导购员名称
        /// </summary>
        [XmlElement("o2o_guide_name")]
        public string O2oGuideName { get; set; }

        /// <summary>
        /// 门店Id
        /// </summary>
        [XmlElement("o2o_shop_id")]
        public string O2oShopId { get; set; }

        /// <summary>
        /// 门店名称
        /// </summary>
        [XmlElement("o2o_shop_name")]
        public string O2oShopName { get; set; }

        /// <summary>
        /// 子订单编号
        /// </summary>
        [XmlElement("oid")]
        public long Oid { get; set; }

        /// <summary>
        /// oidStr
        /// </summary>
        [XmlElement("oid_str")]
        public string OidStr { get; set; }

        /// <summary>
        /// 经销商货品商家编码
        /// </summary>
        [XmlElement("omni_jxs_outerid")]
        public string OmniJxsOuterid { get; set; }

        /// <summary>
        /// 全渠道商品标
        /// </summary>
        [XmlArray("omni_tags")]
        [XmlArrayItem("number")]
        public List<long> OmniTags { get; set; }

        /// <summary>
        /// 主品关联的1元商品订单号
        /// </summary>
        [XmlElement("one_yuan_reservation_orders")]
        public string OneYuanReservationOrders { get; set; }

        /// <summary>
        /// 子订单扩展属性:<br/> is_free_down_payment:是否免首付：true：是，false：否，可选字段<br/> car_back_payment:返还免首付金额，单位：分，<br/> car_ref_activity_id:服务商传入活动ID，依赖外部服务商传入；<br/> uboxType :value为uboxTaotian 表示天猫U先仓配项目订单 ；<br/>
        /// </summary>
        [XmlElement("order_attr")]
        public string OrderAttr { get; set; }

        /// <summary>
        /// 子订单来源,如jhs(聚划算)、taobao(淘宝)、wap(无线)
        /// </summary>
        [XmlElement("order_from")]
        public string OrderFrom { get; set; }

        /// <summary>
        /// 云店接单标记
        /// </summary>
        [XmlElement("order_taking")]
        public string OrderTaking { get; set; }

        /// <summary>
        /// 测试xxx，无用
        /// </summary>
        [XmlElement("orderxxx")]
        public string Orderxxx { get; set; }

        /// <summary>
        /// 前N有礼活动id
        /// </summary>
        [XmlElement("os_activity_id")]
        public string OsActivityId { get; set; }

        /// <summary>
        /// 预约配送，用户预约时间
        /// </summary>
        [XmlElement("os_date")]
        public string OsDate { get; set; }

        /// <summary>
        /// 前N有礼奖品的商品id
        /// </summary>
        [XmlElement("os_fg_item_id")]
        public string OsFgItemId { get; set; }

        /// <summary>
        /// 前N有礼获得奖品的数量
        /// </summary>
        [XmlElement("os_gift_count")]
        public string OsGiftCount { get; set; }

        /// <summary>
        /// 预约配送，用户预约时间段
        /// </summary>
        [XmlElement("os_range")]
        public string OsRange { get; set; }

        /// <summary>
        /// 前N有礼中奖名次，获得奖品的订单才会有该字段
        /// </summary>
        [XmlElement("os_sort_num")]
        public string OsSortNum { get; set; }

        /// <summary>
        /// 天猫未来店外部 ERP 商品 ID
        /// </summary>
        [XmlElement("out_item_id")]
        public string OutItemId { get; set; }

        /// <summary>
        /// outUniqueId
        /// </summary>
        [XmlElement("out_unique_id")]
        public string OutUniqueId { get; set; }

        /// <summary>
        /// 商家外部编码(可与商家外部系统对接)。外部商家自己定义的商品Item的id，可以通过taobao.items.custom.get获取商品的Item的信息
        /// </summary>
        [XmlElement("outer_iid")]
        public string OuterIid { get; set; }

        /// <summary>
        /// 外部网店自己定义的Sku编号
        /// </summary>
        [XmlElement("outer_sku_id")]
        public string OuterSkuId { get; set; }

        /// <summary>
        /// 优惠分摊
        /// </summary>
        [XmlElement("part_mjz_discount")]
        public string PartMjzDiscount { get; set; }

        /// <summary>
        /// 子订单实付金额。精确到2位小数，单位:元。如:200.07，表示:200元7分。对于多子订单的交易，计算公式如下：payment = price * num + adjust_fee - discount_fee ；单子订单交易，payment与主订单的payment一致，对于退款成功的子订单，由于主订单的优惠分摊金额，会造成该字段可能不为0.00元。建议使用退款前的实付金额减去退款单中的实际退款金额计算。
        /// </summary>
        [XmlElement("payment")]
        public string Payment { get; set; }

        /// <summary>
        /// penalty
        /// </summary>
        [XmlElement("penalty")]
        public string Penalty { get; set; }

        /// <summary>
        /// 商品图片的绝对路径
        /// </summary>
        [XmlElement("pic_path")]
        public string PicPath { get; set; }

        /// <summary>
        /// platformSubsidyFee
        /// </summary>
        [XmlElement("platform_subsidy_fee")]
        public string PlatformSubsidyFee { get; set; }

        /// <summary>
        /// 商品价格。精确到2位小数;单位:元。如:200.07，表示:200元7分
        /// </summary>
        [XmlElement("price")]
        public string Price { get; set; }

        /// <summary>
        /// 承诺/最晚揽收时间
        /// </summary>
        [XmlElement("pro_ct")]
        public string ProCt { get; set; }

        /// <summary>
        /// 承诺/最晚揽收时间
        /// </summary>
        [XmlElement("promise_collect_time")]
        public string PromiseCollectTime { get; set; }

        /// <summary>
        /// 信用购履约结束时间
        /// </summary>
        [XmlElement("promise_end_time")]
        public string PromiseEndTime { get; set; }

        /// <summary>
        /// 时效服务字段，服务字段，会有多个服务值，以英文半角逗号","切割
        /// </summary>
        [XmlElement("promise_service")]
        public string PromiseService { get; set; }

        /// <summary>
        /// 使用淘金币的数量，以分为单位，和订单标propoint中间那一段一样
        /// </summary>
        [XmlElement("propoint")]
        public string Propoint { get; set; }

        /// <summary>
        /// 个人充值红包金额
        /// </summary>
        [XmlElement("recharge_fee")]
        public string RechargeFee { get; set; }

        /// <summary>
        /// 最近退款ID
        /// </summary>
        [XmlElement("refund_id")]
        public string RefundId { get; set; }

        /// <summary>
        /// 退款状态。退款状态。可选值 WAIT_SELLER_AGREE(买家已经申请退款，等待卖家同意) WAIT_BUYER_RETURN_GOODS(卖家已经同意退款，等待买家退货) WAIT_SELLER_CONFIRM_GOODS(买家已经退货，等待卖家确认收货) SELLER_REFUSE_BUYER(卖家拒绝退款) CLOSED(退款关闭) SUCCESS(退款成功)
        /// </summary>
        [XmlElement("refund_status")]
        public string RefundStatus { get; set; }

        /// <summary>
        /// 天猫未来店线下店铺 ID
        /// </summary>
        [XmlElement("retail_store_id")]
        public string RetailStoreId { get; set; }

        /// <summary>
        /// 新零售全渠道订单：商家自有货品编码
        /// </summary>
        [XmlElement("rt_omni_outer_sc_id")]
        public string RtOmniOuterScId { get; set; }

        /// <summary>
        /// 新零售全渠道订单：后端货品ID
        /// </summary>
        [XmlElement("rt_omni_sc_id")]
        public string RtOmniScId { get; set; }

        /// <summary>
        /// 子订单商家代缴税费
        /// </summary>
        [XmlElement("s_tariff_fee")]
        public string STariffFee { get; set; }

        /// <summary>
        /// 卖家展示昵称
        /// </summary>
        [XmlElement("seller_display_nick")]
        public string SellerDisplayNick { get; set; }

        /// <summary>
        /// 卖家昵称
        /// </summary>
        [XmlElement("seller_nick")]
        public string SellerNick { get; set; }

        /// <summary>
        /// 卖家是否已评价。可选值：true(已评价)，false(未评价)
        /// </summary>
        [XmlElement("seller_rate")]
        public bool SellerRate { get; set; }

        /// <summary>
        /// 卖家类型，可选值为：B（商城商家），C（普通卖家）
        /// </summary>
        [XmlElement("seller_type")]
        public string SellerType { get; set; }

        /// <summary>
        /// 服务详情的URL地址
        /// </summary>
        [XmlElement("service_detail_url")]
        public string ServiceDetailUrl { get; set; }

        /// <summary>
        /// serviceFee
        /// </summary>
        [XmlElement("service_fee")]
        public string ServiceFee { get; set; }

        /// <summary>
        /// 服务数字id
        /// </summary>
        [XmlElement("service_id")]
        public long ServiceId { get; set; }

        /// <summary>
        /// 服务供应链-服务订单类型,1:主子挂载；2：双主挂载；3：单独售卖
        /// </summary>
        [XmlElement("service_order_type")]
        public string ServiceOrderType { get; set; }

        /// <summary>
        /// 服务供应链-服务商外部编码
        /// </summary>
        [XmlElement("service_outer_id")]
        public string ServiceOuterId { get; set; }

        /// <summary>
        /// 发货信息，目前只记录了发货方式
        /// </summary>
        [XmlArray("ship_info")]
        [XmlArrayItem("ship_info")]
        public List<Top.Api.Domain.ShipInfo> ShipInfo { get; set; }

        /// <summary>
        /// 仓储信息
        /// </summary>
        [XmlElement("shipper")]
        public string Shipper { get; set; }

        /// <summary>
        /// 子订单的运送方式（卖家对订单进行多次发货之后，一个主订单下的子订单的运送方式可能不同，用order.shipping_type来区分子订单的运送方式）
        /// </summary>
        [XmlElement("shipping_type")]
        public string ShippingType { get; set; }

        /// <summary>
        /// 发货信息，目前只记录了发货方式
        /// </summary>
        [XmlArray("shp_info")]
        [XmlArrayItem("ship_info")]
        public List<Top.Api.Domain.ShipInfo> ShpInfo { get; set; }

        /// <summary>
        /// 最晚签收时间
        /// </summary>
        [XmlElement("sign_time")]
        public string SignTime { get; set; }

        /// <summary>
        /// 商品的最小库存单位Sku的id.可以通过taobao.item.sku.get获取详细的Sku信息
        /// </summary>
        [XmlElement("sku_id")]
        public string SkuId { get; set; }

        /// <summary>
        /// SKU的值。如：机身颜色:黑色;手机套餐:官方标配
        /// </summary>
        [XmlElement("sku_properties_name")]
        public string SkuPropertiesName { get; set; }

        /// <summary>
        /// 订单快照详细信息
        /// </summary>
        [XmlElement("snapshot")]
        public string Snapshot { get; set; }

        /// <summary>
        /// 订单快照URL
        /// </summary>
        [XmlElement("snapshot_url")]
        public string SnapshotUrl { get; set; }

        /// <summary>
        /// sortInfo
        /// </summary>
        [XmlElement("sort_info")]
        public Top.Api.Domain.SortInfo SortInfo { get; set; }

        /// <summary>
        /// 特殊退款状态
        /// </summary>
        [XmlElement("special_refund_type")]
        public string SpecialRefundType { get; set; }

        /// <summary>
        /// 订单状态（请关注此状态，如果为TRADE_CLOSED_BY_TAOBAO状态，则不要对此订单进行发货，切记啊！）。可选值: <ul><li>TRADE_NO_CREATE_PAY(没有创建支付宝交易) <li>WAIT_BUYER_PAY(等待买家付款) <li>WAIT_SELLER_SEND_GOODS(等待卖家发货,即:买家已付款) <li>WAIT_BUYER_CONFIRM_GOODS(等待买家确认收货,即:卖家已发货) <li>TRADE_BUYER_SIGNED(买家已签收,货到付款专用) <li>TRADE_FINISHED(交易成功) <li>TRADE_CLOSED(付款以后用户退款成功，交易自动关闭) <li>TRADE_CLOSED_BY_TAOBAO(付款以前，卖家或买家主动关闭交易)<li>PAY_PENDING(国际信用卡支付付款确认中)
        /// </summary>
        [XmlElement("status")]
        public string Status { get; set; }

        /// <summary>
        /// 发货的仓库编码
        /// </summary>
        [XmlElement("store_code")]
        public string StoreCode { get; set; }

        /// <summary>
        /// 天猫国际官网直供子订单关税税费
        /// </summary>
        [XmlElement("sub_order_tax_fee")]
        public string SubOrderTaxFee { get; set; }

        /// <summary>
        /// 天猫国际子订单计税优惠金额
        /// </summary>
        [XmlElement("sub_order_tax_promotion_fee")]
        public string SubOrderTaxPromotionFee { get; set; }

        /// <summary>
        /// 天猫国际官网直供子订单关税税率
        /// </summary>
        [XmlElement("sub_order_tax_rate")]
        public string SubOrderTaxRate { get; set; }

        /// <summary>
        /// tailPayment
        /// </summary>
        [XmlElement("tail_payment")]
        public string TailPayment { get; set; }

        /// <summary>
        /// 天猫国际子订单包税金额
        /// </summary>
        [XmlElement("tax_coupon_discount")]
        public string TaxCouponDiscount { get; set; }

        /// <summary>
        /// 天猫国际子订单是否包税
        /// </summary>
        [XmlElement("tax_free")]
        public bool TaxFree { get; set; }

        /// <summary>
        /// 测试字段
        /// </summary>
        [XmlElement("test_field")]
        public string TestField { get; set; }

        /// <summary>
        /// 买家修改地址时间
        /// </summary>
        [XmlElement("ti_modify_address_time")]
        public string TiModifyAddressTime { get; set; }

        /// <summary>
        /// 门票有效期的key
        /// </summary>
        [XmlElement("ticket_expdate_key")]
        public string TicketExpdateKey { get; set; }

        /// <summary>
        /// 对应门票有效期的外部id
        /// </summary>
        [XmlElement("ticket_outer_id")]
        public string TicketOuterId { get; set; }

        /// <summary>
        /// 订单超时到期时间。格式:yyyy-MM-dd HH:mm:ss
        /// </summary>
        [XmlElement("timeout_action_time")]
        public string TimeoutActionTime { get; set; }

        /// <summary>
        /// 时效服务身份，如tmallPromise代表天猫时效承诺
        /// </summary>
        [XmlElement("timing_promise")]
        public string TimingPromise { get; set; }

        /// <summary>
        /// 商品标题
        /// </summary>
        [XmlElement("title")]
        public string Title { get; set; }

        /// <summary>
        /// 支持家装类物流的类型
        /// </summary>
        [XmlElement("tmser_spu_code")]
        public string TmserSpuCode { get; set; }

        /// <summary>
        /// 应付金额（商品价格 * 商品数量 + 手工调整金额 - 子订单级订单优惠金额）。精确到2位小数;单位:元。如:200.07，表示:200元7分
        /// </summary>
        [XmlElement("total_fee")]
        public string TotalFee { get; set; }

        /// <summary>
        /// 订单履约类型
        /// </summary>
        [XmlElement("trade_fulfillment_type")]
        public long TradeFulfillmentType { get; set; }

        /// <summary>
        /// 交易类型
        /// </summary>
        [XmlElement("type")]
        public string Type { get; set; }

        /// <summary>
        /// wsBankApplyNo
        /// </summary>
        [XmlElement("ws_bank_apply_no")]
        public string WsBankApplyNo { get; set; }

        /// <summary>
        /// xxx
        /// </summary>
        [XmlElement("xxx")]
        public string Xxx { get; set; }

        /// <summary>
        /// 征集预售订单征集状态：1（征集中），2（征集成功），3（征集失败）
        /// </summary>
        [XmlElement("zhengji_status")]
        public string ZhengjiStatus { get; set; }
    }
}
