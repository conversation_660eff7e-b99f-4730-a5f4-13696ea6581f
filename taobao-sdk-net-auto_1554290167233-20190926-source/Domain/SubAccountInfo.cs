using System;
using System.Xml.Serialization;

namespace Top.Api.Domain
{
    /// <summary>
    /// SubAccountInfo Data Structure.
    /// </summary>
    [Serializable]
    public class SubAccountInfo : TopObject
    {
        /// <summary>
        /// true
        /// </summary>
        [XmlElement("sub_dispatch_status")]
        public bool SubDispatchStatus { get; set; }

        /// <summary>
        /// 123456
        /// </summary>
        [XmlElement("sub_id")]
        public long SubId { get; set; }

        /// <summary>
        /// 小红
        /// </summary>
        [XmlElement("sub_name")]
        public string SubName { get; set; }

        /// <summary>
        /// zhangsan:no1
        /// </summary>
        [XmlElement("sub_nick")]
        public string SubNick { get; set; }

        /// <summary>
        /// true
        /// </summary>
        [XmlElement("sub_owed_status")]
        public bool SubOwedStatus { get; set; }

        /// <summary>
        /// 1
        /// </summary>
        [XmlElement("sub_status")]
        public long SubStatus { get; set; }

        /// <summary>
        /// 654321
        /// </summary>
        [XmlElement("user_id")]
        public long UserId { get; set; }

        /// <summary>
        /// zhangsan
        /// </summary>
        [XmlElement("user_nick")]
        public string UserNick { get; set; }
    }
}
