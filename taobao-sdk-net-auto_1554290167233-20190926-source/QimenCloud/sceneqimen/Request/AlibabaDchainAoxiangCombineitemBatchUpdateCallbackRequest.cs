using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using System.Xml.Serialization;
using Top.Api.Util;
using Top.Api;

namespace QimenCloud.Api.sceneqimen.Request
{
    /// <summary>
    /// TOP API: alibaba.dchain.aoxiang.combineitem.batch.update.callback
    /// </summary>
    public class AlibabaDchainAoxiangCombineitemBatchUpdateCallbackRequest : BaseQimenCloudRequest<QimenCloud.Api.sceneqimen.Response.AlibabaDchainAoxiangCombineitemBatchUpdateCallbackResponse>
    {
        /// <summary>
        /// 业务请求id
        /// </summary>
        public string BizRequestId { get; set; }

        /// <summary>
        /// 结果列表
        /// </summary>
        public string Results { get; set; }

        public List<ResultsDomain> Results_ { set { this.Results = TopUtils.ObjectToJson(value); } } 

        #region IQimenCloudRequest Members

        public override string GetApiName()
        {
            return "alibaba.dchain.aoxiang.combineitem.batch.update.callback";
        }
        
        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("bizRequestId", this.BizRequestId);
            parameters.Add("results", this.Results);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("bizRequestId", this.BizRequestId);
            RequestValidator.ValidateRequired("results", this.Results);
            RequestValidator.ValidateObjectMaxListSize("results", this.Results, 999999);
        }

	/// <summary>
/// ResultsDomain Data Structure.
/// </summary>
[Serializable]

public class ResultsDomain : TopObject
{
	        /// <summary>
	        /// 响应码
	        /// </summary>
	        [XmlElement("bizCode")]
	        public string BizCode { get; set; }
	
	        /// <summary>
	        /// 响应信息
	        /// </summary>
	        [XmlElement("bizMessage")]
	        public string BizMessage { get; set; }
	
	        /// <summary>
	        /// 组合ERP货品id
	        /// </summary>
	        [XmlElement("combineScItemId")]
	        public string CombineScItemId { get; set; }
	
	        /// <summary>
	        /// 卖家id
	        /// </summary>
	        [XmlElement("sellerId")]
	        public string SellerId { get; set; }
	
	        /// <summary>
	        /// true|false
	        /// </summary>
	        [XmlElement("success")]
	        public Nullable<bool> Success { get; set; }
}

        #endregion
    }
}
