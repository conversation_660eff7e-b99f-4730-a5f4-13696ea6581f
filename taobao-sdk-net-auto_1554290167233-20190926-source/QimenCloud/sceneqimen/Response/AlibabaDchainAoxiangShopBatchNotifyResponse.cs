using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Top.Api;


namespace QimenCloud.Api.sceneqimen.Response
{
    /// <summary>
    /// AlibabaDchainAoxiangShopBatchNotifyResponse.
    /// </summary>
    public class AlibabaDchainAoxiangShopBatchNotifyResponse : QimenCloudResponse
    {
        /// <summary>
        /// 返回信息码
        /// </summary>
        [XmlElement("bizCode")]
        public string BizCode { get; set; }

        /// <summary>
        /// 返回信息
        /// </summary>
        [XmlElement("bizMessage")]
        public string BizMessage { get; set; }

        /// <summary>
        /// 业务处理结果
        /// </summary>
        [XmlElement("data")]
        public bool Data { get; set; }

        /// <summary>
        /// 成功或者失败
        /// </summary>
        [XmlElement("success")]
        public string Success { get; set; }

    }
}
