using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Top.Api;


namespace QimenCloud.Api.sceneqimen.Response
{
    /// <summary>
    /// TaobaoOpenModifyskuResponse.
    /// </summary>
    public class TaobaoOpenModifyskuResponse : QimenCloudResponse
    {
        /// <summary>
        /// 标准错误码
        /// </summary>
        [XmlElement("errorCode")]
        public string ErrorCode { get; set; }

        /// <summary>
        /// 错误描述
        /// </summary>
        [XmlElement("errorMsg")]
        public string ErrorMsg { get; set; }

        /// <summary>
        /// 是否可修改
        /// </summary>
        [XmlElement("success")]
        public bool Success { get; set; }

    }
}
