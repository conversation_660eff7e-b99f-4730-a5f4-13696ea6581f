using System;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: alibaba.aliqin.fc.iot.device.post
    /// </summary>
    public class AlibabaAliqinFcIotDevicePostRequest : BaseTopRequest<Top.Api.Response.AlibabaAliqinFcIotDevicePostResponse>
    {
        /// <summary>
        /// 备注
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// 设备类型（将来扩展）
        /// </summary>
        public string DeviceType { get; set; }

        /// <summary>
        /// 15位imei号
        /// </summary>
        public string Imei { get; set; }

        /// <summary>
        /// 扩展字段
        /// </summary>
        public string MidPatChannel { get; set; }

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "alibaba.aliqin.fc.iot.device.post";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("comments", this.Comments);
            parameters.Add("device_type", this.DeviceType);
            parameters.Add("imei", this.Imei);
            parameters.Add("mid_pat_channel", this.MidPatChannel);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("imei", this.Imei);
        }

        #endregion
    }
}
