using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: alibaba.dchain.aoxiang.combinescitem.batch.update
    /// </summary>
    public class AlibabaDchainAoxiangCombinescitemBatchUpdateRequest : BaseTopRequest<Top.Api.Response.AlibabaDchainAoxiangCombinescitemBatchUpdateResponse>
    {
        /// <summary>
        /// 批量更新组合货品入参
        /// </summary>
        public string BatchUpdateCombineScitemRequest { get; set; }

        public BatchUpdateCombineScItemRequestDomain BatchUpdateCombineScitemRequest_ { set { this.BatchUpdateCombineScitemRequest = TopUtils.ObjectToJson(value); } } 

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "alibaba.dchain.aoxiang.combinescitem.batch.update";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("batch_update_combine_scitem_request", this.BatchUpdateCombineScitemRequest);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("batch_update_combine_scitem_request", this.BatchUpdateCombineScitemRequest);
        }

	/// <summary>
/// SubScItemDomain Data Structure.
/// </summary>
[Serializable]

public class SubScItemDomain : TopObject
{
	        /// <summary>
	        /// 币种，USD-美元，CNY-人民币，RUB-卢布，JPY-日元，EUR-欧元，GBP-英镑，HKD-港币，NZD-新西兰元，SGD-新加坡元，AUD-澳元，KRW-韩元，THB-泰铢
	        /// </summary>
	        [XmlElement("currency")]
	        public string Currency { get; set; }
	
	        /// <summary>
	        /// 固定价格，1=是，0=否
	        /// </summary>
	        [XmlElement("fixed_price")]
	        public Nullable<long> FixedPrice { get; set; }
	
	        /// <summary>
	        /// 子货品数量
	        /// </summary>
	        [XmlElement("quantity")]
	        public Nullable<long> Quantity { get; set; }
	
	        /// <summary>
	        /// 子货品零售价（人民币-分）
	        /// </summary>
	        [XmlElement("retail_price")]
	        public Nullable<long> RetailPrice { get; set; }
	
	        /// <summary>
	        /// 子货品商家编码
	        /// </summary>
	        [XmlElement("sc_item_code")]
	        public string ScItemCode { get; set; }
}

	/// <summary>
/// CombineScItemDomain Data Structure.
/// </summary>
[Serializable]

public class CombineScItemDomain : TopObject
{
	        /// <summary>
	        /// 品牌名称
	        /// </summary>
	        [XmlElement("brand_name")]
	        public string BrandName { get; set; }
	
	        /// <summary>
	        /// 类目名称
	        /// </summary>
	        [XmlElement("category_name")]
	        public string CategoryName { get; set; }
	
	        /// <summary>
	        /// 组合货品商家编码
	        /// </summary>
	        [XmlElement("combine_sc_item_code")]
	        public string CombineScItemCode { get; set; }
	
	        /// <summary>
	        /// 组合货品名称
	        /// </summary>
	        [XmlElement("combine_sc_item_name")]
	        public string CombineScItemName { get; set; }
	
	        /// <summary>
	        /// 币种，USD-美元，CNY-人民币，RUB-卢布，JPY-日元，EUR-欧元，GBP-英镑，HKD-港币，NZD-新西兰元，SGD-新加坡元，AUD-澳元，KRW-韩元，THB-泰铢
	        /// </summary>
	        [XmlElement("currency")]
	        public string Currency { get; set; }
	
	        /// <summary>
	        /// 货主id
	        /// </summary>
	        [XmlElement("owner_code")]
	        public string OwnerCode { get; set; }
	
	        /// <summary>
	        /// 备注
	        /// </summary>
	        [XmlElement("remark")]
	        public string Remark { get; set; }
	
	        /// <summary>
	        /// 零售价（人民币-分）
	        /// </summary>
	        [XmlElement("retail_price")]
	        public Nullable<long> RetailPrice { get; set; }
	
	        /// <summary>
	        /// 子货品列表
	        /// </summary>
	        [XmlArray("sub_sc_items")]
	        [XmlArrayItem("sub_sc_item")]
	        public List<SubScItemDomain> SubScItems { get; set; }
}

	/// <summary>
/// BatchUpdateCombineScItemRequestDomain Data Structure.
/// </summary>
[Serializable]

public class BatchUpdateCombineScItemRequestDomain : TopObject
{
	        /// <summary>
	        /// 组合货品列表，批量数量不可超过30
	        /// </summary>
	        [XmlArray("combine_sc_items")]
	        [XmlArrayItem("combine_sc_item")]
	        public List<CombineScItemDomain> CombineScItems { get; set; }
	
	        /// <summary>
	        /// 业务请求ID，用于幂等
	        /// </summary>
	        [XmlElement("request_id")]
	        public string RequestId { get; set; }
	
	        /// <summary>
	        /// 业务请求时间
	        /// </summary>
	        [XmlElement("request_time")]
	        public Nullable<long> RequestTime { get; set; }
}

        #endregion
    }
}
