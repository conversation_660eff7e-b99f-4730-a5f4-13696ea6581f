using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: alibaba.dchain.aoxiang.cooperate.distributor.query
    /// </summary>
    public class AlibabaDchainAoxiangCooperateDistributorQueryRequest : BaseTopRequest<Top.Api.Response.AlibabaDchainAoxiangCooperateDistributorQueryResponse>
    {
        /// <summary>
        /// 请求入参
        /// </summary>
        public string QueryDistributorRequest { get; set; }

        public QueryDistributorRequestDomain QueryDistributorRequest_ { set { this.QueryDistributorRequest = TopUtils.ObjectToJson(value); } } 

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "alibaba.dchain.aoxiang.cooperate.distributor.query";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("query_distributor_request", this.QueryDistributorRequest);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("query_distributor_request", this.QueryDistributorRequest);
        }

	/// <summary>
/// QueryDistributorRequestDomain Data Structure.
/// </summary>
[Serializable]

public class QueryDistributorRequestDomain : TopObject
{
	        /// <summary>
	        /// 当前页
	        /// </summary>
	        [XmlElement("current_page")]
	        public Nullable<long> CurrentPage { get; set; }
	
	        /// <summary>
	        /// 一页多少条
	        /// </summary>
	        [XmlElement("page_size")]
	        public Nullable<long> PageSize { get; set; }
	
	        /// <summary>
	        /// 业务请求ID，用于幂等
	        /// </summary>
	        [XmlElement("request_id")]
	        public string RequestId { get; set; }
	
	        /// <summary>
	        /// 业务请求时间。时间戳。 毫秒
	        /// </summary>
	        [XmlElement("request_time")]
	        public Nullable<long> RequestTime { get; set; }
}

        #endregion
    }
}
