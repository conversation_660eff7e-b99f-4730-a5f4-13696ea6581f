using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: alibaba.dchain.aoxiang.item.batch.update.async
    /// </summary>
    public class AlibabaDchainAoxiangItemBatchUpdateAsyncRequest : BaseTopRequest<Top.Api.Response.AlibabaDchainAoxiangItemBatchUpdateAsyncResponse>
    {
        /// <summary>
        /// 请求入参
        /// </summary>
        public string ItemUpdateRequest { get; set; }

        public ItemBatchUpdateAsyncRequestDomain ItemUpdateRequest_ { set { this.ItemUpdateRequest = TopUtils.ObjectToJson(value); } } 

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "alibaba.dchain.aoxiang.item.batch.update.async";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("item_update_request", this.ItemUpdateRequest);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("item_update_request", this.ItemUpdateRequest);
        }

	/// <summary>
/// WarehouseScItemRelationDomain Data Structure.
/// </summary>
[Serializable]

public class WarehouseScItemRelationDomain : TopObject
{
	        /// <summary>
	        /// 仓库编码
	        /// </summary>
	        [XmlElement("erp_warehouse_code")]
	        public string ErpWarehouseCode { get; set; }
	
	        /// <summary>
	        /// 仓库货品编码 
	        /// </summary>
	        [XmlElement("warehouse_sc_item_code")]
	        public string WarehouseScItemCode { get; set; }
}

	/// <summary>
/// ScItemDomain Data Structure.
/// </summary>
[Serializable]

public class ScItemDomain : TopObject
{
	        /// <summary>
	        /// 货品条码
	        /// </summary>
	        [XmlElement("bar_code")]
	        public string BarCode { get; set; }
	
	        /// <summary>
	        /// 扩展字段
	        /// </summary>
	        [XmlElement("extend_props")]
	        public string ExtendProps { get; set; }
	
	        /// <summary>
	        /// 货品商家编码
	        /// </summary>
	        [XmlElement("sc_item_code")]
	        public string ScItemCode { get; set; }
	
	        /// <summary>
	        /// ERP货品ID
	        /// </summary>
	        [XmlElement("sc_item_id")]
	        public string ScItemId { get; set; }
	
	        /// <summary>
	        /// 货品名称
	        /// </summary>
	        [XmlElement("sc_item_name")]
	        public string ScItemName { get; set; }
	
	        /// <summary>
	        /// 仓库货品编码
	        /// </summary>
	        [XmlArray("warehouse_sc_item_relation")]
	        [XmlArrayItem("warehouse_sc_item_relation")]
	        public List<WarehouseScItemRelationDomain> WarehouseScItemRelation { get; set; }
}

	/// <summary>
/// ItemBatchUpdateAsyncRequestDomain Data Structure.
/// </summary>
[Serializable]

public class ItemBatchUpdateAsyncRequestDomain : TopObject
{
	        /// <summary>
	        /// 业务请求ID，用于做幂等
	        /// </summary>
	        [XmlElement("request_id")]
	        public string RequestId { get; set; }
	
	        /// <summary>
	        /// ERP调翱象接口创建货品的时间戳
	        /// </summary>
	        [XmlElement("request_time")]
	        public Nullable<long> RequestTime { get; set; }
	
	        /// <summary>
	        /// 货品列表
	        /// </summary>
	        [XmlArray("sc_items")]
	        [XmlArrayItem("sc_item")]
	        public List<ScItemDomain> ScItems { get; set; }
}

        #endregion
    }
}
