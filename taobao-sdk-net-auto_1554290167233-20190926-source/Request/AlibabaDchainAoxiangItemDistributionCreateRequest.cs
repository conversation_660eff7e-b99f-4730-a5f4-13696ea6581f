using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: alibaba.dchain.aoxiang.item.distribution.create
    /// </summary>
    public class AlibabaDchainAoxiangItemDistributionCreateRequest : BaseTopRequest<Top.Api.Response.AlibabaDchainAoxiangItemDistributionCreateResponse>
    {
        /// <summary>
        /// 创建商品分销入参
        /// </summary>
        public string CreateItemDistributionRequest { get; set; }

        public CreateItemDistributionRequestDomain CreateItemDistributionRequest_ { set { this.CreateItemDistributionRequest = TopUtils.ObjectToJson(value); } } 

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "alibaba.dchain.aoxiang.item.distribution.create";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("create_item_distribution_request", this.CreateItemDistributionRequest);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("create_item_distribution_request", this.CreateItemDistributionRequest);
        }

	/// <summary>
/// CreateItemDistributionRequestDomain Data Structure.
/// </summary>
[Serializable]

public class CreateItemDistributionRequestDomain : TopObject
{
	        /// <summary>
	        /// 币种
	        /// </summary>
	        [XmlElement("distribute_currency")]
	        public string DistributeCurrency { get; set; }
	
	        /// <summary>
	        /// 【必传】分销价格，卖给分销商的价格， 单位 分
	        /// </summary>
	        [XmlElement("distribute_price")]
	        public Nullable<long> DistributePrice { get; set; }
	
	        /// <summary>
	        /// 【非必传】如果商品没有sku则必传，如果有sku则非必传。
	        /// </summary>
	        [XmlElement("item_code")]
	        public string ItemCode { get; set; }
	
	        /// <summary>
	        /// 【必传】要选择进行铺货的店铺宝贝 itemId
	        /// </summary>
	        [XmlElement("item_id")]
	        public string ItemId { get; set; }
	
	        /// <summary>
	        /// 【必传】商品名称
	        /// </summary>
	        [XmlElement("item_title")]
	        public string ItemTitle { get; set; }
	
	        /// <summary>
	        /// 1级分销价格
	        /// </summary>
	        [XmlElement("level1_price")]
	        public Nullable<long> Level1Price { get; set; }
	
	        /// <summary>
	        /// 2级分销价格
	        /// </summary>
	        [XmlElement("level2_price")]
	        public Nullable<long> Level2Price { get; set; }
	
	        /// <summary>
	        /// 3级分销价格
	        /// </summary>
	        [XmlElement("level3_price")]
	        public Nullable<long> Level3Price { get; set; }
	
	        /// <summary>
	        /// 4级分销价格
	        /// </summary>
	        [XmlElement("level4_price")]
	        public Nullable<long> Level4Price { get; set; }
	
	        /// <summary>
	        /// 5级分销价格
	        /// </summary>
	        [XmlElement("level5_price")]
	        public Nullable<long> Level5Price { get; set; }
	
	        /// <summary>
	        /// 【必传】运费模板id， 可以通过alibaba.dchain.aoxiang.deliverytemplate.query 这个接口进行获取
	        /// </summary>
	        [XmlElement("logistics_cost_template_id")]
	        public Nullable<long> LogisticsCostTemplateId { get; set; }
	
	        /// <summary>
	        /// 业务请求ID，用于幂等
	        /// </summary>
	        [XmlElement("request_id")]
	        public string RequestId { get; set; }
	
	        /// <summary>
	        /// 业务请求时间。时间戳。 毫秒
	        /// </summary>
	        [XmlElement("request_time")]
	        public Nullable<long> RequestTime { get; set; }
	
	        /// <summary>
	        /// 币种
	        /// </summary>
	        [XmlElement("retail_currency")]
	        public string RetailCurrency { get; set; }
	
	        /// <summary>
	        /// 建议零售价。 建议分销商卖给消费者的价格， 单位 分。 非必传
	        /// </summary>
	        [XmlElement("retail_price")]
	        public Nullable<long> RetailPrice { get; set; }
	
	        /// <summary>
	        /// 【和货品ID二选一】货品编码
	        /// </summary>
	        [XmlElement("sc_item_code")]
	        public string ScItemCode { get; set; }
	
	        /// <summary>
	        /// 【和货品编码二选一】货品id
	        /// </summary>
	        [XmlElement("sc_item_id")]
	        public string ScItemId { get; set; }
	
	        /// <summary>
	        /// sku的商家编码， 如果没有sku就不传， 如果有sku则必传
	        /// </summary>
	        [XmlElement("sku_code")]
	        public string SkuCode { get; set; }
	
	        /// <summary>
	        /// 要选择进行铺货的店铺宝贝 skuId， 如果没有sku就不传
	        /// </summary>
	        [XmlElement("sku_id")]
	        public string SkuId { get; set; }
	
	        /// <summary>
	        /// sku规格
	        /// </summary>
	        [XmlElement("sku_title")]
	        public string SkuTitle { get; set; }
}

        #endregion
    }
}
