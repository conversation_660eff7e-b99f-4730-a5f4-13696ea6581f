using System;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: alibaba.product.merchantproduct.edit.get
    /// </summary>
    public class AlibabaProductMerchantproductEditGetRequest : BaseTopRequest<Top.Api.Response.AlibabaProductMerchantproductEditGetResponse>
    {
        /// <summary>
        /// 产品ID
        /// </summary>
        public Nullable<long> Id { get; set; }

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "alibaba.product.merchantproduct.edit.get";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("id", this.Id);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("id", this.Id);
        }

        #endregion
    }
}
