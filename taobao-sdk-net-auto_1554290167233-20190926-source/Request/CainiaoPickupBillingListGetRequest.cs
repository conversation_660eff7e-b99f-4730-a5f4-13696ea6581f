using System;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: cainiao.pickup.billing.list.get
    /// </summary>
    public class CainiaoPickupBillingListGetRequest : BaseTopRequest<Top.Api.Response.CainiaoPickupBillingListGetResponse>
    {
        /// <summary>
        /// 商家联系电话
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 快递始发地地址
        /// </summary>
        public string SenderAddress { get; set; }

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "cainiao.pickup.billing.list.get";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("mobile", this.Mobile);
            parameters.Add("sender_address", this.SenderAddress);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("sender_address", this.SenderAddress);
        }

        #endregion
    }
}
