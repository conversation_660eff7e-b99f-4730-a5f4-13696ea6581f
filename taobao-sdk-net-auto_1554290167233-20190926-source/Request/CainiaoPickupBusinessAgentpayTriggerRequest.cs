using System;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: cainiao.pickup.business.agentpay.trigger
    /// </summary>
    public class CainiaoPickupBusinessAgentpayTriggerRequest : BaseTopRequest<Top.Api.Response.CainiaoPickupBusinessAgentpayTriggerResponse>
    {
        /// <summary>
        /// 提供给合作伙伴使用的商家域业务类型
        /// </summary>
        public string BizType { get; set; }

        /// <summary>
        /// 裹裹寄件订单号
        /// </summary>
        public Nullable<long> OrderId { get; set; }

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "cainiao.pickup.business.agentpay.trigger";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("biz_type", this.BizType);
            parameters.Add("order_id", this.OrderId);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("order_id", this.OrderId);
        }

        #endregion
    }
}
