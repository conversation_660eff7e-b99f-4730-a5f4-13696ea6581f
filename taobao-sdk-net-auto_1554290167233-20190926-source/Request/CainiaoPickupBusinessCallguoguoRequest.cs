using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: cainiao.pickup.business.callguoguo
    /// </summary>
    public class CainiaoPickupBusinessCallguoguoRequest : BaseTopRequest<Top.Api.Response.CainiaoPickupBusinessCallguoguoResponse>
    {
        /// <summary>
        /// 业务场景
        /// </summary>
        public string BizType { get; set; }

        /// <summary>
        /// 商家是否有打印能力
        /// </summary>
        public Nullable<bool> CanPrint { get; set; }

        /// <summary>
        /// 包裹信息，最多传30个
        /// </summary>
        public string GuoGuoPackageInfo { get; set; }

        public List<GuoGuoPackageInfoDomain> GuoGuoPackageInfo_ { set { this.GuoGuoPackageInfo = TopUtils.ObjectToJson(value); } } 

        /// <summary>
        /// 电话
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 是否采用隐私寄件
        /// </summary>
        public Nullable<bool> PrivacyOn { get; set; }

        /// <summary>
        /// 门店id
        /// </summary>
        public string StoreId { get; set; }

        /// <summary>
        /// 门店名称
        /// </summary>
        public string StoreName { get; set; }

        /// <summary>
        /// 扩展字段，暂时先不要用
        /// </summary>
        public string ValueServices { get; set; }

        public List<ValueServiceDomain> ValueServices_ { set { this.ValueServices = TopUtils.ObjectToJson(value); } } 

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "cainiao.pickup.business.callguoguo";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("biz_type", this.BizType);
            parameters.Add("can_print", this.CanPrint);
            parameters.Add("guo_guo_package_info", this.GuoGuoPackageInfo);
            parameters.Add("mobile", this.Mobile);
            parameters.Add("privacy_on", this.PrivacyOn);
            parameters.Add("store_id", this.StoreId);
            parameters.Add("store_name", this.StoreName);
            parameters.Add("value_services", this.ValueServices);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("guo_guo_package_info", this.GuoGuoPackageInfo);
            RequestValidator.ValidateObjectMaxListSize("guo_guo_package_info", this.GuoGuoPackageInfo, 30);
            RequestValidator.ValidateObjectMaxListSize("value_services", this.ValueServices, 999);
        }

	/// <summary>
/// EncryptedCustomerInfoDomain Data Structure.
/// </summary>
[Serializable]

public class EncryptedCustomerInfoDomain : TopObject
{
	        /// <summary>
	        /// 淘宝加密后的收件人信息ID
	        /// </summary>
	        [XmlElement("oaid")]
	        public string Oaid { get; set; }
	
	        /// <summary>
	        /// 淘宝订单ID
	        /// </summary>
	        [XmlElement("taobao_order_id")]
	        public string TaobaoOrderId { get; set; }
}

	/// <summary>
/// BusinessCustomerDomain Data Structure.
/// </summary>
[Serializable]

public class BusinessCustomerDomain : TopObject
{
	        /// <summary>
	        /// 地址码
	        /// </summary>
	        [XmlElement("area_id")]
	        public string AreaId { get; set; }
	
	        /// <summary>
	        /// 淘宝加密字段（如果无法取到明文的收件人信息，可以本字段代替，否则勿使用本字段。若本字段下属2个字段都不为空，则取解密后的地址为收件人地址。否则此字段无效，依然取上面的full_address_detail等信息为收件人信息。若下属2个字段都不为空但有误，会下单失败）
	        /// </summary>
	        [XmlElement("encrypted_customer_info")]
	        public EncryptedCustomerInfoDomain EncryptedCustomerInfo { get; set; }
	
	        /// <summary>
	        /// 详细地址
	        /// </summary>
	        [XmlElement("full_address_detail")]
	        public string FullAddressDetail { get; set; }
	
	        /// <summary>
	        /// 电话
	        /// </summary>
	        [XmlElement("mobile")]
	        public string Mobile { get; set; }
	
	        /// <summary>
	        /// 姓名
	        /// </summary>
	        [XmlElement("name")]
	        public string Name { get; set; }
}

	/// <summary>
/// GuoGuoPackageInfoDomain Data Structure.
/// </summary>
[Serializable]

public class GuoGuoPackageInfoDomain : TopObject
{
	        /// <summary>
	        /// 服务商品
	        /// </summary>
	        [XmlElement("item_id")]
	        public string ItemId { get; set; }
	
	        /// <summary>
	        /// 商品名称
	        /// </summary>
	        [XmlElement("item_title")]
	        public string ItemTitle { get; set; }
	
	        /// <summary>
	        /// 包裹订单号
	        /// </summary>
	        [XmlElement("package_id")]
	        public string PackageId { get; set; }
	
	        /// <summary>
	        /// 收件人
	        /// </summary>
	        [XmlElement("receiver")]
	        public BusinessCustomerDomain Receiver { get; set; }
	
	        /// <summary>
	        /// 寄件人
	        /// </summary>
	        [XmlElement("sender")]
	        public BusinessCustomerDomain Sender { get; set; }
	
	        /// <summary>
	        /// 重量：克
	        /// </summary>
	        [XmlElement("weight")]
	        public Nullable<long> Weight { get; set; }
}

	/// <summary>
/// ValueServiceDomain Data Structure.
/// </summary>
[Serializable]

public class ValueServiceDomain : TopObject
{
	        /// <summary>
	        /// 增值服务类型
	        /// </summary>
	        [XmlElement("key")]
	        public string Key { get; set; }
	
	        /// <summary>
	        /// 增值服务值
	        /// </summary>
	        [XmlElement("value")]
	        public string Value { get; set; }
}

        #endregion
    }
}
