using System;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: cainiao.pickup.business.cancelreason.get
    /// </summary>
    public class CainiaoPickupBusinessCancelreasonGetRequest : BaseTopRequest<Top.Api.Response.CainiaoPickupBusinessCancelreasonGetResponse>
    {
        /// <summary>
        /// 下单操作人的手机号，用作联系，可以不填写
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 裹裹订单id
        /// </summary>
        public Nullable<long> OrderId { get; set; }

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "cainiao.pickup.business.cancelreason.get";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("mobile", this.Mobile);
            parameters.Add("order_id", this.OrderId);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("order_id", this.OrderId);
        }

        #endregion
    }
}
