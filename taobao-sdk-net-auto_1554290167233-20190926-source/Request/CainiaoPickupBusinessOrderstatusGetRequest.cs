using System;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: cainiao.pickup.business.orderstatus.get
    /// </summary>
    public class CainiaoPickupBusinessOrderstatusGetRequest : BaseTopRequest<Top.Api.Response.CainiaoPickupBusinessOrderstatusGetResponse>
    {
        /// <summary>
        /// 下单操作人的手机号，用作联系，可以不填写
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 裹裹寄件订单号
        /// </summary>
        public Nullable<long> TdOrderId { get; set; }

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "cainiao.pickup.business.orderstatus.get";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("mobile", this.Mobile);
            parameters.Add("td_order_id", this.TdOrderId);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("td_order_id", this.TdOrderId);
        }

        #endregion
    }
}
