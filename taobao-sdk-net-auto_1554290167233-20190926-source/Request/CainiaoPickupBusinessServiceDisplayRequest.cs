using System;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: cainiao.pickup.business.service.display
    /// </summary>
    public class CainiaoPickupBusinessServiceDisplayRequest : BaseTopRequest<Top.Api.Response.CainiaoPickupBusinessServiceDisplayResponse>
    {
        /// <summary>
        /// 业务类型
        /// </summary>
        public string BizType { get; set; }

        /// <summary>
        /// 寄件地址
        /// </summary>
        public string SenderAddress { get; set; }

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "cainiao.pickup.business.service.display";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("biz_type", this.BizType);
            parameters.Add("sender_address", this.SenderAddress);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("sender_address", this.SenderAddress);
        }

        #endregion
    }
}
