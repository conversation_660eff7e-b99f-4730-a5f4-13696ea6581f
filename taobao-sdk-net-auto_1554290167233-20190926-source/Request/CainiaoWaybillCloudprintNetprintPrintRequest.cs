using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: cainiao.waybill.cloudprint.netprint.print
    /// </summary>
    public class CainiaoWaybillCloudprintNetprintPrintRequest : BaseTopRequest<Top.Api.Response.CainiaoWaybillCloudprintNetprintPrintResponse>
    {
        /// <summary>
        /// 请求
        /// </summary>
        public string PrinterPrintData { get; set; }

        public CloudPrinterPrintRequestDomain PrinterPrintData_ { set { this.PrinterPrintData = TopUtils.ObjectToJson(value); } } 

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "cainiao.waybill.cloudprint.netprint.print";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("printer_print_data", this.PrinterPrintData);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("printer_print_data", this.PrinterPrintData);
        }

	/// <summary>
/// CustomDataDomain Data Structure.
/// </summary>
[Serializable]

public class CustomDataDomain : TopObject
{
	        /// <summary>
	        ///  自定义区数据
	        /// </summary>
	        [XmlElement("data")]
	        public string Data { get; set; }
	
	        /// <summary>
	        ///  自定义区链接
	        /// </summary>
	        [XmlElement("template_url")]
	        public string TemplateUrl { get; set; }
}

	/// <summary>
/// PrintDataDomain Data Structure.
/// </summary>
[Serializable]

public class PrintDataDomain : TopObject
{
	        /// <summary>
	        /// 追加的 data
	        /// </summary>
	        [XmlElement("add_data")]
	        public string AddData { get; set; }
	
	        /// <summary>
	        ///  打印数据
	        /// </summary>
	        [XmlElement("data")]
	        public string Data { get; set; }
	
	        /// <summary>
	        ///  是否加密
	        /// </summary>
	        [XmlElement("encrypted")]
	        public Nullable<bool> Encrypted { get; set; }
	
	        /// <summary>
	        /// 签名
	        /// </summary>
	        [XmlElement("signature")]
	        public string Signature { get; set; }
	
	        /// <summary>
	        ///  模板 url
	        /// </summary>
	        [XmlElement("template_url")]
	        public string TemplateUrl { get; set; }
	
	        /// <summary>
	        /// 版本
	        /// </summary>
	        [XmlElement("ver")]
	        public string Ver { get; set; }
}

	/// <summary>
/// CloudPrinterPrintRequestDomain Data Structure.
/// </summary>
[Serializable]

public class CloudPrinterPrintRequestDomain : TopObject
{
	        /// <summary>
	        /// 自定义内容
	        /// </summary>
	        [XmlElement("custom_data")]
	        public CustomDataDomain CustomData { get; set; }
	
	        /// <summary>
	        /// 打印数据
	        /// </summary>
	        [XmlElement("print_data")]
	        public PrintDataDomain PrintData { get; set; }
	
	        /// <summary>
	        /// 共享码
	        /// </summary>
	        [XmlElement("share_code")]
	        public string ShareCode { get; set; }
	
	        /// <summary>
	        /// 打印机 id
	        /// </summary>
	        [XmlElement("uid")]
	        public string Uid { get; set; }
}

        #endregion
    }
}
