using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: cainiao.waybill.ii.confirm
    /// </summary>
    public class CainiaoWaybillIiConfirmRequest : BaseTopRequest<Top.Api.Response.CainiaoWaybillIiConfirmResponse>
    {
        /// <summary>
        /// 订单确认信息
        /// </summary>
        public string ParamWaybillOrderConfirmRequest { get; set; }

        public WaybillOrderConfirmRequestDomain ParamWaybillOrderConfirmRequest_ { set { this.ParamWaybillOrderConfirmRequest = TopUtils.ObjectToJson(value); } } 

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "cainiao.waybill.ii.confirm";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("param_waybill_order_confirm_request", this.ParamWaybillOrderConfirmRequest);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
        }

	/// <summary>
/// WaybillOrderConfirmWaybillInfoDomain Data Structure.
/// </summary>
[Serializable]

public class WaybillOrderConfirmWaybillInfoDomain : TopObject
{
	        /// <summary>
	        /// 包裹高，单位厘米
	        /// </summary>
	        [XmlElement("height")]
	        public Nullable<long> Height { get; set; }
	
	        /// <summary>
	        /// 包裹长，单位厘米
	        /// </summary>
	        [XmlElement("length")]
	        public Nullable<long> Length { get; set; }
	
	        /// <summary>
	        /// 体积, 单位 ml
	        /// </summary>
	        [XmlElement("volume")]
	        public Nullable<long> Volume { get; set; }
	
	        /// <summary>
	        /// 面单号
	        /// </summary>
	        [XmlElement("waybill_code")]
	        public string WaybillCode { get; set; }
	
	        /// <summary>
	        /// 重量,单位 g
	        /// </summary>
	        [XmlElement("weight")]
	        public Nullable<long> Weight { get; set; }
	
	        /// <summary>
	        /// 包裹宽，单位厘米
	        /// </summary>
	        [XmlElement("width")]
	        public Nullable<long> Width { get; set; }
}

	/// <summary>
/// WaybillOrderConfirmRequestDomain Data Structure.
/// </summary>
[Serializable]

public class WaybillOrderConfirmRequestDomain : TopObject
{
	        /// <summary>
	        /// 预约上门收件
	        /// </summary>
	        [XmlElement("call_door_pick_up")]
	        public Nullable<bool> CallDoorPickUp { get; set; }
	
	        /// <summary>
	        /// cpCode
	        /// </summary>
	        [XmlElement("cp_code")]
	        public string CpCode { get; set; }
	
	        /// <summary>
	        /// 预约上门截止时间
	        /// </summary>
	        [XmlElement("door_pick_up_end_time")]
	        public string DoorPickUpEndTime { get; set; }
	
	        /// <summary>
	        /// 预约上门时间
	        /// </summary>
	        [XmlElement("door_pick_up_time")]
	        public string DoorPickUpTime { get; set; }
	
	        /// <summary>
	        /// 扩展信息，json String
	        /// </summary>
	        [XmlElement("extra_info")]
	        public string ExtraInfo { get; set; }
	
	        /// <summary>
	        /// 物流服务， json String
	        /// </summary>
	        [XmlElement("logistics_services")]
	        public string LogisticsServices { get; set; }
	
	        /// <summary>
	        /// 快递产品编码
	        /// </summary>
	        [XmlElement("product_code")]
	        public string ProductCode { get; set; }
	
	        /// <summary>
	        /// 客户订单货物总高，单位厘米
	        /// </summary>
	        [XmlElement("total_height")]
	        public Nullable<long> TotalHeight { get; set; }
	
	        /// <summary>
	        /// 订单货物总长,单位厘米
	        /// </summary>
	        [XmlElement("total_length")]
	        public Nullable<long> TotalLength { get; set; }
	
	        /// <summary>
	        /// 货物总体积，单位立方厘米
	        /// </summary>
	        [XmlElement("total_volume")]
	        public Nullable<long> TotalVolume { get; set; }
	
	        /// <summary>
	        /// 货物总重量，单位g
	        /// </summary>
	        [XmlElement("total_weight")]
	        public Nullable<long> TotalWeight { get; set; }
	
	        /// <summary>
	        /// 订单货物总宽，单位厘米
	        /// </summary>
	        [XmlElement("total_width")]
	        public Nullable<long> TotalWidth { get; set; }
	
	        /// <summary>
	        /// 物流单号信息
	        /// </summary>
	        [XmlArray("waybill_info")]
	        [XmlArrayItem("waybill_order_confirm_waybill_info")]
	        public List<WaybillOrderConfirmWaybillInfoDomain> WaybillInfo { get; set; }
}

        #endregion
    }
}
