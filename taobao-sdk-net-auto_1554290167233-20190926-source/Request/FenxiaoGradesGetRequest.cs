using System;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: taobao.fenxiao.grades.get
    /// </summary>
    public class FenxiaoGradesGetRequest : BaseTopRequest<Top.Api.Response.FenxiaoGradesGetResponse>
    {
        #region ITopRequest Members

        public override string GetApiName()
        {
            return "taobao.fenxiao.grades.get";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
        }

        #endregion
    }
}
