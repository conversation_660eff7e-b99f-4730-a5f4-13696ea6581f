using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: taobao.jst.sms.template.create
    /// </summary>
    public class JstSmsTemplateCreateRequest : BaseTopRequest<Top.Api.Response.JstSmsTemplateCreateResponse>
    {
        /// <summary>
        /// 申请模板入参
        /// </summary>
        public string SmsTemplateForIsvRequest { get; set; }

        public AddSmsTemplateForIsvRequestDomain SmsTemplateForIsvRequest_ { set { this.SmsTemplateForIsvRequest = TopUtils.ObjectToJson(value); } } 

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "taobao.jst.sms.template.create";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("sms_template_for_isv_request", this.SmsTemplateForIsvRequest);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
        }

	/// <summary>
/// DigitalSmsTemplateContentDTODomain Data Structure.
/// </summary>
[Serializable]

public class DigitalSmsTemplateContentDTODomain : TopObject
{
	        /// <summary>
	        /// 文件Base64转码后的字符串
	        /// </summary>
	        [XmlElement("file_contents")]
	        public string FileContents { get; set; }
	
	        /// <summary>
	        /// 文件名称
	        /// </summary>
	        [XmlElement("file_name")]
	        public string FileName { get; set; }
	
	        /// <summary>
	        /// 文件大小
	        /// </summary>
	        [XmlElement("file_size")]
	        public Nullable<long> FileSize { get; set; }
	
	        /// <summary>
	        /// 文件后缀名
	        /// </summary>
	        [XmlElement("file_suffix")]
	        public string FileSuffix { get; set; }
}

	/// <summary>
/// AddSmsTemplateForIsvRequestDomain Data Structure.
/// </summary>
[Serializable]

public class AddSmsTemplateForIsvRequestDomain : TopObject
{
	        /// <summary>
	        /// 使用场景说明
	        /// </summary>
	        [XmlElement("remark")]
	        public string Remark { get; set; }
	
	        /// <summary>
	        /// 模板内容，占位符用${}标识
	        /// </summary>
	        [XmlElement("template_content")]
	        public string TemplateContent { get; set; }
	
	        /// <summary>
	        /// 上传文件
	        /// </summary>
	        [XmlArray("template_infos")]
	        [XmlArrayItem("digital_sms_template_content_d_t_o")]
	        public List<DigitalSmsTemplateContentDTODomain> TemplateInfos { get; set; }
	
	        /// <summary>
	        /// 模板名称
	        /// </summary>
	        [XmlElement("template_name")]
	        public string TemplateName { get; set; }
	
	        /// <summary>
	        /// 0--验证码 1--短信通知 2-- 推广短信 3--国际/港澳台消息
	        /// </summary>
	        [XmlElement("template_type")]
	        public Nullable<long> TemplateType { get; set; }
	
	        /// <summary>
	        /// NORMAL -- 普通模板  DIGITAL--数字短信模板
	        /// </summary>
	        [XmlElement("template_type_class")]
	        public string TemplateTypeClass { get; set; }
}

        #endregion
    }
}
