using System;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: taobao.open.seller.biz.logistic.time.rule
    /// </summary>
    public class OpenSellerBizLogisticTimeRuleRequest : BaseTopRequest<Top.Api.Response.OpenSellerBizLogisticTimeRuleResponse>
    {
        /// <summary>
        /// 当日仓库最晚出库时间 当日可发货订单最晚的出库时间 24小时制 格式：HH:mm
        /// </summary>
        public string LastDeliveryTime { get; set; }

        /// <summary>
        /// 当日订单最晚截单时间， 当日可发货订单最晚的支付时间 24小时制 格式：HH:mm
        /// </summary>
        public string LastPayTime { get; set; }

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "taobao.open.seller.biz.logistic.time.rule";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("last_delivery_time", this.LastDeliveryTime);
            parameters.Add("last_pay_time", this.LastPayTime);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
        }

        #endregion
    }
}
