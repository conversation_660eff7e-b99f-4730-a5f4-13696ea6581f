using System;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: taobao.shop.coverage.query
    /// </summary>
    public class ShopCoverageQueryRequest : BaseTopRequest<Top.Api.Response.ShopCoverageQueryResponse>
    {
        /// <summary>
        /// 门店code
        /// </summary>
        public string ShopCode { get; set; }

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "taobao.shop.coverage.query";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("shop_code", this.ShopCode);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("shop_code", this.ShopCode);
        }

        #endregion
    }
}
