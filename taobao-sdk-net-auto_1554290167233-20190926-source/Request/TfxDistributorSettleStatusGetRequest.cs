using System;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: taobao.tfx.distributor.settle.status.get
    /// </summary>
    public class TfxDistributorSettleStatusGetRequest : BaseTopRequest<Top.Api.Response.TfxDistributorSettleStatusGetResponse>
    {
        #region ITopRequest Members

        public override string GetApiName()
        {
            return "taobao.tfx.distributor.settle.status.get";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
        }

        #endregion
    }
}
