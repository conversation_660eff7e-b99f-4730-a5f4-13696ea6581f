using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: taobao.tfx.item.publish
    /// </summary>
    public class TfxItemPublishRequest : BaseTopRequest<Top.Api.Response.TfxItemPublishResponse>
    {
        /// <summary>
        /// 接口参数
        /// </summary>
        public string IsvDistributeItemReq { get; set; }

        public IsvDistributeItemReqDomain IsvDistributeItemReq_ { set { this.IsvDistributeItemReq = TopUtils.ObjectToJson(value); } } 

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "taobao.tfx.item.publish";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("isv_distribute_item_req", this.IsvDistributeItemReq);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
        }

	/// <summary>
/// IsvDistributeItemReqDomain Data Structure.
/// </summary>
[Serializable]

public class IsvDistributeItemReqDomain : TopObject
{
	        /// <summary>
	        /// 供应商渠道商品id
	        /// </summary>
	        [XmlElement("supplier_item_id")]
	        public Nullable<long> SupplierItemId { get; set; }
}

        #endregion
    }
}
