using System;
using System.Xml.Serialization;
using System.Collections.Generic;
using Top.Api.Util;

namespace Top.Api.Request
{
    /// <summary>
    /// TOP API: taobao.trades.sold.query
    /// </summary>
    public class TradesSoldQueryRequest : BaseTopRequest<Top.Api.Response.TradesSoldQueryResponse>
    {
        /// <summary>
        /// 订单类型，默认值为1，可选值为：交易(1)，分销(2)，换货(3)
        /// </summary>
        public string OrderType { get; set; }

        /// <summary>
        /// 查询条件列表，多个条件之间是OR关系，最多支持20个。receiver_name、receiver_mobile、receiver_phone至少有一个值不为空。
        /// </summary>
        public string QueryList { get; set; }

        public List<OrderQueryDomain> QueryList_ { set { this.QueryList = TopUtils.ObjectToJson(value); } } 

        /// <summary>
        /// 业务场景编码。不同场景，策略不同。请根据产品功能选择相应的场景编号。可选的场景：1001(客服咨询)、1002(售后服务)，<a href="https://open.taobao.com/doc.htm?docId=120186&docType=1" target="_blank">详情点击</a>
        /// </summary>
        public string Scene { get; set; }

        #region ITopRequest Members

        public override string GetApiName()
        {
            return "taobao.trades.sold.query";
        }

        public override IDictionary<string, string> GetParameters()
        {
            TopDictionary parameters = new TopDictionary();
            parameters.Add("order_type", this.OrderType);
            parameters.Add("query_list", this.QueryList);
            parameters.Add("scene", this.Scene);
            if (this.otherParams != null)
            {
                parameters.AddAll(this.otherParams);
            }
            return parameters;
        }

        public override void Validate()
        {
            RequestValidator.ValidateRequired("query_list", this.QueryList);
            RequestValidator.ValidateObjectMaxListSize("query_list", this.QueryList, 999);
        }

	/// <summary>
/// OrderQueryDomain Data Structure.
/// </summary>
[Serializable]

public class OrderQueryDomain : TopObject
{
	        /// <summary>
	        /// 查询三个月内交易创建时间开始。格式:yyyy-MM-dd HH:mm:ss
	        /// </summary>
	        [XmlElement("end_created")]
	        public Nullable<DateTime> EndCreated { get; set; }
	
	        /// <summary>
	        /// 收件人的手机号
	        /// </summary>
	        [XmlElement("receiver_mobile")]
	        public string ReceiverMobile { get; set; }
	
	        /// <summary>
	        /// 收件人的姓名
	        /// </summary>
	        [XmlElement("receiver_name")]
	        public string ReceiverName { get; set; }
	
	        /// <summary>
	        /// 收件人电话号码
	        /// </summary>
	        [XmlElement("receiver_phone")]
	        public string ReceiverPhone { get; set; }
	
	        /// <summary>
	        /// 查询交易创建时间结束。格式:yyyy-MM-dd HH:mm:ss
	        /// </summary>
	        [XmlElement("start_created")]
	        public Nullable<DateTime> StartCreated { get; set; }
}

        #endregion
    }
}
