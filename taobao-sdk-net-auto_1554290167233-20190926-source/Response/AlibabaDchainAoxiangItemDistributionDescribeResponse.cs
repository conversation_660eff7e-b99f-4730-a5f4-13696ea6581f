using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Top.Api.Response
{
    /// <summary>
    /// AlibabaDchainAoxiangItemDistributionDescribeResponse.
    /// </summary>
    public class AlibabaDchainAoxiangItemDistributionDescribeResponse : TopResponse
    {
        /// <summary>
        /// 分销文描结果
        /// </summary>
        [XmlElement("create_item_distribution_request")]
        public TopResponseDomain CreateItemDistributionRequest { get; set; }

	/// <summary>
/// MaterialChangeResponseDomain Data Structure.
/// </summary>
[Serializable]

public class MaterialChangeResponseDomain : TopObject
{
	        /// <summary>
	        /// 错误码
	        /// </summary>
	        [XmlElement("code")]
	        public string Code { get; set; }
	
	        /// <summary>
	        /// 错误信息
	        /// </summary>
	        [XmlElement("message")]
	        public string Message { get; set; }
	
	        /// <summary>
	        /// 处理结果
	        /// </summary>
	        [XmlElement("success")]
	        public bool Success { get; set; }
}

	/// <summary>
/// TopResponseDomain Data Structure.
/// </summary>
[Serializable]

public class TopResponseDomain : TopObject
{
	        /// <summary>
	        /// 错误码
	        /// </summary>
	        [XmlElement("code")]
	        public string Code { get; set; }
	
	        /// <summary>
	        /// 业务处理结果
	        /// </summary>
	        [XmlElement("data")]
	        public MaterialChangeResponseDomain Data { get; set; }
	
	        /// <summary>
	        /// 错误信息
	        /// </summary>
	        [XmlElement("message")]
	        public string Message { get; set; }
	
	        /// <summary>
	        /// 调用接口是否成功。调用成功之后，需要看data里面的success，才能知道业务处理是否成功
	        /// </summary>
	        [XmlElement("success")]
	        public bool Success { get; set; }
	
	        /// <summary>
	        /// 操作码
	        /// </summary>
	        [XmlElement("trace_id")]
	        public string TraceId { get; set; }
}

    }
}
