using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Top.Api.Response
{
    /// <summary>
    /// AlibabaDchainAoxiangReceiverinfoQueryResponse.
    /// </summary>
    public class AlibabaDchainAoxiangReceiverinfoQueryResponse : TopResponse
    {
        /// <summary>
        /// 用户信息
        /// </summary>
        [XmlElement("order_receiver_privacy_response")]
        public OrderReceiverPrivacyResponseDomain OrderReceiverPrivacyResponse { get; set; }

	/// <summary>
/// OrderReceiverPrivacyDataDomain Data Structure.
/// </summary>
[Serializable]

public class OrderReceiverPrivacyDataDomain : TopObject
{
	        /// <summary>
	        /// 收件人所在城市
	        /// </summary>
	        [XmlElement("city")]
	        public string City { get; set; }
	
	        /// <summary>
	        /// 国家二字码
	        /// </summary>
	        [XmlElement("country_code")]
	        public string CountryCode { get; set; }
	
	        /// <summary>
	        /// 详细地址
	        /// </summary>
	        [XmlElement("detail_address")]
	        public string DetailAddress { get; set; }
	
	        /// <summary>
	        /// 收件人所在区
	        /// </summary>
	        [XmlElement("district")]
	        public string District { get; set; }
	
	        /// <summary>
	        /// 姓名
	        /// </summary>
	        [XmlElement("name")]
	        public string Name { get; set; }
	
	        /// <summary>
	        /// 移动电话隐私
	        /// </summary>
	        [XmlElement("phone")]
	        public string Phone { get; set; }
	
	        /// <summary>
	        /// 省份
	        /// </summary>
	        [XmlElement("province")]
	        public string Province { get; set; }
	
	        /// <summary>
	        /// 收件人所在街道
	        /// </summary>
	        [XmlElement("town")]
	        public string Town { get; set; }
}

	/// <summary>
/// OrderReceiverPrivacyResponseDomain Data Structure.
/// </summary>
[Serializable]

public class OrderReceiverPrivacyResponseDomain : TopObject
{
	        /// <summary>
	        /// 响应码
	        /// </summary>
	        [XmlElement("code")]
	        public string Code { get; set; }
	
	        /// <summary>
	        /// 用户信息
	        /// </summary>
	        [XmlElement("data")]
	        public OrderReceiverPrivacyDataDomain Data { get; set; }
	
	        /// <summary>
	        /// 响应信息
	        /// </summary>
	        [XmlElement("message")]
	        public string Message { get; set; }
	
	        /// <summary>
	        /// 成功或者失败
	        /// </summary>
	        [XmlElement("success")]
	        public bool Success { get; set; }
}

    }
}
