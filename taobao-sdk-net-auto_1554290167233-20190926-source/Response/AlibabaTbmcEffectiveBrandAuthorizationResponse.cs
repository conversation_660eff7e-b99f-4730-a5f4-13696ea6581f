using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Top.Api.Response
{
    /// <summary>
    /// AlibabaTbmcEffectiveBrandAuthorizationResponse.
    /// </summary>
    public class AlibabaTbmcEffectiveBrandAuthorizationResponse : TopResponse
    {
        /// <summary>
        /// 授权类目信息对象
        /// </summary>
        [XmlElement("result")]
        public PageResultDTODomain Result { get; set; }

	/// <summary>
/// BrandAuthorizationDTODomain Data Structure.
/// </summary>
[Serializable]

public class BrandAuthorizationDTODomain : TopObject
{
	        /// <summary>
	        /// ("I", "未开始合作"), ("N", "正常合作"), ("S", "暂停合作"), ("T", "终止合作"),  ("VM_CHECKING", "待验商验仓")
	        /// </summary>
	        [XmlElement("authorize_cooperate_status")]
	        public string AuthorizeCooperateStatus { get; set; }
	
	        /// <summary>
	        /// forest类目ID
	        /// </summary>
	        [XmlElement("category_id")]
	        public long CategoryId { get; set; }
	
	        /// <summary>
	        /// 类目名称
	        /// </summary>
	        [XmlElement("category_name")]
	        public string CategoryName { get; set; }
	
	        /// <summary>
	        /// 授权结束时间
	        /// </summary>
	        [XmlElement("end_date")]
	        public string EndDate { get; set; }
	
	        /// <summary>
	        /// 授权开始时间
	        /// </summary>
	        [XmlElement("start_date")]
	        public string StartDate { get; set; }
}

	/// <summary>
/// PageResultDTODomain Data Structure.
/// </summary>
[Serializable]

public class PageResultDTODomain : TopObject
{
	        /// <summary>
	        /// 错误码
	        /// </summary>
	        [XmlElement("code")]
	        public string Code { get; set; }
	
	        /// <summary>
	        /// 列表
	        /// </summary>
	        [XmlArray("data")]
	        [XmlArrayItem("brand_authorization_d_t_o")]
	        public List<BrandAuthorizationDTODomain> Data { get; set; }
	
	        /// <summary>
	        /// 错误信息
	        /// </summary>
	        [XmlElement("message")]
	        public string Message { get; set; }
	
	        /// <summary>
	        /// 总页数
	        /// </summary>
	        [XmlElement("page_count")]
	        public long PageCount { get; set; }
	
	        /// <summary>
	        /// 页码
	        /// </summary>
	        [XmlElement("page_index")]
	        public long PageIndex { get; set; }
	
	        /// <summary>
	        /// 每页个数
	        /// </summary>
	        [XmlElement("page_size")]
	        public long PageSize { get; set; }
	
	        /// <summary>
	        /// 接口响应
	        /// </summary>
	        [XmlElement("success")]
	        public bool Success { get; set; }
	
	        /// <summary>
	        /// 总条数
	        /// </summary>
	        [XmlElement("total")]
	        public long Total { get; set; }
}

    }
}
