using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Top.Api.Response
{
    /// <summary>
    /// AlibabaTbmcInventoryPublishResponse.
    /// </summary>
    public class AlibabaTbmcInventoryPublishResponse : TopResponse
    {
        /// <summary>
        /// 库存发布结果对象
        /// </summary>
        [XmlElement("result")]
        public TopBaseResultDomain Result { get; set; }

	/// <summary>
/// InventoryPublishResultDTODomain Data Structure.
/// </summary>
[Serializable]

public class InventoryPublishResultDTODomain : TopObject
{
	        /// <summary>
	        /// 错误编码
	        /// </summary>
	        [XmlElement("error_code")]
	        public string ErrorCode { get; set; }
	
	        /// <summary>
	        /// 错误信息
	        /// </summary>
	        [XmlElement("error_message")]
	        public string ErrorMessage { get; set; }
	
	        /// <summary>
	        /// 商品编码
	        /// </summary>
	        [XmlElement("sku_code")]
	        public string SkuCode { get; set; }
	
	        /// <summary>
	        /// 是否处理成功（true标识成功，false标识失败）
	        /// </summary>
	        [XmlElement("success")]
	        public bool Success { get; set; }
	
	        /// <summary>
	        /// 仓库编码
	        /// </summary>
	        [XmlElement("warehouse_code")]
	        public string WarehouseCode { get; set; }
}

	/// <summary>
/// TopBaseResultDomain Data Structure.
/// </summary>
[Serializable]

public class TopBaseResultDomain : TopObject
{
	        /// <summary>
	        /// 数据
	        /// </summary>
	        [XmlArray("model")]
	        [XmlArrayItem("inventory_publish_result_d_t_o")]
	        public List<InventoryPublishResultDTODomain> Model { get; set; }
	
	        /// <summary>
	        /// 错误编码
	        /// </summary>
	        [XmlElement("return_code")]
	        public string ReturnCode { get; set; }
	
	        /// <summary>
	        /// 错误信息
	        /// </summary>
	        [XmlElement("return_msg")]
	        public string ReturnMsg { get; set; }
	
	        /// <summary>
	        /// 是否成功
	        /// </summary>
	        [XmlElement("success")]
	        public bool Success { get; set; }
}

    }
}
