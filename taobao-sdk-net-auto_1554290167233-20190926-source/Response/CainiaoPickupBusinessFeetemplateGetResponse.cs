using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Top.Api.Response
{
    /// <summary>
    /// CainiaoPickupBusinessFeetemplateGetResponse.
    /// </summary>
    public class CainiaoPickupBusinessFeetemplateGetResponse : TopResponse
    {
        /// <summary>
        /// 服务请求返回
        /// </summary>
        [XmlElement("result")]
        public TdOpenServiceResultDomain Result { get; set; }

	/// <summary>
/// AreaInfoDomain Data Structure.
/// </summary>
[Serializable]

public class AreaInfoDomain : TopObject
{
	        /// <summary>
	        /// 省份
	        /// </summary>
	        [XmlElement("prov_name")]
	        public string ProvName { get; set; }
}

	/// <summary>
/// StartContinuePriceDomain Data Structure.
/// </summary>
[Serializable]

public class StartContinuePriceDomain : TopObject
{
	        /// <summary>
	        /// 首重区间结束值（克）
	        /// </summary>
	        [XmlElement("end_weight")]
	        public long EndWeight { get; set; }
	
	        /// <summary>
	        /// 首重价格（分）
	        /// </summary>
	        [XmlElement("fix_price")]
	        public long FixPrice { get; set; }
	
	        /// <summary>
	        /// 首重区间起始值（克）
	        /// </summary>
	        [XmlElement("start_weight")]
	        public long StartWeight { get; set; }
}

	/// <summary>
/// LadderStartContinuePriceDataDomain Data Structure.
/// </summary>
[Serializable]

public class LadderStartContinuePriceDataDomain : TopObject
{
	        /// <summary>
	        /// 首重价格列表
	        /// </summary>
	        [XmlArray("start_continue_price_list")]
	        [XmlArrayItem("start_continue_price")]
	        public List<StartContinuePriceDomain> StartContinuePriceList { get; set; }
}

	/// <summary>
/// LadderOrderNumberDataDomain Data Structure.
/// </summary>
[Serializable]

public class LadderOrderNumberDataDomain : TopObject
{
	        /// <summary>
	        /// 续重价格（分）
	        /// </summary>
	        [XmlElement("continue_price")]
	        public long ContinuePrice { get; set; }
	
	        /// <summary>
	        /// 续重的起始重量，对应首重区间的最大值（克）
	        /// </summary>
	        [XmlElement("continue_start_weight")]
	        public long ContinueStartWeight { get; set; }
	
	        /// <summary>
	        /// 续重重量（克）
	        /// </summary>
	        [XmlElement("continue_weight")]
	        public long ContinueWeight { get; set; }
	
	        /// <summary>
	        /// 阶梯价单量结束值
	        /// </summary>
	        [XmlElement("end_order_number")]
	        public long EndOrderNumber { get; set; }
	
	        /// <summary>
	        /// 该单量区间对应的首重价格
	        /// </summary>
	        [XmlElement("ladder_start_continue_price")]
	        public LadderStartContinuePriceDataDomain LadderStartContinuePrice { get; set; }
	
	        /// <summary>
	        /// 用于展示的阶梯价单量区间
	        /// </summary>
	        [XmlElement("order_number_desc")]
	        public string OrderNumberDesc { get; set; }
	
	        /// <summary>
	        /// 阶梯价单量起始值
	        /// </summary>
	        [XmlElement("start_order_number")]
	        public long StartOrderNumber { get; set; }
}

	/// <summary>
/// GeneralAreaInfoDomain Data Structure.
/// </summary>
[Serializable]

public class GeneralAreaInfoDomain : TopObject
{
	        /// <summary>
	        /// 大区包含的省份列表
	        /// </summary>
	        [XmlArray("area_infos")]
	        [XmlArrayItem("area_info")]
	        public List<AreaInfoDomain> AreaInfos { get; set; }
	
	        /// <summary>
	        /// 大区名称
	        /// </summary>
	        [XmlElement("general_area_name")]
	        public string GeneralAreaName { get; set; }
	
	        /// <summary>
	        /// 阶梯价的单量区间，以及该区间对应的首重和续重价格
	        /// </summary>
	        [XmlArray("ladder_order_number_data_list")]
	        [XmlArrayItem("ladder_order_number_data")]
	        public List<LadderOrderNumberDataDomain> LadderOrderNumberDataList { get; set; }
}

	/// <summary>
/// FeeTemplateDomain Data Structure.
/// </summary>
[Serializable]

public class FeeTemplateDomain : TopObject
{
	        /// <summary>
	        /// 大区列表
	        /// </summary>
	        [XmlArray("general_area_infos")]
	        [XmlArrayItem("general_area_info")]
	        public List<GeneralAreaInfoDomain> GeneralAreaInfos { get; set; }
}

	/// <summary>
/// TdOpenServiceResultDomain Data Structure.
/// </summary>
[Serializable]

public class TdOpenServiceResultDomain : TopObject
{
	        /// <summary>
	        /// 报价单详情
	        /// </summary>
	        [XmlElement("data")]
	        public FeeTemplateDomain Data { get; set; }
	
	        /// <summary>
	        /// 是否需要重试
	        /// </summary>
	        [XmlElement("retry")]
	        public bool Retry { get; set; }
	
	        /// <summary>
	        /// 错误
	        /// </summary>
	        [XmlElement("status_code")]
	        public string StatusCode { get; set; }
	
	        /// <summary>
	        /// 错误的具体文案，可用于展示
	        /// </summary>
	        [XmlElement("status_message")]
	        public string StatusMessage { get; set; }
	
	        /// <summary>
	        /// 调用服务是否成功
	        /// </summary>
	        [XmlElement("success")]
	        public bool Success { get; set; }
}

    }
}
