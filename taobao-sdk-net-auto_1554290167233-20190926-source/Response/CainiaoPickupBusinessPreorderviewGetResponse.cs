using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Top.Api.Response
{
    /// <summary>
    /// CainiaoPickupBusinessPreorderviewGetResponse.
    /// </summary>
    public class CainiaoPickupBusinessPreorderviewGetResponse : TopResponse
    {
        /// <summary>
        /// 返回对象
        /// </summary>
        [XmlElement("result")]
        public TdOpenServiceResultDomain Result { get; set; }

	/// <summary>
/// PromotioninfosDomain Data Structure.
/// </summary>
[Serializable]

public class PromotioninfosDomain : TopObject
{
	        /// <summary>
	        /// 优惠文案
	        /// </summary>
	        [XmlElement("promotion_copy")]
	        public string PromotionCopy { get; set; }
	
	        /// <summary>
	        /// 优惠价格
	        /// </summary>
	        [XmlElement("promotion_price")]
	        public string PromotionPrice { get; set; }
	
	        /// <summary>
	        /// 优惠类型，用于区分优惠类别
	        /// </summary>
	        [XmlElement("promotion_type")]
	        public long PromotionType { get; set; }
}

	/// <summary>
/// GuoGuoPreOrderInfoDomain Data Structure.
/// </summary>
[Serializable]

public class GuoGuoPreOrderInfoDomain : TopObject
{
	        /// <summary>
	        /// 订单能否提供服务
	        /// </summary>
	        [XmlElement("can_service")]
	        public bool CanService { get; set; }
	
	        /// <summary>
	        /// 优惠价格
	        /// </summary>
	        [XmlElement("coupon_price")]
	        public long CouponPrice { get; set; }
	
	        /// <summary>
	        /// 实际价格
	        /// </summary>
	        [XmlElement("estimate_actual_price")]
	        public long EstimateActualPrice { get; set; }
	
	        /// <summary>
	        /// 原始价格
	        /// </summary>
	        [XmlElement("estimate_origin_price")]
	        public long EstimateOriginPrice { get; set; }
	
	        /// <summary>
	        /// 无法提供服务的原因
	        /// </summary>
	        [XmlElement("no_service_reason")]
	        public string NoServiceReason { get; set; }
	
	        /// <summary>
	        /// 传入的包裹运单号
	        /// </summary>
	        [XmlElement("package_id")]
	        public string PackageId { get; set; }
}

	/// <summary>
/// HrefLabelDomain Data Structure.
/// </summary>
[Serializable]

public class HrefLabelDomain : TopObject
{
	        /// <summary>
	        /// 描述信息
	        /// </summary>
	        [XmlElement("display_info")]
	        public string DisplayInfo { get; set; }
	
	        /// <summary>
	        /// 描述信息里建议加高亮处理的部分文案
	        /// </summary>
	        [XmlArray("high_lights")]
	        [XmlArrayItem("string")]
	        public List<string> HighLights { get; set; }
	
	        /// <summary>
	        /// 链接描述（如果描述信息里如果有链接）
	        /// </summary>
	        [XmlElement("href_desc")]
	        public string HrefDesc { get; set; }
	
	        /// <summary>
	        /// 链接类型（若link_url无值则本字段无效）：LINK("LINK"，"页面地址，当前页面打开")，NEW_LINK("NEW_LINK"，"页面地址，新页面打开")，IMG("IMG","图片地址")，如果没有传递，则默认代表LINK
	        /// </summary>
	        [XmlElement("link_type")]
	        public string LinkType { get; set; }
	
	        /// <summary>
	        /// 链接地址（如果描述信息里如果有链接）
	        /// </summary>
	        [XmlElement("link_url")]
	        public string LinkUrl { get; set; }
	
	        /// <summary>
	        /// 勾选类型（本字段暂时无效）
	        /// </summary>
	        [XmlElement("select_type")]
	        public string SelectType { get; set; }
	
	        /// <summary>
	        /// 提示信息
	        /// </summary>
	        [XmlElement("tooltip")]
	        public string Tooltip { get; set; }
}

	/// <summary>
/// OrderBenefitItemDomain Data Structure.
/// </summary>
[Serializable]

public class OrderBenefitItemDomain : TopObject
{
	        /// <summary>
	        /// 现价（可能是价格，也可能是优惠描述）
	        /// </summary>
	        [XmlElement("current_price")]
	        public string CurrentPrice { get; set; }
	
	        /// <summary>
	        /// 权益介绍
	        /// </summary>
	        [XmlElement("desc")]
	        public string Desc { get; set; }
	
	        /// <summary>
	        /// 权益Key
	        /// </summary>
	        [XmlElement("key")]
	        public string Key { get; set; }
	
	        /// <summary>
	        /// 权益Name
	        /// </summary>
	        [XmlElement("name")]
	        public string Name { get; set; }
	
	        /// <summary>
	        /// 原价（一般是价格，如果显示“无价格”表示这个本来不收费，建议就不要展示价格信息）
	        /// </summary>
	        [XmlElement("ori_price")]
	        public string OriPrice { get; set; }
	
	        /// <summary>
	        /// 勾选类型：AutoSelected（无需选择器，自动包含），Default（有选择器，由客户决定是否勾选）
	        /// </summary>
	        [XmlElement("select_type")]
	        public string SelectType { get; set; }
	
	        /// <summary>
	        /// 展示条件：AlwaysShow（始终展示），AlwaysHidden（始终隐藏）
	        /// </summary>
	        [XmlElement("show_condition")]
	        public string ShowCondition { get; set; }
	
	        /// <summary>
	        /// 补充介绍信息，建议做成浮层效果
	        /// </summary>
	        [XmlElement("tooltip")]
	        public string Tooltip { get; set; }
	
	        /// <summary>
	        /// 使用条件：NoCondition（无使用条件）
	        /// </summary>
	        [XmlElement("use_condition")]
	        public string UseCondition { get; set; }
}

	/// <summary>
/// ExternalOrderFeeDomain Data Structure.
/// </summary>
[Serializable]

public class ExternalOrderFeeDomain : TopObject
{
	        /// <summary>
	        /// 费用描述
	        /// </summary>
	        [XmlElement("fee_desc")]
	        public string FeeDesc { get; set; }
	
	        /// <summary>
	        /// 费用价格，单位：分
	        /// </summary>
	        [XmlElement("fee_price")]
	        public long FeePrice { get; set; }
	
	        /// <summary>
	        /// 当前费用收取状态，Used（已计入总价中），Removed（已免除，免收），若无返回也表示已经计入到了总价中
	        /// </summary>
	        [XmlElement("fee_status")]
	        public string FeeStatus { get; set; }
	
	        /// <summary>
	        /// 当前费用收取状态原因描述，例如常见的免收原因：VIP专享：免收权益
	        /// </summary>
	        [XmlElement("fee_status_reason")]
	        public string FeeStatusReason { get; set; }
	
	        /// <summary>
	        /// 该费用的展示的建议方式(PC端)
	        /// </summary>
	        [XmlElement("suggest_display_info")]
	        public HrefLabelDomain SuggestDisplayInfo { get; set; }
	
	        /// <summary>
	        /// 该费用的展示的建议方式(移动端)
	        /// </summary>
	        [XmlElement("suggest_display_info_for_mobile")]
	        public HrefLabelDomain SuggestDisplayInfoForMobile { get; set; }
}

	/// <summary>
/// OrderViewDtoDomain Data Structure.
/// </summary>
[Serializable]

public class OrderViewDtoDomain : TopObject
{
	        /// <summary>
	        /// 批量下单能够优惠的总金额
	        /// </summary>
	        [XmlElement("coupon_price")]
	        public long CouponPrice { get; set; }
	
	        /// <summary>
	        /// 预估价时，采用的预估重量描述
	        /// </summary>
	        [XmlElement("estimation_weight_desc")]
	        public string EstimationWeightDesc { get; set; }
	
	        /// <summary>
	        /// 本次订单额外需要支付的费用（已经计入到总价里了，这里只是展示告知客户）
	        /// </summary>
	        [XmlArray("external_order_fees")]
	        [XmlArrayItem("external_order_fee")]
	        public List<ExternalOrderFeeDomain> ExternalOrderFees { get; set; }
	
	        /// <summary>
	        /// 该次预估价调用的一些服务广告信息
	        /// </summary>
	        [XmlElement("order_ads_desc")]
	        public HrefLabelDomain OrderAdsDesc { get; set; }
	
	        /// <summary>
	        /// 本单可以享受的权益
	        /// </summary>
	        [XmlArray("order_benefit_desc_list")]
	        [XmlArrayItem("order_benefit_item")]
	        public List<OrderBenefitItemDomain> OrderBenefitDescList { get; set; }
	
	        /// <summary>
	        /// 该次预估价调用的整体描述
	        /// </summary>
	        [XmlElement("order_preview_desc")]
	        public HrefLabelDomain OrderPreviewDesc { get; set; }
	
	        /// <summary>
	        /// 单个包裹的预估信息
	        /// </summary>
	        [XmlArray("pre_order_info_list")]
	        [XmlArrayItem("guo_guo_pre_order_info")]
	        public List<GuoGuoPreOrderInfoDomain> PreOrderInfoList { get; set; }
	
	        /// <summary>
	        /// 报价类型（本字段可能没有返回）
	        /// </summary>
	        [XmlElement("price_tag")]
	        public string PriceTag { get; set; }
	
	        /// <summary>
	        /// 整批下单的促销信息列表
	        /// </summary>
	        [XmlArray("promotion_infos")]
	        [XmlArrayItem("promotioninfos")]
	        public List<PromotioninfosDomain> PromotionInfos { get; set; }
	
	        /// <summary>
	        /// 寄件人地址
	        /// </summary>
	        [XmlElement("sender_address_desc")]
	        public string SenderAddressDesc { get; set; }
	
	        /// <summary>
	        /// 批量下单优惠后预估价格
	        /// </summary>
	        [XmlElement("total_actual_price")]
	        public long TotalActualPrice { get; set; }
	
	        /// <summary>
	        /// 批量下单原始预估价格
	        /// </summary>
	        [XmlElement("total_origin_price")]
	        public long TotalOriginPrice { get; set; }
}

	/// <summary>
/// TdOpenServiceResultDomain Data Structure.
/// </summary>
[Serializable]

public class TdOpenServiceResultDomain : TopObject
{
	        /// <summary>
	        /// 返回对象
	        /// </summary>
	        [XmlElement("data")]
	        public OrderViewDtoDomain Data { get; set; }
	
	        /// <summary>
	        /// 是否需要重试
	        /// </summary>
	        [XmlElement("retry")]
	        public bool Retry { get; set; }
	
	        /// <summary>
	        /// 错误码
	        /// </summary>
	        [XmlElement("status_code")]
	        public string StatusCode { get; set; }
	
	        /// <summary>
	        /// 错误描述，如果不能使用服务，会在这里提示错误信息
	        /// </summary>
	        [XmlElement("status_message")]
	        public string StatusMessage { get; set; }
	
	        /// <summary>
	        /// 是否操作成功
	        /// </summary>
	        [XmlElement("success")]
	        public bool Success { get; set; }
}

    }
}
