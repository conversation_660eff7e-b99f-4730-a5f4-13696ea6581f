using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Top.Api.Response
{
    /// <summary>
    /// CainiaoWaybillPrivacySellerOrderGetResponse.
    /// </summary>
    public class CainiaoWaybillPrivacySellerOrderGetResponse : TopResponse
    {
        /// <summary>
        /// 错误列表
        /// </summary>
        [XmlArray("error_code_list")]
        [XmlArrayItem("string")]
        public List<string> ErrorCodeList { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [XmlArray("error_info_list")]
        [XmlArrayItem("string")]
        public List<string> ErrorInfoList { get; set; }

        /// <summary>
        /// 是否失败
        /// </summary>
        [XmlElement("failure")]
        public bool Failure { get; set; }

        /// <summary>
        /// objectId
        /// </summary>
        [XmlElement("object_id")]
        public string ObjectId { get; set; }

        /// <summary>
        /// 第一个错误
        /// </summary>
        [XmlElement("one_error_info")]
        public string OneErrorInfo { get; set; }

        /// <summary>
        /// 返回值
        /// </summary>
        [XmlArray("response_list")]
        [XmlArrayItem("module")]
        public List<ModuleDomain> ResponseList { get; set; }

	/// <summary>
/// ModuleDomain Data Structure.
/// </summary>
[Serializable]

public class ModuleDomain : TopObject
{
	        /// <summary>
	        /// 订单渠道
	        /// </summary>
	        [XmlElement("order_channel")]
	        public string OrderChannel { get; set; }
	
	        /// <summary>
	        /// 日期
	        /// </summary>
	        [XmlElement("order_date")]
	        public string OrderDate { get; set; }
	
	        /// <summary>
	        /// 隐私次数
	        /// </summary>
	        [XmlElement("privacy_count")]
	        public long PrivacyCount { get; set; }
	
	        /// <summary>
	        /// 商家ID
	        /// </summary>
	        [XmlElement("seller_id")]
	        public string SellerId { get; set; }
	
	        /// <summary>
	        /// 店铺id
	        /// </summary>
	        [XmlElement("shop_id")]
	        public string ShopId { get; set; }
}

    }
}
