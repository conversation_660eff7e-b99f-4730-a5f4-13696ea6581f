using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Top.Api.Response
{
    /// <summary>
    /// FenxiaoRefundGetResponse.
    /// </summary>
    public class FenxiaoRefundGetResponse : TopResponse
    {
        /// <summary>
        /// 退款详情
        /// </summary>
        [XmlElement("refund_detail")]
        public TopDpRefundDetailDODomain RefundDetail { get; set; }

	/// <summary>
/// BuyerRefundDomain Data Structure.
/// </summary>
[Serializable]

public class BuyerRefundDomain : TopObject
{
	        /// <summary>
	        /// 消费者订单退款涉及的消费者正向子订单号
	        /// </summary>
	        [XmlElement("biz_order_id")]
	        public long BizOrderId { get; set; }
	
	        /// <summary>
	        /// 消费者nick
	        /// </summary>
	        [XmlElement("buyer_nick")]
	        public string BuyerNick { get; set; }
	
	        /// <summary>
	        /// 货物的状态： 买家已收到货 买家已退货 买家未收到货
	        /// </summary>
	        [XmlElement("goods_status_desc")]
	        public string GoodsStatusDesc { get; set; }
	
	        /// <summary>
	        /// 消费者退款修改时间。格式:yyyy-MM-dd HH:mm:ss
	        /// </summary>
	        [XmlElement("modified")]
	        public string Modified { get; set; }
	
	        /// <summary>
	        /// 买家是否退货
	        /// </summary>
	        [XmlElement("need_return_goods")]
	        public bool NeedReturnGoods { get; set; }
	
	        /// <summary>
	        /// 消费者淘宝id的加密key
	        /// </summary>
	        [XmlElement("open_buyer_id")]
	        public string OpenBuyerId { get; set; }
	
	        /// <summary>
	        /// 消费者订单退款创建时间
	        /// </summary>
	        [XmlElement("refund_create_time")]
	        public string RefundCreateTime { get; set; }
	
	        /// <summary>
	        /// 消费者退款说明
	        /// </summary>
	        [XmlElement("refund_desc")]
	        public string RefundDesc { get; set; }
	
	        /// <summary>
	        /// 消费者订单对应的退款单号
	        /// </summary>
	        [XmlElement("refund_id")]
	        public long RefundId { get; set; }
	
	        /// <summary>
	        /// 消费者退款原因
	        /// </summary>
	        [XmlElement("refund_reason")]
	        public string RefundReason { get; set; }
	
	        /// <summary>
	        /// 消费者订单退款状态 1、消费者已经申请退款，等待分销商确认 2、分销商已经同意退货，等待消费者退货  3、消费者已经退货，等待分销商确认收货 4、退款关闭   5、退款成功 6、分销商拒绝退款,待消费者重新修改  7、等待消费者确认重新邮寄的货物  
	        /// </summary>
	        [XmlElement("refund_status")]
	        public long RefundStatus { get; set; }
	
	        /// <summary>
	        /// 退还给消费者的金额(分)
	        /// </summary>
	        [XmlElement("return_fee")]
	        public long ReturnFee { get; set; }
	
	        /// <summary>
	        /// 消费者退货数量
	        /// </summary>
	        [XmlElement("return_goods_quantity")]
	        public long ReturnGoodsQuantity { get; set; }
	
	        /// <summary>
	        /// 分销子订单号
	        /// </summary>
	        [XmlElement("sub_order_id")]
	        public long SubOrderId { get; set; }
	
	        /// <summary>
	        /// 确认收货后会打款给分销商的金额(分),分摊到子单的实付金额-退款给消费者的金额
	        /// </summary>
	        [XmlElement("to_seller_fee")]
	        public long ToSellerFee { get; set; }
}

	/// <summary>
/// RefundLogisticsDomain Data Structure.
/// </summary>
[Serializable]

public class RefundLogisticsDomain : TopObject
{
	        /// <summary>
	        /// 退货物流公司编码，如顺丰、韵达等
	        /// </summary>
	        [XmlElement("company_code")]
	        public string CompanyCode { get; set; }
	
	        /// <summary>
	        /// 退货物流公司名称，如顺丰
	        /// </summary>
	        [XmlElement("company_name")]
	        public string CompanyName { get; set; }
	
	        /// <summary>
	        /// 退货物流运单号
	        /// </summary>
	        [XmlElement("mail_no")]
	        public string MailNo { get; set; }
}

	/// <summary>
/// RefundItemDomain Data Structure.
/// </summary>
[Serializable]

public class RefundItemDomain : TopObject
{
	        /// <summary>
	        /// 退款明细ID，针对一笔退款每一个品就映射为一个明细，每一个明细有一个全局唯一的ID
	        /// </summary>
	        [XmlElement("refund_item_id")]
	        public long RefundItemId { get; set; }
	
	        /// <summary>
	        /// 退货数量
	        /// </summary>
	        [XmlElement("refund_quantity")]
	        public long RefundQuantity { get; set; }
	
	        /// <summary>
	        /// 分销子订单号
	        /// </summary>
	        [XmlElement("sub_order_id")]
	        public long SubOrderId { get; set; }
}

	/// <summary>
/// TopDpRefundDetailDODomain Data Structure.
/// </summary>
[Serializable]

public class TopDpRefundDetailDODomain : TopObject
{
	        /// <summary>
	        /// 前台消费者订单对应的退款详情
	        /// </summary>
	        [XmlElement("buyer_refund")]
	        public BuyerRefundDomain BuyerRefund { get; set; }
	
	        /// <summary>
	        /// 分销商nick
	        /// </summary>
	        [XmlElement("distributor_nick")]
	        public string DistributorNick { get; set; }
	
	        /// <summary>
	        /// 是否退货,如果是已发货退货退款/售后退货退款，就是true
	        /// </summary>
	        [XmlElement("is_return_goods")]
	        public bool IsReturnGoods { get; set; }
	
	        /// <summary>
	        /// 退款修改时间。格式:yyyy-MM-dd HH:mm:ss
	        /// </summary>
	        [XmlElement("modified")]
	        public string Modified { get; set; }
	
	        /// <summary>
	        /// 支付给供应商的金额(元)，分销订单子单实付金额-退款金额
	        /// </summary>
	        [XmlElement("pay_sup_fee")]
	        public string PaySupFee { get; set; }
	
	        /// <summary>
	        /// 分销主订单号
	        /// </summary>
	        [XmlElement("purchase_order_id")]
	        public long PurchaseOrderId { get; set; }
	
	        /// <summary>
	        /// 退款创建时间
	        /// </summary>
	        [XmlElement("refund_create_time")]
	        public string RefundCreateTime { get; set; }
	
	        /// <summary>
	        /// 退款说明
	        /// </summary>
	        [XmlElement("refund_desc")]
	        public string RefundDesc { get; set; }
	
	        /// <summary>
	        /// 退款的金额(元)
	        /// </summary>
	        [XmlElement("refund_fee")]
	        public string RefundFee { get; set; }
	
	        /// <summary>
	        /// 退款流程类型：4：未发货退款；1：已发货仅退款；2：已发货退货退款；3：售后仅退款；5：物流拒收；6：售后退货退款
	        /// </summary>
	        [XmlElement("refund_flow_type")]
	        public long RefundFlowType { get; set; }
	
	        /// <summary>
	        /// 分销退款单号
	        /// </summary>
	        [XmlElement("refund_id")]
	        public long RefundId { get; set; }
	
	        /// <summary>
	        /// 退款明细项，记录退款涉及的订单
	        /// </summary>
	        [XmlArray("refund_items")]
	        [XmlArrayItem("refund_item")]
	        public List<RefundItemDomain> RefundItems { get; set; }
	
	        /// <summary>
	        /// 退款原因
	        /// </summary>
	        [XmlElement("refund_reason")]
	        public string RefundReason { get; set; }
	
	        /// <summary>
	        /// 退款状态1：分销商已经申请退款，等待供应商确认2：供应商已经同意退货，等待分销商退货3：分销商已经退货，等待供应商确认收货4：退款关闭5：退款成功  6：供应商拒绝退款12：供应商同意退款，待系统打款  9：没有申请退款 10：供应商拒绝确认收货,待分销商重新修改
	        /// </summary>
	        [XmlElement("refund_status")]
	        public long RefundStatus { get; set; }
	
	        /// <summary>
	        /// 退货的物流信息
	        /// </summary>
	        [XmlArray("return_logistics")]
	        [XmlArrayItem("refund_logistics")]
	        public List<RefundLogisticsDomain> ReturnLogistics { get; set; }
	
	        /// <summary>
	        /// 分销子订单号，如果是by子单发起退款，就会在退款主单上记录分销子订单号
	        /// </summary>
	        [XmlElement("sub_order_id")]
	        public long SubOrderId { get; set; }
	
	        /// <summary>
	        /// 供应商nick
	        /// </summary>
	        [XmlElement("supplier_nick")]
	        public string SupplierNick { get; set; }
	
	        /// <summary>
	        /// 超时时间
	        /// </summary>
	        [XmlElement("timeout")]
	        public string Timeout { get; set; }
	
	        /// <summary>
	        /// 超时类型：  1：供应商同意退款/同意退货超时；  2：供应商确认收货超时
	        /// </summary>
	        [XmlElement("to_type")]
	        public long ToType { get; set; }
}

    }
}
