using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Top.Api.Response
{
    /// <summary>
    /// TfxAlipaySignUrlGetResponse.
    /// </summary>
    public class TfxAlipaySignUrlGetResponse : TopResponse
    {
        /// <summary>
        /// 支付宝协议签署链接
        /// </summary>
        [XmlElement("data")]
        public string Data { get; set; }

        /// <summary>
        /// 接口是否成功
        /// </summary>
        [XmlElement("is_success")]
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 返回码
        /// </summary>
        [XmlElement("result_code")]
        public long ResultCode { get; set; }

        /// <summary>
        /// 返回信息
        /// </summary>
        [XmlElement("result_msg")]
        public string ResultMsg { get; set; }

    }
}
