using System;
using System.Xml.Serialization;
using System.Collections.Generic;

namespace Top.Api.Response
{
    /// <summary>
    /// UserOpenuidGetbynickResponse.
    /// </summary>
    public class UserOpenuidGetbynickResponse : TopResponse
    {
        /// <summary>
        /// 买家uid对象
        /// </summary>
        [XmlArray("open_uids")]
        [XmlArrayItem("open_uid_info")]
        public List<OpenUidInfoDomain> OpenUids { get; set; }

	/// <summary>
/// OpenUidInfoDomain Data Structure.
/// </summary>
[Serializable]

public class OpenUidInfoDomain : TopObject
{
	        /// <summary>
	        /// 买家nick
	        /// </summary>
	        [XmlElement("buyer_nick")]
	        public string BuyerNick { get; set; }
	
	        /// <summary>
	        /// 买家openuid
	        /// </summary>
	        [XmlElement("buyer_open_uid")]
	        public string BuyerOpenUid { get; set; }
}

    }
}
