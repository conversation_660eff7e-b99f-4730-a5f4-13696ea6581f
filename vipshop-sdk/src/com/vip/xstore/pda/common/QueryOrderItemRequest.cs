using System;
using System.Collections.Generic;
using System.Text;

namespace com.vip.xstore.pda.common{
	
	
	
	
	
	public class QueryOrderItemRequest {
		
		///<summary>
		/// 系统来源  XUP-PC提交，PDA-APP提交
		///</summary>
		
		private string source_;
		
		///<summary>
		/// 设备ID
		///</summary>
		
		private string client_id_;
		
		///<summary>
		/// pda版本
		///</summary>
		
		private string pda_version_;
		
		///<summary>
		/// 调拨单号
		///</summary>
		
		private string transferring_no_;
		
		///<summary>
		/// 查询条码，最多支持50个
		///</summary>
		
		private List<string> barcodes_;
		
		///<summary>
		/// 当前页，默认1(-1返回全部，不分页，用于导出)
		///</summary>
		
		private int? page_;
		
		///<summary>
		/// 每页记录数，默认20，最大500
		///</summary>
		
		private int? page_size_;
		
		public string GetSource(){
			return this.source_;
		}
		
		public void SetSource(string value){
			this.source_ = value;
		}
		public string GetClient_id(){
			return this.client_id_;
		}
		
		public void SetClient_id(string value){
			this.client_id_ = value;
		}
		public string GetPda_version(){
			return this.pda_version_;
		}
		
		public void SetPda_version(string value){
			this.pda_version_ = value;
		}
		public string GetTransferring_no(){
			return this.transferring_no_;
		}
		
		public void SetTransferring_no(string value){
			this.transferring_no_ = value;
		}
		public List<string> GetBarcodes(){
			return this.barcodes_;
		}
		
		public void SetBarcodes(List<string> value){
			this.barcodes_ = value;
		}
		public int? GetPage(){
			return this.page_;
		}
		
		public void SetPage(int? value){
			this.page_ = value;
		}
		public int? GetPage_size(){
			return this.page_size_;
		}
		
		public void SetPage_size(int? value){
			this.page_size_ = value;
		}
		
	}
	
}