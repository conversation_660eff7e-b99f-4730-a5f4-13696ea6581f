using System;
using System.Collections.Generic;
using System.Text;

namespace com.vip.xstore.pda.order.common{
	
	
	
	
	
	public class ApprovalFlowRequest {
		
		///<summary>
		/// 系统来源  XUP-PC提交，PDA-APP提交
		///</summary>
		
		private string source_;
		
		///<summary>
		/// 设备ID
		///</summary>
		
		private string client_id_;
		
		///<summary>
		/// pda版本
		///</summary>
		
		private string pda_version_;
		
		///<summary>
		/// 调拔单号
		///</summary>
		
		private string transferring_no_;
		
		///<summary>
		/// 操作类型 1-提交 2-确认 3-取消
		///</summary>
		
		private int? approval_flag_;
		
		///<summary>
		/// 箱号
		///</summary>
		
		private string box_no_;
		
		public string GetSource(){
			return this.source_;
		}
		
		public void SetSource(string value){
			this.source_ = value;
		}
		public string GetClient_id(){
			return this.client_id_;
		}
		
		public void SetClient_id(string value){
			this.client_id_ = value;
		}
		public string GetPda_version(){
			return this.pda_version_;
		}
		
		public void SetPda_version(string value){
			this.pda_version_ = value;
		}
		public string GetTransferring_no(){
			return this.transferring_no_;
		}
		
		public void SetTransferring_no(string value){
			this.transferring_no_ = value;
		}
		public int? GetApproval_flag(){
			return this.approval_flag_;
		}
		
		public void SetApproval_flag(int? value){
			this.approval_flag_ = value;
		}
		public string GetBox_no(){
			return this.box_no_;
		}
		
		public void SetBox_no(string value){
			this.box_no_ = value;
		}
		
	}
	
}