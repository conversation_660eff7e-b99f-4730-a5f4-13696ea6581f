using System;
using System.Collections.Generic;
using System.Text;
using Osp.Sdk.Base;
using Osp.Sdk.Protocol;
using Osp.Sdk.Exception;
namespace com.vip.xstore.pda.order.common{
	
	
	
	public class DifferenceItemHelper : TBeanSerializer<DifferenceItem>
	{
		
		public static DifferenceItemHelper OBJ = new DifferenceItemHelper();
		
		public static DifferenceItemHelper getInstance() {
			
			return OBJ;
		}
		
		
		public void Read(DifferenceItem structs, Protocol iprot){
			
			
			String schemeStruct = iprot.ReadStructBegin();
			if(schemeStruct != null){
				
				while(true){
					
					String schemeField = iprot.ReadFieldBegin();
					if (schemeField == null) break;
					bool needSkip = true;
					
					
					if ("barcode".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetBarcode(value);
					}
					
					
					
					
					
					if ("confirmed_quantity".Equals(schemeField.Trim())){
						
						needSkip = false;
						int? value;
						value = iprot.ReadI32(); 
						
						structs.SetConfirmed_quantity(value);
					}
					
					
					
					
					
					if ("expect_quantity".Equals(schemeField.Trim())){
						
						needSkip = false;
						int? value;
						value = iprot.ReadI32(); 
						
						structs.SetExpect_quantity(value);
					}
					
					
					
					
					
					if ("submit_quantity".Equals(schemeField.Trim())){
						
						needSkip = false;
						int? value;
						value = iprot.ReadI32(); 
						
						structs.SetSubmit_quantity(value);
					}
					
					
					
					
					
					if ("stock".Equals(schemeField.Trim())){
						
						needSkip = false;
						int? value;
						value = iprot.ReadI32(); 
						
						structs.SetStock(value);
					}
					
					
					
					
					
					if ("diff_stock".Equals(schemeField.Trim())){
						
						needSkip = false;
						int? value;
						value = iprot.ReadI32(); 
						
						structs.SetDiff_stock(value);
					}
					
					
					
					
					
					if ("diff_quantity".Equals(schemeField.Trim())){
						
						needSkip = false;
						int? value;
						value = iprot.ReadI32(); 
						
						structs.SetDiff_quantity(value);
					}
					
					
					
					
					
					if ("failures".Equals(schemeField.Trim())){
						
						needSkip = false;
						List<com.vip.xstore.pda.order.common.FailureItem> value;
						
						value = new List<com.vip.xstore.pda.order.common.FailureItem>();
						iprot.ReadListBegin();
						while(true){
							
							try{
								
								com.vip.xstore.pda.order.common.FailureItem elem0;
								
								elem0 = new com.vip.xstore.pda.order.common.FailureItem();
								com.vip.xstore.pda.order.common.FailureItemHelper.getInstance().Read(elem0, iprot);
								
								value.Add(elem0);
							}
							catch(Exception e){
								
								
								break;
							}
						}
						
						iprot.ReadListEnd();
						
						structs.SetFailures(value);
					}
					
					
					
					
					if(needSkip){
						
						ProtocolUtil.skip(iprot);
					}
					
					iprot.ReadFieldEnd();
				}
				
				iprot.ReadStructEnd();
				Validate(structs);
			}
			else{
				
				throw new OspException();
			}
			
			
		}
		
		
		public void Write(DifferenceItem structs, Protocol oprot){
			
			Validate(structs);
			oprot.WriteStructBegin();
			
			if(structs.GetBarcode() != null) {
				
				oprot.WriteFieldBegin("barcode");
				oprot.WriteString(structs.GetBarcode());
				
				oprot.WriteFieldEnd();
			}
			
			else{
				throw new ArgumentException("barcode can not be null!");
			}
			
			
			if(structs.GetConfirmed_quantity() != null) {
				
				oprot.WriteFieldBegin("confirmed_quantity");
				oprot.WriteI32((int)structs.GetConfirmed_quantity()); 
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetExpect_quantity() != null) {
				
				oprot.WriteFieldBegin("expect_quantity");
				oprot.WriteI32((int)structs.GetExpect_quantity()); 
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetSubmit_quantity() != null) {
				
				oprot.WriteFieldBegin("submit_quantity");
				oprot.WriteI32((int)structs.GetSubmit_quantity()); 
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetStock() != null) {
				
				oprot.WriteFieldBegin("stock");
				oprot.WriteI32((int)structs.GetStock()); 
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetDiff_stock() != null) {
				
				oprot.WriteFieldBegin("diff_stock");
				oprot.WriteI32((int)structs.GetDiff_stock()); 
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetDiff_quantity() != null) {
				
				oprot.WriteFieldBegin("diff_quantity");
				oprot.WriteI32((int)structs.GetDiff_quantity()); 
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetFailures() != null) {
				
				oprot.WriteFieldBegin("failures");
				
				oprot.WriteListBegin();
				foreach(com.vip.xstore.pda.order.common.FailureItem _item0 in structs.GetFailures()){
					
					
					com.vip.xstore.pda.order.common.FailureItemHelper.getInstance().Write(_item0, oprot);
					
				}
				
				oprot.WriteListEnd();
				
				oprot.WriteFieldEnd();
			}
			
			
			oprot.WriteFieldStop();
			oprot.WriteStructEnd();
		}
		
		
		public void Validate(DifferenceItem bean){
			
			
		}
		
	}
	
}