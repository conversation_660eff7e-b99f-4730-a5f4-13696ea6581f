using System;
using System.Collections.Generic;
using System.Text;

namespace com.vip.xstore.pda.order.common{
	
	
	
	
	
	public class SubmitFlowResponse {
		
		///<summary>
		/// 是否成功 0-失败 1-成功
		///</summary>
		
		private int? is_succeed_;
		
		///<summary>
		/// 总数
		///</summary>
		
		private int? total_expect_quantity_;
		
		///<summary>
		/// 此次提交数
		///</summary>
		
		private int? total_submit_quantity_;
		
		///<summary>
		/// 已确认数
		///</summary>
		
		private int? total_confirmed_quantity_;
		
		///<summary>
		/// 剩余总数 (总计划数 - 总确认数 - 此次提交数)
		///</summary>
		
		private int? leaving_quantity_;
		
		///<summary>
		/// 提交流水差异
		///</summary>
		
		private List<com.vip.xstore.pda.order.common.DifferenceItem> diffs_;
		
		///<summary>
		/// 事务ID，is_succeed=0时返回
		///</summary>
		
		private string tx_id_;
		
		public int? GetIs_succeed(){
			return this.is_succeed_;
		}
		
		public void SetIs_succeed(int? value){
			this.is_succeed_ = value;
		}
		public int? GetTotal_expect_quantity(){
			return this.total_expect_quantity_;
		}
		
		public void SetTotal_expect_quantity(int? value){
			this.total_expect_quantity_ = value;
		}
		public int? GetTotal_submit_quantity(){
			return this.total_submit_quantity_;
		}
		
		public void SetTotal_submit_quantity(int? value){
			this.total_submit_quantity_ = value;
		}
		public int? GetTotal_confirmed_quantity(){
			return this.total_confirmed_quantity_;
		}
		
		public void SetTotal_confirmed_quantity(int? value){
			this.total_confirmed_quantity_ = value;
		}
		public int? GetLeaving_quantity(){
			return this.leaving_quantity_;
		}
		
		public void SetLeaving_quantity(int? value){
			this.leaving_quantity_ = value;
		}
		public List<com.vip.xstore.pda.order.common.DifferenceItem> GetDiffs(){
			return this.diffs_;
		}
		
		public void SetDiffs(List<com.vip.xstore.pda.order.common.DifferenceItem> value){
			this.diffs_ = value;
		}
		public string GetTx_id(){
			return this.tx_id_;
		}
		
		public void SetTx_id(string value){
			this.tx_id_ = value;
		}
		
	}
	
}