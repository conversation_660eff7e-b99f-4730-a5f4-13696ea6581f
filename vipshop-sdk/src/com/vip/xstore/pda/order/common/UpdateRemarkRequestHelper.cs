using System;
using System.Collections.Generic;
using System.Text;
using Osp.Sdk.Base;
using Osp.Sdk.Protocol;
using Osp.Sdk.Exception;
namespace com.vip.xstore.pda.order.common{
	
	
	
	public class UpdateRemarkRequestHelper : TBeanSerializer<UpdateRemarkRequest>
	{
		
		public static UpdateRemarkRequestHelper OBJ = new UpdateRemarkRequestHelper();
		
		public static UpdateRemarkRequestHelper getInstance() {
			
			return OBJ;
		}
		
		
		public void Read(UpdateRemarkRequest structs, Protocol iprot){
			
			
			String schemeStruct = iprot.ReadStructBegin();
			if(schemeStruct != null){
				
				while(true){
					
					String schemeField = iprot.ReadFieldBegin();
					if (schemeField == null) break;
					bool needSkip = true;
					
					
					if ("source".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetSource(value);
					}
					
					
					
					
					
					if ("client_id".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetClient_id(value);
					}
					
					
					
					
					
					if ("pda_version".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetPda_version(value);
					}
					
					
					
					
					
					if ("transferring_no".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetTransferring_no(value);
					}
					
					
					
					
					
					if ("barcode".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetBarcode(value);
					}
					
					
					
					
					
					if ("box_no".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetBox_no(value);
					}
					
					
					
					
					
					if ("remark".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetRemark(value);
					}
					
					
					
					
					if(needSkip){
						
						ProtocolUtil.skip(iprot);
					}
					
					iprot.ReadFieldEnd();
				}
				
				iprot.ReadStructEnd();
				Validate(structs);
			}
			else{
				
				throw new OspException();
			}
			
			
		}
		
		
		public void Write(UpdateRemarkRequest structs, Protocol oprot){
			
			Validate(structs);
			oprot.WriteStructBegin();
			
			if(structs.GetSource() != null) {
				
				oprot.WriteFieldBegin("source");
				oprot.WriteString(structs.GetSource());
				
				oprot.WriteFieldEnd();
			}
			
			else{
				throw new ArgumentException("source can not be null!");
			}
			
			
			if(structs.GetClient_id() != null) {
				
				oprot.WriteFieldBegin("client_id");
				oprot.WriteString(structs.GetClient_id());
				
				oprot.WriteFieldEnd();
			}
			
			else{
				throw new ArgumentException("client_id can not be null!");
			}
			
			
			if(structs.GetPda_version() != null) {
				
				oprot.WriteFieldBegin("pda_version");
				oprot.WriteString(structs.GetPda_version());
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetTransferring_no() != null) {
				
				oprot.WriteFieldBegin("transferring_no");
				oprot.WriteString(structs.GetTransferring_no());
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetBarcode() != null) {
				
				oprot.WriteFieldBegin("barcode");
				oprot.WriteString(structs.GetBarcode());
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetBox_no() != null) {
				
				oprot.WriteFieldBegin("box_no");
				oprot.WriteString(structs.GetBox_no());
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetRemark() != null) {
				
				oprot.WriteFieldBegin("remark");
				oprot.WriteString(structs.GetRemark());
				
				oprot.WriteFieldEnd();
			}
			
			
			oprot.WriteFieldStop();
			oprot.WriteStructEnd();
		}
		
		
		public void Validate(UpdateRemarkRequest bean){
			
			
		}
		
	}
	
}