using System;
using System.Collections.Generic;
using System.Text;

namespace com.vip.xstore.pda.order.receiving{
	
	
	
	
	
	public class PDAReceivingOrderRequest {
		
		///<summary>
		/// 系统来源  XUP-PC提交，PDA-APP提交
		///</summary>
		
		private string source_;
		
		///<summary>
		/// 设备ID
		///</summary>
		
		private string client_id_;
		
		///<summary>
		/// pda版本
		///</summary>
		
		private string pda_version_;
		
		///<summary>
		/// 收货类型 0-全部 1-单 2-箱（PDA查询时必须传）
		///</summary>
		
		private byte? receiving_type_;
		
		///<summary>
		/// 创建时间范围
		///</summary>
		
		private com.vip.xstore.pda.common.TimeRange create_time_range_;
		
		///<summary>
		/// 调拔单号（有单号时忽略查询时间）
		///</summary>
		
		private List<string> order_nos_;
		
		///<summary>
		/// 收货单状态，0-新增 1-收货中 2-收货完成 3-已取消
		///</summary>
		
		private List<byte?> action_types_;
		
		///<summary>
		/// 页码，默认第1页
		///</summary>
		
		private int? page_;
		
		///<summary>
		/// 每页记录数 默认20，最大100
		///</summary>
		
		private int? page_size_;
		
		public string GetSource(){
			return this.source_;
		}
		
		public void SetSource(string value){
			this.source_ = value;
		}
		public string GetClient_id(){
			return this.client_id_;
		}
		
		public void SetClient_id(string value){
			this.client_id_ = value;
		}
		public string GetPda_version(){
			return this.pda_version_;
		}
		
		public void SetPda_version(string value){
			this.pda_version_ = value;
		}
		public byte? GetReceiving_type(){
			return this.receiving_type_;
		}
		
		public void SetReceiving_type(byte? value){
			this.receiving_type_ = value;
		}
		public com.vip.xstore.pda.common.TimeRange GetCreate_time_range(){
			return this.create_time_range_;
		}
		
		public void SetCreate_time_range(com.vip.xstore.pda.common.TimeRange value){
			this.create_time_range_ = value;
		}
		public List<string> GetOrder_nos(){
			return this.order_nos_;
		}
		
		public void SetOrder_nos(List<string> value){
			this.order_nos_ = value;
		}
		public List<byte?> GetAction_types(){
			return this.action_types_;
		}
		
		public void SetAction_types(List<byte?> value){
			this.action_types_ = value;
		}
		public int? GetPage(){
			return this.page_;
		}
		
		public void SetPage(int? value){
			this.page_ = value;
		}
		public int? GetPage_size(){
			return this.page_size_;
		}
		
		public void SetPage_size(int? value){
			this.page_size_ = value;
		}
		
	}
	
}