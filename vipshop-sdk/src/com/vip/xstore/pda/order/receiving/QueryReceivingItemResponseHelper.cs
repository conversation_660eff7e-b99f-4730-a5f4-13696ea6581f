using System;
using System.Collections.Generic;
using System.Text;
using Osp.Sdk.Base;
using Osp.Sdk.Protocol;
using Osp.Sdk.Exception;
namespace com.vip.xstore.pda.order.receiving{
	
	
	
	public class QueryReceivingItemResponseHelper : TBeanSerializer<QueryReceivingItemResponse>
	{
		
		public static QueryReceivingItemResponseHelper OBJ = new QueryReceivingItemResponseHelper();
		
		public static QueryReceivingItemResponseHelper getInstance() {
			
			return OBJ;
		}
		
		
		public void Read(QueryReceivingItemResponse structs, Protocol iprot){
			
			
			String schemeStruct = iprot.ReadStructBegin();
			if(schemeStruct != null){
				
				while(true){
					
					String schemeField = iprot.ReadFieldBegin();
					if (schemeField == null) break;
					bool needSkip = true;
					
					
					if ("total".Equals(schemeField.Trim())){
						
						needSkip = false;
						int value;
						value = iprot.ReadI32(); 
						
						structs.SetTotal(value);
					}
					
					
					
					
					
					if ("items".Equals(schemeField.Trim())){
						
						needSkip = false;
						List<com.vip.xstore.pda.order.receiving.ReceivingOrderItem> value;
						
						value = new List<com.vip.xstore.pda.order.receiving.ReceivingOrderItem>();
						iprot.ReadListBegin();
						while(true){
							
							try{
								
								com.vip.xstore.pda.order.receiving.ReceivingOrderItem elem0;
								
								elem0 = new com.vip.xstore.pda.order.receiving.ReceivingOrderItem();
								com.vip.xstore.pda.order.receiving.ReceivingOrderItemHelper.getInstance().Read(elem0, iprot);
								
								value.Add(elem0);
							}
							catch(Exception e){
								
								
								break;
							}
						}
						
						iprot.ReadListEnd();
						
						structs.SetItems(value);
					}
					
					
					
					
					if(needSkip){
						
						ProtocolUtil.skip(iprot);
					}
					
					iprot.ReadFieldEnd();
				}
				
				iprot.ReadStructEnd();
				Validate(structs);
			}
			else{
				
				throw new OspException();
			}
			
			
		}
		
		
		public void Write(QueryReceivingItemResponse structs, Protocol oprot){
			
			Validate(structs);
			oprot.WriteStructBegin();
			
			if(structs.GetTotal() != null) {
				
				oprot.WriteFieldBegin("total");
				oprot.WriteI32((int)structs.GetTotal()); 
				
				oprot.WriteFieldEnd();
			}
			
			else{
				throw new ArgumentException("total can not be null!");
			}
			
			
			if(structs.GetItems() != null) {
				
				oprot.WriteFieldBegin("items");
				
				oprot.WriteListBegin();
				foreach(com.vip.xstore.pda.order.receiving.ReceivingOrderItem _item0 in structs.GetItems()){
					
					
					com.vip.xstore.pda.order.receiving.ReceivingOrderItemHelper.getInstance().Write(_item0, oprot);
					
				}
				
				oprot.WriteListEnd();
				
				oprot.WriteFieldEnd();
			}
			
			else{
				throw new ArgumentException("items can not be null!");
			}
			
			
			oprot.WriteFieldStop();
			oprot.WriteStructEnd();
		}
		
		
		public void Validate(QueryReceivingItemResponse bean){
			
			
		}
		
	}
	
}