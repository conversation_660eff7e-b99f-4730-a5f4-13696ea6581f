using System;
using System.Collections.Generic;
using System.Text;
using Osp.Sdk.Base;
using Osp.Sdk.Protocol;
using Osp.Sdk.Exception;
namespace com.vip.xstore.pda.order.receiving{
	
	
	
	public class XUPReceivingOrderRequestHelper : TBeanSerializer<XUPReceivingOrderRequest>
	{
		
		public static XUPReceivingOrderRequestHelper OBJ = new XUPReceivingOrderRequestHelper();
		
		public static XUPReceivingOrderRequestHelper getInstance() {
			
			return OBJ;
		}
		
		
		public void Read(XUPReceivingOrderRequest structs, Protocol iprot){
			
			
			String schemeStruct = iprot.ReadStructBegin();
			if(schemeStruct != null){
				
				while(true){
					
					String schemeField = iprot.ReadFieldBegin();
					if (schemeField == null) break;
					bool needSkip = true;
					
					
					if ("source".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetSource(value);
					}
					
					
					
					
					
					if ("client_id".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetClient_id(value);
					}
					
					
					
					
					
					if ("pda_version".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetPda_version(value);
					}
					
					
					
					
					
					if ("receiving_type".Equals(schemeField.Trim())){
						
						needSkip = false;
						byte? value;
						value = iprot.ReadByte(); 
						
						structs.SetReceiving_type(value);
					}
					
					
					
					
					
					if ("create_time_range".Equals(schemeField.Trim())){
						
						needSkip = false;
						com.vip.xstore.pda.common.TimeRange value;
						
						value = new com.vip.xstore.pda.common.TimeRange();
						com.vip.xstore.pda.common.TimeRangeHelper.getInstance().Read(value, iprot);
						
						structs.SetCreate_time_range(value);
					}
					
					
					
					
					
					if ("order_nos".Equals(schemeField.Trim())){
						
						needSkip = false;
						List<string> value;
						
						value = new List<string>();
						iprot.ReadSetBegin();
						while(true){
							
							try{
								
								string elem1;
								elem1 = iprot.ReadString();
								
								value.Add(elem1);
							}
							catch(Exception e){
								
								
								break;
							}
						}
						
						iprot.ReadSetEnd();
						
						structs.SetOrder_nos(value);
					}
					
					
					
					
					
					if ("action_types".Equals(schemeField.Trim())){
						
						needSkip = false;
						List<byte?> value;
						
						value = new List<byte?>();
						iprot.ReadSetBegin();
						while(true){
							
							try{
								
								byte elem2;
								elem2 = iprot.ReadByte(); 
								
								value.Add(elem2);
							}
							catch(Exception e){
								
								
								break;
							}
						}
						
						iprot.ReadSetEnd();
						
						structs.SetAction_types(value);
					}
					
					
					
					
					
					if ("page".Equals(schemeField.Trim())){
						
						needSkip = false;
						int value;
						value = iprot.ReadI32(); 
						
						structs.SetPage(value);
					}
					
					
					
					
					
					if ("page_size".Equals(schemeField.Trim())){
						
						needSkip = false;
						int value;
						value = iprot.ReadI32(); 
						
						structs.SetPage_size(value);
					}
					
					
					
					
					
					if ("delivery_warehouse_code".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetDelivery_warehouse_code(value);
					}
					
					
					
					
					
					if ("receiving_warehouse_code".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetReceiving_warehouse_code(value);
					}
					
					
					
					
					
					if ("company_code".Equals(schemeField.Trim())){
						
						needSkip = false;
						string value;
						value = iprot.ReadString();
						
						structs.SetCompany_code(value);
					}
					
					
					
					
					
					if ("transferring_type".Equals(schemeField.Trim())){
						
						needSkip = false;
						byte? value;
						value = iprot.ReadByte(); 
						
						structs.SetTransferring_type(value);
					}
					
					
					
					
					
					if ("receiving_time_range".Equals(schemeField.Trim())){
						
						needSkip = false;
						com.vip.xstore.pda.common.TimeRange value;
						
						value = new com.vip.xstore.pda.common.TimeRange();
						com.vip.xstore.pda.common.TimeRangeHelper.getInstance().Read(value, iprot);
						
						structs.SetReceiving_time_range(value);
					}
					
					
					
					
					
					if ("delivery_time_range".Equals(schemeField.Trim())){
						
						needSkip = false;
						com.vip.xstore.pda.common.TimeRange value;
						
						value = new com.vip.xstore.pda.common.TimeRange();
						com.vip.xstore.pda.common.TimeRangeHelper.getInstance().Read(value, iprot);
						
						structs.SetDelivery_time_range(value);
					}
					
					
					
					
					if(needSkip){
						
						ProtocolUtil.skip(iprot);
					}
					
					iprot.ReadFieldEnd();
				}
				
				iprot.ReadStructEnd();
				Validate(structs);
			}
			else{
				
				throw new OspException();
			}
			
			
		}
		
		
		public void Write(XUPReceivingOrderRequest structs, Protocol oprot){
			
			Validate(structs);
			oprot.WriteStructBegin();
			
			if(structs.GetSource() != null) {
				
				oprot.WriteFieldBegin("source");
				oprot.WriteString(structs.GetSource());
				
				oprot.WriteFieldEnd();
			}
			
			else{
				throw new ArgumentException("source can not be null!");
			}
			
			
			if(structs.GetClient_id() != null) {
				
				oprot.WriteFieldBegin("client_id");
				oprot.WriteString(structs.GetClient_id());
				
				oprot.WriteFieldEnd();
			}
			
			else{
				throw new ArgumentException("client_id can not be null!");
			}
			
			
			if(structs.GetPda_version() != null) {
				
				oprot.WriteFieldBegin("pda_version");
				oprot.WriteString(structs.GetPda_version());
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetReceiving_type() != null) {
				
				oprot.WriteFieldBegin("receiving_type");
				oprot.WriteByte((byte)structs.GetReceiving_type()); 
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetCreate_time_range() != null) {
				
				oprot.WriteFieldBegin("create_time_range");
				
				com.vip.xstore.pda.common.TimeRangeHelper.getInstance().Write(structs.GetCreate_time_range(), oprot);
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetOrder_nos() != null) {
				
				oprot.WriteFieldBegin("order_nos");
				
				oprot.WriteSetBegin();
				foreach(string _item0 in structs.GetOrder_nos()){
					
					oprot.WriteString(_item0);
					
				}
				
				oprot.WriteSetEnd();
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetAction_types() != null) {
				
				oprot.WriteFieldBegin("action_types");
				
				oprot.WriteSetBegin();
				foreach(byte _item0 in structs.GetAction_types()){
					
					oprot.WriteByte((byte)_item0); 
					
				}
				
				oprot.WriteSetEnd();
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetPage() != null) {
				
				oprot.WriteFieldBegin("page");
				oprot.WriteI32((int)structs.GetPage()); 
				
				oprot.WriteFieldEnd();
			}
			
			else{
				throw new ArgumentException("page can not be null!");
			}
			
			
			if(structs.GetPage_size() != null) {
				
				oprot.WriteFieldBegin("page_size");
				oprot.WriteI32((int)structs.GetPage_size()); 
				
				oprot.WriteFieldEnd();
			}
			
			else{
				throw new ArgumentException("page_size can not be null!");
			}
			
			
			if(structs.GetDelivery_warehouse_code() != null) {
				
				oprot.WriteFieldBegin("delivery_warehouse_code");
				oprot.WriteString(structs.GetDelivery_warehouse_code());
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetReceiving_warehouse_code() != null) {
				
				oprot.WriteFieldBegin("receiving_warehouse_code");
				oprot.WriteString(structs.GetReceiving_warehouse_code());
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetCompany_code() != null) {
				
				oprot.WriteFieldBegin("company_code");
				oprot.WriteString(structs.GetCompany_code());
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetTransferring_type() != null) {
				
				oprot.WriteFieldBegin("transferring_type");
				oprot.WriteByte((byte)structs.GetTransferring_type()); 
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetReceiving_time_range() != null) {
				
				oprot.WriteFieldBegin("receiving_time_range");
				
				com.vip.xstore.pda.common.TimeRangeHelper.getInstance().Write(structs.GetReceiving_time_range(), oprot);
				
				oprot.WriteFieldEnd();
			}
			
			
			if(structs.GetDelivery_time_range() != null) {
				
				oprot.WriteFieldBegin("delivery_time_range");
				
				com.vip.xstore.pda.common.TimeRangeHelper.getInstance().Write(structs.GetDelivery_time_range(), oprot);
				
				oprot.WriteFieldEnd();
			}
			
			
			oprot.WriteFieldStop();
			oprot.WriteStructEnd();
		}
		
		
		public void Validate(XUPReceivingOrderRequest bean){
			
			
		}
		
	}
	
}