using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Security.Cryptography;
using System.Web;
using System.Net;
using System.IO;

namespace ych_sdk
{
    class YchClient
    {
        private string appkey = "68757317";

        private string secret= "e8VwzX9CqlpBoEn4YNr7";

        public YchClient()
        {
        }

        public YchClient(string appkey, string secret)
        {
            this.appkey = appkey;
            this.secret = secret;
        }

        public string send(string url, SortedDictionary<string, string> param)
        {
            if (string.IsNullOrEmpty(appkey)
                || string.IsNullOrEmpty(secret)
                || string.IsNullOrEmpty(url)
                || param == null
                || param.Keys.Count == 0)
            {
                return "Parameter error.";
            }

            foreach (KeyValuePair<string, string> kv in param)
            {
                string value = kv.Value;

                if (value != null && value.ToLower().Equals("null"))
                {
                    return kv.Key + " is null.";
                }
            }

            param["appKey"] = appkey;

            return doPost(url, param);
        }

        private string doPost(string url, SortedDictionary<string, string> param)
        {
            string queryString = getSignedUrl(secret, param);

            if (string.IsNullOrEmpty(queryString))
            {
                return "getSignedUrl error.";
            }

            return sendRequest(url, queryString);
        }

        private string getSignedUrl(string appSecret, SortedDictionary<string, string> param)
        {
            string sign = getSignature(appSecret, param);
            StringBuilder query = new StringBuilder();

            foreach (KeyValuePair<string, string> kv in param)
            {
                query.Append(kv.Key);
                query.Append("=");

                if (!string.IsNullOrEmpty(kv.Value))
                {
                    query.Append(HttpUtility.UrlEncode(kv.Value, Encoding.GetEncoding("UTF-8")));
                }

                query.Append("&");
            }

            query.Append("sign=");
            query.Append(sign);

            return query.ToString();
        }

        private string getSignature(string appSecret, SortedDictionary<string, string> param)
        {
            StringBuilder query = new StringBuilder(appSecret);

            foreach (KeyValuePair<string, string> kv in param)
            {
                query.Append(kv.Key).Append(kv.Value);
            }

            query.Append(appSecret);

            MD5 md5 = MD5.Create();
            byte[] bytes = md5.ComputeHash(Encoding.UTF8.GetBytes(query.ToString()));

            // 把二进制转化为大写的十六进制
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < bytes.Length; i++)
            {
                result.Append(bytes[i].ToString("X2"));
            }

            return result.ToString().ToLower();
        }

        private string sendRequest(string path, string query)
        {
            string result = "";
            HttpWebRequest req = (HttpWebRequest)WebRequest.Create(path);
            req.Method = "POST";
            req.ContentType = "application/x-www-form-urlencoded";
            req.UserAgent = "ych-sdk-dotnet";

            byte[] data = Encoding.UTF8.GetBytes(query);
            req.ContentLength = data.Length;
            using (Stream reqStream = req.GetRequestStream())
            {
                reqStream.Write(data, 0, data.Length);
                reqStream.Close();
            }

            HttpWebResponse resp = (HttpWebResponse)req.GetResponse();
            Stream stream = resp.GetResponseStream();
            //获取响应内容  
            using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
            {
                result = reader.ReadToEnd();
            }
            //YchLog.WriteLine($"御城河日志请求，Path:{path} 参数：{query} 返回：{result}");
            return result;
        }
    }
}
