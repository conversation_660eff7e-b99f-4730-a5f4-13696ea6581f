using System;
using open_sdk.common.constant;

namespace open_sdk.api
{
	// Token: 0x0200001A RID: 26
	public class GeneralApi : AbstractAPI
	{
		// Token: 0x06000052 RID: 82 RVA: 0x00002BC4 File Offset: 0x00000DC4
		public override string GetGateway()
		{
			return this.gateway;
		}

		// Token: 0x06000053 RID: 83 RVA: 0x00002BDC File Offset: 0x00000DDC
		public override string GetHttpMethod()
		{
			return this.httpMethod;
		}

		// Token: 0x06000054 RID: 84 RVA: 0x00002BF4 File Offset: 0x00000DF4
		public override string GetName()
		{
			return this.serviceName;
		}

		// Token: 0x06000055 RID: 85 RVA: 0x00002C0C File Offset: 0x00000E0C
		public override OAuthEnum GetOAuthType()
		{
			return this.oAuthType;
		}

		// Token: 0x06000056 RID: 86 RVA: 0x00002C24 File Offset: 0x00000E24
		public OAuthEnum SetOAuthType(OAuthEnum oAuthType)
		{
			this.oAuthType = oAuthType;
			return oAuthType;
		}

		// Token: 0x06000057 RID: 87 RVA: 0x00002C40 File Offset: 0x00000E40
		public void SetHttpMethod(string httpMethod)
		{
			this.httpMethod = httpMethod;
		}

		// Token: 0x06000058 RID: 88 RVA: 0x00002C4A File Offset: 0x00000E4A
		public void SetName(string serviceName)
		{
			this.serviceName = serviceName;
		}

		// Token: 0x06000059 RID: 89 RVA: 0x00002C54 File Offset: 0x00000E54
		public void SetVersion(string serviceVersion)
		{
			this.version = serviceVersion;
		}

		// Token: 0x0600005A RID: 90 RVA: 0x00002C60 File Offset: 0x00000E60
		public override string ToString()
		{
			return string.Concat(new object[]
			{
				"GeneralApi{httpMethod='",
				this.httpMethod,
				"', serviceName='",
				this.serviceName,
				"', serviceVersion='",
				this.version,
				"', oAuthType=",
				this.oAuthType,
				", apiParams=",
				this.apiParams,
				", gateway='",
				this.gateway,
				"', headers=",
				this.headers,
				"}"
			});
		}

		// Token: 0x0400003A RID: 58
		private string httpMethod;

		// Token: 0x0400003B RID: 59
		private string serviceName;

		// Token: 0x0400003C RID: 60
		private OAuthEnum oAuthType;
	}
}
