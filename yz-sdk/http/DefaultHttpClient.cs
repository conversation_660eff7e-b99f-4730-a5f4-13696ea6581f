using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;
using open_sdk.exception;

namespace open_sdk.http
{
	// Token: 0x0200000C RID: 12
	public class DefaultHttpClient : IHttpClient
	{
		// Token: 0x06000029 RID: 41 RVA: 0x0000252C File Offset: 0x0000072C
		public string Send(string url, IDictionary<string, dynamic> apiParams, IDictionary<string, string> headers, List<KeyValuePair<string, string>> files)
		{
			string result2;
			using (HttpClient httpClient = new HttpClient())
			{
				bool flag = headers != null;
				if (flag)
				{
					foreach (KeyValuePair<string, string> keyValuePair in headers)
					{
						httpClient.DefaultRequestHeaders.Add(keyValuePair.Key, keyValuePair.Value);
					}
				}
				httpClient.DefaultRequestHeaders.Add("User-Agent", "YZY-Open-Client 1.0.0 - CSharp");
				UriBuilder uriBuilder = new UriBuilder(url);
				string content = JsonConvert.SerializeObject(apiParams);
				HttpContent httpContent = new StringContent(content, Encoding.UTF8, "application/json");
				bool flag2 = files != null;
				if (flag2)
				{
					MultipartFormDataContent multipartFormDataContent = new MultipartFormDataContent();
					foreach (KeyValuePair<string, string> keyValuePair2 in files)
					{
						StreamContent content2 = new StreamContent(new FileStream(keyValuePair2.Value, FileMode.Open));
						string value = keyValuePair2.Value;
						int num = value.LastIndexOf("/", StringComparison.Ordinal) + 1;
						multipartFormDataContent.Add(content2, keyValuePair2.Key, value.Substring(num, value.Length - num));
					}
					httpContent = multipartFormDataContent;
				}
				httpContent.Headers.ContentType.MediaType = "application/json";
				httpContent.Headers.ContentType.CharSet = "utf-8";
				Console.WriteLine("Request Result------>" + httpContent.ReadAsStringAsync().Result);
				HttpResponseMessage result = httpClient.PostAsync(url, httpContent).Result;
				bool isSuccessStatusCode = result.IsSuccessStatusCode;
				if (!isSuccessStatusCode)
				{
					throw new SDKException(result.StatusCode, "Internal server error");
				}
				result2 = result.Content.ReadAsStringAsync().Result;
			}
			return result2;
		}

		// Token: 0x04000013 RID: 19
		private const string CONTENT_TYPE = "application/json";

		// Token: 0x04000014 RID: 20
		private const string CHART_SET = "utf-8";
	}
}
