using System;
using open_sdk.exception;

namespace open_sdk.utils
{
	// Token: 0x02000004 RID: 4
	public static class CheckUtils
	{
		// Token: 0x06000003 RID: 3 RVA: 0x0000208C File Offset: 0x0000028C
		public static void CheckArgument(bool expression, int code, string message)
		{
			bool flag = !expression;
			if (flag)
			{
				throw new SDKException(code, message);
			}
		}

		// Token: 0x06000004 RID: 4 RVA: 0x000020AB File Offset: 0x000002AB
		internal static void CheckArgument(bool v1, Func<TypeCode> getTypeCode, string v2)
		{
		}

		// Token: 0x06000005 RID: 5 RVA: 0x000020B0 File Offset: 0x000002B0
		public static void CheckParams(string clientId, string clientSecret)
		{
			bool flag = string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret);
			if (flag)
			{
				throw new Exception("clientId or clientSecret cannot be null");
			}
		}
	}
}
